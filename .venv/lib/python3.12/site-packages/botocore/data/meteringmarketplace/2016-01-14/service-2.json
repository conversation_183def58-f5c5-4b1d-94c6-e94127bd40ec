{"version": "2.0", "metadata": {"apiVersion": "2016-01-14", "endpointPrefix": "metering.marketplace", "jsonVersion": "1.1", "protocol": "json", "serviceFullName": "AWSMarketplace Metering", "serviceId": "Marketplace Metering", "signatureVersion": "v4", "signingName": "aws-marketplace", "targetPrefix": "AWSMPMeteringService", "uid": "meteringmarketplace-2016-01-14"}, "operations": {"BatchMeterUsage": {"name": "BatchMeterUsage", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "BatchMeterUsageRequest"}, "output": {"shape": "BatchMeterUsageResult"}, "errors": [{"shape": "InternalServiceErrorException"}, {"shape": "InvalidProductCodeException"}, {"shape": "InvalidUsageDimensionException"}, {"shape": "InvalidTagException"}, {"shape": "InvalidUsageAllocationsException"}, {"shape": "InvalidCustomerIdentifierException"}, {"shape": "TimestampOutOfBoundsException"}, {"shape": "ThrottlingException"}, {"shape": "DisabledApiException"}], "documentation": "<p> <code>BatchMeterUsage</code> is called from a SaaS application listed on AWS Marketplace to post metering records for a set of customers.</p> <p>For identical requests, the API is idempotent; requests can be retried with the same records or a subset of the input records.</p> <p>Every request to <code>BatchMeterUsage</code> is for one product. If you need to meter usage for multiple products, you must make multiple calls to <code>BatchMeterUsage</code>.</p> <p>Usage records are expected to be submitted as quickly as possible after the event that is being recorded, and are not accepted more than 6 hours after the event.</p> <p> <code>BatchMeterUsage</code> can process up to 25 <code>UsageRecords</code> at a time.</p> <p>A <code>UsageRecord</code> can optionally include multiple usage allocations, to provide customers with usage data split into buckets by tags that you define (or allow the customer to define).</p> <p> <code>BatchMeterUsage</code> returns a list of <code>UsageRecordResult</code> objects, showing the result for each <code>UsageRecord</code>, as well as a list of <code>UnprocessedRecords</code>, indicating errors in the service side that you should retry.</p> <p> <code>BatchMeterUsage</code> requests must be less than 1MB in size.</p> <note> <p>For an example of using <code>BatchMeterUsage</code>, see <a href=\"https://docs.aws.amazon.com/marketplace/latest/userguide/saas-code-examples.html#saas-batchmeterusage-example\"> BatchMeterUsage code example</a> in the <i>AWS Marketplace Seller Guide</i>.</p> </note>"}, "MeterUsage": {"name": "MeterUsage", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "MeterUsageRequest"}, "output": {"shape": "MeterUsageResult"}, "errors": [{"shape": "InternalServiceErrorException"}, {"shape": "InvalidProductCodeException"}, {"shape": "InvalidUsageDimensionException"}, {"shape": "InvalidTagException"}, {"shape": "InvalidUsageAllocationsException"}, {"shape": "InvalidEndpointRegionException"}, {"shape": "TimestampOutOfBoundsException"}, {"shape": "DuplicateRequestException"}, {"shape": "ThrottlingException"}, {"shape": "CustomerNotEntitledException"}], "documentation": "<p>API to emit metering records. For identical requests, the API is idempotent. It simply returns the metering record ID.</p> <p> <code>MeterUsage</code> is authenticated on the buyer's AWS account using credentials from the EC2 instance, ECS task, or EKS pod.</p> <p> <code>MeterUsage</code> can optionally include multiple usage allocations, to provide customers with usage data split into buckets by tags that you define (or allow the customer to define).</p> <p>Usage records are expected to be submitted as quickly as possible after the event that is being recorded, and are not accepted more than 6 hours after the event.</p>"}, "RegisterUsage": {"name": "RegisterUsage", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RegisterUsageRequest"}, "output": {"shape": "RegisterUsageResult"}, "errors": [{"shape": "InvalidProductCodeException"}, {"shape": "InvalidRegionException"}, {"shape": "InvalidPublicKeyVersionException"}, {"shape": "PlatformNotSupportedException"}, {"shape": "CustomerNotEntitledException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServiceErrorException"}, {"shape": "DisabledApiException"}], "documentation": "<p>Paid container software products sold through AWS Marketplace must integrate with the AWS Marketplace Metering Service and call the <code>RegisterUsage</code> operation for software entitlement and metering. Free and BYOL products for Amazon ECS or Amazon EKS aren't required to call <code>RegisterUsage</code>, but you may choose to do so if you would like to receive usage data in your seller reports. The sections below explain the behavior of <code>RegisterUsage</code>. <code>RegisterUsage</code> performs two primary functions: metering and entitlement.</p> <ul> <li> <p> <i>Entitlement</i>: <code>RegisterUsage</code> allows you to verify that the customer running your paid software is subscribed to your product on AWS Marketplace, enabling you to guard against unauthorized use. Your container image that integrates with <code>RegisterUsage</code> is only required to guard against unauthorized use at container startup, as such a <code>CustomerNotSubscribedException</code> or <code>PlatformNotSupportedException</code> will only be thrown on the initial call to <code>RegisterUsage</code>. Subsequent calls from the same Amazon ECS task instance (e.g. task-id) or Amazon EKS pod will not throw a <code>CustomerNotSubscribedException</code>, even if the customer unsubscribes while the Amazon ECS task or Amazon EKS pod is still running.</p> </li> <li> <p> <i>Metering</i>: <code>RegisterUsage</code> meters software use per ECS task, per hour, or per pod for Amazon EKS with usage prorated to the second. A minimum of 1 minute of usage applies to tasks that are short lived. For example, if a customer has a 10 node Amazon ECS or Amazon EKS cluster and a service configured as a Daemon Set, then Amazon ECS or Amazon EKS will launch a task on all 10 cluster nodes and the customer will be charged: (10 * hourly_rate). Metering for software use is automatically handled by the AWS Marketplace Metering Control Plane -- your software is not required to perform any metering specific actions, other than call <code>RegisterUsage</code> once for metering of software use to commence. The AWS Marketplace Metering Control Plane will also continue to bill customers for running ECS tasks and Amazon EKS pods, regardless of the customers subscription state, removing the need for your software to perform entitlement checks at runtime.</p> </li> </ul>"}, "ResolveCustomer": {"name": "ResolveCustomer", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ResolveCustomerRequest"}, "output": {"shape": "ResolveCustomerResult"}, "errors": [{"shape": "InvalidTokenException"}, {"shape": "ExpiredTokenException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServiceErrorException"}, {"shape": "DisabledApiException"}], "documentation": "<p> <code>ResolveCustomer</code> is called by a SaaS application during the registration process. When a buyer visits your website during the registration process, the buyer submits a registration token through their browser. The registration token is resolved through this API to obtain a <code>CustomerIdentifier</code> along with the <code>CustomerAWSAccountId</code> and <code>ProductCode</code>.</p> <note> <p>The API needs to called from the seller account id used to publish the SaaS application to successfully resolve the token.</p> <p>For an example of using <code>ResolveCustomer</code>, see <a href=\"https://docs.aws.amazon.com/marketplace/latest/userguide/saas-code-examples.html#saas-resolvecustomer-example\"> ResolveCustomer code example</a> in the <i>AWS Marketplace Seller Guide</i>.</p> </note>"}}, "shapes": {"AllocatedUsageQuantity": {"type": "integer", "max": **********, "min": 0}, "BatchMeterUsageRequest": {"type": "structure", "required": ["UsageRecords", "ProductCode"], "members": {"UsageRecords": {"shape": "UsageRecordList", "documentation": "<p>The set of <code>UsageRecords</code> to submit. <code>BatchMeterUsage</code> accepts up to 25 <code>UsageRecords</code> at a time.</p>"}, "ProductCode": {"shape": "ProductCode", "documentation": "<p>Product code is used to uniquely identify a product in AWS Marketplace. The product code should be the same as the one used during the publishing of a new product.</p>"}}, "documentation": "<p>A <code>BatchMeterUsageRequest</code> contains <code>UsageRecords</code>, which indicate quantities of usage within your application.</p>"}, "BatchMeterUsageResult": {"type": "structure", "members": {"Results": {"shape": "UsageRecordResultList", "documentation": "<p>Contains all <code>UsageRecords</code> processed by <code>BatchMeterUsage</code>. These records were either honored by AWS Marketplace Metering Service or were invalid. Invalid records should be fixed before being resubmitted.</p>"}, "UnprocessedRecords": {"shape": "UsageRecordList", "documentation": "<p>Contains all <code>UsageRecords</code> that were not processed by <code>BatchMeterUsage</code>. This is a list of <code>UsageRecords</code>. You can retry the failed request by making another <code>BatchMeterUsage</code> call with this list as input in the <code>BatchMeterUsageRequest</code>.</p>"}}, "documentation": "<p>Contains the <code>UsageRecords</code> processed by <code>BatchMeterUsage</code> and any records that have failed due to transient error.</p>"}, "Boolean": {"type": "boolean"}, "CustomerAWSAccountId": {"type": "string", "max": 255, "min": 1, "pattern": "^[0-9]+$"}, "CustomerIdentifier": {"type": "string", "max": 255, "min": 1, "pattern": "[\\s\\S]+"}, "CustomerNotEntitledException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>Exception thrown when the customer does not have a valid subscription for the product.</p>", "exception": true}, "DisabledApiException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>The API is disabled in the Region.</p>", "exception": true}, "DuplicateRequestException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>A metering record has already been emitted by the same EC2 instance, ECS task, or EKS pod for the given {<code>usageDimension</code>, <code>timestamp</code>} with a different <code>usageQuantity</code>.</p>", "exception": true}, "ExpiredTokenException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>The submitted registration token has expired. This can happen if the buyer's browser takes too long to redirect to your page, the buyer has resubmitted the registration token, or your application has held on to the registration token for too long. Your SaaS registration website should redeem this token as soon as it is submitted by the buyer's browser.</p>", "exception": true}, "InternalServiceErrorException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>An internal error has occurred. Retry your request. If the problem persists, post a message with details on the AWS forums.</p>", "exception": true, "fault": true}, "InvalidCustomerIdentifierException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>You have metered usage for a <code>CustomerIdentifier</code> that does not exist.</p>", "exception": true}, "InvalidEndpointRegionException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>The endpoint being called is in a AWS Region different from your EC2 instance, ECS task, or EKS pod. The Region of the Metering Service endpoint and the AWS Region of the resource must match.</p>", "exception": true}, "InvalidProductCodeException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>The product code passed does not match the product code used for publishing the product.</p>", "exception": true}, "InvalidPublicKeyVersionException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>Public Key version is invalid.</p>", "exception": true}, "InvalidRegionException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p> <code>RegisterUsage</code> must be called in the same AWS Region the ECS task was launched in. This prevents a container from hardcoding a Region (e.g. withRegion(“us-east-1”) when calling <code>RegisterUsage</code>.</p>", "exception": true}, "InvalidTagException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>The tag is invalid, or the number of tags is greater than 5.</p>", "exception": true}, "InvalidTokenException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>Registration token is invalid.</p>", "exception": true}, "InvalidUsageAllocationsException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>The usage allocation objects are invalid, or the number of allocations is greater than 500 for a single usage record.</p>", "exception": true}, "InvalidUsageDimensionException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>The usage dimension does not match one of the <code>UsageDimensions</code> associated with products.</p>", "exception": true}, "MeterUsageRequest": {"type": "structure", "required": ["ProductCode", "Timestamp", "UsageDimension"], "members": {"ProductCode": {"shape": "ProductCode", "documentation": "<p>Product code is used to uniquely identify a product in AWS Marketplace. The product code should be the same as the one used during the publishing of a new product.</p>"}, "Timestamp": {"shape": "Timestamp", "documentation": "<p>Timestamp, in UTC, for which the usage is being reported. Your application can meter usage for up to one hour in the past. Make sure the <code>timestamp</code> value is not before the start of the software usage.</p>"}, "UsageDimension": {"shape": "UsageDimension", "documentation": "<p>It will be one of the fcp dimension name provided during the publishing of the product.</p>"}, "UsageQuantity": {"shape": "UsageQuantity", "documentation": "<p>Consumption value for the hour. Defaults to <code>0</code> if not specified.</p>"}, "DryRun": {"shape": "Boolean", "documentation": "<p>Checks whether you have the permissions required for the action, but does not make the request. If you have the permissions, the request returns <code>DryRunOperation</code>; otherwise, it returns <code>UnauthorizedException</code>. Defaults to <code>false</code> if not specified.</p>"}, "UsageAllocations": {"shape": "UsageAllocations", "documentation": "<p>The set of <code>UsageAllocations</code> to submit.</p> <p>The sum of all <code>UsageAllocation</code> quantities must equal the <code>UsageQuantity</code> of the <code>MeterUsage</code> request, and each <code>UsageAllocation</code> must have a unique set of tags (include no tags).</p>"}}}, "MeterUsageResult": {"type": "structure", "members": {"MeteringRecordId": {"shape": "String", "documentation": "<p>Metering record id.</p>"}}}, "NonEmptyString": {"type": "string", "pattern": "[\\s\\S]+"}, "Nonce": {"type": "string", "max": 255, "pattern": "[\\s\\S]*"}, "PlatformNotSupportedException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>AWS Marketplace does not support metering usage from the underlying platform. Currently, Amazon ECS, Amazon EKS, and AWS Fargate are supported.</p>", "exception": true}, "ProductCode": {"type": "string", "max": 255, "min": 1, "pattern": "^[-a-zA-Z0-9/=:_.@]*$"}, "RegisterUsageRequest": {"type": "structure", "required": ["ProductCode", "PublicKeyVersion"], "members": {"ProductCode": {"shape": "ProductCode", "documentation": "<p>Product code is used to uniquely identify a product in AWS Marketplace. The product code should be the same as the one used during the publishing of a new product.</p>"}, "PublicKeyVersion": {"shape": "VersionInteger", "documentation": "<p>Public Key Version provided by AWS Marketplace</p>"}, "Nonce": {"shape": "<PERSON><PERSON>", "documentation": "<p>(Optional) To scope down the registration to a specific running software instance and guard against replay attacks.</p>"}}}, "RegisterUsageResult": {"type": "structure", "members": {"PublicKeyRotationTimestamp": {"shape": "Timestamp", "documentation": "<p>(Optional) Only included when public key version has expired</p>"}, "Signature": {"shape": "NonEmptyString", "documentation": "<p>JWT <PERSON></p>"}}}, "ResolveCustomerRequest": {"type": "structure", "required": ["RegistrationToken"], "members": {"RegistrationToken": {"shape": "NonEmptyString", "documentation": "<p>When a buyer visits your website during the registration process, the buyer submits a registration token through the browser. The registration token is resolved to obtain a <code>CustomerIdentifier</code> along with the <code>CustomerAWSAccountId</code> and <code>ProductCode</code>.</p>"}}, "documentation": "<p>Contains input to the <code>ResolveCustomer</code> operation.</p>"}, "ResolveCustomerResult": {"type": "structure", "members": {"CustomerIdentifier": {"shape": "CustomerIdentifier", "documentation": "<p>The <code>CustomerIdentifier</code> is used to identify an individual customer in your application. Calls to <code>BatchMeterUsage</code> require <code>CustomerIdentifiers</code> for each <code>UsageRecord</code>.</p>"}, "ProductCode": {"shape": "ProductCode", "documentation": "<p>The product code is returned to confirm that the buyer is registering for your product. Subsequent <code>BatchMeterUsage</code> calls should be made using this product code.</p>"}, "CustomerAWSAccountId": {"shape": "CustomerAWSAccountId", "documentation": "<p>The <code>CustomerAWSAccountId</code> provides the AWS account ID associated with the <code>CustomerIdentifier</code> for the individual customer.</p>"}}, "documentation": "<p>The result of the <code>ResolveCustomer</code> operation. Contains the <code>CustomerIdentifier</code> along with the <code>CustomerAWSAccountId</code> and <code>ProductCode</code>.</p>"}, "String": {"type": "string"}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>One part of a key-value pair that makes up a <code>tag</code>. A <code>key</code> is a label that acts like a category for the specific tag values.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>One part of a key-value pair that makes up a <code>tag</code>. A <code>value</code> acts as a descriptor within a tag category (key). The value can be empty or null.</p>"}}, "documentation": "<p>Metadata assigned to an allocation. Each tag is made up of a <code>key</code> and a <code>value</code>.</p>"}, "TagKey": {"type": "string", "max": 100, "min": 1, "pattern": "^[a-zA-Z0-9+ -=._:\\/@]+$"}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "max": 5, "min": 1}, "TagValue": {"type": "string", "max": 256, "min": 1, "pattern": "^[a-zA-Z0-9+ -=._:\\/@]+$"}, "ThrottlingException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>The calls to the API are throttled.</p>", "exception": true}, "Timestamp": {"type": "timestamp"}, "TimestampOutOfBoundsException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>The <code>timestamp</code> value passed in the <code>UsageRecord</code> is out of allowed range.</p> <p>For <code>BatchMeterUsage</code>, if any of the records are outside of the allowed range, the entire batch is not processed. You must remove invalid records and try again.</p>", "exception": true}, "UsageAllocation": {"type": "structure", "required": ["AllocatedUsageQuantity"], "members": {"AllocatedUsageQuantity": {"shape": "AllocatedUsageQuantity", "documentation": "<p>The total quantity allocated to this bucket of usage.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The set of tags that define the bucket of usage. For the bucket of items with no tags, this parameter can be left out.</p>"}}, "documentation": "<p>Usage allocations allow you to split usage into buckets by tags.</p> <p>Each <code>UsageAllocation</code> indicates the usage quantity for a specific set of tags.</p>"}, "UsageAllocations": {"type": "list", "member": {"shape": "UsageAllocation"}, "max": 2500, "min": 1}, "UsageDimension": {"type": "string", "max": 255, "min": 1, "pattern": "[\\s\\S]+"}, "UsageQuantity": {"type": "integer", "max": **********, "min": 0}, "UsageRecord": {"type": "structure", "required": ["Timestamp", "CustomerIdentifier", "Dimension"], "members": {"Timestamp": {"shape": "Timestamp", "documentation": "<p>Timestamp, in UTC, for which the usage is being reported.</p> <p>Your application can meter usage for up to one hour in the past. Make sure the <code>timestamp</code> value is not before the start of the software usage.</p>"}, "CustomerIdentifier": {"shape": "CustomerIdentifier", "documentation": "<p>The <code>CustomerIdentifier</code> is obtained through the <code>ResolveCustomer</code> operation and represents an individual buyer in your application.</p>"}, "Dimension": {"shape": "UsageDimension", "documentation": "<p>During the process of registering a product on AWS Marketplace, dimensions are specified. These represent different units of value in your application.</p>"}, "Quantity": {"shape": "UsageQuantity", "documentation": "<p>The quantity of usage consumed by the customer for the given dimension and time. Defaults to <code>0</code> if not specified.</p>"}, "UsageAllocations": {"shape": "UsageAllocations", "documentation": "<p>The set of <code>UsageAllocations</code> to submit. The sum of all <code>UsageAllocation</code> quantities must equal the Quantity of the <code>UsageRecord</code>.</p>"}}, "documentation": "<p>A <code>UsageRecord</code> indicates a quantity of usage for a given product, customer, dimension and time.</p> <p>Multiple requests with the same <code>UsageRecords</code> as input will be de-duplicated to prevent double charges.</p>"}, "UsageRecordList": {"type": "list", "member": {"shape": "UsageRecord"}, "max": 25, "min": 0}, "UsageRecordResult": {"type": "structure", "members": {"UsageRecord": {"shape": "UsageRecord", "documentation": "<p>The <code>UsageRecord</code> that was part of the <code>BatchMeterUsage</code> request.</p>"}, "MeteringRecordId": {"shape": "String", "documentation": "<p>The <code>MeteringRecordId</code> is a unique identifier for this metering event.</p>"}, "Status": {"shape": "UsageRecordResultStatus", "documentation": "<p>The <code>UsageRecordResult</code> <code>Status</code> indicates the status of an individual <code>UsageRecord</code> processed by <code>BatchMeterUsage</code>.</p> <ul> <li> <p> <i>Success</i>- The <code>UsageRecord</code> was accepted and honored by <code>BatchMeterUsage</code>.</p> </li> <li> <p> <i>CustomerNotSubscribed</i>- The <code>CustomerIdentifier</code> specified is not able to use your product. The <code>UsageRecord</code> was not honored. There are three causes for this result:</p> <ul> <li> <p>The customer identifier is invalid.</p> </li> <li> <p>The customer identifier provided in the metering record does not have an active agreement or subscription with this product. Future <code>UsageRecords</code> for this customer will fail until the customer subscribes to your product.</p> </li> <li> <p>The customer's AWS account was suspended.</p> </li> </ul> </li> <li> <p> <i>DuplicateRecord</i>- Indicates that the <code>UsageRecord</code> was invalid and not honored. A previously metered <code>UsageRecord</code> had the same customer, dimension, and time, but a different quantity.</p> </li> </ul>"}}, "documentation": "<p>A <code>UsageRecordResult</code> indicates the status of a given <code>UsageRecord</code> processed by <code>BatchMeterUsage</code>.</p>"}, "UsageRecordResultList": {"type": "list", "member": {"shape": "UsageRecordResult"}}, "UsageRecordResultStatus": {"type": "string", "enum": ["Success", "CustomerNotSubscribed", "DuplicateRecord"]}, "VersionInteger": {"type": "integer", "min": 1}, "errorMessage": {"type": "string"}}, "documentation": "<fullname>AWS Marketplace Metering Service</fullname> <p>This reference provides descriptions of the low-level AWS Marketplace Metering Service API.</p> <p>AWS Marketplace sellers can use this API to submit usage data for custom usage dimensions.</p> <p>For information on the permissions you need to use this API, see <a href=\"https://docs.aws.amazon.com/marketplace/latest/userguide/iam-user-policy-for-aws-marketplace-actions.html\">AWS Marketplace metering and entitlement API permissions</a> in the <i>AWS Marketplace Seller Guide.</i> </p> <p> <b>Submitting Metering Records</b> </p> <ul> <li> <p> <i>MeterUsage</i> - Submits the metering record for an AWS Marketplace product. <code>MeterUsage</code> is called from an EC2 instance or a container running on EKS or ECS.</p> </li> <li> <p> <i>BatchMeterUsage</i> - Submits the metering record for a set of customers. <code>BatchMeterUsage</code> is called from a software-as-a-service (SaaS) application.</p> </li> </ul> <p> <b>Accepting New Customers</b> </p> <ul> <li> <p> <i>ResolveCustomer</i> - Called by a SaaS application during the registration process. When a buyer visits your website during the registration process, the buyer submits a Registration Token through the browser. The Registration Token is resolved through this API to obtain a <code>CustomerIdentifier</code> along with the <code>CustomerAWSAccountId</code> and <code>ProductCode</code>.</p> </li> </ul> <p> <b>Entitlement and Metering for Paid Container Products</b> </p> <ul> <li> <p>Paid container software products sold through AWS Marketplace must integrate with the AWS Marketplace Metering Service and call the <code>RegisterUsage</code> operation for software entitlement and metering. Free and BYOL products for Amazon ECS or Amazon EKS aren't required to call <code>RegisterUsage</code>, but you can do so if you want to receive usage data in your seller reports. For more information on using the <code>RegisterUsage</code> operation, see <a href=\"https://docs.aws.amazon.com/marketplace/latest/userguide/container-based-products.html\">Container-Based Products</a>. </p> </li> </ul> <p> <code>BatchMeterUsage</code> API calls are captured by AWS CloudTrail. You can use Cloudtrail to verify that the SaaS metering records that you sent are accurate by searching for records with the <code>eventName</code> of <code>BatchMeterUsage</code>. You can also use CloudTrail to audit records over time. For more information, see the <i> <a href=\"http://docs.aws.amazon.com/awscloudtrail/latest/userguide/cloudtrail-concepts.html\">AWS CloudTrail User Guide</a>.</i> </p>"}