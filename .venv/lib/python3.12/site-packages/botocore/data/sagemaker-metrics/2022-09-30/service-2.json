{"version": "2.0", "metadata": {"apiVersion": "2022-09-30", "endpointPrefix": "metrics.sagemaker", "protocol": "rest-json", "serviceAbbreviation": "SageMaker Metrics", "serviceFullName": "Amazon SageMaker Metrics Service", "serviceId": "SageMaker Metrics", "signatureVersion": "v4", "signingName": "sagemaker", "uid": "sagemaker-metrics-2022-09-30"}, "operations": {"BatchPutMetrics": {"name": "BatchPutMetrics", "http": {"method": "PUT", "requestUri": "/BatchPutMetrics"}, "input": {"shape": "BatchPutMetricsRequest"}, "output": {"shape": "BatchPutMetricsResponse"}, "documentation": "<p>Used to ingest training metrics into SageMaker. These metrics can be visualized in SageMaker Studio and retrieved with the <code>GetMetrics</code> API. </p>"}}, "shapes": {"BatchPutMetricsError": {"type": "structure", "members": {"Code": {"shape": "PutMetricsErrorCode", "documentation": "<p>The error code of an error that occured when attempting to put metrics.</p> <ul> <li> <p> <code>METRIC_LIMIT_EXCEEDED</code>: The maximum amount of metrics per resource is exceeded.</p> </li> <li> <p> <code>INTERNAL_ERROR</code>: An internal error occured.</p> </li> <li> <p> <code>VALIDATION_ERROR</code>: The metric data failed validation.</p> </li> <li> <p> <code>CONFLICT_ERROR</code>: Multiple requests attempted to modify the same data simultaneously.</p> </li> </ul>"}, "MetricIndex": {"shape": "Integer", "documentation": "<p>An index that corresponds to the metric in the request.</p>"}}, "documentation": "<p>An error that occured when putting the metric data.</p>"}, "BatchPutMetricsErrorList": {"type": "list", "member": {"shape": "BatchPutMetricsError"}, "max": 10, "min": 1}, "BatchPutMetricsRequest": {"type": "structure", "required": ["TrialComponentName", "MetricData"], "members": {"TrialComponentName": {"shape": "ExperimentEntityName", "documentation": "<p>The name of the Trial Component to associate with the metrics.</p>"}, "MetricData": {"shape": "RawMetricDataList", "documentation": "<p>A list of raw metric values to put.</p>"}}}, "BatchPutMetricsResponse": {"type": "structure", "members": {"Errors": {"shape": "BatchPutMetricsErrorList", "documentation": "<p>Lists any errors that occur when inserting metric data.</p>"}}}, "Double": {"type": "double"}, "ExperimentEntityName": {"type": "string", "max": 120, "min": 1, "pattern": "^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,119}"}, "Integer": {"type": "integer"}, "MetricName": {"type": "string", "max": 255, "min": 1, "pattern": ".+"}, "PutMetricsErrorCode": {"type": "string", "enum": ["METRIC_LIMIT_EXCEEDED", "INTERNAL_ERROR", "VALIDATION_ERROR", "CONFLICT_ERROR"]}, "RawMetricData": {"type": "structure", "required": ["MetricName", "Timestamp", "Value"], "members": {"MetricName": {"shape": "MetricName", "documentation": "<p>The name of the metric.</p>"}, "Timestamp": {"shape": "Timestamp", "documentation": "<p>The time that the metric was recorded.</p>"}, "Step": {"shape": "Step", "documentation": "<p>The metric step (epoch). </p>"}, "Value": {"shape": "Double", "documentation": "<p>The metric value.</p>"}}, "documentation": "<p>The raw metric data to associate with the resource.</p>"}, "RawMetricDataList": {"type": "list", "member": {"shape": "RawMetricData"}, "max": 10, "min": 1}, "Step": {"type": "integer", "min": 0}, "Timestamp": {"type": "timestamp"}}, "documentation": "<p>Contains all data plane API operations and data types for Amazon SageMaker Metrics. Use these APIs to put and retrieve (get) features related to your training run.</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/sagemaker/latest/APIReference/API_metrics_BatchPutMetrics.html\">BatchPutMetrics</a> </p> </li> </ul>"}