{"metadata": {"apiVersion": "2020-11-02", "endpointPrefix": "route53-recovery-control-config", "signingName": "route53-recovery-control-config", "serviceFullName": "AWS Route53 Recovery Control Config", "serviceId": "Route53 Recovery Control Config", "protocol": "rest-json", "jsonVersion": "1.1", "uid": "route53-recovery-control-config-2020-11-02", "signatureVersion": "v4"}, "operations": {"CreateCluster": {"name": "CreateCluster", "http": {"method": "POST", "requestUri": "/cluster", "responseCode": 200}, "input": {"shape": "CreateClusterRequest"}, "output": {"shape": "CreateClusterResponse", "documentation": "<p>200 response - Success.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>400 response - Multiple causes. For example, you might have a malformed query string and input parameter might be out of range, or you used parameters together incorrectly.</p>"}, {"shape": "InternalServerException", "documentation": "<p>500 response - InternalServiceError. Temporary service error. Retry the request.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>402 response</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>403 response - AccessDeniedException. You do not have sufficient access to perform this action.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>404 response - MalformedQueryString. The query string contains a syntax error or resource not found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>429 response - LimitExceededException or TooManyRequestsException.</p>"}, {"shape": "ConflictException", "documentation": "<p>409 response - ConflictException. You might be using a predefined variable.</p>"}], "documentation": "<p>Create a new cluster. A cluster is a set of redundant Regional endpoints against which you can run API calls to update or get the state of one or more routing controls. Each cluster has a name, status, Amazon Resource Name (ARN), and an array of the five cluster endpoints (one for each supported Amazon Web Services Region) that you can use with API calls to the cluster data plane.</p>"}, "CreateControlPanel": {"name": "CreateControlPanel", "http": {"method": "POST", "requestUri": "/controlpanel", "responseCode": 200}, "input": {"shape": "CreateControlPanelRequest"}, "output": {"shape": "CreateControlPanelResponse", "documentation": "<p>200 response - Success.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>400 response - Multiple causes. For example, you might have a malformed query string and input parameter might be out of range, or you used parameters together incorrectly.</p>"}, {"shape": "InternalServerException", "documentation": "<p>500 response - InternalServiceError. Temporary service error. Retry the request.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>402 response</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>403 response - AccessDeniedException. You do not have sufficient access to perform this action.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>404 response - MalformedQueryString. The query string contains a syntax error or resource not found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>429 response - LimitExceededException or TooManyRequestsException.</p>"}, {"shape": "ConflictException", "documentation": "<p>409 response - ConflictException. You might be using a predefined variable.</p>"}], "documentation": "<p>Creates a new control panel. A control panel represents a group of routing controls that can be changed together in a single transaction. You can use a control panel to centrally view the operational status of applications across your organization, and trigger multi-app failovers in a single transaction, for example, to fail over an Availability Zone or Amazon Web Services Region.</p>"}, "CreateRoutingControl": {"name": "CreateRoutingControl", "http": {"method": "POST", "requestUri": "/routingcontrol", "responseCode": 200}, "input": {"shape": "CreateRoutingControlRequest"}, "output": {"shape": "CreateRoutingControlResponse", "documentation": "<p>200 response - Success.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>400 response - Multiple causes. For example, you might have a malformed query string and input parameter might be out of range, or you used parameters together incorrectly.</p>"}, {"shape": "InternalServerException", "documentation": "<p>500 response - InternalServiceError. Temporary service error. Retry the request.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>402 response</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>403 response - AccessDeniedException. You do not have sufficient access to perform this action.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>404 response - MalformedQueryString. The query string contains a syntax error or resource not found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>429 response - LimitExceededException or TooManyRequestsException.</p>"}, {"shape": "ConflictException", "documentation": "<p>409 response - ConflictException. You might be using a predefined variable.</p>"}], "documentation": "<p>Creates a new routing control.</p> <p>A routing control has one of two states: ON and OFF. You can map the routing control state to the state of an Amazon Route 53 health check, which can be used to control traffic routing.</p> <p>To get or update the routing control state, see the Recovery Cluster (data plane) API actions for Amazon Route 53 Application Recovery Controller.</p>"}, "CreateSafetyRule": {"name": "CreateSafetyRule", "http": {"method": "POST", "requestUri": "/safetyrule", "responseCode": 200}, "input": {"shape": "CreateSafetyRuleRequest"}, "output": {"shape": "CreateSafetyRuleResponse", "documentation": "<p>200 response - Success.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>400 response - Multiple causes. For example, you might have a malformed query string and input parameter might be out of range, or you used parameters together incorrectly.</p>"}, {"shape": "InternalServerException", "documentation": "<p>500 response - InternalServiceError. Temporary service error. Retry the request.</p>"}], "documentation": "<p>Creates a safety rule in a control panel. Safety rules let you add safeguards around changing routing control states, and for enabling and disabling routing controls, to help prevent unexpected outcomes.</p> <p>There are two types of safety rules: assertion rules and gating rules.</p> <p>Assertion rule: An assertion rule enforces that, when you change a routing control state, that a certain criteria is met. For example, the criteria might be that at least one routing control state is On after the transaction so that traffic continues to flow to at least one cell for the application. This ensures that you avoid a fail-open scenario.</p> <p>Gating rule: A gating rule lets you configure a gating routing control as an overall \"on/off\" switch for a group of routing controls. Or, you can configure more complex gating scenarios, for example by configuring multiple gating routing controls.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/r53recovery/latest/dg/routing-control.safety-rules.html\">Safety rules</a> in the Amazon Route 53 Application Recovery Controller Developer Guide.</p>"}, "DeleteCluster": {"name": "DeleteCluster", "http": {"method": "DELETE", "requestUri": "/cluster/{ClusterArn}", "responseCode": 200}, "input": {"shape": "DeleteClusterRequest"}, "output": {"shape": "DeleteClusterResponse", "documentation": "<p>200 response - Success.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>400 response - Multiple causes. For example, you might have a malformed query string and input parameter might be out of range, or you used parameters together incorrectly.</p>"}, {"shape": "InternalServerException", "documentation": "<p>500 response - InternalServiceError. Temporary service error. Retry the request.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>403 response - AccessDeniedException. You do not have sufficient access to perform this action.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>404 response - MalformedQueryString. The query string contains a syntax error or resource not found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>429 response - LimitExceededException or TooManyRequestsException.</p>"}, {"shape": "ConflictException", "documentation": "<p>409 response - ConflictException. You might be using a predefined variable.</p>"}], "documentation": "<p>Delete a cluster.</p>"}, "DeleteControlPanel": {"name": "DeleteControlPanel", "http": {"method": "DELETE", "requestUri": "/controlpanel/{ControlPanelArn}", "responseCode": 200}, "input": {"shape": "DeleteControlPanelRequest"}, "output": {"shape": "DeleteControlPanelResponse", "documentation": "<p>200 response - Success.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>400 response - Multiple causes. For example, you might have a malformed query string and input parameter might be out of range, or you used parameters together incorrectly.</p>"}, {"shape": "InternalServerException", "documentation": "<p>500 response - InternalServiceError. Temporary service error. Retry the request.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>403 response - AccessDeniedException. You do not have sufficient access to perform this action.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>404 response - MalformedQueryString. The query string contains a syntax error or resource not found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>429 response - LimitExceededException or TooManyRequestsException.</p>"}, {"shape": "ConflictException", "documentation": "<p>409 response - ConflictException. You might be using a predefined variable.</p>"}], "documentation": "<p>Deletes a control panel.</p>"}, "DeleteRoutingControl": {"name": "DeleteRoutingControl", "http": {"method": "DELETE", "requestUri": "/routingcontrol/{RoutingControlArn}", "responseCode": 200}, "input": {"shape": "DeleteRoutingControlRequest"}, "output": {"shape": "DeleteRoutingControlResponse", "documentation": "<p>200 response - Success.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>400 response - Multiple causes. For example, you might have a malformed query string and input parameter might be out of range, or you used parameters together incorrectly.</p>"}, {"shape": "InternalServerException", "documentation": "<p>500 response - InternalServiceError. Temporary service error. Retry the request.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>403 response - AccessDeniedException. You do not have sufficient access to perform this action.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>404 response - MalformedQueryString. The query string contains a syntax error or resource not found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>429 response - LimitExceededException or TooManyRequestsException.</p>"}, {"shape": "ConflictException", "documentation": "<p>409 response - ConflictException. You might be using a predefined variable.</p>"}], "documentation": "<p>Deletes a routing control.</p>"}, "DeleteSafetyRule": {"name": "DeleteSafetyRule", "http": {"method": "DELETE", "requestUri": "/safetyrule/{SafetyRuleArn}", "responseCode": 200}, "input": {"shape": "DeleteSafetyRuleRequest"}, "output": {"shape": "DeleteSafetyRuleResponse", "documentation": "<p>200 response - Success.</p>"}, "errors": [{"shape": "ResourceNotFoundException", "documentation": "<p>404 response - MalformedQueryString. The query string contains a syntax error or resource not found.</p>"}, {"shape": "ValidationException", "documentation": "<p>400 response - Multiple causes. For example, you might have a malformed query string and input parameter might be out of range, or you used parameters together incorrectly.</p>"}, {"shape": "InternalServerException", "documentation": "<p>500 response - InternalServiceError. Temporary service error. Retry the request.</p>"}], "documentation": "<p>Deletes a safety rule.</p>/&gt;"}, "DescribeCluster": {"name": "DescribeCluster", "http": {"method": "GET", "requestUri": "/cluster/{ClusterArn}", "responseCode": 200}, "input": {"shape": "DescribeClusterRequest"}, "output": {"shape": "DescribeClusterResponse", "documentation": "<p>200 response - Success.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>400 response - Multiple causes. For example, you might have a malformed query string and input parameter might be out of range, or you used parameters together incorrectly.</p>"}, {"shape": "InternalServerException", "documentation": "<p>500 response - InternalServiceError. Temporary service error. Retry the request.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>403 response - AccessDeniedException. You do not have sufficient access to perform this action.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>404 response - MalformedQueryString. The query string contains a syntax error or resource not found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>429 response - LimitExceededException or TooManyRequestsException.</p>"}, {"shape": "ConflictException", "documentation": "<p>409 response - ConflictException. You might be using a predefined variable.</p>"}], "documentation": "<p>Display the details about a cluster. The response includes the cluster name, endpoints, status, and Amazon Resource Name (ARN).</p>"}, "DescribeControlPanel": {"name": "DescribeControlPanel", "http": {"method": "GET", "requestUri": "/controlpanel/{ControlPanelArn}", "responseCode": 200}, "input": {"shape": "DescribeControlPanelRequest"}, "output": {"shape": "DescribeControlPanelResponse", "documentation": "<p>200 response - Success.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>400 response - Multiple causes. For example, you might have a malformed query string and input parameter might be out of range, or you used parameters together incorrectly.</p>"}, {"shape": "InternalServerException", "documentation": "<p>500 response - InternalServiceError. Temporary service error. Retry the request.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>403 response - AccessDeniedException. You do not have sufficient access to perform this action.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>404 response - MalformedQueryString. The query string contains a syntax error or resource not found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>429 response - LimitExceededException or TooManyRequestsException.</p>"}, {"shape": "ConflictException", "documentation": "<p>409 response - ConflictException. You might be using a predefined variable.</p>"}], "documentation": "<p>Displays details about a control panel.</p>"}, "DescribeRoutingControl": {"name": "DescribeRoutingControl", "http": {"method": "GET", "requestUri": "/routingcontrol/{RoutingControlArn}", "responseCode": 200}, "input": {"shape": "DescribeRoutingControlRequest"}, "output": {"shape": "DescribeRoutingControlResponse", "documentation": "<p>200 response - Success.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>400 response - Multiple causes. For example, you might have a malformed query string and input parameter might be out of range, or you used parameters together incorrectly.</p>"}, {"shape": "InternalServerException", "documentation": "<p>500 response - InternalServiceError. Temporary service error. Retry the request.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>403 response - AccessDeniedException. You do not have sufficient access to perform this action.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>404 response - MalformedQueryString. The query string contains a syntax error or resource not found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>429 response - LimitExceededException or TooManyRequestsException.</p>"}, {"shape": "ConflictException", "documentation": "<p>409 response - ConflictException. You might be using a predefined variable.</p>"}], "documentation": "<p>Displays details about a routing control. A routing control has one of two states: ON and OFF. You can map the routing control state to the state of an Amazon Route 53 health check, which can be used to control routing.</p> <p>To get or update the routing control state, see the Recovery Cluster (data plane) API actions for Amazon Route 53 Application Recovery Controller.</p>"}, "DescribeSafetyRule": {"name": "DescribeSafetyRule", "http": {"method": "GET", "requestUri": "/safetyrule/{SafetyRuleArn}", "responseCode": 200}, "input": {"shape": "DescribeSafetyRuleRequest"}, "output": {"shape": "DescribeSafetyRuleResponse", "documentation": "<p>200 response - Success.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>400 response - Multiple causes. For example, you might have a malformed query string and input parameter might be out of range, or you used parameters together incorrectly.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>404 response - MalformedQueryString. The query string contains a syntax error or resource not found.</p>"}], "documentation": "<p>Returns information about a safety rule.</p>"}, "GetResourcePolicy": {"name": "GetResourcePolicy", "http": {"method": "GET", "requestUri": "/resourcePolicy/{ResourceArn}", "responseCode": 200}, "input": {"shape": "GetResourcePolicyRequest"}, "output": {"shape": "GetResourcePolicyResponse", "documentation": "<p>200 response - Success.</p>"}, "errors": [{"shape": "ResourceNotFoundException", "documentation": "<p>404 response - MalformedQueryString. The query string contains a syntax error or resource not found.</p>"}, {"shape": "InternalServerException", "documentation": "<p>500 response - InternalServiceError. Temporary service error. Retry the request.</p>"}], "documentation": "<p>Get information about the resource policy for a cluster.</p>"}, "ListAssociatedRoute53HealthChecks": {"name": "ListAssociatedRoute53HealthChecks", "http": {"method": "GET", "requestUri": "/routingcontrol/{RoutingControlArn}/associatedRoute53HealthChecks", "responseCode": 200}, "input": {"shape": "ListAssociatedRoute53HealthChecksRequest"}, "output": {"shape": "ListAssociatedRoute53HealthChecksResponse", "documentation": "<p>200 response - Success.</p>"}, "errors": [{"shape": "ResourceNotFoundException", "documentation": "<p>404 response - MalformedQueryString. The query string contains a syntax error or resource not found.</p>"}, {"shape": "ValidationException", "documentation": "<p>400 response - Multiple causes. For example, you might have a malformed query string and input parameter might be out of range, or you used parameters together incorrectly.</p>"}, {"shape": "InternalServerException", "documentation": "<p>500 response - InternalServiceError. Temporary service error. Retry the request.</p>"}], "documentation": "<p>Returns an array of all Amazon Route 53 health checks associated with a specific routing control.</p>"}, "ListClusters": {"name": "ListClusters", "http": {"method": "GET", "requestUri": "/cluster", "responseCode": 200}, "input": {"shape": "ListClustersRequest"}, "output": {"shape": "ListClustersResponse", "documentation": "<p>200 response - Success.</p>"}, "errors": [{"shape": "ResourceNotFoundException", "documentation": "<p>404 response - MalformedQueryString. The query string contains a syntax error or resource not found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>429 response - LimitExceededException or TooManyRequestsException.</p>"}, {"shape": "ValidationException", "documentation": "<p>400 response - Multiple causes. For example, you might have a malformed query string and input parameter might be out of range, or you used parameters together incorrectly.</p>"}, {"shape": "InternalServerException", "documentation": "<p>500 response - InternalServiceError. Temporary service error. Retry the request.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>403 response - AccessDeniedException. You do not have sufficient access to perform this action.</p>"}], "documentation": "<p>Returns an array of all the clusters in an account.</p>"}, "ListControlPanels": {"name": "ListControlPanels", "http": {"method": "GET", "requestUri": "/controlpanels", "responseCode": 200}, "input": {"shape": "ListControlPanelsRequest"}, "output": {"shape": "ListControlPanelsResponse", "documentation": "<p>200 response - Success.</p>"}, "errors": [{"shape": "ResourceNotFoundException", "documentation": "<p>404 response - MalformedQueryString. The query string contains a syntax error or resource not found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>429 response - LimitExceededException or TooManyRequestsException.</p>"}, {"shape": "ValidationException", "documentation": "<p>400 response - Multiple causes. For example, you might have a malformed query string and input parameter might be out of range, or you used parameters together incorrectly.</p>"}, {"shape": "InternalServerException", "documentation": "<p>500 response - InternalServiceError. Temporary service error. Retry the request.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>403 response - AccessDeniedException. You do not have sufficient access to perform this action.</p>"}], "documentation": "<p>Returns an array of control panels in an account or in a cluster.</p>"}, "ListRoutingControls": {"name": "ListRoutingControls", "http": {"method": "GET", "requestUri": "/controlpanel/{ControlPanelArn}/routingcontrols", "responseCode": 200}, "input": {"shape": "ListRoutingControlsRequest"}, "output": {"shape": "ListRoutingControlsResponse", "documentation": "<p>200 response - Success.</p>"}, "errors": [{"shape": "ResourceNotFoundException", "documentation": "<p>404 response - MalformedQueryString. The query string contains a syntax error or resource not found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>429 response - LimitExceededException or TooManyRequestsException.</p>"}, {"shape": "ValidationException", "documentation": "<p>400 response - Multiple causes. For example, you might have a malformed query string and input parameter might be out of range, or you used parameters together incorrectly.</p>"}, {"shape": "InternalServerException", "documentation": "<p>500 response - InternalServiceError. Temporary service error. Retry the request.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>403 response - AccessDeniedException. You do not have sufficient access to perform this action.</p>"}], "documentation": "<p>Returns an array of routing controls for a control panel. A routing control is an Amazon Route 53 Application Recovery Controller construct that has one of two states: ON and OFF. You can map the routing control state to the state of an Amazon Route 53 health check, which can be used to control routing.</p>"}, "ListSafetyRules": {"name": "ListSafetyRules", "http": {"method": "GET", "requestUri": "/controlpanel/{ControlPanelArn}/safetyrules", "responseCode": 200}, "input": {"shape": "ListSafetyRulesRequest"}, "output": {"shape": "ListSafetyRulesResponse", "documentation": "<p>200 response - Success.</p>"}, "errors": [{"shape": "ResourceNotFoundException", "documentation": "<p>404 response - MalformedQueryString. The query string contains a syntax error or resource not found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>429 response - LimitExceededException or TooManyRequestsException.</p>"}, {"shape": "ValidationException", "documentation": "<p>400 response - Multiple causes. For example, you might have a malformed query string and input parameter might be out of range, or you used parameters together incorrectly.</p>"}, {"shape": "InternalServerException", "documentation": "<p>500 response - InternalServiceError. Temporary service error. Retry the request.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>403 response - AccessDeniedException. You do not have sufficient access to perform this action.</p>"}], "documentation": "<p>List the safety rules (the assertion rules and gating rules) that you've defined for the routing controls in a control panel.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{ResourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse", "documentation": "<p>200 response - Success.</p>"}, "errors": [{"shape": "ResourceNotFoundException", "documentation": "<p>404 response - MalformedQueryString. The query string contains a syntax error or resource not found.</p>"}, {"shape": "ValidationException", "documentation": "<p>400 response - Multiple causes. For example, you might have a malformed query string and input parameter might be out of range, or you used parameters together incorrectly.</p>"}, {"shape": "InternalServerException", "documentation": "<p>500 response - InternalServiceError. Temporary service error. Retry the request.</p>"}], "documentation": "<p>Lists the tags for a resource.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{ResourceArn}", "responseCode": 200}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse", "documentation": "<p>200 response - Success.</p>"}, "errors": [{"shape": "ResourceNotFoundException", "documentation": "<p>404 response - MalformedQueryString. The query string contains a syntax error or resource not found.</p>"}, {"shape": "ValidationException", "documentation": "<p>400 response - Multiple causes. For example, you might have a malformed query string and input parameter might be out of range, or you used parameters together incorrectly.</p>"}, {"shape": "InternalServerException", "documentation": "<p>500 response - InternalServiceError. Temporary service error. Retry the request.</p>"}], "documentation": "<p>Adds a tag to a resource.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{ResourceArn}", "responseCode": 200}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse", "documentation": "<p>200 response - Success.</p>"}, "errors": [{"shape": "ResourceNotFoundException", "documentation": "<p>404 response - MalformedQueryString. The query string contains a syntax error or resource not found.</p>"}, {"shape": "ValidationException", "documentation": "<p>400 response - Multiple causes. For example, you might have a malformed query string and input parameter might be out of range, or you used parameters together incorrectly.</p>"}, {"shape": "InternalServerException", "documentation": "<p>500 response - InternalServiceError. Temporary service error. Retry the request.</p>"}], "documentation": "<p>Removes a tag from a resource.</p>"}, "UpdateControlPanel": {"name": "UpdateControlPanel", "http": {"method": "PUT", "requestUri": "/controlpanel", "responseCode": 200}, "input": {"shape": "UpdateControlPanelRequest"}, "output": {"shape": "UpdateControlPanelResponse", "documentation": "<p>200 response - Success.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>400 response - Multiple causes. For example, you might have a malformed query string and input parameter might be out of range, or you used parameters together incorrectly.</p>"}, {"shape": "InternalServerException", "documentation": "<p>500 response - InternalServiceError. Temporary service error. Retry the request.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>403 response - AccessDeniedException. You do not have sufficient access to perform this action.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>404 response - MalformedQueryString. The query string contains a syntax error or resource not found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>429 response - LimitExceededException or TooManyRequestsException.</p>"}, {"shape": "ConflictException", "documentation": "<p>409 response - ConflictException. You might be using a predefined variable.</p>"}], "documentation": "<p>Updates a control panel. The only update you can make to a control panel is to change the name of the control panel.</p>"}, "UpdateRoutingControl": {"name": "UpdateRoutingControl", "http": {"method": "PUT", "requestUri": "/routingcontrol", "responseCode": 200}, "input": {"shape": "UpdateRoutingControlRequest"}, "output": {"shape": "UpdateRoutingControlResponse", "documentation": "<p>200 response - Success.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>400 response - Multiple causes. For example, you might have a malformed query string and input parameter might be out of range, or you used parameters together incorrectly.</p>"}, {"shape": "InternalServerException", "documentation": "<p>500 response - InternalServiceError. Temporary service error. Retry the request.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>403 response - AccessDeniedException. You do not have sufficient access to perform this action.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>404 response - MalformedQueryString. The query string contains a syntax error or resource not found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>429 response - LimitExceededException or TooManyRequestsException.</p>"}, {"shape": "ConflictException", "documentation": "<p>409 response - ConflictException. You might be using a predefined variable.</p>"}], "documentation": "<p>Updates a routing control. You can only update the name of the routing control. To get or update the routing control state, see the Recovery Cluster (data plane) API actions for Amazon Route 53 Application Recovery Controller.</p>"}, "UpdateSafetyRule": {"name": "UpdateSafetyRule", "http": {"method": "PUT", "requestUri": "/safetyrule", "responseCode": 200}, "input": {"shape": "UpdateSafetyRuleRequest"}, "output": {"shape": "UpdateSafetyRuleResponse", "documentation": "<p>200 response - Success.</p>"}, "errors": [{"shape": "ResourceNotFoundException", "documentation": "<p>404 response - MalformedQueryString. The query string contains a syntax error or resource not found.</p>"}, {"shape": "ValidationException", "documentation": "<p>400 response - Multiple causes. For example, you might have a malformed query string and input parameter might be out of range, or you used parameters together incorrectly.</p>"}, {"shape": "InternalServerException", "documentation": "<p>500 response - InternalServiceError. Temporary service error. Retry the request.</p>"}], "documentation": "<p>Update a safety rule (an assertion rule or gating rule). You can only update the name and the waiting period for a safety rule. To make other updates, delete the safety rule and create a new one.</p>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"Message": {"shape": "__string", "locationName": "message"}}, "documentation": "<p>403 response - You do not have sufficient access to perform this action.</p>", "required": ["Message"], "exception": true, "error": {"httpStatusCode": 403}}, "AssertionRule": {"type": "structure", "members": {"AssertedControls": {"shape": "__listOf__stringMin1Max256PatternAZaZ09", "documentation": "<p>The routing controls that are part of transactions that are evaluated to determine if a request to change a routing control state is allowed. For example, you might include three routing controls, one for each of three Amazon Web Services Regions.</p>"}, "ControlPanelArn": {"shape": "__stringMin1Max256PatternAZaZ09", "documentation": "<p>The Amazon Resource Name (ARN) of the control panel.</p>"}, "Name": {"shape": "__stringMin1Max64PatternS", "documentation": "<p>Name of the assertion rule. You can use any non-white space character in the name.</p>"}, "RuleConfig": {"shape": "RuleConfig", "documentation": "<p>The criteria that you set for specific assertion routing controls (AssertedControls) that designate how many routing control states must be ON as the result of a transaction. For example, if you have three assertion routing controls, you might specify ATLEAST 2 for your rule configuration. This means that at least two assertion routing control states must be ON, so that at least two Amazon Web Services Regions have traffic flowing to them.</p>"}, "SafetyRuleArn": {"shape": "__stringMin1Max256PatternAZaZ09", "documentation": "<p>The Amazon Resource Name (ARN) of the assertion rule.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The deployment status of an assertion rule. Status can be one of the following: PENDING, DEPLOYED, PENDING_DELETION.</p>"}, "WaitPeriodMs": {"shape": "__integer", "documentation": "<p>An evaluation period, in milliseconds (ms), during which any request against the target routing controls will fail. This helps prevent \"flapping\" of state. The wait period is 5000 ms by default, but you can choose a custom value.</p>"}, "Owner": {"shape": "__stringMin12Max12PatternD12", "documentation": "<p>The Amazon Web Services account ID of the assertion rule owner.</p>"}}, "documentation": "<p>An assertion rule enforces that, when you change a routing control state, that the criteria that you set in the rule configuration is met. Otherwise, the change to the routing control is not accepted. For example, the criteria might be that at least one routing control state is On after the transaction so that traffic continues to flow to at least one cell for the application. This ensures that you avoid a fail-open scenario.</p>", "required": ["Status", "ControlPanelArn", "SafetyRuleArn", "AssertedControls", "RuleConfig", "WaitPeriodMs", "Name"]}, "AssertionRuleUpdate": {"type": "structure", "members": {"Name": {"shape": "__stringMin1Max64PatternS", "documentation": "<p>The name of the assertion rule. You can use any non-white space character in the name.</p>"}, "SafetyRuleArn": {"shape": "__stringMin1Max256PatternAZaZ09", "documentation": "<p>The Amazon Resource Name (ARN) of the assertion rule.</p>"}, "WaitPeriodMs": {"shape": "__integer", "documentation": "<p>An evaluation period, in milliseconds (ms), during which any request against the target routing controls will fail. This helps prevent \"flapping\" of state. The wait period is 5000 ms by default, but you can choose a custom value.</p>"}}, "documentation": "<p>An update to an assertion rule. You can update the name or the evaluation period (wait period). If you don't specify one of the items to update, the item is unchanged.</p>", "required": ["SafetyRuleArn", "WaitPeriodMs", "Name"]}, "Cluster": {"type": "structure", "members": {"ClusterArn": {"shape": "__stringMin1Max256PatternAZaZ09", "documentation": "<p>The Amazon Resource Name (ARN) of the cluster.</p>"}, "ClusterEndpoints": {"shape": "__listOfClusterEndpoint", "documentation": "<p>Endpoints for a cluster. Specify one of these endpoints when you want to set or retrieve a routing control state in the cluster.</p> <p>To get or update the routing control state, see the Amazon Route 53 Application Recovery Controller Routing Control Actions.</p>"}, "Name": {"shape": "__stringMin1Max64PatternS", "documentation": "<p>The name of the cluster.</p>"}, "Status": {"shape": "Status", "documentation": "<p>Deployment status of a resource. Status can be one of the following: PENDING, DEPLOYED, PENDING_DELETION.</p>"}, "Owner": {"shape": "__stringMin12Max12PatternD12", "documentation": "<p>The Amazon Web Services account ID of the cluster owner.</p>"}}, "documentation": "<p>A set of five redundant Regional endpoints against which you can execute API calls to update or get the state of routing controls. You can host multiple control panels and routing controls on one cluster.</p>"}, "ClusterEndpoint": {"type": "structure", "members": {"Endpoint": {"shape": "__stringMin1Max128PatternAZaZ09", "documentation": "<p>A cluster endpoint. Specify an endpoint and Amazon Web Services Region when you want to set or retrieve a routing control state in the cluster.</p> <p>To get or update the routing control state, see the Amazon Route 53 Application Recovery Controller Routing Control Actions.</p>"}, "Region": {"shape": "__stringMin1Max32PatternS", "documentation": "<p>The Amazon Web Services Region for a cluster endpoint.</p>"}}, "documentation": "<p>A cluster endpoint. Specify an endpoint when you want to set or retrieve a routing control state in the cluster.</p>"}, "ConflictException": {"type": "structure", "members": {"Message": {"shape": "__string", "locationName": "message"}}, "documentation": "<p>409 response - ConflictException. You might be using a predefined variable.</p>", "required": ["Message"], "exception": true, "error": {"httpStatusCode": 409}}, "ControlPanel": {"type": "structure", "members": {"ClusterArn": {"shape": "__stringMin1Max256PatternAZaZ09", "documentation": "<p>The Amazon Resource Name (ARN) of the cluster that includes the control panel.</p>"}, "ControlPanelArn": {"shape": "__stringMin1Max256PatternAZaZ09", "documentation": "<p>The Amazon Resource Name (ARN) of the control panel.</p>"}, "DefaultControlPanel": {"shape": "__boolean", "documentation": "<p>A flag that Amazon Route 53 Application Recovery Controller sets to true to designate the default control panel for a cluster. When you create a cluster, Amazon Route 53 Application Recovery Controller creates a control panel, and sets this flag for that control panel. If you create a control panel yourself, this flag is set to false.</p>"}, "Name": {"shape": "__stringMin1Max64PatternS", "documentation": "<p>The name of the control panel. You can use any non-white space character in the name.</p>"}, "RoutingControlCount": {"shape": "__integer", "documentation": "<p>The number of routing controls in the control panel.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The deployment status of control panel. Status can be one of the following: PENDING, DEPLOYED, PENDING_DELETION.</p>"}, "Owner": {"shape": "__stringMin12Max12PatternD12", "documentation": "<p>The Amazon Web Services account ID of the control panel owner.</p>"}}, "documentation": "<p>A control panel represents a group of routing controls that can be changed together in a single transaction.</p>"}, "CreateClusterRequest": {"type": "structure", "members": {"ClientToken": {"shape": "__stringMin1Max64PatternS", "documentation": "<p>A unique, case-sensitive string of up to 64 ASCII characters. To make an idempotent API request with an action, specify a client token in the request.</p>", "idempotencyToken": true}, "ClusterName": {"shape": "__stringMin1Max64PatternS", "documentation": "<p>The name of the cluster.</p>"}, "Tags": {"shape": "__mapOf__stringMin0Max256PatternS", "documentation": "<p>The tags associated with the cluster.</p>"}}, "documentation": "<p>Creates a cluster.</p>", "required": ["ClusterName"]}, "CreateClusterResponse": {"type": "structure", "members": {"Cluster": {"shape": "Cluster", "documentation": "<p>The cluster that was created.</p>"}}}, "CreateControlPanelRequest": {"type": "structure", "members": {"ClientToken": {"shape": "__stringMin1Max64PatternS", "documentation": "<p>A unique, case-sensitive string of up to 64 ASCII characters. To make an idempotent API request with an action, specify a client token in the request.</p>", "idempotencyToken": true}, "ClusterArn": {"shape": "__stringMin1Max256PatternAZaZ09", "documentation": "<p>The Amazon Resource Name (ARN) of the cluster for the control panel.</p>"}, "ControlPanelName": {"shape": "__stringMin1Max64PatternS", "documentation": "<p>The name of the control panel.</p>"}, "Tags": {"shape": "__mapOf__stringMin0Max256PatternS", "documentation": "<p>The tags associated with the control panel.</p>"}}, "documentation": "<p>The details of the control panel that you're creating.</p>", "required": ["ClusterArn", "ControlPanelName"]}, "CreateControlPanelResponse": {"type": "structure", "members": {"ControlPanel": {"shape": "ControlPanel", "documentation": "<p>Information about a control panel.</p>"}}}, "CreateRoutingControlRequest": {"type": "structure", "members": {"ClientToken": {"shape": "__stringMin1Max64PatternS", "documentation": "<p>A unique, case-sensitive string of up to 64 ASCII characters. To make an idempotent API request with an action, specify a client token in the request.</p>", "idempotencyToken": true}, "ClusterArn": {"shape": "__stringMin1Max256PatternAZaZ09", "documentation": "<p>The Amazon Resource Name (ARN) of the cluster that includes the routing control.</p>"}, "ControlPanelArn": {"shape": "__stringMin1Max256PatternAZaZ09", "documentation": "<p>The Amazon Resource Name (ARN) of the control panel that includes the routing control.</p>"}, "RoutingControlName": {"shape": "__stringMin1Max64PatternS", "documentation": "<p>The name of the routing control.</p>"}}, "documentation": "<p>The details of the routing control that you're creating.</p>", "required": ["ClusterArn", "RoutingControlName"]}, "CreateRoutingControlResponse": {"type": "structure", "members": {"RoutingControl": {"shape": "RoutingControl", "documentation": "<p>The routing control that is created.</p>"}}}, "CreateSafetyRuleRequest": {"type": "structure", "members": {"AssertionRule": {"shape": "NewAssertionRule", "documentation": "<p>The assertion rule requested.</p>"}, "ClientToken": {"shape": "__stringMin1Max64PatternS", "documentation": "<p>A unique, case-sensitive string of up to 64 ASCII characters. To make an idempotent API request with an action, specify a client token in the request.</p>", "idempotencyToken": true}, "GatingRule": {"shape": "NewGatingRule", "documentation": "<p>The gating rule requested.</p>"}, "Tags": {"shape": "__mapOf__stringMin0Max256PatternS", "documentation": "<p>The tags associated with the safety rule.</p>"}}, "documentation": "<p>The request body that you include when you create a safety rule.</p>"}, "CreateSafetyRuleResponse": {"type": "structure", "members": {"AssertionRule": {"shape": "AssertionRule", "documentation": "<p>The assertion rule created.</p>"}, "GatingRule": {"shape": "GatingRule", "documentation": "<p>The gating rule created.</p>"}}}, "DeleteClusterRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "ClusterArn", "documentation": "<p>The Amazon Resource Name (ARN) of the cluster that you're deleting.</p>"}}, "required": ["ClusterArn"]}, "DeleteClusterResponse": {"type": "structure", "members": {}}, "DeleteControlPanelRequest": {"type": "structure", "members": {"ControlPanelArn": {"shape": "__string", "location": "uri", "locationName": "ControlPanelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the control panel.</p>"}}, "required": ["ControlPanelArn"]}, "DeleteControlPanelResponse": {"type": "structure", "members": {}}, "DeleteRoutingControlRequest": {"type": "structure", "members": {"RoutingControlArn": {"shape": "__string", "location": "uri", "locationName": "RoutingControlArn", "documentation": "<p>The Amazon Resource Name (ARN) of the routing control that you're deleting.</p>"}}, "required": ["RoutingControlArn"]}, "DeleteRoutingControlResponse": {"type": "structure", "members": {}}, "DeleteSafetyRuleRequest": {"type": "structure", "members": {"SafetyRuleArn": {"shape": "__string", "location": "uri", "locationName": "SafetyRuleArn", "documentation": "<p>The ARN of the safety rule.</p>"}}, "required": ["SafetyRuleArn"]}, "DeleteSafetyRuleResponse": {"type": "structure", "members": {}}, "DescribeClusterRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "ClusterArn", "documentation": "<p>The Amazon Resource Name (ARN) of the cluster.</p>"}}, "required": ["ClusterArn"]}, "DescribeClusterResponse": {"type": "structure", "members": {"Cluster": {"shape": "Cluster", "documentation": "<p>The cluster for the DescribeCluster request.</p>"}}}, "DescribeControlPanelRequest": {"type": "structure", "members": {"ControlPanelArn": {"shape": "__string", "location": "uri", "locationName": "ControlPanelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the control panel.</p>"}}, "required": ["ControlPanelArn"]}, "DescribeControlPanelResponse": {"type": "structure", "members": {"ControlPanel": {"shape": "ControlPanel", "documentation": "<p>Information about the control panel.</p>"}}}, "DescribeRoutingControlRequest": {"type": "structure", "members": {"RoutingControlArn": {"shape": "__string", "location": "uri", "locationName": "RoutingControlArn", "documentation": "<p>The Amazon Resource Name (ARN) of the routing control.</p>"}}, "required": ["RoutingControlArn"]}, "DescribeRoutingControlResponse": {"type": "structure", "members": {"RoutingControl": {"shape": "RoutingControl", "documentation": "<p>Information about the routing control.</p>"}}}, "DescribeSafetyRuleRequest": {"type": "structure", "members": {"SafetyRuleArn": {"shape": "__string", "location": "uri", "locationName": "SafetyRuleArn", "documentation": "<p>The ARN of the safety rule.</p>"}}, "required": ["SafetyRuleArn"]}, "DescribeSafetyRuleResponse": {"type": "structure", "members": {"AssertionRule": {"shape": "AssertionRule", "documentation": "<p>The assertion rule in the response.</p>"}, "GatingRule": {"shape": "GatingRule", "documentation": "<p>The gating rule in the response.</p>"}}}, "GatingRule": {"type": "structure", "members": {"ControlPanelArn": {"shape": "__stringMin1Max256PatternAZaZ09", "documentation": "<p>The Amazon Resource Name (ARN) of the control panel.</p>"}, "GatingControls": {"shape": "__listOf__stringMin1Max256PatternAZaZ09", "documentation": "<p>An array of gating routing control Amazon Resource Names (ARNs). For a simple \"on/off\" switch, specify the ARN for one routing control. The gating routing controls are evaluated by the rule configuration that you specify to determine if the target routing control states can be changed.</p>"}, "Name": {"shape": "__stringMin1Max64PatternS", "documentation": "<p>The name for the gating rule. You can use any non-white space character in the name.</p>"}, "RuleConfig": {"shape": "RuleConfig", "documentation": "<p>The criteria that you set for gating routing controls that designate how many of the routing control states must be ON to allow you to update target routing control states.</p>"}, "SafetyRuleArn": {"shape": "__stringMin1Max256PatternAZaZ09", "documentation": "<p>The Amazon Resource Name (ARN) of the gating rule.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The deployment status of a gating rule. Status can be one of the following: PENDING, DEPLOYED, PENDING_DELETION.</p>"}, "TargetControls": {"shape": "__listOf__stringMin1Max256PatternAZaZ09", "documentation": "<p>An array of target routing control Amazon Resource Names (ARNs) for which the states can only be updated if the rule configuration that you specify evaluates to true for the gating routing control. As a simple example, if you have a single gating control, it acts as an overall \"on/off\" switch for a set of target routing controls. You can use this to manually override automated failover, for example.</p>"}, "WaitPeriodMs": {"shape": "__integer", "documentation": "<p>An evaluation period, in milliseconds (ms), during which any request against the target routing controls will fail. This helps prevent \"flapping\" of state. The wait period is 5000 ms by default, but you can choose a custom value.</p>"}, "Owner": {"shape": "__stringMin12Max12PatternD12", "documentation": "<p>The Amazon Web Services account ID of the gating rule owner.</p>"}}, "documentation": "<p>A gating rule verifies that a gating routing control or set of gating routing controls, evaluates as true, based on a rule configuration that you specify, which allows a set of routing control state changes to complete.</p> <p>For example, if you specify one gating routing control and you set the Type in the rule configuration to OR, that indicates that you must set the gating routing control to On for the rule to evaluate as true; that is, for the gating control \"switch\" to be \"On\". When you do that, then you can update the routing control states for the target routing controls that you specify in the gating rule.</p>", "required": ["Status", "TargetControls", "ControlPanelArn", "SafetyRuleArn", "GatingControls", "RuleConfig", "WaitPeriodMs", "Name"]}, "GatingRuleUpdate": {"type": "structure", "members": {"Name": {"shape": "__stringMin1Max64PatternS", "documentation": "<p>The name for the gating rule. You can use any non-white space character in the name.</p>"}, "SafetyRuleArn": {"shape": "__stringMin1Max256PatternAZaZ09", "documentation": "<p>The Amazon Resource Name (ARN) of the gating rule.</p>"}, "WaitPeriodMs": {"shape": "__integer", "documentation": "<p>An evaluation period, in milliseconds (ms), during which any request against the target routing controls will fail. This helps prevent \"flapping\" of state. The wait period is 5000 ms by default, but you can choose a custom value.</p>"}}, "documentation": "<p>Update to a gating rule. You can update the name or the evaluation period (wait period). If you don't specify one of the items to update, the item is unchanged.</p>", "required": ["SafetyRuleArn", "WaitPeriodMs", "Name"]}, "GetResourcePolicyRequest": {"type": "structure", "members": {"ResourceArn": {"shape": "__string", "location": "uri", "locationName": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>"}}, "required": ["ResourceArn"]}, "GetResourcePolicyResponse": {"type": "structure", "members": {"Policy": {"shape": "__policy", "documentation": "<p>The resource policy.</p>"}}}, "InternalServerException": {"type": "structure", "members": {"Message": {"shape": "__string", "locationName": "message"}}, "documentation": "<p>500 response - InternalServiceError. Temporary service error. Retry the request.</p>", "required": ["Message"], "exception": true, "error": {"httpStatusCode": 500}}, "ListAssociatedRoute53HealthChecksRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "MaxResults", "documentation": "<p>The number of objects that you want to return with this call.</p>"}, "NextToken": {"shape": "__string", "location": "querystring", "locationName": "NextToken", "documentation": "<p>The token that identifies which batch of results you want to see.</p>"}, "RoutingControlArn": {"shape": "__string", "location": "uri", "locationName": "RoutingControlArn", "documentation": "<p>The Amazon Resource Name (ARN) of the routing control.</p>"}}, "required": ["RoutingControlArn"]}, "ListAssociatedRoute53HealthChecksResponse": {"type": "structure", "members": {"HealthCheckIds": {"shape": "__listOf__stringMax36PatternS", "documentation": "<p>Identifiers for the health checks.</p>"}, "NextToken": {"shape": "__stringMin1Max8096PatternS", "documentation": "<p>Next token for listing health checks.</p>"}}}, "ListClustersRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "MaxResults", "documentation": "<p>The number of objects that you want to return with this call.</p>"}, "NextToken": {"shape": "__string", "location": "querystring", "locationName": "NextToken", "documentation": "<p>The token that identifies which batch of results you want to see.</p>"}}}, "ListClustersResponse": {"type": "structure", "members": {"Clusters": {"shape": "__listOfCluster", "documentation": "<p>An array of the clusters in an account.</p>"}, "NextToken": {"shape": "__stringMin1Max8096PatternS", "documentation": "<p>The token that identifies which batch of results you want to see.</p>"}}}, "ListControlPanelsRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "querystring", "locationName": "ClusterArn", "documentation": "<p>The Amazon Resource Name (ARN) of a cluster.</p>"}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "MaxResults", "documentation": "<p>The number of objects that you want to return with this call.</p>"}, "NextToken": {"shape": "__string", "location": "querystring", "locationName": "NextToken", "documentation": "<p>The token that identifies which batch of results you want to see.</p>"}}}, "ListControlPanelsResponse": {"type": "structure", "members": {"ControlPanels": {"shape": "__listOfControlPanel", "documentation": "<p>The result of a successful ListControlPanel request.</p>"}, "NextToken": {"shape": "__stringMin1Max8096PatternS", "documentation": "<p>The token that identifies which batch of results you want to see.</p>"}}}, "ListRoutingControlsRequest": {"type": "structure", "members": {"ControlPanelArn": {"shape": "__string", "location": "uri", "locationName": "ControlPanelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the control panel.</p>"}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "MaxResults", "documentation": "<p>The number of objects that you want to return with this call.</p>"}, "NextToken": {"shape": "__string", "location": "querystring", "locationName": "NextToken", "documentation": "<p>The token that identifies which batch of results you want to see.</p>"}}, "required": ["ControlPanelArn"]}, "ListRoutingControlsResponse": {"type": "structure", "members": {"NextToken": {"shape": "__stringMin1Max8096PatternS", "documentation": "<p>The token that identifies which batch of results you want to see.</p>"}, "RoutingControls": {"shape": "__listOfRoutingControl", "documentation": "<p>An array of routing controls.</p>"}}}, "ListSafetyRulesRequest": {"type": "structure", "members": {"ControlPanelArn": {"shape": "__string", "location": "uri", "locationName": "ControlPanelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the control panel.</p>"}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "MaxResults", "documentation": "<p>The number of objects that you want to return with this call.</p>"}, "NextToken": {"shape": "__string", "location": "querystring", "locationName": "NextToken", "documentation": "<p>The token that identifies which batch of results you want to see.</p>"}}, "required": ["ControlPanelArn"]}, "ListSafetyRulesResponse": {"type": "structure", "members": {"NextToken": {"shape": "__stringMin1Max8096PatternS", "documentation": "<p>The token that identifies which batch of results you want to see.</p>"}, "SafetyRules": {"shape": "__listOfRule", "documentation": "<p>The list of safety rules in a control panel.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "members": {"ResourceArn": {"shape": "__string", "location": "uri", "locationName": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) for the resource that's tagged.</p>"}}, "required": ["ResourceArn"]}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "__mapOf__stringMin0Max256PatternS", "documentation": "<p>The tags associated with the resource.</p>"}}}, "MaxResults": {"type": "integer", "min": 1, "max": 1000}, "NewAssertionRule": {"type": "structure", "members": {"AssertedControls": {"shape": "__listOf__stringMin1Max256PatternAZaZ09", "documentation": "<p>The routing controls that are part of transactions that are evaluated to determine if a request to change a routing control state is allowed. For example, you might include three routing controls, one for each of three Amazon Web Services Regions.</p>"}, "ControlPanelArn": {"shape": "__stringMin1Max256PatternAZaZ09", "documentation": "<p>The Amazon Resource Name (ARN) for the control panel.</p>"}, "Name": {"shape": "__stringMin1Max64PatternS", "documentation": "<p>The name of the assertion rule. You can use any non-white space character in the name.</p>"}, "RuleConfig": {"shape": "RuleConfig", "documentation": "<p>The criteria that you set for specific assertion controls (routing controls) that designate how many control states must be ON as the result of a transaction. For example, if you have three assertion controls, you might specify ATLEAST 2 for your rule configuration. This means that at least two assertion controls must be ON, so that at least two Amazon Web Services Regions have traffic flowing to them.</p>"}, "WaitPeriodMs": {"shape": "__integer", "documentation": "<p>An evaluation period, in milliseconds (ms), during which any request against the target routing controls will fail. This helps prevent \"flapping\" of state. The wait period is 5000 ms by default, but you can choose a custom value.</p>"}}, "documentation": "<p>A new assertion rule for a control panel.</p>", "required": ["ControlPanelArn", "AssertedControls", "RuleConfig", "WaitPeriodMs", "Name"]}, "NewGatingRule": {"type": "structure", "members": {"ControlPanelArn": {"shape": "__stringMin1Max256PatternAZaZ09", "documentation": "<p>The Amazon Resource Name (ARN) of the control panel.</p>"}, "GatingControls": {"shape": "__listOf__stringMin1Max256PatternAZaZ09", "documentation": "<p>The gating controls for the new gating rule. That is, routing controls that are evaluated by the rule configuration that you specify.</p>"}, "Name": {"shape": "__stringMin1Max64PatternS", "documentation": "<p>The name for the new gating rule.</p>"}, "RuleConfig": {"shape": "RuleConfig", "documentation": "<p>The criteria that you set for specific gating controls (routing controls) that designate how many control states must be ON to allow you to change (set or unset) the target control states.</p>"}, "TargetControls": {"shape": "__listOf__stringMin1Max256PatternAZaZ09", "documentation": "<p>Routing controls that can only be set or unset if the specified RuleConfig evaluates to true for the specified GatingControls. For example, say you have three gating controls, one for each of three Amazon Web Services Regions. Now you specify ATLEAST 2 as your RuleConfig. With these settings, you can only change (set or unset) the routing controls that you have specified as TargetControls if that rule evaluates to true.</p> <p>In other words, your ability to change the routing controls that you have specified as TargetControls is gated by the rule that you set for the routing controls in GatingControls.</p>"}, "WaitPeriodMs": {"shape": "__integer", "documentation": "<p>An evaluation period, in milliseconds (ms), during which any request against the target routing controls will fail. This helps prevent \"flapping\" of state. The wait period is 5000 ms by default, but you can choose a custom value.</p>"}}, "documentation": "<p>A new gating rule for a control panel.</p>", "required": ["TargetControls", "ControlPanelArn", "GatingControls", "RuleConfig", "WaitPeriodMs", "Name"]}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "__string", "locationName": "message"}}, "documentation": "<p>404 response - MalformedQueryString. The query string contains a syntax error or resource not found.</p>", "required": ["Message"], "exception": true, "error": {"httpStatusCode": 404}}, "RoutingControl": {"type": "structure", "members": {"ControlPanelArn": {"shape": "__stringMin1Max256PatternAZaZ09", "documentation": "<p>The Amazon Resource Name (ARN) of the control panel that includes the routing control.</p>"}, "Name": {"shape": "__stringMin1Max64PatternS", "documentation": "<p>The name of the routing control.</p>"}, "RoutingControlArn": {"shape": "__stringMin1Max256PatternAZaZ09", "documentation": "<p>The Amazon Resource Name (ARN) of the routing control.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The deployment status of a routing control. Status can be one of the following: PENDING, DEPLOYED, PENDING_DELETION.</p>"}, "Owner": {"shape": "__stringMin12Max12PatternD12", "documentation": "<p>The Amazon Web Services account ID of the routing control owner.</p>"}}, "documentation": "<p>A routing control has one of two states: ON and OFF. You can map the routing control state to the state of an Amazon Route 53 health check, which can be used to control traffic routing.</p>"}, "Rule": {"type": "structure", "members": {"ASSERTION": {"shape": "AssertionRule", "documentation": "<p>An assertion rule enforces that, when a routing control state is changed, the criteria set by the rule configuration is met. Otherwise, the change to the routing control state is not accepted. For example, the criteria might be that at least one routing control state is On after the transaction so that traffic continues to flow to at least one cell for the application. This ensures that you avoid a fail-open scenario.</p>"}, "GATING": {"shape": "GatingRule", "documentation": "<p>A gating rule verifies that a gating routing control or set of gating routing controls, evaluates as true, based on a rule configuration that you specify, which allows a set of routing control state changes to complete.</p> <p>For example, if you specify one gating routing control and you set the Type in the rule configuration to OR, that indicates that you must set the gating routing control to On for the rule to evaluate as true; that is, for the gating control \"switch\" to be \"On\". When you do that, then you can update the routing control states for the target routing controls that you specify in the gating rule.</p>"}}, "documentation": "<p>A safety rule. A safety rule can be an assertion rule or a gating rule.</p>"}, "RuleConfig": {"type": "structure", "members": {"Inverted": {"shape": "__boolean", "documentation": "<p>Logical negation of the rule. If the rule would usually evaluate true, it's evaluated as false, and vice versa.</p>"}, "Threshold": {"shape": "__integer", "documentation": "<p>The value of N, when you specify an ATLEAST rule type. That is, Threshold is the number of controls that must be set when you specify an ATLEAST type.</p>"}, "Type": {"shape": "RuleType", "documentation": "<p>A rule can be one of the following: ATLEAST, AND, or OR.</p>"}}, "documentation": "<p>The rule configuration for an assertion rule. That is, the criteria that you set for specific assertion controls (routing controls) that specify how many control states must be ON after a transaction completes.</p>", "required": ["Type", "Inverted", "<PERSON><PERSON><PERSON><PERSON>"]}, "RuleType": {"type": "string", "documentation": "<p>An enumerated type that determines how the evaluated rules are processed. RuleType can be one of the following:</p> <p>ATLEAST - At least N routing controls must be set. You specify N as the Threshold in the rule configuration.</p> <p>AND - All routing controls must be set. This is a shortcut for \"At least N,\" where N is the total number of controls in the rule.</p> <p>OR - Any control must be set. This is a shortcut for \"At least N,\" where N is 1.</p>", "enum": ["ATLEAST", "AND", "OR"]}, "ServiceQuotaExceededException": {"type": "structure", "members": {"Message": {"shape": "__string", "locationName": "message"}}, "documentation": "<p>402 response - You attempted to create more resources than the service allows based on service quotas.</p>", "required": ["Message"], "exception": true, "error": {"httpStatusCode": 402}}, "Status": {"type": "string", "documentation": "<p>The deployment status of a resource. Status can be one of the following:</p> <p>PENDING: Amazon Route 53 Application Recovery Controller is creating the resource.</p> <p>DEPLOYED: The resource is deployed and ready to use.</p> <p>PENDING_DELETION: Amazon Route 53 Application Recovery Controller is deleting the resource.</p>", "enum": ["PENDING", "DEPLOYED", "PENDING_DELETION"]}, "TagResourceRequest": {"type": "structure", "members": {"ResourceArn": {"shape": "__string", "location": "uri", "locationName": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) for the resource that's tagged.</p>"}, "Tags": {"shape": "__mapOf__stringMin0Max256PatternS", "documentation": "<p>The tags associated with the resource.</p>"}}, "documentation": "<p>Request of adding tag to the resource</p>", "required": ["ResourceArn", "Tags"]}, "TagResourceResponse": {"type": "structure", "members": {}}, "ThrottlingException": {"type": "structure", "members": {"Message": {"shape": "__string", "locationName": "message"}}, "documentation": "<p>429 response - LimitExceededException or TooManyRequestsException.</p>", "required": ["Message"], "exception": true, "error": {"httpStatusCode": 429}}, "UntagResourceRequest": {"type": "structure", "members": {"ResourceArn": {"shape": "__string", "location": "uri", "locationName": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) for the resource that's tagged.</p>"}, "TagKeys": {"shape": "__listOf__string", "location": "querystring", "locationName": "TagKeys", "documentation": "<p>Keys for the tags to be removed.</p>"}}, "required": ["ResourceArn", "TagKeys"]}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateControlPanelRequest": {"type": "structure", "members": {"ControlPanelArn": {"shape": "__stringMin1Max256PatternAZaZ09", "documentation": "<p>The Amazon Resource Name (ARN) of the control panel.</p>"}, "ControlPanelName": {"shape": "__stringMin1Max64PatternS", "documentation": "<p>The name of the control panel.</p>"}}, "documentation": "<p>The details of the control panel that you're updating.</p>", "required": ["ControlPanelArn", "ControlPanelName"]}, "UpdateControlPanelResponse": {"type": "structure", "members": {"ControlPanel": {"shape": "ControlPanel", "documentation": "<p>The control panel to update.</p>"}}}, "UpdateRoutingControlRequest": {"type": "structure", "members": {"RoutingControlArn": {"shape": "__stringMin1Max256PatternAZaZ09", "documentation": "<p>The Amazon Resource Name (ARN) of the routing control.</p>"}, "RoutingControlName": {"shape": "__stringMin1Max64PatternS", "documentation": "<p>The name of the routing control.</p>"}}, "documentation": "<p>The details of the routing control that you're updating.</p>", "required": ["RoutingControlName", "RoutingControlArn"]}, "UpdateRoutingControlResponse": {"type": "structure", "members": {"RoutingControl": {"shape": "RoutingControl", "documentation": "<p>The routing control that was updated.</p>"}}}, "UpdateSafetyRuleRequest": {"type": "structure", "members": {"AssertionRuleUpdate": {"shape": "AssertionRuleUpdate", "documentation": "<p>The assertion rule to update.</p>"}, "GatingRuleUpdate": {"shape": "GatingRuleUpdate", "documentation": "<p>The gating rule to update.</p>"}}, "documentation": "<p>A rule that you add to Application Recovery Controller to ensure that recovery actions don't accidentally impair your application's availability.</p>"}, "UpdateSafetyRuleResponse": {"type": "structure", "members": {"AssertionRule": {"shape": "AssertionRule", "documentation": "<p>The assertion rule updated.</p>"}, "GatingRule": {"shape": "GatingRule", "documentation": "<p>The gating rule updated.</p>"}}}, "ValidationException": {"type": "structure", "members": {"Message": {"shape": "__string", "locationName": "message"}}, "documentation": "<p>400 response - Multiple causes. For example, you might have a malformed query string and input parameter might be out of range, or you might have used parameters together incorrectly.</p>", "required": ["Message"], "exception": true, "error": {"httpStatusCode": 400}}, "__boolean": {"type": "boolean"}, "__double": {"type": "double"}, "__integer": {"type": "integer"}, "__listOfCluster": {"type": "list", "member": {"shape": "Cluster"}}, "__listOfClusterEndpoint": {"type": "list", "member": {"shape": "ClusterEndpoint"}}, "__listOfControlPanel": {"type": "list", "member": {"shape": "ControlPanel"}}, "__listOfRoutingControl": {"type": "list", "member": {"shape": "RoutingControl"}}, "__listOfRule": {"type": "list", "member": {"shape": "Rule"}}, "__listOf__string": {"type": "list", "member": {"shape": "__string"}}, "__listOf__stringMax36PatternS": {"type": "list", "member": {"shape": "__stringMax36PatternS"}}, "__listOf__stringMin1Max256PatternAZaZ09": {"type": "list", "member": {"shape": "__stringMin1Max256PatternAZaZ09"}}, "__long": {"type": "long"}, "__mapOf__stringMin0Max256PatternS": {"type": "map", "key": {"shape": "__string"}, "value": {"shape": "__stringMin0Max256PatternS"}}, "__string": {"type": "string"}, "__stringMax36PatternS": {"type": "string", "max": 36, "pattern": "^\\S+$"}, "__stringMin0Max256PatternS": {"type": "string", "min": 0, "max": 256, "pattern": "^\\S+$"}, "__stringMin12Max12PatternD12": {"type": "string", "min": 12, "max": 12, "pattern": "^\\d{12}$"}, "__stringMin1Max128PatternAZaZ09": {"type": "string", "min": 1, "max": 128, "pattern": "^[A-Za-z0-9:.\\/_-]*$"}, "__stringMin1Max256PatternAZaZ09": {"type": "string", "min": 1, "max": 256, "pattern": "^[A-Za-z0-9:\\/_-]*$"}, "__stringMin1Max32PatternS": {"type": "string", "min": 1, "max": 32, "pattern": "^\\S+$"}, "__stringMin1Max64PatternS": {"type": "string", "min": 1, "max": 64, "pattern": "^\\S+$"}, "__stringMin1Max8096PatternS": {"type": "string", "min": 1, "max": 8096, "pattern": "[\\S]*"}, "__policy": {"type": "string", "min": 2, "max": 10240, "pattern": "[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u00A1-\\u00FF]+"}, "__timestampIso8601": {"type": "timestamp", "timestampFormat": "iso8601"}, "__timestampUnix": {"type": "timestamp", "timestampFormat": "unixTimestamp"}}, "documentation": "<p>Recovery Control Configuration API Reference for Amazon Route 53 Application Recovery Controller</p>"}