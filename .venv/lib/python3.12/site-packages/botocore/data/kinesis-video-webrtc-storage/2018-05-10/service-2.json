{"version": "2.0", "metadata": {"apiVersion": "2018-05-10", "endpointPrefix": "kinesisvideo", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "Amazon Kinesis Video WebRTC Storage", "serviceId": "Kinesis Video WebRTC Storage", "signatureVersion": "v4", "signingName": "kinesisvideo", "uid": "kinesis-video-webrtc-storage-2018-05-10"}, "operations": {"JoinStorageSession": {"name": "JoinStorageSession", "http": {"method": "POST", "requestUri": "/joinStorageSession", "responseCode": 200}, "input": {"shape": "JoinStorageSessionInput"}, "errors": [{"shape": "ClientLimitExceededException"}, {"shape": "InvalidArgumentException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Join the ongoing one way-video and/or multi-way audio WebRTC session as a video producing device for an input channel. If there’s no existing session for the channel, a new streaming session needs to be created, and the Amazon Resource Name (ARN) of the signaling channel must be provided. </p> <p>Currently for the <code>SINGLE_MASTER</code> type, a video producing device is able to ingest both audio and video media into a stream, while viewers can only ingest audio. Both a video producing device and viewers can join the session first, and wait for other participants.</p> <p>While participants are having peer to peer conversations through webRTC, the ingested media session will be stored into the Kinesis Video Stream. Multiple viewers are able to playback real-time media.</p> <p>Customers can also use existing Kinesis Video Streams features like <code>HLS</code> or <code>DASH</code> playback, Image generation, and more with ingested WebRTC media.</p> <note> <p>Assume that only one video producing device client can be associated with a session for the channel. If more than one client joins the session of a specific channel as a video producing device, the most recent client request takes precedence. </p> </note>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>You do not have required permissions to perform this operation.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "ChannelArn": {"type": "string", "pattern": "^arn:(aws[a-zA-Z-]*):kinesisvideo:[a-z0-9-]+:[0-9]+:[a-z]+/[a-zA-Z0-9_.-]+/[0-9]+$"}, "ClientLimitExceededException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p> Kinesis Video Streams has throttled the request because you have exceeded the limit of allowed client calls. Try making the call later. </p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "InvalidArgumentException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The value for this input parameter is invalid.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "JoinStorageSessionInput": {"type": "structure", "required": ["channelArn"], "members": {"channelArn": {"shape": "ChannelArn", "documentation": "<p> The Amazon Resource Name (ARN) of the signaling channel. </p>"}}}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The specified resource is not found.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "String": {"type": "string"}}, "documentation": "<p> </p>"}