{"version": "2.0", "metadata": {"apiVersion": "2018-05-10", "endpointPrefix": "entityresolution", "jsonVersion": "1.1", "protocol": "rest-json", "serviceAbbreviation": "AWSEntityResolution", "serviceFullName": "AWS EntityResolution", "serviceId": "EntityResolution", "signatureVersion": "v4", "signingName": "entityresolution", "uid": "entityresolution-2018-05-10"}, "operations": {"CreateIdMappingWorkflow": {"name": "CreateIdMappingWorkflow", "http": {"method": "POST", "requestUri": "/idmappingworkflows", "responseCode": 200}, "input": {"shape": "CreateIdMappingWorkflowInput"}, "output": {"shape": "CreateIdMappingWorkflowOutput"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ExceedsLimitException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}], "documentation": "<p>Creates an <code>IdMappingWorkflow</code> object which stores the configuration of the data processing job to be run. Each <code>IdMappingWorkflow</code> must have a unique workflow name. To modify an existing workflow, use the <code>UpdateIdMappingWorkflow</code> API.</p>"}, "CreateMatchingWorkflow": {"name": "CreateMatchingWorkflow", "http": {"method": "POST", "requestUri": "/matchingworkflows", "responseCode": 200}, "input": {"shape": "CreateMatchingWorkflowInput"}, "output": {"shape": "CreateMatchingWorkflowOutput"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ExceedsLimitException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}], "documentation": "<p>Creates a <code>MatchingWorkflow</code> object which stores the configuration of the data processing job to be run. It is important to note that there should not be a pre-existing <code>MatchingWorkflow</code> with the same name. To modify an existing workflow, utilize the <code>UpdateMatchingWorkflow</code> API.</p>"}, "CreateSchemaMapping": {"name": "CreateSchemaMapping", "http": {"method": "POST", "requestUri": "/schemas", "responseCode": 200}, "input": {"shape": "CreateSchemaMappingInput"}, "output": {"shape": "CreateSchemaMappingOutput"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ExceedsLimitException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}], "documentation": "<p>Creates a schema mapping, which defines the schema of the input customer records table. The <code>SchemaMapping</code> also provides Entity Resolution with some metadata about the table, such as the attribute types of the columns and which columns to match on.</p>"}, "DeleteIdMappingWorkflow": {"name": "DeleteIdMappingWorkflow", "http": {"method": "DELETE", "requestUri": "/idmappingworkflows/{workflowName}", "responseCode": 200}, "input": {"shape": "DeleteIdMappingWorkflowInput"}, "output": {"shape": "DeleteIdMappingWorkflowOutput"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Deletes the <code>IdMappingWorkflow</code> with a given name. This operation will succeed even if a workflow with the given name does not exist.</p>", "idempotent": true}, "DeleteMatchingWorkflow": {"name": "DeleteMatchingWorkflow", "http": {"method": "DELETE", "requestUri": "/matchingworkflows/{workflowName}", "responseCode": 200}, "input": {"shape": "DeleteMatchingWorkflowInput"}, "output": {"shape": "DeleteMatchingWorkflowOutput"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Deletes the <code>MatchingWorkflow</code> with a given name. This operation will succeed even if a workflow with the given name does not exist.</p>", "idempotent": true}, "DeleteSchemaMapping": {"name": "DeleteSchemaMapping", "http": {"method": "DELETE", "requestUri": "/schemas/{schemaName}", "responseCode": 200}, "input": {"shape": "DeleteSchemaMappingInput"}, "output": {"shape": "DeleteSchemaMappingOutput"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}], "documentation": "<p>Deletes the <code>SchemaMapping</code> with a given name. This operation will succeed even if a schema with the given name does not exist. This operation will fail if there is a <code>MatchingWorkflow</code> object that references the <code>SchemaMapping</code> in the workflow's <code>InputSourceConfig</code>.</p>", "idempotent": true}, "GetIdMappingJob": {"name": "GetIdMappingJob", "http": {"method": "GET", "requestUri": "/idmappingworkflows/{workflowName}/jobs/{jobId}", "responseCode": 200}, "input": {"shape": "GetIdMappingJobInput"}, "output": {"shape": "GetIdMappingJobOutput"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Gets the status, metrics, and errors (if there are any) that are associated with a job.</p>"}, "GetIdMappingWorkflow": {"name": "GetIdMappingWorkflow", "http": {"method": "GET", "requestUri": "/idmappingworkflows/{workflowName}", "responseCode": 200}, "input": {"shape": "GetIdMappingWorkflowInput"}, "output": {"shape": "GetIdMappingWorkflowOutput"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns the <code>IdMappingWorkflow</code> with a given name, if it exists.</p>"}, "GetMatchId": {"name": "GetMatchId", "http": {"method": "POST", "requestUri": "/matchingworkflows/{workflowName}/matches", "responseCode": 200}, "input": {"shape": "GetMatchIdInput"}, "output": {"shape": "GetMatchIdOutput"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns the corresponding Match ID of a customer record if the record has been processed.</p>"}, "GetMatchingJob": {"name": "GetMatchingJob", "http": {"method": "GET", "requestUri": "/matchingworkflows/{workflowName}/jobs/{jobId}", "responseCode": 200}, "input": {"shape": "GetMatchingJobInput"}, "output": {"shape": "GetMatchingJobOutput"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Gets the status, metrics, and errors (if there are any) that are associated with a job.</p>"}, "GetMatchingWorkflow": {"name": "GetMatchingWorkflow", "http": {"method": "GET", "requestUri": "/matchingworkflows/{workflowName}", "responseCode": 200}, "input": {"shape": "GetMatchingWorkflowInput"}, "output": {"shape": "GetMatchingWorkflowOutput"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns the <code>MatchingWorkflow</code> with a given name, if it exists.</p>"}, "GetProviderService": {"name": "GetProviderService", "http": {"method": "GET", "requestUri": "/providerservices/{providerName}/{providerServiceName}", "responseCode": 200}, "input": {"shape": "GetProviderServiceInput"}, "output": {"shape": "GetProviderServiceOutput"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns the <code>ProviderService</code> of a given name.</p>"}, "GetSchemaMapping": {"name": "GetSchemaMapping", "http": {"method": "GET", "requestUri": "/schemas/{schemaName}", "responseCode": 200}, "input": {"shape": "GetSchemaMappingInput"}, "output": {"shape": "GetSchemaMappingOutput"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns the SchemaMapping of a given name.</p>"}, "ListIdMappingJobs": {"name": "ListIdMappingJobs", "http": {"method": "GET", "requestUri": "/idmappingworkflows/{workflowName}/jobs", "responseCode": 200}, "input": {"shape": "ListIdMappingJobsInput"}, "output": {"shape": "ListIdMappingJobsOutput"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists all ID mapping jobs for a given workflow.</p>"}, "ListIdMappingWorkflows": {"name": "ListIdMappingWorkflows", "http": {"method": "GET", "requestUri": "/idmappingworkflows", "responseCode": 200}, "input": {"shape": "ListIdMappingWorkflowsInput"}, "output": {"shape": "ListIdMappingWorkflowsOutput"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns a list of all the <code>IdMappingWorkflows</code> that have been created for an Amazon Web Services account.</p>"}, "ListMatchingJobs": {"name": "ListMatchingJobs", "http": {"method": "GET", "requestUri": "/matchingworkflows/{workflowName}/jobs", "responseCode": 200}, "input": {"shape": "ListMatchingJobsInput"}, "output": {"shape": "ListMatchingJobsOutput"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists all jobs for a given workflow.</p>"}, "ListMatchingWorkflows": {"name": "ListMatchingWorkflows", "http": {"method": "GET", "requestUri": "/matchingworkflows", "responseCode": 200}, "input": {"shape": "ListMatchingWorkflowsInput"}, "output": {"shape": "ListMatchingWorkflowsOutput"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns a list of all the <code>MatchingWorkflows</code> that have been created for an Amazon Web Services account.</p>"}, "ListProviderServices": {"name": "ListProviderServices", "http": {"method": "GET", "requestUri": "/providerservices", "responseCode": 200}, "input": {"shape": "ListProviderServicesInput"}, "output": {"shape": "ListProviderServicesOutput"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns a list of all the <code>ProviderServices</code> that are available in this Amazon Web Services Region.</p>"}, "ListSchemaMappings": {"name": "ListSchemaMappings", "http": {"method": "GET", "requestUri": "/schemas", "responseCode": 200}, "input": {"shape": "ListSchemaMappingsInput"}, "output": {"shape": "ListSchemaMappingsOutput"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns a list of all the <code>SchemaMappings</code> that have been created for an Amazon Web Services account.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceInput"}, "output": {"shape": "ListTagsForResourceOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Displays the tags associated with an Entity Resolution resource. In Entity Resolution, <code>SchemaMapping</code>, and <code>MatchingWorkflow</code> can be tagged.</p>"}, "StartIdMappingJob": {"name": "StartIdMappingJob", "http": {"method": "POST", "requestUri": "/idmappingworkflows/{workflowName}/jobs", "responseCode": 200}, "input": {"shape": "StartIdMappingJobInput"}, "output": {"shape": "StartIdMappingJobOutput"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ExceedsLimitException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}], "documentation": "<p>Starts the <code>IdMappingJob</code> of a workflow. The workflow must have previously been created using the <code>CreateIdMappingWorkflow</code> endpoint.</p>"}, "StartMatchingJob": {"name": "StartMatchingJob", "http": {"method": "POST", "requestUri": "/matchingworkflows/{workflowName}/jobs", "responseCode": 200}, "input": {"shape": "StartMatchingJobInput"}, "output": {"shape": "StartMatchingJobOutput"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ExceedsLimitException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}], "documentation": "<p>Starts the <code>MatchingJob</code> of a workflow. The workflow must have previously been created using the <code>CreateMatchingWorkflow</code> endpoint.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "TagResourceInput"}, "output": {"shape": "TagResourceOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Assigns one or more tags (key-value pairs) to the specified Entity Resolution resource. Tags can help you organize and categorize your resources. You can also use them to scope user permissions by granting a user permission to access or change only resources with certain tag values. In Entity Resolution, <code>SchemaMapping</code> and <code>MatchingWorkflow</code> can be tagged. Tags don't have any semantic meaning to Amazon Web Services and are interpreted strictly as strings of characters. You can use the <code>TagResource</code> action with a resource that already has tags. If you specify a new tag key, this tag is appended to the list of tags associated with the resource. If you specify a tag key that is already associated with the resource, the new tag value that you specify replaces the previous value for that tag.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "UntagResourceInput"}, "output": {"shape": "UntagResourceOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Removes one or more tags from the specified Entity Resolution resource. In Entity Resolution, <code>SchemaMapping</code>, and <code>MatchingWorkflow</code> can be tagged.</p>", "idempotent": true}, "UpdateIdMappingWorkflow": {"name": "UpdateIdMappingWorkflow", "http": {"method": "PUT", "requestUri": "/idmappingworkflows/{workflowName}", "responseCode": 200}, "input": {"shape": "UpdateIdMappingWorkflowInput"}, "output": {"shape": "UpdateIdMappingWorkflowOutput"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Updates an existing <code>IdMappingWorkflow</code>. This method is identical to <code>CreateIdMappingWorkflow</code>, except it uses an HTTP <code>PUT</code> request instead of a <code>POST</code> request, and the <code>IdMappingWorkflow</code> must already exist for the method to succeed.</p>", "idempotent": true}, "UpdateMatchingWorkflow": {"name": "UpdateMatchingWorkflow", "http": {"method": "PUT", "requestUri": "/matchingworkflows/{workflowName}", "responseCode": 200}, "input": {"shape": "UpdateMatchingWorkflowInput"}, "output": {"shape": "UpdateMatchingWorkflowOutput"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Updates an existing <code>MatchingWorkflow</code>. This method is identical to <code>CreateMatchingWorkflow</code>, except it uses an HTTP <code>PUT</code> request instead of a <code>POST</code> request, and the <code>MatchingWorkflow</code> must already exist for the method to succeed.</p>", "idempotent": true}, "UpdateSchemaMapping": {"name": "UpdateSchemaMapping", "http": {"method": "PUT", "requestUri": "/schemas/{schemaName}", "responseCode": 200}, "input": {"shape": "UpdateSchemaMappingInput"}, "output": {"shape": "UpdateSchemaMappingOutput"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}], "documentation": "<p>Updates a schema mapping.</p> <note> <p>A schema is immutable if it is being used by a workflow. Therefore, you can't update a schema mapping if it's associated with a workflow. </p> </note>", "idempotent": true}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>You do not have sufficient access to perform this action. <code>HTTP Status Code: 403</code> </p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "AttributeMatchingModel": {"type": "string", "enum": ["ONE_TO_ONE", "MANY_TO_MANY"]}, "AttributeName": {"type": "string", "max": 255, "min": 0, "pattern": "^[a-zA-Z_0-9- \\t]*$"}, "AwsAccountId": {"type": "string", "pattern": "\\d{12}"}, "AwsAccountIdList": {"type": "list", "member": {"shape": "AwsAccountId"}}, "Boolean": {"type": "boolean", "box": true}, "ConflictException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The request could not be processed because of conflict in the current state of the resource. Example: Workflow already exists, Schema already exists, Workflow is currently running, etc. <code>HTTP Status Code: 400</code> </p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "CreateIdMappingWorkflowInput": {"type": "structure", "required": ["idMappingTechniques", "inputSourceConfig", "outputSourceConfig", "roleArn", "workflowName"], "members": {"description": {"shape": "Description", "documentation": "<p>A description of the workflow.</p>"}, "idMappingTechniques": {"shape": "IdMappingTechniques", "documentation": "<p>An object which defines the <code>idMappingType</code> and the <code>providerProperties</code>.</p>"}, "inputSourceConfig": {"shape": "IdMappingWorkflowInputSourceConfig", "documentation": "<p>A list of <code>InputSource</code> objects, which have the fields <code>InputSourceARN</code> and <code>SchemaName</code>.</p>"}, "outputSourceConfig": {"shape": "IdMappingWorkflowOutputSourceConfig", "documentation": "<p>A list of <code>IdMappingWorkflowOutputSource</code> objects, each of which contains fields <code>OutputS3Path</code> and <code>Output</code>.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role. Entity Resolution assumes this role to create resources on your behalf as part of workflow execution.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}, "workflowName": {"shape": "EntityName", "documentation": "<p>The name of the workflow. There can't be multiple <code>IdMappingWorkflows</code> with the same name.</p>"}}}, "CreateIdMappingWorkflowOutput": {"type": "structure", "required": ["idMappingTechniques", "inputSourceConfig", "outputSourceConfig", "roleArn", "workflowArn", "workflowName"], "members": {"description": {"shape": "Description", "documentation": "<p>A description of the workflow.</p>"}, "idMappingTechniques": {"shape": "IdMappingTechniques", "documentation": "<p>An object which defines the <code>idMappingType</code> and the <code>providerProperties</code>.</p>"}, "inputSourceConfig": {"shape": "IdMappingWorkflowInputSourceConfig", "documentation": "<p>A list of <code>InputSource</code> objects, which have the fields <code>InputSourceARN</code> and <code>SchemaName</code>.</p>"}, "outputSourceConfig": {"shape": "IdMappingWorkflowOutputSourceConfig", "documentation": "<p>A list of <code>IdMappingWorkflowOutputSource</code> objects, each of which contains fields <code>OutputS3Path</code> and <code>Output</code>.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role. Entity Resolution assumes this role to create resources on your behalf as part of workflow execution.</p>"}, "workflowArn": {"shape": "IdMappingWorkflowArn", "documentation": "<p>The ARN (Amazon Resource Name) that Entity Resolution generated for the <code>IDMappingWorkflow</code>.</p>"}, "workflowName": {"shape": "EntityName", "documentation": "<p>The name of the workflow.</p>"}}}, "CreateMatchingWorkflowInput": {"type": "structure", "required": ["inputSourceConfig", "outputSourceConfig", "resolutionTechniques", "roleArn", "workflowName"], "members": {"description": {"shape": "Description", "documentation": "<p>A description of the workflow.</p>"}, "incrementalRunConfig": {"shape": "IncrementalRunConfig", "documentation": "<p>An object which defines an incremental run type and has only <code>incrementalRunType</code> as a field.</p>"}, "inputSourceConfig": {"shape": "InputSourceConfig", "documentation": "<p>A list of <code>InputSource</code> objects, which have the fields <code>InputSourceARN</code> and <code>SchemaName</code>.</p>"}, "outputSourceConfig": {"shape": "OutputSourceConfig", "documentation": "<p>A list of <code>OutputSource</code> objects, each of which contains fields <code>OutputS3Path</code>, <code>ApplyNormalization</code>, and <code>Output</code>.</p>"}, "resolutionTechniques": {"shape": "ResolutionTechniques", "documentation": "<p>An object which defines the <code>resolutionType</code> and the <code>ruleBasedProperties</code>.</p>"}, "roleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role. Entity Resolution assumes this role to create resources on your behalf as part of workflow execution.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}, "workflowName": {"shape": "EntityName", "documentation": "<p>The name of the workflow. There can't be multiple <code>MatchingWorkflows</code> with the same name.</p>"}}}, "CreateMatchingWorkflowOutput": {"type": "structure", "required": ["inputSourceConfig", "outputSourceConfig", "resolutionTechniques", "roleArn", "workflowArn", "workflowName"], "members": {"description": {"shape": "Description", "documentation": "<p>A description of the workflow.</p>"}, "incrementalRunConfig": {"shape": "IncrementalRunConfig", "documentation": "<p>An object which defines an incremental run type and has only <code>incrementalRunType</code> as a field.</p>"}, "inputSourceConfig": {"shape": "InputSourceConfig", "documentation": "<p>A list of <code>InputSource</code> objects, which have the fields <code>InputSourceARN</code> and <code>SchemaName</code>.</p>"}, "outputSourceConfig": {"shape": "OutputSourceConfig", "documentation": "<p>A list of <code>OutputSource</code> objects, each of which contains fields <code>OutputS3Path</code>, <code>ApplyNormalization</code>, and <code>Output</code>.</p>"}, "resolutionTechniques": {"shape": "ResolutionTechniques", "documentation": "<p>An object which defines the <code>resolutionType</code> and the <code>ruleBasedProperties</code>.</p>"}, "roleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role. Entity Resolution assumes this role to create resources on your behalf as part of workflow execution.</p>"}, "workflowArn": {"shape": "MatchingWorkflowArn", "documentation": "<p>The ARN (Amazon Resource Name) that Entity Resolution generated for the <code>MatchingWorkflow</code>.</p>"}, "workflowName": {"shape": "EntityName", "documentation": "<p>The name of the workflow.</p>"}}}, "CreateSchemaMappingInput": {"type": "structure", "required": ["mappedInputFields", "schemaName"], "members": {"description": {"shape": "Description", "documentation": "<p>A description of the schema.</p>"}, "mappedInputFields": {"shape": "SchemaInputAttributes", "documentation": "<p>A list of <code>MappedInputFields</code>. Each <code>MappedInputField</code> corresponds to a column the source data table, and contains column name plus additional information that Entity Resolution uses for matching.</p>"}, "schemaName": {"shape": "EntityName", "documentation": "<p>The name of the schema. There can't be multiple <code>SchemaMappings</code> with the same name.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}}}, "CreateSchemaMappingOutput": {"type": "structure", "required": ["description", "mappedInputFields", "schemaArn", "schemaName"], "members": {"description": {"shape": "Description", "documentation": "<p>A description of the schema.</p>"}, "mappedInputFields": {"shape": "SchemaInputAttributes", "documentation": "<p>A list of <code>MappedInputFields</code>. Each <code>MappedInputField</code> corresponds to a column the source data table, and contains column name plus additional information that Entity Resolution uses for matching.</p>"}, "schemaArn": {"shape": "SchemaMappingArn", "documentation": "<p>The ARN (Amazon Resource Name) that Entity Resolution generated for the <code>SchemaMapping</code>.</p>"}, "schemaName": {"shape": "EntityName", "documentation": "<p>The name of the schema.</p>"}}}, "DeleteIdMappingWorkflowInput": {"type": "structure", "required": ["workflowName"], "members": {"workflowName": {"shape": "EntityName", "documentation": "<p>The name of the workflow to be deleted.</p>", "location": "uri", "locationName": "workflowName"}}}, "DeleteIdMappingWorkflowOutput": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String", "documentation": "<p>A successful operation message.</p>"}}}, "DeleteMatchingWorkflowInput": {"type": "structure", "required": ["workflowName"], "members": {"workflowName": {"shape": "EntityName", "documentation": "<p>The name of the workflow to be retrieved.</p>", "location": "uri", "locationName": "workflowName"}}}, "DeleteMatchingWorkflowOutput": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String", "documentation": "<p>A successful operation message.</p>"}}}, "DeleteSchemaMappingInput": {"type": "structure", "required": ["schemaName"], "members": {"schemaName": {"shape": "EntityName", "documentation": "<p>The name of the schema to delete.</p>", "location": "uri", "locationName": "schemaName"}}}, "DeleteSchemaMappingOutput": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String", "documentation": "<p>A successful operation message.</p>"}}}, "Description": {"type": "string", "max": 255, "min": 0}, "Document": {"type": "structure", "members": {}, "document": true}, "EntityName": {"type": "string", "max": 255, "min": 1, "pattern": "^[a-zA-Z_0-9-]*$"}, "ErrorDetails": {"type": "structure", "members": {"errorMessage": {"shape": "ErrorMessage", "documentation": "<p>The error message from the job, if there is one.</p>"}}, "documentation": "<p>An object containing an error message, if there was an error.</p>"}, "ErrorMessage": {"type": "string", "max": 2048, "min": 1}, "ExceedsLimitException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}, "quotaName": {"shape": "String", "documentation": "<p>The name of the quota that has been breached.</p>"}, "quotaValue": {"shape": "Integer", "documentation": "<p>The current quota value for the customers.</p>"}}, "documentation": "<p>The request was rejected because it attempted to create resources beyond the current Entity Resolution account limits. The error message describes the limit exceeded. <code>HTTP Status Code: 402</code> </p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "GetIdMappingJobInput": {"type": "structure", "required": ["jobId", "workflowName"], "members": {"jobId": {"shape": "JobId", "documentation": "<p>The ID of the job.</p>", "location": "uri", "locationName": "jobId"}, "workflowName": {"shape": "EntityName", "documentation": "<p>The name of the workflow.</p>", "location": "uri", "locationName": "workflowName"}}}, "GetIdMappingJobOutput": {"type": "structure", "required": ["jobId", "startTime", "status"], "members": {"endTime": {"shape": "Timestamp", "documentation": "<p>The time at which the job has finished.</p>"}, "errorDetails": {"shape": "ErrorDetails"}, "jobId": {"shape": "JobId", "documentation": "<p>The ID of the job.</p>"}, "metrics": {"shape": "IdMappingJobMetrics", "documentation": "<p>Metrics associated with the execution, specifically total records processed, unique IDs generated, and records the execution skipped.</p>"}, "startTime": {"shape": "Timestamp", "documentation": "<p>The time at which the job was started.</p>"}, "status": {"shape": "JobStatus", "documentation": "<p>The current status of the job.</p>"}}}, "GetIdMappingWorkflowInput": {"type": "structure", "required": ["workflowName"], "members": {"workflowName": {"shape": "EntityName", "documentation": "<p>The name of the workflow.</p>", "location": "uri", "locationName": "workflowName"}}}, "GetIdMappingWorkflowOutput": {"type": "structure", "required": ["createdAt", "idMappingTechniques", "inputSourceConfig", "outputSourceConfig", "roleArn", "updatedAt", "workflowArn", "workflowName"], "members": {"createdAt": {"shape": "Timestamp", "documentation": "<p>The timestamp of when the workflow was created.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the workflow.</p>"}, "idMappingTechniques": {"shape": "IdMappingTechniques", "documentation": "<p>An object which defines the <code>idMappingType</code> and the <code>providerProperties</code>.</p>"}, "inputSourceConfig": {"shape": "IdMappingWorkflowInputSourceConfig", "documentation": "<p>A list of <code>InputSource</code> objects, which have the fields <code>InputSourceARN</code> and <code>SchemaName</code>.</p>"}, "outputSourceConfig": {"shape": "IdMappingWorkflowOutputSourceConfig", "documentation": "<p>A list of <code>OutputSource</code> objects, each of which contains fields <code>OutputS3Path</code> and <code>KMSArn</code>.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role. Entity Resolution assumes this role to access resources on your behalf.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp of when the workflow was last updated.</p>"}, "workflowArn": {"shape": "IdMappingWorkflowArn", "documentation": "<p>The ARN (Amazon Resource Name) that Entity Resolution generated for the <code>IdMappingWorkflow</code> .</p>"}, "workflowName": {"shape": "EntityName", "documentation": "<p>The name of the workflow.</p>"}}}, "GetMatchIdInput": {"type": "structure", "required": ["record", "workflowName"], "members": {"record": {"shape": "RecordAttributeMap", "documentation": "<p>The record to fetch the Match ID for.</p>"}, "workflowName": {"shape": "EntityName", "documentation": "<p>The name of the workflow.</p>", "location": "uri", "locationName": "workflowName"}}}, "GetMatchIdOutput": {"type": "structure", "members": {"matchId": {"shape": "String", "documentation": "<p>The unique identifiers for this group of match records.</p>"}}}, "GetMatchingJobInput": {"type": "structure", "required": ["jobId", "workflowName"], "members": {"jobId": {"shape": "JobId", "documentation": "<p>The ID of the job.</p>", "location": "uri", "locationName": "jobId"}, "workflowName": {"shape": "EntityName", "documentation": "<p>The name of the workflow.</p>", "location": "uri", "locationName": "workflowName"}}}, "GetMatchingJobOutput": {"type": "structure", "required": ["jobId", "startTime", "status"], "members": {"endTime": {"shape": "Timestamp", "documentation": "<p>The time at which the job has finished.</p>"}, "errorDetails": {"shape": "ErrorDetails", "documentation": "<p>An object containing an error message, if there was an error.</p>"}, "jobId": {"shape": "JobId", "documentation": "<p>The ID of the job.</p>"}, "metrics": {"shape": "JobMetrics", "documentation": "<p>Metrics associated with the execution, specifically total records processed, unique IDs generated, and records the execution skipped.</p>"}, "startTime": {"shape": "Timestamp", "documentation": "<p>The time at which the job was started.</p>"}, "status": {"shape": "JobStatus", "documentation": "<p>The current status of the job.</p>"}}}, "GetMatchingWorkflowInput": {"type": "structure", "required": ["workflowName"], "members": {"workflowName": {"shape": "EntityName", "documentation": "<p>The name of the workflow.</p>", "location": "uri", "locationName": "workflowName"}}}, "GetMatchingWorkflowOutput": {"type": "structure", "required": ["createdAt", "inputSourceConfig", "outputSourceConfig", "resolutionTechniques", "roleArn", "updatedAt", "workflowArn", "workflowName"], "members": {"createdAt": {"shape": "Timestamp", "documentation": "<p>The timestamp of when the workflow was created.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the workflow.</p>"}, "incrementalRunConfig": {"shape": "IncrementalRunConfig", "documentation": "<p>An object which defines an incremental run type and has only <code>incrementalRunType</code> as a field.</p>"}, "inputSourceConfig": {"shape": "InputSourceConfig", "documentation": "<p>A list of <code>InputSource</code> objects, which have the fields <code>InputSourceARN</code> and <code>SchemaName</code>.</p>"}, "outputSourceConfig": {"shape": "OutputSourceConfig", "documentation": "<p>A list of <code>OutputSource</code> objects, each of which contains fields <code>OutputS3Path</code>, <code>ApplyNormalization</code>, and <code>Output</code>.</p>"}, "resolutionTechniques": {"shape": "ResolutionTechniques", "documentation": "<p>An object which defines the <code>resolutionType</code> and the <code>ruleBasedProperties</code>.</p>"}, "roleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role. Entity Resolution assumes this role to access resources on your behalf.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp of when the workflow was last updated.</p>"}, "workflowArn": {"shape": "MatchingWorkflowArn", "documentation": "<p>The ARN (Amazon Resource Name) that Entity Resolution generated for the <code>MatchingWorkflow</code>.</p>"}, "workflowName": {"shape": "EntityName", "documentation": "<p>The name of the workflow.</p>"}}}, "GetProviderServiceInput": {"type": "structure", "required": ["providerName", "providerServiceName"], "members": {"providerName": {"shape": "EntityName", "documentation": "<p>The name of the provider. This name is typically the company name.</p>", "location": "uri", "locationName": "providerName"}, "providerServiceName": {"shape": "ProviderServiceArn", "documentation": "<p>The ARN (Amazon Resource Name) of the product that the provider service provides.</p>", "location": "uri", "locationName": "providerServiceName"}}}, "GetProviderServiceOutput": {"type": "structure", "required": ["anonymizedOutput", "providerEndpointConfiguration", "providerEntityOutputDefinition", "providerName", "providerServiceArn", "providerServiceDisplayName", "providerServiceName", "providerServiceType"], "members": {"anonymizedOutput": {"shape": "Boolean", "documentation": "<p>Specifies whether output data from the provider is anonymized. A value of <code>TRUE</code> means the output will be anonymized and you can't relate the data that comes back from the provider to the identifying input. A value of <code>FALSE</code> means the output won't be anonymized and you can relate the data that comes back from the provider to your source data. </p>"}, "providerConfigurationDefinition": {"shape": "Document", "documentation": "<p>The definition of the provider configuration.</p>"}, "providerEndpointConfiguration": {"shape": "ProviderEndpointConfiguration", "documentation": "<p>The required configuration fields to use with the provider service.</p>"}, "providerEntityOutputDefinition": {"shape": "Document", "documentation": "<p>The definition of the provider entity output.</p>"}, "providerIntermediateDataAccessConfiguration": {"shape": "ProviderIntermediateDataAccessConfiguration", "documentation": "<p>The Amazon Web Services accounts and the S3 permissions that are required by some providers to create an S3 bucket for intermediate data storage.</p>"}, "providerName": {"shape": "EntityName", "documentation": "<p>The name of the provider. This name is typically the company name.</p>"}, "providerServiceArn": {"shape": "ProviderServiceArn", "documentation": "<p>The ARN (Amazon Resource Name) that Entity Resolution generated for the provider service.</p>"}, "providerServiceDisplayName": {"shape": "ProviderServiceDisplayName", "documentation": "<p>The display name of the provider service.</p>"}, "providerServiceName": {"shape": "EntityName", "documentation": "<p>The name of the product that the provider service provides. </p>"}, "providerServiceType": {"shape": "ServiceType", "documentation": "<p>The type of provider service.</p>"}}}, "GetSchemaMappingInput": {"type": "structure", "required": ["schemaName"], "members": {"schemaName": {"shape": "EntityName", "documentation": "<p>The name of the schema to be retrieved.</p>", "location": "uri", "locationName": "schemaName"}}}, "GetSchemaMappingOutput": {"type": "structure", "required": ["createdAt", "hasWorkflows", "mappedInputFields", "schemaArn", "schemaName", "updatedAt"], "members": {"createdAt": {"shape": "Timestamp", "documentation": "<p>The timestamp of when the <code>SchemaMapping</code> was created.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the schema.</p>"}, "hasWorkflows": {"shape": "Boolean", "documentation": "<p>Specifies whether the schema mapping has been applied to a workflow.</p>"}, "mappedInputFields": {"shape": "SchemaInputAttributes", "documentation": "<p>A list of <code>MappedInputFields</code>. Each <code>MappedInputField</code> corresponds to a column the source data table, and contains column name plus additional information Venice uses for matching.</p>"}, "schemaArn": {"shape": "SchemaMappingArn", "documentation": "<p>The ARN (Amazon Resource Name) that Entity Resolution generated for the SchemaMapping.</p>"}, "schemaName": {"shape": "EntityName", "documentation": "<p>The name of the schema.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp of when the <code>SchemaMapping</code> was last updated.</p>"}}}, "IdMappingJobMetrics": {"type": "structure", "members": {"inputRecords": {"shape": "Integer", "documentation": "<p>The total number of input records.</p>"}, "recordsNotProcessed": {"shape": "Integer", "documentation": "<p>The total number of records that did not get processed.</p>"}, "totalRecordsProcessed": {"shape": "Integer", "documentation": "<p>The total number of records processed.</p>"}}, "documentation": "<p>An object containing <code>InputRecords</code>, <code>TotalRecordsProcessed</code>, <code>MatchIDs</code>, and <code>RecordsNotProcessed</code>.</p>"}, "IdMappingTechniques": {"type": "structure", "required": ["idMappingType", "providerProperties"], "members": {"idMappingType": {"shape": "IdMappingType", "documentation": "<p>The type of ID mapping.</p>"}, "providerProperties": {"shape": "ProviderProperties", "documentation": "<p>An object which defines any additional configurations required by the provider service.</p>"}}, "documentation": "<p>An object which defines the ID mapping techniques and provider configurations.</p>"}, "IdMappingType": {"type": "string", "enum": ["PROVIDER"]}, "IdMappingWorkflowArn": {"type": "string", "pattern": "^arn:(aws|aws-us-gov|aws-cn):entityresolution:.*:[0-9]+:(idmappingworkflow/.*)$"}, "IdMappingWorkflowInputSource": {"type": "structure", "required": ["inputSourceARN", "schemaName"], "members": {"inputSourceARN": {"shape": "IdMappingWorkflowInputSourceInputSourceARNString", "documentation": "<p>An Gluetable ARN for the input source table.</p>"}, "schemaName": {"shape": "EntityName", "documentation": "<p>The name of the schema to be retrieved.</p>"}}, "documentation": "<p>An object containing <code>InputSourceARN</code> and <code>SchemaName</code>.</p>"}, "IdMappingWorkflowInputSourceConfig": {"type": "list", "member": {"shape": "IdMappingWorkflowInputSource"}, "max": 20, "min": 1}, "IdMappingWorkflowInputSourceInputSourceARNString": {"type": "string", "pattern": "^arn:aws:.*:.*:[0-9]+:.*$"}, "IdMappingWorkflowList": {"type": "list", "member": {"shape": "IdMappingWorkflowSummary"}}, "IdMappingWorkflowOutputSource": {"type": "structure", "required": ["outputS3Path"], "members": {"KMSArn": {"shape": "KMSArn", "documentation": "<p>Customer KMS ARN for encryption at rest. If not provided, system will use an Entity Resolution managed KMS key.</p>"}, "outputS3Path": {"shape": "S3Path", "documentation": "<p>The S3 path to which Entity Resolution will write the output table.</p>"}}, "documentation": "<p>The output source for the ID mapping workflow.</p>"}, "IdMappingWorkflowOutputSourceConfig": {"type": "list", "member": {"shape": "IdMappingWorkflowOutputSource"}, "max": 1, "min": 1}, "IdMappingWorkflowSummary": {"type": "structure", "required": ["createdAt", "updatedAt", "workflowArn", "workflowName"], "members": {"createdAt": {"shape": "Timestamp", "documentation": "<p>The timestamp of when the workflow was created.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp of when the workflow was last updated.</p>"}, "workflowArn": {"shape": "IdMappingWorkflowArn", "documentation": "<p>The ARN (Amazon Resource Name) that Entity Resolution generated for the <code>IdMappingWorkflow</code>.</p>"}, "workflowName": {"shape": "EntityName", "documentation": "<p>The name of the workflow.</p>"}}, "documentation": "<p>A list of <code>IdMappingWorkflowSummary</code> objects, each of which contain the fields <code>WorkflowName</code>, <code>WorkflowArn</code>, <code>CreatedAt</code>, and <code>UpdatedAt</code>.</p>"}, "IncrementalRunConfig": {"type": "structure", "members": {"incrementalRunType": {"shape": "IncrementalRunType", "documentation": "<p>The type of incremental run. It takes only one value: <code>IMMEDIATE</code>.</p>"}}, "documentation": "<p>An object which defines an incremental run type and has only <code>incrementalRunType</code> as a field.</p>"}, "IncrementalRunType": {"type": "string", "enum": ["IMMEDIATE"]}, "InputSource": {"type": "structure", "required": ["inputSourceARN", "schemaName"], "members": {"applyNormalization": {"shape": "Boolean", "documentation": "<p>Normalizes the attributes defined in the schema in the input data. For example, if an attribute has an <code>AttributeType</code> of <code>PHONE_NUMBER</code>, and the data in the input table is in a format of 1234567890, Entity Resolution will normalize this field in the output to (*************.</p>"}, "inputSourceARN": {"shape": "InputSourceInputSourceARNString", "documentation": "<p>An Glue table ARN for the input source table.</p>"}, "schemaName": {"shape": "EntityName", "documentation": "<p>The name of the schema to be retrieved.</p>"}}, "documentation": "<p>An object containing <code>InputSourceARN</code>, <code>SchemaName</code>, and <code>ApplyNormalization</code>.</p>"}, "InputSourceConfig": {"type": "list", "member": {"shape": "InputSource"}, "max": 20, "min": 1}, "InputSourceInputSourceARNString": {"type": "string", "pattern": "^arn:aws:.*:.*:[0-9]+:.*$"}, "Integer": {"type": "integer", "box": true}, "IntermediateSourceConfiguration": {"type": "structure", "required": ["intermediateS3Path"], "members": {"intermediateS3Path": {"shape": "S3Path", "documentation": "<p>The Amazon S3 location (bucket and prefix). For example: <code>s3://provider_bucket/DOC-EXAMPLE-BUCKET</code> </p>"}}, "documentation": "<p>The Amazon S3 location that temporarily stores your data while it processes. Your information won't be saved permanently.</p>"}, "InternalServerException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>This exception occurs when there is an internal failure in the Entity Resolution service. <code>HTTP Status Code: 500</code> </p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "JobId": {"type": "string", "pattern": "^[a-f0-9]{32}$"}, "JobList": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "JobMetrics": {"type": "structure", "members": {"inputRecords": {"shape": "Integer", "documentation": "<p>The total number of input records.</p>"}, "matchIDs": {"shape": "Integer", "documentation": "<p>The total number of <code>matchID</code>s generated.</p>"}, "recordsNotProcessed": {"shape": "Integer", "documentation": "<p>The total number of records that did not get processed.</p>"}, "totalRecordsProcessed": {"shape": "Integer", "documentation": "<p>The total number of records processed.</p>"}}, "documentation": "<p>An object containing <code>InputRecords</code>, <code>TotalRecordsProcessed</code>, <code>MatchIDs</code>, and <code>RecordsNotProcessed</code>.</p>"}, "JobStatus": {"type": "string", "enum": ["RUNNING", "SUCCEEDED", "FAILED", "QUEUED"]}, "JobSummary": {"type": "structure", "required": ["jobId", "startTime", "status"], "members": {"endTime": {"shape": "Timestamp", "documentation": "<p>The time at which the job has finished.</p>"}, "jobId": {"shape": "JobId", "documentation": "<p>The ID of the job.</p>"}, "startTime": {"shape": "Timestamp", "documentation": "<p>The time at which the job was started.</p>"}, "status": {"shape": "JobStatus", "documentation": "<p>The current status of the job.</p>"}}, "documentation": "<p>An object containing the <code>JobId</code>, <code>Status</code>, <code>StartTime</code>, and <code>EndTime</code> of a job.</p>"}, "KMSArn": {"type": "string", "pattern": "^arn:aws:kms:.*:[0-9]+:.*$"}, "ListIdMappingJobsInput": {"type": "structure", "required": ["workflowName"], "members": {"maxResults": {"shape": "ListIdMappingJobsInputMaxResultsInteger", "documentation": "<p>The maximum number of objects returned per page.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token from the previous API call.</p>", "location": "querystring", "locationName": "nextToken"}, "workflowName": {"shape": "EntityName", "documentation": "<p>The name of the workflow to be retrieved.</p>", "location": "uri", "locationName": "workflowName"}}}, "ListIdMappingJobsInputMaxResultsInteger": {"type": "integer", "box": true, "max": 25, "min": 1}, "ListIdMappingJobsOutput": {"type": "structure", "members": {"jobs": {"shape": "JobList", "documentation": "<p>A list of <code>JobSummary</code> objects.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token from the previous API call.</p>"}}}, "ListIdMappingWorkflowsInput": {"type": "structure", "members": {"maxResults": {"shape": "ListIdMappingWorkflowsInputMaxResultsInteger", "documentation": "<p>The maximum number of objects returned per page.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token from the previous API call.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListIdMappingWorkflowsInputMaxResultsInteger": {"type": "integer", "box": true, "max": 25}, "ListIdMappingWorkflowsOutput": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token from the previous API call.</p>"}, "workflowSummaries": {"shape": "IdMappingWorkflowList", "documentation": "<p>A list of <code>IdMappingWorkflowSummary</code> objects.</p>"}}}, "ListMatchingJobsInput": {"type": "structure", "required": ["workflowName"], "members": {"maxResults": {"shape": "ListMatchingJobsInputMaxResultsInteger", "documentation": "<p>The maximum number of objects returned per page.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token from the previous API call.</p>", "location": "querystring", "locationName": "nextToken"}, "workflowName": {"shape": "EntityName", "documentation": "<p>The name of the workflow to be retrieved.</p>", "location": "uri", "locationName": "workflowName"}}}, "ListMatchingJobsInputMaxResultsInteger": {"type": "integer", "box": true, "max": 25, "min": 1}, "ListMatchingJobsOutput": {"type": "structure", "members": {"jobs": {"shape": "JobList", "documentation": "<p>A list of <code>JobSummary</code> objects, each of which contain the ID, status, start time, and end time of a job.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token from the previous API call.</p>"}}}, "ListMatchingWorkflowsInput": {"type": "structure", "members": {"maxResults": {"shape": "ListMatchingWorkflowsInputMaxResultsInteger", "documentation": "<p>The maximum number of objects returned per page.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token from the previous API call.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListMatchingWorkflowsInputMaxResultsInteger": {"type": "integer", "box": true, "max": 25}, "ListMatchingWorkflowsOutput": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token from the previous API call.</p>"}, "workflowSummaries": {"shape": "MatchingWorkflowList", "documentation": "<p>A list of <code>MatchingWorkflowSummary</code> objects, each of which contain the fields <code>WorkflowName</code>, <code>WorkflowArn</code>, <code>CreatedAt</code>, and <code>UpdatedAt</code>.</p>"}}}, "ListProviderServicesInput": {"type": "structure", "members": {"maxResults": {"shape": "ListProviderServicesInputMaxResultsInteger", "documentation": "<p>The maximum number of objects returned per page.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token from the previous API call.</p>", "location": "querystring", "locationName": "nextToken"}, "providerName": {"shape": "EntityName", "documentation": "<p>The name of the provider. This name is typically the company name.</p>", "location": "querystring", "locationName": "providerName"}}}, "ListProviderServicesInputMaxResultsInteger": {"type": "integer", "box": true, "max": 25, "min": 15}, "ListProviderServicesOutput": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token from the previous API call.</p>"}, "providerServiceSummaries": {"shape": "ProviderServiceList", "documentation": "<p>A list of <code>ProviderServices</code> objects.</p>"}}}, "ListSchemaMappingsInput": {"type": "structure", "members": {"maxResults": {"shape": "ListSchemaMappingsInputMaxResultsInteger", "documentation": "<p>The maximum number of objects returned per page.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token from the previous API call.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListSchemaMappingsInputMaxResultsInteger": {"type": "integer", "box": true, "max": 25}, "ListSchemaMappingsOutput": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token from the previous API call.</p>"}, "schemaList": {"shape": "SchemaMappingList", "documentation": "<p>A list of <code>SchemaMappingSummary</code> objects, each of which contain the fields <code>SchemaName</code>, <code>SchemaArn</code>, <code>CreatedAt</code>, <code>UpdatedAt</code>.</p>"}}}, "ListTagsForResourceInput": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "VeniceGlobalArn", "documentation": "<p>The ARN of the resource for which you want to view tags.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceOutput": {"type": "structure", "required": ["tags"], "members": {"tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}}}, "MatchingWorkflowArn": {"type": "string", "pattern": "^arn:(aws|aws-us-gov|aws-cn):entityresolution:.*:[0-9]+:(matchingworkflow/.*)$"}, "MatchingWorkflowList": {"type": "list", "member": {"shape": "MatchingWorkflowSummary"}}, "MatchingWorkflowSummary": {"type": "structure", "required": ["createdAt", "resolutionType", "updatedAt", "workflowArn", "workflowName"], "members": {"createdAt": {"shape": "Timestamp", "documentation": "<p>The timestamp of when the workflow was created.</p>"}, "resolutionType": {"shape": "ResolutionType", "documentation": "<p>The method that has been specified for data matching, either using matching provided by Entity Resolution or through a provider service.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp of when the workflow was last updated.</p>"}, "workflowArn": {"shape": "MatchingWorkflowArn", "documentation": "<p>The ARN (Amazon Resource Name) that Entity Resolution generated for the <code>MatchingWorkflow</code>.</p>"}, "workflowName": {"shape": "EntityName", "documentation": "<p>The name of the workflow.</p>"}}, "documentation": "<p>A list of <code>MatchingWorkflowSummary</code> objects, each of which contain the fields <code>WorkflowName</code>, <code>WorkflowArn</code>, <code>CreatedAt</code>, <code>UpdatedAt</code>.</p>"}, "NextToken": {"type": "string", "max": 1024, "min": 1, "pattern": "^[a-zA-Z_0-9-=+/]*$"}, "OutputAttribute": {"type": "structure", "required": ["name"], "members": {"hashed": {"shape": "Boolean", "documentation": "<p>Enables the ability to hash the column values in the output.</p>"}, "name": {"shape": "AttributeName", "documentation": "<p>A name of a column to be written to the output. This must be an <code>InputField</code> name in the schema mapping.</p>"}}, "documentation": "<p>A list of <code>OutputAttribute</code> objects, each of which have the fields <code>Name</code> and <code>Hashed</code>. Each of these objects selects a column to be included in the output table, and whether the values of the column should be hashed.</p>"}, "OutputSource": {"type": "structure", "required": ["output", "outputS3Path"], "members": {"KMSArn": {"shape": "KMSArn", "documentation": "<p>Customer KMS ARN for encryption at rest. If not provided, system will use an Entity Resolution managed KMS key.</p>"}, "applyNormalization": {"shape": "Boolean", "documentation": "<p>Normalizes the attributes defined in the schema in the input data. For example, if an attribute has an <code>AttributeType</code> of <code>PHONE_NUMBER</code>, and the data in the input table is in a format of 1234567890, Entity Resolution will normalize this field in the output to (*************.</p>"}, "output": {"shape": "OutputSourceOutputList", "documentation": "<p>A list of <code>OutputAttribute</code> objects, each of which have the fields <code>Name</code> and <code>Hashed</code>. Each of these objects selects a column to be included in the output table, and whether the values of the column should be hashed.</p>"}, "outputS3Path": {"shape": "S3Path", "documentation": "<p>The S3 path to which Entity Resolution will write the output table.</p>"}}, "documentation": "<p>A list of <code>OutputAttribute</code> objects, each of which have the fields <code>Name</code> and <code>Hashed</code>. Each of these objects selects a column to be included in the output table, and whether the values of the column should be hashed.</p>"}, "OutputSourceConfig": {"type": "list", "member": {"shape": "OutputSource"}, "max": 1, "min": 1}, "OutputSourceOutputList": {"type": "list", "member": {"shape": "OutputAttribute"}, "max": 750, "min": 0}, "ProviderEndpointConfiguration": {"type": "structure", "members": {"marketplaceConfiguration": {"shape": "ProviderMarketplaceConfiguration", "documentation": "<p>The identifiers of the provider service, from Data Exchange.</p>"}}, "documentation": "<p>The required configuration fields to use with the provider service.</p>", "union": true}, "ProviderIntermediateDataAccessConfiguration": {"type": "structure", "members": {"awsAccountIds": {"shape": "AwsAccountIdList", "documentation": "<p>The Amazon Web Services account that provider can use to read or write data into the customer's intermediate S3 bucket.</p>"}, "requiredBucketActions": {"shape": "RequiredBucketActionsList", "documentation": "<p>The S3 bucket actions that the provider requires permission for.</p>"}}, "documentation": "<p>The required configuration fields to give intermediate access to a provider service.</p>"}, "ProviderMarketplaceConfiguration": {"type": "structure", "required": ["assetId", "dataSetId", "listingId", "revisionId"], "members": {"assetId": {"shape": "String", "documentation": "<p>The asset ID on Data Exchange.</p>"}, "dataSetId": {"shape": "String", "documentation": "<p>The dataset ID on Data Exchange.</p>"}, "listingId": {"shape": "String", "documentation": "<p>The listing ID on Data Exchange.</p>"}, "revisionId": {"shape": "String", "documentation": "<p>The revision ID on Data Exchange.</p>"}}, "documentation": "<p>The identifiers of the provider service, from Data Exchange.</p>"}, "ProviderProperties": {"type": "structure", "required": ["providerServiceArn"], "members": {"intermediateSourceConfiguration": {"shape": "IntermediateSourceConfiguration", "documentation": "<p>The Amazon S3 location that temporarily stores your data while it processes. Your information won't be saved permanently.</p>"}, "providerConfiguration": {"shape": "Document", "documentation": "<p>The required configuration fields to use with the provider service.</p>"}, "providerServiceArn": {"shape": "ProviderServiceArn", "documentation": "<p>The ARN of the provider service.</p>"}}, "documentation": "<p>An object containing the <code>providerServiceARN</code>, <code>intermediateSourceConfiguration</code>, and <code>providerConfiguration</code>.</p>"}, "ProviderServiceArn": {"type": "string", "max": 255, "min": 20, "pattern": "^arn:(aws|aws-us-gov|aws-cn):(entityresolution):([a-z]{2}-[a-z-]+?-[0-9])::providerservice/([a-zA-Z0-9_-]+)/([a-zA-Z0-9_-]+)$"}, "ProviderServiceDisplayName": {"type": "string", "max": 255, "min": 0}, "ProviderServiceList": {"type": "list", "member": {"shape": "ProviderServiceSummary"}}, "ProviderServiceSummary": {"type": "structure", "required": ["providerName", "providerServiceArn", "providerServiceDisplayName", "providerServiceName", "providerServiceType"], "members": {"providerName": {"shape": "EntityName", "documentation": "<p>The name of the provider. This name is typically the company name.</p>"}, "providerServiceArn": {"shape": "ProviderServiceArn", "documentation": "<p>The ARN (Amazon Resource Name) that Entity Resolution generated for the <code>providerService</code>.</p>"}, "providerServiceDisplayName": {"shape": "ProviderServiceDisplayName", "documentation": "<p>The display name of the provider service.</p>"}, "providerServiceName": {"shape": "EntityName", "documentation": "<p>The name of the product that the provider service provides.</p>"}, "providerServiceType": {"shape": "ServiceType", "documentation": "<p>The type of provider service.</p>"}}, "documentation": "<p>A list of <code>ProviderService</code> objects, each of which contain the fields <code>providerName</code>, <code>providerServiceArn</code>, <code>providerServiceName</code>, and <code>providerServiceType</code>.</p>"}, "RecordAttributeMap": {"type": "map", "key": {"shape": "RecordAttributeMapKeyString"}, "value": {"shape": "RecordAttributeMapValueString"}, "sensitive": true}, "RecordAttributeMapKeyString": {"type": "string", "max": 255, "min": 0, "pattern": "^[a-zA-Z_0-9- \\t]*$"}, "RecordAttributeMapValueString": {"type": "string", "max": 255, "min": 0, "pattern": "^[a-zA-Z_0-9-.@ ()+\\t]*$"}, "RequiredBucketActionsList": {"type": "list", "member": {"shape": "String"}}, "ResolutionTechniques": {"type": "structure", "required": ["resolutionType"], "members": {"providerProperties": {"shape": "ProviderProperties", "documentation": "<p>The properties of the provider service.</p>"}, "resolutionType": {"shape": "ResolutionType", "documentation": "<p>The type of matching. There are two types of matching: <code>RULE_MATCHING</code> and <code>ML_MATCHING</code>.</p>"}, "ruleBasedProperties": {"shape": "RuleBasedProperties", "documentation": "<p>An object which defines the list of matching rules to run and has a field <code>Rules</code>, which is a list of rule objects.</p>"}}, "documentation": "<p>An object which defines the <code>resolutionType</code> and the <code>ruleBasedProperties</code>.</p>"}, "ResolutionType": {"type": "string", "enum": ["RULE_MATCHING", "ML_MATCHING", "PROVIDER"]}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The resource could not be found. <code>HTTP Status Code: 404</code> </p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "RoleArn": {"type": "string", "pattern": "^arn:aws:iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+$"}, "Rule": {"type": "structure", "required": ["matching<PERSON><PERSON>s", "ruleName"], "members": {"matchingKeys": {"shape": "RuleMatchingKeysList", "documentation": "<p>A list of <code>MatchingKeys</code>. The <code>MatchingKeys</code> must have been defined in the <code>SchemaMapping</code>. Two records are considered to match according to this rule if all of the <code>MatchingKeys</code> match.</p>"}, "ruleName": {"shape": "RuleRuleNameString", "documentation": "<p>A name for the matching rule.</p>"}}, "documentation": "<p>An object containing <code>RuleName</code>, and <code>MatchingKeys</code>.</p>"}, "RuleBasedProperties": {"type": "structure", "required": ["attributeMatchingModel", "rules"], "members": {"attributeMatchingModel": {"shape": "AttributeMatchingModel", "documentation": "<p>The comparison type. You can either choose <code>ONE_TO_ONE</code> or <code>MANY_TO_MANY</code> as the AttributeMatchingModel. When choosing <code>MANY_TO_MANY</code>, the system can match attributes across the sub-types of an attribute type. For example, if the value of the <code>Email</code> field of Profile A and the value of <code>BusinessEmail</code> field of Profile B matches, the two profiles are matched on the <code>Email</code> type. When choosing <code>ONE_TO_ONE</code> ,the system can only match if the sub-types are exact matches. For example, only when the value of the <code>Email</code> field of Profile A and the value of the <code>Email</code> field of Profile B matches, the two profiles are matched on the <code>Email</code> type.</p>"}, "rules": {"shape": "RuleBasedPropertiesRulesList", "documentation": "<p>A list of <code>Rule</code> objects, each of which have fields <code>RuleName</code> and <code>MatchingKeys</code>.</p>"}}, "documentation": "<p>An object which defines the list of matching rules to run and has a field <code>Rules</code>, which is a list of rule objects.</p>"}, "RuleBasedPropertiesRulesList": {"type": "list", "member": {"shape": "Rule"}, "max": 15, "min": 1}, "RuleMatchingKeysList": {"type": "list", "member": {"shape": "AttributeName"}, "max": 15, "min": 1}, "RuleRuleNameString": {"type": "string", "max": 255, "min": 0, "pattern": "^[a-zA-Z_0-9- \\t]*$"}, "S3Path": {"type": "string", "max": 1024, "min": 1, "pattern": "^s3://[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9](/.*)?$"}, "SchemaAttributeType": {"type": "string", "enum": ["NAME", "NAME_FIRST", "NAME_MIDDLE", "NAME_LAST", "ADDRESS", "ADDRESS_STREET1", "ADDRESS_STREET2", "ADDRESS_STREET3", "ADDRESS_CITY", "ADDRESS_STATE", "ADDRESS_COUNTRY", "ADDRESS_POSTALCODE", "PHONE", "PHONE_NUMBER", "PHONE_COUNTRYCODE", "EMAIL_ADDRESS", "UNIQUE_ID", "DATE", "STRING", "PROVIDER_ID"]}, "SchemaInputAttribute": {"type": "structure", "required": ["fieldName", "type"], "members": {"fieldName": {"shape": "AttributeName", "documentation": "<p>A string containing the field name.</p>"}, "groupName": {"shape": "AttributeName", "documentation": "<p>Instruct Entity Resolution to combine several columns into a unified column with the identical attribute type. For example, when working with columns such as first_name, middle_name, and last_name, assigning them a common <code>GroupName</code> will prompt Entity Resolution to concatenate them into a single value.</p>"}, "matchKey": {"shape": "AttributeName", "documentation": "<p>A key that allows grouping of multiple input attributes into a unified matching group. For example, let's consider a scenario where the source table contains various addresses, such as <code>business_address</code> and <code>shipping_address</code>. By assigning the <code>MatchKey</code> <i>Address</i> to both attributes, Entity Resolution will match records across these fields to create a consolidated matching group. If no <code>MatchKey</code> is specified for a column, it won't be utilized for matching purposes but will still be included in the output table.</p>"}, "subType": {"shape": "AttributeName", "documentation": "<p>The subtype of the attribute, selected from a list of values.</p>"}, "type": {"shape": "SchemaAttributeType", "documentation": "<p>The type of the attribute, selected from a list of values.</p>"}}, "documentation": "<p>An object containing <code>FieldName</code>, <code>Type</code>, <code>GroupName</code>, and <code>Match<PERSON>ey</code>.</p>"}, "SchemaInputAttributes": {"type": "list", "member": {"shape": "SchemaInputAttribute"}, "max": 25, "min": 2}, "SchemaMappingArn": {"type": "string", "pattern": "^arn:(aws|aws-us-gov|aws-cn):entityresolution:.*:[0-9]+:(schemamapping/.*)$"}, "SchemaMappingList": {"type": "list", "member": {"shape": "SchemaMappingSummary"}}, "SchemaMappingSummary": {"type": "structure", "required": ["createdAt", "hasWorkflows", "schemaArn", "schemaName", "updatedAt"], "members": {"createdAt": {"shape": "Timestamp", "documentation": "<p>The timestamp of when the <code>SchemaMapping</code> was created.</p>"}, "hasWorkflows": {"shape": "Boolean", "documentation": "<p>Specifies whether the schema mapping has been applied to a workflow.</p>"}, "schemaArn": {"shape": "SchemaMappingArn", "documentation": "<p>The ARN (Amazon Resource Name) that Entity Resolution generated for the <code>SchemaMapping</code>.</p>"}, "schemaName": {"shape": "EntityName", "documentation": "<p>The name of the schema.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp of when the <code>SchemaMapping</code> was last updated.</p>"}}, "documentation": "<p>An object containing <code>SchemaName</code>, <code>SchemaArn</code>, <code>CreatedAt</code>, and<code>UpdatedAt</code>.</p>"}, "ServiceType": {"type": "string", "enum": ["ASSIGNMENT", "ID_MAPPING"]}, "StartIdMappingJobInput": {"type": "structure", "required": ["workflowName"], "members": {"workflowName": {"shape": "EntityName", "documentation": "<p>The name of the ID mapping job to be retrieved.</p>", "location": "uri", "locationName": "workflowName"}}}, "StartIdMappingJobOutput": {"type": "structure", "required": ["jobId"], "members": {"jobId": {"shape": "JobId", "documentation": "<p>The ID of the job.</p>"}}}, "StartMatchingJobInput": {"type": "structure", "required": ["workflowName"], "members": {"workflowName": {"shape": "EntityName", "documentation": "<p>The name of the matching job to be retrieved.</p>", "location": "uri", "locationName": "workflowName"}}}, "StartMatchingJobOutput": {"type": "structure", "required": ["jobId"], "members": {"jobId": {"shape": "JobId", "documentation": "<p>The ID of the job.</p>"}}}, "String": {"type": "string"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 0}, "TagMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "max": 200, "min": 0}, "TagResourceInput": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "VeniceGlobalArn", "documentation": "<p>The ARN of the resource for which you want to view tags.</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}}}, "TagResourceOutput": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "ThrottlingException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The request was denied due to request throttling. <code>HTTP Status Code: 429</code> </p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true, "retryable": {"throttling": true}}, "Timestamp": {"type": "timestamp"}, "UntagResourceInput": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "VeniceGlobalArn", "documentation": "<p>The ARN of the resource for which you want to untag.</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>The list of tag keys to remove from the resource.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceOutput": {"type": "structure", "members": {}}, "UpdateIdMappingWorkflowInput": {"type": "structure", "required": ["idMappingTechniques", "inputSourceConfig", "outputSourceConfig", "roleArn", "workflowName"], "members": {"description": {"shape": "Description", "documentation": "<p>A description of the workflow.</p>"}, "idMappingTechniques": {"shape": "IdMappingTechniques", "documentation": "<p>An object which defines the <code>idMappingType</code> and the <code>providerProperties</code>.</p>"}, "inputSourceConfig": {"shape": "IdMappingWorkflowInputSourceConfig", "documentation": "<p>A list of <code>InputSource</code> objects, which have the fields <code>InputSourceARN</code> and <code>SchemaName</code>.</p>"}, "outputSourceConfig": {"shape": "IdMappingWorkflowOutputSourceConfig", "documentation": "<p>A list of <code>OutputSource</code> objects, each of which contains fields <code>OutputS3Path</code> and <code>KMSArn</code>.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role. Entity Resolution assumes this role to access resources on your behalf.</p>"}, "workflowName": {"shape": "EntityName", "documentation": "<p>The name of the workflow.</p>", "location": "uri", "locationName": "workflowName"}}}, "UpdateIdMappingWorkflowOutput": {"type": "structure", "required": ["idMappingTechniques", "inputSourceConfig", "outputSourceConfig", "roleArn", "workflowArn", "workflowName"], "members": {"description": {"shape": "Description", "documentation": "<p>A description of the workflow.</p>"}, "idMappingTechniques": {"shape": "IdMappingTechniques", "documentation": "<p>An object which defines the <code>idMappingType</code> and the <code>providerProperties</code>.</p>"}, "inputSourceConfig": {"shape": "IdMappingWorkflowInputSourceConfig", "documentation": "<p>A list of <code>InputSource</code> objects, which have the fields <code>InputSourceARN</code> and <code>SchemaName</code>.</p>"}, "outputSourceConfig": {"shape": "IdMappingWorkflowOutputSourceConfig", "documentation": "<p>A list of <code>OutputSource</code> objects, each of which contains fields <code>OutputS3Path</code> and <code>KMSArn</code>.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role. Entity Resolution assumes this role to access resources on your behalf.</p>"}, "workflowArn": {"shape": "IdMappingWorkflowArn", "documentation": "<p>The Amazon Resource Name (ARN) of the workflow role. Entity Resolution assumes this role to access resources on your behalf.</p>"}, "workflowName": {"shape": "EntityName", "documentation": "<p>The name of the workflow.</p>"}}}, "UpdateMatchingWorkflowInput": {"type": "structure", "required": ["inputSourceConfig", "outputSourceConfig", "resolutionTechniques", "roleArn", "workflowName"], "members": {"description": {"shape": "Description", "documentation": "<p>A description of the workflow.</p>"}, "incrementalRunConfig": {"shape": "IncrementalRunConfig", "documentation": "<p>An object which defines an incremental run type and has only <code>incrementalRunType</code> as a field.</p>"}, "inputSourceConfig": {"shape": "InputSourceConfig", "documentation": "<p>A list of <code>InputSource</code> objects, which have the fields <code>InputSourceARN</code> and <code>SchemaName</code>.</p>"}, "outputSourceConfig": {"shape": "OutputSourceConfig", "documentation": "<p>A list of <code>OutputSource</code> objects, each of which contains fields <code>OutputS3Path</code>, <code>ApplyNormalization</code>, and <code>Output</code>.</p>"}, "resolutionTechniques": {"shape": "ResolutionTechniques", "documentation": "<p>An object which defines the <code>resolutionType</code> and the <code>ruleBasedProperties</code>.</p>"}, "roleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role. Entity Resolution assumes this role to create resources on your behalf as part of workflow execution.</p>"}, "workflowName": {"shape": "EntityName", "documentation": "<p>The name of the workflow to be retrieved.</p>", "location": "uri", "locationName": "workflowName"}}}, "UpdateMatchingWorkflowOutput": {"type": "structure", "required": ["inputSourceConfig", "outputSourceConfig", "resolutionTechniques", "roleArn", "workflowName"], "members": {"description": {"shape": "Description", "documentation": "<p>A description of the workflow.</p>"}, "incrementalRunConfig": {"shape": "IncrementalRunConfig", "documentation": "<p>An object which defines an incremental run type and has only <code>incrementalRunType</code> as a field.</p>"}, "inputSourceConfig": {"shape": "InputSourceConfig", "documentation": "<p>A list of <code>InputSource</code> objects, which have the fields <code>InputSourceARN</code> and <code>SchemaName</code>.</p>"}, "outputSourceConfig": {"shape": "OutputSourceConfig", "documentation": "<p>A list of <code>OutputSource</code> objects, each of which contains fields <code>OutputS3Path</code>, <code>ApplyNormalization</code>, and <code>Output</code>.</p>"}, "resolutionTechniques": {"shape": "ResolutionTechniques", "documentation": "<p>An object which defines the <code>resolutionType</code> and the <code>ruleBasedProperties</code> </p>"}, "roleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role. Entity Resolution assumes this role to create resources on your behalf as part of workflow execution.</p>"}, "workflowName": {"shape": "EntityName", "documentation": "<p>The name of the workflow.</p>"}}}, "UpdateSchemaMappingInput": {"type": "structure", "required": ["mappedInputFields", "schemaName"], "members": {"description": {"shape": "Description", "documentation": "<p>A description of the schema.</p>"}, "mappedInputFields": {"shape": "SchemaInputAttributes", "documentation": "<p>A list of <code>MappedInputFields</code>. Each <code>MappedInputField</code> corresponds to a column the source data table, and contains column name plus additional information that Entity Resolution uses for matching.</p>"}, "schemaName": {"shape": "EntityName", "documentation": "<p>The name of the schema. There can't be multiple <code>SchemaMappings</code> with the same name.</p>", "location": "uri", "locationName": "schemaName"}}}, "UpdateSchemaMappingOutput": {"type": "structure", "required": ["mappedInputFields", "schemaArn", "schemaName"], "members": {"description": {"shape": "Description", "documentation": "<p>A description of the schema.</p>"}, "mappedInputFields": {"shape": "SchemaInputAttributes", "documentation": "<p>A list of <code>MappedInputFields</code>. Each <code>MappedInputField</code> corresponds to a column the source data table, and contains column name plus additional information that Entity Resolution uses for matching.</p>"}, "schemaArn": {"shape": "SchemaMappingArn", "documentation": "<p>The ARN (Amazon Resource Name) that Entity Resolution generated for the <code>SchemaMapping</code>.</p>"}, "schemaName": {"shape": "EntityName", "documentation": "<p>The name of the schema.</p>"}}}, "ValidationException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The input fails to satisfy the constraints specified by Entity Resolution. <code>HTTP Status Code: 400</code> </p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "VeniceGlobalArn": {"type": "string", "pattern": "^arn:(aws|aws-us-gov|aws-cn):(entityresolution):.*:[0-9]+:((schemamapping|matchingworkflow|idmappingworkflow)/[a-zA-Z0-9_-]+)$"}}, "documentation": "<p>Welcome to the <i>Entity Resolution API Reference</i>.</p> <p>Entity Resolution is an Amazon Web Services service that provides pre-configured entity resolution capabilities that enable developers and analysts at advertising and marketing companies to build an accurate and complete view of their consumers.</p> <p> With Entity Resolution, you can match source records containing consumer identifiers, such as name, email address, and phone number. This is true even when these records have incomplete or conflicting identifiers. For example, Entity Resolution can effectively match a source record from a customer relationship management (CRM) system with a source record from a marketing system containing campaign information.</p> <p>To learn more about Entity Resolution concepts, procedures, and best practices, see the <a href=\"https://docs.aws.amazon.com/entityresolution/latest/userguide/what-is-service.html\">Entity Resolution User Guide</a>.</p>"}