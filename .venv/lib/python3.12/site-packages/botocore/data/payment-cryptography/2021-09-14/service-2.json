{"version": "2.0", "metadata": {"apiVersion": "2021-09-14", "endpointPrefix": "controlplane.payment-cryptography", "jsonVersion": "1.0", "protocol": "json", "serviceFullName": "Payment Cryptography Control Plane", "serviceId": "Payment Cryptography", "signatureVersion": "v4", "signingName": "payment-cryptography", "targetPrefix": "PaymentCryptographyControlPlane", "uid": "payment-cryptography-2021-09-14"}, "operations": {"CreateAlias": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateAliasInput"}, "output": {"shape": "CreateAliasOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates an <i>alias</i>, or a friendly name, for an Amazon Web Services Payment Cryptography key. You can use an alias to identify a key in the console and when you call cryptographic operations such as <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/DataAPIReference/API_EncryptData.html\">EncryptData</a> or <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/DataAPIReference/API_DecryptData.html\">DecryptData</a>.</p> <p>You can associate the alias with any key in the same Amazon Web Services Region. Each alias is associated with only one key at a time, but a key can have multiple aliases. You can't create an alias without a key. The alias must be unique in the account and Amazon Web Services Region, but you can create another alias with the same name in a different Amazon Web Services Region.</p> <p>To change the key that's associated with the alias, call <a>UpdateAlias</a>. To delete the alias, call <a>DeleteAlias</a>. These operations don't affect the underlying key. To get the alias that you created, call <a>ListAliases</a>.</p> <p> <b>Cross-account use</b>: This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>DeleteAlias</a> </p> </li> <li> <p> <a>GetAlias</a> </p> </li> <li> <p> <a>ListAliases</a> </p> </li> <li> <p> <a>UpdateAlias</a> </p> </li> </ul>"}, "CreateKey": {"name": "<PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateKeyInput"}, "output": {"shape": "CreateKeyOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates an Amazon Web Services Payment Cryptography key, a logical representation of a cryptographic key, that is unique in your account and Amazon Web Services Region. You use keys for cryptographic functions such as encryption and decryption. </p> <p>In addition to the key material used in cryptographic operations, an Amazon Web Services Payment Cryptography key includes metadata such as the key ARN, key usage, key origin, creation date, description, and key state.</p> <p>When you create a key, you specify both immutable and mutable data about the key. The immutable data contains key attributes that defines the scope and cryptographic operations that you can perform using the key, for example key class (example: <code>SYMMETRIC_KEY</code>), key algorithm (example: <code>TDES_2KEY</code>), key usage (example: <code>TR31_P0_PIN_ENCRYPTION_KEY</code>) and key modes of use (example: <code>Encrypt</code>). For information about valid combinations of key attributes, see <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/keys-validattributes.html\">Understanding key attributes</a> in the <i>Amazon Web Services Payment Cryptography User Guide</i>. The mutable data contained within a key includes usage timestamp and key deletion timestamp and can be modified after creation.</p> <p>Amazon Web Services Payment Cryptography binds key attributes to keys using key blocks when you store or export them. Amazon Web Services Payment Cryptography stores the key contents wrapped and never stores or transmits them in the clear. </p> <p> <b>Cross-account use</b>: This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>DeleteKey</a> </p> </li> <li> <p> <a>GetKey</a> </p> </li> <li> <p> <a>ListKeys</a> </p> </li> </ul>"}, "DeleteAlias": {"name": "DeleteAlias", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteAliasInput"}, "output": {"shape": "DeleteAliasOutput"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes the alias, but doesn't affect the underlying key.</p> <p>Each key can have multiple aliases. To get the aliases of all keys, use the <a>ListAliases</a> operation. To change the alias of a key, first use <a><PERSON>ete<PERSON><PERSON><PERSON></a> to delete the current alias and then use <a><PERSON><PERSON><PERSON><PERSON><PERSON></a> to create a new alias. To associate an existing alias with a different key, call <a>UpdateAlias</a>.</p> <p> <b>Cross-account use:</b> This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>CreateAlias</a> </p> </li> <li> <p> <a>GetAlias</a> </p> </li> <li> <p> <a>ListAliases</a> </p> </li> <li> <p> <a>UpdateAlias</a> </p> </li> </ul>"}, "DeleteKey": {"name": "DeleteKey", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteKeyInput"}, "output": {"shape": "DeleteKeyOutput"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes the key material and all metadata associated with Amazon Web Services Payment Cryptography key.</p> <p>Key deletion is irreversible. After a key is deleted, you can't perform cryptographic operations using the key. For example, you can't decrypt data that was encrypted by a deleted Amazon Web Services Payment Cryptography key, and the data may become unrecoverable. Because key deletion is destructive, Amazon Web Services Payment Cryptography has a safety mechanism to prevent accidental deletion of a key. When you call this operation, Amazon Web Services Payment Cryptography disables the specified key but doesn't delete it until after a waiting period. The default waiting period is 7 days. To set a different waiting period, set <code>DeleteKeyInDays</code>. During the waiting period, the <code>KeyState</code> is <code>DELETE_PENDING</code>. After the key is deleted, the <code>KeyState</code> is <code>DELETE_COMPLETE</code>.</p> <p>If you delete key material, you can use <a>ImportKey</a> to reimport the same key material into the Amazon Web Services Payment Cryptography key.</p> <p>You should delete a key only when you are sure that you don't need to use it anymore and no other parties are utilizing this key. If you aren't sure, consider deactivating it instead by calling <a>StopKeyUsage</a>.</p> <p> <b>Cross-account use:</b> This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>RestoreKey</a> </p> </li> <li> <p> <a>StartKeyUsage</a> </p> </li> <li> <p> <a>StopKeyUsage</a> </p> </li> </ul>"}, "ExportKey": {"name": "ExportKey", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ExportKeyInput"}, "output": {"shape": "ExportKeyOutput"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Exports a key from Amazon Web Services Payment Cryptography using either ANSI X9 TR-34 or TR-31 key export standard.</p> <p>Amazon Web Services Payment Cryptography simplifies main or root key exchange process by eliminating the need of a paper-based key exchange process. It takes a modern and secure approach based of the ANSI X9 TR-34 key exchange standard.</p> <p>You can use <code>ExportKey</code> to export main or root keys such as KEK (Key Encryption Key), using asymmetric key exchange technique following ANSI X9 TR-34 standard. The ANSI X9 TR-34 standard uses asymmetric keys to establishes bi-directional trust between the two parties exchanging keys. After which you can export working keys using the ANSI X9 TR-31 symmetric key exchange standard as mandated by PCI PIN. Using this operation, you can share your Amazon Web Services Payment Cryptography generated keys with other service partners to perform cryptographic operations outside of Amazon Web Services Payment Cryptography </p> <p> <b>TR-34 key export</b> </p> <p>Amazon Web Services Payment Cryptography uses TR-34 asymmetric key exchange standard to export main keys such as KEK. In TR-34 terminology, the sending party of the key is called Key Distribution Host (KDH) and the receiving party of the key is called Key Receiving Host (KRH). In key export process, KDH is Amazon Web Services Payment Cryptography which initiates key export. <PERSON><PERSON><PERSON> is the user receiving the key. Before you initiate TR-34 key export, you must obtain an export token by calling <a>GetParametersForExport</a>. This operation also returns the signing key certificate that KDH uses to sign the wrapped key to generate a TR-34 wrapped key block. The export token expires after 7 days.</p> <p>Set the following parameters:</p> <dl> <dt>CertificateAuthorityPublicKeyIdentifier</dt> <dd> <p>The <code>KeyARN</code> of the certificate chain that will sign the wrapping key certificate. This must exist within Amazon Web Services Payment Cryptography before you initiate TR-34 key export. If it does not exist, you can import it by calling <a>ImportKey</a> for <code>RootCertificatePublicKey</code>.</p> </dd> <dt>ExportToken</dt> <dd> <p>Obtained from KDH by calling <a>GetParametersForExport</a>.</p> </dd> <dt>WrappingKeyCertificate</dt> <dd> <p>Amazon Web Services Payment Cryptography uses this to wrap the key under export.</p> </dd> </dl> <p>When this operation is successful, Amazon Web Services Payment Cryptography returns the TR-34 wrapped key block. </p> <p> <b>TR-31 key export</b> </p> <p>Amazon Web Services Payment Cryptography uses TR-31 symmetric key exchange standard to export working keys. In TR-31, you must use a main key such as KEK to encrypt or wrap the key under export. To establish a KEK, you can use <a>CreateKey</a> or <a>ImportKey</a>. When this operation is successful, Amazon Web Services Payment Cryptography returns a TR-31 wrapped key block. </p> <p> <b>Cross-account use:</b> This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>GetParametersForExport</a> </p> </li> <li> <p> <a>ImportKey</a> </p> </li> </ul>"}, "GetAlias": {"name": "GetAlias", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetAliasInput"}, "output": {"shape": "GetAliasOutput"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the Amazon Web Services Payment Cryptography key associated with the alias.</p> <p> <b>Cross-account use:</b> This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a><PERSON><PERSON><PERSON><PERSON><PERSON></a> </p> </li> <li> <p> <a>DeleteAlias</a> </p> </li> <li> <p> <a>ListAliases</a> </p> </li> <li> <p> <a>UpdateAlias</a> </p> </li> </ul>"}, "GetKey": {"name": "Get<PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetKeyInput"}, "output": {"shape": "GetKeyOutput"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the key material for an Amazon Web Services Payment Cryptography key, including the immutable and mutable data specified when the key was created.</p> <p> <b>Cross-account use:</b> This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>Create<PERSON>ey</a> </p> </li> <li> <p> <a>DeleteKey</a> </p> </li> <li> <p> <a>ListKeys</a> </p> </li> </ul>"}, "GetParametersForExport": {"name": "GetParametersForExport", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetParametersForExportInput"}, "output": {"shape": "GetParametersForExportOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the export token and the signing key certificate to initiate a TR-34 key export from Amazon Web Services Payment Cryptography.</p> <p>The signing key certificate signs the wrapped key under export within the TR-34 key payload. The export token and signing key certificate must be in place and operational before calling <a>ExportKey</a>. The export token expires in 7 days. You can use the same export token to export multiple keys from your service account.</p> <p> <b>Cross-account use:</b> This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>ExportKey</a> </p> </li> <li> <p> <a>GetParametersForImport</a> </p> </li> </ul>"}, "GetParametersForImport": {"name": "GetParametersForImport", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetParametersForImportInput"}, "output": {"shape": "GetParametersForImportOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the import token and the wrapping key certificate to initiate a TR-34 key import into Amazon Web Services Payment Cryptography.</p> <p>The wrapping key certificate wraps the key under import within the TR-34 key payload. The import token and wrapping key certificate must be in place and operational before calling <a>ImportKey</a>. The import token expires in 7 days. The same import token can be used to import multiple keys into your service account.</p> <p> <b>Cross-account use:</b> This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>GetParametersForExport</a> </p> </li> <li> <p> <a>ImportKey</a> </p> </li> </ul>"}, "GetPublicKeyCertificate": {"name": "GetPublicKeyCertificate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetPublicKeyCertificateInput"}, "output": {"shape": "GetPublicKeyCertificateOutput"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the public key certificate of the asymmetric key pair that exists within Amazon Web Services Payment Cryptography.</p> <p>Unlike the private key of an asymmetric key, which never leaves Amazon Web Services Payment Cryptography unencrypted, callers with <code>GetPublicKeyCertificate</code> permission can download the public key certificate of the asymmetric key. You can share the public key certificate to allow others to encrypt messages and verify signatures outside of Amazon Web Services Payment Cryptography</p> <p> <b>Cross-account use:</b> This operation can't be used across different Amazon Web Services accounts.</p>"}, "ImportKey": {"name": "ImportKey", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ImportKeyInput"}, "output": {"shape": "ImportKeyOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Imports keys and public key certificates into Amazon Web Services Payment Cryptography.</p> <p>Amazon Web Services Payment Cryptography simplifies main or root key exchange process by eliminating the need of a paper-based key exchange process. It takes a modern and secure approach based of the ANSI X9 TR-34 key exchange standard. </p> <p>You can use <code>ImportKey</code> to import main or root keys such as KEK (Key Encryption Key) using asymmetric key exchange technique following the ANSI X9 TR-34 standard. The ANSI X9 TR-34 standard uses asymmetric keys to establishes bi-directional trust between the two parties exchanging keys. </p> <p>After you have imported a main or root key, you can import working keys to perform various cryptographic operations within Amazon Web Services Payment Cryptography using the ANSI X9 TR-31 symmetric key exchange standard as mandated by PCI PIN.</p> <p>You can also import a <i>root public key certificate</i>, a self-signed certificate used to sign other public key certificates, or a <i>trusted public key certificate</i> under an already established root public key certificate. </p> <p> <b>To import a public root key certificate</b> </p> <p>Using this operation, you can import the public component (in PEM cerificate format) of your private root key. You can use the imported public root key certificate for digital signatures, for example signing wrapping key or signing key in TR-34, within your Amazon Web Services Payment Cryptography account. </p> <p>Set the following parameters:</p> <ul> <li> <p> <code>KeyMaterial</code>: <code>RootCertificatePublicKey</code> </p> </li> <li> <p> <code>KeyClass</code>: <code>PUBLIC_KEY</code> </p> </li> <li> <p> <code>KeyModesOfUse</code>: <code>Verify</code> </p> </li> <li> <p> <code>KeyUsage</code>: <code>TR31_S0_ASYMMETRIC_KEY_FOR_DIGITAL_SIGNATURE</code> </p> </li> <li> <p> <code>PublicKeyCertificate</code>: The certificate authority used to sign the root public key certificate.</p> </li> </ul> <p> <b>To import a trusted public key certificate</b> </p> <p>The root public key certificate must be in place and operational before you import a trusted public key certificate. Set the following parameters:</p> <ul> <li> <p> <code>KeyMaterial</code>: <code>TrustedCertificatePublicKey</code> </p> </li> <li> <p> <code>CertificateAuthorityPublicKeyIdentifier</code>: <code>KeyArn</code> of the <code>RootCertificatePublicKey</code>.</p> </li> <li> <p> <code>KeyModesOfUse</code> and <code>KeyUsage</code>: Corresponding to the cryptographic operations such as wrap, sign, or encrypt that you will allow the trusted public key certificate to perform.</p> </li> <li> <p> <code>PublicKeyCertificate</code>: The certificate authority used to sign the trusted public key certificate.</p> </li> </ul> <p> <b>Import main keys</b> </p> <p>Amazon Web Services Payment Cryptography uses TR-34 asymmetric key exchange standard to import main keys such as KEK. In TR-34 terminology, the sending party of the key is called Key Distribution Host (KDH) and the receiving party of the key is called Key Receiving Host (KRH). During the key import process, KDH is the user who initiates the key import and KRH is Amazon Web Services Payment Cryptography who receives the key. Before initiating TR-34 key import, you must obtain an import token by calling <a>GetParametersForImport</a>. This operation also returns the wrapping key certificate that KDH uses wrap key under import to generate a TR-34 wrapped key block. The import token expires after 7 days.</p> <p>Set the following parameters:</p> <ul> <li> <p> <code>CertificateAuthorityPublicKeyIdentifier</code>: The <code>KeyArn</code> of the certificate chain that will sign the signing key certificate and should exist within Amazon Web Services Payment Cryptography before initiating TR-34 key import. If it does not exist, you can import it by calling by calling <code>ImportKey</code> for <code>RootCertificatePublicKey</code>.</p> </li> <li> <p> <code>ImportToken</code>: Obtained from KRH by calling <a>GetParametersForImport</a>.</p> </li> <li> <p> <code>WrappedKeyBlock</code>: The TR-34 wrapped key block from KDH. It contains the KDH key under import, wrapped with KRH provided wrapping key certificate and signed by the KDH private signing key. This TR-34 key block is generated by the KDH Hardware Security Module (HSM) outside of Amazon Web Services Payment Cryptography.</p> </li> <li> <p> <code>SigningKeyCertificate</code>: The public component of the private key that signed the KDH TR-34 wrapped key block. In PEM certificate format.</p> </li> </ul> <note> <p>TR-34 is intended primarily to exchange 3DES keys. Your ability to export AES-128 and larger AES keys may be dependent on your source system.</p> </note> <p> <b>Import working keys</b> </p> <p>Amazon Web Services Payment Cryptography uses TR-31 symmetric key exchange standard to import working keys. A KEK must be established within Amazon Web Services Payment Cryptography by using TR-34 key import. To initiate a TR-31 key import, set the following parameters:</p> <ul> <li> <p> <code>WrappedKeyBlock</code>: The key under import and encrypted using KEK. The TR-31 key block generated by your HSM outside of Amazon Web Services Payment Cryptography. </p> </li> <li> <p> <code>WrappingKeyIdentifier</code>: The <code>KeyArn</code> of the KEK that Amazon Web Services Payment Cryptography uses to decrypt or unwrap the key under import.</p> </li> </ul> <p> <b>Cross-account use:</b> This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>ExportKey</a> </p> </li> <li> <p> <a>GetParametersForImport</a> </p> </li> </ul>"}, "ListAliases": {"name": "ListAliases", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAliasesInput"}, "output": {"shape": "ListAliasesOutput"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the aliases for all keys in the caller's Amazon Web Services account and Amazon Web Services Region. You can filter the list of aliases. For more information, see <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/keys-managealias.html\">Using aliases</a> in the <i>Amazon Web Services Payment Cryptography User Guide</i>.</p> <p>This is a paginated operation, which means that each response might contain only a subset of all the aliases. When the response contains only a subset of aliases, it includes a <code>NextToken</code> value. Use this value in a subsequent <code>ListAliases</code> request to get more aliases. When you receive a response with no NextToken (or an empty or null value), that means there are no more aliases to get.</p> <p> <b>Cross-account use:</b> This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>CreateAlias</a> </p> </li> <li> <p> <a>Delete<PERSON>lias</a> </p> </li> <li> <p> <a>Get<PERSON>lias</a> </p> </li> <li> <p> <a>UpdateAlias</a> </p> </li> </ul>"}, "ListKeys": {"name": "ListKeys", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListKeysInput"}, "output": {"shape": "ListKeysOutput"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the keys in the caller's Amazon Web Services account and Amazon Web Services Region. You can filter the list of keys.</p> <p>This is a paginated operation, which means that each response might contain only a subset of all the keys. When the response contains only a subset of keys, it includes a <code>NextToken</code> value. Use this value in a subsequent <code>ListKeys</code> request to get more keys. When you receive a response with no NextToken (or an empty or null value), that means there are no more keys to get.</p> <p> <b>Cross-account use:</b> This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>Create<PERSON>ey</a> </p> </li> <li> <p> <a>DeleteKey</a> </p> </li> <li> <p> <a>GetKey</a> </p> </li> </ul>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceInput"}, "output": {"shape": "ListTagsForResourceOutput"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the tags for an Amazon Web Services resource.</p> <p>This is a paginated operation, which means that each response might contain only a subset of all the tags. When the response contains only a subset of tags, it includes a <code>NextToken</code> value. Use this value in a subsequent <code>ListTagsForResource</code> request to get more tags. When you receive a response with no NextToken (or an empty or null value), that means there are no more tags to get.</p> <p> <b>Cross-account use:</b> This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>TagResource</a> </p> </li> <li> <p> <a>UntagResource</a> </p> </li> </ul>"}, "RestoreKey": {"name": "<PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RestoreKeyInput"}, "output": {"shape": "RestoreKeyOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Cancels a scheduled key deletion during the waiting period. Use this operation to restore a <code>Key</code> that is scheduled for deletion.</p> <p>During the waiting period, the <code>KeyState</code> is <code>DELETE_PENDING</code> and <code>deletePendingTimestamp</code> contains the date and time after which the <code>Key</code> will be deleted. After <code>Key</code> is restored, the <code>KeyState</code> is <code>CREATE_COMPLETE</code>, and the value for <code>deletePendingTimestamp</code> is removed.</p> <p> <b>Cross-account use:</b> This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>DeleteKey</a> </p> </li> <li> <p> <a>StartKeyUsage</a> </p> </li> <li> <p> <a>StopKeyUsage</a> </p> </li> </ul>"}, "StartKeyUsage": {"name": "StartKeyUsage", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartKeyUsageInput"}, "output": {"shape": "StartKeyUsageOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Enables an Amazon Web Services Payment Cryptography key, which makes it active for cryptographic operations within Amazon Web Services Payment Cryptography</p> <p> <b>Cross-account use:</b> This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>StopKeyUsage</a> </p> </li> </ul>"}, "StopKeyUsage": {"name": "StopKeyUsage", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StopKeyUsageInput"}, "output": {"shape": "StopKeyUsageOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Disables an Amazon Web Services Payment Cryptography key, which makes it inactive within Amazon Web Services Payment Cryptography.</p> <p>You can use this operation instead of <a>DeleteKey</a> to deactivate a key. You can enable the key in the future by calling <a>StartKeyUsage</a>.</p> <p> <b>Cross-account use:</b> This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>DeleteKey</a> </p> </li> <li> <p> <a>StartKeyUsage</a> </p> </li> </ul>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceInput"}, "output": {"shape": "TagResourceOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Adds or edits tags on an Amazon Web Services Payment Cryptography key.</p> <note> <p>Tagging or untagging an Amazon Web Services Payment Cryptography key can allow or deny permission to the key.</p> </note> <p>Each tag consists of a tag key and a tag value, both of which are case-sensitive strings. The tag value can be an empty (null) string. To add a tag, specify a new tag key and a tag value. To edit a tag, specify an existing tag key and a new tag value. You can also add tags to an Amazon Web Services Payment Cryptography key when you create it with <a>CreateKey</a>.</p> <p> <b>Cross-account use:</b> This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>ListTagsForResource</a> </p> </li> <li> <p> <a>UntagResource</a> </p> </li> </ul>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceInput"}, "output": {"shape": "UntagResourceOutput"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes a tag from an Amazon Web Services Payment Cryptography key.</p> <note> <p>Tagging or untagging an Amazon Web Services Payment Cryptography key can allow or deny permission to the key.</p> </note> <p> <b>Cross-account use:</b> This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>ListTagsForResource</a> </p> </li> <li> <p> <a>TagResource</a> </p> </li> </ul>"}, "UpdateAlias": {"name": "Update<PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateAliasInput"}, "output": {"shape": "UpdateAliasOutput"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Associates an existing Amazon Web Services Payment Cryptography alias with a different key. Each alias is associated with only one Amazon Web Services Payment Cryptography key at a time, although a key can have multiple aliases. The alias and the Amazon Web Services Payment Cryptography key must be in the same Amazon Web Services account and Amazon Web Services Region</p> <p> <b>Cross-account use:</b> This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>CreateAlias</a> </p> </li> <li> <p> <a>DeleteAlias</a> </p> </li> <li> <p> <a>GetAlias</a> </p> </li> <li> <p> <a>ListAliases</a> </p> </li> </ul>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>You do not have sufficient access to perform this action.</p>", "exception": true}, "Alias": {"type": "structure", "required": ["AliasName"], "members": {"AliasName": {"shape": "AliasName", "documentation": "<p>A friendly name that you can use to refer to a key. The value must begin with <code>alias/</code>.</p> <important> <p>Do not include confidential or sensitive information in this field. This field may be displayed in plaintext in CloudTrail logs and other output.</p> </important>"}, "KeyArn": {"shape": "KeyArn", "documentation": "<p>The <code>KeyARN</code> of the key associated with the alias.</p>"}}, "documentation": "<p>Contains information about an alias.</p>"}, "AliasName": {"type": "string", "max": 256, "min": 7, "pattern": "^alias/[a-zA-Z0-9/_-]+$"}, "Aliases": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}}, "Boolean": {"type": "boolean", "box": true}, "CertificateType": {"type": "string", "max": 32768, "min": 1, "pattern": "^[^\\[;\\]<>]+$", "sensitive": true}, "ConflictException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>This request can cause an inconsistent state for the resource.</p>", "exception": true}, "CreateAliasInput": {"type": "structure", "required": ["AliasName"], "members": {"AliasName": {"shape": "AliasName", "documentation": "<p>A friendly name that you can use to refer a key. An alias must begin with <code>alias/</code> followed by a name, for example <code>alias/ExampleAlias</code>. It can contain only alphanumeric characters, forward slashes (/), underscores (_), and dashes (-).</p> <important> <p>Don't include confidential or sensitive information in this field. This field may be displayed in plaintext in CloudTrail logs and other output.</p> </important>"}, "KeyArn": {"shape": "KeyArn", "documentation": "<p>The <code>KeyARN</code> of the key to associate with the alias.</p>"}}}, "CreateAliasOutput": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"Alias": {"shape": "<PERSON><PERSON>", "documentation": "<p>The alias for the key.</p>"}}}, "CreateKeyInput": {"type": "structure", "required": ["Exportable", "KeyAttributes"], "members": {"Enabled": {"shape": "Boolean", "documentation": "<p>Specifies whether to enable the key. If the key is enabled, it is activated for use within the service. If the key not enabled, then it is created but not activated. The default value is enabled.</p>"}, "Exportable": {"shape": "Boolean", "documentation": "<p>Specifies whether the key is exportable from the service.</p>"}, "KeyAttributes": {"shape": "KeyAttributes", "documentation": "<p>The role of the key, the algorithm it supports, and the cryptographic operations allowed with the key. This data is immutable after the key is created.</p>"}, "KeyCheckValueAlgorithm": {"shape": "KeyCheckValueAlgorithm", "documentation": "<p>The algorithm that Amazon Web Services Payment Cryptography uses to calculate the key check value (KCV) for DES and AES keys.</p> <p>For DES key, the KCV is computed by encrypting 8 bytes, each with value '00', with the key to be checked and retaining the 3 highest order bytes of the encrypted result. For AES key, the KCV is computed by encrypting 8 bytes, each with value '01', with the key to be checked and retaining the 3 highest order bytes of the encrypted result.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>The tags to attach to the key. Each tag consists of a tag key and a tag value. Both the tag key and the tag value are required, but the tag value can be an empty (null) string. You can't have more than one tag on an Amazon Web Services Payment Cryptography key with the same tag key. </p> <p>To use this parameter, you must have <code>TagResource</code> permission.</p> <important> <p>Don't include confidential or sensitive information in this field. This field may be displayed in plaintext in CloudTrail logs and other output.</p> </important> <note> <p>Tagging or untagging an Amazon Web Services Payment Cryptography key can allow or deny permission to the key.</p> </note>"}}}, "CreateKeyOutput": {"type": "structure", "required": ["Key"], "members": {"Key": {"shape": "Key", "documentation": "<p>The key material that contains all the key attributes.</p>"}}}, "DeleteAliasInput": {"type": "structure", "required": ["AliasName"], "members": {"AliasName": {"shape": "AliasName", "documentation": "<p>A friendly name that you can use to refer Amazon Web Services Payment Cryptography key. This value must begin with <code>alias/</code> followed by a name, such as <code>alias/ExampleAlias</code>.</p>"}}}, "DeleteAliasOutput": {"type": "structure", "members": {}}, "DeleteKeyInput": {"type": "structure", "required": ["KeyIdentifier"], "members": {"DeleteKeyInDays": {"shape": "DeleteKeyInputDeleteKeyInDaysInteger", "documentation": "<p>The waiting period for key deletion. The default value is seven days.</p>"}, "KeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>KeyARN</code> of the key that is scheduled for deletion.</p>"}}}, "DeleteKeyInputDeleteKeyInDaysInteger": {"type": "integer", "box": true, "max": 180, "min": 3}, "DeleteKeyOutput": {"type": "structure", "required": ["Key"], "members": {"Key": {"shape": "Key", "documentation": "<p>The <code>KeyARN</code> of the key that is scheduled for deletion.</p>"}}}, "ExportKeyInput": {"type": "structure", "required": ["ExportKeyIdentifier", "KeyMaterial"], "members": {"ExportKeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>KeyARN</code> of the key under export from Amazon Web Services Payment Cryptography.</p>"}, "KeyMaterial": {"shape": "ExportKeyMaterial", "documentation": "<p>The key block format type, for example, TR-34 or TR-31, to use during key material export.</p>"}}}, "ExportKeyMaterial": {"type": "structure", "members": {"Tr31KeyBlock": {"shape": "ExportTr31KeyBlock", "documentation": "<p>Parameter information for key material export using TR-31 standard.</p>"}, "Tr34KeyBlock": {"shape": "ExportTr34KeyBlock", "documentation": "<p>Parameter information for key material export using TR-34 standard.</p>"}}, "documentation": "<p>Parameter information for key material export from Amazon Web Services Payment Cryptography.</p>", "union": true}, "ExportKeyOutput": {"type": "structure", "members": {"WrappedKey": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The key material under export as a TR-34 or TR-31 wrapped key block.</p>"}}}, "ExportTokenId": {"type": "string", "pattern": "^export-token-[0-9a-zA-Z]{16,64}$"}, "ExportTr31KeyBlock": {"type": "structure", "required": ["WrappingKeyIdentifier"], "members": {"WrappingKeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>KeyARN</code> of the the wrapping key. This key encrypts or wraps the key under export for TR-31 key block generation.</p>"}}, "documentation": "<p>Parameter information for key material export using TR-31 standard.</p>"}, "ExportTr34KeyBlock": {"type": "structure", "required": ["CertificateAuthorityPublicKeyIdentifier", "ExportToken", "KeyBlockFormat", "WrappingKeyCertificate"], "members": {"CertificateAuthorityPublicKeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>KeyARN</code> of the certificate chain that signs the wrapping key certificate during TR-34 key export.</p>"}, "ExportToken": {"shape": "ExportTokenId", "documentation": "<p>The export token to initiate key export from Amazon Web Services Payment Cryptography. It also contains the signing key certificate that will sign the wrapped key during TR-34 key block generation. Call <a>GetParametersForExport</a> to receive an export token. It expires after 7 days. You can use the same export token to export multiple keys from the same service account.</p>"}, "KeyBlockFormat": {"shape": "Tr34KeyBlockFormat", "documentation": "<p>The format of key block that Amazon Web Services Payment Cryptography will use during key export.</p>"}, "RandomNonce": {"shape": "HexLength16", "documentation": "<p>A random number value that is unique to the TR-34 key block generated using 2 pass. The operation will fail, if a random nonce value is not provided for a TR-34 key block generated using 2 pass.</p>"}, "WrappingKeyCertificate": {"shape": "CertificateType", "documentation": "<p>The <code>KeyARN</code> of the wrapping key certificate. Amazon Web Services Payment Cryptography uses this certificate to wrap the key under export.</p>"}}, "documentation": "<p>Parameter information for key material export using TR-34 standard.</p>"}, "GetAliasInput": {"type": "structure", "required": ["AliasName"], "members": {"AliasName": {"shape": "AliasName", "documentation": "<p>The alias of the Amazon Web Services Payment Cryptography key.</p>"}}}, "GetAliasOutput": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"Alias": {"shape": "<PERSON><PERSON>", "documentation": "<p>The alias of the Amazon Web Services Payment Cryptography key.</p>"}}}, "GetKeyInput": {"type": "structure", "required": ["KeyIdentifier"], "members": {"KeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>KeyARN</code> of the Amazon Web Services Payment Cryptography key.</p>"}}}, "GetKeyOutput": {"type": "structure", "required": ["Key"], "members": {"Key": {"shape": "Key", "documentation": "<p>The key material, including the immutable and mutable data for the key.</p>"}}}, "GetParametersForExportInput": {"type": "structure", "required": ["KeyMaterialType", "SigningKeyAlgorithm"], "members": {"KeyMaterialType": {"shape": "KeyMaterialType", "documentation": "<p>The key block format type (for example, TR-34 or TR-31) to use during key material export. Export token is only required for a TR-34 key export, <code>TR34_KEY_BLOCK</code>. Export token is not required for TR-31 key export.</p>"}, "SigningKeyAlgorithm": {"shape": "KeyAlgorithm", "documentation": "<p>The signing key algorithm to generate a signing key certificate. This certificate signs the wrapped key under export within the TR-34 key block cryptogram. <code>RSA_2048</code> is the only signing key algorithm allowed.</p>"}}}, "GetParametersForExportOutput": {"type": "structure", "required": ["ExportToken", "ParametersValidUntilTimestamp", "SigningKeyAlgorithm", "SigningKeyCertificate", "SigningKeyCertificate<PERSON><PERSON>n"], "members": {"ExportToken": {"shape": "ExportTokenId", "documentation": "<p>The export token to initiate key export from Amazon Web Services Payment Cryptography. The export token expires after 7 days. You can use the same export token to export multiple keys from the same service account.</p>"}, "ParametersValidUntilTimestamp": {"shape": "Timestamp", "documentation": "<p>The validity period of the export token.</p>"}, "SigningKeyAlgorithm": {"shape": "KeyAlgorithm", "documentation": "<p>The algorithm of the signing key certificate for use in TR-34 key block generation. <code>RSA_2048</code> is the only signing key algorithm allowed.</p>"}, "SigningKeyCertificate": {"shape": "CertificateType", "documentation": "<p>The signing key certificate of the public key for signature within the TR-34 key block cryptogram. The certificate expires after 7 days.</p>"}, "SigningKeyCertificateChain": {"shape": "CertificateType", "documentation": "<p>The certificate chain that signed the signing key certificate. This is the root certificate authority (CA) within your service account.</p>"}}}, "GetParametersForImportInput": {"type": "structure", "required": ["KeyMaterialType", "WrappingKeyAlgorithm"], "members": {"KeyMaterialType": {"shape": "KeyMaterialType", "documentation": "<p>The key block format type such as TR-34 or TR-31 to use during key material import. Import token is only required for TR-34 key import <code>TR34_KEY_BLOCK</code>. Import token is not required for TR-31 key import.</p>"}, "WrappingKeyAlgorithm": {"shape": "KeyAlgorithm", "documentation": "<p>The wrapping key algorithm to generate a wrapping key certificate. This certificate wraps the key under import within the TR-34 key block cryptogram. <code>RSA_2048</code> is the only wrapping key algorithm allowed.</p>"}}}, "GetParametersForImportOutput": {"type": "structure", "required": ["ImportToken", "ParametersValidUntilTimestamp", "WrappingKeyAlgorithm", "WrappingKeyCertificate", "WrappingKeyCertificate<PERSON><PERSON><PERSON>"], "members": {"ImportToken": {"shape": "ImportTokenId", "documentation": "<p>The import token to initiate key import into Amazon Web Services Payment Cryptography. The import token expires after 7 days. You can use the same import token to import multiple keys to the same service account.</p>"}, "ParametersValidUntilTimestamp": {"shape": "Timestamp", "documentation": "<p>The validity period of the import token.</p>"}, "WrappingKeyAlgorithm": {"shape": "KeyAlgorithm", "documentation": "<p>The algorithm of the wrapping key for use within TR-34 key block. <code>RSA_2048</code> is the only wrapping key algorithm allowed.</p>"}, "WrappingKeyCertificate": {"shape": "CertificateType", "documentation": "<p>The wrapping key certificate of the wrapping key for use within the TR-34 key block. The certificate expires in 7 days.</p>"}, "WrappingKeyCertificateChain": {"shape": "CertificateType", "documentation": "<p>The Amazon Web Services Payment Cryptography certificate chain that signed the wrapping key certificate. This is the root certificate authority (CA) within your service account.</p>"}}}, "GetPublicKeyCertificateInput": {"type": "structure", "required": ["KeyIdentifier"], "members": {"KeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>KeyARN</code> of the asymmetric key pair.</p>"}}}, "GetPublicKeyCertificateOutput": {"type": "structure", "required": ["KeyCertificate", "KeyCertificate<PERSON><PERSON><PERSON>"], "members": {"KeyCertificate": {"shape": "CertificateType", "documentation": "<p>The public key component of the asymmetric key pair in a certificate (PEM) format. It is signed by the root certificate authority (CA) within your service account. The certificate expires in 90 days.</p>"}, "KeyCertificateChain": {"shape": "CertificateType", "documentation": "<p>The certificate chain that signed the public key certificate of the asymmetric key pair. This is the root certificate authority (CA) within your service account.</p>"}}}, "HexLength16": {"type": "string", "max": 16, "min": 16, "pattern": "^[0-9A-F]+$"}, "ImportKeyInput": {"type": "structure", "required": ["KeyMaterial"], "members": {"Enabled": {"shape": "Boolean", "documentation": "<p>Specifies whether import key is enabled.</p>"}, "KeyCheckValueAlgorithm": {"shape": "KeyCheckValueAlgorithm", "documentation": "<p>The algorithm that Amazon Web Services Payment Cryptography uses to calculate the key check value (KCV) for DES and AES keys.</p> <p>For DES key, the KCV is computed by encrypting 8 bytes, each with value '00', with the key to be checked and retaining the 3 highest order bytes of the encrypted result. For AES key, the KCV is computed by encrypting 8 bytes, each with value '01', with the key to be checked and retaining the 3 highest order bytes of the encrypted result.</p>"}, "KeyMaterial": {"shape": "ImportKeyMaterial", "documentation": "<p>The key or public key certificate type to use during key material import, for example TR-34 or RootCertificatePublicKey.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>The tags to attach to the key. Each tag consists of a tag key and a tag value. Both the tag key and the tag value are required, but the tag value can be an empty (null) string. You can't have more than one tag on an Amazon Web Services Payment Cryptography key with the same tag key. </p> <p>You can't have more than one tag on an Amazon Web Services Payment Cryptography key with the same tag key. If you specify an existing tag key with a different tag value, Amazon Web Services Payment Cryptography replaces the current tag value with the specified one.</p> <p>To use this parameter, you must have <code>TagResource</code> permission.</p> <important> <p>Don't include confidential or sensitive information in this field. This field may be displayed in plaintext in CloudTrail logs and other output.</p> </important> <note> <p>Tagging or untagging an Amazon Web Services Payment Cryptography key can allow or deny permission to the key.</p> </note>"}}}, "ImportKeyMaterial": {"type": "structure", "members": {"RootCertificatePublicKey": {"shape": "RootCertificatePublicKey", "documentation": "<p>Parameter information for root public key certificate import.</p>"}, "Tr31KeyBlock": {"shape": "ImportTr31KeyBlock", "documentation": "<p>Parameter information for key material import using TR-31 standard.</p>"}, "Tr34KeyBlock": {"shape": "ImportTr34KeyBlock", "documentation": "<p>Parameter information for key material import using TR-34 standard.</p>"}, "TrustedCertificatePublicKey": {"shape": "TrustedCertificatePublicKey", "documentation": "<p>Parameter information for trusted public key certificate import.</p>"}}, "documentation": "<p>Parameter information for key material import.</p>", "union": true}, "ImportKeyOutput": {"type": "structure", "required": ["Key"], "members": {"Key": {"shape": "Key", "documentation": "<p>The <code>KeyARN</code> of the key material imported within Amazon Web Services Payment Cryptography.</p>"}}}, "ImportTokenId": {"type": "string", "pattern": "^import-token-[0-9a-zA-Z]{16,64}$"}, "ImportTr31KeyBlock": {"type": "structure", "required": ["WrappedKeyBlock", "WrappingKeyIdentifier"], "members": {"WrappedKeyBlock": {"shape": "Tr31WrappedKeyBlock", "documentation": "<p>The TR-34 wrapped key block to import.</p>"}, "WrappingKeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>KeyARN</code> of the key that will decrypt or unwrap a TR-31 key block during import.</p>"}}, "documentation": "<p>Parameter information for key material import using TR-31 standard.</p>"}, "ImportTr34KeyBlock": {"type": "structure", "required": ["CertificateAuthorityPublicKeyIdentifier", "ImportToken", "KeyBlockFormat", "SigningKeyCertificate", "WrappedKeyBlock"], "members": {"CertificateAuthorityPublicKeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>KeyARN</code> of the certificate chain that signs the signing key certificate during TR-34 key import.</p>"}, "ImportToken": {"shape": "ImportTokenId", "documentation": "<p>The import token that initiates key import into Amazon Web Services Payment Cryptography. It expires after 7 days. You can use the same import token to import multiple keys to the same service account.</p>"}, "KeyBlockFormat": {"shape": "Tr34KeyBlockFormat", "documentation": "<p>The key block format to use during key import. The only value allowed is <code>X9_TR34_2012</code>.</p>"}, "RandomNonce": {"shape": "HexLength16", "documentation": "<p>A random number value that is unique to the TR-34 key block generated using 2 pass. The operation will fail, if a random nonce value is not provided for a TR-34 key block generated using 2 pass.</p>"}, "SigningKeyCertificate": {"shape": "CertificateType", "documentation": "<p>The public key component in PEM certificate format of the private key that signs the KDH TR-34 wrapped key block.</p>"}, "WrappedKeyBlock": {"shape": "Tr34WrappedKeyBlock", "documentation": "<p>The TR-34 wrapped key block to import.</p>"}}, "documentation": "<p>Parameter information for key material import using TR-34 standard.</p>"}, "InternalServerException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The request processing has failed because of an unknown error, exception, or failure.</p>", "exception": true, "fault": true}, "Key": {"type": "structure", "required": ["CreateTimestamp", "Enabled", "Exportable", "KeyArn", "KeyAttributes", "KeyCheckValue", "KeyCheckValueAlgorithm", "<PERSON><PERSON><PERSON><PERSON>", "KeyState"], "members": {"CreateTimestamp": {"shape": "Timestamp", "documentation": "<p>The date and time when the key was created.</p>"}, "DeletePendingTimestamp": {"shape": "Timestamp", "documentation": "<p>The date and time after which Amazon Web Services Payment Cryptography will delete the key. This value is present only when <code>KeyState</code> is <code>DELETE_PENDING</code> and the key is scheduled for deletion.</p>"}, "DeleteTimestamp": {"shape": "Timestamp", "documentation": "<p>The date and time after which Amazon Web Services Payment Cryptography will delete the key. This value is present only when when the <code>KeyState</code> is <code>DELETE_COMPLETE</code> and the Amazon Web Services Payment Cryptography key is deleted.</p>"}, "Enabled": {"shape": "Boolean", "documentation": "<p>Specifies whether the key is enabled. </p>"}, "Exportable": {"shape": "Boolean", "documentation": "<p>Specifies whether the key is exportable. This data is immutable after the key is created.</p>"}, "KeyArn": {"shape": "KeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the key.</p>"}, "KeyAttributes": {"shape": "KeyAttributes", "documentation": "<p>The role of the key, the algorithm it supports, and the cryptographic operations allowed with the key. This data is immutable after the key is created.</p>"}, "KeyCheckValue": {"shape": "KeyCheckValue", "documentation": "<p>The key check value (KCV) is used to check if all parties holding a given key have the same key or to detect that a key has changed. Amazon Web Services Payment Cryptography calculates the KCV by using standard algorithms, typically by encrypting 8 or 16 bytes or \"00\" or \"01\" and then truncating the result to the first 3 bytes, or 6 hex digits, of the resulting cryptogram.</p>"}, "KeyCheckValueAlgorithm": {"shape": "KeyCheckValueAlgorithm", "documentation": "<p>The algorithm used for calculating key check value (KCV) for DES and AES keys. For a DES key, Amazon Web Services Payment Cryptography computes the KCV by encrypting 8 bytes, each with value '00', with the key to be checked and retaining the 3 highest order bytes of the encrypted result. For an AES key, Amazon Web Services Payment Cryptography computes the KCV by encrypting 8 bytes, each with value '01', with the key to be checked and retaining the 3 highest order bytes of the encrypted result.</p>"}, "KeyOrigin": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The source of the key material. For keys created within Amazon Web Services Payment Cryptography, the value is <code>AWS_PAYMENT_CRYPTOGRAPHY</code>. For keys imported into Amazon Web Services Payment Cryptography, the value is <code>EXTERNAL</code>.</p>"}, "KeyState": {"shape": "KeyState", "documentation": "<p>The state of key that is being created or deleted.</p>"}, "UsageStartTimestamp": {"shape": "Timestamp", "documentation": "<p>The date and time after which Amazon Web Services Payment Cryptography will start using the key material for cryptographic operations.</p>"}, "UsageStopTimestamp": {"shape": "Timestamp", "documentation": "<p>The date and time after which Amazon Web Services Payment Cryptography will stop using the key material for cryptographic operations.</p>"}}, "documentation": "<p><PERSON>ada<PERSON> about an Amazon Web Services Payment Cryptography key.</p>"}, "KeyAlgorithm": {"type": "string", "enum": ["TDES_2KEY", "TDES_3KEY", "AES_128", "AES_192", "AES_256", "RSA_2048", "RSA_3072", "RSA_4096"]}, "KeyArn": {"type": "string", "max": 150, "min": 70, "pattern": "^arn:aws:payment-cryptography:[a-z]{2}-[a-z]{1,16}-[0-9]+:[0-9]{12}:key/[0-9a-zA-Z]{16,64}$"}, "KeyArnOrKeyAliasType": {"type": "string", "max": 322, "min": 7, "pattern": "^arn:aws:payment-cryptography:[a-z]{2}-[a-z]{1,16}-[0-9]+:[0-9]{12}:(key/[0-9a-zA-Z]{16,64}|alias/[a-zA-Z0-9/_-]+)$|^alias/[a-zA-Z0-9/_-]+$"}, "KeyAttributes": {"type": "structure", "required": ["KeyAlgorithm", "KeyClass", "KeyModesOfUse", "KeyUsage"], "members": {"KeyAlgorithm": {"shape": "KeyAlgorithm", "documentation": "<p>The key algorithm to be use during creation of an Amazon Web Services Payment Cryptography key.</p> <p>For symmetric keys, Amazon Web Services Payment Cryptography supports <code>AES</code> and <code>TDES</code> algorithms. For asymmetric keys, Amazon Web Services Payment Cryptography supports <code>RSA</code> and <code>ECC_NIST</code> algorithms.</p>"}, "KeyClass": {"shape": "KeyClass", "documentation": "<p>The type of Amazon Web Services Payment Cryptography key to create, which determines the classiﬁcation of the cryptographic method and whether Amazon Web Services Payment Cryptography key contains a symmetric key or an asymmetric key pair.</p>"}, "KeyModesOfUse": {"shape": "KeyModesOfUse", "documentation": "<p>The list of cryptographic operations that you can perform using the key.</p>"}, "KeyUsage": {"shape": "KeyUsage", "documentation": "<p>The cryptographic usage of an Amazon Web Services Payment Cryptography key as deﬁned in section A.5.2 of the TR-31 spec.</p>"}}, "documentation": "<p>The role of the key, the algorithm it supports, and the cryptographic operations allowed with the key. This data is immutable after the key is created.</p>"}, "KeyCheckValue": {"type": "string", "max": 16, "min": 4, "pattern": "^[0-9a-fA-F]+$"}, "KeyCheckValueAlgorithm": {"type": "string", "enum": ["CMAC", "ANSI_X9_24"]}, "KeyClass": {"type": "string", "enum": ["SYMMETRIC_KEY", "ASYMMETRIC_KEY_PAIR", "PRIVATE_KEY", "PUBLIC_KEY"]}, "KeyMaterial": {"type": "string", "max": 16384, "min": 48, "sensitive": true}, "KeyMaterialType": {"type": "string", "enum": ["TR34_KEY_BLOCK", "TR31_KEY_BLOCK", "ROOT_PUBLIC_KEY_CERTIFICATE", "TRUSTED_PUBLIC_KEY_CERTIFICATE"]}, "KeyModesOfUse": {"type": "structure", "members": {"Decrypt": {"shape": "PrimitiveBoolean", "documentation": "<p>Speciﬁes whether an Amazon Web Services Payment Cryptography key can be used to decrypt data.</p>"}, "DeriveKey": {"shape": "PrimitiveBoolean", "documentation": "<p>Speciﬁes whether an Amazon Web Services Payment Cryptography key can be used to derive new keys.</p>"}, "Encrypt": {"shape": "PrimitiveBoolean", "documentation": "<p>Speciﬁes whether an Amazon Web Services Payment Cryptography key can be used to encrypt data.</p>"}, "Generate": {"shape": "PrimitiveBoolean", "documentation": "<p>Speciﬁes whether an Amazon Web Services Payment Cryptography key can be used to generate and verify other card and PIN verification keys.</p>"}, "NoRestrictions": {"shape": "PrimitiveBoolean", "documentation": "<p>Speciﬁes whether an Amazon Web Services Payment Cryptography key has no special restrictions other than the restrictions implied by <code>KeyUsage</code>.</p>"}, "Sign": {"shape": "PrimitiveBoolean", "documentation": "<p>Speciﬁes whether an Amazon Web Services Payment Cryptography key can be used for signing.</p>"}, "Unwrap": {"shape": "PrimitiveBoolean", "documentation": "<p>Speciﬁes whether an Amazon Web Services Payment Cryptography key can be used to unwrap other keys.</p>"}, "Verify": {"shape": "PrimitiveBoolean", "documentation": "<p>Speciﬁes whether an Amazon Web Services Payment Cryptography key can be used to verify signatures.</p>"}, "Wrap": {"shape": "PrimitiveBoolean", "documentation": "<p>Speciﬁes whether an Amazon Web Services Payment Cryptography key can be used to wrap other keys.</p>"}}, "documentation": "<p>The list of cryptographic operations that you can perform using the key. The modes of use are deﬁned in section A.5.3 of the TR-31 spec.</p>"}, "KeyOrigin": {"type": "string", "documentation": "<p>Defines the source of a key</p>", "enum": ["EXTERNAL", "AWS_PAYMENT_CRYPTOGRAPHY"]}, "KeyState": {"type": "string", "documentation": "<p>Defines the state of a key</p>", "enum": ["CREATE_IN_PROGRESS", "CREATE_COMPLETE", "DELETE_PENDING", "DELETE_COMPLETE"]}, "KeySummary": {"type": "structure", "required": ["Enabled", "Exportable", "KeyArn", "KeyAttributes", "KeyCheckValue", "KeyState"], "members": {"Enabled": {"shape": "Boolean", "documentation": "<p>Specifies whether the key is enabled. </p>"}, "Exportable": {"shape": "Boolean", "documentation": "<p>Specifies whether the key is exportable. This data is immutable after the key is created.</p>"}, "KeyArn": {"shape": "KeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the key.</p>"}, "KeyAttributes": {"shape": "KeyAttributes", "documentation": "<p>The role of the key, the algorithm it supports, and the cryptographic operations allowed with the key. This data is immutable after the key is created.</p>"}, "KeyCheckValue": {"shape": "KeyCheckValue", "documentation": "<p>The key check value (KCV) is used to check if all parties holding a given key have the same key or to detect that a key has changed. Amazon Web Services Payment Cryptography calculates the KCV by using standard algorithms, typically by encrypting 8 or 16 bytes or \"00\" or \"01\" and then truncating the result to the first 3 bytes, or 6 hex digits, of the resulting cryptogram.</p>"}, "KeyState": {"shape": "KeyState", "documentation": "<p>The state of an Amazon Web Services Payment Cryptography that is being created or deleted.</p>"}}, "documentation": "<p><PERSON>ada<PERSON> about an Amazon Web Services Payment Cryptography key.</p>"}, "KeySummaryList": {"type": "list", "member": {"shape": "KeySummary"}}, "KeyUsage": {"type": "string", "enum": ["TR31_B0_BASE_DERIVATION_KEY", "TR31_C0_CARD_VERIFICATION_KEY", "TR31_D0_SYMMETRIC_DATA_ENCRYPTION_KEY", "TR31_D1_ASYMMETRIC_KEY_FOR_DATA_ENCRYPTION", "TR31_E0_EMV_MKEY_APP_CRYPTOGRAMS", "TR31_E1_EMV_MKEY_CONFIDENTIALITY", "TR31_E2_EMV_MKEY_INTEGRITY", "TR31_E4_EMV_MKEY_DYNAMIC_NUMBERS", "TR31_E5_EMV_MKEY_CARD_PERSONALIZATION", "TR31_E6_EMV_MKEY_OTHER", "TR31_K0_KEY_ENCRYPTION_KEY", "TR31_K1_KEY_BLOCK_PROTECTION_KEY", "TR31_K3_ASYMMETRIC_KEY_FOR_KEY_AGREEMENT", "TR31_M3_ISO_9797_3_MAC_KEY", "TR31_M6_ISO_9797_5_CMAC_KEY", "TR31_M7_HMAC_KEY", "TR31_P0_PIN_ENCRYPTION_KEY", "TR31_P1_PIN_GENERATION_KEY", "TR31_S0_ASYMMETRIC_KEY_FOR_DIGITAL_SIGNATURE", "TR31_V1_IBM3624_PIN_VERIFICATION_KEY", "TR31_V2_VISA_PIN_VERIFICATION_KEY", "TR31_K2_TR34_ASYMMETRIC_KEY"]}, "ListAliasesInput": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>Use this parameter to specify the maximum number of items to return. When this value is present, Amazon Web Services Payment Cryptography does not return more than the specified number of items, but it might return fewer.</p> <p>This value is optional. If you include a value, it must be between 1 and 100, inclusive. If you do not include a value, it defaults to 50.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Use this parameter in a subsequent request after you receive a response with truncated results. Set it to the value of <code>NextToken</code> from the truncated response you just received.</p>"}}}, "ListAliasesOutput": {"type": "structure", "required": ["Aliases"], "members": {"Aliases": {"shape": "Aliases", "documentation": "<p>The list of aliases. Each alias describes the <code>KeyArn</code> contained within.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results, or an empty or null value if there are no more results.</p>"}}}, "ListKeysInput": {"type": "structure", "members": {"KeyState": {"shape": "KeyState", "documentation": "<p>The key state of the keys you want to list.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>Use this parameter to specify the maximum number of items to return. When this value is present, Amazon Web Services Payment Cryptography does not return more than the specified number of items, but it might return fewer.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Use this parameter in a subsequent request after you receive a response with truncated results. Set it to the value of <code>NextToken</code> from the truncated response you just received.</p>"}}}, "ListKeysOutput": {"type": "structure", "required": ["Keys"], "members": {"Keys": {"shape": "KeySummaryList", "documentation": "<p>The list of keys created within the caller's Amazon Web Services account and Amazon Web Services Region.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results, or an empty or null value if there are no more results.</p>"}}}, "ListTagsForResourceInput": {"type": "structure", "required": ["ResourceArn"], "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>Use this parameter to specify the maximum number of items to return. When this value is present, Amazon Web Services Payment Cryptography does not return more than the specified number of items, but it might return fewer.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Use this parameter in a subsequent request after you receive a response with truncated results. Set it to the value of <code>NextToken</code> from the truncated response you just received.</p>"}, "ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The <code>KeyARN</code> of the key whose tags you are getting.</p>"}}}, "ListTagsForResourceOutput": {"type": "structure", "required": ["Tags"], "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results, or an empty or null value if there are no more results.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>The list of tags associated with a <code>ResourceArn</code>. Each tag will list the key-value pair contained within that tag.</p>"}}}, "MaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "NextToken": {"type": "string", "max": 8192, "min": 1}, "PrimitiveBoolean": {"type": "boolean"}, "ResourceArn": {"type": "string", "max": 150, "min": 70, "pattern": "^arn:aws:payment-cryptography:[a-z]{2}-[a-z]{1,16}-[0-9]+:[0-9]{12}:key/[0-9a-zA-Z]{16,64}$"}, "ResourceNotFoundException": {"type": "structure", "members": {"ResourceId": {"shape": "String", "documentation": "<p>The string for the exception.</p>"}}, "documentation": "<p>The request was denied due to an invalid resource error.</p>", "exception": true}, "RestoreKeyInput": {"type": "structure", "required": ["KeyIdentifier"], "members": {"KeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>KeyARN</code> of the key to be restored within Amazon Web Services Payment Cryptography.</p>"}}}, "RestoreKeyOutput": {"type": "structure", "required": ["Key"], "members": {"Key": {"shape": "Key", "documentation": "<p>The key material of the restored key. The <code>KeyState</code> will change to <code>CREATE_COMPLETE</code> and value for <code>DeletePendingTimestamp</code> gets removed. </p>"}}}, "RootCertificatePublicKey": {"type": "structure", "required": ["KeyAttributes", "PublicKeyCertificate"], "members": {"KeyAttributes": {"shape": "KeyAttributes", "documentation": "<p>The role of the key, the algorithm it supports, and the cryptographic operations allowed with the key. This data is immutable after the root public key is imported.</p>"}, "PublicKeyCertificate": {"shape": "CertificateType", "documentation": "<p>Parameter information for root public key certificate import.</p>"}}, "documentation": "<p>Parameter information for root public key certificate import.</p>"}, "ServiceQuotaExceededException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>This request would cause a service quota to be exceeded.</p>", "exception": true}, "ServiceUnavailableException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The service cannot complete the request.</p>", "exception": true, "fault": true}, "StartKeyUsageInput": {"type": "structure", "required": ["KeyIdentifier"], "members": {"KeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>KeyArn</code> of the key.</p>"}}}, "StartKeyUsageOutput": {"type": "structure", "required": ["Key"], "members": {"Key": {"shape": "Key", "documentation": "<p>The <code>KeyARN</code> of the Amazon Web Services Payment Cryptography key activated for use.</p>"}}}, "StopKeyUsageInput": {"type": "structure", "required": ["KeyIdentifier"], "members": {"KeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>KeyArn</code> of the key.</p>"}}}, "StopKeyUsageOutput": {"type": "structure", "required": ["Key"], "members": {"Key": {"shape": "Key", "documentation": "<p>The <code>KeyARN</code> of the key.</p>"}}}, "String": {"type": "string"}, "Tag": {"type": "structure", "required": ["Key"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>The key of the tag.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The value of the tag.</p>"}}, "documentation": "<p>A structure that contains information about a tag.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagKeys": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 0}, "TagResourceInput": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The <code>KeyARN</code> of the key whose tags are being updated.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>One or more tags. Each tag consists of a tag key and a tag value. The tag value can be an empty (null) string. You can't have more than one tag on an Amazon Web Services Payment Cryptography key with the same tag key. If you specify an existing tag key with a different tag value, Amazon Web Services Payment Cryptography replaces the current tag value with the new one.</p> <important> <p>Don't include confidential or sensitive information in this field. This field may be displayed in plaintext in CloudTrail logs and other output.</p> </important> <p>To use this parameter, you must have <a>TagResource</a> permission in an IAM policy.</p> <important> <p>Don't include confidential or sensitive information in this field. This field may be displayed in plaintext in CloudTrail logs and other output.</p> </important>"}}}, "TagResourceOutput": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "Tags": {"type": "list", "member": {"shape": "Tag"}, "max": 200, "min": 0}, "ThrottlingException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The request was denied due to request throttling.</p>", "exception": true}, "Timestamp": {"type": "timestamp"}, "Tr31WrappedKeyBlock": {"type": "string", "max": 9984, "min": 56, "pattern": "^[0-9A-Z]+$"}, "Tr34KeyBlockFormat": {"type": "string", "enum": ["X9_TR34_2012"]}, "Tr34WrappedKeyBlock": {"type": "string", "max": 4096, "min": 2, "pattern": "^[0-9A-F]+$"}, "TrustedCertificatePublicKey": {"type": "structure", "required": ["CertificateAuthorityPublicKeyIdentifier", "KeyAttributes", "PublicKeyCertificate"], "members": {"CertificateAuthorityPublicKeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>KeyARN</code> of the root public key certificate or certificate chain that signs the trusted public key certificate import.</p>"}, "KeyAttributes": {"shape": "KeyAttributes", "documentation": "<p>The role of the key, the algorithm it supports, and the cryptographic operations allowed with the key. This data is immutable after a trusted public key is imported.</p>"}, "PublicKeyCertificate": {"shape": "CertificateType", "documentation": "<p>Parameter information for trusted public key certificate import.</p>"}}, "documentation": "<p>Parameter information for trusted public key certificate import.</p>"}, "UntagResourceInput": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The <code>KeyARN</code> of the key whose tags are being removed.</p>"}, "TagKeys": {"shape": "TagKeys", "documentation": "<p>One or more tag keys. Don't include the tag values.</p> <p>If the Amazon Web Services Payment Cryptography key doesn't have the specified tag key, Amazon Web Services Payment Cryptography doesn't throw an exception or return a response. To confirm that the operation succeeded, use the <a>ListTagsForResource</a> operation.</p>"}}}, "UntagResourceOutput": {"type": "structure", "members": {}}, "UpdateAliasInput": {"type": "structure", "required": ["AliasName"], "members": {"AliasName": {"shape": "AliasName", "documentation": "<p>The alias whose associated key is changing.</p>"}, "KeyArn": {"shape": "KeyArn", "documentation": "<p>The <code>KeyARN</code> for the key that you are updating or removing from the alias.</p>"}}}, "UpdateAliasOutput": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"Alias": {"shape": "<PERSON><PERSON>", "documentation": "<p>The alias name.</p>"}}}, "ValidationException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The request was denied due to an invalid request error.</p>", "exception": true}, "WrappedKey": {"type": "structure", "required": ["KeyMaterial", "WrappedKeyMaterialFormat", "WrappingKeyArn"], "members": {"KeyMaterial": {"shape": "KeyMaterial", "documentation": "<p>Parameter information for generating a wrapped key using TR-31 or TR-34 standard.</p>"}, "WrappedKeyMaterialFormat": {"shape": "WrappedKeyMaterialFormat", "documentation": "<p>The key block format of a wrapped key.</p>"}, "WrappingKeyArn": {"shape": "KeyArn", "documentation": "<p>The <code>KeyARN</code> of the wrapped key.</p>"}}, "documentation": "<p>Parameter information for generating a wrapped key using TR-31 or TR-34 standard.</p>"}, "WrappedKeyMaterialFormat": {"type": "string", "enum": ["KEY_CRYPTOGRAM", "TR31_KEY_BLOCK", "TR34_KEY_BLOCK"]}}, "documentation": "<p>You use the Amazon Web Services Payment Cryptography Control Plane to manage the encryption keys you use for payment-related cryptographic operations. You can create, import, export, share, manage, and delete keys. You can also manage Identity and Access Management (IAM) policies for keys. For more information, see <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/security-iam.html\">Identity and access management</a> in the <i>Amazon Web Services Payment Cryptography User Guide.</i> </p> <p>To use encryption keys for payment-related transaction processing and associated cryptographic operations, you use the <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/DataAPIReference/Welcome.html\">Amazon Web Services Payment Cryptography Data Plane</a>. You can encrypt, decrypt, generate, verify, and translate payment-related cryptographic operations. </p> <p>All Amazon Web Services Payment Cryptography API calls must be signed and transmitted using Transport Layer Security (TLS). We recommend you always use the latest supported TLS version for logging API requests. </p> <p>Amazon Web Services Payment Cryptography supports CloudTrail, a service that logs Amazon Web Services API calls and related events for your Amazon Web Services account and delivers them to an Amazon S3 bucket that you specify. By using the information collected by CloudTrail, you can determine what requests were made to Amazon Web Services Payment Cryptography, who made the request, when it was made, and so on. If you don't conﬁgure a trail, you can still view the most recent events in the CloudTrail console. For more information, see the <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/\">CloudTrail User Guide</a>.</p>"}