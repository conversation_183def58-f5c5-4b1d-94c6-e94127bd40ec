{"version": "2.0", "metadata": {"apiVersion": "2018-09-17", "endpointPrefix": "catalog.marketplace", "jsonVersion": "1.1", "protocol": "rest-json", "serviceAbbreviation": "AWS Marketplace Catalog", "serviceFullName": "AWS Marketplace Catalog Service", "serviceId": "Marketplace Catalog", "signatureVersion": "v4", "signingName": "aws-marketplace", "uid": "marketplace-catalog-2018-09-17"}, "operations": {"CancelChangeSet": {"name": "CancelChangeSet", "http": {"method": "PATCH", "requestUri": "/CancelChangeSet"}, "input": {"shape": "CancelChangeSetRequest"}, "output": {"shape": "CancelChangeSetResponse"}, "errors": [{"shape": "InternalServiceException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Used to cancel an open change request. Must be sent before the status of the request changes to <code>APPLYING</code>, the final stage of completing your change request. You can describe a change during the 60-day request history retention period for API calls.</p>"}, "DeleteResourcePolicy": {"name": "DeleteResourcePolicy", "http": {"method": "DELETE", "requestUri": "/DeleteResourcePolicy"}, "input": {"shape": "DeleteResourcePolicyRequest"}, "output": {"shape": "DeleteResourcePolicyResponse"}, "errors": [{"shape": "InternalServiceException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes a resource-based policy on an entity that is identified by its resource ARN.</p>"}, "DescribeChangeSet": {"name": "DescribeChangeSet", "http": {"method": "GET", "requestUri": "/DescribeChangeSet"}, "input": {"shape": "DescribeChangeSetRequest"}, "output": {"shape": "DescribeChangeSetResponse"}, "errors": [{"shape": "InternalServiceException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Provides information about a given change set.</p>"}, "DescribeEntity": {"name": "DescribeEntity", "http": {"method": "GET", "requestUri": "/DescribeEntity"}, "input": {"shape": "DescribeEntityRequest"}, "output": {"shape": "DescribeEntityResponse"}, "errors": [{"shape": "InternalServiceException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotSupportedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns the metadata and content of the entity.</p>"}, "GetResourcePolicy": {"name": "GetResourcePolicy", "http": {"method": "GET", "requestUri": "/GetResourcePolicy"}, "input": {"shape": "GetResourcePolicyRequest"}, "output": {"shape": "GetResourcePolicyResponse"}, "errors": [{"shape": "InternalServiceException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Gets a resource-based policy of an entity that is identified by its resource ARN.</p>"}, "ListChangeSets": {"name": "ListChangeSets", "http": {"method": "POST", "requestUri": "/ListChangeSets"}, "input": {"shape": "ListChangeSetsRequest"}, "output": {"shape": "ListChangeSetsResponse"}, "errors": [{"shape": "InternalServiceException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns the list of change sets owned by the account being used to make the call. You can filter this list by providing any combination of <code>entityId</code>, <code>ChangeSetName</code>, and status. If you provide more than one filter, the API operation applies a logical AND between the filters.</p> <p>You can describe a change during the 60-day request history retention period for API calls.</p>"}, "ListEntities": {"name": "ListEntities", "http": {"method": "POST", "requestUri": "/ListEntities"}, "input": {"shape": "ListEntitiesRequest"}, "output": {"shape": "ListEntitiesResponse"}, "errors": [{"shape": "InternalServiceException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Provides the list of entities of a given type.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/ListTagsForResource"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServiceException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Lists all tags that have been added to a resource (either an <a href=\"https://docs.aws.amazon.com/marketplace-catalog/latest/api-reference/welcome.html#catalog-api-entities\">entity</a> or <a href=\"https://docs.aws.amazon.com/marketplace-catalog/latest/api-reference/welcome.html#working-with-change-sets\">change set</a>).</p>"}, "PutResourcePolicy": {"name": "PutResourcePolicy", "http": {"method": "POST", "requestUri": "/PutResourcePolicy"}, "input": {"shape": "PutResourcePolicyRequest"}, "output": {"shape": "PutResourcePolicyResponse"}, "errors": [{"shape": "InternalServiceException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Attaches a resource-based policy to an entity. Examples of an entity include: <code>AmiProduct</code> and <code>ContainerProduct</code>.</p>"}, "StartChangeSet": {"name": "StartChangeSet", "http": {"method": "POST", "requestUri": "/StartChangeSet"}, "input": {"shape": "StartChangeSetRequest"}, "output": {"shape": "StartChangeSetResponse"}, "errors": [{"shape": "InternalServiceException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Allows you to request changes for your entities. Within a single <code>ChangeSet</code>, you can't start the same change type against the same entity multiple times. Additionally, when a <code>ChangeSet</code> is running, all the entities targeted by the different changes are locked until the change set has completed (either succeeded, cancelled, or failed). If you try to start a change set containing a change against an entity that is already locked, you will receive a <code>ResourceInUseException</code> error.</p> <p>For example, you can't start the <code>ChangeSet</code> described in the <a href=\"https://docs.aws.amazon.com/marketplace-catalog/latest/api-reference/API_StartChangeSet.html#API_StartChangeSet_Examples\">example</a> later in this topic because it contains two changes to run the same change type (<code>AddRevisions</code>) against the same entity (<code>entity-id@1</code>).</p> <p>For more information about working with change sets, see <a href=\"https://docs.aws.amazon.com/marketplace-catalog/latest/api-reference/welcome.html#working-with-change-sets\"> Working with change sets</a>. For information about change types for single-AMI products, see <a href=\"https://docs.aws.amazon.com/marketplace-catalog/latest/api-reference/ami-products.html#working-with-single-AMI-products\">Working with single-AMI products</a>. Also, for more information about change types available for container-based products, see <a href=\"https://docs.aws.amazon.com/marketplace-catalog/latest/api-reference/container-products.html#working-with-container-products\">Working with container products</a>.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/TagResource"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServiceException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Tags a resource (either an <a href=\"https://docs.aws.amazon.com/marketplace-catalog/latest/api-reference/welcome.html#catalog-api-entities\">entity</a> or <a href=\"https://docs.aws.amazon.com/marketplace-catalog/latest/api-reference/welcome.html#working-with-change-sets\">change set</a>).</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/UntagResource"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServiceException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Removes a tag or list of tags from a resource (either an <a href=\"https://docs.aws.amazon.com/marketplace-catalog/latest/api-reference/welcome.html#catalog-api-entities\">entity</a> or <a href=\"https://docs.aws.amazon.com/marketplace-catalog/latest/api-reference/welcome.html#working-with-change-sets\">change set</a>).</p>"}}, "shapes": {"ARN": {"type": "string", "max": 2048, "min": 1, "pattern": "^[a-zA-Z0-9:*/-]+$"}, "AccessDeniedException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessageContent"}}, "documentation": "<p>Access is denied.</p> <p>HTTP status code: 403</p>", "error": {"httpStatusCode": 403}, "exception": true, "synthetic": true}, "CancelChangeSetRequest": {"type": "structure", "required": ["Catalog", "ChangeSetId"], "members": {"Catalog": {"shape": "Catalog", "documentation": "<p>Required. The catalog related to the request. Fixed value: <code>AWSMarketplace</code>.</p>", "location": "querystring", "locationName": "catalog"}, "ChangeSetId": {"shape": "ResourceId", "documentation": "<p>Required. The unique identifier of the <code>StartChangeSet</code> request that you want to cancel.</p>", "location": "querystring", "locationName": "changeSetId"}}}, "CancelChangeSetResponse": {"type": "structure", "members": {"ChangeSetId": {"shape": "ResourceId", "documentation": "<p>The unique identifier for the change set referenced in this request.</p>"}, "ChangeSetArn": {"shape": "ARN", "documentation": "<p>The ARN associated with the change set referenced in this request.</p>"}}}, "Catalog": {"type": "string", "max": 64, "min": 1, "pattern": "^[a-zA-Z]+$"}, "Change": {"type": "structure", "required": ["ChangeType", "Entity"], "members": {"ChangeType": {"shape": "ChangeType", "documentation": "<p>Change types are single string values that describe your intention for the change. Each change type is unique for each <code>EntityType</code> provided in the change's scope. For more information on change types available for single-AMI products, see <a href=\"https://docs.aws.amazon.com/marketplace-catalog/latest/api-reference/ami-products.html#working-with-single-AMI-products\">Working with single-AMI products</a>. Also, for more information about change types available for container-based products, see <a href=\"https://docs.aws.amazon.com/marketplace-catalog/latest/api-reference/container-products.html#working-with-container-products\">Working with container products</a>.</p>"}, "Entity": {"shape": "Entity", "documentation": "<p>The entity to be changed.</p>"}, "EntityTags": {"shape": "TagList", "documentation": "<p>The tags associated with the change.</p>"}, "Details": {"shape": "Json", "documentation": "<p>This object contains details specific to the change type of the requested change. For more information about change types available for single-AMI products, see <a href=\"https://docs.aws.amazon.com/marketplace-catalog/latest/api-reference/ami-products.html#working-with-single-AMI-products\">Working with single-AMI products</a>. Also, for more information about change types available for container-based products, see <a href=\"https://docs.aws.amazon.com/marketplace-catalog/latest/api-reference/container-products.html#working-with-container-products\">Working with container products</a>.</p>"}, "DetailsDocument": {"shape": "JsonDocumentType", "documentation": "<p>Alternative field that accepts a JSON value instead of a string for <code>ChangeType</code> details. You can use either <code>Details</code> or <code>DetailsDocument</code>, but not both.</p>"}, "ChangeName": {"shape": "ChangeName", "documentation": "<p>Optional name for the change.</p>"}}, "documentation": "<p>An object that contains the <code>ChangeType</code>, <code>Details</code>, and <code>Entity</code>.</p>"}, "ChangeName": {"type": "string", "max": 72, "min": 1, "pattern": "^[a-zA-Z]$"}, "ChangeSetDescription": {"type": "list", "member": {"shape": "ChangeSummary"}}, "ChangeSetName": {"type": "string", "max": 100, "min": 1, "pattern": "^[\\w\\s+=.:@-]+$"}, "ChangeSetSummaryList": {"type": "list", "member": {"shape": "ChangeSetSummaryListItem"}}, "ChangeSetSummaryListItem": {"type": "structure", "members": {"ChangeSetId": {"shape": "ResourceId", "documentation": "<p>The unique identifier for a change set.</p>"}, "ChangeSetArn": {"shape": "ARN", "documentation": "<p>The ARN associated with the unique identifier for the change set referenced in this request.</p>"}, "ChangeSetName": {"shape": "ChangeSetName", "documentation": "<p>The non-unique name for the change set.</p>"}, "StartTime": {"shape": "DateTimeISO8601", "documentation": "<p>The time, in ISO 8601 format (2018-02-27T13:45:22Z), when the change set was started.</p>"}, "EndTime": {"shape": "DateTimeISO8601", "documentation": "<p>The time, in ISO 8601 format (2018-02-27T13:45:22Z), when the change set was finished.</p>"}, "Status": {"shape": "ChangeStatus", "documentation": "<p>The current status of the change set.</p>"}, "EntityIdList": {"shape": "ResourceIdList", "documentation": "<p>This object is a list of entity IDs (string) that are a part of a change set. The entity ID list is a maximum of 20 entities. It must contain at least one entity.</p>"}, "FailureCode": {"shape": "FailureCode", "documentation": "<p>Returned if the change set is in <code>FAILED</code> status. Can be either <code>CLIENT_ERROR</code>, which means that there are issues with the request (see the <code>ErrorDetailList</code> of <code>DescribeChangeSet</code>), or <code>SERVER_FAULT</code>, which means that there is a problem in the system, and you should retry your request.</p>"}}, "documentation": "<p>A summary of a change set returned in a list of change sets when the <code>ListChangeSets</code> action is called.</p>"}, "ChangeStatus": {"type": "string", "enum": ["PREPARING", "APPLYING", "SUCCEEDED", "CANCELLED", "FAILED"]}, "ChangeSummary": {"type": "structure", "members": {"ChangeType": {"shape": "ChangeType", "documentation": "<p>The type of the change.</p>"}, "Entity": {"shape": "Entity", "documentation": "<p>The entity to be changed.</p>"}, "Details": {"shape": "Json", "documentation": "<p>This object contains details specific to the change type of the requested change.</p>"}, "DetailsDocument": {"shape": "JsonDocumentType", "documentation": "<p>The JSON value of the details specific to the change type of the requested change.</p>"}, "ErrorDetailList": {"shape": "ErrorDetailList", "documentation": "<p>An array of <code>ErrorDetail</code> objects associated with the change.</p>"}, "ChangeName": {"shape": "ChangeName", "documentation": "<p>Optional name for the change.</p>"}}, "documentation": "<p>This object is a container for common summary information about the change. The summary doesn't contain the whole change structure.</p>"}, "ChangeType": {"type": "string", "max": 255, "min": 1, "pattern": "^[A-Z][\\w]*$"}, "ClientRequestToken": {"type": "string", "max": 64, "min": 1, "pattern": "^[!-~]+$"}, "DateTimeISO8601": {"type": "string", "max": 20, "min": 20, "pattern": "^([\\d]{4})\\-(1[0-2]|0[1-9])\\-(3[01]|0[1-9]|[12][\\d])T(2[0-3]|[01][\\d]):([0-5][\\d]):([0-5][\\d])Z$"}, "DeleteResourcePolicyRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the entity resource that is associated with the resource policy.</p>", "location": "querystring", "locationName": "resourceArn"}}}, "DeleteResourcePolicyResponse": {"type": "structure", "members": {}}, "DescribeChangeSetRequest": {"type": "structure", "required": ["Catalog", "ChangeSetId"], "members": {"Catalog": {"shape": "Catalog", "documentation": "<p>Required. The catalog related to the request. Fixed value: <code>AWSMarketplace</code> </p>", "location": "querystring", "locationName": "catalog"}, "ChangeSetId": {"shape": "ResourceId", "documentation": "<p>Required. The unique identifier for the <code>StartChangeSet</code> request that you want to describe the details for.</p>", "location": "querystring", "locationName": "changeSetId"}}}, "DescribeChangeSetResponse": {"type": "structure", "members": {"ChangeSetId": {"shape": "ResourceId", "documentation": "<p>Required. The unique identifier for the change set referenced in this request.</p>"}, "ChangeSetArn": {"shape": "ARN", "documentation": "<p>The ARN associated with the unique identifier for the change set referenced in this request.</p>"}, "ChangeSetName": {"shape": "ChangeSetName", "documentation": "<p>The optional name provided in the <code>StartChangeSet</code> request. If you do not provide a name, one is set by default.</p>"}, "StartTime": {"shape": "DateTimeISO8601", "documentation": "<p>The date and time, in ISO 8601 format (2018-02-27T13:45:22Z), the request started. </p>"}, "EndTime": {"shape": "DateTimeISO8601", "documentation": "<p>The date and time, in ISO 8601 format (2018-02-27T13:45:22Z), the request transitioned to a terminal state. The change cannot transition to a different state. Null if the request is not in a terminal state. </p>"}, "Status": {"shape": "ChangeStatus", "documentation": "<p>The status of the change request.</p>"}, "FailureCode": {"shape": "FailureCode", "documentation": "<p>Returned if the change set is in <code>FAILED</code> status. Can be either <code>CLIENT_ERROR</code>, which means that there are issues with the request (see the <code>ErrorDetailList</code>), or <code>SERVER_FAULT</code>, which means that there is a problem in the system, and you should retry your request.</p>"}, "FailureDescription": {"shape": "ExceptionMessageContent", "documentation": "<p>Returned if there is a failure on the change set, but that failure is not related to any of the changes in the request.</p>"}, "ChangeSet": {"shape": "ChangeSetDescription", "documentation": "<p>An array of <code>ChangeSummary</code> objects.</p>"}}}, "DescribeEntityRequest": {"type": "structure", "required": ["Catalog", "EntityId"], "members": {"Catalog": {"shape": "Catalog", "documentation": "<p>Required. The catalog related to the request. Fixed value: <code>AWSMarketplace</code> </p>", "location": "querystring", "locationName": "catalog"}, "EntityId": {"shape": "ResourceId", "documentation": "<p>Required. The unique ID of the entity to describe.</p>", "location": "querystring", "locationName": "entityId"}}}, "DescribeEntityResponse": {"type": "structure", "members": {"EntityType": {"shape": "EntityType", "documentation": "<p>The named type of the entity, in the format of <code>EntityType@Version</code>.</p>"}, "EntityIdentifier": {"shape": "Identifier", "documentation": "<p>The identifier of the entity, in the format of <code>EntityId@RevisionId</code>.</p>"}, "EntityArn": {"shape": "ARN", "documentation": "<p>The ARN associated to the unique identifier for the entity referenced in this request.</p>"}, "LastModifiedDate": {"shape": "DateTimeISO8601", "documentation": "<p>The last modified date of the entity, in ISO 8601 format (2018-02-27T13:45:22Z).</p>"}, "Details": {"shape": "Json", "documentation": "<p>This stringified JSON object includes the details of the entity.</p>"}, "DetailsDocument": {"shape": "JsonDocumentType", "documentation": "<p>The JSON value of the details specific to the entity.</p>"}}}, "Entity": {"type": "structure", "required": ["Type"], "members": {"Type": {"shape": "EntityType", "documentation": "<p>The type of entity.</p>"}, "Identifier": {"shape": "Identifier", "documentation": "<p>The identifier for the entity.</p>"}}, "documentation": "<p>An entity contains data that describes your product, its supported features, and how it can be used or launched by your customer. </p>"}, "EntityNameString": {"type": "string", "max": 255, "min": 1, "pattern": "^\\\\S+[\\\\S\\\\s]*"}, "EntitySummary": {"type": "structure", "members": {"Name": {"shape": "EntityNameString", "documentation": "<p>The name for the entity. This value is not unique. It is defined by the seller.</p>"}, "EntityType": {"shape": "EntityType", "documentation": "<p>The type of the entity.</p>"}, "EntityId": {"shape": "ResourceId", "documentation": "<p>The unique identifier for the entity.</p>"}, "EntityArn": {"shape": "ARN", "documentation": "<p>The ARN associated with the unique identifier for the entity.</p>"}, "LastModifiedDate": {"shape": "DateTimeISO8601", "documentation": "<p>The last time the entity was published, using ISO 8601 format (2018-02-27T13:45:22Z).</p>"}, "Visibility": {"shape": "VisibilityValue", "documentation": "<p>The visibility status of the entity to buyers. This value can be <code>Public</code> (everyone can view the entity), <code>Limited</code> (the entity is visible to limited accounts only), or <code>Restricted</code> (the entity was published and then unpublished and only existing buyers can view it). </p>"}}, "documentation": "<p>This object is a container for common summary information about the entity. The summary doesn't contain the whole entity structure, but it does contain information common across all entities.</p>"}, "EntitySummaryList": {"type": "list", "member": {"shape": "EntitySummary"}}, "EntityType": {"type": "string", "max": 255, "min": 1, "pattern": "^[a-zA-Z]+$"}, "ErrorCodeString": {"type": "string", "max": 72, "min": 1, "pattern": "^[a-zA-Z_]+$"}, "ErrorDetail": {"type": "structure", "members": {"ErrorCode": {"shape": "ErrorCodeString", "documentation": "<p>The error code that identifies the type of error.</p>"}, "ErrorMessage": {"shape": "ExceptionMessageContent", "documentation": "<p>The message for the error.</p>"}}, "documentation": "<p>Details about the error.</p>"}, "ErrorDetailList": {"type": "list", "member": {"shape": "ErrorDetail"}}, "ExceptionMessageContent": {"type": "string", "max": 2048, "min": 1, "pattern": "^(.)+$"}, "FailureCode": {"type": "string", "enum": ["CLIENT_ERROR", "SERVER_FAULT"]}, "Filter": {"type": "structure", "members": {"Name": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>For <code>ListEntities</code>, the supported value for this is an <code>EntityId</code>.</p> <p>For <code>ListChangeSets</code>, the supported values are as follows:</p>"}, "ValueList": {"shape": "ValueList", "documentation": "<p> <code>ListEntities</code> - This is a list of unique <code>EntityId</code>s.</p> <p> <code>ListChangeSets</code> - The supported filter names and associated <code>ValueList</code>s is as follows:</p> <ul> <li> <p> <code>ChangeSetName</code> - The supported <code>ValueList</code> is a list of non-unique <code>ChangeSetName</code>s. These are defined when you call the <code>StartChangeSet</code> action.</p> </li> <li> <p> <code>Status</code> - The supported <code>ValueList</code> is a list of statuses for all change set requests.</p> </li> <li> <p> <code>EntityId</code> - The supported <code>ValueList</code> is a list of unique <code>EntityId</code>s.</p> </li> <li> <p> <code>BeforeStartTime</code> - The supported <code>ValueList</code> is a list of all change sets that started before the filter value.</p> </li> <li> <p> <code>AfterStartTime</code> - The supported <code>ValueList</code> is a list of all change sets that started after the filter value.</p> </li> <li> <p> <code>BeforeEndTime</code> - The supported <code>ValueList</code> is a list of all change sets that ended before the filter value.</p> </li> <li> <p> <code>AfterEndTime</code> - The supported <code>ValueList</code> is a list of all change sets that ended after the filter value.</p> </li> </ul>"}}, "documentation": "<p>A filter object, used to optionally filter results from calls to the <code>ListEntities</code> and <code>ListChangeSets</code> actions.</p>"}, "FilterList": {"type": "list", "member": {"shape": "Filter"}, "max": 8, "min": 1}, "FilterName": {"type": "string", "max": 255, "min": 1, "pattern": "^[a-zA-Z]+$"}, "FilterValueContent": {"type": "string", "max": 255, "min": 1, "pattern": "^(.)+$"}, "GetResourcePolicyRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the entity resource that is associated with the resource policy.</p>", "location": "querystring", "locationName": "resourceArn"}}}, "GetResourcePolicyResponse": {"type": "structure", "members": {"Policy": {"shape": "ResourcePolicyJson", "documentation": "<p>The policy document to set; formatted in JSON.</p>"}}}, "Identifier": {"type": "string", "max": 255, "min": 1, "pattern": "^[\\w\\-@]+$"}, "InternalServiceException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessageContent"}}, "documentation": "<p>There was an internal service exception.</p> <p>HTTP status code: 500</p>", "error": {"httpStatusCode": 500}, "exception": true, "synthetic": true}, "Json": {"type": "string", "max": 16384, "min": 2, "pattern": "^[\\s]*\\{[\\s\\S]*\\}[\\s]*$"}, "JsonDocumentType": {"type": "structure", "members": {}, "document": true}, "ListChangeSetsMaxResultInteger": {"type": "integer", "box": true, "max": 20, "min": 1}, "ListChangeSetsRequest": {"type": "structure", "required": ["Catalog"], "members": {"Catalog": {"shape": "Catalog", "documentation": "<p>The catalog related to the request. Fixed value: <code>AWSMarketplace</code> </p>"}, "FilterList": {"shape": "FilterList", "documentation": "<p>An array of filter objects.</p>"}, "Sort": {"shape": "Sort", "documentation": "<p>An object that contains two attributes, <code>SortBy</code> and <code>SortOrder</code>.</p>"}, "MaxResults": {"shape": "ListChangeSetsMaxResultInteger", "documentation": "<p>The maximum number of results returned by a single call. This value must be provided in the next call to retrieve the next set of results. By default, this value is 20.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>"}}}, "ListChangeSetsResponse": {"type": "structure", "members": {"ChangeSetSummaryList": {"shape": "ChangeSetSummaryList", "documentation": "<p> Array of <code>ChangeSetSummaryListItem</code> objects.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The value of the next token, if it exists. Null if there are no more results.</p>"}}}, "ListEntitiesMaxResultInteger": {"type": "integer", "box": true, "max": 50, "min": 1}, "ListEntitiesRequest": {"type": "structure", "required": ["Catalog", "EntityType"], "members": {"Catalog": {"shape": "Catalog", "documentation": "<p>The catalog related to the request. Fixed value: <code>AWSMarketplace</code> </p>"}, "EntityType": {"shape": "EntityType", "documentation": "<p>The type of entities to retrieve.</p>"}, "FilterList": {"shape": "FilterList", "documentation": "<p>An array of filter objects. Each filter object contains two attributes, <code>filterName</code> and <code>filterValues</code>.</p>"}, "Sort": {"shape": "Sort", "documentation": "<p>An object that contains two attributes, <code>SortBy</code> and <code>SortOrder</code>.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The value of the next token, if it exists. Null if there are no more results.</p>"}, "MaxResults": {"shape": "ListEntitiesMaxResultInteger", "documentation": "<p>Specifies the upper limit of the elements on a single page. If a value isn't provided, the default value is 20.</p>"}, "OwnershipType": {"shape": "OwnershipType", "documentation": "<p>Filters the returned set of entities based on their owner. The default is <code>SELF</code>. To list entities shared with you through AWS Resource Access Manager (AWS RAM), set to <code>SHARED</code>. Entities shared through the AWS Marketplace Catalog API <code>PutResourcePolicy</code> operation can't be discovered through the <code>SHARED</code> parameter.</p>"}}}, "ListEntitiesResponse": {"type": "structure", "members": {"EntitySummaryList": {"shape": "EntitySummaryList", "documentation": "<p> Array of <code>EntitySummary</code> object.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The value of the next token if it exists. Null if there is no more result.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "ResourceARN", "documentation": "<p>Required. The Amazon Resource Name (ARN) associated with the resource you want to list tags on.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"ResourceArn": {"shape": "ResourceARN", "documentation": "<p>Required. The ARN associated with the resource you want to list tags on.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>Required. A list of objects specifying each key name and value. Number of objects allowed: 1-50.</p>"}}}, "NextToken": {"type": "string", "max": 2048, "min": 1, "pattern": "^[\\w+=.:@\\-\\/]$"}, "OwnershipType": {"type": "string", "enum": ["SELF", "SHARED"]}, "PutResourcePolicyRequest": {"type": "structure", "required": ["ResourceArn", "Policy"], "members": {"ResourceArn": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the entity resource you want to associate with a resource policy.</p>"}, "Policy": {"shape": "ResourcePolicyJson", "documentation": "<p>The policy document to set; formatted in JSON.</p>"}}}, "PutResourcePolicyResponse": {"type": "structure", "members": {}}, "RequestedChangeList": {"type": "list", "member": {"shape": "Change"}, "max": 20, "min": 1}, "ResourceARN": {"type": "string", "max": 255, "min": 1, "pattern": "^arn:[\\w+=/,.@-]+:aws-marketplace:[\\w+=/,.@-]*:[0-9]+:[\\w+=,.@-]+(/[\\w+=,.@-]+)*$"}, "ResourceId": {"type": "string", "max": 255, "min": 1, "pattern": "^[\\w\\-]+$"}, "ResourceIdList": {"type": "list", "member": {"shape": "ResourceId"}}, "ResourceInUseException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessageContent"}}, "documentation": "<p>The resource is currently in use.</p>", "error": {"httpStatusCode": 423}, "exception": true, "synthetic": true}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessageContent"}}, "documentation": "<p>The specified resource wasn't found.</p> <p>HTTP status code: 404</p>", "error": {"httpStatusCode": 404}, "exception": true, "synthetic": true}, "ResourceNotSupportedException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessageContent"}}, "documentation": "<p>Currently, the specified resource is not supported.</p>", "error": {"httpStatusCode": 415}, "exception": true, "synthetic": true}, "ResourcePolicyJson": {"type": "string", "max": 10240, "min": 1, "pattern": "^[\\u0009\\u000A\\u000D\\u0020-\\u00FF]+$"}, "ServiceQuotaExceededException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessageContent"}}, "documentation": "<p>The maximum number of open requests per account has been exceeded.</p>", "error": {"httpStatusCode": 402}, "exception": true, "synthetic": true}, "Sort": {"type": "structure", "members": {"SortBy": {"shape": "SortBy", "documentation": "<p>For <code>ListEntities</code>, supported attributes include <code>LastModifiedDate</code> (default) and <code>EntityId</code>. In addition to <code>LastModifiedDate</code> and <code>EntityId</code>, each <code>EntityType</code> might support additional fields.</p> <p>For <code>ListChangeSets</code>, supported attributes include <code>StartTime</code> and <code>EndTime</code>.</p>"}, "SortOrder": {"shape": "SortOrder", "documentation": "<p>The sorting order. Can be <code>ASCENDING</code> or <code>DESCENDING</code>. The default value is <code>DESCENDING</code>.</p>"}}, "documentation": "<p>An object that contains two attributes, <code>SortBy</code> and <code>SortOrder</code>.</p>"}, "SortBy": {"type": "string", "max": 255, "min": 1, "pattern": "^[a-zA-Z]+$"}, "SortOrder": {"type": "string", "enum": ["ASCENDING", "DESCENDING"]}, "StartChangeSetRequest": {"type": "structure", "required": ["Catalog", "ChangeSet"], "members": {"Catalog": {"shape": "Catalog", "documentation": "<p>The catalog related to the request. Fixed value: <code>AWSMarketplace</code> </p>"}, "ChangeSet": {"shape": "RequestedChangeList", "documentation": "<p>Array of <code>change</code> object.</p>"}, "ChangeSetName": {"shape": "ChangeSetName", "documentation": "<p>Optional case sensitive string of up to 100 ASCII characters. The change set name can be used to filter the list of change sets. </p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p>A unique token to identify the request to ensure idempotency.</p>", "idempotencyToken": true}, "ChangeSetTags": {"shape": "TagList", "documentation": "<p>A list of objects specifying each key name and value for the <code>ChangeSetTags</code> property.</p>"}}}, "StartChangeSetResponse": {"type": "structure", "members": {"ChangeSetId": {"shape": "ResourceId", "documentation": "<p>Unique identifier generated for the request.</p>"}, "ChangeSetArn": {"shape": "ARN", "documentation": "<p>The ARN associated to the unique identifier generated for the request.</p>"}}}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>The key associated with the tag.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The value associated with the tag.</p>"}}, "documentation": "<p>A list of objects specifying each key name and value.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 1}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "max": 200, "min": 1}, "TagResourceRequest": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"shape": "ResourceARN", "documentation": "<p>Required. The Amazon Resource Name (ARN) associated with the resource you want to tag.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>Required. A list of objects specifying each key name and value. Number of objects allowed: 1-50.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "ThrottlingException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessageContent"}}, "documentation": "<p>Too many requests.</p> <p>HTTP status code: 429</p>", "error": {"httpStatusCode": 429}, "exception": true, "synthetic": true}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {"shape": "ResourceARN", "documentation": "<p>Required. The Amazon Resource Name (ARN) associated with the resource you want to remove the tag from.</p>"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>Required. A list of key names of tags to be removed. Number of strings allowed: 0-256.</p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "ValidationException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessageContent"}}, "documentation": "<p>An error occurred during validation.</p> <p>HTTP status code: 422</p>", "error": {"httpStatusCode": 422}, "exception": true, "synthetic": true}, "ValueList": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "max": 10, "min": 1}, "VisibilityValue": {"type": "string", "max": 64, "min": 1, "pattern": "^[a-zA-Z]+$"}}, "documentation": "<p>Catalog API actions allow you to manage your entities through list, describe, and update capabilities. An entity can be a product or an offer on AWS Marketplace. </p> <p>You can automate your entity update process by integrating the AWS Marketplace Catalog API with your AWS Marketplace product build or deployment pipelines. You can also create your own applications on top of the Catalog API to manage your products on AWS Marketplace.</p>"}