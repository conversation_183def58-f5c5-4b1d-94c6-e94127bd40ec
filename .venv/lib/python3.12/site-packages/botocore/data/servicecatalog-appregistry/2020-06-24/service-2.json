{"version": "2.0", "metadata": {"apiVersion": "2020-06-24", "endpointPrefix": "servicecatalog-appregistry", "jsonVersion": "1.1", "protocol": "rest-json", "serviceAbbreviation": "AppRegistry", "serviceFullName": "AWS Service Catalog App Registry", "serviceId": "Service Catalog AppRegistry", "signatureVersion": "v4", "signingName": "servicecatalog", "uid": "AWS242AppRegistry-2020-06-24"}, "operations": {"AssociateAttributeGroup": {"name": "AssociateAttributeGroup", "http": {"method": "PUT", "requestUri": "/applications/{application}/attribute-groups/{attributeGroup}"}, "input": {"shape": "AssociateAttributeGroupRequest"}, "output": {"shape": "AssociateAttributeGroupResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}], "documentation": "<p>Associates an attribute group with an application to augment the application's metadata with the group's attributes. This feature enables applications to be described with user-defined details that are machine-readable, such as third-party integrations.</p>"}, "AssociateResource": {"name": "AssociateResource", "http": {"method": "PUT", "requestUri": "/applications/{application}/resources/{resourceType}/{resource}"}, "input": {"shape": "AssociateResourceRequest"}, "output": {"shape": "AssociateResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}], "documentation": "<p> Associates a resource with an application. The resource can be specified by its ARN or name. The application can be specified by ARN, ID, or name. </p> <p> <b>Minimum permissions</b> </p> <p> You must have the following permissions to associate a resource using the <code>OPTIONS</code> parameter set to <code>APPLY_APPLICATION_TAG</code>. </p> <ul> <li> <p> <code>tag:GetResources</code> </p> </li> <li> <p> <code>tag:TagResources</code> </p> </li> </ul> <p> You must also have these additional permissions if you don't use the <code>AWSServiceCatalogAppRegistryFullAccess</code> policy. For more information, see <a href=\"https://docs.aws.amazon.com/servicecatalog/latest/arguide/full.html\">AWSServiceCatalogAppRegistryFullAccess</a> in the AppRegistry Administrator Guide. </p> <ul> <li> <p> <code>resource-groups:DisassociateResource</code> </p> </li> <li> <p> <code>cloudformation:UpdateStack</code> </p> </li> <li> <p> <code>cloudformation:DescribeStacks</code> </p> </li> </ul> <note> <p> In addition, you must have the tagging permission defined by the Amazon Web Services service that creates the resource. For more information, see <a href=\"https://docs.aws.amazon.com/resourcegroupstagging/latest/APIReference/API_TagResources.html\">TagResources</a> in the <i>Resource Groups Tagging API Reference</i>. </p> </note>"}, "CreateApplication": {"name": "CreateApplication", "http": {"method": "POST", "requestUri": "/applications", "responseCode": 201}, "input": {"shape": "CreateApplicationRequest"}, "output": {"shape": "CreateApplicationResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Creates a new application that is the top-level node in a hierarchy of related cloud resource abstractions.</p>"}, "CreateAttributeGroup": {"name": "CreateAttributeGroup", "http": {"method": "POST", "requestUri": "/attribute-groups", "responseCode": 201}, "input": {"shape": "CreateAttributeGroupRequest"}, "output": {"shape": "CreateAttributeGroupResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a new attribute group as a container for user-defined attributes. This feature enables users to have full control over their cloud application's metadata in a rich machine-readable format to facilitate integration with automated workflows and third-party tools.</p>"}, "DeleteApplication": {"name": "DeleteApplication", "http": {"method": "DELETE", "requestUri": "/applications/{application}"}, "input": {"shape": "DeleteApplicationRequest"}, "output": {"shape": "DeleteApplicationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an application that is specified either by its application ID, name, or ARN. All associated attribute groups and resources must be disassociated from it before deleting an application.</p>"}, "DeleteAttributeGroup": {"name": "DeleteAttributeGroup", "http": {"method": "DELETE", "requestUri": "/attribute-groups/{attributeGroup}"}, "input": {"shape": "DeleteAttributeGroupRequest"}, "output": {"shape": "DeleteAttributeGroupResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an attribute group, specified either by its attribute group ID, name, or ARN.</p>"}, "DisassociateAttributeGroup": {"name": "DisassociateAttributeGroup", "http": {"method": "DELETE", "requestUri": "/applications/{application}/attribute-groups/{attributeGroup}"}, "input": {"shape": "DisassociateAttributeGroupRequest"}, "output": {"shape": "DisassociateAttributeGroupResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Disassociates an attribute group from an application to remove the extra attributes contained in the attribute group from the application's metadata. This operation reverts <code>AssociateAttributeGroup</code>.</p>"}, "DisassociateResource": {"name": "DisassociateResource", "http": {"method": "DELETE", "requestUri": "/applications/{application}/resources/{resourceType}/{resource}"}, "input": {"shape": "DisassociateResourceRequest"}, "output": {"shape": "DisassociateResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}], "documentation": "<p> Disassociates a resource from application. Both the resource and the application can be specified either by ID or name. </p> <p> <b>Minimum permissions</b> </p> <p> You must have the following permissions to remove a resource that's been associated with an application using the <code>APPLY_APPLICATION_TAG</code> option for <a href=\"https://docs.aws.amazon.com/servicecatalog/latest/dg/API_app-registry_AssociateResource.html\">AssociateResource</a>. </p> <ul> <li> <p> <code>tag:GetResources</code> </p> </li> <li> <p> <code>tag:UntagResources</code> </p> </li> </ul> <p> You must also have the following permissions if you don't use the <code>AWSServiceCatalogAppRegistryFullAccess</code> policy. For more information, see <a href=\"https://docs.aws.amazon.com/servicecatalog/latest/arguide/full.html\">AWSServiceCatalogAppRegistryFullAccess</a> in the AppRegistry Administrator Guide. </p> <ul> <li> <p> <code>resource-groups:DisassociateResource</code> </p> </li> <li> <p> <code>cloudformation:UpdateStack</code> </p> </li> <li> <p> <code>cloudformation:DescribeStacks</code> </p> </li> </ul> <note> <p> In addition, you must have the tagging permission defined by the Amazon Web Services service that creates the resource. For more information, see <a href=\"https://docs.aws.amazon.com/resourcegroupstagging/latest/APIReference/API_UntTagResources.html\">UntagResources</a> in the <i>Resource Groups Tagging API Reference</i>. </p> </note>"}, "GetApplication": {"name": "GetApplication", "http": {"method": "GET", "requestUri": "/applications/{application}"}, "input": {"shape": "GetApplicationRequest"}, "output": {"shape": "GetApplicationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}], "documentation": "<p> Retrieves metadata information about one of your applications. The application can be specified by its ARN, ID, or name (which is unique within one account in one region at a given point in time). Specify by ARN or ID in automated workflows if you want to make sure that the exact same application is returned or a <code>ResourceNotFoundException</code> is thrown, avoiding the ABA addressing problem. </p>"}, "GetAssociatedResource": {"name": "GetAssociatedResource", "http": {"method": "GET", "requestUri": "/applications/{application}/resources/{resourceType}/{resource}"}, "input": {"shape": "GetAssociatedResourceRequest"}, "output": {"shape": "GetAssociatedResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the resource associated with the application.</p>", "idempotent": true}, "GetAttributeGroup": {"name": "GetAttributeGroup", "http": {"method": "GET", "requestUri": "/attribute-groups/{attributeGroup}"}, "input": {"shape": "GetAttributeGroupRequest"}, "output": {"shape": "GetAttributeGroupResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}], "documentation": "<p> Retrieves an attribute group by its ARN, ID, or name. The attribute group can be specified by its ARN, ID, or name. </p>"}, "GetConfiguration": {"name": "GetConfiguration", "http": {"method": "GET", "requestUri": "/configuration"}, "output": {"shape": "GetConfigurationResponse"}, "errors": [{"shape": "InternalServerException"}], "documentation": "<p> Retrieves a <code>TagKey</code> configuration from an account. </p>"}, "ListApplications": {"name": "ListApplications", "http": {"method": "GET", "requestUri": "/applications"}, "input": {"shape": "ListApplicationsRequest"}, "output": {"shape": "ListApplicationsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves a list of all of your applications. Results are paginated.</p>", "idempotent": true}, "ListAssociatedAttributeGroups": {"name": "ListAssociatedAttributeGroups", "http": {"method": "GET", "requestUri": "/applications/{application}/attribute-groups"}, "input": {"shape": "ListAssociatedAttributeGroupsRequest"}, "output": {"shape": "ListAssociatedAttributeGroupsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists all attribute groups that are associated with specified application. Results are paginated.</p>", "idempotent": true}, "ListAssociatedResources": {"name": "ListAssociatedResources", "http": {"method": "GET", "requestUri": "/applications/{application}/resources"}, "input": {"shape": "ListAssociatedResourcesRequest"}, "output": {"shape": "ListAssociatedResourcesResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Lists all of the resources that are associated with the specified application. Results are paginated. </p> <note> <p> If you share an application, and a consumer account associates a tag query to the application, all of the users who can access the application can also view the tag values in all accounts that are associated with it using this API. </p> </note>", "idempotent": true}, "ListAttributeGroups": {"name": "ListAttributeGroups", "http": {"method": "GET", "requestUri": "/attribute-groups"}, "input": {"shape": "ListAttributeGroupsRequest"}, "output": {"shape": "ListAttributeGroupsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists all attribute groups which you have access to. Results are paginated.</p>", "idempotent": true}, "ListAttributeGroupsForApplication": {"name": "ListAttributeGroupsForApplication", "http": {"method": "GET", "requestUri": "/applications/{application}/attribute-group-details"}, "input": {"shape": "ListAttributeGroupsForApplicationRequest"}, "output": {"shape": "ListAttributeGroupsForApplicationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the details of all attribute groups associated with a specific application. The results display in pages.</p>", "idempotent": true}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists all of the tags on the resource.</p>"}, "PutConfiguration": {"name": "PutConfiguration", "http": {"method": "PUT", "requestUri": "/configuration"}, "input": {"shape": "PutConfigurationRequest"}, "errors": [{"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}], "documentation": "<p> Associates a <code>TagKey</code> configuration to an account. </p>"}, "SyncResource": {"name": "SyncResource", "http": {"method": "POST", "requestUri": "/sync/{resourceType}/{resource}"}, "input": {"shape": "SyncResourceRequest"}, "output": {"shape": "SyncResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Syncs the resource with current AppRegistry records.</p> <p>Specifically, the resource’s AppRegistry system tags sync with its associated application. We remove the resource's AppRegistry system tags if it does not associate with the application. The caller must have permissions to read and update the resource.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Assigns one or more tags (key-value pairs) to the specified resource.</p> <p>Each tag consists of a key and an optional value. If a tag with the same key is already associated with the resource, this action updates its value.</p> <p>This operation returns an empty response if the call was successful.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Removes tags from a resource.</p> <p>This operation returns an empty response if the call was successful.</p>"}, "UpdateApplication": {"name": "UpdateApplication", "http": {"method": "PATCH", "requestUri": "/applications/{application}"}, "input": {"shape": "UpdateApplicationRequest"}, "output": {"shape": "UpdateApplicationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Updates an existing application with new attributes.</p>"}, "UpdateAttributeGroup": {"name": "UpdateAttributeGroup", "http": {"method": "PATCH", "requestUri": "/attribute-groups/{attributeGroup}"}, "input": {"shape": "UpdateAttributeGroupRequest"}, "output": {"shape": "UpdateAttributeGroupResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates an existing attribute group with new details. </p>"}}, "shapes": {"AppRegistryConfiguration": {"type": "structure", "members": {"tagQueryConfiguration": {"shape": "TagQueryConfiguration", "documentation": "<p> Includes the definition of a <code>tagQuery</code>. </p>"}}, "documentation": "<p> Includes all of the AppRegistry settings. </p>"}, "Application": {"type": "structure", "members": {"id": {"shape": "ApplicationId", "documentation": "<p>The identifier of the application.</p>"}, "arn": {"shape": "ApplicationArn", "documentation": "<p>The Amazon resource name (ARN) that specifies the application across services.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the application. The name must be unique in the region in which you are creating the application.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the application.</p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p>The ISO-8601 formatted timestamp of the moment when the application was created.</p>"}, "lastUpdateTime": {"shape": "Timestamp", "documentation": "<p> The ISO-8601 formatted timestamp of the moment when the application was last updated.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Key-value pairs you can use to associate with the application.</p>"}, "applicationTag": {"shape": "ApplicationTagDefinition", "documentation": "<p> A key-value pair that identifies an associated resource. </p>"}}, "documentation": "<p>Represents a Amazon Web Services Service Catalog AppRegistry application that is the top-level node in a hierarchy of related cloud resource abstractions.</p>"}, "ApplicationArn": {"type": "string", "pattern": "arn:aws[-a-z]*:servicecatalog:[a-z]{2}(-gov)?-[a-z]+-\\d:\\d{12}:/applications/[a-z0-9]+"}, "ApplicationId": {"type": "string", "max": 26, "min": 26, "pattern": "[a-z0-9]+"}, "ApplicationSpecifier": {"type": "string", "max": 256, "min": 1, "pattern": "([-.\\w]+)|(arn:aws[-a-z]*:servicecatalog:[a-z]{2}(-gov)?-[a-z]+-\\d:\\d{12}:/applications/[-.\\w]+)"}, "ApplicationSummaries": {"type": "list", "member": {"shape": "ApplicationSummary"}}, "ApplicationSummary": {"type": "structure", "members": {"id": {"shape": "ApplicationId", "documentation": "<p>The identifier of the application.</p>"}, "arn": {"shape": "ApplicationArn", "documentation": "<p>The Amazon resource name (ARN) that specifies the application across services.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the application. The name must be unique in the region in which you are creating the application.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the application.</p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p>The ISO-8601 formatted timestamp of the moment when the application was created.</p>"}, "lastUpdateTime": {"shape": "Timestamp", "documentation": "<p> The ISO-8601 formatted timestamp of the moment when the application was last updated.</p>"}}, "documentation": "<p>Summary of a Amazon Web Services Service Catalog AppRegistry application.</p>"}, "ApplicationTagDefinition": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}}, "ApplicationTagResult": {"type": "structure", "members": {"applicationTagStatus": {"shape": "ApplicationTagStatus", "documentation": "<p> The application tag is in the process of being applied to a resource, was successfully applied to a resource, or failed to apply to a resource. </p>"}, "errorMessage": {"shape": "String", "documentation": "<p> The message returned if the call fails. </p>"}, "resources": {"shape": "ResourcesList", "documentation": "<p> The resources associated with an application </p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p> A unique pagination token for each page of results. Make the call again with the returned token to retrieve the next page of results. </p>"}}, "documentation": "<p> The result of the application tag that's applied to a resource. </p>"}, "ApplicationTagStatus": {"type": "string", "enum": ["IN_PROGRESS", "SUCCESS", "FAILURE"]}, "Arn": {"type": "string", "max": 1600, "min": 1, "pattern": "arn:(aws[a-zA-Z0-9-]*):([a-zA-Z0-9\\-])+:([a-z]{2}(-gov)?-[a-z]+-\\d{1})?:(\\d{12})?:(.*)"}, "AssociateAttributeGroupRequest": {"type": "structure", "required": ["application", "attributeGroup"], "members": {"application": {"shape": "ApplicationSpecifier", "documentation": "<p> The name, ID, or ARN of the application. </p>", "location": "uri", "locationName": "application"}, "attributeGroup": {"shape": "AttributeGroupSpecifier", "documentation": "<p> The name, ID, or ARN of the attribute group that holds the attributes to describe the application. </p>", "location": "uri", "locationName": "attributeGroup"}}}, "AssociateAttributeGroupResponse": {"type": "structure", "members": {"applicationArn": {"shape": "ApplicationArn", "documentation": "<p>The Amazon resource name (ARN) of the application that was augmented with attributes.</p>"}, "attributeGroupArn": {"shape": "AttributeGroupArn", "documentation": "<p>The Amazon resource name (ARN) of the attribute group that contains the application's new attributes.</p>"}}}, "AssociateResourceRequest": {"type": "structure", "required": ["application", "resourceType", "resource"], "members": {"application": {"shape": "ApplicationSpecifier", "documentation": "<p> The name, ID, or ARN of the application. </p>", "location": "uri", "locationName": "application"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The type of resource of which the application will be associated.</p>", "location": "uri", "locationName": "resourceType"}, "resource": {"shape": "ResourceSpecifier", "documentation": "<p>The name or ID of the resource of which the application will be associated.</p>", "location": "uri", "locationName": "resource"}, "options": {"shape": "Options", "documentation": "<p> Determines whether an application tag is applied or skipped. </p>"}}}, "AssociateResourceResponse": {"type": "structure", "members": {"applicationArn": {"shape": "ApplicationArn", "documentation": "<p>The Amazon resource name (ARN) of the application that was augmented with attributes.</p>"}, "resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon resource name (ARN) that specifies the resource.</p>"}, "options": {"shape": "Options", "documentation": "<p> Determines whether an application tag is applied or skipped. </p>"}}}, "AssociationCount": {"type": "integer", "min": 0}, "AssociationOption": {"type": "string", "enum": ["APPLY_APPLICATION_TAG", "SKIP_APPLICATION_TAG"]}, "AttributeGroup": {"type": "structure", "members": {"id": {"shape": "AttributeGroupId", "documentation": "<p>The globally unique attribute group identifier of the attribute group.</p>"}, "arn": {"shape": "AttributeGroupArn", "documentation": "<p>The Amazon resource name (ARN) that specifies the attribute group across services.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the attribute group.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the attribute group that the user provides.</p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p>The ISO-8601 formatted timestamp of the moment the attribute group was created.</p>"}, "lastUpdateTime": {"shape": "Timestamp", "documentation": "<p>The ISO-8601 formatted timestamp of the moment the attribute group was last updated. This time is the same as the creationTime for a newly created attribute group.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Key-value pairs you can use to associate with the attribute group.</p>"}}, "documentation": "<p>Represents a Amazon Web Services Service Catalog AppRegistry attribute group that is rich metadata which describes an application and its components.</p>"}, "AttributeGroupArn": {"type": "string", "pattern": "arn:aws[-a-z]*:servicecatalog:[a-z]{2}(-gov)?-[a-z]+-\\d:\\d{12}:/attribute-groups/[-.\\w]+"}, "AttributeGroupDetails": {"type": "structure", "members": {"id": {"shape": "AttributeGroupId", "documentation": "<p>The unique identifier of the attribute group.</p>"}, "arn": {"shape": "AttributeGroupArn", "documentation": "<p>The Amazon resource name (ARN) that specifies the attribute group.</p>"}, "name": {"shape": "Name", "documentation": "<important> <p> This field is no longer supported. We recommend you don't use the field when using <code>ListAttributeGroupsForApplication</code>. </p> </important> <p> The name of the attribute group. </p>"}, "createdBy": {"shape": "CreatedBy", "documentation": "<p>The service principal that created the attribute group.</p>"}}, "documentation": "<p> The details related to a specific AttributeGroup. </p>"}, "AttributeGroupDetailsList": {"type": "list", "member": {"shape": "AttributeGroupDetails"}}, "AttributeGroupId": {"type": "string", "max": 256, "min": 1, "pattern": "[-.\\w]+"}, "AttributeGroupIds": {"type": "list", "member": {"shape": "AttributeGroupId"}}, "AttributeGroupSpecifier": {"type": "string", "max": 512, "min": 1, "pattern": "([-.\\w]+)|(arn:aws[-a-z]*:servicecatalog:[a-z]{2}(-gov)?-[a-z]+-\\d:\\d{12}:/attribute-groups/[-.\\w]+)"}, "AttributeGroupSummaries": {"type": "list", "member": {"shape": "AttributeGroupSummary"}}, "AttributeGroupSummary": {"type": "structure", "members": {"id": {"shape": "AttributeGroupId", "documentation": "<p>The globally unique attribute group identifier of the attribute group.</p>"}, "arn": {"shape": "AttributeGroupArn", "documentation": "<p>The Amazon resource name (ARN) that specifies the attribute group across services.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the attribute group.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the attribute group that the user provides.</p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p>The ISO-8601 formatted timestamp of the moment the attribute group was created.</p>"}, "lastUpdateTime": {"shape": "Timestamp", "documentation": "<p>The ISO-8601 formatted timestamp of the moment the attribute group was last updated. This time is the same as the creationTime for a newly created attribute group.</p>"}, "createdBy": {"shape": "CreatedBy", "documentation": "<p>The service principal that created the attribute group.</p>"}}, "documentation": "<p>Summary of a Amazon Web Services Service Catalog AppRegistry attribute group.</p>"}, "Attributes": {"type": "string", "max": 8000, "min": 1, "pattern": "[\\u0009\\u000A\\u000D\\u0020-\\u00FF]+"}, "ClientToken": {"type": "string", "max": 128, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_-]*"}, "ConflictException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>There was a conflict when processing the request (for example, a resource with the given name already exists within the account).</p>", "error": {"httpStatusCode": 409}, "exception": true}, "CreateApplicationRequest": {"type": "structure", "required": ["name", "clientToken"], "members": {"name": {"shape": "Name", "documentation": "<p>The name of the application. The name must be unique in the region in which you are creating the application.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the application.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Key-value pairs you can use to associate with the application.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique identifier that you provide to ensure idempotency. If you retry a request that completed successfully using the same client token and the same parameters, the retry succeeds without performing any further actions. If you retry a successful request using the same client token, but one or more of the parameters are different, the retry fails.</p>", "idempotencyToken": true}}}, "CreateApplicationResponse": {"type": "structure", "members": {"application": {"shape": "Application", "documentation": "<p>Information about the application.</p>"}}}, "CreateAttributeGroupRequest": {"type": "structure", "required": ["name", "attributes", "clientToken"], "members": {"name": {"shape": "Name", "documentation": "<p>The name of the attribute group.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the attribute group that the user provides.</p>"}, "attributes": {"shape": "Attributes", "documentation": "<p>A JSON string in the form of nested key-value pairs that represent the attributes in the group and describes an application and its components.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Key-value pairs you can use to associate with the attribute group.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique identifier that you provide to ensure idempotency. If you retry a request that completed successfully using the same client token and the same parameters, the retry succeeds without performing any further actions. If you retry a successful request using the same client token, but one or more of the parameters are different, the retry fails.</p>", "idempotencyToken": true}}}, "CreateAttributeGroupResponse": {"type": "structure", "members": {"attributeGroup": {"shape": "AttributeGroup", "documentation": "<p>Information about the attribute group.</p>"}}}, "CreatedBy": {"type": "string", "max": 128, "min": 1, "pattern": "^(?!-)([a-z0-9-]+\\.)+(aws\\.internal|amazonaws\\.com(\\.cn)?)$"}, "DeleteApplicationRequest": {"type": "structure", "required": ["application"], "members": {"application": {"shape": "ApplicationSpecifier", "documentation": "<p> The name, ID, or ARN of the application. </p>", "location": "uri", "locationName": "application"}}}, "DeleteApplicationResponse": {"type": "structure", "members": {"application": {"shape": "ApplicationSummary", "documentation": "<p>Information about the deleted application.</p>"}}}, "DeleteAttributeGroupRequest": {"type": "structure", "required": ["attributeGroup"], "members": {"attributeGroup": {"shape": "AttributeGroupSpecifier", "documentation": "<p> The name, ID, or ARN of the attribute group that holds the attributes to describe the application. </p>", "location": "uri", "locationName": "attributeGroup"}}}, "DeleteAttributeGroupResponse": {"type": "structure", "members": {"attributeGroup": {"shape": "AttributeGroupSummary", "documentation": "<p>Information about the deleted attribute group.</p>"}}}, "Description": {"type": "string", "max": 1024}, "DisassociateAttributeGroupRequest": {"type": "structure", "required": ["application", "attributeGroup"], "members": {"application": {"shape": "ApplicationSpecifier", "documentation": "<p> The name, ID, or ARN of the application. </p>", "location": "uri", "locationName": "application"}, "attributeGroup": {"shape": "AttributeGroupSpecifier", "documentation": "<p> The name, ID, or ARN of the attribute group that holds the attributes to describe the application. </p>", "location": "uri", "locationName": "attributeGroup"}}}, "DisassociateAttributeGroupResponse": {"type": "structure", "members": {"applicationArn": {"shape": "ApplicationArn", "documentation": "<p>The Amazon resource name (ARN) that specifies the application.</p>"}, "attributeGroupArn": {"shape": "AttributeGroupArn", "documentation": "<p>The Amazon resource name (ARN) that specifies the attribute group.</p>"}}}, "DisassociateResourceRequest": {"type": "structure", "required": ["application", "resourceType", "resource"], "members": {"application": {"shape": "ApplicationSpecifier", "documentation": "<p>The name or ID of the application.</p>", "location": "uri", "locationName": "application"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The type of the resource that is being disassociated.</p>", "location": "uri", "locationName": "resourceType"}, "resource": {"shape": "ResourceSpecifier", "documentation": "<p>The name or ID of the resource.</p>", "location": "uri", "locationName": "resource"}}}, "DisassociateResourceResponse": {"type": "structure", "members": {"applicationArn": {"shape": "ApplicationArn", "documentation": "<p>The Amazon resource name (ARN) that specifies the application.</p>"}, "resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon resource name (ARN) that specifies the resource.</p>"}}}, "GetApplicationRequest": {"type": "structure", "required": ["application"], "members": {"application": {"shape": "ApplicationSpecifier", "documentation": "<p> The name, ID, or ARN of the application. </p>", "location": "uri", "locationName": "application"}}}, "GetApplicationResponse": {"type": "structure", "members": {"id": {"shape": "ApplicationId", "documentation": "<p>The identifier of the application.</p>"}, "arn": {"shape": "ApplicationArn", "documentation": "<p>The Amazon resource name (ARN) that specifies the application across services.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the application. The name must be unique in the region in which you are creating the application.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the application.</p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p>The ISO-8601 formatted timestamp of the moment when the application was created.</p>"}, "lastUpdateTime": {"shape": "Timestamp", "documentation": "<p>The ISO-8601 formatted timestamp of the moment when the application was last updated.</p>"}, "associatedResourceCount": {"shape": "AssociationCount", "documentation": "<p>The number of top-level resources that were registered as part of this application.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Key-value pairs associated with the application.</p>"}, "integrations": {"shape": "Integrations", "documentation": "<p> The information about the integration of the application with other services, such as Resource Groups. </p>"}, "applicationTag": {"shape": "ApplicationTagDefinition", "documentation": "<p> A key-value pair that identifies an associated resource. </p>"}}}, "GetAssociatedResourceFilter": {"type": "list", "member": {"shape": "ResourceItemStatus"}, "max": 4, "min": 1}, "GetAssociatedResourceRequest": {"type": "structure", "required": ["application", "resourceType", "resource"], "members": {"application": {"shape": "ApplicationSpecifier", "documentation": "<p> The name, ID, or ARN of the application. </p>", "location": "uri", "locationName": "application"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The type of resource associated with the application.</p>", "location": "uri", "locationName": "resourceType"}, "resource": {"shape": "ResourceSpecifier", "documentation": "<p>The name or ID of the resource associated with the application.</p>", "location": "uri", "locationName": "resource"}, "nextToken": {"shape": "NextToken", "documentation": "<p> A unique pagination token for each page of results. Make the call again with the returned token to retrieve the next page of results. </p>", "location": "querystring", "locationName": "nextToken"}, "resourceTagStatus": {"shape": "GetAssociatedResourceFilter", "documentation": "<p> States whether an application tag is applied, not applied, in the process of being applied, or skipped. </p>", "location": "querystring", "locationName": "resourceTagStatus"}, "maxResults": {"shape": "MaxResults", "documentation": "<p> The maximum number of results to return. If the parameter is omitted, it defaults to 25. The value is optional. </p>", "box": true, "location": "querystring", "locationName": "maxResults"}}}, "GetAssociatedResourceResponse": {"type": "structure", "members": {"resource": {"shape": "Resource", "documentation": "<p>The resource associated with the application.</p>"}, "options": {"shape": "Options", "documentation": "<p> Determines whether an application tag is applied or skipped. </p>"}, "applicationTagResult": {"shape": "ApplicationTagResult", "documentation": "<p> The result of the application that's tag applied to a resource. </p>"}}}, "GetAttributeGroupRequest": {"type": "structure", "required": ["attributeGroup"], "members": {"attributeGroup": {"shape": "AttributeGroupSpecifier", "documentation": "<p> The name, ID, or ARN of the attribute group that holds the attributes to describe the application. </p>", "location": "uri", "locationName": "attributeGroup"}}}, "GetAttributeGroupResponse": {"type": "structure", "members": {"id": {"shape": "AttributeGroupId", "documentation": "<p>The identifier of the attribute group.</p>"}, "arn": {"shape": "AttributeGroupArn", "documentation": "<p>The Amazon resource name (ARN) that specifies the attribute group across services.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the attribute group.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the attribute group that the user provides.</p>"}, "attributes": {"shape": "Attributes", "documentation": "<p>A JSON string in the form of nested key-value pairs that represent the attributes in the group and describes an application and its components.</p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p>The ISO-8601 formatted timestamp of the moment the attribute group was created.</p>"}, "lastUpdateTime": {"shape": "Timestamp", "documentation": "<p>The ISO-8601 formatted timestamp of the moment the attribute group was last updated. This time is the same as the creationTime for a newly created attribute group.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Key-value pairs associated with the attribute group.</p>"}, "createdBy": {"shape": "CreatedBy", "documentation": "<p>The service principal that created the attribute group.</p>"}}}, "GetConfigurationResponse": {"type": "structure", "members": {"configuration": {"shape": "AppRegistryConfiguration", "documentation": "<p> Retrieves <code>Tag<PERSON>ey</code> configuration from an account. </p>"}}}, "Integrations": {"type": "structure", "members": {"resourceGroup": {"shape": "ResourceGroup", "documentation": "<p> The information about the resource group integration.</p>"}, "applicationTagResourceGroup": {"shape": "ResourceGroup"}}, "documentation": "<p> The information about the service integration.</p>"}, "InternalServerException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The service is experiencing internal problems.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "ListApplicationsRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token to use to get the next page of results after a previous API call. </p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The upper bound of the number of results to return (cannot exceed 25). If this parameter is omitted, it defaults to 25. This value is optional.</p>", "box": true, "location": "querystring", "locationName": "maxResults"}}}, "ListApplicationsResponse": {"type": "structure", "members": {"applications": {"shape": "ApplicationSummaries", "documentation": "<p>This list of applications.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to use to get the next page of results after a previous API call. </p>"}}}, "ListAssociatedAttributeGroupsRequest": {"type": "structure", "required": ["application"], "members": {"application": {"shape": "ApplicationSpecifier", "documentation": "<p>The name or ID of the application.</p>", "location": "uri", "locationName": "application"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to use to get the next page of results after a previous API call. </p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The upper bound of the number of results to return (cannot exceed 25). If this parameter is omitted, it defaults to 25. This value is optional.</p>", "box": true, "location": "querystring", "locationName": "maxResults"}}}, "ListAssociatedAttributeGroupsResponse": {"type": "structure", "members": {"attributeGroups": {"shape": "AttributeGroupIds", "documentation": "<p>A list of attribute group IDs.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to use to get the next page of results after a previous API call. </p>"}}}, "ListAssociatedResourcesRequest": {"type": "structure", "required": ["application"], "members": {"application": {"shape": "ApplicationSpecifier", "documentation": "<p> The name, ID, or ARN of the application. </p>", "location": "uri", "locationName": "application"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to use to get the next page of results after a previous API call. </p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The upper bound of the number of results to return (cannot exceed 25). If this parameter is omitted, it defaults to 25. This value is optional.</p>", "box": true, "location": "querystring", "locationName": "maxResults"}}}, "ListAssociatedResourcesResponse": {"type": "structure", "members": {"resources": {"shape": "Resources", "documentation": "<p>Information about the resources.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to use to get the next page of results after a previous API call. </p>"}}}, "ListAttributeGroupsForApplicationRequest": {"type": "structure", "required": ["application"], "members": {"application": {"shape": "ApplicationSpecifier", "documentation": "<p>The name or ID of the application.</p>", "location": "uri", "locationName": "application"}, "nextToken": {"shape": "NextToken", "documentation": "<p>This token retrieves the next page of results after a previous API call.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The upper bound of the number of results to return. The value cannot exceed 25. If you omit this parameter, it defaults to 25. This value is optional.</p>", "box": true, "location": "querystring", "locationName": "maxResults"}}}, "ListAttributeGroupsForApplicationResponse": {"type": "structure", "members": {"attributeGroupsDetails": {"shape": "AttributeGroupDetailsList", "documentation": "<p> The details related to a specific attribute group. </p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to use to get the next page of results after a previous API call.</p>"}}}, "ListAttributeGroupsRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token to use to get the next page of results after a previous API call. </p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The upper bound of the number of results to return (cannot exceed 25). If this parameter is omitted, it defaults to 25. This value is optional.</p>", "box": true, "location": "querystring", "locationName": "maxResults"}}}, "ListAttributeGroupsResponse": {"type": "structure", "members": {"attributeGroups": {"shape": "AttributeGroupSummaries", "documentation": "<p>This list of attribute groups.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to use to get the next page of results after a previous API call. </p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon resource name (ARN) that specifies the resource.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "Tags", "documentation": "<p>The tags on the resource.</p>"}}}, "MaxResults": {"type": "integer", "max": 100, "min": 1}, "Name": {"type": "string", "max": 256, "min": 1, "pattern": "[-.\\w]+"}, "NextToken": {"type": "string", "max": 2024, "min": 1, "pattern": "[A-Za-z0-9+/=]+"}, "Options": {"type": "list", "member": {"shape": "AssociationOption"}}, "PutConfigurationRequest": {"type": "structure", "required": ["configuration"], "members": {"configuration": {"shape": "AppRegistryConfiguration", "documentation": "<p> Associates a <code>TagKey</code> configuration to an account. </p>"}}}, "Resource": {"type": "structure", "members": {"name": {"shape": "ResourceSpecifier", "documentation": "<p>The name of the resource.</p>"}, "arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon resource name (ARN) of the resource.</p>"}, "associationTime": {"shape": "Timestamp", "documentation": "<p>The time the resource was associated with the application.</p>"}, "integrations": {"shape": "ResourceIntegrations", "documentation": "<p>The service integration information about the resource. </p>"}}, "documentation": "<p> The information about the resource.</p>"}, "ResourceDetails": {"type": "structure", "members": {"tagValue": {"shape": "TagValue", "documentation": "<p>The value of the tag.</p>"}}, "documentation": "<p> The details related to the resource. </p>"}, "ResourceGroup": {"type": "structure", "members": {"state": {"shape": "ResourceGroupState", "documentation": "<p>The state of the propagation process for the resource group. The states includes:</p> <p> <code>CREATING </code>if the resource group is in the process of being created.</p> <p> <code>CREATE_COMPLETE</code> if the resource group was created successfully.</p> <p> <code>CREATE_FAILED</code> if the resource group failed to be created.</p> <p> <code>UPDATING</code> if the resource group is in the process of being updated.</p> <p> <code>UPDATE_COMPLETE</code> if the resource group updated successfully.</p> <p> <code>UPDATE_FAILED</code> if the resource group could not update successfully.</p>"}, "arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon resource name (ARN) of the resource group.</p>"}, "errorMessage": {"shape": "String", "documentation": "<p>The error message that generates when the propagation process for the resource group fails.</p>"}}, "documentation": "<p>The information about the resource group integration.</p>"}, "ResourceGroupState": {"type": "string", "enum": ["CREATING", "CREATE_COMPLETE", "CREATE_FAILED", "UPDATING", "UPDATE_COMPLETE", "UPDATE_FAILED"]}, "ResourceInfo": {"type": "structure", "members": {"name": {"shape": "ResourceSpecifier", "documentation": "<p>The name of the resource.</p>"}, "arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon resource name (ARN) that specifies the resource across services.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p> Provides information about the Service Catalog App Registry resource type. </p>"}, "resourceDetails": {"shape": "ResourceDetails", "documentation": "<p> The details related to the resource. </p>"}, "options": {"shape": "Options", "documentation": "<p> Determines whether an application tag is applied or skipped. </p>"}}, "documentation": "<p>The information about the resource.</p>"}, "ResourceIntegrations": {"type": "structure", "members": {"resourceGroup": {"shape": "ResourceGroup", "documentation": "<p>The information about the integration of Resource Groups.</p>"}}, "documentation": "<p>The service integration information about the resource.</p>"}, "ResourceItemStatus": {"type": "string", "enum": ["SUCCESS", "FAILED", "IN_PROGRESS", "SKIPPED"]}, "ResourceItemType": {"type": "string", "pattern": "AWS::[a-zA-Z0-9]+::\\w+"}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The specified resource does not exist.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "ResourceSpecifier": {"type": "string", "max": 256, "min": 1, "pattern": "\\S+"}, "ResourceType": {"type": "string", "enum": ["CFN_STACK", "RESOURCE_TAG_VALUE"]}, "Resources": {"type": "list", "member": {"shape": "ResourceInfo"}}, "ResourcesList": {"type": "list", "member": {"shape": "ResourcesListItem"}}, "ResourcesListItem": {"type": "structure", "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p> The Amazon resource name (ARN) of the resource. </p>"}, "errorMessage": {"shape": "ResourcesListItemErrorMessage", "documentation": "<p> The message returned if the call fails. </p>"}, "status": {"shape": "String", "documentation": "<p> The status of the list item. </p>"}, "resourceType": {"shape": "ResourceItemType", "documentation": "<p> Provides information about the AppRegistry resource type. </p>"}}, "documentation": "<p> The resource in a list of resources. </p>"}, "ResourcesListItemErrorMessage": {"type": "string", "max": 1024, "min": 1}, "ServiceQuotaExceededException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p> The maximum number of resources per account has been reached.</p>", "error": {"httpStatusCode": 402}, "exception": true}, "String": {"type": "string"}, "SyncAction": {"type": "string", "enum": ["START_SYNC", "NO_ACTION"]}, "SyncResourceRequest": {"type": "structure", "required": ["resourceType", "resource"], "members": {"resourceType": {"shape": "ResourceType", "documentation": "<p>The type of resource of which the application will be associated.</p>", "location": "uri", "locationName": "resourceType"}, "resource": {"shape": "ResourceSpecifier", "documentation": "<p>An entity you can work with and specify with a name or ID. Examples include an Amazon EC2 instance, an Amazon Web Services CloudFormation stack, or an Amazon S3 bucket.</p>", "location": "uri", "locationName": "resource"}}}, "SyncResourceResponse": {"type": "structure", "members": {"applicationArn": {"shape": "ApplicationArn", "documentation": "<p>The Amazon resource name (ARN) that specifies the application.</p>"}, "resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon resource name (ARN) that specifies the resource.</p>"}, "actionTaken": {"shape": "SyncAction", "documentation": "<p>The results of the output if an application is associated with an ARN value, which could be <code>syncStarted</code> or None.</p>"}}}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:\\/=+\\-@]*)$"}, "TagKeyConfig": {"type": "string", "max": 128, "min": 0, "pattern": "^(?!\\s+$)[\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*"}, "TagKeys": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 0}, "TagQueryConfiguration": {"type": "structure", "members": {"tagKey": {"shape": "TagKeyConfig", "documentation": "<p> Condition in the IAM policy that associates resources to an application. </p>"}}, "documentation": "<p> The definition of <code>tagQuery</code>. Specifies which resources are associated with an application. </p>"}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon resource name (ARN) that specifies the resource.</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "Tags", "documentation": "<p>The new or modified tags for the resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "pattern": "[\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*"}, "Tags": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "max": 50, "min": 0}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String", "documentation": "<p>A message associated with the Throttling exception.</p>"}, "serviceCode": {"shape": "String", "documentation": "<p>The originating service code.</p>"}}, "documentation": "<p> The maximum number of API requests has been exceeded. </p>", "error": {"httpStatusCode": 429}, "exception": true}, "Timestamp": {"type": "timestamp", "timestampFormat": "iso8601"}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon resource name (ARN) that specifies the resource.</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeys", "documentation": "<p>A list of the tag keys to remove from the specified resource.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateApplicationRequest": {"type": "structure", "required": ["application"], "members": {"application": {"shape": "ApplicationSpecifier", "documentation": "<p> The name, ID, or ARN of the application that will be updated. </p>", "location": "uri", "locationName": "application"}, "name": {"shape": "Name", "documentation": "<p>Deprecated: The new name of the application. The name must be unique in the region in which you are updating the application. Please do not use this field as we have stopped supporting name updates.</p>", "deprecated": true, "deprecatedMessage": "Name update for application is deprecated."}, "description": {"shape": "Description", "documentation": "<p>The new description of the application.</p>"}}}, "UpdateApplicationResponse": {"type": "structure", "members": {"application": {"shape": "Application", "documentation": "<p>The updated information of the application.</p>"}}}, "UpdateAttributeGroupRequest": {"type": "structure", "required": ["attributeGroup"], "members": {"attributeGroup": {"shape": "AttributeGroupSpecifier", "documentation": "<p> The name, ID, or ARN of the attribute group that holds the attributes to describe the application. </p>", "location": "uri", "locationName": "attributeGroup"}, "name": {"shape": "Name", "documentation": "<p>Deprecated: The new name of the attribute group. The name must be unique in the region in which you are updating the attribute group. Please do not use this field as we have stopped supporting name updates.</p>", "deprecated": true, "deprecatedMessage": "Name update for attribute group is deprecated."}, "description": {"shape": "Description", "documentation": "<p>The description of the attribute group that the user provides.</p>"}, "attributes": {"shape": "Attributes", "documentation": "<p>A JSON string in the form of nested key-value pairs that represent the attributes in the group and describes an application and its components.</p>"}}}, "UpdateAttributeGroupResponse": {"type": "structure", "members": {"attributeGroup": {"shape": "AttributeGroup", "documentation": "<p>The updated information of the attribute group.</p>"}}}, "ValidationException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The request has invalid or missing parameters.</p>", "error": {"httpStatusCode": 400}, "exception": true}}, "documentation": "<p> Amazon Web Services Service Catalog AppRegistry enables organizations to understand the application context of their Amazon Web Services resources. AppRegistry provides a repository of your applications, their resources, and the application metadata that you use within your enterprise.</p>"}