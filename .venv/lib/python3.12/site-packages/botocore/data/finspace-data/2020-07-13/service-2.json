{"version": "2.0", "metadata": {"apiVersion": "2020-07-13", "endpointPrefix": "finspace-api", "jsonVersion": "1.1", "protocol": "rest-json", "serviceAbbreviation": "FinSpace Data", "serviceFullName": "FinSpace Public API", "serviceId": "finspace data", "signatureVersion": "v4", "signingName": "finspace-api", "uid": "finspace-2020-07-13"}, "operations": {"AssociateUserToPermissionGroup": {"name": "AssociateUserToPermissionGroup", "http": {"method": "POST", "requestUri": "/permission-group/{permissionGroupId}/users/{userId}"}, "input": {"shape": "AssociateUserToPermissionGroupRequest"}, "output": {"shape": "AssociateUserToPermissionGroupResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Adds a user to a permission group to grant permissions for actions a user can perform in FinSpace.</p>"}, "CreateChangeset": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/datasets/{datasetId}/changesetsv2"}, "input": {"shape": "CreateChangesetRequest"}, "output": {"shape": "CreateChangesetResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates a new Changeset in a FinSpace Dataset.</p>"}, "CreateDataView": {"name": "CreateDataView", "http": {"method": "POST", "requestUri": "/datasets/{datasetId}/dataviewsv2"}, "input": {"shape": "CreateDataViewRequest"}, "output": {"shape": "CreateDataViewResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Creates a Dataview for a Dataset.</p>"}, "CreateDataset": {"name": "CreateDataset", "http": {"method": "POST", "requestUri": "/datasetsv2"}, "input": {"shape": "CreateDatasetRequest"}, "output": {"shape": "CreateDatasetResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Creates a new FinSpace Dataset.</p>"}, "CreatePermissionGroup": {"name": "CreatePermissionGroup", "http": {"method": "POST", "requestUri": "/permission-group"}, "input": {"shape": "CreatePermissionGroupRequest"}, "output": {"shape": "CreatePermissionGroupResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates a group of permissions for various actions that a user can perform in FinSpace.</p>"}, "CreateUser": {"name": "CreateUser", "http": {"method": "POST", "requestUri": "/user"}, "input": {"shape": "CreateUserRequest"}, "output": {"shape": "CreateUserResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates a new user in FinSpace.</p>"}, "DeleteDataset": {"name": "DeleteDataset", "http": {"method": "DELETE", "requestUri": "/datasetsv2/{datasetId}"}, "input": {"shape": "DeleteDatasetRequest"}, "output": {"shape": "DeleteDatasetResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes a FinSpace Dataset.</p>"}, "DeletePermissionGroup": {"name": "DeletePermissionGroup", "http": {"method": "DELETE", "requestUri": "/permission-group/{permissionGroupId}"}, "input": {"shape": "DeletePermissionGroupRequest"}, "output": {"shape": "DeletePermissionGroupResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes a permission group. This action is irreversible.</p>"}, "DisableUser": {"name": "DisableUser", "http": {"method": "POST", "requestUri": "/user/{userId}/disable"}, "input": {"shape": "DisableUserRequest"}, "output": {"shape": "DisableUserResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Denies access to the FinSpace web application and API for the specified user.</p>"}, "DisassociateUserFromPermissionGroup": {"name": "DisassociateUserFromPermissionGroup", "http": {"method": "DELETE", "requestUri": "/permission-group/{permissionGroupId}/users/{userId}"}, "input": {"shape": "DisassociateUserFromPermissionGroupRequest"}, "output": {"shape": "DisassociateUserFromPermissionGroupResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Removes a user from a permission group.</p>"}, "EnableUser": {"name": "EnableUser", "http": {"method": "POST", "requestUri": "/user/{userId}/enable"}, "input": {"shape": "EnableUserRequest"}, "output": {"shape": "EnableUserResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictException"}], "documentation": "<p> Allows the specified user to access the FinSpace web application and API.</p>"}, "GetChangeset": {"name": "GetChangeset", "http": {"method": "GET", "requestUri": "/datasets/{datasetId}/changesetsv2/{changesetId}"}, "input": {"shape": "GetChangesetRequest"}, "output": {"shape": "GetChangesetResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}], "documentation": "<p>Get information about a Changeset.</p>"}, "GetDataView": {"name": "GetDataView", "http": {"method": "GET", "requestUri": "/datasets/{datasetId}/dataviewsv2/{dataviewId}"}, "input": {"shape": "GetDataViewRequest"}, "output": {"shape": "GetDataViewResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Gets information about a Dataview.</p>"}, "GetDataset": {"name": "GetDataset", "http": {"method": "GET", "requestUri": "/datasetsv2/{datasetId}"}, "input": {"shape": "GetDatasetRequest"}, "output": {"shape": "GetDatasetResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Returns information about a Dataset.</p>"}, "GetExternalDataViewAccessDetails": {"name": "GetExternalDataViewAccessDetails", "http": {"method": "POST", "requestUri": "/datasets/{datasetId}/dataviewsv2/{dataviewId}/external-access-details"}, "input": {"shape": "GetExternalDataViewAccessDetailsRequest"}, "output": {"shape": "GetExternalDataViewAccessDetailsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Returns the credentials to access the external Dataview from an S3 location. To call this API:</p> <ul> <li> <p>You must retrieve the programmatic credentials.</p> </li> <li> <p>You must be a member of a FinSpace user group, where the dataset that you want to access has <code>Read Dataset Data</code> permissions.</p> </li> </ul>"}, "GetPermissionGroup": {"name": "GetPermissionGroup", "http": {"method": "GET", "requestUri": "/permission-group/{permissionGroupId}"}, "input": {"shape": "GetPermissionGroupRequest"}, "output": {"shape": "GetPermissionGroupResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves the details of a specific permission group.</p>"}, "GetProgrammaticAccessCredentials": {"name": "GetProgrammaticAccessCredentials", "http": {"method": "GET", "requestUri": "/credentials/programmatic"}, "input": {"shape": "GetProgrammaticAccessCredentialsRequest"}, "output": {"shape": "GetProgrammaticAccessCredentialsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Request programmatic credentials to use with FinSpace SDK. For more information, see <a href=\"https://docs.aws.amazon.com/finspace/latest/data-api/fs-using-the-finspace-api.html#accessing-credentials\">Step 2. Access credentials programmatically using IAM access key id and secret access key</a>.</p>"}, "GetUser": {"name": "GetUser", "http": {"method": "GET", "requestUri": "/user/{userId}"}, "input": {"shape": "GetUserRequest"}, "output": {"shape": "GetUserResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves details for a specific user.</p>"}, "GetWorkingLocation": {"name": "GetWorkingLocation", "http": {"method": "POST", "requestUri": "/workingLocationV1"}, "input": {"shape": "GetWorkingLocationRequest"}, "output": {"shape": "GetWorkingLocationResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>A temporary Amazon S3 location, where you can copy your files from a source location to stage or use as a scratch space in FinSpace notebook.</p>"}, "ListChangesets": {"name": "ListChangesets", "http": {"method": "GET", "requestUri": "/datasets/{datasetId}/changesetsv2"}, "input": {"shape": "ListChangesetsRequest"}, "output": {"shape": "ListChangesetsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}], "documentation": "<p>Lists the FinSpace Changesets for a Dataset.</p>"}, "ListDataViews": {"name": "ListDataViews", "http": {"method": "GET", "requestUri": "/datasets/{datasetId}/dataviewsv2"}, "input": {"shape": "ListDataViewsRequest"}, "output": {"shape": "ListDataViewsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Lists all available Dataviews for a Dataset.</p>"}, "ListDatasets": {"name": "ListDatasets", "http": {"method": "GET", "requestUri": "/datasetsv2"}, "input": {"shape": "ListDatasetsRequest"}, "output": {"shape": "ListDatasetsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists all of the active Datasets that a user has access to.</p>"}, "ListPermissionGroups": {"name": "ListPermissionGroups", "http": {"method": "GET", "requestUri": "/permission-group"}, "input": {"shape": "ListPermissionGroupsRequest"}, "output": {"shape": "ListPermissionGroupsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists all available permission groups in FinSpace.</p>"}, "ListPermissionGroupsByUser": {"name": "ListPermissionGroupsByUser", "http": {"method": "GET", "requestUri": "/user/{userId}/permission-groups"}, "input": {"shape": "ListPermissionGroupsByUserRequest"}, "output": {"shape": "ListPermissionGroupsByUserResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists all the permission groups that are associated with a specific user.</p>"}, "ListUsers": {"name": "ListUsers", "http": {"method": "GET", "requestUri": "/user"}, "input": {"shape": "ListUsersRequest"}, "output": {"shape": "ListUsersResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists all available users in FinSpace.</p>"}, "ListUsersByPermissionGroup": {"name": "ListUsersByPermissionGroup", "http": {"method": "GET", "requestUri": "/permission-group/{permissionGroupId}/users"}, "input": {"shape": "ListUsersByPermissionGroupRequest"}, "output": {"shape": "ListUsersByPermissionGroupResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists details of all the users in a specific permission group.</p>"}, "ResetUserPassword": {"name": "ResetUserPassword", "http": {"method": "POST", "requestUri": "/user/{userId}/password"}, "input": {"shape": "ResetUserPasswordRequest"}, "output": {"shape": "ResetUserPasswordResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Resets the password for a specified user ID and generates a temporary one. Only a superuser can reset password for other users. Resetting the password immediately invalidates the previous password associated with the user.</p>"}, "UpdateChangeset": {"name": "Update<PERSON><PERSON><PERSON><PERSON>", "http": {"method": "PUT", "requestUri": "/datasets/{datasetId}/changesetsv2/{changesetId}"}, "input": {"shape": "UpdateChangesetRequest"}, "output": {"shape": "UpdateChangesetResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}], "documentation": "<p>Updates a FinSpace Changeset.</p>"}, "UpdateDataset": {"name": "UpdateDataset", "http": {"method": "PUT", "requestUri": "/datasetsv2/{datasetId}"}, "input": {"shape": "UpdateDatasetRequest"}, "output": {"shape": "UpdateDatasetResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Updates a FinSpace Dataset.</p>"}, "UpdatePermissionGroup": {"name": "UpdatePermissionGroup", "http": {"method": "PUT", "requestUri": "/permission-group/{permissionGroupId}"}, "input": {"shape": "UpdatePermissionGroupRequest"}, "output": {"shape": "UpdatePermissionGroupResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Modifies the details of a permission group. You cannot modify a <code>permissionGroupID</code>.</p>"}, "UpdateUser": {"name": "UpdateUser", "http": {"method": "PUT", "requestUri": "/user/{userId}"}, "input": {"shape": "UpdateUserRequest"}, "output": {"shape": "UpdateUserResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Modifies the details of the specified user. You cannot update the <code>userId</code> for a user.</p>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>You do not have sufficient access to perform this action.</p>", "error": {"httpStatusCode": 403}, "exception": true}, "AccessKeyId": {"type": "string", "max": 255, "min": 1, "pattern": "[\\s\\S]*\\S[\\s\\S]*"}, "AliasString": {"type": "string", "max": 255, "min": 1, "pattern": "^alias\\/\\S+"}, "ApiAccess": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "ApplicationPermission": {"type": "string", "enum": ["CreateDataset", "ManageClusters", "ManageUsersAndGroups", "ManageAttributeSets", "ViewAuditData", "AccessNotebooks", "GetTemporaryCredentials"]}, "ApplicationPermissionList": {"type": "list", "member": {"shape": "ApplicationPermission"}}, "AssociateUserToPermissionGroupRequest": {"type": "structure", "required": ["permissionGroupId", "userId"], "members": {"permissionGroupId": {"shape": "PermissionGroupId", "documentation": "<p>The unique identifier for the permission group.</p>", "location": "uri", "locationName": "permissionGroupId"}, "userId": {"shape": "UserId", "documentation": "<p>The unique identifier for the user.</p>", "location": "uri", "locationName": "userId"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}}}, "AssociateUserToPermissionGroupResponse": {"type": "structure", "members": {"statusCode": {"shape": "StatusCode", "documentation": "<p>The returned status code of the response.</p>", "location": "statusCode"}}}, "AwsCredentials": {"type": "structure", "members": {"accessKeyId": {"shape": "AccessKeyId", "documentation": "<p> The unique identifier for the security credentials.</p>"}, "secretAccessKey": {"shape": "SecretAccess<PERSON>ey", "documentation": "<p> The secret access key that can be used to sign requests.</p>"}, "sessionToken": {"shape": "SessionToken", "documentation": "<p> The token that users must pass to use the credentials.</p>"}, "expiration": {"shape": "TimestampEpoch", "documentation": "<p> The Epoch time when the current credentials expire.</p>"}}, "documentation": "<p> The credentials required to access the external Dataview from the S3 location.</p>", "sensitive": true}, "Boolean": {"type": "boolean", "documentation": "Common Boolean data type"}, "ChangeType": {"type": "string", "documentation": "Indicates how the given change will be applied to the dataset.", "enum": ["REPLACE", "APPEND", "MODIFY"]}, "ChangesetArn": {"type": "string", "documentation": "Arn for a given Changeset"}, "ChangesetErrorInfo": {"type": "structure", "members": {"errorMessage": {"shape": "ErrorMessage", "documentation": "<p>The text of the error message.</p>"}, "errorCategory": {"shape": "Error<PERSON>ate<PERSON><PERSON>", "documentation": "<p>The category of the error.</p> <ul> <li> <p> <code>VALIDATION</code> – The inputs to this request are invalid.</p> </li> <li> <p> <code>SERVICE_QUOTA_EXCEEDED</code> – Service quotas have been exceeded. Please contact AWS support to increase quotas.</p> </li> <li> <p> <code>ACCESS_DENIED</code> – Missing required permission to perform this request.</p> </li> <li> <p> <code>RESOURCE_NOT_FOUND</code> – One or more inputs to this request were not found.</p> </li> <li> <p> <code>THROTTLING</code> – The system temporarily lacks sufficient resources to process the request.</p> </li> <li> <p> <code>INTERNAL_SERVICE_EXCEPTION</code> – An internal service error has occurred.</p> </li> <li> <p> <code>CANCELLED</code> – Cancelled.</p> </li> <li> <p> <code>USER_RECOVERABLE</code> – A user recoverable error has occurred.</p> </li> </ul>"}}, "documentation": "<p>The structure with error messages.</p>"}, "ChangesetId": {"type": "string", "documentation": "ID used to identify a Changeset", "max": 26, "min": 1}, "ChangesetList": {"type": "list", "member": {"shape": "ChangesetSummary"}, "documentation": "List of Changeset Summaries"}, "ChangesetSummary": {"type": "structure", "members": {"changesetId": {"shape": "ChangesetId", "documentation": "<p>The unique identifier for a Changeset.</p>"}, "changesetArn": {"shape": "ChangesetArn", "documentation": "<p>The ARN identifier of the Changeset.</p>"}, "datasetId": {"shape": "DatasetId", "documentation": "<p>The unique identifier for the FinSpace Dataset in which the Changeset is created.</p>"}, "changeType": {"shape": "ChangeType", "documentation": "<p>Type that indicates how a Changeset is applied to a Dataset.</p> <ul> <li> <p> <code>REPLACE</code> – Changeset is considered as a replacement to all prior loaded Changesets.</p> </li> <li> <p> <code>APPEND</code> – Changeset is considered as an addition to the end of all prior loaded Changesets.</p> </li> <li> <p> <code>MODIFY</code> – Changeset is considered as a replacement to a specific prior ingested Changeset.</p> </li> </ul>"}, "sourceParams": {"shape": "SourceParams", "documentation": "<p>Options that define the location of the data being ingested.</p>"}, "formatParams": {"shape": "FormatParams", "documentation": "<p>Options that define the structure of the source file(s).</p>"}, "createTime": {"shape": "TimestampEpoch", "documentation": "<p>The timestamp at which the Changeset was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as 1635768000000.</p>"}, "status": {"shape": "IngestionStatus", "documentation": "<p>Status of the Changeset ingestion.</p> <ul> <li> <p> <code>PENDING</code> – Changeset is pending creation.</p> </li> <li> <p> <code>FAILED</code> – Changeset creation has failed.</p> </li> <li> <p> <code>SUCCESS</code> – Changeset creation has succeeded.</p> </li> <li> <p> <code>RUNNING</code> – Changeset creation is running.</p> </li> <li> <p> <code>STOP_REQUESTED</code> – User requested Changeset creation to stop.</p> </li> </ul>"}, "errorInfo": {"shape": "ChangesetErrorInfo", "documentation": "<p>The structure with error messages.</p>"}, "activeUntilTimestamp": {"shape": "TimestampEpoch", "documentation": "<p>Time until which the Changeset is active. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as 1635768000000.</p>", "box": true}, "activeFromTimestamp": {"shape": "TimestampEpoch", "documentation": "<p>Beginning time from which the Changeset is active. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as 1635768000000.</p>", "box": true}, "updatesChangesetId": {"shape": "ChangesetId", "documentation": "<p>The unique identifier of the Changeset that is updated.</p>"}, "updatedByChangesetId": {"shape": "ChangesetId", "documentation": "<p>The unique identifier of the updated Changeset.</p>"}}, "documentation": "<p>A Changeset is unit of data in a Dataset.</p>"}, "ClientToken": {"type": "string", "documentation": "Idempotence Token for API operations", "max": 128, "min": 1, "pattern": ".*\\S.*"}, "ColumnDataType": {"type": "string", "documentation": "Data type of a column.", "enum": ["STRING", "CHAR", "INTEGER", "TINYINT", "SMALLINT", "BIGINT", "FLOAT", "DOUBLE", "DATE", "DATETIME", "BOOLEAN", "BINARY"]}, "ColumnDefinition": {"type": "structure", "members": {"dataType": {"shape": "ColumnDataType", "documentation": "<p>Data type of a column.</p> <ul> <li> <p> <code>STRING</code> – A String data type.</p> <p> <code>CHAR</code> – A char data type.</p> <p> <code>INTEGER</code> – An integer data type.</p> <p> <code>TINYINT</code> – A tinyint data type.</p> <p> <code>SMALLINT</code> – A smallint data type.</p> <p> <code>BIGINT</code> – A bigint data type.</p> <p> <code>FLOAT</code> – A float data type.</p> <p> <code>DOUBLE</code> – A double data type.</p> <p> <code>DATE</code> – A date data type.</p> <p> <code>DATETIME</code> – A datetime data type.</p> <p> <code>BOOLEAN</code> – A boolean data type.</p> <p> <code>BINARY</code> – A binary data type.</p> </li> </ul>"}, "columnName": {"shape": "ColumnName", "documentation": "<p>The name of a column.</p>"}, "columnDescription": {"shape": "ColumnDescription", "documentation": "<p>Description for a column.</p>"}}, "documentation": "<p>The definition of a column in a tabular Dataset.</p>"}, "ColumnDescription": {"type": "string", "documentation": "Column Description", "max": 512, "pattern": "[\\s\\S]*"}, "ColumnList": {"type": "list", "member": {"shape": "ColumnDefinition"}, "documentation": "List of Column Definitions"}, "ColumnName": {"type": "string", "documentation": "Column Name", "max": 126, "pattern": ".*\\S.*"}, "ColumnNameList": {"type": "list", "member": {"shape": "ColumnName"}, "documentation": "List of Column Names"}, "ConflictException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}, "reason": {"shape": "errorMessage"}}, "documentation": "<p>The request conflicts with an existing resource.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "CreateChangesetRequest": {"type": "structure", "required": ["datasetId", "changeType", "sourceParams", "formatParams"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}, "datasetId": {"shape": "DatasetId", "documentation": "<p>The unique identifier for the FinSpace Dataset where the Changeset will be created. </p>", "location": "uri", "locationName": "datasetId"}, "changeType": {"shape": "ChangeType", "documentation": "<p>The option to indicate how a Changeset will be applied to a Dataset.</p> <ul> <li> <p> <code>REPLACE</code> – Changeset will be considered as a replacement to all prior loaded Changesets.</p> </li> <li> <p> <code>APPEND</code> – Changeset will be considered as an addition to the end of all prior loaded Changesets.</p> </li> <li> <p> <code>MODIFY</code> – Changeset is considered as a replacement to a specific prior ingested Changeset.</p> </li> </ul>"}, "sourceParams": {"shape": "SourceParams", "documentation": "<p>Options that define the location of the data being ingested (<code>s3SourcePath</code>) and the source of the changeset (<code>sourceType</code>).</p> <p>Both <code>s3SourcePath</code> and <code>sourceType</code> are required attributes.</p> <p>Here is an example of how you could specify the <code>sourceParams</code>:</p> <p> <code> \"sourceParams\": { \"s3SourcePath\": \"s3://finspace-landing-us-east-2-bk7gcfvitndqa6ebnvys4d/scratch/wr5hh8pwkpqqkxa4sxrmcw/ingestion/equity.csv\", \"sourceType\": \"S3\" } </code> </p> <p>The S3 path that you specify must allow the FinSpace role access. To do that, you first need to configure the IAM policy on S3 bucket. For more information, see <a href=\"https://docs.aws.amazon.com/finspace/latest/data-api/fs-using-the-finspace-api.html#access-s3-buckets\">Loading data from an Amazon S3 Bucket using the FinSpace API</a> section.</p>"}, "formatParams": {"shape": "FormatParams", "documentation": "<p>Options that define the structure of the source file(s) including the format type (<code>formatType</code>), header row (<code>withHeader</code>), data separation character (<code>separator</code>) and the type of compression (<code>compression</code>). </p> <p> <code>formatType</code> is a required attribute and can have the following values: </p> <ul> <li> <p> <code>PARQUET</code> – Parquet source file format.</p> </li> <li> <p> <code>CSV</code> – CSV source file format.</p> </li> <li> <p> <code>JSON</code> – JSON source file format.</p> </li> <li> <p> <code>XML</code> – XML source file format.</p> </li> </ul> <p>Here is an example of how you could specify the <code>formatParams</code>:</p> <p> <code> \"formatParams\": { \"formatType\": \"CSV\", \"withHeader\": \"true\", \"separator\": \",\", \"compression\":\"None\" } </code> </p> <p>Note that if you only provide <code>formatType</code> as <code>CSV</code>, the rest of the attributes will automatically default to CSV values as following:</p> <p> <code> { \"withHeader\": \"true\", \"separator\": \",\" } </code> </p> <p> For more information about supported file formats, see <a href=\"https://docs.aws.amazon.com/finspace/latest/userguide/supported-data-types.html\">Supported Data Types and File Formats</a> in the FinSpace User Guide.</p>"}}, "documentation": "The request for a CreateChangeset operation."}, "CreateChangesetResponse": {"type": "structure", "members": {"datasetId": {"shape": "DatasetId", "documentation": "<p>The unique identifier for the FinSpace Dataset where the Changeset is created.</p>"}, "changesetId": {"shape": "ChangesetId", "documentation": "<p>The unique identifier of the Changeset that is created.</p>"}}, "documentation": "The response from a CreateChangeset operation."}, "CreateDataViewRequest": {"type": "structure", "required": ["datasetId", "destinationTypeParams"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}, "datasetId": {"shape": "DatasetId", "documentation": "<p>The unique Dataset identifier that is used to create a Dataview.</p>", "location": "uri", "locationName": "datasetId"}, "autoUpdate": {"shape": "Boolean", "documentation": "<p>Flag to indicate Dataview should be updated automatically.</p>"}, "sortColumns": {"shape": "SortColumnList", "documentation": "<p>Columns to be used for sorting the data.</p>"}, "partitionColumns": {"shape": "PartitionColumnList", "documentation": "<p>Ordered set of column names used to partition data.</p>"}, "asOfTimestamp": {"shape": "TimestampEpoch", "documentation": "<p>Beginning time to use for the Dataview. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as 1635768000000.</p>", "box": true}, "destinationTypeParams": {"shape": "DataViewDestinationTypeParams", "documentation": "<p>Options that define the destination type for the Dataview.</p>"}}, "documentation": "Request for creating a data view."}, "CreateDataViewResponse": {"type": "structure", "members": {"datasetId": {"shape": "DatasetId", "documentation": "<p>The unique identifier of the Dataset used for the Dataview.</p>"}, "dataViewId": {"shape": "DataViewId", "documentation": "<p>The unique identifier for the created Dataview.</p>"}}, "documentation": "Response for creating a data view."}, "CreateDatasetRequest": {"type": "structure", "required": ["datasetTitle", "kind", "permissionGroupParams"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}, "datasetTitle": {"shape": "DatasetTitle", "documentation": "<p>Display title for a FinSpace Dataset.</p>"}, "kind": {"shape": "DatasetKind", "documentation": "<p>The format in which Dataset data is structured.</p> <ul> <li> <p> <code>TABULAR</code> – Data is structured in a tabular format.</p> </li> <li> <p> <code>NON_TABULAR</code> – Data is structured in a non-tabular format.</p> </li> </ul>"}, "datasetDescription": {"shape": "DatasetDescription", "documentation": "<p>Description of a Dataset.</p>"}, "ownerInfo": {"shape": "DatasetOwnerInfo", "documentation": "<p>Contact information for a Dataset owner.</p>"}, "permissionGroupParams": {"shape": "PermissionGroupParams", "documentation": "<p>Permission group parameters for Dataset permissions.</p>"}, "alias": {"shape": "AliasString", "documentation": "<p>The unique resource identifier for a Dataset.</p>"}, "schemaDefinition": {"shape": "SchemaUnion", "documentation": "<p>Definition for a schema on a tabular Dataset.</p>"}}, "documentation": "The request for a CreateDataset operation"}, "CreateDatasetResponse": {"type": "structure", "members": {"datasetId": {"shape": "DatasetId", "documentation": "<p>The unique identifier for the created Dataset.</p>"}}, "documentation": "The response from a CreateDataset operation"}, "CreatePermissionGroupRequest": {"type": "structure", "required": ["name", "applicationPermissions"], "members": {"name": {"shape": "PermissionGroupName", "documentation": "<p>The name of the permission group.</p>"}, "description": {"shape": "PermissionGroupDescription", "documentation": "<p>A brief description for the permission group.</p>"}, "applicationPermissions": {"shape": "ApplicationPermissionList", "documentation": "<p>The option to indicate FinSpace application permissions that are granted to a specific group.</p> <important> <p>When assigning application permissions, be aware that the permission <code>ManageUsersAndGroups</code> allows users to grant themselves or others access to any functionality in their FinSpace environment's application. It should only be granted to trusted users.</p> </important> <ul> <li> <p> <code>CreateDataset</code> – Group members can create new datasets.</p> </li> <li> <p> <code>ManageClusters</code> – Group members can manage Apache Spark clusters from FinSpace notebooks.</p> </li> <li> <p> <code>ManageUsersAndGroups</code> – Group members can manage users and permission groups. This is a privileged permission that allows users to grant themselves or others access to any functionality in the application. It should only be granted to trusted users.</p> </li> <li> <p> <code>ManageAttributeSets</code> – Group members can manage attribute sets.</p> </li> <li> <p> <code>ViewAuditData</code> – Group members can view audit data.</p> </li> <li> <p> <code>AccessNotebooks</code> – Group members will have access to FinSpace notebooks.</p> </li> <li> <p> <code>GetTemporaryCredentials</code> – Group members can get temporary API credentials.</p> </li> </ul>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}}}, "CreatePermissionGroupResponse": {"type": "structure", "members": {"permissionGroupId": {"shape": "PermissionGroupId", "documentation": "<p>The unique identifier for the permission group.</p>"}}}, "CreateUserRequest": {"type": "structure", "required": ["emailAddress", "type"], "members": {"emailAddress": {"shape": "Email", "documentation": "<p>The email address of the user that you want to register. The email address serves as a uniquer identifier for each user and cannot be changed after it's created.</p>"}, "type": {"shape": "UserType", "documentation": "<p>The option to indicate the type of user. Use one of the following options to specify this parameter:</p> <ul> <li> <p> <code>SUPER_USER</code> – A user with permission to all the functionality and data in FinSpace.</p> </li> <li> <p> <code>APP_USER</code> – A user with specific permissions in FinSpace. The users are assigned permissions by adding them to a permission group.</p> </li> </ul>"}, "firstName": {"shape": "FirstName", "documentation": "<p>The first name of the user that you want to register.</p>"}, "lastName": {"shape": "LastName", "documentation": "<p>The last name of the user that you want to register.</p>"}, "apiAccess": {"shape": "ApiAccess", "documentation": "<p>The option to indicate whether the user can use the <code>GetProgrammaticAccessCredentials</code> API to obtain credentials that can then be used to access other FinSpace Data API operations.</p> <ul> <li> <p> <code>ENABLED</code> – The user has permissions to use the APIs.</p> </li> <li> <p> <code>DISABLED</code> – The user does not have permissions to use any APIs.</p> </li> </ul>"}, "apiAccessPrincipalArn": {"shape": "RoleArn", "documentation": "<p>The ARN identifier of an AWS user or role that is allowed to call the <code>GetProgrammaticAccessCredentials</code> API to obtain a credentials token for a specific FinSpace user. This must be an IAM role within your FinSpace account.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}}}, "CreateUserResponse": {"type": "structure", "members": {"userId": {"shape": "UserId", "documentation": "<p>The unique identifier for the user.</p>"}}}, "Credentials": {"type": "structure", "members": {"accessKeyId": {"shape": "stringValueLength1to255", "documentation": "<p>The access key identifier.</p>"}, "secretAccessKey": {"shape": "stringValueMaxLength1000", "documentation": "<p>The access key.</p>"}, "sessionToken": {"shape": "stringValueMaxLength1000", "documentation": "<p>The session token.</p>"}}, "documentation": "<p>Short term API credentials.</p>", "sensitive": true}, "DataViewArn": {"type": "string", "documentation": "Arn of a DataView"}, "DataViewDestinationType": {"type": "string", "documentation": "DataView Destination Type"}, "DataViewDestinationTypeParams": {"type": "structure", "required": ["destinationType"], "members": {"destinationType": {"shape": "DataViewDestinationType", "documentation": "<p>Destination type for a Dataview.</p> <ul> <li> <p> <code>GLUE_TABLE</code> – Glue table destination type.</p> </li> <li> <p> <code>S3</code> – S3 destination type.</p> </li> </ul>"}, "s3DestinationExportFileFormat": {"shape": "ExportFileFormat", "documentation": "<p>Dataview export file format.</p> <ul> <li> <p> <code>PARQUET</code> – Parquet export file format.</p> </li> <li> <p> <code>DELIMITED_TEXT</code> – Delimited text export file format.</p> </li> </ul>"}, "s3DestinationExportFileFormatOptions": {"shape": "S3DestinationFormatOptions", "documentation": "<p>Format Options for S3 Destination type.</p> <p>Here is an example of how you could specify the <code>s3DestinationExportFileFormatOptions</code> </p> <p> <code> { \"header\": \"true\", \"delimiter\": \",\", \"compression\": \"gzip\" }</code> </p>"}}, "documentation": "<p>Structure for the Dataview destination type parameters.</p>"}, "DataViewErrorInfo": {"type": "structure", "members": {"errorMessage": {"shape": "ErrorMessage", "documentation": "<p>The text of the error message.</p>"}, "errorCategory": {"shape": "Error<PERSON>ate<PERSON><PERSON>", "documentation": "<p>The category of the error.</p> <ul> <li> <p> <code>VALIDATION</code> – The inputs to this request are invalid.</p> </li> <li> <p> <code>SERVICE_QUOTA_EXCEEDED</code> – Service quotas have been exceeded. Please contact AWS support to increase quotas.</p> </li> <li> <p> <code>ACCESS_DENIED</code> – Missing required permission to perform this request.</p> </li> <li> <p> <code>RESOURCE_NOT_FOUND</code> – One or more inputs to this request were not found.</p> </li> <li> <p> <code>THROTTLING</code> – The system temporarily lacks sufficient resources to process the request.</p> </li> <li> <p> <code>INTERNAL_SERVICE_EXCEPTION</code> – An internal service error has occurred.</p> </li> <li> <p> <code>CANCELLED</code> – Cancelled.</p> </li> <li> <p> <code>USER_RECOVERABLE</code> – A user recoverable error has occurred.</p> </li> </ul>"}}, "documentation": "<p>The structure with error messages.</p>"}, "DataViewId": {"type": "string", "documentation": "DataView ID", "max": 26, "min": 1}, "DataViewList": {"type": "list", "member": {"shape": "DataViewSummary"}, "documentation": "List of Data Views"}, "DataViewStatus": {"type": "string", "documentation": "Status of a DataView", "enum": ["RUNNING", "STARTING", "FAILED", "CANCELLED", "TIMEOUT", "SUCCESS", "PENDING", "FAILED_CLEANUP_FAILED"]}, "DataViewSummary": {"type": "structure", "members": {"dataViewId": {"shape": "DataViewId", "documentation": "<p>The unique identifier for the Dataview.</p>"}, "dataViewArn": {"shape": "DataViewArn", "documentation": "<p>The ARN identifier of the Dataview.</p>"}, "datasetId": {"shape": "DatasetId", "documentation": "<p>Th unique identifier for the Dataview Dataset.</p>"}, "asOfTimestamp": {"shape": "TimestampEpoch", "documentation": "<p>Time range to use for the Dataview. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as 1635768000000.</p>", "box": true}, "partitionColumns": {"shape": "PartitionColumnList", "documentation": "<p>Ordered set of column names used to partition data.</p>"}, "sortColumns": {"shape": "SortColumnList", "documentation": "<p>Columns to be used for sorting the data.</p>"}, "status": {"shape": "DataViewStatus", "documentation": "<p>The status of a Dataview creation.</p> <ul> <li> <p> <code>RUNNING</code> – Dataview creation is running.</p> </li> <li> <p> <code>STARTING</code> – Dataview creation is starting.</p> </li> <li> <p> <code>FAILED</code> – Dataview creation has failed.</p> </li> <li> <p> <code>CANCELLED</code> – Dataview creation has been cancelled.</p> </li> <li> <p> <code>TIMEOUT</code> – Dataview creation has timed out.</p> </li> <li> <p> <code>SUCCESS</code> – Dataview creation has succeeded.</p> </li> <li> <p> <code>PENDING</code> – Dataview creation is pending.</p> </li> <li> <p> <code>FAILED_CLEANUP_FAILED</code> – Dataview creation failed and resource cleanup failed.</p> </li> </ul>"}, "errorInfo": {"shape": "DataViewErrorInfo", "documentation": "<p>The structure with error messages.</p>"}, "destinationTypeProperties": {"shape": "DataViewDestinationTypeParams", "documentation": "<p>Information about the Dataview destination.</p>"}, "autoUpdate": {"shape": "Boolean", "documentation": "<p>The flag to indicate Dataview should be updated automatically.</p>"}, "createTime": {"shape": "TimestampEpoch", "documentation": "<p>The timestamp at which the Dataview was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as 1635768000000.</p>"}, "lastModifiedTime": {"shape": "TimestampEpoch", "documentation": "<p>The last time that a Dataview was modified. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as 1635768000000.</p>"}}, "documentation": "<p>Structure for the summary of a Dataview.</p>"}, "Dataset": {"type": "structure", "members": {"datasetId": {"shape": "DatasetId", "documentation": "<p>An identifier for a Dataset.</p>"}, "datasetArn": {"shape": "DatasetArn", "documentation": "<p>The ARN identifier of the Dataset.</p>"}, "datasetTitle": {"shape": "DatasetTitle", "documentation": "<p>Display title for a Dataset.</p>"}, "kind": {"shape": "DatasetKind", "documentation": "<p>The format in which Dataset data is structured.</p> <ul> <li> <p> <code>TABULAR</code> – Data is structured in a tabular format.</p> </li> <li> <p> <code>NON_TABULAR</code> – Data is structured in a non-tabular format.</p> </li> </ul>"}, "datasetDescription": {"shape": "DatasetDescription", "documentation": "<p>Description for a Dataset.</p>"}, "ownerInfo": {"shape": "DatasetOwnerInfo", "documentation": "<p>Contact information for a Dataset owner.</p>"}, "createTime": {"shape": "TimestampEpoch", "documentation": "<p>The timestamp at which the Dataset was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as 1635768000000.</p>"}, "lastModifiedTime": {"shape": "TimestampEpoch", "documentation": "<p>The last time that the Dataset was modified. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as 1635768000000.</p>"}, "schemaDefinition": {"shape": "SchemaUnion", "documentation": "<p>Definition for a schema on a tabular Dataset.</p>"}, "alias": {"shape": "AliasString", "documentation": "<p>The unique resource identifier for a Dataset.</p>"}}, "documentation": "<p>The structure for a Dataset.</p>"}, "DatasetArn": {"type": "string", "documentation": "Arn of a Dataset"}, "DatasetDescription": {"type": "string", "documentation": "Description of a dataset", "max": 1000, "pattern": "[\\s\\S]*"}, "DatasetId": {"type": "string", "documentation": "ID for a given Dataset", "max": 26, "min": 1}, "DatasetKind": {"type": "string", "documentation": "Dataset Kind", "enum": ["TABULAR", "NON_TABULAR"]}, "DatasetList": {"type": "list", "member": {"shape": "Dataset"}, "documentation": "List of Dataset structures"}, "DatasetOwnerInfo": {"type": "structure", "members": {"name": {"shape": "OwnerName", "documentation": "<p>The name of the Dataset owner.</p>"}, "phoneNumber": {"shape": "PhoneNumber", "documentation": "<p>Phone number for the Dataset owner.</p>"}, "email": {"shape": "Email", "documentation": "<p>Email address for the Dataset owner.</p>"}}, "documentation": "<p>A structure for Dataset owner info.</p>"}, "DatasetStatus": {"type": "string", "documentation": "Status of the dataset process returned from scheduler service.", "enum": ["PENDING", "FAILED", "SUCCESS", "RUNNING"]}, "DatasetTitle": {"type": "string", "documentation": "Title for a given Dataset", "max": 255, "min": 1, "pattern": ".*\\S.*"}, "DeleteDatasetRequest": {"type": "structure", "required": ["datasetId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}, "datasetId": {"shape": "DatasetId", "documentation": "<p>The unique identifier of the Dataset to be deleted.</p>", "location": "uri", "locationName": "datasetId"}}, "documentation": "The request for a DeleteDataset operation."}, "DeleteDatasetResponse": {"type": "structure", "members": {"datasetId": {"shape": "DatasetId", "documentation": "<p>The unique identifier for the deleted Dataset.</p>"}}, "documentation": "The response from an DeleteDataset operation"}, "DeletePermissionGroupRequest": {"type": "structure", "required": ["permissionGroupId"], "members": {"permissionGroupId": {"shape": "PermissionGroupId", "documentation": "<p>The unique identifier for the permission group that you want to delete.</p>", "location": "uri", "locationName": "permissionGroupId"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}}}, "DeletePermissionGroupResponse": {"type": "structure", "members": {"permissionGroupId": {"shape": "PermissionGroupId", "documentation": "<p>The unique identifier for the deleted permission group.</p>"}}}, "DisableUserRequest": {"type": "structure", "required": ["userId"], "members": {"userId": {"shape": "UserId", "documentation": "<p>The unique identifier for the user that you want to deactivate.</p>", "location": "uri", "locationName": "userId"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}}}, "DisableUserResponse": {"type": "structure", "members": {"userId": {"shape": "UserId", "documentation": "<p>The unique identifier for the deactivated user.</p>"}}}, "DisassociateUserFromPermissionGroupRequest": {"type": "structure", "required": ["permissionGroupId", "userId"], "members": {"permissionGroupId": {"shape": "PermissionGroupId", "documentation": "<p>The unique identifier for the permission group.</p>", "location": "uri", "locationName": "permissionGroupId"}, "userId": {"shape": "UserId", "documentation": "<p>The unique identifier for the user.</p>", "location": "uri", "locationName": "userId"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}}}, "DisassociateUserFromPermissionGroupResponse": {"type": "structure", "members": {"statusCode": {"shape": "StatusCode", "documentation": "<p>The returned status code of the response.</p>", "location": "statusCode"}}}, "Email": {"type": "string", "max": 320, "min": 4, "pattern": "[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,4}", "sensitive": true}, "EnableUserRequest": {"type": "structure", "required": ["userId"], "members": {"userId": {"shape": "UserId", "documentation": "<p>The unique identifier for the user that you want to activate.</p>", "location": "uri", "locationName": "userId"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}}}, "EnableUserResponse": {"type": "structure", "members": {"userId": {"shape": "UserId", "documentation": "<p>The unique identifier for the active user.</p>"}}}, "ErrorCategory": {"type": "string", "documentation": "Changeset Error Category", "enum": ["VALIDATION", "SERVICE_QUOTA_EXCEEDED", "ACCESS_DENIED", "RESOURCE_NOT_FOUND", "THROTTLING", "INTERNAL_SERVICE_EXCEPTION", "CANCELLED", "USER_RECOVERABLE"]}, "ErrorMessage": {"type": "string", "documentation": "Changeset Error Message", "max": 1000}, "ExportFileFormat": {"type": "string", "documentation": "Data View Export File Format", "enum": ["PARQUET", "DELIMITED_TEXT"]}, "FirstName": {"type": "string", "max": 50, "min": 1, "pattern": ".*\\S.*", "sensitive": true}, "FormatParams": {"type": "map", "key": {"shape": "StringMapKey"}, "value": {"shape": "StringMapValue"}, "documentation": "Format Parameters of a Changeset"}, "GetChangesetRequest": {"type": "structure", "required": ["datasetId", "changesetId"], "members": {"datasetId": {"shape": "DatasetId", "documentation": "<p>The unique identifier for the FinSpace Dataset where the Changeset is created.</p>", "location": "uri", "locationName": "datasetId"}, "changesetId": {"shape": "ChangesetId", "documentation": "<p>The unique identifier of the Changeset for which to get data.</p>", "location": "uri", "locationName": "changesetId"}}, "documentation": "Request to describe a changeset."}, "GetChangesetResponse": {"type": "structure", "members": {"changesetId": {"shape": "ChangesetId", "documentation": "<p>The unique identifier for a Changeset.</p>"}, "changesetArn": {"shape": "ChangesetArn", "documentation": "<p>The ARN identifier of the Changeset.</p>"}, "datasetId": {"shape": "DatasetId", "documentation": "<p>The unique identifier for the FinSpace Dataset where the Changeset is created.</p>"}, "changeType": {"shape": "ChangeType", "documentation": "<p>Type that indicates how a Changeset is applied to a Dataset.</p> <ul> <li> <p> <code>REPLACE</code> – Changeset is considered as a replacement to all prior loaded Changesets.</p> </li> <li> <p> <code>APPEND</code> – Changeset is considered as an addition to the end of all prior loaded Changesets.</p> </li> <li> <p> <code>MODIFY</code> – Changeset is considered as a replacement to a specific prior ingested Changeset.</p> </li> </ul>"}, "sourceParams": {"shape": "SourceParams", "documentation": "<p>Options that define the location of the data being ingested.</p>"}, "formatParams": {"shape": "FormatParams", "documentation": "<p>Structure of the source file(s).</p>"}, "createTime": {"shape": "TimestampEpoch", "documentation": "<p>The timestamp at which the Changeset was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as 1635768000000.</p>"}, "status": {"shape": "IngestionStatus", "documentation": "<p>The status of Changeset creation operation.</p>"}, "errorInfo": {"shape": "ChangesetErrorInfo", "documentation": "<p>The structure with error messages.</p>"}, "activeUntilTimestamp": {"shape": "TimestampEpoch", "documentation": "<p>Time until which the Changeset is active. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as 1635768000000.</p>", "box": true}, "activeFromTimestamp": {"shape": "TimestampEpoch", "documentation": "<p>Beginning time from which the Changeset is active. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as 1635768000000.</p>", "box": true}, "updatesChangesetId": {"shape": "ChangesetId", "documentation": "<p>The unique identifier of the Changeset that is being updated.</p>"}, "updatedByChangesetId": {"shape": "ChangesetId", "documentation": "<p>The unique identifier of the updated Changeset.</p>"}}, "documentation": "The response from a describe changeset operation"}, "GetDataViewRequest": {"type": "structure", "required": ["dataViewId", "datasetId"], "members": {"dataViewId": {"shape": "DataViewId", "documentation": "<p>The unique identifier for the Dataview.</p>", "location": "uri", "locationName": "dataviewId"}, "datasetId": {"shape": "DatasetId", "documentation": "<p>The unique identifier for the Dataset used in the Dataview.</p>", "location": "uri", "locationName": "datasetId"}}, "documentation": "Request for retrieving a data view detail. Grouped / accessible within a dataset by its dataset id."}, "GetDataViewResponse": {"type": "structure", "members": {"autoUpdate": {"shape": "Boolean", "documentation": "<p>Flag to indicate Dataview should be updated automatically.</p>"}, "partitionColumns": {"shape": "PartitionColumnList", "documentation": "<p>Ordered set of column names used to partition data.</p>"}, "datasetId": {"shape": "DatasetId", "documentation": "<p>The unique identifier for the Dataset used in the Dataview.</p>"}, "asOfTimestamp": {"shape": "TimestampEpoch", "documentation": "<p>Time range to use for the Dataview. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as 1635768000000.</p>", "box": true}, "errorInfo": {"shape": "DataViewErrorInfo", "documentation": "<p>Information about an error that occurred for the Dataview.</p>"}, "lastModifiedTime": {"shape": "TimestampEpoch", "documentation": "<p>The last time that a Dataview was modified. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as 1635768000000.</p>"}, "createTime": {"shape": "TimestampEpoch", "documentation": "<p>The timestamp at which the Dataview was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as 1635768000000.</p>"}, "sortColumns": {"shape": "SortColumnList", "documentation": "<p>Columns to be used for sorting the data.</p>"}, "dataViewId": {"shape": "DataViewId", "documentation": "<p>The unique identifier for the Dataview.</p>"}, "dataViewArn": {"shape": "DataViewArn", "documentation": "<p>The ARN identifier of the Dataview.</p>"}, "destinationTypeParams": {"shape": "DataViewDestinationTypeParams", "documentation": "<p>Options that define the destination type for the Dataview.</p>"}, "status": {"shape": "DataViewStatus", "documentation": "<p>The status of a Dataview creation.</p> <ul> <li> <p> <code>RUNNING</code> – Dataview creation is running.</p> </li> <li> <p> <code>STARTING</code> – Dataview creation is starting.</p> </li> <li> <p> <code>FAILED</code> – Dataview creation has failed.</p> </li> <li> <p> <code>CANCELLED</code> – Dataview creation has been cancelled.</p> </li> <li> <p> <code>TIMEOUT</code> – Dataview creation has timed out.</p> </li> <li> <p> <code>SUCCESS</code> – Dataview creation has succeeded.</p> </li> <li> <p> <code>PENDING</code> – Dataview creation is pending.</p> </li> <li> <p> <code>FAILED_CLEANUP_FAILED</code> – Dataview creation failed and resource cleanup failed.</p> </li> </ul>"}}, "documentation": "Response from retrieving a dataview, which includes details on the target database and table name"}, "GetDatasetRequest": {"type": "structure", "required": ["datasetId"], "members": {"datasetId": {"shape": "StringValueLength1to255", "documentation": "<p>The unique identifier for a Dataset.</p>", "location": "uri", "locationName": "datasetId"}}, "documentation": "Request for the GetDataset operation."}, "GetDatasetResponse": {"type": "structure", "members": {"datasetId": {"shape": "DatasetId", "documentation": "<p>The unique identifier for a Dataset.</p>"}, "datasetArn": {"shape": "DatasetArn", "documentation": "<p>The ARN identifier of the Dataset.</p>"}, "datasetTitle": {"shape": "DatasetTitle", "documentation": "<p>Display title for a Dataset.</p>"}, "kind": {"shape": "DatasetKind", "documentation": "<p>The format in which Dataset data is structured.</p> <ul> <li> <p> <code>TABULAR</code> – Data is structured in a tabular format.</p> </li> <li> <p> <code>NON_TABULAR</code> – Data is structured in a non-tabular format.</p> </li> </ul>"}, "datasetDescription": {"shape": "DatasetDescription", "documentation": "<p>A description of the Dataset.</p>"}, "createTime": {"shape": "TimestampEpoch", "documentation": "<p>The timestamp at which the Dataset was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as 1635768000000.</p>"}, "lastModifiedTime": {"shape": "TimestampEpoch", "documentation": "<p>The last time that the Dataset was modified. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as 1635768000000.</p>"}, "schemaDefinition": {"shape": "SchemaUnion", "documentation": "<p>Definition for a schema on a tabular Dataset.</p>"}, "alias": {"shape": "AliasString", "documentation": "<p>The unique resource identifier for a Dataset.</p>"}, "status": {"shape": "DatasetStatus", "documentation": "<p>Status of the Dataset creation.</p> <ul> <li> <p> <code>PENDING</code> – Dataset is pending creation.</p> </li> <li> <p> <code>FAILED</code> – Dataset creation has failed.</p> </li> <li> <p> <code>SUCCESS</code> – Dataset creation has succeeded.</p> </li> <li> <p> <code>RUNNING</code> – Dataset creation is running.</p> </li> </ul>"}}, "documentation": "Response for the GetDataset operation"}, "GetExternalDataViewAccessDetailsRequest": {"type": "structure", "required": ["dataViewId", "datasetId"], "members": {"dataViewId": {"shape": "DataViewId", "documentation": "<p>The unique identifier for the Dataview that you want to access.</p>", "location": "uri", "locationName": "dataviewId"}, "datasetId": {"shape": "DatasetId", "documentation": "<p>The unique identifier for the Dataset.</p>", "location": "uri", "locationName": "datasetId"}}}, "GetExternalDataViewAccessDetailsResponse": {"type": "structure", "members": {"credentials": {"shape": "AwsCredentials", "documentation": "<p>The credentials required to access the external Dataview from the S3 location.</p>"}, "s3Location": {"shape": "S3Location", "documentation": "<p>The location where the external Dataview is stored.</p>"}}}, "GetPermissionGroupRequest": {"type": "structure", "required": ["permissionGroupId"], "members": {"permissionGroupId": {"shape": "PermissionGroupId", "documentation": "<p>The unique identifier for the permission group.</p>", "location": "uri", "locationName": "permissionGroupId"}}}, "GetPermissionGroupResponse": {"type": "structure", "members": {"permissionGroup": {"shape": "PermissionGroup"}}}, "GetProgrammaticAccessCredentialsRequest": {"type": "structure", "required": ["environmentId"], "members": {"durationInMinutes": {"shape": "SessionDuration", "documentation": "<p>The time duration in which the credentials remain valid. </p>", "location": "querystring", "locationName": "durationInMinutes"}, "environmentId": {"shape": "IdType", "documentation": "<p>The FinSpace environment identifier.</p>", "location": "querystring", "locationName": "environmentId"}}, "documentation": "Request for GetProgrammaticAccessCredentials operation"}, "GetProgrammaticAccessCredentialsResponse": {"type": "structure", "members": {"credentials": {"shape": "Credentials", "documentation": "<p>Returns the programmatic credentials.</p>"}, "durationInMinutes": {"shape": "SessionDuration", "documentation": "<p>Returns the duration in which the credentials will remain valid.</p>"}}, "documentation": "Response for GetProgrammaticAccessCredentials operation"}, "GetUserRequest": {"type": "structure", "required": ["userId"], "members": {"userId": {"shape": "UserId", "documentation": "<p>The unique identifier of the user to get data for.</p>", "location": "uri", "locationName": "userId"}}}, "GetUserResponse": {"type": "structure", "members": {"userId": {"shape": "UserId", "documentation": "<p>The unique identifier for the user that is retrieved.</p>"}, "status": {"shape": "UserStatus", "documentation": "<p>The current status of the user. </p> <ul> <li> <p> <code>CREATING</code> – The creation is in progress.</p> </li> <li> <p> <code>ENABLED</code> – The user is created and is currently active.</p> </li> <li> <p> <code>DISABLED</code> – The user is currently inactive.</p> </li> </ul>"}, "firstName": {"shape": "FirstName", "documentation": "<p>The first name of the user.</p>"}, "lastName": {"shape": "LastName", "documentation": "<p>The last name of the user.</p>"}, "emailAddress": {"shape": "Email", "documentation": "<p>The email address that is associated with the user.</p>"}, "type": {"shape": "UserType", "documentation": "<p>Indicates the type of user. </p> <ul> <li> <p> <code>SUPER_USER</code> – A user with permission to all the functionality and data in FinSpace.</p> </li> </ul> <ul> <li> <p> <code>APP_USER</code> – A user with specific permissions in FinSpace. The users are assigned permissions by adding them to a permission group.</p> </li> </ul>"}, "apiAccess": {"shape": "ApiAccess", "documentation": "<p>Indicates whether the user can use the <code>GetProgrammaticAccessCredentials</code> API to obtain credentials that can then be used to access other FinSpace Data API operations. </p> <ul> <li> <p> <code>ENABLED</code> – The user has permissions to use the APIs.</p> </li> <li> <p> <code>DISABLED</code> – The user does not have permissions to use any APIs.</p> </li> </ul>"}, "apiAccessPrincipalArn": {"shape": "RoleArn", "documentation": "<p>The ARN identifier of an AWS user or role that is allowed to call the <code>GetProgrammaticAccessCredentials</code> API to obtain a credentials token for a specific FinSpace user. This must be an IAM role within your FinSpace account.</p>"}, "createTime": {"shape": "TimestampEpoch", "documentation": "<p>The timestamp at which the user was created in FinSpace. The value is determined as epoch time in milliseconds. </p>"}, "lastEnabledTime": {"shape": "TimestampEpoch", "documentation": "<p>Describes the last time the user was activated. The value is determined as epoch time in milliseconds.</p>"}, "lastDisabledTime": {"shape": "TimestampEpoch", "documentation": "<p>Describes the last time the user was deactivated. The value is determined as epoch time in milliseconds.</p>"}, "lastModifiedTime": {"shape": "TimestampEpoch", "documentation": "<p>Describes the last time the user details were updated. The value is determined as epoch time in milliseconds.</p>"}, "lastLoginTime": {"shape": "TimestampEpoch", "documentation": "<p>Describes the last time that the user logged into their account. The value is determined as epoch time in milliseconds.</p>"}}}, "GetWorkingLocationRequest": {"type": "structure", "members": {"locationType": {"shape": "locationType", "documentation": "<p>Specify the type of the working location.</p> <ul> <li> <p> <code>SAGEMAKER</code> – Use the Amazon S3 location as a temporary location to store data content when working with FinSpace Notebooks that run on SageMaker studio.</p> </li> <li> <p> <code>INGESTION</code> – Use the Amazon S3 location as a staging location to copy your data content and then use the location with the Changeset creation operation.</p> </li> </ul>"}}}, "GetWorkingLocationResponse": {"type": "structure", "members": {"s3Uri": {"shape": "stringValueLength1to1024", "documentation": "<p>Returns the Amazon S3 URI for the working location.</p>"}, "s3Path": {"shape": "stringValueLength1to1024", "documentation": "<p>Returns the Amazon S3 Path for the working location.</p>"}, "s3Bucket": {"shape": "stringValueLength1to63", "documentation": "<p>Returns the Amazon S3 bucket name for the working location.</p>"}}}, "IdType": {"type": "string", "max": 26, "min": 1}, "IngestionStatus": {"type": "string", "documentation": "Status of the ingestion process returned from scheduler service.", "enum": ["PENDING", "FAILED", "SUCCESS", "RUNNING", "STOP_REQUESTED"]}, "InternalServerException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>The request processing has failed because of an unknown error, exception or failure.</p>", "error": {"httpStatusCode": 500}, "exception": true}, "LastName": {"type": "string", "max": 50, "min": 1, "pattern": ".*\\S.*", "sensitive": true}, "LimitExceededException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>A limit has exceeded.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ListChangesetsRequest": {"type": "structure", "required": ["datasetId"], "members": {"datasetId": {"shape": "DatasetId", "documentation": "<p>The unique identifier for the FinSpace Dataset to which the Changeset belongs.</p>", "location": "uri", "locationName": "datasetId"}, "maxResults": {"shape": "ResultLimit", "documentation": "<p>The maximum number of results per page.</p>", "box": true, "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>", "location": "querystring", "locationName": "nextToken"}}, "documentation": "Request to ListChangesetsRequest. It exposes minimal query filters."}, "ListChangesetsResponse": {"type": "structure", "members": {"changesets": {"shape": "ChangesetList", "documentation": "<p>List of Changesets found.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>"}}, "documentation": "Response to ListChangesetsResponse. This returns a list of dataset changesets that match the query criteria."}, "ListDataViewsRequest": {"type": "structure", "required": ["datasetId"], "members": {"datasetId": {"shape": "DatasetId", "documentation": "<p>The unique identifier of the Dataset for which to retrieve Dataviews.</p>", "location": "uri", "locationName": "datasetId"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "ResultLimit", "documentation": "<p>The maximum number of results per page.</p>", "box": true, "location": "querystring", "locationName": "maxResults"}}, "documentation": "Request for a list data views."}, "ListDataViewsResponse": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>"}, "dataViews": {"shape": "DataViewList", "documentation": "<p>A list of Dataviews.</p>"}}}, "ListDatasetsRequest": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "ResultLimit", "documentation": "<p>The maximum number of results per page.</p>", "box": true, "location": "querystring", "locationName": "maxResults"}}, "documentation": "Request for the ListDatasets operation."}, "ListDatasetsResponse": {"type": "structure", "members": {"datasets": {"shape": "DatasetList", "documentation": "<p>List of Datasets.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>"}}, "documentation": "Response for the ListDatasets operation"}, "ListPermissionGroupsByUserRequest": {"type": "structure", "required": ["userId", "maxResults"], "members": {"userId": {"shape": "UserId", "documentation": "<p>The unique identifier for the user.</p>", "location": "uri", "locationName": "userId"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "ResultLimit", "documentation": "<p>The maximum number of results per page.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListPermissionGroupsByUserResponse": {"type": "structure", "members": {"permissionGroups": {"shape": "PermissionGroupByUserList", "documentation": "<p>A list of returned permission groups.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>"}}}, "ListPermissionGroupsRequest": {"type": "structure", "required": ["maxResults"], "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "ResultLimit", "documentation": "<p>The maximum number of results per page.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListPermissionGroupsResponse": {"type": "structure", "members": {"permissionGroups": {"shape": "PermissionGroupList", "documentation": "<p>A list of all the permission groups.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>"}}}, "ListUsersByPermissionGroupRequest": {"type": "structure", "required": ["permissionGroupId", "maxResults"], "members": {"permissionGroupId": {"shape": "PermissionGroupId", "documentation": "<p>The unique identifier for the permission group.</p>", "location": "uri", "locationName": "permissionGroupId"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "ResultLimit", "documentation": "<p>The maximum number of results per page.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListUsersByPermissionGroupResponse": {"type": "structure", "members": {"users": {"shape": "UserByPermissionGroupList", "documentation": "<p>Lists details of all users in a specific permission group.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>"}}}, "ListUsersRequest": {"type": "structure", "required": ["maxResults"], "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "ResultLimit", "documentation": "<p>The maximum number of results per page.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListUsersResponse": {"type": "structure", "members": {"users": {"shape": "UserList", "documentation": "<p>A list of all the users.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>"}}}, "OwnerName": {"type": "string", "documentation": "1 - 250 character String", "max": 250, "min": 1, "pattern": ".*\\S.*"}, "PaginationToken": {"type": "string"}, "PartitionColumnList": {"type": "list", "member": {"shape": "StringValueLength1to255"}, "documentation": "DataView Partition Column List"}, "Password": {"type": "string", "max": 20, "min": 8, "pattern": ".*\\S.*", "sensitive": true}, "PermissionGroup": {"type": "structure", "members": {"permissionGroupId": {"shape": "PermissionGroupId", "documentation": "<p> The unique identifier for the permission group.</p>"}, "name": {"shape": "PermissionGroupName", "documentation": "<p>The name of the permission group.</p>"}, "description": {"shape": "PermissionGroupDescription", "documentation": "<p> A brief description for the permission group.</p>"}, "applicationPermissions": {"shape": "ApplicationPermissionList", "documentation": "<p>Indicates the permissions that are granted to a specific group for accessing the FinSpace application.</p> <important> <p>When assigning application permissions, be aware that the permission <code>ManageUsersAndGroups</code> allows users to grant themselves or others access to any functionality in their FinSpace environment's application. It should only be granted to trusted users.</p> </important> <ul> <li> <p> <code>CreateDataset</code> – Group members can create new datasets.</p> </li> <li> <p> <code>ManageClusters</code> – Group members can manage Apache Spark clusters from FinSpace notebooks.</p> </li> <li> <p> <code>ManageUsersAndGroups</code> – Group members can manage users and permission groups. This is a privileged permission that allows users to grant themselves or others access to any functionality in the application. It should only be granted to trusted users.</p> </li> <li> <p> <code>ManageAttributeSets</code> – Group members can manage attribute sets.</p> </li> <li> <p> <code>ViewAuditData</code> – Group members can view audit data.</p> </li> <li> <p> <code>AccessNotebooks</code> – Group members will have access to FinSpace notebooks.</p> </li> <li> <p> <code>GetTemporaryCredentials</code> – Group members can get temporary API credentials.</p> </li> </ul>"}, "createTime": {"shape": "TimestampEpoch", "documentation": "<p>The timestamp at which the group was created in FinSpace. The value is determined as epoch time in milliseconds. </p>"}, "lastModifiedTime": {"shape": "TimestampEpoch", "documentation": "<p>Describes the last time the permission group was updated. The value is determined as epoch time in milliseconds. </p>"}, "membershipStatus": {"shape": "PermissionGroupMembershipStatus", "documentation": "<p>Indicates the status of the user within a permission group.</p> <ul> <li> <p> <code>ADDITION_IN_PROGRESS</code> – The user is currently being added to the permission group.</p> </li> <li> <p> <code>ADDITION_SUCCESS</code> – The user is successfully added to the permission group.</p> </li> <li> <p> <code>REMOVAL_IN_PROGRESS</code> – The user is currently being removed from the permission group.</p> </li> </ul>"}}, "documentation": "<p>The structure for a permission group.</p>"}, "PermissionGroupByUser": {"type": "structure", "members": {"permissionGroupId": {"shape": "PermissionGroupId", "documentation": "<p>The unique identifier for the permission group.</p>"}, "name": {"shape": "PermissionGroupName", "documentation": "<p>The name of the permission group.</p>"}, "membershipStatus": {"shape": "PermissionGroupMembershipStatus", "documentation": "<p>Indicates the status of the user within a permission group.</p> <ul> <li> <p> <code>ADDITION_IN_PROGRESS</code> – The user is currently being added to the permission group.</p> </li> <li> <p> <code>ADDITION_SUCCESS</code> – The user is successfully added to the permission group.</p> </li> <li> <p> <code>REMOVAL_IN_PROGRESS</code> – The user is currently being removed from the permission group.</p> </li> </ul>"}}, "documentation": "<p>The structure of a permission group associated with a user.</p>"}, "PermissionGroupByUserList": {"type": "list", "member": {"shape": "PermissionGroupByUser"}}, "PermissionGroupDescription": {"type": "string", "max": 4000, "min": 1, "pattern": "[\\s\\S]*", "sensitive": true}, "PermissionGroupId": {"type": "string", "max": 26, "min": 1, "pattern": ".*\\S.*"}, "PermissionGroupList": {"type": "list", "member": {"shape": "PermissionGroup"}}, "PermissionGroupMembershipStatus": {"type": "string", "enum": ["ADDITION_IN_PROGRESS", "ADDITION_SUCCESS", "REMOVAL_IN_PROGRESS"]}, "PermissionGroupName": {"type": "string", "max": 255, "min": 1, "pattern": ".*\\S.*", "sensitive": true}, "PermissionGroupParams": {"type": "structure", "members": {"permissionGroupId": {"shape": "PermissionGroupId", "documentation": "<p>The unique identifier for the <code>PermissionGroup</code>.</p>"}, "datasetPermissions": {"shape": "ResourcePermissionsList", "documentation": "<p>List of resource permissions.</p>"}}, "documentation": "<p>Permission group parameters for Dataset permissions.</p> <p>Here is an example of how you could specify the <code>PermissionGroupParams</code>:</p> <p> <code> { \"permissionGroupId\": \"0r6fCRtSTUk4XPfXQe3M0g\", \"datasetPermissions\": [ {\"permission\": \"ViewDatasetDetails\"}, {\"permission\": \"AddDatasetData\"}, {\"permission\": \"EditDatasetMetadata\"}, {\"permission\": \"DeleteDataset\"} ] } </code> </p>"}, "PhoneNumber": {"type": "string", "documentation": "PhoneNumber of Dataset Owner", "max": 20, "min": 10, "pattern": "^[\\+0-9\\#\\,\\(][\\+0-9\\-\\.\\/\\(\\)\\,\\#\\s]+$"}, "ResetUserPasswordRequest": {"type": "structure", "required": ["userId"], "members": {"userId": {"shape": "UserId", "documentation": "<p>The unique identifier of the user that a temporary password is requested for.</p>", "location": "uri", "locationName": "userId"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}}}, "ResetUserPasswordResponse": {"type": "structure", "members": {"userId": {"shape": "UserId", "documentation": "<p>The unique identifier of the user that a new password is generated for.</p>"}, "temporaryPassword": {"shape": "Password", "documentation": "<p>A randomly generated temporary password for the requested user. This password expires in 7 days.</p>"}}}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}, "reason": {"shape": "errorMessage"}}, "documentation": "<p>One or more resources can't be found.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "ResourcePermission": {"type": "structure", "members": {"permission": {"shape": "StringValueLength1to250", "documentation": "<p>Permission for a resource.</p>"}}, "documentation": "<p>Resource permission for a dataset. When you create a dataset, all the other members of the same user group inherit access to the dataset. You can only create a dataset if your user group has application permission for Create Datasets.</p> <p>The following is a list of valid dataset permissions that you can apply: </p> <ul> <li> <p> <code>ViewDatasetDetails</code> </p> </li> <li> <p> <code>ReadDatasetDetails</code> </p> </li> <li> <p> <code>AddDatasetData</code> </p> </li> <li> <p> <code>CreateDataView</code> </p> </li> <li> <p> <code>EditDatasetMetadata</code> </p> </li> <li> <p> <code>DeleteDataset</code> </p> </li> </ul> <p>For more information on the dataset permissions, see <a href=\"https://docs.aws.amazon.com/finspace/latest/userguide/managing-user-permissions.html#supported-dataset-permissions\">Supported Dataset Permissions</a> in the FinSpace User Guide.</p>"}, "ResourcePermissionsList": {"type": "list", "member": {"shape": "ResourcePermission"}, "documentation": "List of Resource Permissions"}, "ResultLimit": {"type": "integer", "max": 100, "min": 1}, "RoleArn": {"type": "string", "max": 2048, "min": 20, "pattern": "^arn:aws[a-z\\-]*:iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+$"}, "S3BucketName": {"type": "string", "max": 63, "min": 1, "pattern": "^.*\\S.*$"}, "S3DestinationFormatOptions": {"type": "map", "key": {"shape": "StringMapKey"}, "value": {"shape": "StringMapValue"}}, "S3Key": {"type": "string", "max": 1024, "min": 1, "pattern": "^.*\\S.*$"}, "S3Location": {"type": "structure", "required": ["bucket", "key"], "members": {"bucket": {"shape": "S3BucketName", "documentation": "<p> The name of the S3 bucket.</p>"}, "key": {"shape": "S3Key", "documentation": "<p> The path of the folder, within the S3 bucket that contains the Dataset.</p>"}}, "documentation": "<p>The location of an external Dataview in an S3 bucket.</p>"}, "SchemaDefinition": {"type": "structure", "members": {"columns": {"shape": "ColumnList", "documentation": "<p>List of column definitions.</p>"}, "primaryKeyColumns": {"shape": "ColumnNameList", "documentation": "<p>List of column names used for primary key.</p>"}}, "documentation": "<p>Definition for a schema on a tabular Dataset.</p>"}, "SchemaUnion": {"type": "structure", "members": {"tabularSchemaConfig": {"shape": "SchemaDefinition", "documentation": "<p>The configuration for a schema on a tabular Dataset.</p>"}}, "documentation": "<p>A union of schema types.</p>"}, "SecretAccessKey": {"type": "string", "max": 1000, "min": 1, "pattern": "[\\s\\S]*\\S[\\s\\S]*", "sensitive": true}, "SessionDuration": {"type": "long", "max": 60, "min": 1}, "SessionToken": {"type": "string", "max": 1000, "min": 1, "pattern": "[\\s\\S]*\\S[\\s\\S]*", "sensitive": true}, "SortColumnList": {"type": "list", "member": {"shape": "StringValueLength1to255"}, "documentation": "DataView Sort Column List"}, "SourceParams": {"type": "map", "key": {"shape": "StringMapKey"}, "value": {"shape": "StringMapValue"}, "documentation": "Source Parameters of a Changeset"}, "StatusCode": {"type": "integer"}, "StringMapKey": {"type": "string", "max": 128, "pattern": "[\\s\\S]*\\S[\\s\\S]*"}, "StringMapValue": {"type": "string", "max": 1000, "pattern": "[\\s\\S]*\\S[\\s\\S]*"}, "StringValueLength1to250": {"type": "string", "documentation": "1 - 250 character String", "max": 250, "min": 1, "pattern": "[\\s\\S]*\\S[\\s\\S]*"}, "StringValueLength1to255": {"type": "string", "documentation": "1 - 255 character String", "max": 255, "min": 1, "pattern": "[\\s\\S]*\\S[\\s\\S]*"}, "ThrottlingException": {"type": "structure", "members": {}, "documentation": "<p>The request was denied due to request throttling.</p>", "error": {"httpStatusCode": 429}, "exception": true}, "TimestampEpoch": {"type": "long", "documentation": "Milliseconds since UTC epoch"}, "UpdateChangesetRequest": {"type": "structure", "required": ["datasetId", "changesetId", "sourceParams", "formatParams"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}, "datasetId": {"shape": "DatasetId", "documentation": "<p>The unique identifier for the FinSpace Dataset in which the Changeset is created.</p>", "location": "uri", "locationName": "datasetId"}, "changesetId": {"shape": "ChangesetId", "documentation": "<p>The unique identifier for the Changeset to update.</p>", "location": "uri", "locationName": "changesetId"}, "sourceParams": {"shape": "SourceParams", "documentation": "<p>Options that define the location of the data being ingested (<code>s3SourcePath</code>) and the source of the changeset (<code>sourceType</code>).</p> <p>Both <code>s3SourcePath</code> and <code>sourceType</code> are required attributes.</p> <p>Here is an example of how you could specify the <code>sourceParams</code>:</p> <p> <code> \"sourceParams\": { \"s3SourcePath\": \"s3://finspace-landing-us-east-2-bk7gcfvitndqa6ebnvys4d/scratch/wr5hh8pwkpqqkxa4sxrmcw/ingestion/equity.csv\", \"sourceType\": \"S3\" } </code> </p> <p>The S3 path that you specify must allow the FinSpace role access. To do that, you first need to configure the IAM policy on S3 bucket. For more information, see <a href=\"https://docs.aws.amazon.com/finspace/latest/data-api/fs-using-the-finspace-api.html#access-s3-buckets\">Loading data from an Amazon S3 Bucket using the FinSpace API</a>section.</p>"}, "formatParams": {"shape": "FormatParams", "documentation": "<p>Options that define the structure of the source file(s) including the format type (<code>formatType</code>), header row (<code>withHeader</code>), data separation character (<code>separator</code>) and the type of compression (<code>compression</code>). </p> <p> <code>formatType</code> is a required attribute and can have the following values: </p> <ul> <li> <p> <code>PARQUET</code> – Parquet source file format.</p> </li> <li> <p> <code>CSV</code> – CSV source file format.</p> </li> <li> <p> <code>JSON</code> – JSON source file format.</p> </li> <li> <p> <code>XML</code> – XML source file format.</p> </li> </ul> <p>Here is an example of how you could specify the <code>formatParams</code>:</p> <p> <code> \"formatParams\": { \"formatType\": \"CSV\", \"withHeader\": \"true\", \"separator\": \",\", \"compression\":\"None\" } </code> </p> <p>Note that if you only provide <code>formatType</code> as <code>CSV</code>, the rest of the attributes will automatically default to CSV values as following:</p> <p> <code> { \"withHeader\": \"true\", \"separator\": \",\" } </code> </p> <p> For more information about supported file formats, see <a href=\"https://docs.aws.amazon.com/finspace/latest/userguide/supported-data-types.html\">Supported Data Types and File Formats</a> in the FinSpace User Guide.</p>"}}, "documentation": "Request to update an existing changeset."}, "UpdateChangesetResponse": {"type": "structure", "members": {"changesetId": {"shape": "ChangesetId", "documentation": "<p>The unique identifier for the Changeset to update.</p>"}, "datasetId": {"shape": "DatasetId", "documentation": "<p>The unique identifier for the FinSpace Dataset in which the Changeset is created.</p>"}}, "documentation": "The response from a update changeset operation."}, "UpdateDatasetRequest": {"type": "structure", "required": ["datasetId", "datasetTitle", "kind"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}, "datasetId": {"shape": "DatasetId", "documentation": "<p>The unique identifier for the Dataset to update.</p>", "location": "uri", "locationName": "datasetId"}, "datasetTitle": {"shape": "DatasetTitle", "documentation": "<p>A display title for the Dataset.</p>"}, "kind": {"shape": "DatasetKind", "documentation": "<p>The format in which the Dataset data is structured.</p> <ul> <li> <p> <code>TABULAR</code> – Data is structured in a tabular format.</p> </li> <li> <p> <code>NON_TABULAR</code> – Data is structured in a non-tabular format.</p> </li> </ul>"}, "datasetDescription": {"shape": "DatasetDescription", "documentation": "<p>A description for the Dataset.</p>"}, "alias": {"shape": "AliasString", "documentation": "<p>The unique resource identifier for a Dataset.</p>"}, "schemaDefinition": {"shape": "SchemaUnion", "documentation": "<p>Definition for a schema on a tabular Dataset.</p>"}}, "documentation": "The request for an UpdateDataset operation"}, "UpdateDatasetResponse": {"type": "structure", "members": {"datasetId": {"shape": "DatasetId", "documentation": "<p>The unique identifier for updated Dataset.</p>"}}, "documentation": "The response from an UpdateDataset operation"}, "UpdatePermissionGroupRequest": {"type": "structure", "required": ["permissionGroupId"], "members": {"permissionGroupId": {"shape": "PermissionGroupId", "documentation": "<p>The unique identifier for the permission group to update.</p>", "location": "uri", "locationName": "permissionGroupId"}, "name": {"shape": "PermissionGroupName", "documentation": "<p>The name of the permission group.</p>"}, "description": {"shape": "PermissionGroupDescription", "documentation": "<p>A brief description for the permission group.</p>"}, "applicationPermissions": {"shape": "ApplicationPermissionList", "documentation": "<p>The permissions that are granted to a specific group for accessing the FinSpace application.</p> <important> <p>When assigning application permissions, be aware that the permission <code>ManageUsersAndGroups</code> allows users to grant themselves or others access to any functionality in their FinSpace environment's application. It should only be granted to trusted users.</p> </important> <ul> <li> <p> <code>CreateDataset</code> – Group members can create new datasets.</p> </li> <li> <p> <code>ManageClusters</code> – Group members can manage Apache Spark clusters from FinSpace notebooks.</p> </li> <li> <p> <code>ManageUsersAndGroups</code> – Group members can manage users and permission groups. This is a privileged permission that allows users to grant themselves or others access to any functionality in the application. It should only be granted to trusted users.</p> </li> <li> <p> <code>ManageAttributeSets</code> – Group members can manage attribute sets.</p> </li> <li> <p> <code>ViewAuditData</code> – Group members can view audit data.</p> </li> <li> <p> <code>AccessNotebooks</code> – Group members will have access to FinSpace notebooks.</p> </li> <li> <p> <code>GetTemporaryCredentials</code> – Group members can get temporary API credentials.</p> </li> </ul>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}}}, "UpdatePermissionGroupResponse": {"type": "structure", "members": {"permissionGroupId": {"shape": "PermissionGroupId", "documentation": "<p>The unique identifier for the updated permission group.</p>"}}}, "UpdateUserRequest": {"type": "structure", "required": ["userId"], "members": {"userId": {"shape": "UserId", "documentation": "<p>The unique identifier for the user that you want to update.</p>", "location": "uri", "locationName": "userId"}, "type": {"shape": "UserType", "documentation": "<p>The option to indicate the type of user.</p> <ul> <li> <p> <code>SUPER_USER</code>– A user with permission to all the functionality and data in FinSpace.</p> </li> <li> <p> <code>APP_USER</code> – A user with specific permissions in FinSpace. The users are assigned permissions by adding them to a permission group.</p> </li> </ul>"}, "firstName": {"shape": "FirstName", "documentation": "<p>The first name of the user.</p>"}, "lastName": {"shape": "LastName", "documentation": "<p>The last name of the user.</p>"}, "apiAccess": {"shape": "ApiAccess", "documentation": "<p>The option to indicate whether the user can use the <code>GetProgrammaticAccessCredentials</code> API to obtain credentials that can then be used to access other FinSpace Data API operations.</p> <ul> <li> <p> <code>ENABLED</code> – The user has permissions to use the APIs.</p> </li> <li> <p> <code>DISABLED</code> – The user does not have permissions to use any APIs.</p> </li> </ul>"}, "apiAccessPrincipalArn": {"shape": "RoleArn", "documentation": "<p>The ARN identifier of an AWS user or role that is allowed to call the <code>GetProgrammaticAccessCredentials</code> API to obtain a credentials token for a specific FinSpace user. This must be an IAM role within your FinSpace account.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}}}, "UpdateUserResponse": {"type": "structure", "members": {"userId": {"shape": "UserId", "documentation": "<p>The unique identifier of the updated user.</p>"}}}, "User": {"type": "structure", "members": {"userId": {"shape": "UserId", "documentation": "<p>The unique identifier for the user.</p>"}, "status": {"shape": "UserStatus", "documentation": "<p>The current status of the user. </p> <ul> <li> <p> <code>CREATING</code> – The user creation is in progress.</p> </li> <li> <p> <code>ENABLED</code> – The user is created and is currently active.</p> </li> <li> <p> <code>DISABLED</code> – The user is currently inactive.</p> </li> </ul>"}, "firstName": {"shape": "FirstName", "documentation": "<p>The first name of the user.</p>"}, "lastName": {"shape": "LastName", "documentation": "<p> The last name of the user.</p>"}, "emailAddress": {"shape": "Email", "documentation": "<p>The email address of the user. The email address serves as a uniquer identifier for each user and cannot be changed after it's created.</p>"}, "type": {"shape": "UserType", "documentation": "<p> Indicates the type of user.</p> <ul> <li> <p> <code>SUPER_USER</code> – A user with permission to all the functionality and data in FinSpace.</p> </li> <li> <p> <code>APP_USER</code> – A user with specific permissions in FinSpace. The users are assigned permissions by adding them to a permission group.</p> </li> </ul>"}, "apiAccess": {"shape": "ApiAccess", "documentation": "<p>Indicates whether the user can use the <code>GetProgrammaticAccessCredentials</code> API to obtain credentials that can then be used to access other FinSpace Data API operations.</p> <ul> <li> <p> <code>ENABLED</code> – The user has permissions to use the APIs.</p> </li> <li> <p> <code>DISABLED</code> – The user does not have permissions to use any APIs.</p> </li> </ul>"}, "apiAccessPrincipalArn": {"shape": "RoleArn", "documentation": "<p>The ARN identifier of an AWS user or role that is allowed to call the <code>GetProgrammaticAccessCredentials</code> API to obtain a credentials token for a specific FinSpace user. This must be an IAM role within your FinSpace account.</p>"}, "createTime": {"shape": "TimestampEpoch", "documentation": "<p>The timestamp at which the user was created in FinSpace. The value is determined as epoch time in milliseconds. </p>"}, "lastEnabledTime": {"shape": "TimestampEpoch", "documentation": "<p> Describes the last time the user was activated. The value is determined as epoch time in milliseconds. </p>"}, "lastDisabledTime": {"shape": "TimestampEpoch", "documentation": "<p>Describes the last time the user was deactivated. The value is determined as epoch time in milliseconds.</p>"}, "lastModifiedTime": {"shape": "TimestampEpoch", "documentation": "<p>Describes the last time the user was updated. The value is determined as epoch time in milliseconds. </p>"}, "lastLoginTime": {"shape": "TimestampEpoch", "documentation": "<p>Describes the last time that the user logged into their account. The value is determined as epoch time in milliseconds. </p>"}}, "documentation": "<p>The details of the user.</p>"}, "UserByPermissionGroup": {"type": "structure", "members": {"userId": {"shape": "UserId", "documentation": "<p>The unique identifier for the user.</p>"}, "status": {"shape": "UserStatus", "documentation": "<p>The current status of the user. </p> <ul> <li> <p> <code>CREATING</code> – The user creation is in progress.</p> </li> <li> <p> <code>ENABLED</code> – The user is created and is currently active.</p> </li> <li> <p> <code>DISABLED</code> – The user is currently inactive.</p> </li> </ul>"}, "firstName": {"shape": "FirstName", "documentation": "<p>The first name of the user.</p>"}, "lastName": {"shape": "LastName", "documentation": "<p>The last name of the user.</p>"}, "emailAddress": {"shape": "Email", "documentation": "<p>The email address of the user. The email address serves as a unique identifier for each user and cannot be changed after it's created.</p>"}, "type": {"shape": "UserType", "documentation": "<p> Indicates the type of user.</p> <ul> <li> <p> <code>SUPER_USER</code> – A user with permission to all the functionality and data in FinSpace.</p> </li> <li> <p> <code>APP_USER</code> – A user with specific permissions in FinSpace. The users are assigned permissions by adding them to a permission group.</p> </li> </ul>"}, "apiAccess": {"shape": "ApiAccess", "documentation": "<p>Indicates whether the user can access FinSpace API operations.</p> <ul> <li> <p> <code>ENABLED</code> – The user has permissions to use the API operations.</p> </li> <li> <p> <code>DISABLED</code> – The user does not have permissions to use any API operations.</p> </li> </ul>"}, "apiAccessPrincipalArn": {"shape": "RoleArn", "documentation": "<p>The IAM ARN identifier that is attached to FinSpace API calls.</p>"}, "membershipStatus": {"shape": "PermissionGroupMembershipStatus", "documentation": "<p>Indicates the status of the user within a permission group.</p> <ul> <li> <p> <code>ADDITION_IN_PROGRESS</code> – The user is currently being added to the permission group.</p> </li> <li> <p> <code>ADDITION_SUCCESS</code> – The user is successfully added to the permission group.</p> </li> <li> <p> <code>REMOVAL_IN_PROGRESS</code> – The user is currently being removed from the permission group.</p> </li> </ul>"}}, "documentation": "<p>The structure of a user associated with a permission group.</p>"}, "UserByPermissionGroupList": {"type": "list", "member": {"shape": "UserByPermissionGroup"}}, "UserId": {"type": "string", "max": 26, "min": 1, "pattern": ".*\\S.*"}, "UserList": {"type": "list", "member": {"shape": "User"}}, "UserStatus": {"type": "string", "enum": ["CREATING", "ENABLED", "DISABLED"]}, "UserType": {"type": "string", "enum": ["SUPER_USER", "APP_USER"]}, "ValidationException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}, "reason": {"shape": "errorMessage"}}, "documentation": "<p>The input fails to satisfy the constraints specified by an AWS service.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "errorMessage": {"type": "string"}, "locationType": {"type": "string", "enum": ["INGESTION", "SAGEMAKER"]}, "stringValueLength1to1024": {"type": "string", "max": 1024, "min": 1, "pattern": ".*\\S.*"}, "stringValueLength1to255": {"type": "string", "max": 255, "min": 1}, "stringValueLength1to63": {"type": "string", "max": 63, "min": 1, "pattern": ".*\\S.*"}, "stringValueMaxLength1000": {"type": "string", "max": 1000}}, "documentation": "<p> The FinSpace APIs let you take actions inside the FinSpace.</p>"}