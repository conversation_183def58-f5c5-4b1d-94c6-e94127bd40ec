{"metadata": {"apiVersion": "2018-11-14", "endpointPrefix": "mediaconnect", "signingName": "mediaconnect", "serviceFullName": "AWS MediaConnect", "serviceId": "MediaConnect", "protocol": "rest-json", "jsonVersion": "1.1", "uid": "mediaconnect-2018-11-14", "signatureVersion": "v4"}, "operations": {"AddBridgeOutputs": {"name": "AddBridgeOutputs", "http": {"method": "POST", "requestUri": "/v1/bridges/{bridgeArn}/outputs", "responseCode": 202}, "input": {"shape": "AddBridgeOutputsRequest"}, "output": {"shape": "AddBridgeOutputsResponse", "documentation": "AWS Elemental MediaConnect added the bridge outputs successfully."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "ConflictException", "documentation": "The request could not be completed due to a conflict with the current state of the target resource."}], "documentation": "Adds outputs to an existing bridge."}, "AddBridgeSources": {"name": "AddBridgeSources", "http": {"method": "POST", "requestUri": "/v1/bridges/{bridgeArn}/sources", "responseCode": 202}, "input": {"shape": "AddBridgeSourcesRequest"}, "output": {"shape": "AddBridgeSourcesResponse", "documentation": "AWS Elemental MediaConnect added the bridge sources successfully."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "ConflictException", "documentation": "The request could not be completed due to a conflict with the current state of the target resource."}], "documentation": "Adds sources to an existing bridge."}, "AddFlowMediaStreams": {"name": "AddFlowMediaStreams", "http": {"method": "POST", "requestUri": "/v1/flows/{flowArn}/mediaStreams", "responseCode": 201}, "input": {"shape": "AddFlowMediaStreamsRequest"}, "output": {"shape": "AddFlowMediaStreamsResponse", "documentation": "MediaConnect created the new resource successfully."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}], "documentation": "Adds media streams to an existing flow. After you add a media stream to a flow, you can associate it with a source and/or an output that uses the ST 2110 JPEG XS or CDI protocol."}, "AddFlowOutputs": {"name": "AddFlowOutputs", "http": {"method": "POST", "requestUri": "/v1/flows/{flowArn}/outputs", "responseCode": 201}, "input": {"shape": "AddFlowOutputsRequest"}, "output": {"shape": "AddFlowOutputsResponse", "documentation": "AWS Elemental MediaConnect added the outputs successfully."}, "errors": [{"shape": "AddFlowOutputs420Exception", "documentation": "AWS Elemental MediaConnect can't complete this request because this flow already has the maximum number of allowed outputs (50). For more information, contact AWS Customer Support."}, {"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}], "documentation": "Adds outputs to an existing flow. You can create up to 50 outputs per flow."}, "AddFlowSources": {"name": "AddFlowSources", "http": {"method": "POST", "requestUri": "/v1/flows/{flowArn}/source", "responseCode": 201}, "input": {"shape": "AddFlowSourcesRequest"}, "output": {"shape": "AddFlowSourcesResponse", "documentation": "AWS Elemental MediaConnect added sources to the flow successfully."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}], "documentation": "Adds Sources to flow"}, "AddFlowVpcInterfaces": {"name": "AddFlowVpcInterfaces", "http": {"method": "POST", "requestUri": "/v1/flows/{flowArn}/vpcInterfaces", "responseCode": 201}, "input": {"shape": "AddFlowVpcInterfacesRequest"}, "output": {"shape": "AddFlowVpcInterfacesResponse", "documentation": "The following VPC interface was added to the Flow configuration."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}], "documentation": "Adds VPC interfaces to flow"}, "CreateBridge": {"name": "CreateBridge", "http": {"method": "POST", "requestUri": "/v1/bridges", "responseCode": 201}, "input": {"shape": "CreateBridgeRequest"}, "output": {"shape": "CreateBridgeResponse", "documentation": "AWS Elemental MediaConnect created the new bridge successfully."}, "errors": [{"shape": "CreateBridge420Exception", "documentation": "Your account already contains the maximum number of bridges per account, per Region. For more information, contact AWS Customer Support."}, {"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "ConflictException", "documentation": "The request could not be completed due to a conflict with the current state of the target resource."}], "documentation": "Creates a new bridge. The request must include one source."}, "CreateFlow": {"name": "CreateFlow", "http": {"method": "POST", "requestUri": "/v1/flows", "responseCode": 201}, "input": {"shape": "CreateFlowRequest"}, "output": {"shape": "CreateFlowResponse", "documentation": "AWS Elemental MediaConnect created the new flow successfully."}, "errors": [{"shape": "CreateFlow420Exception", "documentation": "Your account already contains the maximum number of 20 flows per account, per Region. For more information, contact AWS Customer Support."}, {"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}], "documentation": "Creates a new flow. The request must include one source. The request optionally can include outputs (up to 50) and entitlements (up to 50)."}, "CreateGateway": {"name": "CreateGateway", "http": {"method": "POST", "requestUri": "/v1/gateways", "responseCode": 201}, "input": {"shape": "CreateGatewayRequest"}, "output": {"shape": "CreateGatewayResponse", "documentation": "AWS Elemental MediaConnect created the new gateway successfully."}, "errors": [{"shape": "CreateGateway420Exception", "documentation": "Your account already contains the maximum number of gateways per account, per Region. For more information, contact AWS Customer Support."}, {"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "ConflictException", "documentation": "The request could not be completed due to a conflict with the current state of the target resource."}], "documentation": "Creates a new gateway. The request must include at least one network (up to 4)."}, "DeleteBridge": {"name": "DeleteBridge", "http": {"method": "DELETE", "requestUri": "/v1/bridges/{bridgeArn}", "responseCode": 200}, "input": {"shape": "DeleteBridgeRequest"}, "output": {"shape": "DeleteBridgeResponse", "documentation": "AWS Elemental MediaConnect deleted the bridge."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "ConflictException", "documentation": "The request could not be completed due to a conflict with the current state of the target resource."}], "documentation": "Deletes a bridge. Before you can delete a bridge, you must stop the bridge."}, "DeleteFlow": {"name": "DeleteFlow", "http": {"method": "DELETE", "requestUri": "/v1/flows/{flowArn}", "responseCode": 202}, "input": {"shape": "DeleteFlowRequest"}, "output": {"shape": "DeleteFlowResponse", "documentation": "AWS Elemental MediaConnect is deleting the flow."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}], "documentation": "Deletes a flow. Before you can delete a flow, you must stop the flow."}, "DeleteGateway": {"name": "DeleteGateway", "http": {"method": "DELETE", "requestUri": "/v1/gateways/{gatewayArn}", "responseCode": 200}, "input": {"shape": "DeleteGatewayRequest"}, "output": {"shape": "DeleteGatewayResponse", "documentation": "AWS Elemental MediaConnect deleted the gateway."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "ConflictException", "documentation": "The request could not be completed due to a conflict with the current state of the target resource."}], "documentation": "Deletes a gateway. Before you can delete a gateway, you must deregister its instances and delete its bridges."}, "DeregisterGatewayInstance": {"name": "DeregisterGatewayInstance", "http": {"method": "DELETE", "requestUri": "/v1/gateway-instances/{gatewayInstanceArn}", "responseCode": 202}, "input": {"shape": "DeregisterGatewayInstanceRequest"}, "output": {"shape": "DeregisterGatewayInstanceResponse", "documentation": "AWS Elemental MediaConnect is deleting the instance."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "ConflictException", "documentation": "The request could not be completed due to a conflict with the current state of the target resource."}], "documentation": "Deregisters an instance. Before you deregister an instance, all bridges running on the instance must be stopped. If you want to deregister an instance without stopping the bridges, you must use the --force option."}, "DescribeBridge": {"name": "DescribeBridge", "http": {"method": "GET", "requestUri": "/v1/bridges/{bridgeArn}", "responseCode": 200}, "input": {"shape": "DescribeBridgeRequest"}, "output": {"shape": "DescribeBridgeResponse", "documentation": "AWS Elemental MediaConnect returned the bridge details successfully."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "ConflictException", "documentation": "The request could not be completed due to a conflict with the current state of the target resource."}], "documentation": "Displays the details of a bridge."}, "DescribeFlow": {"name": "DescribeFlow", "http": {"method": "GET", "requestUri": "/v1/flows/{flowArn}", "responseCode": 200}, "input": {"shape": "DescribeFlowRequest"}, "output": {"shape": "DescribeFlowResponse", "documentation": "AWS Elemental MediaConnect returned the flow details successfully."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}], "documentation": "Displays the details of a flow. The response includes the flow ARN, name, and Availability Zone, as well as details about the source, outputs, and entitlements."}, "DescribeGateway": {"name": "DescribeGateway", "http": {"method": "GET", "requestUri": "/v1/gateways/{gatewayArn}", "responseCode": 200}, "input": {"shape": "DescribeGatewayRequest"}, "output": {"shape": "DescribeGatewayResponse", "documentation": "AWS Elemental MediaConnect returned the gateway details successfully."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "ConflictException", "documentation": "The request could not be completed due to a conflict with the current state of the target resource."}], "documentation": "Displays the details of a gateway. The response includes the gateway ARN, name, and CIDR blocks, as well as details about the networks."}, "DescribeGatewayInstance": {"name": "DescribeGatewayInstance", "http": {"method": "GET", "requestUri": "/v1/gateway-instances/{gatewayInstanceArn}", "responseCode": 200}, "input": {"shape": "DescribeGatewayInstanceRequest"}, "output": {"shape": "DescribeGatewayInstanceResponse", "documentation": "AWS Elemental MediaConnect returned the instance details successfully."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "ConflictException", "documentation": "The request could not be completed due to a conflict with the current state of the target resource."}], "documentation": "Displays the details of an instance."}, "DescribeOffering": {"name": "DescribeOffering", "http": {"method": "GET", "requestUri": "/v1/offerings/{offeringArn}", "responseCode": 200}, "input": {"shape": "DescribeOfferingRequest"}, "output": {"shape": "DescribeOfferingResponse", "documentation": "MediaConnect returned the offering details successfully."}, "errors": [{"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}], "documentation": "Displays the details of an offering. The response includes the offering description, duration, outbound bandwidth, price, and Amazon Resource Name (ARN)."}, "DescribeReservation": {"name": "DescribeReservation", "http": {"method": "GET", "requestUri": "/v1/reservations/{reservationArn}", "responseCode": 200}, "input": {"shape": "DescribeReservationRequest"}, "output": {"shape": "DescribeReservationResponse", "documentation": "MediaConnect returned the reservation details successfully."}, "errors": [{"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}], "documentation": "Displays the details of a reservation. The response includes the reservation name, state, start date and time, and the details of the offering that make up the rest of the reservation (such as price, duration, and outbound bandwidth)."}, "GrantFlowEntitlements": {"name": "GrantFlowEntitlements", "http": {"method": "POST", "requestUri": "/v1/flows/{flowArn}/entitlements", "responseCode": 200}, "input": {"shape": "GrantFlowEntitlementsRequest"}, "output": {"shape": "GrantFlowEntitlementsResponse", "documentation": "AWS Elemental MediaConnect granted the entitlements successfully."}, "errors": [{"shape": "GrantFlowEntitlements420Exception", "documentation": "AWS Elemental MediaConnect can't complete this request because this flow already has the maximum number of allowed entitlements (50). For more information, contact AWS Customer Support."}, {"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}], "documentation": "Grants entitlements to an existing flow."}, "ListBridges": {"name": "ListBridges", "http": {"method": "GET", "requestUri": "/v1/bridges", "responseCode": 200}, "input": {"shape": "ListBridgesRequest"}, "output": {"shape": "ListBridgesResponse", "documentation": "AWS Elemental MediaConnect returned the list of bridges successfully."}, "errors": [{"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ConflictException", "documentation": "The request could not be completed due to a conflict with the current state of the target resource."}], "documentation": "Displays a list of bridges that are associated with this account and an optionally specified Arn. This request returns a paginated result."}, "ListEntitlements": {"name": "ListEntitlements", "http": {"method": "GET", "requestUri": "/v1/entitlements", "responseCode": 200}, "input": {"shape": "ListEntitlementsRequest"}, "output": {"shape": "ListEntitlementsResponse", "documentation": "AWS Elemental MediaConnect returned the list of entitlements successfully."}, "errors": [{"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}], "documentation": "Displays a list of all entitlements that have been granted to this account. This request returns 20 results per page."}, "ListFlows": {"name": "ListFlows", "http": {"method": "GET", "requestUri": "/v1/flows", "responseCode": 200}, "input": {"shape": "ListFlowsRequest"}, "output": {"shape": "ListFlowsResponse", "documentation": "AWS Elemental MediaConnect returned the list of flows successfully."}, "errors": [{"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}], "documentation": "Displays a list of flows that are associated with this account. This request returns a paginated result."}, "ListGatewayInstances": {"name": "ListGatewayInstances", "http": {"method": "GET", "requestUri": "/v1/gateway-instances", "responseCode": 200}, "input": {"shape": "ListGatewayInstancesRequest"}, "output": {"shape": "ListGatewayInstancesResponse", "documentation": "AWS Elemental MediaConnect returned the list of instances in the gateway successfully."}, "errors": [{"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ConflictException", "documentation": "The request could not be completed due to a conflict with the current state of the target resource."}], "documentation": "Displays a list of instances associated with the AWS account. This request returns a paginated result. You can use the filterArn property to display only the instances associated with the selected Gateway Amazon Resource Name (ARN)."}, "ListGateways": {"name": "ListGateways", "http": {"method": "GET", "requestUri": "/v1/gateways", "responseCode": 200}, "input": {"shape": "ListGatewaysRequest"}, "output": {"shape": "ListGatewaysResponse", "documentation": "AWS Elemental MediaConnect returned the list of gateways successfully."}, "errors": [{"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ConflictException", "documentation": "The request could not be completed due to a conflict with the current state of the target resource."}], "documentation": "Displays a list of gateways that are associated with this account. This request returns a paginated result."}, "ListOfferings": {"name": "ListOfferings", "http": {"method": "GET", "requestUri": "/v1/offerings", "responseCode": 200}, "input": {"shape": "ListOfferingsRequest"}, "output": {"shape": "ListOfferingsResponse", "documentation": "MediaConnect returned the list of offerings successfully."}, "errors": [{"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}], "documentation": "Displays a list of all offerings that are available to this account in the current AWS Region. If you have an active reservation (which means you've purchased an offering that has already started and hasn't expired yet), your account isn't eligible for other offerings."}, "ListReservations": {"name": "ListReservations", "http": {"method": "GET", "requestUri": "/v1/reservations", "responseCode": 200}, "input": {"shape": "ListReservationsRequest"}, "output": {"shape": "ListReservationsResponse", "documentation": "MediaConnect returned the list of reservations successfully."}, "errors": [{"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}], "documentation": "Displays a list of all reservations that have been purchased by this account in the current AWS Region. This list includes all reservations in all states (such as active and expired)."}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse", "documentation": "The tags for the resource"}, "errors": [{"shape": "NotFoundException", "documentation": "The requested resource was not found"}, {"shape": "BadRequestException", "documentation": "The client performed an invalid request"}, {"shape": "InternalServerErrorException", "documentation": "Internal service error"}], "documentation": "List all tags on an AWS Elemental MediaConnect resource"}, "PurchaseOffering": {"name": "PurchaseOffering", "http": {"method": "POST", "requestUri": "/v1/offerings/{offeringArn}", "responseCode": 201}, "input": {"shape": "PurchaseOfferingRequest"}, "output": {"shape": "PurchaseOfferingResponse", "documentation": "AWS Elemental MediaConnect purchased offering successfully."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}], "documentation": "Submits a request to purchase an offering. If you already have an active reservation, you can't purchase another offering."}, "RemoveBridgeOutput": {"name": "RemoveBridgeOutput", "http": {"method": "DELETE", "requestUri": "/v1/bridges/{bridgeArn}/outputs/{outputName}", "responseCode": 202}, "input": {"shape": "RemoveBridgeOutputRequest"}, "output": {"shape": "RemoveBridgeOutputResponse", "documentation": "The output was successfully removed from the bridge."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "ConflictException", "documentation": "The request could not be completed due to a conflict with the current state of the target resource."}], "documentation": "Removes an output from a bridge."}, "RemoveBridgeSource": {"name": "RemoveBridgeSource", "http": {"method": "DELETE", "requestUri": "/v1/bridges/{bridgeArn}/sources/{sourceName}", "responseCode": 202}, "input": {"shape": "RemoveBridgeSourceRequest"}, "output": {"shape": "RemoveBridgeSourceResponse", "documentation": "The bridge source was successfully removed from the flow."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "ConflictException", "documentation": "The request could not be completed due to a conflict with the current state of the target resource."}], "documentation": "Removes a source from a bridge."}, "RemoveFlowMediaStream": {"name": "RemoveFlowMediaStream", "http": {"method": "DELETE", "requestUri": "/v1/flows/{flowArn}/mediaStreams/{mediaStreamName}", "responseCode": 200}, "input": {"shape": "RemoveFlowMediaStreamRequest"}, "output": {"shape": "RemoveFlowMediaStreamResponse", "documentation": "The media stream was successfully removed from the flow."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}], "documentation": "Removes a media stream from a flow. This action is only available if the media stream is not associated with a source or output."}, "RemoveFlowOutput": {"name": "RemoveFlowOutput", "http": {"method": "DELETE", "requestUri": "/v1/flows/{flowArn}/outputs/{outputArn}", "responseCode": 202}, "input": {"shape": "RemoveFlowOutputRequest"}, "output": {"shape": "RemoveFlowOutputResponse", "documentation": "output successfully removed from flow configuration."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}], "documentation": "Removes an output from an existing flow. This request can be made only on an output that does not have an entitlement associated with it. If the output has an entitlement, you must revoke the entitlement instead. When an entitlement is revoked from a flow, the service automatically removes the associated output."}, "RemoveFlowSource": {"name": "RemoveFlowSource", "http": {"method": "DELETE", "requestUri": "/v1/flows/{flowArn}/source/{sourceArn}", "responseCode": 202}, "input": {"shape": "RemoveFlowSourceRequest"}, "output": {"shape": "RemoveFlowSourceResponse", "documentation": "source successfully removed from flow configuration."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}], "documentation": "Removes a source from an existing flow. This request can be made only if there is more than one source on the flow."}, "RemoveFlowVpcInterface": {"name": "RemoveFlowVpcInterface", "http": {"method": "DELETE", "requestUri": "/v1/flows/{flowArn}/vpcInterfaces/{vpcInterfaceName}", "responseCode": 200}, "input": {"shape": "RemoveFlowVpcInterfaceRequest"}, "output": {"shape": "RemoveFlowVpcInterfaceResponse", "documentation": "VPC interface successfully removed from flow configuration."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}], "documentation": "Removes a VPC Interface from an existing flow. This request can be made only on a VPC interface that does not have a Source or Output associated with it. If the VPC interface is referenced by a Source or Output, you must first delete or update the Source or Output to no longer reference the VPC interface."}, "RevokeFlowEntitlement": {"name": "RevokeFlowEntitlement", "http": {"method": "DELETE", "requestUri": "/v1/flows/{flowArn}/entitlements/{entitlementArn}", "responseCode": 202}, "input": {"shape": "RevokeFlowEntitlementRequest"}, "output": {"shape": "RevokeFlowEntitlementResponse", "documentation": "AWS Elemental MediaConnect revoked the entitlement successfully."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}], "documentation": "Revokes an entitlement from a flow. Once an entitlement is revoked, the content becomes unavailable to the subscriber and the associated output is removed."}, "StartFlow": {"name": "StartFlow", "http": {"method": "POST", "requestUri": "/v1/flows/start/{flowArn}", "responseCode": 202}, "input": {"shape": "StartFlowRequest"}, "output": {"shape": "StartFlowResponse", "documentation": "AWS Elemental MediaConnect is starting the flow."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}], "documentation": "Starts a flow."}, "StopFlow": {"name": "StopFlow", "http": {"method": "POST", "requestUri": "/v1/flows/stop/{flowArn}", "responseCode": 202}, "input": {"shape": "StopFlowRequest"}, "output": {"shape": "StopFlowResponse", "documentation": "AWS Elemental MediaConnect is stopping the flow."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}], "documentation": "Stops a flow."}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}", "responseCode": 204}, "input": {"shape": "TagResourceRequest"}, "errors": [{"shape": "NotFoundException", "documentation": "The requested resource was not found"}, {"shape": "BadRequestException", "documentation": "The client performed an invalid request"}, {"shape": "InternalServerErrorException", "documentation": "Internal service error"}], "documentation": "Associates the specified tags to a resource with the specified resourceArn. If existing tags on a resource are not specified in the request parameters, they are not changed. When a resource is deleted, the tags associated with that resource are deleted as well."}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 204}, "input": {"shape": "UntagResourceRequest"}, "errors": [{"shape": "NotFoundException", "documentation": "The requested resource was not found"}, {"shape": "BadRequestException", "documentation": "The client performed an invalid request"}, {"shape": "InternalServerErrorException", "documentation": "Internal service error"}], "documentation": "Deletes specified tags from a resource."}, "UpdateBridge": {"name": "UpdateBridge", "http": {"method": "PUT", "requestUri": "/v1/bridges/{bridgeArn}", "responseCode": 202}, "input": {"shape": "UpdateBridgeRequest"}, "output": {"shape": "UpdateBridgeResponse", "documentation": "AWS Elemental MediaConnect updated the bridge successfully."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "ConflictException", "documentation": "The request could not be completed due to a conflict with the current state of the target resource."}], "documentation": "Updates the bridge"}, "UpdateBridgeOutput": {"name": "UpdateBridgeOutput", "http": {"method": "PUT", "requestUri": "/v1/bridges/{bridgeArn}/outputs/{outputName}", "responseCode": 202}, "input": {"shape": "UpdateBridgeOutputRequest"}, "output": {"shape": "UpdateBridgeOutputResponse", "documentation": "MediaConnect is updating the bridge output."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "ConflictException", "documentation": "The request could not be completed due to a conflict with the current state of the target resource."}], "documentation": "Updates an existing bridge output."}, "UpdateBridgeSource": {"name": "UpdateBridgeSource", "http": {"method": "PUT", "requestUri": "/v1/bridges/{bridgeArn}/sources/{sourceName}", "responseCode": 202}, "input": {"shape": "UpdateBridgeSourceRequest"}, "output": {"shape": "UpdateBridgeSourceResponse", "documentation": "MediaConnect is updating the bridge source."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "ConflictException", "documentation": "The request could not be completed due to a conflict with the current state of the target resource."}], "documentation": "Updates an existing bridge source."}, "UpdateBridgeState": {"name": "UpdateBridgeState", "http": {"method": "PUT", "requestUri": "/v1/bridges/{bridgeArn}/state", "responseCode": 202}, "input": {"shape": "UpdateBridgeStateRequest"}, "output": {"shape": "UpdateBridgeStateResponse", "documentation": "AWS Elemental MediaConnect updated the bridge successfully."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "ConflictException", "documentation": "The request could not be completed due to a conflict with the current state of the target resource."}], "documentation": "Updates the bridge state"}, "UpdateFlow": {"name": "UpdateFlow", "http": {"method": "PUT", "requestUri": "/v1/flows/{flowArn}", "responseCode": 202}, "input": {"shape": "UpdateFlowRequest"}, "output": {"shape": "UpdateFlowResponse", "documentation": "AWS Elemental MediaConnect updated the flow successfully."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}], "documentation": "Updates flow"}, "UpdateFlowEntitlement": {"name": "UpdateFlowEntitlement", "http": {"method": "PUT", "requestUri": "/v1/flows/{flowArn}/entitlements/{entitlementArn}", "responseCode": 202}, "input": {"shape": "UpdateFlowEntitlementRequest"}, "output": {"shape": "UpdateFlowEntitlementResponse", "documentation": "AWS Elemental MediaConnect updated the entitlement successfully."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}], "documentation": "You can change an entitlement's description, subscribers, and encryption. If you change the subscribers, the service will remove the outputs that are are used by the subscribers that are removed."}, "UpdateFlowMediaStream": {"name": "UpdateFlowMediaStream", "http": {"method": "PUT", "requestUri": "/v1/flows/{flowArn}/mediaStreams/{mediaStreamName}", "responseCode": 202}, "input": {"shape": "UpdateFlowMediaStreamRequest"}, "output": {"shape": "UpdateFlowMediaStreamResponse", "documentation": "MediaConnect is updating the media stream."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}], "documentation": "Updates an existing media stream."}, "UpdateFlowOutput": {"name": "UpdateFlowOutput", "http": {"method": "PUT", "requestUri": "/v1/flows/{flowArn}/outputs/{outputArn}", "responseCode": 202}, "input": {"shape": "UpdateFlowOutputRequest"}, "output": {"shape": "UpdateFlowOutputResponse", "documentation": "AWS Elemental MediaConnect updated the output successfully."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}], "documentation": "Updates an existing flow output."}, "UpdateFlowSource": {"name": "UpdateFlowSource", "http": {"method": "PUT", "requestUri": "/v1/flows/{flowArn}/source/{sourceArn}", "responseCode": 202}, "input": {"shape": "UpdateFlowSourceRequest"}, "output": {"shape": "UpdateFlowSourceResponse", "documentation": "AWS Elemental MediaConnect updated the flow successfully."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}], "documentation": "Updates the source of a flow."}, "UpdateGatewayInstance": {"name": "UpdateGatewayInstance", "http": {"method": "PUT", "requestUri": "/v1/gateway-instances/{gatewayInstanceArn}", "responseCode": 200}, "input": {"shape": "UpdateGatewayInstanceRequest"}, "output": {"shape": "UpdateGatewayInstanceResponse", "documentation": "AWS Elemental MediaConnect is applying the instance state."}, "errors": [{"shape": "BadRequestException", "documentation": "The request that you submitted is not valid."}, {"shape": "InternalServerErrorException", "documentation": "AWS Elemental MediaConnect can't fulfill your request because it encountered an unexpected condition."}, {"shape": "ForbiddenException", "documentation": "You don't have the required permissions to perform this operation."}, {"shape": "NotFoundException", "documentation": "AWS Elemental MediaConnect did not find the resource that you specified in the request."}, {"shape": "ServiceUnavailableException", "documentation": "AWS Elemental MediaConnect is currently unavailable. Try again later."}, {"shape": "TooManyRequestsException", "documentation": "You have exceeded the service request rate limit for your AWS Elemental MediaConnect account."}, {"shape": "ConflictException", "documentation": "The request could not be completed due to a conflict with the current state of the target resource."}], "documentation": "Updates the configuration of an existing Gateway Instance."}}, "shapes": {"AddBridgeFlowSourceRequest": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:flow:.+$", "locationName": "flowArn", "documentation": "The Amazon Resource Number (ARN) of the cloud flow to use as a source of this bridge."}, "FlowVpcInterfaceAttachment": {"shape": "VpcInterfaceAttachment", "locationName": "flowVpcInterfaceAttachment", "documentation": "The name of the VPC interface attachment to use for this source."}, "Name": {"shape": "__string", "locationName": "name", "documentation": "The name of the flow source. This name is used to reference the source and must be unique among sources in this bridge."}}, "documentation": "Add a flow source to an existing bridge.", "required": ["FlowArn", "Name"]}, "AddBridgeNetworkOutputRequest": {"type": "structure", "members": {"IpAddress": {"shape": "__string", "locationName": "ip<PERSON><PERSON><PERSON>", "documentation": "The network output IP Address."}, "Name": {"shape": "__string", "locationName": "name", "documentation": "The network output name. This name is used to reference the output and must be unique among outputs in this bridge."}, "NetworkName": {"shape": "__string", "locationName": "networkName", "documentation": "The network output's gateway network name."}, "Port": {"shape": "__integer", "locationName": "port", "documentation": "The network output port."}, "Protocol": {"shape": "Protocol", "locationName": "protocol", "documentation": "The network output protocol."}, "Ttl": {"shape": "__integer", "locationName": "ttl", "documentation": "The network output TTL."}}, "documentation": "Add a network output to an existing bridge.", "required": ["NetworkName", "Port", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Protocol", "Ttl", "Name"]}, "AddBridgeNetworkSourceRequest": {"type": "structure", "members": {"MulticastIp": {"shape": "__string", "locationName": "multicastIp", "documentation": "The network source multicast IP."}, "Name": {"shape": "__string", "locationName": "name", "documentation": "The name of the network source. This name is used to reference the source and must be unique among sources in this bridge."}, "NetworkName": {"shape": "__string", "locationName": "networkName", "documentation": "The network source's gateway network name."}, "Port": {"shape": "__integer", "locationName": "port", "documentation": "The network source port."}, "Protocol": {"shape": "Protocol", "locationName": "protocol", "documentation": "The network source protocol."}}, "documentation": "Add a network source to an existing bridge.", "required": ["NetworkName", "MulticastIp", "Port", "Protocol", "Name"]}, "AddBridgeOutputRequest": {"type": "structure", "members": {"NetworkOutput": {"shape": "AddBridgeNetworkOutputRequest", "locationName": "networkOutput"}}, "documentation": "Add an output to a bridge."}, "AddBridgeOutputsRequest": {"type": "structure", "members": {"BridgeArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:bridge:.+$", "location": "uri", "locationName": "bridgeArn", "documentation": "The ARN of the bridge that you want to update."}, "Outputs": {"shape": "__listOfAddBridgeOutputRequest", "locationName": "outputs", "documentation": "The outputs that you want to add to this bridge."}}, "documentation": "A request to add outputs to the specified bridge.", "required": ["BridgeArn", "Outputs"]}, "AddBridgeOutputsResponse": {"type": "structure", "members": {"BridgeArn": {"shape": "__string", "locationName": "bridgeArn", "documentation": "The Amazon Resource Number (ARN) of the bridge."}, "Outputs": {"shape": "__listOfBridgeOutput", "locationName": "outputs", "documentation": "The outputs that you added to this bridge."}}}, "AddBridgeSourceRequest": {"type": "structure", "members": {"FlowSource": {"shape": "AddBridgeFlowSourceRequest", "locationName": "flowSource"}, "NetworkSource": {"shape": "AddBridgeNetworkSourceRequest", "locationName": "networkSource"}}, "documentation": "Add a source to an existing bridge."}, "AddBridgeSourcesRequest": {"type": "structure", "members": {"BridgeArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:bridge:.+$", "location": "uri", "locationName": "bridgeArn", "documentation": "The ARN of the bridge that you want to update."}, "Sources": {"shape": "__listOfAddBridgeSourceRequest", "locationName": "sources", "documentation": "The sources that you want to add to this bridge."}}, "documentation": "A request to add sources to the specified bridge.", "required": ["BridgeArn", "Sources"]}, "AddBridgeSourcesResponse": {"type": "structure", "members": {"BridgeArn": {"shape": "__string", "locationName": "bridgeArn", "documentation": "The Amazon Resource Number (ARN) of the bridge."}, "Sources": {"shape": "__listOfBridgeSource", "locationName": "sources", "documentation": "The sources that you added to this bridge."}}}, "AddEgressGatewayBridgeRequest": {"type": "structure", "members": {"MaxBitrate": {"shape": "__integer", "locationName": "maxBitrate", "documentation": "The maximum expected bitrate (in bps)."}}, "required": ["MaxBitrate"]}, "AddFlowMediaStreamsRequest": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:flow:.+$", "location": "uri", "locationName": "flowArn", "documentation": "The Amazon Resource Name (ARN) of the flow."}, "MediaStreams": {"shape": "__listOfAddMediaStreamRequest", "locationName": "mediaStreams", "documentation": "The media streams that you want to add to the flow."}}, "documentation": "A request to add media streams to the flow.", "required": ["FlowArn", "MediaStreams"]}, "AddFlowMediaStreamsResponse": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "locationName": "flowArn", "documentation": "The ARN of the flow that you added media streams to."}, "MediaStreams": {"shape": "__listOfMediaStream", "locationName": "mediaStreams", "documentation": "The media streams that you added to the flow."}}}, "AddFlowOutputs420Exception": {"type": "structure", "members": {"Message": {"shape": "__string", "locationName": "message", "documentation": "The error message returned by AWS Elemental MediaConnect."}}, "documentation": "Exception raised by AWS Elemental MediaConnect. See the error message and documentation for the operation for more information on the cause of this exception.", "required": ["Message"], "exception": true, "error": {"httpStatusCode": 420}}, "AddFlowOutputsRequest": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:flow:.+$", "location": "uri", "locationName": "flowArn", "documentation": "The flow that you want to add outputs to."}, "Outputs": {"shape": "__listOfAddOutputRequest", "locationName": "outputs", "documentation": "A list of outputs that you want to add."}}, "documentation": "A request to add outputs to the specified flow.", "required": ["FlowArn", "Outputs"]}, "AddFlowOutputsResponse": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "locationName": "flowArn", "documentation": "The ARN of the flow that these outputs were added to."}, "Outputs": {"shape": "__listOfOutput", "locationName": "outputs", "documentation": "The details of the newly added outputs."}}}, "AddFlowSourcesRequest": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:flow:.+$", "location": "uri", "locationName": "flowArn", "documentation": "The flow that you want to mutate."}, "Sources": {"shape": "__listOfSetSourceRequest", "locationName": "sources", "documentation": "A list of sources that you want to add."}}, "documentation": "A request to add sources to the flow.", "required": ["FlowArn", "Sources"]}, "AddFlowSourcesResponse": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "locationName": "flowArn", "documentation": "The ARN of the flow that these sources were added to."}, "Sources": {"shape": "__listOfSource", "locationName": "sources", "documentation": "The details of the newly added sources."}}}, "AddFlowVpcInterfacesRequest": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:flow:.+$", "location": "uri", "locationName": "flowArn", "documentation": "The flow that you want to mutate."}, "VpcInterfaces": {"shape": "__listOfVpcInterfaceRequest", "locationName": "vpcInterfaces", "documentation": "A list of VPC interfaces that you want to add."}}, "documentation": "A request to add VPC interfaces to the flow.", "required": ["FlowArn", "VpcInterfaces"]}, "AddFlowVpcInterfacesResponse": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "locationName": "flowArn", "documentation": "The ARN of the flow that these VPC interfaces were added to."}, "VpcInterfaces": {"shape": "__listOfVpcInterface", "locationName": "vpcInterfaces", "documentation": "The details of the newly added VPC interfaces."}}}, "AddIngressGatewayBridgeRequest": {"type": "structure", "members": {"MaxBitrate": {"shape": "__integer", "locationName": "maxBitrate", "documentation": "The maximum expected bitrate (in bps)."}, "MaxOutputs": {"shape": "__integer", "locationName": "maxOutputs", "documentation": "The maximum number of expected outputs."}}, "required": ["MaxOutputs", "MaxBitrate"]}, "AddMaintenance": {"type": "structure", "members": {"MaintenanceDay": {"shape": "MaintenanceDay", "locationName": "maintenanceDay", "documentation": "A day of a week when the maintenance will happen. Use Monday/Tuesday/Wednesday/Thursday/Friday/Saturday/Sunday."}, "MaintenanceStartHour": {"shape": "__string", "locationName": "maintenanceStartHour", "documentation": "UTC time when the maintenance will happen. Use 24-hour HH:MM format. Minutes must be 00. Example: 13:00. The default value is 02:00."}}, "documentation": "Create maintenance setting for a flow", "required": ["MaintenanceDay", "MaintenanceStartHour"]}, "AddMediaStreamRequest": {"type": "structure", "members": {"Attributes": {"shape": "MediaStreamAttributesRequest", "locationName": "attributes", "documentation": "The attributes that you want to assign to the new media stream."}, "ClockRate": {"shape": "__integer", "locationName": "clockRate", "documentation": "The sample rate (in Hz) for the stream. If the media stream type is video or ancillary data, set this value to 90000. If the media stream type is audio, set this value to either 48000 or 96000."}, "Description": {"shape": "__string", "locationName": "description", "documentation": "A description that can help you quickly identify what your media stream is used for."}, "MediaStreamId": {"shape": "__integer", "locationName": "mediaStreamId", "documentation": "A unique identifier for the media stream."}, "MediaStreamName": {"shape": "__string", "locationName": "mediaStreamName", "documentation": "A name that helps you distinguish one media stream from another."}, "MediaStreamType": {"shape": "MediaStreamType", "locationName": "mediaStreamType", "documentation": "The type of media stream."}, "VideoFormat": {"shape": "__string", "locationName": "videoFormat", "documentation": "The resolution of the video."}}, "documentation": "The media stream that you want to add to the flow.", "required": ["MediaStreamType", "MediaStreamId", "MediaStreamName"]}, "AddOutputRequest": {"type": "structure", "members": {"CidrAllowList": {"shape": "__listOf__string", "locationName": "cidrAllowList", "documentation": "The range of IP addresses that should be allowed to initiate output requests to this flow. These IP addresses should be in the form of a Classless Inter-Domain Routing (CIDR) block; for example, 10.0.0.0/16."}, "Description": {"shape": "__string", "locationName": "description", "documentation": "A description of the output. This description appears only on the AWS Elemental MediaConnect console and will not be seen by the end user."}, "Destination": {"shape": "__string", "locationName": "destination", "documentation": "The IP address from which video will be sent to output destinations."}, "Encryption": {"shape": "Encryption", "locationName": "encryption", "documentation": "The type of key used for the encryption. If no keyType is provided, the service will use the default setting (static-key). Allowable encryption types: static-key."}, "MaxLatency": {"shape": "__integer", "locationName": "maxLatency", "documentation": "The maximum latency in milliseconds. This parameter applies only to RIST-based, Zixi-based, and Fujitsu-based streams."}, "MediaStreamOutputConfigurations": {"shape": "__listOfMediaStreamOutputConfigurationRequest", "locationName": "mediaStreamOutputConfigurations", "documentation": "The media streams that are associated with the output, and the parameters for those associations."}, "MinLatency": {"shape": "__integer", "locationName": "minLatency", "documentation": "The minimum latency in milliseconds for SRT-based streams. In streams that use the SRT protocol, this value that you set on your MediaConnect source or output represents the minimal potential latency of that connection. The latency of the stream is set to the highest number between the sender’s minimum latency and the receiver’s minimum latency."}, "Name": {"shape": "__string", "locationName": "name", "documentation": "The name of the output. This value must be unique within the current flow."}, "Port": {"shape": "__integer", "locationName": "port", "documentation": "The port to use when content is distributed to this output."}, "Protocol": {"shape": "Protocol", "locationName": "protocol", "documentation": "The protocol to use for the output."}, "RemoteId": {"shape": "__string", "locationName": "remoteId", "documentation": "The remote ID for the Zixi-pull output stream."}, "SenderControlPort": {"shape": "__integer", "locationName": "senderControlPort", "documentation": "The port that the flow uses to send outbound requests to initiate connection with the sender."}, "SmoothingLatency": {"shape": "__integer", "locationName": "smoothingLatency", "documentation": "The smoothing latency in milliseconds for RIST, RTP, and RTP-FEC streams."}, "StreamId": {"shape": "__string", "locationName": "streamId", "documentation": "The stream ID that you want to use for this transport. This parameter applies only to Zixi and SRT caller-based streams."}, "VpcInterfaceAttachment": {"shape": "VpcInterfaceAttachment", "locationName": "vpcInterfaceAttachment", "documentation": "The name of the VPC interface attachment to use for this output."}}, "documentation": "The output that you want to add to this flow.", "required": ["Protocol"]}, "Algorithm": {"type": "string", "enum": ["aes128", "aes192", "aes256"]}, "BadRequestException": {"type": "structure", "members": {"Message": {"shape": "__string", "locationName": "message", "documentation": "The error message returned by AWS Elemental MediaConnect."}}, "documentation": "Exception raised by AWS Elemental MediaConnect. See the error message and documentation for the operation for more information on the cause of this exception.", "required": ["Message"], "exception": true, "error": {"httpStatusCode": 400}}, "Bridge": {"type": "structure", "members": {"BridgeArn": {"shape": "__string", "locationName": "bridgeArn", "documentation": "The Amazon Resource Number (ARN) of the bridge."}, "BridgeMessages": {"shape": "__listOfMessageDetail", "locationName": "bridgeMessages"}, "BridgeState": {"shape": "BridgeState", "locationName": "bridgeState"}, "EgressGatewayBridge": {"shape": "EgressGatewayBridge", "locationName": "egressGatewayBridge"}, "IngressGatewayBridge": {"shape": "IngressGatewayBridge", "locationName": "ingressGatewayBridge"}, "Name": {"shape": "__string", "locationName": "name", "documentation": "The name of the bridge."}, "Outputs": {"shape": "__listOfBridgeOutput", "locationName": "outputs", "documentation": "The outputs on this bridge."}, "PlacementArn": {"shape": "__string", "locationName": "placementArn", "documentation": "The placement Amazon Resource Number (ARN) of the bridge."}, "SourceFailoverConfig": {"shape": "FailoverConfig", "locationName": "sourceFailoverConfig"}, "Sources": {"shape": "__listOfBridgeSource", "locationName": "sources", "documentation": "The sources on this bridge."}}, "documentation": "A Bridge is the connection between your datacenter's Instances and the AWS cloud. A bridge can be used to send video from the AWS cloud to your datacenter or from your datacenter to the AWS cloud.", "required": ["BridgeArn", "BridgeState", "PlacementArn", "Name"]}, "BridgeFlowOutput": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "locationName": "flowArn", "documentation": "The Amazon Resource Number (ARN) of the cloud flow."}, "FlowSourceArn": {"shape": "__string", "locationName": "flowSourceArn", "documentation": "The Amazon Resource Number (ARN) of the flow source."}, "Name": {"shape": "__string", "locationName": "name", "documentation": "The name of the bridge's output."}}, "documentation": "The output of the bridge. A flow output is delivered to the AWS cloud.", "required": ["FlowSourceArn", "FlowArn", "Name"]}, "BridgeFlowSource": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "locationName": "flowArn", "documentation": "The ARN of the cloud flow used as a source of this bridge."}, "FlowVpcInterfaceAttachment": {"shape": "VpcInterfaceAttachment", "locationName": "flowVpcInterfaceAttachment", "documentation": "The name of the VPC interface attachment to use for this source."}, "Name": {"shape": "__string", "locationName": "name", "documentation": "The name of the flow source."}, "OutputArn": {"shape": "__string", "locationName": "outputArn", "documentation": "The Amazon Resource Number (ARN) of the output."}}, "documentation": "The source of the bridge. A flow source originates in MediaConnect as an existing cloud flow.", "required": ["FlowArn", "Name"]}, "BridgeNetworkOutput": {"type": "structure", "members": {"IpAddress": {"shape": "__string", "locationName": "ip<PERSON><PERSON><PERSON>", "documentation": "The network output IP Address."}, "Name": {"shape": "__string", "locationName": "name", "documentation": "The network output name."}, "NetworkName": {"shape": "__string", "locationName": "networkName", "documentation": "The network output's gateway network name."}, "Port": {"shape": "__integer", "locationName": "port", "documentation": "The network output port."}, "Protocol": {"shape": "Protocol", "locationName": "protocol", "documentation": "The network output protocol."}, "Ttl": {"shape": "__integer", "locationName": "ttl", "documentation": "The network output TTL."}}, "documentation": "The output of the bridge. A network output is delivered to your premises.", "required": ["NetworkName", "Port", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Protocol", "Ttl", "Name"]}, "BridgeNetworkSource": {"type": "structure", "members": {"MulticastIp": {"shape": "__string", "locationName": "multicastIp", "documentation": "The network source multicast IP."}, "Name": {"shape": "__string", "locationName": "name", "documentation": "The name of the network source."}, "NetworkName": {"shape": "__string", "locationName": "networkName", "documentation": "The network source's gateway network name."}, "Port": {"shape": "__integer", "locationName": "port", "documentation": "The network source port."}, "Protocol": {"shape": "Protocol", "locationName": "protocol", "documentation": "The network source protocol."}}, "documentation": "The source of the bridge. A network source originates at your premises.", "required": ["NetworkName", "MulticastIp", "Port", "Protocol", "Name"]}, "BridgeOutput": {"type": "structure", "members": {"FlowOutput": {"shape": "BridgeFlowOutput", "locationName": "flowOutput"}, "NetworkOutput": {"shape": "BridgeNetworkOutput", "locationName": "networkOutput"}}, "documentation": "The output of the bridge."}, "BridgePlacement": {"type": "string", "enum": ["AVAILABLE", "LOCKED"]}, "BridgeSource": {"type": "structure", "members": {"FlowSource": {"shape": "BridgeFlowSource", "locationName": "flowSource"}, "NetworkSource": {"shape": "BridgeNetworkSource", "locationName": "networkSource"}}, "documentation": "The bridge's source."}, "BridgeState": {"type": "string", "enum": ["CREATING", "STANDBY", "STARTING", "DEPLOYING", "ACTIVE", "STOPPING", "DELETING", "DELETED", "START_FAILED", "START_PENDING", "STOP_FAILED", "UPDATING"]}, "Colorimetry": {"type": "string", "enum": ["BT601", "BT709", "BT2020", "BT2100", "ST2065-1", "ST2065-3", "XYZ"]}, "ConflictException": {"type": "structure", "members": {"Message": {"shape": "__string", "locationName": "message", "documentation": "The error message returned by AWS Elemental MediaConnect."}}, "documentation": "Exception raised by AWS Elemental MediaConnect. See the error message and documentation for the operation for more information on the cause of this exception.", "required": ["Message"], "exception": true, "error": {"httpStatusCode": 409}}, "ConnectionStatus": {"type": "string", "enum": ["CONNECTED", "DISCONNECTED"]}, "CreateBridge420Exception": {"type": "structure", "members": {"Message": {"shape": "__string", "locationName": "message", "documentation": "The error message returned by AWS Elemental MediaConnect."}}, "documentation": "Exception raised by AWS Elemental MediaConnect. See the error message and documentation for the operation for more information on the cause of this exception.", "required": ["Message"], "exception": true, "error": {"httpStatusCode": 420}}, "CreateBridgeRequest": {"type": "structure", "members": {"EgressGatewayBridge": {"shape": "AddEgressGatewayBridgeRequest", "locationName": "egressGatewayBridge", "documentation": "Create a bridge with the egress bridge type. An egress bridge is a cloud-to-ground bridge. The content comes from an existing MediaConnect flow and is delivered to your premises."}, "IngressGatewayBridge": {"shape": "AddIngressGatewayBridgeRequest", "locationName": "ingressGatewayBridge", "documentation": "Create a bridge with the ingress bridge type. An ingress bridge is a ground-to-cloud bridge. The content originates at your premises and is delivered to the cloud."}, "Name": {"shape": "__string", "locationName": "name", "documentation": "The name of the bridge. This name can not be modified after the bridge is created."}, "Outputs": {"shape": "__listOfAddBridgeOutputRequest", "locationName": "outputs", "documentation": "The outputs that you want to add to this bridge."}, "PlacementArn": {"shape": "__string", "locationName": "placementArn", "documentation": "The bridge placement Amazon Resource Number (ARN)."}, "SourceFailoverConfig": {"shape": "FailoverConfig", "locationName": "sourceFailoverConfig", "documentation": "The settings for source failover."}, "Sources": {"shape": "__listOfAddBridgeSourceRequest", "locationName": "sources", "documentation": "The sources that you want to add to this bridge."}}, "documentation": "Creates a new bridge. The request must include one source.", "required": ["Sources", "PlacementArn", "Name"]}, "CreateBridgeResponse": {"type": "structure", "members": {"Bridge": {"shape": "Bridge", "locationName": "bridge"}}}, "CreateFlow420Exception": {"type": "structure", "members": {"Message": {"shape": "__string", "locationName": "message", "documentation": "The error message returned by AWS Elemental MediaConnect."}}, "documentation": "Exception raised by AWS Elemental MediaConnect. See the error message and documentation for the operation for more information on the cause of this exception.", "required": ["Message"], "exception": true, "error": {"httpStatusCode": 420}}, "CreateFlowRequest": {"type": "structure", "members": {"AvailabilityZone": {"shape": "__string", "locationName": "availabilityZone", "documentation": "The Availability Zone that you want to create the flow in. These options are limited to the Availability Zones within the current AWS Region."}, "Entitlements": {"shape": "__listOfGrantEntitlementRequest", "locationName": "entitlements", "documentation": "The entitlements that you want to grant on a flow."}, "MediaStreams": {"shape": "__listOfAddMediaStreamRequest", "locationName": "mediaStreams", "documentation": "The media streams that you want to add to the flow. You can associate these media streams with sources and outputs on the flow."}, "Name": {"shape": "__string", "locationName": "name", "documentation": "The name of the flow."}, "Outputs": {"shape": "__listOfAddOutputRequest", "locationName": "outputs", "documentation": "The outputs that you want to add to this flow."}, "Source": {"shape": "SetSourceRequest", "locationName": "source"}, "SourceFailoverConfig": {"shape": "FailoverConfig", "locationName": "sourceFailoverConfig"}, "Sources": {"shape": "__listOfSetSourceRequest", "locationName": "sources"}, "VpcInterfaces": {"shape": "__listOfVpcInterfaceRequest", "locationName": "vpcInterfaces", "documentation": "The VPC interfaces you want on the flow."}, "Maintenance": {"shape": "AddMaintenance", "locationName": "maintenance"}}, "documentation": "Creates a new flow. The request must include one source. The request optionally can include outputs (up to 50) and entitlements (up to 50).", "required": ["Name"]}, "CreateFlowResponse": {"type": "structure", "members": {"Flow": {"shape": "Flow", "locationName": "flow"}}}, "CreateGateway420Exception": {"type": "structure", "members": {"Message": {"shape": "__string", "locationName": "message", "documentation": "The error message returned by AWS Elemental MediaConnect."}}, "documentation": "Exception raised by AWS Elemental MediaConnect. See the error message and documentation for the operation for more information on the cause of this exception.", "required": ["Message"], "exception": true, "error": {"httpStatusCode": 420}}, "CreateGatewayRequest": {"type": "structure", "members": {"EgressCidrBlocks": {"shape": "__listOf__string", "locationName": "egressCidrBlocks", "documentation": "The range of IP addresses that are allowed to contribute content or initiate output requests for flows communicating with this gateway. These IP addresses should be in the form of a Classless Inter-Domain Routing (CIDR) block; for example, 10.0.0.0/16."}, "Name": {"shape": "__string", "locationName": "name", "documentation": "The name of the gateway. This name can not be modified after the gateway is created."}, "Networks": {"shape": "__listOfGatewayNetwork", "locationName": "networks", "documentation": "The list of networks that you want to add."}}, "documentation": "Creates a new gateway. The request must include at least one network (up to 4).", "required": ["Networks", "EgressCidrBlocks", "Name"]}, "CreateGatewayResponse": {"type": "structure", "members": {"Gateway": {"shape": "Gateway", "locationName": "gateway"}}}, "DeleteBridgeRequest": {"type": "structure", "members": {"BridgeArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:bridge:.+$", "location": "uri", "locationName": "bridgeArn", "documentation": "The ARN of the bridge that you want to delete."}}, "required": ["BridgeArn"]}, "DeleteBridgeResponse": {"type": "structure", "members": {"BridgeArn": {"shape": "__string", "locationName": "bridgeArn", "documentation": "The Amazon Resource Number (ARN) of the deleted bridge."}}}, "DeleteFlowRequest": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:flow:.+$", "location": "uri", "locationName": "flowArn", "documentation": "The ARN of the flow that you want to delete."}}, "required": ["FlowArn"]}, "DeleteFlowResponse": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "locationName": "flowArn", "documentation": "The ARN of the flow that was deleted."}, "Status": {"shape": "Status", "locationName": "status", "documentation": "The status of the flow when the DeleteFlow process begins."}}}, "DeleteGatewayRequest": {"type": "structure", "members": {"GatewayArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:gateway:.+$", "location": "uri", "locationName": "gatewayArn", "documentation": "The ARN of the gateway that you want to delete."}}, "required": ["GatewayArn"]}, "DeleteGatewayResponse": {"type": "structure", "members": {"GatewayArn": {"shape": "__string", "locationName": "gatewayArn", "documentation": "The Amazon Resource Name (ARN) of the gateway that was deleted."}}}, "DeregisterGatewayInstanceRequest": {"type": "structure", "members": {"Force": {"shape": "__boolean", "location": "querystring", "locationName": "force", "documentation": "Force the deregistration of an instance. Force will deregister an instance, even if there are bridges running on it."}, "GatewayInstanceArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:gateway:.+:instance:.+$", "location": "uri", "locationName": "gatewayInstanceArn", "documentation": "The Amazon Resource Name (ARN) of the gateway that contains the instance that you want to deregister."}}, "required": ["GatewayInstanceArn"]}, "DeregisterGatewayInstanceResponse": {"type": "structure", "members": {"GatewayInstanceArn": {"shape": "__string", "locationName": "gatewayInstanceArn", "documentation": "The Amazon Resource Name (ARN) of the instance."}, "InstanceState": {"shape": "InstanceState", "locationName": "instanceState", "documentation": "The status of the instance."}}}, "DescribeBridgeRequest": {"type": "structure", "members": {"BridgeArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:bridge:.+$", "location": "uri", "locationName": "bridgeArn", "documentation": "The ARN of the bridge that you want to describe."}}, "required": ["BridgeArn"]}, "DescribeBridgeResponse": {"type": "structure", "members": {"Bridge": {"shape": "Bridge", "locationName": "bridge"}}}, "DescribeFlowRequest": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:flow:.+$", "location": "uri", "locationName": "flowArn", "documentation": "The ARN of the flow that you want to describe."}}, "required": ["FlowArn"]}, "DescribeFlowResponse": {"type": "structure", "members": {"Flow": {"shape": "Flow", "locationName": "flow"}, "Messages": {"shape": "Messages", "locationName": "messages"}}}, "DescribeGatewayInstanceRequest": {"type": "structure", "members": {"GatewayInstanceArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:gateway:.+:instance:.+$", "location": "uri", "locationName": "gatewayInstanceArn", "documentation": "The Amazon Resource Name (ARN) of the gateway instance that you want to describe."}}, "required": ["GatewayInstanceArn"]}, "DescribeGatewayInstanceResponse": {"type": "structure", "members": {"GatewayInstance": {"shape": "GatewayInstance", "locationName": "gatewayInstance"}}}, "DescribeGatewayRequest": {"type": "structure", "members": {"GatewayArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:gateway:.+$", "location": "uri", "locationName": "gatewayArn", "documentation": "The Amazon Resource Name (ARN) of the gateway that you want to describe."}}, "required": ["GatewayArn"]}, "DescribeGatewayResponse": {"type": "structure", "members": {"Gateway": {"shape": "Gateway", "locationName": "gateway"}}}, "DescribeOfferingRequest": {"type": "structure", "members": {"OfferingArn": {"shape": "__string", "location": "uri", "locationName": "offeringArn", "documentation": "The Amazon Resource Name (ARN) of the offering."}}, "required": ["OfferingArn"]}, "DescribeOfferingResponse": {"type": "structure", "members": {"Offering": {"shape": "Offering", "locationName": "offering"}}}, "DescribeReservationRequest": {"type": "structure", "members": {"ReservationArn": {"shape": "__string", "location": "uri", "locationName": "reservationArn", "documentation": "The Amazon Resource Name (ARN) of the reservation."}}, "required": ["ReservationArn"]}, "DescribeReservationResponse": {"type": "structure", "members": {"Reservation": {"shape": "Reservation", "locationName": "reservation"}}}, "DesiredState": {"type": "string", "enum": ["ACTIVE", "STANDBY", "DELETED"]}, "DestinationConfiguration": {"type": "structure", "members": {"DestinationIp": {"shape": "__string", "locationName": "destinationIp", "documentation": "The IP address where contents of the media stream will be sent."}, "DestinationPort": {"shape": "__integer", "locationName": "destinationPort", "documentation": "The port to use when the content of the media stream is distributed to the output."}, "Interface": {"shape": "Interface", "locationName": "interface", "documentation": "The VPC interface that is used for the media stream associated with the output."}, "OutboundIp": {"shape": "__string", "locationName": "outboundIp", "documentation": "The IP address that the receiver requires in order to establish a connection with the flow. This value is represented by the elastic network interface IP address of the VPC. This field applies only to outputs that use the CDI or ST 2110 JPEG XS protocol."}}, "documentation": "The transport parameters that are associated with an outbound media stream.", "required": ["DestinationIp", "DestinationPort", "Interface", "OutboundIp"]}, "DestinationConfigurationRequest": {"type": "structure", "members": {"DestinationIp": {"shape": "__string", "locationName": "destinationIp", "documentation": "The IP address where you want MediaConnect to send contents of the media stream."}, "DestinationPort": {"shape": "__integer", "locationName": "destinationPort", "documentation": "The port that you want MediaConnect to use when it distributes the media stream to the output."}, "Interface": {"shape": "InterfaceRequest", "locationName": "interface", "documentation": "The VPC interface that you want to use for the media stream associated with the output."}}, "documentation": "The transport parameters that you want to associate with an outbound media stream.", "required": ["DestinationIp", "DestinationPort", "Interface"]}, "DurationUnits": {"type": "string", "enum": ["MONTHS"]}, "EgressGatewayBridge": {"type": "structure", "members": {"InstanceId": {"shape": "__string", "locationName": "instanceId", "documentation": "The ID of the instance running this bridge."}, "MaxBitrate": {"shape": "__integer", "locationName": "maxBitrate", "documentation": "The maximum expected bitrate (in bps) of the egress bridge."}}, "required": ["MaxBitrate"]}, "EncoderProfile": {"type": "string", "enum": ["main", "high"]}, "EncodingName": {"type": "string", "enum": ["jxsv", "raw", "smpte291", "pcm"]}, "EncodingParameters": {"type": "structure", "members": {"CompressionFactor": {"shape": "__double", "locationName": "compressionFactor", "documentation": "A value that is used to calculate compression for an output. The bitrate of the output is calculated as follows: Output bitrate = (1 / compressionFactor) * (source bitrate) This property only applies to outputs that use the ST 2110 JPEG XS protocol, with a flow source that uses the CDI protocol. Valid values are floating point numbers in the range of 3.0 to 10.0, inclusive."}, "EncoderProfile": {"shape": "EncoderProfile", "locationName": "encoderProfile", "documentation": "A setting on the encoder that drives compression settings. This property only applies to video media streams associated with outputs that use the ST 2110 JPEG XS protocol, with a flow source that uses the CDI protocol."}}, "documentation": "A collection of parameters that determine how MediaConnect will convert the content. These fields only apply to outputs on flows that have a CDI source.", "required": ["EncoderProfile", "CompressionFactor"]}, "EncodingParametersRequest": {"type": "structure", "members": {"CompressionFactor": {"shape": "__double", "locationName": "compressionFactor", "documentation": "A value that is used to calculate compression for an output. The bitrate of the output is calculated as follows: Output bitrate = (1 / compressionFactor) * (source bitrate) This property only applies to outputs that use the ST 2110 JPEG XS protocol, with a flow source that uses the CDI protocol. Valid values are floating point numbers in the range of 3.0 to 10.0, inclusive."}, "EncoderProfile": {"shape": "EncoderProfile", "locationName": "encoderProfile", "documentation": "A setting on the encoder that drives compression settings. This property only applies to video media streams associated with outputs that use the ST 2110 JPEG XS protocol, if at least one source on the flow uses the CDI protocol."}}, "documentation": "A collection of parameters that determine how MediaConnect will convert the content. These fields only apply to outputs on flows that have a CDI source.", "required": ["EncoderProfile", "CompressionFactor"]}, "Encryption": {"type": "structure", "members": {"Algorithm": {"shape": "Algorithm", "locationName": "algorithm", "documentation": "The type of algorithm that is used for the encryption (such as aes128, aes192, or aes256)."}, "ConstantInitializationVector": {"shape": "__string", "locationName": "constantInitializationVector", "documentation": "A 128-bit, 16-byte hex value represented by a 32-character string, to be used with the key for encrypting content. This parameter is not valid for static key encryption."}, "DeviceId": {"shape": "__string", "locationName": "deviceId", "documentation": "The value of one of the devices that you configured with your digital rights management (DRM) platform key provider. This parameter is required for SPEKE encryption and is not valid for static key encryption."}, "KeyType": {"shape": "KeyType", "locationName": "keyType", "documentation": "The type of key that is used for the encryption. If no keyType is provided, the service will use the default setting (static-key)."}, "Region": {"shape": "__string", "locationName": "region", "documentation": "The AWS Region that the API Gateway proxy endpoint was created in. This parameter is required for SPEKE encryption and is not valid for static key encryption."}, "ResourceId": {"shape": "__string", "locationName": "resourceId", "documentation": "An identifier for the content. The service sends this value to the key server to identify the current endpoint. The resource ID is also known as the content ID. This parameter is required for SPEKE encryption and is not valid for static key encryption."}, "RoleArn": {"shape": "__string", "locationName": "roleArn", "documentation": "The ARN of the role that you created during setup (when you set up AWS Elemental MediaConnect as a trusted entity)."}, "SecretArn": {"shape": "__string", "locationName": "secretArn", "documentation": "The ARN of the secret that you created in AWS Secrets Manager to store the encryption key. This parameter is required for static key encryption and is not valid for SPEKE encryption."}, "Url": {"shape": "__string", "locationName": "url", "documentation": "The URL from the API Gateway proxy that you set up to talk to your key server. This parameter is required for SPEKE encryption and is not valid for static key encryption."}}, "documentation": "Information about the encryption of the flow.", "required": ["RoleArn"]}, "Entitlement": {"type": "structure", "members": {"DataTransferSubscriberFeePercent": {"shape": "__integer", "locationName": "dataTransferSubscriberFeePercent", "documentation": "Percentage from 0-100 of the data transfer cost to be billed to the subscriber."}, "Description": {"shape": "__string", "locationName": "description", "documentation": "A description of the entitlement."}, "Encryption": {"shape": "Encryption", "locationName": "encryption", "documentation": "The type of encryption that will be used on the output that is associated with this entitlement."}, "EntitlementArn": {"shape": "__string", "locationName": "entitlementArn", "documentation": "The ARN of the entitlement."}, "EntitlementStatus": {"shape": "EntitlementStatus", "locationName": "entitlementStatus", "documentation": "An indication of whether the entitlement is enabled."}, "Name": {"shape": "__string", "locationName": "name", "documentation": "The name of the entitlement."}, "Subscribers": {"shape": "__listOf__string", "locationName": "subscribers", "documentation": "The AWS account IDs that you want to share your content with. The receiving accounts (subscribers) will be allowed to create their own flow using your content as the source."}}, "documentation": "The settings for a flow entitlement.", "required": ["EntitlementArn", "Subscribers", "Name"]}, "EntitlementStatus": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "FailoverConfig": {"type": "structure", "members": {"FailoverMode": {"shape": "FailoverMode", "locationName": "failoverMode", "documentation": "The type of failover you choose for this flow. MERGE combines the source streams into a single stream, allowing graceful recovery from any single-source loss. FAILOVER allows switching between different streams."}, "RecoveryWindow": {"shape": "__integer", "locationName": "recoveryWindow", "documentation": "Search window time to look for dash-7 packets"}, "SourcePriority": {"shape": "SourcePriority", "locationName": "sourcePriority", "documentation": "The priority you want to assign to a source. You can have a primary stream and a backup stream or two equally prioritized streams."}, "State": {"shape": "State", "locationName": "state"}}, "documentation": "The settings for source failover."}, "FailoverMode": {"type": "string", "enum": ["MERGE", "FAILOVER"]}, "Flow": {"type": "structure", "members": {"AvailabilityZone": {"shape": "__string", "locationName": "availabilityZone", "documentation": "The Availability Zone that you want to create the flow in. These options are limited to the Availability Zones within the current AWS."}, "Description": {"shape": "__string", "locationName": "description", "documentation": "A description of the flow. This value is not used or seen outside of the current AWS Elemental MediaConnect account."}, "EgressIp": {"shape": "__string", "locationName": "egressIp", "documentation": "The IP address from which video will be sent to output destinations."}, "Entitlements": {"shape": "__listOfEntitlement", "locationName": "entitlements", "documentation": "The entitlements in this flow."}, "FlowArn": {"shape": "__string", "locationName": "flowArn", "documentation": "The Amazon Resource Name (ARN) of the flow."}, "MediaStreams": {"shape": "__listOfMediaStream", "locationName": "mediaStreams", "documentation": "The media streams that are associated with the flow. After you associate a media stream with a source, you can also associate it with outputs on the flow."}, "Name": {"shape": "__string", "locationName": "name", "documentation": "The name of the flow."}, "Outputs": {"shape": "__listOfOutput", "locationName": "outputs", "documentation": "The outputs in this flow."}, "Source": {"shape": "Source", "locationName": "source"}, "SourceFailoverConfig": {"shape": "FailoverConfig", "locationName": "sourceFailoverConfig"}, "Sources": {"shape": "__listOfSource", "locationName": "sources"}, "Status": {"shape": "Status", "locationName": "status", "documentation": "The current status of the flow."}, "VpcInterfaces": {"shape": "__listOfVpcInterface", "locationName": "vpcInterfaces", "documentation": "The VPC Interfaces for this flow."}, "Maintenance": {"shape": "Maintenance", "locationName": "maintenance"}}, "documentation": "The settings for a flow, including its source, outputs, and entitlements.", "required": ["Status", "AvailabilityZone", "Source", "Name", "Entitlements", "Outputs", "FlowArn"]}, "Fmtp": {"type": "structure", "members": {"ChannelOrder": {"shape": "__string", "locationName": "channelOrder", "documentation": "The format of the audio channel."}, "Colorimetry": {"shape": "Colorimetry", "locationName": "colorimetry", "documentation": "The format that is used for the representation of color."}, "ExactFramerate": {"shape": "__string", "locationName": "exactFramerate", "documentation": "The frame rate for the video stream, in frames/second. For example: 60000/1001. If you specify a whole number, MediaConnect uses a ratio of N/1. For example, if you specify 60, MediaConnect uses 60/1 as the exactFramerate."}, "Par": {"shape": "__string", "locationName": "par", "documentation": "The pixel aspect ratio (PAR) of the video."}, "Range": {"shape": "Range", "locationName": "range", "documentation": "The encoding range of the video."}, "ScanMode": {"shape": "ScanMode", "locationName": "scanMode", "documentation": "The type of compression that was used to smooth the video’s appearance"}, "Tcs": {"shape": "Tcs", "locationName": "tcs", "documentation": "The transfer characteristic system (TCS) that is used in the video."}}, "documentation": "FMTP"}, "FmtpRequest": {"type": "structure", "members": {"ChannelOrder": {"shape": "__string", "locationName": "channelOrder", "documentation": "The format of the audio channel."}, "Colorimetry": {"shape": "Colorimetry", "locationName": "colorimetry", "documentation": "The format that is used for the representation of color."}, "ExactFramerate": {"shape": "__string", "locationName": "exactFramerate", "documentation": "The frame rate for the video stream, in frames/second. For example: 60000/1001. If you specify a whole number, MediaConnect uses a ratio of N/1. For example, if you specify 60, MediaConnect uses 60/1 as the exactFramerate."}, "Par": {"shape": "__string", "locationName": "par", "documentation": "The pixel aspect ratio (PAR) of the video."}, "Range": {"shape": "Range", "locationName": "range", "documentation": "The encoding range of the video."}, "ScanMode": {"shape": "ScanMode", "locationName": "scanMode", "documentation": "The type of compression that was used to smooth the video’s appearance."}, "Tcs": {"shape": "Tcs", "locationName": "tcs", "documentation": "The transfer characteristic system (TCS) that is used in the video."}}, "documentation": "The settings that you want to use to define the media stream."}, "ForbiddenException": {"type": "structure", "members": {"Message": {"shape": "__string", "locationName": "message", "documentation": "The error message returned by AWS Elemental MediaConnect."}}, "documentation": "Exception raised by AWS Elemental MediaConnect. See the error message and documentation for the operation for more information on the cause of this exception.", "required": ["Message"], "exception": true, "error": {"httpStatusCode": 403}}, "Gateway": {"type": "structure", "members": {"EgressCidrBlocks": {"shape": "__listOf__string", "locationName": "egressCidrBlocks", "documentation": "The range of IP addresses that contribute content or initiate output requests for flows communicating with this gateway. These IP addresses should be in the form of a Classless Inter-Domain Routing (CIDR) block; for example, 10.0.0.0/16."}, "GatewayArn": {"shape": "__string", "locationName": "gatewayArn", "documentation": "The Amazon Resource Name (ARN) of the gateway."}, "GatewayMessages": {"shape": "__listOfMessageDetail", "locationName": "gatewayMessages"}, "GatewayState": {"shape": "GatewayState", "locationName": "gatewayState", "documentation": "The current status of the gateway."}, "Name": {"shape": "__string", "locationName": "name", "documentation": "The name of the gateway. This name can not be modified after the gateway is created."}, "Networks": {"shape": "__listOfGatewayNetwork", "locationName": "networks", "documentation": "The list of networks in the gateway."}}, "documentation": "The settings for a gateway, including its networks.", "required": ["GatewayArn", "Networks", "EgressCidrBlocks", "Name"]}, "GatewayBridgeSource": {"type": "structure", "members": {"BridgeArn": {"shape": "__string", "locationName": "bridgeArn", "documentation": "The ARN of the bridge feeding this flow."}, "VpcInterfaceAttachment": {"shape": "VpcInterfaceAttachment", "locationName": "vpcInterfaceAttachment", "documentation": "The name of the VPC interface attachment to use for this bridge source."}}, "documentation": "The source configuration for cloud flows receiving a stream from a bridge.", "required": ["BridgeArn"]}, "GatewayInstance": {"type": "structure", "members": {"BridgePlacement": {"shape": "BridgePlacement", "locationName": "bridgePlacement", "documentation": "The availability of the instance to host new bridges. The bridgePlacement property can be LOCKED or AVAILABLE. If it is LOCKED, no new bridges can be deployed to this instance. If it is AVAILABLE, new bridges can be added to this instance."}, "ConnectionStatus": {"shape": "ConnectionStatus", "locationName": "connectionStatus", "documentation": "The connection state of the instance."}, "GatewayArn": {"shape": "__string", "locationName": "gatewayArn", "documentation": "The Amazon Resource Name (ARN) of the instance."}, "GatewayInstanceArn": {"shape": "__string", "locationName": "gatewayInstanceArn", "documentation": "The Amazon Resource Name (ARN) of the gateway."}, "InstanceId": {"shape": "__string", "locationName": "instanceId", "documentation": "The managed instance ID generated by the SSM install. This will begin with \"mi-\"."}, "InstanceMessages": {"shape": "__listOfMessageDetail", "locationName": "instanceMessages"}, "InstanceState": {"shape": "InstanceState", "locationName": "instanceState", "documentation": "The status of the instance."}, "RunningBridgeCount": {"shape": "__integer", "locationName": "runningBridgeCount", "documentation": "The running bridge count."}}, "documentation": "The settings for an instance in a gateway.", "required": ["GatewayArn", "InstanceState", "GatewayInstanceArn", "InstanceId", "RunningBridgeCount", "BridgePlacement", "ConnectionStatus"]}, "GatewayNetwork": {"type": "structure", "members": {"CidrBlock": {"shape": "__string", "locationName": "cidrBlock", "documentation": "A unique IP address range to use for this network. These IP addresses should be in the form of a Classless Inter-Domain Routing (CIDR) block; for example, 10.0.0.0/16."}, "Name": {"shape": "__string", "locationName": "name", "documentation": "The name of the network. This name is used to reference the network and must be unique among networks in this gateway."}}, "documentation": "The network settings for a gateway.", "required": ["CidrBlock", "Name"]}, "GatewayState": {"type": "string", "enum": ["CREATING", "ACTIVE", "UPDATING", "ERROR", "DELETING", "DELETED"]}, "GrantEntitlementRequest": {"type": "structure", "members": {"DataTransferSubscriberFeePercent": {"shape": "__integer", "locationName": "dataTransferSubscriberFeePercent", "documentation": "Percentage from 0-100 of the data transfer cost to be billed to the subscriber."}, "Description": {"shape": "__string", "locationName": "description", "documentation": "A description of the entitlement. This description appears only on the AWS Elemental MediaConnect console and will not be seen by the subscriber or end user."}, "Encryption": {"shape": "Encryption", "locationName": "encryption", "documentation": "The type of encryption that will be used on the output that is associated with this entitlement. Allowable encryption types: static-key, speke."}, "EntitlementStatus": {"shape": "EntitlementStatus", "locationName": "entitlementStatus", "documentation": "An indication of whether the new entitlement should be enabled or disabled as soon as it is created. If you don’t specify the entitlementStatus field in your request, MediaConnect sets it to ENABLED."}, "Name": {"shape": "__string", "locationName": "name", "documentation": "The name of the entitlement. This value must be unique within the current flow."}, "Subscribers": {"shape": "__listOf__string", "locationName": "subscribers", "documentation": "The AWS account IDs that you want to share your content with. The receiving accounts (subscribers) will be allowed to create their own flows using your content as the source."}}, "documentation": "The entitlements that you want to grant on a flow.", "required": ["Subscribers"]}, "GrantFlowEntitlements420Exception": {"type": "structure", "members": {"Message": {"shape": "__string", "locationName": "message", "documentation": "The error message returned by AWS Elemental MediaConnect."}}, "documentation": "Exception raised by AWS Elemental MediaConnect. See the error message and documentation for the operation for more information on the cause of this exception.", "required": ["Message"], "exception": true, "error": {"httpStatusCode": 420}}, "GrantFlowEntitlementsRequest": {"type": "structure", "members": {"Entitlements": {"shape": "__listOfGrantEntitlementRequest", "locationName": "entitlements", "documentation": "The list of entitlements that you want to grant."}, "FlowArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:flow:.+$", "location": "uri", "locationName": "flowArn", "documentation": "The flow that you want to grant entitlements on."}}, "documentation": "A request to grant entitlements on a flow.", "required": ["FlowArn", "Entitlements"]}, "GrantFlowEntitlementsResponse": {"type": "structure", "members": {"Entitlements": {"shape": "__listOfEntitlement", "locationName": "entitlements", "documentation": "The entitlements that were just granted."}, "FlowArn": {"shape": "__string", "locationName": "flowArn", "documentation": "The ARN of the flow that these entitlements were granted to."}}}, "IngressGatewayBridge": {"type": "structure", "members": {"InstanceId": {"shape": "__string", "locationName": "instanceId", "documentation": "The ID of the instance running this bridge."}, "MaxBitrate": {"shape": "__integer", "locationName": "maxBitrate", "documentation": "The maximum expected bitrate (in bps) of the ingress bridge."}, "MaxOutputs": {"shape": "__integer", "locationName": "maxOutputs", "documentation": "The maximum number of outputs on the ingress bridge."}}, "required": ["MaxOutputs", "MaxBitrate"]}, "InputConfiguration": {"type": "structure", "members": {"InputIp": {"shape": "__string", "locationName": "inputIp", "documentation": "The IP address that the flow listens on for incoming content for a media stream."}, "InputPort": {"shape": "__integer", "locationName": "inputPort", "documentation": "The port that the flow listens on for an incoming media stream."}, "Interface": {"shape": "Interface", "locationName": "interface", "documentation": "The VPC interface where the media stream comes in from."}}, "documentation": "The transport parameters that are associated with an incoming media stream.", "required": ["InputPort", "InputIp", "Interface"]}, "InputConfigurationRequest": {"type": "structure", "members": {"InputPort": {"shape": "__integer", "locationName": "inputPort", "documentation": "The port that you want the flow to listen on for an incoming media stream."}, "Interface": {"shape": "InterfaceRequest", "locationName": "interface", "documentation": "The VPC interface that you want to use for the incoming media stream."}}, "documentation": "The transport parameters that you want to associate with an incoming media stream.", "required": ["InputPort", "Interface"]}, "InstanceState": {"type": "string", "enum": ["REGISTERING", "ACTIVE", "DEREGISTERING", "DEREGISTERED", "REGISTRATION_ERROR", "DEREGISTRATION_ERROR"]}, "Interface": {"type": "structure", "members": {"Name": {"shape": "__string", "locationName": "name", "documentation": "The name of the VPC interface."}}, "documentation": "The VPC interface that is used for the media stream associated with the source or output.", "required": ["Name"]}, "InterfaceRequest": {"type": "structure", "members": {"Name": {"shape": "__string", "locationName": "name", "documentation": "The name of the VPC interface."}}, "documentation": "The VPC interface that you want to designate where the media stream is coming from or going to.", "required": ["Name"]}, "InternalServerErrorException": {"type": "structure", "members": {"Message": {"shape": "__string", "locationName": "message", "documentation": "The error message returned by AWS Elemental MediaConnect."}}, "documentation": "Exception raised by AWS Elemental MediaConnect. See the error message and documentation for the operation for more information on the cause of this exception.", "required": ["Message"], "exception": true, "error": {"httpStatusCode": 500}}, "KeyType": {"type": "string", "enum": ["speke", "static-key", "srt-password"]}, "ListBridgesRequest": {"type": "structure", "members": {"FilterArn": {"shape": "__string", "location": "querystring", "locationName": "filterArn", "documentation": "Filter the list results to display only the bridges associated with the selected Amazon Resource Name (ARN)."}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "The maximum number of results to return per API request. For example, you submit a ListBridges request with MaxResults set at 5. Although 20 items match your request, the service returns no more than the first 5 items. (The service also returns a NextToken value that you can use to fetch the next batch of results.) The service might return fewer results than the MaxResults value. If MaxResults is not included in the request, the service defaults to pagination with a maximum of 10 results per page."}, "NextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "The token that identifies which batch of results that you want to see. For example, you submit a ListBridges request with MaxResults set at 5. The service returns the first batch of results (up to 5) and a NextToken value. To see the next batch of results, you can submit the ListBridges request a second time and specify the NextToken value."}}}, "ListBridgesResponse": {"type": "structure", "members": {"Bridges": {"shape": "__listOfListedBridge", "locationName": "bridges", "documentation": "A list of bridge summaries."}, "NextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "The token that identifies which batch of results that you want to see. For example, you submit a ListBridges request with MaxResults set at 5. The service returns the first batch of results (up to 5) and a NextToken value. To see the next batch of results, you can submit the ListBridges request a second time and specify the NextToken value."}}}, "ListEntitlementsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "The maximum number of results to return per API request. For example, you submit a ListEntitlements request with MaxResults set at 5. Although 20 items match your request, the service returns no more than the first 5 items. (The service also returns a NextToken value that you can use to fetch the next batch of results.) The service might return fewer results than the MaxResults value. If MaxResults is not included in the request, the service defaults to pagination with a maximum of 20 results per page."}, "NextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "The token that identifies which batch of results that you want to see. For example, you submit a ListEntitlements request with MaxResults set at 5. The service returns the first batch of results (up to 5) and a NextToken value. To see the next batch of results, you can submit the ListEntitlements request a second time and specify the NextToken value."}}}, "ListEntitlementsResponse": {"type": "structure", "members": {"Entitlements": {"shape": "__listOfListedEntitlement", "locationName": "entitlements", "documentation": "A list of entitlements that have been granted to you from other AWS accounts."}, "NextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "The token that identifies which batch of results that you want to see. For example, you submit a ListEntitlements request with MaxResults set at 5. The service returns the first batch of results (up to 5) and a NextToken value. To see the next batch of results, you can submit the ListEntitlements request a second time and specify the NextToken value."}}}, "ListFlowsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "The maximum number of results to return per API request. For example, you submit a ListFlows request with MaxResults set at 5. Although 20 items match your request, the service returns no more than the first 5 items. (The service also returns a NextToken value that you can use to fetch the next batch of results.) The service might return fewer results than the MaxResults value. If MaxResults is not included in the request, the service defaults to pagination with a maximum of 10 results per page."}, "NextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "The token that identifies which batch of results that you want to see. For example, you submit a ListFlows request with MaxResults set at 5. The service returns the first batch of results (up to 5) and a NextToken value. To see the next batch of results, you can submit the ListFlows request a second time and specify the NextToken value."}}}, "ListFlowsResponse": {"type": "structure", "members": {"Flows": {"shape": "__listOfListedFlow", "locationName": "flows", "documentation": "A list of flow summaries."}, "NextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "The token that identifies which batch of results that you want to see. For example, you submit a ListFlows request with MaxResults set at 5. The service returns the first batch of results (up to 5) and a NextToken value. To see the next batch of results, you can submit the ListFlows request a second time and specify the NextToken value."}}}, "ListGatewayInstancesRequest": {"type": "structure", "members": {"FilterArn": {"shape": "__string", "location": "querystring", "locationName": "filterArn", "documentation": "Filter the list results to display only the instances associated with the selected Gateway Amazon Resource Name (ARN)."}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "The maximum number of results to return per API request. For example, you submit a ListInstances request with MaxResults set at 5. Although 20 items match your request, the service returns no more than the first 5 items. (The service also returns a NextToken value that you can use to fetch the next batch of results.) The service might return fewer results than the MaxResults value. If MaxResults is not included in the request, the service defaults to pagination with a maximum of 10 results per page."}, "NextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "The token that identifies which batch of results that you want to see. For example, you submit a ListInstances request with MaxResults set at 5. The service returns the first batch of results (up to 5) and a NextToken value. To see the next batch of results, you can submit the ListInstances request a second time and specify the NextToken value."}}}, "ListGatewayInstancesResponse": {"type": "structure", "members": {"Instances": {"shape": "__listOfListedGatewayInstance", "locationName": "instances", "documentation": "A list of instance summaries."}, "NextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "The token that identifies which batch of results that you want to see. For example, you submit a ListInstances request with MaxResults set at 5. The service returns the first batch of results (up to 5) and a NextToken value. To see the next batch of results, you can submit the ListInstances request a second time and specify the NextToken value."}}}, "ListGatewaysRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "The maximum number of results to return per API request. For example, you submit a ListGateways request with MaxResults set at 5. Although 20 items match your request, the service returns no more than the first 5 items. (The service also returns a NextToken value that you can use to fetch the next batch of results.) The service might return fewer results than the MaxResults value. If MaxResults is not included in the request, the service defaults to pagination with a maximum of 10 results per page."}, "NextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "The token that identifies which batch of results that you want to see. For example, you submit a ListGateways request with MaxResults set at 5. The service returns the first batch of results (up to 5) and a NextToken value. To see the next batch of results, you can submit the ListGateways request a second time and specify the NextToken value."}}}, "ListGatewaysResponse": {"type": "structure", "members": {"Gateways": {"shape": "__listOfListedGateway", "locationName": "gateways", "documentation": "A list of gateway summaries."}, "NextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "The token that identifies which batch of results that you want to see. For example, you submit a ListGateways request with MaxResults set at 5. The service returns the first batch of results (up to 5) and a NextToken value. To see the next batch of results, you can submit the ListGateways request a second time and specify the NextToken value."}}}, "ListOfferingsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "The maximum number of results to return per API request. For example, you submit a ListOfferings request with MaxResults set at 5. Although 20 items match your request, the service returns no more than the first 5 items. (The service also returns a NextToken value that you can use to fetch the next batch of results.) The service might return fewer results than the MaxResults value. If MaxResults is not included in the request, the service defaults to pagination with a maximum of 10 results per page."}, "NextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "The token that identifies which batch of results that you want to see. For example, you submit a ListOfferings request with MaxResults set at 5. The service returns the first batch of results (up to 5) and a NextToken value. To see the next batch of results, you can submit the ListOfferings request a second time and specify the NextToken value."}}}, "ListOfferingsResponse": {"type": "structure", "members": {"NextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "The token that identifies which batch of results that you want to see. For example, you submit a ListOfferings request with MaxResults set at 5. The service returns the first batch of results (up to 5) and a NextToken value. To see the next batch of results, you can submit the ListOfferings request a second time and specify the NextToken value."}, "Offerings": {"shape": "__listOfOffering", "locationName": "offerings", "documentation": "A list of offerings that are available to this account in the current AWS Region."}}}, "ListReservationsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "The maximum number of results to return per API request. For example, you submit a ListReservations request with MaxResults set at 5. Although 20 items match your request, the service returns no more than the first 5 items. (The service also returns a NextToken value that you can use to fetch the next batch of results.) The service might return fewer results than the MaxResults value. If MaxResults is not included in the request, the service defaults to pagination with a maximum of 10 results per page."}, "NextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "The token that identifies which batch of results that you want to see. For example, you submit a ListReservations request with MaxResults set at 5. The service returns the first batch of results (up to 5) and a NextToken value. To see the next batch of results, you can submit the ListOfferings request a second time and specify the NextToken value."}}}, "ListReservationsResponse": {"type": "structure", "members": {"NextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "The token that identifies which batch of results that you want to see. For example, you submit a ListReservations request with MaxResults set at 5. The service returns the first batch of results (up to 5) and a NextToken value. To see the next batch of results, you can submit the ListReservations request a second time and specify the NextToken value."}, "Reservations": {"shape": "__listOfReservation", "locationName": "reservations", "documentation": "A list of all reservations that have been purchased by this account in the current AWS Region."}}}, "ListTagsForResourceRequest": {"type": "structure", "members": {"ResourceArn": {"shape": "__string", "location": "uri", "locationName": "resourceArn", "documentation": "The Amazon Resource Name (ARN) that identifies the AWS Elemental MediaConnect resource for which to list the tags."}}, "required": ["ResourceArn"]}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "__mapOf__string", "locationName": "tags", "documentation": "A map from tag keys to values. Tag keys can have a maximum character length of 128 characters, and tag values can have a maximum length of 256 characters."}}}, "ListedBridge": {"type": "structure", "members": {"BridgeArn": {"shape": "__string", "locationName": "bridgeArn", "documentation": "The ARN of the bridge."}, "BridgeState": {"shape": "BridgeState", "locationName": "bridgeState"}, "BridgeType": {"shape": "__string", "locationName": "bridgeType", "documentation": "The type of the bridge."}, "Name": {"shape": "__string", "locationName": "name", "documentation": "The name of the bridge."}, "PlacementArn": {"shape": "__string", "locationName": "placementArn", "documentation": "The ARN of the gateway associated with the bridge."}}, "documentation": "Displays details of the selected bridge.", "required": ["BridgeArn", "BridgeState", "PlacementArn", "BridgeType", "Name"]}, "ListedEntitlement": {"type": "structure", "members": {"DataTransferSubscriberFeePercent": {"shape": "__integer", "locationName": "dataTransferSubscriberFeePercent", "documentation": "Percentage from 0-100 of the data transfer cost to be billed to the subscriber."}, "EntitlementArn": {"shape": "__string", "locationName": "entitlementArn", "documentation": "The ARN of the entitlement."}, "EntitlementName": {"shape": "__string", "locationName": "entitlementName", "documentation": "The name of the entitlement."}}, "documentation": "An entitlement that has been granted to you from other AWS accounts.", "required": ["EntitlementArn", "EntitlementName"]}, "ListedFlow": {"type": "structure", "members": {"AvailabilityZone": {"shape": "__string", "locationName": "availabilityZone", "documentation": "The Availability Zone that the flow was created in."}, "Description": {"shape": "__string", "locationName": "description", "documentation": "A description of the flow."}, "FlowArn": {"shape": "__string", "locationName": "flowArn", "documentation": "The ARN of the flow."}, "Name": {"shape": "__string", "locationName": "name", "documentation": "The name of the flow."}, "SourceType": {"shape": "SourceType", "locationName": "sourceType", "documentation": "The type of source. This value is either owned (originated somewhere other than an AWS Elemental MediaConnect flow owned by another AWS account) or entitled (originated at an AWS Elemental MediaConnect flow owned by another AWS account)."}, "Status": {"shape": "Status", "locationName": "status", "documentation": "The current status of the flow."}, "Maintenance": {"shape": "Maintenance", "locationName": "maintenance"}}, "documentation": "Provides a summary of a flow, including its ARN, Availability Zone, and source type.", "required": ["Status", "Description", "SourceType", "AvailabilityZone", "FlowArn", "Name"]}, "ListedGateway": {"type": "structure", "members": {"GatewayArn": {"shape": "__string", "locationName": "gatewayArn", "documentation": "The Amazon Resource Name (ARN) of the gateway."}, "GatewayState": {"shape": "GatewayState", "locationName": "gatewayState"}, "Name": {"shape": "__string", "locationName": "name", "documentation": "The name of the gateway."}}, "documentation": "Provides a summary of a gateway, including its name, ARN, and status.", "required": ["GatewayArn", "GatewayState", "Name"]}, "ListedGatewayInstance": {"type": "structure", "members": {"GatewayArn": {"shape": "__string", "locationName": "gatewayArn", "documentation": "The Amazon Resource Name (ARN) of the gateway."}, "GatewayInstanceArn": {"shape": "__string", "locationName": "gatewayInstanceArn", "documentation": "The Amazon Resource Name (ARN) of the instance."}, "InstanceId": {"shape": "__string", "locationName": "instanceId", "documentation": "The managed instance ID generated by the SSM install. This will begin with \"mi-\"."}, "InstanceState": {"shape": "InstanceState", "locationName": "instanceState", "documentation": "The status of the instance."}}, "documentation": "Provides a summary of an instance.", "required": ["GatewayArn", "GatewayInstanceArn", "InstanceId"]}, "Maintenance": {"type": "structure", "members": {"MaintenanceDay": {"shape": "MaintenanceDay", "locationName": "maintenanceDay", "documentation": "A day of a week when the maintenance will happen. Use Monday/Tuesday/Wednesday/Thursday/Friday/Saturday/Sunday."}, "MaintenanceDeadline": {"shape": "__string", "locationName": "maintenanceDeadline", "documentation": "The Maintenance has to be performed before this deadline in ISO UTC format. Example: 2021-01-30T08:30:00Z."}, "MaintenanceScheduledDate": {"shape": "__string", "locationName": "maintenanceScheduledDate", "documentation": "A scheduled date in ISO UTC format when the maintenance will happen. Use YYYY-MM-DD format. Example: 2021-01-30."}, "MaintenanceStartHour": {"shape": "__string", "locationName": "maintenanceStartHour", "documentation": "UTC time when the maintenance will happen. Use 24-hour HH:MM format. Minutes must be 00. Example: 13:00. The default value is 02:00."}}, "documentation": "The maintenance setting of a flow"}, "MaintenanceDay": {"type": "string", "enum": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]}, "MaxResults": {"type": "integer", "min": 1, "max": 1000}, "MediaStream": {"type": "structure", "members": {"Attributes": {"shape": "MediaStreamAttributes", "locationName": "attributes", "documentation": "Attributes that are related to the media stream."}, "ClockRate": {"shape": "__integer", "locationName": "clockRate", "documentation": "The sample rate for the stream. This value is measured in Hz."}, "Description": {"shape": "__string", "locationName": "description", "documentation": "A description that can help you quickly identify what your media stream is used for."}, "Fmt": {"shape": "__integer", "locationName": "fmt", "documentation": "The format type number (sometimes referred to as RTP payload type) of the media stream. MediaConnect assigns this value to the media stream. For ST 2110 JPEG XS outputs, you need to provide this value to the receiver."}, "MediaStreamId": {"shape": "__integer", "locationName": "mediaStreamId", "documentation": "A unique identifier for the media stream."}, "MediaStreamName": {"shape": "__string", "locationName": "mediaStreamName", "documentation": "A name that helps you distinguish one media stream from another."}, "MediaStreamType": {"shape": "MediaStreamType", "locationName": "mediaStreamType", "documentation": "The type of media stream."}, "VideoFormat": {"shape": "__string", "locationName": "videoFormat", "documentation": "The resolution of the video."}}, "documentation": "A single track or stream of media that contains video, audio, or ancillary data. After you add a media stream to a flow, you can associate it with sources and outputs on that flow, as long as they use the CDI protocol or the ST 2110 JPEG XS protocol. Each source or output can consist of one or many media streams.", "required": ["MediaStreamType", "MediaStreamId", "MediaStreamName", "Fmt"]}, "MediaStreamAttributes": {"type": "structure", "members": {"Fmtp": {"shape": "Fmtp", "locationName": "fmtp", "documentation": "A set of parameters that define the media stream."}, "Lang": {"shape": "__string", "locationName": "lang", "documentation": "The audio language, in a format that is recognized by the receiver."}}, "documentation": "Attributes that are related to the media stream.", "required": ["Fmtp"]}, "MediaStreamAttributesRequest": {"type": "structure", "members": {"Fmtp": {"shape": "FmtpRequest", "locationName": "fmtp", "documentation": "The settings that you want to use to define the media stream."}, "Lang": {"shape": "__string", "locationName": "lang", "documentation": "The audio language, in a format that is recognized by the receiver."}}, "documentation": "Attributes that are related to the media stream."}, "MediaStreamOutputConfiguration": {"type": "structure", "members": {"DestinationConfigurations": {"shape": "__listOfDestinationConfiguration", "locationName": "destinationConfigurations", "documentation": "The transport parameters that are associated with each outbound media stream."}, "EncodingName": {"shape": "EncodingName", "locationName": "encodingName", "documentation": "The format that was used to encode the data. For ancillary data streams, set the encoding name to smpte291. For audio streams, set the encoding name to pcm. For video, 2110 streams, set the encoding name to raw. For video, JPEG XS streams, set the encoding name to jxsv."}, "EncodingParameters": {"shape": "EncodingParameters", "locationName": "encodingParameters", "documentation": "Encoding parameters"}, "MediaStreamName": {"shape": "__string", "locationName": "mediaStreamName", "documentation": "The name of the media stream."}}, "documentation": "The media stream that is associated with the output, and the parameters for that association.", "required": ["MediaStreamName", "EncodingName"]}, "MediaStreamOutputConfigurationRequest": {"type": "structure", "members": {"DestinationConfigurations": {"shape": "__listOfDestinationConfigurationRequest", "locationName": "destinationConfigurations", "documentation": "The transport parameters that you want to associate with the media stream."}, "EncodingName": {"shape": "EncodingName", "locationName": "encodingName", "documentation": "The format that will be used to encode the data. For ancillary data streams, set the encoding name to smpte291. For audio streams, set the encoding name to pcm. For video, 2110 streams, set the encoding name to raw. For video, JPEG XS streams, set the encoding name to jxsv."}, "EncodingParameters": {"shape": "EncodingParametersRequest", "locationName": "encodingParameters", "documentation": "A collection of parameters that determine how MediaConnect will convert the content. These fields only apply to outputs on flows that have a CDI source."}, "MediaStreamName": {"shape": "__string", "locationName": "mediaStreamName", "documentation": "The name of the media stream that is associated with the output."}}, "documentation": "The media stream that you want to associate with the output, and the parameters for that association.", "required": ["MediaStreamName", "EncodingName"]}, "MediaStreamSourceConfiguration": {"type": "structure", "members": {"EncodingName": {"shape": "EncodingName", "locationName": "encodingName", "documentation": "The format that was used to encode the data. For ancillary data streams, set the encoding name to smpte291. For audio streams, set the encoding name to pcm. For video, 2110 streams, set the encoding name to raw. For video, JPEG XS streams, set the encoding name to jxsv."}, "InputConfigurations": {"shape": "__listOfInputConfiguration", "locationName": "inputConfigurations", "documentation": "The transport parameters that are associated with an incoming media stream."}, "MediaStreamName": {"shape": "__string", "locationName": "mediaStreamName", "documentation": "The name of the media stream."}}, "documentation": "The media stream that is associated with the source, and the parameters for that association.", "required": ["MediaStreamName", "EncodingName"]}, "MediaStreamSourceConfigurationRequest": {"type": "structure", "members": {"EncodingName": {"shape": "EncodingName", "locationName": "encodingName", "documentation": "The format you want to use to encode the data. For ancillary data streams, set the encoding name to smpte291. For audio streams, set the encoding name to pcm. For video, 2110 streams, set the encoding name to raw. For video, JPEG XS streams, set the encoding name to jxsv."}, "InputConfigurations": {"shape": "__listOfInputConfigurationRequest", "locationName": "inputConfigurations", "documentation": "The transport parameters that you want to associate with the media stream."}, "MediaStreamName": {"shape": "__string", "locationName": "mediaStreamName", "documentation": "The name of the media stream."}}, "documentation": "The definition of a media stream that you want to associate with the source.", "required": ["MediaStreamName", "EncodingName"]}, "MediaStreamType": {"type": "string", "enum": ["video", "audio", "ancillary-data"]}, "MessageDetail": {"type": "structure", "members": {"Code": {"shape": "__string", "locationName": "code", "documentation": "The error code."}, "Message": {"shape": "__string", "locationName": "message", "documentation": "The specific error message that MediaConnect returns to help you understand the reason that the request did not succeed."}, "ResourceName": {"shape": "__string", "locationName": "resourceName", "documentation": "The name of the resource."}}, "required": ["Message", "Code"]}, "Messages": {"type": "structure", "members": {"Errors": {"shape": "__listOf__string", "locationName": "errors", "documentation": "A list of errors that might have been generated from processes on this flow."}}, "documentation": "Messages that provide the state of the flow.", "required": ["Errors"]}, "NetworkInterfaceType": {"type": "string", "enum": ["ena", "efa"]}, "NotFoundException": {"type": "structure", "members": {"Message": {"shape": "__string", "locationName": "message", "documentation": "The error message returned by AWS Elemental MediaConnect."}}, "documentation": "Exception raised by AWS Elemental MediaConnect. See the error message and documentation for the operation for more information on the cause of this exception.", "required": ["Message"], "exception": true, "error": {"httpStatusCode": 404}}, "Offering": {"type": "structure", "members": {"CurrencyCode": {"shape": "__string", "locationName": "currencyCode", "documentation": "The type of currency that is used for billing. The currencyCode used for all reservations is US dollars."}, "Duration": {"shape": "__integer", "locationName": "duration", "documentation": "The length of time that your reservation would be active."}, "DurationUnits": {"shape": "DurationUnits", "locationName": "durationUnits", "documentation": "The unit of measurement for the duration of the offering."}, "OfferingArn": {"shape": "__string", "locationName": "offeringArn", "documentation": "The Amazon Resource Name (ARN) that MediaConnect assigns to the offering."}, "OfferingDescription": {"shape": "__string", "locationName": "offeringDescription", "documentation": "A description of the offering."}, "PricePerUnit": {"shape": "__string", "locationName": "pricePerUnit", "documentation": "The cost of a single unit. This value, in combination with priceUnits, makes up the rate."}, "PriceUnits": {"shape": "PriceUnits", "locationName": "priceUnits", "documentation": "The unit of measurement that is used for billing. This value, in combination with pricePerUnit, makes up the rate."}, "ResourceSpecification": {"shape": "ResourceSpecification", "locationName": "resourceSpecification", "documentation": "A definition of the amount of outbound bandwidth that you would be reserving if you purchase the offering."}}, "documentation": "A savings plan that reserves a certain amount of outbound bandwidth usage at a discounted rate each month over a period of time.", "required": ["CurrencyCode", "OfferingArn", "OfferingDescription", "DurationUnits", "Duration", "PricePerUnit", "ResourceSpecification", "PriceUnits"]}, "Output": {"type": "structure", "members": {"DataTransferSubscriberFeePercent": {"shape": "__integer", "locationName": "dataTransferSubscriberFeePercent", "documentation": "Percentage from 0-100 of the data transfer cost to be billed to the subscriber."}, "Description": {"shape": "__string", "locationName": "description", "documentation": "A description of the output."}, "Destination": {"shape": "__string", "locationName": "destination", "documentation": "The address where you want to send the output."}, "Encryption": {"shape": "Encryption", "locationName": "encryption", "documentation": "The type of key used for the encryption. If no keyType is provided, the service will use the default setting (static-key)."}, "EntitlementArn": {"shape": "__string", "locationName": "entitlementArn", "documentation": "The ARN of the entitlement on the originator''s flow. This value is relevant only on entitled flows."}, "ListenerAddress": {"shape": "__string", "locationName": "listenerAddress", "documentation": "The IP address that the receiver requires in order to establish a connection with the flow. For public networking, the ListenerAddress is represented by the elastic IP address of the flow. For private networking, the ListenerAddress is represented by the elastic network interface IP address of the VPC. This field applies only to outputs that use the Zixi pull or SRT listener protocol."}, "MediaLiveInputArn": {"shape": "__string", "locationName": "mediaLiveInputArn", "documentation": "The input ARN of the AWS Elemental MediaLive channel. This parameter is relevant only for outputs that were added by creating a MediaLive input."}, "MediaStreamOutputConfigurations": {"shape": "__listOfMediaStreamOutputConfiguration", "locationName": "mediaStreamOutputConfigurations", "documentation": "The configuration for each media stream that is associated with the output."}, "Name": {"shape": "__string", "locationName": "name", "documentation": "The name of the output. This value must be unique within the current flow."}, "OutputArn": {"shape": "__string", "locationName": "outputArn", "documentation": "The ARN of the output."}, "Port": {"shape": "__integer", "locationName": "port", "documentation": "The port to use when content is distributed to this output."}, "Transport": {"shape": "Transport", "locationName": "transport", "documentation": "Attributes related to the transport stream that are used in the output."}, "VpcInterfaceAttachment": {"shape": "VpcInterfaceAttachment", "locationName": "vpcInterfaceAttachment", "documentation": "The name of the VPC interface attachment to use for this output."}, "BridgeArn": {"shape": "__string", "locationName": "bridgeArn", "documentation": "The ARN of the bridge that added this output."}, "BridgePorts": {"shape": "__listOf__integer", "locationName": "bridgePorts", "documentation": "The bridge output ports currently in use."}}, "documentation": "The settings for an output.", "required": ["OutputArn", "Name"]}, "PriceUnits": {"type": "string", "enum": ["HOURLY"]}, "Protocol": {"type": "string", "enum": ["zixi-push", "rtp-fec", "rtp", "zixi-pull", "rist", "st2110-jpegxs", "cdi", "srt-listener", "srt-caller", "fujitsu-qos", "udp"]}, "PurchaseOfferingRequest": {"type": "structure", "members": {"OfferingArn": {"shape": "__string", "location": "uri", "locationName": "offeringArn", "documentation": "The Amazon Resource Name (ARN) of the offering."}, "ReservationName": {"shape": "__string", "locationName": "reservationName", "documentation": "The name that you want to use for the reservation."}, "Start": {"shape": "__string", "locationName": "start", "documentation": "The date and time that you want the reservation to begin, in Coordinated Universal Time (UTC). You can specify any date and time between 12:00am on the first day of the current month to the current time on today's date, inclusive. Specify the start in a 24-hour notation. Use the following format: YYYY-MM-DDTHH:mm:SSZ, where T and Z are literal characters. For example, to specify 11:30pm on March 5, 2020, enter 2020-03-05T23:30:00Z."}}, "documentation": "A request to purchase a offering.", "required": ["OfferingArn", "Start", "ReservationName"]}, "PurchaseOfferingResponse": {"type": "structure", "members": {"Reservation": {"shape": "Reservation", "locationName": "reservation"}}}, "Range": {"type": "string", "enum": ["NARROW", "FULL", "FULLPROTECT"]}, "RemoveBridgeOutputRequest": {"type": "structure", "members": {"BridgeArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:bridge:.+$", "location": "uri", "locationName": "bridgeArn", "documentation": "The ARN of the bridge that you want to update."}, "OutputName": {"shape": "__string", "location": "uri", "locationName": "outputName", "documentation": "The name of the bridge output that you want to remove."}}, "required": ["OutputName", "BridgeArn"]}, "RemoveBridgeOutputResponse": {"type": "structure", "members": {"BridgeArn": {"shape": "__string", "locationName": "bridgeArn"}, "OutputName": {"shape": "__string", "locationName": "outputName"}}}, "RemoveBridgeSourceRequest": {"type": "structure", "members": {"BridgeArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:bridge:.+$", "location": "uri", "locationName": "bridgeArn", "documentation": "The ARN of the bridge that you want to update."}, "SourceName": {"shape": "__string", "location": "uri", "locationName": "sourceName", "documentation": "The name of the bridge source that you want to remove."}}, "required": ["BridgeArn", "SourceName"]}, "RemoveBridgeSourceResponse": {"type": "structure", "members": {"BridgeArn": {"shape": "__string", "locationName": "bridgeArn"}, "SourceName": {"shape": "__string", "locationName": "sourceName"}}}, "RemoveFlowMediaStreamRequest": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:flow:.+$", "location": "uri", "locationName": "flowArn", "documentation": "The Amazon Resource Name (ARN) of the flow."}, "MediaStreamName": {"shape": "__string", "location": "uri", "locationName": "mediaStreamName", "documentation": "The name of the media stream that you want to remove."}}, "required": ["FlowArn", "MediaStreamName"]}, "RemoveFlowMediaStreamResponse": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "locationName": "flowArn", "documentation": "The Amazon Resource Name (ARN) of the flow."}, "MediaStreamName": {"shape": "__string", "locationName": "mediaStreamName", "documentation": "The name of the media stream that was removed."}}}, "RemoveFlowOutputRequest": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:flow:.+$", "location": "uri", "locationName": "flowArn", "documentation": "The flow that you want to remove an output from."}, "OutputArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:output:.+$", "location": "uri", "locationName": "outputArn", "documentation": "The ARN of the output that you want to remove."}}, "required": ["FlowArn", "OutputArn"]}, "RemoveFlowOutputResponse": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "locationName": "flowArn", "documentation": "The ARN of the flow that is associated with the output you removed."}, "OutputArn": {"shape": "__string", "locationName": "outputArn", "documentation": "The ARN of the output that was removed."}}}, "RemoveFlowSourceRequest": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:flow:.+$", "location": "uri", "locationName": "flowArn", "documentation": "The flow that you want to remove a source from."}, "SourceArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:source:.+$", "location": "uri", "locationName": "sourceArn", "documentation": "The ARN of the source that you want to remove."}}, "required": ["FlowArn", "SourceArn"]}, "RemoveFlowSourceResponse": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "locationName": "flowArn", "documentation": "The ARN of the flow that is associated with the source you removed."}, "SourceArn": {"shape": "__string", "locationName": "sourceArn", "documentation": "The ARN of the source that was removed."}}}, "RemoveFlowVpcInterfaceRequest": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:flow:.+$", "location": "uri", "locationName": "flowArn", "documentation": "The flow that you want to remove a VPC interface from."}, "VpcInterfaceName": {"shape": "__string", "location": "uri", "locationName": "vpcInterfaceName", "documentation": "The name of the VPC interface that you want to remove."}}, "required": ["FlowArn", "VpcInterfaceName"]}, "RemoveFlowVpcInterfaceResponse": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "locationName": "flowArn", "documentation": "The ARN of the flow that is associated with the VPC interface you removed."}, "NonDeletedNetworkInterfaceIds": {"shape": "__listOf__string", "locationName": "nonDeletedNetworkInterfaceIds", "documentation": "IDs of network interfaces associated with the removed VPC interface that Media Connect was unable to remove."}, "VpcInterfaceName": {"shape": "__string", "locationName": "vpcInterfaceName", "documentation": "The name of the VPC interface that was removed."}}}, "Reservation": {"type": "structure", "members": {"CurrencyCode": {"shape": "__string", "locationName": "currencyCode", "documentation": "The type of currency that is used for billing. The currencyCode used for your reservation is US dollars."}, "Duration": {"shape": "__integer", "locationName": "duration", "documentation": "The length of time that this reservation is active. MediaConnect defines this value in the offering."}, "DurationUnits": {"shape": "DurationUnits", "locationName": "durationUnits", "documentation": "The unit of measurement for the duration of the reservation. MediaConnect defines this value in the offering."}, "End": {"shape": "__string", "locationName": "end", "documentation": "The day and time that this reservation expires. This value is calculated based on the start date and time that you set and the offering's duration."}, "OfferingArn": {"shape": "__string", "locationName": "offeringArn", "documentation": "The Amazon Resource Name (ARN) that MediaConnect assigns to the offering."}, "OfferingDescription": {"shape": "__string", "locationName": "offeringDescription", "documentation": "A description of the offering. MediaConnect defines this value in the offering."}, "PricePerUnit": {"shape": "__string", "locationName": "pricePerUnit", "documentation": "The cost of a single unit. This value, in combination with priceUnits, makes up the rate. MediaConnect defines this value in the offering."}, "PriceUnits": {"shape": "PriceUnits", "locationName": "priceUnits", "documentation": "The unit of measurement that is used for billing. This value, in combination with pricePerUnit, makes up the rate. MediaConnect defines this value in the offering."}, "ReservationArn": {"shape": "__string", "locationName": "reservationArn", "documentation": "The Amazon Resource Name (ARN) that MediaConnect assigns to the reservation when you purchase an offering."}, "ReservationName": {"shape": "__string", "locationName": "reservationName", "documentation": "The name that you assigned to the reservation when you purchased the offering."}, "ReservationState": {"shape": "ReservationState", "locationName": "reservationState", "documentation": "The status of your reservation."}, "ResourceSpecification": {"shape": "ResourceSpecification", "locationName": "resourceSpecification", "documentation": "A definition of the amount of outbound bandwidth that you would be reserving if you purchase the offering. MediaConnect defines the values that make up the resourceSpecification in the offering."}, "Start": {"shape": "__string", "locationName": "start", "documentation": "The day and time that the reservation becomes active. You set this value when you purchase the offering."}}, "documentation": "A pricing agreement for a discounted rate for a specific outbound bandwidth that your MediaConnect account will use each month over a specific time period. The discounted rate in the reservation applies to outbound bandwidth for all flows from your account until your account reaches the amount of bandwidth in your reservation. If you use more outbound bandwidth than the agreed upon amount in a single month, the overage is charged at the on-demand rate.", "required": ["CurrencyCode", "ReservationState", "OfferingArn", "ReservationArn", "Start", "OfferingDescription", "ReservationName", "End", "Duration", "DurationUnits", "PricePerUnit", "ResourceSpecification", "PriceUnits"]}, "ReservationState": {"type": "string", "enum": ["ACTIVE", "EXPIRED", "PROCESSING", "CANCELED"]}, "ResourceSpecification": {"type": "structure", "members": {"ReservedBitrate": {"shape": "__integer", "locationName": "reservedBitrate", "documentation": "The amount of outbound bandwidth that is discounted in the offering."}, "ResourceType": {"shape": "ResourceType", "locationName": "resourceType", "documentation": "The type of resource and the unit that is being billed for."}}, "documentation": "A definition of what is being billed for, including the type and amount.", "required": ["ResourceType"]}, "ResourceType": {"type": "string", "enum": ["Mbps_Outbound_Bandwidth"]}, "ResponseError": {"type": "structure", "members": {"Message": {"shape": "__string", "locationName": "message", "documentation": "The error message returned by AWS Elemental MediaConnect."}}, "documentation": "Exception raised by AWS Elemental MediaConnect. See the error message and documentation for the operation for more information on the cause of this exception.", "required": ["Message"]}, "RevokeFlowEntitlementRequest": {"type": "structure", "members": {"EntitlementArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:entitlement:.+$", "location": "uri", "locationName": "entitlementArn", "documentation": "The ARN of the entitlement that you want to revoke."}, "FlowArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:flow:.+$", "location": "uri", "locationName": "flowArn", "documentation": "The flow that you want to revoke an entitlement from."}}, "required": ["FlowArn", "EntitlementArn"]}, "RevokeFlowEntitlementResponse": {"type": "structure", "members": {"EntitlementArn": {"shape": "__string", "locationName": "entitlementArn", "documentation": "The ARN of the entitlement that was revoked."}, "FlowArn": {"shape": "__string", "locationName": "flowArn", "documentation": "The ARN of the flow that the entitlement was revoked from."}}}, "ScanMode": {"type": "string", "enum": ["progressive", "interlace", "progressive-segmented-frame"]}, "ServiceUnavailableException": {"type": "structure", "members": {"Message": {"shape": "__string", "locationName": "message", "documentation": "The error message returned by AWS Elemental MediaConnect."}}, "documentation": "Exception raised by AWS Elemental MediaConnect. See the error message and documentation for the operation for more information on the cause of this exception.", "required": ["Message"], "exception": true, "error": {"httpStatusCode": 503}}, "SetGatewayBridgeSourceRequest": {"type": "structure", "members": {"BridgeArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:bridge:.+$", "locationName": "bridgeArn", "documentation": "The ARN of the bridge feeding this flow."}, "VpcInterfaceAttachment": {"shape": "VpcInterfaceAttachment", "locationName": "vpcInterfaceAttachment", "documentation": "The name of the VPC interface attachment to use for this bridge source."}}, "documentation": "The source configuration for cloud flows receiving a stream from a bridge.", "required": ["BridgeArn"]}, "SetSourceRequest": {"type": "structure", "members": {"Decryption": {"shape": "Encryption", "locationName": "decryption", "documentation": "The type of encryption that is used on the content ingested from this source. Allowable encryption types: static-key."}, "Description": {"shape": "__string", "locationName": "description", "documentation": "A description for the source. This value is not used or seen outside of the current AWS Elemental MediaConnect account."}, "EntitlementArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:entitlement:.+$", "locationName": "entitlementArn", "documentation": "The ARN of the entitlement that allows you to subscribe to this flow. The entitlement is set by the flow originator, and the ARN is generated as part of the originator's flow."}, "IngestPort": {"shape": "__integer", "locationName": "ingestPort", "documentation": "The port that the flow will be listening on for incoming content."}, "MaxBitrate": {"shape": "__integer", "locationName": "maxBitrate", "documentation": "The smoothing max bitrate (in bps) for RIST, RTP, and RTP-FEC streams."}, "MaxLatency": {"shape": "__integer", "locationName": "maxLatency", "documentation": "The maximum latency in milliseconds. This parameter applies only to RIST-based, Zixi-based, and Fujitsu-based streams."}, "MaxSyncBuffer": {"shape": "__integer", "locationName": "maxSyncBuffer", "documentation": "The size of the buffer (in milliseconds) to use to sync incoming source data."}, "MediaStreamSourceConfigurations": {"shape": "__listOfMediaStreamSourceConfigurationRequest", "locationName": "mediaStreamSourceConfigurations", "documentation": "The media streams that are associated with the source, and the parameters for those associations."}, "MinLatency": {"shape": "__integer", "locationName": "minLatency", "documentation": "The minimum latency in milliseconds for SRT-based streams. In streams that use the SRT protocol, this value that you set on your MediaConnect source or output represents the minimal potential latency of that connection. The latency of the stream is set to the highest number between the sender’s minimum latency and the receiver’s minimum latency."}, "Name": {"shape": "__string", "locationName": "name", "documentation": "The name of the source."}, "Protocol": {"shape": "Protocol", "locationName": "protocol", "documentation": "The protocol that is used by the source."}, "SenderControlPort": {"shape": "__integer", "locationName": "senderControlPort", "documentation": "The port that the flow uses to send outbound requests to initiate connection with the sender."}, "SenderIpAddress": {"shape": "__string", "locationName": "sender<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "The IP address that the flow communicates with to initiate connection with the sender."}, "SourceListenerAddress": {"shape": "__string", "locationName": "sourceListenerAddress", "documentation": "Source IP or domain name for SRT-caller protocol."}, "SourceListenerPort": {"shape": "__integer", "locationName": "sourceListenerPort", "documentation": "Source port for SRT-caller protocol."}, "StreamId": {"shape": "__string", "locationName": "streamId", "documentation": "The stream ID that you want to use for this transport. This parameter applies only to Zixi and SRT caller-based streams."}, "VpcInterfaceName": {"shape": "__string", "locationName": "vpcInterfaceName", "documentation": "The name of the VPC interface to use for this source."}, "WhitelistCidr": {"shape": "__string", "locationName": "whitelist<PERSON><PERSON><PERSON>", "documentation": "The range of IP addresses that should be allowed to contribute content to your source. These IP addresses should be in the form of a Classless Inter-Domain Routing (CIDR) block; for example, 10.0.0.0/16."}, "GatewayBridgeSource": {"shape": "SetGatewayBridgeSourceRequest", "locationName": "gatewayBridgeSource", "documentation": "The source configuration for cloud flows receiving a stream from a bridge."}}, "documentation": "The settings for the source of the flow."}, "Source": {"type": "structure", "members": {"DataTransferSubscriberFeePercent": {"shape": "__integer", "locationName": "dataTransferSubscriberFeePercent", "documentation": "Percentage from 0-100 of the data transfer cost to be billed to the subscriber."}, "Decryption": {"shape": "Encryption", "locationName": "decryption", "documentation": "The type of encryption that is used on the content ingested from this source."}, "Description": {"shape": "__string", "locationName": "description", "documentation": "A description for the source. This value is not used or seen outside of the current AWS Elemental MediaConnect account."}, "EntitlementArn": {"shape": "__string", "locationName": "entitlementArn", "documentation": "The ARN of the entitlement that allows you to subscribe to content that comes from another AWS account. The entitlement is set by the content originator and the ARN is generated as part of the originator's flow."}, "IngestIp": {"shape": "__string", "locationName": "ingestIp", "documentation": "The IP address that the flow will be listening on for incoming content."}, "IngestPort": {"shape": "__integer", "locationName": "ingestPort", "documentation": "The port that the flow will be listening on for incoming content."}, "MediaStreamSourceConfigurations": {"shape": "__listOfMediaStreamSourceConfiguration", "locationName": "mediaStreamSourceConfigurations", "documentation": "The media streams that are associated with the source, and the parameters for those associations."}, "Name": {"shape": "__string", "locationName": "name", "documentation": "The name of the source."}, "SenderControlPort": {"shape": "__integer", "locationName": "senderControlPort", "documentation": "The port that the flow uses to send outbound requests to initiate connection with the sender."}, "SenderIpAddress": {"shape": "__string", "locationName": "sender<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "The IP address that the flow communicates with to initiate connection with the sender."}, "SourceArn": {"shape": "__string", "locationName": "sourceArn", "documentation": "The ARN of the source."}, "Transport": {"shape": "Transport", "locationName": "transport", "documentation": "Attributes related to the transport stream that are used in the source."}, "VpcInterfaceName": {"shape": "__string", "locationName": "vpcInterfaceName", "documentation": "The name of the VPC interface that is used for this source."}, "WhitelistCidr": {"shape": "__string", "locationName": "whitelist<PERSON><PERSON><PERSON>", "documentation": "The range of IP addresses that should be allowed to contribute content to your source. These IP addresses should be in the form of a Classless Inter-Domain Routing (CIDR) block; for example, 10.0.0.0/16."}, "GatewayBridgeSource": {"shape": "GatewayBridgeSource", "locationName": "gatewayBridgeSource", "documentation": "The source configuration for cloud flows receiving a stream from a bridge."}}, "documentation": "The settings for the source of the flow.", "required": ["Name", "SourceArn"]}, "SourcePriority": {"type": "structure", "members": {"PrimarySource": {"shape": "__string", "locationName": "primarySource", "documentation": "The name of the source you choose as the primary source for this flow."}}, "documentation": "The priority you want to assign to a source. You can have a primary stream and a backup stream or two equally prioritized streams."}, "SourceType": {"type": "string", "enum": ["OWNED", "ENTITLED"]}, "StartFlowRequest": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:flow:.+$", "location": "uri", "locationName": "flowArn", "documentation": "The ARN of the flow that you want to start."}}, "required": ["FlowArn"]}, "StartFlowResponse": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "locationName": "flowArn", "documentation": "The ARN of the flow that you started."}, "Status": {"shape": "Status", "locationName": "status", "documentation": "The status of the flow when the StartFlow process begins."}}}, "State": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "Status": {"type": "string", "enum": ["STANDBY", "ACTIVE", "UPDATING", "DELETING", "STARTING", "STOPPING", "ERROR"]}, "StopFlowRequest": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:flow:.+$", "location": "uri", "locationName": "flowArn", "documentation": "The ARN of the flow that you want to stop."}}, "required": ["FlowArn"]}, "StopFlowResponse": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "locationName": "flowArn", "documentation": "The ARN of the flow that you stopped."}, "Status": {"shape": "Status", "locationName": "status", "documentation": "The status of the flow when the StopFlow process begins."}}}, "TagResourceRequest": {"type": "structure", "members": {"ResourceArn": {"shape": "__string", "location": "uri", "locationName": "resourceArn", "documentation": "The Amazon Resource Name (ARN) that identifies the AWS Elemental MediaConnect resource to which to add tags."}, "Tags": {"shape": "__mapOf__string", "locationName": "tags", "documentation": "A map from tag keys to values. Tag keys can have a maximum character length of 128 characters, and tag values can have a maximum length of 256 characters."}}, "documentation": "The tags to add to the resource. A tag is an array of key-value pairs. Tag keys can have a maximum character length of 128 characters, and tag values can have a maximum length of 256 characters.", "required": ["ResourceArn", "Tags"]}, "Tcs": {"type": "string", "enum": ["SDR", "PQ", "HLG", "LINEAR", "BT2100LINPQ", "BT2100LINHLG", "ST2065-1", "ST428-1", "DENSITY"]}, "TooManyRequestsException": {"type": "structure", "members": {"Message": {"shape": "__string", "locationName": "message", "documentation": "The error message returned by AWS Elemental MediaConnect."}}, "documentation": "Exception raised by AWS Elemental MediaConnect. See the error message and documentation for the operation for more information on the cause of this exception.", "required": ["Message"], "exception": true, "error": {"httpStatusCode": 429}}, "Transport": {"type": "structure", "members": {"CidrAllowList": {"shape": "__listOf__string", "locationName": "cidrAllowList", "documentation": "The range of IP addresses that should be allowed to initiate output requests to this flow. These IP addresses should be in the form of a Classless Inter-Domain Routing (CIDR) block; for example, 10.0.0.0/16."}, "MaxBitrate": {"shape": "__integer", "locationName": "maxBitrate", "documentation": "The smoothing max bitrate (in bps) for RIST, RTP, and RTP-FEC streams."}, "MaxLatency": {"shape": "__integer", "locationName": "maxLatency", "documentation": "The maximum latency in milliseconds. This parameter applies only to RIST-based, Zixi-based, and Fujitsu-based streams."}, "MaxSyncBuffer": {"shape": "__integer", "locationName": "maxSyncBuffer", "documentation": "The size of the buffer (in milliseconds) to use to sync incoming source data."}, "MinLatency": {"shape": "__integer", "locationName": "minLatency", "documentation": "The minimum latency in milliseconds for SRT-based streams. In streams that use the SRT protocol, this value that you set on your MediaConnect source or output represents the minimal potential latency of that connection. The latency of the stream is set to the highest number between the sender’s minimum latency and the receiver’s minimum latency."}, "Protocol": {"shape": "Protocol", "locationName": "protocol", "documentation": "The protocol that is used by the source or output."}, "RemoteId": {"shape": "__string", "locationName": "remoteId", "documentation": "The remote ID for the Zixi-pull stream."}, "SenderControlPort": {"shape": "__integer", "locationName": "senderControlPort", "documentation": "The port that the flow uses to send outbound requests to initiate connection with the sender."}, "SenderIpAddress": {"shape": "__string", "locationName": "sender<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "The IP address that the flow communicates with to initiate connection with the sender."}, "SmoothingLatency": {"shape": "__integer", "locationName": "smoothingLatency", "documentation": "The smoothing latency in milliseconds for RIST, RTP, and RTP-FEC streams."}, "SourceListenerAddress": {"shape": "__string", "locationName": "sourceListenerAddress", "documentation": "Source IP or domain name for SRT-caller protocol."}, "SourceListenerPort": {"shape": "__integer", "locationName": "sourceListenerPort", "documentation": "Source port for SRT-caller protocol."}, "StreamId": {"shape": "__string", "locationName": "streamId", "documentation": "The stream ID that you want to use for this transport. This parameter applies only to Zixi and SRT caller-based streams."}}, "documentation": "Attributes related to the transport stream that are used in a source or output.", "required": ["Protocol"]}, "UntagResourceRequest": {"type": "structure", "members": {"ResourceArn": {"shape": "__string", "location": "uri", "locationName": "resourceArn", "documentation": "The Amazon Resource Name (ARN) that identifies the AWS Elemental MediaConnect resource from which to delete tags."}, "TagKeys": {"shape": "__listOf__string", "location": "querystring", "locationName": "tagKeys", "documentation": "The keys of the tags to be removed."}}, "required": ["TagKeys", "ResourceArn"]}, "UpdateBridgeFlowSourceRequest": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:flow:.+$", "locationName": "flowArn", "documentation": "The ARN of the cloud flow to use as a source of this bridge."}, "FlowVpcInterfaceAttachment": {"shape": "VpcInterfaceAttachment", "locationName": "flowVpcInterfaceAttachment", "documentation": "The name of the VPC interface attachment to use for this source."}}, "documentation": "Update the flow source of the bridge."}, "UpdateBridgeNetworkOutputRequest": {"type": "structure", "members": {"IpAddress": {"shape": "__string", "locationName": "ip<PERSON><PERSON><PERSON>", "documentation": "The network output IP Address."}, "NetworkName": {"shape": "__string", "locationName": "networkName", "documentation": "The network output's gateway network name."}, "Port": {"shape": "__integer", "locationName": "port", "documentation": "The network output port."}, "Protocol": {"shape": "Protocol", "locationName": "protocol", "documentation": "The network output protocol."}, "Ttl": {"shape": "__integer", "locationName": "ttl", "documentation": "The network output TTL."}}, "documentation": "Update an existing network output."}, "UpdateBridgeNetworkSourceRequest": {"type": "structure", "members": {"MulticastIp": {"shape": "__string", "locationName": "multicastIp", "documentation": "The network source multicast IP."}, "NetworkName": {"shape": "__string", "locationName": "networkName", "documentation": "The network source's gateway network name."}, "Port": {"shape": "__integer", "locationName": "port", "documentation": "The network source port."}, "Protocol": {"shape": "Protocol", "locationName": "protocol", "documentation": "The network source protocol."}}, "documentation": "Update the network source of the bridge."}, "UpdateBridgeOutputRequest": {"type": "structure", "members": {"BridgeArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:bridge:.+$", "location": "uri", "locationName": "bridgeArn", "documentation": "The ARN of the bridge that you want to update."}, "NetworkOutput": {"shape": "UpdateBridgeNetworkOutputRequest", "locationName": "networkOutput"}, "OutputName": {"shape": "__string", "location": "uri", "locationName": "outputName", "documentation": "The name of the bridge output that you want to update."}}, "documentation": "The fields that you want to update in the bridge output.", "required": ["OutputName", "BridgeArn"]}, "UpdateBridgeOutputResponse": {"type": "structure", "members": {"BridgeArn": {"shape": "__string", "locationName": "bridgeArn", "documentation": "The Amazon Resource Number (ARN) of the bridge."}, "Output": {"shape": "BridgeOutput", "locationName": "output", "documentation": "The output that you updated."}}}, "UpdateBridgeRequest": {"type": "structure", "members": {"BridgeArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:bridge:.+$", "location": "uri", "locationName": "bridgeArn", "documentation": "The Amazon Resource Number (ARN) of the bridge that you want to update."}, "EgressGatewayBridge": {"shape": "UpdateEgressGatewayBridgeRequest", "locationName": "egressGatewayBridge"}, "IngressGatewayBridge": {"shape": "UpdateIngressGatewayBridgeRequest", "locationName": "ingressGatewayBridge"}, "SourceFailoverConfig": {"shape": "UpdateFailoverConfig", "locationName": "sourceFailoverConfig"}}, "documentation": "A request to update the bridge.", "required": ["BridgeArn"]}, "UpdateBridgeResponse": {"type": "structure", "members": {"Bridge": {"shape": "Bridge", "locationName": "bridge"}}}, "UpdateBridgeSourceRequest": {"type": "structure", "members": {"BridgeArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:bridge:.+$", "location": "uri", "locationName": "bridgeArn", "documentation": "The ARN of the bridge that you want to update."}, "FlowSource": {"shape": "UpdateBridgeFlowSourceRequest", "locationName": "flowSource"}, "NetworkSource": {"shape": "UpdateBridgeNetworkSourceRequest", "locationName": "networkSource"}, "SourceName": {"shape": "__string", "location": "uri", "locationName": "sourceName", "documentation": "The name of the source that you want to update."}}, "documentation": "The fields that you want to update in the bridge source.", "required": ["BridgeArn", "SourceName"]}, "UpdateBridgeSourceResponse": {"type": "structure", "members": {"BridgeArn": {"shape": "__string", "locationName": "bridgeArn", "documentation": "The Amazon Resource Number (ARN) of the bridge."}, "Source": {"shape": "BridgeSource", "locationName": "source"}}}, "UpdateBridgeStateRequest": {"type": "structure", "members": {"BridgeArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:bridge:.+$", "location": "uri", "locationName": "bridgeArn", "documentation": "The ARN of the bridge that you want to update."}, "DesiredState": {"shape": "DesiredState", "locationName": "desiredState"}}, "documentation": "A request to update the bridge state.", "required": ["BridgeArn", "DesiredState"]}, "UpdateBridgeStateResponse": {"type": "structure", "members": {"BridgeArn": {"shape": "__string", "locationName": "bridgeArn", "documentation": "The Amazon Resource Number (ARN) of the bridge."}, "DesiredState": {"shape": "DesiredState", "locationName": "desiredState", "documentation": "The state of the bridge. ACTIVE or STANDBY."}}}, "UpdateEgressGatewayBridgeRequest": {"type": "structure", "members": {"MaxBitrate": {"shape": "__integer", "locationName": "maxBitrate", "documentation": "Update an existing egress-type bridge."}}}, "UpdateEncryption": {"type": "structure", "members": {"Algorithm": {"shape": "Algorithm", "locationName": "algorithm", "documentation": "The type of algorithm that is used for the encryption (such as aes128, aes192, or aes256)."}, "ConstantInitializationVector": {"shape": "__string", "locationName": "constantInitializationVector", "documentation": "A 128-bit, 16-byte hex value represented by a 32-character string, to be used with the key for encrypting content. This parameter is not valid for static key encryption."}, "DeviceId": {"shape": "__string", "locationName": "deviceId", "documentation": "The value of one of the devices that you configured with your digital rights management (DRM) platform key provider. This parameter is required for SPEKE encryption and is not valid for static key encryption."}, "KeyType": {"shape": "KeyType", "locationName": "keyType", "documentation": "The type of key that is used for the encryption. If no keyType is provided, the service will use the default setting (static-key)."}, "Region": {"shape": "__string", "locationName": "region", "documentation": "The AWS Region that the API Gateway proxy endpoint was created in. This parameter is required for SPEKE encryption and is not valid for static key encryption."}, "ResourceId": {"shape": "__string", "locationName": "resourceId", "documentation": "An identifier for the content. The service sends this value to the key server to identify the current endpoint. The resource ID is also known as the content ID. This parameter is required for SPEKE encryption and is not valid for static key encryption."}, "RoleArn": {"shape": "__string", "locationName": "roleArn", "documentation": "The ARN of the role that you created during setup (when you set up AWS Elemental MediaConnect as a trusted entity)."}, "SecretArn": {"shape": "__string", "locationName": "secretArn", "documentation": "The ARN of the secret that you created in AWS Secrets Manager to store the encryption key. This parameter is required for static key encryption and is not valid for SPEKE encryption."}, "Url": {"shape": "__string", "locationName": "url", "documentation": "The URL from the API Gateway proxy that you set up to talk to your key server. This parameter is required for SPEKE encryption and is not valid for static key encryption."}}, "documentation": "Information about the encryption of the flow."}, "UpdateFailoverConfig": {"type": "structure", "members": {"FailoverMode": {"shape": "FailoverMode", "locationName": "failoverMode", "documentation": "The type of failover you choose for this flow. MERGE combines the source streams into a single stream, allowing graceful recovery from any single-source loss. FAILOVER allows switching between different streams."}, "RecoveryWindow": {"shape": "__integer", "locationName": "recoveryWindow", "documentation": "Recovery window time to look for dash-7 packets"}, "SourcePriority": {"shape": "SourcePriority", "locationName": "sourcePriority", "documentation": "The priority you want to assign to a source. You can have a primary stream and a backup stream or two equally prioritized streams."}, "State": {"shape": "State", "locationName": "state"}}, "documentation": "The settings for source failover."}, "UpdateFlowEntitlementRequest": {"type": "structure", "members": {"Description": {"shape": "__string", "locationName": "description", "documentation": "A description of the entitlement. This description appears only on the AWS Elemental MediaConnect console and will not be seen by the subscriber or end user."}, "Encryption": {"shape": "UpdateEncryption", "locationName": "encryption", "documentation": "The type of encryption that will be used on the output associated with this entitlement. Allowable encryption types: static-key, speke."}, "EntitlementArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:entitlement:.+$", "location": "uri", "locationName": "entitlementArn", "documentation": "The ARN of the entitlement that you want to update."}, "EntitlementStatus": {"shape": "EntitlementStatus", "locationName": "entitlementStatus", "documentation": "An indication of whether you want to enable the entitlement to allow access, or disable it to stop streaming content to the subscriber’s flow temporarily. If you don’t specify the entitlementStatus field in your request, MediaConnect leaves the value unchanged."}, "FlowArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:flow:.+$", "location": "uri", "locationName": "flowArn", "documentation": "The flow that is associated with the entitlement that you want to update."}, "Subscribers": {"shape": "__listOf__string", "locationName": "subscribers", "documentation": "The AWS account IDs that you want to share your content with. The receiving accounts (subscribers) will be allowed to create their own flow using your content as the source."}}, "documentation": "The entitlement fields that you want to update.", "required": ["FlowArn", "EntitlementArn"]}, "UpdateFlowEntitlementResponse": {"type": "structure", "members": {"Entitlement": {"shape": "Entitlement", "locationName": "entitlement", "documentation": "The new configuration of the entitlement that you updated."}, "FlowArn": {"shape": "__string", "locationName": "flowArn", "documentation": "The ARN of the flow that this entitlement was granted on."}}}, "UpdateFlowMediaStreamRequest": {"type": "structure", "members": {"Attributes": {"shape": "MediaStreamAttributesRequest", "locationName": "attributes", "documentation": "The attributes that you want to assign to the media stream."}, "ClockRate": {"shape": "__integer", "locationName": "clockRate", "documentation": "The sample rate (in Hz) for the stream. If the media stream type is video or ancillary data, set this value to 90000. If the media stream type is audio, set this value to either 48000 or 96000."}, "Description": {"shape": "__string", "locationName": "description", "documentation": "Description"}, "FlowArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:flow:.+$", "location": "uri", "locationName": "flowArn", "documentation": "The Amazon Resource Name (ARN) of the flow."}, "MediaStreamName": {"shape": "__string", "location": "uri", "locationName": "mediaStreamName", "documentation": "The name of the media stream that you want to update."}, "MediaStreamType": {"shape": "MediaStreamType", "locationName": "mediaStreamType", "documentation": "The type of media stream."}, "VideoFormat": {"shape": "__string", "locationName": "videoFormat", "documentation": "The resolution of the video."}}, "documentation": "The fields that you want to update in the media stream.", "required": ["FlowArn", "MediaStreamName"]}, "UpdateFlowMediaStreamResponse": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "locationName": "flowArn", "documentation": "The ARN of the flow that is associated with the media stream that you updated."}, "MediaStream": {"shape": "MediaStream", "locationName": "mediaStream", "documentation": "The media stream that you updated."}}}, "UpdateFlowOutputRequest": {"type": "structure", "members": {"CidrAllowList": {"shape": "__listOf__string", "locationName": "cidrAllowList", "documentation": "The range of IP addresses that should be allowed to initiate output requests to this flow. These IP addresses should be in the form of a Classless Inter-Domain Routing (CIDR) block; for example, 10.0.0.0/16."}, "Description": {"shape": "__string", "locationName": "description", "documentation": "A description of the output. This description appears only on the AWS Elemental MediaConnect console and will not be seen by the end user."}, "Destination": {"shape": "__string", "locationName": "destination", "documentation": "The IP address where you want to send the output."}, "Encryption": {"shape": "UpdateEncryption", "locationName": "encryption", "documentation": "The type of key used for the encryption. If no keyType is provided, the service will use the default setting (static-key). Allowable encryption types: static-key."}, "FlowArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:flow:.+$", "location": "uri", "locationName": "flowArn", "documentation": "The flow that is associated with the output that you want to update."}, "MaxLatency": {"shape": "__integer", "locationName": "maxLatency", "documentation": "The maximum latency in milliseconds. This parameter applies only to RIST-based, Zixi-based, and Fujitsu-based streams."}, "MediaStreamOutputConfigurations": {"shape": "__listOfMediaStreamOutputConfigurationRequest", "locationName": "mediaStreamOutputConfigurations", "documentation": "The media streams that are associated with the output, and the parameters for those associations."}, "MinLatency": {"shape": "__integer", "locationName": "minLatency", "documentation": "The minimum latency in milliseconds for SRT-based streams. In streams that use the SRT protocol, this value that you set on your MediaConnect source or output represents the minimal potential latency of that connection. The latency of the stream is set to the highest number between the sender’s minimum latency and the receiver’s minimum latency."}, "OutputArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:output:.+$", "location": "uri", "locationName": "outputArn", "documentation": "The ARN of the output that you want to update."}, "Port": {"shape": "__integer", "locationName": "port", "documentation": "The port to use when content is distributed to this output."}, "Protocol": {"shape": "Protocol", "locationName": "protocol", "documentation": "The protocol to use for the output."}, "RemoteId": {"shape": "__string", "locationName": "remoteId", "documentation": "The remote ID for the Zixi-pull stream."}, "SenderControlPort": {"shape": "__integer", "locationName": "senderControlPort", "documentation": "The port that the flow uses to send outbound requests to initiate connection with the sender."}, "SenderIpAddress": {"shape": "__string", "locationName": "sender<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "The IP address that the flow communicates with to initiate connection with the sender."}, "SmoothingLatency": {"shape": "__integer", "locationName": "smoothingLatency", "documentation": "The smoothing latency in milliseconds for RIST, RTP, and RTP-FEC streams."}, "StreamId": {"shape": "__string", "locationName": "streamId", "documentation": "The stream ID that you want to use for this transport. This parameter applies only to Zixi and SRT caller-based streams."}, "VpcInterfaceAttachment": {"shape": "VpcInterfaceAttachment", "locationName": "vpcInterfaceAttachment", "documentation": "The name of the VPC interface attachment to use for this output."}}, "documentation": "The fields that you want to update in the output.", "required": ["FlowArn", "OutputArn"]}, "UpdateFlowOutputResponse": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "locationName": "flowArn", "documentation": "The ARN of the flow that is associated with the updated output."}, "Output": {"shape": "Output", "locationName": "output", "documentation": "The new settings of the output that you updated."}}}, "UpdateFlowRequest": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:flow:.+$", "location": "uri", "locationName": "flowArn", "documentation": "The flow that you want to update."}, "SourceFailoverConfig": {"shape": "UpdateFailoverConfig", "locationName": "sourceFailoverConfig"}, "Maintenance": {"shape": "UpdateMaintenance", "locationName": "maintenance"}}, "documentation": "A request to update flow.", "required": ["FlowArn"]}, "UpdateFlowResponse": {"type": "structure", "members": {"Flow": {"shape": "Flow", "locationName": "flow"}}}, "UpdateFlowSourceRequest": {"type": "structure", "members": {"Decryption": {"shape": "UpdateEncryption", "locationName": "decryption", "documentation": "The type of encryption used on the content ingested from this source. Allowable encryption types: static-key."}, "Description": {"shape": "__string", "locationName": "description", "documentation": "A description for the source. This value is not used or seen outside of the current AWS Elemental MediaConnect account."}, "EntitlementArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:entitlement:.+$", "locationName": "entitlementArn", "documentation": "The ARN of the entitlement that allows you to subscribe to this flow. The entitlement is set by the flow originator, and the ARN is generated as part of the originator's flow."}, "FlowArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:flow:.+$", "location": "uri", "locationName": "flowArn", "documentation": "The flow that is associated with the source that you want to update."}, "IngestPort": {"shape": "__integer", "locationName": "ingestPort", "documentation": "The port that the flow will be listening on for incoming content."}, "MaxBitrate": {"shape": "__integer", "locationName": "maxBitrate", "documentation": "The smoothing max bitrate (in bps) for RIST, RTP, and RTP-FEC streams."}, "MaxLatency": {"shape": "__integer", "locationName": "maxLatency", "documentation": "The maximum latency in milliseconds. This parameter applies only to RIST-based, Zixi-based, and Fujitsu-based streams."}, "MaxSyncBuffer": {"shape": "__integer", "locationName": "maxSyncBuffer", "documentation": "The size of the buffer (in milliseconds) to use to sync incoming source data."}, "MediaStreamSourceConfigurations": {"shape": "__listOfMediaStreamSourceConfigurationRequest", "locationName": "mediaStreamSourceConfigurations", "documentation": "The media streams that are associated with the source, and the parameters for those associations."}, "MinLatency": {"shape": "__integer", "locationName": "minLatency", "documentation": "The minimum latency in milliseconds for SRT-based streams. In streams that use the SRT protocol, this value that you set on your MediaConnect source or output represents the minimal potential latency of that connection. The latency of the stream is set to the highest number between the sender’s minimum latency and the receiver’s minimum latency."}, "Protocol": {"shape": "Protocol", "locationName": "protocol", "documentation": "The protocol that is used by the source."}, "SenderControlPort": {"shape": "__integer", "locationName": "senderControlPort", "documentation": "The port that the flow uses to send outbound requests to initiate connection with the sender."}, "SenderIpAddress": {"shape": "__string", "locationName": "sender<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "The IP address that the flow communicates with to initiate connection with the sender."}, "SourceArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:source:.+$", "location": "uri", "locationName": "sourceArn", "documentation": "The ARN of the source that you want to update."}, "SourceListenerAddress": {"shape": "__string", "locationName": "sourceListenerAddress", "documentation": "Source IP or domain name for SRT-caller protocol."}, "SourceListenerPort": {"shape": "__integer", "locationName": "sourceListenerPort", "documentation": "Source port for SRT-caller protocol."}, "StreamId": {"shape": "__string", "locationName": "streamId", "documentation": "The stream ID that you want to use for this transport. This parameter applies only to Zixi and SRT caller-based streams."}, "VpcInterfaceName": {"shape": "__string", "locationName": "vpcInterfaceName", "documentation": "The name of the VPC interface to use for this source."}, "WhitelistCidr": {"shape": "__string", "locationName": "whitelist<PERSON><PERSON><PERSON>", "documentation": "The range of IP addresses that should be allowed to contribute content to your source. These IP addresses should be in the form of a Classless Inter-Domain Routing (CIDR) block; for example, 10.0.0.0/16."}, "GatewayBridgeSource": {"shape": "UpdateGatewayBridgeSourceRequest", "locationName": "gatewayBridgeSource", "documentation": "The source configuration for cloud flows receiving a stream from a bridge."}}, "documentation": "A request to update the source of a flow.", "required": ["FlowArn", "SourceArn"]}, "UpdateFlowSourceResponse": {"type": "structure", "members": {"FlowArn": {"shape": "__string", "locationName": "flowArn", "documentation": "The ARN of the flow that you want to update."}, "Source": {"shape": "Source", "locationName": "source", "documentation": "The settings for the source of the flow."}}}, "UpdateGatewayBridgeSourceRequest": {"type": "structure", "members": {"BridgeArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:bridge:.+$", "locationName": "bridgeArn", "documentation": "The ARN of the bridge feeding this flow."}, "VpcInterfaceAttachment": {"shape": "VpcInterfaceAttachment", "locationName": "vpcInterfaceAttachment", "documentation": "The name of the VPC interface attachment to use for this bridge source."}}, "documentation": "The source configuration for cloud flows receiving a stream from a bridge."}, "UpdateGatewayInstanceRequest": {"type": "structure", "members": {"BridgePlacement": {"shape": "BridgePlacement", "locationName": "bridgePlacement", "documentation": "The availability of the instance to host new bridges. The bridgePlacement property can be LOCKED or AVAILABLE. If it is LOCKED, no new bridges can be deployed to this instance. If it is AVAILABLE, new bridges can be added to this instance."}, "GatewayInstanceArn": {"shape": "__string", "pattern": "^arn:.+:mediaconnect.+:gateway:.+:instance:.+$", "location": "uri", "locationName": "gatewayInstanceArn", "documentation": "The Amazon Resource Name (ARN) of the instance that you want to update."}}, "documentation": "A request to update gateway instance state.", "required": ["GatewayInstanceArn"]}, "UpdateGatewayInstanceResponse": {"type": "structure", "members": {"BridgePlacement": {"shape": "BridgePlacement", "locationName": "bridgePlacement", "documentation": "The availability of the instance to host new bridges. The bridgePlacement property can be LOCKED or AVAILABLE. If it is LOCKED, no new bridges can be deployed to this instance. If it is AVAILABLE, new bridges can be added to this instance."}, "GatewayInstanceArn": {"shape": "__string", "locationName": "gatewayInstanceArn", "documentation": "The Amazon Resource Name (ARN) of the instance."}}}, "UpdateIngressGatewayBridgeRequest": {"type": "structure", "members": {"MaxBitrate": {"shape": "__integer", "locationName": "maxBitrate", "documentation": "The maximum expected bitrate (in bps)."}, "MaxOutputs": {"shape": "__integer", "locationName": "maxOutputs", "documentation": "The maximum number of expected outputs."}}}, "UpdateMaintenance": {"type": "structure", "members": {"MaintenanceDay": {"shape": "MaintenanceDay", "locationName": "maintenanceDay", "documentation": "A day of a week when the maintenance will happen. use Monday/Tuesday/Wednesday/Thursday/Friday/Saturday/Sunday."}, "MaintenanceScheduledDate": {"shape": "__string", "locationName": "maintenanceScheduledDate", "documentation": "A scheduled date in ISO UTC format when the maintenance will happen. Use YYYY-MM-DD format. Example: 2021-01-30."}, "MaintenanceStartHour": {"shape": "__string", "locationName": "maintenanceStartHour", "documentation": "UTC time when the maintenance will happen. Use 24-hour HH:MM format. Minutes must be 00. Example: 13:00. The default value is 02:00."}}, "documentation": "Update maintenance setting for a flow"}, "VpcInterface": {"type": "structure", "members": {"Name": {"shape": "__string", "locationName": "name", "documentation": "Immutable and has to be a unique against other VpcInterfaces in this Flow."}, "NetworkInterfaceIds": {"shape": "__listOf__string", "locationName": "networkInterfaceIds", "documentation": "IDs of the network interfaces created in customer's account by MediaConnect."}, "NetworkInterfaceType": {"shape": "NetworkInterfaceType", "locationName": "networkInterfaceType", "documentation": "The type of network interface."}, "RoleArn": {"shape": "__string", "locationName": "roleArn", "documentation": "Role Arn MediaConnect can assumes to create ENIs in customer's account"}, "SecurityGroupIds": {"shape": "__listOf__string", "locationName": "securityGroupIds", "documentation": "Security Group IDs to be used on ENI."}, "SubnetId": {"shape": "__string", "locationName": "subnetId", "documentation": "Subnet must be in the AZ of the Flow"}}, "documentation": "The settings for a VPC Source.", "required": ["NetworkInterfaceType", "NetworkInterfaceIds", "SubnetId", "SecurityGroupIds", "RoleArn", "Name"]}, "VpcInterfaceAttachment": {"type": "structure", "members": {"VpcInterfaceName": {"shape": "__string", "locationName": "vpcInterfaceName", "documentation": "The name of the VPC interface to use for this resource."}}, "documentation": "The settings for attaching a VPC interface to an resource."}, "VpcInterfaceRequest": {"type": "structure", "members": {"Name": {"shape": "__string", "locationName": "name", "documentation": "The name of the VPC Interface. This value must be unique within the current flow."}, "NetworkInterfaceType": {"shape": "NetworkInterfaceType", "locationName": "networkInterfaceType", "documentation": "The type of network interface. If this value is not included in the request, MediaConnect uses ENA as the networkInterfaceType."}, "RoleArn": {"shape": "__string", "locationName": "roleArn", "documentation": "Role Arn MediaConnect can assumes to create ENIs in customer's account"}, "SecurityGroupIds": {"shape": "__listOf__string", "locationName": "securityGroupIds", "documentation": "Security Group IDs to be used on ENI."}, "SubnetId": {"shape": "__string", "locationName": "subnetId", "documentation": "Subnet must be in the AZ of the Flow"}}, "documentation": "Desired VPC Interface for a Flow", "required": ["SubnetId", "SecurityGroupIds", "RoleArn", "Name"]}, "__boolean": {"type": "boolean"}, "__double": {"type": "double"}, "__integer": {"type": "integer"}, "__listOfAddBridgeOutputRequest": {"type": "list", "member": {"shape": "AddBridgeOutputRequest"}}, "__listOfAddBridgeSourceRequest": {"type": "list", "member": {"shape": "AddBridgeSourceRequest"}}, "__listOfAddMediaStreamRequest": {"type": "list", "member": {"shape": "AddMediaStreamRequest"}}, "__listOfAddOutputRequest": {"type": "list", "member": {"shape": "AddOutputRequest"}}, "__listOfBridgeOutput": {"type": "list", "member": {"shape": "BridgeOutput"}}, "__listOfBridgeSource": {"type": "list", "member": {"shape": "BridgeSource"}}, "__listOfDestinationConfiguration": {"type": "list", "member": {"shape": "DestinationConfiguration"}}, "__listOfDestinationConfigurationRequest": {"type": "list", "member": {"shape": "DestinationConfigurationRequest"}}, "__listOfEntitlement": {"type": "list", "member": {"shape": "Entitlement"}}, "__listOfGatewayNetwork": {"type": "list", "member": {"shape": "GatewayNetwork"}}, "__listOfGrantEntitlementRequest": {"type": "list", "member": {"shape": "GrantEntitlementRequest"}}, "__listOfInputConfiguration": {"type": "list", "member": {"shape": "InputConfiguration"}}, "__listOfInputConfigurationRequest": {"type": "list", "member": {"shape": "InputConfigurationRequest"}}, "__listOfListedBridge": {"type": "list", "member": {"shape": "ListedBridge"}}, "__listOfListedEntitlement": {"type": "list", "member": {"shape": "ListedEntitlement"}}, "__listOfListedFlow": {"type": "list", "member": {"shape": "ListedFlow"}}, "__listOfListedGateway": {"type": "list", "member": {"shape": "ListedGateway"}}, "__listOfListedGatewayInstance": {"type": "list", "member": {"shape": "ListedGatewayInstance"}}, "__listOfMediaStream": {"type": "list", "member": {"shape": "MediaStream"}}, "__listOfMediaStreamOutputConfiguration": {"type": "list", "member": {"shape": "MediaStreamOutputConfiguration"}}, "__listOfMediaStreamOutputConfigurationRequest": {"type": "list", "member": {"shape": "MediaStreamOutputConfigurationRequest"}}, "__listOfMediaStreamSourceConfiguration": {"type": "list", "member": {"shape": "MediaStreamSourceConfiguration"}}, "__listOfMediaStreamSourceConfigurationRequest": {"type": "list", "member": {"shape": "MediaStreamSourceConfigurationRequest"}}, "__listOfMessageDetail": {"type": "list", "member": {"shape": "MessageDetail"}}, "__listOfOffering": {"type": "list", "member": {"shape": "Offering"}}, "__listOfOutput": {"type": "list", "member": {"shape": "Output"}}, "__listOfReservation": {"type": "list", "member": {"shape": "Reservation"}}, "__listOfSetSourceRequest": {"type": "list", "member": {"shape": "SetSourceRequest"}}, "__listOfSource": {"type": "list", "member": {"shape": "Source"}}, "__listOfVpcInterface": {"type": "list", "member": {"shape": "VpcInterface"}}, "__listOfVpcInterfaceRequest": {"type": "list", "member": {"shape": "VpcInterfaceRequest"}}, "__listOf__integer": {"type": "list", "member": {"shape": "__integer"}}, "__listOf__string": {"type": "list", "member": {"shape": "__string"}}, "__long": {"type": "long"}, "__mapOf__string": {"type": "map", "key": {"shape": "__string"}, "value": {"shape": "__string"}}, "__string": {"type": "string"}, "__timestampIso8601": {"type": "timestamp", "timestampFormat": "iso8601"}, "__timestampUnix": {"type": "timestamp", "timestampFormat": "unixTimestamp"}}, "documentation": "API for AWS Elemental MediaConnect"}