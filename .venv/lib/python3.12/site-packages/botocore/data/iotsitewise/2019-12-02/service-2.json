{"version": "2.0", "metadata": {"apiVersion": "2019-12-02", "endpointPrefix": "iotsitewise", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "AWS IoT SiteWise", "serviceId": "IoTSiteWise", "signatureVersion": "v4", "signingName": "iotsitewise", "uid": "iotsitewise-2019-12-02"}, "operations": {"AssociateAssets": {"name": "AssociateAssets", "http": {"method": "POST", "requestUri": "/assets/{assetId}/associate"}, "input": {"shape": "AssociateAssetsRequest"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "LimitExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictingOperationException"}], "documentation": "<p>Associates a child asset with the given parent asset through a hierarchy defined in the parent asset's model. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/add-associated-assets.html\">Associating assets</a> in the <i>IoT SiteWise User Guide</i>.</p>", "endpoint": {"hostPrefix": "api."}}, "AssociateTimeSeriesToAssetProperty": {"name": "AssociateTimeSeriesToAssetProperty", "http": {"method": "POST", "requestUri": "/timeseries/associate/"}, "input": {"shape": "AssociateTimeSeriesToAssetPropertyRequest"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictingOperationException"}], "documentation": "<p>Associates a time series (data stream) with an asset property.</p>", "endpoint": {"hostPrefix": "api."}}, "BatchAssociateProjectAssets": {"name": "BatchAssociateProjectAssets", "http": {"method": "POST", "requestUri": "/projects/{projectId}/assets/associate", "responseCode": 200}, "input": {"shape": "BatchAssociateProjectAssetsRequest"}, "output": {"shape": "BatchAssociateProjectAssetsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Associates a group (batch) of assets with an IoT SiteWise Monitor project.</p>", "endpoint": {"hostPrefix": "monitor."}}, "BatchDisassociateProjectAssets": {"name": "BatchDisassociateProjectAssets", "http": {"method": "POST", "requestUri": "/projects/{projectId}/assets/disassociate", "responseCode": 200}, "input": {"shape": "BatchDisassociateProjectAssetsRequest"}, "output": {"shape": "BatchDisassociateProjectAssetsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Disassociates a group (batch) of assets from an IoT SiteWise Monitor project.</p>", "endpoint": {"hostPrefix": "monitor."}}, "BatchGetAssetPropertyAggregates": {"name": "BatchGetAssetPropertyAggregates", "http": {"method": "POST", "requestUri": "/properties/batch/aggregates"}, "input": {"shape": "BatchGetAssetPropertyAggregatesRequest"}, "output": {"shape": "BatchGetAssetPropertyAggregatesResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Gets aggregated values (for example, average, minimum, and maximum) for one or more asset properties. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/query-industrial-data.html#aggregates\">Querying aggregates</a> in the <i>IoT SiteWise User Guide</i>.</p>", "endpoint": {"hostPrefix": "data."}}, "BatchGetAssetPropertyValue": {"name": "BatchGetAssetPropertyValue", "http": {"method": "POST", "requestUri": "/properties/batch/latest"}, "input": {"shape": "BatchGetAssetPropertyValueRequest"}, "output": {"shape": "BatchGetAssetPropertyValueResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Gets the current value for one or more asset properties. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/query-industrial-data.html#current-values\">Querying current values</a> in the <i>IoT SiteWise User Guide</i>.</p>", "endpoint": {"hostPrefix": "data."}}, "BatchGetAssetPropertyValueHistory": {"name": "BatchGetAssetPropertyValueHistory", "http": {"method": "POST", "requestUri": "/properties/batch/history"}, "input": {"shape": "BatchGetAssetPropertyValueHistoryRequest"}, "output": {"shape": "BatchGetAssetPropertyValueHistoryResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Gets the historical values for one or more asset properties. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/query-industrial-data.html#historical-values\">Querying historical values</a> in the <i>IoT SiteWise User Guide</i>.</p>", "endpoint": {"hostPrefix": "data."}}, "BatchPutAssetPropertyValue": {"name": "BatchPutAssetPropertyValue", "http": {"method": "POST", "requestUri": "/properties"}, "input": {"shape": "BatchPutAssetPropertyValueRequest"}, "output": {"shape": "BatchPutAssetPropertyValueResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ConflictingOperationException"}], "documentation": "<p>Sends a list of asset property values to IoT SiteWise. Each value is a timestamp-quality-value (TQV) data point. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/ingest-api.html\">Ingesting data using the API</a> in the <i>IoT SiteWise User Guide</i>.</p> <p>To identify an asset property, you must specify one of the following:</p> <ul> <li> <p>The <code>assetId</code> and <code>propertyId</code> of an asset property.</p> </li> <li> <p>A <code>propertyAlias</code>, which is a data stream alias (for example, <code>/company/windfarm/3/turbine/7/temperature</code>). To define an asset property's alias, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_UpdateAssetProperty.html\">UpdateAssetProperty</a>.</p> </li> </ul> <important> <p>With respect to Unix epoch time, IoT SiteWise accepts only TQVs that have a timestamp of no more than 7 days in the past and no more than 10 minutes in the future. IoT SiteWise rejects timestamps outside of the inclusive range of [-7 days, +10 minutes] and returns a <code>TimestampOutOfRangeException</code> error.</p> <p>For each asset property, IoT SiteWise overwrites TQVs with duplicate timestamps unless the newer TQV has a different quality. For example, if you store a TQV <code>{T1, GOOD, V1}</code>, then storing <code>{T1, GOOD, V2}</code> replaces the existing TQV.</p> </important> <p>IoT SiteWise authorizes access to each <code>BatchPutAssetPropertyValue</code> entry individually. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/security_iam_service-with-iam.html#security_iam_service-with-iam-id-based-policies-batchputassetpropertyvalue-action\">BatchPutAssetPropertyValue authorization</a> in the <i>IoT SiteWise User Guide</i>.</p>", "endpoint": {"hostPrefix": "data."}}, "CreateAccessPolicy": {"name": "CreateAccessPolicy", "http": {"method": "POST", "requestUri": "/access-policies", "responseCode": 201}, "input": {"shape": "CreateAccessPolicyRequest"}, "output": {"shape": "CreateAccessPolicyResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates an access policy that grants the specified identity (IAM Identity Center user, IAM Identity Center group, or IAM user) access to the specified IoT SiteWise Monitor portal or project resource.</p>", "endpoint": {"hostPrefix": "monitor."}}, "CreateAsset": {"name": "CreateAsset", "http": {"method": "POST", "requestUri": "/assets", "responseCode": 202}, "input": {"shape": "CreateAssetRequest"}, "output": {"shape": "CreateAssetResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictingOperationException"}], "documentation": "<p>Creates an asset from an existing asset model. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/create-assets.html\">Creating assets</a> in the <i>IoT SiteWise User Guide</i>.</p>", "endpoint": {"hostPrefix": "api."}}, "CreateAssetModel": {"name": "CreateAssetModel", "http": {"method": "POST", "requestUri": "/asset-models", "responseCode": 202}, "input": {"shape": "CreateAssetModelRequest"}, "output": {"shape": "CreateAssetModelResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictingOperationException"}], "documentation": "<p>Creates an asset model from specified property and hierarchy definitions. You create assets from asset models. With asset models, you can easily create assets of the same type that have standardized definitions. Each asset created from a model inherits the asset model's property and hierarchy definitions. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/define-models.html\">Defining asset models</a> in the <i>IoT SiteWise User Guide</i>.</p>", "endpoint": {"hostPrefix": "api."}}, "CreateBulkImportJob": {"name": "CreateBulkImportJob", "http": {"method": "POST", "requestUri": "/jobs", "responseCode": 202}, "input": {"shape": "CreateBulkImportJobRequest"}, "output": {"shape": "CreateBulkImportJobResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictingOperationException"}], "documentation": "<p>Defines a job to ingest data to IoT SiteWise from Amazon S3. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/CreateBulkImportJob.html\">Create a bulk import job (CLI)</a> in the <i>Amazon Simple Storage Service User Guide</i>.</p> <important> <p>You must enable IoT SiteWise to export data to Amazon S3 before you create a bulk import job. For more information about how to configure storage settings, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_PutStorageConfiguration.html\">PutStorageConfiguration</a>.</p> </important>", "endpoint": {"hostPrefix": "data."}}, "CreateDashboard": {"name": "CreateDashboard", "http": {"method": "POST", "requestUri": "/dashboards", "responseCode": 201}, "input": {"shape": "CreateDashboardRequest"}, "output": {"shape": "CreateDashboardResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates a dashboard in an IoT SiteWise Monitor project.</p>", "endpoint": {"hostPrefix": "monitor."}}, "CreateGateway": {"name": "CreateGateway", "http": {"method": "POST", "requestUri": "/********/gateways", "responseCode": 201}, "input": {"shape": "CreateGatewayRequest"}, "output": {"shape": "CreateGatewayResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates a gateway, which is a virtual or edge device that delivers industrial data streams from local servers to IoT SiteWise. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/gateway-connector.html\">Ingesting data using a gateway</a> in the <i>IoT SiteWise User Guide</i>.</p>", "endpoint": {"hostPrefix": "api."}}, "CreatePortal": {"name": "CreatePortal", "http": {"method": "POST", "requestUri": "/portals", "responseCode": 202}, "input": {"shape": "CreatePortalRequest"}, "output": {"shape": "CreatePortalResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates a portal, which can contain projects and dashboards. IoT SiteWise Monitor uses IAM Identity Center or IAM to authenticate portal users and manage user permissions.</p> <note> <p>Before you can sign in to a new portal, you must add at least one identity to that portal. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/administer-portals.html#portal-change-admins\">Adding or removing portal administrators</a> in the <i>IoT SiteWise User Guide</i>.</p> </note>", "endpoint": {"hostPrefix": "monitor."}}, "CreateProject": {"name": "CreateProject", "http": {"method": "POST", "requestUri": "/projects", "responseCode": 201}, "input": {"shape": "CreateProjectRequest"}, "output": {"shape": "CreateProjectResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates a project in the specified portal.</p> <note> <p>Make sure that the project name and description don't contain confidential information.</p> </note>", "endpoint": {"hostPrefix": "monitor."}}, "DeleteAccessPolicy": {"name": "DeleteAccessPolicy", "http": {"method": "DELETE", "requestUri": "/access-policies/{accessPolicyId}", "responseCode": 204}, "input": {"shape": "DeleteAccessPolicyRequest"}, "output": {"shape": "DeleteAccessPolicyResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes an access policy that grants the specified identity access to the specified IoT SiteWise Monitor resource. You can use this operation to revoke access to an IoT SiteWise Monitor resource.</p>", "endpoint": {"hostPrefix": "monitor."}}, "DeleteAsset": {"name": "DeleteAsset", "http": {"method": "DELETE", "requestUri": "/assets/{assetId}", "responseCode": 202}, "input": {"shape": "DeleteAssetRequest"}, "output": {"shape": "DeleteAssetResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictingOperationException"}], "documentation": "<p>Deletes an asset. This action can't be undone. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/delete-assets-and-models.html\">Deleting assets and models</a> in the <i>IoT SiteWise User Guide</i>. </p> <note> <p>You can't delete an asset that's associated to another asset. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_DisassociateAssets.html\">DisassociateAssets</a>.</p> </note>", "endpoint": {"hostPrefix": "api."}}, "DeleteAssetModel": {"name": "DeleteAssetModel", "http": {"method": "DELETE", "requestUri": "/asset-models/{assetModelId}", "responseCode": 202}, "input": {"shape": "DeleteAssetModelRequest"}, "output": {"shape": "DeleteAssetModelResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictingOperationException"}], "documentation": "<p>Deletes an asset model. This action can't be undone. You must delete all assets created from an asset model before you can delete the model. Also, you can't delete an asset model if a parent asset model exists that contains a property formula expression that depends on the asset model that you want to delete. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/delete-assets-and-models.html\">Deleting assets and models</a> in the <i>IoT SiteWise User Guide</i>.</p>", "endpoint": {"hostPrefix": "api."}}, "DeleteDashboard": {"name": "DeleteDashboard", "http": {"method": "DELETE", "requestUri": "/dashboards/{dashboardId}", "responseCode": 204}, "input": {"shape": "DeleteDashboardRequest"}, "output": {"shape": "DeleteDashboardResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes a dashboard from IoT SiteWise Monitor.</p>", "endpoint": {"hostPrefix": "monitor."}}, "DeleteGateway": {"name": "DeleteGateway", "http": {"method": "DELETE", "requestUri": "/********/gateways/{gatewayId}"}, "input": {"shape": "DeleteGatewayRequest"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes a gateway from IoT SiteWise. When you delete a gateway, some of the gateway's files remain in your gateway's file system.</p>", "endpoint": {"hostPrefix": "api."}}, "DeletePortal": {"name": "DeletePortal", "http": {"method": "DELETE", "requestUri": "/portals/{portalId}", "responseCode": 202}, "input": {"shape": "DeletePortalRequest"}, "output": {"shape": "DeletePortalResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictingOperationException"}], "documentation": "<p>Deletes a portal from IoT SiteWise Monitor.</p>", "endpoint": {"hostPrefix": "monitor."}}, "DeleteProject": {"name": "DeleteProject", "http": {"method": "DELETE", "requestUri": "/projects/{projectId}", "responseCode": 204}, "input": {"shape": "DeleteProjectRequest"}, "output": {"shape": "DeleteProjectResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes a project from IoT SiteWise Monitor.</p>", "endpoint": {"hostPrefix": "monitor."}}, "DeleteTimeSeries": {"name": "DeleteTimeSeries", "http": {"method": "POST", "requestUri": "/timeseries/delete/"}, "input": {"shape": "DeleteTimeSeriesRequest"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictingOperationException"}], "documentation": "<p>Deletes a time series (data stream). If you delete a time series that's associated with an asset property, the asset property still exists, but the time series will no longer be associated with this asset property.</p> <p>To identify a time series, do one of the following:</p> <ul> <li> <p>If the time series isn't associated with an asset property, specify the <code>alias</code> of the time series.</p> </li> <li> <p>If the time series is associated with an asset property, specify one of the following: </p> <ul> <li> <p>The <code>alias</code> of the time series.</p> </li> <li> <p>The <code>assetId</code> and <code>propertyId</code> that identifies the asset property.</p> </li> </ul> </li> </ul>", "endpoint": {"hostPrefix": "api."}}, "DescribeAccessPolicy": {"name": "DescribeAccessPolicy", "http": {"method": "GET", "requestUri": "/access-policies/{accessPolicyId}", "responseCode": 200}, "input": {"shape": "DescribeAccessPolicyRequest"}, "output": {"shape": "DescribeAccessPolicyResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Describes an access policy, which specifies an identity's access to an IoT SiteWise Monitor portal or project.</p>", "endpoint": {"hostPrefix": "monitor."}}, "DescribeAsset": {"name": "DescribeAsset", "http": {"method": "GET", "requestUri": "/assets/{assetId}"}, "input": {"shape": "DescribeAssetRequest"}, "output": {"shape": "DescribeAssetResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves information about an asset.</p>", "endpoint": {"hostPrefix": "api."}}, "DescribeAssetModel": {"name": "DescribeAssetModel", "http": {"method": "GET", "requestUri": "/asset-models/{assetModelId}"}, "input": {"shape": "DescribeAssetModelRequest"}, "output": {"shape": "DescribeAssetModelResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves information about an asset model.</p>", "endpoint": {"hostPrefix": "api."}}, "DescribeAssetProperty": {"name": "DescribeAssetProperty", "http": {"method": "GET", "requestUri": "/assets/{assetId}/properties/{propertyId}"}, "input": {"shape": "DescribeAssetPropertyRequest"}, "output": {"shape": "DescribeAssetPropertyResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves information about an asset property.</p> <note> <p>When you call this operation for an attribute property, this response includes the default attribute value that you define in the asset model. If you update the default value in the model, this operation's response includes the new default value.</p> </note> <p>This operation doesn't return the value of the asset property. To get the value of an asset property, use <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_GetAssetPropertyValue.html\">GetAssetPropertyValue</a>.</p>", "endpoint": {"hostPrefix": "api."}}, "DescribeBulkImportJob": {"name": "DescribeBulkImportJob", "http": {"method": "GET", "requestUri": "/jobs/{jobId}"}, "input": {"shape": "DescribeBulkImportJobRequest"}, "output": {"shape": "DescribeBulkImportJobResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves information about a bulk import job request. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/DescribeBulkImportJob.html\">Describe a bulk import job (CLI)</a> in the <i>Amazon Simple Storage Service User Guide</i>.</p>", "endpoint": {"hostPrefix": "data."}}, "DescribeDashboard": {"name": "DescribeDashboard", "http": {"method": "GET", "requestUri": "/dashboards/{dashboardId}", "responseCode": 200}, "input": {"shape": "DescribeDashboardRequest"}, "output": {"shape": "DescribeDashboardResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves information about a dashboard.</p>", "endpoint": {"hostPrefix": "monitor."}}, "DescribeDefaultEncryptionConfiguration": {"name": "DescribeDefaultEncryptionConfiguration", "http": {"method": "GET", "requestUri": "/configuration/account/encryption"}, "input": {"shape": "DescribeDefaultEncryptionConfigurationRequest"}, "output": {"shape": "DescribeDefaultEncryptionConfigurationResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves information about the default encryption configuration for the Amazon Web Services account in the default or specified Region. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/key-management.html\">Key management</a> in the <i>IoT SiteWise User Guide</i>.</p>", "endpoint": {"hostPrefix": "api."}}, "DescribeGateway": {"name": "DescribeGateway", "http": {"method": "GET", "requestUri": "/********/gateways/{gatewayId}"}, "input": {"shape": "DescribeGatewayRequest"}, "output": {"shape": "DescribeGatewayResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves information about a gateway.</p>", "endpoint": {"hostPrefix": "api."}}, "DescribeGatewayCapabilityConfiguration": {"name": "DescribeGatewayCapabilityConfiguration", "http": {"method": "GET", "requestUri": "/********/gateways/{gatewayId}/capability/{capabilityNamespace}"}, "input": {"shape": "DescribeGatewayCapabilityConfigurationRequest"}, "output": {"shape": "DescribeGatewayCapabilityConfigurationResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves information about a gateway capability configuration. Each gateway capability defines data sources for a gateway. A capability configuration can contain multiple data source configurations. If you define OPC-UA sources for a gateway in the IoT SiteWise console, all of your OPC-UA sources are stored in one capability configuration. To list all capability configurations for a gateway, use <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_DescribeGateway.html\">DescribeGateway</a>.</p>", "endpoint": {"hostPrefix": "api."}}, "DescribeLoggingOptions": {"name": "DescribeLoggingOptions", "http": {"method": "GET", "requestUri": "/logging"}, "input": {"shape": "DescribeLoggingOptionsRequest"}, "output": {"shape": "DescribeLoggingOptionsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottlingException"}, {"shape": "InternalFailureException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves the current IoT SiteWise logging options.</p>", "endpoint": {"hostPrefix": "api."}}, "DescribePortal": {"name": "DescribePortal", "http": {"method": "GET", "requestUri": "/portals/{portalId}", "responseCode": 200}, "input": {"shape": "DescribePortalRequest"}, "output": {"shape": "DescribePortalResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves information about a portal.</p>", "endpoint": {"hostPrefix": "monitor."}}, "DescribeProject": {"name": "DescribeProject", "http": {"method": "GET", "requestUri": "/projects/{projectId}", "responseCode": 200}, "input": {"shape": "DescribeProjectRequest"}, "output": {"shape": "DescribeProjectResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves information about a project.</p>", "endpoint": {"hostPrefix": "monitor."}}, "DescribeStorageConfiguration": {"name": "DescribeStorageConfiguration", "http": {"method": "GET", "requestUri": "/configuration/account/storage"}, "input": {"shape": "DescribeStorageConfigurationRequest"}, "output": {"shape": "DescribeStorageConfigurationResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictingOperationException"}], "documentation": "<p>Retrieves information about the storage configuration for IoT SiteWise.</p>", "endpoint": {"hostPrefix": "api."}}, "DescribeTimeSeries": {"name": "DescribeTimeSeries", "http": {"method": "GET", "requestUri": "/timeseries/describe/"}, "input": {"shape": "DescribeTimeSeriesRequest"}, "output": {"shape": "DescribeTimeSeriesResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves information about a time series (data stream).</p> <p>To identify a time series, do one of the following:</p> <ul> <li> <p>If the time series isn't associated with an asset property, specify the <code>alias</code> of the time series.</p> </li> <li> <p>If the time series is associated with an asset property, specify one of the following: </p> <ul> <li> <p>The <code>alias</code> of the time series.</p> </li> <li> <p>The <code>assetId</code> and <code>propertyId</code> that identifies the asset property.</p> </li> </ul> </li> </ul>", "endpoint": {"hostPrefix": "api."}}, "DisassociateAssets": {"name": "DisassociateAssets", "http": {"method": "POST", "requestUri": "/assets/{assetId}/disassociate"}, "input": {"shape": "DisassociateAssetsRequest"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictingOperationException"}], "documentation": "<p>Disassociates a child asset from the given parent asset through a hierarchy defined in the parent asset's model.</p>", "endpoint": {"hostPrefix": "api."}}, "DisassociateTimeSeriesFromAssetProperty": {"name": "DisassociateTimeSeriesFromAssetProperty", "http": {"method": "POST", "requestUri": "/timeseries/disassociate/"}, "input": {"shape": "DisassociateTimeSeriesFromAssetPropertyRequest"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictingOperationException"}], "documentation": "<p>Disassociates a time series (data stream) from an asset property.</p>", "endpoint": {"hostPrefix": "api."}}, "GetAssetPropertyAggregates": {"name": "GetAssetPropertyAggregates", "http": {"method": "GET", "requestUri": "/properties/aggregates"}, "input": {"shape": "GetAssetPropertyAggregatesRequest"}, "output": {"shape": "GetAssetPropertyAggregatesResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Gets aggregated values for an asset property. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/query-industrial-data.html#aggregates\">Querying aggregates</a> in the <i>IoT SiteWise User Guide</i>.</p> <p>To identify an asset property, you must specify one of the following:</p> <ul> <li> <p>The <code>assetId</code> and <code>propertyId</code> of an asset property.</p> </li> <li> <p>A <code>propertyAlias</code>, which is a data stream alias (for example, <code>/company/windfarm/3/turbine/7/temperature</code>). To define an asset property's alias, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_UpdateAssetProperty.html\">UpdateAssetProperty</a>.</p> </li> </ul>", "endpoint": {"hostPrefix": "data."}}, "GetAssetPropertyValue": {"name": "GetAssetPropertyValue", "http": {"method": "GET", "requestUri": "/properties/latest"}, "input": {"shape": "GetAssetPropertyValueRequest"}, "output": {"shape": "GetAssetPropertyValueResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Gets an asset property's current value. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/query-industrial-data.html#current-values\">Querying current values</a> in the <i>IoT SiteWise User Guide</i>.</p> <p>To identify an asset property, you must specify one of the following:</p> <ul> <li> <p>The <code>assetId</code> and <code>propertyId</code> of an asset property.</p> </li> <li> <p>A <code>propertyAlias</code>, which is a data stream alias (for example, <code>/company/windfarm/3/turbine/7/temperature</code>). To define an asset property's alias, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_UpdateAssetProperty.html\">UpdateAssetProperty</a>.</p> </li> </ul>", "endpoint": {"hostPrefix": "data."}}, "GetAssetPropertyValueHistory": {"name": "GetAssetPropertyValueHistory", "http": {"method": "GET", "requestUri": "/properties/history"}, "input": {"shape": "GetAssetPropertyValueHistoryRequest"}, "output": {"shape": "GetAssetPropertyValueHistoryResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Gets the history of an asset property's values. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/query-industrial-data.html#historical-values\">Querying historical values</a> in the <i>IoT SiteWise User Guide</i>.</p> <p>To identify an asset property, you must specify one of the following:</p> <ul> <li> <p>The <code>assetId</code> and <code>propertyId</code> of an asset property.</p> </li> <li> <p>A <code>propertyAlias</code>, which is a data stream alias (for example, <code>/company/windfarm/3/turbine/7/temperature</code>). To define an asset property's alias, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_UpdateAssetProperty.html\">UpdateAssetProperty</a>.</p> </li> </ul>", "endpoint": {"hostPrefix": "data."}}, "GetInterpolatedAssetPropertyValues": {"name": "GetInterpolatedAssetPropertyValues", "http": {"method": "GET", "requestUri": "/properties/interpolated"}, "input": {"shape": "GetInterpolatedAssetPropertyValuesRequest"}, "output": {"shape": "GetInterpolatedAssetPropertyValuesResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Get interpolated values for an asset property for a specified time interval, during a period of time. If your time series is missing data points during the specified time interval, you can use interpolation to estimate the missing data.</p> <p>For example, you can use this operation to return the interpolated temperature values for a wind turbine every 24 hours over a duration of 7 days.</p> <p>To identify an asset property, you must specify one of the following:</p> <ul> <li> <p>The <code>assetId</code> and <code>propertyId</code> of an asset property.</p> </li> <li> <p>A <code>propertyAlias</code>, which is a data stream alias (for example, <code>/company/windfarm/3/turbine/7/temperature</code>). To define an asset property's alias, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_UpdateAssetProperty.html\">UpdateAssetProperty</a>.</p> </li> </ul>", "endpoint": {"hostPrefix": "data."}}, "ListAccessPolicies": {"name": "ListAccessPolicies", "http": {"method": "GET", "requestUri": "/access-policies", "responseCode": 200}, "input": {"shape": "ListAccessPoliciesRequest"}, "output": {"shape": "ListAccessPoliciesResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves a paginated list of access policies for an identity (an IAM Identity Center user, an IAM Identity Center group, or an IAM user) or an IoT SiteWise Monitor resource (a portal or project).</p>", "endpoint": {"hostPrefix": "monitor."}}, "ListAssetModelProperties": {"name": "ListAssetModelProperties", "http": {"method": "GET", "requestUri": "/asset-models/{assetModelId}/properties"}, "input": {"shape": "ListAssetModelPropertiesRequest"}, "output": {"shape": "ListAssetModelPropertiesResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves a paginated list of properties associated with an asset model. If you update properties associated with the model before you finish listing all the properties, you need to start all over again.</p>", "endpoint": {"hostPrefix": "api."}}, "ListAssetModels": {"name": "ListAssetModels", "http": {"method": "GET", "requestUri": "/asset-models"}, "input": {"shape": "ListAssetModelsRequest"}, "output": {"shape": "ListAssetModelsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves a paginated list of summaries of all asset models.</p>", "endpoint": {"hostPrefix": "api."}}, "ListAssetProperties": {"name": "ListAssetProperties", "http": {"method": "GET", "requestUri": "/assets/{assetId}/properties"}, "input": {"shape": "ListAssetPropertiesRequest"}, "output": {"shape": "ListAssetPropertiesResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves a paginated list of properties associated with an asset. If you update properties associated with the model before you finish listing all the properties, you need to start all over again.</p>", "endpoint": {"hostPrefix": "api."}}, "ListAssetRelationships": {"name": "ListAssetRelationships", "http": {"method": "GET", "requestUri": "/assets/{assetId}/assetRelationships"}, "input": {"shape": "ListAssetRelationshipsRequest"}, "output": {"shape": "ListAssetRelationshipsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves a paginated list of asset relationships for an asset. You can use this operation to identify an asset's root asset and all associated assets between that asset and its root.</p>", "endpoint": {"hostPrefix": "api."}}, "ListAssets": {"name": "ListAssets", "http": {"method": "GET", "requestUri": "/assets"}, "input": {"shape": "ListAssetsRequest"}, "output": {"shape": "ListAssetsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves a paginated list of asset summaries.</p> <p>You can use this operation to do the following:</p> <ul> <li> <p>List assets based on a specific asset model.</p> </li> <li> <p>List top-level assets.</p> </li> </ul> <p>You can't use this operation to list all assets. To retrieve summaries for all of your assets, use <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_ListAssetModels.html\">ListAssetModels</a> to get all of your asset model IDs. Then, use ListAssets to get all assets for each asset model.</p>", "endpoint": {"hostPrefix": "api."}}, "ListAssociatedAssets": {"name": "ListAssociatedAssets", "http": {"method": "GET", "requestUri": "/assets/{assetId}/hierarchies"}, "input": {"shape": "ListAssociatedAssetsRequest"}, "output": {"shape": "ListAssociatedAssetsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves a paginated list of associated assets.</p> <p>You can use this operation to do the following:</p> <ul> <li> <p>List child assets associated to a parent asset by a hierarchy that you specify.</p> </li> <li> <p>List an asset's parent asset.</p> </li> </ul>", "endpoint": {"hostPrefix": "api."}}, "ListBulkImportJobs": {"name": "ListBulkImportJobs", "http": {"method": "GET", "requestUri": "/jobs"}, "input": {"shape": "ListBulkImportJobsRequest"}, "output": {"shape": "ListBulkImportJobsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves a paginated list of bulk import job requests. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/ListBulkImportJobs.html\">List bulk import jobs (CLI)</a> in the <i>IoT SiteWise User Guide</i>.</p>", "endpoint": {"hostPrefix": "data."}}, "ListDashboards": {"name": "ListDashboards", "http": {"method": "GET", "requestUri": "/dashboards", "responseCode": 200}, "input": {"shape": "ListDashboardsRequest"}, "output": {"shape": "ListDashboardsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves a paginated list of dashboards for an IoT SiteWise Monitor project.</p>", "endpoint": {"hostPrefix": "monitor."}}, "ListGateways": {"name": "ListGateways", "http": {"method": "GET", "requestUri": "/********/gateways"}, "input": {"shape": "ListGatewaysRequest"}, "output": {"shape": "ListGatewaysResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves a paginated list of gateways.</p>", "endpoint": {"hostPrefix": "api."}}, "ListPortals": {"name": "ListPortals", "http": {"method": "GET", "requestUri": "/portals", "responseCode": 200}, "input": {"shape": "ListPortalsRequest"}, "output": {"shape": "ListPortalsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves a paginated list of IoT SiteWise Monitor portals.</p>", "endpoint": {"hostPrefix": "monitor."}}, "ListProjectAssets": {"name": "ListProjectAssets", "http": {"method": "GET", "requestUri": "/projects/{projectId}/assets", "responseCode": 200}, "input": {"shape": "ListProjectAssetsRequest"}, "output": {"shape": "ListProjectAssetsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves a paginated list of assets associated with an IoT SiteWise Monitor project.</p>", "endpoint": {"hostPrefix": "monitor."}}, "ListProjects": {"name": "ListProjects", "http": {"method": "GET", "requestUri": "/projects", "responseCode": 200}, "input": {"shape": "ListProjectsRequest"}, "output": {"shape": "ListProjectsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves a paginated list of projects for an IoT SiteWise Monitor portal.</p>", "endpoint": {"hostPrefix": "monitor."}}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictingOperationException"}, {"shape": "LimitExceededException"}, {"shape": "UnauthorizedException"}], "documentation": "<p>Retrieves the list of tags for an IoT SiteWise resource.</p>", "endpoint": {"hostPrefix": "api."}}, "ListTimeSeries": {"name": "ListTimeSeries", "http": {"method": "GET", "requestUri": "/timeseries/"}, "input": {"shape": "ListTimeSeriesRequest"}, "output": {"shape": "ListTimeSeriesResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves a paginated list of time series (data streams).</p>", "endpoint": {"hostPrefix": "api."}}, "PutDefaultEncryptionConfiguration": {"name": "PutDefaultEncryptionConfiguration", "http": {"method": "POST", "requestUri": "/configuration/account/encryption"}, "input": {"shape": "PutDefaultEncryptionConfigurationRequest"}, "output": {"shape": "PutDefaultEncryptionConfigurationResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictingOperationException"}], "documentation": "<p>Sets the default encryption configuration for the Amazon Web Services account. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/key-management.html\">Key management</a> in the <i>IoT SiteWise User Guide</i>.</p>", "endpoint": {"hostPrefix": "api."}}, "PutLoggingOptions": {"name": "PutLoggingOptions", "http": {"method": "PUT", "requestUri": "/logging"}, "input": {"shape": "PutLoggingOptionsRequest"}, "output": {"shape": "PutLoggingOptionsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottlingException"}, {"shape": "InternalFailureException"}, {"shape": "ConflictingOperationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Sets logging options for IoT SiteWise.</p>", "endpoint": {"hostPrefix": "api."}}, "PutStorageConfiguration": {"name": "PutStorageConfiguration", "http": {"method": "POST", "requestUri": "/configuration/account/storage"}, "input": {"shape": "PutStorageConfigurationRequest"}, "output": {"shape": "PutStorageConfigurationResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictingOperationException"}], "documentation": "<p>Configures storage settings for IoT SiteWise.</p>", "endpoint": {"hostPrefix": "api."}}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictingOperationException"}, {"shape": "LimitExceededException"}, {"shape": "UnauthorizedException"}, {"shape": "TooManyTagsException"}], "documentation": "<p>Adds tags to an IoT SiteWise resource. If a tag already exists for the resource, this operation updates the tag's value.</p>", "endpoint": {"hostPrefix": "api."}}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictingOperationException"}, {"shape": "LimitExceededException"}, {"shape": "UnauthorizedException"}], "documentation": "<p>Removes a tag from an IoT SiteWise resource.</p>", "endpoint": {"hostPrefix": "api."}}, "UpdateAccessPolicy": {"name": "UpdateAccessPolicy", "http": {"method": "PUT", "requestUri": "/access-policies/{accessPolicyId}", "responseCode": 200}, "input": {"shape": "UpdateAccessPolicyRequest"}, "output": {"shape": "UpdateAccessPolicyResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Updates an existing access policy that specifies an identity's access to an IoT SiteWise Monitor portal or project resource.</p>", "endpoint": {"hostPrefix": "monitor."}}, "UpdateAsset": {"name": "UpdateAsset", "http": {"method": "PUT", "requestUri": "/assets/{assetId}", "responseCode": 202}, "input": {"shape": "UpdateAssetRequest"}, "output": {"shape": "UpdateAssetResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictingOperationException"}], "documentation": "<p>Updates an asset's name. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/update-assets-and-models.html\">Updating assets and models</a> in the <i>IoT SiteWise User Guide</i>.</p>", "endpoint": {"hostPrefix": "api."}}, "UpdateAssetModel": {"name": "UpdateAssetModel", "http": {"method": "PUT", "requestUri": "/asset-models/{assetModelId}", "responseCode": 202}, "input": {"shape": "UpdateAssetModelRequest"}, "output": {"shape": "UpdateAssetModelResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "LimitExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictingOperationException"}], "documentation": "<p>Updates an asset model and all of the assets that were created from the model. Each asset created from the model inherits the updated asset model's property and hierarchy definitions. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/update-assets-and-models.html\">Updating assets and models</a> in the <i>IoT SiteWise User Guide</i>.</p> <important> <p>This operation overwrites the existing model with the provided model. To avoid deleting your asset model's properties or hierarchies, you must include their IDs and definitions in the updated asset model payload. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_DescribeAssetModel.html\">DescribeAssetModel</a>.</p> <p>If you remove a property from an asset model, IoT SiteWise deletes all previous data for that property. If you remove a hierarchy definition from an asset model, IoT SiteWise disassociates every asset associated with that hierarchy. You can't change the type or data type of an existing property.</p> </important>", "endpoint": {"hostPrefix": "api."}}, "UpdateAssetProperty": {"name": "UpdateAssetProperty", "http": {"method": "PUT", "requestUri": "/assets/{assetId}/properties/{propertyId}"}, "input": {"shape": "UpdateAssetPropertyRequest"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictingOperationException"}], "documentation": "<p>Updates an asset property's alias and notification state.</p> <important> <p>This operation overwrites the property's existing alias and notification state. To keep your existing property's alias or notification state, you must include the existing values in the UpdateAssetProperty request. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_DescribeAssetProperty.html\">DescribeAssetProperty</a>.</p> </important>", "endpoint": {"hostPrefix": "api."}}, "UpdateDashboard": {"name": "UpdateDashboard", "http": {"method": "PUT", "requestUri": "/dashboards/{dashboardId}", "responseCode": 200}, "input": {"shape": "UpdateDashboardRequest"}, "output": {"shape": "UpdateDashboardResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Updates an IoT SiteWise Monitor dashboard.</p>", "endpoint": {"hostPrefix": "monitor."}}, "UpdateGateway": {"name": "UpdateGateway", "http": {"method": "PUT", "requestUri": "/********/gateways/{gatewayId}"}, "input": {"shape": "UpdateGatewayRequest"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictingOperationException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Updates a gateway's name.</p>", "endpoint": {"hostPrefix": "api."}}, "UpdateGatewayCapabilityConfiguration": {"name": "UpdateGatewayCapabilityConfiguration", "http": {"method": "POST", "requestUri": "/********/gateways/{gatewayId}/capability", "responseCode": 201}, "input": {"shape": "UpdateGatewayCapabilityConfigurationRequest"}, "output": {"shape": "UpdateGatewayCapabilityConfigurationResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictingOperationException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Updates a gateway capability configuration or defines a new capability configuration. Each gateway capability defines data sources for a gateway. A capability configuration can contain multiple data source configurations. If you define OPC-UA sources for a gateway in the IoT SiteWise console, all of your OPC-UA sources are stored in one capability configuration. To list all capability configurations for a gateway, use <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_DescribeGateway.html\">DescribeGateway</a>.</p>", "endpoint": {"hostPrefix": "api."}}, "UpdatePortal": {"name": "UpdatePortal", "http": {"method": "PUT", "requestUri": "/portals/{portalId}", "responseCode": 202}, "input": {"shape": "UpdatePortalRequest"}, "output": {"shape": "UpdatePortalResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictingOperationException"}], "documentation": "<p>Updates an IoT SiteWise Monitor portal.</p>", "endpoint": {"hostPrefix": "monitor."}}, "UpdateProject": {"name": "UpdateProject", "http": {"method": "PUT", "requestUri": "/projects/{projectId}", "responseCode": 200}, "input": {"shape": "UpdateProjectRequest"}, "output": {"shape": "UpdateProjectResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Updates an IoT SiteWise Monitor project.</p>", "endpoint": {"hostPrefix": "monitor."}}}, "shapes": {"ARN": {"type": "string", "max": 1600, "min": 1, "pattern": ".*"}, "AccessPolicySummaries": {"type": "list", "member": {"shape": "AccessPolicySummary"}}, "AccessPolicySummary": {"type": "structure", "required": ["id", "identity", "resource", "permission"], "members": {"id": {"shape": "ID", "documentation": "<p>The ID of the access policy.</p>"}, "identity": {"shape": "Identity", "documentation": "<p>The identity (an IAM Identity Center user, an IAM Identity Center group, or an IAM user).</p>"}, "resource": {"shape": "Resource", "documentation": "<p>The IoT SiteWise Monitor resource (a portal or project).</p>"}, "permission": {"shape": "Permission", "documentation": "<p>The permissions for the access policy. Note that a project <code>ADMINISTRATOR</code> is also known as a project owner.</p>"}, "creationDate": {"shape": "Timestamp", "documentation": "<p>The date the access policy was created, in Unix epoch time.</p>"}, "lastUpdateDate": {"shape": "Timestamp", "documentation": "<p>The date the access policy was last updated, in Unix epoch time.</p>"}}, "documentation": "<p>Contains an access policy that defines an identity's access to an IoT SiteWise Monitor resource.</p>"}, "AggregateType": {"type": "string", "enum": ["AVERAGE", "COUNT", "MAXIMUM", "MINIMUM", "SUM", "STANDARD_DEVIATION"]}, "AggregateTypes": {"type": "list", "member": {"shape": "AggregateType"}, "min": 1}, "AggregatedDoubleValue": {"type": "double"}, "AggregatedValue": {"type": "structure", "required": ["timestamp", "value"], "members": {"timestamp": {"shape": "Timestamp", "documentation": "<p>The date the aggregating computations occurred, in Unix epoch time.</p>"}, "quality": {"shape": "Quality", "documentation": "<p>The quality of the aggregated data.</p>"}, "value": {"shape": "Aggregates", "documentation": "<p>The value of the aggregates.</p>"}}, "documentation": "<p>Contains aggregated asset property values (for example, average, minimum, and maximum).</p>"}, "AggregatedValues": {"type": "list", "member": {"shape": "AggregatedValue"}}, "Aggregates": {"type": "structure", "members": {"average": {"shape": "AggregatedDoubleValue", "documentation": "<p>The average (mean) value of the time series over a time interval window.</p>"}, "count": {"shape": "AggregatedDoubleValue", "documentation": "<p>The count of data points in the time series over a time interval window.</p>"}, "maximum": {"shape": "AggregatedDoubleValue", "documentation": "<p>The maximum value of the time series over a time interval window.</p>"}, "minimum": {"shape": "AggregatedDoubleValue", "documentation": "<p>The minimum value of the time series over a time interval window.</p>"}, "sum": {"shape": "AggregatedDoubleValue", "documentation": "<p>The sum of the time series over a time interval window.</p>"}, "standardDeviation": {"shape": "AggregatedDoubleValue", "documentation": "<p>The standard deviation of the time series over a time interval window.</p>"}}, "documentation": "<p>Contains the (pre-calculated) aggregate values for an asset property.</p>"}, "Alarms": {"type": "structure", "required": ["alarmRoleArn"], "members": {"alarmRoleArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the IAM role that allows the alarm to perform actions and access Amazon Web Services resources and services, such as IoT Events.</p>"}, "notificationLambdaArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the Lambda function that manages alarm notifications. For more information, see <a href=\"https://docs.aws.amazon.com/iotevents/latest/developerguide/lambda-support.html\">Managing alarm notifications</a> in the <i>IoT Events Developer Guide</i>.</p>"}}, "documentation": "<p>Contains the configuration information of an alarm created in an IoT SiteWise Monitor portal. You can use the alarm to monitor an asset property and get notified when the asset property value is outside a specified range. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/appguide/monitor-alarms.html\">Monitoring with alarms</a> in the <i>IoT SiteWise Application Guide</i>.</p>"}, "AmazonResourceName": {"type": "string", "max": 1011, "min": 1}, "AssetCompositeModel": {"type": "structure", "required": ["name", "type", "properties"], "members": {"name": {"shape": "Name", "documentation": "<p>The name of the composite model.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the composite model.</p>"}, "type": {"shape": "Name", "documentation": "<p>The type of the composite model. For alarm composite models, this type is <code>AWS/ALARM</code>.</p>"}, "properties": {"shape": "AssetProperties", "documentation": "<p>The asset properties that this composite model defines.</p>"}, "id": {"shape": "ID", "documentation": "<p> The ID of the asset composite model. </p>"}}, "documentation": "<p>Contains information about a composite model in an asset. This object contains the asset's properties that you define in the composite model.</p>"}, "AssetCompositeModels": {"type": "list", "member": {"shape": "AssetCompositeModel"}}, "AssetErrorCode": {"type": "string", "enum": ["INTERNAL_FAILURE"]}, "AssetErrorDetails": {"type": "structure", "required": ["assetId", "code", "message"], "members": {"assetId": {"shape": "ID", "documentation": "<p>The ID of the asset.</p>"}, "code": {"shape": "AssetErrorCode", "documentation": "<p>The error code.</p>"}, "message": {"shape": "AssetErrorMessage", "documentation": "<p>The error message.</p>"}}, "documentation": "<p>Contains error details for the requested associate project asset action.</p>"}, "AssetErrorMessage": {"type": "string"}, "AssetHierarchies": {"type": "list", "member": {"shape": "AssetHierarchy"}}, "AssetHierarchy": {"type": "structure", "required": ["name"], "members": {"id": {"shape": "ID", "documentation": "<p>The ID of the hierarchy. This ID is a <code>hierarchyId</code>.</p>"}, "name": {"shape": "Name", "documentation": "<p>The hierarchy name provided in the <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_CreateAssetModel.html\">CreateAssetModel</a> or <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_UpdateAssetModel.html\">UpdateAssetModel</a> API operation.</p>"}}, "documentation": "<p>Describes an asset hierarchy that contains a hierarchy's name and ID.</p>"}, "AssetHierarchyInfo": {"type": "structure", "members": {"parentAssetId": {"shape": "ID", "documentation": "<p>The ID of the parent asset in this asset relationship.</p>"}, "childAssetId": {"shape": "ID", "documentation": "<p>The ID of the child asset in this asset relationship.</p>"}}, "documentation": "<p>Contains information about a parent asset and a child asset that are related through an asset hierarchy.</p>"}, "AssetIDs": {"type": "list", "member": {"shape": "ID"}}, "AssetModelCompositeModel": {"type": "structure", "required": ["name", "type"], "members": {"name": {"shape": "Name", "documentation": "<p>The name of the composite model.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the composite model.</p>"}, "type": {"shape": "Name", "documentation": "<p>The type of the composite model. For alarm composite models, this type is <code>AWS/ALARM</code>.</p>"}, "properties": {"shape": "AssetModelProperties", "documentation": "<p>The asset property definitions for this composite model.</p>"}, "id": {"shape": "ID", "documentation": "<p> The ID of the asset model composite model. </p>"}}, "documentation": "<p>Contains information about a composite model in an asset model. This object contains the asset property definitions that you define in the composite model.</p>"}, "AssetModelCompositeModelDefinition": {"type": "structure", "required": ["name", "type"], "members": {"name": {"shape": "Name", "documentation": "<p>The name of the composite model.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the composite model.</p>"}, "type": {"shape": "Name", "documentation": "<p>The type of the composite model. For alarm composite models, this type is <code>AWS/ALARM</code>.</p>"}, "properties": {"shape": "AssetModelPropertyDefinitions", "documentation": "<p>The asset property definitions for this composite model.</p>"}}, "documentation": "<p>Contains a composite model definition in an asset model. This composite model definition is applied to all assets created from the asset model.</p>"}, "AssetModelCompositeModelDefinitions": {"type": "list", "member": {"shape": "AssetModelCompositeModelDefinition"}}, "AssetModelCompositeModels": {"type": "list", "member": {"shape": "AssetModelCompositeModel"}}, "AssetModelHierarchies": {"type": "list", "member": {"shape": "AssetModelHierarchy"}}, "AssetModelHierarchy": {"type": "structure", "required": ["name", "childAssetModelId"], "members": {"id": {"shape": "ID", "documentation": "<p>The ID of the asset model hierarchy. This ID is a <code>hierarchyId</code>.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the asset model hierarchy that you specify by using the <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_CreateAssetModel.html\">CreateAssetModel</a> or <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_UpdateAssetModel.html\">UpdateAssetModel</a> API operation.</p>"}, "childAssetModelId": {"shape": "ID", "documentation": "<p>The ID of the asset model. All assets in this hierarchy must be instances of the <code>childAssetModelId</code> asset model.</p>"}}, "documentation": "<p>Describes an asset hierarchy that contains a hierarchy's name, ID, and child asset model ID that specifies the type of asset that can be in this hierarchy.</p>"}, "AssetModelHierarchyDefinition": {"type": "structure", "required": ["name", "childAssetModelId"], "members": {"name": {"shape": "Name", "documentation": "<p>The name of the asset model hierarchy definition (as specified in the <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_CreateAssetModel.html\">CreateAssetModel</a> or <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_UpdateAssetModel.html\">UpdateAssetModel</a> API operation).</p>"}, "childAssetModelId": {"shape": "ID", "documentation": "<p>The ID of an asset model for this hierarchy.</p>"}}, "documentation": "<p>Contains an asset model hierarchy used in asset model creation. An asset model hierarchy determines the kind (or type) of asset that can belong to a hierarchy.</p>"}, "AssetModelHierarchyDefinitions": {"type": "list", "member": {"shape": "AssetModelHierarchyDefinition"}}, "AssetModelProperties": {"type": "list", "member": {"shape": "AssetModelProperty"}}, "AssetModelProperty": {"type": "structure", "required": ["name", "dataType", "type"], "members": {"id": {"shape": "ID", "documentation": "<p>The ID of the asset model property.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the asset model property.</p>"}, "dataType": {"shape": "PropertyDataType", "documentation": "<p>The data type of the asset model property.</p>"}, "dataTypeSpec": {"shape": "Name", "documentation": "<p>The data type of the structure for this property. This parameter exists on properties that have the <code>STRUCT</code> data type.</p>"}, "unit": {"shape": "PropertyUnit", "documentation": "<p>The unit of the asset model property, such as <code>Newtons</code> or <code>RPM</code>.</p>"}, "type": {"shape": "PropertyType", "documentation": "<p>The property type (see <code>PropertyType</code>).</p>"}}, "documentation": "<p>Contains information about an asset model property.</p>"}, "AssetModelPropertyDefinition": {"type": "structure", "required": ["name", "dataType", "type"], "members": {"name": {"shape": "Name", "documentation": "<p>The name of the property definition.</p>"}, "dataType": {"shape": "PropertyDataType", "documentation": "<p>The data type of the property definition.</p> <p>If you specify <code>STRUCT</code>, you must also specify <code>dataTypeSpec</code> to identify the type of the structure for this property.</p>"}, "dataTypeSpec": {"shape": "Name", "documentation": "<p>The data type of the structure for this property. This parameter is required on properties that have the <code>STRUCT</code> data type.</p> <p>The options for this parameter depend on the type of the composite model in which you define this property. Use <code>AWS/ALARM_STATE</code> for alarm state in alarm composite models.</p>"}, "unit": {"shape": "PropertyUnit", "documentation": "<p>The unit of the property definition, such as <code>Newtons</code> or <code>RPM</code>.</p>"}, "type": {"shape": "PropertyType", "documentation": "<p>The property definition type (see <code>PropertyType</code>). You can only specify one type in a property definition.</p>"}}, "documentation": "<p>Contains an asset model property definition. This property definition is applied to all assets created from the asset model.</p>"}, "AssetModelPropertyDefinitions": {"type": "list", "member": {"shape": "AssetModelPropertyDefinition"}}, "AssetModelPropertySummaries": {"type": "list", "member": {"shape": "AssetModelPropertySummary"}}, "AssetModelPropertySummary": {"type": "structure", "required": ["name", "dataType", "type"], "members": {"id": {"shape": "ID", "documentation": "<p>The ID of the property.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the property.</p>"}, "dataType": {"shape": "PropertyDataType", "documentation": "<p>The data type of the property.</p>"}, "dataTypeSpec": {"shape": "Name", "documentation": "<p>The data type of the structure for this property. This parameter exists on properties that have the <code>STRUCT</code> data type.</p>"}, "unit": {"shape": "PropertyUnit", "documentation": "<p>The unit (such as <code>Newtons</code> or <code>RPM</code>) of the property.</p>"}, "type": {"shape": "PropertyType"}, "assetModelCompositeModelId": {"shape": "ID", "documentation": "<p> The ID of the composite model that contains the asset model property. </p>"}}, "documentation": "<p>Contains a summary of a property associated with a model.</p>"}, "AssetModelState": {"type": "string", "enum": ["CREATING", "ACTIVE", "UPDATING", "PROPAGATING", "DELETING", "FAILED"]}, "AssetModelStatus": {"type": "structure", "required": ["state"], "members": {"state": {"shape": "AssetModelState", "documentation": "<p>The current state of the asset model.</p>"}, "error": {"shape": "ErrorDetails", "documentation": "<p>Contains associated error information, if any.</p>"}}, "documentation": "<p>Contains current status information for an asset model. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/asset-and-model-states.html\">Asset and model states</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "AssetModelSummaries": {"type": "list", "member": {"shape": "AssetModelSummary"}}, "AssetModelSummary": {"type": "structure", "required": ["id", "arn", "name", "description", "creationDate", "lastUpdateDate", "status"], "members": {"id": {"shape": "ID", "documentation": "<p>The ID of the asset model (used with IoT SiteWise APIs).</p>"}, "arn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the asset model, which has the following format.</p> <p> <code>arn:${Partition}:iotsitewise:${Region}:${Account}:asset-model/${AssetModelId}</code> </p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the asset model.</p>"}, "description": {"shape": "Description", "documentation": "<p>The asset model description.</p>"}, "creationDate": {"shape": "Timestamp", "documentation": "<p>The date the asset model was created, in Unix epoch time.</p>"}, "lastUpdateDate": {"shape": "Timestamp", "documentation": "<p>The date the asset model was last updated, in Unix epoch time.</p>"}, "status": {"shape": "AssetModelStatus", "documentation": "<p>The current status of the asset model.</p>"}}, "documentation": "<p>Contains a summary of an asset model.</p>"}, "AssetProperties": {"type": "list", "member": {"shape": "AssetProperty"}}, "AssetProperty": {"type": "structure", "required": ["id", "name", "dataType"], "members": {"id": {"shape": "ID", "documentation": "<p>The ID of the asset property.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the property.</p>"}, "alias": {"shape": "PropertyAlias", "documentation": "<p>The alias that identifies the property, such as an OPC-UA server data stream path (for example, <code>/company/windfarm/3/turbine/7/temperature</code>). For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/connect-data-streams.html\">Mapping industrial data streams to asset properties</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "notification": {"shape": "PropertyNotification", "documentation": "<p>The asset property's notification topic and state. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_UpdateAssetProperty.html\">UpdateAssetProperty</a>.</p>"}, "dataType": {"shape": "PropertyDataType", "documentation": "<p>The data type of the asset property.</p>"}, "dataTypeSpec": {"shape": "Name", "documentation": "<p>The data type of the structure for this property. This parameter exists on properties that have the <code>STRUCT</code> data type.</p>"}, "unit": {"shape": "PropertyUnit", "documentation": "<p>The unit (such as <code>Newtons</code> or <code>RPM</code>) of the asset property.</p>"}}, "documentation": "<p>Contains asset property information.</p>"}, "AssetPropertyAlias": {"type": "string", "max": 2048, "min": 1, "pattern": "[^\\u0000-\\u001F\\u007F]+"}, "AssetPropertySummaries": {"type": "list", "member": {"shape": "AssetPropertySummary"}}, "AssetPropertySummary": {"type": "structure", "members": {"id": {"shape": "ID", "documentation": "<p>The ID of the property.</p>"}, "alias": {"shape": "PropertyAlias", "documentation": "<p>The alias that identifies the property, such as an OPC-UA server data stream path (for example, <code>/company/windfarm/3/turbine/7/temperature</code>). For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/connect-data-streams.html\">Mapping industrial data streams to asset properties</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "unit": {"shape": "PropertyUnit", "documentation": "<p> The unit of measure (such as Newtons or RPM) of the asset property. </p>"}, "notification": {"shape": "PropertyNotification"}, "assetCompositeModelId": {"shape": "ID", "documentation": "<p> The ID of the composite model that contains the asset property. </p>"}}, "documentation": "<p>Contains a summary of a property associated with an asset.</p>"}, "AssetPropertyValue": {"type": "structure", "required": ["value", "timestamp"], "members": {"value": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The value of the asset property (see <code>Variant</code>).</p>"}, "timestamp": {"shape": "TimeInNanos", "documentation": "<p>The timestamp of the asset property value.</p>"}, "quality": {"shape": "Quality", "documentation": "<p>The quality of the asset property value.</p>"}}, "documentation": "<p>Contains asset property value information.</p>"}, "AssetPropertyValueHistory": {"type": "list", "member": {"shape": "AssetPropertyValue"}}, "AssetPropertyValues": {"type": "list", "member": {"shape": "AssetPropertyValue"}}, "AssetRelationshipSummaries": {"type": "list", "member": {"shape": "AssetRelationshipSummary"}}, "AssetRelationshipSummary": {"type": "structure", "required": ["relationshipType"], "members": {"hierarchyInfo": {"shape": "AssetHierarchyInfo", "documentation": "<p>The assets that are related through an asset hierarchy.</p> <p>This object is present if the <code>relationshipType</code> is <code>HIERARCHY</code>.</p>"}, "relationshipType": {"shape": "AssetRelationshipType", "documentation": "<p>The relationship type of the assets in this relationship. This value is one of the following:</p> <ul> <li> <p> <code>HIERARCHY</code> – The assets are related through an asset hierarchy. If you specify this relationship type, this asset relationship includes the <code>hierarchyInfo</code> object.</p> </li> </ul>"}}, "documentation": "<p>Contains information about assets that are related to one another.</p>"}, "AssetRelationshipType": {"type": "string", "enum": ["HIERARCHY"]}, "AssetState": {"type": "string", "enum": ["CREATING", "ACTIVE", "UPDATING", "DELETING", "FAILED"]}, "AssetStatus": {"type": "structure", "required": ["state"], "members": {"state": {"shape": "AssetState", "documentation": "<p>The current status of the asset.</p>"}, "error": {"shape": "ErrorDetails", "documentation": "<p>Contains associated error information, if any.</p>"}}, "documentation": "<p>Contains information about the current status of an asset. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/asset-and-model-states.html\">Asset and model states</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "AssetSummaries": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "AssetSummary": {"type": "structure", "required": ["id", "arn", "name", "assetModelId", "creationDate", "lastUpdateDate", "status", "hierarchies"], "members": {"id": {"shape": "ID", "documentation": "<p>The ID of the asset.</p>"}, "arn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the asset, which has the following format.</p> <p> <code>arn:${Partition}:iotsitewise:${Region}:${Account}:asset/${AssetId}</code> </p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the asset.</p>"}, "assetModelId": {"shape": "ID", "documentation": "<p>The ID of the asset model used to create this asset.</p>"}, "creationDate": {"shape": "Timestamp", "documentation": "<p>The date the asset was created, in Unix epoch time.</p>"}, "lastUpdateDate": {"shape": "Timestamp", "documentation": "<p>The date the asset was last updated, in Unix epoch time.</p>"}, "status": {"shape": "AssetStatus", "documentation": "<p>The current status of the asset.</p>"}, "hierarchies": {"shape": "AssetHierarchies", "documentation": "<p>A list of asset hierarchies that each contain a <code>hierarchyId</code>. A hierarchy specifies allowed parent/child asset relationships.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description for the asset.</p>"}}, "documentation": "<p>Contains a summary of an asset.</p>"}, "AssociateAssetsRequest": {"type": "structure", "required": ["assetId", "hierarchyId", "childAssetId"], "members": {"assetId": {"shape": "ID", "documentation": "<p>The ID of the parent asset.</p>", "location": "uri", "locationName": "assetId"}, "hierarchyId": {"shape": "ID", "documentation": "<p>The ID of a hierarchy in the parent asset's model. Hierarchies allow different groupings of assets to be formed that all come from the same asset model. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/asset-hierarchies.html\">Asset hierarchies</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "childAssetId": {"shape": "ID", "documentation": "<p>The ID of the child asset to be associated.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true}}}, "AssociateTimeSeriesToAssetPropertyRequest": {"type": "structure", "required": ["alias", "assetId", "propertyId"], "members": {"alias": {"shape": "PropertyAlias", "documentation": "<p>The alias that identifies the time series.</p>", "location": "querystring", "locationName": "alias"}, "assetId": {"shape": "ID", "documentation": "<p>The ID of the asset in which the asset property was created.</p>", "location": "querystring", "locationName": "assetId"}, "propertyId": {"shape": "ID", "documentation": "<p>The ID of the asset property.</p>", "location": "querystring", "locationName": "propertyId"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true}}}, "AssociatedAssetsSummaries": {"type": "list", "member": {"shape": "AssociatedAssetsSummary"}}, "AssociatedAssetsSummary": {"type": "structure", "required": ["id", "arn", "name", "assetModelId", "creationDate", "lastUpdateDate", "status", "hierarchies"], "members": {"id": {"shape": "ID", "documentation": "<p>The ID of the asset.</p>"}, "arn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the asset, which has the following format.</p> <p> <code>arn:${Partition}:iotsitewise:${Region}:${Account}:asset/${AssetId}</code> </p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the asset.</p>"}, "assetModelId": {"shape": "ID", "documentation": "<p>The ID of the asset model used to create the asset.</p>"}, "creationDate": {"shape": "Timestamp", "documentation": "<p>The date the asset was created, in Unix epoch time.</p>"}, "lastUpdateDate": {"shape": "Timestamp", "documentation": "<p>The date the asset was last updated, in Unix epoch time.</p>"}, "status": {"shape": "AssetStatus", "documentation": "<p>The current status of the asset.</p>"}, "hierarchies": {"shape": "AssetHierarchies", "documentation": "<p>A list of asset hierarchies that each contain a <code>hierarchyId</code>. A hierarchy specifies allowed parent/child asset relationships.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description for the asset.</p>"}}, "documentation": "<p>Contains a summary of an associated asset.</p>"}, "Attribute": {"type": "structure", "members": {"defaultValue": {"shape": "DefaultValue", "documentation": "<p>The default value of the asset model property attribute. All assets that you create from the asset model contain this attribute value. You can update an attribute's value after you create an asset. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/update-attribute-values.html\">Updating attribute values</a> in the <i>IoT SiteWise User Guide</i>.</p>"}}, "documentation": "<p>Contains an asset attribute property. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/asset-properties.html#attributes\">Attributes</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "AuthMode": {"type": "string", "enum": ["IAM", "SSO"]}, "BatchAssociateProjectAssetsErrors": {"type": "list", "member": {"shape": "AssetErrorDetails"}}, "BatchAssociateProjectAssetsRequest": {"type": "structure", "required": ["projectId", "assetIds"], "members": {"projectId": {"shape": "ID", "documentation": "<p>The ID of the project to which to associate the assets.</p>", "location": "uri", "locationName": "projectId"}, "assetIds": {"shape": "IDs", "documentation": "<p>The IDs of the assets to be associated to the project.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true}}}, "BatchAssociateProjectAssetsResponse": {"type": "structure", "members": {"errors": {"shape": "BatchAssociateProjectAssetsErrors", "documentation": "<p>A list of associated error information, if any.</p>"}}}, "BatchDisassociateProjectAssetsErrors": {"type": "list", "member": {"shape": "AssetErrorDetails"}}, "BatchDisassociateProjectAssetsRequest": {"type": "structure", "required": ["projectId", "assetIds"], "members": {"projectId": {"shape": "ID", "documentation": "<p>The ID of the project from which to disassociate the assets.</p>", "location": "uri", "locationName": "projectId"}, "assetIds": {"shape": "IDs", "documentation": "<p>The IDs of the assets to be disassociated from the project.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true}}}, "BatchDisassociateProjectAssetsResponse": {"type": "structure", "members": {"errors": {"shape": "BatchDisassociateProjectAssetsErrors", "documentation": "<p>A list of associated error information, if any.</p>"}}}, "BatchEntryCompletionStatus": {"type": "string", "enum": ["SUCCESS", "ERROR"]}, "BatchGetAssetPropertyAggregatesEntries": {"type": "list", "member": {"shape": "BatchGetAssetPropertyAggregatesEntry"}}, "BatchGetAssetPropertyAggregatesEntry": {"type": "structure", "required": ["entryId", "aggregateTypes", "resolution", "startDate", "endDate"], "members": {"entryId": {"shape": "EntryId", "documentation": "<p>The ID of the entry.</p>"}, "assetId": {"shape": "ID", "documentation": "<p>The ID of the asset in which the asset property was created.</p>"}, "propertyId": {"shape": "ID", "documentation": "<p>The ID of the asset property.</p>"}, "propertyAlias": {"shape": "AssetPropertyAlias", "documentation": "<p>The alias that identifies the property, such as an OPC-UA server data stream path (for example, <code>/company/windfarm/3/turbine/7/temperature</code>). For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/connect-data-streams.html\">Mapping industrial data streams to asset properties</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "aggregateTypes": {"shape": "AggregateTypes", "documentation": "<p>The data aggregating function.</p>"}, "resolution": {"shape": "Resolution", "documentation": "<p>The time interval over which to aggregate data.</p>"}, "startDate": {"shape": "Timestamp", "documentation": "<p>The exclusive start of the range from which to query historical data, expressed in seconds in Unix epoch time.</p>"}, "endDate": {"shape": "Timestamp", "documentation": "<p>The inclusive end of the range from which to query historical data, expressed in seconds in Unix epoch time.</p>"}, "qualities": {"shape": "Qualities", "documentation": "<p>The quality by which to filter asset data.</p>"}, "timeOrdering": {"shape": "TimeOrdering", "documentation": "<p>The chronological sorting order of the requested information.</p> <p>Default: <code>ASCENDING</code> </p>"}}, "documentation": "<p>Contains information for an asset property aggregate entry that is associated with the <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_BatchGetAssetPropertyAggregates.html\">BatchGetAssetPropertyAggregates</a> API.</p> <p>To identify an asset property, you must specify one of the following:</p> <ul> <li> <p>The <code>assetId</code> and <code>propertyId</code> of an asset property.</p> </li> <li> <p>A <code>propertyAlias</code>, which is a data stream alias (for example, <code>/company/windfarm/3/turbine/7/temperature</code>). To define an asset property's alias, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_UpdateAssetProperty.html\">UpdateAssetProperty</a>.</p> </li> </ul>"}, "BatchGetAssetPropertyAggregatesErrorCode": {"type": "string", "enum": ["ResourceNotFoundException", "InvalidRequestException", "AccessDeniedException"]}, "BatchGetAssetPropertyAggregatesErrorEntries": {"type": "list", "member": {"shape": "BatchGetAssetPropertyAggregatesErrorEntry"}}, "BatchGetAssetPropertyAggregatesErrorEntry": {"type": "structure", "required": ["errorCode", "errorMessage", "entryId"], "members": {"errorCode": {"shape": "BatchGetAssetPropertyAggregatesErrorCode", "documentation": "<p>The error code.</p>"}, "errorMessage": {"shape": "ErrorMessage", "documentation": "<p>The associated error message.</p>"}, "entryId": {"shape": "EntryId", "documentation": "<p>The ID of the entry.</p>"}}, "documentation": "<p>Contains error information for an asset property aggregate entry that is associated with the <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_BatchGetAssetPropertyAggregates.html\">BatchGetAssetPropertyAggregates</a> API.</p>"}, "BatchGetAssetPropertyAggregatesErrorInfo": {"type": "structure", "required": ["errorCode", "errorTimestamp"], "members": {"errorCode": {"shape": "BatchGetAssetPropertyAggregatesErrorCode", "documentation": "<p>The error code.</p>"}, "errorTimestamp": {"shape": "Timestamp", "documentation": "<p>The date the error occurred, in Unix epoch time.</p>"}}, "documentation": "<p>Contains the error code and the timestamp for an asset property aggregate entry that is associated with the <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_BatchGetAssetPropertyAggregates.html\">BatchGetAssetPropertyAggregates</a> API.</p>"}, "BatchGetAssetPropertyAggregatesMaxResults": {"type": "integer", "min": 1}, "BatchGetAssetPropertyAggregatesRequest": {"type": "structure", "required": ["entries"], "members": {"entries": {"shape": "BatchGetAssetPropertyAggregatesEntries", "documentation": "<p>The list of asset property aggregate entries for the batch get request. You can specify up to 16 entries per request.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results.</p>"}, "maxResults": {"shape": "BatchGetAssetPropertyAggregatesMaxResults", "documentation": "<p>The maximum number of results to return for each paginated request. A result set is returned in the two cases, whichever occurs first.</p> <ul> <li> <p>The size of the result set is equal to 1 MB.</p> </li> <li> <p>The number of data points in the result set is equal to the value of <code>maxResults</code>. The maximum value of <code>maxResults</code> is 4000.</p> </li> </ul>"}}}, "BatchGetAssetPropertyAggregatesResponse": {"type": "structure", "required": ["errorEntries", "successEntries", "skippedEntries"], "members": {"errorEntries": {"shape": "BatchGetAssetPropertyAggregatesErrorEntries", "documentation": "<p>A list of the errors (if any) associated with the batch request. Each error entry contains the <code>entryId</code> of the entry that failed.</p>"}, "successEntries": {"shape": "BatchGetAssetPropertyAggregatesSuccessEntries", "documentation": "<p>A list of entries that were processed successfully by this batch request. Each success entry contains the <code>entryId</code> of the entry that succeeded and the latest query result.</p>"}, "skippedEntries": {"shape": "BatchGetAssetPropertyAggregatesSkippedEntries", "documentation": "<p>A list of entries that were not processed by this batch request. because these entries had been completely processed by previous paginated requests. Each skipped entry contains the <code>entryId</code> of the entry that skipped.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results, or null if there are no additional results.</p>"}}}, "BatchGetAssetPropertyAggregatesSkippedEntries": {"type": "list", "member": {"shape": "BatchGetAssetPropertyAggregatesSkippedEntry"}}, "BatchGetAssetPropertyAggregatesSkippedEntry": {"type": "structure", "required": ["entryId", "completionStatus"], "members": {"entryId": {"shape": "EntryId", "documentation": "<p>The ID of the entry.</p>"}, "completionStatus": {"shape": "BatchEntryCompletionStatus", "documentation": "<p>The completion status of each entry that is associated with the <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_BatchGetAssetPropertyAggregates.html\">BatchGetAssetPropertyAggregates</a> API.</p>"}, "errorInfo": {"shape": "BatchGetAssetPropertyAggregatesErrorInfo", "documentation": "<p>The error information, such as the error code and the timestamp.</p>"}}, "documentation": "<p>Contains information for an entry that has been processed by the previous <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_BatchGetAssetPropertyAggregates.html\">BatchGetAssetPropertyAggregates</a> request.</p>"}, "BatchGetAssetPropertyAggregatesSuccessEntries": {"type": "list", "member": {"shape": "BatchGetAssetPropertyAggregatesSuccessEntry"}}, "BatchGetAssetPropertyAggregatesSuccessEntry": {"type": "structure", "required": ["entryId", "aggregatedValues"], "members": {"entryId": {"shape": "EntryId", "documentation": "<p>The ID of the entry.</p>"}, "aggregatedValues": {"shape": "AggregatedValues", "documentation": "<p>The requested aggregated asset property values (for example, average, minimum, and maximum).</p>"}}, "documentation": "<p>Contains success information for an entry that is associated with the <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_BatchGetAssetPropertyAggregates.html\">BatchGetAssetPropertyAggregates</a> API.</p>"}, "BatchGetAssetPropertyValueEntries": {"type": "list", "member": {"shape": "BatchGetAssetPropertyValueEntry"}}, "BatchGetAssetPropertyValueEntry": {"type": "structure", "required": ["entryId"], "members": {"entryId": {"shape": "EntryId", "documentation": "<p>The ID of the entry.</p>"}, "assetId": {"shape": "ID", "documentation": "<p>The ID of the asset in which the asset property was created.</p>"}, "propertyId": {"shape": "ID", "documentation": "<p>The ID of the asset property.</p>"}, "propertyAlias": {"shape": "AssetPropertyAlias", "documentation": "<p>The alias that identifies the property, such as an OPC-UA server data stream path (for example, <code>/company/windfarm/3/turbine/7/temperature</code>). For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/connect-data-streams.html\">Mapping industrial data streams to asset properties</a> in the <i>IoT SiteWise User Guide</i>.</p>"}}, "documentation": "<p>Contains information for an asset property value entry that is associated with the <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_BatchGetAssetPropertyValue.html\">BatchGetAssetPropertyValue</a> API.</p> <p>To identify an asset property, you must specify one of the following:</p> <ul> <li> <p>The <code>assetId</code> and <code>propertyId</code> of an asset property.</p> </li> <li> <p>A <code>propertyAlias</code>, which is a data stream alias (for example, <code>/company/windfarm/3/turbine/7/temperature</code>). To define an asset property's alias, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_UpdateAssetProperty.html\">UpdateAssetProperty</a>.</p> </li> </ul>"}, "BatchGetAssetPropertyValueErrorCode": {"type": "string", "enum": ["ResourceNotFoundException", "InvalidRequestException", "AccessDeniedException"]}, "BatchGetAssetPropertyValueErrorEntries": {"type": "list", "member": {"shape": "BatchGetAssetPropertyValueErrorEntry"}}, "BatchGetAssetPropertyValueErrorEntry": {"type": "structure", "required": ["errorCode", "errorMessage", "entryId"], "members": {"errorCode": {"shape": "BatchGetAssetPropertyValueErrorCode", "documentation": "<p>The error code.</p>"}, "errorMessage": {"shape": "ErrorMessage", "documentation": "<p>The associated error message.</p>"}, "entryId": {"shape": "EntryId", "documentation": "<p>The ID of the entry.</p>"}}, "documentation": "<p>Contains error information for an asset property value entry that is associated with the <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_BatchGetAssetPropertyValue.html\">BatchGetAssetPropertyValue</a> API.</p>"}, "BatchGetAssetPropertyValueErrorInfo": {"type": "structure", "required": ["errorCode", "errorTimestamp"], "members": {"errorCode": {"shape": "BatchGetAssetPropertyValueErrorCode", "documentation": "<p>The error code.</p>"}, "errorTimestamp": {"shape": "Timestamp", "documentation": "<p>The date the error occurred, in Unix epoch time.</p>"}}, "documentation": "<p>The error information, such as the error code and the timestamp.</p>"}, "BatchGetAssetPropertyValueHistoryEntries": {"type": "list", "member": {"shape": "BatchGetAssetPropertyValueHistoryEntry"}}, "BatchGetAssetPropertyValueHistoryEntry": {"type": "structure", "required": ["entryId"], "members": {"entryId": {"shape": "EntryId", "documentation": "<p>The ID of the entry.</p>"}, "assetId": {"shape": "ID", "documentation": "<p>The ID of the asset in which the asset property was created.</p>"}, "propertyId": {"shape": "ID", "documentation": "<p>The ID of the asset property.</p>"}, "propertyAlias": {"shape": "AssetPropertyAlias", "documentation": "<p>The alias that identifies the property, such as an OPC-UA server data stream path (for example, <code>/company/windfarm/3/turbine/7/temperature</code>). For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/connect-data-streams.html\">Mapping industrial data streams to asset properties</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "startDate": {"shape": "Timestamp", "documentation": "<p>The exclusive start of the range from which to query historical data, expressed in seconds in Unix epoch time.</p>"}, "endDate": {"shape": "Timestamp", "documentation": "<p>The inclusive end of the range from which to query historical data, expressed in seconds in Unix epoch time.</p>"}, "qualities": {"shape": "Qualities", "documentation": "<p>The quality by which to filter asset data.</p>"}, "timeOrdering": {"shape": "TimeOrdering", "documentation": "<p>The chronological sorting order of the requested information.</p> <p>Default: <code>ASCENDING</code> </p>"}}, "documentation": "<p>Contains information for an asset property historical value entry that is associated with the <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_BatchGetAssetPropertyValue.html\">BatchGetAssetPropertyValueHistory</a> API.</p> <p>To identify an asset property, you must specify one of the following:</p> <ul> <li> <p>The <code>assetId</code> and <code>propertyId</code> of an asset property.</p> </li> <li> <p>A <code>propertyAlias</code>, which is a data stream alias (for example, <code>/company/windfarm/3/turbine/7/temperature</code>). To define an asset property's alias, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_UpdateAssetProperty.html\">UpdateAssetProperty</a>.</p> </li> </ul>"}, "BatchGetAssetPropertyValueHistoryErrorCode": {"type": "string", "enum": ["ResourceNotFoundException", "InvalidRequestException", "AccessDeniedException"]}, "BatchGetAssetPropertyValueHistoryErrorEntries": {"type": "list", "member": {"shape": "BatchGetAssetPropertyValueHistoryErrorEntry"}}, "BatchGetAssetPropertyValueHistoryErrorEntry": {"type": "structure", "required": ["errorCode", "errorMessage", "entryId"], "members": {"errorCode": {"shape": "BatchGetAssetPropertyValueHistoryErrorCode", "documentation": "<p>The error code.</p>"}, "errorMessage": {"shape": "ErrorMessage", "documentation": "<p>The associated error message.</p>"}, "entryId": {"shape": "EntryId", "documentation": "<p>The ID of the entry.</p>"}}, "documentation": "<p>A list of the errors (if any) associated with the batch request. Each error entry contains the <code>entryId</code> of the entry that failed.</p>"}, "BatchGetAssetPropertyValueHistoryErrorInfo": {"type": "structure", "required": ["errorCode", "errorTimestamp"], "members": {"errorCode": {"shape": "BatchGetAssetPropertyValueHistoryErrorCode", "documentation": "<p>The error code.</p>"}, "errorTimestamp": {"shape": "Timestamp", "documentation": "<p>The date the error occurred, in Unix epoch time.</p>"}}, "documentation": "<p>The error information, such as the error code and the timestamp.</p>"}, "BatchGetAssetPropertyValueHistoryMaxResults": {"type": "integer", "min": 1}, "BatchGetAssetPropertyValueHistoryRequest": {"type": "structure", "required": ["entries"], "members": {"entries": {"shape": "BatchGetAssetPropertyValueHistoryEntries", "documentation": "<p>The list of asset property historical value entries for the batch get request. You can specify up to 16 entries per request.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results.</p>"}, "maxResults": {"shape": "BatchGetAssetPropertyValueHistoryMaxResults", "documentation": "<p>The maximum number of results to return for each paginated request. A result set is returned in the two cases, whichever occurs first.</p> <ul> <li> <p>The size of the result set is equal to 4 MB.</p> </li> <li> <p>The number of data points in the result set is equal to the value of <code>maxResults</code>. The maximum value of <code>maxResults</code> is 20000.</p> </li> </ul>"}}}, "BatchGetAssetPropertyValueHistoryResponse": {"type": "structure", "required": ["errorEntries", "successEntries", "skippedEntries"], "members": {"errorEntries": {"shape": "BatchGetAssetPropertyValueHistoryErrorEntries", "documentation": "<p>A list of the errors (if any) associated with the batch request. Each error entry contains the <code>entryId</code> of the entry that failed.</p>"}, "successEntries": {"shape": "BatchGetAssetPropertyValueHistorySuccessEntries", "documentation": "<p>A list of entries that were processed successfully by this batch request. Each success entry contains the <code>entryId</code> of the entry that succeeded and the latest query result.</p>"}, "skippedEntries": {"shape": "BatchGetAssetPropertyValueHistorySkippedEntries", "documentation": "<p>A list of entries that were not processed by this batch request. because these entries had been completely processed by previous paginated requests. Each skipped entry contains the <code>entryId</code> of the entry that skipped.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results, or null if there are no additional results.</p>"}}}, "BatchGetAssetPropertyValueHistorySkippedEntries": {"type": "list", "member": {"shape": "BatchGetAssetPropertyValueHistorySkippedEntry"}}, "BatchGetAssetPropertyValueHistorySkippedEntry": {"type": "structure", "required": ["entryId", "completionStatus"], "members": {"entryId": {"shape": "EntryId", "documentation": "<p>The ID of the entry.</p>"}, "completionStatus": {"shape": "BatchEntryCompletionStatus", "documentation": "<p>The completion status of each entry that is associated with the <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_BatchGetAssetPropertyValueHistory.html\">BatchGetAssetPropertyValueHistory</a> API.</p>"}, "errorInfo": {"shape": "BatchGetAssetPropertyValueHistoryErrorInfo", "documentation": "<p>The error information, such as the error code and the timestamp.</p>"}}, "documentation": "<p>Contains information for an entry that has been processed by the previous <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_BatchGetAssetPropertyValue.html\">BatchGetAssetPropertyValueHistory</a> request.</p>"}, "BatchGetAssetPropertyValueHistorySuccessEntries": {"type": "list", "member": {"shape": "BatchGetAssetPropertyValueHistorySuccessEntry"}}, "BatchGetAssetPropertyValueHistorySuccessEntry": {"type": "structure", "required": ["entryId", "assetPropertyValueHistory"], "members": {"entryId": {"shape": "EntryId", "documentation": "<p>The ID of the entry.</p>"}, "assetPropertyValueHistory": {"shape": "AssetPropertyValueHistory", "documentation": "<p>The requested historical values for the specified asset property.</p>"}}, "documentation": "<p>Contains success information for an entry that is associated with the <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_BatchGetAssetPropertyValue.html\">BatchGetAssetPropertyValueHistory</a> API.</p>"}, "BatchGetAssetPropertyValueRequest": {"type": "structure", "required": ["entries"], "members": {"entries": {"shape": "BatchGetAssetPropertyValueEntries", "documentation": "<p>The list of asset property value entries for the batch get request. You can specify up to 128 entries per request.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results.</p>"}}}, "BatchGetAssetPropertyValueResponse": {"type": "structure", "required": ["errorEntries", "successEntries", "skippedEntries"], "members": {"errorEntries": {"shape": "BatchGetAssetPropertyValueErrorEntries", "documentation": "<p>A list of the errors (if any) associated with the batch request. Each error entry contains the <code>entryId</code> of the entry that failed.</p>"}, "successEntries": {"shape": "BatchGetAssetPropertyValueSuccessEntries", "documentation": "<p>A list of entries that were processed successfully by this batch request. Each success entry contains the <code>entryId</code> of the entry that succeeded and the latest query result.</p>"}, "skippedEntries": {"shape": "BatchGetAssetPropertyValueSkippedEntries", "documentation": "<p>A list of entries that were not processed by this batch request. because these entries had been completely processed by previous paginated requests. Each skipped entry contains the <code>entryId</code> of the entry that skipped.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results, or null if there are no additional results.</p>"}}}, "BatchGetAssetPropertyValueSkippedEntries": {"type": "list", "member": {"shape": "BatchGetAssetPropertyValueSkippedEntry"}}, "BatchGetAssetPropertyValueSkippedEntry": {"type": "structure", "required": ["entryId", "completionStatus"], "members": {"entryId": {"shape": "EntryId", "documentation": "<p>The ID of the entry.</p>"}, "completionStatus": {"shape": "BatchEntryCompletionStatus", "documentation": "<p>The completion status of each entry that is associated with the <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_BatchGetAssetPropertyValue.html\">BatchGetAssetPropertyValue</a> request.</p>"}, "errorInfo": {"shape": "BatchGetAssetPropertyValueErrorInfo", "documentation": "<p>The error information, such as the error code and the timestamp.</p>"}}, "documentation": "<p>Contains information for an entry that has been processed by the previous <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_BatchGetAssetPropertyValue.html\">BatchGetAssetPropertyValue</a> request.</p>"}, "BatchGetAssetPropertyValueSuccessEntries": {"type": "list", "member": {"shape": "BatchGetAssetPropertyValueSuccessEntry"}}, "BatchGetAssetPropertyValueSuccessEntry": {"type": "structure", "required": ["entryId"], "members": {"entryId": {"shape": "EntryId", "documentation": "<p>The ID of the entry.</p>"}, "assetPropertyValue": {"shape": "AssetPropertyValue"}}, "documentation": "<p>Contains success information for an entry that is associated with the <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_BatchGetAssetPropertyValue.html\">BatchGetAssetPropertyValue</a> API.</p>"}, "BatchPutAssetPropertyError": {"type": "structure", "required": ["errorCode", "errorMessage", "timestamps"], "members": {"errorCode": {"shape": "BatchPutAssetPropertyValueErrorCode", "documentation": "<p>The error code.</p>"}, "errorMessage": {"shape": "ErrorMessage", "documentation": "<p>The associated error message.</p>"}, "timestamps": {"shape": "Timestamps", "documentation": "<p>A list of timestamps for each error, if any.</p>"}}, "documentation": "<p>Contains error information from updating a batch of asset property values.</p>"}, "BatchPutAssetPropertyErrorEntries": {"type": "list", "member": {"shape": "BatchPutAssetPropertyErrorEntry"}}, "BatchPutAssetPropertyErrorEntry": {"type": "structure", "required": ["entryId", "errors"], "members": {"entryId": {"shape": "EntryId", "documentation": "<p>The ID of the failed entry.</p>"}, "errors": {"shape": "BatchPutAssetPropertyErrors", "documentation": "<p>The list of update property value errors.</p>"}}, "documentation": "<p>Contains error information for asset property value entries that are associated with the <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_BatchPutAssetPropertyValue.html\">BatchPutAssetPropertyValue</a> API.</p>"}, "BatchPutAssetPropertyErrors": {"type": "list", "member": {"shape": "BatchPutAssetPropertyError"}}, "BatchPutAssetPropertyValueErrorCode": {"type": "string", "enum": ["ResourceNotFoundException", "InvalidRequestException", "InternalFailureException", "ServiceUnavailableException", "ThrottlingException", "LimitExceededException", "ConflictingOperationException", "TimestampOutOfRangeException", "AccessDeniedException"]}, "BatchPutAssetPropertyValueRequest": {"type": "structure", "required": ["entries"], "members": {"entries": {"shape": "PutAssetPropertyValueEntries", "documentation": "<p>The list of asset property value entries for the batch put request. You can specify up to 10 entries per request.</p>"}}}, "BatchPutAssetPropertyValueResponse": {"type": "structure", "required": ["errorEntries"], "members": {"errorEntries": {"shape": "BatchPutAssetPropertyErrorEntries", "documentation": "<p>A list of the errors (if any) associated with the batch put request. Each error entry contains the <code>entryId</code> of the entry that failed.</p>"}}}, "Bucket": {"type": "string", "max": 63, "min": 3}, "CapabilityConfiguration": {"type": "string", "max": 104857600, "min": 1}, "CapabilityNamespace": {"type": "string", "max": 512, "min": 1, "pattern": "^[a-zA-Z]+:[a-zA-Z]+:[0-9]+$"}, "CapabilitySyncStatus": {"type": "string", "enum": ["IN_SYNC", "OUT_OF_SYNC", "SYNC_FAILED", "UNKNOWN"]}, "ClientToken": {"type": "string", "max": 64, "min": 36, "pattern": "\\S{36,64}"}, "ColumnName": {"type": "string", "enum": ["ALIAS", "ASSET_ID", "PROPERTY_ID", "DATA_TYPE", "TIMESTAMP_SECONDS", "TIMESTAMP_NANO_OFFSET", "QUALITY", "VALUE"]}, "ColumnNames": {"type": "list", "member": {"shape": "ColumnName"}}, "CompositeModelProperty": {"type": "structure", "required": ["name", "type", "assetProperty"], "members": {"name": {"shape": "Name", "documentation": "<p>The name of the property.</p>"}, "type": {"shape": "Name", "documentation": "<p>The type of the composite model that defines this property.</p>"}, "assetProperty": {"shape": "Property"}, "id": {"shape": "ID", "documentation": "<p> The ID of the composite model that contains the property. </p>"}}, "documentation": "<p>Contains information about a composite model property on an asset.</p>"}, "ComputeLocation": {"type": "string", "enum": ["EDGE", "CLOUD"]}, "ConfigurationErrorDetails": {"type": "structure", "required": ["code", "message"], "members": {"code": {"shape": "ErrorCode", "documentation": "<p>The error code.</p>"}, "message": {"shape": "ErrorMessage", "documentation": "<p>The error message.</p>"}}, "documentation": "<p>Contains the details of an IoT SiteWise configuration error.</p>"}, "ConfigurationState": {"type": "string", "enum": ["ACTIVE", "UPDATE_IN_PROGRESS", "UPDATE_FAILED"]}, "ConfigurationStatus": {"type": "structure", "required": ["state"], "members": {"state": {"shape": "ConfigurationState", "documentation": "<p>The current state of the configuration.</p>"}, "error": {"shape": "ConfigurationErrorDetails", "documentation": "<p>Contains associated error information, if any.</p>"}}, "documentation": "<p>Contains current status information for the configuration.</p>"}, "ConflictingOperationException": {"type": "structure", "required": ["message", "resourceId", "resourceArn"], "members": {"message": {"shape": "ErrorMessage"}, "resourceId": {"shape": "ResourceId", "documentation": "<p>The ID of the resource that conflicts with this operation.</p>"}, "resourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource that conflicts with this operation.</p>"}}, "documentation": "<p>Your request has conflicting operations. This can occur if you're trying to perform more than one operation on the same resource at the same time.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "CoreDeviceThingName": {"type": "string", "max": 128, "min": 1}, "CreateAccessPolicyRequest": {"type": "structure", "required": ["accessPolicyIdentity", "accessPolicyResource", "accessPolicyPermission"], "members": {"accessPolicyIdentity": {"shape": "Identity", "documentation": "<p>The identity for this access policy. Choose an IAM Identity Center user, an IAM Identity Center group, or an IAM user.</p>"}, "accessPolicyResource": {"shape": "Resource", "documentation": "<p>The IoT SiteWise Monitor resource for this access policy. Choose either a portal or a project.</p>"}, "accessPolicyPermission": {"shape": "Permission", "documentation": "<p>The permission level for this access policy. Note that a project <code>ADMINISTRATOR</code> is also known as a project owner.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true}, "tags": {"shape": "TagMap", "documentation": "<p>A list of key-value pairs that contain metadata for the access policy. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/tag-resources.html\">Tagging your IoT SiteWise resources</a> in the <i>IoT SiteWise User Guide</i>.</p>"}}}, "CreateAccessPolicyResponse": {"type": "structure", "required": ["accessPolicyId", "accessPolicyArn"], "members": {"accessPolicyId": {"shape": "ID", "documentation": "<p>The ID of the access policy.</p>"}, "accessPolicyArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the access policy, which has the following format.</p> <p> <code>arn:${Partition}:iotsitewise:${Region}:${Account}:access-policy/${AccessPolicyId}</code> </p>"}}}, "CreateAssetModelRequest": {"type": "structure", "required": ["assetModelName"], "members": {"assetModelName": {"shape": "Name", "documentation": "<p>A unique, friendly name for the asset model.</p>"}, "assetModelDescription": {"shape": "Description", "documentation": "<p>A description for the asset model.</p>"}, "assetModelProperties": {"shape": "AssetModelPropertyDefinitions", "documentation": "<p>The property definitions of the asset model. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/asset-properties.html\">Asset properties</a> in the <i>IoT SiteWise User Guide</i>.</p> <p>You can specify up to 200 properties per asset model. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/quotas.html\">Quotas</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "assetModelHierarchies": {"shape": "AssetModelHierarchyDefinitions", "documentation": "<p>The hierarchy definitions of the asset model. Each hierarchy specifies an asset model whose assets can be children of any other assets created from this asset model. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/asset-hierarchies.html\">Asset hierarchies</a> in the <i>IoT SiteWise User Guide</i>.</p> <p>You can specify up to 10 hierarchies per asset model. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/quotas.html\">Quotas</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "assetModelCompositeModels": {"shape": "AssetModelCompositeModelDefinitions", "documentation": "<p>The composite asset models that are part of this asset model. Composite asset models are asset models that contain specific properties. Each composite model has a type that defines the properties that the composite model supports. Use composite asset models to define alarms on this asset model.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true}, "tags": {"shape": "TagMap", "documentation": "<p>A list of key-value pairs that contain metadata for the asset model. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/tag-resources.html\">Tagging your IoT SiteWise resources</a> in the <i>IoT SiteWise User Guide</i>.</p>"}}}, "CreateAssetModelResponse": {"type": "structure", "required": ["assetModelId", "assetModelArn", "assetModelStatus"], "members": {"assetModelId": {"shape": "ID", "documentation": "<p>The ID of the asset model. You can use this ID when you call other IoT SiteWise APIs.</p>"}, "assetModelArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the asset model, which has the following format.</p> <p> <code>arn:${Partition}:iotsitewise:${Region}:${Account}:asset-model/${AssetModelId}</code> </p>"}, "assetModelStatus": {"shape": "AssetModelStatus", "documentation": "<p>The status of the asset model, which contains a state (<code>CREATING</code> after successfully calling this operation) and any error message.</p>"}}}, "CreateAssetRequest": {"type": "structure", "required": ["assetName", "assetModelId"], "members": {"assetName": {"shape": "Name", "documentation": "<p>A friendly name for the asset.</p>"}, "assetModelId": {"shape": "ID", "documentation": "<p>The ID of the asset model from which to create the asset.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true}, "tags": {"shape": "TagMap", "documentation": "<p>A list of key-value pairs that contain metadata for the asset. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/tag-resources.html\">Tagging your IoT SiteWise resources</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "assetDescription": {"shape": "Description", "documentation": "<p>A description for the asset.</p>"}}}, "CreateAssetResponse": {"type": "structure", "required": ["assetId", "assetArn", "assetStatus"], "members": {"assetId": {"shape": "ID", "documentation": "<p>The ID of the asset. This ID uniquely identifies the asset within IoT SiteWise and can be used with other IoT SiteWise APIs.</p>"}, "assetArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the asset, which has the following format.</p> <p> <code>arn:${Partition}:iotsitewise:${Region}:${Account}:asset/${AssetId}</code> </p>"}, "assetStatus": {"shape": "AssetStatus", "documentation": "<p>The status of the asset, which contains a state (<code>CREATING</code> after successfully calling this operation) and any error message.</p>"}}}, "CreateBulkImportJobRequest": {"type": "structure", "required": ["job<PERSON>ame", "jobRoleArn", "files", "errorReportLocation", "jobConfiguration"], "members": {"jobName": {"shape": "Name", "documentation": "<p>The unique name that helps identify the job request.</p>"}, "jobRoleArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the IAM role that allows IoT SiteWise to read Amazon S3 data.</p>"}, "files": {"shape": "Files", "documentation": "<p>The files in the specified Amazon S3 bucket that contain your data.</p>"}, "errorReportLocation": {"shape": "ErrorReportLocation", "documentation": "<p>The Amazon S3 destination where errors associated with the job creation request are saved.</p>"}, "jobConfiguration": {"shape": "JobConfiguration", "documentation": "<p>Contains the configuration information of a job, such as the file format used to save data in Amazon S3.</p>"}}}, "CreateBulkImportJobResponse": {"type": "structure", "required": ["jobId", "job<PERSON>ame", "jobStatus"], "members": {"jobId": {"shape": "ID", "documentation": "<p>The ID of the job.</p>"}, "jobName": {"shape": "Name", "documentation": "<p>The unique name that helps identify the job request.</p>"}, "jobStatus": {"shape": "JobStatus", "documentation": "<p>The status of the bulk import job can be one of following values.</p> <ul> <li> <p> <code>PENDING</code> – IoT SiteWise is waiting for the current bulk import job to finish.</p> </li> <li> <p> <code>CANCELLED</code> – The bulk import job has been canceled.</p> </li> <li> <p> <code>RUNNING</code> – IoT SiteWise is processing your request to import your data from Amazon S3.</p> </li> <li> <p> <code>COMPLETED</code> – IoT SiteWise successfully completed your request to import data from Amazon S3.</p> </li> <li> <p> <code>FAILED</code> – IoT SiteWise couldn't process your request to import data from Amazon S3. You can use logs saved in the specified error report location in Amazon S3 to troubleshoot issues.</p> </li> <li> <p> <code>COMPLETED_WITH_FAILURES</code> – IoT SiteWise completed your request to import data from Amazon S3 with errors. You can use logs saved in the specified error report location in Amazon S3 to troubleshoot issues.</p> </li> </ul>"}}}, "CreateDashboardRequest": {"type": "structure", "required": ["projectId", "dashboardName", "dashboardDefinition"], "members": {"projectId": {"shape": "ID", "documentation": "<p>The ID of the project in which to create the dashboard.</p>"}, "dashboardName": {"shape": "Name", "documentation": "<p>A friendly name for the dashboard.</p>"}, "dashboardDescription": {"shape": "Description", "documentation": "<p>A description for the dashboard.</p>"}, "dashboardDefinition": {"shape": "DashboardDefinition", "documentation": "<p>The dashboard definition specified in a JSON literal. For detailed information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/create-dashboards-using-aws-cli.html\">Creating dashboards (CLI)</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true}, "tags": {"shape": "TagMap", "documentation": "<p>A list of key-value pairs that contain metadata for the dashboard. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/tag-resources.html\">Tagging your IoT SiteWise resources</a> in the <i>IoT SiteWise User Guide</i>.</p>"}}}, "CreateDashboardResponse": {"type": "structure", "required": ["dashboardId", "dashboardArn"], "members": {"dashboardId": {"shape": "ID", "documentation": "<p>The ID of the dashboard.</p>"}, "dashboardArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the dashboard, which has the following format.</p> <p> <code>arn:${Partition}:iotsitewise:${Region}:${Account}:dashboard/${DashboardId}</code> </p>"}}}, "CreateGatewayRequest": {"type": "structure", "required": ["gatewayName", "gatewayPlatform"], "members": {"gatewayName": {"shape": "Name", "documentation": "<p>A unique, friendly name for the gateway.</p>"}, "gatewayPlatform": {"shape": "GatewayPlatform", "documentation": "<p>The gateway's platform. You can only specify one platform in a gateway.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>A list of key-value pairs that contain metadata for the gateway. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/tag-resources.html\">Tagging your IoT SiteWise resources</a> in the <i>IoT SiteWise User Guide</i>.</p>"}}}, "CreateGatewayResponse": {"type": "structure", "required": ["gatewayId", "gatewayArn"], "members": {"gatewayId": {"shape": "ID", "documentation": "<p>The ID of the gateway device. You can use this ID when you call other IoT SiteWise APIs.</p>"}, "gatewayArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the gateway, which has the following format.</p> <p> <code>arn:${Partition}:iotsitewise:${Region}:${Account}:gateway/${GatewayId}</code> </p>"}}}, "CreatePortalRequest": {"type": "structure", "required": ["portalName", "portalContactEmail", "roleArn"], "members": {"portalName": {"shape": "Name", "documentation": "<p>A friendly name for the portal.</p>"}, "portalDescription": {"shape": "Description", "documentation": "<p>A description for the portal.</p>"}, "portalContactEmail": {"shape": "Email", "documentation": "<p>The Amazon Web Services administrator's contact email address.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true}, "portalLogoImageFile": {"shape": "ImageFile", "documentation": "<p>A logo image to display in the portal. Upload a square, high-resolution image. The image is displayed on a dark background.</p>"}, "roleArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of a service role that allows the portal's users to access your IoT SiteWise resources on your behalf. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/monitor-service-role.html\">Using service roles for IoT SiteWise Monitor</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>A list of key-value pairs that contain metadata for the portal. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/tag-resources.html\">Tagging your IoT SiteWise resources</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "portalAuthMode": {"shape": "AuthMode", "documentation": "<p>The service to use to authenticate users to the portal. Choose from the following options:</p> <ul> <li> <p> <code>SSO</code> – The portal uses IAM Identity Center (successor to Single Sign-On) to authenticate users and manage user permissions. Before you can create a portal that uses IAM Identity Center, you must enable IAM Identity Center. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/monitor-get-started.html#mon-gs-sso\">Enabling IAM Identity Center</a> in the <i>IoT SiteWise User Guide</i>. This option is only available in Amazon Web Services Regions other than the China Regions.</p> </li> <li> <p> <code>IAM</code> – The portal uses Identity and Access Management to authenticate users and manage user permissions.</p> </li> </ul> <p>You can't change this value after you create a portal.</p> <p>Default: <code>SSO</code> </p>"}, "notificationSenderEmail": {"shape": "Email", "documentation": "<p>The email address that sends alarm notifications.</p> <important> <p>If you use the <a href=\"https://docs.aws.amazon.com/iotevents/latest/developerguide/lambda-support.html\">IoT Events managed Lambda function</a> to manage your emails, you must <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/verify-email-addresses.html\">verify the sender email address in Amazon SES</a>.</p> </important>"}, "alarms": {"shape": "Alarms", "documentation": "<p>Contains the configuration information of an alarm created in an IoT SiteWise Monitor portal. You can use the alarm to monitor an asset property and get notified when the asset property value is outside a specified range. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/appguide/monitor-alarms.html\">Monitoring with alarms</a> in the <i>IoT SiteWise Application Guide</i>.</p>"}}}, "CreatePortalResponse": {"type": "structure", "required": ["portalId", "portalArn", "portalStartUrl", "portalStatus", "ssoApplicationId"], "members": {"portalId": {"shape": "ID", "documentation": "<p>The ID of the created portal.</p>"}, "portalArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the portal, which has the following format.</p> <p> <code>arn:${Partition}:iotsitewise:${Region}:${Account}:portal/${PortalId}</code> </p>"}, "portalStartUrl": {"shape": "Url", "documentation": "<p>The URL for the IoT SiteWise Monitor portal. You can use this URL to access portals that use IAM Identity Center for authentication. For portals that use IAM for authentication, you must use the IoT SiteWise console to get a URL that you can use to access the portal.</p>"}, "portalStatus": {"shape": "PortalStatus", "documentation": "<p>The status of the portal, which contains a state (<code>CREATING</code> after successfully calling this operation) and any error message.</p>"}, "ssoApplicationId": {"shape": "SSOApplicationId", "documentation": "<p>The associated IAM Identity Center application ID, if the portal uses IAM Identity Center.</p>"}}}, "CreateProjectRequest": {"type": "structure", "required": ["portalId", "projectName"], "members": {"portalId": {"shape": "ID", "documentation": "<p>The ID of the portal in which to create the project.</p>"}, "projectName": {"shape": "Name", "documentation": "<p>A friendly name for the project.</p>"}, "projectDescription": {"shape": "Description", "documentation": "<p>A description for the project.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true}, "tags": {"shape": "TagMap", "documentation": "<p>A list of key-value pairs that contain metadata for the project. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/tag-resources.html\">Tagging your IoT SiteWise resources</a> in the <i>IoT SiteWise User Guide</i>.</p>"}}}, "CreateProjectResponse": {"type": "structure", "required": ["projectId", "projectArn"], "members": {"projectId": {"shape": "ID", "documentation": "<p>The ID of the project.</p>"}, "projectArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the project, which has the following format.</p> <p> <code>arn:${Partition}:iotsitewise:${Region}:${Account}:project/${ProjectId}</code> </p>"}}}, "Csv": {"type": "structure", "members": {"columnNames": {"shape": "ColumnNames", "documentation": "<p>The column names specified in the .csv file.</p>"}}, "documentation": "<p>A .csv file.</p>"}, "CustomerManagedS3Storage": {"type": "structure", "required": ["s3ResourceArn", "roleArn"], "members": {"s3ResourceArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the Amazon S3 object. For more information about how to find the ARN for an Amazon S3 object, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/s3-arn-format.html\">Amazon S3 resources</a> in the <i>Amazon Simple Storage Service User Guide</i>.</p>"}, "roleArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the Identity and Access Management role that allows IoT SiteWise to send data to Amazon S3.</p>"}}, "documentation": "<p>Contains information about a customer managed Amazon S3 bucket.</p>"}, "DashboardDefinition": {"type": "string", "max": 204800, "min": 0, "pattern": ".+"}, "DashboardSummaries": {"type": "list", "member": {"shape": "DashboardSummary"}}, "DashboardSummary": {"type": "structure", "required": ["id", "name"], "members": {"id": {"shape": "ID", "documentation": "<p>The ID of the dashboard.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the dashboard</p>"}, "description": {"shape": "Description", "documentation": "<p>The dashboard's description.</p>"}, "creationDate": {"shape": "Timestamp", "documentation": "<p>The date the dashboard was created, in Unix epoch time.</p>"}, "lastUpdateDate": {"shape": "Timestamp", "documentation": "<p>The date the dashboard was last updated, in Unix epoch time.</p>"}}, "documentation": "<p>Contains a dashboard summary.</p>"}, "DefaultValue": {"type": "string", "max": 1024, "min": 1, "pattern": "[^\\u0000-\\u001F\\u007F]+"}, "DeleteAccessPolicyRequest": {"type": "structure", "required": ["accessPolicyId"], "members": {"accessPolicyId": {"shape": "ID", "documentation": "<p>The ID of the access policy to be deleted.</p>", "location": "uri", "locationName": "accessPolicyId"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}}}, "DeleteAccessPolicyResponse": {"type": "structure", "members": {}}, "DeleteAssetModelRequest": {"type": "structure", "required": ["assetModelId"], "members": {"assetModelId": {"shape": "ID", "documentation": "<p>The ID of the asset model to delete.</p>", "location": "uri", "locationName": "assetModelId"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}}}, "DeleteAssetModelResponse": {"type": "structure", "required": ["assetModelStatus"], "members": {"assetModelStatus": {"shape": "AssetModelStatus", "documentation": "<p>The status of the asset model, which contains a state (<code>DELETING</code> after successfully calling this operation) and any error message.</p>"}}}, "DeleteAssetRequest": {"type": "structure", "required": ["assetId"], "members": {"assetId": {"shape": "ID", "documentation": "<p>The ID of the asset to delete.</p>", "location": "uri", "locationName": "assetId"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}}}, "DeleteAssetResponse": {"type": "structure", "required": ["assetStatus"], "members": {"assetStatus": {"shape": "AssetStatus", "documentation": "<p>The status of the asset, which contains a state (<code>DELETING</code> after successfully calling this operation) and any error message.</p>"}}}, "DeleteDashboardRequest": {"type": "structure", "required": ["dashboardId"], "members": {"dashboardId": {"shape": "ID", "documentation": "<p>The ID of the dashboard to delete.</p>", "location": "uri", "locationName": "dashboardId"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}}}, "DeleteDashboardResponse": {"type": "structure", "members": {}}, "DeleteGatewayRequest": {"type": "structure", "required": ["gatewayId"], "members": {"gatewayId": {"shape": "ID", "documentation": "<p>The ID of the gateway to delete.</p>", "location": "uri", "locationName": "gatewayId"}}}, "DeletePortalRequest": {"type": "structure", "required": ["portalId"], "members": {"portalId": {"shape": "ID", "documentation": "<p>The ID of the portal to delete.</p>", "location": "uri", "locationName": "portalId"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}}}, "DeletePortalResponse": {"type": "structure", "required": ["portalStatus"], "members": {"portalStatus": {"shape": "PortalStatus", "documentation": "<p>The status of the portal, which contains a state (<code>DELETING</code> after successfully calling this operation) and any error message.</p>"}}}, "DeleteProjectRequest": {"type": "structure", "required": ["projectId"], "members": {"projectId": {"shape": "ID", "documentation": "<p>The ID of the project.</p>", "location": "uri", "locationName": "projectId"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}}}, "DeleteProjectResponse": {"type": "structure", "members": {}}, "DeleteTimeSeriesRequest": {"type": "structure", "members": {"alias": {"shape": "PropertyAlias", "documentation": "<p>The alias that identifies the time series.</p>", "location": "querystring", "locationName": "alias"}, "assetId": {"shape": "ID", "documentation": "<p>The ID of the asset in which the asset property was created.</p>", "location": "querystring", "locationName": "assetId"}, "propertyId": {"shape": "ID", "documentation": "<p>The ID of the asset property.</p>", "location": "querystring", "locationName": "propertyId"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true}}}, "DescribeAccessPolicyRequest": {"type": "structure", "required": ["accessPolicyId"], "members": {"accessPolicyId": {"shape": "ID", "documentation": "<p>The ID of the access policy.</p>", "location": "uri", "locationName": "accessPolicyId"}}}, "DescribeAccessPolicyResponse": {"type": "structure", "required": ["accessPolicyId", "accessPolicyArn", "accessPolicyIdentity", "accessPolicyResource", "accessPolicyPermission", "accessPolicyCreationDate", "accessPolicyLastUpdateDate"], "members": {"accessPolicyId": {"shape": "ID", "documentation": "<p>The ID of the access policy.</p>"}, "accessPolicyArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the access policy, which has the following format.</p> <p> <code>arn:${Partition}:iotsitewise:${Region}:${Account}:access-policy/${AccessPolicyId}</code> </p>"}, "accessPolicyIdentity": {"shape": "Identity", "documentation": "<p>The identity (IAM Identity Center user, IAM Identity Center group, or IAM user) to which this access policy applies.</p>"}, "accessPolicyResource": {"shape": "Resource", "documentation": "<p>The IoT SiteWise Monitor resource (portal or project) to which this access policy provides access.</p>"}, "accessPolicyPermission": {"shape": "Permission", "documentation": "<p>The access policy permission. Note that a project <code>ADMINISTRATOR</code> is also known as a project owner.</p>"}, "accessPolicyCreationDate": {"shape": "Timestamp", "documentation": "<p>The date the access policy was created, in Unix epoch time.</p>"}, "accessPolicyLastUpdateDate": {"shape": "Timestamp", "documentation": "<p>The date the access policy was last updated, in Unix epoch time.</p>"}}}, "DescribeAssetModelRequest": {"type": "structure", "required": ["assetModelId"], "members": {"assetModelId": {"shape": "ID", "documentation": "<p>The ID of the asset model.</p>", "location": "uri", "locationName": "assetModelId"}, "excludeProperties": {"shape": "ExcludeProperties", "documentation": "<p> Whether or not to exclude asset model properties from the response. </p>", "location": "querystring", "locationName": "excludeProperties"}}}, "DescribeAssetModelResponse": {"type": "structure", "required": ["assetModelId", "assetModelArn", "assetModelName", "assetModelDescription", "assetModelProperties", "assetModelHierarchies", "assetModelCreationDate", "assetModelLastUpdateDate", "assetModelStatus"], "members": {"assetModelId": {"shape": "ID", "documentation": "<p>The ID of the asset model.</p>"}, "assetModelArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the asset model, which has the following format.</p> <p> <code>arn:${Partition}:iotsitewise:${Region}:${Account}:asset-model/${AssetModelId}</code> </p>"}, "assetModelName": {"shape": "Name", "documentation": "<p>The name of the asset model.</p>"}, "assetModelDescription": {"shape": "Description", "documentation": "<p>The asset model's description.</p>"}, "assetModelProperties": {"shape": "AssetModelProperties", "documentation": "<p>The list of asset properties for the asset model.</p> <p>This object doesn't include properties that you define in composite models. You can find composite model properties in the <code>assetModelCompositeModels</code> object.</p>"}, "assetModelHierarchies": {"shape": "AssetModelHierarchies", "documentation": "<p>A list of asset model hierarchies that each contain a <code>childAssetModelId</code> and a <code>hierarchyId</code> (named <code>id</code>). A hierarchy specifies allowed parent/child asset relationships for an asset model.</p>"}, "assetModelCompositeModels": {"shape": "AssetModelCompositeModels", "documentation": "<p>The list of composite asset models for the asset model.</p>"}, "assetModelCreationDate": {"shape": "Timestamp", "documentation": "<p>The date the asset model was created, in Unix epoch time.</p>"}, "assetModelLastUpdateDate": {"shape": "Timestamp", "documentation": "<p>The date the asset model was last updated, in Unix epoch time.</p>"}, "assetModelStatus": {"shape": "AssetModelStatus", "documentation": "<p>The current status of the asset model, which contains a state and any error message.</p>"}}}, "DescribeAssetPropertyRequest": {"type": "structure", "required": ["assetId", "propertyId"], "members": {"assetId": {"shape": "ID", "documentation": "<p>The ID of the asset.</p>", "location": "uri", "locationName": "assetId"}, "propertyId": {"shape": "ID", "documentation": "<p>The ID of the asset property.</p>", "location": "uri", "locationName": "propertyId"}}}, "DescribeAssetPropertyResponse": {"type": "structure", "required": ["assetId", "assetName", "assetModelId"], "members": {"assetId": {"shape": "ID", "documentation": "<p>The ID of the asset.</p>"}, "assetName": {"shape": "Name", "documentation": "<p>The name of the asset.</p>"}, "assetModelId": {"shape": "ID", "documentation": "<p>The ID of the asset model.</p>"}, "assetProperty": {"shape": "Property", "documentation": "<p>The asset property's definition, alias, and notification state.</p> <p>This response includes this object for normal asset properties. If you describe an asset property in a composite model, this response includes the asset property information in <code>compositeModel</code>.</p>"}, "compositeModel": {"shape": "CompositeModelProperty", "documentation": "<p>The composite asset model that declares this asset property, if this asset property exists in a composite model.</p>"}}}, "DescribeAssetRequest": {"type": "structure", "required": ["assetId"], "members": {"assetId": {"shape": "ID", "documentation": "<p>The ID of the asset.</p>", "location": "uri", "locationName": "assetId"}, "excludeProperties": {"shape": "ExcludeProperties", "documentation": "<p> Whether or not to exclude asset properties from the response. </p>", "location": "querystring", "locationName": "excludeProperties"}}}, "DescribeAssetResponse": {"type": "structure", "required": ["assetId", "assetArn", "assetName", "assetModelId", "assetProperties", "assetHierarchies", "assetCreationDate", "assetLastUpdateDate", "assetStatus"], "members": {"assetId": {"shape": "ID", "documentation": "<p>The ID of the asset.</p>"}, "assetArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the asset, which has the following format.</p> <p> <code>arn:${Partition}:iotsitewise:${Region}:${Account}:asset/${AssetId}</code> </p>"}, "assetName": {"shape": "Name", "documentation": "<p>The name of the asset.</p>"}, "assetModelId": {"shape": "ID", "documentation": "<p>The ID of the asset model that was used to create the asset.</p>"}, "assetProperties": {"shape": "AssetProperties", "documentation": "<p>The list of asset properties for the asset.</p> <p>This object doesn't include properties that you define in composite models. You can find composite model properties in the <code>assetCompositeModels</code> object.</p>"}, "assetHierarchies": {"shape": "AssetHierarchies", "documentation": "<p>A list of asset hierarchies that each contain a <code>hierarchyId</code>. A hierarchy specifies allowed parent/child asset relationships.</p>"}, "assetCompositeModels": {"shape": "AssetCompositeModels", "documentation": "<p>The composite models for the asset.</p>"}, "assetCreationDate": {"shape": "Timestamp", "documentation": "<p>The date the asset was created, in Unix epoch time.</p>"}, "assetLastUpdateDate": {"shape": "Timestamp", "documentation": "<p>The date the asset was last updated, in Unix epoch time.</p>"}, "assetStatus": {"shape": "AssetStatus", "documentation": "<p>The current status of the asset, which contains a state and any error message.</p>"}, "assetDescription": {"shape": "Description", "documentation": "<p>A description for the asset.</p>"}}}, "DescribeBulkImportJobRequest": {"type": "structure", "required": ["jobId"], "members": {"jobId": {"shape": "ID", "documentation": "<p>The ID of the job.</p>", "location": "uri", "locationName": "jobId"}}}, "DescribeBulkImportJobResponse": {"type": "structure", "required": ["jobId", "job<PERSON>ame", "jobStatus", "jobRoleArn", "files", "errorReportLocation", "jobConfiguration", "jobCreationDate", "jobLastUpdateDate"], "members": {"jobId": {"shape": "ID", "documentation": "<p>The ID of the job.</p>"}, "jobName": {"shape": "Name", "documentation": "<p>The unique name that helps identify the job request.</p>"}, "jobStatus": {"shape": "JobStatus", "documentation": "<p>The status of the bulk import job can be one of following values.</p> <ul> <li> <p> <code>PENDING</code> – IoT SiteWise is waiting for the current bulk import job to finish.</p> </li> <li> <p> <code>CANCELLED</code> – The bulk import job has been canceled.</p> </li> <li> <p> <code>RUNNING</code> – IoT SiteWise is processing your request to import your data from Amazon S3.</p> </li> <li> <p> <code>COMPLETED</code> – IoT SiteWise successfully completed your request to import data from Amazon S3.</p> </li> <li> <p> <code>FAILED</code> – IoT SiteWise couldn't process your request to import data from Amazon S3. You can use logs saved in the specified error report location in Amazon S3 to troubleshoot issues.</p> </li> <li> <p> <code>COMPLETED_WITH_FAILURES</code> – IoT SiteWise completed your request to import data from Amazon S3 with errors. You can use logs saved in the specified error report location in Amazon S3 to troubleshoot issues.</p> </li> </ul>"}, "jobRoleArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the IAM role that allows IoT SiteWise to read Amazon S3 data.</p>"}, "files": {"shape": "Files", "documentation": "<p>The files in the specified Amazon S3 bucket that contain your data.</p>"}, "errorReportLocation": {"shape": "ErrorReportLocation", "documentation": "<p>The Amazon S3 destination where errors associated with the job creation request are saved.</p>"}, "jobConfiguration": {"shape": "JobConfiguration", "documentation": "<p>Contains the configuration information of a job, such as the file format used to save data in Amazon S3.</p>"}, "jobCreationDate": {"shape": "Timestamp", "documentation": "<p>The date the job was created, in Unix epoch TIME.</p>"}, "jobLastUpdateDate": {"shape": "Timestamp", "documentation": "<p>The date the job was last updated, in Unix epoch time.</p>"}}}, "DescribeDashboardRequest": {"type": "structure", "required": ["dashboardId"], "members": {"dashboardId": {"shape": "ID", "documentation": "<p>The ID of the dashboard.</p>", "location": "uri", "locationName": "dashboardId"}}}, "DescribeDashboardResponse": {"type": "structure", "required": ["dashboardId", "dashboardArn", "dashboardName", "projectId", "dashboardDefinition", "dashboardCreationDate", "dashboardLastUpdateDate"], "members": {"dashboardId": {"shape": "ID", "documentation": "<p>The ID of the dashboard.</p>"}, "dashboardArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the dashboard, which has the following format.</p> <p> <code>arn:${Partition}:iotsitewise:${Region}:${Account}:dashboard/${DashboardId}</code> </p>"}, "dashboardName": {"shape": "Name", "documentation": "<p>The name of the dashboard.</p>"}, "projectId": {"shape": "ID", "documentation": "<p>The ID of the project that the dashboard is in.</p>"}, "dashboardDescription": {"shape": "Description", "documentation": "<p>The dashboard's description.</p>"}, "dashboardDefinition": {"shape": "DashboardDefinition", "documentation": "<p>The dashboard's definition JSON literal. For detailed information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/create-dashboards-using-aws-cli.html\">Creating dashboards (CLI)</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "dashboardCreationDate": {"shape": "Timestamp", "documentation": "<p>The date the dashboard was created, in Unix epoch time.</p>"}, "dashboardLastUpdateDate": {"shape": "Timestamp", "documentation": "<p>The date the dashboard was last updated, in Unix epoch time.</p>"}}}, "DescribeDefaultEncryptionConfigurationRequest": {"type": "structure", "members": {}}, "DescribeDefaultEncryptionConfigurationResponse": {"type": "structure", "required": ["encryptionType", "configurationStatus"], "members": {"encryptionType": {"shape": "EncryptionType", "documentation": "<p>The type of encryption used for the encryption configuration.</p>"}, "kmsKeyArn": {"shape": "ARN", "documentation": "<p>The key ARN of the customer managed key used for KMS encryption if you use <code>KMS_BASED_ENCRYPTION</code>.</p>"}, "configurationStatus": {"shape": "ConfigurationStatus", "documentation": "<p>The status of the account configuration. This contains the <code>ConfigurationState</code>. If there's an error, it also contains the <code>ErrorDetails</code>.</p>"}}}, "DescribeGatewayCapabilityConfigurationRequest": {"type": "structure", "required": ["gatewayId", "capabilityNamespace"], "members": {"gatewayId": {"shape": "ID", "documentation": "<p>The ID of the gateway that defines the capability configuration.</p>", "location": "uri", "locationName": "gatewayId"}, "capabilityNamespace": {"shape": "CapabilityNamespace", "documentation": "<p>The namespace of the capability configuration. For example, if you configure OPC-UA sources from the IoT SiteWise console, your OPC-UA capability configuration has the namespace <code>iotsitewise:opcuacollector:version</code>, where <code>version</code> is a number such as <code>1</code>.</p>", "location": "uri", "locationName": "capabilityNamespace"}}}, "DescribeGatewayCapabilityConfigurationResponse": {"type": "structure", "required": ["gatewayId", "capabilityNamespace", "capabilityConfiguration", "capabilitySyncStatus"], "members": {"gatewayId": {"shape": "ID", "documentation": "<p>The ID of the gateway that defines the capability configuration.</p>"}, "capabilityNamespace": {"shape": "CapabilityNamespace", "documentation": "<p>The namespace of the gateway capability.</p>"}, "capabilityConfiguration": {"shape": "CapabilityConfiguration", "documentation": "<p>The JSON document that defines the gateway capability's configuration. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/configure-sources.html#configure-source-cli\">Configuring data sources (CLI)</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "capabilitySyncStatus": {"shape": "CapabilitySyncStatus", "documentation": "<p>The synchronization status of the capability configuration. The sync status can be one of the following:</p> <ul> <li> <p> <code>IN_SYNC</code> – The gateway is running the capability configuration.</p> </li> <li> <p> <code>OUT_OF_SYNC</code> – The gateway hasn't received the capability configuration.</p> </li> <li> <p> <code>SYNC_FAILED</code> – The gateway rejected the capability configuration.</p> </li> </ul>"}}}, "DescribeGatewayRequest": {"type": "structure", "required": ["gatewayId"], "members": {"gatewayId": {"shape": "ID", "documentation": "<p>The ID of the gateway device.</p>", "location": "uri", "locationName": "gatewayId"}}}, "DescribeGatewayResponse": {"type": "structure", "required": ["gatewayId", "gatewayName", "gatewayArn", "gatewayCapabilitySummaries", "creationDate", "lastUpdateDate"], "members": {"gatewayId": {"shape": "ID", "documentation": "<p>The ID of the gateway device.</p>"}, "gatewayName": {"shape": "Name", "documentation": "<p>The name of the gateway.</p>"}, "gatewayArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the gateway, which has the following format.</p> <p> <code>arn:${Partition}:iotsitewise:${Region}:${Account}:gateway/${GatewayId}</code> </p>"}, "gatewayPlatform": {"shape": "GatewayPlatform", "documentation": "<p>The gateway's platform.</p>"}, "gatewayCapabilitySummaries": {"shape": "GatewayCapabilitySummaries", "documentation": "<p>A list of gateway capability summaries that each contain a namespace and status. Each gateway capability defines data sources for the gateway. To retrieve a capability configuration's definition, use <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_DescribeGatewayCapabilityConfiguration.html\">DescribeGatewayCapabilityConfiguration</a>.</p>"}, "creationDate": {"shape": "Timestamp", "documentation": "<p>The date the gateway was created, in Unix epoch time.</p>"}, "lastUpdateDate": {"shape": "Timestamp", "documentation": "<p>The date the gateway was last updated, in Unix epoch time.</p>"}}}, "DescribeLoggingOptionsRequest": {"type": "structure", "members": {}}, "DescribeLoggingOptionsResponse": {"type": "structure", "required": ["loggingOptions"], "members": {"loggingOptions": {"shape": "LoggingOptions", "documentation": "<p>The current logging options.</p>"}}}, "DescribePortalRequest": {"type": "structure", "required": ["portalId"], "members": {"portalId": {"shape": "ID", "documentation": "<p>The ID of the portal.</p>", "location": "uri", "locationName": "portalId"}}}, "DescribePortalResponse": {"type": "structure", "required": ["portalId", "portalArn", "portalName", "portalClientId", "portalStartUrl", "portalContactEmail", "portalStatus", "portalCreationDate", "portalLastUpdateDate"], "members": {"portalId": {"shape": "ID", "documentation": "<p>The ID of the portal.</p>"}, "portalArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the portal, which has the following format.</p> <p> <code>arn:${Partition}:iotsitewise:${Region}:${Account}:portal/${PortalId}</code> </p>"}, "portalName": {"shape": "Name", "documentation": "<p>The name of the portal.</p>"}, "portalDescription": {"shape": "Description", "documentation": "<p>The portal's description.</p>"}, "portalClientId": {"shape": "PortalClientId", "documentation": "<p>The IAM Identity Center application generated client ID (used with IAM Identity Center APIs). IoT SiteWise includes <code>portalClientId</code> for only portals that use IAM Identity Center to authenticate users.</p>"}, "portalStartUrl": {"shape": "Url", "documentation": "<p>The URL for the IoT SiteWise Monitor portal. You can use this URL to access portals that use IAM Identity Center for authentication. For portals that use IAM for authentication, you must use the IoT SiteWise console to get a URL that you can use to access the portal.</p>"}, "portalContactEmail": {"shape": "Email", "documentation": "<p>The Amazon Web Services administrator's contact email address.</p>"}, "portalStatus": {"shape": "PortalStatus", "documentation": "<p>The current status of the portal, which contains a state and any error message.</p>"}, "portalCreationDate": {"shape": "Timestamp", "documentation": "<p>The date the portal was created, in Unix epoch time.</p>"}, "portalLastUpdateDate": {"shape": "Timestamp", "documentation": "<p>The date the portal was last updated, in Unix epoch time.</p>"}, "portalLogoImageLocation": {"shape": "ImageLocation", "documentation": "<p>The portal's logo image, which is available at a URL.</p>"}, "roleArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the service role that allows the portal's users to access your IoT SiteWise resources on your behalf. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/monitor-service-role.html\">Using service roles for IoT SiteWise Monitor</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "portalAuthMode": {"shape": "AuthMode", "documentation": "<p>The service to use to authenticate users to the portal.</p>"}, "notificationSenderEmail": {"shape": "Email", "documentation": "<p>The email address that sends alarm notifications.</p>"}, "alarms": {"shape": "Alarms", "documentation": "<p>Contains the configuration information of an alarm created in an IoT SiteWise Monitor portal.</p>"}}}, "DescribeProjectRequest": {"type": "structure", "required": ["projectId"], "members": {"projectId": {"shape": "ID", "documentation": "<p>The ID of the project.</p>", "location": "uri", "locationName": "projectId"}}}, "DescribeProjectResponse": {"type": "structure", "required": ["projectId", "projectArn", "projectName", "portalId", "projectCreationDate", "projectLastUpdateDate"], "members": {"projectId": {"shape": "ID", "documentation": "<p>The ID of the project.</p>"}, "projectArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the project, which has the following format.</p> <p> <code>arn:${Partition}:iotsitewise:${Region}:${Account}:project/${ProjectId}</code> </p>"}, "projectName": {"shape": "Name", "documentation": "<p>The name of the project.</p>"}, "portalId": {"shape": "ID", "documentation": "<p>The ID of the portal that the project is in.</p>"}, "projectDescription": {"shape": "Description", "documentation": "<p>The project's description.</p>"}, "projectCreationDate": {"shape": "Timestamp", "documentation": "<p>The date the project was created, in Unix epoch time.</p>"}, "projectLastUpdateDate": {"shape": "Timestamp", "documentation": "<p>The date the project was last updated, in Unix epoch time.</p>"}}}, "DescribeStorageConfigurationRequest": {"type": "structure", "members": {}}, "DescribeStorageConfigurationResponse": {"type": "structure", "required": ["storageType", "configurationStatus"], "members": {"storageType": {"shape": "StorageType", "documentation": "<p>The storage tier that you specified for your data. The <code>storageType</code> parameter can be one of the following values:</p> <ul> <li> <p> <code>SITEWISE_DEFAULT_STORAGE</code> – IoT SiteWise saves your data into the hot tier. The hot tier is a service-managed database.</p> </li> <li> <p> <code>MULTI_LAYER_STORAGE</code> – IoT SiteWise saves your data in both the cold tier and the hot tier. The cold tier is a customer-managed Amazon S3 bucket.</p> </li> </ul>"}, "multiLayerStorage": {"shape": "MultiLayerStorage", "documentation": "<p>Contains information about the storage destination.</p>"}, "disassociatedDataStorage": {"shape": "DisassociatedDataStorageState", "documentation": "<p>Contains the storage configuration for time series (data streams) that aren't associated with asset properties. The <code>disassociatedDataStorage</code> can be one of the following values:</p> <ul> <li> <p> <code>ENABLED</code> – IoT SiteWise accepts time series that aren't associated with asset properties.</p> <important> <p>After the <code>disassociatedDataStorage</code> is enabled, you can't disable it.</p> </important> </li> <li> <p> <code>DISABLED</code> – IoT SiteWise doesn't accept time series (data streams) that aren't associated with asset properties.</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/data-streams.html\">Data streams</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "retentionPeriod": {"shape": "RetentionPeriod", "documentation": "<p>How many days your data is kept in the hot tier. By default, your data is kept indefinitely in the hot tier.</p>"}, "configurationStatus": {"shape": "ConfigurationStatus"}, "lastUpdateDate": {"shape": "Timestamp", "documentation": "<p>The date the storage configuration was last updated, in Unix epoch time.</p>"}}}, "DescribeTimeSeriesRequest": {"type": "structure", "members": {"alias": {"shape": "PropertyAlias", "documentation": "<p>The alias that identifies the time series.</p>", "location": "querystring", "locationName": "alias"}, "assetId": {"shape": "ID", "documentation": "<p>The ID of the asset in which the asset property was created.</p>", "location": "querystring", "locationName": "assetId"}, "propertyId": {"shape": "ID", "documentation": "<p>The ID of the asset property.</p>", "location": "querystring", "locationName": "propertyId"}}}, "DescribeTimeSeriesResponse": {"type": "structure", "required": ["timeSeriesId", "dataType", "timeSeriesCreationDate", "timeSeriesLastUpdateDate", "timeSeriesArn"], "members": {"assetId": {"shape": "ID", "documentation": "<p>The ID of the asset in which the asset property was created.</p>"}, "propertyId": {"shape": "ID", "documentation": "<p>The ID of the asset property.</p>"}, "alias": {"shape": "PropertyAlias", "documentation": "<p>The alias that identifies the time series.</p>"}, "timeSeriesId": {"shape": "TimeSeriesId", "documentation": "<p>The ID of the time series.</p>"}, "dataType": {"shape": "PropertyDataType", "documentation": "<p>The data type of the time series.</p> <p>If you specify <code>STRUCT</code>, you must also specify <code>dataTypeSpec</code> to identify the type of the structure for this time series.</p>"}, "dataTypeSpec": {"shape": "Name", "documentation": "<p>The data type of the structure for this time series. This parameter is required for time series that have the <code>STRUCT</code> data type.</p> <p>The options for this parameter depend on the type of the composite model in which you created the asset property that is associated with your time series. Use <code>AWS/ALARM_STATE</code> for alarm state in alarm composite models.</p>"}, "timeSeriesCreationDate": {"shape": "Timestamp", "documentation": "<p>The date that the time series was created, in Unix epoch time.</p>"}, "timeSeriesLastUpdateDate": {"shape": "Timestamp", "documentation": "<p>The date that the time series was last updated, in Unix epoch time.</p>"}, "timeSeriesArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the time series, which has the following format.</p> <p> <code>arn:${Partition}:iotsitewise:${Region}:${Account}:time-series/${TimeSeriesId}</code> </p>"}}}, "Description": {"type": "string", "max": 2048, "min": 1, "pattern": "[^\\u0000-\\u001F\\u007F]+"}, "DetailedError": {"type": "structure", "required": ["code", "message"], "members": {"code": {"shape": "DetailedErrorCode", "documentation": "<p>The error code. </p>"}, "message": {"shape": "DetailedErrorMessage", "documentation": "<p>The error message. </p>"}}, "documentation": "<p>Contains detailed error information. </p>"}, "DetailedErrorCode": {"type": "string", "enum": ["INCOMPATIBLE_COMPUTE_LOCATION", "INCOMPATIBLE_FORWARDING_CONFIGURATION"]}, "DetailedErrorMessage": {"type": "string"}, "DetailedErrors": {"type": "list", "member": {"shape": "DetailedError"}}, "DisassociateAssetsRequest": {"type": "structure", "required": ["assetId", "hierarchyId", "childAssetId"], "members": {"assetId": {"shape": "ID", "documentation": "<p>The ID of the parent asset from which to disassociate the child asset.</p>", "location": "uri", "locationName": "assetId"}, "hierarchyId": {"shape": "ID", "documentation": "<p>The ID of a hierarchy in the parent asset's model. Hierarchies allow different groupings of assets to be formed that all come from the same asset model. You can use the hierarchy ID to identify the correct asset to disassociate. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/asset-hierarchies.html\">Asset hierarchies</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "childAssetId": {"shape": "ID", "documentation": "<p>The ID of the child asset to disassociate.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true}}}, "DisassociateTimeSeriesFromAssetPropertyRequest": {"type": "structure", "required": ["alias", "assetId", "propertyId"], "members": {"alias": {"shape": "PropertyAlias", "documentation": "<p>The alias that identifies the time series.</p>", "location": "querystring", "locationName": "alias"}, "assetId": {"shape": "ID", "documentation": "<p>The ID of the asset in which the asset property was created.</p>", "location": "querystring", "locationName": "assetId"}, "propertyId": {"shape": "ID", "documentation": "<p>The ID of the asset property.</p>", "location": "querystring", "locationName": "propertyId"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true}}}, "DisassociatedDataStorageState": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "Email": {"type": "string", "max": 255, "min": 1, "pattern": "[^@]+@[^@]+"}, "EncryptionType": {"type": "string", "enum": ["SITEWISE_DEFAULT_ENCRYPTION", "KMS_BASED_ENCRYPTION"]}, "EntryId": {"type": "string", "max": 64, "min": 1, "pattern": "^[a-zA-Z0-9_-]+$"}, "ErrorCode": {"type": "string", "enum": ["VALIDATION_ERROR", "INTERNAL_FAILURE"]}, "ErrorDetails": {"type": "structure", "required": ["code", "message"], "members": {"code": {"shape": "ErrorCode", "documentation": "<p>The error code.</p>"}, "message": {"shape": "ErrorMessage", "documentation": "<p>The error message.</p>"}, "details": {"shape": "DetailedErrors", "documentation": "<p> A list of detailed errors. </p>"}}, "documentation": "<p>Contains the details of an IoT SiteWise error.</p>"}, "ErrorMessage": {"type": "string"}, "ErrorReportLocation": {"type": "structure", "required": ["bucket", "prefix"], "members": {"bucket": {"shape": "Bucket", "documentation": "<p>The name of the Amazon S3 bucket to which errors associated with the bulk import job are sent.</p>"}, "prefix": {"shape": "String", "documentation": "<p>Amazon S3 uses the prefix as a folder name to organize data in the bucket. Each Amazon S3 object has a key that is its unique identifier in the bucket. Each object in a bucket has exactly one key. The prefix must end with a forward slash (/). For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/using-prefixes.html\">Organizing objects using prefixes</a> in the <i>Amazon Simple Storage Service User Guide</i>.</p>"}}, "documentation": "<p>The Amazon S3 destination where errors associated with the job creation request are saved.</p>"}, "ExceptionMessage": {"type": "string"}, "ExcludeProperties": {"type": "boolean"}, "Expression": {"type": "string", "max": 1024, "min": 1}, "ExpressionVariable": {"type": "structure", "required": ["name", "value"], "members": {"name": {"shape": "VariableName", "documentation": "<p>The friendly name of the variable to be used in the expression.</p>"}, "value": {"shape": "VariableValue", "documentation": "<p>The variable that identifies an asset property from which to use values.</p>"}}, "documentation": "<p>Contains expression variable information.</p>"}, "ExpressionVariables": {"type": "list", "member": {"shape": "ExpressionVariable"}}, "File": {"type": "structure", "required": ["bucket", "key"], "members": {"bucket": {"shape": "Bucket", "documentation": "<p>The name of the Amazon S3 bucket from which data is imported.</p>"}, "key": {"shape": "String", "documentation": "<p>The key of the Amazon S3 object that contains your data. Each object has a key that is a unique identifier. Each object has exactly one key.</p>"}, "versionId": {"shape": "String", "documentation": "<p>The version ID to identify a specific version of the Amazon S3 object that contains your data.</p>"}}, "documentation": "<p>The file in Amazon S3 where your data is saved. </p>"}, "FileFormat": {"type": "structure", "members": {"csv": {"shape": "Csv", "documentation": "<p>The .csv file format.</p>"}}, "documentation": "<p>The file format of the data.</p>"}, "Files": {"type": "list", "member": {"shape": "File"}}, "ForwardingConfig": {"type": "structure", "required": ["state"], "members": {"state": {"shape": "ForwardingConfigState", "documentation": "<p>The forwarding state for the given property. </p>"}}, "documentation": "<p>The forwarding configuration for a given property.</p>"}, "ForwardingConfigState": {"type": "string", "enum": ["DISABLED", "ENABLED"]}, "GatewayCapabilitySummaries": {"type": "list", "member": {"shape": "GatewayCapabilitySummary"}}, "GatewayCapabilitySummary": {"type": "structure", "required": ["capabilityNamespace", "capabilitySyncStatus"], "members": {"capabilityNamespace": {"shape": "CapabilityNamespace", "documentation": "<p>The namespace of the capability configuration. For example, if you configure OPC-UA sources from the IoT SiteWise console, your OPC-UA capability configuration has the namespace <code>iotsitewise:opcuacollector:version</code>, where <code>version</code> is a number such as <code>1</code>.</p>"}, "capabilitySyncStatus": {"shape": "CapabilitySyncStatus", "documentation": "<p>The synchronization status of the capability configuration. The sync status can be one of the following:</p> <ul> <li> <p> <code>IN_SYNC</code> – The gateway is running the capability configuration.</p> </li> <li> <p> <code>OUT_OF_SYNC</code> – The gateway hasn't received the capability configuration.</p> </li> <li> <p> <code>SYNC_FAILED</code> – The gateway rejected the capability configuration.</p> </li> </ul>"}}, "documentation": "<p>Contains a summary of a gateway capability configuration.</p>"}, "GatewayPlatform": {"type": "structure", "members": {"greengrass": {"shape": "Greengrass", "documentation": "<p>A gateway that runs on IoT Greengrass.</p>"}, "greengrassV2": {"shape": "GreengrassV2", "documentation": "<p>A gateway that runs on IoT Greengrass V2.</p>"}}, "documentation": "<p>Contains a gateway's platform information.</p>"}, "GatewaySummaries": {"type": "list", "member": {"shape": "GatewaySummary"}}, "GatewaySummary": {"type": "structure", "required": ["gatewayId", "gatewayName", "creationDate", "lastUpdateDate"], "members": {"gatewayId": {"shape": "ID", "documentation": "<p>The ID of the gateway device.</p>"}, "gatewayName": {"shape": "Name", "documentation": "<p>The name of the asset.</p>"}, "gatewayPlatform": {"shape": "GatewayPlatform"}, "gatewayCapabilitySummaries": {"shape": "GatewayCapabilitySummaries", "documentation": "<p>A list of gateway capability summaries that each contain a namespace and status. Each gateway capability defines data sources for the gateway. To retrieve a capability configuration's definition, use <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_DescribeGatewayCapabilityConfiguration.html\">DescribeGatewayCapabilityConfiguration</a>.</p>"}, "creationDate": {"shape": "Timestamp", "documentation": "<p>The date the gateway was created, in Unix epoch time.</p>"}, "lastUpdateDate": {"shape": "Timestamp", "documentation": "<p>The date the gateway was last updated, in Unix epoch time.</p>"}}, "documentation": "<p>Contains a summary of a gateway.</p>"}, "GetAssetPropertyAggregatesRequest": {"type": "structure", "required": ["aggregateTypes", "resolution", "startDate", "endDate"], "members": {"assetId": {"shape": "ID", "documentation": "<p>The ID of the asset.</p>", "location": "querystring", "locationName": "assetId"}, "propertyId": {"shape": "ID", "documentation": "<p>The ID of the asset property.</p>", "location": "querystring", "locationName": "propertyId"}, "propertyAlias": {"shape": "AssetPropertyAlias", "documentation": "<p>The alias that identifies the property, such as an OPC-UA server data stream path (for example, <code>/company/windfarm/3/turbine/7/temperature</code>). For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/connect-data-streams.html\">Mapping industrial data streams to asset properties</a> in the <i>IoT SiteWise User Guide</i>.</p>", "location": "querystring", "locationName": "propertyAlias"}, "aggregateTypes": {"shape": "AggregateTypes", "documentation": "<p>The data aggregating function.</p>", "location": "querystring", "locationName": "aggregateTypes"}, "resolution": {"shape": "Resolution", "documentation": "<p>The time interval over which to aggregate data.</p>", "location": "querystring", "locationName": "resolution"}, "qualities": {"shape": "Qualities", "documentation": "<p>The quality by which to filter asset data.</p>", "location": "querystring", "locationName": "qualities"}, "startDate": {"shape": "Timestamp", "documentation": "<p>The exclusive start of the range from which to query historical data, expressed in seconds in Unix epoch time.</p>", "location": "querystring", "locationName": "startDate"}, "endDate": {"shape": "Timestamp", "documentation": "<p>The inclusive end of the range from which to query historical data, expressed in seconds in Unix epoch time.</p>", "location": "querystring", "locationName": "endDate"}, "timeOrdering": {"shape": "TimeOrdering", "documentation": "<p>The chronological sorting order of the requested information.</p> <p>Default: <code>ASCENDING</code> </p>", "location": "querystring", "locationName": "timeOrdering"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "GetAssetPropertyValueAggregatesMaxResults", "documentation": "<p>The maximum number of results to return for each paginated request. A result set is returned in the two cases, whichever occurs first.</p> <ul> <li> <p>The size of the result set is equal to 1 MB.</p> </li> <li> <p>The number of data points in the result set is equal to the value of <code>maxResults</code>. The maximum value of <code>maxResults</code> is 250.</p> </li> </ul>", "location": "querystring", "locationName": "maxResults"}}}, "GetAssetPropertyAggregatesResponse": {"type": "structure", "required": ["aggregatedValues"], "members": {"aggregatedValues": {"shape": "AggregatedValues", "documentation": "<p>The requested aggregated values.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results, or null if there are no additional results.</p>"}}}, "GetAssetPropertyValueAggregatesMaxResults": {"type": "integer", "min": 1}, "GetAssetPropertyValueHistoryMaxResults": {"type": "integer", "min": 1}, "GetAssetPropertyValueHistoryRequest": {"type": "structure", "members": {"assetId": {"shape": "ID", "documentation": "<p>The ID of the asset.</p>", "location": "querystring", "locationName": "assetId"}, "propertyId": {"shape": "ID", "documentation": "<p>The ID of the asset property.</p>", "location": "querystring", "locationName": "propertyId"}, "propertyAlias": {"shape": "AssetPropertyAlias", "documentation": "<p>The alias that identifies the property, such as an OPC-UA server data stream path (for example, <code>/company/windfarm/3/turbine/7/temperature</code>). For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/connect-data-streams.html\">Mapping industrial data streams to asset properties</a> in the <i>IoT SiteWise User Guide</i>.</p>", "location": "querystring", "locationName": "propertyAlias"}, "startDate": {"shape": "Timestamp", "documentation": "<p>The exclusive start of the range from which to query historical data, expressed in seconds in Unix epoch time.</p>", "location": "querystring", "locationName": "startDate"}, "endDate": {"shape": "Timestamp", "documentation": "<p>The inclusive end of the range from which to query historical data, expressed in seconds in Unix epoch time.</p>", "location": "querystring", "locationName": "endDate"}, "qualities": {"shape": "Qualities", "documentation": "<p>The quality by which to filter asset data.</p>", "location": "querystring", "locationName": "qualities"}, "timeOrdering": {"shape": "TimeOrdering", "documentation": "<p>The chronological sorting order of the requested information.</p> <p>Default: <code>ASCENDING</code> </p>", "location": "querystring", "locationName": "timeOrdering"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "GetAssetPropertyValueHistoryMaxResults", "documentation": "<p>The maximum number of results to return for each paginated request. A result set is returned in the two cases, whichever occurs first.</p> <ul> <li> <p>The size of the result set is equal to 4 MB.</p> </li> <li> <p>The number of data points in the result set is equal to the value of <code>maxResults</code>. The maximum value of <code>maxResults</code> is 20000.</p> </li> </ul>", "location": "querystring", "locationName": "maxResults"}}}, "GetAssetPropertyValueHistoryResponse": {"type": "structure", "required": ["assetPropertyValueHistory"], "members": {"assetPropertyValueHistory": {"shape": "AssetPropertyValueHistory", "documentation": "<p>The asset property's value history.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results, or null if there are no additional results.</p>"}}}, "GetAssetPropertyValueRequest": {"type": "structure", "members": {"assetId": {"shape": "ID", "documentation": "<p>The ID of the asset.</p>", "location": "querystring", "locationName": "assetId"}, "propertyId": {"shape": "ID", "documentation": "<p>The ID of the asset property.</p>", "location": "querystring", "locationName": "propertyId"}, "propertyAlias": {"shape": "AssetPropertyAlias", "documentation": "<p>The alias that identifies the property, such as an OPC-UA server data stream path (for example, <code>/company/windfarm/3/turbine/7/temperature</code>). For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/connect-data-streams.html\">Mapping industrial data streams to asset properties</a> in the <i>IoT SiteWise User Guide</i>.</p>", "location": "querystring", "locationName": "propertyAlias"}}}, "GetAssetPropertyValueResponse": {"type": "structure", "members": {"propertyValue": {"shape": "AssetPropertyValue", "documentation": "<p>The current asset property value.</p>"}}}, "GetInterpolatedAssetPropertyValuesRequest": {"type": "structure", "required": ["startTimeInSeconds", "endTimeInSeconds", "quality", "intervalInSeconds", "type"], "members": {"assetId": {"shape": "ID", "documentation": "<p>The ID of the asset.</p>", "location": "querystring", "locationName": "assetId"}, "propertyId": {"shape": "ID", "documentation": "<p>The ID of the asset property.</p>", "location": "querystring", "locationName": "propertyId"}, "propertyAlias": {"shape": "AssetPropertyAlias", "documentation": "<p>The alias that identifies the property, such as an OPC-UA server data stream path (for example, <code>/company/windfarm/3/turbine/7/temperature</code>). For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/connect-data-streams.html\">Mapping industrial data streams to asset properties</a> in the <i>IoT SiteWise User Guide</i>.</p>", "location": "querystring", "locationName": "propertyAlias"}, "startTimeInSeconds": {"shape": "TimeInSeconds", "documentation": "<p>The exclusive start of the range from which to interpolate data, expressed in seconds in Unix epoch time.</p>", "location": "querystring", "locationName": "startTimeInSeconds"}, "startTimeOffsetInNanos": {"shape": "OffsetInNanos", "documentation": "<p>The nanosecond offset converted from <code>startTimeInSeconds</code>.</p>", "location": "querystring", "locationName": "startTimeOffsetInNanos"}, "endTimeInSeconds": {"shape": "TimeInSeconds", "documentation": "<p>The inclusive end of the range from which to interpolate data, expressed in seconds in Unix epoch time.</p>", "location": "querystring", "locationName": "endTimeInSeconds"}, "endTimeOffsetInNanos": {"shape": "OffsetInNanos", "documentation": "<p>The nanosecond offset converted from <code>endTimeInSeconds</code>.</p>", "location": "querystring", "locationName": "endTimeOffsetInNanos"}, "quality": {"shape": "Quality", "documentation": "<p>The quality of the asset property value. You can use this parameter as a filter to choose only the asset property values that have a specific quality.</p>", "location": "querystring", "locationName": "quality"}, "intervalInSeconds": {"shape": "IntervalInSeconds", "documentation": "<p>The time interval in seconds over which to interpolate data. Each interval starts when the previous one ends.</p>", "location": "querystring", "locationName": "intervalInSeconds"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxInterpolatedResults", "documentation": "<p>The maximum number of results to return for each paginated request. If not specified, the default value is 10.</p>", "location": "querystring", "locationName": "maxResults"}, "type": {"shape": "InterpolationType", "documentation": "<p>The interpolation type.</p> <p>Valid values: <code>LINEAR_INTERPOLATION | LOCF_INTERPOLATION</code> </p> <ul> <li> <p> <code>LINEAR_INTERPOLATION</code> – Estimates missing data using <a href=\"https://en.wikipedia.org/wiki/Linear_interpolation\">linear interpolation</a>.</p> <p>For example, you can use this operation to return the interpolated temperature values for a wind turbine every 24 hours over a duration of 7 days. If the interpolation starts July 1, 2021, at 9 AM, IoT SiteWise returns the first interpolated value on July 2, 2021, at 9 AM, the second interpolated value on July 3, 2021, at 9 AM, and so on.</p> </li> <li> <p> <code>LOCF_INTERPOLATION</code> – Estimates missing data using last observation carried forward interpolation</p> <p>If no data point is found for an interval, IoT SiteWise returns the last observed data point for the previous interval and carries forward this interpolated value until a new data point is found.</p> <p>For example, you can get the state of an on-off valve every 24 hours over a duration of 7 days. If the interpolation starts July 1, 2021, at 9 AM, IoT SiteWise returns the last observed data point between July 1, 2021, at 9 AM and July 2, 2021, at 9 AM as the first interpolated value. If a data point isn't found after 9 AM on July 2, 2021, IoT SiteWise uses the same interpolated value for the rest of the days.</p> </li> </ul>", "location": "querystring", "locationName": "type"}, "intervalWindowInSeconds": {"shape": "IntervalWindowInSeconds", "documentation": "<p>The query interval for the window, in seconds. IoT SiteWise computes each interpolated value by using data points from the timestamp of each interval, minus the window to the timestamp of each interval plus the window. If not specified, the window ranges between the start time minus the interval and the end time plus the interval.</p> <note> <ul> <li> <p>If you specify a value for the <code>intervalWindowInSeconds</code> parameter, the value for the <code>type</code> parameter must be <code>LINEAR_INTERPOLATION</code>.</p> </li> <li> <p>If a data point isn't found during the specified query window, IoT SiteWise won't return an interpolated value for the interval. This indicates that there's a gap in the ingested data points.</p> </li> </ul> </note> <p>For example, you can get the interpolated temperature values for a wind turbine every 24 hours over a duration of 7 days. If the interpolation starts on July 1, 2021, at 9 AM with a window of 2 hours, IoT SiteWise uses the data points from 7 AM (9 AM minus 2 hours) to 11 AM (9 AM plus 2 hours) on July 2, 2021 to compute the first interpolated value. Next, IoT SiteWise uses the data points from 7 AM (9 AM minus 2 hours) to 11 AM (9 AM plus 2 hours) on July 3, 2021 to compute the second interpolated value, and so on. </p>", "location": "querystring", "locationName": "intervalWindowInSeconds"}}}, "GetInterpolatedAssetPropertyValuesResponse": {"type": "structure", "required": ["interpolatedAssetPropertyValues"], "members": {"interpolatedAssetPropertyValues": {"shape": "InterpolatedAssetPropertyValues", "documentation": "<p>The requested interpolated values.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results, or null if there are no additional results.</p>"}}}, "Greengrass": {"type": "structure", "required": ["groupArn"], "members": {"groupArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the Greengrass group. For more information about how to find a group's ARN, see <a href=\"https://docs.aws.amazon.com/greengrass/latest/apireference/listgroups-get.html\">ListGroups</a> and <a href=\"https://docs.aws.amazon.com/greengrass/latest/apireference/getgroup-get.html\">GetGroup</a> in the <i>IoT Greengrass API Reference</i>.</p>"}}, "documentation": "<p>Contains details for a gateway that runs on IoT Greengrass. To create a gateway that runs on IoT Greengrass, you must add the IoT SiteWise connector to a Greengrass group and deploy it. Your Greengrass group must also have permissions to upload data to IoT SiteWise. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/gateway-connector.html\">Ingesting data using a gateway</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "GreengrassV2": {"type": "structure", "required": ["coreDeviceThingName"], "members": {"coreDeviceThingName": {"shape": "CoreDeviceThingName", "documentation": "<p>The name of the IoT thing for your IoT Greengrass V2 core device.</p>"}}, "documentation": "<p>Contains details for a gateway that runs on IoT Greengrass V2. To create a gateway that runs on IoT Greengrass V2, you must deploy the IoT SiteWise Edge component to your gateway device. Your <a href=\"https://docs.aws.amazon.com/greengrass/v2/developerguide/device-service-role.html\">Greengrass device role</a> must use the <code>AWSIoTSiteWiseEdgeAccess</code> policy. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/sw-gateways.html\">Using IoT SiteWise at the edge</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "GroupIdentity": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "IdentityId", "documentation": "<p>The IAM Identity Center ID of the group.</p>"}}, "documentation": "<p>Contains information for a group identity in an access policy.</p>"}, "IAMRoleIdentity": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "ARN", "documentation": "<p>The ARN of the IAM role. For more information, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_identifiers.html\">IAM ARNs</a> in the <i>IAM User Guide</i>.</p>"}}, "documentation": "<p>Contains information about an Identity and Access Management role. For more information, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/id_roles.html\">IAM roles</a> in the <i>IAM User Guide</i>.</p>"}, "IAMUserIdentity": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "ARN", "documentation": "<p>The ARN of the IAM user. For more information, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_identifiers.html\">IAM ARNs</a> in the <i>IAM User Guide</i>.</p> <note> <p>If you delete the IAM user, access policies that contain this identity include an empty <code>arn</code>. You can delete the access policy for the IAM user that no longer exists.</p> </note>"}}, "documentation": "<p>Contains information about an Identity and Access Management user.</p>"}, "ID": {"type": "string", "max": 36, "min": 36, "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"}, "IDs": {"type": "list", "member": {"shape": "ID"}, "max": 100, "min": 1}, "Identity": {"type": "structure", "members": {"user": {"shape": "UserIdentity", "documentation": "<p>An IAM Identity Center user identity.</p>"}, "group": {"shape": "GroupIdentity", "documentation": "<p>An IAM Identity Center group identity.</p>"}, "iamUser": {"shape": "IAMUserIdentity", "documentation": "<p>An IAM user identity.</p>"}, "iamRole": {"shape": "IAMRoleIdentity", "documentation": "<p>An IAM role identity.</p>"}}, "documentation": "<p>Contains an identity that can access an IoT SiteWise Monitor resource.</p> <note> <p>Currently, you can't use Amazon Web Services APIs to retrieve IAM Identity Center identity IDs. You can find the IAM Identity Center identity IDs in the URL of user and group pages in the <a href=\"https://console.aws.amazon.com/singlesignon\">IAM Identity Center console</a>.</p> </note>"}, "IdentityId": {"type": "string", "max": 256, "min": 1, "pattern": "\\S+"}, "IdentityType": {"type": "string", "enum": ["USER", "GROUP", "IAM"]}, "Image": {"type": "structure", "members": {"id": {"shape": "ID", "documentation": "<p>The ID of an existing image. Specify this parameter to keep an existing image.</p>"}, "file": {"shape": "ImageFile"}}, "documentation": "<p>Contains an image that is one of the following:</p> <ul> <li> <p>An image file. Choose this option to upload a new image.</p> </li> <li> <p>The ID of an existing image. Choose this option to keep an existing image.</p> </li> </ul>"}, "ImageFile": {"type": "structure", "required": ["data", "type"], "members": {"data": {"shape": "ImageFileData", "documentation": "<p>The image file contents, represented as a base64-encoded string. The file size must be less than 1 MB.</p>"}, "type": {"shape": "ImageFileType", "documentation": "<p>The file type of the image.</p>"}}, "documentation": "<p>Contains an image file.</p>"}, "ImageFileData": {"type": "blob", "max": 1500000, "min": 1}, "ImageFileType": {"type": "string", "enum": ["PNG"]}, "ImageLocation": {"type": "structure", "required": ["id", "url"], "members": {"id": {"shape": "ID", "documentation": "<p>The ID of the image.</p>"}, "url": {"shape": "Url", "documentation": "<p>The URL where the image is available. The URL is valid for 15 minutes so that you can view and download the image</p>"}}, "documentation": "<p>Contains an image that is uploaded to IoT SiteWise and available at a URL.</p>"}, "InternalFailureException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>IoT SiteWise can't process your request right now. Try again later.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "InterpolatedAssetPropertyValue": {"type": "structure", "required": ["timestamp", "value"], "members": {"timestamp": {"shape": "TimeInNanos"}, "value": {"shape": "<PERSON><PERSON><PERSON>"}}, "documentation": "<p>Contains information about an interpolated asset property value.</p>"}, "InterpolatedAssetPropertyValues": {"type": "list", "member": {"shape": "InterpolatedAssetPropertyValue"}}, "InterpolationType": {"type": "string", "max": 256, "min": 1}, "Interval": {"type": "string", "max": 23, "min": 2}, "IntervalInSeconds": {"type": "long", "max": 320000000, "min": 1}, "IntervalWindowInSeconds": {"type": "long", "max": 320000000, "min": 1}, "InvalidRequestException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The request isn't valid. This can occur if your request contains malformed JSON or unsupported characters. Check your request and try again.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "JobConfiguration": {"type": "structure", "required": ["fileFormat"], "members": {"fileFormat": {"shape": "FileFormat", "documentation": "<p>The file format of the data in Amazon S3.</p>"}}, "documentation": "<p>Contains the configuration information of a job, such as the file format used to save data in Amazon S3.</p>"}, "JobStatus": {"type": "string", "enum": ["PENDING", "CANCELLED", "RUNNING", "COMPLETED", "FAILED", "COMPLETED_WITH_FAILURES"]}, "JobSummaries": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "JobSummary": {"type": "structure", "required": ["id", "name", "status"], "members": {"id": {"shape": "ID", "documentation": "<p>The ID of the job.</p>"}, "name": {"shape": "Name", "documentation": "<p>The unique name that helps identify the job request.</p>"}, "status": {"shape": "JobStatus", "documentation": "<p>The status of the bulk import job can be one of following values.</p> <ul> <li> <p> <code>PENDING</code> – IoT SiteWise is waiting for the current bulk import job to finish.</p> </li> <li> <p> <code>CANCELLED</code> – The bulk import job has been canceled.</p> </li> <li> <p> <code>RUNNING</code> – IoT SiteWise is processing your request to import your data from Amazon S3.</p> </li> <li> <p> <code>COMPLETED</code> – IoT SiteWise successfully completed your request to import data from Amazon S3.</p> </li> <li> <p> <code>FAILED</code> – IoT SiteWise couldn't process your request to import data from Amazon S3. You can use logs saved in the specified error report location in Amazon S3 to troubleshoot issues.</p> </li> <li> <p> <code>COMPLETED_WITH_FAILURES</code> – IoT SiteWise completed your request to import data from Amazon S3 with errors. You can use logs saved in the specified error report location in Amazon S3 to troubleshoot issues.</p> </li> </ul>"}}, "documentation": "<p>Contains a job summary information.</p>"}, "KmsKeyId": {"type": "string", "max": 2048, "min": 1}, "LimitExceededException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>You've reached the limit for a resource. For example, this can occur if you're trying to associate more than the allowed number of child assets or attempting to create more than the allowed number of properties for an asset model.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/quotas.html\">Quotas</a> in the <i>IoT SiteWise User Guide</i>.</p>", "error": {"httpStatusCode": 410}, "exception": true}, "ListAccessPoliciesRequest": {"type": "structure", "members": {"identityType": {"shape": "IdentityType", "documentation": "<p>The type of identity (IAM Identity Center user, IAM Identity Center group, or IAM user). This parameter is required if you specify <code>identityId</code>.</p>", "location": "querystring", "locationName": "identityType"}, "identityId": {"shape": "IdentityId", "documentation": "<p>The ID of the identity. This parameter is required if you specify <code>USER</code> or <code>GROUP</code> for <code>identityType</code>.</p>", "location": "querystring", "locationName": "identityId"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The type of resource (portal or project). This parameter is required if you specify <code>resourceId</code>.</p>", "location": "querystring", "locationName": "resourceType"}, "resourceId": {"shape": "ID", "documentation": "<p>The ID of the resource. This parameter is required if you specify <code>resourceType</code>.</p>", "location": "querystring", "locationName": "resourceId"}, "iamArn": {"shape": "ARN", "documentation": "<p>The ARN of the IAM user. For more information, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_identifiers.html\">IAM ARNs</a> in the <i>IAM User Guide</i>. This parameter is required if you specify <code>IAM</code> for <code>identityType</code>.</p>", "location": "querystring", "locationName": "iamArn"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return for each paginated request.</p> <p>Default: 50</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListAccessPoliciesResponse": {"type": "structure", "required": ["accessPolicySummaries"], "members": {"accessPolicySummaries": {"shape": "AccessPolicySummaries", "documentation": "<p>A list that summarizes each access policy.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results, or null if there are no additional results.</p>"}}}, "ListAssetModelPropertiesFilter": {"type": "string", "enum": ["ALL", "BASE"]}, "ListAssetModelPropertiesRequest": {"type": "structure", "required": ["assetModelId"], "members": {"assetModelId": {"shape": "ID", "documentation": "<p>The ID of the asset model.</p>", "location": "uri", "locationName": "assetModelId"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return for each paginated request. If not specified, the default value is 50.</p>", "location": "querystring", "locationName": "maxResults"}, "filter": {"shape": "ListAssetModelPropertiesFilter", "documentation": "<p> Filters the requested list of asset model properties. You can choose one of the following options:</p> <ul> <li> <p> <code>ALL</code> – The list includes all asset model properties for a given asset model ID. </p> </li> <li> <p> <code>BASE</code> – The list includes only base asset model properties for a given asset model ID. </p> </li> </ul> <p>Default: <code>BASE</code> </p>", "location": "querystring", "locationName": "filter"}}}, "ListAssetModelPropertiesResponse": {"type": "structure", "required": ["assetModelPropertySummaries"], "members": {"assetModelPropertySummaries": {"shape": "AssetModelPropertySummaries", "documentation": "<p>A list that summarizes the properties associated with the specified asset model.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results, or null if there are no additional results.</p>"}}}, "ListAssetModelsRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return for each paginated request.</p> <p>Default: 50</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListAssetModelsResponse": {"type": "structure", "required": ["assetModelSummaries"], "members": {"assetModelSummaries": {"shape": "AssetModelSummaries", "documentation": "<p>A list that summarizes each asset model.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results, or null if there are no additional results.</p>"}}}, "ListAssetPropertiesFilter": {"type": "string", "enum": ["ALL", "BASE"]}, "ListAssetPropertiesRequest": {"type": "structure", "required": ["assetId"], "members": {"assetId": {"shape": "ID", "documentation": "<p>The ID of the asset.</p>", "location": "uri", "locationName": "assetId"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return for each paginated request. If not specified, the default value is 50.</p>", "location": "querystring", "locationName": "maxResults"}, "filter": {"shape": "ListAssetPropertiesFilter", "documentation": "<p> Filters the requested list of asset properties. You can choose one of the following options:</p> <ul> <li> <p> <code>ALL</code> – The list includes all asset properties for a given asset model ID. </p> </li> <li> <p> <code>BASE</code> – The list includes only base asset properties for a given asset model ID. </p> </li> </ul> <p>Default: <code>BASE</code> </p>", "location": "querystring", "locationName": "filter"}}}, "ListAssetPropertiesResponse": {"type": "structure", "required": ["assetPropertySummaries"], "members": {"assetPropertySummaries": {"shape": "AssetPropertySummaries", "documentation": "<p>A list that summarizes the properties associated with the specified asset.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results, or null if there are no additional results.</p>"}}}, "ListAssetRelationshipsRequest": {"type": "structure", "required": ["assetId", "traversalType"], "members": {"assetId": {"shape": "ID", "documentation": "<p>The ID of the asset.</p>", "location": "uri", "locationName": "assetId"}, "traversalType": {"shape": "TraversalType", "documentation": "<p>The type of traversal to use to identify asset relationships. Choose the following option:</p> <ul> <li> <p> <code>PATH_TO_ROOT</code> – Identify the asset's parent assets up to the root asset. The asset that you specify in <code>assetId</code> is the first result in the list of <code>assetRelationshipSummaries</code>, and the root asset is the last result.</p> </li> </ul>", "location": "querystring", "locationName": "traversalType"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return for each paginated request.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListAssetRelationshipsResponse": {"type": "structure", "required": ["assetRelationshipSummaries"], "members": {"assetRelationshipSummaries": {"shape": "AssetRelationshipSummaries", "documentation": "<p>A list that summarizes each asset relationship.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results, or null if there are no additional results.</p>"}}}, "ListAssetsFilter": {"type": "string", "enum": ["ALL", "TOP_LEVEL"]}, "ListAssetsRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return for each paginated request.</p> <p>Default: 50</p>", "location": "querystring", "locationName": "maxResults"}, "assetModelId": {"shape": "ID", "documentation": "<p>The ID of the asset model by which to filter the list of assets. This parameter is required if you choose <code>ALL</code> for <code>filter</code>.</p>", "location": "querystring", "locationName": "assetModelId"}, "filter": {"shape": "ListAssetsFilter", "documentation": "<p>The filter for the requested list of assets. Choose one of the following options:</p> <ul> <li> <p> <code>ALL</code> – The list includes all assets for a given asset model ID. The <code>assetModelId</code> parameter is required if you filter by <code>ALL</code>.</p> </li> <li> <p> <code>TOP_LEVEL</code> – The list includes only top-level assets in the asset hierarchy tree.</p> </li> </ul> <p>Default: <code>ALL</code> </p>", "location": "querystring", "locationName": "filter"}}}, "ListAssetsResponse": {"type": "structure", "required": ["assetSummaries"], "members": {"assetSummaries": {"shape": "AssetSummaries", "documentation": "<p>A list that summarizes each asset.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results, or null if there are no additional results.</p>"}}}, "ListAssociatedAssetsRequest": {"type": "structure", "required": ["assetId"], "members": {"assetId": {"shape": "ID", "documentation": "<p>The ID of the asset to query.</p>", "location": "uri", "locationName": "assetId"}, "hierarchyId": {"shape": "ID", "documentation": "<p>The ID of the hierarchy by which child assets are associated to the asset. To find a hierarchy ID, use the <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_DescribeAsset.html\">DescribeAsset</a> or <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_DescribeAssetModel.html\">DescribeAssetModel</a> operations. This parameter is required if you choose <code>CHILD</code> for <code>traversalDirection</code>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/asset-hierarchies.html\">Asset hierarchies</a> in the <i>IoT SiteWise User Guide</i>.</p>", "location": "querystring", "locationName": "hierarchyId"}, "traversalDirection": {"shape": "TraversalDirection", "documentation": "<p>The direction to list associated assets. Choose one of the following options:</p> <ul> <li> <p> <code>CHILD</code> – The list includes all child assets associated to the asset. The <code>hierarchyId</code> parameter is required if you choose <code>CHILD</code>.</p> </li> <li> <p> <code>PARENT</code> – The list includes the asset's parent asset.</p> </li> </ul> <p>Default: <code>CHILD</code> </p>", "location": "querystring", "locationName": "traversalDirection"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return for each paginated request.</p> <p>Default: 50</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListAssociatedAssetsResponse": {"type": "structure", "required": ["assetSummaries"], "members": {"assetSummaries": {"shape": "AssociatedAssetsSummaries", "documentation": "<p>A list that summarizes the associated assets.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results, or null if there are no additional results.</p>"}}}, "ListBulkImportJobsFilter": {"type": "string", "enum": ["ALL", "PENDING", "RUNNING", "CANCELLED", "FAILED", "COMPLETED_WITH_FAILURES", "COMPLETED"]}, "ListBulkImportJobsRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return for each paginated request.</p>", "location": "querystring", "locationName": "maxResults"}, "filter": {"shape": "ListBulkImportJobsFilter", "documentation": "<p>You can use a filter to select the bulk import jobs that you want to retrieve.</p>", "location": "querystring", "locationName": "filter"}}}, "ListBulkImportJobsResponse": {"type": "structure", "required": ["jobSummaries"], "members": {"jobSummaries": {"shape": "JobSummaries", "documentation": "<p>One or more job summaries to list.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results, or null if there are no additional results.</p>"}}}, "ListDashboardsRequest": {"type": "structure", "required": ["projectId"], "members": {"projectId": {"shape": "ID", "documentation": "<p>The ID of the project.</p>", "location": "querystring", "locationName": "projectId"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return for each paginated request.</p> <p>Default: 50</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListDashboardsResponse": {"type": "structure", "required": ["dashboardSummaries"], "members": {"dashboardSummaries": {"shape": "DashboardSummaries", "documentation": "<p>A list that summarizes each dashboard in the project.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results, or null if there are no additional results.</p>"}}}, "ListGatewaysRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return for each paginated request.</p> <p>Default: 50</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListGatewaysResponse": {"type": "structure", "required": ["gatewaySummaries"], "members": {"gatewaySummaries": {"shape": "GatewaySummaries", "documentation": "<p>A list that summarizes each gateway.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results, or null if there are no additional results.</p>"}}}, "ListPortalsRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return for each paginated request.</p> <p>Default: 50</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListPortalsResponse": {"type": "structure", "members": {"portalSummaries": {"shape": "PortalSummaries", "documentation": "<p>A list that summarizes each portal.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results, or null if there are no additional results.</p>"}}}, "ListProjectAssetsRequest": {"type": "structure", "required": ["projectId"], "members": {"projectId": {"shape": "ID", "documentation": "<p>The ID of the project.</p>", "location": "uri", "locationName": "projectId"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return for each paginated request.</p> <p>Default: 50</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListProjectAssetsResponse": {"type": "structure", "required": ["assetIds"], "members": {"assetIds": {"shape": "AssetIDs", "documentation": "<p>A list that contains the IDs of each asset associated with the project.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results, or null if there are no additional results.</p>"}}}, "ListProjectsRequest": {"type": "structure", "required": ["portalId"], "members": {"portalId": {"shape": "ID", "documentation": "<p>The ID of the portal.</p>", "location": "querystring", "locationName": "portalId"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return for each paginated request.</p> <p>Default: 50</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListProjectsResponse": {"type": "structure", "required": ["projectSummaries"], "members": {"projectSummaries": {"shape": "ProjectSummaries", "documentation": "<p>A list that summarizes each project in the portal.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results, or null if there are no additional results.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "AmazonResourceName", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the resource.</p>", "location": "querystring", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagMap", "documentation": "<p>The list of key-value pairs that contain metadata for the resource. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/tag-resources.html\">Tagging your IoT SiteWise resources</a> in the <i>IoT SiteWise User Guide</i>.</p>"}}}, "ListTimeSeriesRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return for each paginated request.</p>", "location": "querystring", "locationName": "maxResults"}, "assetId": {"shape": "ID", "documentation": "<p>The ID of the asset in which the asset property was created.</p>", "location": "querystring", "locationName": "assetId"}, "aliasPrefix": {"shape": "PropertyAlias", "documentation": "<p>The alias prefix of the time series.</p>", "location": "querystring", "locationName": "aliasPrefix"}, "timeSeriesType": {"shape": "ListTimeSeriesType", "documentation": "<p>The type of the time series. The time series type can be one of the following values:</p> <ul> <li> <p> <code>ASSOCIATED</code> – The time series is associated with an asset property.</p> </li> <li> <p> <code>DISASSOCIATED</code> – The time series isn't associated with any asset property.</p> </li> </ul>", "location": "querystring", "locationName": "timeSeriesType"}}}, "ListTimeSeriesResponse": {"type": "structure", "required": ["TimeSeriesSummaries"], "members": {"TimeSeriesSummaries": {"shape": "TimeSeriesSummaries", "documentation": "<p>One or more time series summaries to list.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results, or null if there are no additional results.</p>"}}}, "ListTimeSeriesType": {"type": "string", "enum": ["ASSOCIATED", "DISASSOCIATED"]}, "LoggingLevel": {"type": "string", "enum": ["ERROR", "INFO", "OFF"]}, "LoggingOptions": {"type": "structure", "required": ["level"], "members": {"level": {"shape": "LoggingLevel", "documentation": "<p>The IoT SiteWise logging verbosity level.</p>"}}, "documentation": "<p>Contains logging options.</p>"}, "Macro": {"type": "string", "max": 256, "min": 1, "pattern": "[^\\u0000-\\u001F\\u007F]+"}, "MaxInterpolatedResults": {"type": "integer", "min": 1}, "MaxResults": {"type": "integer", "max": 250, "min": 1}, "Measurement": {"type": "structure", "members": {"processingConfig": {"shape": "MeasurementProcessingConfig", "documentation": "<p>The processing configuration for the given measurement property. You can configure measurements to be kept at the edge or forwarded to the Amazon Web Services Cloud. By default, measurements are forwarded to the cloud.</p>"}}, "documentation": "<p>Contains an asset measurement property. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/asset-properties.html#measurements\">Measurements</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "MeasurementProcessingConfig": {"type": "structure", "required": ["forwardingConfig"], "members": {"forwardingConfig": {"shape": "ForwardingConfig", "documentation": "<p>The forwarding configuration for the given measurement property. </p>"}}, "documentation": "<p>The processing configuration for the given measurement property. You can configure measurements to be kept at the edge or forwarded to the Amazon Web Services Cloud. By default, measurements are forwarded to the cloud.</p>"}, "Metric": {"type": "structure", "required": ["expression", "variables", "window"], "members": {"expression": {"shape": "Expression", "documentation": "<p>The mathematical expression that defines the metric aggregation function. You can specify up to 10 variables per expression. You can specify up to 10 functions per expression. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/quotas.html\">Quotas</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "variables": {"shape": "ExpressionVariables", "documentation": "<p>The list of variables used in the expression.</p>"}, "window": {"shape": "MetricWindow", "documentation": "<p>The window (time interval) over which IoT SiteWise computes the metric's aggregation expression. IoT SiteWise computes one data point per <code>window</code>.</p>"}, "processingConfig": {"shape": "MetricProcessingConfig", "documentation": "<p>The processing configuration for the given metric property. You can configure metrics to be computed at the edge or in the Amazon Web Services Cloud. By default, metrics are forwarded to the cloud.</p>"}}, "documentation": "<p>Contains an asset metric property. With metrics, you can calculate aggregate functions, such as an average, maximum, or minimum, as specified through an expression. A metric maps several values to a single value (such as a sum).</p> <p>The maximum number of dependent/cascading variables used in any one metric calculation is 10. Therefore, a <i>root</i> metric can have up to 10 cascading metrics in its computational dependency tree. Additionally, a metric can only have a data type of <code>DOUBLE</code> and consume properties with data types of <code>INTEGER</code> or <code>DOUBLE</code>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/asset-properties.html#metrics\">Metrics</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "MetricProcessingConfig": {"type": "structure", "required": ["computeLocation"], "members": {"computeLocation": {"shape": "ComputeLocation", "documentation": "<p>The compute location for the given metric property. </p>"}}, "documentation": "<p>The processing configuration for the given metric property. You can configure metrics to be computed at the edge or in the Amazon Web Services Cloud. By default, metrics are forwarded to the cloud.</p>"}, "MetricWindow": {"type": "structure", "members": {"tumbling": {"shape": "TumblingWindow", "documentation": "<p>The tumbling time interval window.</p>"}}, "documentation": "<p>Contains a time interval window used for data aggregate computations (for example, average, sum, count, and so on).</p>"}, "MonitorErrorCode": {"type": "string", "enum": ["INTERNAL_FAILURE", "VALIDATION_ERROR", "LIMIT_EXCEEDED"]}, "MonitorErrorDetails": {"type": "structure", "members": {"code": {"shape": "MonitorErrorCode", "documentation": "<p>The error code.</p>"}, "message": {"shape": "MonitorErrorMessage", "documentation": "<p>The error message.</p>"}}, "documentation": "<p>Contains IoT SiteWise Monitor error details.</p>"}, "MonitorErrorMessage": {"type": "string"}, "MultiLayerStorage": {"type": "structure", "required": ["customerManagedS3Storage"], "members": {"customerManagedS3Storage": {"shape": "CustomerManagedS3Storage", "documentation": "<p>Contains information about a customer managed Amazon S3 bucket.</p>"}}, "documentation": "<p>Contains information about the storage destination.</p>"}, "Name": {"type": "string", "max": 256, "min": 1, "pattern": "[^\\u0000-\\u001F\\u007F]+"}, "NextToken": {"type": "string", "max": 4096, "min": 1, "pattern": "[A-Za-z0-9+/=]+"}, "NumberOfDays": {"type": "integer", "min": 30}, "Offset": {"type": "string", "max": 25, "min": 2}, "OffsetInNanos": {"type": "integer", "max": 999999999, "min": 0}, "Permission": {"type": "string", "enum": ["ADMINISTRATOR", "VIEWER"]}, "PortalClientId": {"type": "string", "max": 256, "min": 1, "pattern": "^[!-~]*"}, "PortalResource": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "ID", "documentation": "<p>The ID of the portal.</p>"}}, "documentation": "<p>Identifies an IoT SiteWise Monitor portal.</p>"}, "PortalState": {"type": "string", "enum": ["CREATING", "UPDATING", "DELETING", "ACTIVE", "FAILED"]}, "PortalStatus": {"type": "structure", "required": ["state"], "members": {"state": {"shape": "PortalState", "documentation": "<p>The current state of the portal.</p>"}, "error": {"shape": "MonitorErrorDetails", "documentation": "<p>Contains associated error information, if any.</p>"}}, "documentation": "<p>Contains information about the current status of a portal.</p>"}, "PortalSummaries": {"type": "list", "member": {"shape": "PortalSummary"}}, "PortalSummary": {"type": "structure", "required": ["id", "name", "startUrl", "status"], "members": {"id": {"shape": "ID", "documentation": "<p>The ID of the portal.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the portal.</p>"}, "description": {"shape": "Description", "documentation": "<p>The portal's description.</p>"}, "startUrl": {"shape": "Url", "documentation": "<p>The URL for the IoT SiteWise Monitor portal. You can use this URL to access portals that use IAM Identity Center for authentication. For portals that use IAM for authentication, you must use the IoT SiteWise console to get a URL that you can use to access the portal.</p>"}, "creationDate": {"shape": "Timestamp", "documentation": "<p>The date the portal was created, in Unix epoch time.</p>"}, "lastUpdateDate": {"shape": "Timestamp", "documentation": "<p>The date the portal was last updated, in Unix epoch time.</p>"}, "roleArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the service role that allows the portal's users to access your IoT SiteWise resources on your behalf. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/monitor-service-role.html\">Using service roles for IoT SiteWise Monitor</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "status": {"shape": "PortalStatus"}}, "documentation": "<p>Contains a portal summary.</p>"}, "ProjectResource": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "ID", "documentation": "<p>The ID of the project.</p>"}}, "documentation": "<p>Identifies a specific IoT SiteWise Monitor project.</p>"}, "ProjectSummaries": {"type": "list", "member": {"shape": "ProjectSummary"}}, "ProjectSummary": {"type": "structure", "required": ["id", "name"], "members": {"id": {"shape": "ID", "documentation": "<p>The ID of the project.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the project.</p>"}, "description": {"shape": "Description", "documentation": "<p>The project's description.</p>"}, "creationDate": {"shape": "Timestamp", "documentation": "<p>The date the project was created, in Unix epoch time.</p>"}, "lastUpdateDate": {"shape": "Timestamp", "documentation": "<p>The date the project was last updated, in Unix epoch time.</p>"}}, "documentation": "<p>Contains project summary information.</p>"}, "Property": {"type": "structure", "required": ["id", "name", "dataType"], "members": {"id": {"shape": "ID", "documentation": "<p>The ID of the asset property.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the property.</p>"}, "alias": {"shape": "PropertyAlias", "documentation": "<p>The alias that identifies the property, such as an OPC-UA server data stream path (for example, <code>/company/windfarm/3/turbine/7/temperature</code>). For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/connect-data-streams.html\">Mapping industrial data streams to asset properties</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "notification": {"shape": "PropertyNotification", "documentation": "<p>The asset property's notification topic and state. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_UpdateAssetProperty.html\">UpdateAssetProperty</a>.</p>"}, "dataType": {"shape": "PropertyDataType", "documentation": "<p>The property data type.</p>"}, "unit": {"shape": "PropertyUnit", "documentation": "<p>The unit (such as <code>Newtons</code> or <code>RPM</code>) of the asset property.</p>"}, "type": {"shape": "PropertyType", "documentation": "<p>The property type (see <code>PropertyType</code>). A property contains one type.</p>"}}, "documentation": "<p>Contains asset property information.</p>"}, "PropertyAlias": {"type": "string", "min": 1, "pattern": "[^\\u0000-\\u001F\\u007F]+"}, "PropertyDataType": {"type": "string", "enum": ["STRING", "INTEGER", "DOUBLE", "BOOLEAN", "STRUCT"]}, "PropertyNotification": {"type": "structure", "required": ["topic", "state"], "members": {"topic": {"shape": "PropertyNotificationTopic", "documentation": "<p>The MQTT topic to which IoT SiteWise publishes property value update notifications.</p>"}, "state": {"shape": "PropertyNotificationState", "documentation": "<p>The current notification state.</p>"}}, "documentation": "<p>Contains asset property value notification information. When the notification state is enabled, IoT SiteWise publishes property value updates to a unique MQTT topic. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/interact-with-other-services.html\">Interacting with other services</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "PropertyNotificationState": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "PropertyNotificationTopic": {"type": "string"}, "PropertyType": {"type": "structure", "members": {"attribute": {"shape": "Attribute", "documentation": "<p>Specifies an asset attribute property. An attribute generally contains static information, such as the serial number of an <a href=\"https://en.wikipedia.org/wiki/Internet_of_things#Industrial_applications\">IIoT</a> wind turbine.</p>"}, "measurement": {"shape": "Measurement", "documentation": "<p>Specifies an asset measurement property. A measurement represents a device's raw sensor data stream, such as timestamped temperature values or timestamped power values.</p>"}, "transform": {"shape": "Transform", "documentation": "<p>Specifies an asset transform property. A transform contains a mathematical expression that maps a property's data points from one form to another, such as a unit conversion from Celsius to Fahrenheit.</p>"}, "metric": {"shape": "Metric", "documentation": "<p>Specifies an asset metric property. A metric contains a mathematical expression that uses aggregate functions to process all input data points over a time interval and output a single data point, such as to calculate the average hourly temperature.</p>"}}, "documentation": "<p>Contains a property type, which can be one of <code>attribute</code>, <code>measurement</code>, <code>metric</code>, or <code>transform</code>.</p>"}, "PropertyUnit": {"type": "string", "max": 256, "min": 1, "pattern": "[^\\u0000-\\u001F\\u007F]+"}, "PropertyValueBooleanValue": {"type": "boolean"}, "PropertyValueDoubleValue": {"type": "double"}, "PropertyValueIntegerValue": {"type": "integer"}, "PropertyValueStringValue": {"type": "string"}, "PutAssetPropertyValueEntries": {"type": "list", "member": {"shape": "PutAssetPropertyValueEntry"}}, "PutAssetPropertyValueEntry": {"type": "structure", "required": ["entryId", "propertyValues"], "members": {"entryId": {"shape": "EntryId", "documentation": "<p>The user specified ID for the entry. You can use this ID to identify which entries failed.</p>"}, "assetId": {"shape": "ID", "documentation": "<p>The ID of the asset to update.</p>"}, "propertyId": {"shape": "ID", "documentation": "<p>The ID of the asset property for this entry.</p>"}, "propertyAlias": {"shape": "AssetPropertyAlias", "documentation": "<p>The alias that identifies the property, such as an OPC-UA server data stream path (for example, <code>/company/windfarm/3/turbine/7/temperature</code>). For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/connect-data-streams.html\">Mapping industrial data streams to asset properties</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "propertyValues": {"shape": "Asset<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The list of property values to upload. You can specify up to 10 <code>propertyValues</code> array elements. </p>"}}, "documentation": "<p>Contains a list of value updates for an asset property in the list of asset entries consumed by the <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_BatchPutAssetPropertyValue.html\">BatchPutAssetPropertyValue</a> API operation.</p>"}, "PutDefaultEncryptionConfigurationRequest": {"type": "structure", "required": ["encryptionType"], "members": {"encryptionType": {"shape": "EncryptionType", "documentation": "<p>The type of encryption used for the encryption configuration.</p>"}, "kmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>The Key ID of the customer managed key used for KMS encryption. This is required if you use <code>KMS_BASED_ENCRYPTION</code>.</p>"}}}, "PutDefaultEncryptionConfigurationResponse": {"type": "structure", "required": ["encryptionType", "configurationStatus"], "members": {"encryptionType": {"shape": "EncryptionType", "documentation": "<p>The type of encryption used for the encryption configuration.</p>"}, "kmsKeyArn": {"shape": "ARN", "documentation": "<p>The Key ARN of the KMS key used for KMS encryption if you use <code>KMS_BASED_ENCRYPTION</code>.</p>"}, "configurationStatus": {"shape": "ConfigurationStatus", "documentation": "<p>The status of the account configuration. This contains the <code>ConfigurationState</code>. If there is an error, it also contains the <code>ErrorDetails</code>.</p>"}}}, "PutLoggingOptionsRequest": {"type": "structure", "required": ["loggingOptions"], "members": {"loggingOptions": {"shape": "LoggingOptions", "documentation": "<p>The logging options to set.</p>"}}}, "PutLoggingOptionsResponse": {"type": "structure", "members": {}}, "PutStorageConfigurationRequest": {"type": "structure", "required": ["storageType"], "members": {"storageType": {"shape": "StorageType", "documentation": "<p>The storage tier that you specified for your data. The <code>storageType</code> parameter can be one of the following values:</p> <ul> <li> <p> <code>SITEWISE_DEFAULT_STORAGE</code> – IoT SiteWise saves your data into the hot tier. The hot tier is a service-managed database.</p> </li> <li> <p> <code>MULTI_LAYER_STORAGE</code> – IoT SiteWise saves your data in both the cold tier and the hot tier. The cold tier is a customer-managed Amazon S3 bucket.</p> </li> </ul>"}, "multiLayerStorage": {"shape": "MultiLayerStorage", "documentation": "<p>Identifies a storage destination. If you specified <code>MULTI_LAYER_STORAGE</code> for the storage type, you must specify a <code>MultiLayerStorage</code> object.</p>"}, "disassociatedDataStorage": {"shape": "DisassociatedDataStorageState", "documentation": "<p>Contains the storage configuration for time series (data streams) that aren't associated with asset properties. The <code>disassociatedDataStorage</code> can be one of the following values:</p> <ul> <li> <p> <code>ENABLED</code> – IoT SiteWise accepts time series that aren't associated with asset properties.</p> <important> <p>After the <code>disassociatedDataStorage</code> is enabled, you can't disable it.</p> </important> </li> <li> <p> <code>DISABLED</code> – IoT SiteWise doesn't accept time series (data streams) that aren't associated with asset properties.</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/data-streams.html\">Data streams</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "retentionPeriod": {"shape": "RetentionPeriod"}}}, "PutStorageConfigurationResponse": {"type": "structure", "required": ["storageType", "configurationStatus"], "members": {"storageType": {"shape": "StorageType", "documentation": "<p>The storage tier that you specified for your data. The <code>storageType</code> parameter can be one of the following values:</p> <ul> <li> <p> <code>SITEWISE_DEFAULT_STORAGE</code> – IoT SiteWise saves your data into the hot tier. The hot tier is a service-managed database.</p> </li> <li> <p> <code>MULTI_LAYER_STORAGE</code> – IoT SiteWise saves your data in both the cold tier and the hot tier. The cold tier is a customer-managed Amazon S3 bucket.</p> </li> </ul>"}, "multiLayerStorage": {"shape": "MultiLayerStorage", "documentation": "<p>Contains information about the storage destination.</p>"}, "disassociatedDataStorage": {"shape": "DisassociatedDataStorageState", "documentation": "<p>Contains the storage configuration for time series (data streams) that aren't associated with asset properties. The <code>disassociatedDataStorage</code> can be one of the following values:</p> <ul> <li> <p> <code>ENABLED</code> – IoT SiteWise accepts time series that aren't associated with asset properties.</p> <important> <p>After the <code>disassociatedDataStorage</code> is enabled, you can't disable it.</p> </important> </li> <li> <p> <code>DISABLED</code> – IoT SiteWise doesn't accept time series (data streams) that aren't associated with asset properties.</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/data-streams.html\">Data streams</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "retentionPeriod": {"shape": "RetentionPeriod"}, "configurationStatus": {"shape": "ConfigurationStatus"}}}, "Qualities": {"type": "list", "member": {"shape": "Quality"}, "max": 1, "min": 1}, "Quality": {"type": "string", "enum": ["GOOD", "BAD", "UNCERTAIN"]}, "Resolution": {"type": "string", "max": 3, "min": 2, "pattern": "1m|15m|1h|1d"}, "Resource": {"type": "structure", "members": {"portal": {"shape": "PortalResource", "documentation": "<p>A portal resource.</p>"}, "project": {"shape": "ProjectResource", "documentation": "<p>A project resource.</p>"}}, "documentation": "<p>Contains an IoT SiteWise Monitor resource ID for a portal or project.</p>"}, "ResourceAlreadyExistsException": {"type": "structure", "required": ["message", "resourceId", "resourceArn"], "members": {"message": {"shape": "ErrorMessage"}, "resourceId": {"shape": "ResourceId", "documentation": "<p>The ID of the resource that already exists.</p>"}, "resourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource that already exists.</p>"}}, "documentation": "<p>The resource already exists.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "ResourceArn": {"type": "string"}, "ResourceId": {"type": "string"}, "ResourceNotFoundException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The requested resource can't be found.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "ResourceType": {"type": "string", "enum": ["PORTAL", "PROJECT"]}, "RetentionPeriod": {"type": "structure", "members": {"numberOfDays": {"shape": "NumberOfDays", "documentation": "<p>The number of days that your data is kept.</p> <note> <p>If you specified a value for this parameter, the <code>unlimited</code> parameter must be <code>false</code>.</p> </note>"}, "unlimited": {"shape": "Unlimited", "documentation": "<p>If true, your data is kept indefinitely.</p> <note> <p>If configured to <code>true</code>, you must not specify a value for the <code>numberOfDays</code> parameter.</p> </note>"}}, "documentation": "<p>How many days your data is kept in the hot tier. By default, your data is kept indefinitely in the hot tier.</p>"}, "SSOApplicationId": {"type": "string", "max": 64, "min": 1, "pattern": "^[!-~]*"}, "ServiceUnavailableException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The requested service is unavailable.</p>", "error": {"httpStatusCode": 503}, "exception": true, "fault": true}, "StorageType": {"type": "string", "enum": ["SITEWISE_DEFAULT_STORAGE", "MULTI_LAYER_STORAGE"]}, "String": {"type": "string"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 0}, "TagMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "max": 50, "min": 1}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "AmazonResourceName", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the resource to tag.</p>", "location": "querystring", "locationName": "resourceArn"}, "tags": {"shape": "TagMap", "documentation": "<p>A list of key-value pairs that contain metadata for the resource. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/tag-resources.html\">Tagging your IoT SiteWise resources</a> in the <i>IoT SiteWise User Guide</i>.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>Your request exceeded a rate limit. For example, you might have exceeded the number of IoT SiteWise assets that can be created per second, the allowed number of messages per second, and so on.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/quotas.html\">Quotas</a> in the <i>IoT SiteWise User Guide</i>.</p>", "error": {"httpStatusCode": 429}, "exception": true}, "TimeInNanos": {"type": "structure", "required": ["timeInSeconds"], "members": {"timeInSeconds": {"shape": "TimeInSeconds", "documentation": "<p>The timestamp date, in seconds, in the Unix epoch format. Fractional nanosecond data is provided by <code>offsetInNanos</code>.</p>"}, "offsetInNanos": {"shape": "OffsetInNanos", "documentation": "<p>The nanosecond offset from <code>timeInSeconds</code>.</p>"}}, "documentation": "<p>Contains a timestamp with optional nanosecond granularity.</p>"}, "TimeInSeconds": {"type": "long", "max": 9223372036854774, "min": 1}, "TimeOrdering": {"type": "string", "enum": ["ASCENDING", "DESCENDING"]}, "TimeSeriesId": {"type": "string", "max": 73, "min": 36}, "TimeSeriesSummaries": {"type": "list", "member": {"shape": "TimeSeriesSummary"}}, "TimeSeriesSummary": {"type": "structure", "required": ["timeSeriesId", "dataType", "timeSeriesCreationDate", "timeSeriesLastUpdateDate", "timeSeriesArn"], "members": {"assetId": {"shape": "ID", "documentation": "<p>The ID of the asset in which the asset property was created.</p>"}, "propertyId": {"shape": "ID", "documentation": "<p>The ID of the asset property.</p>"}, "alias": {"shape": "PropertyAlias", "documentation": "<p>The alias that identifies the time series.</p>"}, "timeSeriesId": {"shape": "TimeSeriesId", "documentation": "<p>The ID of the time series.</p>"}, "dataType": {"shape": "PropertyDataType", "documentation": "<p>The data type of the time series.</p> <p>If you specify <code>STRUCT</code>, you must also specify <code>dataTypeSpec</code> to identify the type of the structure for this time series.</p>"}, "dataTypeSpec": {"shape": "Name", "documentation": "<p>The data type of the structure for this time series. This parameter is required for time series that have the <code>STRUCT</code> data type.</p> <p>The options for this parameter depend on the type of the composite model in which you created the asset property that is associated with your time series. Use <code>AWS/ALARM_STATE</code> for alarm state in alarm composite models.</p>"}, "timeSeriesCreationDate": {"shape": "Timestamp", "documentation": "<p>The date that the time series was created, in Unix epoch time.</p>"}, "timeSeriesLastUpdateDate": {"shape": "Timestamp", "documentation": "<p>The date that the time series was last updated, in Unix epoch time.</p>"}, "timeSeriesArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the time series, which has the following format.</p> <p> <code>arn:${Partition}:iotsitewise:${Region}:${Account}:time-series/${TimeSeriesId}</code> </p>"}}, "documentation": "<p>Contains a summary of a time series (data stream).</p>"}, "Timestamp": {"type": "timestamp"}, "Timestamps": {"type": "list", "member": {"shape": "TimeInNanos"}}, "TooManyTagsException": {"type": "structure", "members": {"message": {"shape": "ExceptionMessage"}, "resourceName": {"shape": "AmazonResourceName", "documentation": "<p>The name of the resource with too many tags.</p>"}}, "documentation": "<p>You've reached the limit for the number of tags allowed for a resource. For more information, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html#tag-conventions\">Tag naming limits and requirements</a> in the <i>Amazon Web Services General Reference</i>.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "Transform": {"type": "structure", "required": ["expression", "variables"], "members": {"expression": {"shape": "Expression", "documentation": "<p>The mathematical expression that defines the transformation function. You can specify up to 10 variables per expression. You can specify up to 10 functions per expression. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/quotas.html\">Quotas</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "variables": {"shape": "ExpressionVariables", "documentation": "<p>The list of variables used in the expression.</p>"}, "processingConfig": {"shape": "TransformProcessingConfig", "documentation": "<p>The processing configuration for the given transform property. You can configure transforms to be kept at the edge or forwarded to the Amazon Web Services Cloud. You can also configure transforms to be computed at the edge or in the cloud.</p>"}}, "documentation": "<p>Contains an asset transform property. A transform is a one-to-one mapping of a property's data points from one form to another. For example, you can use a transform to convert a Celsius data stream to Fahrenheit by applying the transformation expression to each data point of the Celsius stream. A transform can only have a data type of <code>DOUBLE</code> and consume properties with data types of <code>INTEGER</code> or <code>DOUBLE</code>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/asset-properties.html#transforms\">Transforms</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "TransformProcessingConfig": {"type": "structure", "required": ["computeLocation"], "members": {"computeLocation": {"shape": "ComputeLocation", "documentation": "<p>The compute location for the given transform property. </p>"}, "forwardingConfig": {"shape": "ForwardingConfig"}}, "documentation": "<p>The processing configuration for the given transform property. You can configure transforms to be kept at the edge or forwarded to the Amazon Web Services Cloud. You can also configure transforms to be computed at the edge or in the cloud.</p>"}, "TraversalDirection": {"type": "string", "enum": ["PARENT", "CHILD"]}, "TraversalType": {"type": "string", "enum": ["PATH_TO_ROOT"]}, "TumblingWindow": {"type": "structure", "required": ["interval"], "members": {"interval": {"shape": "Interval", "documentation": "<p>The time interval for the tumbling window. The interval time must be between 1 minute and 1 week.</p> <p>IoT SiteWise computes the <code>1w</code> interval the end of Sunday at midnight each week (UTC), the <code>1d</code> interval at the end of each day at midnight (UTC), the <code>1h</code> interval at the end of each hour, and so on. </p> <p>When IoT SiteWise aggregates data points for metric computations, the start of each interval is exclusive and the end of each interval is inclusive. IoT SiteWise places the computed data point at the end of the interval.</p>"}, "offset": {"shape": "Offset", "documentation": "<p>The offset for the tumbling window. The <code>offset</code> parameter accepts the following:</p> <ul> <li> <p>The offset time.</p> <p>For example, if you specify <code>18h</code> for <code>offset</code> and <code>1d</code> for <code>interval</code>, IoT SiteWise aggregates data in one of the following ways:</p> <ul> <li> <p>If you create the metric before or at 6 PM (UTC), you get the first aggregation result at 6 PM (UTC) on the day when you create the metric.</p> </li> <li> <p>If you create the metric after 6 PM (UTC), you get the first aggregation result at 6 PM (UTC) the next day.</p> </li> </ul> </li> <li> <p>The ISO 8601 format.</p> <p>For example, if you specify <code>PT18H</code> for <code>offset</code> and <code>1d</code> for <code>interval</code>, IoT SiteWise aggregates data in one of the following ways:</p> <ul> <li> <p>If you create the metric before or at 6 PM (UTC), you get the first aggregation result at 6 PM (UTC) on the day when you create the metric.</p> </li> <li> <p>If you create the metric after 6 PM (UTC), you get the first aggregation result at 6 PM (UTC) the next day.</p> </li> </ul> </li> <li> <p>The 24-hour clock.</p> <p>For example, if you specify <code>00:03:00</code> for <code>offset</code>, <code>5m</code> for <code>interval</code>, and you create the metric at 2 PM (UTC), you get the first aggregation result at 2:03 PM (UTC). You get the second aggregation result at 2:08 PM (UTC). </p> </li> <li> <p>The offset time zone.</p> <p>For example, if you specify <code>2021-07-23T18:00-08</code> for <code>offset</code> and <code>1d</code> for <code>interval</code>, IoT SiteWise aggregates data in one of the following ways:</p> <ul> <li> <p>If you create the metric before or at 6 PM (PST), you get the first aggregation result at 6 PM (PST) on the day when you create the metric.</p> </li> <li> <p>If you create the metric after 6 PM (PST), you get the first aggregation result at 6 PM (PST) the next day.</p> </li> </ul> </li> </ul>"}}, "documentation": "<p>Contains a tumbling window, which is a repeating fixed-sized, non-overlapping, and contiguous time window. You can use this window in metrics to aggregate data from properties and other assets.</p> <p>You can use <code>m</code>, <code>h</code>, <code>d</code>, and <code>w</code> when you specify an interval or offset. Note that <code>m</code> represents minutes, <code>h</code> represents hours, <code>d</code> represents days, and <code>w</code> represents weeks. You can also use <code>s</code> to represent seconds in <code>offset</code>.</p> <p>The <code>interval</code> and <code>offset</code> parameters support the <a href=\"https://en.wikipedia.org/wiki/ISO_8601\">ISO 8601 format</a>. For example, <code>PT5S</code> represents 5 seconds, <code>PT5M</code> represents 5 minutes, and <code>PT5H</code> represents 5 hours.</p>"}, "UnauthorizedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>You are not authorized.</p>", "error": {"httpStatusCode": 401}, "exception": true}, "Unlimited": {"type": "boolean"}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "AmazonResourceName", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of the resource to untag.</p>", "location": "querystring", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>A list of keys for tags to remove from the resource.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateAccessPolicyRequest": {"type": "structure", "required": ["accessPolicyId", "accessPolicyIdentity", "accessPolicyResource", "accessPolicyPermission"], "members": {"accessPolicyId": {"shape": "ID", "documentation": "<p>The ID of the access policy.</p>", "location": "uri", "locationName": "accessPolicyId"}, "accessPolicyIdentity": {"shape": "Identity", "documentation": "<p>The identity for this access policy. Choose an IAM Identity Center user, an IAM Identity Center group, or an IAM user.</p>"}, "accessPolicyResource": {"shape": "Resource", "documentation": "<p>The IoT SiteWise Monitor resource for this access policy. Choose either a portal or a project.</p>"}, "accessPolicyPermission": {"shape": "Permission", "documentation": "<p>The permission level for this access policy. Note that a project <code>ADMINISTRATOR</code> is also known as a project owner.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true}}}, "UpdateAccessPolicyResponse": {"type": "structure", "members": {}}, "UpdateAssetModelRequest": {"type": "structure", "required": ["assetModelId", "assetModelName"], "members": {"assetModelId": {"shape": "ID", "documentation": "<p>The ID of the asset model to update.</p>", "location": "uri", "locationName": "assetModelId"}, "assetModelName": {"shape": "Name", "documentation": "<p>A unique, friendly name for the asset model.</p>"}, "assetModelDescription": {"shape": "Description", "documentation": "<p>A description for the asset model.</p>"}, "assetModelProperties": {"shape": "AssetModelProperties", "documentation": "<p>The updated property definitions of the asset model. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/asset-properties.html\">Asset properties</a> in the <i>IoT SiteWise User Guide</i>.</p> <p>You can specify up to 200 properties per asset model. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/quotas.html\">Quotas</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "assetModelHierarchies": {"shape": "AssetModelHierarchies", "documentation": "<p>The updated hierarchy definitions of the asset model. Each hierarchy specifies an asset model whose assets can be children of any other assets created from this asset model. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/asset-hierarchies.html\">Asset hierarchies</a> in the <i>IoT SiteWise User Guide</i>.</p> <p>You can specify up to 10 hierarchies per asset model. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/quotas.html\">Quotas</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "assetModelCompositeModels": {"shape": "AssetModelCompositeModels", "documentation": "<p>The composite asset models that are part of this asset model. Composite asset models are asset models that contain specific properties. Each composite model has a type that defines the properties that the composite model supports. Use composite asset models to define alarms on this asset model.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true}}}, "UpdateAssetModelResponse": {"type": "structure", "required": ["assetModelStatus"], "members": {"assetModelStatus": {"shape": "AssetModelStatus", "documentation": "<p>The status of the asset model, which contains a state (<code>UPDATING</code> after successfully calling this operation) and any error message.</p>"}}}, "UpdateAssetPropertyRequest": {"type": "structure", "required": ["assetId", "propertyId"], "members": {"assetId": {"shape": "ID", "documentation": "<p>The ID of the asset to be updated.</p>", "location": "uri", "locationName": "assetId"}, "propertyId": {"shape": "ID", "documentation": "<p>The ID of the asset property to be updated.</p>", "location": "uri", "locationName": "propertyId"}, "propertyAlias": {"shape": "PropertyAlias", "documentation": "<p>The alias that identifies the property, such as an OPC-UA server data stream path (for example, <code>/company/windfarm/3/turbine/7/temperature</code>). For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/connect-data-streams.html\">Mapping industrial data streams to asset properties</a> in the <i>IoT SiteWise User Guide</i>.</p> <p>If you omit this parameter, the alias is removed from the property.</p>"}, "propertyNotificationState": {"shape": "PropertyNotificationState", "documentation": "<p>The MQTT notification state (enabled or disabled) for this asset property. When the notification state is enabled, IoT SiteWise publishes property value updates to a unique MQTT topic. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/interact-with-other-services.html\">Interacting with other services</a> in the <i>IoT SiteWise User Guide</i>.</p> <p>If you omit this parameter, the notification state is set to <code>DISABLED</code>.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true}, "propertyUnit": {"shape": "PropertyUnit", "documentation": "<p>The unit of measure (such as Newtons or RPM) of the asset property. If you don't specify a value for this parameter, the service uses the value of the <code>assetModelProperty</code> in the asset model.</p>"}}}, "UpdateAssetRequest": {"type": "structure", "required": ["assetId", "assetName"], "members": {"assetId": {"shape": "ID", "documentation": "<p>The ID of the asset to update.</p>", "location": "uri", "locationName": "assetId"}, "assetName": {"shape": "Name", "documentation": "<p>A friendly name for the asset.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true}, "assetDescription": {"shape": "Description", "documentation": "<p>A description for the asset.</p>"}}}, "UpdateAssetResponse": {"type": "structure", "required": ["assetStatus"], "members": {"assetStatus": {"shape": "AssetStatus", "documentation": "<p>The status of the asset, which contains a state (<code>UPDATING</code> after successfully calling this operation) and any error message.</p>"}}}, "UpdateDashboardRequest": {"type": "structure", "required": ["dashboardId", "dashboardName", "dashboardDefinition"], "members": {"dashboardId": {"shape": "ID", "documentation": "<p>The ID of the dashboard to update.</p>", "location": "uri", "locationName": "dashboardId"}, "dashboardName": {"shape": "Name", "documentation": "<p>A new friendly name for the dashboard.</p>"}, "dashboardDescription": {"shape": "Description", "documentation": "<p>A new description for the dashboard.</p>"}, "dashboardDefinition": {"shape": "DashboardDefinition", "documentation": "<p>The new dashboard definition, as specified in a JSON literal. For detailed information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/create-dashboards-using-aws-cli.html\">Creating dashboards (CLI)</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true}}}, "UpdateDashboardResponse": {"type": "structure", "members": {}}, "UpdateGatewayCapabilityConfigurationRequest": {"type": "structure", "required": ["gatewayId", "capabilityNamespace", "capabilityConfiguration"], "members": {"gatewayId": {"shape": "ID", "documentation": "<p>The ID of the gateway to be updated.</p>", "location": "uri", "locationName": "gatewayId"}, "capabilityNamespace": {"shape": "CapabilityNamespace", "documentation": "<p>The namespace of the gateway capability configuration to be updated. For example, if you configure OPC-UA sources from the IoT SiteWise console, your OPC-UA capability configuration has the namespace <code>iotsitewise:opcuacollector:version</code>, where <code>version</code> is a number such as <code>1</code>.</p>"}, "capabilityConfiguration": {"shape": "CapabilityConfiguration", "documentation": "<p>The JSON document that defines the configuration for the gateway capability. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/configure-sources.html#configure-source-cli\">Configuring data sources (CLI)</a> in the <i>IoT SiteWise User Guide</i>.</p>"}}}, "UpdateGatewayCapabilityConfigurationResponse": {"type": "structure", "required": ["capabilityNamespace", "capabilitySyncStatus"], "members": {"capabilityNamespace": {"shape": "CapabilityNamespace", "documentation": "<p>The namespace of the gateway capability.</p>"}, "capabilitySyncStatus": {"shape": "CapabilitySyncStatus", "documentation": "<p>The synchronization status of the capability configuration. The sync status can be one of the following:</p> <ul> <li> <p> <code>IN_SYNC</code> – The gateway is running the capability configuration.</p> </li> <li> <p> <code>OUT_OF_SYNC</code> – The gateway hasn't received the capability configuration.</p> </li> <li> <p> <code>SYNC_FAILED</code> – The gateway rejected the capability configuration.</p> </li> </ul> <p>After you update a capability configuration, its sync status is <code>OUT_OF_SYNC</code> until the gateway receives and applies or rejects the updated configuration.</p>"}}}, "UpdateGatewayRequest": {"type": "structure", "required": ["gatewayId", "gatewayName"], "members": {"gatewayId": {"shape": "ID", "documentation": "<p>The ID of the gateway to update.</p>", "location": "uri", "locationName": "gatewayId"}, "gatewayName": {"shape": "Name", "documentation": "<p>A unique, friendly name for the gateway.</p>"}}}, "UpdatePortalRequest": {"type": "structure", "required": ["portalId", "portalName", "portalContactEmail", "roleArn"], "members": {"portalId": {"shape": "ID", "documentation": "<p>The ID of the portal to update.</p>", "location": "uri", "locationName": "portalId"}, "portalName": {"shape": "Name", "documentation": "<p>A new friendly name for the portal.</p>"}, "portalDescription": {"shape": "Description", "documentation": "<p>A new description for the portal.</p>"}, "portalContactEmail": {"shape": "Email", "documentation": "<p>The Amazon Web Services administrator's contact email address.</p>"}, "portalLogoImage": {"shape": "Image"}, "roleArn": {"shape": "ARN", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">ARN</a> of a service role that allows the portal's users to access your IoT SiteWise resources on your behalf. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/monitor-service-role.html\">Using service roles for IoT SiteWise Monitor</a> in the <i>IoT SiteWise User Guide</i>.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true}, "notificationSenderEmail": {"shape": "Email", "documentation": "<p>The email address that sends alarm notifications.</p>"}, "alarms": {"shape": "Alarms", "documentation": "<p>Contains the configuration information of an alarm created in an IoT SiteWise Monitor portal. You can use the alarm to monitor an asset property and get notified when the asset property value is outside a specified range. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/appguide/monitor-alarms.html\">Monitoring with alarms</a> in the <i>IoT SiteWise Application Guide</i>.</p>"}}}, "UpdatePortalResponse": {"type": "structure", "required": ["portalStatus"], "members": {"portalStatus": {"shape": "PortalStatus", "documentation": "<p>The status of the portal, which contains a state (<code>UPDATING</code> after successfully calling this operation) and any error message.</p>"}}}, "UpdateProjectRequest": {"type": "structure", "required": ["projectId", "projectName"], "members": {"projectId": {"shape": "ID", "documentation": "<p>The ID of the project to update.</p>", "location": "uri", "locationName": "projectId"}, "projectName": {"shape": "Name", "documentation": "<p>A new friendly name for the project.</p>"}, "projectDescription": {"shape": "Description", "documentation": "<p>A new description for the project.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique case-sensitive identifier that you can provide to ensure the idempotency of the request. Don't reuse this client token if a new idempotent request is required.</p>", "idempotencyToken": true}}}, "UpdateProjectResponse": {"type": "structure", "members": {}}, "Url": {"type": "string", "max": 256, "min": 1, "pattern": "^(http|https)\\://\\S+"}, "UserIdentity": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "IdentityId", "documentation": "<p>The IAM Identity Center ID of the user.</p>"}}, "documentation": "<p>Contains information for a user identity in an access policy.</p>"}, "VariableName": {"type": "string", "max": 64, "min": 1, "pattern": "^[a-z][a-z0-9_]*$"}, "VariableValue": {"type": "structure", "required": ["propertyId"], "members": {"propertyId": {"shape": "Macro", "documentation": "<p>The ID of the property to use as the variable. You can use the property <code>name</code> if it's from the same asset model.</p>"}, "hierarchyId": {"shape": "Macro", "documentation": "<p>The ID of the hierarchy to query for the property ID. You can use the hierarchy's name instead of the hierarchy's ID.</p> <p>You use a hierarchy ID instead of a model ID because you can have several hierarchies using the same model and therefore the same <code>propertyId</code>. For example, you might have separately grouped assets that come from the same asset model. For more information, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/asset-hierarchies.html\">Asset hierarchies</a> in the <i>IoT SiteWise User Guide</i>.</p>"}}, "documentation": "<p>Identifies a property value used in an expression.</p>"}, "Variant": {"type": "structure", "members": {"stringValue": {"shape": "PropertyValueStringValue", "documentation": "<p>Asset property data of type string (sequence of characters).</p>"}, "integerValue": {"shape": "PropertyValueIntegerValue", "documentation": "<p>Asset property data of type integer (whole number).</p>"}, "doubleValue": {"shape": "PropertyValueDoubleValue", "documentation": "<p>Asset property data of type double (floating point number).</p>"}, "booleanValue": {"shape": "PropertyValueBooleanValue", "documentation": "<p>Asset property data of type Boolean (true or false).</p>"}}, "documentation": "<p>Contains an asset property value (of a single type only).</p>"}}, "documentation": "<p>Welcome to the IoT SiteWise API Reference. IoT SiteWise is an Amazon Web Services service that connects <a href=\"https://en.wikipedia.org/wiki/Internet_of_things#Industrial_applications\">Industrial Internet of Things (IIoT)</a> devices to the power of the Amazon Web Services Cloud. For more information, see the <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/\">IoT SiteWise User Guide</a>. For information about IoT SiteWise quotas, see <a href=\"https://docs.aws.amazon.com/iot-sitewise/latest/userguide/quotas.html\">Quotas</a> in the <i>IoT SiteWise User Guide</i>.</p>"}