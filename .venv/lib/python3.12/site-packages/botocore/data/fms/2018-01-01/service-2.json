{"version": "2.0", "metadata": {"apiVersion": "2018-01-01", "endpointPrefix": "fms", "jsonVersion": "1.1", "protocol": "json", "serviceAbbreviation": "FMS", "serviceFullName": "Firewall Management Service", "serviceId": "FMS", "signatureVersion": "v4", "targetPrefix": "AWSFMS_20180101", "uid": "fms-2018-01-01"}, "operations": {"AssociateAdminAccount": {"name": "AssociateAdminAccount", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AssociateAdminAccountRequest"}, "errors": [{"shape": "InvalidOperationException"}, {"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalErrorException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Sets a Firewall Manager default administrator account. The Firewall Manager default administrator account can manage third-party firewalls and has full administrative scope that allows administration of all policy types, accounts, organizational units, and Regions. This account must be a member account of the organization in Organizations whose resources you want to protect.</p> <p>For information about working with Firewall Manager administrator accounts, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/fms-administrators.html\">Managing Firewall Manager administrators</a> in the <i>Firewall Manager Developer Guide</i>.</p>"}, "AssociateThirdPartyFirewall": {"name": "AssociateThirdPartyFirewall", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AssociateThirdPartyFirewallRequest"}, "output": {"shape": "AssociateThirdPartyFirewallResponse"}, "errors": [{"shape": "InvalidOperationException"}, {"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Sets the Firewall Manager policy administrator as a tenant administrator of a third-party firewall service. A tenant is an instance of the third-party firewall service that's associated with your Amazon Web Services customer account.</p>"}, "BatchAssociateResource": {"name": "BatchAssociateResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "BatchAssociateResourceRequest"}, "output": {"shape": "BatchAssociateResourceResponse"}, "errors": [{"shape": "InvalidOperationException"}, {"shape": "InternalErrorException"}, {"shape": "InvalidInputException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Associate resources to a Firewall Manager resource set.</p>"}, "BatchDisassociateResource": {"name": "BatchDisassociateResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "BatchDisassociateResourceRequest"}, "output": {"shape": "BatchDisassociateResourceResponse"}, "errors": [{"shape": "InvalidOperationException"}, {"shape": "InternalErrorException"}, {"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Disassociates resources from a Firewall Manager resource set.</p>"}, "DeleteAppsList": {"name": "DeleteAppsList", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteAppsListRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidOperationException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Permanently deletes an Firewall Manager applications list.</p>"}, "DeleteNotificationChannel": {"name": "DeleteNotificationChannel", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteNotificationChannelRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidOperationException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Deletes an Firewall Manager association with the IAM role and the Amazon Simple Notification Service (SNS) topic that is used to record Firewall Manager SNS logs.</p>"}, "DeletePolicy": {"name": "DeletePolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeletePolicyRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidOperationException"}, {"shape": "InternalErrorException"}, {"shape": "InvalidInputException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Permanently deletes an Firewall Manager policy. </p>"}, "DeleteProtocolsList": {"name": "DeleteProtocolsList", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteProtocolsListRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidOperationException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Permanently deletes an Firewall Manager protocols list.</p>"}, "DeleteResourceSet": {"name": "DeleteResourceSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteResourceSetRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidOperationException"}, {"shape": "InternalErrorException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Deletes the specified <a>ResourceSet</a>.</p>"}, "DisassociateAdminAccount": {"name": "DisassociateAdminAccount", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DisassociateAdminAccountRequest"}, "errors": [{"shape": "InvalidOperationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Disassociates an Firewall Manager administrator account. To set a different account as an Firewall Manager administrator, submit a <a>PutAdminAccount</a> request. To set an account as a default administrator account, you must submit an <a>AssociateAdminAccount</a> request.</p> <p>Disassociation of the default administrator account follows the first in, last out principle. If you are the default administrator, all Firewall Manager administrators within the organization must first disassociate their accounts before you can disassociate your account.</p>"}, "DisassociateThirdPartyFirewall": {"name": "DisassociateThirdPartyFirewall", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DisassociateThirdPartyFirewallRequest"}, "output": {"shape": "DisassociateThirdPartyFirewallResponse"}, "errors": [{"shape": "InvalidOperationException"}, {"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Disassociates a Firewall Manager policy administrator from a third-party firewall tenant. When you call <code>DisassociateThirdPartyFirewall</code>, the third-party firewall vendor deletes all of the firewalls that are associated with the account.</p>"}, "GetAdminAccount": {"name": "GetAdminAccount", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetAdminAccountRequest"}, "output": {"shape": "GetAdminAccountResponse"}, "errors": [{"shape": "InvalidOperationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Returns the Organizations account that is associated with Firewall Manager as the Firewall Manager default administrator.</p>"}, "GetAdminScope": {"name": "GetAdminScope", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetAdminScopeRequest"}, "output": {"shape": "GetAdminScopeResponse"}, "errors": [{"shape": "InvalidOperationException"}, {"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalErrorException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Returns information about the specified account's administrative scope. The admistrative scope defines the resources that an Firewall Manager administrator can manage.</p>"}, "GetAppsList": {"name": "GetAppsList", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetAppsListRequest"}, "output": {"shape": "GetAppsListResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidOperationException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Returns information about the specified Firewall Manager applications list.</p>"}, "GetComplianceDetail": {"name": "GetComplianceDetail", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetComplianceDetailRequest"}, "output": {"shape": "GetComplianceDetailResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalErrorException"}, {"shape": "InvalidInputException"}, {"shape": "InvalidOperationException"}], "documentation": "<p>Returns detailed compliance information about the specified member account. Details include resources that are in and out of compliance with the specified policy. </p> <ul> <li> <p>Resources are considered noncompliant for WAF and Shield Advanced policies if the specified policy has not been applied to them.</p> </li> <li> <p>Resources are considered noncompliant for security group policies if they are in scope of the policy, they violate one or more of the policy rules, and remediation is disabled or not possible.</p> </li> <li> <p>Resources are considered noncompliant for Network Firewall policies if a firewall is missing in the VPC, if the firewall endpoint isn't set up in an expected Availability Zone and subnet, if a subnet created by the Firewall Manager doesn't have the expected route table, and for modifications to a firewall policy that violate the Firewall Manager policy's rules.</p> </li> <li> <p>Resources are considered noncompliant for DNS Firewall policies if a DNS Firewall rule group is missing from the rule group associations for the VPC. </p> </li> </ul>"}, "GetNotificationChannel": {"name": "GetNotificationChannel", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetNotificationChannelRequest"}, "output": {"shape": "GetNotificationChannelResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidOperationException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Information about the Amazon Simple Notification Service (SNS) topic that is used to record Firewall Manager SNS logs.</p>"}, "GetPolicy": {"name": "GetPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetPolicyRequest"}, "output": {"shape": "GetPolicyResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidOperationException"}, {"shape": "InternalErrorException"}, {"shape": "InvalidTypeException"}], "documentation": "<p>Returns information about the specified Firewall Manager policy.</p>"}, "GetProtectionStatus": {"name": "GetProtectionStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetProtectionStatusRequest"}, "output": {"shape": "GetProtectionStatusResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalErrorException"}], "documentation": "<p>If you created a Shield Advanced policy, returns policy-level attack summary information in the event of a potential DDoS attack. Other policy types are currently unsupported.</p>"}, "GetProtocolsList": {"name": "GetProtocolsList", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetProtocolsListRequest"}, "output": {"shape": "GetProtocolsListResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidOperationException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Returns information about the specified Firewall Manager protocols list.</p>"}, "GetResourceSet": {"name": "GetResourceSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetResourceSetRequest"}, "output": {"shape": "GetResourceSetResponse"}, "errors": [{"shape": "InvalidOperationException"}, {"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Gets information about a specific resource set.</p>"}, "GetThirdPartyFirewallAssociationStatus": {"name": "GetThirdPartyFirewallAssociationStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetThirdPartyFirewallAssociationStatusRequest"}, "output": {"shape": "GetThirdPartyFirewallAssociationStatusResponse"}, "errors": [{"shape": "InvalidOperationException"}, {"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalErrorException"}], "documentation": "<p>The onboarding status of a Firewall Manager admin account to third-party firewall vendor tenant.</p>"}, "GetViolationDetails": {"name": "GetViolationDetails", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetViolationDetailsRequest"}, "output": {"shape": "GetViolationDetailsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidInputException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Retrieves violations for a resource based on the specified Firewall Manager policy and Amazon Web Services account.</p>"}, "ListAdminAccountsForOrganization": {"name": "ListAdminAccountsForOrganization", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAdminAccountsForOrganizationRequest"}, "output": {"shape": "ListAdminAccountsForOrganizationResponse"}, "errors": [{"shape": "InvalidOperationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalErrorException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Returns a <code>AdminAccounts</code> object that lists the Firewall Manager administrators within the organization that are onboarded to Firewall Manager by <a>AssociateAdminAccount</a>.</p> <p>This operation can be called only from the organization's management account.</p>"}, "ListAdminsManagingAccount": {"name": "ListAdminsManagingAccount", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAdminsManagingAccountRequest"}, "output": {"shape": "ListAdminsManagingAccountResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidInputException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Lists the accounts that are managing the specified Organizations member account. This is useful for any member account so that they can view the accounts who are managing their account. This operation only returns the managing administrators that have the requested account within their <a>AdminScope</a>.</p>"}, "ListAppsLists": {"name": "ListAppsLists", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAppsListsRequest"}, "output": {"shape": "ListAppsListsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidOperationException"}, {"shape": "LimitExceededException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Returns an array of <code>AppsListDataSummary</code> objects.</p>"}, "ListComplianceStatus": {"name": "ListComplianceStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListComplianceStatusRequest"}, "output": {"shape": "ListComplianceStatusResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Returns an array of <code>PolicyComplianceStatus</code> objects. Use <code>PolicyComplianceStatus</code> to get a summary of which member accounts are protected by the specified policy. </p>"}, "ListDiscoveredResources": {"name": "ListDiscoveredResources", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListDiscoveredResourcesRequest"}, "output": {"shape": "ListDiscoveredResourcesResponse"}, "errors": [{"shape": "InvalidOperationException"}, {"shape": "InvalidInputException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Returns an array of resources in the organization's accounts that are available to be associated with a resource set.</p>"}, "ListMemberAccounts": {"name": "ListMemberAccounts", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListMemberAccountsRequest"}, "output": {"shape": "ListMemberAccountsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Returns a <code>MemberAccounts</code> object that lists the member accounts in the administrator's Amazon Web Services organization.</p> <p>Either an Firewall Manager administrator or the organization's management account can make this request.</p>"}, "ListPolicies": {"name": "ListPolicies", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListPoliciesRequest"}, "output": {"shape": "ListPoliciesResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidOperationException"}, {"shape": "LimitExceededException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Returns an array of <code>PolicySummary</code> objects.</p>"}, "ListProtocolsLists": {"name": "ListProtocolsLists", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListProtocolsListsRequest"}, "output": {"shape": "ListProtocolsListsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidOperationException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Returns an array of <code>ProtocolsListDataSummary</code> objects.</p>"}, "ListResourceSetResources": {"name": "ListResourceSetResources", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListResourceSetResourcesRequest"}, "output": {"shape": "ListResourceSetResourcesResponse"}, "errors": [{"shape": "InvalidOperationException"}, {"shape": "InternalErrorException"}, {"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns an array of resources that are currently associated to a resource set.</p>"}, "ListResourceSets": {"name": "ListResourceSets", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListResourceSetsRequest"}, "output": {"shape": "ListResourceSetsResponse"}, "errors": [{"shape": "InvalidOperationException"}, {"shape": "InvalidInputException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Returns an array of <code>ResourceSetSummary</code> objects.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidOperationException"}, {"shape": "InternalErrorException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Retrieves the list of tags for the specified Amazon Web Services resource. </p>"}, "ListThirdPartyFirewallFirewallPolicies": {"name": "ListThirdPartyFirewallFirewallPolicies", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListThirdPartyFirewallFirewallPoliciesRequest"}, "output": {"shape": "ListThirdPartyFirewallFirewallPoliciesResponse"}, "errors": [{"shape": "InvalidOperationException"}, {"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Retrieves a list of all of the third-party firewall policies that are associated with the third-party firewall administrator's account.</p>"}, "PutAdminAccount": {"name": "PutAdminAccount", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutAdminAccountRequest"}, "errors": [{"shape": "InvalidOperationException"}, {"shape": "InvalidInputException"}, {"shape": "InternalErrorException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates or updates an Firewall Manager administrator account. The account must be a member of the organization that was onboarded to Firewall Manager by <a>AssociateAdminAccount</a>. Only the organization's management account can create an Firewall Manager administrator account. When you create an Firewall Manager administrator account, the service checks to see if the account is already a delegated administrator within Organizations. If the account isn't a delegated administrator, Firewall Manager calls Organizations to delegate the account within Organizations. For more information about administrator accounts within Organizations, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_accounts.html\">Managing the Amazon Web Services Accounts in Your Organization</a>.</p>"}, "PutAppsList": {"name": "PutAppsList", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutAppsListRequest"}, "output": {"shape": "PutAppsListResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidOperationException"}, {"shape": "InvalidInputException"}, {"shape": "LimitExceededException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Creates an Firewall Manager applications list.</p>"}, "PutNotificationChannel": {"name": "PutNotificationChannel", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutNotificationChannelRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidOperationException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Designates the IAM role and Amazon Simple Notification Service (SNS) topic that Firewall Manager uses to record SNS logs.</p> <p>To perform this action outside of the console, you must first configure the SNS topic's access policy to allow the <code>SnsRoleName</code> to publish SNS logs. If the <code>SnsRoleName</code> provided is a role other than the <code>AWSServiceRoleForFMS</code> service-linked role, this role must have a trust relationship configured to allow the Firewall Manager service principal <code>fms.amazonaws.com</code> to assume this role. For information about configuring an SNS access policy, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/fms-security_iam_service-with-iam.html#fms-security_iam_service-with-iam-roles-service\">Service roles for Firewall Manager</a> in the <i>Firewall Manager Developer Guide</i>.</p>"}, "PutPolicy": {"name": "PutPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutPolicyRequest"}, "output": {"shape": "PutPolicyResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidOperationException"}, {"shape": "InvalidInputException"}, {"shape": "LimitExceededException"}, {"shape": "InternalErrorException"}, {"shape": "InvalidTypeException"}], "documentation": "<p>Creates an Firewall Manager policy.</p> <p>A Firewall Manager policy is specific to the individual policy type. If you want to enforce multiple policy types across accounts, you can create multiple policies. You can create more than one policy for each type. </p> <p>If you add a new account to an organization that you created with Organizations, Firewall Manager automatically applies the policy to the resources in that account that are within scope of the policy. </p> <p>Firewall Manager provides the following types of policies: </p> <ul> <li> <p> <b>Shield Advanced policy</b> - This policy applies Shield Advanced protection to specified accounts and resources. </p> </li> <li> <p> <b>Security Groups policy</b> - This type of policy gives you control over security groups that are in use throughout your organization in Organizations and lets you enforce a baseline set of rules across your organization. </p> </li> <li> <p> <b>Network Firewall policy</b> - This policy applies Network Firewall protection to your organization's VPCs. </p> </li> <li> <p> <b>DNS Firewall policy</b> - This policy applies Amazon Route 53 Resolver DNS Firewall protections to your organization's VPCs. </p> </li> <li> <p> <b>Third-party firewall policy</b> - This policy applies third-party firewall protections. Third-party firewalls are available by subscription through the Amazon Web Services Marketplace console at <a href=\"https://aws.amazon.com/marketplace\">Amazon Web Services Marketplace</a>.</p> <ul> <li> <p> <b>Palo Alto Networks Cloud NGFW policy</b> - This policy applies Palo Alto Networks Cloud Next Generation Firewall (NGFW) protections and Palo Alto Networks Cloud NGFW rulestacks to your organization's VPCs.</p> </li> <li> <p> <b>Fortigate CNF policy</b> - This policy applies Fortigate Cloud Native Firewall (CNF) protections. Fortigate CNF is a cloud-centered solution that blocks Zero-Day threats and secures cloud infrastructures with industry-leading advanced threat prevention, smart web application firewalls (WAF), and API protection.</p> </li> </ul> </li> </ul>"}, "PutProtocolsList": {"name": "PutProtocolsList", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutProtocolsListRequest"}, "output": {"shape": "PutProtocolsListResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidOperationException"}, {"shape": "InvalidInputException"}, {"shape": "LimitExceededException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Creates an Firewall Manager protocols list.</p>"}, "PutResourceSet": {"name": "PutResourceSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutResourceSetRequest"}, "output": {"shape": "PutResourceSetResponse"}, "errors": [{"shape": "InvalidOperationException"}, {"shape": "InvalidInputException"}, {"shape": "LimitExceededException"}, {"shape": "InternalErrorException"}], "documentation": "<p>Creates the resource set.</p> <p>An Firewall Manager resource set defines the resources to import into an Firewall Manager policy from another Amazon Web Services service.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidOperationException"}, {"shape": "InternalErrorException"}, {"shape": "InvalidInputException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Adds one or more tags to an Amazon Web Services resource.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidOperationException"}, {"shape": "InternalErrorException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Removes one or more tags from an Amazon Web Services resource.</p>"}}, "shapes": {"AWSAccountId": {"type": "string", "max": 1024, "min": 1, "pattern": "^[0-9]+$"}, "AWSAccountIdList": {"type": "list", "member": {"shape": "AWSAccountId"}}, "AWSRegion": {"type": "string", "max": 32, "min": 6, "pattern": "^(af|ap|ca|eu|il|me|mx|sa|us|cn|us-gov)-\\w+-\\d+$"}, "AWSRegionList": {"type": "list", "member": {"shape": "AWSRegion"}, "max": 64, "min": 0}, "AccountIdList": {"type": "list", "member": {"shape": "AWSAccountId"}}, "AccountRoleStatus": {"type": "string", "enum": ["READY", "CREATING", "PENDING_DELETION", "DELETING", "DELETED"]}, "AccountScope": {"type": "structure", "members": {"Accounts": {"shape": "AccountIdList", "documentation": "<p>The list of accounts within the organization that the specified Firewall Manager administrator either can or cannot apply policies to, based on the value of <code>ExcludeSpecifiedAccounts</code>. If <code>ExcludeSpecifiedAccounts</code> is set to <code>true</code>, then the Firewall Manager administrator can apply policies to all members of the organization except for the accounts in this list. If <code>ExcludeSpecifiedAccounts</code> is set to <code>false</code>, then the Firewall Manager administrator can only apply policies to the accounts in this list.</p>"}, "AllAccountsEnabled": {"shape": "Boolean", "documentation": "<p>A boolean value that indicates if the administrator can apply policies to all accounts within an organization. If true, the administrator can apply policies to all accounts within the organization. You can either enable management of all accounts through this operation, or you can specify a list of accounts to manage in <code>AccountScope$Accounts</code>. You cannot specify both.</p>"}, "ExcludeSpecifiedAccounts": {"shape": "Boolean", "documentation": "<p>A boolean value that excludes the accounts in <code>AccountScope$Accounts</code> from the administrator's scope. If true, the Firewall Manager administrator can apply policies to all members of the organization except for the accounts listed in <code>AccountScope$Accounts</code>. You can either specify a list of accounts to exclude by <code>AccountScope$Accounts</code>, or you can enable management of all accounts by <code>AccountScope$AllAccountsEnabled</code>. You cannot specify both.</p>"}}, "documentation": "<p>Configures the accounts within the administrator's Organizations organization that the specified Firewall Manager administrator can apply policies to.</p>"}, "ActionTarget": {"type": "structure", "members": {"ResourceId": {"shape": "ResourceId", "documentation": "<p>The ID of the remediation target.</p>"}, "Description": {"shape": "LengthBoundedString", "documentation": "<p>A description of the remediation action target.</p>"}}, "documentation": "<p>Describes a remediation action target.</p>"}, "AdminAccountSummary": {"type": "structure", "members": {"AdminAccount": {"shape": "AWSAccountId", "documentation": "<p>The Amazon Web Services account ID of the Firewall Manager administrator's account.</p>"}, "DefaultAdmin": {"shape": "Boolean", "documentation": "<p>A boolean value that indicates if the administrator is the default administrator. If true, then this is the default administrator account. The default administrator can manage third-party firewalls and has full administrative scope. There is only one default administrator account per organization. For information about Firewall Manager default administrator accounts, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/fms-administrators.html\">Managing Firewall Manager administrators</a> in the <i>Firewall Manager Developer Guide</i>.</p>"}, "Status": {"shape": "OrganizationStatus", "documentation": "<p>The current status of the request to onboard a member account as an Firewall Manager administator.</p> <ul> <li> <p> <code>ONBOARDING</code> - The account is onboarding to Firewall Manager as an administrator.</p> </li> <li> <p> <code>ONBOARDING_COMPLETE</code> - Firewall Manager The account is onboarded to Firewall Manager as an administrator, and can perform actions on the resources defined in their <a>AdminScope</a>.</p> </li> <li> <p> <code>OFFBOARDING</code> - The account is being removed as an Firewall Manager administrator.</p> </li> <li> <p> <code>OFFBOARDING_COMPLETE</code> - The account has been removed as an Firewall Manager administrator.</p> </li> </ul>"}}, "documentation": "<p>Contains high level information about the Firewall Manager administrator account.</p>"}, "AdminAccountSummaryList": {"type": "list", "member": {"shape": "AdminAccountSummary"}}, "AdminScope": {"type": "structure", "members": {"AccountScope": {"shape": "AccountScope", "documentation": "<p>Defines the accounts that the specified Firewall Manager administrator can apply policies to.</p>"}, "OrganizationalUnitScope": {"shape": "OrganizationalUnitScope", "documentation": "<p>Defines the Organizations organizational units that the specified Firewall Manager administrator can apply policies to. For more information about OUs in Organizations, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_ous.html\">Managing organizational units (OUs) </a> in the <i>Organizations User Guide</i>.</p>"}, "RegionScope": {"shape": "RegionScope", "documentation": "<p>Defines the Amazon Web Services Regions that the specified Firewall Manager administrator can perform actions in.</p>"}, "PolicyTypeScope": {"shape": "PolicyTypeScope", "documentation": "<p>Defines the Firewall Manager policy types that the specified Firewall Manager administrator can create and manage.</p>"}}, "documentation": "<p>Defines the resources that the Firewall Manager administrator can manage. For more information about administrative scope, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/fms-administrators.html\">Managing Firewall Manager administrators</a> in the <i>Firewall Manager Developer Guide</i>.</p>"}, "App": {"type": "structure", "required": ["AppName", "Protocol", "Port"], "members": {"AppName": {"shape": "ResourceName", "documentation": "<p>The application's name.</p>"}, "Protocol": {"shape": "Protocol", "documentation": "<p>The IP protocol name or number. The name can be one of <code>tcp</code>, <code>udp</code>, or <code>icmp</code>. For information on possible numbers, see <a href=\"https://www.iana.org/assignments/protocol-numbers/protocol-numbers.xhtml\">Protocol Numbers</a>.</p>"}, "Port": {"shape": "IPPortNumber", "documentation": "<p>The application's port number, for example <code>80</code>.</p>"}}, "documentation": "<p>An individual Firewall Manager application.</p>"}, "AppsList": {"type": "list", "member": {"shape": "App"}}, "AppsListData": {"type": "structure", "required": ["ListName", "AppsList"], "members": {"ListId": {"shape": "ListId", "documentation": "<p>The ID of the Firewall Manager applications list.</p>"}, "ListName": {"shape": "ResourceName", "documentation": "<p>The name of the Firewall Manager applications list.</p>"}, "ListUpdateToken": {"shape": "UpdateToken", "documentation": "<p>A unique identifier for each update to the list. When you update the list, the update token must match the token of the current version of the application list. You can retrieve the update token by getting the list. </p>"}, "CreateTime": {"shape": "TimeStamp", "documentation": "<p>The time that the Firewall Manager applications list was created.</p>"}, "LastUpdateTime": {"shape": "TimeStamp", "documentation": "<p>The time that the Firewall Manager applications list was last updated.</p>"}, "AppsList": {"shape": "AppsList", "documentation": "<p>An array of applications in the Firewall Manager applications list.</p>"}, "PreviousAppsList": {"shape": "PreviousAppsList", "documentation": "<p>A map of previous version numbers to their corresponding <code>App</code> object arrays.</p>"}}, "documentation": "<p>An Firewall Manager applications list.</p>"}, "AppsListDataSummary": {"type": "structure", "members": {"ListArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the applications list.</p>"}, "ListId": {"shape": "ListId", "documentation": "<p>The ID of the applications list.</p>"}, "ListName": {"shape": "ResourceName", "documentation": "<p>The name of the applications list.</p>"}, "AppsList": {"shape": "AppsList", "documentation": "<p>An array of <code>App</code> objects in the Firewall Manager applications list.</p>"}}, "documentation": "<p>Details of the Firewall Manager applications list.</p>"}, "AppsListsData": {"type": "list", "member": {"shape": "AppsListDataSummary"}}, "AssociateAdminAccountRequest": {"type": "structure", "required": ["AdminAccount"], "members": {"AdminAccount": {"shape": "AWSAccountId", "documentation": "<p>The Amazon Web Services account ID to associate with Firewall Manager as the Firewall Manager default administrator account. This account must be a member account of the organization in Organizations whose resources you want to protect. For more information about Organizations, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_accounts.html\">Managing the Amazon Web Services Accounts in Your Organization</a>. </p>"}}}, "AssociateThirdPartyFirewallRequest": {"type": "structure", "required": ["ThirdPartyFirewall"], "members": {"ThirdPartyFirewall": {"shape": "ThirdPartyFirewall", "documentation": "<p>The name of the third-party firewall vendor.</p>"}}}, "AssociateThirdPartyFirewallResponse": {"type": "structure", "members": {"ThirdPartyFirewallStatus": {"shape": "ThirdPartyFirewallAssociationStatus", "documentation": "<p>The current status for setting a Firewall Manager policy administrator's account as an administrator of the third-party firewall tenant.</p> <ul> <li> <p> <code>ONBOARDING</code> - The Firewall Manager policy administrator is being designated as a tenant administrator.</p> </li> <li> <p> <code>ONBOARD_COMPLETE</code> - The Firewall Manager policy administrator is designated as a tenant administrator.</p> </li> <li> <p> <code>OFFBOARDING</code> - The Firewall Manager policy administrator is being removed as a tenant administrator.</p> </li> <li> <p> <code>OFFBOARD_COMPLETE</code> - The Firewall Manager policy administrator has been removed as a tenant administrator.</p> </li> <li> <p> <code>NOT_EXIST</code> - The Firewall Manager policy administrator doesn't exist as a tenant administrator.</p> </li> </ul>"}}}, "AwsEc2InstanceViolation": {"type": "structure", "members": {"ViolationTarget": {"shape": "Violation<PERSON>arget", "documentation": "<p>The resource ID of the EC2 instance.</p>"}, "AwsEc2NetworkInterfaceViolations": {"shape": "AwsEc2NetworkInterfaceViolations", "documentation": "<p>Violation detail for network interfaces associated with the EC2 instance.</p>"}}, "documentation": "<p>Violation detail for an EC2 instance resource.</p>"}, "AwsEc2NetworkInterfaceViolation": {"type": "structure", "members": {"ViolationTarget": {"shape": "Violation<PERSON>arget", "documentation": "<p>The resource ID of the network interface.</p>"}, "ViolatingSecurityGroups": {"shape": "ResourceIdList", "documentation": "<p>List of security groups that violate the rules specified in the primary security group of the Firewall Manager policy.</p>"}}, "documentation": "<p>Violation detail for network interfaces associated with an EC2 instance.</p>"}, "AwsEc2NetworkInterfaceViolations": {"type": "list", "member": {"shape": "AwsEc2NetworkInterfaceViolation"}}, "AwsVPCSecurityGroupViolation": {"type": "structure", "members": {"ViolationTarget": {"shape": "Violation<PERSON>arget", "documentation": "<p>The security group rule that is being evaluated.</p>"}, "ViolationTargetDescription": {"shape": "LengthBoundedString", "documentation": "<p>A description of the security group that violates the policy.</p>"}, "PartialMatches": {"shape": "PartialMatches", "documentation": "<p>List of rules specified in the security group of the Firewall Manager policy that partially match the <code>ViolationTarget</code> rule.</p>"}, "PossibleSecurityGroupRemediationActions": {"shape": "SecurityGroupRemediationActions", "documentation": "<p>Remediation options for the rule specified in the <code>ViolationTarget</code>.</p>"}}, "documentation": "<p>Violation detail for the rule violation in a security group when compared to the primary security group of the Firewall Manager policy.</p>"}, "Base62Id": {"type": "string", "max": 22, "min": 22, "pattern": "^[a-z0-9A-Z]{22}$"}, "BasicInteger": {"type": "integer", "max": 2147483647, "min": -2147483648}, "BatchAssociateResourceRequest": {"type": "structure", "required": ["ResourceSetIdentifier", "Items"], "members": {"ResourceSetIdentifier": {"shape": "Identifier", "documentation": "<p>A unique identifier for the resource set, used in a request to refer to the resource set.</p>"}, "Items": {"shape": "IdentifierList", "documentation": "<p>The uniform resource identifiers (URIs) of resources that should be associated to the resource set. The URIs must be Amazon Resource Names (ARNs).</p>"}}}, "BatchAssociateResourceResponse": {"type": "structure", "required": ["ResourceSetIdentifier", "FailedItems"], "members": {"ResourceSetIdentifier": {"shape": "Identifier", "documentation": "<p>A unique identifier for the resource set, used in a request to refer to the resource set.</p>"}, "FailedItems": {"shape": "FailedItemList", "documentation": "<p>The resources that failed to associate to the resource set.</p>"}}}, "BatchDisassociateResourceRequest": {"type": "structure", "required": ["ResourceSetIdentifier", "Items"], "members": {"ResourceSetIdentifier": {"shape": "Identifier", "documentation": "<p>A unique identifier for the resource set, used in a request to refer to the resource set.</p>"}, "Items": {"shape": "IdentifierList", "documentation": "<p>The uniform resource identifiers (URI) of resources that should be disassociated from the resource set. The URIs must be Amazon Resource Names (ARNs).</p>"}}}, "BatchDisassociateResourceResponse": {"type": "structure", "required": ["ResourceSetIdentifier", "FailedItems"], "members": {"ResourceSetIdentifier": {"shape": "Identifier", "documentation": "<p>A unique identifier for the resource set, used in a request to refer to the resource set.</p>"}, "FailedItems": {"shape": "FailedItemList", "documentation": "<p>The resources that failed to disassociate from the resource set.</p>"}}}, "Boolean": {"type": "boolean"}, "CIDR": {"type": "string", "max": 256, "min": 0, "pattern": "[a-f0-9:./]+"}, "ComplianceViolator": {"type": "structure", "members": {"ResourceId": {"shape": "ResourceId", "documentation": "<p>The resource ID.</p>"}, "ViolationReason": {"shape": "ViolationReason", "documentation": "<p>The reason that the resource is not protected by the policy.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The resource type. This is in the format shown in the <a href=\"https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-template-resource-type-ref.html\">Amazon Web Services Resource Types Reference</a>. For example: <code>AWS::ElasticLoadBalancingV2::LoadBalancer</code>, <code>AWS::CloudFront::Distribution</code>, or <code>AWS::NetworkFirewall::FirewallPolicy</code>.</p>"}, "Metadata": {"shape": "ComplianceViolatorMetadata", "documentation": "<p><PERSON><PERSON><PERSON> about the resource that doesn't comply with the policy scope.</p>"}}, "documentation": "<p>Details of the resource that is not protected by the policy.</p>"}, "ComplianceViolatorMetadata": {"type": "map", "key": {"shape": "LengthBoundedString"}, "value": {"shape": "LengthBoundedString"}}, "ComplianceViolators": {"type": "list", "member": {"shape": "ComplianceViolator"}}, "CustomerPolicyScopeId": {"type": "string", "max": 1024, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "CustomerPolicyScopeIdList": {"type": "list", "member": {"shape": "CustomerPolicyScopeId"}}, "CustomerPolicyScopeIdType": {"type": "string", "enum": ["ACCOUNT", "ORG_UNIT"]}, "CustomerPolicyScopeMap": {"type": "map", "key": {"shape": "CustomerPolicyScopeIdType"}, "value": {"shape": "CustomerPolicyScopeIdList"}}, "CustomerPolicyStatus": {"type": "string", "enum": ["ACTIVE", "OUT_OF_ADMIN_SCOPE"]}, "DeleteAppsListRequest": {"type": "structure", "required": ["ListId"], "members": {"ListId": {"shape": "ListId", "documentation": "<p>The ID of the applications list that you want to delete. You can retrieve this ID from <code>PutAppsList</code>, <code>ListAppsLists</code>, and <code>GetAppsList</code>.</p>"}}}, "DeleteNotificationChannelRequest": {"type": "structure", "members": {}}, "DeletePolicyRequest": {"type": "structure", "required": ["PolicyId"], "members": {"PolicyId": {"shape": "PolicyId", "documentation": "<p>The ID of the policy that you want to delete. You can retrieve this ID from <code>PutPolicy</code> and <code>ListPolicies</code>.</p>"}, "DeleteAllPolicyResources": {"shape": "Boolean", "documentation": "<p>If <code>True</code>, the request performs cleanup according to the policy type. </p> <p>For WAF and Shield Advanced policies, the cleanup does the following:</p> <ul> <li> <p>Deletes rule groups created by Firewall Manager</p> </li> <li> <p>Removes web ACLs from in-scope resources</p> </li> <li> <p>Deletes web ACLs that contain no rules or rule groups</p> </li> </ul> <p>For security group policies, the cleanup does the following for each security group in the policy:</p> <ul> <li> <p>Disassociates the security group from in-scope resources </p> </li> <li> <p>Deletes the security group if it was created through Firewall Manager and if it's no longer associated with any resources through another policy</p> </li> </ul> <note> <p>For security group common policies, even if set to <code>False</code>, Firewall Manager deletes all security groups created by Firewall Manager that aren't associated with any other resources through another policy.</p> </note> <p>After the cleanup, in-scope resources are no longer protected by web ACLs in this policy. Protection of out-of-scope resources remains unchanged. Scope is determined by tags that you create and accounts that you associate with the policy. When creating the policy, if you specify that only resources in specific accounts or with specific tags are in scope of the policy, those accounts and resources are handled by the policy. All others are out of scope. If you don't specify tags or accounts, all resources are in scope. </p>"}}}, "DeleteProtocolsListRequest": {"type": "structure", "required": ["ListId"], "members": {"ListId": {"shape": "ListId", "documentation": "<p>The ID of the protocols list that you want to delete. You can retrieve this ID from <code>PutProtocolsList</code>, <code>ListProtocolsLists</code>, and <code>GetProtocolsLost</code>.</p>"}}}, "DeleteResourceSetRequest": {"type": "structure", "required": ["Identifier"], "members": {"Identifier": {"shape": "Base62Id", "documentation": "<p>A unique identifier for the resource set, used in a request to refer to the resource set.</p>"}}}, "DependentServiceName": {"type": "string", "enum": ["AWSCONFIG", "AWSWAF", "AWSSHIELD_ADVANCED", "AWSVPC"]}, "Description": {"type": "string", "max": 256, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "DestinationType": {"type": "string", "enum": ["IPV4", "IPV6", "PREFIX_LIST"]}, "DetailedInfo": {"type": "string", "max": 4096, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=,+\\-@]*)$"}, "DisassociateAdminAccountRequest": {"type": "structure", "members": {}}, "DisassociateThirdPartyFirewallRequest": {"type": "structure", "required": ["ThirdPartyFirewall"], "members": {"ThirdPartyFirewall": {"shape": "ThirdPartyFirewall", "documentation": "<p>The name of the third-party firewall vendor.</p>"}}}, "DisassociateThirdPartyFirewallResponse": {"type": "structure", "members": {"ThirdPartyFirewallStatus": {"shape": "ThirdPartyFirewallAssociationStatus", "documentation": "<p>The current status for the disassociation of a Firewall Manager administrators account with a third-party firewall.</p>"}}}, "DiscoveredResource": {"type": "structure", "members": {"URI": {"shape": "Identifier", "documentation": "<p>The universal resource identifier (URI) of the discovered resource.</p>"}, "AccountId": {"shape": "AWSAccountId", "documentation": "<p>The Amazon Web Services account ID associated with the discovered resource.</p>"}, "Type": {"shape": "ResourceType", "documentation": "<p>The type of the discovered resource.</p>"}, "Name": {"shape": "ResourceName", "documentation": "<p>The name of the discovered resource.</p>"}}, "documentation": "<p>A resource in the organization that's available to be associated with a Firewall Manager resource set.</p>"}, "DiscoveredResourceList": {"type": "list", "member": {"shape": "DiscoveredResource"}}, "DnsDuplicateRuleGroupViolation": {"type": "structure", "members": {"ViolationTarget": {"shape": "Violation<PERSON>arget", "documentation": "<p>Information about the VPC ID. </p>"}, "ViolationTargetDescription": {"shape": "LengthBoundedString", "documentation": "<p>A description of the violation that specifies the rule group and VPC.</p>"}}, "documentation": "<p>A DNS Firewall rule group that Firewall Manager tried to associate with a VPC is already associated with the VPC and can't be associated again. </p>"}, "DnsRuleGroupLimitExceededViolation": {"type": "structure", "members": {"ViolationTarget": {"shape": "Violation<PERSON>arget", "documentation": "<p>Information about the VPC ID. </p>"}, "ViolationTargetDescription": {"shape": "LengthBoundedString", "documentation": "<p>A description of the violation that specifies the rule group and VPC.</p>"}, "NumberOfRuleGroupsAlreadyAssociated": {"shape": "BasicInteger", "documentation": "<p>The number of rule groups currently associated with the VPC. </p>"}}, "documentation": "<p>The VPC that Firewall Manager was applying a DNS Fireall policy to reached the limit for associated DNS Firewall rule groups. Firewall Manager tried to associate another rule group with the VPC and failed due to the limit. </p>"}, "DnsRuleGroupPriorities": {"type": "list", "member": {"shape": "DnsRuleGroupPriority"}}, "DnsRuleGroupPriority": {"type": "integer", "max": 10000, "min": 0}, "DnsRuleGroupPriorityConflictViolation": {"type": "structure", "members": {"ViolationTarget": {"shape": "Violation<PERSON>arget", "documentation": "<p>Information about the VPC ID. </p>"}, "ViolationTargetDescription": {"shape": "LengthBoundedString", "documentation": "<p>A description of the violation that specifies the VPC and the rule group that's already associated with it.</p>"}, "ConflictingPriority": {"shape": "DnsRuleGroupPriority", "documentation": "<p>The priority setting of the two conflicting rule groups.</p>"}, "ConflictingPolicyId": {"shape": "PolicyId", "documentation": "<p>The ID of the Firewall Manager DNS Firewall policy that was already applied to the VPC. This policy contains the rule group that's already associated with the VPC. </p>"}, "UnavailablePriorities": {"shape": "DnsRuleGroupPriorities", "documentation": "<p>The priorities of rule groups that are already associated with the VPC. To retry your operation, choose priority settings that aren't in this list for the rule groups in your new DNS Firewall policy. </p>"}}, "documentation": "<p>A rule group that Firewall Manager tried to associate with a VPC has the same priority as a rule group that's already associated. </p>"}, "EC2AssociateRouteTableAction": {"type": "structure", "required": ["RouteTableId"], "members": {"Description": {"shape": "LengthBoundedString", "documentation": "<p>A description of the EC2 route table that is associated with the remediation action.</p>"}, "RouteTableId": {"shape": "ActionTarget", "documentation": "<p>The ID of the EC2 route table that is associated with the remediation action.</p>"}, "SubnetId": {"shape": "ActionTarget", "documentation": "<p>The ID of the subnet for the EC2 route table that is associated with the remediation action.</p>"}, "GatewayId": {"shape": "ActionTarget", "documentation": "<p>The ID of the gateway to be used with the EC2 route table that is associated with the remediation action.</p>"}}, "documentation": "<p>The action of associating an EC2 resource, such as a subnet or internet gateway, with a route table.</p>"}, "EC2CopyRouteTableAction": {"type": "structure", "required": ["VpcId", "RouteTableId"], "members": {"Description": {"shape": "LengthBoundedString", "documentation": "<p>A description of the copied EC2 route table that is associated with the remediation action.</p>"}, "VpcId": {"shape": "ActionTarget", "documentation": "<p>The VPC ID of the copied EC2 route table that is associated with the remediation action.</p>"}, "RouteTableId": {"shape": "ActionTarget", "documentation": "<p>The ID of the copied EC2 route table that is associated with the remediation action.</p>"}}, "documentation": "<p>An action that copies the EC2 route table for use in remediation.</p>"}, "EC2CreateRouteAction": {"type": "structure", "required": ["RouteTableId"], "members": {"Description": {"shape": "LengthBoundedString", "documentation": "<p>A description of CreateRoute action in Amazon EC2.</p>"}, "DestinationCidrBlock": {"shape": "CIDR", "documentation": "<p>Information about the IPv4 CIDR address block used for the destination match.</p>"}, "DestinationPrefixListId": {"shape": "ResourceId", "documentation": "<p>Information about the ID of a prefix list used for the destination match.</p>"}, "DestinationIpv6CidrBlock": {"shape": "CIDR", "documentation": "<p>Information about the IPv6 CIDR block destination.</p>"}, "VpcEndpointId": {"shape": "ActionTarget", "documentation": "<p>Information about the ID of a VPC endpoint. Supported for Gateway Load Balancer endpoints only.</p>"}, "GatewayId": {"shape": "ActionTarget", "documentation": "<p>Information about the ID of an internet gateway or virtual private gateway attached to your VPC.</p>"}, "RouteTableId": {"shape": "ActionTarget", "documentation": "<p>Information about the ID of the route table for the route.</p>"}}, "documentation": "<p>Information about the CreateRoute action in Amazon EC2.</p>"}, "EC2CreateRouteTableAction": {"type": "structure", "required": ["VpcId"], "members": {"Description": {"shape": "LengthBoundedString", "documentation": "<p>A description of the CreateRouteTable action.</p>"}, "VpcId": {"shape": "ActionTarget", "documentation": "<p>Information about the ID of a VPC.</p>"}}, "documentation": "<p>Information about the CreateRouteTable action in Amazon EC2.</p>"}, "EC2DeleteRouteAction": {"type": "structure", "required": ["RouteTableId"], "members": {"Description": {"shape": "LengthBoundedString", "documentation": "<p>A description of the DeleteRoute action.</p>"}, "DestinationCidrBlock": {"shape": "CIDR", "documentation": "<p>Information about the IPv4 CIDR range for the route. The value you specify must match the CIDR for the route exactly.</p>"}, "DestinationPrefixListId": {"shape": "ResourceId", "documentation": "<p>Information about the ID of the prefix list for the route.</p>"}, "DestinationIpv6CidrBlock": {"shape": "CIDR", "documentation": "<p>Information about the IPv6 CIDR range for the route. The value you specify must match the CIDR for the route exactly.</p>"}, "RouteTableId": {"shape": "ActionTarget", "documentation": "<p>Information about the ID of the route table.</p>"}}, "documentation": "<p>Information about the DeleteRoute action in Amazon EC2.</p>"}, "EC2ReplaceRouteAction": {"type": "structure", "required": ["RouteTableId"], "members": {"Description": {"shape": "LengthBoundedString", "documentation": "<p>A description of the ReplaceRoute action in Amazon EC2.</p>"}, "DestinationCidrBlock": {"shape": "CIDR", "documentation": "<p>Information about the IPv4 CIDR address block used for the destination match. The value that you provide must match the CIDR of an existing route in the table.</p>"}, "DestinationPrefixListId": {"shape": "ResourceId", "documentation": "<p>Information about the ID of the prefix list for the route.</p>"}, "DestinationIpv6CidrBlock": {"shape": "CIDR", "documentation": "<p>Information about the IPv6 CIDR address block used for the destination match. The value that you provide must match the CIDR of an existing route in the table.</p>"}, "GatewayId": {"shape": "ActionTarget", "documentation": "<p>Information about the ID of an internet gateway or virtual private gateway.</p>"}, "RouteTableId": {"shape": "ActionTarget", "documentation": "<p>Information about the ID of the route table.</p>"}}, "documentation": "<p>Information about the ReplaceRoute action in Amazon EC2.</p>"}, "EC2ReplaceRouteTableAssociationAction": {"type": "structure", "required": ["AssociationId", "RouteTableId"], "members": {"Description": {"shape": "LengthBoundedString", "documentation": "<p>A description of the ReplaceRouteTableAssociation action in Amazon EC2.</p>"}, "AssociationId": {"shape": "ActionTarget", "documentation": "<p>Information about the association ID.</p>"}, "RouteTableId": {"shape": "ActionTarget", "documentation": "<p>Information about the ID of the new route table to associate with the subnet.</p>"}}, "documentation": "<p>Information about the ReplaceRouteTableAssociation action in Amazon EC2.</p>"}, "ErrorMessage": {"type": "string"}, "EvaluationResult": {"type": "structure", "members": {"ComplianceStatus": {"shape": "PolicyComplianceStatusType", "documentation": "<p>Describes an Amazon Web Services account's compliance with the Firewall Manager policy.</p>"}, "ViolatorCount": {"shape": "ResourceCount", "documentation": "<p>The number of resources that are noncompliant with the specified policy. For WAF and Shield Advanced policies, a resource is considered noncompliant if it is not associated with the policy. For security group policies, a resource is considered noncompliant if it doesn't comply with the rules of the policy and remediation is disabled or not possible.</p>"}, "EvaluationLimitExceeded": {"shape": "Boolean", "documentation": "<p>Indicates that over 100 resources are noncompliant with the Firewall Manager policy.</p>"}}, "documentation": "<p>Describes the compliance status for the account. An account is considered noncompliant if it includes resources that are not protected by the specified policy or that don't comply with the policy.</p>"}, "EvaluationResults": {"type": "list", "member": {"shape": "EvaluationResult"}}, "ExpectedRoute": {"type": "structure", "members": {"IpV4Cidr": {"shape": "CIDR", "documentation": "<p>Information about the IPv4 CIDR block.</p>"}, "PrefixListId": {"shape": "CIDR", "documentation": "<p>Information about the ID of the prefix list for the route.</p>"}, "IpV6Cidr": {"shape": "CIDR", "documentation": "<p>Information about the IPv6 CIDR block.</p>"}, "ContributingSubnets": {"shape": "ResourceIdList", "documentation": "<p>Information about the contributing subnets.</p>"}, "AllowedTargets": {"shape": "LengthBoundedStringList", "documentation": "<p>Information about the allowed targets.</p>"}, "RouteTableId": {"shape": "ResourceId", "documentation": "<p>Information about the route table ID.</p>"}}, "documentation": "<p>Information about the expected route in the route table.</p>"}, "ExpectedRoutes": {"type": "list", "member": {"shape": "ExpectedRoute"}}, "FMSPolicyUpdateFirewallCreationConfigAction": {"type": "structure", "members": {"Description": {"shape": "LengthBoundedString", "documentation": "<p>Describes the remedial action.</p>"}, "FirewallCreationConfig": {"shape": "ManagedServiceData", "documentation": "<p>A <code>FirewallCreationConfig</code> that you can copy into your current policy's <a href=\"https://docs.aws.amazon.com/fms/2018-01-01/APIReference/API_SecurityServicePolicyData.html\">SecurityServiceData</a> in order to remedy scope violations.</p>"}}, "documentation": "<p>Contains information about the actions that you can take to remediate scope violations caused by your policy's <code>FirewallCreationConfig</code>. <code>FirewallCreationConfig</code> is an optional configuration that you can use to choose which Availability Zones Firewall Manager creates Network Firewall endpoints in.</p>"}, "FailedItem": {"type": "structure", "members": {"URI": {"shape": "Identifier", "documentation": "<p>The univeral resource indicator (URI) of the resource that failed.</p>"}, "Reason": {"shape": "FailedItemReason", "documentation": "<p>The reason the resource's association could not be updated.</p>"}}, "documentation": "<p>Details of a resource that failed when trying to update it's association to a resource set.</p>"}, "FailedItemList": {"type": "list", "member": {"shape": "FailedItem"}}, "FailedItemReason": {"type": "string", "enum": ["NOT_VALID_ARN", "NOT_VALID_PARTITION", "NOT_VALID_REGION", "NOT_VALID_SERVICE", "NOT_VALID_RESOURCE_TYPE", "NOT_VALID_ACCOUNT_ID"]}, "FirewallDeploymentModel": {"type": "string", "enum": ["CENTRALIZED", "DISTRIBUTED"]}, "FirewallPolicyId": {"type": "string", "max": 1024, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "FirewallPolicyName": {"type": "string", "max": 1024, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "FirewallSubnetIsOutOfScopeViolation": {"type": "structure", "members": {"FirewallSubnetId": {"shape": "ResourceId", "documentation": "<p>The ID of the firewall subnet that violates the policy scope.</p>"}, "VpcId": {"shape": "ResourceId", "documentation": "<p>The VPC ID of the firewall subnet that violates the policy scope.</p>"}, "SubnetAvailabilityZone": {"shape": "LengthBoundedString", "documentation": "<p>The Availability Zone of the firewall subnet that violates the policy scope.</p>"}, "SubnetAvailabilityZoneId": {"shape": "LengthBoundedString", "documentation": "<p>The Availability Zone ID of the firewall subnet that violates the policy scope.</p>"}, "VpcEndpointId": {"shape": "ResourceId", "documentation": "<p>The VPC endpoint ID of the firewall subnet that violates the policy scope.</p>"}}, "documentation": "<p>Contains details about the firewall subnet that violates the policy scope.</p>"}, "FirewallSubnetMissingVPCEndpointViolation": {"type": "structure", "members": {"FirewallSubnetId": {"shape": "ResourceId", "documentation": "<p>The ID of the firewall that this VPC endpoint is associated with.</p>"}, "VpcId": {"shape": "ResourceId", "documentation": "<p>The resource ID of the VPC associated with the deleted VPC subnet.</p>"}, "SubnetAvailabilityZone": {"shape": "LengthBoundedString", "documentation": "<p>The name of the Availability Zone of the deleted VPC subnet.</p>"}, "SubnetAvailabilityZoneId": {"shape": "LengthBoundedString", "documentation": "<p>The ID of the Availability Zone of the deleted VPC subnet.</p>"}}, "documentation": "<p>The violation details for a firewall subnet's VPC endpoint that's deleted or missing.</p>"}, "GetAdminAccountRequest": {"type": "structure", "members": {}}, "GetAdminAccountResponse": {"type": "structure", "members": {"AdminAccount": {"shape": "AWSAccountId", "documentation": "<p>The account that is set as the Firewall Manager default administrator.</p>"}, "RoleStatus": {"shape": "AccountRoleStatus", "documentation": "<p>The status of the account that you set as the Firewall Manager default administrator.</p>"}}}, "GetAdminScopeRequest": {"type": "structure", "required": ["AdminAccount"], "members": {"AdminAccount": {"shape": "AWSAccountId", "documentation": "<p>The administator account that you want to get the details for.</p>"}}}, "GetAdminScopeResponse": {"type": "structure", "members": {"AdminScope": {"shape": "AdminScope", "documentation": "<p>Contains details about the administrative scope of the requested account.</p>"}, "Status": {"shape": "OrganizationStatus", "documentation": "<p>The current status of the request to onboard a member account as an Firewall Manager administator.</p> <ul> <li> <p> <code>ONBOARDING</code> - The account is onboarding to Firewall Manager as an administrator.</p> </li> <li> <p> <code>ONBOARDING_COMPLETE</code> - Firewall Manager The account is onboarded to Firewall Manager as an administrator, and can perform actions on the resources defined in their <a>AdminScope</a>.</p> </li> <li> <p> <code>OFFBOARDING</code> - The account is being removed as an Firewall Manager administrator.</p> </li> <li> <p> <code>OFFBOARDING_COMPLETE</code> - The account has been removed as an Firewall Manager administrator.</p> </li> </ul>"}}}, "GetAppsListRequest": {"type": "structure", "required": ["ListId"], "members": {"ListId": {"shape": "ListId", "documentation": "<p>The ID of the Firewall Manager applications list that you want the details for.</p>"}, "DefaultList": {"shape": "Boolean", "documentation": "<p>Specifies whether the list to retrieve is a default list owned by Firewall Manager.</p>"}}}, "GetAppsListResponse": {"type": "structure", "members": {"AppsList": {"shape": "AppsListData", "documentation": "<p>Information about the specified Firewall Manager applications list.</p>"}, "AppsListArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the applications list.</p>"}}}, "GetComplianceDetailRequest": {"type": "structure", "required": ["PolicyId", "MemberAccount"], "members": {"PolicyId": {"shape": "PolicyId", "documentation": "<p>The ID of the policy that you want to get the details for. <code>PolicyId</code> is returned by <code>PutPolicy</code> and by <code>ListPolicies</code>.</p>"}, "MemberAccount": {"shape": "AWSAccountId", "documentation": "<p>The Amazon Web Services account that owns the resources that you want to get the details for.</p>"}}}, "GetComplianceDetailResponse": {"type": "structure", "members": {"PolicyComplianceDetail": {"shape": "PolicyComplianceDetail", "documentation": "<p>Information about the resources and the policy that you specified in the <code>GetComplianceDetail</code> request.</p>"}}}, "GetNotificationChannelRequest": {"type": "structure", "members": {}}, "GetNotificationChannelResponse": {"type": "structure", "members": {"SnsTopicArn": {"shape": "ResourceArn", "documentation": "<p>The SNS topic that records Firewall Manager activity. </p>"}, "SnsRoleName": {"shape": "ResourceArn", "documentation": "<p>The IAM role that is used by Firewall Manager to record activity to SNS.</p>"}}}, "GetPolicyRequest": {"type": "structure", "required": ["PolicyId"], "members": {"PolicyId": {"shape": "PolicyId", "documentation": "<p>The ID of the Firewall Manager policy that you want the details for.</p>"}}}, "GetPolicyResponse": {"type": "structure", "members": {"Policy": {"shape": "Policy", "documentation": "<p>Information about the specified Firewall Manager policy.</p>"}, "PolicyArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the specified policy.</p>"}}}, "GetProtectionStatusRequest": {"type": "structure", "required": ["PolicyId"], "members": {"PolicyId": {"shape": "PolicyId", "documentation": "<p>The ID of the policy for which you want to get the attack information.</p>"}, "MemberAccountId": {"shape": "AWSAccountId", "documentation": "<p>The Amazon Web Services account that is in scope of the policy that you want to get the details for.</p>"}, "StartTime": {"shape": "TimeStamp", "documentation": "<p>The start of the time period to query for the attacks. This is a <code>timestamp</code> type. The request syntax listing indicates a <code>number</code> type because the default used by Firewall Manager is Unix time in seconds. However, any valid <code>timestamp</code> format is allowed.</p>"}, "EndTime": {"shape": "TimeStamp", "documentation": "<p>The end of the time period to query for the attacks. This is a <code>timestamp</code> type. The request syntax listing indicates a <code>number</code> type because the default used by Firewall Manager is Unix time in seconds. However, any valid <code>timestamp</code> format is allowed.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>If you specify a value for <code>MaxResults</code> and you have more objects than the number that you specify for <code>MaxResults</code>, Firewall Manager returns a <code>NextToken</code> value in the response, which you can use to retrieve another group of objects. For the second and subsequent <code>GetProtectionStatus</code> requests, specify the value of <code>NextToken</code> from the previous response to get information about another batch of objects.</p>"}, "MaxResults": {"shape": "PaginationMaxResults", "documentation": "<p>Specifies the number of objects that you want Firewall Manager to return for this request. If you have more objects than the number that you specify for <code>MaxResults</code>, the response includes a <code>NextToken</code> value that you can use to get another batch of objects.</p>"}}}, "GetProtectionStatusResponse": {"type": "structure", "members": {"AdminAccountId": {"shape": "AWSAccountId", "documentation": "<p>The ID of the Firewall Manager administrator account for this policy.</p>"}, "ServiceType": {"shape": "SecurityServiceType", "documentation": "<p>The service type that is protected by the policy. Currently, this is always <code>SHIELD_ADVANCED</code>.</p>"}, "Data": {"shape": "ProtectionData", "documentation": "<p>Details about the attack, including the following:</p> <ul> <li> <p>Attack type</p> </li> <li> <p>Account ID</p> </li> <li> <p>ARN of the resource attacked</p> </li> <li> <p>Start time of the attack</p> </li> <li> <p>End time of the attack (ongoing attacks will not have an end time)</p> </li> </ul> <p>The details are in JSON format. </p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>If you have more objects than the number that you specified for <code>MaxResults</code> in the request, the response includes a <code>NextToken</code> value. To list more objects, submit another <code>GetProtectionStatus</code> request, and specify the <code>NextToken</code> value from the response in the <code>NextToken</code> value in the next request.</p> <p>Amazon Web Services SDKs provide auto-pagination that identify <code>NextToken</code> in a response and make subsequent request calls automatically on your behalf. However, this feature is not supported by <code>GetProtectionStatus</code>. You must submit subsequent requests with <code>NextToken</code> using your own processes. </p>"}}}, "GetProtocolsListRequest": {"type": "structure", "required": ["ListId"], "members": {"ListId": {"shape": "ListId", "documentation": "<p>The ID of the Firewall Manager protocols list that you want the details for.</p>"}, "DefaultList": {"shape": "Boolean", "documentation": "<p>Specifies whether the list to retrieve is a default list owned by Firewall Manager.</p>"}}}, "GetProtocolsListResponse": {"type": "structure", "members": {"ProtocolsList": {"shape": "ProtocolsListData", "documentation": "<p>Information about the specified Firewall Manager protocols list.</p>"}, "ProtocolsListArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the specified protocols list.</p>"}}}, "GetResourceSetRequest": {"type": "structure", "required": ["Identifier"], "members": {"Identifier": {"shape": "Base62Id", "documentation": "<p>A unique identifier for the resource set, used in a request to refer to the resource set.</p>"}}}, "GetResourceSetResponse": {"type": "structure", "required": ["ResourceSet", "ResourceSetArn"], "members": {"ResourceSet": {"shape": "ResourceSet", "documentation": "<p>Information about the specified resource set.</p>"}, "ResourceSetArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource set.</p>"}}}, "GetThirdPartyFirewallAssociationStatusRequest": {"type": "structure", "required": ["ThirdPartyFirewall"], "members": {"ThirdPartyFirewall": {"shape": "ThirdPartyFirewall", "documentation": "<p>The name of the third-party firewall vendor.</p>"}}}, "GetThirdPartyFirewallAssociationStatusResponse": {"type": "structure", "members": {"ThirdPartyFirewallStatus": {"shape": "ThirdPartyFirewallAssociationStatus", "documentation": "<p>The current status for setting a Firewall Manager policy administrators account as an administrator of the third-party firewall tenant.</p> <ul> <li> <p> <code>ONBOARDING</code> - The Firewall Manager policy administrator is being designated as a tenant administrator.</p> </li> <li> <p> <code>ONBOARD_COMPLETE</code> - The Firewall Manager policy administrator is designated as a tenant administrator.</p> </li> <li> <p> <code>OFFBOARDING</code> - The Firewall Manager policy administrator is being removed as a tenant administrator.</p> </li> <li> <p> <code>OFFBOARD_COMPLETE</code> - The Firewall Manager policy administrator has been removed as a tenant administrator.</p> </li> <li> <p> <code>NOT_EXIST</code> - The Firewall Manager policy administrator doesn't exist as a tenant administrator.</p> </li> </ul>"}, "MarketplaceOnboardingStatus": {"shape": "MarketplaceSubscriptionOnboardingStatus", "documentation": "<p>The status for subscribing to the third-party firewall vendor in the Amazon Web Services Marketplace.</p> <ul> <li> <p> <code>NO_SUBSCRIPTION</code> - The Firewall Manager policy administrator isn't subscribed to the third-party firewall service in the Amazon Web Services Marketplace.</p> </li> <li> <p> <code>NOT_COMPLETE</code> - The Firewall Manager policy administrator is in the process of subscribing to the third-party firewall service in the Amazon Web Services Marketplace, but doesn't yet have an active subscription.</p> </li> <li> <p> <code>COMPLETE</code> - The Firewall Manager policy administrator has an active subscription to the third-party firewall service in the Amazon Web Services Marketplace.</p> </li> </ul>"}}}, "GetViolationDetailsRequest": {"type": "structure", "required": ["PolicyId", "MemberAccount", "ResourceId", "ResourceType"], "members": {"PolicyId": {"shape": "PolicyId", "documentation": "<p>The ID of the Firewall Manager policy that you want the details for. You can get violation details for the following policy types:</p> <ul> <li> <p>DNS Firewall</p> </li> <li> <p>Imported Network Firewall</p> </li> <li> <p>Network Firewall</p> </li> <li> <p>Security group content audit</p> </li> <li> <p>Third-party firewall</p> </li> </ul>"}, "MemberAccount": {"shape": "AWSAccountId", "documentation": "<p>The Amazon Web Services account ID that you want the details for.</p>"}, "ResourceId": {"shape": "ResourceId", "documentation": "<p>The ID of the resource that has violations.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The resource type. This is in the format shown in the <a href=\"https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-template-resource-type-ref.html\">Amazon Web Services Resource Types Reference</a>. Supported resource types are: <code>AWS::EC2::Instance</code>, <code>AWS::EC2::NetworkInterface</code>, <code>AWS::EC2::SecurityGroup</code>, <code>AWS::NetworkFirewall::FirewallPolicy</code>, and <code>AWS::EC2::Subnet</code>. </p>"}}}, "GetViolationDetailsResponse": {"type": "structure", "members": {"ViolationDetail": {"shape": "ViolationDetail", "documentation": "<p>Violation detail for a resource.</p>"}}}, "IPPortNumber": {"type": "long", "max": 65535, "min": 0}, "Identifier": {"type": "string", "max": 2048, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "IdentifierList": {"type": "list", "member": {"shape": "Identifier"}}, "InternalErrorException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The operation failed because of a system problem, even though the request was valid. Retry your request.</p>", "exception": true}, "InvalidInputException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The parameters of the request were invalid.</p>", "exception": true}, "InvalidOperationException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The operation failed because there was nothing to do or the operation wasn't possible. For example, you might have submitted an <code>AssociateAdminAccount</code> request for an account ID that was already set as the Firewall Manager administrator. Or you might have tried to access a Region that's disabled by default, and that you need to enable for the Firewall Manager administrator account and for Organizations before you can access it.</p>", "exception": true}, "InvalidTypeException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The value of the <code>Type</code> parameter is invalid.</p>", "exception": true}, "IssueInfoMap": {"type": "map", "key": {"shape": "DependentServiceName"}, "value": {"shape": "DetailedInfo"}}, "LengthBoundedString": {"type": "string", "max": 1024, "min": 0}, "LengthBoundedStringList": {"type": "list", "member": {"shape": "LengthBoundedString"}}, "LimitExceededException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The operation exceeds a resource limit, for example, the maximum number of <code>policy</code> objects that you can create for an Amazon Web Services account. For more information, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/fms-limits.html\">Firewall Manager Limits</a> in the <i>WAF Developer Guide</i>.</p>", "exception": true}, "ListAdminAccountsForOrganizationRequest": {"type": "structure", "members": {"NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Firewall Manager returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}, "MaxResults": {"shape": "PaginationMaxResults", "documentation": "<p>The maximum number of objects that you want Firewall Manager to return for this request. If more objects are available, in the response, Firewall Manager provides a <code>NextToken</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}}}, "ListAdminAccountsForOrganizationResponse": {"type": "structure", "members": {"AdminAccounts": {"shape": "AdminAccountSummaryList", "documentation": "<p>A list of Firewall Manager administrator accounts within the organization that were onboarded as administrators by <a>AssociateAdminAccount</a> or <a>PutAdminAccount</a>.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Firewall Manager returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}}}, "ListAdminsManagingAccountRequest": {"type": "structure", "members": {"NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Firewall Manager returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}, "MaxResults": {"shape": "PaginationMaxResults", "documentation": "<p>The maximum number of objects that you want Firewall Manager to return for this request. If more objects are available, in the response, Firewall Manager provides a <code>NextToken</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}}}, "ListAdminsManagingAccountResponse": {"type": "structure", "members": {"AdminAccounts": {"shape": "AccountIdList", "documentation": "<p>The list of accounts who manage member accounts within their <a>AdminScope</a>.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Firewall Manager returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}}}, "ListAppsListsRequest": {"type": "structure", "required": ["MaxResults"], "members": {"DefaultLists": {"shape": "Boolean", "documentation": "<p>Specifies whether the lists to retrieve are default lists owned by Firewall Manager.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>If you specify a value for <code>MaxResults</code> in your list request, and you have more objects than the maximum, Firewall Manager returns this token in the response. For all but the first request, you provide the token returned by the prior request in the request parameters, to retrieve the next batch of objects.</p>"}, "MaxResults": {"shape": "PaginationMaxResults", "documentation": "<p>The maximum number of objects that you want Firewall Manager to return for this request. If more objects are available, in the response, Firewall Manager provides a <code>NextToken</code> value that you can use in a subsequent call to get the next batch of objects.</p> <p>If you don't specify this, Firewall Manager returns all available objects.</p>"}}}, "ListAppsListsResponse": {"type": "structure", "members": {"AppsLists": {"shape": "AppsListsData", "documentation": "<p>An array of <code>AppsListDataSummary</code> objects.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>If you specify a value for <code>MaxResults</code> in your list request, and you have more objects than the maximum, Firewall Manager returns this token in the response. You can use this token in subsequent requests to retrieve the next batch of objects.</p>"}}}, "ListComplianceStatusRequest": {"type": "structure", "required": ["PolicyId"], "members": {"PolicyId": {"shape": "PolicyId", "documentation": "<p>The ID of the Firewall Manager policy that you want the details for.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>If you specify a value for <code>MaxResults</code> and you have more <code>PolicyComplianceStatus</code> objects than the number that you specify for <code>MaxResults</code>, Firewall Manager returns a <code>NextToken</code> value in the response that allows you to list another group of <code>PolicyComplianceStatus</code> objects. For the second and subsequent <code>ListComplianceStatus</code> requests, specify the value of <code>NextToken</code> from the previous response to get information about another batch of <code>PolicyComplianceStatus</code> objects.</p>"}, "MaxResults": {"shape": "PaginationMaxResults", "documentation": "<p>Specifies the number of <code>PolicyComplianceStatus</code> objects that you want Firewall Manager to return for this request. If you have more <code>PolicyComplianceStatus</code> objects than the number that you specify for <code>MaxResults</code>, the response includes a <code>NextToken</code> value that you can use to get another batch of <code>PolicyComplianceStatus</code> objects.</p>"}}}, "ListComplianceStatusResponse": {"type": "structure", "members": {"PolicyComplianceStatusList": {"shape": "PolicyComplianceStatusList", "documentation": "<p>An array of <code>PolicyComplianceStatus</code> objects.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>If you have more <code>PolicyComplianceStatus</code> objects than the number that you specified for <code>MaxResults</code> in the request, the response includes a <code>NextToken</code> value. To list more <code>PolicyComplianceStatus</code> objects, submit another <code>ListComplianceStatus</code> request, and specify the <code>NextToken</code> value from the response in the <code>NextToken</code> value in the next request.</p>"}}}, "ListDiscoveredResourcesRequest": {"type": "structure", "required": ["MemberAccountIds", "ResourceType"], "members": {"MemberAccountIds": {"shape": "AWSAccountIdList", "documentation": "<p>The Amazon Web Services account IDs to discover resources in. Only one account is supported per request. The account must be a member of your organization.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The type of resources to discover.</p>"}, "MaxResults": {"shape": "PaginationMaxResults", "documentation": "<p>The maximum number of objects that you want Firewall Manager to return for this request. If more objects are available, in the response, Firewall Manager provides a <code>NextToken</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Firewall Manager returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}}}, "ListDiscoveredResourcesResponse": {"type": "structure", "members": {"Items": {"shape": "DiscoveredResourceList", "documentation": "<p>Details of the resources that were discovered.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Firewall Manager returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}}}, "ListId": {"type": "string", "max": 36, "min": 36, "pattern": "^[a-z0-9A-Z-]{36}$"}, "ListMemberAccountsRequest": {"type": "structure", "members": {"NextToken": {"shape": "PaginationToken", "documentation": "<p>If you specify a value for <code>MaxResults</code> and you have more account IDs than the number that you specify for <code>MaxResults</code>, Firewall Manager returns a <code>NextToken</code> value in the response that allows you to list another group of IDs. For the second and subsequent <code>ListMemberAccountsRequest</code> requests, specify the value of <code>NextToken</code> from the previous response to get information about another batch of member account IDs.</p>"}, "MaxResults": {"shape": "PaginationMaxResults", "documentation": "<p>Specifies the number of member account IDs that you want Firewall Manager to return for this request. If you have more IDs than the number that you specify for <code>MaxResults</code>, the response includes a <code>NextToken</code> value that you can use to get another batch of member account IDs.</p>"}}}, "ListMemberAccountsResponse": {"type": "structure", "members": {"MemberAccounts": {"shape": "MemberAccounts", "documentation": "<p>An array of account IDs.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>If you have more member account IDs than the number that you specified for <code>MaxResults</code> in the request, the response includes a <code>NextToken</code> value. To list more IDs, submit another <code>ListMemberAccounts</code> request, and specify the <code>NextToken</code> value from the response in the <code>NextToken</code> value in the next request.</p>"}}}, "ListPoliciesRequest": {"type": "structure", "members": {"NextToken": {"shape": "PaginationToken", "documentation": "<p>If you specify a value for <code>MaxResults</code> and you have more <code>PolicySummary</code> objects than the number that you specify for <code>MaxResults</code>, Firewall Manager returns a <code>NextToken</code> value in the response that allows you to list another group of <code>PolicySummary</code> objects. For the second and subsequent <code>ListPolicies</code> requests, specify the value of <code>NextToken</code> from the previous response to get information about another batch of <code>PolicySummary</code> objects.</p>"}, "MaxResults": {"shape": "PaginationMaxResults", "documentation": "<p>Specifies the number of <code>PolicySummary</code> objects that you want Firewall Manager to return for this request. If you have more <code>PolicySummary</code> objects than the number that you specify for <code>MaxResults</code>, the response includes a <code>NextToken</code> value that you can use to get another batch of <code>PolicySummary</code> objects.</p>"}}}, "ListPoliciesResponse": {"type": "structure", "members": {"PolicyList": {"shape": "PolicySummaryList", "documentation": "<p>An array of <code>PolicySummary</code> objects.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>If you have more <code>PolicySummary</code> objects than the number that you specified for <code>MaxResults</code> in the request, the response includes a <code>NextToken</code> value. To list more <code>PolicySummary</code> objects, submit another <code>ListPolicies</code> request, and specify the <code>NextToken</code> value from the response in the <code>NextToken</code> value in the next request.</p>"}}}, "ListProtocolsListsRequest": {"type": "structure", "required": ["MaxResults"], "members": {"DefaultLists": {"shape": "Boolean", "documentation": "<p>Specifies whether the lists to retrieve are default lists owned by Firewall Manager.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>If you specify a value for <code>MaxResults</code> in your list request, and you have more objects than the maximum, Firewall Manager returns this token in the response. For all but the first request, you provide the token returned by the prior request in the request parameters, to retrieve the next batch of objects.</p>"}, "MaxResults": {"shape": "PaginationMaxResults", "documentation": "<p>The maximum number of objects that you want Firewall Manager to return for this request. If more objects are available, in the response, Firewall Manager provides a <code>NextToken</code> value that you can use in a subsequent call to get the next batch of objects.</p> <p>If you don't specify this, Firewall Manager returns all available objects.</p>"}}}, "ListProtocolsListsResponse": {"type": "structure", "members": {"ProtocolsLists": {"shape": "ProtocolsListsData", "documentation": "<p>An array of <code>ProtocolsListDataSummary</code> objects.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>If you specify a value for <code>MaxResults</code> in your list request, and you have more objects than the maximum, Firewall Manager returns this token in the response. You can use this token in subsequent requests to retrieve the next batch of objects.</p>"}}}, "ListResourceSetResourcesRequest": {"type": "structure", "required": ["Identifier"], "members": {"Identifier": {"shape": "ResourceId", "documentation": "<p>A unique identifier for the resource set, used in a request to refer to the resource set.</p>"}, "MaxResults": {"shape": "PaginationMaxResults", "documentation": "<p>The maximum number of objects that you want Firewall Manager to return for this request. If more objects are available, in the response, Firewall Manager provides a <code>NextToken</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Firewall Manager returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}}}, "ListResourceSetResourcesResponse": {"type": "structure", "required": ["Items"], "members": {"Items": {"shape": "ResourceList", "documentation": "<p>An array of the associated resources' uniform resource identifiers (URI).</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Firewall Manager returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}}}, "ListResourceSetsRequest": {"type": "structure", "members": {"NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Firewall Manager returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}, "MaxResults": {"shape": "PaginationMaxResults", "documentation": "<p>The maximum number of objects that you want Firewall Manager to return for this request. If more objects are available, in the response, Firewall Manager provides a <code>NextToken</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}}}, "ListResourceSetsResponse": {"type": "structure", "members": {"ResourceSets": {"shape": "ResourceSetSummaryList", "documentation": "<p>An array of <code>ResourceSetSummary</code> objects.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Firewall Manager returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource to return tags for. The Firewall Manager resources that support tagging are policies, applications lists, and protocols lists. </p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"TagList": {"shape": "TagList", "documentation": "<p>The tags associated with the resource.</p>"}}}, "ListThirdPartyFirewallFirewallPoliciesRequest": {"type": "structure", "required": ["ThirdPartyFirewall", "MaxResults"], "members": {"ThirdPartyFirewall": {"shape": "ThirdPartyFirewall", "documentation": "<p>The name of the third-party firewall vendor.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>If the previous response included a <code>NextToken</code> element, the specified third-party firewall vendor is associated with more third-party firewall policies. To get more third-party firewall policies, submit another <code>ListThirdPartyFirewallFirewallPoliciesRequest</code> request.</p> <p> For the value of <code>NextToken</code>, specify the value of <code>NextToken</code> from the previous response. If the previous response didn't include a <code>NextToken</code> element, there are no more third-party firewall policies to get. </p>"}, "MaxResults": {"shape": "PaginationMaxResults", "documentation": "<p>The maximum number of third-party firewall policies that you want Firewall Manager to return. If the specified third-party firewall vendor is associated with more than <code>MaxResults</code> firewall policies, the response includes a <code>NextToken</code> element. <code>NextToken</code> contains an encrypted token that identifies the first third-party firewall policies that Firewall Manager will return if you submit another request.</p>"}}}, "ListThirdPartyFirewallFirewallPoliciesResponse": {"type": "structure", "members": {"ThirdPartyFirewallFirewallPolicies": {"shape": "ThirdPartyFirewallFirewallPolicies", "documentation": "<p>A list that contains one <code>ThirdPartyFirewallFirewallPolicies</code> element for each third-party firewall policies that the specified third-party firewall vendor is associated with. Each <code>ThirdPartyFirewallFirewallPolicies</code> element contains the firewall policy name and ID.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>The value that you will use for <code>NextToken</code> in the next <code>ListThirdPartyFirewallFirewallPolicies</code> request.</p>"}}}, "ManagedServiceData": {"type": "string", "max": 10000, "min": 1, "pattern": "^((?!\\\\[nr]).)+"}, "MarketplaceSubscriptionOnboardingStatus": {"type": "string", "enum": ["NO_SUBSCRIPTION", "NOT_COMPLETE", "COMPLETE"]}, "MemberAccounts": {"type": "list", "member": {"shape": "AWSAccountId"}}, "Name": {"type": "string", "max": 128, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "NetworkFirewallAction": {"type": "string", "max": 128, "min": 1, "pattern": "^[a-zA-Z0-9]+$"}, "NetworkFirewallActionList": {"type": "list", "member": {"shape": "NetworkFirewallAction"}}, "NetworkFirewallBlackHoleRouteDetectedViolation": {"type": "structure", "members": {"ViolationTarget": {"shape": "Violation<PERSON>arget", "documentation": "<p>The subnet that has an inactive state.</p>"}, "RouteTableId": {"shape": "ResourceId", "documentation": "<p>Information about the route table ID.</p>"}, "VpcId": {"shape": "ResourceId", "documentation": "<p>Information about the VPC ID.</p>"}, "ViolatingRoutes": {"shape": "Routes", "documentation": "<p>Information about the route or routes that are in violation.</p>"}}, "documentation": "<p>Violation detail for an internet gateway route with an inactive state in the customer subnet route table or Network Firewall subnet route table.</p>"}, "NetworkFirewallInternetTrafficNotInspectedViolation": {"type": "structure", "members": {"SubnetId": {"shape": "ResourceId", "documentation": "<p>The subnet ID.</p>"}, "SubnetAvailabilityZone": {"shape": "LengthBoundedString", "documentation": "<p>The subnet Availability Zone.</p>"}, "RouteTableId": {"shape": "ResourceId", "documentation": "<p>Information about the route table ID.</p>"}, "ViolatingRoutes": {"shape": "Routes", "documentation": "<p>The route or routes that are in violation.</p>"}, "IsRouteTableUsedInDifferentAZ": {"shape": "Boolean", "documentation": "<p>Information about whether the route table is used in another Availability Zone.</p>"}, "CurrentFirewallSubnetRouteTable": {"shape": "ResourceId", "documentation": "<p>Information about the subnet route table for the current firewall.</p>"}, "ExpectedFirewallEndpoint": {"shape": "ResourceId", "documentation": "<p>The expected endpoint for the current firewall.</p>"}, "FirewallSubnetId": {"shape": "ResourceId", "documentation": "<p>The firewall subnet ID.</p>"}, "ExpectedFirewallSubnetRoutes": {"shape": "ExpectedRoutes", "documentation": "<p>The firewall subnet routes that are expected.</p>"}, "ActualFirewallSubnetRoutes": {"shape": "Routes", "documentation": "<p>The actual firewall subnet routes.</p>"}, "InternetGatewayId": {"shape": "ResourceId", "documentation": "<p>The internet gateway ID.</p>"}, "CurrentInternetGatewayRouteTable": {"shape": "ResourceId", "documentation": "<p>The current route table for the internet gateway.</p>"}, "ExpectedInternetGatewayRoutes": {"shape": "ExpectedRoutes", "documentation": "<p>The internet gateway routes that are expected.</p>"}, "ActualInternetGatewayRoutes": {"shape": "Routes", "documentation": "<p>The actual internet gateway routes.</p>"}, "VpcId": {"shape": "ResourceId", "documentation": "<p>Information about the VPC ID.</p>"}}, "documentation": "<p>Violation detail for the subnet for which internet traffic that hasn't been inspected.</p>"}, "NetworkFirewallInvalidRouteConfigurationViolation": {"type": "structure", "members": {"AffectedSubnets": {"shape": "ResourceIdList", "documentation": "<p>The subnets that are affected.</p>"}, "RouteTableId": {"shape": "ResourceId", "documentation": "<p>The route table ID.</p>"}, "IsRouteTableUsedInDifferentAZ": {"shape": "Boolean", "documentation": "<p>Information about whether the route table is used in another Availability Zone.</p>"}, "ViolatingRoute": {"shape": "Route", "documentation": "<p>The route that's in violation.</p>"}, "CurrentFirewallSubnetRouteTable": {"shape": "ResourceId", "documentation": "<p>The subnet route table for the current firewall.</p>"}, "ExpectedFirewallEndpoint": {"shape": "ResourceId", "documentation": "<p>The firewall endpoint that's expected.</p>"}, "ActualFirewallEndpoint": {"shape": "ResourceId", "documentation": "<p>The actual firewall endpoint.</p>"}, "ExpectedFirewallSubnetId": {"shape": "ResourceId", "documentation": "<p>The expected subnet ID for the firewall.</p>"}, "ActualFirewallSubnetId": {"shape": "ResourceId", "documentation": "<p>The actual subnet ID for the firewall.</p>"}, "ExpectedFirewallSubnetRoutes": {"shape": "ExpectedRoutes", "documentation": "<p>The firewall subnet routes that are expected.</p>"}, "ActualFirewallSubnetRoutes": {"shape": "Routes", "documentation": "<p>The actual firewall subnet routes that are expected.</p>"}, "InternetGatewayId": {"shape": "ResourceId", "documentation": "<p>The internet gateway ID.</p>"}, "CurrentInternetGatewayRouteTable": {"shape": "ResourceId", "documentation": "<p>The route table for the current internet gateway.</p>"}, "ExpectedInternetGatewayRoutes": {"shape": "ExpectedRoutes", "documentation": "<p>The expected routes for the internet gateway.</p>"}, "ActualInternetGatewayRoutes": {"shape": "Routes", "documentation": "<p>The actual internet gateway routes.</p>"}, "VpcId": {"shape": "ResourceId", "documentation": "<p>Information about the VPC ID.</p>"}}, "documentation": "<p>Violation detail for the improperly configured subnet route. It's possible there is a missing route table route, or a configuration that causes traffic to cross an Availability Zone boundary.</p>"}, "NetworkFirewallMissingExpectedRTViolation": {"type": "structure", "members": {"ViolationTarget": {"shape": "Violation<PERSON>arget", "documentation": "<p>The ID of the Network Firewall or VPC resource that's in violation.</p>"}, "VPC": {"shape": "ResourceId", "documentation": "<p>The resource ID of the VPC associated with a violating subnet.</p>"}, "AvailabilityZone": {"shape": "LengthBoundedString", "documentation": "<p>The Availability Zone of a violating subnet. </p>"}, "CurrentRouteTable": {"shape": "ResourceId", "documentation": "<p>The resource ID of the current route table that's associated with the subnet, if one is available.</p>"}, "ExpectedRouteTable": {"shape": "ResourceId", "documentation": "<p>The resource ID of the route table that should be associated with the subnet.</p>"}}, "documentation": "<p>Violation detail for Network Firewall for a subnet that's not associated to the expected Firewall Manager managed route table.</p>"}, "NetworkFirewallMissingExpectedRoutesViolation": {"type": "structure", "members": {"ViolationTarget": {"shape": "Violation<PERSON>arget", "documentation": "<p>The target of the violation.</p>"}, "ExpectedRoutes": {"shape": "ExpectedRoutes", "documentation": "<p>The expected routes.</p>"}, "VpcId": {"shape": "ResourceId", "documentation": "<p>Information about the VPC ID.</p>"}}, "documentation": "<p>Violation detail for an expected route missing in Network Firewall.</p>"}, "NetworkFirewallMissingFirewallViolation": {"type": "structure", "members": {"ViolationTarget": {"shape": "Violation<PERSON>arget", "documentation": "<p>The ID of the Network Firewall or VPC resource that's in violation.</p>"}, "VPC": {"shape": "ResourceId", "documentation": "<p>The resource ID of the VPC associated with a violating subnet.</p>"}, "AvailabilityZone": {"shape": "LengthBoundedString", "documentation": "<p>The Availability Zone of a violating subnet. </p>"}, "TargetViolationReason": {"shape": "TargetViolationReason", "documentation": "<p>The reason the resource has this violation, if one is available. </p>"}}, "documentation": "<p>Violation detail for Network Firewall for a subnet that doesn't have a Firewall Manager managed firewall in its VPC. </p>"}, "NetworkFirewallMissingSubnetViolation": {"type": "structure", "members": {"ViolationTarget": {"shape": "Violation<PERSON>arget", "documentation": "<p>The ID of the Network Firewall or VPC resource that's in violation.</p>"}, "VPC": {"shape": "ResourceId", "documentation": "<p>The resource ID of the VPC associated with a violating subnet.</p>"}, "AvailabilityZone": {"shape": "LengthBoundedString", "documentation": "<p>The Availability Zone of a violating subnet. </p>"}, "TargetViolationReason": {"shape": "TargetViolationReason", "documentation": "<p>The reason the resource has this violation, if one is available. </p>"}}, "documentation": "<p>Violation detail for Network Firewall for an Availability Zone that's missing the expected Firewall Manager managed subnet.</p>"}, "NetworkFirewallOverrideAction": {"type": "string", "enum": ["DROP_TO_ALERT"]}, "NetworkFirewallPolicy": {"type": "structure", "members": {"FirewallDeploymentModel": {"shape": "FirewallDeploymentModel", "documentation": "<p>Defines the deployment model to use for the firewall policy. To use a distributed model, set <a href=\"https://docs.aws.amazon.com/fms/2018-01-01/APIReference/API_PolicyOption.html\">PolicyOption</a> to <code>NULL</code>.</p>"}}, "documentation": "<p>Configures the firewall policy deployment model of Network Firewall. For information about Network Firewall deployment models, see <a href=\"https://docs.aws.amazon.com/network-firewall/latest/developerguide/architectures.html\">Network Firewall example architectures with routing</a> in the <i>Network Firewall Developer Guide</i>.</p>"}, "NetworkFirewallPolicyDescription": {"type": "structure", "members": {"StatelessRuleGroups": {"shape": "StatelessRuleGroupList", "documentation": "<p>The stateless rule groups that are used in the Network Firewall firewall policy. </p>"}, "StatelessDefaultActions": {"shape": "NetworkFirewallActionList", "documentation": "<p>The actions to take on packets that don't match any of the stateless rule groups. </p>"}, "StatelessFragmentDefaultActions": {"shape": "NetworkFirewallActionList", "documentation": "<p>The actions to take on packet fragments that don't match any of the stateless rule groups. </p>"}, "StatelessCustomActions": {"shape": "NetworkFirewallActionList", "documentation": "<p>Names of custom actions that are available for use in the stateless default actions settings.</p>"}, "StatefulRuleGroups": {"shape": "StatefulRuleGroupList", "documentation": "<p>The stateful rule groups that are used in the Network Firewall firewall policy. </p>"}, "StatefulDefaultActions": {"shape": "NetworkFirewallActionList", "documentation": "<p>The default actions to take on a packet that doesn't match any stateful rules. The stateful default action is optional, and is only valid when using the strict rule order.</p> <p> Valid values of the stateful default action: </p> <ul> <li> <p>aws:drop_strict</p> </li> <li> <p>aws:drop_established</p> </li> <li> <p>aws:alert_strict</p> </li> <li> <p>aws:alert_established</p> </li> </ul>"}, "StatefulEngineOptions": {"shape": "StatefulEngineOptions", "documentation": "<p>Additional options governing how Network Firewall handles stateful rules. The stateful rule groups that you use in your policy must have stateful rule options settings that are compatible with these settings.</p>"}}, "documentation": "<p>The definition of the Network Firewall firewall policy.</p>"}, "NetworkFirewallPolicyModifiedViolation": {"type": "structure", "members": {"ViolationTarget": {"shape": "Violation<PERSON>arget", "documentation": "<p>The ID of the Network Firewall or VPC resource that's in violation.</p>"}, "CurrentPolicyDescription": {"shape": "NetworkFirewallPolicyDescription", "documentation": "<p>The policy that's currently in use in the individual account. </p>"}, "ExpectedPolicyDescription": {"shape": "NetworkFirewallPolicyDescription", "documentation": "<p>The policy that should be in use in the individual account in order to be compliant. </p>"}}, "documentation": "<p>Violation detail for Network Firewall for a firewall policy that has a different <a>NetworkFirewallPolicyDescription</a> than is required by the Firewall Manager policy. </p>"}, "NetworkFirewallResourceName": {"type": "string", "max": 128, "min": 1, "pattern": "^[a-zA-Z0-9-]+$"}, "NetworkFirewallStatefulRuleGroupOverride": {"type": "structure", "members": {"Action": {"shape": "NetworkFirewallOverrideAction", "documentation": "<p>The action that changes the rule group from <code>DROP</code> to <code>ALERT</code>. This only applies to managed rule groups.</p>"}}, "documentation": "<p>The setting that allows the policy owner to change the behavior of the rule group within a policy.</p>"}, "NetworkFirewallUnexpectedFirewallRoutesViolation": {"type": "structure", "members": {"FirewallSubnetId": {"shape": "ResourceId", "documentation": "<p>The subnet ID for the firewall.</p>"}, "ViolatingRoutes": {"shape": "Routes", "documentation": "<p>The routes that are in violation.</p>"}, "RouteTableId": {"shape": "ResourceId", "documentation": "<p>The ID of the route table.</p>"}, "FirewallEndpoint": {"shape": "ResourceId", "documentation": "<p>The endpoint of the firewall.</p>"}, "VpcId": {"shape": "ResourceId", "documentation": "<p>Information about the VPC ID.</p>"}}, "documentation": "<p>Violation detail for an unexpected route that's present in a route table.</p>"}, "NetworkFirewallUnexpectedGatewayRoutesViolation": {"type": "structure", "members": {"GatewayId": {"shape": "ResourceId", "documentation": "<p>Information about the gateway ID.</p>"}, "ViolatingRoutes": {"shape": "Routes", "documentation": "<p>The routes that are in violation.</p>"}, "RouteTableId": {"shape": "ResourceId", "documentation": "<p>Information about the route table.</p>"}, "VpcId": {"shape": "ResourceId", "documentation": "<p>Information about the VPC ID.</p>"}}, "documentation": "<p>Violation detail for an unexpected gateway route that’s present in a route table.</p>"}, "OrderedRemediationActions": {"type": "list", "member": {"shape": "RemediationActionWithOrder"}}, "OrganizationStatus": {"type": "string", "enum": ["ONBOARDING", "ONBOARDING_COMPLETE", "OFFBOARDING", "OFFBOARDING_COMPLETE"]}, "OrganizationalUnitId": {"type": "string", "max": 68, "min": 16, "pattern": "^ou-[0-9a-z]{4,32}-[a-z0-9]{8,32}$"}, "OrganizationalUnitIdList": {"type": "list", "member": {"shape": "OrganizationalUnitId"}}, "OrganizationalUnitScope": {"type": "structure", "members": {"OrganizationalUnits": {"shape": "OrganizationalUnitIdList", "documentation": "<p>The list of OUs within the organization that the specified Firewall Manager administrator either can or cannot apply policies to, based on the value of <code>OrganizationalUnitScope$ExcludeSpecifiedOrganizationalUnits</code>. If <code>OrganizationalUnitScope$ExcludeSpecifiedOrganizationalUnits</code> is set to <code>true</code>, then the Firewall Manager administrator can apply policies to all OUs in the organization except for the OUs in this list. If <code>OrganizationalUnitScope$ExcludeSpecifiedOrganizationalUnits</code> is set to <code>false</code>, then the Firewall Manager administrator can only apply policies to the OUs in this list.</p>"}, "AllOrganizationalUnitsEnabled": {"shape": "Boolean", "documentation": "<p>A boolean value that indicates if the administrator can apply policies to all OUs within an organization. If true, the administrator can manage all OUs within the organization. You can either enable management of all OUs through this operation, or you can specify OUs to manage in <code>OrganizationalUnitScope$OrganizationalUnits</code>. You cannot specify both.</p>"}, "ExcludeSpecifiedOrganizationalUnits": {"shape": "Boolean", "documentation": "<p>A boolean value that excludes the OUs in <code>OrganizationalUnitScope$OrganizationalUnits</code> from the administrator's scope. If true, the Firewall Manager administrator can apply policies to all OUs in the organization except for the OUs listed in <code>OrganizationalUnitScope$OrganizationalUnits</code>. You can either specify a list of OUs to exclude by <code>OrganizationalUnitScope$OrganizationalUnits</code>, or you can enable management of all OUs by <code>OrganizationalUnitScope$AllOrganizationalUnitsEnabled</code>. You cannot specify both.</p>"}}, "documentation": "<p>Defines the Organizations organizational units (OUs) that the specified Firewall Manager administrator can apply policies to. For more information about OUs in Organizations, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_ous.html\">Managing organizational units (OUs) </a> in the <i>Organizations User Guide</i>.</p>"}, "PaginationMaxResults": {"type": "integer", "max": 100, "min": 1}, "PaginationToken": {"type": "string", "max": 4096, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "PartialMatch": {"type": "structure", "members": {"Reference": {"shape": "ReferenceRule", "documentation": "<p>The reference rule from the primary security group of the Firewall Manager policy.</p>"}, "TargetViolationReasons": {"shape": "TargetViolationReasons", "documentation": "<p>The violation reason.</p>"}}, "documentation": "<p>The reference rule that partially matches the <code>ViolationTarget</code> rule and violation reason.</p>"}, "PartialMatches": {"type": "list", "member": {"shape": "PartialMatch"}}, "Policy": {"type": "structure", "required": ["PolicyName", "SecurityServicePolicyData", "ResourceType", "ExcludeResourceTags", "RemediationEnabled"], "members": {"PolicyId": {"shape": "PolicyId", "documentation": "<p>The ID of the Firewall Manager policy.</p>"}, "PolicyName": {"shape": "ResourceName", "documentation": "<p>The name of the Firewall Manager policy.</p>"}, "PolicyUpdateToken": {"shape": "PolicyUpdateToken", "documentation": "<p>A unique identifier for each update to the policy. When issuing a <code>PutPolicy</code> request, the <code>PolicyUpdateToken</code> in the request must match the <code>PolicyUpdateToken</code> of the current policy version. To get the <code>PolicyUpdateToken</code> of the current policy version, use a <code>GetPolicy</code> request.</p>"}, "SecurityServicePolicyData": {"shape": "SecurityServicePolicyData", "documentation": "<p>Details about the security service that is being used to protect the resources.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The type of resource protected by or in scope of the policy. This is in the format shown in the <a href=\"https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-template-resource-type-ref.html\">Amazon Web Services Resource Types Reference</a>. To apply this policy to multiple resource types, specify a resource type of <code>ResourceTypeList</code> and then specify the resource types in a <code>ResourceTypeList</code>.</p> <p>The following are valid resource types for each Firewall Manager policy type:</p> <ul> <li> <p>Amazon Web Services WAF Classic - <code>AWS::ApiGateway::Stage</code>, <code>AWS::CloudFront::Distribution</code>, and <code>AWS::ElasticLoadBalancingV2::LoadBalancer</code>.</p> </li> <li> <p>WAF - <code>AWS::ApiGateway::Stage</code>, <code>AWS::ElasticLoadBalancingV2::LoadBalancer</code>, and <code>AWS::CloudFront::Distribution</code>.</p> </li> <li> <p> DNS Firewall, Network Firewall, and third-party firewall - <code>AWS::EC2::VPC</code>.</p> </li> <li> <p>Shield Advanced - <code>AWS::ElasticLoadBalancingV2::LoadBalancer</code>, <code>AWS::ElasticLoadBalancing::LoadBalancer</code>, <code>AWS::EC2::EIP</code>, and <code>AWS::CloudFront::Distribution</code>.</p> </li> <li> <p>Security group content audit - <code>AWS::EC2::SecurityGroup</code>, <code>AWS::EC2::NetworkInterface</code>, and <code>AWS::EC2::Instance</code>.</p> </li> <li> <p>Security group usage audit - <code>AWS::EC2::SecurityGroup</code>.</p> </li> </ul>"}, "ResourceTypeList": {"shape": "ResourceTypeList", "documentation": "<p>An array of <code>ResourceType</code> objects. Use this only to specify multiple resource types. To specify a single resource type, use <code>ResourceType</code>.</p>"}, "ResourceTags": {"shape": "ResourceTags", "documentation": "<p>An array of <code>ResourceTag</code> objects.</p>"}, "ExcludeResourceTags": {"shape": "Boolean", "documentation": "<p>If set to <code>True</code>, resources with the tags that are specified in the <code>ResourceTag</code> array are not in scope of the policy. If set to <code>False</code>, and the <code>ResourceTag</code> array is not null, only resources with the specified tags are in scope of the policy.</p>"}, "RemediationEnabled": {"shape": "Boolean", "documentation": "<p>Indicates if the policy should be automatically applied to new resources.</p>"}, "DeleteUnusedFMManagedResources": {"shape": "Boolean", "documentation": "<p>Indicates whether Firewall Manager should automatically remove protections from resources that leave the policy scope and clean up resources that Firewall Manager is managing for accounts when those accounts leave policy scope. For example, Firewall Manager will disassociate a Firewall Manager managed web ACL from a protected customer resource when the customer resource leaves policy scope. </p> <p>By default, Firewall Manager doesn't remove protections or delete Firewall Manager managed resources. </p> <p>This option is not available for Shield Advanced or WAF Classic policies.</p>"}, "IncludeMap": {"shape": "CustomerPolicyScopeMap", "documentation": "<p>Specifies the Amazon Web Services account IDs and Organizations organizational units (OUs) to include in the policy. Specifying an OU is the equivalent of specifying all accounts in the OU and in any of its child OUs, including any child OUs and accounts that are added at a later time.</p> <p>You can specify inclusions or exclusions, but not both. If you specify an <code>IncludeMap</code>, Firewall Manager applies the policy to all accounts specified by the <code>IncludeMap</code>, and does not evaluate any <code>ExcludeMap</code> specifications. If you do not specify an <code>IncludeMap</code>, then Firewall Manager applies the policy to all accounts except for those specified by the <code>ExcludeMap</code>.</p> <p>You can specify account IDs, OUs, or a combination: </p> <ul> <li> <p>Specify account IDs by setting the key to <code>ACCOUNT</code>. For example, the following is a valid map: <code>{“ACCOUNT” : [“accountID1”, “accountID2”]}</code>.</p> </li> <li> <p>Specify OUs by setting the key to <code>ORG_UNIT</code>. For example, the following is a valid map: <code>{“ORG_UNIT” : [“ouid111”, “ouid112”]}</code>.</p> </li> <li> <p>Specify accounts and OUs together in a single map, separated with a comma. For example, the following is a valid map: <code>{“ACCOUNT” : [“accountID1”, “accountID2”], “ORG_UNIT” : [“ouid111”, “ouid112”]}</code>.</p> </li> </ul>"}, "ExcludeMap": {"shape": "CustomerPolicyScopeMap", "documentation": "<p>Specifies the Amazon Web Services account IDs and Organizations organizational units (OUs) to exclude from the policy. Specifying an OU is the equivalent of specifying all accounts in the OU and in any of its child OUs, including any child OUs and accounts that are added at a later time.</p> <p>You can specify inclusions or exclusions, but not both. If you specify an <code>IncludeMap</code>, Firewall Manager applies the policy to all accounts specified by the <code>IncludeMap</code>, and does not evaluate any <code>ExcludeMap</code> specifications. If you do not specify an <code>IncludeMap</code>, then Firewall Manager applies the policy to all accounts except for those specified by the <code>ExcludeMap</code>.</p> <p>You can specify account IDs, OUs, or a combination: </p> <ul> <li> <p>Specify account IDs by setting the key to <code>ACCOUNT</code>. For example, the following is a valid map: <code>{“ACCOUNT” : [“accountID1”, “accountID2”]}</code>.</p> </li> <li> <p>Specify OUs by setting the key to <code>ORG_UNIT</code>. For example, the following is a valid map: <code>{“ORG_UNIT” : [“ouid111”, “ouid112”]}</code>.</p> </li> <li> <p>Specify accounts and OUs together in a single map, separated with a comma. For example, the following is a valid map: <code>{“ACCOUNT” : [“accountID1”, “accountID2”], “ORG_UNIT” : [“ouid111”, “ouid112”]}</code>.</p> </li> </ul>"}, "ResourceSetIds": {"shape": "ResourceSetIds", "documentation": "<p>The unique identifiers of the resource sets used by the policy.</p>"}, "PolicyDescription": {"shape": "ResourceDescription", "documentation": "<p>The definition of the Network Firewall firewall policy.</p>"}, "PolicyStatus": {"shape": "CustomerPolicyStatus", "documentation": "<p>Indicates whether the policy is in or out of an admin's policy or Region scope.</p> <ul> <li> <p> <code>ACTIVE</code> - The administrator can manage and delete the policy.</p> </li> <li> <p> <code>OUT_OF_ADMIN_SCOPE</code> - The administrator can view the policy, but they can't edit or delete the policy. Existing policy protections stay in place. Any new resources that come into scope of the policy won't be protected.</p> </li> </ul>"}}, "documentation": "<p>An Firewall Manager policy.</p>"}, "PolicyComplianceDetail": {"type": "structure", "members": {"PolicyOwner": {"shape": "AWSAccountId", "documentation": "<p>The Amazon Web Services account that created the Firewall Manager policy.</p>"}, "PolicyId": {"shape": "PolicyId", "documentation": "<p>The ID of the Firewall Manager policy.</p>"}, "MemberAccount": {"shape": "AWSAccountId", "documentation": "<p>The Amazon Web Services account ID.</p>"}, "Violators": {"shape": "ComplianceViolators", "documentation": "<p>An array of resources that aren't protected by the WAF or Shield Advanced policy or that aren't in compliance with the security group policy.</p>"}, "EvaluationLimitExceeded": {"shape": "Boolean", "documentation": "<p>Indicates if over 100 resources are noncompliant with the Firewall Manager policy.</p>"}, "ExpiredAt": {"shape": "TimeStamp", "documentation": "<p>A timestamp that indicates when the returned information should be considered out of date.</p>"}, "IssueInfoMap": {"shape": "IssueInfoMap", "documentation": "<p>Details about problems with dependent services, such as WAF or Config, and the error message received that indicates the problem with the service.</p>"}}, "documentation": "<p>Describes the noncompliant resources in a member account for a specific Firewall Manager policy. A maximum of 100 entries are displayed. If more than 100 resources are noncompliant, <code>EvaluationLimitExceeded</code> is set to <code>True</code>.</p>"}, "PolicyComplianceStatus": {"type": "structure", "members": {"PolicyOwner": {"shape": "AWSAccountId", "documentation": "<p>The Amazon Web Services account that created the Firewall Manager policy.</p>"}, "PolicyId": {"shape": "PolicyId", "documentation": "<p>The ID of the Firewall Manager policy.</p>"}, "PolicyName": {"shape": "ResourceName", "documentation": "<p>The name of the Firewall Manager policy.</p>"}, "MemberAccount": {"shape": "AWSAccountId", "documentation": "<p>The member account ID.</p>"}, "EvaluationResults": {"shape": "EvaluationResults", "documentation": "<p>An array of <code>EvaluationResult</code> objects.</p>"}, "LastUpdated": {"shape": "TimeStamp", "documentation": "<p>Timestamp of the last update to the <code>EvaluationResult</code> objects.</p>"}, "IssueInfoMap": {"shape": "IssueInfoMap", "documentation": "<p>Details about problems with dependent services, such as WAF or Config, and the error message received that indicates the problem with the service.</p>"}}, "documentation": "<p>Indicates whether the account is compliant with the specified policy. An account is considered noncompliant if it includes resources that are not protected by the policy, for WAF and Shield Advanced policies, or that are noncompliant with the policy, for security group policies.</p>"}, "PolicyComplianceStatusList": {"type": "list", "member": {"shape": "PolicyComplianceStatus"}}, "PolicyComplianceStatusType": {"type": "string", "enum": ["COMPLIANT", "NON_COMPLIANT"]}, "PolicyId": {"type": "string", "max": 36, "min": 36, "pattern": "^[a-z0-9A-Z-]{36}$"}, "PolicyOption": {"type": "structure", "members": {"NetworkFirewallPolicy": {"shape": "NetworkFirewallPolicy", "documentation": "<p>Defines the deployment model to use for the firewall policy.</p>"}, "ThirdPartyFirewallPolicy": {"shape": "ThirdPartyFirewallPolicy", "documentation": "<p>Defines the policy options for a third-party firewall policy.</p>"}}, "documentation": "<p>Contains the Network Firewall firewall policy options to configure the policy's deployment model and third-party firewall policy settings.</p>"}, "PolicySummary": {"type": "structure", "members": {"PolicyArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the specified policy.</p>"}, "PolicyId": {"shape": "PolicyId", "documentation": "<p>The ID of the specified policy.</p>"}, "PolicyName": {"shape": "ResourceName", "documentation": "<p>The name of the specified policy.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The type of resource protected by or in scope of the policy. This is in the format shown in the <a href=\"https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-template-resource-type-ref.html\">Amazon Web Services Resource Types Reference</a>. For WAF and Shield Advanced, examples include <code>AWS::ElasticLoadBalancingV2::LoadBalancer</code> and <code>AWS::CloudFront::Distribution</code>. For a security group common policy, valid values are <code>AWS::EC2::NetworkInterface</code> and <code>AWS::EC2::Instance</code>. For a security group content audit policy, valid values are <code>AWS::EC2::SecurityGroup</code>, <code>AWS::EC2::NetworkInterface</code>, and <code>AWS::EC2::Instance</code>. For a security group usage audit policy, the value is <code>AWS::EC2::SecurityGroup</code>. For an Network Firewall policy or DNS Firewall policy, the value is <code>AWS::EC2::VPC</code>.</p>"}, "SecurityServiceType": {"shape": "SecurityServiceType", "documentation": "<p>The service that the policy is using to protect the resources. This specifies the type of policy that is created, either an WAF policy, a Shield Advanced policy, or a security group policy.</p>"}, "RemediationEnabled": {"shape": "Boolean", "documentation": "<p>Indicates if the policy should be automatically applied to new resources.</p>"}, "DeleteUnusedFMManagedResources": {"shape": "Boolean", "documentation": "<p>Indicates whether Firewall Manager should automatically remove protections from resources that leave the policy scope and clean up resources that Firewall Manager is managing for accounts when those accounts leave policy scope. For example, Firewall Manager will disassociate a Firewall Manager managed web ACL from a protected customer resource when the customer resource leaves policy scope. </p> <p>By default, Firewall Manager doesn't remove protections or delete Firewall Manager managed resources. </p> <p>This option is not available for Shield Advanced or WAF Classic policies.</p>"}, "PolicyStatus": {"shape": "CustomerPolicyStatus", "documentation": "<p>Indicates whether the policy is in or out of an admin's policy or Region scope.</p> <ul> <li> <p> <code>ACTIVE</code> - The administrator can manage and delete the policy.</p> </li> <li> <p> <code>OUT_OF_ADMIN_SCOPE</code> - The administrator can view the policy, but they can't edit or delete the policy. Existing policy protections stay in place. Any new resources that come into scope of the policy won't be protected.</p> </li> </ul>"}}, "documentation": "<p>Details of the Firewall Manager policy. </p>"}, "PolicySummaryList": {"type": "list", "member": {"shape": "PolicySummary"}}, "PolicyTypeScope": {"type": "structure", "members": {"PolicyTypes": {"shape": "SecurityServiceTypeList", "documentation": "<p>The list of policy types that the specified Firewall Manager administrator can manage.</p>"}, "AllPolicyTypesEnabled": {"shape": "Boolean", "documentation": "<p>Allows the specified Firewall Manager administrator to manage all Firewall Manager policy types, except for third-party policy types. Third-party policy types can only be managed by the Firewall Manager default administrator.</p>"}}, "documentation": "<p>Defines the policy types that the specified Firewall Manager administrator can manage.</p>"}, "PolicyUpdateToken": {"type": "string", "max": 1024, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "PossibleRemediationAction": {"type": "structure", "required": ["OrderedRemediationActions"], "members": {"Description": {"shape": "LengthBoundedString", "documentation": "<p>A description of the list of remediation actions.</p>"}, "OrderedRemediationActions": {"shape": "OrderedRemediationActions", "documentation": "<p>The ordered list of remediation actions.</p>"}, "IsDefaultAction": {"shape": "Boolean", "documentation": "<p>Information about whether an action is taken by default.</p>"}}, "documentation": "<p>A list of remediation actions.</p>"}, "PossibleRemediationActionList": {"type": "list", "member": {"shape": "PossibleRemediationAction"}}, "PossibleRemediationActions": {"type": "structure", "members": {"Description": {"shape": "LengthBoundedString", "documentation": "<p>A description of the possible remediation actions list.</p>"}, "Actions": {"shape": "PossibleRemediationActionList", "documentation": "<p>Information about the actions.</p>"}}, "documentation": "<p>A list of possible remediation action lists. Each individual possible remediation action is a list of individual remediation actions.</p>"}, "PreviousAppsList": {"type": "map", "key": {"shape": "PreviousListVersion"}, "value": {"shape": "AppsList"}}, "PreviousListVersion": {"type": "string", "max": 2, "min": 1, "pattern": "^\\d{1,2}$"}, "PreviousProtocolsList": {"type": "map", "key": {"shape": "PreviousListVersion"}, "value": {"shape": "ProtocolsList"}}, "PriorityNumber": {"type": "integer"}, "ProtectionData": {"type": "string"}, "Protocol": {"type": "string", "max": 20, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "ProtocolsList": {"type": "list", "member": {"shape": "Protocol"}}, "ProtocolsListData": {"type": "structure", "required": ["ListName", "ProtocolsList"], "members": {"ListId": {"shape": "ListId", "documentation": "<p>The ID of the Firewall Manager protocols list.</p>"}, "ListName": {"shape": "ResourceName", "documentation": "<p>The name of the Firewall Manager protocols list.</p>"}, "ListUpdateToken": {"shape": "UpdateToken", "documentation": "<p>A unique identifier for each update to the list. When you update the list, the update token must match the token of the current version of the application list. You can retrieve the update token by getting the list. </p>"}, "CreateTime": {"shape": "TimeStamp", "documentation": "<p>The time that the Firewall Manager protocols list was created.</p>"}, "LastUpdateTime": {"shape": "TimeStamp", "documentation": "<p>The time that the Firewall Manager protocols list was last updated.</p>"}, "ProtocolsList": {"shape": "ProtocolsList", "documentation": "<p>An array of protocols in the Firewall Manager protocols list.</p>"}, "PreviousProtocolsList": {"shape": "PreviousProtocolsList", "documentation": "<p>A map of previous version numbers to their corresponding protocol arrays.</p>"}}, "documentation": "<p>An Firewall Manager protocols list.</p>"}, "ProtocolsListDataSummary": {"type": "structure", "members": {"ListArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the specified protocols list.</p>"}, "ListId": {"shape": "ListId", "documentation": "<p>The ID of the specified protocols list.</p>"}, "ListName": {"shape": "ResourceName", "documentation": "<p>The name of the specified protocols list.</p>"}, "ProtocolsList": {"shape": "ProtocolsList", "documentation": "<p>An array of protocols in the Firewall Manager protocols list.</p>"}}, "documentation": "<p>Details of the Firewall Manager protocols list.</p>"}, "ProtocolsListsData": {"type": "list", "member": {"shape": "ProtocolsListDataSummary"}}, "PutAdminAccountRequest": {"type": "structure", "required": ["AdminAccount"], "members": {"AdminAccount": {"shape": "AWSAccountId", "documentation": "<p>The Amazon Web Services account ID to add as an Firewall Manager administrator account. The account must be a member of the organization that was onboarded to Firewall Manager by <a>AssociateAdminAccount</a>. For more information about Organizations, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_accounts.html\">Managing the Amazon Web Services Accounts in Your Organization</a>.</p>"}, "AdminScope": {"shape": "AdminScope", "documentation": "<p>Configures the resources that the specified Firewall Manager administrator can manage. As a best practice, set the administrative scope according to the principles of least privilege. Only grant the administrator the specific resources or permissions that they need to perform the duties of their role.</p>"}}}, "PutAppsListRequest": {"type": "structure", "required": ["AppsList"], "members": {"AppsList": {"shape": "AppsListData", "documentation": "<p>The details of the Firewall Manager applications list to be created.</p>"}, "TagList": {"shape": "TagList", "documentation": "<p>The tags associated with the resource.</p>"}}}, "PutAppsListResponse": {"type": "structure", "members": {"AppsList": {"shape": "AppsListData", "documentation": "<p>The details of the Firewall Manager applications list.</p>"}, "AppsListArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the applications list.</p>"}}}, "PutNotificationChannelRequest": {"type": "structure", "required": ["SnsTopicArn", "SnsRoleName"], "members": {"SnsTopicArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the SNS topic that collects notifications from Firewall Manager.</p>"}, "SnsRoleName": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role that allows Amazon SNS to record Firewall Manager activity. </p>"}}}, "PutPolicyRequest": {"type": "structure", "required": ["Policy"], "members": {"Policy": {"shape": "Policy", "documentation": "<p>The details of the Firewall Manager policy to be created.</p>"}, "TagList": {"shape": "TagList", "documentation": "<p>The tags to add to the Amazon Web Services resource.</p>"}}}, "PutPolicyResponse": {"type": "structure", "members": {"Policy": {"shape": "Policy", "documentation": "<p>The details of the Firewall Manager policy.</p>"}, "PolicyArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the policy.</p>"}}}, "PutProtocolsListRequest": {"type": "structure", "required": ["ProtocolsList"], "members": {"ProtocolsList": {"shape": "ProtocolsListData", "documentation": "<p>The details of the Firewall Manager protocols list to be created.</p>"}, "TagList": {"shape": "TagList", "documentation": "<p>The tags associated with the resource.</p>"}}}, "PutProtocolsListResponse": {"type": "structure", "members": {"ProtocolsList": {"shape": "ProtocolsListData", "documentation": "<p>The details of the Firewall Manager protocols list.</p>"}, "ProtocolsListArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the protocols list.</p>"}}}, "PutResourceSetRequest": {"type": "structure", "required": ["ResourceSet"], "members": {"ResourceSet": {"shape": "ResourceSet", "documentation": "<p>Details about the resource set to be created or updated.&gt;</p>"}, "TagList": {"shape": "TagList", "documentation": "<p>Retrieves the tags associated with the specified resource set. Tags are key:value pairs that you can use to categorize and manage your resources, for purposes like billing. For example, you might set the tag key to \"customer\" and the value to the customer name or ID. You can specify one or more tags to add to each Amazon Web Services resource, up to 50 tags for a resource.</p>"}}}, "PutResourceSetResponse": {"type": "structure", "required": ["ResourceSet", "ResourceSetArn"], "members": {"ResourceSet": {"shape": "ResourceSet", "documentation": "<p>Details about the resource set.</p>"}, "ResourceSetArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource set.</p>"}}}, "ReferenceRule": {"type": "string"}, "RegionScope": {"type": "structure", "members": {"Regions": {"shape": "AWSRegionList", "documentation": "<p>The Amazon Web Services Regions that the specified Firewall Manager administrator can perform actions in.</p>"}, "AllRegionsEnabled": {"shape": "Boolean", "documentation": "<p>Allows the specified Firewall Manager administrator to manage all Amazon Web Services Regions.</p>"}}, "documentation": "<p>Defines the Amazon Web Services Regions that the specified Firewall Manager administrator can manage.</p>"}, "RemediationAction": {"type": "structure", "members": {"Description": {"shape": "LengthBoundedString", "documentation": "<p>A description of a remediation action.</p>"}, "EC2CreateRouteAction": {"shape": "EC2CreateRouteAction", "documentation": "<p>Information about the CreateRoute action in the Amazon EC2 API.</p>"}, "EC2ReplaceRouteAction": {"shape": "EC2ReplaceRouteAction", "documentation": "<p>Information about the ReplaceRoute action in the Amazon EC2 API.</p>"}, "EC2DeleteRouteAction": {"shape": "EC2DeleteRouteAction", "documentation": "<p>Information about the DeleteRoute action in the Amazon EC2 API.</p>"}, "EC2CopyRouteTableAction": {"shape": "EC2CopyRouteTableAction", "documentation": "<p>Information about the CopyRouteTable action in the Amazon EC2 API.</p>"}, "EC2ReplaceRouteTableAssociationAction": {"shape": "EC2ReplaceRouteTableAssociationAction", "documentation": "<p>Information about the ReplaceRouteTableAssociation action in the Amazon EC2 API.</p>"}, "EC2AssociateRouteTableAction": {"shape": "EC2AssociateRouteTableAction", "documentation": "<p>Information about the AssociateRouteTable action in the Amazon EC2 API.</p>"}, "EC2CreateRouteTableAction": {"shape": "EC2CreateRouteTableAction", "documentation": "<p>Information about the CreateRouteTable action in the Amazon EC2 API.</p>"}, "FMSPolicyUpdateFirewallCreationConfigAction": {"shape": "FMSPolicyUpdateFirewallCreationConfigAction", "documentation": "<p>The remedial action to take when updating a firewall configuration.</p>"}}, "documentation": "<p>Information about an individual action you can take to remediate a violation.</p>"}, "RemediationActionDescription": {"type": "string", "max": 1024, "min": 0, "pattern": ".*"}, "RemediationActionType": {"type": "string", "enum": ["REMOVE", "MODIFY"]}, "RemediationActionWithOrder": {"type": "structure", "members": {"RemediationAction": {"shape": "RemediationAction", "documentation": "<p>Information about an action you can take to remediate a violation.</p>"}, "Order": {"shape": "BasicInteger", "documentation": "<p>The order of the remediation actions in the list.</p>"}}, "documentation": "<p>An ordered list of actions you can take to remediate a violation.</p>"}, "Resource": {"type": "structure", "required": ["URI"], "members": {"URI": {"shape": "Identifier", "documentation": "<p>The resource's universal resource indicator (URI).</p>"}, "AccountId": {"shape": "AWSAccountId", "documentation": "<p>The Amazon Web Services account ID that the associated resource belongs to.</p>"}}, "documentation": "<p>Details of a resource that is associated to an Firewall Manager resource set.</p>"}, "ResourceArn": {"type": "string", "max": 1024, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "ResourceCount": {"type": "long", "min": 0}, "ResourceDescription": {"type": "string", "max": 256, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "ResourceId": {"type": "string", "max": 1024, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "ResourceIdList": {"type": "list", "member": {"shape": "ResourceId"}}, "ResourceList": {"type": "list", "member": {"shape": "Resource"}}, "ResourceName": {"type": "string", "max": 128, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The specified resource was not found.</p>", "exception": true}, "ResourceSet": {"type": "structure", "required": ["Name", "ResourceTypeList"], "members": {"Id": {"shape": "Base62Id", "documentation": "<p>A unique identifier for the resource set. This ID is returned in the responses to create and list commands. You provide it to operations like update and delete.</p>"}, "Name": {"shape": "Name", "documentation": "<p>The descriptive name of the resource set. You can't change the name of a resource set after you create it.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description of the resource set.</p>"}, "UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Firewall Manager returns a token to your requests that access the resource set. The token marks the state of the resource set resource at the time of the request. Update tokens are not allowed when creating a resource set. After creation, each subsequent update call to the resource set requires the update token. </p> <p>To make an unconditional change to the resource set, omit the token in your update request. Without the token, Firewall Manager performs your updates regardless of whether the resource set has changed since you last retrieved it.</p> <p>To make a conditional change to the resource set, provide the token in your update request. Firewall Manager uses the token to ensure that the resource set hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the resource set again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "ResourceTypeList": {"shape": "ResourceTypeList", "documentation": "<p>Determines the resources that can be associated to the resource set. Depending on your setting for max results and the number of resource sets, a single call might not return the full list.</p>"}, "LastUpdateTime": {"shape": "TimeStamp", "documentation": "<p>The last time that the resource set was changed.</p>"}, "ResourceSetStatus": {"shape": "ResourceSetStatus", "documentation": "<p>Indicates whether the resource set is in or out of an admin's Region scope.</p> <ul> <li> <p> <code>ACTIVE</code> - The administrator can manage and delete the resource set.</p> </li> <li> <p> <code>OUT_OF_ADMIN_SCOPE</code> - The administrator can view the resource set, but they can't edit or delete the resource set. Existing protections stay in place. Any new resource that come into scope of the resource set won't be protected.</p> </li> </ul>"}}, "documentation": "<p>A set of resources to include in a policy.</p>"}, "ResourceSetIds": {"type": "list", "member": {"shape": "Base62Id"}}, "ResourceSetStatus": {"type": "string", "enum": ["ACTIVE", "OUT_OF_ADMIN_SCOPE"]}, "ResourceSetSummary": {"type": "structure", "members": {"Id": {"shape": "Base62Id", "documentation": "<p>A unique identifier for the resource set. This ID is returned in the responses to create and list commands. You provide it to operations like update and delete.</p>"}, "Name": {"shape": "Name", "documentation": "<p>The descriptive name of the resource set. You can't change the name of a resource set after you create it.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description of the resource set.</p>"}, "LastUpdateTime": {"shape": "TimeStamp", "documentation": "<p>The last time that the resource set was changed.</p>"}, "ResourceSetStatus": {"shape": "ResourceSetStatus", "documentation": "<p>Indicates whether the resource set is in or out of an admin's Region scope.</p> <ul> <li> <p> <code>ACTIVE</code> - The administrator can manage and delete the resource set.</p> </li> <li> <p> <code>OUT_OF_ADMIN_SCOPE</code> - The administrator can view the resource set, but they can't edit or delete the resource set. Existing protections stay in place. Any new resource that come into scope of the resource set won't be protected.</p> </li> </ul>"}}, "documentation": "<p>Summarizes the resource sets used in a policy.</p>"}, "ResourceSetSummaryList": {"type": "list", "member": {"shape": "ResourceSetSummary"}}, "ResourceTag": {"type": "structure", "required": ["Key"], "members": {"Key": {"shape": "ResourceTagKey", "documentation": "<p>The resource tag key.</p>"}, "Value": {"shape": "ResourceTagValue", "documentation": "<p>The resource tag value.</p>"}}, "documentation": "<p>The resource tags that Firewall Manager uses to determine if a particular resource should be included or excluded from the Firewall Manager policy. Tags enable you to categorize your Amazon Web Services resources in different ways, for example, by purpose, owner, or environment. Each tag consists of a key and an optional value. Firewall Manager combines the tags with \"AND\" so that, if you add more than one tag to a policy scope, a resource must have all the specified tags to be included or excluded. For more information, see <a href=\"https://docs.aws.amazon.com/awsconsolehelpdocs/latest/gsg/tag-editor.html\">Working with Tag Editor</a>.</p>"}, "ResourceTagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "ResourceTagValue": {"type": "string", "max": 256, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "ResourceTags": {"type": "list", "member": {"shape": "ResourceTag"}, "max": 8, "min": 0}, "ResourceType": {"type": "string", "max": 128, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "ResourceTypeList": {"type": "list", "member": {"shape": "ResourceType"}}, "ResourceViolation": {"type": "structure", "members": {"AwsVPCSecurityGroupViolation": {"shape": "AwsVPCSecurityGroupViolation", "documentation": "<p>Violation detail for security groups.</p>"}, "AwsEc2NetworkInterfaceViolation": {"shape": "AwsEc2NetworkInterfaceViolation", "documentation": "<p>Violation detail for a network interface.</p>"}, "AwsEc2InstanceViolation": {"shape": "AwsEc2InstanceViolation", "documentation": "<p>Violation detail for an EC2 instance.</p>"}, "NetworkFirewallMissingFirewallViolation": {"shape": "NetworkFirewallMissingFirewallViolation", "documentation": "<p>Violation detail for an Network Firewall policy that indicates that a subnet has no Firewall Manager managed firewall in its VPC. </p>"}, "NetworkFirewallMissingSubnetViolation": {"shape": "NetworkFirewallMissingSubnetViolation", "documentation": "<p>Violation detail for an Network Firewall policy that indicates that an Availability Zone is missing the expected Firewall Manager managed subnet.</p>"}, "NetworkFirewallMissingExpectedRTViolation": {"shape": "NetworkFirewallMissingExpectedRTViolation", "documentation": "<p>Violation detail for an Network Firewall policy that indicates that a subnet is not associated with the expected Firewall Manager managed route table. </p>"}, "NetworkFirewallPolicyModifiedViolation": {"shape": "NetworkFirewallPolicyModifiedViolation", "documentation": "<p>Violation detail for an Network Firewall policy that indicates that a firewall policy in an individual account has been modified in a way that makes it noncompliant. For example, the individual account owner might have deleted a rule group, changed the priority of a stateless rule group, or changed a policy default action.</p>"}, "NetworkFirewallInternetTrafficNotInspectedViolation": {"shape": "NetworkFirewallInternetTrafficNotInspectedViolation", "documentation": "<p>Violation detail for the subnet for which internet traffic hasn't been inspected.</p>"}, "NetworkFirewallInvalidRouteConfigurationViolation": {"shape": "NetworkFirewallInvalidRouteConfigurationViolation", "documentation": "<p>The route configuration is invalid.</p>"}, "NetworkFirewallBlackHoleRouteDetectedViolation": {"shape": "NetworkFirewallBlackHoleRouteDetectedViolation"}, "NetworkFirewallUnexpectedFirewallRoutesViolation": {"shape": "NetworkFirewallUnexpectedFirewallRoutesViolation", "documentation": "<p>There's an unexpected firewall route.</p>"}, "NetworkFirewallUnexpectedGatewayRoutesViolation": {"shape": "NetworkFirewallUnexpectedGatewayRoutesViolation", "documentation": "<p>There's an unexpected gateway route.</p>"}, "NetworkFirewallMissingExpectedRoutesViolation": {"shape": "NetworkFirewallMissingExpectedRoutesViolation", "documentation": "<p>Expected routes are missing from Network Firewall.</p>"}, "DnsRuleGroupPriorityConflictViolation": {"shape": "DnsRuleGroupPriorityConflictViolation", "documentation": "<p>Violation detail for a DNS Firewall policy that indicates that a rule group that Firewall Manager tried to associate with a VPC has the same priority as a rule group that's already associated. </p>"}, "DnsDuplicateRuleGroupViolation": {"shape": "DnsDuplicateRuleGroupViolation", "documentation": "<p>Violation detail for a DNS Firewall policy that indicates that a rule group that Firewall Manager tried to associate with a VPC is already associated with the VPC and can't be associated again. </p>"}, "DnsRuleGroupLimitExceededViolation": {"shape": "DnsRuleGroupLimitExceededViolation", "documentation": "<p>Violation detail for a DNS Firewall policy that indicates that the VPC reached the limit for associated DNS Firewall rule groups. Firewall Manager tried to associate another rule group with the VPC and failed. </p>"}, "PossibleRemediationActions": {"shape": "PossibleRemediationActions", "documentation": "<p>A list of possible remediation action lists. Each individual possible remediation action is a list of individual remediation actions.</p>"}, "FirewallSubnetIsOutOfScopeViolation": {"shape": "FirewallSubnetIsOutOfScopeViolation", "documentation": "<p>Contains details about the firewall subnet that violates the policy scope.</p>"}, "RouteHasOutOfScopeEndpointViolation": {"shape": "RouteHasOutOfScopeEndpointViolation", "documentation": "<p>Contains details about the route endpoint that violates the policy scope.</p>"}, "ThirdPartyFirewallMissingFirewallViolation": {"shape": "ThirdPartyFirewallMissingFirewallViolation", "documentation": "<p>The violation details for a third-party firewall that's been deleted.</p>"}, "ThirdPartyFirewallMissingSubnetViolation": {"shape": "ThirdPartyFirewallMissingSubnetViolation", "documentation": "<p>The violation details for a third-party firewall's subnet that's been deleted.</p>"}, "ThirdPartyFirewallMissingExpectedRouteTableViolation": {"shape": "ThirdPartyFirewallMissingExpectedRouteTableViolation", "documentation": "<p>The violation details for a third-party firewall that has the Firewall Manager managed route table that was associated with the third-party firewall has been deleted.</p>"}, "FirewallSubnetMissingVPCEndpointViolation": {"shape": "FirewallSubnetMissingVPCEndpointViolation", "documentation": "<p>The violation details for a third-party firewall's VPC endpoint subnet that was deleted.</p>"}}, "documentation": "<p>Violation detail based on resource type.</p>"}, "ResourceViolations": {"type": "list", "member": {"shape": "ResourceViolation"}}, "Route": {"type": "structure", "members": {"DestinationType": {"shape": "DestinationType", "documentation": "<p>The type of destination for the route.</p>"}, "TargetType": {"shape": "TargetType", "documentation": "<p>The type of target for the route.</p>"}, "Destination": {"shape": "LengthBoundedString", "documentation": "<p>The destination of the route.</p>"}, "Target": {"shape": "LengthBoundedString", "documentation": "<p>The route's target.</p>"}}, "documentation": "<p>Describes a route in a route table.</p>"}, "RouteHasOutOfScopeEndpointViolation": {"type": "structure", "members": {"SubnetId": {"shape": "ResourceId", "documentation": "<p>The ID of the subnet associated with the route that violates the policy scope.</p>"}, "VpcId": {"shape": "ResourceId", "documentation": "<p>The VPC ID of the route that violates the policy scope.</p>"}, "RouteTableId": {"shape": "ResourceId", "documentation": "<p>The ID of the route table.</p>"}, "ViolatingRoutes": {"shape": "Routes", "documentation": "<p>The list of routes that violate the route table.</p>"}, "SubnetAvailabilityZone": {"shape": "LengthBoundedString", "documentation": "<p>The subnet's Availability Zone.</p>"}, "SubnetAvailabilityZoneId": {"shape": "LengthBoundedString", "documentation": "<p>The ID of the subnet's Availability Zone.</p>"}, "CurrentFirewallSubnetRouteTable": {"shape": "ResourceId", "documentation": "<p>The route table associated with the current firewall subnet.</p>"}, "FirewallSubnetId": {"shape": "ResourceId", "documentation": "<p>The ID of the firewall subnet.</p>"}, "FirewallSubnetRoutes": {"shape": "Routes", "documentation": "<p>The list of firewall subnet routes.</p>"}, "InternetGatewayId": {"shape": "ResourceId", "documentation": "<p>The ID of the Internet Gateway.</p>"}, "CurrentInternetGatewayRouteTable": {"shape": "ResourceId", "documentation": "<p>The current route table associated with the Internet Gateway.</p>"}, "InternetGatewayRoutes": {"shape": "Routes", "documentation": "<p>The routes in the route table associated with the Internet Gateway.</p>"}}, "documentation": "<p>Contains details about the route endpoint that violates the policy scope.</p>"}, "Routes": {"type": "list", "member": {"shape": "Route"}}, "RuleOrder": {"type": "string", "enum": ["STRICT_ORDER", "DEFAULT_ACTION_ORDER"]}, "SecurityGroupRemediationAction": {"type": "structure", "members": {"RemediationActionType": {"shape": "RemediationActionType", "documentation": "<p>The remediation action that will be performed.</p>"}, "Description": {"shape": "RemediationActionDescription", "documentation": "<p>Brief description of the action that will be performed.</p>"}, "RemediationResult": {"shape": "SecurityGroupRuleDescription", "documentation": "<p>The final state of the rule specified in the <code>ViolationTarget</code> after it is remediated.</p>"}, "IsDefaultAction": {"shape": "Boolean", "documentation": "<p>Indicates if the current action is the default action.</p>"}}, "documentation": "<p>Remediation option for the rule specified in the <code>ViolationTarget</code>.</p>"}, "SecurityGroupRemediationActions": {"type": "list", "member": {"shape": "SecurityGroupRemediationAction"}}, "SecurityGroupRuleDescription": {"type": "structure", "members": {"IPV4Range": {"shape": "CIDR", "documentation": "<p>The IPv4 ranges for the security group rule.</p>"}, "IPV6Range": {"shape": "CIDR", "documentation": "<p>The IPv6 ranges for the security group rule.</p>"}, "PrefixListId": {"shape": "ResourceId", "documentation": "<p>The ID of the prefix list for the security group rule.</p>"}, "Protocol": {"shape": "LengthBoundedString", "documentation": "<p>The IP protocol name (<code>tcp</code>, <code>udp</code>, <code>icmp</code>, <code>icmpv6</code>) or number.</p>"}, "FromPort": {"shape": "IPPortNumber", "documentation": "<p>The start of the port range for the TCP and UDP protocols, or an ICMP/ICMPv6 type number. A value of <code>-1</code> indicates all ICMP/ICMPv6 types.</p>"}, "ToPort": {"shape": "IPPortNumber", "documentation": "<p>The end of the port range for the TCP and UDP protocols, or an ICMP/ICMPv6 code. A value of <code>-1</code> indicates all ICMP/ICMPv6 codes.</p>"}}, "documentation": "<p>Describes a set of permissions for a security group rule.</p>"}, "SecurityServicePolicyData": {"type": "structure", "required": ["Type"], "members": {"Type": {"shape": "SecurityServiceType", "documentation": "<p>The service that the policy is using to protect the resources. This specifies the type of policy that is created, either an WAF policy, a Shield Advanced policy, or a security group policy. For security group policies, Firewall Manager supports one security group for each common policy and for each content audit policy. This is an adjustable limit that you can increase by contacting Amazon Web Services Support.</p>"}, "ManagedServiceData": {"shape": "ManagedServiceData", "documentation": "<p>Details about the service that are specific to the service type, in JSON format. </p> <ul> <li> <p>Example: <code>DNS_FIREWALL</code> </p> <p> <code>\"{\\\"type\\\":\\\"DNS_FIREWALL\\\",\\\"preProcessRuleGroups\\\":[{\\\"ruleGroupId\\\":\\\"rslvr-frg-1\\\",\\\"priority\\\":10}],\\\"postProcessRuleGroups\\\":[{\\\"ruleGroupId\\\":\\\"rslvr-frg-2\\\",\\\"priority\\\":9911}]}\"</code> </p> <note> <p>Valid values for <code>preProcessRuleGroups</code> are between 1 and 99. Valid values for <code>postProcessRuleGroups</code> are between 9901 and 10000.</p> </note> </li> <li> <p>Example: <code>IMPORT_NETWORK_FIREWALL</code> </p> <p> <code>\"{\\\"type\\\":\\\"IMPORT_NETWORK_FIREWALL\\\",\\\"awsNetworkFirewallConfig\\\":{\\\"networkFirewallStatelessRuleGroupReferences\\\":[{\\\"resourceARN\\\":\\\"arn:aws:network-firewall:us-west-2:000000000000:stateless-rulegroup\\/rg1\\\",\\\"priority\\\":1}],\\\"networkFirewallStatelessDefaultActions\\\":[\\\"aws:drop\\\"],\\\"networkFirewallStatelessFragmentDefaultActions\\\":[\\\"aws:pass\\\"],\\\"networkFirewallStatelessCustomActions\\\":[],\\\"networkFirewallStatefulRuleGroupReferences\\\":[{\\\"resourceARN\\\":\\\"arn:aws:network-firewall:us-west-2:aws-managed:stateful-rulegroup\\/ThreatSignaturesEmergingEventsStrictOrder\\\",\\\"priority\\\":8}],\\\"networkFirewallStatefulEngineOptions\\\":{\\\"ruleOrder\\\":\\\"STRICT_ORDER\\\"},\\\"networkFirewallStatefulDefaultActions\\\":[\\\"aws:drop_strict\\\"]}}\"</code> </p> <p> <code>\"{\\\"type\\\":\\\"DNS_FIREWALL\\\",\\\"preProcessRuleGroups\\\":[{\\\"ruleGroupId\\\":\\\"rslvr-frg-1\\\",\\\"priority\\\":10}],\\\"postProcessRuleGroups\\\":[{\\\"ruleGroupId\\\":\\\"rslvr-frg-2\\\",\\\"priority\\\":9911}]}\"</code> </p> <note> <p>Valid values for <code>preProcessRuleGroups</code> are between 1 and 99. Valid values for <code>postProcessRuleGroups</code> are between 9901 and 10000.</p> </note> </li> <li> <p>Example: <code>NETWORK_FIREWALL</code> - Centralized deployment model</p> <p> <code>\"{\\\"type\\\":\\\"NETWORK_FIREWALL\\\",\\\"awsNetworkFirewallConfig\\\":{\\\"networkFirewallStatelessRuleGroupReferences\\\":[{\\\"resourceARN\\\":\\\"arn:aws:network-firewall:us-east-1:************:stateless-rulegroup/test\\\",\\\"priority\\\":1}],\\\"networkFirewallStatelessDefaultActions\\\":[\\\"aws:forward_to_sfe\\\",\\\"customActionName\\\"],\\\"networkFirewallStatelessFragmentDefaultActions\\\":[\\\"aws:forward_to_sfe\\\",\\\"customActionName\\\"],\\\"networkFirewallStatelessCustomActions\\\":[{\\\"actionName\\\":\\\"customActionName\\\",\\\"actionDefinition\\\":{\\\"publishMetricAction\\\":{\\\"dimensions\\\":[{\\\"value\\\":\\\"metricdimensionvalue\\\"}]}}}],\\\"networkFirewallStatefulRuleGroupReferences\\\":[{\\\"resourceARN\\\":\\\"arn:aws:network-firewall:us-east-1:************:stateful-rulegroup/test\\\"}],\\\"networkFirewallLoggingConfiguration\\\":{\\\"logDestinationConfigs\\\":[{\\\"logDestinationType\\\":\\\"S3\\\",\\\"logType\\\":\\\"ALERT\\\",\\\"logDestination\\\":{\\\"bucketName\\\":\\\"s3-bucket-name\\\"}},{\\\"logDestinationType\\\":\\\"S3\\\",\\\"logType\\\":\\\"FLOW\\\",\\\"logDestination\\\":{\\\"bucketName\\\":\\\"s3-bucket-name\\\"}}],\\\"overrideExistingConfig\\\":true}},\\\"firewallDeploymentModel\\\":{\\\"centralizedFirewallDeploymentModel\\\":{\\\"centralizedFirewallOrchestrationConfig\\\":{\\\"inspectionVpcIds\\\":[{\\\"resourceId\\\":\\\"vpc-1234\\\",\\\"accountId\\\":\\\"************\\\"}],\\\"firewallCreationConfig\\\":{\\\"endpointLocation\\\":{\\\"availabilityZoneConfigList\\\":[{\\\"availabilityZoneId\\\":null,\\\"availabilityZoneName\\\":\\\"us-east-1a\\\",\\\"allowedIPV4CidrList\\\":[\\\"10.0.0.0/28\\\"]}]}},\\\"allowedIPV4CidrList\\\":[]}}}}\"</code> </p> <p> To use the centralized deployment model, you must set <a href=\"https://docs.aws.amazon.com/fms/2018-01-01/APIReference/API_PolicyOption.html\">PolicyOption</a> to <code>CENTRALIZED</code>. </p> </li> <li> <p>Example: <code>NETWORK_FIREWALL</code> - Distributed deployment model with automatic Availability Zone configuration</p> <p> <code> \"{\\\"type\\\":\\\"NETWORK_FIREWALL\\\",\\\"networkFirewallStatelessRuleGroupReferences\\\":[{\\\"resourceARN\\\":\\\"arn:aws:network-firewall:us-east-1:************:stateless-rulegroup/test\\\",\\\"priority\\\":1}],\\\"networkFirewallStatelessDefaultActions\\\":[\\\"aws:forward_to_sfe\\\",\\\"customActionName\\\"],\\\"networkFirewallStatelessFragmentDefaultActions\\\":[\\\"aws:forward_to_sfe\\\",\\\"customActionName\\\"],\\\"networkFirewallStatelessCustomActions\\\":[{\\\"actionName\\\":\\\"customActionName\\\",\\\"actionDefinition\\\":{\\\"publishMetricAction\\\":{\\\"dimensions\\\":[{\\\"value\\\":\\\"metricdimensionvalue\\\"}]}}}],\\\"networkFirewallStatefulRuleGroupReferences\\\":[{\\\"resourceARN\\\":\\\"arn:aws:network-firewall:us-east-1:************:stateful-rulegroup/test\\\"}],\\\"networkFirewallOrchestrationConfig\\\":{\\\"singleFirewallEndpointPerVPC\\\":false,\\\"allowedIPV4CidrList\\\":[\\\"10.0.0.0/28\\\",\\\"***********/28\\\"],\\\"routeManagementAction\\\":\\\"OFF\\\"},\\\"networkFirewallLoggingConfiguration\\\":{\\\"logDestinationConfigs\\\":[{\\\"logDestinationType\\\":\\\"S3\\\",\\\"logType\\\":\\\"ALERT\\\",\\\"logDestination\\\":{\\\"bucketName\\\":\\\"s3-bucket-name\\\"}},{\\\"logDestinationType\\\":\\\"S3\\\",\\\"logType\\\":\\\"FLOW\\\",\\\"logDestination\\\":{\\\"bucketName\\\":\\\"s3-bucket-name\\\"}}],\\\"overrideExistingConfig\\\":true}}\" </code> </p> <p> With automatic Availbility Zone configuration, Firewall Manager chooses which Availability Zones to create the endpoints in. To use the distributed deployment model, you must set <a href=\"https://docs.aws.amazon.com/fms/2018-01-01/APIReference/API_PolicyOption.html\">PolicyOption</a> to <code>NULL</code>. </p> </li> <li> <p>Example: <code>NETWORK_FIREWALL</code> - Distributed deployment model with automatic Availability Zone configuration and route management</p> <p> <code> \"{\\\"type\\\":\\\"NETWORK_FIREWALL\\\",\\\"networkFirewallStatelessRuleGroupReferences\\\":[{\\\"resourceARN\\\":\\\"arn:aws:network-firewall:us-east-1:************:stateless-rulegroup/test\\\",\\\"priority\\\":1}],\\\"networkFirewallStatelessDefaultActions\\\":[\\\"aws:forward_to_sfe\\\",\\\"customActionName\\\"],\\\"networkFirewallStatelessFragmentDefaultActions\\\":[\\\"aws:forward_to_sfe\\\",\\\"customActionName\\\"],\\\"networkFirewallStatelessCustomActions\\\":[{\\\"actionName\\\":\\\"customActionName\\\",\\\"actionDefinition\\\":{\\\"publishMetricAction\\\":{\\\"dimensions\\\":[{\\\"value\\\":\\\"metricdimensionvalue\\\"}]}}}],\\\"networkFirewallStatefulRuleGroupReferences\\\":[{\\\"resourceARN\\\":\\\"arn:aws:network-firewall:us-east-1:************:stateful-rulegroup/test\\\"}],\\\"networkFirewallOrchestrationConfig\\\":{\\\"singleFirewallEndpointPerVPC\\\":false,\\\"allowedIPV4CidrList\\\":[\\\"10.0.0.0/28\\\",\\\"***********/28\\\"],\\\"routeManagementAction\\\":\\\"MONITOR\\\",\\\"routeManagementTargetTypes\\\":[\\\"InternetGateway\\\"]},\\\"networkFirewallLoggingConfiguration\\\":{\\\"logDestinationConfigs\\\":[{\\\"logDestinationType\\\":\\\"S3\\\",\\\"logType\\\":\\\"ALERT\\\",\\\"logDestination\\\":{\\\"bucketName\\\":\\\"s3-bucket-name\\\"}},{\\\"logDestinationType\\\":\\\"S3\\\",\\\"logType\\\": \\\"FLOW\\\",\\\"logDestination\\\":{\\\"bucketName\\\":\\\"s3-bucket-name\\\"}}],\\\"overrideExistingConfig\\\":true}}\" </code> </p> <p>To use the distributed deployment model, you must set <a href=\"https://docs.aws.amazon.com/fms/2018-01-01/APIReference/API_PolicyOption.html\">PolicyOption</a> to <code>NULL</code>. </p> </li> <li> <p>Example: <code>NETWORK_FIREWALL</code> - Distributed deployment model with custom Availability Zone configuration</p> <p> <code>\"{\\\"type\\\":\\\"NETWORK_FIREWALL\\\",\\\"networkFirewallStatelessRuleGroupReferences\\\":[{\\\"resourceARN\\\":\\\"arn:aws:network-firewall:us-east-1:************:stateless-rulegroup/test\\\",\\\"priority\\\":1}],\\\"networkFirewallStatelessDefaultActions\\\":[\\\"aws:forward_to_sfe\\\",\\\"customActionName\\\"],\\\"networkFirewallStatelessFragmentDefaultActions\\\":[\\\"aws:forward_to_sfe\\\",\\\"fragmentcustomactionname\\\"],\\\"networkFirewallStatelessCustomActions\\\":[{\\\"actionName\\\":\\\"customActionName\\\", \\\"actionDefinition\\\":{\\\"publishMetricAction\\\":{\\\"dimensions\\\":[{\\\"value\\\":\\\"metricdimensionvalue\\\"}]}}},{\\\"actionName\\\":\\\"fragmentcustomactionname\\\",\\\"actionDefinition\\\":{\\\"publishMetricAction\\\":{\\\"dimensions\\\":[{\\\"value\\\":\\\"fragmentmetricdimensionvalue\\\"}]}}}],\\\"networkFirewallStatefulRuleGroupReferences\\\":[{\\\"resourceARN\\\":\\\"arn:aws:network-firewall:us-east-1:************:stateful-rulegroup/test\\\"}],\\\"networkFirewallOrchestrationConfig\\\":{\\\"firewallCreationConfig\\\":{ \\\"endpointLocation\\\":{\\\"availabilityZoneConfigList\\\":[{\\\"availabilityZoneName\\\":\\\"us-east-1a\\\",\\\"allowedIPV4CidrList\\\":[\\\"10.0.0.0/28\\\"]},{\\\"availabilityZoneName\\\":\\\"us-east-1b\\\",\\\"allowedIPV4CidrList\\\":[ \\\"10.0.0.0/28\\\"]}]} },\\\"singleFirewallEndpointPerVPC\\\":false,\\\"allowedIPV4CidrList\\\":null,\\\"routeManagementAction\\\":\\\"OFF\\\",\\\"networkFirewallLoggingConfiguration\\\":{\\\"logDestinationConfigs\\\":[{\\\"logDestinationType\\\":\\\"S3\\\",\\\"logType\\\":\\\"ALERT\\\",\\\"logDestination\\\":{\\\"bucketName\\\":\\\"s3-bucket-name\\\"}},{\\\"logDestinationType\\\":\\\"S3\\\",\\\"logType\\\":\\\"FLOW\\\",\\\"logDestination\\\":{\\\"bucketName\\\":\\\"s3-bucket-name\\\"}}],\\\"overrideExistingConfig\\\":boolean}}\" </code> </p> <p> With custom Availability Zone configuration, you define which specific Availability Zones to create endpoints in by configuring <code>firewallCreationConfig</code>. To configure the Availability Zones in <code>firewallCreationConfig</code>, specify either the <code>availabilityZoneName</code> or <code>availabilityZoneId</code> parameter, not both parameters. </p> <p>To use the distributed deployment model, you must set <a href=\"https://docs.aws.amazon.com/fms/2018-01-01/APIReference/API_PolicyOption.html\">PolicyOption</a> to <code>NULL</code>. </p> </li> <li> <p>Example: <code>NETWORK_FIREWALL</code> - Distributed deployment model with custom Availability Zone configuration and route management</p> <p> <code>\"{\\\"type\\\":\\\"NETWORK_FIREWALL\\\",\\\"networkFirewallStatelessRuleGroupReferences\\\":[{\\\"resourceARN\\\":\\\"arn:aws:network-firewall:us-east-1:************:stateless-rulegroup/test\\\",\\\"priority\\\":1}],\\\"networkFirewallStatelessDefaultActions\\\":[\\\"aws:forward_to_sfe\\\",\\\"customActionName\\\"],\\\"networkFirewallStatelessFragmentDefaultActions\\\":[\\\"aws:forward_to_sfe\\\",\\\"fragmentcustomactionname\\\"],\\\"networkFirewallStatelessCustomActions\\\":[{\\\"actionName\\\":\\\"customActionName\\\",\\\"actionDefinition\\\":{\\\"publishMetricAction\\\":{\\\"dimensions\\\":[{\\\"value\\\":\\\"metricdimensionvalue\\\"}]}}},{\\\"actionName\\\":\\\"fragmentcustomactionname\\\",\\\"actionDefinition\\\":{\\\"publishMetricAction\\\":{\\\"dimensions\\\":[{\\\"value\\\":\\\"fragmentmetricdimensionvalue\\\"}]}}}],\\\"networkFirewallStatefulRuleGroupReferences\\\":[{\\\"resourceARN\\\":\\\"arn:aws:network-firewall:us-east-1:************:stateful-rulegroup/test\\\"}],\\\"networkFirewallOrchestrationConfig\\\":{\\\"firewallCreationConfig\\\":{\\\"endpointLocation\\\":{\\\"availabilityZoneConfigList\\\":[{\\\"availabilityZoneName\\\":\\\"us-east-1a\\\",\\\"allowedIPV4CidrList\\\":[\\\"10.0.0.0/28\\\"]},{\\\"availabilityZoneName\\\":\\\"us-east-1b\\\",\\\"allowedIPV4CidrList\\\":[\\\"10.0.0.0/28\\\"]}]}},\\\"singleFirewallEndpointPerVPC\\\":false,\\\"allowedIPV4CidrList\\\":null,\\\"routeManagementAction\\\":\\\"MONITOR\\\",\\\"routeManagementTargetTypes\\\":[\\\"InternetGateway\\\"],\\\"routeManagementConfig\\\":{\\\"allowCrossAZTrafficIfNoEndpoint\\\":true}},\\\"networkFirewallLoggingConfiguration\\\":{\\\"logDestinationConfigs\\\":[{\\\"logDestinationType\\\":\\\"S3\\\",\\\"logType\\\":\\\"ALERT\\\",\\\"logDestination\\\":{\\\"bucketName\\\":\\\"s3-bucket-name\\\"}},{\\\"logDestinationType\\\":\\\"S3\\\",\\\"logType\\\":\\\"FLOW\\\",\\\"logDestination\\\":{\\\"bucketName\\\":\\\"s3-bucket-name\\\"}}],\\\"overrideExistingConfig\\\":boolean}}\" </code> </p> <p>To use the distributed deployment model, you must set <a href=\"https://docs.aws.amazon.com/fms/2018-01-01/APIReference/API_PolicyOption.html\">PolicyOption</a> to <code>NULL</code>. </p> </li> <li> <p>Example: <code>SECURITY_GROUPS_COMMON</code> </p> <p> <code>\"{\\\"type\\\":\\\"SECURITY_GROUPS_COMMON\\\",\\\"revertManualSecurityGroupChanges\\\":false,\\\"exclusiveResourceSecurityGroupManagement\\\":false, \\\"applyToAllEC2InstanceENIs\\\":false,\\\"securityGroups\\\":[{\\\"id\\\":\\\" sg-000e55995d61a06bd\\\"}]}\"</code> </p> </li> <li> <p>Example: <code>SECURITY_GROUPS_COMMON</code> - Security group tag distribution </p> <p> <code>\"\"{\\\"type\\\":\\\"SECURITY_GROUPS_COMMON\\\",\\\"securityGroups\\\":[{\\\"id\\\":\\\"sg-000e55995d61a06bd\\\"}],\\\"revertManualSecurityGroupChanges\\\":true,\\\"exclusiveResourceSecurityGroupManagement\\\":false,\\\"applyToAllEC2InstanceENIs\\\":false,\\\"includeSharedVPC\\\":false,\\\"enableTagDistribution\\\":true}\"\"</code> </p> <p> Firewall Manager automatically distributes tags from the primary group to the security groups created by this policy. To use security group tag distribution, you must also set <code>revertManualSecurityGroupChanges</code> to <code>true</code>, otherwise Firewall Manager won't be able to create the policy. When you enable <code>revertManualSecurityGroupChanges</code>, Firewall Manager identifies and reports when the security groups created by this policy become non-compliant. </p> <p> Firewall Manager won't distrubute system tags added by Amazon Web Services services into the replica security groups. System tags begin with the <code>aws:</code> prefix. </p> </li> <li> <p>Example: Shared VPCs. Apply the preceding policy to resources in shared VPCs as well as to those in VPCs that the account owns </p> <p> <code>\"{\\\"type\\\":\\\"SECURITY_GROUPS_COMMON\\\",\\\"revertManualSecurityGroupChanges\\\":false,\\\"exclusiveResourceSecurityGroupManagement\\\":false, \\\"applyToAllEC2InstanceENIs\\\":false,\\\"includeSharedVPC\\\":true,\\\"securityGroups\\\":[{\\\"id\\\":\\\" sg-000e55995d61a06bd\\\"}]}\"</code> </p> </li> <li> <p>Example: <code>SECURITY_GROUPS_CONTENT_AUDIT</code> </p> <p> <code>\"{\\\"type\\\":\\\"SECURITY_GROUPS_CONTENT_AUDIT\\\",\\\"securityGroups\\\":[{\\\"id\\\":\\\"sg-000e55995d61a06bd\\\"}],\\\"securityGroupAction\\\":{\\\"type\\\":\\\"ALLOW\\\"}}\"</code> </p> <p>The security group action for content audit can be <code>ALLOW</code> or <code>DENY</code>. For <code>ALLOW</code>, all in-scope security group rules must be within the allowed range of the policy's security group rules. For <code>DENY</code>, all in-scope security group rules must not contain a value or a range that matches a rule value or range in the policy security group.</p> </li> <li> <p>Example: <code>SECURITY_GROUPS_USAGE_AUDIT</code> </p> <p> <code>\"{\\\"type\\\":\\\"SECURITY_GROUPS_USAGE_AUDIT\\\",\\\"deleteUnusedSecurityGroups\\\":true,\\\"coalesceRedundantSecurityGroups\\\":true}\"</code> </p> </li> <li> <p>Example: <code>SHIELD_ADVANCED</code> with web ACL management</p> <p> <code>\"{\\\"type\\\":\\\"SHIELD_ADVANCED\\\",\\\"optimizeUnassociatedWebACL\\\":true}\"</code> </p> <p>If you set <code>optimizeUnassociatedWebACL</code> to <code>true</code>, Firewall Manager creates web ACLs in accounts within the policy scope if the web ACLs will be used by at least one resource. Firewall Manager creates web ACLs in the accounts within policy scope only if the web ACLs will be used by at least one resource. If at any time an account comes into policy scope, Firewall Manager automatically creates a web ACL in the account if at least one resource will use the web ACL.</p> <p>Upon enablement, Firewall Manager performs a one-time cleanup of unused web ACLs in your account. The cleanup process can take several hours. If a resource leaves policy scope after Firewall Manager creates a web ACL, Firewall Manager doesn't disassociate the resource from the web ACL. If you want Firewall Manager to clean up the web ACL, you must first manually disassociate the resources from the web ACL, and then enable the manage unused web ACLs option in your policy.</p> <p>If you set <code>optimizeUnassociatedWebACL</code> to <code>false</code>, and Firewall Manager automatically creates an empty web ACL in each account that's within policy scope.</p> </li> <li> <p>Specification for <code>SHIELD_ADVANCED</code> for Amazon CloudFront distributions </p> <p> <code>\"{\\\"type\\\":\\\"SHIELD_ADVANCED\\\",\\\"automaticResponseConfiguration\\\": {\\\"automaticResponseStatus\\\":\\\"ENABLED|IGNORED|DISABLED\\\", \\\"automaticResponseAction\\\":\\\"BLOCK|COUNT\\\"}, \\\"overrideCustomerWebaclClassic\\\":true|false, \\\"optimizeUnassociatedWebACL\\\":true|false}\"</code> </p> <p>For example: <code>\"{\\\"type\\\":\\\"SHIELD_ADVANCED\\\",\\\"automaticResponseConfiguration\\\": {\\\"automaticResponseStatus\\\":\\\"ENABLED\\\", \\\"automaticResponseAction\\\":\\\"COUNT\\\"}}\"</code> </p> <p>The default value for <code>automaticResponseStatus</code> is <code>IGNORED</code>. The value for <code>automaticResponseAction</code> is only required when <code>automaticResponseStatus</code> is set to <code>ENABLED</code>. The default value for <code>overrideCustomerWebaclClassic</code> is <code>false</code>.</p> <p>For other resource types that you can protect with a Shield Advanced policy, this <code>ManagedServiceData</code> configuration is an empty string.</p> </li> <li> <p>Example: <code>THIRD_PARTY_FIREWALL</code> </p> <p>Replace <code>THIRD_PARTY_FIREWALL_NAME</code> with the name of the third-party firewall.</p> <p> <code>\"{ \"type\":\"THIRD_PARTY_FIREWALL\", \"thirdPartyFirewall\":\"THIRD_PARTY_FIREWALL_NAME\", \"thirdPartyFirewallConfig\":{ \"thirdPartyFirewallPolicyList\":[\"global-1\"] }, \"firewallDeploymentModel\":{ \"distributedFirewallDeploymentModel\":{ \"distributedFirewallOrchestrationConfig\":{ \"firewallCreationConfig\":{ \"endpointLocation\":{ \"availabilityZoneConfigList\":[ { \"availabilityZoneName\":\"${AvailabilityZone}\" } ] } }, \"allowedIPV4CidrList\":[ ] } } } }\"</code> </p> </li> <li> <p>Example: <code>WAFV2</code> - Account takeover prevention, Bot Control managed rule groups, optimize unassociated web ACL, and rule action override </p> <p> <code>\"{\\\"type\\\":\\\"WAFV2\\\",\\\"preProcessRuleGroups\\\":[{\\\"ruleGroupArn\\\":null,\\\"overrideAction\\\":{\\\"type\\\":\\\"NONE\\\"},\\\"managedRuleGroupIdentifier\\\":{\\\"versionEnabled\\\":null,\\\"version\\\":null,\\\"vendorName\\\":\\\"AWS\\\",\\\"managedRuleGroupName\\\":\\\"AWSManagedRulesATPRuleSet\\\",\\\"managedRuleGroupConfigs\\\":[{\\\"awsmanagedRulesATPRuleSet\\\":{\\\"loginPath\\\":\\\"/loginpath\\\",\\\"requestInspection\\\":{\\\"payloadType\\\":\\\"FORM_ENCODED|JSON\\\",\\\"usernameField\\\":{\\\"identifier\\\":\\\"/form/username\\\"},\\\"passwordField\\\":{\\\"identifier\\\":\\\"/form/password\\\"}}}}]},\\\"ruleGroupType\\\":\\\"ManagedRuleGroup\\\",\\\"excludeRules\\\":[],\\\"sampledRequestsEnabled\\\":true},{\\\"ruleGroupArn\\\":null,\\\"overrideAction\\\":{\\\"type\\\":\\\"NONE\\\"},\\\"managedRuleGroupIdentifier\\\":{\\\"versionEnabled\\\":null,\\\"version\\\":null,\\\"vendorName\\\":\\\"AWS\\\",\\\"managedRuleGroupName\\\":\\\"AWSManagedRulesBotControlRuleSet\\\",\\\"managedRuleGroupConfigs\\\":[{\\\"awsmanagedRulesBotControlRuleSet\\\":{\\\"inspectionLevel\\\":\\\"TARGETED|COMMON\\\"}}]},\\\"ruleGroupType\\\":\\\"ManagedRuleGroup\\\",\\\"excludeRules\\\":[],\\\"sampledRequestsEnabled\\\":true,\\\"ruleActionOverrides\\\":[{\\\"name\\\":\\\"Rule1\\\",\\\"actionToUse\\\":{\\\"allow|block|count|captcha|challenge\\\":{}}},{\\\"name\\\":\\\"Rule2\\\",\\\"actionToUse\\\":{\\\"allow|block|count|captcha|challenge\\\":{}}}]}],\\\"postProcessRuleGroups\\\":[],\\\"defaultAction\\\":{\\\"type\\\":\\\"ALLOW\\\"},\\\"customRequestHandling\\\":null,\\\"customResponse\\\":null,\\\"overrideCustomerWebACLAssociation\\\":false,\\\"loggingConfiguration\\\":null,\\\"sampledRequestsEnabledForDefaultActions\\\":true,\\\"optimizeUnassociatedWebACL\\\":true}\"</code> </p> <ul> <li> <p>Bot Control - For information about <code>AWSManagedRulesBotControlRuleSet</code> managed rule groups, see <a href=\"https://docs.aws.amazon.com/waf/latest/APIReference/API_AWSManagedRulesBotControlRuleSet.html\">AWSManagedRulesBotControlRuleSet</a> in the <i>WAF API Reference</i>.</p> </li> <li> <p>Fraud Control account takeover prevention (ATP) - For information about the properties available for <code>AWSManagedRulesATPRuleSet</code> managed rule groups, see <a href=\"https://docs.aws.amazon.com/waf/latest/APIReference/API_AWSManagedRulesATPRuleSet.html\">AWSManagedRulesATPRuleSet</a> in the <i>WAF API Reference</i>.</p> </li> <li> <p>Optimize unassociated web ACL - If you set <code>optimizeUnassociatedWebACL</code> to <code>true</code>, Firewall Manager creates web ACLs in accounts within the policy scope if the web ACLs will be used by at least one resource. Firewall Manager creates web ACLs in the accounts within policy scope only if the web ACLs will be used by at least one resource. If at any time an account comes into policy scope, Firewall Manager automatically creates a web ACL in the account if at least one resource will use the web ACL.</p> <p>Upon enablement, Firewall Manager performs a one-time cleanup of unused web ACLs in your account. The cleanup process can take several hours. If a resource leaves policy scope after Firewall Manager creates a web ACL, Firewall Manager disassociates the resource from the web ACL, but won't clean up the unused web ACL. Firewall Manager only cleans up unused web ACLs when you first enable management of unused web ACLs in a policy.</p> <p>If you set <code>optimizeUnassociatedWebACL</code> to <code>false</code> Firewall Manager doesn't manage unused web ACLs, and Firewall Manager automatically creates an empty web ACL in each account that's within policy scope.</p> </li> <li> <p>Rule action overrides - Firewall Manager supports rule action overrides only for managed rule groups. To configure a <code>RuleActionOverrides</code> add the <code>Name</code> of the rule to override, and <code>ActionToUse</code>, which is the new action to use for the rule. For information about using rule action override, see <a href=\"https://docs.aws.amazon.com/waf/latest/APIReference/API_RuleActionOverride.html\">RuleActionOverride</a> in the <i>WAF API Reference</i>.</p> </li> </ul> </li> <li> <p>Example: <code>WAFV2</code> - <code>CAPTCHA</code> and <code>Challenge</code> configs </p> <p> <code>\"{\\\"type\\\":\\\"WAFV2\\\",\\\"preProcessRuleGroups\\\":[{\\\"ruleGroupArn\\\":null,\\\"overrideAction\\\":{\\\"type\\\":\\\"NONE\\\"},\\\"managedRuleGroupIdentifier\\\":{\\\"versionEnabled\\\":null,\\\"version\\\":null,\\\"vendorName\\\":\\\"AWS\\\",\\\"managedRuleGroupName\\\":\\\"AWSManagedRulesAdminProtectionRuleSet\\\"},\\\"ruleGroupType\\\":\\\"ManagedRuleGroup\\\",\\\"excludeRules\\\":[],\\\"sampledRequestsEnabled\\\":true}],\\\"postProcessRuleGroups\\\":[],\\\"defaultAction\\\":{\\\"type\\\":\\\"ALLOW\\\"},\\\"customRequestHandling\\\":null,\\\"customResponse\\\":null,\\\"overrideCustomerWebACLAssociation\\\":false,\\\"loggingConfiguration\\\":null,\\\"sampledRequestsEnabledForDefaultActions\\\":true,\\\"captchaConfig\\\":{\\\"immunityTimeProperty\\\":{\\\"immunityTime\\\":500}},\\\"challengeConfig\\\":{\\\"immunityTimeProperty\\\":{\\\"immunityTime\\\":800}},\\\"tokenDomains\\\":[\\\"google.com\\\",\\\"amazon.com\\\"],\\\"associationConfig\\\":{\\\"requestBody\\\":{\\\"CLOUDFRONT\\\":{\\\"defaultSizeInspectionLimit\\\":\\\"KB_16\\\"}}}}\"</code> </p> <ul> <li> <p> <code>CAPTCHA</code> and <code>Challenge</code> configs - If you update the policy's values for <code>associationConfig</code>, <code>captchaConfig</code>, <code>challengeConfig</code>, or <code>tokenDomains</code>, Firewall Manager will overwrite your local web ACLs to contain the new value(s). However, if you don't update the policy's <code>associationConfig</code>, <code>captchaConfig</code>, <code>challengeConfig</code>, or <code>tokenDomains</code> values, the values in your local web ACLs will remain unchanged. For information about association configs, see <a href=\"https://docs.aws.amazon.com/waf/latest/APIReference/API_AssociationConfig.html\">AssociationConfig</a>. For information about CAPTCHA and Challenge configs, see <a href=\"https://docs.aws.amazon.com/waf/latest/APIReference/API_CaptchaConfig.html\">CaptchaConfig</a> and <a href=\"https://docs.aws.amazon.com/waf/latest/APIReference/API_ChallengeConfig.html\">ChallengeConfig</a> in the <i>WAF API Reference</i>.</p> </li> <li> <p> <code>defaultSizeInspectionLimit</code> - Specifies the maximum size of the web request body component that an associated Amazon CloudFront distribution should send to WAF for inspection. For more information, see <a href=\"https://docs.aws.amazon.com/waf/latest/APIReference/API_RequestBodyAssociatedResourceTypeConfig.html#WAF-Type-RequestBodyAssociatedResourceTypeConfig-DefaultSizeInspectionLimit\">DefaultSizeInspectionLimit</a> in the <i>WAF API Reference</i>.</p> </li> </ul> </li> <li> <p>Example: <code>WAFV2</code> - Firewall Manager support for WAF managed rule group versioning </p> <p> <code>\"{\\\"type\\\":\\\"WAFV2\\\",\\\"preProcessRuleGroups\\\":[{\\\"ruleGroupArn\\\":null,\\\"overrideAction\\\":{\\\"type\\\":\\\"NONE\\\"},\\\"managedRuleGroupIdentifier\\\":{\\\"versionEnabled\\\":true,\\\"version\\\":\\\"Version_2.0\\\",\\\"vendorName\\\":\\\"AWS\\\",\\\"managedRuleGroupName\\\":\\\"AWSManagedRulesCommonRuleSet\\\"},\\\"ruleGroupType\\\":\\\"ManagedRuleGroup\\\",\\\"excludeRules\\\":[{\\\"name\\\":\\\"NoUserAgent_HEADER\\\"}]}],\\\"postProcessRuleGroups\\\":[],\\\"defaultAction\\\":{\\\"type\\\":\\\"ALLOW\\\"},\\\"overrideCustomerWebACLAssociation\\\":false,\\\"loggingConfiguration\\\":{\\\"logDestinationConfigs\\\":[\\\"arn:aws:firehose:us-west-2:12345678912:deliverystream/aws-waf-logs-fms-admin-destination\\\"],\\\"redactedFields\\\":[{\\\"redactedFieldType\\\":\\\"SingleHeader\\\",\\\"redactedFieldValue\\\":\\\"Cookies\\\"},{\\\"redactedFieldType\\\":\\\"Method\\\"}]}}\"</code> </p> <p> To use a specific version of a WAF managed rule group in your Firewall Manager policy, you must set <code>versionEnabled</code> to <code>true</code>, and set <code>version</code> to the version you'd like to use. If you don't set <code>versionEnabled</code> to <code>true</code>, or if you omit <code>versionEnabled</code>, then Firewall Manager uses the default version of the WAF managed rule group. </p> </li> <li> <p>Example: <code>WAFV2</code> - Logging configurations </p> <p> <code>\"{\\\"type\\\":\\\"WAFV2\\\",\\\"preProcessRuleGroups\\\":[{\\\"ruleGroupArn\\\":null, \\\"overrideAction\\\":{\\\"type\\\":\\\"NONE\\\"},\\\"managedRuleGroupIdentifier\\\": {\\\"versionEnabled\\\":null,\\\"version\\\":null,\\\"vendorName\\\":\\\"AWS\\\", \\\"managedRuleGroupName\\\":\\\"AWSManagedRulesAdminProtectionRuleSet\\\"} ,\\\"ruleGroupType\\\":\\\"ManagedRuleGroup\\\",\\\"excludeRules\\\":[], \\\"sampledRequestsEnabled\\\":true}],\\\"postProcessRuleGroups\\\":[], \\\"defaultAction\\\":{\\\"type\\\":\\\"ALLOW\\\"},\\\"customRequestHandling\\\" :null,\\\"customResponse\\\":null,\\\"overrideCustomerWebACLAssociation\\\" :false,\\\"loggingConfiguration\\\":{\\\"logDestinationConfigs\\\": [\\\"arn:aws:s3:::aws-waf-logs-example-bucket\\\"] ,\\\"redactedFields\\\":[],\\\"loggingFilterConfigs\\\":{\\\"defaultBehavior\\\":\\\"KEEP\\\", \\\"filters\\\":[{\\\"behavior\\\":\\\"KEEP\\\",\\\"requirement\\\":\\\"MEETS_ALL\\\", \\\"conditions\\\":[{\\\"actionCondition\\\":\\\"CAPTCHA\\\"},{\\\"actionCondition\\\": \\\"CHALLENGE\\\"}, {\\\"actionCondition\\\":\\\"EXCLUDED_AS_COUNT\\\"}]}]}},\\\"sampledRequestsEnabledForDefaultActions\\\":true}\"</code> </p> <p>Firewall Manager supports Amazon Kinesis Data Firehose and Amazon S3 as the <code>logDestinationConfigs</code> in your <code>loggingConfiguration</code>. For information about WAF logging configurations, see <a href=\"https://docs.aws.amazon.com/waf/latest/APIReference/API_LoggingConfiguration.html\">LoggingConfiguration</a> in the <i>WAF API Reference</i> </p> <p>In the <code>loggingConfiguration</code>, you can specify one <code>logDestinationConfigs</code>. Optionally provide as many as 20 <code>redactedFields</code>. The <code>RedactedFieldType</code> must be one of <code>URI</code>, <code>QUERY_STRING</code>, <code>HEADER</code>, or <code>METHOD</code>.</p> </li> <li> <p>Example: <code>WAF Classic</code> </p> <p> <code>\"{\\\"type\\\": \\\"WAF\\\", \\\"ruleGroups\\\": [{\\\"id\\\":\\\"12345678-1bcd-9012-efga-0987654321ab\\\", \\\"overrideAction\\\" : {\\\"type\\\": \\\"COUNT\\\"}}], \\\"defaultAction\\\": {\\\"type\\\": \\\"BLOCK\\\"}}\"</code> </p> </li> </ul>"}, "PolicyOption": {"shape": "PolicyOption", "documentation": "<p>Contains the Network Firewall firewall policy options to configure a centralized deployment model.</p>"}}, "documentation": "<p>Details about the security service that is being used to protect the resources.</p>"}, "SecurityServiceType": {"type": "string", "enum": ["WAF", "WAFV2", "SHIELD_ADVANCED", "SECURITY_GROUPS_COMMON", "SECURITY_GROUPS_CONTENT_AUDIT", "SECURITY_GROUPS_USAGE_AUDIT", "NETWORK_FIREWALL", "DNS_FIREWALL", "THIRD_PARTY_FIREWALL", "IMPORT_NETWORK_FIREWALL"]}, "SecurityServiceTypeList": {"type": "list", "member": {"shape": "SecurityServiceType"}, "max": 32, "min": 0}, "StatefulEngineOptions": {"type": "structure", "members": {"RuleOrder": {"shape": "RuleOrder", "documentation": "<p>Indicates how to manage the order of stateful rule evaluation for the policy. <code>DEFAULT_ACTION_ORDER</code> is the default behavior. Stateful rules are provided to the rule engine as Suricata compatible strings, and Suricata evaluates them based on certain settings. For more information, see <a href=\"https://docs.aws.amazon.com/network-firewall/latest/developerguide/suricata-rule-evaluation-order.html\">Evaluation order for stateful rules</a> in the <i>Network Firewall Developer Guide</i>.</p>"}}, "documentation": "<p>Configuration settings for the handling of the stateful rule groups in a Network Firewall firewall policy.</p>"}, "StatefulRuleGroup": {"type": "structure", "members": {"RuleGroupName": {"shape": "NetworkFirewallResourceName", "documentation": "<p>The name of the rule group.</p>"}, "ResourceId": {"shape": "ResourceId", "documentation": "<p>The resource ID of the rule group.</p>"}, "Priority": {"shape": "PriorityNumber", "documentation": "<p>An integer setting that indicates the order in which to run the stateful rule groups in a single Network Firewall firewall policy. This setting only applies to firewall policies that specify the <code>STRICT_ORDER</code> rule order in the stateful engine options settings.</p> <p> Network Firewall evalutes each stateful rule group against a packet starting with the group that has the lowest priority setting. You must ensure that the priority settings are unique within each policy. For information about </p> <p> You can change the priority settings of your rule groups at any time. To make it easier to insert rule groups later, number them so there's a wide range in between, for example use 100, 200, and so on. </p>"}, "Override": {"shape": "NetworkFirewallStatefulRuleGroupOverride", "documentation": "<p>The action that allows the policy owner to override the behavior of the rule group within a policy.</p>"}}, "documentation": "<p>Network Firewall stateful rule group, used in a <a>NetworkFirewallPolicyDescription</a>. </p>"}, "StatefulRuleGroupList": {"type": "list", "member": {"shape": "StatefulRuleGroup"}}, "StatelessRuleGroup": {"type": "structure", "members": {"RuleGroupName": {"shape": "NetworkFirewallResourceName", "documentation": "<p>The name of the rule group.</p>"}, "ResourceId": {"shape": "ResourceId", "documentation": "<p>The resource ID of the rule group.</p>"}, "Priority": {"shape": "StatelessRuleGroupPriority", "documentation": "<p>The priority of the rule group. Network Firewall evaluates the stateless rule groups in a firewall policy starting from the lowest priority setting. </p>"}}, "documentation": "<p>Network Firewall stateless rule group, used in a <a>NetworkFirewallPolicyDescription</a>. </p>"}, "StatelessRuleGroupList": {"type": "list", "member": {"shape": "StatelessRuleGroup"}}, "StatelessRuleGroupPriority": {"type": "integer", "max": 65535, "min": 1}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>Part of the key:value pair that defines a tag. You can use a tag key to describe a category of information, such as \"customer.\" Tag keys are case-sensitive.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>Part of the key:value pair that defines a tag. You can use a tag value to describe a specific value within a category, such as \"companyA\" or \"companyB.\" Tag values are case-sensitive. </p>"}}, "documentation": "<p>A collection of key:value pairs associated with an Amazon Web Services resource. The key:value pair can be anything you define. Typically, the tag key represents a category (such as \"environment\") and the tag value represents a specific value within that category (such as \"test,\" \"development,\" or \"production\"). You can add up to 50 tags to each Amazon Web Services resource. </p>"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 0}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "max": 200, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["ResourceArn", "TagList"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource to return tags for. The Firewall Manager resources that support tagging are policies, applications lists, and protocols lists. </p>"}, "TagList": {"shape": "TagList", "documentation": "<p>The tags to add to the resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "TargetType": {"type": "string", "enum": ["GATEWAY", "CARRIER_GATEWAY", "INSTANCE", "LOCAL_GATEWAY", "NAT_GATEWAY", "NETWORK_INTERFACE", "VPC_ENDPOINT", "VPC_PEERING_CONNECTION", "EGRESS_ONLY_INTERNET_GATEWAY", "TRANSIT_GATEWAY"]}, "TargetViolationReason": {"type": "string", "max": 256, "min": 0, "pattern": "\\w+"}, "TargetViolationReasons": {"type": "list", "member": {"shape": "TargetViolationReason"}}, "ThirdPartyFirewall": {"type": "string", "enum": ["PALO_ALTO_NETWORKS_CLOUD_NGFW", "FORTIGATE_CLOUD_NATIVE_FIREWALL"]}, "ThirdPartyFirewallAssociationStatus": {"type": "string", "enum": ["ONBOARDING", "ONBOARD_COMPLETE", "OFFBOARDING", "OFFBOARD_COMPLETE", "NOT_EXIST"]}, "ThirdPartyFirewallFirewallPolicies": {"type": "list", "member": {"shape": "ThirdPartyFirewallFirewallPolicy"}}, "ThirdPartyFirewallFirewallPolicy": {"type": "structure", "members": {"FirewallPolicyId": {"shape": "FirewallPolicyId", "documentation": "<p>The ID of the specified firewall policy.</p>"}, "FirewallPolicyName": {"shape": "FirewallPolicyName", "documentation": "<p>The name of the specified firewall policy.</p>"}}, "documentation": "<p>Configures the third-party firewall's firewall policy.</p>"}, "ThirdPartyFirewallMissingExpectedRouteTableViolation": {"type": "structure", "members": {"ViolationTarget": {"shape": "Violation<PERSON>arget", "documentation": "<p>The ID of the third-party firewall or VPC resource that's causing the violation.</p>"}, "VPC": {"shape": "ResourceId", "documentation": "<p>The resource ID of the VPC associated with a fireawll subnet that's causing the violation.</p>"}, "AvailabilityZone": {"shape": "LengthBoundedString", "documentation": "<p>The Availability Zone of the firewall subnet that's causing the violation.</p>"}, "CurrentRouteTable": {"shape": "ResourceId", "documentation": "<p>The resource ID of the current route table that's associated with the subnet, if one is available.</p>"}, "ExpectedRouteTable": {"shape": "ResourceId", "documentation": "<p>The resource ID of the route table that should be associated with the subnet.</p>"}}, "documentation": "<p>The violation details for a third-party firewall that's not associated with an Firewall Manager managed route table.</p>"}, "ThirdPartyFirewallMissingFirewallViolation": {"type": "structure", "members": {"ViolationTarget": {"shape": "Violation<PERSON>arget", "documentation": "<p>The ID of the third-party firewall that's causing the violation.</p>"}, "VPC": {"shape": "ResourceId", "documentation": "<p>The resource ID of the VPC associated with a third-party firewall.</p>"}, "AvailabilityZone": {"shape": "LengthBoundedString", "documentation": "<p>The Availability Zone of the third-party firewall that's causing the violation.</p>"}, "TargetViolationReason": {"shape": "TargetViolationReason", "documentation": "<p>The reason the resource is causing this violation, if a reason is available.</p>"}}, "documentation": "<p>The violation details about a third-party firewall's subnet that doesn't have a Firewall Manager managed firewall in its VPC.</p>"}, "ThirdPartyFirewallMissingSubnetViolation": {"type": "structure", "members": {"ViolationTarget": {"shape": "Violation<PERSON>arget", "documentation": "<p>The ID of the third-party firewall or VPC resource that's causing the violation.</p>"}, "VPC": {"shape": "ResourceId", "documentation": "<p>The resource ID of the VPC associated with a subnet that's causing the violation.</p>"}, "AvailabilityZone": {"shape": "LengthBoundedString", "documentation": "<p>The Availability Zone of a subnet that's causing the violation.</p>"}, "TargetViolationReason": {"shape": "TargetViolationReason", "documentation": "<p>The reason the resource is causing the violation, if a reason is available.</p>"}}, "documentation": "<p>The violation details for a third-party firewall for an Availability Zone that's missing the Firewall Manager managed subnet.</p>"}, "ThirdPartyFirewallPolicy": {"type": "structure", "members": {"FirewallDeploymentModel": {"shape": "FirewallDeploymentModel", "documentation": "<p>Defines the deployment model to use for the third-party firewall policy.</p>"}}, "documentation": "<p>Configures the deployment model for the third-party firewall.</p>"}, "TimeStamp": {"type": "timestamp"}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource to return tags for. The Firewall Manager resources that support tagging are policies, applications lists, and protocols lists. </p>"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>The keys of the tags to remove from the resource. </p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateToken": {"type": "string", "max": 1024, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "ViolationDetail": {"type": "structure", "required": ["PolicyId", "MemberAccount", "ResourceId", "ResourceType", "ResourceViolations"], "members": {"PolicyId": {"shape": "PolicyId", "documentation": "<p>The ID of the Firewall Manager policy that the violation details were requested for.</p>"}, "MemberAccount": {"shape": "AWSAccountId", "documentation": "<p>The Amazon Web Services account that the violation details were requested for.</p>"}, "ResourceId": {"shape": "ResourceId", "documentation": "<p>The resource ID that the violation details were requested for.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The resource type that the violation details were requested for.</p>"}, "ResourceViolations": {"shape": "ResourceViolations", "documentation": "<p>List of violations for the requested resource.</p>"}, "ResourceTags": {"shape": "TagList", "documentation": "<p>The <code>ResourceTag</code> objects associated with the resource.</p>"}, "ResourceDescription": {"shape": "LengthBoundedString", "documentation": "<p>Brief description for the requested resource.</p>"}}, "documentation": "<p>Violations for a resource based on the specified Firewall Manager policy and Amazon Web Services account.</p>"}, "ViolationReason": {"type": "string", "enum": ["WEB_ACL_MISSING_RULE_GROUP", "RESOURCE_MISSING_WEB_ACL", "RESOURCE_INCORRECT_WEB_ACL", "RESOURCE_MISSING_SHIELD_PROTECTION", "RESOURCE_MISSING_WEB_ACL_OR_SHIELD_PROTECTION", "RESOURCE_MISSING_SECURITY_GROUP", "RESOURCE_VIOLATES_AUDIT_SECURITY_GROUP", "SECURITY_GROUP_UNUSED", "SECURITY_GROUP_REDUNDANT", "FMS_CREATED_SECURITY_GROUP_EDITED", "MISSING_FIREWALL", "MISSING_FIREWALL_SUBNET_IN_AZ", "MISSING_EXPECTED_ROUTE_TABLE", "NETWORK_FIREWALL_POLICY_MODIFIED", "FIREWALL_SUBNET_IS_OUT_OF_SCOPE", "INTERNET_GATEWAY_MISSING_EXPECTED_ROUTE", "FIREWALL_SUBNET_MISSING_EXPECTED_ROUTE", "UNEXPECTED_FIREWALL_ROUTES", "UNEXPECTED_TARGET_GATEWAY_ROUTES", "TRAFFIC_INSPECTION_CROSSES_AZ_BOUNDARY", "INVALID_ROUTE_CONFIGURATION", "MISSING_TARGET_GATEWAY", "INTERNET_TRAFFIC_NOT_INSPECTED", "BLACK_HOLE_ROUTE_DETECTED", "BLACK_HOLE_ROUTE_DETECTED_IN_FIREWALL_SUBNET", "RESOURCE_MISSING_DNS_FIREWALL", "ROUTE_HAS_OUT_OF_SCOPE_ENDPOINT", "FIREWALL_SUBNET_MISSING_VPCE_ENDPOINT"]}, "ViolationTarget": {"type": "string", "max": 1024, "min": 0, "pattern": ".*"}}, "documentation": "<p>This is the <i>Firewall Manager API Reference</i>. This guide is for developers who need detailed information about the Firewall Manager API actions, data types, and errors. For detailed information about Firewall Manager features, see the <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/fms-chapter.html\">Firewall Manager Developer Guide</a>.</p> <p>Some API actions require explicit resource permissions. For information, see the developer guide topic <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/fms-security_iam_service-with-iam.html#fms-security_iam_service-with-iam-roles-service\">Service roles for Firewall Manager</a>. </p>"}