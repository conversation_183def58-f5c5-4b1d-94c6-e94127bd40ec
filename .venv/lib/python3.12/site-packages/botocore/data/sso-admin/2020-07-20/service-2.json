{"version": "2.0", "metadata": {"apiVersion": "2020-07-20", "endpointPrefix": "sso", "jsonVersion": "1.1", "protocol": "json", "serviceAbbreviation": "SSO Admin", "serviceFullName": "AWS Single Sign-On Admin", "serviceId": "SSO Admin", "signatureVersion": "v4", "signingName": "sso", "targetPrefix": "SWBExternalService", "uid": "sso-admin-2020-07-20"}, "operations": {"AttachCustomerManagedPolicyReferenceToPermissionSet": {"name": "AttachCustomerManagedPolicyReferenceToPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AttachCustomerManagedPolicyReferenceToPermissionSetRequest"}, "output": {"shape": "AttachCustomerManagedPolicyReferenceToPermissionSetResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Attaches the specified customer managed policy to the specified <a>PermissionSet</a>.</p>"}, "AttachManagedPolicyToPermissionSet": {"name": "AttachManagedPolicyToPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AttachManagedPolicyToPermissionSetRequest"}, "output": {"shape": "AttachManagedPolicyToPermissionSetResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Attaches an Amazon Web Services managed policy ARN to a permission set.</p> <note> <p>If the permission set is already referenced by one or more account assignments, you will need to call <code> <a>ProvisionPermissionSet</a> </code> after this operation. Calling <code>ProvisionPermissionSet</code> applies the corresponding IAM policy updates to all assigned accounts.</p> </note>"}, "CreateAccountAssignment": {"name": "CreateAccountAssignment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateAccountAssignmentRequest"}, "output": {"shape": "CreateAccountAssignmentResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Assigns access to a principal for a specified Amazon Web Services account using a specified permission set.</p> <note> <p>The term <i>principal</i> here refers to a user or group that is defined in IAM Identity Center.</p> </note> <note> <p>As part of a successful <code>CreateAccountAssignment</code> call, the specified permission set will automatically be provisioned to the account in the form of an IAM policy. That policy is attached to the IAM role created in IAM Identity Center. If the permission set is subsequently updated, the corresponding IAM policies attached to roles in your accounts will not be updated automatically. In this case, you must call <code> <a>ProvisionPermissionSet</a> </code> to make these updates.</p> </note> <note> <p> After a successful response, call <code>DescribeAccountAssignmentCreationStatus</code> to describe the status of an assignment creation request. </p> </note>"}, "CreateInstanceAccessControlAttributeConfiguration": {"name": "CreateInstanceAccessControlAttributeConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateInstanceAccessControlAttributeConfigurationRequest"}, "output": {"shape": "CreateInstanceAccessControlAttributeConfigurationResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Enables the attributes-based access control (ABAC) feature for the specified IAM Identity Center instance. You can also specify new attributes to add to your ABAC configuration during the enabling process. For more information about ABAC, see <a href=\"/singlesignon/latest/userguide/abac.html\">Attribute-Based Access Control</a> in the <i>IAM Identity Center User Guide</i>.</p> <note> <p>After a successful response, call <code>DescribeInstanceAccessControlAttributeConfiguration</code> to validate that <code>InstanceAccessControlAttributeConfiguration</code> was created.</p> </note>"}, "CreatePermissionSet": {"name": "CreatePermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreatePermissionSetRequest"}, "output": {"shape": "CreatePermissionSetResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates a permission set within a specified IAM Identity Center instance.</p> <note> <p>To grant users and groups access to Amazon Web Services account resources, use <code> <a>CreateAccountAssignment</a> </code>.</p> </note>"}, "DeleteAccountAssignment": {"name": "DeleteAccountAssignment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteAccountAssignmentRequest"}, "output": {"shape": "DeleteAccountAssignmentResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes a principal's access from a specified Amazon Web Services account using a specified permission set.</p> <note> <p>After a successful response, call <code>DescribeAccountAssignmentDeletionStatus</code> to describe the status of an assignment deletion request.</p> </note>"}, "DeleteInlinePolicyFromPermissionSet": {"name": "DeleteInlinePolicyFromPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteInlinePolicyFromPermissionSetRequest"}, "output": {"shape": "DeleteInlinePolicyFromPermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes the inline policy from a specified permission set.</p>"}, "DeleteInstanceAccessControlAttributeConfiguration": {"name": "DeleteInstanceAccessControlAttributeConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteInstanceAccessControlAttributeConfigurationRequest"}, "output": {"shape": "DeleteInstanceAccessControlAttributeConfigurationResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Disables the attributes-based access control (ABAC) feature for the specified IAM Identity Center instance and deletes all of the attribute mappings that have been configured. Once deleted, any attributes that are received from an identity source and any custom attributes you have previously configured will not be passed. For more information about ABAC, see <a href=\"/singlesignon/latest/userguide/abac.html\">Attribute-Based Access Control</a> in the <i>IAM Identity Center User Guide</i>.</p>"}, "DeletePermissionSet": {"name": "DeletePermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeletePermissionSetRequest"}, "output": {"shape": "DeletePermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes the specified permission set.</p>"}, "DeletePermissionsBoundaryFromPermissionSet": {"name": "DeletePermissionsBoundaryFromPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeletePermissionsBoundaryFromPermissionSetRequest"}, "output": {"shape": "DeletePermissionsBoundaryFromPermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Deletes the permissions boundary from a specified <a>PermissionSet</a>.</p>"}, "DescribeAccountAssignmentCreationStatus": {"name": "DescribeAccountAssignmentCreationStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeAccountAssignmentCreationStatusRequest"}, "output": {"shape": "DescribeAccountAssignmentCreationStatusResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Describes the status of the assignment creation request.</p>"}, "DescribeAccountAssignmentDeletionStatus": {"name": "DescribeAccountAssignmentDeletionStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeAccountAssignmentDeletionStatusRequest"}, "output": {"shape": "DescribeAccountAssignmentDeletionStatusResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Describes the status of the assignment deletion request.</p>"}, "DescribeInstanceAccessControlAttributeConfiguration": {"name": "DescribeInstanceAccessControlAttributeConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeInstanceAccessControlAttributeConfigurationRequest"}, "output": {"shape": "DescribeInstanceAccessControlAttributeConfigurationResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns the list of IAM Identity Center identity store attributes that have been configured to work with attributes-based access control (ABAC) for the specified IAM Identity Center instance. This will not return attributes configured and sent by an external identity provider. For more information about ABAC, see <a href=\"/singlesignon/latest/userguide/abac.html\">Attribute-Based Access Control</a> in the <i>IAM Identity Center User Guide</i>.</p>"}, "DescribePermissionSet": {"name": "DescribePermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribePermissionSetRequest"}, "output": {"shape": "DescribePermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Gets the details of the permission set.</p>"}, "DescribePermissionSetProvisioningStatus": {"name": "DescribePermissionSetProvisioningStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribePermissionSetProvisioningStatusRequest"}, "output": {"shape": "DescribePermissionSetProvisioningStatusResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Describes the status for the given permission set provisioning request.</p>"}, "DetachCustomerManagedPolicyReferenceFromPermissionSet": {"name": "DetachCustomerManagedPolicyReferenceFromPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DetachCustomerManagedPolicyReferenceFromPermissionSetRequest"}, "output": {"shape": "DetachCustomerManagedPolicyReferenceFromPermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Detaches the specified customer managed policy from the specified <a>PermissionSet</a>.</p>"}, "DetachManagedPolicyFromPermissionSet": {"name": "DetachManagedPolicyFromPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DetachManagedPolicyFromPermissionSetRequest"}, "output": {"shape": "DetachManagedPolicyFromPermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Detaches the attached Amazon Web Services managed policy ARN from the specified permission set.</p>"}, "GetInlinePolicyForPermissionSet": {"name": "GetInlinePolicyForPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetInlinePolicyForPermissionSetRequest"}, "output": {"shape": "GetInlinePolicyForPermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Obtains the inline policy assigned to the permission set.</p>"}, "GetPermissionsBoundaryForPermissionSet": {"name": "GetPermissionsBoundaryForPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetPermissionsBoundaryForPermissionSetRequest"}, "output": {"shape": "GetPermissionsBoundaryForPermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Obtains the permissions boundary for a specified <a>PermissionSet</a>.</p>"}, "ListAccountAssignmentCreationStatus": {"name": "ListAccountAssignmentCreationStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAccountAssignmentCreationStatusRequest"}, "output": {"shape": "ListAccountAssignmentCreationStatusResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the status of the Amazon Web Services account assignment creation requests for a specified IAM Identity Center instance.</p>"}, "ListAccountAssignmentDeletionStatus": {"name": "ListAccountAssignmentDeletionStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAccountAssignmentDeletionStatusRequest"}, "output": {"shape": "ListAccountAssignmentDeletionStatusResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the status of the Amazon Web Services account assignment deletion requests for a specified IAM Identity Center instance.</p>"}, "ListAccountAssignments": {"name": "ListAccountAssignments", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAccountAssignmentsRequest"}, "output": {"shape": "ListAccountAssignmentsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the assignee of the specified Amazon Web Services account with the specified permission set.</p>"}, "ListAccountsForProvisionedPermissionSet": {"name": "ListAccountsForProvisionedPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAccountsForProvisionedPermissionSetRequest"}, "output": {"shape": "ListAccountsForProvisionedPermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists all the Amazon Web Services accounts where the specified permission set is provisioned.</p>"}, "ListCustomerManagedPolicyReferencesInPermissionSet": {"name": "ListCustomerManagedPolicyReferencesInPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListCustomerManagedPolicyReferencesInPermissionSetRequest"}, "output": {"shape": "ListCustomerManagedPolicyReferencesInPermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists all customer managed policies attached to a specified <a>PermissionSet</a>.</p>"}, "ListInstances": {"name": "ListInstances", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListInstancesRequest"}, "output": {"shape": "ListInstancesResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the IAM Identity Center instances that the caller has access to.</p>"}, "ListManagedPoliciesInPermissionSet": {"name": "ListManagedPoliciesInPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListManagedPoliciesInPermissionSetRequest"}, "output": {"shape": "ListManagedPoliciesInPermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the Amazon Web Services managed policy that is attached to a specified permission set.</p>"}, "ListPermissionSetProvisioningStatus": {"name": "ListPermissionSetProvisioningStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListPermissionSetProvisioningStatusRequest"}, "output": {"shape": "ListPermissionSetProvisioningStatusResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the status of the permission set provisioning requests for a specified IAM Identity Center instance.</p>"}, "ListPermissionSets": {"name": "ListPermissionSets", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListPermissionSetsRequest"}, "output": {"shape": "ListPermissionSetsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the <a>PermissionSet</a>s in an IAM Identity Center instance.</p>"}, "ListPermissionSetsProvisionedToAccount": {"name": "ListPermissionSetsProvisionedToAccount", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListPermissionSetsProvisionedToAccountRequest"}, "output": {"shape": "ListPermissionSetsProvisionedToAccountResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists all the permission sets that are provisioned to a specified Amazon Web Services account.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the tags that are attached to a specified resource.</p>"}, "ProvisionPermissionSet": {"name": "ProvisionPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ProvisionPermissionSetRequest"}, "output": {"shape": "ProvisionPermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>The process by which a specified permission set is provisioned to the specified target.</p>"}, "PutInlinePolicyToPermissionSet": {"name": "PutInlinePolicyToPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutInlinePolicyToPermissionSetRequest"}, "output": {"shape": "PutInlinePolicyToPermissionSetResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Attaches an inline policy to a permission set.</p> <note> <p>If the permission set is already referenced by one or more account assignments, you will need to call <code> <a>ProvisionPermissionSet</a> </code> after this action to apply the corresponding IAM policy updates to all assigned accounts.</p> </note>"}, "PutPermissionsBoundaryToPermissionSet": {"name": "PutPermissionsBoundaryToPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutPermissionsBoundaryToPermissionSetRequest"}, "output": {"shape": "PutPermissionsBoundaryToPermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Attaches an Amazon Web Services managed or customer managed policy to the specified <a>PermissionSet</a> as a permissions boundary.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Associates a set of tags with a specified resource.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Disassociates a set of tags from a specified resource.</p>"}, "UpdateInstanceAccessControlAttributeConfiguration": {"name": "UpdateInstanceAccessControlAttributeConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateInstanceAccessControlAttributeConfigurationRequest"}, "output": {"shape": "UpdateInstanceAccessControlAttributeConfigurationResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Updates the IAM Identity Center identity store attributes that you can use with the IAM Identity Center instance for attributes-based access control (ABAC). When using an external identity provider as an identity source, you can pass attributes through the SAML assertion as an alternative to configuring attributes from the IAM Identity Center identity store. If a SAML assertion passes any of these attributes, IAM Identity Center replaces the attribute value with the value from the IAM Identity Center identity store. For more information about ABAC, see <a href=\"/singlesignon/latest/userguide/abac.html\">Attribute-Based Access Control</a> in the <i>IAM Identity Center User Guide</i>.</p>"}, "UpdatePermissionSet": {"name": "UpdatePermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdatePermissionSetRequest"}, "output": {"shape": "UpdatePermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Updates an existing permission set.</p>"}}, "shapes": {"AccessControlAttribute": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "AccessControlAttributeKey", "documentation": "<p>The name of the attribute associated with your identities in your identity source. This is used to map a specified attribute in your identity source with an attribute in IAM Identity Center.</p>"}, "Value": {"shape": "AccessControlAttributeValue", "documentation": "<p>The value used for mapping a specified attribute to an identity source.</p>"}}, "documentation": "<p>These are IAM Identity Center identity store attributes that you can configure for use in attributes-based access control (ABAC). You can create permissions policies that determine who can access your Amazon Web Services resources based upon the configured attribute values. When you enable ABAC and specify <code>AccessControlAttributes</code>, IAM Identity Center passes the attribute values of the authenticated user into IAM for use in policy evaluation.</p>"}, "AccessControlAttributeKey": {"type": "string", "max": 128, "min": 1, "pattern": "^[\\p{L}\\p{Z}\\p{N}_.:\\/=+\\-@]+$"}, "AccessControlAttributeList": {"type": "list", "member": {"shape": "AccessControlAttribute"}, "max": 50, "min": 0}, "AccessControlAttributeValue": {"type": "structure", "required": ["Source"], "members": {"Source": {"shape": "AccessControlAttributeValueSourceList", "documentation": "<p>The identity source to use when mapping a specified attribute to IAM Identity Center.</p>"}}, "documentation": "<p>The value used for mapping a specified attribute to an identity source. For more information, see <a href=\"https://docs.aws.amazon.com/singlesignon/latest/userguide/attributemappingsconcept.html\">Attribute mappings</a> in the <i>IAM Identity Center User Guide</i>.</p>"}, "AccessControlAttributeValueSource": {"type": "string", "max": 256, "min": 0, "pattern": "^[\\p{L}\\p{Z}\\p{N}_.:\\/=+\\-@\\[\\]\\{\\}\\$\\\\\"]*$"}, "AccessControlAttributeValueSourceList": {"type": "list", "member": {"shape": "AccessControlAttributeValueSource"}, "max": 1, "min": 1}, "AccessDeniedException": {"type": "structure", "members": {"Message": {"shape": "AccessDeniedExceptionMessage"}}, "documentation": "<p>You do not have sufficient access to perform this action.</p>", "exception": true}, "AccessDeniedExceptionMessage": {"type": "string"}, "AccountAssignment": {"type": "structure", "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The identifier of the Amazon Web Services account.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PrincipalId": {"shape": "PrincipalId", "documentation": "<p>An identifier for an object in IAM Identity Center, such as a user or group. PrincipalIds are GUIDs (For example, f81d4fae-7dec-11d0-a765-00a0c91e6bf6). For more information about PrincipalIds in IAM Identity Center, see the <a href=\"/singlesignon/latest/IdentityStoreAPIReference/welcome.html\">IAM Identity Center Identity Store API Reference</a>.</p>"}, "PrincipalType": {"shape": "PrincipalType", "documentation": "<p>The entity type for which the assignment will be created.</p>"}}, "documentation": "<p>The assignment that indicates a principal's limited access to a specified Amazon Web Services account with a specified permission set.</p> <note> <p>The term <i>principal</i> here refers to a user or group that is defined in IAM Identity Center.</p> </note>"}, "AccountAssignmentList": {"type": "list", "member": {"shape": "AccountAssignment"}}, "AccountAssignmentOperationStatus": {"type": "structure", "members": {"CreatedDate": {"shape": "Date", "documentation": "<p>The date that the permission set was created.</p>"}, "FailureReason": {"shape": "Reason", "documentation": "<p>The message that contains an error or exception in case of an operation failure.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PrincipalId": {"shape": "PrincipalId", "documentation": "<p>An identifier for an object in IAM Identity Center, such as a user or group. PrincipalIds are GUIDs (For example, f81d4fae-7dec-11d0-a765-00a0c91e6bf6). For more information about PrincipalIds in IAM Identity Center, see the <a href=\"/singlesignon/latest/IdentityStoreAPIReference/welcome.html\">IAM Identity Center Identity Store API Reference</a>.</p>"}, "PrincipalType": {"shape": "PrincipalType", "documentation": "<p>The entity type for which the assignment will be created.</p>"}, "RequestId": {"shape": "UUId", "documentation": "<p>The identifier for tracking the request operation that is generated by the universally unique identifier (UUID) workflow.</p>"}, "Status": {"shape": "StatusValues", "documentation": "<p>The status of the permission set provisioning process.</p>"}, "TargetId": {"shape": "TargetId", "documentation": "<p>TargetID is an Amazon Web Services account identifier, (For example, ************).</p>"}, "TargetType": {"shape": "TargetType", "documentation": "<p>The entity type for which the assignment will be created.</p>"}}, "documentation": "<p>The status of the creation or deletion operation of an assignment that a principal needs to access an account.</p>"}, "AccountAssignmentOperationStatusList": {"type": "list", "member": {"shape": "AccountAssignmentOperationStatusMetadata"}}, "AccountAssignmentOperationStatusMetadata": {"type": "structure", "members": {"CreatedDate": {"shape": "Date", "documentation": "<p>The date that the permission set was created.</p>"}, "RequestId": {"shape": "UUId", "documentation": "<p>The identifier for tracking the request operation that is generated by the universally unique identifier (UUID) workflow.</p>"}, "Status": {"shape": "StatusValues", "documentation": "<p>The status of the permission set provisioning process.</p>"}}, "documentation": "<p>Provides information about the <a>AccountAssignment</a> creation request.</p>"}, "AccountId": {"type": "string", "max": 12, "min": 12, "pattern": "^\\d{12}$"}, "AccountList": {"type": "list", "member": {"shape": "AccountId"}}, "AttachCustomerManagedPolicyReferenceToPermissionSetRequest": {"type": "structure", "required": ["CustomerManagedPolicyReference", "InstanceArn", "PermissionSetArn"], "members": {"CustomerManagedPolicyReference": {"shape": "CustomerManagedPolicyReference", "documentation": "<p>Specifies the name and path of a customer managed policy. You must have an IAM policy that matches the name and path in each Amazon Web Services account where you want to deploy your permission set.</p>"}, "InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. </p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the <code>PermissionSet</code>.</p>"}}}, "AttachCustomerManagedPolicyReferenceToPermissionSetResponse": {"type": "structure", "members": {}}, "AttachManagedPolicyToPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "ManagedPolicyArn", "PermissionSetArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "ManagedPolicyArn": {"shape": "ManagedPolicyArn", "documentation": "<p>The Amazon Web Services managed policy ARN to be attached to a permission set.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the <a>PermissionSet</a> that the managed policy should be attached to.</p>"}}}, "AttachManagedPolicyToPermissionSetResponse": {"type": "structure", "members": {}}, "AttachedManagedPolicy": {"type": "structure", "members": {"Arn": {"shape": "ManagedPolicyArn", "documentation": "<p>The ARN of the Amazon Web Services managed policy. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "Name": {"shape": "Name", "documentation": "<p>The name of the Amazon Web Services managed policy.</p>"}}, "documentation": "<p>A structure that stores the details of the Amazon Web Services managed policy.</p>"}, "AttachedManagedPolicyList": {"type": "list", "member": {"shape": "AttachedManagedPolicy"}}, "ConflictException": {"type": "structure", "members": {"Message": {"shape": "ConflictExceptionMessage"}}, "documentation": "<p>Occurs when a conflict with a previous successful write is detected. This generally occurs when the previous write did not have time to propagate to the host serving the current request. A retry (with appropriate backoff logic) is the recommended response to this exception.</p>", "exception": true}, "ConflictExceptionMessage": {"type": "string"}, "CreateAccountAssignmentRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn", "PrincipalId", "PrincipalType", "TargetId", "TargetType"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set that the admin wants to grant the principal access to.</p>"}, "PrincipalId": {"shape": "PrincipalId", "documentation": "<p>An identifier for an object in IAM Identity Center, such as a user or group. PrincipalIds are GUIDs (For example, f81d4fae-7dec-11d0-a765-00a0c91e6bf6). For more information about PrincipalIds in IAM Identity Center, see the <a href=\"/singlesignon/latest/IdentityStoreAPIReference/welcome.html\">IAM Identity Center Identity Store API Reference</a>.</p>"}, "PrincipalType": {"shape": "PrincipalType", "documentation": "<p>The entity type for which the assignment will be created.</p>"}, "TargetId": {"shape": "TargetId", "documentation": "<p>TargetID is an Amazon Web Services account identifier, (For example, ************).</p>"}, "TargetType": {"shape": "TargetType", "documentation": "<p>The entity type for which the assignment will be created.</p>"}}}, "CreateAccountAssignmentResponse": {"type": "structure", "members": {"AccountAssignmentCreationStatus": {"shape": "AccountAssignmentOperationStatus", "documentation": "<p>The status object for the account assignment creation operation.</p>"}}}, "CreateInstanceAccessControlAttributeConfigurationRequest": {"type": "structure", "required": ["InstanceAccessControlAttributeConfiguration", "InstanceArn"], "members": {"InstanceAccessControlAttributeConfiguration": {"shape": "InstanceAccessControlAttributeConfiguration", "documentation": "<p>Specifies the IAM Identity Center identity store attributes to add to your ABAC configuration. When using an external identity provider as an identity source, you can pass attributes through the SAML assertion. Doing so provides an alternative to configuring attributes from the IAM Identity Center identity store. If a SAML assertion passes any of these attributes, IAM Identity Center will replace the attribute value with the value from the IAM Identity Center identity store.</p>"}, "InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed.</p>"}}}, "CreateInstanceAccessControlAttributeConfigurationResponse": {"type": "structure", "members": {}}, "CreatePermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "Name"], "members": {"Description": {"shape": "PermissionSetDescription", "documentation": "<p>The description of the <a>PermissionSet</a>.</p>"}, "InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "Name": {"shape": "PermissionSetName", "documentation": "<p>The name of the <a>PermissionSet</a>.</p>"}, "RelayState": {"shape": "RelayState", "documentation": "<p>Used to redirect users within the application during the federation authentication process.</p>"}, "SessionDuration": {"shape": "Duration", "documentation": "<p>The length of time that the application user sessions are valid in the ISO-8601 standard.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags to attach to the new <a>PermissionSet</a>.</p>"}}}, "CreatePermissionSetResponse": {"type": "structure", "members": {"PermissionSet": {"shape": "PermissionSet", "documentation": "<p>Defines the level of access on an Amazon Web Services account.</p>"}}}, "CustomerManagedPolicyReference": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "ManagedPolicyName", "documentation": "<p>The name of the IAM policy that you have configured in each account where you want to deploy your permission set.</p>"}, "Path": {"shape": "ManagedPolicyPath", "documentation": "<p>The path to the IAM policy that you have configured in each account where you want to deploy your permission set. The default is <code>/</code>. For more information, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_identifiers.html#identifiers-friendly-names\">Friendly names and paths</a> in the <i>IAM User Guide</i>.</p>"}}, "documentation": "<p>Specifies the name and path of a customer managed policy. You must have an IAM policy that matches the name and path in each Amazon Web Services account where you want to deploy your permission set.</p>"}, "CustomerManagedPolicyReferenceList": {"type": "list", "member": {"shape": "CustomerManagedPolicyReference"}}, "Date": {"type": "timestamp"}, "DeleteAccountAssignmentRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn", "PrincipalId", "PrincipalType", "TargetId", "TargetType"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set that will be used to remove access.</p>"}, "PrincipalId": {"shape": "PrincipalId", "documentation": "<p>An identifier for an object in IAM Identity Center, such as a user or group. PrincipalIds are GUIDs (For example, f81d4fae-7dec-11d0-a765-00a0c91e6bf6). For more information about PrincipalIds in IAM Identity Center, see the <a href=\"/singlesignon/latest/IdentityStoreAPIReference/welcome.html\">IAM Identity Center Identity Store API Reference</a>.</p>"}, "PrincipalType": {"shape": "PrincipalType", "documentation": "<p>The entity type for which the assignment will be deleted.</p>"}, "TargetId": {"shape": "TargetId", "documentation": "<p>TargetID is an Amazon Web Services account identifier, (For example, ************).</p>"}, "TargetType": {"shape": "TargetType", "documentation": "<p>The entity type for which the assignment will be deleted.</p>"}}}, "DeleteAccountAssignmentResponse": {"type": "structure", "members": {"AccountAssignmentDeletionStatus": {"shape": "AccountAssignmentOperationStatus", "documentation": "<p>The status object for the account assignment deletion operation.</p>"}}}, "DeleteInlinePolicyFromPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set that will be used to remove access.</p>"}}}, "DeleteInlinePolicyFromPermissionSetResponse": {"type": "structure", "members": {}}, "DeleteInstanceAccessControlAttributeConfigurationRequest": {"type": "structure", "required": ["InstanceArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed.</p>"}}}, "DeleteInstanceAccessControlAttributeConfigurationResponse": {"type": "structure", "members": {}}, "DeletePermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set that should be deleted.</p>"}}}, "DeletePermissionSetResponse": {"type": "structure", "members": {}}, "DeletePermissionsBoundaryFromPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. </p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the <code>PermissionSet</code>.</p>"}}}, "DeletePermissionsBoundaryFromPermissionSetResponse": {"type": "structure", "members": {}}, "DescribeAccountAssignmentCreationStatusRequest": {"type": "structure", "required": ["AccountAssignmentCreationRequestId", "InstanceArn"], "members": {"AccountAssignmentCreationRequestId": {"shape": "UUId", "documentation": "<p>The identifier that is used to track the request operation progress.</p>"}, "InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}}}, "DescribeAccountAssignmentCreationStatusResponse": {"type": "structure", "members": {"AccountAssignmentCreationStatus": {"shape": "AccountAssignmentOperationStatus", "documentation": "<p>The status object for the account assignment creation operation.</p>"}}}, "DescribeAccountAssignmentDeletionStatusRequest": {"type": "structure", "required": ["AccountAssignmentDeletionRequestId", "InstanceArn"], "members": {"AccountAssignmentDeletionRequestId": {"shape": "UUId", "documentation": "<p>The identifier that is used to track the request operation progress.</p>"}, "InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}}}, "DescribeAccountAssignmentDeletionStatusResponse": {"type": "structure", "members": {"AccountAssignmentDeletionStatus": {"shape": "AccountAssignmentOperationStatus", "documentation": "<p>The status object for the account assignment deletion operation.</p>"}}}, "DescribeInstanceAccessControlAttributeConfigurationRequest": {"type": "structure", "required": ["InstanceArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed.</p>"}}}, "DescribeInstanceAccessControlAttributeConfigurationResponse": {"type": "structure", "members": {"InstanceAccessControlAttributeConfiguration": {"shape": "InstanceAccessControlAttributeConfiguration", "documentation": "<p>Gets the list of IAM Identity Center identity store attributes that have been added to your ABAC configuration.</p>"}, "Status": {"shape": "InstanceAccessControlAttributeConfigurationStatus", "documentation": "<p>The status of the attribute configuration process.</p>"}, "StatusReason": {"shape": "InstanceAccessControlAttributeConfigurationStatusReason", "documentation": "<p>Provides more details about the current status of the specified attribute.</p>"}}}, "DescribePermissionSetProvisioningStatusRequest": {"type": "structure", "required": ["InstanceArn", "ProvisionPermissionSetRequestId"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "ProvisionPermissionSetRequestId": {"shape": "UUId", "documentation": "<p>The identifier that is provided by the <a>ProvisionPermissionSet</a> call to retrieve the current status of the provisioning workflow.</p>"}}}, "DescribePermissionSetProvisioningStatusResponse": {"type": "structure", "members": {"PermissionSetProvisioningStatus": {"shape": "PermissionSetProvisioningStatus", "documentation": "<p>The status object for the permission set provisioning operation.</p>"}}}, "DescribePermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set. </p>"}}}, "DescribePermissionSetResponse": {"type": "structure", "members": {"PermissionSet": {"shape": "PermissionSet", "documentation": "<p>Describes the level of access on an Amazon Web Services account.</p>"}}}, "DetachCustomerManagedPolicyReferenceFromPermissionSetRequest": {"type": "structure", "required": ["CustomerManagedPolicyReference", "InstanceArn", "PermissionSetArn"], "members": {"CustomerManagedPolicyReference": {"shape": "CustomerManagedPolicyReference", "documentation": "<p>Specifies the name and path of a customer managed policy. You must have an IAM policy that matches the name and path in each Amazon Web Services account where you want to deploy your permission set.</p>"}, "InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. </p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the <code>PermissionSet</code>.</p>"}}}, "DetachCustomerManagedPolicyReferenceFromPermissionSetResponse": {"type": "structure", "members": {}}, "DetachManagedPolicyFromPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "ManagedPolicyArn", "PermissionSetArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "ManagedPolicyArn": {"shape": "ManagedPolicyArn", "documentation": "<p>The Amazon Web Services managed policy ARN to be detached from a permission set.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the <a>PermissionSet</a> from which the policy should be detached.</p>"}}}, "DetachManagedPolicyFromPermissionSetResponse": {"type": "structure", "members": {}}, "Duration": {"type": "string", "max": 100, "min": 1, "pattern": "^(-?)P(?=\\d|T\\d)(?:(\\d+)Y)?(?:(\\d+)M)?(?:(\\d+)([DW]))?(?:T(?:(\\d+)H)?(?:(\\d+)M)?(?:(\\d+(?:\\.\\d+)?)S)?)?$"}, "GetInlinePolicyForPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set.</p>"}}}, "GetInlinePolicyForPermissionSetResponse": {"type": "structure", "members": {"InlinePolicy": {"shape": "PermissionSetPolicyDocument", "documentation": "<p>The inline policy that is attached to the permission set.</p> <note> <p>For <code>Length Constraints</code>, if a valid ARN is provided for a permission set, it is possible for an empty inline policy to be returned.</p> </note>"}}}, "GetPermissionsBoundaryForPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. </p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the <code>PermissionSet</code>.</p>"}}}, "GetPermissionsBoundaryForPermissionSetResponse": {"type": "structure", "members": {"PermissionsBoundary": {"shape": "PermissionsBoundary", "documentation": "<p>The permissions boundary attached to the specified permission set.</p>"}}}, "Id": {"type": "string", "max": 64, "min": 1, "pattern": "^[a-zA-Z0-9-]*$"}, "InstanceAccessControlAttributeConfiguration": {"type": "structure", "required": ["AccessControlAttributes"], "members": {"AccessControlAttributes": {"shape": "AccessControlAttributeList", "documentation": "<p>Lists the attributes that are configured for ABAC in the specified IAM Identity Center instance.</p>"}}, "documentation": "<p>Specifies the attributes to add to your attribute-based access control (ABAC) configuration.</p>"}, "InstanceAccessControlAttributeConfigurationStatus": {"type": "string", "enum": ["ENABLED", "CREATION_IN_PROGRESS", "CREATION_FAILED"]}, "InstanceAccessControlAttributeConfigurationStatusReason": {"type": "string"}, "InstanceArn": {"type": "string", "max": 1224, "min": 10, "pattern": "^arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso:::instance/(sso)?ins-[a-zA-Z0-9-.]{16}$"}, "InstanceList": {"type": "list", "member": {"shape": "InstanceMetadata"}}, "InstanceMetadata": {"type": "structure", "members": {"IdentityStoreId": {"shape": "Id", "documentation": "<p>The identifier of the identity store that is connected to the IAM Identity Center instance.</p>"}, "InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}}, "documentation": "<p>Provides information about the IAM Identity Center instance.</p>"}, "InternalFailureMessage": {"type": "string"}, "InternalServerException": {"type": "structure", "members": {"Message": {"shape": "InternalFailureMessage"}}, "documentation": "<p>The request processing has failed because of an unknown error, exception, or failure with an internal server.</p>", "exception": true, "fault": true}, "ListAccountAssignmentCreationStatusRequest": {"type": "structure", "required": ["InstanceArn"], "members": {"Filter": {"shape": "OperationStatusFilter", "documentation": "<p>Filters results based on the passed attribute value.</p>"}, "InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to display for the assignment.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListAccountAssignmentCreationStatusResponse": {"type": "structure", "members": {"AccountAssignmentsCreationStatus": {"shape": "AccountAssignmentOperationStatusList", "documentation": "<p>The status object for the account assignment creation operation.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListAccountAssignmentDeletionStatusRequest": {"type": "structure", "required": ["InstanceArn"], "members": {"Filter": {"shape": "OperationStatusFilter", "documentation": "<p>Filters results based on the passed attribute value.</p>"}, "InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to display for the assignment.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListAccountAssignmentDeletionStatusResponse": {"type": "structure", "members": {"AccountAssignmentsDeletionStatus": {"shape": "AccountAssignmentOperationStatusList", "documentation": "<p>The status object for the account assignment deletion operation.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListAccountAssignmentsRequest": {"type": "structure", "required": ["AccountId", "InstanceArn", "PermissionSetArn"], "members": {"AccountId": {"shape": "TargetId", "documentation": "<p>The identifier of the Amazon Web Services account from which to list the assignments.</p>"}, "InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to display for the assignment.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set from which to list assignments.</p>"}}}, "ListAccountAssignmentsResponse": {"type": "structure", "members": {"AccountAssignments": {"shape": "AccountAssignmentList", "documentation": "<p>The list of assignments that match the input Amazon Web Services account and permission set.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListAccountsForProvisionedPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to display for the <a>PermissionSet</a>.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the <a>PermissionSet</a> from which the associated Amazon Web Services accounts will be listed.</p>"}, "ProvisioningStatus": {"shape": "ProvisioningStatus", "documentation": "<p>The permission set provisioning status for an Amazon Web Services account.</p>"}}}, "ListAccountsForProvisionedPermissionSetResponse": {"type": "structure", "members": {"AccountIds": {"shape": "AccountList", "documentation": "<p>The list of Amazon Web Services <code>AccountIds</code>.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListCustomerManagedPolicyReferencesInPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. </p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to display for the list call.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the <code>PermissionSet</code>. </p>"}}}, "ListCustomerManagedPolicyReferencesInPermissionSetResponse": {"type": "structure", "members": {"CustomerManagedPolicyReferences": {"shape": "CustomerManagedPolicyReferenceList", "documentation": "<p>Specifies the names and paths of the customer managed policies that you have attached to your permission set.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListInstancesRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to display for the instance.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListInstancesResponse": {"type": "structure", "members": {"Instances": {"shape": "InstanceList", "documentation": "<p>Lists the IAM Identity Center instances that the caller has access to.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListManagedPoliciesInPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to display for the <a>PermissionSet</a>.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the <a>PermissionSet</a> whose managed policies will be listed.</p>"}}}, "ListManagedPoliciesInPermissionSetResponse": {"type": "structure", "members": {"AttachedManagedPolicies": {"shape": "AttachedManagedPolicyList", "documentation": "<p>An array of the <a>AttachedManagedPolicy</a> data type object.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListPermissionSetProvisioningStatusRequest": {"type": "structure", "required": ["InstanceArn"], "members": {"Filter": {"shape": "OperationStatusFilter", "documentation": "<p>Filters results based on the passed attribute value.</p>"}, "InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to display for the assignment.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListPermissionSetProvisioningStatusResponse": {"type": "structure", "members": {"NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}, "PermissionSetsProvisioningStatus": {"shape": "PermissionSetProvisioningStatusList", "documentation": "<p>The status object for the permission set provisioning operation.</p>"}}}, "ListPermissionSetsProvisionedToAccountRequest": {"type": "structure", "required": ["AccountId", "InstanceArn"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The identifier of the Amazon Web Services account from which to list the assignments.</p>"}, "InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to display for the assignment.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}, "ProvisioningStatus": {"shape": "ProvisioningStatus", "documentation": "<p>The status object for the permission set provisioning operation.</p>"}}}, "ListPermissionSetsProvisionedToAccountResponse": {"type": "structure", "members": {"NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}, "PermissionSets": {"shape": "PermissionSetList", "documentation": "<p>Defines the level of access that an Amazon Web Services account has.</p>"}}}, "ListPermissionSetsRequest": {"type": "structure", "required": ["InstanceArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to display for the assignment.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListPermissionSetsResponse": {"type": "structure", "members": {"NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}, "PermissionSets": {"shape": "PermissionSetList", "documentation": "<p>Defines the level of access on an Amazon Web Services account.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["InstanceArn", "ResourceArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}, "ResourceArn": {"shape": "TaggableResourceArn", "documentation": "<p>The ARN of the resource with the tags to be listed.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>A set of key-value pairs that are used to manage the resource.</p>"}}}, "ManagedPolicyArn": {"type": "string", "max": 2048, "min": 20, "pattern": "^arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):iam::aws:policy/[\\p{L}\\p{M}\\p{Z}\\p{S}\\p{N}\\p{P}]+$"}, "ManagedPolicyName": {"type": "string", "max": 128, "min": 1, "pattern": "^[\\w+=,.@-]+$"}, "ManagedPolicyPath": {"type": "string", "max": 512, "min": 1, "pattern": "^((/[A-Za-z0-9\\.,\\+@=_-]+)*)/$"}, "MaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "Name": {"type": "string", "max": 100, "min": 1}, "OperationStatusFilter": {"type": "structure", "members": {"Status": {"shape": "StatusValues", "documentation": "<p>Filters the list operations result based on the status attribute.</p>"}}, "documentation": "<p>Filters he operation status list based on the passed attribute value.</p>"}, "PermissionSet": {"type": "structure", "members": {"CreatedDate": {"shape": "Date", "documentation": "<p>The date that the permission set was created.</p>"}, "Description": {"shape": "PermissionSetDescription", "documentation": "<p>The description of the <a>PermissionSet</a>.</p>"}, "Name": {"shape": "PermissionSetName", "documentation": "<p>The name of the permission set.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "RelayState": {"shape": "RelayState", "documentation": "<p>Used to redirect users within the application during the federation authentication process.</p>"}, "SessionDuration": {"shape": "Duration", "documentation": "<p>The length of time that the application user sessions are valid for in the ISO-8601 standard.</p>"}}, "documentation": "<p>An entity that contains IAM policies.</p>"}, "PermissionSetArn": {"type": "string", "max": 1224, "min": 10, "pattern": "^arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso:::permissionSet/(sso)?ins-[a-zA-Z0-9-.]{16}/ps-[a-zA-Z0-9-./]{16}$"}, "PermissionSetDescription": {"type": "string", "max": 700, "min": 1, "pattern": "^[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u00A1-\\u00FF]*$"}, "PermissionSetList": {"type": "list", "member": {"shape": "PermissionSetArn"}}, "PermissionSetName": {"type": "string", "max": 32, "min": 1, "pattern": "^[\\w+=,.@-]+$"}, "PermissionSetPolicyDocument": {"type": "string", "max": 32768, "min": 1, "pattern": "^[\\u0009\\u000A\\u000D\\u0020-\\u00FF]+$"}, "PermissionSetProvisioningStatus": {"type": "structure", "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The identifier of the Amazon Web Services account from which to list the assignments.</p>"}, "CreatedDate": {"shape": "Date", "documentation": "<p>The date that the permission set was created.</p>"}, "FailureReason": {"shape": "Reason", "documentation": "<p>The message that contains an error or exception in case of an operation failure.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set that is being provisioned. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "RequestId": {"shape": "UUId", "documentation": "<p>The identifier for tracking the request operation that is generated by the universally unique identifier (UUID) workflow.</p>"}, "Status": {"shape": "StatusValues", "documentation": "<p>The status of the permission set provisioning process.</p>"}}, "documentation": "<p>A structure that is used to provide the status of the provisioning operation for a specified permission set.</p>"}, "PermissionSetProvisioningStatusList": {"type": "list", "member": {"shape": "PermissionSetProvisioningStatusMetadata"}}, "PermissionSetProvisioningStatusMetadata": {"type": "structure", "members": {"CreatedDate": {"shape": "Date", "documentation": "<p>The date that the permission set was created.</p>"}, "RequestId": {"shape": "UUId", "documentation": "<p>The identifier for tracking the request operation that is generated by the universally unique identifier (UUID) workflow.</p>"}, "Status": {"shape": "StatusValues", "documentation": "<p>The status of the permission set provisioning process.</p>"}}, "documentation": "<p>Provides information about the permission set provisioning status.</p>"}, "PermissionsBoundary": {"type": "structure", "members": {"CustomerManagedPolicyReference": {"shape": "CustomerManagedPolicyReference", "documentation": "<p>Specifies the name and path of a customer managed policy. You must have an IAM policy that matches the name and path in each Amazon Web Services account where you want to deploy your permission set.</p>"}, "ManagedPolicyArn": {"shape": "ManagedPolicyArn", "documentation": "<p>The Amazon Web Services managed policy ARN that you want to attach to a permission set as a permissions boundary.</p>"}}, "documentation": "<p>Specifies the configuration of the Amazon Web Services managed or customer managed policy that you want to set as a permissions boundary. Specify either <code>CustomerManagedPolicyReference</code> to use the name and path of a customer managed policy, or <code>ManagedPolicyArn</code> to use the ARN of an Amazon Web Services managed policy. A permissions boundary represents the maximum permissions that any policy can grant your role. For more information, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access_policies_boundaries.html\">Permissions boundaries for IAM entities</a> in the <i>IAM User Guide</i>.</p> <important> <p>Policies used as permissions boundaries don't provide permissions. You must also attach an IAM policy to the role. To learn how the effective permissions for a role are evaluated, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_policies_evaluation-logic.html\">IAM JSON policy evaluation logic</a> in the <i>IAM User Guide</i>.</p> </important>"}, "PrincipalId": {"type": "string", "max": 47, "min": 1, "pattern": "^([0-9a-f]{10}-|)[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}$"}, "PrincipalType": {"type": "string", "enum": ["USER", "GROUP"]}, "ProvisionPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn", "TargetType"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set.</p>"}, "TargetId": {"shape": "TargetId", "documentation": "<p>TargetID is an Amazon Web Services account identifier, (For example, ************).</p>"}, "TargetType": {"shape": "ProvisionTargetType", "documentation": "<p>The entity type for which the assignment will be created.</p>"}}}, "ProvisionPermissionSetResponse": {"type": "structure", "members": {"PermissionSetProvisioningStatus": {"shape": "PermissionSetProvisioningStatus", "documentation": "<p>The status object for the permission set provisioning operation.</p>"}}}, "ProvisionTargetType": {"type": "string", "enum": ["AWS_ACCOUNT", "ALL_PROVISIONED_ACCOUNTS"]}, "ProvisioningStatus": {"type": "string", "enum": ["LATEST_PERMISSION_SET_PROVISIONED", "LATEST_PERMISSION_SET_NOT_PROVISIONED"]}, "PutInlinePolicyToPermissionSetRequest": {"type": "structure", "required": ["InlinePolicy", "InstanceArn", "PermissionSetArn"], "members": {"InlinePolicy": {"shape": "PermissionSetPolicyDocument", "documentation": "<p>The inline policy to attach to a <a>PermissionSet</a>.</p>"}, "InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set.</p>"}}}, "PutInlinePolicyToPermissionSetResponse": {"type": "structure", "members": {}}, "PutPermissionsBoundaryToPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn", "PermissionsBoundary"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. </p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the <code>PermissionSet</code>.</p>"}, "PermissionsBoundary": {"shape": "PermissionsBoundary", "documentation": "<p>The permissions boundary that you want to attach to a <code>PermissionSet</code>.</p>"}}}, "PutPermissionsBoundaryToPermissionSetResponse": {"type": "structure", "members": {}}, "Reason": {"type": "string", "pattern": "^[\\p{L}\\p{M}\\p{Z}\\p{S}\\p{N}\\p{P}]*$"}, "RelayState": {"type": "string", "max": 240, "min": 1, "pattern": "^[a-zA-Z0-9&$@#\\\\\\/%?=~\\-_'\"|!:,.;*+\\[\\]\\ \\(\\)\\{\\}]+$"}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ResourceNotFoundMessage"}}, "documentation": "<p>Indicates that a requested resource is not found.</p>", "exception": true}, "ResourceNotFoundMessage": {"type": "string"}, "ServiceQuotaExceededException": {"type": "structure", "members": {"Message": {"shape": "ServiceQuotaExceededMessage"}}, "documentation": "<p>Indicates that the principal has crossed the permitted number of resources that can be created.</p>", "exception": true}, "ServiceQuotaExceededMessage": {"type": "string"}, "StatusValues": {"type": "string", "enum": ["IN_PROGRESS", "FAILED", "SUCCEEDED"]}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>The key for the tag.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The value of the tag.</p>"}}, "documentation": "<p>A set of key-value pairs that are used to manage the resource. Tags can only be applied to permission sets and cannot be applied to corresponding roles that IAM Identity Center creates in Amazon Web Services accounts.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 1}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "max": 50, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["InstanceArn", "ResourceArn", "Tags"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "ResourceArn": {"shape": "TaggableResourceArn", "documentation": "<p>The ARN of the resource with the tags to be listed.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>A set of key-value pairs that are used to manage the resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "TaggableResourceArn": {"type": "string", "max": 2048, "min": 10, "pattern": "^arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso:::permissionSet/(sso)?ins-[a-zA-Z0-9-.]{16}/ps-[a-zA-Z0-9-./]{16}$"}, "TargetId": {"type": "string", "max": 12, "min": 12, "pattern": "^\\d{12}$"}, "TargetType": {"type": "string", "enum": ["AWS_ACCOUNT"]}, "ThrottlingException": {"type": "structure", "members": {"Message": {"shape": "ThrottlingExceptionMessage"}}, "documentation": "<p>Indicates that the principal has crossed the throttling limits of the API operations.</p>", "exception": true}, "ThrottlingExceptionMessage": {"type": "string"}, "Token": {"type": "string", "max": 2048, "min": 0, "pattern": "^[-a-zA-Z0-9+=/_]*$"}, "UUId": {"type": "string", "max": 36, "min": 36, "pattern": "^\\b[0-9a-f]{8}\\b-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-\\b[0-9a-f]{12}\\b$"}, "UntagResourceRequest": {"type": "structure", "required": ["InstanceArn", "ResourceArn", "TagKeys"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "ResourceArn": {"shape": "TaggableResourceArn", "documentation": "<p>The ARN of the resource with the tags to be listed.</p>"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>The keys of tags that are attached to the resource.</p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateInstanceAccessControlAttributeConfigurationRequest": {"type": "structure", "required": ["InstanceAccessControlAttributeConfiguration", "InstanceArn"], "members": {"InstanceAccessControlAttributeConfiguration": {"shape": "InstanceAccessControlAttributeConfiguration", "documentation": "<p>Updates the attributes for your ABAC configuration.</p>"}, "InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed.</p>"}}}, "UpdateInstanceAccessControlAttributeConfigurationResponse": {"type": "structure", "members": {}}, "UpdatePermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"Description": {"shape": "PermissionSetDescription", "documentation": "<p>The description of the <a>PermissionSet</a>.</p>"}, "InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set.</p>"}, "RelayState": {"shape": "RelayState", "documentation": "<p>Used to redirect users within the application during the federation authentication process.</p>"}, "SessionDuration": {"shape": "Duration", "documentation": "<p>The length of time that the application user sessions are valid for in the ISO-8601 standard.</p>"}}}, "UpdatePermissionSetResponse": {"type": "structure", "members": {}}, "ValidationException": {"type": "structure", "members": {"Message": {"shape": "ValidationExceptionMessage"}}, "documentation": "<p>The request failed because it contains a syntax error.</p>", "exception": true}, "ValidationExceptionMessage": {"type": "string"}}, "documentation": "<p>IAM Identity Center (successor to Single Sign-On) helps you securely create, or connect, your workforce identities and manage their access centrally across Amazon Web Services accounts and applications. IAM Identity Center is the recommended approach for workforce authentication and authorization in Amazon Web Services, for organizations of any size and type.</p> <note> <p>IAM Identity Center uses the <code>sso</code> and <code>identitystore</code> API namespaces.</p> </note> <p>This reference guide provides information on single sign-on operations which could be used for access management of Amazon Web Services accounts. For information about IAM Identity Center features, see the <a href=\"https://docs.aws.amazon.com/singlesignon/latest/userguide/what-is.html\">IAM Identity Center User Guide</a>.</p> <p>Many operations in the IAM Identity Center APIs rely on identifiers for users and groups, known as principals. For more information about how to work with principals and principal IDs in IAM Identity Center, see the <a href=\"https://docs.aws.amazon.com/singlesignon/latest/IdentityStoreAPIReference/welcome.html\">Identity Store API Reference</a>.</p> <note> <p>Amazon Web Services provides SDKs that consist of libraries and sample code for various programming languages and platforms (Java, Ruby, .Net, iOS, Android, and more). The SDKs provide a convenient way to create programmatic access to IAM Identity Center and other Amazon Web Services services. For more information about the Amazon Web Services SDKs, including how to download and install them, see <a href=\"http://aws.amazon.com/tools/\">Tools for Amazon Web Services</a>.</p> </note>"}