{"version": "2.0", "metadata": {"apiVersion": "2018-05-10", "endpointPrefix": "license-manager-linux-subscriptions", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "AWS License Manager Linux Subscriptions", "serviceId": "License Manager Linux Subscriptions", "signatureVersion": "v4", "signingName": "license-manager-linux-subscriptions", "uid": "license-manager-linux-subscriptions-2018-05-10"}, "operations": {"GetServiceSettings": {"name": "GetServiceSettings", "http": {"method": "POST", "requestUri": "/subscription/GetServiceSettings", "responseCode": 200}, "input": {"shape": "GetServiceSettingsRequest"}, "output": {"shape": "GetServiceSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the Linux subscriptions service settings.</p>", "idempotent": true}, "ListLinuxSubscriptionInstances": {"name": "ListLinuxSubscriptionInstances", "http": {"method": "POST", "requestUri": "/subscription/ListLinuxSubscriptionInstances", "responseCode": 200}, "input": {"shape": "ListLinuxSubscriptionInstancesRequest"}, "output": {"shape": "ListLinuxSubscriptionInstancesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the running Amazon EC2 instances that were discovered with commercial Linux subscriptions.</p>", "idempotent": true}, "ListLinuxSubscriptions": {"name": "ListLinuxSubscriptions", "http": {"method": "POST", "requestUri": "/subscription/ListLinuxSubscriptions", "responseCode": 200}, "input": {"shape": "ListLinuxSubscriptionsRequest"}, "output": {"shape": "ListLinuxSubscriptionsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the Linux subscriptions that have been discovered. If you have linked your organization, the returned results will include data aggregated across your accounts in Organizations.</p>", "idempotent": true}, "UpdateServiceSettings": {"name": "UpdateServiceSettings", "http": {"method": "POST", "requestUri": "/subscription/UpdateServiceSettings", "responseCode": 200}, "input": {"shape": "UpdateServiceSettingsRequest"}, "output": {"shape": "UpdateServiceSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Updates the service settings for Linux subscriptions.</p>", "idempotent": true}}, "shapes": {"Boolean": {"type": "boolean", "box": true}, "BoxInteger": {"type": "integer", "box": true}, "BoxLong": {"type": "long", "box": true}, "Filter": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>The type of name to filter by.</p>"}, "Operator": {"shape": "Operator", "documentation": "<p>An operator for filtering results.</p>"}, "Values": {"shape": "StringList", "documentation": "<p>One or more values for the name to filter by.</p>"}}, "documentation": "<p>A filter object that is used to return more specific results from a describe operation. Filters can be used to match a set of resources by specific criteria.</p>"}, "FilterList": {"type": "list", "member": {"shape": "Filter"}}, "GetServiceSettingsRequest": {"type": "structure", "members": {}}, "GetServiceSettingsResponse": {"type": "structure", "members": {"HomeRegions": {"shape": "StringList", "documentation": "<p>The Region in which License Manager displays the aggregated data for Linux subscriptions.</p>"}, "LinuxSubscriptionsDiscovery": {"shape": "LinuxSubscriptionsDiscovery", "documentation": "<p>Lists if discovery has been enabled for Linux subscriptions.</p>"}, "LinuxSubscriptionsDiscoverySettings": {"shape": "LinuxSubscriptionsDiscoverySettings", "documentation": "<p>Lists the settings defined for Linux subscriptions discovery. The settings include if Organizations integration has been enabled, and which Regions data will be aggregated from.</p>"}, "Status": {"shape": "Status", "documentation": "<p>Indicates the status of Linux subscriptions settings being applied.</p>"}, "StatusMessage": {"shape": "StringMap", "documentation": "<p>A message which details the Linux subscriptions service settings current status.</p>"}}}, "Instance": {"type": "structure", "members": {"AccountID": {"shape": "String", "documentation": "<p>The account ID which owns the instance.</p>"}, "AmiId": {"shape": "String", "documentation": "<p>The AMI ID used to launch the instance.</p>"}, "InstanceID": {"shape": "String", "documentation": "<p>The instance ID of the resource.</p>"}, "InstanceType": {"shape": "String", "documentation": "<p>The instance type of the resource.</p>"}, "LastUpdatedTime": {"shape": "String", "documentation": "<p>The time in which the last discovery updated the instance details.</p>"}, "ProductCode": {"shape": "ProductCodeList", "documentation": "<p>The product code for the instance. For more information, see <a href=\"https://docs.aws.amazon.com/license-manager/latest/userguide/linux-subscriptions-usage-operation.html\">Usage operation values</a> in the <i>License Manager User Guide</i> .</p>"}, "Region": {"shape": "String", "documentation": "<p>The Region the instance is running in.</p>"}, "Status": {"shape": "String", "documentation": "<p>The status of the instance.</p>"}, "SubscriptionName": {"shape": "String", "documentation": "<p>The name of the subscription being used by the instance.</p>"}, "UsageOperation": {"shape": "String", "documentation": "<p>The usage operation of the instance. For more information, see For more information, see <a href=\"https://docs.aws.amazon.com/license-manager/latest/userguide/linux-subscriptions-usage-operation.html\">Usage operation values</a> in the <i>License Manager User Guide</i>.</p>"}}, "documentation": "<p>Details discovered information about a running instance using Linux subscriptions.</p>"}, "InstanceList": {"type": "list", "member": {"shape": "Instance"}}, "InternalServerException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>An exception occurred with the service.</p>", "exception": true, "fault": true}, "LinuxSubscriptionsDiscovery": {"type": "string", "enum": ["Enabled", "Disabled"]}, "LinuxSubscriptionsDiscoverySettings": {"type": "structure", "required": ["OrganizationIntegration", "SourceRegions"], "members": {"OrganizationIntegration": {"shape": "OrganizationIntegration", "documentation": "<p>Details if you have enabled resource discovery across your accounts in Organizations.</p>"}, "SourceRegions": {"shape": "StringList", "documentation": "<p>The Regions in which to discover data for Linux subscriptions.</p>"}}, "documentation": "<p>Lists the settings defined for discovering Linux subscriptions.</p>"}, "ListLinuxSubscriptionInstancesRequest": {"type": "structure", "members": {"Filters": {"shape": "FilterList", "documentation": "<p>An array of structures that you can use to filter the results to those that match one or more sets of key-value pairs that you specify. For example, you can filter by the name of <code>AmiID</code> with an optional operator to see subscriptions that match, partially match, or don't match a certain Amazon Machine Image (AMI) ID.</p> <p>The valid names for this filter are:</p> <ul> <li> <p> <code>AmiID</code> </p> </li> <li> <p> <code>InstanceID</code> </p> </li> <li> <p> <code>AccountID</code> </p> </li> <li> <p> <code>Status</code> </p> </li> <li> <p> <code>Region</code> </p> </li> <li> <p> <code>UsageOperation</code> </p> </li> <li> <p> <code>ProductCode</code> </p> </li> <li> <p> <code>InstanceType</code> </p> </li> </ul> <p>The valid Operators for this filter are:</p> <ul> <li> <p> <code>contains</code> </p> </li> <li> <p> <code>equals</code> </p> </li> <li> <p> <code>Notequal</code> </p> </li> </ul>"}, "MaxResults": {"shape": "BoxInteger", "documentation": "<p>Maximum number of results to return in a single call.</p>"}, "NextToken": {"shape": "ListLinuxSubscriptionInstancesRequestNextTokenString", "documentation": "<p>Token for the next set of results.</p>"}}, "documentation": "<p>NextToken length limit is half of ddb accepted limit. Increase this limit if parameters in request increases.</p>"}, "ListLinuxSubscriptionInstancesRequestNextTokenString": {"type": "string", "max": 16384, "min": 1}, "ListLinuxSubscriptionInstancesResponse": {"type": "structure", "members": {"Instances": {"shape": "InstanceList", "documentation": "<p>An array that contains instance objects.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>Token for the next set of results.</p>"}}}, "ListLinuxSubscriptionsRequest": {"type": "structure", "members": {"Filters": {"shape": "FilterList", "documentation": "<p>An array of structures that you can use to filter the results to those that match one or more sets of key-value pairs that you specify. For example, you can filter by the name of <code>Subscription</code> with an optional operator to see subscriptions that match, partially match, or don't match a certain subscription's name.</p> <p>The valid names for this filter are:</p> <ul> <li> <p> <code>Subscription</code> </p> </li> </ul> <p>The valid Operators for this filter are:</p> <ul> <li> <p> <code>contains</code> </p> </li> <li> <p> <code>equals</code> </p> </li> <li> <p> <code>Notequal</code> </p> </li> </ul>"}, "MaxResults": {"shape": "BoxInteger", "documentation": "<p>Maximum number of results to return in a single call.</p>"}, "NextToken": {"shape": "ListLinuxSubscriptionsRequestNextTokenString", "documentation": "<p>Token for the next set of results.</p>"}}, "documentation": "<p>NextToken length limit is half of ddb accepted limit. Increase this limit if parameters in request increases.</p>"}, "ListLinuxSubscriptionsRequestNextTokenString": {"type": "string", "max": 16384, "min": 1}, "ListLinuxSubscriptionsResponse": {"type": "structure", "members": {"NextToken": {"shape": "String", "documentation": "<p>Token for the next set of results.</p>"}, "Subscriptions": {"shape": "SubscriptionList", "documentation": "<p>An array that contains subscription objects.</p>"}}}, "Operator": {"type": "string", "enum": ["Equal", "NotEqual", "Contains"], "max": 20, "min": 1}, "OrganizationIntegration": {"type": "string", "enum": ["Enabled", "Disabled"]}, "ProductCodeList": {"type": "list", "member": {"shape": "String"}}, "Status": {"type": "string", "enum": ["InProgress", "Completed", "Successful", "Failed"]}, "String": {"type": "string"}, "StringList": {"type": "list", "member": {"shape": "StringListMemberString"}, "max": 100, "min": 1}, "StringListMemberString": {"type": "string", "max": 100, "min": 1}, "StringMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "Subscription": {"type": "structure", "members": {"InstanceCount": {"shape": "BoxLong", "documentation": "<p>The total amount of running instances using this subscription.</p>"}, "Name": {"shape": "String", "documentation": "<p>The name of the subscription.</p>"}, "Type": {"shape": "String", "documentation": "<p>The type of subscription. The type can be subscription-included with Amazon EC2, Bring Your Own Subscription model (BYOS), or from the Amazon Web Services Marketplace. Certain subscriptions may use licensing from the Amazon Web Services Marketplace as well as OS licensing from Amazon EC2 or BYOS.</p>"}}, "documentation": "<p>An object which details a discovered Linux subscription.</p>"}, "SubscriptionList": {"type": "list", "member": {"shape": "Subscription"}}, "ThrottlingException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The request was denied due to request throttling.</p>", "exception": true}, "UpdateServiceSettingsRequest": {"type": "structure", "required": ["LinuxSubscriptionsDiscovery", "LinuxSubscriptionsDiscoverySettings"], "members": {"AllowUpdate": {"shape": "Boolean", "documentation": "<p>Describes if updates are allowed to the service settings for Linux subscriptions. If you allow updates, you can aggregate Linux subscription data in more than one home Region.</p>"}, "LinuxSubscriptionsDiscovery": {"shape": "LinuxSubscriptionsDiscovery", "documentation": "<p>Describes if the discovery of Linux subscriptions is enabled.</p>"}, "LinuxSubscriptionsDiscoverySettings": {"shape": "LinuxSubscriptionsDiscoverySettings", "documentation": "<p>The settings defined for Linux subscriptions discovery. The settings include if Organizations integration has been enabled, and which Regions data will be aggregated from.</p>"}}}, "UpdateServiceSettingsResponse": {"type": "structure", "members": {"HomeRegions": {"shape": "StringList", "documentation": "<p>The Region in which License Manager displays the aggregated data for Linux subscriptions.</p>"}, "LinuxSubscriptionsDiscovery": {"shape": "LinuxSubscriptionsDiscovery", "documentation": "<p>Lists if discovery has been enabled for Linux subscriptions.</p>"}, "LinuxSubscriptionsDiscoverySettings": {"shape": "LinuxSubscriptionsDiscoverySettings", "documentation": "<p>The settings defined for Linux subscriptions discovery. The settings include if Organizations integration has been enabled, and which Regions data will be aggregated from.</p>"}, "Status": {"shape": "Status", "documentation": "<p>Indicates the status of Linux subscriptions settings being applied.</p>"}, "StatusMessage": {"shape": "StringMap", "documentation": "<p>A message which details the Linux subscriptions service settings current status.</p>"}}}, "ValidationException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The provided input is not valid. Try your request again.</p>", "exception": true}}, "documentation": "<p>With License Manager, you can discover and track your commercial Linux subscriptions on running Amazon EC2 instances.</p>"}