{"version": "2.0", "metadata": {"apiVersion": "2018-05-10", "endpointPrefix": "license-manager-user-subscriptions", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "AWS License Manager User Subscriptions", "serviceId": "License Manager User Subscriptions", "signatureVersion": "v4", "signingName": "license-manager-user-subscriptions", "uid": "license-manager-user-subscriptions-2018-05-10"}, "operations": {"AssociateUser": {"name": "AssociateUser", "http": {"method": "POST", "requestUri": "/user/AssociateUser", "responseCode": 200}, "input": {"shape": "AssociateUserRequest"}, "output": {"shape": "AssociateUserResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Associates the user to an EC2 instance to utilize user-based subscriptions.</p> <note> <p>Your estimated bill for charges on the number of users and related costs will take 48 hours to appear for billing periods that haven't closed (marked as <b>Pending</b> billing status) in Amazon Web Services Billing. For more information, see <a href=\"https://docs.aws.amazon.com/awsaccountbilling/latest/aboutv2/invoice.html\">Viewing your monthly charges</a> in the <i>Amazon Web Services Billing User Guide</i>.</p> </note>", "idempotent": true}, "DeregisterIdentityProvider": {"name": "DeregisterIdentityProvider", "http": {"method": "POST", "requestUri": "/identity-provider/DeregisterIdentityProvider", "responseCode": 200}, "input": {"shape": "DeregisterIdentityProviderRequest"}, "output": {"shape": "DeregisterIdentityProviderResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deregisters the identity provider from providing user-based subscriptions.</p>", "idempotent": true}, "DisassociateUser": {"name": "DisassociateUser", "http": {"method": "POST", "requestUri": "/user/DisassociateUser", "responseCode": 200}, "input": {"shape": "DisassociateUserRequest"}, "output": {"shape": "DisassociateUserResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Disassociates the user from an EC2 instance providing user-based subscriptions.</p>", "idempotent": true}, "ListIdentityProviders": {"name": "ListIdentityProviders", "http": {"method": "POST", "requestUri": "/identity-provider/ListIdentityProviders", "responseCode": 200}, "input": {"shape": "ListIdentityProvidersRequest"}, "output": {"shape": "ListIdentityProvidersResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the identity providers for user-based subscriptions.</p>"}, "ListInstances": {"name": "ListInstances", "http": {"method": "POST", "requestUri": "/instance/ListInstances", "responseCode": 200}, "input": {"shape": "ListInstancesRequest"}, "output": {"shape": "ListInstancesResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the EC2 instances providing user-based subscriptions.</p>"}, "ListProductSubscriptions": {"name": "ListProductSubscriptions", "http": {"method": "POST", "requestUri": "/user/ListProductSubscriptions", "responseCode": 200}, "input": {"shape": "ListProductSubscriptionsRequest"}, "output": {"shape": "ListProductSubscriptionsResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the user-based subscription products available from an identity provider.</p>"}, "ListUserAssociations": {"name": "ListUserAssociations", "http": {"method": "POST", "requestUri": "/user/ListUserAssociations", "responseCode": 200}, "input": {"shape": "ListUserAssociationsRequest"}, "output": {"shape": "ListUserAssociationsResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists user associations for an identity provider.</p>"}, "RegisterIdentityProvider": {"name": "RegisterIdentityProvider", "http": {"method": "POST", "requestUri": "/identity-provider/RegisterIdentityProvider", "responseCode": 200}, "input": {"shape": "RegisterIdentityProviderRequest"}, "output": {"shape": "RegisterIdentityProviderResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Registers an identity provider for user-based subscriptions.</p>", "idempotent": true}, "StartProductSubscription": {"name": "StartProductSubscription", "http": {"method": "POST", "requestUri": "/user/StartProductSubscription", "responseCode": 200}, "input": {"shape": "StartProductSubscriptionRequest"}, "output": {"shape": "StartProductSubscriptionResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Starts a product subscription for a user with the specified identity provider.</p> <note> <p>Your estimated bill for charges on the number of users and related costs will take 48 hours to appear for billing periods that haven't closed (marked as <b>Pending</b> billing status) in Amazon Web Services Billing. For more information, see <a href=\"https://docs.aws.amazon.com/awsaccountbilling/latest/aboutv2/invoice.html\">Viewing your monthly charges</a> in the <i>Amazon Web Services Billing User Guide</i>.</p> </note>"}, "StopProductSubscription": {"name": "StopProductSubscription", "http": {"method": "POST", "requestUri": "/user/StopProductSubscription", "responseCode": 200}, "input": {"shape": "StopProductSubscriptionRequest"}, "output": {"shape": "StopProductSubscriptionResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Stops a product subscription for a user with the specified identity provider.</p>"}, "UpdateIdentityProviderSettings": {"name": "UpdateIdentityProviderSettings", "http": {"method": "POST", "requestUri": "/identity-provider/UpdateIdentityProviderSettings", "responseCode": 200}, "input": {"shape": "UpdateIdentityProviderSettingsRequest"}, "output": {"shape": "UpdateIdentityProviderSettingsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates additional product configuration settings for the registered identity provider.</p>", "idempotent": true}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>You don't have sufficient access to perform this action.</p>", "exception": true}, "ActiveDirectoryIdentityProvider": {"type": "structure", "members": {"DirectoryId": {"shape": "String", "documentation": "<p>The directory ID for an Active Directory identity provider.</p>"}}, "documentation": "<p>Details about an Active Directory identity provider.</p>"}, "AssociateUserRequest": {"type": "structure", "required": ["IdentityProvider", "InstanceId", "Username"], "members": {"Domain": {"shape": "String", "documentation": "<p>The domain name of the user.</p>"}, "IdentityProvider": {"shape": "IdentityProvider", "documentation": "<p>The identity provider of the user.</p>"}, "InstanceId": {"shape": "String", "documentation": "<p>The ID of the EC2 instance, which provides user-based subscriptions.</p>"}, "Username": {"shape": "String", "documentation": "<p>The user name from the identity provider for the user.</p>"}}}, "AssociateUserResponse": {"type": "structure", "required": ["InstanceUserSummary"], "members": {"InstanceUserSummary": {"shape": "InstanceUserSummary", "documentation": "<p>Metadata that describes the associate user operation.</p>"}}}, "BoxInteger": {"type": "integer", "box": true}, "ConflictException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The request couldn't be completed because it conflicted with the current state of the resource.</p>", "exception": true, "fault": true}, "DeregisterIdentityProviderRequest": {"type": "structure", "required": ["IdentityProvider", "Product"], "members": {"IdentityProvider": {"shape": "IdentityProvider", "documentation": "<p>An object that specifies details for the identity provider.</p>"}, "Product": {"shape": "String", "documentation": "<p>The name of the user-based subscription product.</p>"}}}, "DeregisterIdentityProviderResponse": {"type": "structure", "required": ["IdentityProviderSummary"], "members": {"IdentityProviderSummary": {"shape": "IdentityProviderSummary", "documentation": "<p>Metadata that describes the results of an identity provider operation.</p>"}}}, "DisassociateUserRequest": {"type": "structure", "required": ["IdentityProvider", "InstanceId", "Username"], "members": {"Domain": {"shape": "String", "documentation": "<p>The domain name of the user.</p>"}, "IdentityProvider": {"shape": "IdentityProvider", "documentation": "<p>An object that specifies details for the identity provider.</p>"}, "InstanceId": {"shape": "String", "documentation": "<p>The ID of the EC2 instance, which provides user-based subscriptions.</p>"}, "Username": {"shape": "String", "documentation": "<p>The user name from the identity provider for the user.</p>"}}}, "DisassociateUserResponse": {"type": "structure", "required": ["InstanceUserSummary"], "members": {"InstanceUserSummary": {"shape": "InstanceUserSummary", "documentation": "<p>Metadata that describes the associate user operation.</p>"}}}, "Filter": {"type": "structure", "members": {"Attribute": {"shape": "String", "documentation": "<p>The name of an attribute to use as a filter.</p>"}, "Operation": {"shape": "String", "documentation": "<p>The type of search (For example, eq, geq, leq)</p>"}, "Value": {"shape": "String", "documentation": "<p>Value of the filter.</p>"}}, "documentation": "<p>A filter name and value pair that is used to return more specific results from a describe operation. Filters can be used to match a set of resources by specific criteria, such as tags, attributes, or IDs.</p>"}, "FilterList": {"type": "list", "member": {"shape": "Filter"}}, "IdentityProvider": {"type": "structure", "members": {"ActiveDirectoryIdentityProvider": {"shape": "ActiveDirectoryIdentityProvider", "documentation": "<p>An object that details an Active Directory identity provider.</p>"}}, "documentation": "<p>Details about an identity provider.</p>", "union": true}, "IdentityProviderSummary": {"type": "structure", "required": ["IdentityProvider", "Product", "Settings", "Status"], "members": {"FailureMessage": {"shape": "String", "documentation": "<p>The failure message associated with an identity provider.</p>"}, "IdentityProvider": {"shape": "IdentityProvider", "documentation": "<p>An object that specifies details for the identity provider.</p>"}, "Product": {"shape": "String", "documentation": "<p>The name of the user-based subscription product.</p>"}, "Settings": {"shape": "Settings", "documentation": "<p>An object that details the registered identity provider’s product related configuration settings such as the subnets to provision VPC endpoints.</p>"}, "Status": {"shape": "String", "documentation": "<p>The status of an identity provider.</p>"}}, "documentation": "<p>Describes an identity provider.</p>"}, "IdentityProviderSummaryList": {"type": "list", "member": {"shape": "IdentityProviderSummary"}}, "InstanceSummary": {"type": "structure", "required": ["InstanceId", "Products", "Status"], "members": {"InstanceId": {"shape": "String", "documentation": "<p>The ID of the EC2 instance, which provides user-based subscriptions.</p>"}, "LastStatusCheckDate": {"shape": "String", "documentation": "<p>The date of the last status check.</p>"}, "Products": {"shape": "StringList", "documentation": "<p>A list of provided user-based subscription products.</p>"}, "Status": {"shape": "String", "documentation": "<p>The status of an EC2 instance resource.</p>"}, "StatusMessage": {"shape": "String", "documentation": "<p>The status message for an EC2 instance.</p>"}}, "documentation": "<p>Describes an EC2 instance providing user-based subscriptions.</p>"}, "InstanceSummaryList": {"type": "list", "member": {"shape": "Instance<PERSON><PERSON><PERSON><PERSON>"}}, "InstanceUserSummary": {"type": "structure", "required": ["IdentityProvider", "InstanceId", "Status", "Username"], "members": {"AssociationDate": {"shape": "String", "documentation": "<p>The date a user was associated with an EC2 instance.</p>"}, "DisassociationDate": {"shape": "String", "documentation": "<p>The date a user was disassociated from an EC2 instance.</p>"}, "Domain": {"shape": "String", "documentation": "<p>The domain name of the user.</p>"}, "IdentityProvider": {"shape": "IdentityProvider", "documentation": "<p>An object that specifies details for the identity provider.</p>"}, "InstanceId": {"shape": "String", "documentation": "<p>The ID of the EC2 instance, which provides user-based subscriptions.</p>"}, "Status": {"shape": "String", "documentation": "<p>The status of a user associated with an EC2 instance.</p>"}, "StatusMessage": {"shape": "String", "documentation": "<p>The status message for users of an EC2 instance.</p>"}, "Username": {"shape": "String", "documentation": "<p>The user name from the identity provider for the user.</p>"}}, "documentation": "<p>Describes users of an EC2 instance providing user-based subscriptions.</p>"}, "InstanceUserSummaryList": {"type": "list", "member": {"shape": "InstanceUserSummary"}}, "InternalServerException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>An exception occurred with the service.</p>", "exception": true, "fault": true}, "ListIdentityProvidersRequest": {"type": "structure", "members": {"MaxResults": {"shape": "BoxInteger", "documentation": "<p>Maximum number of results to return in a single call.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>Token for the next set of results.</p>"}}}, "ListIdentityProvidersResponse": {"type": "structure", "required": ["IdentityProviderSummaries"], "members": {"IdentityProviderSummaries": {"shape": "IdentityProviderSummaryList", "documentation": "<p>Metadata that describes the list identity providers operation.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>Token for the next set of results.</p>"}}}, "ListInstancesRequest": {"type": "structure", "members": {"Filters": {"shape": "FilterList", "documentation": "<p>An array of structures that you can use to filter the results to those that match one or more sets of key-value pairs that you specify.</p>"}, "MaxResults": {"shape": "BoxInteger", "documentation": "<p>Maximum number of results to return in a single call.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>Token for the next set of results.</p>"}}}, "ListInstancesResponse": {"type": "structure", "members": {"InstanceSummaries": {"shape": "InstanceSummaryList", "documentation": "<p>Metadata that describes the list instances operation.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>Token for the next set of results.</p>"}}}, "ListProductSubscriptionsRequest": {"type": "structure", "required": ["IdentityProvider", "Product"], "members": {"Filters": {"shape": "FilterList", "documentation": "<p>An array of structures that you can use to filter the results to those that match one or more sets of key-value pairs that you specify.</p>"}, "IdentityProvider": {"shape": "IdentityProvider", "documentation": "<p>An object that specifies details for the identity provider.</p>"}, "MaxResults": {"shape": "BoxInteger", "documentation": "<p>Maximum number of results to return in a single call.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>Token for the next set of results.</p>"}, "Product": {"shape": "String", "documentation": "<p>The name of the user-based subscription product.</p>"}}}, "ListProductSubscriptionsResponse": {"type": "structure", "members": {"NextToken": {"shape": "String", "documentation": "<p>Token for the next set of results.</p>"}, "ProductUserSummaries": {"shape": "ProductUserSummaryList", "documentation": "<p>Metadata that describes the list product subscriptions operation.</p>"}}}, "ListUserAssociationsRequest": {"type": "structure", "required": ["IdentityProvider", "InstanceId"], "members": {"Filters": {"shape": "FilterList", "documentation": "<p>An array of structures that you can use to filter the results to those that match one or more sets of key-value pairs that you specify.</p>"}, "IdentityProvider": {"shape": "IdentityProvider", "documentation": "<p>An object that specifies details for the identity provider.</p>"}, "InstanceId": {"shape": "String", "documentation": "<p>The ID of the EC2 instance, which provides user-based subscriptions.</p>"}, "MaxResults": {"shape": "BoxInteger", "documentation": "<p>Maximum number of results to return in a single call.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>Token for the next set of results.</p>"}}}, "ListUserAssociationsResponse": {"type": "structure", "members": {"InstanceUserSummaries": {"shape": "InstanceUserSummaryList", "documentation": "<p>Metadata that describes the list user association operation.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>Token for the next set of results.</p>"}}}, "ProductUserSummary": {"type": "structure", "required": ["IdentityProvider", "Product", "Status", "Username"], "members": {"Domain": {"shape": "String", "documentation": "<p>The domain name of the user.</p>"}, "IdentityProvider": {"shape": "IdentityProvider", "documentation": "<p>An object that specifies details for the identity provider.</p>"}, "Product": {"shape": "String", "documentation": "<p>The name of the user-based subscription product.</p>"}, "Status": {"shape": "String", "documentation": "<p>The status of a product for a user.</p>"}, "StatusMessage": {"shape": "String", "documentation": "<p>The status message for a product for a user.</p>"}, "SubscriptionEndDate": {"shape": "String", "documentation": "<p>The end date of a subscription.</p>"}, "SubscriptionStartDate": {"shape": "String", "documentation": "<p>The start date of a subscription.</p>"}, "Username": {"shape": "String", "documentation": "<p>The user name from the identity provider of the user.</p>"}}, "documentation": "<p>The summary of the user-based subscription products for a user.</p>"}, "ProductUserSummaryList": {"type": "list", "member": {"shape": "ProductUserSummary"}}, "RegisterIdentityProviderRequest": {"type": "structure", "required": ["IdentityProvider", "Product"], "members": {"IdentityProvider": {"shape": "IdentityProvider", "documentation": "<p>An object that specifies details for the identity provider.</p>"}, "Product": {"shape": "String", "documentation": "<p>The name of the user-based subscription product.</p>"}, "Settings": {"shape": "Settings", "documentation": "<p>The registered identity provider’s product related configuration settings such as the subnets to provision VPC endpoints.</p>"}}}, "RegisterIdentityProviderResponse": {"type": "structure", "required": ["IdentityProviderSummary"], "members": {"IdentityProviderSummary": {"shape": "IdentityProviderSummary", "documentation": "<p>Metadata that describes the results of an identity provider operation.</p>"}}}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The resource couldn't be found.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "SecurityGroup": {"type": "string", "max": 200, "min": 5, "pattern": "^sg-(([0-9a-z]{8})|([0-9a-z]{17}))$"}, "ServiceQuotaExceededException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The request failed because a service quota is exceeded.</p>", "exception": true}, "Settings": {"type": "structure", "required": ["SecurityGroupId", "Subnets"], "members": {"SecurityGroupId": {"shape": "SecurityGroup", "documentation": "<p>A security group ID that allows inbound TCP port 1688 communication between resources in your VPC and the VPC endpoint for activation servers.</p>"}, "Subnets": {"shape": "SettingsSubnetsList", "documentation": "<p>The subnets defined for the registered identity provider.</p>"}}, "documentation": "<p>The registered identity provider’s product related configuration settings such as the subnets to provision VPC endpoints, and the security group ID that is associated with the VPC endpoints. The security group should permit inbound TCP port 1688 communication from resources in the VPC.</p>"}, "SettingsSubnetsList": {"type": "list", "member": {"shape": "Subnet"}, "min": 1}, "StartProductSubscriptionRequest": {"type": "structure", "required": ["IdentityProvider", "Product", "Username"], "members": {"Domain": {"shape": "String", "documentation": "<p>The domain name of the user.</p>"}, "IdentityProvider": {"shape": "IdentityProvider", "documentation": "<p>An object that specifies details for the identity provider.</p>"}, "Product": {"shape": "String", "documentation": "<p>The name of the user-based subscription product.</p>"}, "Username": {"shape": "String", "documentation": "<p>The user name from the identity provider of the user.</p>"}}}, "StartProductSubscriptionResponse": {"type": "structure", "required": ["ProductUserSummary"], "members": {"ProductUserSummary": {"shape": "ProductUserSummary", "documentation": "<p>Metadata that describes the start product subscription operation.</p>"}}}, "StopProductSubscriptionRequest": {"type": "structure", "required": ["IdentityProvider", "Product", "Username"], "members": {"Domain": {"shape": "String", "documentation": "<p>The domain name of the user.</p>"}, "IdentityProvider": {"shape": "IdentityProvider", "documentation": "<p>An object that specifies details for the identity provider.</p>"}, "Product": {"shape": "String", "documentation": "<p>The name of the user-based subscription product.</p>"}, "Username": {"shape": "String", "documentation": "<p>The user name from the identity provider for the user.</p>"}}}, "StopProductSubscriptionResponse": {"type": "structure", "required": ["ProductUserSummary"], "members": {"ProductUserSummary": {"shape": "ProductUserSummary", "documentation": "<p>Metadata that describes the start product subscription operation.</p>"}}}, "String": {"type": "string"}, "StringList": {"type": "list", "member": {"shape": "String"}}, "Subnet": {"type": "string", "pattern": "subnet-[a-z0-9]{8,17}"}, "Subnets": {"type": "list", "member": {"shape": "Subnet"}}, "ThrottlingException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The request was denied because of request throttling. Retry the request.</p>", "exception": true}, "UpdateIdentityProviderSettingsRequest": {"type": "structure", "required": ["IdentityProvider", "Product", "UpdateSettings"], "members": {"IdentityProvider": {"shape": "IdentityProvider"}, "Product": {"shape": "String", "documentation": "<p>The name of the user-based subscription product.</p>"}, "UpdateSettings": {"shape": "UpdateSettings", "documentation": "<p>Updates the registered identity provider’s product related configuration settings. You can update any combination of settings in a single operation such as the:</p> <ul> <li> <p>Subnets which you want to add to provision VPC endpoints.</p> </li> <li> <p>Subnets which you want to remove the VPC endpoints from.</p> </li> <li> <p>Security group ID which permits traffic to the VPC endpoints.</p> </li> </ul>"}}}, "UpdateIdentityProviderSettingsResponse": {"type": "structure", "required": ["IdentityProviderSummary"], "members": {"IdentityProviderSummary": {"shape": "IdentityProviderSummary"}}}, "UpdateSettings": {"type": "structure", "required": ["AddSubnets", "RemoveSubnets"], "members": {"AddSubnets": {"shape": "Subnets", "documentation": "<p>The ID of one or more subnets in which License Manager will create a VPC endpoint for products that require connectivity to activation servers.</p>"}, "RemoveSubnets": {"shape": "Subnets", "documentation": "<p>The ID of one or more subnets to remove.</p>"}, "SecurityGroupId": {"shape": "SecurityGroup", "documentation": "<p>A security group ID that allows inbound TCP port 1688 communication between resources in your VPC and the VPC endpoints for activation servers.</p>"}}, "documentation": "<p>Updates the registered identity provider’s product related configuration settings such as the subnets to provision VPC endpoints.</p>"}, "ValidationException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>A parameter is not valid.</p>", "exception": true}}, "documentation": "<p>With License Manager, you can create user-based subscriptions to utilize licensed software with a per user subscription fee on Amazon EC2 instances.</p>"}