{"version": "2.0", "metadata": {"apiVersion": "2020-07-14", "endpointPrefix": "ivsrealtime", "jsonVersion": "1.1", "protocol": "rest-json", "serviceAbbreviation": "ivsrealtime", "serviceFullName": "Amazon Interactive Video Service RealTime", "serviceId": "IVS RealTime", "signatureVersion": "v4", "signingName": "ivs", "uid": "ivs-realtime-2020-07-14"}, "operations": {"CreateParticipantToken": {"name": "CreateParticipantToken", "http": {"method": "POST", "requestUri": "/CreateParticipantToken", "responseCode": 200}, "input": {"shape": "CreateParticipantTokenRequest"}, "output": {"shape": "CreateParticipantTokenResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "PendingVerification"}], "documentation": "<p>Creates an additional token for a specified stage. This can be done after stage creation or when tokens expire. Tokens always are scoped to the stage for which they are created.</p> <p>Encryption keys are owned by Amazon IVS and never used directly by your application.</p>"}, "CreateStage": {"name": "CreateStage", "http": {"method": "POST", "requestUri": "/CreateStage", "responseCode": 200}, "input": {"shape": "CreateStageRequest"}, "output": {"shape": "CreateStageResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "PendingVerification"}], "documentation": "<p>Creates a new stage (and optionally participant tokens).</p>"}, "DeleteStage": {"name": "DeleteStage", "http": {"method": "POST", "requestUri": "/DeleteStage", "responseCode": 200}, "input": {"shape": "DeleteStageRequest"}, "output": {"shape": "DeleteStageResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "PendingVerification"}], "documentation": "<p>Shuts down and deletes the specified stage (disconnecting all participants).</p>"}, "DisconnectParticipant": {"name": "DisconnectParticipant", "http": {"method": "POST", "requestUri": "/DisconnectParticipant", "responseCode": 200}, "input": {"shape": "DisconnectParticipantRequest"}, "output": {"shape": "DisconnectParticipantResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "PendingVerification"}], "documentation": "<p>Disconnects a specified participant and revokes the participant permanently from a specified stage.</p>"}, "GetParticipant": {"name": "GetParticipant", "http": {"method": "POST", "requestUri": "/GetParticipant", "responseCode": 200}, "input": {"shape": "GetParticipantRequest"}, "output": {"shape": "GetParticipantResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets information about the specified participant token.</p>"}, "GetStage": {"name": "GetStage", "http": {"method": "POST", "requestUri": "/GetStage", "responseCode": 200}, "input": {"shape": "GetStageRequest"}, "output": {"shape": "GetStageResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets information for the specified stage.</p>"}, "GetStageSession": {"name": "GetStageSession", "http": {"method": "POST", "requestUri": "/GetStageSession", "responseCode": 200}, "input": {"shape": "GetStageSessionRequest"}, "output": {"shape": "GetStageSessionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets information for the specified stage session.</p>"}, "ListParticipantEvents": {"name": "ListParticipantEvents", "http": {"method": "POST", "requestUri": "/ListParticipantEvents", "responseCode": 200}, "input": {"shape": "ListParticipantEventsRequest"}, "output": {"shape": "ListParticipantEventsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists events for a specified participant that occurred during a specified stage session.</p>"}, "ListParticipants": {"name": "ListParticipants", "http": {"method": "POST", "requestUri": "/ListParticipants", "responseCode": 200}, "input": {"shape": "ListParticipantsRequest"}, "output": {"shape": "ListParticipantsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists all participants in a specified stage session.</p>"}, "ListStageSessions": {"name": "ListStageSessions", "http": {"method": "POST", "requestUri": "/ListStageSessions", "responseCode": 200}, "input": {"shape": "ListStageSessionsRequest"}, "output": {"shape": "ListStageSessionsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets all sessions for a specified stage.</p>"}, "ListStages": {"name": "ListStages", "http": {"method": "POST", "requestUri": "/ListStages", "responseCode": 200}, "input": {"shape": "ListStagesRequest"}, "output": {"shape": "ListStagesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}], "documentation": "<p>Gets summary information about all stages in your account, in the AWS region where the API request is processed.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets information about AWS tags for the specified ARN.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Adds or updates tags for the AWS resource with the specified ARN.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Removes tags from the resource with the specified ARN.</p>", "idempotent": true}, "UpdateStage": {"name": "UpdateStage", "http": {"method": "POST", "requestUri": "/UpdateStage", "responseCode": 200}, "input": {"shape": "UpdateStageRequest"}, "output": {"shape": "UpdateStageResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "PendingVerification"}], "documentation": "<p>Updates a stage’s configuration.</p>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"exceptionMessage": {"shape": "errorMessage", "documentation": "<p>User does not have sufficient access to perform this action.</p>"}}, "documentation": "<p/>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "ConflictException": {"type": "structure", "members": {"exceptionMessage": {"shape": "errorMessage", "documentation": "<p>Updating or deleting a resource can cause an inconsistent state.</p>"}}, "documentation": "<p/>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "CreateParticipantTokenRequest": {"type": "structure", "required": ["stageArn"], "members": {"attributes": {"shape": "ParticipantTokenAttributes", "documentation": "<p>Application-provided attributes to encode into the token and attach to a stage. Map keys and values can contain UTF-8 encoded text. The maximum length of this field is 1 KB total. <i>This field is exposed to all stage participants and should not be used for personally identifying, confidential, or sensitive information.</i> </p>"}, "capabilities": {"shape": "ParticipantTokenCapabilities", "documentation": "<p>Set of capabilities that the user is allowed to perform in the stage. Default: <code>PUBLISH, SUBSCRIBE</code>.</p>"}, "duration": {"shape": "ParticipantTokenDurationMinutes", "documentation": "<p>Duration (in minutes), after which the token expires. Default: 720 (12 hours).</p>"}, "stageArn": {"shape": "StageArn", "documentation": "<p>ARN of the stage to which this token is scoped.</p>"}, "userId": {"shape": "ParticipantTokenUserId", "documentation": "<p>Name that can be specified to help identify the token. This can be any UTF-8 encoded text. <i>This field is exposed to all stage participants and should not be used for personally identifying, confidential, or sensitive information.</i> </p>"}}}, "CreateParticipantTokenResponse": {"type": "structure", "members": {"participantToken": {"shape": "ParticipantToken", "documentation": "<p>The participant token that was created.</p>"}}}, "CreateStageRequest": {"type": "structure", "members": {"name": {"shape": "StageName", "documentation": "<p>Optional name that can be specified for the stage being created.</p>"}, "participantTokenConfigurations": {"shape": "ParticipantTokenConfigurations", "documentation": "<p>Array of participant token configuration objects to attach to the new stage.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Tags attached to the resource. Array of maps, each of the form <code>string:string (key:value)</code>. See <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html\">Tagging AWS Resources</a> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS has no constraints on tags beyond what is documented there. </p>"}}}, "CreateStageResponse": {"type": "structure", "members": {"participantTokens": {"shape": "ParticipantTokenList", "documentation": "<p>Participant tokens attached to the stage. These correspond to the <code>participants</code> in the request.</p>"}, "stage": {"shape": "Stage", "documentation": "<p>The stage that was created.</p>"}}}, "DeleteStageRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "StageArn", "documentation": "<p>ARN of the stage to be deleted.</p>"}}}, "DeleteStageResponse": {"type": "structure", "members": {}}, "DisconnectParticipantReason": {"type": "string", "max": 128, "min": 0}, "DisconnectParticipantRequest": {"type": "structure", "required": ["participantId", "stageArn"], "members": {"participantId": {"shape": "ParticipantTokenId", "documentation": "<p>Identifier of the participant to be disconnected. This is assigned by IVS and returned by <a>CreateParticipantToken</a>.</p>"}, "reason": {"shape": "DisconnectParticipantReason", "documentation": "<p>Description of why this participant is being disconnected.</p>"}, "stageArn": {"shape": "StageArn", "documentation": "<p>ARN of the stage to which the participant is attached.</p>"}}}, "DisconnectParticipantResponse": {"type": "structure", "members": {}}, "Event": {"type": "structure", "members": {"errorCode": {"shape": "EventErrorCode", "documentation": "<p>If the event is an error event, the error code is provided to give insight into the specific error that occurred. If the event is not an error event, this field is null. <code>INSUFFICIENT_CAPABILITIES</code> indicates that the participant tried to take an action that the participant’s token is not allowed to do. For more information about participant capabilities, see the <code>capabilities</code> field in <a>CreateParticipantToken</a>. <code>QUOTA_EXCEEDED</code> indicates that the number of participants who want to publish/subscribe to a stage exceeds the quota; for more information, see <a href=\"https://docs.aws.amazon.com/ivs/latest/RealTimeUserGuide/service-quotas.html\">Service Quotas</a>. <code>PUBLISHER_NOT_FOUND</code> indicates that the participant tried to subscribe to a publisher that doesn’t exist. </p>"}, "eventTime": {"shape": "Time", "documentation": "<p>ISO 8601 timestamp (returned as a string) for when the event occurred.</p>"}, "name": {"shape": "EventName", "documentation": "<p>The name of the event.</p>"}, "participantId": {"shape": "ParticipantId", "documentation": "<p>Unique identifier for the participant who triggered the event. This is assigned by IVS.</p>"}, "remoteParticipantId": {"shape": "ParticipantId", "documentation": "<p>Unique identifier for the remote participant. For a subscribe event, this is the publisher. For a publish or join event, this is null. This is assigned by IVS.</p>"}}, "documentation": "<p>An occurrence during a stage session.</p>"}, "EventErrorCode": {"type": "string", "enum": ["INSUFFICIENT_CAPABILITIES", "QUOTA_EXCEEDED", "PUBLISHER_NOT_FOUND"]}, "EventList": {"type": "list", "member": {"shape": "Event"}}, "EventName": {"type": "string", "enum": ["JOINED", "LEFT", "PUBLISH_STARTED", "PUBLISH_STOPPED", "SUBSCRIBE_STARTED", "SUBSCRIBE_STOPPED", "PUBLISH_ERROR", "SUBSCRIBE_ERROR", "JOIN_ERROR"]}, "GetParticipantRequest": {"type": "structure", "required": ["participantId", "sessionId", "stageArn"], "members": {"participantId": {"shape": "ParticipantId", "documentation": "<p>Unique identifier for the participant. This is assigned by IVS and returned by <a>CreateParticipantToken</a>.</p>"}, "sessionId": {"shape": "StageSessionId", "documentation": "<p>ID of a session within the stage.</p>"}, "stageArn": {"shape": "StageArn", "documentation": "<p>Stage ARN.</p>"}}}, "GetParticipantResponse": {"type": "structure", "members": {"participant": {"shape": "Participant", "documentation": "<p>The participant that is returned.</p>"}}}, "GetStageRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "StageArn", "documentation": "<p>ARN of the stage for which the information is to be retrieved.</p>"}}}, "GetStageResponse": {"type": "structure", "members": {"stage": {"shape": "Stage", "documentation": "<p>The stage that is returned.</p>"}}}, "GetStageSessionRequest": {"type": "structure", "required": ["sessionId", "stageArn"], "members": {"sessionId": {"shape": "StageSessionId", "documentation": "<p>ID of a session within the stage.</p>"}, "stageArn": {"shape": "StageArn", "documentation": "<p>ARN of the stage for which the information is to be retrieved.</p>"}}}, "GetStageSessionResponse": {"type": "structure", "members": {"stageSession": {"shape": "StageSession", "documentation": "<p>The stage session that is returned.</p>"}}}, "InternalServerException": {"type": "structure", "members": {"exceptionMessage": {"shape": "errorMessage", "documentation": "<p>Unexpected error during processing of request.</p>"}}, "documentation": "<p/>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "ListParticipantEventsRequest": {"type": "structure", "required": ["participantId", "sessionId", "stageArn"], "members": {"maxResults": {"shape": "MaxParticipantEventResults", "documentation": "<p>Maximum number of results to return. Default: 50.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The first participant to retrieve. This is used for pagination; see the <code>nextToken</code> response field.</p>"}, "participantId": {"shape": "ParticipantId", "documentation": "<p>Unique identifier for this participant. This is assigned by IVS and returned by <a>CreateParticipantToken</a>.</p>"}, "sessionId": {"shape": "StageSessionId", "documentation": "<p>ID of a session within the stage.</p>"}, "stageArn": {"shape": "StageArn", "documentation": "<p>Stage ARN.</p>"}}}, "ListParticipantEventsResponse": {"type": "structure", "required": ["events"], "members": {"events": {"shape": "EventList", "documentation": "<p>List of the matching events.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>If there are more rooms than <code>maxResults</code>, use <code>nextToken</code> in the request to get the next set. </p>"}}}, "ListParticipantsRequest": {"type": "structure", "required": ["sessionId", "stageArn"], "members": {"filterByPublished": {"shape": "Published", "documentation": "<p>Filters the response list to only show participants who published during the stage session. Only one of <code>filterByUserId</code>, <code>filterByPublished</code>, or <code>filterByState</code> can be provided per request.</p>"}, "filterByState": {"shape": "ParticipantState", "documentation": "<p>Filters the response list to only show participants in the specified state. Only one of <code>filterByUserId</code>, <code>filterByPublished</code>, or <code>filterByState</code> can be provided per request.</p>"}, "filterByUserId": {"shape": "UserId", "documentation": "<p>Filters the response list to match the specified user ID. Only one of <code>filterByUserId</code>, <code>filterByPublished</code>, or <code>filterByState</code> can be provided per request. A <code>userId</code> is a customer-assigned name to help identify the token; this can be used to link a participant to a user in the customer’s own systems.</p>"}, "maxResults": {"shape": "MaxParticipantResults", "documentation": "<p>Maximum number of results to return. Default: 50.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The first participant to retrieve. This is used for pagination; see the <code>nextToken</code> response field.</p>"}, "sessionId": {"shape": "StageSessionId", "documentation": "<p>ID of the session within the stage.</p>"}, "stageArn": {"shape": "StageArn", "documentation": "<p>Stage ARN.</p>"}}}, "ListParticipantsResponse": {"type": "structure", "required": ["participants"], "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>If there are more rooms than <code>maxResults</code>, use <code>nextToken</code> in the request to get the next set.</p>"}, "participants": {"shape": "ParticipantList", "documentation": "<p>List of the matching participants (summary information only).</p>"}}}, "ListStageSessionsRequest": {"type": "structure", "required": ["stageArn"], "members": {"maxResults": {"shape": "MaxStageSessionResults", "documentation": "<p>Maximum number of results to return. Default: 50.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The first stage to retrieve. This is used for pagination; see the <code>nextToken</code> response field.</p>"}, "stageArn": {"shape": "StageArn", "documentation": "<p>Stage ARN.</p>"}}}, "ListStageSessionsResponse": {"type": "structure", "required": ["stageSessions"], "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>If there are more rooms than <code>maxResults</code>, use <code>nextToken</code> in the request to get the next set.</p>"}, "stageSessions": {"shape": "StageSessionList", "documentation": "<p>List of matching stage sessions.</p>"}}}, "ListStagesRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxStageResults", "documentation": "<p>Maximum number of results to return. Default: 50.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The first stage to retrieve. This is used for pagination; see the <code>nextToken</code> response field.</p>"}}}, "ListStagesResponse": {"type": "structure", "required": ["stages"], "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>If there are more rooms than <code>maxResults</code>, use <code>nextToken</code> in the request to get the next set.</p>"}, "stages": {"shape": "StageSummaryList", "documentation": "<p>List of the matching stages (summary information only).</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource to be retrieved. The ARN must be URL-encoded.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "required": ["tags"], "members": {"tags": {"shape": "Tags", "documentation": "<p>Tags attached to the resource. Array of maps, each of the form <code>string:string (key:value)</code>.</p>"}}}, "MaxParticipantEventResults": {"type": "integer", "max": 100, "min": 1}, "MaxParticipantResults": {"type": "integer", "max": 100, "min": 1}, "MaxStageResults": {"type": "integer", "max": 100, "min": 1}, "MaxStageSessionResults": {"type": "integer", "max": 100, "min": 1}, "PaginationToken": {"type": "string", "max": 1024, "min": 0, "pattern": "^[a-zA-Z0-9+/=_-]*$"}, "Participant": {"type": "structure", "members": {"attributes": {"shape": "ParticipantAttributes", "documentation": "<p>Application-provided attributes to encode into the token and attach to a stage. Map keys and values can contain UTF-8 encoded text. The maximum length of this field is 1 KB total. <i>This field is exposed to all stage participants and should not be used for personally identifying, confidential, or sensitive information</i>.</p>"}, "browserName": {"shape": "ParticipantClientAttribute", "documentation": "<p>The participant’s browser.</p>"}, "browserVersion": {"shape": "ParticipantClientAttribute", "documentation": "<p>The participant’s browser version.</p>"}, "firstJoinTime": {"shape": "Time", "documentation": "<p>ISO 8601 timestamp (returned as a string) when the participant first joined the stage session.</p>"}, "ispName": {"shape": "ParticipantClientAttribute", "documentation": "<p>The participant’s Internet Service Provider.</p>"}, "osName": {"shape": "ParticipantClientAttribute", "documentation": "<p>The participant’s operating system.</p>"}, "osVersion": {"shape": "ParticipantClientAttribute", "documentation": "<p>The participant’s operating system version.</p>"}, "participantId": {"shape": "ParticipantId", "documentation": "<p>Unique identifier for this participant, assigned by IVS.</p>"}, "published": {"shape": "Published", "documentation": "<p>Whether the participant ever published to the stage session.</p>"}, "sdkVersion": {"shape": "ParticipantClientAttribute", "documentation": "<p>The participant’s SDK version.</p>"}, "state": {"shape": "ParticipantState", "documentation": "<p>Whether the participant is connected to or disconnected from the stage.</p>"}, "userId": {"shape": "UserId", "documentation": "<p>Customer-assigned name to help identify the token; this can be used to link a participant to a user in the customer’s own systems. This can be any UTF-8 encoded text. <i>This field is exposed to all stage participants and should not be used for personally identifying, confidential, or sensitive information</i>.</p>"}}, "documentation": "<p>Object describing a participant that has joined a stage.</p>"}, "ParticipantAttributes": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "ParticipantClientAttribute": {"type": "string", "max": 128, "min": 0, "pattern": "^[a-zA-Z0-9-_.,:;\\s]*$"}, "ParticipantId": {"type": "string"}, "ParticipantList": {"type": "list", "member": {"shape": "ParticipantSummary"}}, "ParticipantState": {"type": "string", "enum": ["CONNECTED", "DISCONNECTED"]}, "ParticipantSummary": {"type": "structure", "members": {"firstJoinTime": {"shape": "Time", "documentation": "<p>ISO 8601 timestamp (returned as a string) when the participant first joined the stage session.</p>"}, "participantId": {"shape": "ParticipantId", "documentation": "<p>Unique identifier for this participant, assigned by IVS.</p>"}, "published": {"shape": "Published", "documentation": "<p>Whether the participant ever published to the stage session.</p>"}, "state": {"shape": "ParticipantState", "documentation": "<p>Whether the participant is connected to or disconnected from the stage.</p>"}, "userId": {"shape": "UserId", "documentation": "<p>Customer-assigned name to help identify the token; this can be used to link a participant to a user in the customer’s own systems. This can be any UTF-8 encoded text. <i>This field is exposed to all stage participants and should not be used for personally identifying, confidential, or sensitive information</i>.</p>"}}, "documentation": "<p>Summary object describing a participant that has joined a stage.</p>"}, "ParticipantToken": {"type": "structure", "members": {"attributes": {"shape": "ParticipantTokenAttributes", "documentation": "<p>Application-provided attributes to encode into the token and attach to a stage. <i>This field is exposed to all stage participants and should not be used for personally identifying, confidential, or sensitive information.</i> </p>"}, "capabilities": {"shape": "ParticipantTokenCapabilities", "documentation": "<p>Set of capabilities that the user is allowed to perform in the stage.</p>"}, "duration": {"shape": "ParticipantTokenDurationMinutes", "documentation": "<p>Duration (in minutes), after which the participant token expires. Default: 720 (12 hours).</p>"}, "expirationTime": {"shape": "ParticipantTokenExpirationTime", "documentation": "<p>ISO 8601 timestamp (returned as a string) for when this token expires.</p>"}, "participantId": {"shape": "ParticipantTokenId", "documentation": "<p>Unique identifier for this participant token, assigned by IVS.</p>"}, "token": {"shape": "ParticipantTokenString", "documentation": "<p>The issued client token, encrypted.</p>"}, "userId": {"shape": "ParticipantTokenUserId", "documentation": "<p>Customer-assigned name to help identify the token; this can be used to link a participant to a user in the customer’s own systems. This can be any UTF-8 encoded text. <i>This field is exposed to all stage participants and should not be used for personally identifying, confidential, or sensitive information.</i> </p>"}}, "documentation": "<p>Object specifying a participant token in a stage.</p> <p> <b>Important</b>: Treat tokens as opaque; i.e., do not build functionality based on token contents. The format of tokens could change in the future.</p>"}, "ParticipantTokenAttributes": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "ParticipantTokenCapabilities": {"type": "list", "member": {"shape": "ParticipantTokenCapability"}, "max": 2, "min": 0}, "ParticipantTokenCapability": {"type": "string", "enum": ["PUBLISH", "SUBSCRIBE"]}, "ParticipantTokenConfiguration": {"type": "structure", "members": {"attributes": {"shape": "ParticipantTokenAttributes", "documentation": "<p>Application-provided attributes to encode into the corresponding participant token and attach to a stage. Map keys and values can contain UTF-8 encoded text. The maximum length of this field is 1 KB total. <i>This field is exposed to all stage participants and should not be used for personally identifying, confidential, or sensitive information.</i> </p>"}, "capabilities": {"shape": "ParticipantTokenCapabilities", "documentation": "<p>Set of capabilities that the user is allowed to perform in the stage.</p>"}, "duration": {"shape": "ParticipantTokenDurationMinutes", "documentation": "<p>Duration (in minutes), after which the corresponding participant token expires. Default: 720 (12 hours).</p>"}, "userId": {"shape": "ParticipantTokenUserId", "documentation": "<p>Customer-assigned name to help identify the token; this can be used to link a participant to a user in the customer’s own systems. This can be any UTF-8 encoded text. <i>This field is exposed to all stage participants and should not be used for personally identifying, confidential, or sensitive information.</i> </p>"}}, "documentation": "<p>Object specifying a participant token configuration in a stage.</p>"}, "ParticipantTokenConfigurations": {"type": "list", "member": {"shape": "ParticipantTokenConfiguration"}, "max": 12, "min": 0}, "ParticipantTokenDurationMinutes": {"type": "integer", "max": 20160, "min": 1}, "ParticipantTokenExpirationTime": {"type": "timestamp", "timestampFormat": "iso8601"}, "ParticipantTokenId": {"type": "string"}, "ParticipantTokenList": {"type": "list", "member": {"shape": "ParticipantToken"}}, "ParticipantTokenString": {"type": "string", "sensitive": true}, "ParticipantTokenUserId": {"type": "string", "max": 128, "min": 0}, "PendingVerification": {"type": "structure", "members": {"exceptionMessage": {"shape": "errorMessage", "documentation": "<p> Your account is pending verification. </p>"}}, "documentation": "<p/>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "Published": {"type": "boolean"}, "ResourceArn": {"type": "string", "max": 128, "min": 1, "pattern": "^arn:aws:[is]vs:[a-z0-9-]+:[0-9]+:[a-z-]/[a-zA-Z0-9-]+$"}, "ResourceNotFoundException": {"type": "structure", "members": {"exceptionMessage": {"shape": "errorMessage", "documentation": "<p>Request references a resource which does not exist.</p>"}}, "documentation": "<p/>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "ServiceQuotaExceededException": {"type": "structure", "members": {"exceptionMessage": {"shape": "errorMessage", "documentation": "<p>Request would cause a service quota to be exceeded.</p>"}}, "documentation": "<p/>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "Stage": {"type": "structure", "required": ["arn"], "members": {"activeSessionId": {"shape": "StageSessionId", "documentation": "<p>ID of the active session within the stage.</p>"}, "arn": {"shape": "StageArn", "documentation": "<p>Stage ARN.</p>"}, "name": {"shape": "StageName", "documentation": "<p>Stage name.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Tags attached to the resource. Array of maps, each of the form <code>string:string (key:value)</code>. See <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html\">Tagging AWS Resources</a> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS has no constraints on tags beyond what is documented there.</p>"}}, "documentation": "<p>Object specifying a stage.</p>"}, "StageArn": {"type": "string", "max": 128, "min": 1, "pattern": "^arn:aws:ivs:[a-z0-9-]+:[0-9]+:stage/[a-zA-Z0-9-]+$"}, "StageName": {"type": "string", "max": 128, "min": 0, "pattern": "^[a-zA-Z0-9-_]*$"}, "StageSession": {"type": "structure", "members": {"endTime": {"shape": "Time", "documentation": "<p>ISO 8601 timestamp (returned as a string) when the stage session ended. This is null if the stage is active.</p>"}, "sessionId": {"shape": "StageSessionId", "documentation": "<p>ID of the session within the stage.</p>"}, "startTime": {"shape": "Time", "documentation": "<p> ISO 8601 timestamp (returned as a string) when this stage session began.</p>"}}, "documentation": "<p>A stage session begins when the first participant joins a stage and ends after the last participant leaves the stage. A stage session helps with debugging stages by grouping events and participants into shorter periods of time (i.e., a session), which is helpful when stages are used over long periods of time.</p>"}, "StageSessionId": {"type": "string", "max": 16, "min": 16, "pattern": "^st-[a-zA-Z0-9]+$"}, "StageSessionList": {"type": "list", "member": {"shape": "StageSessionSummary"}}, "StageSessionSummary": {"type": "structure", "members": {"endTime": {"shape": "Time", "documentation": "<p>ISO 8601 timestamp (returned as a string) when the stage session ended. This is null if the stage is active.</p>"}, "sessionId": {"shape": "StageSessionId", "documentation": "<p>ID of the session within the stage.</p>"}, "startTime": {"shape": "Time", "documentation": "<p> ISO 8601 timestamp (returned as a string) when this stage session began.</p>"}}, "documentation": "<p>Summary information about a stage session.</p>"}, "StageSummary": {"type": "structure", "required": ["arn"], "members": {"activeSessionId": {"shape": "StageSessionId", "documentation": "<p>ID of the active session within the stage.</p>"}, "arn": {"shape": "StageArn", "documentation": "<p>Stage ARN.</p>"}, "name": {"shape": "StageName", "documentation": "<p>Stage name.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Tags attached to the resource. Array of maps, each of the form <code>string:string (key:value)</code>. See <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html\">Tagging AWS Resources</a> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS has no constraints on tags beyond what is documented there.</p>"}}, "documentation": "<p>Summary information about a stage.</p>"}, "StageSummaryList": {"type": "list", "member": {"shape": "StageSummary"}}, "String": {"type": "string"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource to be tagged. The ARN must be URL-encoded.</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "Tags", "documentation": "<p>Array of tags to be added or updated. Array of maps, each of the form <code>string:string (key:value)</code>. See <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html\">Tagging AWS Resources</a> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS has no constraints beyond what is documented there.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "Tags": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "max": 50, "min": 0}, "Time": {"type": "timestamp", "timestampFormat": "iso8601"}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource to be untagged. The ARN must be URL-encoded.</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>Array of tags to be removed. Array of maps, each of the form <code>string:string (key:value)</code>. See <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html\">Tagging AWS Resources</a> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS has no constraints beyond what is documented there.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateStageRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "StageArn", "documentation": "<p>ARN of the stage to be updated.</p>"}, "name": {"shape": "StageName", "documentation": "<p>Name of the stage to be updated.</p>"}}}, "UpdateStageResponse": {"type": "structure", "members": {"stage": {"shape": "Stage", "documentation": "<p>The updated stage.</p>"}}}, "UserId": {"type": "string", "max": 128, "min": 0}, "ValidationException": {"type": "structure", "members": {"exceptionMessage": {"shape": "errorMessage", "documentation": "<p>The input fails to satisfy the constraints specified by an Amazon Web Services service.</p>"}}, "documentation": "<p/>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "errorMessage": {"type": "string"}}, "documentation": "<p> <b>Introduction</b> </p> <p>The Amazon Interactive Video Service (IVS) real-time API is REST compatible, using a standard HTTP API and an AWS EventBridge event stream for responses. JSON is used for both requests and responses, including errors. </p> <p>Terminology:</p> <ul> <li> <p>A <i>stage</i> is a virtual space where participants can exchange video in real time.</p> </li> <li> <p>A <i>participant token</i> is a token that authenticates a participant when they join a stage.</p> </li> <li> <p>A <i>participant object</i> represents participants (people) in the stage and contains information about them. When a token is created, it includes a participant ID; when a participant uses that token to join a stage, the participant is associated with that participant ID There is a 1:1 mapping between participant tokens and participants.</p> </li> </ul> <p> <b>Resources</b> </p> <p>The following resources contain information about your IVS live stream (see <a href=\"https://docs.aws.amazon.com/ivs/latest/RealTimeUserGuide/getting-started.html\">Getting Started with Amazon IVS Real-Time Streaming</a>):</p> <ul> <li> <p> <b>Stage</b> — A stage is a virtual space where participants can exchange video in real time.</p> </li> </ul> <p> <b>Tagging</b> </p> <p>A <i>tag</i> is a metadata label that you assign to an AWS resource. A tag comprises a <i>key</i> and a <i>value</i>, both set by you. For example, you might set a tag as <code>topic:nature</code> to label a particular video category. See <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html\">Tagging AWS Resources</a> for more information, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS stages has no service-specific constraints beyond what is documented there.</p> <p>Tags can help you identify and organize your AWS resources. For example, you can use the same tag for different resources to indicate that they are related. You can also use tags to manage access (see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access_tags.html\">Access Tags</a>).</p> <p>The Amazon IVS real-time API has these tag-related endpoints: <a>TagResource</a>, <a>UntagResource</a>, and <a>ListTagsForResource</a>. The following resource supports tagging: Stage.</p> <p>At most 50 tags can be applied to a resource.</p> <p> <b>Stages Endpoints</b> </p> <ul> <li> <p> <a>CreateParticipantToken</a> — Creates an additional token for a specified stage. This can be done after stage creation or when tokens expire.</p> </li> <li> <p> <a>CreateStage</a> — Creates a new stage (and optionally participant tokens).</p> </li> <li> <p> <a>DeleteStage</a> — Shuts down and deletes the specified stage (disconnecting all participants).</p> </li> <li> <p> <a>DisconnectParticipant</a> — Disconnects a specified participant and revokes the participant permanently from a specified stage.</p> </li> <li> <p> <a>GetParticipant</a> — Gets information about the specified participant token.</p> </li> <li> <p> <a>GetStage</a> — Gets information for the specified stage.</p> </li> <li> <p> <a>GetStageSession</a> — Gets information for the specified stage session.</p> </li> <li> <p> <a>ListParticipantEvents</a> — Lists events for a specified participant that occurred during a specified stage session.</p> </li> <li> <p> <a>ListParticipants</a> — Lists all participants in a specified stage session.</p> </li> <li> <p> <a>ListStages</a> — Gets summary information about all stages in your account, in the AWS region where the API request is processed.</p> </li> <li> <p> <a>ListStageSessions</a> — Gets all sessions for a specified stage.</p> </li> <li> <p> <a>UpdateStage</a> — Updates a stage’s configuration.</p> </li> </ul> <p> <b>Tags Endpoints</b> </p> <ul> <li> <p> <a>ListTagsForResource</a> — Gets information about AWS tags for the specified ARN.</p> </li> <li> <p> <a>TagResource</a> — Adds or updates tags for the AWS resource with the specified ARN.</p> </li> <li> <p> <a>UntagResource</a> — Removes tags from the resource with the specified ARN.</p> </li> </ul>"}