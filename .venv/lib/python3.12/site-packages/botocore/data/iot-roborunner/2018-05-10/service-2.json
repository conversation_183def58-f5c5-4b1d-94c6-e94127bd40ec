{"version": "2.0", "metadata": {"apiVersion": "2018-05-10", "endpointPrefix": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "AWS IoT RoboRunner", "serviceId": "IoT RoboRunner", "signatureVersion": "v4", "signingName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uid": "iot-roborunner-2018-05-10"}, "operations": {"CreateDestination": {"name": "CreateDestination", "http": {"method": "POST", "requestUri": "/createDestination", "responseCode": 200}, "input": {"shape": "CreateDestinationRequest"}, "output": {"shape": "CreateDestinationResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Grants permission to create a destination</p>", "idempotent": true}, "CreateSite": {"name": "CreateSite", "http": {"method": "POST", "requestUri": "/createSite", "responseCode": 200}, "input": {"shape": "CreateSiteRequest"}, "output": {"shape": "CreateSiteResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Grants permission to create a site</p>", "idempotent": true}, "CreateWorker": {"name": "CreateWorker", "http": {"method": "POST", "requestUri": "/createWorker", "responseCode": 200}, "input": {"shape": "CreateWorkerRequest"}, "output": {"shape": "CreateWorkerResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Grants permission to create a worker</p>", "idempotent": true}, "CreateWorkerFleet": {"name": "CreateWorkerFleet", "http": {"method": "POST", "requestUri": "/createWorkerFleet", "responseCode": 200}, "input": {"shape": "CreateWorkerFleetRequest"}, "output": {"shape": "CreateWorkerFleetResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Grants permission to create a worker fleet</p>", "idempotent": true}, "DeleteDestination": {"name": "DeleteDestination", "http": {"method": "POST", "requestUri": "/deleteDestination", "responseCode": 200}, "input": {"shape": "DeleteDestinationRequest"}, "output": {"shape": "DeleteDestinationResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Grants permission to delete a destination</p>"}, "DeleteSite": {"name": "DeleteSite", "http": {"method": "POST", "requestUri": "/deleteSite", "responseCode": 200}, "input": {"shape": "DeleteSiteRequest"}, "output": {"shape": "DeleteSiteResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Grants permission to delete a site</p>"}, "DeleteWorker": {"name": "DeleteWorker", "http": {"method": "POST", "requestUri": "/deleteWorker", "responseCode": 200}, "input": {"shape": "DeleteWorkerRequest"}, "output": {"shape": "DeleteWorkerResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Grants permission to delete a worker</p>"}, "DeleteWorkerFleet": {"name": "DeleteWorkerFleet", "http": {"method": "POST", "requestUri": "/deleteWorkerFleet", "responseCode": 200}, "input": {"shape": "DeleteWorkerFleetRequest"}, "output": {"shape": "DeleteWorkerFleetResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Grants permission to delete a worker fleet</p>"}, "GetDestination": {"name": "GetDestination", "http": {"method": "GET", "requestUri": "/getDestination", "responseCode": 200}, "input": {"shape": "GetDestinationRequest"}, "output": {"shape": "GetDestinationResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Grants permission to get a destination</p>"}, "GetSite": {"name": "GetSite", "http": {"method": "GET", "requestUri": "/getSite", "responseCode": 200}, "input": {"shape": "GetSiteRequest"}, "output": {"shape": "GetSiteResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Grants permission to get a site</p>"}, "GetWorker": {"name": "GetWorker", "http": {"method": "GET", "requestUri": "/getWorker", "responseCode": 200}, "input": {"shape": "GetWorkerRequest"}, "output": {"shape": "GetWorkerResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Grants permission to get a worker</p>"}, "GetWorkerFleet": {"name": "GetWorkerFleet", "http": {"method": "GET", "requestUri": "/getWorkerFleet", "responseCode": 200}, "input": {"shape": "GetWorkerFleetRequest"}, "output": {"shape": "GetWorkerFleetResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Grants permission to get a worker fleet</p>"}, "ListDestinations": {"name": "ListDestinations", "http": {"method": "GET", "requestUri": "/listDestinations", "responseCode": 200}, "input": {"shape": "ListDestinationsRequest"}, "output": {"shape": "ListDestinationsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Grants permission to list destinations</p>"}, "ListSites": {"name": "ListSites", "http": {"method": "GET", "requestUri": "/listSites", "responseCode": 200}, "input": {"shape": "ListSitesRequest"}, "output": {"shape": "ListSitesResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Grants permission to list sites</p>"}, "ListWorkerFleets": {"name": "ListWorkerFleets", "http": {"method": "GET", "requestUri": "/listWorkerFleets", "responseCode": 200}, "input": {"shape": "ListWorkerFleetsRequest"}, "output": {"shape": "ListWorkerFleetsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Grants permission to list worker fleets</p>"}, "ListWorkers": {"name": "ListWorkers", "http": {"method": "GET", "requestUri": "/listWorkers", "responseCode": 200}, "input": {"shape": "ListWorkersRequest"}, "output": {"shape": "ListWorkersResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Grants permission to list workers</p>"}, "UpdateDestination": {"name": "UpdateDestination", "http": {"method": "POST", "requestUri": "/updateDestination", "responseCode": 200}, "input": {"shape": "UpdateDestinationRequest"}, "output": {"shape": "UpdateDestinationResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Grants permission to update a destination</p>"}, "UpdateSite": {"name": "UpdateSite", "http": {"method": "POST", "requestUri": "/updateSite", "responseCode": 200}, "input": {"shape": "UpdateSiteRequest"}, "output": {"shape": "UpdateSiteResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Grants permission to update a site</p>"}, "UpdateWorker": {"name": "UpdateWorker", "http": {"method": "POST", "requestUri": "/updateWorker", "responseCode": 200}, "input": {"shape": "UpdateWorkerRequest"}, "output": {"shape": "UpdateWorkerResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Grants permission to update a worker</p>"}, "UpdateWorkerFleet": {"name": "UpdateWorkerFleet", "http": {"method": "POST", "requestUri": "/updateWorkerFleet", "responseCode": 200}, "input": {"shape": "UpdateWorkerFleetRequest"}, "output": {"shape": "UpdateWorkerFleetResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Grants permission to update a worker fleet</p>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>User does not have sufficient access to perform this action.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true, "retryable": {"throttling": false}}, "CartesianCoordinates": {"type": "structure", "required": ["x", "y"], "members": {"x": {"shape": "Double", "documentation": "<p>X coordinate.</p>"}, "y": {"shape": "Double", "documentation": "<p>Y coordinate.</p>"}, "z": {"shape": "Double", "documentation": "<p>Z coordinate.</p>"}}, "documentation": "<p>Cartesian coordinates in 3D space relative to the RoboRunner origin.</p>"}, "ConflictException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>Exception thrown if a resource in a create request already exists.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "CreateDestinationRequest": {"type": "structure", "required": ["name", "site"], "members": {"clientToken": {"shape": "IdempotencyToken", "idempotencyToken": true}, "name": {"shape": "Name"}, "site": {"shape": "SiteGenericIdentifier"}, "state": {"shape": "DestinationState", "documentation": "<p>The state of the destination. Default used if not specified.</p>"}, "additionalFixedProperties": {"shape": "DestinationAdditionalFixedProperties"}}}, "CreateDestinationResponse": {"type": "structure", "required": ["arn", "id", "createdAt", "updatedAt", "state"], "members": {"arn": {"shape": "DestinationArn"}, "id": {"shape": "DestinationId"}, "createdAt": {"shape": "CreatedAtTimestamp"}, "updatedAt": {"shape": "UpdatedAtTimestamp"}, "state": {"shape": "DestinationState"}}}, "CreateSiteRequest": {"type": "structure", "required": ["name", "countryCode"], "members": {"clientToken": {"shape": "IdempotencyToken", "idempotencyToken": true}, "name": {"shape": "Name"}, "countryCode": {"shape": "SiteCountryCode"}, "description": {"shape": "SiteDescription"}}}, "CreateSiteResponse": {"type": "structure", "required": ["arn", "id", "createdAt", "updatedAt"], "members": {"arn": {"shape": "SiteArn"}, "id": {"shape": "SiteId"}, "createdAt": {"shape": "CreatedAtTimestamp"}, "updatedAt": {"shape": "UpdatedAtTimestamp"}}}, "CreateWorkerFleetRequest": {"type": "structure", "required": ["name", "site"], "members": {"clientToken": {"shape": "IdempotencyToken", "idempotencyToken": true}, "name": {"shape": "Name"}, "site": {"shape": "SiteGenericIdentifier"}, "additionalFixedProperties": {"shape": "WorkerFleetAdditionalFixedProperties"}}}, "CreateWorkerFleetResponse": {"type": "structure", "required": ["arn", "id", "createdAt", "updatedAt"], "members": {"arn": {"shape": "WorkerFleetArn"}, "id": {"shape": "WorkerFleetId"}, "createdAt": {"shape": "CreatedAtTimestamp"}, "updatedAt": {"shape": "UpdatedAtTimestamp"}}}, "CreateWorkerRequest": {"type": "structure", "required": ["name", "fleet"], "members": {"clientToken": {"shape": "IdempotencyToken", "idempotencyToken": true}, "name": {"shape": "Name"}, "fleet": {"shape": "WorkerFleetGenericIdentifier"}, "additionalTransientProperties": {"shape": "WorkerAdditionalTransientPropertiesJson"}, "additionalFixedProperties": {"shape": "WorkerAdditionalFixedPropertiesJson"}, "vendorProperties": {"shape": "VendorProperties"}, "position": {"shape": "PositionCoordinates"}, "orientation": {"shape": "Orientation"}}}, "CreateWorkerResponse": {"type": "structure", "required": ["arn", "id", "createdAt", "updatedAt", "site"], "members": {"arn": {"shape": "WorkerArn"}, "id": {"shape": "WorkerId"}, "createdAt": {"shape": "CreatedAtTimestamp"}, "updatedAt": {"shape": "UpdatedAtTimestamp"}, "site": {"shape": "SiteArn"}}}, "CreatedAtTimestamp": {"type": "timestamp", "documentation": "<p>Timestamp at which the resource was created.</p>"}, "DeleteDestinationRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "DestinationGenericIdentifier"}}}, "DeleteDestinationResponse": {"type": "structure", "members": {}}, "DeleteSiteRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "SiteGenericIdentifier"}}}, "DeleteSiteResponse": {"type": "structure", "members": {}}, "DeleteWorkerFleetRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "WorkerFleetGenericIdentifier"}}}, "DeleteWorkerFleetResponse": {"type": "structure", "members": {}}, "DeleteWorkerRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "WorkerGenericIdentifier"}}}, "DeleteWorkerResponse": {"type": "structure", "members": {}}, "Destination": {"type": "structure", "required": ["arn", "id", "name", "site", "createdAt", "updatedAt", "state"], "members": {"arn": {"shape": "DestinationArn"}, "id": {"shape": "DestinationId"}, "name": {"shape": "Name"}, "site": {"shape": "SiteArn"}, "createdAt": {"shape": "CreatedAtTimestamp"}, "updatedAt": {"shape": "UpdatedAtTimestamp"}, "state": {"shape": "DestinationState"}, "additionalFixedProperties": {"shape": "DestinationAdditionalFixedProperties"}}, "documentation": "<p>Area within a facility where work can be performed.</p>"}, "DestinationAdditionalFixedProperties": {"type": "string", "documentation": "<p>JSON document containing additional fixed properties regarding the destination</p>", "max": 131072, "min": 1}, "DestinationArn": {"type": "string", "documentation": "<p>Destination ARN.</p>", "max": 1011, "min": 1, "pattern": "arn:aws:iotroborunner:[\\w-]+:\\w+:destination/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$|^arn:aws:iotroborunner:[\\w-]+:\\w+:site/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/destination/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"}, "DestinationGenericIdentifier": {"type": "string", "documentation": "<p>Destination ARN.</p>", "max": 1011, "min": 1, "pattern": "arn:aws:iotroborunner:[\\w-]+:\\w+:destination/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$|^arn:aws:iotroborunner:[\\w-]+:\\w+:site/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/destination/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"}, "DestinationId": {"type": "string", "documentation": "<p>Filters access by the destination's identifier</p>", "max": 255, "min": 1}, "DestinationState": {"type": "string", "documentation": "<p>State of the destination.</p>", "enum": ["ENABLED", "DISABLED", "DECOMMISSIONED"]}, "Destinations": {"type": "list", "member": {"shape": "Destination"}, "documentation": "<p>List of destinations.</p>"}, "Double": {"type": "double", "box": true}, "GetDestinationRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "DestinationGenericIdentifier", "location": "querystring", "locationName": "id"}}}, "GetDestinationResponse": {"type": "structure", "required": ["arn", "id", "name", "site", "createdAt", "updatedAt", "state"], "members": {"arn": {"shape": "DestinationArn"}, "id": {"shape": "DestinationId"}, "name": {"shape": "Name"}, "site": {"shape": "SiteArn"}, "createdAt": {"shape": "CreatedAtTimestamp"}, "updatedAt": {"shape": "UpdatedAtTimestamp"}, "state": {"shape": "DestinationState"}, "additionalFixedProperties": {"shape": "DestinationAdditionalFixedProperties"}}}, "GetSiteRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "SiteGenericIdentifier", "location": "querystring", "locationName": "id"}}}, "GetSiteResponse": {"type": "structure", "required": ["arn", "id", "name", "countryCode", "createdAt", "updatedAt"], "members": {"arn": {"shape": "SiteArn"}, "id": {"shape": "SiteId"}, "name": {"shape": "Name"}, "countryCode": {"shape": "SiteCountryCode"}, "description": {"shape": "SiteDescription"}, "createdAt": {"shape": "CreatedAtTimestamp"}, "updatedAt": {"shape": "UpdatedAtTimestamp"}}}, "GetWorkerFleetRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "WorkerFleetGenericIdentifier", "location": "querystring", "locationName": "id"}}}, "GetWorkerFleetResponse": {"type": "structure", "required": ["id", "arn", "name", "site", "createdAt", "updatedAt"], "members": {"id": {"shape": "WorkerFleetId"}, "arn": {"shape": "WorkerFleetArn"}, "name": {"shape": "Name"}, "site": {"shape": "SiteArn"}, "createdAt": {"shape": "CreatedAtTimestamp"}, "updatedAt": {"shape": "UpdatedAtTimestamp"}, "additionalFixedProperties": {"shape": "WorkerFleetAdditionalFixedProperties"}}}, "GetWorkerRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "WorkerGenericIdentifier", "location": "querystring", "locationName": "id"}}}, "GetWorkerResponse": {"type": "structure", "required": ["arn", "id", "fleet", "site", "createdAt", "updatedAt", "name"], "members": {"arn": {"shape": "WorkerArn"}, "id": {"shape": "WorkerId"}, "fleet": {"shape": "WorkerFleetArn"}, "site": {"shape": "SiteArn"}, "createdAt": {"shape": "CreatedAtTimestamp"}, "updatedAt": {"shape": "UpdatedAtTimestamp"}, "name": {"shape": "Name"}, "additionalTransientProperties": {"shape": "WorkerAdditionalTransientPropertiesJson"}, "additionalFixedProperties": {"shape": "WorkerAdditionalFixedPropertiesJson"}, "vendorProperties": {"shape": "VendorProperties"}, "position": {"shape": "PositionCoordinates"}, "orientation": {"shape": "Orientation"}}}, "IdempotencyToken": {"type": "string", "documentation": "<p>Token used for detecting replayed requests. Replayed requests will not be performed multiple times.</p>", "max": 64, "min": 1, "pattern": "[!-~]*"}, "InternalServerException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>Exception thrown if something goes wrong within the service.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "ListDestinationsRequest": {"type": "structure", "required": ["site"], "members": {"site": {"shape": "SiteGenericIdentifier", "location": "querystring", "locationName": "site"}, "maxResults": {"shape": "PageSize", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "location": "querystring", "locationName": "nextToken"}, "state": {"shape": "DestinationState", "location": "querystring", "locationName": "state"}}}, "ListDestinationsResponse": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken"}, "destinations": {"shape": "Destinations"}}}, "ListSitesPageSize": {"type": "integer", "documentation": "<p>Maximum number of results to retrieve in a single ListSites call.</p>", "box": true, "max": 25, "min": 1}, "ListSitesRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListSitesPageSize", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "location": "querystring", "locationName": "nextToken"}}}, "ListSitesResponse": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken"}, "sites": {"shape": "Sites"}}}, "ListWorkerFleetsPageSize": {"type": "integer", "documentation": "<p>Maximum number of results to retrieve in a single ListWorkerFleets call.</p>", "box": true, "max": 25, "min": 1}, "ListWorkerFleetsRequest": {"type": "structure", "required": ["site"], "members": {"site": {"shape": "SiteGenericIdentifier", "location": "querystring", "locationName": "site"}, "maxResults": {"shape": "ListWorkerFleetsPageSize", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "location": "querystring", "locationName": "nextToken"}}}, "ListWorkerFleetsResponse": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken"}, "workerFleets": {"shape": "WorkerFleets"}}}, "ListWorkersPageSize": {"type": "integer", "documentation": "<p>Maximum number of results to retrieve in a single ListWorkers call.</p>", "box": true, "max": 25, "min": 1}, "ListWorkersRequest": {"type": "structure", "required": ["site"], "members": {"site": {"shape": "SiteGenericIdentifier", "location": "querystring", "locationName": "site"}, "maxResults": {"shape": "ListWorkersPageSize", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "location": "querystring", "locationName": "nextToken"}, "fleet": {"shape": "WorkerFleetGenericIdentifier", "location": "querystring", "locationName": "fleet"}}}, "ListWorkersResponse": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken"}, "workers": {"shape": "Workers"}}}, "Name": {"type": "string", "documentation": "<p>Human friendly name of the resource.</p>", "max": 255, "min": 1}, "Orientation": {"type": "structure", "members": {"degrees": {"shape": "OrientationDegreesDouble", "documentation": "<p>Degrees, limited on [0, 360)</p>"}}, "documentation": "<p>Worker orientation measured in units clockwise from north.</p>", "union": true}, "OrientationDegreesDouble": {"type": "double", "box": true, "max": 360, "min": 0}, "PageSize": {"type": "integer", "documentation": "<p>Maximum number of results to retrieve in a single call.</p>", "box": true, "max": 1000, "min": 1}, "PaginationToken": {"type": "string", "documentation": "<p>Pagination token returned when another page of data exists. Provide it in your next call to the API to receive the next page.</p>", "max": 2048, "min": 1, "pattern": ".*[a-zA-Z0-9_.-/+=]*.*"}, "PositionCoordinates": {"type": "structure", "members": {"cartesianCoordinates": {"shape": "CartesianCoordinates", "documentation": "<p>Cartesian coordinates.</p>"}}, "documentation": "<p>Supported coordinates for worker position.</p>", "union": true}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>Exception thrown if a resource referenced in the request doesn't exist.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "ServiceQuotaExceededException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>Exception thrown if the user's AWS account has reached a service limit and the operation cannot proceed.</p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true, "retryable": {"throttling": false}}, "Site": {"type": "structure", "required": ["arn", "name", "countryCode", "createdAt"], "members": {"arn": {"shape": "SiteArn"}, "name": {"shape": "Name", "documentation": "<p>The name of the site. Mutable after creation and unique within a given account.</p>"}, "countryCode": {"shape": "SiteCountryCode"}, "createdAt": {"shape": "CreatedAtTimestamp"}}, "documentation": "<p>Facility containing destinations, workers, activities, and tasks.</p>"}, "SiteArn": {"type": "string", "documentation": "<p>Site ARN.</p>", "max": 1011, "min": 1, "pattern": "arn:aws:iotroborunner:[\\w-]+:\\w+:site/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"}, "SiteCountryCode": {"type": "string", "documentation": "<p>A valid ISO 3166-1 alpha-2 code for the country in which the site resides. e.g., US.</p>", "max": 2, "min": 2, "pattern": "[a-zA-Z]{2}"}, "SiteDescription": {"type": "string", "documentation": "<p>A high-level description of the site.</p>", "max": 140, "min": 0}, "SiteGenericIdentifier": {"type": "string", "documentation": "<p>Site ARN.</p>", "max": 1011, "min": 1, "pattern": "arn:aws:iotroborunner:[\\w-]+:\\w+:site/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"}, "SiteId": {"type": "string", "documentation": "<p>Filters access by the site's identifier</p>", "max": 255, "min": 1}, "Sites": {"type": "list", "member": {"shape": "Site"}, "documentation": "<p>List of facilities.</p>"}, "String": {"type": "string"}, "ThrottlingException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>Exception thrown if the api has been called too quickly be the client.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true, "retryable": {"throttling": true}}, "UpdateDestinationRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "DestinationGenericIdentifier"}, "name": {"shape": "Name"}, "state": {"shape": "DestinationState"}, "additionalFixedProperties": {"shape": "DestinationAdditionalFixedProperties"}}}, "UpdateDestinationResponse": {"type": "structure", "required": ["arn", "id", "name", "updatedAt", "state"], "members": {"arn": {"shape": "DestinationArn"}, "id": {"shape": "DestinationId"}, "name": {"shape": "Name"}, "updatedAt": {"shape": "UpdatedAtTimestamp"}, "state": {"shape": "DestinationState"}, "additionalFixedProperties": {"shape": "DestinationAdditionalFixedProperties"}}}, "UpdateSiteRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "SiteGenericIdentifier"}, "name": {"shape": "Name"}, "countryCode": {"shape": "SiteCountryCode"}, "description": {"shape": "SiteDescription"}}}, "UpdateSiteResponse": {"type": "structure", "required": ["arn", "id", "name", "updatedAt"], "members": {"arn": {"shape": "SiteArn"}, "id": {"shape": "SiteId"}, "name": {"shape": "Name"}, "countryCode": {"shape": "SiteCountryCode"}, "description": {"shape": "SiteDescription"}, "updatedAt": {"shape": "UpdatedAtTimestamp"}}}, "UpdateWorkerFleetRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "WorkerFleetGenericIdentifier"}, "name": {"shape": "Name"}, "additionalFixedProperties": {"shape": "WorkerFleetAdditionalFixedProperties"}}}, "UpdateWorkerFleetResponse": {"type": "structure", "required": ["arn", "id", "name", "updatedAt"], "members": {"arn": {"shape": "WorkerFleetArn"}, "id": {"shape": "WorkerFleetId"}, "name": {"shape": "Name"}, "updatedAt": {"shape": "UpdatedAtTimestamp"}, "additionalFixedProperties": {"shape": "WorkerFleetAdditionalFixedProperties"}}}, "UpdateWorkerRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "WorkerGenericIdentifier"}, "name": {"shape": "Name"}, "additionalTransientProperties": {"shape": "WorkerAdditionalTransientPropertiesJson"}, "additionalFixedProperties": {"shape": "WorkerAdditionalFixedPropertiesJson"}, "vendorProperties": {"shape": "VendorProperties"}, "position": {"shape": "PositionCoordinates"}, "orientation": {"shape": "Orientation"}}}, "UpdateWorkerResponse": {"type": "structure", "required": ["arn", "id", "fleet", "updatedAt", "name"], "members": {"arn": {"shape": "WorkerArn"}, "id": {"shape": "WorkerId"}, "fleet": {"shape": "WorkerFleetArn"}, "updatedAt": {"shape": "UpdatedAtTimestamp"}, "name": {"shape": "Name"}, "additionalTransientProperties": {"shape": "WorkerAdditionalTransientPropertiesJson"}, "additionalFixedProperties": {"shape": "WorkerAdditionalFixedPropertiesJson"}, "orientation": {"shape": "Orientation"}, "vendorProperties": {"shape": "VendorProperties"}, "position": {"shape": "PositionCoordinates"}}}, "UpdatedAtTimestamp": {"type": "timestamp", "documentation": "<p>Timestamp at which the resource was last updated.</p>"}, "ValidationException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>Exception thrown if an invalid parameter is provided to an API.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "VendorAdditionalFixedPropertiesJson": {"type": "string", "documentation": "<p>JSON blob containing unstructured vendor properties that are fixed and won't change during regular operation.</p>", "max": 131072, "min": 1}, "VendorAdditionalTransientPropertiesJson": {"type": "string", "documentation": "<p>JSON blob containing unstructured vendor properties that are transient and may change during regular operation.</p>", "max": 131072, "min": 1}, "VendorProperties": {"type": "structure", "required": ["vendorWorkerId"], "members": {"vendorWorkerId": {"shape": "VendorWorkerId"}, "vendorWorkerIpAddress": {"shape": "VendorWorkerIpAddress"}, "vendorAdditionalTransientProperties": {"shape": "VendorAdditionalTransientPropertiesJson"}, "vendorAdditionalFixedProperties": {"shape": "VendorAdditionalFixedPropertiesJson"}}, "documentation": "<p>Properties of the worker that are provided by the vendor FMS.</p>"}, "VendorWorkerId": {"type": "string", "documentation": "<p>The worker ID defined by the vendor FMS.</p>", "max": 255, "min": 1}, "VendorWorkerIpAddress": {"type": "string", "documentation": "<p>The worker IP address defined by the vendor FMS.</p>", "max": 45, "min": 1}, "Worker": {"type": "structure", "required": ["arn", "id", "fleet", "createdAt", "updatedAt", "name", "site"], "members": {"arn": {"shape": "WorkerArn"}, "id": {"shape": "WorkerId"}, "fleet": {"shape": "WorkerFleetArn"}, "createdAt": {"shape": "CreatedAtTimestamp"}, "updatedAt": {"shape": "UpdatedAtTimestamp"}, "name": {"shape": "Name"}, "site": {"shape": "SiteArn"}, "additionalTransientProperties": {"shape": "WorkerAdditionalTransientPropertiesJson"}, "additionalFixedProperties": {"shape": "WorkerAdditionalFixedPropertiesJson"}, "vendorProperties": {"shape": "VendorProperties"}, "position": {"shape": "PositionCoordinates"}, "orientation": {"shape": "Orientation"}}, "documentation": "<p>A unit capable of performing tasks.</p>"}, "WorkerAdditionalFixedPropertiesJson": {"type": "string", "documentation": "<p>JSON blob containing unstructured worker properties that are fixed and won't change during regular operation.</p>", "max": 131072, "min": 1}, "WorkerAdditionalTransientPropertiesJson": {"type": "string", "documentation": "<p>JSON blob containing unstructured worker properties that are transient and may change during regular operation.</p>", "max": 131072, "min": 1}, "WorkerArn": {"type": "string", "documentation": "<p>Full ARN of the worker.</p>", "max": 1011, "min": 1, "pattern": "arn:aws:iotroborunner:[\\w-]+:\\w+:site/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/worker-fleet/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/worker/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"}, "WorkerFleet": {"type": "structure", "required": ["arn", "id", "name", "site", "createdAt", "updatedAt"], "members": {"arn": {"shape": "WorkerFleetArn"}, "id": {"shape": "WorkerFleetId"}, "name": {"shape": "Name"}, "site": {"shape": "SiteArn"}, "createdAt": {"shape": "CreatedAtTimestamp"}, "updatedAt": {"shape": "UpdatedAtTimestamp"}, "additionalFixedProperties": {"shape": "WorkerFleetAdditionalFixedProperties"}}, "documentation": "<p>A collection of workers organized within a facility.</p>"}, "WorkerFleetAdditionalFixedProperties": {"type": "string", "documentation": "<p>JSON blob containing additional fixed properties regarding the worker fleet</p>", "max": 131072, "min": 1}, "WorkerFleetArn": {"type": "string", "documentation": "<p>Full ARN of the worker fleet.</p>", "max": 1011, "min": 1, "pattern": "arn:aws:iotroborunner:[\\w-]+:\\w+:site/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/worker-fleet/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"}, "WorkerFleetGenericIdentifier": {"type": "string", "documentation": "<p>Full ARN of the worker fleet.</p>", "max": 1011, "min": 1, "pattern": "arn:aws:iotroborunner:[\\w-]+:\\w+:site/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/worker-fleet/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"}, "WorkerFleetId": {"type": "string", "documentation": "<p>Filters access by the worker fleet's identifier</p>", "max": 255, "min": 1}, "WorkerFleets": {"type": "list", "member": {"shape": "WorkerFleet"}, "documentation": "<p>List of worker fleets.</p>"}, "WorkerGenericIdentifier": {"type": "string", "documentation": "<p>Full ARN of the worker.</p>", "max": 1011, "min": 1, "pattern": "arn:aws:iotroborunner:[\\w-]+:\\w+:site/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/worker-fleet/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/worker/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"}, "WorkerId": {"type": "string", "documentation": "<p>Filters access by the workers identifier</p>", "max": 255, "min": 1}, "Workers": {"type": "list", "member": {"shape": "Worker"}, "documentation": "<p>List of workers.</p>"}}, "documentation": "<p>An example service, deployed with the Octane Service creator, which will echo the string</p>"}