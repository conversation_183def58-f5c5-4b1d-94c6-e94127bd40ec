{"version": "2.0", "metadata": {"apiVersion": "2022-11-28", "endpointPrefix": "omics", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "Amazon Omics", "serviceId": "Omics", "signatureVersion": "v4", "signingName": "omics", "uid": "omics-2022-11-28"}, "operations": {"AbortMultipartReadSetUpload": {"name": "AbortMultipartReadSetUpload", "http": {"method": "DELETE", "requestUri": "/sequencestore/{sequenceStoreId}/upload/{uploadId}/abort", "responseCode": 200}, "input": {"shape": "AbortMultipartReadSetUploadRequest"}, "output": {"shape": "AbortMultipartReadSetUploadResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "NotSupportedOperationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p> Stops a multipart upload. </p>", "endpoint": {"hostPrefix": "control-storage-"}}, "AcceptShare": {"name": "AcceptShare", "http": {"method": "POST", "requestUri": "/share/{shareId}", "responseCode": 200}, "input": {"shape": "AcceptShareRequest"}, "output": {"shape": "AcceptShareResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Accepts a share for an analytics store. </p>", "endpoint": {"hostPrefix": "analytics-"}}, "BatchDeleteReadSet": {"name": "BatchDeleteReadSet", "http": {"method": "POST", "requestUri": "/sequencestore/{sequenceStoreId}/readset/batch/delete", "responseCode": 200}, "input": {"shape": "BatchDeleteReadSetRequest"}, "output": {"shape": "BatchDeleteReadSetResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Deletes one or more read sets.</p>", "endpoint": {"hostPrefix": "control-storage-"}, "idempotent": true}, "CancelAnnotationImportJob": {"name": "CancelAnnotationImportJob", "http": {"method": "DELETE", "requestUri": "/import/annotation/{jobId}", "responseCode": 200}, "input": {"shape": "CancelAnnotationImportRequest"}, "output": {"shape": "CancelAnnotationImportResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Cancels an annotation import job.</p>", "endpoint": {"hostPrefix": "analytics-"}, "idempotent": true}, "CancelRun": {"name": "CancelRun", "http": {"method": "POST", "requestUri": "/run/{id}/cancel", "responseCode": 202}, "input": {"shape": "CancelRunRequest"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Cancels a run.</p>", "endpoint": {"hostPrefix": "workflows-"}}, "CancelVariantImportJob": {"name": "CancelVariantImportJob", "http": {"method": "DELETE", "requestUri": "/import/variant/{jobId}", "responseCode": 200}, "input": {"shape": "CancelVariantImportRequest"}, "output": {"shape": "CancelVariantImportResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Cancels a variant import job.</p>", "endpoint": {"hostPrefix": "analytics-"}, "idempotent": true}, "CompleteMultipartReadSetUpload": {"name": "CompleteMultipartReadSetUpload", "http": {"method": "POST", "requestUri": "/sequencestore/{sequenceStoreId}/upload/{uploadId}/complete", "responseCode": 200}, "input": {"shape": "CompleteMultipartReadSetUploadRequest"}, "output": {"shape": "CompleteMultipartReadSetUploadResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "NotSupportedOperationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p> Concludes a multipart upload once you have uploaded all the components. </p>", "endpoint": {"hostPrefix": "storage-"}}, "CreateAnnotationStore": {"name": "CreateAnnotationStore", "http": {"method": "POST", "requestUri": "/annotationStore", "responseCode": 200}, "input": {"shape": "CreateAnnotationStoreRequest"}, "output": {"shape": "CreateAnnotationStoreResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates an annotation store.</p>", "endpoint": {"hostPrefix": "analytics-"}}, "CreateAnnotationStoreVersion": {"name": "CreateAnnotationStoreVersion", "http": {"method": "POST", "requestUri": "/annotationStore/{name}/version", "responseCode": 200}, "input": {"shape": "CreateAnnotationStoreVersionRequest"}, "output": {"shape": "CreateAnnotationStoreVersionResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Creates a new version of an annotation store. </p>", "endpoint": {"hostPrefix": "analytics-"}}, "CreateMultipartReadSetUpload": {"name": "CreateMultipartReadSetUpload", "http": {"method": "POST", "requestUri": "/sequencestore/{sequenceStoreId}/upload", "responseCode": 200}, "input": {"shape": "CreateMultipartReadSetUploadRequest"}, "output": {"shape": "CreateMultipartReadSetUploadResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "NotSupportedOperationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p> Begins a multipart read set upload. </p>", "endpoint": {"hostPrefix": "control-storage-"}}, "CreateReferenceStore": {"name": "CreateReferenceStore", "http": {"method": "POST", "requestUri": "/referencestore", "responseCode": 200}, "input": {"shape": "CreateReferenceStoreRequest"}, "output": {"shape": "CreateReferenceStoreResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Creates a reference store.</p>", "endpoint": {"hostPrefix": "control-storage-"}}, "CreateRunGroup": {"name": "CreateRunGroup", "http": {"method": "POST", "requestUri": "/runGroup", "responseCode": 201}, "input": {"shape": "CreateRunGroupRequest"}, "output": {"shape": "CreateRunGroupResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Creates a run group.</p>", "endpoint": {"hostPrefix": "workflows-"}}, "CreateSequenceStore": {"name": "CreateSequenceStore", "http": {"method": "POST", "requestUri": "/sequencestore", "responseCode": 200}, "input": {"shape": "CreateSequenceStoreRequest"}, "output": {"shape": "CreateSequenceStoreResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Creates a sequence store.</p>", "endpoint": {"hostPrefix": "control-storage-"}}, "CreateShare": {"name": "CreateShare", "http": {"method": "POST", "requestUri": "/share", "responseCode": 200}, "input": {"shape": "CreateShareRequest"}, "output": {"shape": "CreateShareResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Creates a share offer that can be accepted outside the account by a subscriber. The share is created by the owner and accepted by the principal subscriber. </p>", "endpoint": {"hostPrefix": "analytics-"}}, "CreateVariantStore": {"name": "CreateVariantStore", "http": {"method": "POST", "requestUri": "/variantStore", "responseCode": 200}, "input": {"shape": "CreateVariantStoreRequest"}, "output": {"shape": "CreateVariantStoreResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates a variant store.</p>", "endpoint": {"hostPrefix": "analytics-"}}, "CreateWorkflow": {"name": "CreateWorkflow", "http": {"method": "POST", "requestUri": "/workflow", "responseCode": 201}, "input": {"shape": "CreateWorkflowRequest"}, "output": {"shape": "CreateWorkflowResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Creates a workflow.</p>", "endpoint": {"hostPrefix": "workflows-"}}, "DeleteAnnotationStore": {"name": "DeleteAnnotationStore", "http": {"method": "DELETE", "requestUri": "/annotationStore/{name}", "responseCode": 200}, "input": {"shape": "DeleteAnnotationStoreRequest"}, "output": {"shape": "DeleteAnnotationStoreResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes an annotation store.</p>", "endpoint": {"hostPrefix": "analytics-"}, "idempotent": true}, "DeleteAnnotationStoreVersions": {"name": "DeleteAnnotationStoreVersions", "http": {"method": "POST", "requestUri": "/annotationStore/{name}/versions/delete", "responseCode": 200}, "input": {"shape": "DeleteAnnotationStoreVersionsRequest"}, "output": {"shape": "DeleteAnnotationStoreVersionsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Deletes one or multiple versions of an annotation store. </p>", "endpoint": {"hostPrefix": "analytics-"}, "idempotent": true}, "DeleteReference": {"name": "DeleteReference", "http": {"method": "DELETE", "requestUri": "/referencestore/{referenceStoreId}/reference/{id}", "responseCode": 200}, "input": {"shape": "DeleteReferenceRequest"}, "output": {"shape": "DeleteReferenceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Deletes a genome reference.</p>", "endpoint": {"hostPrefix": "control-storage-"}, "idempotent": true}, "DeleteReferenceStore": {"name": "DeleteReferenceStore", "http": {"method": "DELETE", "requestUri": "/referencestore/{id}", "responseCode": 200}, "input": {"shape": "DeleteReferenceStoreRequest"}, "output": {"shape": "DeleteReferenceStoreResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Deletes a genome reference store.</p>", "endpoint": {"hostPrefix": "control-storage-"}, "idempotent": true}, "DeleteRun": {"name": "DeleteRun", "http": {"method": "DELETE", "requestUri": "/run/{id}", "responseCode": 202}, "input": {"shape": "DeleteRunRequest"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Deletes a workflow run.</p>", "endpoint": {"hostPrefix": "workflows-"}, "idempotent": true}, "DeleteRunGroup": {"name": "DeleteRunGroup", "http": {"method": "DELETE", "requestUri": "/runGroup/{id}", "responseCode": 202}, "input": {"shape": "DeleteRunGroupRequest"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Deletes a workflow run group.</p>", "endpoint": {"hostPrefix": "workflows-"}, "idempotent": true}, "DeleteSequenceStore": {"name": "DeleteSequenceStore", "http": {"method": "DELETE", "requestUri": "/sequencestore/{id}", "responseCode": 200}, "input": {"shape": "DeleteSequenceStoreRequest"}, "output": {"shape": "DeleteSequenceStoreResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Deletes a sequence store.</p>", "endpoint": {"hostPrefix": "control-storage-"}, "idempotent": true}, "DeleteShare": {"name": "DeleteShare", "http": {"method": "DELETE", "requestUri": "/share/{shareId}", "responseCode": 200}, "input": {"shape": "DeleteShareRequest"}, "output": {"shape": "DeleteShareResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Deletes a share of an analytics store. </p>", "endpoint": {"hostPrefix": "analytics-"}, "idempotent": true}, "DeleteVariantStore": {"name": "DeleteVariantStore", "http": {"method": "DELETE", "requestUri": "/variantStore/{name}", "responseCode": 200}, "input": {"shape": "DeleteVariantStoreRequest"}, "output": {"shape": "DeleteVariantStoreResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes a variant store.</p>", "endpoint": {"hostPrefix": "analytics-"}, "idempotent": true}, "DeleteWorkflow": {"name": "DeleteWorkflow", "http": {"method": "DELETE", "requestUri": "/workflow/{id}", "responseCode": 202}, "input": {"shape": "DeleteWorkflowRequest"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Deletes a workflow.</p>", "endpoint": {"hostPrefix": "workflows-"}, "idempotent": true}, "GetAnnotationImportJob": {"name": "GetAnnotationImportJob", "http": {"method": "GET", "requestUri": "/import/annotation/{jobId}", "responseCode": 200}, "input": {"shape": "GetAnnotationImportRequest"}, "output": {"shape": "GetAnnotationImportResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets information about an annotation import job.</p>", "endpoint": {"hostPrefix": "analytics-"}}, "GetAnnotationStore": {"name": "GetAnnotationStore", "http": {"method": "GET", "requestUri": "/annotationStore/{name}", "responseCode": 200}, "input": {"shape": "GetAnnotationStoreRequest"}, "output": {"shape": "GetAnnotationStoreResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets information about an annotation store.</p>", "endpoint": {"hostPrefix": "analytics-"}}, "GetAnnotationStoreVersion": {"name": "GetAnnotationStoreVersion", "http": {"method": "GET", "requestUri": "/annotationStore/{name}/version/{versionName}", "responseCode": 200}, "input": {"shape": "GetAnnotationStoreVersionRequest"}, "output": {"shape": "GetAnnotationStoreVersionResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Retrieves the metadata for an annotation store version. </p>", "endpoint": {"hostPrefix": "analytics-"}}, "GetReadSet": {"name": "GetReadSet", "http": {"method": "GET", "requestUri": "/sequencestore/{sequenceStoreId}/readset/{id}", "responseCode": 200}, "input": {"shape": "GetReadSetRequest"}, "output": {"shape": "GetReadSetResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "RangeNotSatisfiableException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Gets a file from a read set.</p>", "endpoint": {"hostPrefix": "storage-"}}, "GetReadSetActivationJob": {"name": "GetReadSetActivationJob", "http": {"method": "GET", "requestUri": "/sequencestore/{sequenceStoreId}/activationjob/{id}", "responseCode": 200}, "input": {"shape": "GetReadSetActivationJobRequest"}, "output": {"shape": "GetReadSetActivationJobResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Gets information about a read set activation job.</p>", "endpoint": {"hostPrefix": "control-storage-"}}, "GetReadSetExportJob": {"name": "GetReadSetExportJob", "http": {"method": "GET", "requestUri": "/sequencestore/{sequenceStoreId}/exportjob/{id}", "responseCode": 200}, "input": {"shape": "GetReadSetExportJobRequest"}, "output": {"shape": "GetReadSetExportJobResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Gets information about a read set export job.</p>", "endpoint": {"hostPrefix": "control-storage-"}}, "GetReadSetImportJob": {"name": "GetReadSetImportJob", "http": {"method": "GET", "requestUri": "/sequencestore/{sequenceStoreId}/importjob/{id}", "responseCode": 200}, "input": {"shape": "GetReadSetImportJobRequest"}, "output": {"shape": "GetReadSetImportJobResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Gets information about a read set import job.</p>", "endpoint": {"hostPrefix": "control-storage-"}}, "GetReadSetMetadata": {"name": "GetReadSetMetadata", "http": {"method": "GET", "requestUri": "/sequencestore/{sequenceStoreId}/readset/{id}/metadata", "responseCode": 200}, "input": {"shape": "GetReadSetMetadataRequest"}, "output": {"shape": "GetReadSetMetadataResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Gets details about a read set.</p>", "endpoint": {"hostPrefix": "control-storage-"}}, "GetReference": {"name": "GetReference", "http": {"method": "GET", "requestUri": "/referencestore/{referenceStoreId}/reference/{id}", "responseCode": 200}, "input": {"shape": "GetReferenceRequest"}, "output": {"shape": "GetReferenceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "RangeNotSatisfiableException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Gets a reference file.</p>", "endpoint": {"hostPrefix": "storage-"}}, "GetReferenceImportJob": {"name": "GetReferenceImportJob", "http": {"method": "GET", "requestUri": "/referencestore/{referenceStoreId}/importjob/{id}", "responseCode": 200}, "input": {"shape": "GetReferenceImportJobRequest"}, "output": {"shape": "GetReferenceImportJobResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Gets information about a reference import job.</p>", "endpoint": {"hostPrefix": "control-storage-"}}, "GetReferenceMetadata": {"name": "GetReferenceMetadata", "http": {"method": "GET", "requestUri": "/referencestore/{referenceStoreId}/reference/{id}/metadata", "responseCode": 200}, "input": {"shape": "GetReferenceMetadataRequest"}, "output": {"shape": "GetReferenceMetadataResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Gets information about a genome reference's metadata.</p>", "endpoint": {"hostPrefix": "control-storage-"}}, "GetReferenceStore": {"name": "GetReferenceStore", "http": {"method": "GET", "requestUri": "/referencestore/{id}", "responseCode": 200}, "input": {"shape": "GetReferenceStoreRequest"}, "output": {"shape": "GetReferenceStoreResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Gets information about a reference store.</p>", "endpoint": {"hostPrefix": "control-storage-"}}, "GetRun": {"name": "GetRun", "http": {"method": "GET", "requestUri": "/run/{id}", "responseCode": 200}, "input": {"shape": "GetRunRequest"}, "output": {"shape": "GetRunResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Gets information about a workflow run.</p>", "endpoint": {"hostPrefix": "workflows-"}}, "GetRunGroup": {"name": "GetRunGroup", "http": {"method": "GET", "requestUri": "/runGroup/{id}", "responseCode": 200}, "input": {"shape": "GetRunGroupRequest"}, "output": {"shape": "GetRunGroupResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Gets information about a workflow run group.</p>", "endpoint": {"hostPrefix": "workflows-"}}, "GetRunTask": {"name": "GetRunTask", "http": {"method": "GET", "requestUri": "/run/{id}/task/{taskId}", "responseCode": 200}, "input": {"shape": "GetRunTaskRequest"}, "output": {"shape": "GetRunTaskResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Gets information about a workflow run task.</p>", "endpoint": {"hostPrefix": "workflows-"}}, "GetSequenceStore": {"name": "GetSequenceStore", "http": {"method": "GET", "requestUri": "/sequencestore/{id}", "responseCode": 200}, "input": {"shape": "GetSequenceStoreRequest"}, "output": {"shape": "GetSequenceStoreResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Gets information about a sequence store.</p>", "endpoint": {"hostPrefix": "control-storage-"}}, "GetShare": {"name": "GetShare", "http": {"method": "GET", "requestUri": "/share/{shareId}", "responseCode": 200}, "input": {"shape": "GetShareRequest"}, "output": {"shape": "GetShareResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Retrieves the metadata for a share. </p>", "endpoint": {"hostPrefix": "analytics-"}}, "GetVariantImportJob": {"name": "GetVariantImportJob", "http": {"method": "GET", "requestUri": "/import/variant/{jobId}", "responseCode": 200}, "input": {"shape": "GetVariantImportRequest"}, "output": {"shape": "GetVariantImportResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets information about a variant import job.</p>", "endpoint": {"hostPrefix": "analytics-"}}, "GetVariantStore": {"name": "GetVariantStore", "http": {"method": "GET", "requestUri": "/variantStore/{name}", "responseCode": 200}, "input": {"shape": "GetVariantStoreRequest"}, "output": {"shape": "GetVariantStoreResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets information about a variant store.</p>", "endpoint": {"hostPrefix": "analytics-"}}, "GetWorkflow": {"name": "GetWorkflow", "http": {"method": "GET", "requestUri": "/workflow/{id}", "responseCode": 200}, "input": {"shape": "GetWorkflowRequest"}, "output": {"shape": "GetWorkflowResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Gets information about a workflow.</p>", "endpoint": {"hostPrefix": "workflows-"}}, "ListAnnotationImportJobs": {"name": "ListAnnotationImportJobs", "http": {"method": "POST", "requestUri": "/import/annotations", "responseCode": 200}, "input": {"shape": "ListAnnotationImportJobsRequest"}, "output": {"shape": "ListAnnotationImportJobsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a list of annotation import jobs.</p>", "endpoint": {"hostPrefix": "analytics-"}}, "ListAnnotationStoreVersions": {"name": "ListAnnotationStoreVersions", "http": {"method": "POST", "requestUri": "/annotationStore/{name}/versions", "responseCode": 200}, "input": {"shape": "ListAnnotationStoreVersionsRequest"}, "output": {"shape": "ListAnnotationStoreVersionsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Lists the versions of an annotation store. </p>", "endpoint": {"hostPrefix": "analytics-"}}, "ListAnnotationStores": {"name": "ListAnnotationStores", "http": {"method": "POST", "requestUri": "/annotationStores", "responseCode": 200}, "input": {"shape": "ListAnnotationStoresRequest"}, "output": {"shape": "ListAnnotationStoresResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a list of annotation stores.</p>", "endpoint": {"hostPrefix": "analytics-"}}, "ListMultipartReadSetUploads": {"name": "ListMultipartReadSetUploads", "http": {"method": "POST", "requestUri": "/sequencestore/{sequenceStoreId}/uploads", "responseCode": 200}, "input": {"shape": "ListMultipartReadSetUploadsRequest"}, "output": {"shape": "ListMultipartReadSetUploadsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "NotSupportedOperationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p> Lists all multipart read set uploads and their statuses. </p>", "endpoint": {"hostPrefix": "control-storage-"}}, "ListReadSetActivationJobs": {"name": "ListReadSetActivationJobs", "http": {"method": "POST", "requestUri": "/sequencestore/{sequenceStoreId}/activationjobs", "responseCode": 200}, "input": {"shape": "ListReadSetActivationJobsRequest"}, "output": {"shape": "ListReadSetActivationJobsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Retrieves a list of read set activation jobs.</p>", "endpoint": {"hostPrefix": "control-storage-"}}, "ListReadSetExportJobs": {"name": "ListReadSetExportJobs", "http": {"method": "POST", "requestUri": "/sequencestore/{sequenceStoreId}/exportjobs", "responseCode": 200}, "input": {"shape": "ListReadSetExportJobsRequest"}, "output": {"shape": "ListReadSetExportJobsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Retrieves a list of read set export jobs.</p>", "endpoint": {"hostPrefix": "control-storage-"}}, "ListReadSetImportJobs": {"name": "ListReadSetImportJobs", "http": {"method": "POST", "requestUri": "/sequencestore/{sequenceStoreId}/importjobs", "responseCode": 200}, "input": {"shape": "ListReadSetImportJobsRequest"}, "output": {"shape": "ListReadSetImportJobsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Retrieves a list of read set import jobs.</p>", "endpoint": {"hostPrefix": "control-storage-"}}, "ListReadSetUploadParts": {"name": "ListReadSetUploadParts", "http": {"method": "POST", "requestUri": "/sequencestore/{sequenceStoreId}/upload/{uploadId}/parts", "responseCode": 200}, "input": {"shape": "ListReadSetUploadPartsRequest"}, "output": {"shape": "ListReadSetUploadPartsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "NotSupportedOperationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p> This operation will list all parts in a requested multipart upload for a sequence store. </p>", "endpoint": {"hostPrefix": "control-storage-"}}, "ListReadSets": {"name": "ListReadSets", "http": {"method": "POST", "requestUri": "/sequencestore/{sequenceStoreId}/readsets", "responseCode": 200}, "input": {"shape": "ListReadSetsRequest"}, "output": {"shape": "ListReadSetsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Retrieves a list of read sets.</p>", "endpoint": {"hostPrefix": "control-storage-"}}, "ListReferenceImportJobs": {"name": "ListReferenceImportJobs", "http": {"method": "POST", "requestUri": "/referencestore/{referenceStoreId}/importjobs", "responseCode": 200}, "input": {"shape": "ListReferenceImportJobsRequest"}, "output": {"shape": "ListReferenceImportJobsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Retrieves a list of reference import jobs.</p>", "endpoint": {"hostPrefix": "control-storage-"}}, "ListReferenceStores": {"name": "ListReferenceStores", "http": {"method": "POST", "requestUri": "/referencestores", "responseCode": 200}, "input": {"shape": "ListReferenceStoresRequest"}, "output": {"shape": "ListReferenceStoresResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Retrieves a list of reference stores.</p>", "endpoint": {"hostPrefix": "control-storage-"}}, "ListReferences": {"name": "ListReferences", "http": {"method": "POST", "requestUri": "/referencestore/{referenceStoreId}/references", "responseCode": 200}, "input": {"shape": "ListReferencesRequest"}, "output": {"shape": "ListReferencesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Retrieves a list of references.</p>", "endpoint": {"hostPrefix": "control-storage-"}}, "ListRunGroups": {"name": "ListRunGroups", "http": {"method": "GET", "requestUri": "/runGroup", "responseCode": 200}, "input": {"shape": "ListRunGroupsRequest"}, "output": {"shape": "ListRunGroupsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Retrieves a list of run groups.</p>", "endpoint": {"hostPrefix": "workflows-"}}, "ListRunTasks": {"name": "ListRunTasks", "http": {"method": "GET", "requestUri": "/run/{id}/task", "responseCode": 200}, "input": {"shape": "ListRunTasksRequest"}, "output": {"shape": "ListRunTasksResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Retrieves a list of tasks for a run.</p>", "endpoint": {"hostPrefix": "workflows-"}}, "ListRuns": {"name": "ListRuns", "http": {"method": "GET", "requestUri": "/run", "responseCode": 200}, "input": {"shape": "ListRunsRequest"}, "output": {"shape": "ListRunsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Retrieves a list of runs.</p>", "endpoint": {"hostPrefix": "workflows-"}}, "ListSequenceStores": {"name": "ListSequenceStores", "http": {"method": "POST", "requestUri": "/sequencestores", "responseCode": 200}, "input": {"shape": "ListSequenceStoresRequest"}, "output": {"shape": "ListSequenceStoresResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Retrieves a list of sequence stores.</p>", "endpoint": {"hostPrefix": "control-storage-"}}, "ListShares": {"name": "ListShares", "http": {"method": "POST", "requestUri": "/shares", "responseCode": 200}, "input": {"shape": "ListSharesRequest"}, "output": {"shape": "ListSharesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Lists all shares associated with an account. </p>", "endpoint": {"hostPrefix": "analytics-"}}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Retrieves a list of tags for a resource.</p>", "endpoint": {"hostPrefix": "tags-"}}, "ListVariantImportJobs": {"name": "ListVariantImportJobs", "http": {"method": "POST", "requestUri": "/import/variants", "responseCode": 200}, "input": {"shape": "ListVariantImportJobsRequest"}, "output": {"shape": "ListVariantImportJobsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a list of variant import jobs.</p>", "endpoint": {"hostPrefix": "analytics-"}}, "ListVariantStores": {"name": "ListVariantStores", "http": {"method": "POST", "requestUri": "/variantStores", "responseCode": 200}, "input": {"shape": "ListVariantStoresRequest"}, "output": {"shape": "ListVariantStoresResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a list of variant stores.</p>", "endpoint": {"hostPrefix": "analytics-"}}, "ListWorkflows": {"name": "ListWorkflows", "http": {"method": "GET", "requestUri": "/workflow", "responseCode": 200}, "input": {"shape": "ListWorkflowsRequest"}, "output": {"shape": "ListWorkflowsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Retrieves a list of workflows.</p>", "endpoint": {"hostPrefix": "workflows-"}}, "StartAnnotationImportJob": {"name": "StartAnnotationImportJob", "http": {"method": "POST", "requestUri": "/import/annotation", "responseCode": 200}, "input": {"shape": "StartAnnotationImportRequest"}, "output": {"shape": "StartAnnotationImportResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Starts an annotation import job.</p>", "endpoint": {"hostPrefix": "analytics-"}}, "StartReadSetActivationJob": {"name": "StartReadSetActivationJob", "http": {"method": "POST", "requestUri": "/sequencestore/{sequenceStoreId}/activationjob", "responseCode": 200}, "input": {"shape": "StartReadSetActivationJobRequest"}, "output": {"shape": "StartReadSetActivationJobResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Activates an archived read set. To reduce storage charges, Amazon Omics archives unused read sets after 30 days.</p>", "endpoint": {"hostPrefix": "control-storage-"}}, "StartReadSetExportJob": {"name": "StartReadSetExportJob", "http": {"method": "POST", "requestUri": "/sequencestore/{sequenceStoreId}/exportjob", "responseCode": 200}, "input": {"shape": "StartReadSetExportJobRequest"}, "output": {"shape": "StartReadSetExportJobResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Exports a read set to Amazon S3.</p>", "endpoint": {"hostPrefix": "control-storage-"}}, "StartReadSetImportJob": {"name": "StartReadSetImportJob", "http": {"method": "POST", "requestUri": "/sequencestore/{sequenceStoreId}/importjob", "responseCode": 200}, "input": {"shape": "StartReadSetImportJobRequest"}, "output": {"shape": "StartReadSetImportJobResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Starts a read set import job.</p>", "endpoint": {"hostPrefix": "control-storage-"}}, "StartReferenceImportJob": {"name": "StartReferenceImportJob", "http": {"method": "POST", "requestUri": "/referencestore/{referenceStoreId}/importjob", "responseCode": 200}, "input": {"shape": "StartReferenceImportJobRequest"}, "output": {"shape": "StartReferenceImportJobResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Starts a reference import job.</p>", "endpoint": {"hostPrefix": "control-storage-"}}, "StartRun": {"name": "StartRun", "http": {"method": "POST", "requestUri": "/run", "responseCode": 201}, "input": {"shape": "StartRunRequest"}, "output": {"shape": "StartRunResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Starts a workflow run. To duplicate a run, specify the run's ID and a role ARN. The remaining parameters are copied from the previous run.</p> <p>The total number of runs in your account is subject to a quota per Region. To avoid needing to delete runs manually, you can set the retention mode to <code>REMOVE</code>. Runs with this setting are deleted automatically when the run quoata is exceeded.</p>", "endpoint": {"hostPrefix": "workflows-"}}, "StartVariantImportJob": {"name": "StartVariantImportJob", "http": {"method": "POST", "requestUri": "/import/variant", "responseCode": 200}, "input": {"shape": "StartVariantImportRequest"}, "output": {"shape": "StartVariantImportResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Starts a variant import job.</p>", "endpoint": {"hostPrefix": "analytics-"}}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}", "responseCode": 204}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Tags a resource.</p>", "endpoint": {"hostPrefix": "tags-"}}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 204}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Removes tags from a resource.</p>", "endpoint": {"hostPrefix": "tags-"}, "idempotent": true}, "UpdateAnnotationStore": {"name": "UpdateAnnotationStore", "http": {"method": "POST", "requestUri": "/annotationStore/{name}", "responseCode": 200}, "input": {"shape": "UpdateAnnotationStoreRequest"}, "output": {"shape": "UpdateAnnotationStoreResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates an annotation store.</p>", "endpoint": {"hostPrefix": "analytics-"}}, "UpdateAnnotationStoreVersion": {"name": "UpdateAnnotationStoreVersion", "http": {"method": "POST", "requestUri": "/annotationStore/{name}/version/{versionName}", "responseCode": 200}, "input": {"shape": "UpdateAnnotationStoreVersionRequest"}, "output": {"shape": "UpdateAnnotationStoreVersionResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Updates the description of an annotation store version. </p>", "endpoint": {"hostPrefix": "analytics-"}}, "UpdateRunGroup": {"name": "UpdateRunGroup", "http": {"method": "POST", "requestUri": "/runGroup/{id}", "responseCode": 202}, "input": {"shape": "UpdateRunGroupRequest"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Updates a run group.</p>", "endpoint": {"hostPrefix": "workflows-"}}, "UpdateVariantStore": {"name": "UpdateVariantStore", "http": {"method": "POST", "requestUri": "/variantStore/{name}", "responseCode": 200}, "input": {"shape": "UpdateVariantStoreRequest"}, "output": {"shape": "UpdateVariantStoreResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates a variant store.</p>", "endpoint": {"hostPrefix": "analytics-"}}, "UpdateWorkflow": {"name": "UpdateWorkflow", "http": {"method": "POST", "requestUri": "/workflow/{id}", "responseCode": 202}, "input": {"shape": "UpdateWorkflowRequest"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p>Updates a workflow.</p>", "endpoint": {"hostPrefix": "workflows-"}}, "UploadReadSetPart": {"name": "UploadReadSetPart", "http": {"method": "PUT", "requestUri": "/sequencestore/{sequenceStoreId}/upload/{uploadId}/part", "responseCode": 200}, "input": {"shape": "UploadReadSetPartRequest"}, "output": {"shape": "UploadReadSetPartResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "NotSupportedOperationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p> This operation uploads a specific part of a read set. If you upload a new part using a previously used part number, the previously uploaded part will be overwritten. </p>", "authtype": "v4-unsigned-body", "endpoint": {"hostPrefix": "storage-"}}}, "shapes": {"AbortMultipartReadSetUploadRequest": {"type": "structure", "required": ["sequenceStoreId", "uploadId"], "members": {"sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p> The sequence store ID for the store involved in the multipart upload. </p>", "location": "uri", "locationName": "sequenceStoreId"}, "uploadId": {"shape": "UploadId", "documentation": "<p> The ID for the multipart upload. </p>", "location": "uri", "locationName": "uploadId"}}}, "AbortMultipartReadSetUploadResponse": {"type": "structure", "members": {}}, "Accelerators": {"type": "string", "enum": ["GPU"], "max": 64, "min": 1}, "AcceptShareRequest": {"type": "structure", "required": ["shareId"], "members": {"shareId": {"shape": "String", "documentation": "<p> The ID for a share offer for analytics store data. </p>", "location": "uri", "locationName": "shareId"}}}, "AcceptShareResponse": {"type": "structure", "members": {"status": {"shape": "ShareStatus", "documentation": "<p> The status of an analytics store share. </p>"}}}, "AccessDeniedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>You do not have sufficient access to perform this action.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "ActivateReadSetFilter": {"type": "structure", "members": {"status": {"shape": "ReadSetActivationJobStatus", "documentation": "<p>The filter's status.</p>"}, "createdAfter": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The filter's start date.</p>"}, "createdBefore": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The filter's end date.</p>"}}, "documentation": "<p>A read set activation job filter.</p>"}, "ActivateReadSetJobItem": {"type": "structure", "required": ["id", "sequenceStoreId", "status", "creationTime"], "members": {"id": {"shape": "ActivationJobId", "documentation": "<p>The job's ID.</p>"}, "sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p>The job's sequence store ID.</p>"}, "status": {"shape": "ReadSetActivationJobStatus", "documentation": "<p>The job's status.</p>"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the job was created.</p>"}, "completionTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the job completed.</p>"}}, "documentation": "<p>A read set activation job.</p>"}, "ActivateReadSetJobList": {"type": "list", "member": {"shape": "ActivateReadSetJobItem"}}, "ActivateReadSetSourceItem": {"type": "structure", "required": ["readSetId", "status"], "members": {"readSetId": {"shape": "ReadSetId", "documentation": "<p>The source's read set ID.</p>"}, "status": {"shape": "ReadSetActivationJobItemStatus", "documentation": "<p>The source's status.</p>"}, "statusMessage": {"shape": "JobStatusMessage", "documentation": "<p>The source's status message.</p>"}}, "documentation": "<p>A source for a read set activation job.</p>"}, "ActivateReadSetSourceList": {"type": "list", "member": {"shape": "ActivateReadSetSourceItem"}}, "ActivationJobId": {"type": "string", "max": 36, "min": 10, "pattern": "[0-9]+"}, "AnnotationFieldMap": {"type": "map", "key": {"shape": "AnnotationFieldMapKeyString"}, "value": {"shape": "AnnotationFieldMapValueString"}}, "AnnotationFieldMapKeyString": {"type": "string", "max": 21, "min": 1}, "AnnotationFieldMapValueString": {"type": "string", "max": 21, "min": 1}, "AnnotationImportItemDetail": {"type": "structure", "required": ["source", "jobStatus"], "members": {"source": {"shape": "S3Uri", "documentation": "<p>The source file's location in Amazon S3.</p>"}, "jobStatus": {"shape": "JobStatus", "documentation": "<p>The item's job status.</p>"}}, "documentation": "<p>Details about an imported annotation item.</p>"}, "AnnotationImportItemDetails": {"type": "list", "member": {"shape": "AnnotationImportItemDetail"}, "max": 1, "min": 1}, "AnnotationImportItemSource": {"type": "structure", "required": ["source"], "members": {"source": {"shape": "S3Uri", "documentation": "<p>The source file's location in Amazon S3.</p>"}}, "documentation": "<p>A source for an annotation import job.</p>"}, "AnnotationImportItemSources": {"type": "list", "member": {"shape": "AnnotationImportItemSource"}, "min": 1}, "AnnotationImportJobItem": {"type": "structure", "required": ["id", "destinationName", "versionName", "roleArn", "status", "creationTime", "updateTime"], "members": {"id": {"shape": "String", "documentation": "<p>The job's ID.</p>"}, "destinationName": {"shape": "String", "documentation": "<p>The job's destination annotation store.</p>"}, "versionName": {"shape": "VersionName", "documentation": "<p> The name of the annotation store version. </p>"}, "roleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The job's service role ARN.</p>"}, "status": {"shape": "JobStatus", "documentation": "<p>The job's status.</p>"}, "creationTime": {"shape": "CreationTime", "documentation": "<p>When the job was created.</p>"}, "updateTime": {"shape": "UpdateTime", "documentation": "<p>When the job was updated.</p>"}, "completionTime": {"shape": "CompletionTime", "documentation": "<p>When the job completed.</p>"}, "runLeftNormalization": {"shape": "RunLeftNormalization", "documentation": "<p>The job's left normalization setting.</p>"}, "annotationFields": {"shape": "AnnotationFieldMap", "documentation": "<p> The annotation schema generated by the parsed annotation data. </p>"}}, "documentation": "<p>An annotation import job.</p>"}, "AnnotationImportJobItems": {"type": "list", "member": {"shape": "AnnotationImportJobItem"}}, "AnnotationStoreItem": {"type": "structure", "required": ["id", "reference", "status", "storeArn", "name", "storeFormat", "description", "sseConfig", "creationTime", "updateTime", "statusMessage", "storeSizeBytes"], "members": {"id": {"shape": "ResourceId", "documentation": "<p>The store's ID.</p>"}, "reference": {"shape": "ReferenceItem", "documentation": "<p>The store's genome reference.</p>"}, "status": {"shape": "StoreStatus", "documentation": "<p>The store's status.</p>"}, "storeArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The store's ARN.</p>"}, "name": {"shape": "String", "documentation": "<p>The store's name.</p>"}, "storeFormat": {"shape": "StoreFormat", "documentation": "<p>The store's file format.</p>"}, "description": {"shape": "Description", "documentation": "<p>The store's description.</p>"}, "sseConfig": {"shape": "SseConfig", "documentation": "<p>The store's server-side encryption (SSE) settings.</p>"}, "creationTime": {"shape": "CreationTime", "documentation": "<p>The store's creation time.</p>"}, "updateTime": {"shape": "UpdateTime", "documentation": "<p>When the store was updated.</p>"}, "statusMessage": {"shape": "StatusMessage", "documentation": "<p>The store's status message.</p>"}, "storeSizeBytes": {"shape": "<PERSON>", "documentation": "<p>The store's size in bytes.</p>"}}, "documentation": "<p>An annotation store.</p>"}, "AnnotationStoreItems": {"type": "list", "member": {"shape": "AnnotationStoreItem"}}, "AnnotationStoreVersionItem": {"type": "structure", "required": ["storeId", "id", "status", "versionArn", "name", "versionName", "description", "creationTime", "updateTime", "statusMessage", "versionSizeBytes"], "members": {"storeId": {"shape": "ResourceId", "documentation": "<p> The store ID for an annotation store version. </p>"}, "id": {"shape": "ResourceId", "documentation": "<p> The annotation store version ID. </p>"}, "status": {"shape": "VersionStatus", "documentation": "<p> The status of an annotation store version. </p>"}, "versionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p> The Arn for an annotation store version. </p>"}, "name": {"shape": "StoreName", "documentation": "<p> A name given to an annotation store version to distinguish it from others. </p>"}, "versionName": {"shape": "VersionName", "documentation": "<p> The name of an annotation store version. </p>"}, "description": {"shape": "Description", "documentation": "<p> The description of an annotation store version. </p>"}, "creationTime": {"shape": "CreationTime", "documentation": "<p> The time stamp for when an annotation store version was created. </p>"}, "updateTime": {"shape": "UpdateTime", "documentation": "<p> The time stamp for when an annotation store version was updated. </p>"}, "statusMessage": {"shape": "StatusMessage", "documentation": "<p> The status of an annotation store version. </p>"}, "versionSizeBytes": {"shape": "<PERSON>", "documentation": "<p> The size of an annotation store version in Bytes. </p>"}}, "documentation": "<p> Annotation store versions. </p>"}, "AnnotationStoreVersionItems": {"type": "list", "member": {"shape": "AnnotationStoreVersionItem"}}, "AnnotationType": {"type": "string", "enum": ["GENERIC", "CHR_POS", "CHR_POS_REF_ALT", "CHR_START_END_ONE_BASE", "CHR_START_END_REF_ALT_ONE_BASE", "CHR_START_END_ZERO_BASE", "CHR_START_END_REF_ALT_ZERO_BASE"]}, "Arn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:([^: ]*):([^: ]*):([^: ]*):([0-9]{12}):([^: ]*)"}, "ArnList": {"type": "list", "member": {"shape": "String"}, "max": 10, "min": 1}, "BatchDeleteReadSetRequest": {"type": "structure", "required": ["ids", "sequenceStoreId"], "members": {"ids": {"shape": "ReadSetIdList", "documentation": "<p>The read sets' IDs.</p>"}, "sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p>The read sets' sequence store ID.</p>", "location": "uri", "locationName": "sequenceStoreId"}}}, "BatchDeleteReadSetResponse": {"type": "structure", "members": {"errors": {"shape": "ReadSetBatchErrorList", "documentation": "<p>Errors returned by individual delete operations.</p>"}}}, "Blob": {"type": "blob"}, "Boolean": {"type": "boolean", "box": true}, "CancelAnnotationImportRequest": {"type": "structure", "required": ["jobId"], "members": {"jobId": {"shape": "ResourceId", "documentation": "<p>The job's ID.</p>", "location": "uri", "locationName": "jobId"}}}, "CancelAnnotationImportResponse": {"type": "structure", "members": {}}, "CancelRunRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "RunId", "documentation": "<p>The run's ID.</p>", "location": "uri", "locationName": "id"}}}, "CancelVariantImportRequest": {"type": "structure", "required": ["jobId"], "members": {"jobId": {"shape": "ResourceId", "documentation": "<p>The job's ID.</p>", "location": "uri", "locationName": "jobId"}}}, "CancelVariantImportResponse": {"type": "structure", "members": {}}, "ClientToken": {"type": "string", "max": 127, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "CommentChar": {"type": "string", "max": 1, "min": 1}, "CompleteMultipartReadSetUploadRequest": {"type": "structure", "required": ["sequenceStoreId", "uploadId", "parts"], "members": {"sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p> The sequence store ID for the store involved in the multipart upload. </p>", "location": "uri", "locationName": "sequenceStoreId"}, "uploadId": {"shape": "UploadId", "documentation": "<p> The ID for the multipart upload. </p>", "location": "uri", "locationName": "uploadId"}, "parts": {"shape": "CompleteReadSetUploadPartList", "documentation": "<p> The individual uploads or parts of a multipart upload. </p>"}}}, "CompleteMultipartReadSetUploadResponse": {"type": "structure", "required": ["readSetId"], "members": {"readSetId": {"shape": "ReadSetId", "documentation": "<p> The read set ID created for an uploaded read set. </p>"}}}, "CompleteReadSetUploadPartList": {"type": "list", "member": {"shape": "CompleteReadSetUploadPartListItem"}}, "CompleteReadSetUploadPartListItem": {"type": "structure", "required": ["partNumber", "partSource", "checksum"], "members": {"partNumber": {"shape": "CompleteReadSetUploadPartListItemPartNumberInteger", "documentation": "<p> A number identifying the part in a read set upload. </p>"}, "partSource": {"shape": "ReadSetPartSource", "documentation": "<p> The source file of the part being uploaded. </p>"}, "checksum": {"shape": "String", "documentation": "<p> A unique identifier used to confirm that parts are being added to the correct upload. </p>"}}, "documentation": "<p> Part of the response to the CompleteReadSetUpload API, including metadata. </p>"}, "CompleteReadSetUploadPartListItemPartNumberInteger": {"type": "integer", "box": true, "max": 10000, "min": 1}, "CompletionTime": {"type": "timestamp", "timestampFormat": "iso8601"}, "ConflictException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The request cannot be applied to the target resource in its current state.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "CreateAnnotationStoreRequest": {"type": "structure", "required": ["storeFormat"], "members": {"reference": {"shape": "ReferenceItem", "documentation": "<p>The genome reference for the store's annotations.</p>"}, "name": {"shape": "StoreName", "documentation": "<p>A name for the store.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description for the store.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Tags for the store.</p>"}, "versionName": {"shape": "VersionName", "documentation": "<p> The name given to an annotation store version to distinguish it from other versions. </p>"}, "sseConfig": {"shape": "SseConfig", "documentation": "<p>Server-side encryption (SSE) settings for the store.</p>"}, "storeFormat": {"shape": "StoreFormat", "documentation": "<p>The annotation file format of the store.</p>"}, "storeOptions": {"shape": "StoreOptions", "documentation": "<p>File parsing options for the annotation store.</p>"}}}, "CreateAnnotationStoreResponse": {"type": "structure", "required": ["id", "status", "name", "versionName", "creationTime"], "members": {"id": {"shape": "ResourceId", "documentation": "<p>The store's ID.</p>"}, "reference": {"shape": "ReferenceItem", "documentation": "<p>The store's genome reference. Required for all stores except TSV format with generic annotations.</p>"}, "storeFormat": {"shape": "StoreFormat", "documentation": "<p>The annotation file format of the store.</p>"}, "storeOptions": {"shape": "StoreOptions", "documentation": "<p>The store's file parsing options.</p>"}, "status": {"shape": "StoreStatus", "documentation": "<p>The store's status.</p>"}, "name": {"shape": "String", "documentation": "<p>The store's name.</p>"}, "versionName": {"shape": "VersionName", "documentation": "<p> The name given to an annotation store version to distinguish it from other versions. </p>"}, "creationTime": {"shape": "CreationTime", "documentation": "<p>When the store was created.</p>"}}}, "CreateAnnotationStoreVersionRequest": {"type": "structure", "required": ["name", "versionName"], "members": {"name": {"shape": "StoreName", "documentation": "<p> The name of an annotation store version from which versions are being created. </p>", "location": "uri", "locationName": "name"}, "versionName": {"shape": "VersionName", "documentation": "<p> The name given to an annotation store version to distinguish it from other versions. </p>"}, "description": {"shape": "Description", "documentation": "<p> The description of an annotation store version. </p>"}, "versionOptions": {"shape": "VersionOptions", "documentation": "<p> The options for an annotation store version. </p>"}, "tags": {"shape": "TagMap", "documentation": "<p> Any tags added to annotation store version. </p>"}}}, "CreateAnnotationStoreVersionResponse": {"type": "structure", "required": ["id", "versionName", "storeId", "name", "status", "creationTime"], "members": {"id": {"shape": "ResourceId", "documentation": "<p> A generated ID for the annotation store </p>"}, "versionName": {"shape": "VersionName", "documentation": "<p> The name given to an annotation store version to distinguish it from other versions. </p>"}, "storeId": {"shape": "ResourceId", "documentation": "<p> The ID for the annotation store from which new versions are being created. </p>"}, "versionOptions": {"shape": "VersionOptions", "documentation": "<p> The options for an annotation store version. </p>"}, "name": {"shape": "StoreName", "documentation": "<p> The name given to an annotation store version to distinguish it from other versions. </p>"}, "status": {"shape": "VersionStatus", "documentation": "<p> The status of a annotation store version. </p>"}, "creationTime": {"shape": "CreationTime", "documentation": "<p> The time stamp for the creation of an annotation store version. </p>"}}}, "CreateMultipartReadSetUploadRequest": {"type": "structure", "required": ["sequenceStoreId", "sourceFileType", "subjectId", "sampleId", "name"], "members": {"sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p> The sequence store ID for the store that is the destination of the multipart uploads. </p>", "location": "uri", "locationName": "sequenceStoreId"}, "clientToken": {"shape": "ClientToken", "documentation": "<p> An idempotency token that can be used to avoid triggering multiple multipart uploads. </p>"}, "sourceFileType": {"shape": "FileType", "documentation": "<p> The type of file being uploaded. </p>"}, "subjectId": {"shape": "SubjectId", "documentation": "<p> The source's subject ID. </p>"}, "sampleId": {"shape": "SampleId", "documentation": "<p> The source's sample ID. </p>"}, "generatedFrom": {"shape": "GeneratedFrom", "documentation": "<p> Where the source originated. </p>"}, "referenceArn": {"shape": "ReferenceArn", "documentation": "<p> The ARN of the reference. </p>"}, "name": {"shape": "ReadSetName", "documentation": "<p> The name of the read set. </p>"}, "description": {"shape": "ReadSetDescription", "documentation": "<p> The description of the read set. </p>"}, "tags": {"shape": "TagMap", "documentation": "<p> Any tags to add to the read set. </p>"}}}, "CreateMultipartReadSetUploadResponse": {"type": "structure", "required": ["sequenceStoreId", "uploadId", "sourceFileType", "subjectId", "sampleId", "referenceArn", "creationTime"], "members": {"sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p> The sequence store ID for the store that the read set will be created in. </p>"}, "uploadId": {"shape": "UploadId", "documentation": "<p> he ID for the initiated multipart upload. </p>"}, "sourceFileType": {"shape": "FileType", "documentation": "<p> The file type of the read set source. </p>"}, "subjectId": {"shape": "SubjectId", "documentation": "<p> The source's subject ID. </p>"}, "sampleId": {"shape": "SampleId", "documentation": "<p> The source's sample ID. </p>"}, "generatedFrom": {"shape": "GeneratedFrom", "documentation": "<p> The source of the read set. </p>"}, "referenceArn": {"shape": "ReferenceArn", "documentation": "<p> The read set source's reference ARN. </p>"}, "name": {"shape": "ReadSetName", "documentation": "<p> The name of the read set. </p>"}, "description": {"shape": "ReadSetDescription", "documentation": "<p> The description of the read set. </p>"}, "tags": {"shape": "TagMap", "documentation": "<p> The tags to add to the read set. </p>"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p> The creation time of the multipart upload. </p>"}}}, "CreateReferenceStoreRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "ReferenceStoreName", "documentation": "<p>A name for the store.</p>"}, "description": {"shape": "ReferenceStoreDescription", "documentation": "<p>A description for the store.</p>"}, "sseConfig": {"shape": "SseConfig", "documentation": "<p>Server-side encryption (SSE) settings for the store.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Tags for the store.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>To ensure that requests don't run multiple times, specify a unique token for each request.</p>"}}}, "CreateReferenceStoreResponse": {"type": "structure", "required": ["id", "arn", "creationTime"], "members": {"id": {"shape": "ReferenceStoreId", "documentation": "<p>The store's ID.</p>"}, "arn": {"shape": "ReferenceStoreArn", "documentation": "<p>The store's ARN.</p>"}, "name": {"shape": "ReferenceStoreName", "documentation": "<p>The store's name.</p>"}, "description": {"shape": "ReferenceStoreDescription", "documentation": "<p>The store's description.</p>"}, "sseConfig": {"shape": "SseConfig", "documentation": "<p>The store's SSE settings.</p>"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the store was created.</p>"}}}, "CreateRunGroupRequest": {"type": "structure", "required": ["requestId"], "members": {"name": {"shape": "RunGroupName", "documentation": "<p>A name for the group.</p>"}, "maxCpus": {"shape": "CreateRunGroupRequestMaxCpusInteger", "documentation": "<p>The maximum number of CPUs to use in the group.</p>"}, "maxRuns": {"shape": "CreateRunGroupRequestMaxRunsInteger", "documentation": "<p>The maximum number of concurrent runs for the group.</p>"}, "maxDuration": {"shape": "CreateRunGroupRequestMaxDurationInteger", "documentation": "<p>A maximum run time for the group in minutes.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Tags for the group.</p>"}, "requestId": {"shape": "RunGroupRequestId", "documentation": "<p>To ensure that requests don't run multiple times, specify a unique ID for each request.</p>", "idempotencyToken": true}, "maxGpus": {"shape": "CreateRunGroupRequestMaxGpusInteger", "documentation": "<p> The maximum GPUs that can be used by a run group. </p>"}}}, "CreateRunGroupRequestMaxCpusInteger": {"type": "integer", "box": true, "max": 100000, "min": 1}, "CreateRunGroupRequestMaxDurationInteger": {"type": "integer", "box": true, "max": 100000, "min": 1}, "CreateRunGroupRequestMaxGpusInteger": {"type": "integer", "box": true, "max": 100000, "min": 1}, "CreateRunGroupRequestMaxRunsInteger": {"type": "integer", "box": true, "max": 100000, "min": 1}, "CreateRunGroupResponse": {"type": "structure", "members": {"arn": {"shape": "RunGroupArn", "documentation": "<p>The group's ARN.</p>"}, "id": {"shape": "RunGroupId", "documentation": "<p>The group's ID.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Tags for the run group.</p>"}}}, "CreateSequenceStoreRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "SequenceStoreName", "documentation": "<p>A name for the store.</p>"}, "description": {"shape": "SequenceStoreDescription", "documentation": "<p>A description for the store.</p>"}, "sseConfig": {"shape": "SseConfig", "documentation": "<p>Server-side encryption (SSE) settings for the store.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Tags for the store.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>To ensure that requests don't run multiple times, specify a unique token for each request.</p>"}, "fallbackLocation": {"shape": "S3Destination", "documentation": "<p> An S3 location that is used to store files that have failed a direct upload. </p>"}}}, "CreateSequenceStoreResponse": {"type": "structure", "required": ["id", "arn", "creationTime"], "members": {"id": {"shape": "SequenceStoreId", "documentation": "<p>The store's ID.</p>"}, "arn": {"shape": "SequenceStoreArn", "documentation": "<p>The store's ARN.</p>"}, "name": {"shape": "SequenceStoreName", "documentation": "<p>The store's name.</p>"}, "description": {"shape": "SequenceStoreDescription", "documentation": "<p>The store's description.</p>"}, "sseConfig": {"shape": "SseConfig", "documentation": "<p>The store's SSE settings.</p>"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the store was created.</p>"}, "fallbackLocation": {"shape": "S3Destination", "documentation": "<p> An S3 location that is used to store files that have failed a direct upload. </p>"}}}, "CreateShareRequest": {"type": "structure", "required": ["resourceArn", "principalSubscriber"], "members": {"resourceArn": {"shape": "String", "documentation": "<p> The resource ARN for the analytics store to be shared. </p>"}, "principalSubscriber": {"shape": "String", "documentation": "<p> The principal subscriber is the account being given access to the analytics store data through the share offer. </p>"}, "shareName": {"shape": "ShareName", "documentation": "<p> A name given to the share. </p>"}}}, "CreateShareResponse": {"type": "structure", "members": {"shareId": {"shape": "String", "documentation": "<p> An ID generated for the share. </p>"}, "status": {"shape": "ShareStatus", "documentation": "<p> The status of a share. </p>"}, "shareName": {"shape": "ShareName", "documentation": "<p> A name given to the share. </p>"}}}, "CreateVariantStoreRequest": {"type": "structure", "required": ["reference"], "members": {"reference": {"shape": "ReferenceItem", "documentation": "<p>The genome reference for the store's variants.</p>"}, "name": {"shape": "StoreName", "documentation": "<p>A name for the store.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description for the store.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Tags for the store.</p>"}, "sseConfig": {"shape": "SseConfig", "documentation": "<p>Server-side encryption (SSE) settings for the store.</p>"}}}, "CreateVariantStoreResponse": {"type": "structure", "required": ["id", "status", "name", "creationTime"], "members": {"id": {"shape": "ResourceId", "documentation": "<p>The store's ID.</p>"}, "reference": {"shape": "ReferenceItem", "documentation": "<p>The store's genome reference.</p>"}, "status": {"shape": "StoreStatus", "documentation": "<p>The store's status.</p>"}, "name": {"shape": "String", "documentation": "<p>The store's name.</p>"}, "creationTime": {"shape": "CreationTime", "documentation": "<p>When the store was created.</p>"}}}, "CreateWorkflowRequest": {"type": "structure", "required": ["requestId"], "members": {"name": {"shape": "WorkflowName", "documentation": "<p>A name for the workflow.</p>"}, "description": {"shape": "WorkflowDescription", "documentation": "<p>A description for the workflow.</p>"}, "engine": {"shape": "WorkflowEngine", "documentation": "<p>An engine for the workflow.</p>"}, "definitionZip": {"shape": "Blob", "documentation": "<p>A ZIP archive for the workflow.</p>"}, "definitionUri": {"shape": "WorkflowDefinition", "documentation": "<p>The URI of a definition for the workflow.</p>"}, "main": {"shape": "WorkflowMain", "documentation": "<p>The path of the main definition file for the workflow.</p>"}, "parameterTemplate": {"shape": "WorkflowParameterTemplate", "documentation": "<p>A parameter template for the workflow.</p>"}, "storageCapacity": {"shape": "CreateWorkflowRequestStorageCapacityInteger", "documentation": "<p>A storage capacity for the workflow in gigabytes.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Tags for the workflow.</p>"}, "requestId": {"shape": "WorkflowRequestId", "documentation": "<p>To ensure that requests don't run multiple times, specify a unique ID for each request.</p>", "idempotencyToken": true}, "accelerators": {"shape": "Accelerators", "documentation": "<p> The computational accelerator specified to run the workflow. </p>"}}}, "CreateWorkflowRequestStorageCapacityInteger": {"type": "integer", "box": true, "max": 100000, "min": 0}, "CreateWorkflowResponse": {"type": "structure", "members": {"arn": {"shape": "WorkflowArn", "documentation": "<p>The workflow's ARN.</p>"}, "id": {"shape": "WorkflowId", "documentation": "<p>The workflow's ID.</p>"}, "status": {"shape": "WorkflowStatus", "documentation": "<p>The workflow's status.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The workflow's tags.</p>"}}}, "CreationTime": {"type": "timestamp", "timestampFormat": "iso8601"}, "CreationType": {"type": "string", "enum": ["IMPORT", "UPLOAD"]}, "DeleteAnnotationStoreRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "String", "documentation": "<p>The store's name.</p>", "location": "uri", "locationName": "name"}, "force": {"shape": "PrimitiveBoolean", "documentation": "<p>Whether to force deletion.</p>", "location": "querystring", "locationName": "force"}}}, "DeleteAnnotationStoreResponse": {"type": "structure", "required": ["status"], "members": {"status": {"shape": "StoreStatus", "documentation": "<p>The store's status.</p>"}}}, "DeleteAnnotationStoreVersionsRequest": {"type": "structure", "required": ["name", "versions"], "members": {"name": {"shape": "String", "documentation": "<p> The name of the annotation store from which versions are being deleted. </p>", "location": "uri", "locationName": "name"}, "versions": {"shape": "VersionList", "documentation": "<p> The versions of an annotation store to be deleted. </p>"}, "force": {"shape": "PrimitiveBoolean", "documentation": "<p> Forces the deletion of an annotation store version when imports are in-progress.. </p>", "location": "querystring", "locationName": "force"}}}, "DeleteAnnotationStoreVersionsResponse": {"type": "structure", "members": {"errors": {"shape": "VersionDeleteErrorList", "documentation": "<p> Any errors that occur when attempting to delete an annotation store version. </p>"}}}, "DeleteReferenceRequest": {"type": "structure", "required": ["id", "referenceStoreId"], "members": {"id": {"shape": "ReferenceId", "documentation": "<p>The reference's ID.</p>", "location": "uri", "locationName": "id"}, "referenceStoreId": {"shape": "ReferenceStoreId", "documentation": "<p>The reference's store ID.</p>", "location": "uri", "locationName": "referenceStoreId"}}}, "DeleteReferenceResponse": {"type": "structure", "members": {}}, "DeleteReferenceStoreRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "ReferenceStoreId", "documentation": "<p>The store's ID.</p>", "location": "uri", "locationName": "id"}}}, "DeleteReferenceStoreResponse": {"type": "structure", "members": {}}, "DeleteRunGroupRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "RunGroupId", "documentation": "<p>The run group's ID.</p>", "location": "uri", "locationName": "id"}}}, "DeleteRunRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "RunId", "documentation": "<p>The run's ID.</p>", "location": "uri", "locationName": "id"}}}, "DeleteSequenceStoreRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "SequenceStoreId", "documentation": "<p>The sequence store's ID.</p>", "location": "uri", "locationName": "id"}}}, "DeleteSequenceStoreResponse": {"type": "structure", "members": {}}, "DeleteShareRequest": {"type": "structure", "required": ["shareId"], "members": {"shareId": {"shape": "String", "documentation": "<p> The ID for the share request to be deleted. </p>", "location": "uri", "locationName": "shareId"}}}, "DeleteShareResponse": {"type": "structure", "members": {"status": {"shape": "ShareStatus", "documentation": "<p> The status of the share being deleted. </p>"}}}, "DeleteVariantStoreRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "String", "documentation": "<p>The store's name.</p>", "location": "uri", "locationName": "name"}, "force": {"shape": "PrimitiveBoolean", "documentation": "<p>Whether to force deletion.</p>", "location": "querystring", "locationName": "force"}}}, "DeleteVariantStoreResponse": {"type": "structure", "required": ["status"], "members": {"status": {"shape": "StoreStatus", "documentation": "<p>The store's status.</p>"}}}, "DeleteWorkflowRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "WorkflowId", "documentation": "<p>The workflow's ID.</p>", "location": "uri", "locationName": "id"}}}, "Description": {"type": "string", "max": 500, "min": 0}, "ETag": {"type": "structure", "members": {"algorithm": {"shape": "ETagAlgorithm", "documentation": "<p> The algorithm used to calculate the read set’s ETag(s). </p>"}, "source1": {"shape": "String", "documentation": "<p> The ETag hash calculated on Source1 of the read set. </p>"}, "source2": {"shape": "String", "documentation": "<p> The ETag hash calculated on Source2 of the read set. </p>"}}, "documentation": "<p> The entity tag (ETag) is a hash of the object representing its semantic content. </p>"}, "ETagAlgorithm": {"type": "string", "enum": ["FASTQ_MD5up", "BAM_MD5up", "CRAM_MD5up"]}, "Encoding": {"type": "string", "max": 20, "min": 1}, "EncryptionType": {"type": "string", "enum": ["KMS"]}, "EngineLogStream": {"type": "string", "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "EscapeChar": {"type": "string", "max": 1, "min": 1}, "EscapeQuotes": {"type": "boolean"}, "ExportJobId": {"type": "string", "max": 36, "min": 10, "pattern": "[0-9]+"}, "ExportReadSet": {"type": "structure", "required": ["readSetId"], "members": {"readSetId": {"shape": "ReadSetId", "documentation": "<p>The set's ID.</p>"}}, "documentation": "<p>A read set.</p>"}, "ExportReadSetDetail": {"type": "structure", "required": ["id", "status"], "members": {"id": {"shape": "ReadSetId", "documentation": "<p>The set's ID.</p>"}, "status": {"shape": "ReadSetExportJobItemStatus", "documentation": "<p>The set's status.</p>"}, "statusMessage": {"shape": "JobStatusMessage", "documentation": "<p>The set's status message.</p>"}}, "documentation": "<p>Details about a read set.</p>"}, "ExportReadSetDetailList": {"type": "list", "member": {"shape": "ExportReadSetDetail"}}, "ExportReadSetFilter": {"type": "structure", "members": {"status": {"shape": "ReadSetExportJobStatus", "documentation": "<p>A status to filter on.</p>"}, "createdAfter": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The filter's start date.</p>"}, "createdBefore": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The filter's end date.</p>"}}, "documentation": "<p>An read set export job filter.</p>"}, "ExportReadSetJobDetail": {"type": "structure", "required": ["id", "sequenceStoreId", "destination", "status", "creationTime"], "members": {"id": {"shape": "ExportJobId", "documentation": "<p>The job's ID.</p>"}, "sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p>The job's sequence store ID.</p>"}, "destination": {"shape": "S3Destination", "documentation": "<p>The job's destination in Amazon S3.</p>"}, "status": {"shape": "ReadSetExportJobStatus", "documentation": "<p>The job's status.</p>"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the job was created.</p>"}, "completionTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the job completed.</p>"}}, "documentation": "<p>Details about a read set export job.</p>"}, "ExportReadSetJobDetailList": {"type": "list", "member": {"shape": "ExportReadSetJobDetail"}}, "FileInformation": {"type": "structure", "members": {"totalParts": {"shape": "FileInformationTotalPartsInteger", "documentation": "<p>The file's total parts.</p>"}, "partSize": {"shape": "FileInformationPartSizeLong", "documentation": "<p>The file's part size.</p>"}, "contentLength": {"shape": "FileInformationContentLengthLong", "documentation": "<p>The file's content length.</p>"}}, "documentation": "<p>Details about a file.</p>"}, "FileInformationContentLengthLong": {"type": "long", "box": true, "max": 5497558138880, "min": 1}, "FileInformationPartSizeLong": {"type": "long", "box": true, "max": 5368709120, "min": 1}, "FileInformationTotalPartsInteger": {"type": "integer", "box": true, "max": 10000, "min": 1}, "FileType": {"type": "string", "enum": ["FASTQ", "BAM", "CRAM", "UBAM"]}, "Filter": {"type": "structure", "members": {"resourceArns": {"shape": "ArnList", "documentation": "<p> The Amazon Resource Number (Arn) for an analytics store. </p>"}, "status": {"shape": "StatusList", "documentation": "<p> The status of an annotation store version. </p>"}}, "documentation": "<p> Use filters to focus the returned annotation store versions on a specific parameter, such as the status of the annotation store. </p>"}, "FormatOptions": {"type": "structure", "members": {"tsvOptions": {"shape": "TsvOptions", "documentation": "<p>Options for a TSV file.</p>"}, "vcfOptions": {"shape": "VcfOptions", "documentation": "<p>Options for a VCF file.</p>"}}, "documentation": "<p>Formatting options for a file.</p>", "union": true}, "FormatToHeader": {"type": "map", "key": {"shape": "FormatToHeaderKey"}, "value": {"shape": "FormatToHeaderValueString"}}, "FormatToHeaderKey": {"type": "string", "enum": ["CHR", "START", "END", "REF", "ALT", "POS"]}, "FormatToHeaderValueString": {"type": "string", "max": 1000, "min": 0}, "GeneratedFrom": {"type": "string", "max": 127, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "GetAnnotationImportRequest": {"type": "structure", "required": ["jobId"], "members": {"jobId": {"shape": "ResourceId", "documentation": "<p>The job's ID.</p>", "location": "uri", "locationName": "jobId"}}}, "GetAnnotationImportResponse": {"type": "structure", "required": ["id", "destinationName", "versionName", "roleArn", "status", "statusMessage", "creationTime", "updateTime", "completionTime", "items", "runLeftNormalization", "formatOptions"], "members": {"id": {"shape": "ResourceId", "documentation": "<p>The job's ID.</p>"}, "destinationName": {"shape": "StoreName", "documentation": "<p>The job's destination annotation store.</p>"}, "versionName": {"shape": "VersionName", "documentation": "<p> The name of the annotation store version. </p>"}, "roleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The job's service role ARN.</p>"}, "status": {"shape": "JobStatus", "documentation": "<p>The job's status.</p>"}, "statusMessage": {"shape": "JobStatusMsg", "documentation": "<p>The job's status message.</p>"}, "creationTime": {"shape": "CreationTime", "documentation": "<p>When the job was created.</p>"}, "updateTime": {"shape": "UpdateTime", "documentation": "<p>When the job was updated.</p>"}, "completionTime": {"shape": "CompletionTime", "documentation": "<p>When the job completed.</p>"}, "items": {"shape": "AnnotationImportItemDetails", "documentation": "<p>The job's imported items.</p>"}, "runLeftNormalization": {"shape": "RunLeftNormalization", "documentation": "<p>The job's left normalization setting.</p>"}, "formatOptions": {"shape": "FormatOptions"}, "annotationFields": {"shape": "AnnotationFieldMap", "documentation": "<p> The annotation schema generated by the parsed annotation data. </p>"}}}, "GetAnnotationStoreRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "String", "documentation": "<p>The store's name.</p>", "location": "uri", "locationName": "name"}}}, "GetAnnotationStoreResponse": {"type": "structure", "required": ["id", "reference", "status", "storeArn", "name", "description", "sseConfig", "creationTime", "updateTime", "tags", "statusMessage", "storeSizeBytes", "numVersions"], "members": {"id": {"shape": "ResourceId", "documentation": "<p>The store's ID.</p>"}, "reference": {"shape": "ReferenceItem", "documentation": "<p>The store's genome reference.</p>"}, "status": {"shape": "StoreStatus", "documentation": "<p>The store's status.</p>"}, "storeArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The store's ARN.</p>"}, "name": {"shape": "String", "documentation": "<p>The store's name.</p>"}, "description": {"shape": "Description", "documentation": "<p>The store's description.</p>"}, "sseConfig": {"shape": "SseConfig", "documentation": "<p>The store's server-side encryption (SSE) settings.</p>"}, "creationTime": {"shape": "CreationTime", "documentation": "<p>When the store was created.</p>"}, "updateTime": {"shape": "UpdateTime", "documentation": "<p>When the store was updated.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The store's tags.</p>"}, "storeOptions": {"shape": "StoreOptions", "documentation": "<p>The store's parsing options.</p>"}, "storeFormat": {"shape": "StoreFormat", "documentation": "<p>The store's annotation file format.</p>"}, "statusMessage": {"shape": "StatusMessage", "documentation": "<p>A status message.</p>"}, "storeSizeBytes": {"shape": "<PERSON>", "documentation": "<p>The store's size in bytes.</p>"}, "numVersions": {"shape": "Integer", "documentation": "<p> An integer indicating how many versions of an annotation store exist. </p>"}}}, "GetAnnotationStoreVersionRequest": {"type": "structure", "required": ["name", "versionName"], "members": {"name": {"shape": "String", "documentation": "<p> The name given to an annotation store version to distinguish it from others. </p>", "location": "uri", "locationName": "name"}, "versionName": {"shape": "String", "documentation": "<p> The name given to an annotation store version to distinguish it from others. </p>", "location": "uri", "locationName": "versionName"}}}, "GetAnnotationStoreVersionResponse": {"type": "structure", "required": ["storeId", "id", "status", "versionArn", "name", "versionName", "description", "creationTime", "updateTime", "tags", "statusMessage", "versionSizeBytes"], "members": {"storeId": {"shape": "ResourceId", "documentation": "<p> The store ID for annotation store version. </p>"}, "id": {"shape": "ResourceId", "documentation": "<p> The annotation store version ID. </p>"}, "status": {"shape": "VersionStatus", "documentation": "<p> The status of an annotation store version. </p>"}, "versionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p> The Arn for the annotation store. </p>"}, "name": {"shape": "StoreName", "documentation": "<p> The name of the annotation store. </p>"}, "versionName": {"shape": "VersionName", "documentation": "<p> The name given to an annotation store version to distinguish it from others. </p>"}, "description": {"shape": "Description", "documentation": "<p> The description for an annotation store version. </p>"}, "creationTime": {"shape": "CreationTime", "documentation": "<p> The time stamp for when an annotation store version was created. </p>"}, "updateTime": {"shape": "UpdateTime", "documentation": "<p> The time stamp for when an annotation store version was updated. </p>"}, "tags": {"shape": "TagMap", "documentation": "<p> Any tags associated with an annotation store version. </p>"}, "versionOptions": {"shape": "VersionOptions", "documentation": "<p> The options for an annotation store version. </p>"}, "statusMessage": {"shape": "StatusMessage", "documentation": "<p> The status of an annotation store version. </p>"}, "versionSizeBytes": {"shape": "<PERSON>", "documentation": "<p> The size of the annotation store version in Bytes. </p>"}}}, "GetReadSetActivationJobRequest": {"type": "structure", "required": ["id", "sequenceStoreId"], "members": {"id": {"shape": "ActivationJobId", "documentation": "<p>The job's ID.</p>", "location": "uri", "locationName": "id"}, "sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p>The job's sequence store ID.</p>", "location": "uri", "locationName": "sequenceStoreId"}}}, "GetReadSetActivationJobResponse": {"type": "structure", "required": ["id", "sequenceStoreId", "status", "creationTime"], "members": {"id": {"shape": "ActivationJobId", "documentation": "<p>The job's ID.</p>"}, "sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p>The job's sequence store ID.</p>"}, "status": {"shape": "ReadSetActivationJobStatus", "documentation": "<p>The job's status.</p>"}, "statusMessage": {"shape": "JobStatusMessage", "documentation": "<p>The job's status message.</p>"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the job was created.</p>"}, "completionTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the job completed.</p>"}, "sources": {"shape": "ActivateReadSetSourceList", "documentation": "<p>The job's source files.</p>"}}}, "GetReadSetExportJobRequest": {"type": "structure", "required": ["sequenceStoreId", "id"], "members": {"sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p>The job's sequence store ID.</p>", "location": "uri", "locationName": "sequenceStoreId"}, "id": {"shape": "ExportJobId", "documentation": "<p>The job's ID.</p>", "location": "uri", "locationName": "id"}}}, "GetReadSetExportJobResponse": {"type": "structure", "required": ["id", "sequenceStoreId", "destination", "status", "creationTime"], "members": {"id": {"shape": "ExportJobId", "documentation": "<p>The job's ID.</p>"}, "sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p>The job's sequence store ID.</p>"}, "destination": {"shape": "S3Destination", "documentation": "<p>The job's destination in Amazon S3.</p>"}, "status": {"shape": "ReadSetExportJobStatus", "documentation": "<p>The job's status.</p>"}, "statusMessage": {"shape": "JobStatusMessage", "documentation": "<p>The job's status message.</p>"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the job was created.</p>"}, "completionTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the job completed.</p>"}, "readSets": {"shape": "ExportReadSetDetailList", "documentation": "<p>The job's read sets.</p>"}}}, "GetReadSetImportJobRequest": {"type": "structure", "required": ["id", "sequenceStoreId"], "members": {"id": {"shape": "ImportJobId", "documentation": "<p>The job's ID.</p>", "location": "uri", "locationName": "id"}, "sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p>The job's sequence store ID.</p>", "location": "uri", "locationName": "sequenceStoreId"}}}, "GetReadSetImportJobResponse": {"type": "structure", "required": ["id", "sequenceStoreId", "roleArn", "status", "creationTime", "sources"], "members": {"id": {"shape": "ImportJobId", "documentation": "<p>The job's ID.</p>"}, "sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p>The job's sequence store ID.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The job's service role ARN.</p>"}, "status": {"shape": "ReadSetImportJobStatus", "documentation": "<p>The job's status.</p>"}, "statusMessage": {"shape": "JobStatusMessage", "documentation": "<p>The job's status message.</p>"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the job was created.</p>"}, "completionTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the job completed.</p>"}, "sources": {"shape": "ImportReadSetSourceList", "documentation": "<p>The job's source files.</p>"}}}, "GetReadSetMetadataRequest": {"type": "structure", "required": ["id", "sequenceStoreId"], "members": {"id": {"shape": "ReadSetId", "documentation": "<p>The read set's ID.</p>", "location": "uri", "locationName": "id"}, "sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p>The read set's sequence store ID.</p>", "location": "uri", "locationName": "sequenceStoreId"}}}, "GetReadSetMetadataResponse": {"type": "structure", "required": ["id", "arn", "sequenceStoreId", "status", "fileType", "creationTime"], "members": {"id": {"shape": "ReadSetId", "documentation": "<p>The read set's ID.</p>"}, "arn": {"shape": "ReadSetArn", "documentation": "<p>The read set's ARN.</p>"}, "sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p>The read set's sequence store ID.</p>"}, "subjectId": {"shape": "SubjectId", "documentation": "<p>The read set's subject ID.</p>"}, "sampleId": {"shape": "SampleId", "documentation": "<p>The read set's sample ID.</p>"}, "status": {"shape": "ReadSetStatus", "documentation": "<p>The read set's status.</p>"}, "name": {"shape": "ReadSetName", "documentation": "<p>The read set's name.</p>"}, "description": {"shape": "ReadSetDescription", "documentation": "<p>The read set's description.</p>"}, "fileType": {"shape": "FileType", "documentation": "<p>The read set's file type.</p>"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the read set was created.</p>"}, "sequenceInformation": {"shape": "SequenceInformation", "documentation": "<p>The read set's sequence information.</p>"}, "referenceArn": {"shape": "ReferenceArn", "documentation": "<p>The read set's genome reference ARN.</p>"}, "files": {"shape": "ReadSetFiles", "documentation": "<p>The read set's files.</p>"}, "statusMessage": {"shape": "ReadSetStatusMessage", "documentation": "<p> The status message for a read set. It provides more detail as to why the read set has a status. </p>"}, "creationType": {"shape": "CreationType", "documentation": "<p> The creation type of the read set. </p>"}, "etag": {"shape": "ETag", "documentation": "<p> The entity tag (ETag) is a hash of the object meant to represent its semantic content. </p>"}}}, "GetReadSetRequest": {"type": "structure", "required": ["id", "sequenceStoreId", "partNumber"], "members": {"id": {"shape": "ReadSetId", "documentation": "<p>The read set's ID.</p>", "location": "uri", "locationName": "id"}, "sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p>The read set's sequence store ID.</p>", "location": "uri", "locationName": "sequenceStoreId"}, "file": {"shape": "ReadSetFile", "documentation": "<p>The file to retrieve.</p>", "location": "querystring", "locationName": "file"}, "partNumber": {"shape": "GetReadSetRequestPartNumberInteger", "documentation": "<p>The part number to retrieve.</p>", "location": "querystring", "locationName": "partNumber"}}}, "GetReadSetRequestPartNumberInteger": {"type": "integer", "box": true, "max": 10000, "min": 1}, "GetReadSetResponse": {"type": "structure", "members": {"payload": {"shape": "ReadSetStreamingBlob", "documentation": "<p>The read set file payload.</p>"}}, "payload": "payload"}, "GetReferenceImportJobRequest": {"type": "structure", "required": ["id", "referenceStoreId"], "members": {"id": {"shape": "ImportJobId", "documentation": "<p>The job's ID.</p>", "location": "uri", "locationName": "id"}, "referenceStoreId": {"shape": "ReferenceStoreId", "documentation": "<p>The job's reference store ID.</p>", "location": "uri", "locationName": "referenceStoreId"}}}, "GetReferenceImportJobResponse": {"type": "structure", "required": ["id", "referenceStoreId", "roleArn", "status", "creationTime", "sources"], "members": {"id": {"shape": "ImportJobId", "documentation": "<p>The job's ID.</p>"}, "referenceStoreId": {"shape": "ReferenceStoreId", "documentation": "<p>The job's reference store ID.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The job's service role ARN.</p>"}, "status": {"shape": "ReferenceImportJobStatus", "documentation": "<p>The job's status.</p>"}, "statusMessage": {"shape": "JobStatusMessage", "documentation": "<p>The job's status message.</p>"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the job was created.</p>"}, "completionTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the job completed.</p>"}, "sources": {"shape": "ImportReferenceSourceList", "documentation": "<p>The job's source files.</p>"}}}, "GetReferenceMetadataRequest": {"type": "structure", "required": ["id", "referenceStoreId"], "members": {"id": {"shape": "ReferenceId", "documentation": "<p>The reference's ID.</p>", "location": "uri", "locationName": "id"}, "referenceStoreId": {"shape": "ReferenceStoreId", "documentation": "<p>The reference's reference store ID.</p>", "location": "uri", "locationName": "referenceStoreId"}}}, "GetReferenceMetadataResponse": {"type": "structure", "required": ["id", "arn", "referenceStoreId", "md5", "creationTime", "updateTime"], "members": {"id": {"shape": "ReferenceId", "documentation": "<p>The reference's ID.</p>"}, "arn": {"shape": "ReferenceArn", "documentation": "<p>The reference's ARN.</p>"}, "referenceStoreId": {"shape": "ReferenceStoreId", "documentation": "<p>The reference's reference store ID.</p>"}, "md5": {"shape": "Md5", "documentation": "<p>The reference's MD5 checksum.</p>"}, "status": {"shape": "ReferenceStatus", "documentation": "<p>The reference's status.</p>"}, "name": {"shape": "ReferenceName", "documentation": "<p>The reference's name.</p>"}, "description": {"shape": "ReferenceDescription", "documentation": "<p>The reference's description.</p>"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the reference was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the reference was updated.</p>"}, "files": {"shape": "ReferenceFiles", "documentation": "<p>The reference's files.</p>"}}}, "GetReferenceRequest": {"type": "structure", "required": ["id", "referenceStoreId", "partNumber"], "members": {"id": {"shape": "ReferenceId", "documentation": "<p>The reference's ID.</p>", "location": "uri", "locationName": "id"}, "referenceStoreId": {"shape": "ReferenceStoreId", "documentation": "<p>The reference's store ID.</p>", "location": "uri", "locationName": "referenceStoreId"}, "range": {"shape": "Range", "documentation": "<p>The range to retrieve.</p>", "location": "header", "locationName": "Range"}, "partNumber": {"shape": "GetReferenceRequestPartNumberInteger", "documentation": "<p>The part number to retrieve.</p>", "location": "querystring", "locationName": "partNumber"}, "file": {"shape": "ReferenceFile", "documentation": "<p>The file to retrieve.</p>", "location": "querystring", "locationName": "file"}}}, "GetReferenceRequestPartNumberInteger": {"type": "integer", "box": true, "max": 10000, "min": 1}, "GetReferenceResponse": {"type": "structure", "members": {"payload": {"shape": "ReferenceStreamingBlob", "documentation": "<p>The reference file payload.</p>"}}, "payload": "payload"}, "GetReferenceStoreRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "ReferenceStoreId", "documentation": "<p>The store's ID.</p>", "location": "uri", "locationName": "id"}}}, "GetReferenceStoreResponse": {"type": "structure", "required": ["id", "arn", "creationTime"], "members": {"id": {"shape": "ReferenceStoreId", "documentation": "<p>The store's ID.</p>"}, "arn": {"shape": "ReferenceStoreArn", "documentation": "<p>The store's ARN.</p>"}, "name": {"shape": "ReferenceStoreName", "documentation": "<p>The store's name.</p>"}, "description": {"shape": "ReferenceStoreDescription", "documentation": "<p>The store's description.</p>"}, "sseConfig": {"shape": "SseConfig", "documentation": "<p>The store's server-side encryption (SSE) settings.</p>"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the store was created.</p>"}}}, "GetRunGroupRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "RunGroupId", "documentation": "<p>The group's ID.</p>", "location": "uri", "locationName": "id"}}}, "GetRunGroupResponse": {"type": "structure", "members": {"arn": {"shape": "RunGroupArn", "documentation": "<p>The group's ARN.</p>"}, "id": {"shape": "RunGroupId", "documentation": "<p>The group's ID.</p>"}, "name": {"shape": "RunGroupName", "documentation": "<p>The group's name.</p>"}, "maxCpus": {"shape": "GetRunGroupResponseMaxCpusInteger", "documentation": "<p>The group's maximum number of CPUs to use.</p>"}, "maxRuns": {"shape": "GetRunGroupResponseMaxRunsInteger", "documentation": "<p>The maximum number of concurrent runs for the group.</p>"}, "maxDuration": {"shape": "GetRunGroupResponseMaxDurationInteger", "documentation": "<p>The group's maximum run time in minutes.</p>"}, "creationTime": {"shape": "RunGroupTimestamp", "documentation": "<p>When the group was created.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The group's tags.</p>"}, "maxGpus": {"shape": "GetRunGroupResponseMaxGpusInteger", "documentation": "<p> The maximum GPUs that can be used by a run group. </p>"}}}, "GetRunGroupResponseMaxCpusInteger": {"type": "integer", "box": true, "max": 100000, "min": 1}, "GetRunGroupResponseMaxDurationInteger": {"type": "integer", "box": true, "max": 100000, "min": 1}, "GetRunGroupResponseMaxGpusInteger": {"type": "integer", "box": true, "max": 100000, "min": 1}, "GetRunGroupResponseMaxRunsInteger": {"type": "integer", "box": true, "max": 100000, "min": 1}, "GetRunRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "RunId", "documentation": "<p>The run's ID.</p>", "location": "uri", "locationName": "id"}, "export": {"shape": "RunExportList", "documentation": "<p>The run's export format.</p>", "location": "querystring", "locationName": "export"}}}, "GetRunResponse": {"type": "structure", "members": {"arn": {"shape": "RunArn", "documentation": "<p>The run's ARN.</p>"}, "id": {"shape": "RunId", "documentation": "<p>The run's ID.</p>"}, "status": {"shape": "RunStatus", "documentation": "<p>The run's status.</p>"}, "workflowId": {"shape": "WorkflowId", "documentation": "<p>The run's workflow ID.</p>"}, "workflowType": {"shape": "WorkflowType", "documentation": "<p>The run's workflow type.</p>"}, "runId": {"shape": "RunId", "documentation": "<p>The run's ID.</p>"}, "roleArn": {"shape": "RunRoleArn", "documentation": "<p>The run's service role ARN.</p>"}, "name": {"shape": "RunName", "documentation": "<p>The run's name.</p>"}, "runGroupId": {"shape": "RunGroupId", "documentation": "<p>The run's group ID.</p>"}, "priority": {"shape": "GetRunResponsePriorityInteger", "documentation": "<p>The run's priority.</p>"}, "definition": {"shape": "WorkflowDefinition", "documentation": "<p>The run's definition.</p>"}, "digest": {"shape": "WorkflowDigest", "documentation": "<p>The run's digest.</p>"}, "parameters": {"shape": "RunParameters", "documentation": "<p>The run's parameters.</p>"}, "storageCapacity": {"shape": "GetRunResponseStorageCapacityInteger", "documentation": "<p>The run's storage capacity in gigabytes.</p>"}, "outputUri": {"shape": "RunOutputUri", "documentation": "<p>The run's output URI.</p>"}, "logLevel": {"shape": "RunLogLevel", "documentation": "<p>The run's log level.</p>"}, "resourceDigests": {"shape": "RunResourceDigests", "documentation": "<p>The run's resource digests.</p>"}, "startedBy": {"shape": "RunStartedBy", "documentation": "<p>Who started the run.</p>"}, "creationTime": {"shape": "RunTimestamp", "documentation": "<p>When the run was created.</p>"}, "startTime": {"shape": "RunTimestamp", "documentation": "<p>When the run started.</p>"}, "stopTime": {"shape": "RunTimestamp", "documentation": "<p>The run's stop time.</p>"}, "statusMessage": {"shape": "RunStatusMessage", "documentation": "<p>The run's status message.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The run's tags.</p>"}, "accelerators": {"shape": "Accelerators", "documentation": "<p> The computational accelerator used to run the workflow. </p>"}, "retentionMode": {"shape": "RunRetentionMode", "documentation": "<p>The run's retention mode.</p>"}, "failureReason": {"shape": "RunFailureReason", "documentation": "<p> The reason a run has failed. </p>"}, "logLocation": {"shape": "RunLogLocation", "documentation": "<p> The location of the run log. </p>"}, "uuid": {"shape": "RunUuid", "documentation": "<p> The universally unique identifier for a run. </p>"}, "runOutputUri": {"shape": "RunOutputUri", "documentation": "<p> The destination for workflow outputs. </p>"}}}, "GetRunResponsePriorityInteger": {"type": "integer", "box": true, "max": 100000, "min": 0}, "GetRunResponseStorageCapacityInteger": {"type": "integer", "box": true, "max": 100000, "min": 0}, "GetRunTaskRequest": {"type": "structure", "required": ["id", "taskId"], "members": {"id": {"shape": "RunId", "documentation": "<p>The workflow run ID.</p>", "location": "uri", "locationName": "id"}, "taskId": {"shape": "TaskId", "documentation": "<p>The task's ID.</p>", "location": "uri", "locationName": "taskId"}}}, "GetRunTaskResponse": {"type": "structure", "members": {"taskId": {"shape": "TaskId", "documentation": "<p>The task's ID.</p>"}, "status": {"shape": "TaskStatus", "documentation": "<p>The task's status.</p>"}, "name": {"shape": "TaskName", "documentation": "<p>The task's name.</p>"}, "cpus": {"shape": "GetRunTaskResponseCpusInteger", "documentation": "<p>The task's CPU usage.</p>"}, "memory": {"shape": "GetRunTaskResponseMemoryInteger", "documentation": "<p>The task's memory use in gigabytes.</p>"}, "creationTime": {"shape": "TaskTimestamp", "documentation": "<p>When the task was created.</p>"}, "startTime": {"shape": "TaskTimestamp", "documentation": "<p>The task's start time.</p>"}, "stopTime": {"shape": "TaskTimestamp", "documentation": "<p>The task's stop time.</p>"}, "statusMessage": {"shape": "TaskStatusMessage", "documentation": "<p>The task's status message.</p>"}, "logStream": {"shape": "TaskLogStream", "documentation": "<p>The task's log stream.</p>"}, "gpus": {"shape": "GetRunTaskResponseGpusInteger", "documentation": "<p> The number of Graphics Processing Units (GPU) specified in the task. </p>"}, "instanceType": {"shape": "TaskInstanceType", "documentation": "<p> The instance type for a task. </p>"}, "failureReason": {"shape": "TaskFailureReason", "documentation": "<p> The reason a task has failed. </p>"}}}, "GetRunTaskResponseCpusInteger": {"type": "integer", "box": true, "min": 1}, "GetRunTaskResponseGpusInteger": {"type": "integer", "box": true, "min": 0}, "GetRunTaskResponseMemoryInteger": {"type": "integer", "box": true, "min": 1}, "GetSequenceStoreRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "SequenceStoreId", "documentation": "<p>The store's ID.</p>", "location": "uri", "locationName": "id"}}}, "GetSequenceStoreResponse": {"type": "structure", "required": ["id", "arn", "creationTime"], "members": {"id": {"shape": "SequenceStoreId", "documentation": "<p>The store's ID.</p>"}, "arn": {"shape": "SequenceStoreArn", "documentation": "<p>The store's ARN.</p>"}, "name": {"shape": "SequenceStoreName", "documentation": "<p>The store's name.</p>"}, "description": {"shape": "SequenceStoreDescription", "documentation": "<p>The store's description.</p>"}, "sseConfig": {"shape": "SseConfig", "documentation": "<p>The store's server-side encryption (SSE) settings.</p>"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the store was created.</p>"}, "fallbackLocation": {"shape": "S3Destination", "documentation": "<p> An S3 location that is used to store files that have failed a direct upload. </p>"}}}, "GetShareRequest": {"type": "structure", "required": ["shareId"], "members": {"shareId": {"shape": "String", "documentation": "<p> The generated ID for a share. </p>", "location": "uri", "locationName": "shareId"}}}, "GetShareResponse": {"type": "structure", "members": {"share": {"shape": "ShareDetails", "documentation": "<p> An analytic store share details object. contains status, resourceArn, ownerId, etc. </p>"}}}, "GetVariantImportRequest": {"type": "structure", "required": ["jobId"], "members": {"jobId": {"shape": "ResourceId", "documentation": "<p>The job's ID.</p>", "location": "uri", "locationName": "jobId"}}}, "GetVariantImportResponse": {"type": "structure", "required": ["id", "destinationName", "roleArn", "status", "statusMessage", "creationTime", "updateTime", "items", "runLeftNormalization"], "members": {"id": {"shape": "ResourceId", "documentation": "<p>The job's ID.</p>"}, "destinationName": {"shape": "StoreName", "documentation": "<p>The job's destination variant store.</p>"}, "roleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The job's service role ARN.</p>"}, "status": {"shape": "JobStatus", "documentation": "<p>The job's status.</p>"}, "statusMessage": {"shape": "JobStatusMsg", "documentation": "<p>The job's status message.</p>"}, "creationTime": {"shape": "CreationTime", "documentation": "<p>When the job was created.</p>"}, "updateTime": {"shape": "UpdateTime", "documentation": "<p>When the job was updated.</p>"}, "completionTime": {"shape": "CompletionTime", "documentation": "<p>When the job completed.</p>"}, "items": {"shape": "VariantImportItemDetails", "documentation": "<p>The job's items.</p>"}, "runLeftNormalization": {"shape": "RunLeftNormalization", "documentation": "<p>The job's left normalization setting.</p>"}, "annotationFields": {"shape": "AnnotationFieldMap", "documentation": "<p> The annotation schema generated by the parsed annotation data. </p>"}}}, "GetVariantStoreRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "String", "documentation": "<p>The store's name.</p>", "location": "uri", "locationName": "name"}}}, "GetVariantStoreResponse": {"type": "structure", "required": ["id", "reference", "status", "storeArn", "name", "description", "sseConfig", "creationTime", "updateTime", "tags", "statusMessage", "storeSizeBytes"], "members": {"id": {"shape": "ResourceId", "documentation": "<p>The store's ID.</p>"}, "reference": {"shape": "ReferenceItem", "documentation": "<p>The store's genome reference.</p>"}, "status": {"shape": "StoreStatus", "documentation": "<p>The store's status.</p>"}, "storeArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The store's ARN.</p>"}, "name": {"shape": "String", "documentation": "<p>The store's name.</p>"}, "description": {"shape": "Description", "documentation": "<p>The store's description.</p>"}, "sseConfig": {"shape": "SseConfig", "documentation": "<p>The store's server-side encryption (SSE) settings.</p>"}, "creationTime": {"shape": "CreationTime", "documentation": "<p>When the store was created.</p>"}, "updateTime": {"shape": "UpdateTime", "documentation": "<p>When the store was updated.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The store's tags.</p>"}, "statusMessage": {"shape": "StatusMessage", "documentation": "<p>The store's status message.</p>"}, "storeSizeBytes": {"shape": "<PERSON>", "documentation": "<p>The store's size in bytes.</p>"}}}, "GetWorkflowRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "WorkflowId", "documentation": "<p>The workflow's ID.</p>", "location": "uri", "locationName": "id"}, "type": {"shape": "WorkflowType", "documentation": "<p>The workflow's type.</p>", "location": "querystring", "locationName": "type"}, "export": {"shape": "WorkflowExportList", "documentation": "<p>The export format for the workflow.</p>", "location": "querystring", "locationName": "export"}}}, "GetWorkflowResponse": {"type": "structure", "members": {"arn": {"shape": "WorkflowArn", "documentation": "<p>The workflow's ARN.</p>"}, "id": {"shape": "WorkflowId", "documentation": "<p>The workflow's ID.</p>"}, "status": {"shape": "WorkflowStatus", "documentation": "<p>The workflow's status.</p>"}, "type": {"shape": "WorkflowType", "documentation": "<p>The workflow's type.</p>"}, "name": {"shape": "WorkflowName", "documentation": "<p>The workflow's name.</p>"}, "description": {"shape": "WorkflowDescription", "documentation": "<p>The workflow's description.</p>"}, "engine": {"shape": "WorkflowEngine", "documentation": "<p>The workflow's engine.</p>"}, "definition": {"shape": "WorkflowDefinition", "documentation": "<p>The workflow's definition.</p>"}, "main": {"shape": "WorkflowMain", "documentation": "<p>The path of the main definition file for the workflow.</p>"}, "digest": {"shape": "WorkflowDigest", "documentation": "<p>The workflow's digest.</p>"}, "parameterTemplate": {"shape": "WorkflowParameterTemplate", "documentation": "<p>The workflow's parameter template.</p>"}, "storageCapacity": {"shape": "GetWorkflowResponseStorageCapacityInteger", "documentation": "<p>The workflow's storage capacity in gigabytes.</p>"}, "creationTime": {"shape": "WorkflowTimestamp", "documentation": "<p>When the workflow was created.</p>"}, "statusMessage": {"shape": "WorkflowStatusMessage", "documentation": "<p>The workflow's status message.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The workflow's tags.</p>"}, "metadata": {"shape": "WorkflowMetadata", "documentation": "<p> Gets metadata for workflow. </p>"}, "accelerators": {"shape": "Accelerators", "documentation": "<p> The computational accelerator specified to run the workflow. </p>"}}}, "GetWorkflowResponseStorageCapacityInteger": {"type": "integer", "box": true, "max": 100000, "min": 0}, "Header": {"type": "boolean"}, "ImportJobId": {"type": "string", "max": 36, "min": 10, "pattern": "[0-9]+"}, "ImportReadSetFilter": {"type": "structure", "members": {"status": {"shape": "ReadSetImportJobStatus", "documentation": "<p>A status to filter on.</p>"}, "createdAfter": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The filter's start date.</p>"}, "createdBefore": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The filter's end date.</p>"}}, "documentation": "<p>A filter for import read set jobs.</p>"}, "ImportReadSetJobItem": {"type": "structure", "required": ["id", "sequenceStoreId", "roleArn", "status", "creationTime"], "members": {"id": {"shape": "ImportJobId", "documentation": "<p>The job's ID.</p>"}, "sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p>The job's sequence store ID.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The job's service role ARN.</p>"}, "status": {"shape": "ReadSetImportJobStatus", "documentation": "<p>The job's status.</p>"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the job was created.</p>"}, "completionTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the job completed.</p>"}}, "documentation": "<p>An import read set job.</p>"}, "ImportReadSetJobList": {"type": "list", "member": {"shape": "ImportReadSetJobItem"}}, "ImportReadSetSourceItem": {"type": "structure", "required": ["sourceFiles", "sourceFileType", "status", "subjectId", "sampleId"], "members": {"sourceFiles": {"shape": "SourceFiles", "documentation": "<p>The source files' location in Amazon S3.</p>"}, "sourceFileType": {"shape": "FileType", "documentation": "<p>The source's file type.</p>"}, "status": {"shape": "ReadSetImportJobItemStatus", "documentation": "<p>The source's status.</p>"}, "statusMessage": {"shape": "JobStatusMessage", "documentation": "<p>The source's status message.</p>"}, "subjectId": {"shape": "SubjectId", "documentation": "<p>The source's subject ID.</p>"}, "sampleId": {"shape": "SampleId", "documentation": "<p>The source's sample ID.</p>"}, "generatedFrom": {"shape": "GeneratedFrom", "documentation": "<p>Where the source originated.</p>"}, "referenceArn": {"shape": "ReferenceArn", "documentation": "<p>The source's genome reference ARN.</p>"}, "name": {"shape": "ReadSetName", "documentation": "<p>The source's name.</p>"}, "description": {"shape": "ReadSetDescription", "documentation": "<p>The source's description.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The source's tags.</p>"}}, "documentation": "<p>A source for an import read set job.</p>"}, "ImportReadSetSourceList": {"type": "list", "member": {"shape": "ImportReadSetSourceItem"}}, "ImportReferenceFilter": {"type": "structure", "members": {"status": {"shape": "ReferenceImportJobStatus", "documentation": "<p>A status to filter on.</p>"}, "createdAfter": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The filter's start date.</p>"}, "createdBefore": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The filter's end date.</p>"}}, "documentation": "<p>A filter for import references.</p>"}, "ImportReferenceJobItem": {"type": "structure", "required": ["id", "referenceStoreId", "roleArn", "status", "creationTime"], "members": {"id": {"shape": "ImportJobId", "documentation": "<p>The job's ID.</p>"}, "referenceStoreId": {"shape": "ReferenceStoreId", "documentation": "<p>The job's reference store ID.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The job's service role ARN.</p>"}, "status": {"shape": "ReferenceImportJobStatus", "documentation": "<p>The job's status.</p>"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the job was created.</p>"}, "completionTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the job completed.</p>"}}, "documentation": "<p>An import reference job.</p>"}, "ImportReferenceJobList": {"type": "list", "member": {"shape": "ImportReferenceJobItem"}}, "ImportReferenceSourceItem": {"type": "structure", "required": ["status"], "members": {"sourceFile": {"shape": "S3Uri", "documentation": "<p>The source file's location in Amazon S3.</p>"}, "status": {"shape": "ReferenceImportJobItemStatus", "documentation": "<p>The source's status.</p>"}, "statusMessage": {"shape": "JobStatusMessage", "documentation": "<p>The source's status message.</p>"}, "name": {"shape": "ReferenceName", "documentation": "<p>The source's name.</p>"}, "description": {"shape": "ReferenceDescription", "documentation": "<p>The source's description.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The source's tags.</p>"}}, "documentation": "<p>An genome reference source.</p>"}, "ImportReferenceSourceList": {"type": "list", "member": {"shape": "ImportReferenceSourceItem"}}, "Integer": {"type": "integer", "box": true}, "InternalServerException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>An unexpected error occurred. Try the request again.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "JobStatus": {"type": "string", "enum": ["SUBMITTED", "IN_PROGRESS", "CANCELLED", "COMPLETED", "FAILED", "COMPLETED_WITH_FAILURES"]}, "JobStatusMessage": {"type": "string", "max": 127, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "JobStatusMsg": {"type": "string"}, "LineSep": {"type": "string", "max": 20, "min": 1}, "ListAnnotationImportJobsFilter": {"type": "structure", "members": {"status": {"shape": "JobStatus", "documentation": "<p>A status to filter on.</p>"}, "storeName": {"shape": "String", "documentation": "<p>A store name to filter on.</p>"}}, "documentation": "<p>A filter for annotation import jobs.</p>"}, "ListAnnotationImportJobsRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListAnnotationImportJobsRequestMaxResultsInteger", "documentation": "<p>The maximum number of jobs to return in one page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "ids": {"shape": "ListAnnotationImportJobsRequestIdsList", "documentation": "<p>IDs of annotation import jobs to retrieve.</p>"}, "nextToken": {"shape": "ListAnnotationImportJobsRequestNextTokenString", "documentation": "<p>Specifies the pagination token from a previous request to retrieve the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "filter": {"shape": "ListAnnotationImportJobsFilter", "documentation": "<p>A filter to apply to the list.</p>"}}}, "ListAnnotationImportJobsRequestIdsList": {"type": "list", "member": {"shape": "ResourceIdentifier"}, "max": 20, "min": 1}, "ListAnnotationImportJobsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListAnnotationImportJobsRequestNextTokenString": {"type": "string", "max": 10000, "min": 1}, "ListAnnotationImportJobsResponse": {"type": "structure", "members": {"annotationImportJobs": {"shape": "AnnotationImportJobItems", "documentation": "<p>A list of jobs.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>Specifies the pagination token from a previous request to retrieve the next page of results.</p>"}}}, "ListAnnotationStoreVersionsFilter": {"type": "structure", "members": {"status": {"shape": "VersionStatus", "documentation": "<p> The status of an annotation store version. </p>"}}, "documentation": "<p> Use filters to focus the returned annotation store versions on a specific parameter, such as the status of the annotation store. </p>"}, "ListAnnotationStoreVersionsRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "String", "documentation": "<p> The name of an annotation store. </p>", "location": "uri", "locationName": "name"}, "maxResults": {"shape": "ListAnnotationStoreVersionsRequestMaxResultsInteger", "documentation": "<p> The maximum number of annotation store versions to return in one page of results. </p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "ListAnnotationStoreVersionsRequestNextTokenString", "documentation": "<p> Specifies the pagination token from a previous request to retrieve the next page of results. </p>", "location": "querystring", "locationName": "nextToken"}, "filter": {"shape": "ListAnnotationStoreVersionsFilter", "documentation": "<p> A filter to apply to the list of annotation store versions. </p>"}}}, "ListAnnotationStoreVersionsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListAnnotationStoreVersionsRequestNextTokenString": {"type": "string", "max": 10000, "min": 1}, "ListAnnotationStoreVersionsResponse": {"type": "structure", "members": {"annotationStoreVersions": {"shape": "AnnotationStoreVersionItems", "documentation": "<p> Lists all versions of an annotation store. </p>"}, "nextToken": {"shape": "String", "documentation": "<p> Specifies the pagination token from a previous request to retrieve the next page of results. </p>"}}}, "ListAnnotationStoresFilter": {"type": "structure", "members": {"status": {"shape": "StoreStatus", "documentation": "<p>A status to filter on.</p>"}}, "documentation": "<p>A filter for annotation stores.</p>"}, "ListAnnotationStoresRequest": {"type": "structure", "members": {"ids": {"shape": "ListAnnotationStoresRequestIdsList", "documentation": "<p>IDs of stores to list.</p>"}, "maxResults": {"shape": "ListAnnotationStoresRequestMaxResultsInteger", "documentation": "<p>The maximum number of stores to return in one page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "ListAnnotationStoresRequestNextTokenString", "documentation": "<p>Specify the pagination token from a previous request to retrieve the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "filter": {"shape": "ListAnnotationStoresFilter", "documentation": "<p>A filter to apply to the list.</p>"}}}, "ListAnnotationStoresRequestIdsList": {"type": "list", "member": {"shape": "ResourceIdentifier"}, "max": 20, "min": 1}, "ListAnnotationStoresRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListAnnotationStoresRequestNextTokenString": {"type": "string", "max": 10000, "min": 1}, "ListAnnotationStoresResponse": {"type": "structure", "members": {"annotationStores": {"shape": "AnnotationStoreItems", "documentation": "<p>A list of stores.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>A pagination token that's included if more results are available.</p>"}}}, "ListMultipartReadSetUploadsRequest": {"type": "structure", "required": ["sequenceStoreId"], "members": {"sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p> The Sequence Store ID used for the multipart uploads. </p>", "location": "uri", "locationName": "sequenceStoreId"}, "maxResults": {"shape": "ListMultipartReadSetUploadsRequestMaxResultsInteger", "documentation": "<p> The maximum number of multipart uploads returned in a page. </p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p> Next token returned in the response of a previous ListMultipartReadSetUploads call. Used to get the next page of results. </p>", "location": "querystring", "locationName": "nextToken"}}}, "ListMultipartReadSetUploadsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListMultipartReadSetUploadsResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p> Next token returned in the response of a previous ListMultipartReadSetUploads call. Used to get the next page of results. </p>"}, "uploads": {"shape": "MultipartReadSetUploadList", "documentation": "<p> An array of multipart uploads. </p>"}}}, "ListReadSetActivationJobsRequest": {"type": "structure", "required": ["sequenceStoreId"], "members": {"sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p>The read set's sequence store ID.</p>", "location": "uri", "locationName": "sequenceStoreId"}, "maxResults": {"shape": "ListReadSetActivationJobsRequestMaxResultsInteger", "documentation": "<p>The maximum number of read set activation jobs to return in one page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Specify the pagination token from a previous request to retrieve the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "filter": {"shape": "ActivateReadSetFilter", "documentation": "<p>A filter to apply to the list.</p>"}}}, "ListReadSetActivationJobsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListReadSetActivationJobsResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>A pagination token that's included if more results are available.</p>"}, "activationJobs": {"shape": "ActivateReadSetJobList", "documentation": "<p>A list of jobs.</p>"}}}, "ListReadSetExportJobsRequest": {"type": "structure", "required": ["sequenceStoreId"], "members": {"sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p>The jobs' sequence store ID.</p>", "location": "uri", "locationName": "sequenceStoreId"}, "maxResults": {"shape": "ListReadSetExportJobsRequestMaxResultsInteger", "documentation": "<p>The maximum number of jobs to return in one page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Specify the pagination token from a previous request to retrieve the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "filter": {"shape": "ExportReadSetFilter", "documentation": "<p>A filter to apply to the list.</p>"}}}, "ListReadSetExportJobsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListReadSetExportJobsResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>A pagination token that's included if more results are available.</p>"}, "exportJobs": {"shape": "ExportReadSetJobDetailList", "documentation": "<p>A list of jobs.</p>"}}}, "ListReadSetImportJobsRequest": {"type": "structure", "required": ["sequenceStoreId"], "members": {"maxResults": {"shape": "ListReadSetImportJobsRequestMaxResultsInteger", "documentation": "<p>The maximum number of jobs to return in one page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Specify the pagination token from a previous request to retrieve the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p>The jobs' sequence store ID.</p>", "location": "uri", "locationName": "sequenceStoreId"}, "filter": {"shape": "ImportReadSetFilter", "documentation": "<p>A filter to apply to the list.</p>"}}}, "ListReadSetImportJobsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListReadSetImportJobsResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>A pagination token that's included if more results are available.</p>"}, "importJobs": {"shape": "ImportReadSetJobList", "documentation": "<p>A list of jobs.</p>"}}}, "ListReadSetUploadPartsRequest": {"type": "structure", "required": ["sequenceStoreId", "uploadId", "partSource"], "members": {"sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p> The Sequence Store ID used for the multipart uploads. </p>", "location": "uri", "locationName": "sequenceStoreId"}, "uploadId": {"shape": "UploadId", "documentation": "<p> The ID for the initiated multipart upload. </p>", "location": "uri", "locationName": "uploadId"}, "partSource": {"shape": "ReadSetPartSource", "documentation": "<p> The source file for the upload part. </p>"}, "maxResults": {"shape": "ListReadSetUploadPartsRequestMaxResultsInteger", "documentation": "<p> The maximum number of read set upload parts returned in a page. </p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p> Next token returned in the response of a previous ListReadSetUploadPartsRequest call. Used to get the next page of results. </p>", "location": "querystring", "locationName": "nextToken"}, "filter": {"shape": "ReadSetUploadPartListFilter", "documentation": "<p> Attributes used to filter for a specific subset of read set part uploads. </p>"}}}, "ListReadSetUploadPartsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListReadSetUploadPartsResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p> Next token returned in the response of a previous ListReadSetUploadParts call. Used to get the next page of results. </p>"}, "parts": {"shape": "ReadSetUploadPartList", "documentation": "<p> An array of upload parts. </p>"}}}, "ListReadSetsRequest": {"type": "structure", "required": ["sequenceStoreId"], "members": {"sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p>The jobs' sequence store ID.</p>", "location": "uri", "locationName": "sequenceStoreId"}, "maxResults": {"shape": "ListReadSetsRequestMaxResultsInteger", "documentation": "<p>The maximum number of read sets to return in one page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Specify the pagination token from a previous request to retrieve the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "filter": {"shape": "ReadSetFilter", "documentation": "<p>A filter to apply to the list.</p>"}}}, "ListReadSetsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListReadSetsResponse": {"type": "structure", "required": ["readSets"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>A pagination token that's included if more results are available.</p>"}, "readSets": {"shape": "ReadSetList", "documentation": "<p>A list of read sets.</p>"}}}, "ListReferenceImportJobsRequest": {"type": "structure", "required": ["referenceStoreId"], "members": {"maxResults": {"shape": "ListReferenceImportJobsRequestMaxResultsInteger", "documentation": "<p>The maximum number of jobs to return in one page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Specify the pagination token from a previous request to retrieve the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "referenceStoreId": {"shape": "ReferenceStoreId", "documentation": "<p>The job's reference store ID.</p>", "location": "uri", "locationName": "referenceStoreId"}, "filter": {"shape": "ImportReferenceFilter", "documentation": "<p>A filter to apply to the list.</p>"}}}, "ListReferenceImportJobsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListReferenceImportJobsResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>A pagination token that's included if more results are available.</p>"}, "importJobs": {"shape": "ImportReferenceJobList", "documentation": "<p>A lis of jobs.</p>"}}}, "ListReferenceStoresRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListReferenceStoresRequestMaxResultsInteger", "documentation": "<p>The maximum number of stores to return in one page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Specify the pagination token from a previous request to retrieve the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "filter": {"shape": "ReferenceStoreFilter", "documentation": "<p>A filter to apply to the list.</p>"}}}, "ListReferenceStoresRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListReferenceStoresResponse": {"type": "structure", "required": ["referenceStores"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>A pagination token that's included if more results are available.</p>"}, "referenceStores": {"shape": "ReferenceStoreDetailList", "documentation": "<p>A list of reference stores.</p>"}}}, "ListReferencesRequest": {"type": "structure", "required": ["referenceStoreId"], "members": {"referenceStoreId": {"shape": "ReferenceStoreId", "documentation": "<p>The references' reference store ID.</p>", "location": "uri", "locationName": "referenceStoreId"}, "maxResults": {"shape": "ListReferencesRequestMaxResultsInteger", "documentation": "<p>The maximum number of references to return in one page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Specify the pagination token from a previous request to retrieve the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "filter": {"shape": "ReferenceFilter", "documentation": "<p>A filter to apply to the list.</p>"}}}, "ListReferencesRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListReferencesResponse": {"type": "structure", "required": ["references"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>A pagination token that's included if more results are available.</p>"}, "references": {"shape": "ReferenceList", "documentation": "<p>A list of references.</p>"}}}, "ListRunGroupsRequest": {"type": "structure", "members": {"name": {"shape": "RunGroupName", "documentation": "<p>The run groups' name.</p>", "location": "querystring", "locationName": "name"}, "startingToken": {"shape": "RunGroupListToken", "documentation": "<p>Specify the pagination token from a previous request to retrieve the next page of results.</p>", "location": "querystring", "locationName": "startingToken"}, "maxResults": {"shape": "ListRunGroupsRequestMaxResultsInteger", "documentation": "<p>The maximum number of run groups to return in one page of results.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListRunGroupsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListRunGroupsResponse": {"type": "structure", "members": {"items": {"shape": "RunGroupList", "documentation": "<p>A list of groups.</p>"}, "nextToken": {"shape": "RunGroupListToken", "documentation": "<p>A pagination token that's included if more results are available.</p>"}}}, "ListRunTasksRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "RunId", "documentation": "<p>The run's ID.</p>", "location": "uri", "locationName": "id"}, "status": {"shape": "TaskStatus", "documentation": "<p>Filter the list by status.</p>", "location": "querystring", "locationName": "status"}, "startingToken": {"shape": "TaskListToken", "documentation": "<p>Specify the pagination token from a previous request to retrieve the next page of results.</p>", "location": "querystring", "locationName": "startingToken"}, "maxResults": {"shape": "ListRunTasksRequestMaxResultsInteger", "documentation": "<p>The maximum number of run tasks to return in one page of results.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListRunTasksRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListRunTasksResponse": {"type": "structure", "members": {"items": {"shape": "TaskList", "documentation": "<p>A list of tasks.</p>"}, "nextToken": {"shape": "TaskListToken", "documentation": "<p>A pagination token that's included if more results are available.</p>"}}}, "ListRunsRequest": {"type": "structure", "members": {"name": {"shape": "RunName", "documentation": "<p>Filter the list by run name.</p>", "location": "querystring", "locationName": "name"}, "runGroupId": {"shape": "RunGroupId", "documentation": "<p>Filter the list by run group ID.</p>", "location": "querystring", "locationName": "runGroupId"}, "startingToken": {"shape": "RunListToken", "documentation": "<p>Specify the pagination token from a previous request to retrieve the next page of results.</p>", "location": "querystring", "locationName": "startingToken"}, "maxResults": {"shape": "ListRunsRequestMaxResultsInteger", "documentation": "<p>The maximum number of runs to return in one page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "status": {"shape": "RunStatus", "documentation": "<p> The status of a run. </p>", "location": "querystring", "locationName": "status"}}}, "ListRunsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListRunsResponse": {"type": "structure", "members": {"items": {"shape": "RunList", "documentation": "<p>A list of runs.</p>"}, "nextToken": {"shape": "RunListToken", "documentation": "<p>A pagination token that's included if more results are available.</p>"}}}, "ListSequenceStoresRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListSequenceStoresRequestMaxResultsInteger", "documentation": "<p>The maximum number of stores to return in one page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Specify the pagination token from a previous request to retrieve the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "filter": {"shape": "SequenceStoreFilter", "documentation": "<p>A filter to apply to the list.</p>"}}}, "ListSequenceStoresRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListSequenceStoresResponse": {"type": "structure", "required": ["sequenceStores"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>A pagination token that's included if more results are available.</p>"}, "sequenceStores": {"shape": "SequenceStoreDetailList", "documentation": "<p>A list of sequence stores.</p>"}}}, "ListSharesRequest": {"type": "structure", "required": ["resourceOwner"], "members": {"resourceOwner": {"shape": "ResourceOwner", "documentation": "<p> The account that owns the analytics store shared. </p>"}, "filter": {"shape": "Filter", "documentation": "<p> Attributes used to filter for a specific subset of shares. </p>"}, "nextToken": {"shape": "String", "documentation": "<p> Next token returned in the response of a previous ListReadSetUploadPartsRequest call. Used to get the next page of results. </p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "Integer", "documentation": "<p> The maximum number of shares to return in one page of results. </p>", "location": "querystring", "locationName": "maxResults"}}}, "ListSharesResponse": {"type": "structure", "required": ["shares"], "members": {"shares": {"shape": "ShareDetailsList", "documentation": "<p> The shares available and their meta details. </p>"}, "nextToken": {"shape": "String", "documentation": "<p> Next token returned in the response of a previous ListSharesResponse call. Used to get the next page of results. </p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "TagArn", "documentation": "<p>The resource's ARN.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "required": ["tags"], "members": {"tags": {"shape": "TagMap", "documentation": "<p>A list of tags.</p>"}}}, "ListVariantImportJobsFilter": {"type": "structure", "members": {"status": {"shape": "JobStatus", "documentation": "<p>A status to filter on.</p>"}, "storeName": {"shape": "String", "documentation": "<p>A store name to filter on.</p>"}}, "documentation": "<p>A filter for variant import jobs.</p>"}, "ListVariantImportJobsRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListVariantImportJobsRequestMaxResultsInteger", "documentation": "<p>The maximum number of import jobs to return in one page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "ids": {"shape": "ListVariantImportJobsRequestIdsList", "documentation": "<p>A list of job IDs.</p>"}, "nextToken": {"shape": "ListVariantImportJobsRequestNextTokenString", "documentation": "<p>Specify the pagination token from a previous request to retrieve the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "filter": {"shape": "ListVariantImportJobsFilter", "documentation": "<p>A filter to apply to the list.</p>"}}}, "ListVariantImportJobsRequestIdsList": {"type": "list", "member": {"shape": "ResourceIdentifier"}, "max": 20, "min": 1}, "ListVariantImportJobsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListVariantImportJobsRequestNextTokenString": {"type": "string", "max": 10000, "min": 1}, "ListVariantImportJobsResponse": {"type": "structure", "members": {"variantImportJobs": {"shape": "VariantImportJobItems", "documentation": "<p>A list of jobs.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>A pagination token that's included if more results are available.</p>"}}}, "ListVariantStoresFilter": {"type": "structure", "members": {"status": {"shape": "StoreStatus", "documentation": "<p>A status to filter on.</p>"}}, "documentation": "<p>A filter for variant stores.</p>"}, "ListVariantStoresRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListVariantStoresRequestMaxResultsInteger", "documentation": "<p>The maximum number of stores to return in one page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "ids": {"shape": "ListVariantStoresRequestIdsList", "documentation": "<p>A list of store IDs.</p>"}, "nextToken": {"shape": "ListVariantStoresRequestNextTokenString", "documentation": "<p>Specify the pagination token from a previous request to retrieve the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "filter": {"shape": "ListVariantStoresFilter", "documentation": "<p>A filter to apply to the list.</p>"}}}, "ListVariantStoresRequestIdsList": {"type": "list", "member": {"shape": "ResourceIdentifier"}, "max": 20, "min": 1}, "ListVariantStoresRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListVariantStoresRequestNextTokenString": {"type": "string", "max": 10000, "min": 1}, "ListVariantStoresResponse": {"type": "structure", "members": {"variantStores": {"shape": "VariantStoreItems", "documentation": "<p>A list of variant stores.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>A pagination token that's included if more results are available.</p>"}}}, "ListWorkflowsRequest": {"type": "structure", "members": {"type": {"shape": "WorkflowType", "documentation": "<p>The workflows' type.</p>", "location": "querystring", "locationName": "type"}, "name": {"shape": "WorkflowName", "documentation": "<p>The workflows' name.</p>", "location": "querystring", "locationName": "name"}, "startingToken": {"shape": "WorkflowListToken", "documentation": "<p>Specify the pagination token from a previous request to retrieve the next page of results.</p>", "location": "querystring", "locationName": "startingToken"}, "maxResults": {"shape": "ListWorkflowsRequestMaxResultsInteger", "documentation": "<p>The maximum number of workflows to return in one page of results.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListWorkflowsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListWorkflowsResponse": {"type": "structure", "members": {"items": {"shape": "WorkflowList", "documentation": "<p>The workflows' items.</p>"}, "nextToken": {"shape": "WorkflowListToken", "documentation": "<p>A pagination token that's included if more results are available.</p>"}}}, "Long": {"type": "long", "box": true}, "Md5": {"type": "string", "max": 255, "min": 1, "pattern": "[\\p{L}||\\p{N}]+"}, "MultipartReadSetUploadList": {"type": "list", "member": {"shape": "MultipartReadSetUploadListItem"}}, "MultipartReadSetUploadListItem": {"type": "structure", "required": ["sequenceStoreId", "uploadId", "sourceFileType", "subjectId", "sampleId", "generatedFrom", "referenceArn", "creationTime"], "members": {"sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p> The sequence store ID used for the multipart upload. </p>"}, "uploadId": {"shape": "UploadId", "documentation": "<p> The ID for the initiated multipart upload. </p>"}, "sourceFileType": {"shape": "FileType", "documentation": "<p> The type of file the read set originated from. </p>"}, "subjectId": {"shape": "SubjectId", "documentation": "<p> The read set source's subject ID. </p>"}, "sampleId": {"shape": "SampleId", "documentation": "<p> The read set source's sample ID. </p>"}, "generatedFrom": {"shape": "GeneratedFrom", "documentation": "<p> The source of an uploaded part. </p>"}, "referenceArn": {"shape": "ReferenceArn", "documentation": "<p> The source's reference ARN. </p>"}, "name": {"shape": "ReadSetName", "documentation": "<p> The name of a read set. </p>"}, "description": {"shape": "ReadSetDescription", "documentation": "<p> The description of a read set. </p>"}, "tags": {"shape": "TagMap", "documentation": "<p> Any tags you wish to add to a read set. </p>"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p> The time stamp for when a direct upload was created. </p>"}}, "documentation": "<p> Part of the response to ListMultipartReadSetUploads, excluding completed and aborted multipart uploads. </p>"}, "NextToken": {"type": "string", "max": 6144, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "NotSupportedOperationException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p> The operation is not supported by Amazon Omics, or the API does not exist. </p>", "error": {"httpStatusCode": 405, "senderFault": true}, "exception": true}, "PrimitiveBoolean": {"type": "boolean"}, "Quote": {"type": "string", "max": 1, "min": 1}, "QuoteAll": {"type": "boolean"}, "Range": {"type": "string", "max": 127, "min": 1, "pattern": "[\\p{N}||\\p{P}]+"}, "RangeNotSatisfiableException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The ranges specified in the request are not valid.</p>", "error": {"httpStatusCode": 416, "senderFault": true}, "exception": true, "retryable": {"throttling": false}}, "ReadOptions": {"type": "structure", "members": {"sep": {"shape": "Separator", "documentation": "<p>The file's field separator.</p>"}, "encoding": {"shape": "Encoding", "documentation": "<p>The file's encoding.</p>"}, "quote": {"shape": "Quote", "documentation": "<p>The file's quote character.</p>"}, "quoteAll": {"shape": "Quote<PERSON>ll", "documentation": "<p>Whether all values need to be quoted, or just those that contain quotes.</p>"}, "escape": {"shape": "EscapeChar", "documentation": "<p>A character for escaping quotes in the file.</p>"}, "escapeQuotes": {"shape": "EscapeQuotes", "documentation": "<p>Whether quotes need to be escaped in the file.</p>"}, "comment": {"shape": "CommentChar", "documentation": "<p>The file's comment character.</p>"}, "header": {"shape": "Header", "documentation": "<p>Whether the file has a header row.</p>"}, "lineSep": {"shape": "LineSep", "documentation": "<p>A line separator for the file.</p>"}}, "documentation": "<p>Read options for an annotation import job.</p>"}, "ReadSetActivationJobItemStatus": {"type": "string", "enum": ["NOT_STARTED", "IN_PROGRESS", "FINISHED", "FAILED"]}, "ReadSetActivationJobStatus": {"type": "string", "enum": ["SUBMITTED", "IN_PROGRESS", "CANCELLING", "CANCELLED", "FAILED", "COMPLETED", "COMPLETED_WITH_FAILURES"]}, "ReadSetArn": {"type": "string", "max": 127, "min": 1, "pattern": "arn:.+"}, "ReadSetBatchError": {"type": "structure", "required": ["id", "code", "message"], "members": {"id": {"shape": "ReadSetId", "documentation": "<p>The error's ID.</p>"}, "code": {"shape": "String", "documentation": "<p>The error's code.</p>"}, "message": {"shape": "String", "documentation": "<p>The error's message.</p>"}}, "documentation": "<p>An error from a batch read set operation.</p>"}, "ReadSetBatchErrorList": {"type": "list", "member": {"shape": "ReadSetBatchError"}}, "ReadSetDescription": {"type": "string", "max": 255, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "ReadSetExportJobItemStatus": {"type": "string", "enum": ["NOT_STARTED", "IN_PROGRESS", "FINISHED", "FAILED"]}, "ReadSetExportJobStatus": {"type": "string", "enum": ["SUBMITTED", "IN_PROGRESS", "CANCELLING", "CANCELLED", "FAILED", "COMPLETED", "COMPLETED_WITH_FAILURES"]}, "ReadSetFile": {"type": "string", "enum": ["SOURCE1", "SOURCE2", "INDEX"]}, "ReadSetFiles": {"type": "structure", "members": {"source1": {"shape": "FileInformation", "documentation": "<p>The location of the first file in Amazon S3.</p>"}, "source2": {"shape": "FileInformation", "documentation": "<p>The location of the second file in Amazon S3.</p>"}, "index": {"shape": "FileInformation", "documentation": "<p>The files' index.</p>"}}, "documentation": "<p>Files in a read set.</p>"}, "ReadSetFilter": {"type": "structure", "members": {"name": {"shape": "ReadSetName", "documentation": "<p>A name to filter on.</p>"}, "status": {"shape": "ReadSetStatus", "documentation": "<p>A status to filter on.</p>"}, "referenceArn": {"shape": "ReferenceArn<PERSON>ilter", "documentation": "<p>A genome reference ARN to filter on.</p>"}, "createdAfter": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The filter's start date.</p>"}, "createdBefore": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The filter's end date.</p>"}, "sampleId": {"shape": "SampleId", "documentation": "<p> The read set source's sample ID. </p>"}, "subjectId": {"shape": "SubjectId", "documentation": "<p> The read set source's subject ID. </p>"}, "generatedFrom": {"shape": "GeneratedFrom", "documentation": "<p> Where the source originated. </p>"}, "creationType": {"shape": "CreationType", "documentation": "<p> The creation type of the read set. </p>"}}, "documentation": "<p>A filter for read sets.</p>"}, "ReadSetId": {"type": "string", "max": 36, "min": 10, "pattern": "[0-9]+"}, "ReadSetIdList": {"type": "list", "member": {"shape": "ReadSetId"}, "max": 100, "min": 1}, "ReadSetImportJobItemStatus": {"type": "string", "enum": ["NOT_STARTED", "IN_PROGRESS", "FINISHED", "FAILED"]}, "ReadSetImportJobStatus": {"type": "string", "enum": ["SUBMITTED", "IN_PROGRESS", "CANCELLING", "CANCELLED", "FAILED", "COMPLETED", "COMPLETED_WITH_FAILURES"]}, "ReadSetList": {"type": "list", "member": {"shape": "ReadSetListItem"}}, "ReadSetListItem": {"type": "structure", "required": ["id", "arn", "sequenceStoreId", "status", "fileType", "creationTime"], "members": {"id": {"shape": "ReadSetId", "documentation": "<p>The read set's ID.</p>"}, "arn": {"shape": "ReadSetArn", "documentation": "<p>The read set's ARN.</p>"}, "sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p>The read set's sequence store ID.</p>"}, "subjectId": {"shape": "SubjectId", "documentation": "<p>The read set's subject ID.</p>"}, "sampleId": {"shape": "SampleId", "documentation": "<p>The read set's sample ID.</p>"}, "status": {"shape": "ReadSetStatus", "documentation": "<p>The read set's status.</p>"}, "name": {"shape": "ReadSetName", "documentation": "<p>The read set's name.</p>"}, "description": {"shape": "ReadSetDescription", "documentation": "<p>The read set's description.</p>"}, "referenceArn": {"shape": "ReferenceArn", "documentation": "<p>The read set's genome reference ARN.</p>"}, "fileType": {"shape": "FileType", "documentation": "<p>The read set's file type.</p>"}, "sequenceInformation": {"shape": "SequenceInformation"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the read set was created.</p>"}, "statusMessage": {"shape": "ReadSetStatusMessage", "documentation": "<p> The status for a read set. It provides more detail as to why the read set has a status. </p>"}, "creationType": {"shape": "CreationType", "documentation": "<p> The creation type of the read set. </p>"}, "etag": {"shape": "ETag", "documentation": "<p> The entity tag (ETag) is a hash of the object representing its semantic content. </p>"}}, "documentation": "<p>A read set.</p>"}, "ReadSetName": {"type": "string", "max": 127, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "ReadSetPartSource": {"type": "string", "enum": ["SOURCE1", "SOURCE2"]}, "ReadSetPartStreamingBlob": {"type": "blob", "requiresLength": true, "streaming": true}, "ReadSetStatus": {"type": "string", "enum": ["ARCHIVED", "ACTIVATING", "ACTIVE", "DELETING", "DELETED", "PROCESSING_UPLOAD", "UPLOAD_FAILED"]}, "ReadSetStatusMessage": {"type": "string", "max": 255, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "ReadSetStreamingBlob": {"type": "blob", "streaming": true}, "ReadSetUploadPartList": {"type": "list", "member": {"shape": "ReadSetUploadPartListItem"}}, "ReadSetUploadPartListFilter": {"type": "structure", "members": {"createdAfter": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p> Filters for read set uploads after a specified time. </p>"}, "createdBefore": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p> Filters for read set part uploads before a specified time. </p>"}}, "documentation": "<p> Filter settings that select for read set upload parts of interest. </p>"}, "ReadSetUploadPartListItem": {"type": "structure", "required": ["partNumber", "partSize", "partSource", "checksum"], "members": {"partNumber": {"shape": "ReadSetUploadPartListItemPartNumberInteger", "documentation": "<p> The number identifying the part in an upload. </p>"}, "partSize": {"shape": "ReadSetUploadPartListItemPartSizeLong", "documentation": "<p> The size of the the part in an upload. </p>"}, "partSource": {"shape": "ReadSetPartSource", "documentation": "<p> The origin of the part being direct uploaded. </p>"}, "checksum": {"shape": "String", "documentation": "<p> A unique identifier used to confirm that parts are being added to the correct upload. </p>"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p> The time stamp for when a direct upload was created. </p>"}, "lastUpdatedTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p> The time stamp for the most recent update to an uploaded part. </p>"}}, "documentation": "<p> The metadata of a single part of a file that was added to a multipart upload. A list of these parts is returned in the response to the ListReadSetUploadParts API. </p>"}, "ReadSetUploadPartListItemPartNumberInteger": {"type": "integer", "box": true, "max": 10000, "min": 1}, "ReadSetUploadPartListItemPartSizeLong": {"type": "long", "box": true, "max": 5368709120, "min": 1}, "ReferenceArn": {"type": "string", "max": 127, "min": 1, "pattern": "arn:.+"}, "ReferenceArnFilter": {"type": "string", "max": 127, "min": 0, "pattern": "$|^arn:.+"}, "ReferenceDescription": {"type": "string", "max": 255, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "ReferenceFile": {"type": "string", "enum": ["SOURCE", "INDEX"]}, "ReferenceFiles": {"type": "structure", "members": {"source": {"shape": "FileInformation", "documentation": "<p>The source file's location in Amazon S3.</p>"}, "index": {"shape": "FileInformation", "documentation": "<p>The files' index.</p>"}}, "documentation": "<p>A set of genome reference files.</p>"}, "ReferenceFilter": {"type": "structure", "members": {"name": {"shape": "ReferenceName", "documentation": "<p>A name to filter on.</p>"}, "md5": {"shape": "Md5", "documentation": "<p>An MD5 checksum to filter on.</p>"}, "createdAfter": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The filter's start date.</p>"}, "createdBefore": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The filter's end date.</p>"}}, "documentation": "<p>A filter for references.</p>"}, "ReferenceId": {"type": "string", "max": 36, "min": 10, "pattern": "[0-9]+"}, "ReferenceImportJobItemStatus": {"type": "string", "enum": ["NOT_STARTED", "IN_PROGRESS", "FINISHED", "FAILED"]}, "ReferenceImportJobStatus": {"type": "string", "enum": ["SUBMITTED", "IN_PROGRESS", "CANCELLING", "CANCELLED", "FAILED", "COMPLETED", "COMPLETED_WITH_FAILURES"]}, "ReferenceItem": {"type": "structure", "members": {"referenceArn": {"shape": "ReferenceArn", "documentation": "<p>The reference's ARN.</p>"}}, "documentation": "<p>A genome reference.</p>", "union": true}, "ReferenceList": {"type": "list", "member": {"shape": "ReferenceListItem"}}, "ReferenceListItem": {"type": "structure", "required": ["id", "arn", "referenceStoreId", "md5", "creationTime", "updateTime"], "members": {"id": {"shape": "ReferenceId", "documentation": "<p>The reference's ID.</p>"}, "arn": {"shape": "ReferenceArn", "documentation": "<p>The reference's ARN.</p>"}, "referenceStoreId": {"shape": "ReferenceStoreId", "documentation": "<p>The reference's store ID.</p>"}, "md5": {"shape": "Md5", "documentation": "<p>The reference's MD5 checksum.</p>"}, "status": {"shape": "ReferenceStatus", "documentation": "<p>The reference's status.</p>"}, "name": {"shape": "ReferenceName", "documentation": "<p>The reference's name.</p>"}, "description": {"shape": "ReferenceDescription", "documentation": "<p>The reference's description.</p>"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the reference was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the reference was updated.</p>"}}, "documentation": "<p>A genome reference.</p>"}, "ReferenceName": {"type": "string", "max": 255, "min": 3, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "ReferenceStatus": {"type": "string", "enum": ["ACTIVE", "DELETING", "DELETED"]}, "ReferenceStoreArn": {"type": "string", "max": 127, "min": 1, "pattern": "arn:.+"}, "ReferenceStoreDescription": {"type": "string", "max": 255, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "ReferenceStoreDetail": {"type": "structure", "required": ["arn", "id", "creationTime"], "members": {"arn": {"shape": "ReferenceStoreArn", "documentation": "<p>The store's ARN.</p>"}, "id": {"shape": "ReferenceStoreId", "documentation": "<p>The store's ID.</p>"}, "name": {"shape": "ReferenceStoreName", "documentation": "<p>The store's name.</p>"}, "description": {"shape": "ReferenceStoreDescription", "documentation": "<p>The store's description.</p>"}, "sseConfig": {"shape": "SseConfig", "documentation": "<p>The store's server-side encryption (SSE) settings.</p>"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the store was created.</p>"}}, "documentation": "<p>Details about a reference store.</p>"}, "ReferenceStoreDetailList": {"type": "list", "member": {"shape": "ReferenceStoreDetail"}}, "ReferenceStoreFilter": {"type": "structure", "members": {"name": {"shape": "ReferenceStoreName", "documentation": "<p>The name to filter on.</p>"}, "createdAfter": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The filter's start date.</p>"}, "createdBefore": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The filter's end date.</p>"}}, "documentation": "<p>A filter for reference stores.</p>"}, "ReferenceStoreId": {"type": "string", "max": 36, "min": 10, "pattern": "[0-9]+"}, "ReferenceStoreName": {"type": "string", "max": 127, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "ReferenceStreamingBlob": {"type": "blob", "streaming": true}, "RequestTimeoutException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The request timed out.</p>", "error": {"httpStatusCode": 408, "senderFault": true}, "exception": true}, "ResourceId": {"type": "string", "pattern": "[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}"}, "ResourceIdentifier": {"type": "string", "max": 50, "min": 1}, "ResourceNotFoundException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The target resource was not found in the current Region.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "ResourceOwner": {"type": "string", "enum": ["SELF", "OTHER"]}, "RoleArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:.*"}, "RunArn": {"type": "string", "max": 128, "min": 1, "pattern": "arn:.+"}, "RunExport": {"type": "string", "enum": ["DEFINITION"], "max": 64, "min": 1}, "RunExportList": {"type": "list", "member": {"shape": "RunExport"}, "max": 32, "min": 0}, "RunFailureReason": {"type": "string", "max": 64, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "RunGroupArn": {"type": "string", "max": 128, "min": 1, "pattern": "arn:.+"}, "RunGroupId": {"type": "string", "max": 18, "min": 1, "pattern": "[0-9]+"}, "RunGroupList": {"type": "list", "member": {"shape": "RunGroupListItem"}}, "RunGroupListItem": {"type": "structure", "members": {"arn": {"shape": "RunGroupArn", "documentation": "<p>The group's ARN.</p>"}, "id": {"shape": "RunGroupId", "documentation": "<p>The group's ID.</p>"}, "name": {"shape": "RunGroupName", "documentation": "<p>The group's name.</p>"}, "maxCpus": {"shape": "RunGroupListItemMaxCpusInteger", "documentation": "<p>The group's maximum CPU count setting.</p>"}, "maxRuns": {"shape": "RunGroupListItemMaxRunsInteger", "documentation": "<p>The group's maximum concurrent run setting.</p>"}, "maxDuration": {"shape": "RunGroupListItemMaxDurationInteger", "documentation": "<p>The group's maximum duration setting in minutes.</p>"}, "creationTime": {"shape": "RunGroupTimestamp", "documentation": "<p>When the group was created.</p>"}, "maxGpus": {"shape": "RunGroupListItemMaxGpusInteger", "documentation": "<p> The maximum GPUs that can be used by a run group. </p>"}}, "documentation": "<p>A run group.</p>"}, "RunGroupListItemMaxCpusInteger": {"type": "integer", "box": true, "max": 100000, "min": 1}, "RunGroupListItemMaxDurationInteger": {"type": "integer", "box": true, "max": 100000, "min": 1}, "RunGroupListItemMaxGpusInteger": {"type": "integer", "box": true, "max": 100000, "min": 1}, "RunGroupListItemMaxRunsInteger": {"type": "integer", "box": true, "max": 100000, "min": 1}, "RunGroupListToken": {"type": "string", "max": 128, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "RunGroupName": {"type": "string", "max": 128, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "RunGroupRequestId": {"type": "string", "max": 128, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "RunGroupTimestamp": {"type": "timestamp", "timestampFormat": "iso8601"}, "RunId": {"type": "string", "max": 18, "min": 1, "pattern": "[0-9]+"}, "RunLeftNormalization": {"type": "boolean"}, "RunList": {"type": "list", "member": {"shape": "RunListItem"}}, "RunListItem": {"type": "structure", "members": {"arn": {"shape": "RunArn", "documentation": "<p>The run's ARN.</p>"}, "id": {"shape": "RunId", "documentation": "<p>The run's ID.</p>"}, "status": {"shape": "RunStatus", "documentation": "<p>The run's status.</p>"}, "workflowId": {"shape": "WorkflowId", "documentation": "<p>The run's workflow ID.</p>"}, "name": {"shape": "RunName", "documentation": "<p>The run's name.</p>"}, "priority": {"shape": "RunListItemPriorityInteger", "documentation": "<p>The run's priority.</p>"}, "storageCapacity": {"shape": "RunListItemStorageCapacityInteger", "documentation": "<p>The run's storage capacity.</p>"}, "creationTime": {"shape": "RunTimestamp", "documentation": "<p>When the run was created.</p>"}, "startTime": {"shape": "RunTimestamp", "documentation": "<p>When the run started.</p>"}, "stopTime": {"shape": "RunTimestamp", "documentation": "<p>When the run stopped.</p>"}}, "documentation": "<p>A workflow run.</p>"}, "RunListItemPriorityInteger": {"type": "integer", "box": true, "max": 100000, "min": 0}, "RunListItemStorageCapacityInteger": {"type": "integer", "box": true, "max": 100000, "min": 0}, "RunListToken": {"type": "string", "max": 128, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "RunLogLevel": {"type": "string", "enum": ["OFF", "FATAL", "ERROR", "ALL"], "max": 64, "min": 1}, "RunLogLocation": {"type": "structure", "members": {"engineLogStream": {"shape": "EngineLogStream", "documentation": "<p> The log stream ARN for the engine log. </p>"}, "runLogStream": {"shape": "RunLogStream", "documentation": "<p> The log stream ARN for the run log. </p>"}}, "documentation": "<p> The URI for the run log. </p>"}, "RunLogStream": {"type": "string", "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "RunName": {"type": "string", "max": 128, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "RunOutputUri": {"type": "string", "max": 750, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "RunParameters": {"type": "structure", "members": {}, "document": true}, "RunRequestId": {"type": "string", "max": 128, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "RunResourceDigest": {"type": "string", "max": 64, "min": 0, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "RunResourceDigestKey": {"type": "string", "max": 256, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "RunResourceDigests": {"type": "map", "key": {"shape": "RunResourceDigestKey"}, "value": {"shape": "RunResourceDigest"}}, "RunRetentionMode": {"type": "string", "enum": ["RETAIN", "REMOVE"], "max": 64, "min": 1}, "RunRoleArn": {"type": "string", "max": 128, "min": 1, "pattern": "arn:.+"}, "RunStartedBy": {"type": "string", "max": 128, "min": 1}, "RunStatus": {"type": "string", "enum": ["PENDING", "STARTING", "RUNNING", "STOPPING", "COMPLETED", "DELETED", "CANCELLED", "FAILED"], "max": 64, "min": 1}, "RunStatusMessage": {"type": "string", "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "RunTimestamp": {"type": "timestamp", "timestampFormat": "iso8601"}, "RunUuid": {"type": "string", "max": 128, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "S3Destination": {"type": "string", "pattern": "s3://([a-z0-9][a-z0-9-.]{1,61}[a-z0-9])/?((.{1,1024})/)?"}, "S3Uri": {"type": "string", "pattern": "s3://([a-z0-9][a-z0-9-.]{1,61}[a-z0-9])/(.{1,1024})"}, "SampleId": {"type": "string", "max": 127, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "SchemaItem": {"type": "map", "key": {"shape": "SchemaItemKeyString"}, "value": {"shape": "SchemaValueType"}, "max": 1, "min": 1}, "SchemaItemKeyString": {"type": "string", "pattern": "[a-z0-9_]{1,255}"}, "SchemaValueType": {"type": "string", "enum": ["LONG", "INT", "STRING", "FLOAT", "DOUBLE", "BOOLEAN"]}, "Separator": {"type": "string", "max": 20, "min": 1}, "SequenceInformation": {"type": "structure", "members": {"totalReadCount": {"shape": "<PERSON>", "documentation": "<p>The sequence's total read count.</p>"}, "totalBaseCount": {"shape": "<PERSON>", "documentation": "<p>The sequence's total base count.</p>"}, "generatedFrom": {"shape": "GeneratedFrom", "documentation": "<p>Where the sequence originated.</p>"}, "alignment": {"shape": "String", "documentation": "<p>The sequence's alignment setting.</p>"}}, "documentation": "<p>Details about a sequence.</p>"}, "SequenceStoreArn": {"type": "string", "max": 127, "min": 1, "pattern": "arn:.+"}, "SequenceStoreDescription": {"type": "string", "max": 255, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "SequenceStoreDetail": {"type": "structure", "required": ["arn", "id", "creationTime"], "members": {"arn": {"shape": "SequenceStoreArn", "documentation": "<p>The store's ARN.</p>"}, "id": {"shape": "SequenceStoreId", "documentation": "<p>The store's ID.</p>"}, "name": {"shape": "SequenceStoreName", "documentation": "<p>The store's name.</p>"}, "description": {"shape": "SequenceStoreDescription", "documentation": "<p>The store's description.</p>"}, "sseConfig": {"shape": "SseConfig", "documentation": "<p>The store's server-side encryption (SSE) settings.</p>"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the store was created.</p>"}, "fallbackLocation": {"shape": "S3Destination", "documentation": "<p> An S3 location that is used to store files that have failed a direct upload. </p>"}}, "documentation": "<p>Details about a sequence store.</p>"}, "SequenceStoreDetailList": {"type": "list", "member": {"shape": "SequenceStoreDetail"}}, "SequenceStoreFilter": {"type": "structure", "members": {"name": {"shape": "SequenceStoreName", "documentation": "<p>A name to filter on.</p>"}, "createdAfter": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The filter's start date.</p>"}, "createdBefore": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The filter's end date.</p>"}}, "documentation": "<p>A filter for a sequence store.</p>"}, "SequenceStoreId": {"type": "string", "max": 36, "min": 10, "pattern": "[0-9]+"}, "SequenceStoreName": {"type": "string", "max": 127, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "ServiceQuotaExceededException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The request exceeds a service quota.</p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "ShareDetails": {"type": "structure", "members": {"shareId": {"shape": "String", "documentation": "<p> The ID for a share offer for an analytics store . </p>"}, "resourceArn": {"shape": "String", "documentation": "<p> The resource Arn of the analytics store being shared. </p>"}, "principalSubscriber": {"shape": "String", "documentation": "<p> The principal subscriber is the account the analytics store data is being shared with. </p>"}, "ownerId": {"shape": "String", "documentation": "<p> The account ID for the data owner. The owner creates the share offer. </p>"}, "status": {"shape": "ShareStatus", "documentation": "<p> The status of a share. </p>"}, "statusMessage": {"shape": "StatusMessage", "documentation": "<p> The status message for a share. It provides more details on the status of the share. </p>"}, "shareName": {"shape": "ShareName", "documentation": "<p> The name of the share. </p>"}, "creationTime": {"shape": "CreationTime", "documentation": "<p> The timestamp for when the share was created. </p>"}, "updateTime": {"shape": "UpdateTime", "documentation": "<p> The timestamp of the share update. </p>"}}, "documentation": "<p> The details of a share. </p>"}, "ShareDetailsList": {"type": "list", "member": {"shape": "ShareDetails"}}, "ShareName": {"type": "string", "max": 256, "min": 1, "pattern": "[a-zA-Z0-9_-]+"}, "ShareStatus": {"type": "string", "enum": ["PENDING", "ACTIVATING", "ACTIVE", "DELETING", "DELETED", "FAILED"]}, "SourceFiles": {"type": "structure", "required": ["source1"], "members": {"source1": {"shape": "S3Uri", "documentation": "<p>The location of the first file in Amazon S3.</p>"}, "source2": {"shape": "S3Uri", "documentation": "<p>The location of the second file in Amazon S3.</p>"}}, "documentation": "<p>Source files for a sequence.</p>"}, "SseConfig": {"type": "structure", "required": ["type"], "members": {"type": {"shape": "EncryptionType", "documentation": "<p>The encryption type.</p>"}, "keyArn": {"shape": "SseConfigKeyArnString", "documentation": "<p>An encryption key ARN.</p>"}}, "documentation": "<p>Server-side encryption (SSE) settings for a store.</p>"}, "SseConfigKeyArnString": {"type": "string", "max": 2048, "min": 20, "pattern": ".*arn:([^: ]*):([^: ]*):([^: ]*):([0-9]{12}):([^: ]*).*"}, "StartAnnotationImportRequest": {"type": "structure", "required": ["destinationName", "roleArn", "items"], "members": {"destinationName": {"shape": "StoreName", "documentation": "<p>A destination annotation store for the job.</p>"}, "roleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>A service role for the job.</p>"}, "items": {"shape": "AnnotationImportItemSources", "documentation": "<p>Items to import.</p>"}, "versionName": {"shape": "VersionName", "documentation": "<p> The name of the annotation store version. </p>"}, "formatOptions": {"shape": "FormatOptions", "documentation": "<p>Formatting options for the annotation file.</p>"}, "runLeftNormalization": {"shape": "RunLeftNormalization", "documentation": "<p>The job's left normalization setting.</p>"}, "annotationFields": {"shape": "AnnotationFieldMap", "documentation": "<p> The annotation schema generated by the parsed annotation data. </p>"}}}, "StartAnnotationImportResponse": {"type": "structure", "required": ["jobId"], "members": {"jobId": {"shape": "ResourceId", "documentation": "<p>The job's ID.</p>"}}}, "StartReadSetActivationJobRequest": {"type": "structure", "required": ["sequenceStoreId", "sources"], "members": {"sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p>The read set's sequence store ID.</p>", "location": "uri", "locationName": "sequenceStoreId"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>To ensure that jobs don't run multiple times, specify a unique token for each job.</p>"}, "sources": {"shape": "StartReadSetActivationJobRequestSourcesList", "documentation": "<p>The job's source files.</p>"}}}, "StartReadSetActivationJobRequestSourcesList": {"type": "list", "member": {"shape": "StartReadSetActivationJobSourceItem"}, "max": 20, "min": 1}, "StartReadSetActivationJobResponse": {"type": "structure", "required": ["id", "sequenceStoreId", "status", "creationTime"], "members": {"id": {"shape": "ActivationJobId", "documentation": "<p>The job's ID.</p>"}, "sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p>The read set's sequence store ID.</p>"}, "status": {"shape": "ReadSetActivationJobStatus", "documentation": "<p>The job's status.</p>"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the job was created.</p>"}}}, "StartReadSetActivationJobSourceItem": {"type": "structure", "required": ["readSetId"], "members": {"readSetId": {"shape": "ReadSetId", "documentation": "<p>The source's read set ID.</p>"}}, "documentation": "<p>A source for a read set activation job.</p>"}, "StartReadSetExportJobRequest": {"type": "structure", "required": ["sequenceStoreId", "destination", "roleArn", "sources"], "members": {"sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p>The read set's sequence store ID.</p>", "location": "uri", "locationName": "sequenceStoreId"}, "destination": {"shape": "S3Destination", "documentation": "<p>A location for exported files in Amazon S3.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>A service role for the job.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>To ensure that jobs don't run multiple times, specify a unique token for each job.</p>"}, "sources": {"shape": "StartReadSetExportJobRequestSourcesList", "documentation": "<p>The job's source files.</p>"}}}, "StartReadSetExportJobRequestSourcesList": {"type": "list", "member": {"shape": "ExportReadSet"}, "max": 100, "min": 1}, "StartReadSetExportJobResponse": {"type": "structure", "required": ["id", "sequenceStoreId", "destination", "status", "creationTime"], "members": {"id": {"shape": "ExportJobId", "documentation": "<p>The job's ID.</p>"}, "sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p>The read set's sequence store ID.</p>"}, "destination": {"shape": "S3Destination", "documentation": "<p>The job's output location.</p>"}, "status": {"shape": "ReadSetExportJobStatus", "documentation": "<p>The job's status.</p>"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the job was created.</p>"}}}, "StartReadSetImportJobRequest": {"type": "structure", "required": ["sequenceStoreId", "roleArn", "sources"], "members": {"sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p>The read set's sequence store ID.</p>", "location": "uri", "locationName": "sequenceStoreId"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>A service role for the job.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>To ensure that jobs don't run multiple times, specify a unique token for each job.</p>"}, "sources": {"shape": "StartReadSetImportJobRequestSourcesList", "documentation": "<p>The job's source files.</p>"}}}, "StartReadSetImportJobRequestSourcesList": {"type": "list", "member": {"shape": "StartReadSetImportJobSourceItem"}, "max": 100, "min": 1}, "StartReadSetImportJobResponse": {"type": "structure", "required": ["id", "sequenceStoreId", "roleArn", "status", "creationTime"], "members": {"id": {"shape": "ImportJobId", "documentation": "<p>The job's ID.</p>"}, "sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p>The read set's sequence store ID.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The job's service role ARN.</p>"}, "status": {"shape": "ReadSetImportJobStatus", "documentation": "<p>The job's status.</p>"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the job was created.</p>"}}}, "StartReadSetImportJobSourceItem": {"type": "structure", "required": ["sourceFiles", "sourceFileType", "subjectId", "sampleId"], "members": {"sourceFiles": {"shape": "SourceFiles", "documentation": "<p>The source files' location in Amazon S3.</p>"}, "sourceFileType": {"shape": "FileType", "documentation": "<p>The source's file type.</p>"}, "subjectId": {"shape": "SubjectId", "documentation": "<p>The source's subject ID.</p>"}, "sampleId": {"shape": "SampleId", "documentation": "<p>The source's sample ID.</p>"}, "generatedFrom": {"shape": "GeneratedFrom", "documentation": "<p>Where the source originated.</p>"}, "referenceArn": {"shape": "ReferenceArn", "documentation": "<p>The source's reference ARN.</p>"}, "name": {"shape": "ReadSetName", "documentation": "<p>The source's name.</p>"}, "description": {"shape": "ReadSetDescription", "documentation": "<p>The source's description.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The source's tags.</p>"}}, "documentation": "<p>A source for a read set import job.</p>"}, "StartReferenceImportJobRequest": {"type": "structure", "required": ["referenceStoreId", "roleArn", "sources"], "members": {"referenceStoreId": {"shape": "ReferenceStoreId", "documentation": "<p>The job's reference store ID.</p>", "location": "uri", "locationName": "referenceStoreId"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>A service role for the job.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>To ensure that jobs don't run multiple times, specify a unique token for each job.</p>"}, "sources": {"shape": "StartReferenceImportJobRequestSourcesList", "documentation": "<p>The job's source files.</p>"}}}, "StartReferenceImportJobRequestSourcesList": {"type": "list", "member": {"shape": "StartReferenceImportJobSourceItem"}, "max": 100, "min": 1}, "StartReferenceImportJobResponse": {"type": "structure", "required": ["id", "referenceStoreId", "roleArn", "status", "creationTime"], "members": {"id": {"shape": "ImportJobId", "documentation": "<p>The job's ID.</p>"}, "referenceStoreId": {"shape": "ReferenceStoreId", "documentation": "<p>The job's reference store ID.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The job's service role ARN.</p>"}, "status": {"shape": "ReferenceImportJobStatus", "documentation": "<p>The job's status.</p>"}, "creationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the job was created.</p>"}}}, "StartReferenceImportJobSourceItem": {"type": "structure", "required": ["sourceFile", "name"], "members": {"sourceFile": {"shape": "S3Uri", "documentation": "<p>The source file's location in Amazon S3.</p>"}, "name": {"shape": "ReferenceName", "documentation": "<p>The source's name.</p>"}, "description": {"shape": "ReferenceDescription", "documentation": "<p>The source's description.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The source's tags.</p>"}}, "documentation": "<p>A source for a reference import job.</p>"}, "StartRunRequest": {"type": "structure", "required": ["roleArn", "requestId"], "members": {"workflowId": {"shape": "WorkflowId", "documentation": "<p>The run's workflow ID.</p>"}, "workflowType": {"shape": "WorkflowType", "documentation": "<p>The run's workflow type.</p>"}, "runId": {"shape": "RunId", "documentation": "<p>The ID of a run to duplicate.</p>"}, "roleArn": {"shape": "RunRoleArn", "documentation": "<p>A service role for the run.</p>"}, "name": {"shape": "RunName", "documentation": "<p>A name for the run.</p>"}, "runGroupId": {"shape": "RunGroupId", "documentation": "<p>The run's group ID.</p>"}, "priority": {"shape": "StartRunRequestPriorityInteger", "documentation": "<p>A priority for the run.</p>"}, "parameters": {"shape": "RunParameters", "documentation": "<p>Parameters for the run.</p>"}, "storageCapacity": {"shape": "StartRunRequestStorageCapacityInteger", "documentation": "<p>A storage capacity for the run in gigabytes.</p>"}, "outputUri": {"shape": "RunOutputUri", "documentation": "<p>An output URI for the run.</p>"}, "logLevel": {"shape": "RunLogLevel", "documentation": "<p>A log level for the run.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Tags for the run.</p>"}, "requestId": {"shape": "RunRequestId", "documentation": "<p>To ensure that requests don't run multiple times, specify a unique ID for each request.</p>", "idempotencyToken": true}, "retentionMode": {"shape": "RunRetentionMode", "documentation": "<p>The retention mode for the run.</p>"}}}, "StartRunRequestPriorityInteger": {"type": "integer", "box": true, "max": 100000, "min": 0}, "StartRunRequestStorageCapacityInteger": {"type": "integer", "box": true, "max": 100000, "min": 0}, "StartRunResponse": {"type": "structure", "members": {"arn": {"shape": "RunArn", "documentation": "<p>The run's ARN.</p>"}, "id": {"shape": "RunId", "documentation": "<p>The run's ID.</p>"}, "status": {"shape": "RunStatus", "documentation": "<p>The run's status.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The run's tags.</p>"}, "uuid": {"shape": "RunUuid", "documentation": "<p> The universally unique identifier for a run. </p>"}, "runOutputUri": {"shape": "RunOutputUri", "documentation": "<p> The destination for workflow outputs. </p>"}}}, "StartVariantImportRequest": {"type": "structure", "required": ["destinationName", "roleArn", "items"], "members": {"destinationName": {"shape": "StoreName", "documentation": "<p>The destination variant store for the job.</p>"}, "roleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>A service role for the job.</p>"}, "items": {"shape": "VariantImportItemSources", "documentation": "<p>Items to import.</p>"}, "runLeftNormalization": {"shape": "RunLeftNormalization", "documentation": "<p>The job's left normalization setting.</p>"}, "annotationFields": {"shape": "AnnotationFieldMap", "documentation": "<p> The annotation schema generated by the parsed annotation data. </p>"}}}, "StartVariantImportResponse": {"type": "structure", "required": ["jobId"], "members": {"jobId": {"shape": "ResourceId", "documentation": "<p>The job's ID.</p>"}}}, "StatusList": {"type": "list", "member": {"shape": "ShareStatus"}}, "StatusMessage": {"type": "string", "max": 1000, "min": 0}, "StoreFormat": {"type": "string", "enum": ["GFF", "TSV", "VCF"]}, "StoreName": {"type": "string", "max": 255, "min": 3, "pattern": "([a-z]){1}([a-z0-9_]){2,254}"}, "StoreOptions": {"type": "structure", "members": {"tsvStoreOptions": {"shape": "TsvStoreOptions", "documentation": "<p>File settings for a TSV store.</p>"}}, "documentation": "<p>Settings for a store.</p>", "union": true}, "StoreStatus": {"type": "string", "enum": ["CREATING", "UPDATING", "DELETING", "ACTIVE", "FAILED"]}, "String": {"type": "string"}, "SubjectId": {"type": "string", "max": 127, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "SyntheticTimestamp_date_time": {"type": "timestamp", "timestampFormat": "iso8601"}, "TagArn": {"type": "string", "max": 128, "min": 1, "pattern": "arn:.+"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 0}, "TagMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "TagArn", "documentation": "<p>The resource's ARN.</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagResourceRequestTagsMap", "documentation": "<p>Tags for the resource.</p>"}}}, "TagResourceRequestTagsMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "max": 50, "min": 0}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "TaskFailureReason": {"type": "string", "max": 64, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "TaskId": {"type": "string", "max": 18, "min": 1, "pattern": "[0-9]+"}, "TaskInstanceType": {"type": "string", "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "TaskList": {"type": "list", "member": {"shape": "TaskListItem"}}, "TaskListItem": {"type": "structure", "members": {"taskId": {"shape": "TaskId", "documentation": "<p>The task's ID.</p>"}, "status": {"shape": "TaskStatus", "documentation": "<p>The task's status.</p>"}, "name": {"shape": "TaskName", "documentation": "<p>The task's name.</p>"}, "cpus": {"shape": "TaskListItemCpusInteger", "documentation": "<p>The task's CPU count.</p>"}, "memory": {"shape": "TaskListItemMemoryInteger", "documentation": "<p>The task's memory use in gigabyes.</p>"}, "creationTime": {"shape": "TaskTimestamp", "documentation": "<p>When the task was created.</p>"}, "startTime": {"shape": "TaskTimestamp", "documentation": "<p>When the task started.</p>"}, "stopTime": {"shape": "TaskTimestamp", "documentation": "<p>When the task stopped.</p>"}, "gpus": {"shape": "TaskListItemGpusInteger", "documentation": "<p> The number of Graphics Processing Units (GPU) specified for the task. </p>"}, "instanceType": {"shape": "TaskInstanceType", "documentation": "<p> The instance type for a task. </p>"}}, "documentation": "<p>A workflow run task.</p>"}, "TaskListItemCpusInteger": {"type": "integer", "box": true, "min": 1}, "TaskListItemGpusInteger": {"type": "integer", "box": true, "min": 0}, "TaskListItemMemoryInteger": {"type": "integer", "box": true, "min": 1}, "TaskListToken": {"type": "string", "max": 128, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "TaskLogStream": {"type": "string", "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "TaskName": {"type": "string", "max": 128, "min": 1}, "TaskStatus": {"type": "string", "enum": ["PENDING", "STARTING", "RUNNING", "STOPPING", "COMPLETED", "CANCELLED", "FAILED"], "max": 64, "min": 1}, "TaskStatusMessage": {"type": "string", "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "TaskTimestamp": {"type": "timestamp", "timestampFormat": "iso8601"}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The request was denied due to request throttling.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true, "retryable": {"throttling": true}}, "TsvOptions": {"type": "structure", "members": {"readOptions": {"shape": "ReadOptions", "documentation": "<p>The file's read options.</p>"}}, "documentation": "<p>Formatting options for a TSV file.</p>"}, "TsvStoreOptions": {"type": "structure", "members": {"annotationType": {"shape": "AnnotationType", "documentation": "<p>The store's annotation type.</p>"}, "formatToHeader": {"shape": "FormatToHeader", "documentation": "<p>The store's header key to column name mapping.</p>"}, "schema": {"shape": "TsvStoreOptionsSchemaList", "documentation": "<p>The store's schema.</p>"}}, "documentation": "<p>File settings for a TSV store.</p>"}, "TsvStoreOptionsSchemaList": {"type": "list", "member": {"shape": "SchemaItem"}, "max": 5000, "min": 1}, "TsvVersionOptions": {"type": "structure", "members": {"annotationType": {"shape": "AnnotationType", "documentation": "<p> The store version's annotation type. </p>"}, "formatToHeader": {"shape": "FormatToHeader", "documentation": "<p> The annotation store version's header key to column name mapping. </p>"}, "schema": {"shape": "TsvVersionOptionsSchemaList", "documentation": "<p> The TSV schema for an annotation store version. </p>"}}, "documentation": "<p> The options for a TSV file. </p>"}, "TsvVersionOptionsSchemaList": {"type": "list", "member": {"shape": "SchemaItem"}, "max": 5000, "min": 1}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "TagArn", "documentation": "<p>The resource's ARN.</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>Keys of tags to remove.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateAnnotationStoreRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "String", "documentation": "<p>A name for the store.</p>", "location": "uri", "locationName": "name"}, "description": {"shape": "Description", "documentation": "<p>A description for the store.</p>"}}}, "UpdateAnnotationStoreResponse": {"type": "structure", "required": ["id", "reference", "status", "name", "description", "creationTime", "updateTime"], "members": {"id": {"shape": "ResourceId", "documentation": "<p>The store's ID.</p>"}, "reference": {"shape": "ReferenceItem", "documentation": "<p>The store's genome reference.</p>"}, "status": {"shape": "StoreStatus", "documentation": "<p>The store's status.</p>"}, "name": {"shape": "String", "documentation": "<p>The store's name.</p>"}, "description": {"shape": "Description", "documentation": "<p>The store's description.</p>"}, "creationTime": {"shape": "CreationTime", "documentation": "<p>When the store was created.</p>"}, "updateTime": {"shape": "UpdateTime", "documentation": "<p>When the store was updated.</p>"}, "storeOptions": {"shape": "StoreOptions", "documentation": "<p>Parsing options for the store.</p>"}, "storeFormat": {"shape": "StoreFormat", "documentation": "<p>The annotation file format of the store.</p>"}}}, "UpdateAnnotationStoreVersionRequest": {"type": "structure", "required": ["name", "versionName"], "members": {"name": {"shape": "String", "documentation": "<p> The name of an annotation store. </p>", "location": "uri", "locationName": "name"}, "versionName": {"shape": "String", "documentation": "<p> The name of an annotation store version. </p>", "location": "uri", "locationName": "versionName"}, "description": {"shape": "Description", "documentation": "<p> The description of an annotation store. </p>"}}}, "UpdateAnnotationStoreVersionResponse": {"type": "structure", "required": ["storeId", "id", "status", "name", "versionName", "description", "creationTime", "updateTime"], "members": {"storeId": {"shape": "ResourceId", "documentation": "<p> The annotation store ID. </p>"}, "id": {"shape": "ResourceId", "documentation": "<p> The annotation store version ID. </p>"}, "status": {"shape": "VersionStatus", "documentation": "<p> The status of an annotation store version. </p>"}, "name": {"shape": "StoreName", "documentation": "<p> The name of an annotation store. </p>"}, "versionName": {"shape": "VersionName", "documentation": "<p> The name of an annotation store version. </p>"}, "description": {"shape": "Description", "documentation": "<p> The description of an annotation store version. </p>"}, "creationTime": {"shape": "CreationTime", "documentation": "<p> The time stamp for when an annotation store version was created. </p>"}, "updateTime": {"shape": "UpdateTime", "documentation": "<p> The time stamp for when an annotation store version was updated. </p>"}}}, "UpdateRunGroupRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "RunGroupId", "documentation": "<p>The group's ID.</p>", "location": "uri", "locationName": "id"}, "name": {"shape": "RunGroupName", "documentation": "<p>A name for the group.</p>"}, "maxCpus": {"shape": "UpdateRunGroupRequestMaxCpusInteger", "documentation": "<p>The maximum number of CPUs to use.</p>"}, "maxRuns": {"shape": "UpdateRunGroupRequestMaxRunsInteger", "documentation": "<p>The maximum number of concurrent runs for the group.</p>"}, "maxDuration": {"shape": "UpdateRunGroupRequestMaxDurationInteger", "documentation": "<p>A maximum run time for the group in minutes.</p>"}, "maxGpus": {"shape": "UpdateRunGroupRequestMaxGpusInteger", "documentation": "<p> The maximum GPUs that can be used by a run group. </p>"}}}, "UpdateRunGroupRequestMaxCpusInteger": {"type": "integer", "box": true, "max": 100000, "min": 1}, "UpdateRunGroupRequestMaxDurationInteger": {"type": "integer", "box": true, "max": 100000, "min": 1}, "UpdateRunGroupRequestMaxGpusInteger": {"type": "integer", "box": true, "max": 100000, "min": 1}, "UpdateRunGroupRequestMaxRunsInteger": {"type": "integer", "box": true, "max": 100000, "min": 1}, "UpdateTime": {"type": "timestamp", "timestampFormat": "iso8601"}, "UpdateVariantStoreRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "String", "documentation": "<p>A name for the store.</p>", "location": "uri", "locationName": "name"}, "description": {"shape": "Description", "documentation": "<p>A description for the store.</p>"}}}, "UpdateVariantStoreResponse": {"type": "structure", "required": ["id", "reference", "status", "name", "description", "creationTime", "updateTime"], "members": {"id": {"shape": "ResourceId", "documentation": "<p>The store's ID.</p>"}, "reference": {"shape": "ReferenceItem", "documentation": "<p>The store's genome reference.</p>"}, "status": {"shape": "StoreStatus", "documentation": "<p>The store's status.</p>"}, "name": {"shape": "String", "documentation": "<p>The store's name.</p>"}, "description": {"shape": "Description", "documentation": "<p>The store's description.</p>"}, "creationTime": {"shape": "CreationTime", "documentation": "<p>When the store was created.</p>"}, "updateTime": {"shape": "UpdateTime", "documentation": "<p>When the store was updated.</p>"}}}, "UpdateWorkflowRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "WorkflowId", "documentation": "<p>The workflow's ID.</p>", "location": "uri", "locationName": "id"}, "name": {"shape": "WorkflowName", "documentation": "<p>A name for the workflow.</p>"}, "description": {"shape": "WorkflowDescription", "documentation": "<p>A description for the workflow.</p>"}}}, "UploadId": {"type": "string", "max": 36, "min": 10, "pattern": "[0-9]+"}, "UploadReadSetPartRequest": {"type": "structure", "required": ["sequenceStoreId", "uploadId", "partSource", "partNumber", "payload"], "members": {"sequenceStoreId": {"shape": "SequenceStoreId", "documentation": "<p> The Sequence Store ID used for the multipart upload. </p>", "location": "uri", "locationName": "sequenceStoreId"}, "uploadId": {"shape": "UploadId", "documentation": "<p> The ID for the initiated multipart upload. </p>", "location": "uri", "locationName": "uploadId"}, "partSource": {"shape": "ReadSetPartSource", "documentation": "<p> The source file for an upload part. </p>", "location": "querystring", "locationName": "partSource"}, "partNumber": {"shape": "UploadReadSetPartRequestPartNumberInteger", "documentation": "<p> The number of the part being uploaded. </p>", "location": "querystring", "locationName": "partNumber"}, "payload": {"shape": "ReadSetPartStreamingBlob", "documentation": "<p> The read set data to upload for a part. </p>"}}, "payload": "payload"}, "UploadReadSetPartRequestPartNumberInteger": {"type": "integer", "box": true, "max": 10000, "min": 1}, "UploadReadSetPartResponse": {"type": "structure", "required": ["checksum"], "members": {"checksum": {"shape": "String", "documentation": "<p> An identifier used to confirm that parts are being added to the intended upload. </p>"}}}, "ValidationException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The input fails to satisfy the constraints specified by an AWS service.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "VariantImportItemDetail": {"type": "structure", "required": ["source", "jobStatus"], "members": {"source": {"shape": "S3Uri", "documentation": "<p>The source file's location in Amazon S3.</p>"}, "jobStatus": {"shape": "JobStatus", "documentation": "<p>The item's job status.</p>"}, "statusMessage": {"shape": "JobStatusMsg", "documentation": "<p> A message that provides additional context about a job </p>"}}, "documentation": "<p>Details about an imported variant item.</p>"}, "VariantImportItemDetails": {"type": "list", "member": {"shape": "VariantImportItemDetail"}, "min": 1}, "VariantImportItemSource": {"type": "structure", "required": ["source"], "members": {"source": {"shape": "S3Uri", "documentation": "<p>The source file's location in Amazon S3.</p>"}}, "documentation": "<p>A imported variant item's source.</p>"}, "VariantImportItemSources": {"type": "list", "member": {"shape": "VariantImportItemSource"}, "min": 1}, "VariantImportJobItem": {"type": "structure", "required": ["id", "destinationName", "roleArn", "status", "creationTime", "updateTime"], "members": {"id": {"shape": "String", "documentation": "<p>The job's ID.</p>"}, "destinationName": {"shape": "String", "documentation": "<p>The job's destination variant store.</p>"}, "roleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The job's service role ARN.</p>"}, "status": {"shape": "JobStatus", "documentation": "<p>The job's status.</p>"}, "creationTime": {"shape": "CreationTime", "documentation": "<p>When the job was created.</p>"}, "updateTime": {"shape": "UpdateTime", "documentation": "<p>When the job was updated.</p>"}, "completionTime": {"shape": "CompletionTime", "documentation": "<p>When the job completed.</p>"}, "runLeftNormalization": {"shape": "RunLeftNormalization", "documentation": "<p>The job's left normalization setting.</p>"}, "annotationFields": {"shape": "AnnotationFieldMap", "documentation": "<p> The annotation schema generated by the parsed annotation data. </p>"}}, "documentation": "<p>A variant import job.</p>"}, "VariantImportJobItems": {"type": "list", "member": {"shape": "VariantImportJobItem"}}, "VariantStoreItem": {"type": "structure", "required": ["id", "reference", "status", "storeArn", "name", "description", "sseConfig", "creationTime", "updateTime", "statusMessage", "storeSizeBytes"], "members": {"id": {"shape": "ResourceId", "documentation": "<p>The store's ID.</p>"}, "reference": {"shape": "ReferenceItem", "documentation": "<p>The store's genome reference.</p>"}, "status": {"shape": "StoreStatus", "documentation": "<p>The store's status.</p>"}, "storeArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The store's ARN.</p>"}, "name": {"shape": "String", "documentation": "<p>The store's name.</p>"}, "description": {"shape": "Description", "documentation": "<p>The store's description.</p>"}, "sseConfig": {"shape": "SseConfig", "documentation": "<p>The store's server-side encryption (SSE) settings.</p>"}, "creationTime": {"shape": "CreationTime", "documentation": "<p>When the store was created.</p>"}, "updateTime": {"shape": "UpdateTime", "documentation": "<p>When the store was updated.</p>"}, "statusMessage": {"shape": "StatusMessage", "documentation": "<p>The store's status message.</p>"}, "storeSizeBytes": {"shape": "<PERSON>", "documentation": "<p>The store's size in bytes.</p>"}}, "documentation": "<p>A variant store.</p>"}, "VariantStoreItems": {"type": "list", "member": {"shape": "VariantStoreItem"}}, "VcfOptions": {"type": "structure", "members": {"ignoreQualField": {"shape": "Boolean", "documentation": "<p>The file's ignore qual field setting.</p>"}, "ignoreFilterField": {"shape": "Boolean", "documentation": "<p>The file's ignore filter field setting.</p>"}}, "documentation": "<p>Formatting options for a VCF file.</p>"}, "VersionDeleteError": {"type": "structure", "required": ["versionName", "message"], "members": {"versionName": {"shape": "VersionName", "documentation": "<p> The name given to an annotation store version. </p>"}, "message": {"shape": "String", "documentation": "<p> The message explaining the error in annotation store deletion. </p>"}}, "documentation": "<p> The error preventing deletion of the annotation store version. </p>"}, "VersionDeleteErrorList": {"type": "list", "member": {"shape": "VersionDeleteError"}}, "VersionList": {"type": "list", "member": {"shape": "VersionName"}, "max": 10, "min": 1}, "VersionName": {"type": "string", "max": 255, "min": 3, "pattern": "([a-z]){1}([a-z0-9_]){2,254}"}, "VersionOptions": {"type": "structure", "members": {"tsvVersionOptions": {"shape": "TsvVersionOptions", "documentation": "<p> File settings for a version of a TSV store. </p>"}}, "documentation": "<p> The options for an annotation store version. </p>", "union": true}, "VersionStatus": {"type": "string", "enum": ["CREATING", "UPDATING", "DELETING", "ACTIVE", "FAILED"]}, "WorkflowArn": {"type": "string", "max": 128, "min": 1, "pattern": "arn:.+"}, "WorkflowDefinition": {"type": "string", "max": 256, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "WorkflowDescription": {"type": "string", "max": 256, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "WorkflowDigest": {"type": "string", "max": 64, "min": 1}, "WorkflowEngine": {"type": "string", "enum": ["WDL", "NEXTFLOW", "CWL"], "max": 64, "min": 1}, "WorkflowExport": {"type": "string", "enum": ["DEFINITION"], "max": 64, "min": 1}, "WorkflowExportList": {"type": "list", "member": {"shape": "WorkflowExport"}, "max": 32, "min": 0}, "WorkflowId": {"type": "string", "max": 18, "min": 1, "pattern": "[0-9]+"}, "WorkflowList": {"type": "list", "member": {"shape": "WorkflowListItem"}}, "WorkflowListItem": {"type": "structure", "members": {"arn": {"shape": "WorkflowArn", "documentation": "<p>The workflow's ARN.</p>"}, "id": {"shape": "WorkflowId", "documentation": "<p>The workflow's ID.</p>"}, "name": {"shape": "WorkflowName", "documentation": "<p>The workflow's name.</p>"}, "status": {"shape": "WorkflowStatus", "documentation": "<p>The workflow's status.</p>"}, "type": {"shape": "WorkflowType", "documentation": "<p>The workflow's type.</p>"}, "digest": {"shape": "WorkflowDigest", "documentation": "<p>The workflow's digest.</p>"}, "creationTime": {"shape": "WorkflowTimestamp", "documentation": "<p>When the workflow was created.</p>"}, "metadata": {"shape": "WorkflowMetadata", "documentation": "<p> Any metadata available for workflow. The information listed may vary depending on the workflow, and there may also be no metadata to return. </p>"}}, "documentation": "<p>A workflow.</p>"}, "WorkflowListToken": {"type": "string", "max": 128, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "WorkflowMain": {"type": "string", "max": 128, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "WorkflowMetadata": {"type": "map", "key": {"shape": "WorkflowMetadataKey"}, "value": {"shape": "WorkflowMetadataValue"}}, "WorkflowMetadataKey": {"type": "string", "max": 128, "min": 1}, "WorkflowMetadataValue": {"type": "string", "max": 256, "min": 0}, "WorkflowName": {"type": "string", "max": 128, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "WorkflowParameter": {"type": "structure", "members": {"description": {"shape": "WorkflowParameterDescription", "documentation": "<p>The parameter's description.</p>"}, "optional": {"shape": "Boolean", "documentation": "<p>Whether the parameter is optional.</p>"}}, "documentation": "<p>A workflow parameter.</p>"}, "WorkflowParameterDescription": {"type": "string", "max": 256, "min": 0, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "WorkflowParameterName": {"type": "string", "max": 128, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "WorkflowParameterTemplate": {"type": "map", "key": {"shape": "WorkflowParameterName"}, "value": {"shape": "WorkflowParameter"}, "max": 1000, "min": 1}, "WorkflowRequestId": {"type": "string", "max": 128, "min": 1, "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "WorkflowStatus": {"type": "string", "enum": ["CREATING", "ACTIVE", "UPDATING", "DELETED", "FAILED", "INACTIVE"], "max": 64, "min": 1}, "WorkflowStatusMessage": {"type": "string", "pattern": "[\\p{L}||\\p{M}||\\p{Z}||\\p{S}||\\p{N}||\\p{P}]+"}, "WorkflowTimestamp": {"type": "timestamp", "timestampFormat": "iso8601"}, "WorkflowType": {"type": "string", "enum": ["PRIVATE", "READY2RUN"], "max": 64, "min": 1}}, "documentation": "<p>This is the <i>AWS HealthOmics API Reference</i>. For an introduction to the service, see <a href=\"https://docs.aws.amazon.com/omics/latest/dev/\">What is AWS HealthOmics?</a> in the <i>AWS HealthOmics User Guide</i>.</p>"}