{"version": "2.0", "metadata": {"apiVersion": "2020-03-01", "endpointPrefix": "honeycode", "jsonVersion": "1.1", "protocol": "rest-json", "serviceAbbreviation": "Honeycode", "serviceFullName": "Amazon Honeycode", "serviceId": "Honeycode", "signatureVersion": "v4", "signingName": "honeycode", "uid": "honeycode-2020-03-01"}, "operations": {"BatchCreateTableRows": {"name": "BatchCreateTableRows", "http": {"method": "POST", "requestUri": "/workbooks/{workbookId}/tables/{tableId}/rows/batchcreate"}, "input": {"shape": "BatchCreateTableRowsRequest"}, "output": {"shape": "BatchCreateTableRowsResult"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "RequestTimeoutException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p> The BatchCreateTableRows API allows you to create one or more rows at the end of a table in a workbook. The API allows you to specify the values to set in some or all of the columns in the new rows. </p> <p> If a column is not explicitly set in a specific row, then the column level formula specified in the table will be applied to the new row. If there is no column level formula but the last row of the table has a formula, then that formula will be copied down to the new row. If there is no column level formula and no formula in the last row of the table, then that column will be left blank for the new rows. </p>"}, "BatchDeleteTableRows": {"name": "BatchDeleteTableRows", "http": {"method": "POST", "requestUri": "/workbooks/{workbookId}/tables/{tableId}/rows/batchdelete"}, "input": {"shape": "BatchDeleteTableRowsRequest"}, "output": {"shape": "BatchDeleteTableRowsResult"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "RequestTimeoutException"}, {"shape": "ThrottlingException"}], "documentation": "<p> The BatchDeleteTableRows API allows you to delete one or more rows from a table in a workbook. You need to specify the ids of the rows that you want to delete from the table. </p>"}, "BatchUpdateTableRows": {"name": "BatchUpdateTableRows", "http": {"method": "POST", "requestUri": "/workbooks/{workbookId}/tables/{tableId}/rows/batchupdate"}, "input": {"shape": "BatchUpdateTableRowsRequest"}, "output": {"shape": "BatchUpdateTableRowsResult"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "RequestTimeoutException"}, {"shape": "ThrottlingException"}], "documentation": "<p> The BatchUpdateTableRows API allows you to update one or more rows in a table in a workbook. </p> <p> You can specify the values to set in some or all of the columns in the table for the specified rows. If a column is not explicitly specified in a particular row, then that column will not be updated for that row. To clear out the data in a specific cell, you need to set the value as an empty string (\"\"). </p>"}, "BatchUpsertTableRows": {"name": "BatchUpsertTableRows", "http": {"method": "POST", "requestUri": "/workbooks/{workbookId}/tables/{tableId}/rows/batchupsert"}, "input": {"shape": "BatchUpsertTableRowsRequest"}, "output": {"shape": "BatchUpsertTableRowsResult"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "RequestTimeoutException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p> The BatchUpsertTableRows API allows you to upsert one or more rows in a table. The upsert operation takes a filter expression as input and evaluates it to find matching rows on the destination table. If matching rows are found, it will update the cells in the matching rows to new values specified in the request. If no matching rows are found, a new row is added at the end of the table and the cells in that row are set to the new values specified in the request. </p> <p> You can specify the values to set in some or all of the columns in the table for the matching or newly appended rows. If a column is not explicitly specified for a particular row, then that column will not be updated for that row. To clear out the data in a specific cell, you need to set the value as an empty string (\"\"). </p>"}, "DescribeTableDataImportJob": {"name": "DescribeTableDataImportJob", "http": {"method": "GET", "requestUri": "/workbooks/{workbookId}/tables/{tableId}/import/{jobId}"}, "input": {"shape": "DescribeTableDataImportJobRequest"}, "output": {"shape": "DescribeTableDataImportJobResult"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "RequestTimeoutException"}], "documentation": "<p> The DescribeTableDataImportJob API allows you to retrieve the status and details of a table data import job. </p>"}, "GetScreenData": {"name": "GetScreenData", "http": {"method": "POST", "requestUri": "/screendata"}, "input": {"shape": "GetScreenDataRequest"}, "output": {"shape": "GetScreenDataResult"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "RequestTimeoutException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p> The GetScreenData API allows retrieval of data from a screen in a Honeycode app. The API allows setting local variables in the screen to filter, sort or otherwise affect what will be displayed on the screen. </p>"}, "InvokeScreenAutomation": {"name": "InvokeScreenAutomation", "http": {"method": "POST", "requestUri": "/workbooks/{workbookId}/apps/{appId}/screens/{screenId}/automations/{automationId}"}, "input": {"shape": "InvokeScreenAutomationRequest"}, "output": {"shape": "InvokeScreenAutomationResult"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceUnavailableException"}, {"shape": "AutomationExecutionException"}, {"shape": "AutomationExecutionTimeoutException"}, {"shape": "RequestTimeoutException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p> The InvokeScreenAutomation API allows invoking an action defined in a screen in a Honeycode app. The API allows setting local variables, which can then be used in the automation being invoked. This allows automating the Honeycode app interactions to write, update or delete data in the workbook. </p>"}, "ListTableColumns": {"name": "ListTableColumns", "http": {"method": "GET", "requestUri": "/workbooks/{workbookId}/tables/{tableId}/columns"}, "input": {"shape": "ListTableColumnsRequest"}, "output": {"shape": "ListTableColumnsResult"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "RequestTimeoutException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p> The ListTableColumns API allows you to retrieve a list of all the columns in a table in a workbook. </p>"}, "ListTableRows": {"name": "ListTableRows", "http": {"method": "POST", "requestUri": "/workbooks/{workbookId}/tables/{tableId}/rows/list"}, "input": {"shape": "ListTableRowsRequest"}, "output": {"shape": "ListTableRowsResult"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "RequestTimeoutException"}, {"shape": "ThrottlingException"}], "documentation": "<p> The ListTableRows API allows you to retrieve a list of all the rows in a table in a workbook. </p>"}, "ListTables": {"name": "ListTables", "http": {"method": "GET", "requestUri": "/workbooks/{workbookId}/tables"}, "input": {"shape": "ListTablesRequest"}, "output": {"shape": "ListTablesResult"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "RequestTimeoutException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p> The ListTables API allows you to retrieve a list of all the tables in a workbook. </p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResult"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "RequestTimeoutException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p> The ListTagsForResource API allows you to return a resource's tags. </p>"}, "QueryTableRows": {"name": "QueryTableRows", "http": {"method": "POST", "requestUri": "/workbooks/{workbookId}/tables/{tableId}/rows/query"}, "input": {"shape": "QueryTableRowsRequest"}, "output": {"shape": "QueryTableRowsResult"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "RequestTimeoutException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p> The QueryTableRows API allows you to use a filter formula to query for specific rows in a table. </p>"}, "StartTableDataImportJob": {"name": "StartTableDataImportJob", "http": {"method": "POST", "requestUri": "/workbooks/{workbookId}/tables/{tableId}/import"}, "input": {"shape": "StartTableDataImportJobRequest"}, "output": {"shape": "StartTableDataImportJobResult"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "RequestTimeoutException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p> The StartTableDataImportJob API allows you to start an import job on a table. This API will only return the id of the job that was started. To find out the status of the import request, you need to call the DescribeTableDataImportJob API. </p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResult"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "RequestTimeoutException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p> The TagResource API allows you to add tags to an ARN-able resource. Resource includes workbook, table, screen and screen-automation. </p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResult"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "RequestTimeoutException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p> The UntagResource API allows you to removes tags from an ARN-able resource. Resource includes workbook, table, screen and screen-automation. </p>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p> You do not have sufficient access to perform this action. Check that the workbook is owned by you and your IAM policy allows access to the resource in the request. </p>", "error": {"httpStatusCode": 403}, "exception": true}, "AutomationExecutionException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The automation execution did not end successfully.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "AutomationExecutionTimeoutException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The automation execution timed out.</p>", "error": {"httpStatusCode": 504, "senderFault": true}, "exception": true}, "AwsUserArn": {"type": "string", "max": 2048, "min": 20}, "BatchCreateTableRowsRequest": {"type": "structure", "required": ["workbookId", "tableId", "rowsToCreate"], "members": {"workbookId": {"shape": "ResourceId", "documentation": "<p>The ID of the workbook where the new rows are being added.</p> <p> If a workbook with the specified ID could not be found, this API throws ResourceNotFoundException. </p>", "location": "uri", "locationName": "workbookId"}, "tableId": {"shape": "ResourceId", "documentation": "<p>The ID of the table where the new rows are being added.</p> <p> If a table with the specified ID could not be found, this API throws ResourceNotFoundException. </p>", "location": "uri", "locationName": "tableId"}, "rowsToCreate": {"shape": "CreateRowDataList", "documentation": "<p> The list of rows to create at the end of the table. Each item in this list needs to have a batch item id to uniquely identify the element in the request and the cells to create for that row. You need to specify at least one item in this list. </p> <p> Note that if one of the column ids in any of the rows in the request does not exist in the table, then the request fails and no updates are made to the table. </p>"}, "clientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p> The request token for performing the batch create operation. Request tokens help to identify duplicate requests. If a call times out or fails due to a transient error like a failed network connection, you can retry the call with the same request token. The service ensures that if the first call using that request token is successfully performed, the second call will not perform the operation again. </p> <p> Note that request tokens are valid only for a few minutes. You cannot use request tokens to dedupe requests spanning hours or days. </p>"}}}, "BatchCreateTableRowsResult": {"type": "structure", "required": ["workbookCursor", "createdRows"], "members": {"workbookCursor": {"shape": "WorkbookCursor", "documentation": "<p>The updated workbook cursor after adding the new rows at the end of the table.</p>"}, "createdRows": {"shape": "CreatedRowsMap", "documentation": "<p>The map of batch item id to the row id that was created for that item.</p>"}, "failedBatchItems": {"shape": "FailedBatchItems", "documentation": "<p> The list of batch items in the request that could not be added to the table. Each element in this list contains one item from the request that could not be added to the table along with the reason why that item could not be added. </p>"}}}, "BatchDeleteTableRowsRequest": {"type": "structure", "required": ["workbookId", "tableId", "rowIds"], "members": {"workbookId": {"shape": "ResourceId", "documentation": "<p>The ID of the workbook where the rows are being deleted.</p> <p> If a workbook with the specified id could not be found, this API throws ResourceNotFoundException. </p>", "location": "uri", "locationName": "workbookId"}, "tableId": {"shape": "ResourceId", "documentation": "<p>The ID of the table where the rows are being deleted.</p> <p> If a table with the specified id could not be found, this API throws ResourceNotFoundException. </p>", "location": "uri", "locationName": "tableId"}, "rowIds": {"shape": "RowIdList", "documentation": "<p> The list of row ids to delete from the table. You need to specify at least one row id in this list. </p> <p> Note that if one of the row ids provided in the request does not exist in the table, then the request fails and no rows are deleted from the table. </p>"}, "clientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p> The request token for performing the delete action. Request tokens help to identify duplicate requests. If a call times out or fails due to a transient error like a failed network connection, you can retry the call with the same request token. The service ensures that if the first call using that request token is successfully performed, the second call will not perform the action again. </p> <p> Note that request tokens are valid only for a few minutes. You cannot use request tokens to dedupe requests spanning hours or days. </p>"}}}, "BatchDeleteTableRowsResult": {"type": "structure", "required": ["workbookCursor"], "members": {"workbookCursor": {"shape": "WorkbookCursor", "documentation": "<p>The updated workbook cursor after deleting the rows from the table.</p>"}, "failedBatchItems": {"shape": "FailedBatchItems", "documentation": "<p> The list of row ids in the request that could not be deleted from the table. Each element in this list contains one row id from the request that could not be deleted along with the reason why that item could not be deleted. </p>"}}}, "BatchErrorMessage": {"type": "string", "pattern": "^(?!\\s*$).+"}, "BatchItemId": {"type": "string", "max": 64, "min": 1, "pattern": "^(?!\\s*$).+"}, "BatchUpdateTableRowsRequest": {"type": "structure", "required": ["workbookId", "tableId", "rowsToUpdate"], "members": {"workbookId": {"shape": "ResourceId", "documentation": "<p>The ID of the workbook where the rows are being updated.</p> <p> If a workbook with the specified id could not be found, this API throws ResourceNotFoundException. </p>", "location": "uri", "locationName": "workbookId"}, "tableId": {"shape": "ResourceId", "documentation": "<p>The ID of the table where the rows are being updated.</p> <p> If a table with the specified id could not be found, this API throws ResourceNotFoundException. </p>", "location": "uri", "locationName": "tableId"}, "rowsToUpdate": {"shape": "UpdateRowDataList", "documentation": "<p> The list of rows to update in the table. Each item in this list needs to contain the row id to update along with the map of column id to cell values for each column in that row that needs to be updated. You need to specify at least one row in this list, and for each row, you need to specify at least one column to update. </p> <p> Note that if one of the row or column ids in the request does not exist in the table, then the request fails and no updates are made to the table. </p>"}, "clientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p> The request token for performing the update action. Request tokens help to identify duplicate requests. If a call times out or fails due to a transient error like a failed network connection, you can retry the call with the same request token. The service ensures that if the first call using that request token is successfully performed, the second call will not perform the action again. </p> <p> Note that request tokens are valid only for a few minutes. You cannot use request tokens to dedupe requests spanning hours or days. </p>"}}}, "BatchUpdateTableRowsResult": {"type": "structure", "required": ["workbookCursor"], "members": {"workbookCursor": {"shape": "WorkbookCursor", "documentation": "<p>The updated workbook cursor after adding the new rows at the end of the table.</p>"}, "failedBatchItems": {"shape": "FailedBatchItems", "documentation": "<p> The list of batch items in the request that could not be updated in the table. Each element in this list contains one item from the request that could not be updated in the table along with the reason why that item could not be updated. </p>"}}}, "BatchUpsertTableRowsRequest": {"type": "structure", "required": ["workbookId", "tableId", "rowsToUpsert"], "members": {"workbookId": {"shape": "ResourceId", "documentation": "<p>The ID of the workbook where the rows are being upserted.</p> <p> If a workbook with the specified id could not be found, this API throws ResourceNotFoundException. </p>", "location": "uri", "locationName": "workbookId"}, "tableId": {"shape": "ResourceId", "documentation": "<p>The ID of the table where the rows are being upserted.</p> <p> If a table with the specified id could not be found, this API throws ResourceNotFoundException. </p>", "location": "uri", "locationName": "tableId"}, "rowsToUpsert": {"shape": "UpsertRowDataList", "documentation": "<p> The list of rows to upsert in the table. Each item in this list needs to have a batch item id to uniquely identify the element in the request, a filter expression to find the rows to update for that element and the cell values to set for each column in the upserted rows. You need to specify at least one item in this list. </p> <p> Note that if one of the filter formulas in the request fails to evaluate because of an error or one of the column ids in any of the rows does not exist in the table, then the request fails and no updates are made to the table. </p>"}, "clientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p> The request token for performing the update action. Request tokens help to identify duplicate requests. If a call times out or fails due to a transient error like a failed network connection, you can retry the call with the same request token. The service ensures that if the first call using that request token is successfully performed, the second call will not perform the action again. </p> <p> Note that request tokens are valid only for a few minutes. You cannot use request tokens to dedupe requests spanning hours or days. </p>"}}}, "BatchUpsertTableRowsResult": {"type": "structure", "required": ["rows", "workbookCursor"], "members": {"rows": {"shape": "UpsertRowsResultMap", "documentation": "<p> A map with the batch item id as the key and the result of the upsert operation as the value. The result of the upsert operation specifies whether existing rows were updated or a new row was appended, along with the list of row ids that were affected. </p>"}, "workbookCursor": {"shape": "WorkbookCursor", "documentation": "<p>The updated workbook cursor after updating or appending rows in the table.</p>"}, "failedBatchItems": {"shape": "FailedBatchItems", "documentation": "<p> The list of batch items in the request that could not be updated or appended in the table. Each element in this list contains one item from the request that could not be updated in the table along with the reason why that item could not be updated or appended. </p>"}}}, "Cell": {"type": "structure", "members": {"formula": {"shape": "Formula", "documentation": "<p> The formula contained in the cell. This field is empty if a cell does not have a formula. </p>"}, "format": {"shape": "Format", "documentation": "<p>The format of the cell. If this field is empty, then the format is either not specified in the workbook or the format is set to AUTO.</p>"}, "rawValue": {"shape": "RawValue", "documentation": "<p> The raw value of the data contained in the cell. The raw value depends on the format of the data in the cell. However the attribute in the API return value is always a string containing the raw value. </p> <p> Cells with format DATE, DATE_TIME or TIME have the raw value as a floating point number where the whole number represents the number of days since 1/1/1900 and the fractional part represents the fraction of the day since midnight. For example, a cell with date 11/3/2020 has the raw value \"44138\". A cell with the time 9:00 AM has the raw value \"0.375\" and a cell with date/time value of 11/3/2020 9:00 AM has the raw value \"44138.375\". Notice that even though the raw value is a number in all three cases, it is still represented as a string. </p> <p> Cells with format NUMBER, CURRENCY, PERCENTAGE and ACCOUNTING have the raw value of the data as the number representing the data being displayed. For example, the number 1.325 with two decimal places in the format will have it's raw value as \"1.325\" and formatted value as \"1.33\". A currency value for $10 will have the raw value as \"10\" and formatted value as \"$10.00\". A value representing 20% with two decimal places in the format will have its raw value as \"0.2\" and the formatted value as \"20.00%\". An accounting value of -$25 will have \"-25\" as the raw value and \"$ (25.00)\" as the formatted value. </p> <p> Cells with format TEXT will have the raw text as the raw value. For example, a cell with text \"<PERSON> <PERSON>\" will have \"<PERSON> <PERSON>\" as both the raw value and the formatted value. </p> <p> Cells with format CONTACT will have the name of the contact as a formatted value and the email address of the contact as the raw value. For example, a contact for John <PERSON> will have \"<PERSON>\" as the formatted value and \"<EMAIL>\" as the raw value. </p> <p> Cells with format ROWLINK (aka picklist) will have the first column of the linked row as the formatted value and the row id of the linked row as the raw value. For example, a cell containing a picklist to a table that displays task status might have \"Completed\" as the formatted value and \"row:dfcefaee-5b37-4355-8f28-40c3e4ff5dd4/ca432b2f-b8eb-431d-9fb5-cbe0342f9f03\" as the raw value. </p> <p> Cells with format ROWSET (aka multi-select or multi-record picklist) will by default have the first column of each of the linked rows as the formatted value in the list, and the rowset id of the linked rows as the raw value. For example, a cell containing a multi-select picklist to a table that contains items might have \"Item A\", \"Item B\" in the formatted value list and \"rows:b742c1f4-6cb0-4650-a845-35eb86fcc2bb/ [fdea123b-8f68-474a-aa8a-5ff87aa333af,6daf41f0-a138-4eee-89da-123086d36ecf]\" as the raw value. </p> <p> Cells with format ATTACHMENT will have the name of the attachment as the formatted value and the attachment id as the raw value. For example, a cell containing an attachment named \"image.jpeg\" will have \"image.jpeg\" as the formatted value and \"attachment:ca432b2f-b8eb-431d-9fb5-cbe0342f9f03\" as the raw value. </p> <p> Cells with format AUTO or cells without any format that are auto-detected as one of the formats above will contain the raw and formatted values as mentioned above, based on the auto-detected formats. If there is no auto-detected format, the raw and formatted values will be the same as the data in the cell. </p>"}, "formattedValue": {"shape": "FormattedValue", "documentation": "<p> The formatted value of the cell. This is the value that you see displayed in the cell in the UI. </p> <p> Note that the formatted value of a cell is always represented as a string irrespective of the data that is stored in the cell. For example, if a cell contains a date, the formatted value of the cell is the string representation of the formatted date being shown in the cell in the UI. See details in the rawValue field below for how cells of different formats will have different raw and formatted values. </p>"}, "formattedValues": {"shape": "FormattedValuesList", "documentation": "<p> A list of formatted values of the cell. This field is only returned when the cell is ROWSET format (aka multi-select or multi-record picklist). Values in the list are always represented as strings. The formattedValue field will be empty if this field is returned. </p>"}}, "documentation": "<p>An object that represents a single cell in a table.</p>", "sensitive": true}, "CellInput": {"type": "structure", "members": {"fact": {"shape": "Fact", "documentation": "<p> Fact represents the data that is entered into a cell. This data can be free text or a formula. Formulas need to start with the equals (=) sign. </p>"}, "facts": {"shape": "FactList", "documentation": "<p> A list representing the values that are entered into a ROWSET cell. Facts list can have either only values or rowIDs, and rowIDs should from the same table. </p>"}}, "documentation": "<p> CellInput object contains the data needed to create or update cells in a table. </p> <note> <p> CellInput object has only a facts field or a fact field, but not both. A 400 bad request will be thrown if both fact and facts field are present. </p> </note>"}, "Cells": {"type": "list", "member": {"shape": "Cell"}}, "ClientRequestToken": {"type": "string", "max": 64, "min": 32, "pattern": "^(?!\\s*$).+"}, "ColumnMetadata": {"type": "structure", "required": ["name", "format"], "members": {"name": {"shape": "Name", "documentation": "<p>The name of the column.</p>"}, "format": {"shape": "Format", "documentation": "<p>The format of the column.</p>"}}, "documentation": "<p><PERSON><PERSON><PERSON> for column in the table.</p>"}, "CreateRowData": {"type": "structure", "required": ["batchItemId", "cellsToCreate"], "members": {"batchItemId": {"shape": "BatchItemId", "documentation": "<p> An external identifier that represents the single row that is being created as part of the BatchCreateTableRows request. This can be any string that you can use to identify the row in the request. The BatchCreateTableRows API puts the batch item id in the results to allow you to link data in the request to data in the results. </p>"}, "cellsToCreate": {"shape": "RowDataInput", "documentation": "<p> A map representing the cells to create in the new row. The key is the column id of the cell and the value is the CellInput object that represents the data to set in that cell. </p>"}}, "documentation": "<p> Data needed to create a single row in a table as part of the BatchCreateTableRows request. </p>"}, "CreateRowDataList": {"type": "list", "member": {"shape": "CreateRowData"}, "max": 100, "min": 1}, "CreatedRowsMap": {"type": "map", "key": {"shape": "BatchItemId"}, "value": {"shape": "RowId"}}, "DataItem": {"type": "structure", "members": {"overrideFormat": {"shape": "Format", "documentation": "<p> The overrideFormat is optional and is specified only if a particular row of data has a different format for the data than the default format defined on the screen or the table. </p>"}, "rawValue": {"shape": "RawValue", "documentation": "<p>The raw value of the data. e.g. <EMAIL></p>"}, "formattedValue": {"shape": "FormattedValue", "documentation": "<p>The formatted value of the data. e.g. <PERSON>.</p>"}}, "documentation": "<p>The data in a particular data cell defined on the screen.</p>", "sensitive": true}, "DataItems": {"type": "list", "member": {"shape": "DataItem"}}, "DelimitedTextDelimiter": {"type": "string", "max": 1, "min": 1, "pattern": "^[^\\n\\r\\x00\\x08\\x0B\\x0C\\x0E\\x1F]?$"}, "DelimitedTextImportOptions": {"type": "structure", "required": ["delimiter"], "members": {"delimiter": {"shape": "DelimitedTextDelimiter", "documentation": "<p>The delimiter to use for separating columns in a single row of the input.</p>"}, "hasHeaderRow": {"shape": "HasHeaderRow", "documentation": "<p>Indicates whether the input file has a header row at the top containing the column names.</p>"}, "ignoreEmptyRows": {"shape": "IgnoreEmptyRows", "documentation": "<p>A parameter to indicate whether empty rows should be ignored or be included in the import.</p>"}, "dataCharacterEncoding": {"shape": "ImportDataCharacterEncoding", "documentation": "<p>The encoding of the data in the input file.</p>"}}, "documentation": "<p> An object that contains the options relating to parsing delimited text as part of an import request. </p>"}, "DescribeTableDataImportJobRequest": {"type": "structure", "required": ["workbookId", "tableId", "jobId"], "members": {"workbookId": {"shape": "ResourceId", "documentation": "<p>The ID of the workbook into which data was imported.</p> <p> If a workbook with the specified id could not be found, this API throws ResourceNotFoundException. </p>", "location": "uri", "locationName": "workbookId"}, "tableId": {"shape": "ResourceId", "documentation": "<p>The ID of the table into which data was imported.</p> <p> If a table with the specified id could not be found, this API throws ResourceNotFoundException. </p>", "location": "uri", "locationName": "tableId"}, "jobId": {"shape": "JobId", "documentation": "<p>The ID of the job that was returned by the StartTableDataImportJob request.</p> <p> If a job with the specified id could not be found, this API throws ResourceNotFoundException. </p>", "location": "uri", "locationName": "jobId"}}}, "DescribeTableDataImportJobResult": {"type": "structure", "required": ["jobStatus", "message", "jobMetadata"], "members": {"jobStatus": {"shape": "TableDataImportJobStatus", "documentation": "<p> The current status of the import job. </p>"}, "message": {"shape": "TableDataImportJobMessage", "documentation": "<p> A message providing more details about the current status of the import job. </p>"}, "jobMetadata": {"shape": "TableDataImportJobMetadata", "documentation": "<p> The metadata about the job that was submitted for import. </p>"}, "errorCode": {"shape": "ErrorCode", "documentation": "<p> If job status is failed, error code to understand reason for the failure. </p>"}}}, "DestinationOptions": {"type": "structure", "members": {"columnMap": {"shape": "ImportColumnMap", "documentation": "<p>A map of the column id to the import properties for each column.</p>"}}, "documentation": "<p>An object that contains the options relating to the destination of the import request.</p>"}, "Email": {"type": "string", "max": 254, "min": 3, "pattern": "^([a-zA-Z0-9_\\-\\.]+)@([a-zA-Z0-9_\\-\\.]+)\\.([a-zA-Z]{2,5})$", "sensitive": true}, "ErrorCode": {"type": "string", "enum": ["ACCESS_DENIED", "INVALID_URL_ERROR", "INVALID_IMPORT_OPTIONS_ERROR", "INVALID_TABLE_ID_ERROR", "INVALID_TABLE_COLUMN_ID_ERROR", "TABLE_NOT_FOUND_ERROR", "FILE_EMPTY_ERROR", "INVALID_FILE_TYPE_ERROR", "FILE_PARSING_ERROR", "FILE_SIZE_LIMIT_ERROR", "FILE_NOT_FOUND_ERROR", "UNKNOWN_ERROR", "RESOURCE_NOT_FOUND_ERROR", "SYSTEM_LIMIT_ERROR"]}, "ErrorMessage": {"type": "string"}, "Fact": {"type": "string", "max": 8192, "min": 0, "pattern": "[\\s\\S]*", "sensitive": true}, "FactList": {"type": "list", "member": {"shape": "Fact"}, "max": 220, "min": 0}, "FailedBatchItem": {"type": "structure", "required": ["id", "errorMessage"], "members": {"id": {"shape": "BatchItemId", "documentation": "<p> The id of the batch item that failed. This is the batch item id for the BatchCreateTableRows and BatchUpsertTableRows operations and the row id for the BatchUpdateTableRows and BatchDeleteTableRows operations. </p>"}, "errorMessage": {"shape": "BatchErrorMessage", "documentation": "<p> The error message that indicates why the batch item failed. </p>"}}, "documentation": "<p> A single item in a batch that failed to perform the intended action because of an error preventing it from succeeding. </p>"}, "FailedBatchItems": {"type": "list", "member": {"shape": "FailedBatchItem"}, "max": 100, "min": 0}, "Filter": {"type": "structure", "required": ["formula"], "members": {"formula": {"shape": "Formula", "documentation": "<p> A formula representing a filter function that returns zero or more matching rows from a table. Valid formulas in this field return a list of rows from a table. The most common ways of writing a formula to return a list of rows are to use the FindRow() or Filter() functions. Any other formula that returns zero or more rows is also acceptable. For example, you can use a formula that points to a cell that contains a filter function. </p>"}, "contextRowId": {"shape": "RowId", "documentation": "<p> The optional contextRowId attribute can be used to specify the row id of the context row if the filter formula contains unqualified references to table columns and needs a context row to evaluate them successfully. </p>"}}, "documentation": "<p> An object that represents a filter formula along with the id of the context row under which the filter function needs to evaluate. </p>"}, "Format": {"type": "string", "enum": ["AUTO", "NUMBER", "CURRENCY", "DATE", "TIME", "DATE_TIME", "PERCENTAGE", "TEXT", "ACCOUNTING", "CONTACT", "ROWLINK", "ROWSET"]}, "FormattedValue": {"type": "string", "max": 8192, "min": 0, "pattern": "[\\s\\S]*"}, "FormattedValuesList": {"type": "list", "member": {"shape": "FormattedValue"}, "max": 220, "min": 0}, "Formula": {"type": "string", "max": 8192, "min": 0, "pattern": "^=.*", "sensitive": true}, "GetScreenDataRequest": {"type": "structure", "required": ["workbookId", "appId", "screenId"], "members": {"workbookId": {"shape": "ResourceId", "documentation": "<p>The ID of the workbook that contains the screen.</p>"}, "appId": {"shape": "ResourceId", "documentation": "<p>The ID of the app that contains the screen.</p>"}, "screenId": {"shape": "ResourceId", "documentation": "<p>The ID of the screen.</p>"}, "variables": {"shape": "VariableValueMap", "documentation": "<p> Variables are optional and are needed only if the screen requires them to render correctly. Variables are specified as a map where the key is the name of the variable as defined on the screen. The value is an object which currently has only one property, rawValue, which holds the value of the variable to be passed to the screen. </p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p> The number of results to be returned on a single page. Specify a number between 1 and 100. The maximum value is 100. </p> <p> This parameter is optional. If you don't specify this parameter, the default page size is 100. </p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> This parameter is optional. If a nextToken is not specified, the API returns the first page of data. </p> <p> Pagination tokens expire after 1 hour. If you use a token that was returned more than an hour back, the API will throw ValidationException. </p>"}}}, "GetScreenDataResult": {"type": "structure", "required": ["results", "workbookCursor"], "members": {"results": {"shape": "ResultSetMap", "documentation": "<p>A map of all the rows on the screen keyed by block name.</p>"}, "workbookCursor": {"shape": "WorkbookCursor", "documentation": "<p> Indicates the cursor of the workbook at which the data returned by this workbook is read. Workbook cursor keeps increasing with every update and the increments are not sequential. </p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> Provides the pagination token to load the next page if there are more results matching the request. If a pagination token is not present in the response, it means that all data matching the query has been loaded. </p>"}}}, "HasHeaderRow": {"type": "boolean"}, "IgnoreEmptyRows": {"type": "boolean"}, "ImportColumnMap": {"type": "map", "key": {"shape": "ResourceId"}, "value": {"shape": "SourceDataColumnProperties"}, "max": 100}, "ImportDataCharacterEncoding": {"type": "string", "enum": ["UTF-8", "US-ASCII", "ISO-8859-1", "UTF-16BE", "UTF-16LE", "UTF-16"]}, "ImportDataSource": {"type": "structure", "required": ["dataSourceConfig"], "members": {"dataSourceConfig": {"shape": "ImportDataSourceConfig", "documentation": "<p>The configuration parameters for the data source of the import</p>"}}, "documentation": "<p>An object that has details about the source of the data that was submitted for import.</p>"}, "ImportDataSourceConfig": {"type": "structure", "members": {"dataSourceUrl": {"shape": "SecureURL", "documentation": "<p> The URL from which source data will be downloaded for the import request. </p>"}}, "documentation": "<p> An object that contains the configuration parameters for the data source of an import request. </p>"}, "ImportJobSubmitter": {"type": "structure", "members": {"email": {"shape": "Email", "documentation": "<p>The email id of the submitter of the import job, if available.</p>"}, "userArn": {"shape": "AwsUserArn", "documentation": "<p>The AWS user ARN of the submitter of the import job, if available.</p>"}}, "documentation": "<p>An object that contains the attributes of the submitter of the import job.</p>"}, "ImportOptions": {"type": "structure", "members": {"destinationOptions": {"shape": "DestinationOptions", "documentation": "<p>Options relating to the destination of the import request.</p>"}, "delimitedTextOptions": {"shape": "DelimitedTextImportOptions", "documentation": "<p>Options relating to parsing delimited text. Required if dataFormat is DELIMITED_TEXT.</p>"}}, "documentation": "<p>An object that contains the options specified by the sumitter of the import request.</p>"}, "ImportSourceDataFormat": {"type": "string", "enum": ["DELIMITED_TEXT"]}, "InternalServerException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>There were unexpected errors from the server.</p>", "error": {"httpStatusCode": 500}, "exception": true}, "InvokeScreenAutomationRequest": {"type": "structure", "required": ["workbookId", "appId", "screenId", "screenAutomationId"], "members": {"workbookId": {"shape": "ResourceId", "documentation": "<p>The ID of the workbook that contains the screen automation.</p>", "location": "uri", "locationName": "workbookId"}, "appId": {"shape": "ResourceId", "documentation": "<p>The ID of the app that contains the screen automation.</p>", "location": "uri", "locationName": "appId"}, "screenId": {"shape": "ResourceId", "documentation": "<p>The ID of the screen that contains the screen automation.</p>", "location": "uri", "locationName": "screenId"}, "screenAutomationId": {"shape": "ResourceId", "documentation": "<p>The ID of the automation action to be performed.</p>", "location": "uri", "locationName": "automationId"}, "variables": {"shape": "VariableValueMap", "documentation": "<p> Variables are specified as a map where the key is the name of the variable as defined on the screen. The value is an object which currently has only one property, rawValue, which holds the value of the variable to be passed to the screen. Any variables defined in a screen are required to be passed in the call. </p>"}, "rowId": {"shape": "RowId", "documentation": "<p> The row ID for the automation if the automation is defined inside a block with source or list. </p>"}, "clientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p> The request token for performing the automation action. Request tokens help to identify duplicate requests. If a call times out or fails due to a transient error like a failed network connection, you can retry the call with the same request token. The service ensures that if the first call using that request token is successfully performed, the second call will return the response of the previous call rather than performing the action again. </p> <p> Note that request tokens are valid only for a few minutes. You cannot use request tokens to dedupe requests spanning hours or days. </p>"}}}, "InvokeScreenAutomationResult": {"type": "structure", "required": ["workbookCursor"], "members": {"workbookCursor": {"shape": "WorkbookCursor", "documentation": "<p>The updated workbook cursor after performing the automation action.</p>"}}}, "JobId": {"type": "string", "max": 100, "min": 1, "pattern": "^[^\\n\\r\\x00\\x08\\x0B\\x0C\\x0E\\x1F]*$"}, "ListTableColumnsRequest": {"type": "structure", "required": ["workbookId", "tableId"], "members": {"workbookId": {"shape": "ResourceId", "documentation": "<p>The ID of the workbook that contains the table whose columns are being retrieved.</p> <p> If a workbook with the specified id could not be found, this API throws ResourceNotFoundException. </p>", "location": "uri", "locationName": "workbookId"}, "tableId": {"shape": "ResourceId", "documentation": "<p>The ID of the table whose columns are being retrieved.</p> <p> If a table with the specified id could not be found, this API throws ResourceNotFoundException. </p>", "location": "uri", "locationName": "tableId"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> This parameter is optional. If a nextToken is not specified, the API returns the first page of data. </p> <p> Pagination tokens expire after 1 hour. If you use a token that was returned more than an hour back, the API will throw ValidationException. </p>", "location": "querystring", "locationName": "nextToken"}}}, "ListTableColumnsResult": {"type": "structure", "required": ["tableColumns"], "members": {"tableColumns": {"shape": "TableColumns", "documentation": "<p> The list of columns in the table. </p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> Provides the pagination token to load the next page if there are more results matching the request. If a pagination token is not present in the response, it means that all data matching the request has been loaded. </p>"}, "workbookCursor": {"shape": "WorkbookCursor", "documentation": "<p> Indicates the cursor of the workbook at which the data returned by this request is read. Workbook cursor keeps increasing with every update and the increments are not sequential. </p>"}}}, "ListTableRowsRequest": {"type": "structure", "required": ["workbookId", "tableId"], "members": {"workbookId": {"shape": "ResourceId", "documentation": "<p>The ID of the workbook that contains the table whose rows are being retrieved.</p> <p> If a workbook with the specified id could not be found, this API throws ResourceNotFoundException. </p>", "location": "uri", "locationName": "workbookId"}, "tableId": {"shape": "ResourceId", "documentation": "<p>The ID of the table whose rows are being retrieved.</p> <p> If a table with the specified id could not be found, this API throws ResourceNotFoundException. </p>", "location": "uri", "locationName": "tableId"}, "rowIds": {"shape": "RowIdList", "documentation": "<p> This parameter is optional. If one or more row ids are specified in this list, then only the specified row ids are returned in the result. If no row ids are specified here, then all the rows in the table are returned. </p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of rows to return in each page of the results.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> This parameter is optional. If a nextToken is not specified, the API returns the first page of data. </p> <p> Pagination tokens expire after 1 hour. If you use a token that was returned more than an hour back, the API will throw ValidationException. </p>"}}}, "ListTableRowsResult": {"type": "structure", "required": ["columnIds", "rows", "workbookCursor"], "members": {"columnIds": {"shape": "ResourceIds", "documentation": "<p> The list of columns in the table whose row data is returned in the result. </p>"}, "rows": {"shape": "TableRows", "documentation": "<p> The list of rows in the table. Note that this result is paginated, so this list contains a maximum of 100 rows. </p>"}, "rowIdsNotFound": {"shape": "RowIdList", "documentation": "<p> The list of row ids included in the request that were not found in the table. </p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> Provides the pagination token to load the next page if there are more results matching the request. If a pagination token is not present in the response, it means that all data matching the request has been loaded. </p>"}, "workbookCursor": {"shape": "WorkbookCursor", "documentation": "<p> Indicates the cursor of the workbook at which the data returned by this request is read. Workbook cursor keeps increasing with every update and the increments are not sequential. </p>"}}}, "ListTablesRequest": {"type": "structure", "required": ["workbookId"], "members": {"workbookId": {"shape": "ResourceId", "documentation": "<p>The ID of the workbook whose tables are being retrieved.</p> <p> If a workbook with the specified id could not be found, this API throws ResourceNotFoundException. </p>", "location": "uri", "locationName": "workbookId"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of tables to return in each page of the results.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> This parameter is optional. If a nextToken is not specified, the API returns the first page of data. </p> <p> Pagination tokens expire after 1 hour. If you use a token that was returned more than an hour back, the API will throw ValidationException. </p>", "location": "querystring", "locationName": "nextToken"}}}, "ListTablesResult": {"type": "structure", "required": ["tables"], "members": {"tables": {"shape": "Tables", "documentation": "<p> The list of tables in the workbook. </p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> Provides the pagination token to load the next page if there are more results matching the request. If a pagination token is not present in the response, it means that all data matching the request has been loaded. </p>"}, "workbookCursor": {"shape": "WorkbookCursor", "documentation": "<p> Indicates the cursor of the workbook at which the data returned by this request is read. Workbook cursor keeps increasing with every update and the increments are not sequential. </p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "ResourceArn", "documentation": "<p>The resource's Amazon Resource Name (ARN).</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResult": {"type": "structure", "members": {"tags": {"shape": "TagsMap", "documentation": "<p>The resource's tags.</p>"}}}, "MaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "Name": {"type": "string", "sensitive": true}, "PaginationToken": {"type": "string", "max": 1024, "min": 1, "pattern": "^(?!\\s*$).+"}, "QueryTableRowsRequest": {"type": "structure", "required": ["workbookId", "tableId", "filterFormula"], "members": {"workbookId": {"shape": "ResourceId", "documentation": "<p>The ID of the workbook whose table rows are being queried.</p> <p> If a workbook with the specified id could not be found, this API throws ResourceNotFoundException. </p>", "location": "uri", "locationName": "workbookId"}, "tableId": {"shape": "ResourceId", "documentation": "<p>The ID of the table whose rows are being queried.</p> <p> If a table with the specified id could not be found, this API throws ResourceNotFoundException. </p>", "location": "uri", "locationName": "tableId"}, "filterFormula": {"shape": "Filter", "documentation": "<p>An object that represents a filter formula along with the id of the context row under which the filter function needs to evaluate.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of rows to return in each page of the results.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> This parameter is optional. If a nextToken is not specified, the API returns the first page of data. </p> <p> Pagination tokens expire after 1 hour. If you use a token that was returned more than an hour back, the API will throw ValidationException. </p>"}}}, "QueryTableRowsResult": {"type": "structure", "required": ["columnIds", "rows", "workbookCursor"], "members": {"columnIds": {"shape": "ResourceIds", "documentation": "<p> The list of columns in the table whose row data is returned in the result. </p>"}, "rows": {"shape": "TableRows", "documentation": "<p> The list of rows in the table that match the query filter. </p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> Provides the pagination token to load the next page if there are more results matching the request. If a pagination token is not present in the response, it means that all data matching the request has been loaded. </p>"}, "workbookCursor": {"shape": "WorkbookCursor", "documentation": "<p> Indicates the cursor of the workbook at which the data returned by this request is read. Workbook cursor keeps increasing with every update and the increments are not sequential. </p>"}}}, "RawValue": {"type": "string", "max": 32767, "min": 0, "pattern": "[\\s\\S]*"}, "RequestTimeoutException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The request timed out.</p>", "error": {"httpStatusCode": 504, "senderFault": true}, "exception": true}, "ResourceArn": {"type": "string", "max": 256, "min": 1, "pattern": "^arn:aws:honeycode:.+:[0-9]{12}:.+:.+$"}, "ResourceId": {"type": "string", "max": 36, "min": 36, "pattern": "[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}"}, "ResourceIds": {"type": "list", "member": {"shape": "ResourceId"}, "max": 100, "min": 1}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>A Workbook, Table, App, Screen or Screen Automation was not found with the given ID.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "ResultHeader": {"type": "list", "member": {"shape": "ColumnMetadata"}}, "ResultRow": {"type": "structure", "required": ["dataItems"], "members": {"rowId": {"shape": "RowId", "documentation": "<p>The ID for a particular row.</p>"}, "dataItems": {"shape": "DataItems", "documentation": "<p>List of all the data cells in a row.</p>"}}, "documentation": "<p>A single row in the ResultSet.</p>"}, "ResultRows": {"type": "list", "member": {"shape": "ResultRow"}}, "ResultSet": {"type": "structure", "required": ["headers", "rows"], "members": {"headers": {"shape": "ResultHeader", "documentation": "<p> List of headers for all the data cells in the block. The header identifies the name and default format of the data cell. Data cells appear in the same order in all rows as defined in the header. The names and formats are not repeated in the rows. If a particular row does not have a value for a data cell, a blank value is used. </p> <p> For example, a task list that displays the task name, due date and assigned person might have headers [ { \"name\": \"Task Name\"}, {\"name\": \"Due Date\", \"format\": \"DATE\"}, {\"name\": \"Assigned\", \"format\": \"CONTACT\"} ]. Every row in the result will have the task name as the first item, due date as the second item and assigned person as the third item. If a particular task does not have a due date, that row will still have a blank value in the second element and the assigned person will still be in the third element. </p>"}, "rows": {"shape": "ResultRows", "documentation": "<p> List of rows returned by the request. Each row has a row Id and a list of data cells in that row. The data cells will be present in the same order as they are defined in the header. </p>"}}, "documentation": "<p> ResultSet contains the results of the request for a single block or list defined on the screen. </p>"}, "ResultSetMap": {"type": "map", "key": {"shape": "Name"}, "value": {"shape": "ResultSet"}}, "RowDataInput": {"type": "map", "key": {"shape": "ResourceId"}, "value": {"shape": "CellInput"}, "max": 100, "min": 1}, "RowId": {"type": "string", "max": 77, "min": 77, "pattern": "row:[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\\/[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}"}, "RowIdList": {"type": "list", "member": {"shape": "RowId"}, "max": 100, "min": 1}, "SecureURL": {"type": "string", "max": 8000, "min": 1, "pattern": "^https:\\/\\/[^\\n\\r\\x00\\x08\\x0B\\x0C\\x0E\\x1F]*$", "sensitive": true}, "ServiceQuotaExceededException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p> The request caused service quota to be breached. </p>", "error": {"httpStatusCode": 402}, "exception": true}, "ServiceUnavailableException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>Remote service is unreachable.</p>", "error": {"httpStatusCode": 503}, "exception": true}, "SourceDataColumnIndex": {"type": "integer", "min": 1}, "SourceDataColumnProperties": {"type": "structure", "members": {"columnIndex": {"shape": "SourceDataColumnIndex", "documentation": "<p>The index of the column in the input file.</p>"}}, "documentation": "<p>An object that contains the properties for importing data to a specific column in a table.</p>"}, "StartTableDataImportJobRequest": {"type": "structure", "required": ["workbookId", "dataSource", "dataFormat", "destinationTableId", "importOptions", "clientRequestToken"], "members": {"workbookId": {"shape": "ResourceId", "documentation": "<p>The ID of the workbook where the rows are being imported.</p> <p> If a workbook with the specified id could not be found, this API throws ResourceNotFoundException. </p>", "location": "uri", "locationName": "workbookId"}, "dataSource": {"shape": "ImportDataSource", "documentation": "<p> The source of the data that is being imported. The size of source must be no larger than 100 MB. Source must have no more than 100,000 cells and no more than 1,000 rows. </p>"}, "dataFormat": {"shape": "ImportSourceDataFormat", "documentation": "<p> The format of the data that is being imported. Currently the only option supported is \"DELIMITED_TEXT\". </p>"}, "destinationTableId": {"shape": "ResourceId", "documentation": "<p>The ID of the table where the rows are being imported.</p> <p> If a table with the specified id could not be found, this API throws ResourceNotFoundException. </p>", "location": "uri", "locationName": "tableId"}, "importOptions": {"shape": "ImportOptions", "documentation": "<p> The options for customizing this import request. </p>"}, "clientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p> The request token for performing the update action. Request tokens help to identify duplicate requests. If a call times out or fails due to a transient error like a failed network connection, you can retry the call with the same request token. The service ensures that if the first call using that request token is successfully performed, the second call will not perform the action again. </p> <p> Note that request tokens are valid only for a few minutes. You cannot use request tokens to dedupe requests spanning hours or days. </p>"}}}, "StartTableDataImportJobResult": {"type": "structure", "required": ["jobId", "jobStatus"], "members": {"jobId": {"shape": "JobId", "documentation": "<p> The id that is assigned to this import job. Future requests to find out the status of this import job need to send this id in the appropriate parameter in the request. </p>"}, "jobStatus": {"shape": "TableDataImportJobStatus", "documentation": "<p> The status of the import job immediately after submitting the request. </p>"}}}, "Table": {"type": "structure", "members": {"tableId": {"shape": "ResourceId", "documentation": "<p>The id of the table.</p>"}, "tableName": {"shape": "TableName", "documentation": "<p>The name of the table.</p>"}}, "documentation": "<p>An object representing the properties of a table in a workbook.</p>"}, "TableColumn": {"type": "structure", "members": {"tableColumnId": {"shape": "ResourceId", "documentation": "<p>The id of the column in the table.</p>"}, "tableColumnName": {"shape": "TableColumnName", "documentation": "<p>The name of the column in the table.</p>"}, "format": {"shape": "Format", "documentation": "<p> The column level format that is applied in the table. An empty value in this field means that the column format is the default value 'AUTO'. </p>"}}, "documentation": "<p>An object that contains attributes about a single column in a table</p>"}, "TableColumnName": {"type": "string"}, "TableColumns": {"type": "list", "member": {"shape": "TableColumn"}}, "TableDataImportJobMessage": {"type": "string"}, "TableDataImportJobMetadata": {"type": "structure", "required": ["submitter", "submitTime", "importOptions", "dataSource"], "members": {"submitter": {"shape": "ImportJobSubmitter", "documentation": "<p>Details about the submitter of the import request.</p>"}, "submitTime": {"shape": "TimestampInMillis", "documentation": "<p>The timestamp when the job was submitted for import.</p>"}, "importOptions": {"shape": "ImportOptions", "documentation": "<p>The options that was specified at the time of submitting the import request.</p>"}, "dataSource": {"shape": "ImportDataSource", "documentation": "<p>The source of the data that was submitted for import.</p>"}}, "documentation": "<p>The metadata associated with the table data import job that was submitted.</p>"}, "TableDataImportJobStatus": {"type": "string", "enum": ["SUBMITTED", "IN_PROGRESS", "COMPLETED", "FAILED"]}, "TableName": {"type": "string"}, "TableRow": {"type": "structure", "required": ["rowId", "cells"], "members": {"rowId": {"shape": "RowId", "documentation": "<p>The id of the row in the table.</p>"}, "cells": {"shape": "Cells", "documentation": "<p>A list of cells in the table row. The cells appear in the same order as the columns of the table. </p>"}}, "documentation": "<p>An object that contains attributes about a single row in a table</p>"}, "TableRows": {"type": "list", "member": {"shape": "TableRow"}}, "Tables": {"type": "list", "member": {"shape": "Table"}}, "TagKey": {"type": "string", "max": 100, "min": 1, "pattern": "^[^\\n\\r\\x00\\x08\\x0B\\x0C\\x0E\\x1F]*$"}, "TagKeysList": {"type": "list", "member": {"shape": "TagKey"}, "documentation": "<p>A list of tag keys</p>", "max": 100}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "ResourceArn", "documentation": "<p>The resource's Amazon Resource Name (ARN).</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagsMap", "documentation": "<p>A list of tags to apply to the resource.</p>"}}}, "TagResourceResult": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 100, "min": 1, "pattern": "^[^\\n\\r\\x00\\x08\\x0B\\x0C\\x0E\\x1F]*$"}, "TagsMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "documentation": "<p>A string to string map representing tags</p>", "max": 100}, "ThrottlingException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>Tps(transactions per second) rate reached.</p>", "error": {"httpStatusCode": 429}, "exception": true}, "TimestampInMillis": {"type": "timestamp"}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "ResourceArn", "documentation": "<p>The resource's Amazon Resource Name (ARN).</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeysList", "documentation": "<p>A list of tag keys to remove from the resource.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResult": {"type": "structure", "members": {}}, "UpdateRowData": {"type": "structure", "required": ["rowId", "cellsToUpdate"], "members": {"rowId": {"shape": "RowId", "documentation": "<p> The id of the row that needs to be updated. </p>"}, "cellsToUpdate": {"shape": "RowDataInput", "documentation": "<p> A map representing the cells to update in the given row. The key is the column id of the cell and the value is the CellInput object that represents the data to set in that cell. </p>"}}, "documentation": "<p> Data needed to create a single row in a table as part of the BatchCreateTableRows request. </p>"}, "UpdateRowDataList": {"type": "list", "member": {"shape": "UpdateRowData"}, "max": 100, "min": 1}, "UpsertAction": {"type": "string", "enum": ["UPDATED", "APPENDED"]}, "UpsertRowData": {"type": "structure", "required": ["batchItemId", "filter", "cellsToUpdate"], "members": {"batchItemId": {"shape": "BatchItemId", "documentation": "<p> An external identifier that represents a single item in the request that is being upserted as part of the BatchUpsertTableRows request. This can be any string that you can use to identify the item in the request. The BatchUpsertTableRows API puts the batch item id in the results to allow you to link data in the request to data in the results. </p>"}, "filter": {"shape": "Filter", "documentation": "<p> The filter formula to use to find existing matching rows to update. The formula needs to return zero or more rows. If the formula returns 0 rows, then a new row will be appended in the target table. If the formula returns one or more rows, then the returned rows will be updated. </p> <p> Note that the filter formula needs to return rows from the target table for the upsert operation to succeed. If the filter formula has a syntax error or it doesn't evaluate to zero or more rows in the target table for any one item in the input list, then the entire BatchUpsertTableRows request fails and no updates are made to the table. </p>"}, "cellsToUpdate": {"shape": "RowDataInput", "documentation": "<p> A map representing the cells to update for the matching rows or an appended row. The key is the column id of the cell and the value is the CellInput object that represents the data to set in that cell. </p>"}}, "documentation": "<p> Data needed to upsert rows in a table as part of a single item in the BatchUpsertTableRows request. </p>"}, "UpsertRowDataList": {"type": "list", "member": {"shape": "UpsertRowData"}}, "UpsertRowsResult": {"type": "structure", "required": ["rowIds", "upsertAction"], "members": {"rowIds": {"shape": "RowIdList", "documentation": "<p> The list of row ids that were changed as part of an upsert row operation. If the upsert resulted in an update, this list could potentially contain multiple rows that matched the filter and hence got updated. If the upsert resulted in an append, this list would only have the single row that was appended. </p>"}, "upsertAction": {"shape": "UpsertAction", "documentation": "<p> The result of the upsert action. </p>"}}, "documentation": "<p> An object that represents the result of a single upsert row request. </p>"}, "UpsertRowsResultMap": {"type": "map", "key": {"shape": "BatchItemId"}, "value": {"shape": "UpsertRowsResult"}}, "ValidationException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p> Request is invalid. The message in the response contains details on why the request is invalid. </p>", "error": {"httpStatusCode": 400}, "exception": true}, "VariableName": {"type": "string", "max": 255, "min": 1, "pattern": "^(?!\\s*$).+", "sensitive": true}, "VariableValue": {"type": "structure", "required": ["rawValue"], "members": {"rawValue": {"shape": "RawValue", "documentation": "<p>Raw value of the variable.</p>"}}, "documentation": "<p>The input variables to the app to be used by the InvokeScreenAutomation action request.</p>", "sensitive": true}, "VariableValueMap": {"type": "map", "key": {"shape": "VariableName"}, "value": {"shape": "VariableValue"}, "sensitive": true}, "WorkbookCursor": {"type": "long"}}, "documentation": "<p> Amazon Honeycode is a fully managed service that allows you to quickly build mobile and web apps for teams—without programming. Build Honeycode apps for managing almost anything, like projects, customers, operations, approvals, resources, and even your team. </p>"}