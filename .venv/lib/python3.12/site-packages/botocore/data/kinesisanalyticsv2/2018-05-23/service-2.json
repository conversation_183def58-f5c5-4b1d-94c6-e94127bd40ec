{"version": "2.0", "metadata": {"apiVersion": "2018-05-23", "endpointPrefix": "kinesisanalytics", "jsonVersion": "1.1", "protocol": "json", "serviceAbbreviation": "Kinesis Analytics V2", "serviceFullName": "Amazon Kinesis Analytics", "serviceId": "Kinesis Analytics V2", "signatureVersion": "v4", "signingName": "kinesisanalytics", "targetPrefix": "KinesisAnalytics_20180523", "uid": "kinesisanalyticsv2-2018-05-23"}, "operations": {"AddApplicationCloudWatchLoggingOption": {"name": "AddApplicationCloudWatchLoggingOption", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AddApplicationCloudWatchLoggingOptionRequest"}, "output": {"shape": "AddApplicationCloudWatchLoggingOptionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidApplicationConfigurationException"}], "documentation": "<p>Adds an Amazon CloudWatch log stream to monitor application configuration errors.</p>"}, "AddApplicationInput": {"name": "AddApplicationInput", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AddApplicationInputRequest"}, "output": {"shape": "AddApplicationInputResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}, {"shape": "CodeValidationException"}, {"shape": "InvalidRequestException"}], "documentation": "<p> Adds a streaming source to your SQL-based Kinesis Data Analytics application. </p> <p>You can add a streaming source when you create an application, or you can use this operation to add a streaming source after you create an application. For more information, see <a>CreateApplication</a>.</p> <p>Any configuration update, including adding a streaming source using this operation, results in a new version of the application. You can use the <a>DescribeApplication</a> operation to find the current application version. </p>"}, "AddApplicationInputProcessingConfiguration": {"name": "AddApplicationInputProcessingConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AddApplicationInputProcessingConfigurationRequest"}, "output": {"shape": "AddApplicationInputProcessingConfigurationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Adds an <a>InputProcessingConfiguration</a> to a SQL-based Kinesis Data Analytics application. An input processor pre-processes records on the input stream before the application's SQL code executes. Currently, the only input processor available is <a href=\"https://docs.aws.amazon.com/lambda/\">Amazon Lambda</a>.</p>"}, "AddApplicationOutput": {"name": "AddApplicationOutput", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AddApplicationOutputRequest"}, "output": {"shape": "AddApplicationOutputResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Adds an external destination to your SQL-based Kinesis Data Analytics application.</p> <p>If you want Kinesis Data Analytics to deliver data from an in-application stream within your application to an external destination (such as an Kinesis data stream, a Kinesis Data Firehose delivery stream, or an Amazon Lambda function), you add the relevant configuration to your application using this operation. You can configure one or more outputs for your application. Each output configuration maps an in-application stream and an external destination.</p> <p> You can use one of the output configurations to deliver data from your in-application error stream to an external destination so that you can analyze the errors. </p> <p> Any configuration update, including adding a streaming source using this operation, results in a new version of the application. You can use the <a>DescribeApplication</a> operation to find the current application version.</p>"}, "AddApplicationReferenceDataSource": {"name": "AddApplicationReferenceDataSource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AddApplicationReferenceDataSourceRequest"}, "output": {"shape": "AddApplicationReferenceDataSourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Adds a reference data source to an existing SQL-based Kinesis Data Analytics application.</p> <p>Kinesis Data Analytics reads reference data (that is, an Amazon S3 object) and creates an in-application table within your application. In the request, you provide the source (S3 bucket name and object key name), name of the in-application table to create, and the necessary mapping information that describes how data in an Amazon S3 object maps to columns in the resulting in-application table.</p>"}, "AddApplicationVpcConfiguration": {"name": "AddApplicationVpcConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AddApplicationVpcConfigurationRequest"}, "output": {"shape": "AddApplicationVpcConfigurationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InvalidApplicationConfigurationException"}], "documentation": "<p>Adds a Virtual Private Cloud (VPC) configuration to the application. Applications can use VPCs to store and access resources securely.</p> <p>Note the following about VPC configurations for Kinesis Data Analytics applications:</p> <ul> <li> <p>VPC configurations are not supported for SQL applications.</p> </li> <li> <p>When a VPC is added to a Kinesis Data Analytics application, the application can no longer be accessed from the Internet directly. To enable Internet access to the application, add an Internet gateway to your VPC.</p> </li> </ul>"}, "CreateApplication": {"name": "CreateApplication", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateApplicationRequest"}, "output": {"shape": "CreateApplicationResponse"}, "errors": [{"shape": "CodeValidationException"}, {"shape": "ResourceInUseException"}, {"shape": "LimitExceededException"}, {"shape": "InvalidArgumentException"}, {"shape": "InvalidRequestException"}, {"shape": "TooManyTagsException"}, {"shape": "ConcurrentModificationException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Creates a Kinesis Data Analytics application. For information about creating a Kinesis Data Analytics application, see <a href=\"https://docs.aws.amazon.com/kinesisanalytics/latest/java/getting-started.html\">Creating an Application</a>.</p>"}, "CreateApplicationPresignedUrl": {"name": "CreateApplicationPresignedUrl", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateApplicationPresignedUrlRequest"}, "output": {"shape": "CreateApplicationPresignedUrlResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "InvalidArgumentException"}], "documentation": "<p>Creates and returns a URL that you can use to connect to an application's extension.</p> <p>The IAM role or user used to call this API defines the permissions to access the extension. After the presigned URL is created, no additional permission is required to access this URL. IAM authorization policies for this API are also enforced for every HTTP request that attempts to connect to the extension. </p> <p>You control the amount of time that the URL will be valid using the <code>SessionExpirationDurationInSeconds</code> parameter. If you do not provide this parameter, the returned URL is valid for twelve hours.</p> <note> <p>The URL that you get from a call to CreateApplicationPresignedUrl must be used within 3 minutes to be valid. If you first try to use the URL after the 3-minute limit expires, the service returns an HTTP 403 Forbidden error.</p> </note>"}, "CreateApplicationSnapshot": {"name": "CreateApplicationSnapshot", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateApplicationSnapshotRequest"}, "output": {"shape": "CreateApplicationSnapshotResponse"}, "errors": [{"shape": "ResourceInUseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "InvalidArgumentException"}, {"shape": "UnsupportedOperationException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidApplicationConfigurationException"}], "documentation": "<p>Creates a snapshot of the application's state data.</p>"}, "DeleteApplication": {"name": "DeleteApplication", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteApplicationRequest"}, "output": {"shape": "DeleteApplicationResponse"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "InvalidArgumentException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidApplicationConfigurationException"}], "documentation": "<p>Deletes the specified application. Kinesis Data Analytics halts application execution and deletes the application.</p>"}, "DeleteApplicationCloudWatchLoggingOption": {"name": "DeleteApplicationCloudWatchLoggingOption", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteApplicationCloudWatchLoggingOptionRequest"}, "output": {"shape": "DeleteApplicationCloudWatchLoggingOptionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidApplicationConfigurationException"}], "documentation": "<p>Deletes an Amazon CloudWatch log stream from an Kinesis Data Analytics application. </p>"}, "DeleteApplicationInputProcessingConfiguration": {"name": "DeleteApplicationInputProcessingConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteApplicationInputProcessingConfigurationRequest"}, "output": {"shape": "DeleteApplicationInputProcessingConfigurationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Deletes an <a>InputProcessingConfiguration</a> from an input.</p>"}, "DeleteApplicationOutput": {"name": "DeleteApplicationOutput", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteApplicationOutputRequest"}, "output": {"shape": "DeleteApplicationOutputResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Deletes the output destination configuration from your SQL-based Kinesis Data Analytics application's configuration. Kinesis Data Analytics will no longer write data from the corresponding in-application stream to the external output destination.</p>"}, "DeleteApplicationReferenceDataSource": {"name": "DeleteApplicationReferenceDataSource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteApplicationReferenceDataSourceRequest"}, "output": {"shape": "DeleteApplicationReferenceDataSourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Deletes a reference data source configuration from the specified SQL-based Kinesis Data Analytics application's configuration.</p> <p>If the application is running, Kinesis Data Analytics immediately removes the in-application table that you created using the <a>AddApplicationReferenceDataSource</a> operation. </p>"}, "DeleteApplicationSnapshot": {"name": "DeleteApplicationSnapshot", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteApplicationSnapshotRequest"}, "output": {"shape": "DeleteApplicationSnapshotResponse"}, "errors": [{"shape": "ResourceInUseException"}, {"shape": "InvalidArgumentException"}, {"shape": "UnsupportedOperationException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Deletes a snapshot of application state.</p>"}, "DeleteApplicationVpcConfiguration": {"name": "DeleteApplicationVpcConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteApplicationVpcConfigurationRequest"}, "output": {"shape": "DeleteApplicationVpcConfigurationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InvalidApplicationConfigurationException"}], "documentation": "<p>Removes a VPC configuration from a Kinesis Data Analytics application.</p>"}, "DescribeApplication": {"name": "DescribeApplication", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeApplicationRequest"}, "output": {"shape": "DescribeApplicationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidArgumentException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Returns information about a specific Kinesis Data Analytics application.</p> <p>If you want to retrieve a list of all applications in your account, use the <a>ListApplications</a> operation.</p>"}, "DescribeApplicationSnapshot": {"name": "DescribeApplicationSnapshot", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeApplicationSnapshotRequest"}, "output": {"shape": "DescribeApplicationSnapshotResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidArgumentException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Returns information about a snapshot of application state data.</p>"}, "DescribeApplicationVersion": {"name": "DescribeApplicationVersion", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeApplicationVersionRequest"}, "output": {"shape": "DescribeApplicationVersionResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "ResourceNotFoundException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Provides a detailed description of a specified version of the application. To see a list of all the versions of an application, invoke the <a>ListApplicationVersions</a> operation.</p> <note> <p>This operation is supported only for Amazon Kinesis Data Analytics for Apache Flink.</p> </note>"}, "DiscoverInputSchema": {"name": "DiscoverInputSchema", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DiscoverInputSchemaRequest"}, "output": {"shape": "DiscoverInputSchemaResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "UnableToDetectSchemaException"}, {"shape": "ResourceProvisionedThroughputExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Infers a schema for a SQL-based Kinesis Data Analytics application by evaluating sample records on the specified streaming source (Kinesis data stream or Kinesis Data Firehose delivery stream) or Amazon S3 object. In the response, the operation returns the inferred schema and also the sample records that the operation used to infer the schema.</p> <p> You can use the inferred schema when configuring a streaming source for your application. When you create an application using the Kinesis Data Analytics console, the console uses this operation to infer a schema and show it in the console user interface. </p>"}, "ListApplicationSnapshots": {"name": "ListApplicationSnapshots", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListApplicationSnapshotsRequest"}, "output": {"shape": "ListApplicationSnapshotsResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Lists information about the current application snapshots.</p>"}, "ListApplicationVersions": {"name": "ListApplicationVersions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListApplicationVersionsRequest"}, "output": {"shape": "ListApplicationVersionsResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "ResourceNotFoundException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Lists all the versions for the specified application, including versions that were rolled back. The response also includes a summary of the configuration associated with each version.</p> <p>To get the complete description of a specific application version, invoke the <a>DescribeApplicationVersion</a> operation.</p> <note> <p>This operation is supported only for Amazon Kinesis Data Analytics for Apache Flink.</p> </note>"}, "ListApplications": {"name": "ListApplications", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListApplicationsRequest"}, "output": {"shape": "ListApplicationsResponse"}, "errors": [{"shape": "InvalidRequestException"}], "documentation": "<p>Returns a list of Kinesis Data Analytics applications in your account. For each application, the response includes the application name, Amazon Resource Name (ARN), and status. </p> <p>If you want detailed information about a specific application, use <a>DescribeApplication</a>.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Retrieves the list of key-value tags assigned to the application. For more information, see <a href=\"https://docs.aws.amazon.com/kinesisanalytics/latest/java/how-tagging.html\">Using Tagging</a>.</p>"}, "RollbackApplication": {"name": "RollbackApplication", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RollbackApplicationRequest"}, "output": {"shape": "RollbackApplicationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidArgumentException"}, {"shape": "ResourceInUseException"}, {"shape": "InvalidRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Reverts the application to the previous running version. You can roll back an application if you suspect it is stuck in a transient status. </p> <p>You can roll back an application only if it is in the <code>UPDATING</code> or <code>AUTOSCALING</code> status.</p> <p>When you rollback an application, it loads state data from the last successful snapshot. If the application has no snapshots, Kinesis Data Analytics rejects the rollback request.</p> <p>This action is not supported for Kinesis Data Analytics for SQL applications.</p>"}, "StartApplication": {"name": "StartApplication", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartApplicationRequest"}, "output": {"shape": "StartApplicationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "InvalidArgumentException"}, {"shape": "InvalidApplicationConfigurationException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Starts the specified Kinesis Data Analytics application. After creating an application, you must exclusively call this operation to start your application.</p>"}, "StopApplication": {"name": "StopApplication", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StopApplicationRequest"}, "output": {"shape": "StopApplicationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "InvalidArgumentException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidApplicationConfigurationException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Stops the application from processing data. You can stop an application only if it is in the running status, unless you set the <code>Force</code> parameter to <code>true</code>.</p> <p>You can use the <a>DescribeApplication</a> operation to find the application status. </p> <p>Kinesis Data Analytics takes a snapshot when the application is stopped, unless <code>Force</code> is set to <code>true</code>.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "TooManyTagsException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Adds one or more key-value tags to a Kinesis Data Analytics application. Note that the maximum number of application tags includes system tags. The maximum number of user-defined application tags is 50. For more information, see <a href=\"https://docs.aws.amazon.com/kinesisanalytics/latest/java/how-tagging.html\">Using Tagging</a>.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "TooManyTagsException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Removes one or more tags from a Kinesis Data Analytics application. For more information, see <a href=\"https://docs.aws.amazon.com/kinesisanalytics/latest/java/how-tagging.html\">Using Tagging</a>.</p>"}, "UpdateApplication": {"name": "UpdateApplication", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateApplicationRequest"}, "output": {"shape": "UpdateApplicationResponse"}, "errors": [{"shape": "CodeValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidApplicationConfigurationException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Updates an existing Kinesis Data Analytics application. Using this operation, you can update application code, input configuration, and output configuration. </p> <p>Kinesis Data Analytics updates the <code>ApplicationVersionId</code> each time you update your application. </p> <note> <p>You cannot update the <code>RuntimeEnvironment</code> of an existing application. If you need to update an application's <code>RuntimeEnvironment</code>, you must delete the application and create it again.</p> </note>"}, "UpdateApplicationMaintenanceConfiguration": {"name": "UpdateApplicationMaintenanceConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateApplicationMaintenanceConfigurationRequest"}, "output": {"shape": "UpdateApplicationMaintenanceConfigurationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Updates the maintenance configuration of the Kinesis Data Analytics application. </p> <p>You can invoke this operation on an application that is in one of the two following states: <code>READY</code> or <code>RUNNING</code>. If you invoke it when the application is in a state other than these two states, it throws a <code>ResourceInUseException</code>. The service makes use of the updated configuration the next time it schedules maintenance for the application. If you invoke this operation after the service schedules maintenance, the service will apply the configuration update the next time it schedules maintenance for the application. This means that you might not see the maintenance configuration update applied to the maintenance process that follows a successful invocation of this operation, but to the following maintenance process instead.</p> <p>To see the current maintenance configuration of your application, invoke the <a>DescribeApplication</a> operation.</p> <p>For information about application maintenance, see <a href=\"https://docs.aws.amazon.com/kinesisanalytics/latest/java/maintenance.html\">Kinesis Data Analytics for Apache Flink Maintenance</a>.</p> <note> <p>This operation is supported only for Amazon Kinesis Data Analytics for Apache Flink.</p> </note>"}}, "shapes": {"AddApplicationCloudWatchLoggingOptionRequest": {"type": "structure", "required": ["ApplicationName", "CloudWatchLoggingOption"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The Kinesis Data Analytics application name.</p>"}, "CurrentApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>The version ID of the Kinesis Data Analytics application. You must provide the <code>CurrentApplicationVersionId</code> or the <code>ConditionalToken</code>.You can retrieve the application version ID using <a>DescribeApplication</a>. For better concurrency support, use the <code>ConditionalToken</code> parameter instead of <code>CurrentApplicationVersionId</code>.</p>"}, "CloudWatchLoggingOption": {"shape": "CloudWatchLoggingOption", "documentation": "<p>Provides the Amazon CloudWatch log stream Amazon Resource Name (ARN). </p>"}, "ConditionalToken": {"shape": "ConditionalToken", "documentation": "<p>A value you use to implement strong concurrency for application updates. You must provide the <code>CurrentApplicationVersionId</code> or the <code>ConditionalToken</code>. You get the application's current <code>ConditionalToken</code> using <a>DescribeApplication</a>. For better concurrency support, use the <code>ConditionalToken</code> parameter instead of <code>CurrentApplicationVersionId</code>.</p>"}}}, "AddApplicationCloudWatchLoggingOptionResponse": {"type": "structure", "members": {"ApplicationARN": {"shape": "ResourceARN", "documentation": "<p>The application's ARN.</p>"}, "ApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>The new version ID of the Kinesis Data Analytics application. Kinesis Data Analytics updates the <code>ApplicationVersionId</code> each time you change the CloudWatch logging options. </p>"}, "CloudWatchLoggingOptionDescriptions": {"shape": "CloudWatchLoggingOptionDescriptions", "documentation": "<p>The descriptions of the current CloudWatch logging options for the Kinesis Data Analytics application.</p>"}}}, "AddApplicationInputProcessingConfigurationRequest": {"type": "structure", "required": ["ApplicationName", "CurrentApplicationVersionId", "InputId", "InputProcessingConfiguration"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of the application to which you want to add the input processing configuration.</p>"}, "CurrentApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>The version of the application to which you want to add the input processing configuration. You can use the <a>DescribeApplication</a> operation to get the current application version. If the version specified is not the current version, the <code>ConcurrentModificationException</code> is returned.</p>"}, "InputId": {"shape": "Id", "documentation": "<p>The ID of the input configuration to add the input processing configuration to. You can get a list of the input IDs for an application using the <a>DescribeApplication</a> operation.</p>"}, "InputProcessingConfiguration": {"shape": "InputProcessingConfiguration", "documentation": "<p>The <a>InputProcessingConfiguration</a> to add to the application.</p>"}}}, "AddApplicationInputProcessingConfigurationResponse": {"type": "structure", "members": {"ApplicationARN": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the application.</p>"}, "ApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>Provides the current application version. </p>"}, "InputId": {"shape": "Id", "documentation": "<p>The input ID that is associated with the application input. This is the ID that Kinesis Data Analytics assigns to each input configuration that you add to your application.</p>"}, "InputProcessingConfigurationDescription": {"shape": "InputProcessingConfigurationDescription", "documentation": "<p>The description of the preprocessor that executes on records in this input before the application's code is run.</p>"}}}, "AddApplicationInputRequest": {"type": "structure", "required": ["ApplicationName", "CurrentApplicationVersionId", "Input"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of your existing application to which you want to add the streaming source.</p>"}, "CurrentApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>The current version of your application. You must provide the <code>ApplicationVersionID</code> or the <code>ConditionalToken</code>.You can use the <a>DescribeApplication</a> operation to find the current application version.</p>"}, "Input": {"shape": "Input", "documentation": "<p>The <a>Input</a> to add.</p>"}}}, "AddApplicationInputResponse": {"type": "structure", "members": {"ApplicationARN": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the application.</p>"}, "ApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>Provides the current application version.</p>"}, "InputDescriptions": {"shape": "InputDescriptions", "documentation": "<p>Describes the application input configuration. </p>"}}}, "AddApplicationOutputRequest": {"type": "structure", "required": ["ApplicationName", "CurrentApplicationVersionId", "Output"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of the application to which you want to add the output configuration.</p>"}, "CurrentApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>The version of the application to which you want to add the output configuration. You can use the <a>DescribeApplication</a> operation to get the current application version. If the version specified is not the current version, the <code>ConcurrentModificationException</code> is returned. </p>"}, "Output": {"shape": "Output", "documentation": "<p>An array of objects, each describing one output configuration. In the output configuration, you specify the name of an in-application stream, a destination (that is, a Kinesis data stream, a Kinesis Data Firehose delivery stream, or an Amazon Lambda function), and record the formation to use when writing to the destination.</p>"}}}, "AddApplicationOutputResponse": {"type": "structure", "members": {"ApplicationARN": {"shape": "ResourceARN", "documentation": "<p>The application Amazon Resource Name (ARN).</p>"}, "ApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>The updated application version ID. Kinesis Data Analytics increments this ID when the application is updated.</p>"}, "OutputDescriptions": {"shape": "OutputDescriptions", "documentation": "<p>Describes the application output configuration. For more information, see <a href=\"https://docs.aws.amazon.com/kinesisanalytics/latest/dev/how-it-works-output.html\">Configuring Application Output</a>. </p>"}}}, "AddApplicationReferenceDataSourceRequest": {"type": "structure", "required": ["ApplicationName", "CurrentApplicationVersionId", "ReferenceDataSource"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of an existing application.</p>"}, "CurrentApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>The version of the application for which you are adding the reference data source. You can use the <a>DescribeApplication</a> operation to get the current application version. If the version specified is not the current version, the <code>ConcurrentModificationException</code> is returned.</p>"}, "ReferenceDataSource": {"shape": "ReferenceDataSource", "documentation": "<p>The reference data source can be an object in your Amazon S3 bucket. Kinesis Data Analytics reads the object and copies the data into the in-application table that is created. You provide an S3 bucket, object key name, and the resulting in-application table that is created. </p>"}}}, "AddApplicationReferenceDataSourceResponse": {"type": "structure", "members": {"ApplicationARN": {"shape": "ResourceARN", "documentation": "<p>The application Amazon Resource Name (ARN).</p>"}, "ApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>The updated application version ID. Kinesis Data Analytics increments this ID when the application is updated.</p>"}, "ReferenceDataSourceDescriptions": {"shape": "ReferenceDataSourceDescriptions", "documentation": "<p>Describes reference data sources configured for the application. </p>"}}}, "AddApplicationVpcConfigurationRequest": {"type": "structure", "required": ["ApplicationName", "VpcConfiguration"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of an existing application.</p>"}, "CurrentApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>The version of the application to which you want to add the VPC configuration. You must provide the <code>CurrentApplicationVersionId</code> or the <code>ConditionalToken</code>. You can use the <a>DescribeApplication</a> operation to get the current application version. If the version specified is not the current version, the <code>ConcurrentModificationException</code> is returned. For better concurrency support, use the <code>ConditionalToken</code> parameter instead of <code>CurrentApplicationVersionId</code>.</p>"}, "VpcConfiguration": {"shape": "VpcConfiguration", "documentation": "<p>Description of the VPC to add to the application.</p>"}, "ConditionalToken": {"shape": "ConditionalToken", "documentation": "<p>A value you use to implement strong concurrency for application updates. You must provide the <code>ApplicationVersionID</code> or the <code>ConditionalToken</code>. You get the application's current <code>ConditionalToken</code> using <a>DescribeApplication</a>. For better concurrency support, use the <code>ConditionalToken</code> parameter instead of <code>CurrentApplicationVersionId</code>.</p>"}}}, "AddApplicationVpcConfigurationResponse": {"type": "structure", "members": {"ApplicationARN": {"shape": "ResourceARN", "documentation": "<p>The ARN of the application.</p>"}, "ApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>Provides the current application version. Kinesis Data Analytics updates the ApplicationVersionId each time you update the application.</p>"}, "VpcConfigurationDescription": {"shape": "VpcConfigurationDescription", "documentation": "<p>The parameters of the new VPC configuration.</p>"}}}, "ApplicationCodeConfiguration": {"type": "structure", "required": ["CodeContentType"], "members": {"CodeContent": {"shape": "CodeContent", "documentation": "<p>The location and type of the application code.</p>"}, "CodeContentType": {"shape": "CodeContentType", "documentation": "<p>Specifies whether the code content is in text or zip format.</p>"}}, "documentation": "<p>Describes code configuration for an application.</p>"}, "ApplicationCodeConfigurationDescription": {"type": "structure", "required": ["CodeContentType"], "members": {"CodeContentType": {"shape": "CodeContentType", "documentation": "<p>Specifies whether the code content is in text or zip format.</p>"}, "CodeContentDescription": {"shape": "CodeContentDescription", "documentation": "<p>Describes details about the location and format of the application code.</p>"}}, "documentation": "<p>Describes code configuration for an application.</p>"}, "ApplicationCodeConfigurationUpdate": {"type": "structure", "members": {"CodeContentTypeUpdate": {"shape": "CodeContentType", "documentation": "<p>Describes updates to the code content type.</p>"}, "CodeContentUpdate": {"shape": "CodeContentUpdate", "documentation": "<p>Describes updates to the code content of an application.</p>"}}, "documentation": "<p>Describes code configuration updates for an application. This is supported for a Flink-based Kinesis Data Analytics application or a SQL-based Kinesis Data Analytics application.</p>"}, "ApplicationConfiguration": {"type": "structure", "members": {"SqlApplicationConfiguration": {"shape": "SqlApplicationConfiguration", "documentation": "<p>The creation and update parameters for a SQL-based Kinesis Data Analytics application.</p>"}, "FlinkApplicationConfiguration": {"shape": "FlinkApplicationConfiguration", "documentation": "<p>The creation and update parameters for a Flink-based Kinesis Data Analytics application.</p>"}, "EnvironmentProperties": {"shape": "EnvironmentProperties", "documentation": "<p>Describes execution properties for a Flink-based Kinesis Data Analytics application.</p>"}, "ApplicationCodeConfiguration": {"shape": "ApplicationCodeConfiguration", "documentation": "<p>The code location and type parameters for a Flink-based Kinesis Data Analytics application.</p>"}, "ApplicationSnapshotConfiguration": {"shape": "ApplicationSnapshotConfiguration", "documentation": "<p>Describes whether snapshots are enabled for a Flink-based Kinesis Data Analytics application.</p>"}, "VpcConfigurations": {"shape": "VpcConfigurations", "documentation": "<p>The array of descriptions of VPC configurations available to the application.</p>"}, "ZeppelinApplicationConfiguration": {"shape": "ZeppelinApplicationConfiguration", "documentation": "<p>The configuration parameters for a Kinesis Data Analytics Studio notebook.</p>"}}, "documentation": "<p>Specifies the creation parameters for a Kinesis Data Analytics application.</p>"}, "ApplicationConfigurationDescription": {"type": "structure", "members": {"SqlApplicationConfigurationDescription": {"shape": "SqlApplicationConfigurationDescription", "documentation": "<p>The details about inputs, outputs, and reference data sources for a SQL-based Kinesis Data Analytics application.</p>"}, "ApplicationCodeConfigurationDescription": {"shape": "ApplicationCodeConfigurationDescription", "documentation": "<p>The details about the application code for a Flink-based Kinesis Data Analytics application.</p>"}, "RunConfigurationDescription": {"shape": "RunConfigurationDescription", "documentation": "<p>The details about the starting properties for a Kinesis Data Analytics application.</p>"}, "FlinkApplicationConfigurationDescription": {"shape": "FlinkApplicationConfigurationDescription", "documentation": "<p>The details about a Flink-based Kinesis Data Analytics application.</p>"}, "EnvironmentPropertyDescriptions": {"shape": "EnvironmentPropertyDescriptions", "documentation": "<p>Describes execution properties for a Flink-based Kinesis Data Analytics application.</p>"}, "ApplicationSnapshotConfigurationDescription": {"shape": "ApplicationSnapshotConfigurationDescription", "documentation": "<p>Describes whether snapshots are enabled for a Flink-based Kinesis Data Analytics application.</p>"}, "VpcConfigurationDescriptions": {"shape": "VpcConfigurationDescriptions", "documentation": "<p>The array of descriptions of VPC configurations available to the application.</p>"}, "ZeppelinApplicationConfigurationDescription": {"shape": "ZeppelinApplicationConfigurationDescription", "documentation": "<p>The configuration parameters for a Kinesis Data Analytics Studio notebook.</p>"}}, "documentation": "<p>Describes details about the application code and starting parameters for a Kinesis Data Analytics application.</p>"}, "ApplicationConfigurationUpdate": {"type": "structure", "members": {"SqlApplicationConfigurationUpdate": {"shape": "SqlApplicationConfigurationUpdate", "documentation": "<p>Describes updates to a SQL-based Kinesis Data Analytics application's configuration.</p>"}, "ApplicationCodeConfigurationUpdate": {"shape": "ApplicationCodeConfigurationUpdate", "documentation": "<p>Describes updates to an application's code configuration.</p>"}, "FlinkApplicationConfigurationUpdate": {"shape": "FlinkApplicationConfigurationUpdate", "documentation": "<p>Describes updates to a Flink-based Kinesis Data Analytics application's configuration.</p>"}, "EnvironmentPropertyUpdates": {"shape": "EnvironmentPropertyUpdates", "documentation": "<p>Describes updates to the environment properties for a Flink-based Kinesis Data Analytics application.</p>"}, "ApplicationSnapshotConfigurationUpdate": {"shape": "ApplicationSnapshotConfigurationUpdate", "documentation": "<p>Describes whether snapshots are enabled for a Flink-based Kinesis Data Analytics application.</p>"}, "VpcConfigurationUpdates": {"shape": "VpcConfigurationUpdates", "documentation": "<p>Updates to the array of descriptions of VPC configurations available to the application.</p>"}, "ZeppelinApplicationConfigurationUpdate": {"shape": "ZeppelinApplicationConfigurationUpdate", "documentation": "<p>Updates to the configuration of a Kinesis Data Analytics Studio notebook.</p>"}}, "documentation": "<p>Describes updates to an application's configuration.</p>"}, "ApplicationDescription": {"type": "string", "max": 1024, "min": 0}, "ApplicationDetail": {"type": "structure", "required": ["ApplicationARN", "ApplicationName", "RuntimeEnvironment", "ApplicationStatus", "ApplicationVersionId"], "members": {"ApplicationARN": {"shape": "ResourceARN", "documentation": "<p>The ARN of the application.</p>"}, "ApplicationDescription": {"shape": "ApplicationDescription", "documentation": "<p>The description of the application.</p>"}, "ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of the application.</p>"}, "RuntimeEnvironment": {"shape": "RuntimeEnvironment", "documentation": "<p>The runtime environment for the application.</p>"}, "ServiceExecutionRole": {"shape": "RoleARN", "documentation": "<p>Specifies the IAM role that the application uses to access external resources.</p>"}, "ApplicationStatus": {"shape": "ApplicationStatus", "documentation": "<p>The status of the application.</p>"}, "ApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>Provides the current application version. Kinesis Data Analytics updates the <code>ApplicationVersionId</code> each time you update the application.</p>"}, "CreateTimestamp": {"shape": "Timestamp", "documentation": "<p>The current timestamp when the application was created.</p>"}, "LastUpdateTimestamp": {"shape": "Timestamp", "documentation": "<p>The current timestamp when the application was last updated.</p>"}, "ApplicationConfigurationDescription": {"shape": "ApplicationConfigurationDescription", "documentation": "<p>Describes details about the application code and starting parameters for a Kinesis Data Analytics application.</p>"}, "CloudWatchLoggingOptionDescriptions": {"shape": "CloudWatchLoggingOptionDescriptions", "documentation": "<p>Describes the application Amazon CloudWatch logging options.</p>"}, "ApplicationMaintenanceConfigurationDescription": {"shape": "ApplicationMaintenanceConfigurationDescription", "documentation": "<p>The details of the maintenance configuration for the application.</p>"}, "ApplicationVersionUpdatedFrom": {"shape": "ApplicationVersionId", "documentation": "<p>The previous application version before the latest application update. <a>RollbackApplication</a> reverts the application to this version.</p>"}, "ApplicationVersionRolledBackFrom": {"shape": "ApplicationVersionId", "documentation": "<p>If you reverted the application using <a>RollbackApplication</a>, the application version when <code>RollbackApplication</code> was called.</p>"}, "ConditionalToken": {"shape": "ConditionalToken", "documentation": "<p>A value you use to implement strong concurrency for application updates.</p>"}, "ApplicationVersionRolledBackTo": {"shape": "ApplicationVersionId", "documentation": "<p>The version to which you want to roll back the application.</p>"}, "ApplicationMode": {"shape": "ApplicationMode", "documentation": "<p>To create a Kinesis Data Analytics Studio notebook, you must set the mode to <code>INTERACTIVE</code>. However, for a Kinesis Data Analytics for Apache Flink application, the mode is optional.</p>"}}, "documentation": "<p>Describes the application, including the application Amazon Resource Name (ARN), status, latest version, and input and output configurations.</p>"}, "ApplicationMaintenanceConfigurationDescription": {"type": "structure", "required": ["ApplicationMaintenanceWindowStartTime", "ApplicationMaintenanceWindowEndTime"], "members": {"ApplicationMaintenanceWindowStartTime": {"shape": "ApplicationMaintenanceWindowStartTime", "documentation": "<p>The start time for the maintenance window.</p>"}, "ApplicationMaintenanceWindowEndTime": {"shape": "ApplicationMaintenanceWindowEndTime", "documentation": "<p>The end time for the maintenance window.</p>"}}, "documentation": "<p>The details of the maintenance configuration for the application.</p>"}, "ApplicationMaintenanceConfigurationUpdate": {"type": "structure", "required": ["ApplicationMaintenanceWindowStartTimeUpdate"], "members": {"ApplicationMaintenanceWindowStartTimeUpdate": {"shape": "ApplicationMaintenanceWindowStartTime", "documentation": "<p>The updated start time for the maintenance window.</p>"}}, "documentation": "<p>Describes the updated maintenance configuration for the application.</p>"}, "ApplicationMaintenanceWindowEndTime": {"type": "string", "max": 5, "min": 5, "pattern": "([01][0-9]|2[0-3]):[0-5][0-9]"}, "ApplicationMaintenanceWindowStartTime": {"type": "string", "max": 5, "min": 5, "pattern": "([01][0-9]|2[0-3]):[0-5][0-9]"}, "ApplicationMode": {"type": "string", "enum": ["STREAMING", "INTERACTIVE"]}, "ApplicationName": {"type": "string", "max": 128, "min": 1, "pattern": "[a-zA-Z0-9_.-]+"}, "ApplicationRestoreConfiguration": {"type": "structure", "required": ["ApplicationRestoreType"], "members": {"ApplicationRestoreType": {"shape": "ApplicationRestoreType", "documentation": "<p>Specifies how the application should be restored.</p>"}, "SnapshotName": {"shape": "SnapshotName", "documentation": "<p>The identifier of an existing snapshot of application state to use to restart an application. The application uses this value if <code>RESTORE_FROM_CUSTOM_SNAPSHOT</code> is specified for the <code>ApplicationRestoreType</code>.</p>"}}, "documentation": "<p>Specifies the method and snapshot to use when restarting an application using previously saved application state.</p>"}, "ApplicationRestoreType": {"type": "string", "enum": ["SKIP_RESTORE_FROM_SNAPSHOT", "RESTORE_FROM_LATEST_SNAPSHOT", "RESTORE_FROM_CUSTOM_SNAPSHOT"]}, "ApplicationSnapshotConfiguration": {"type": "structure", "required": ["SnapshotsEnabled"], "members": {"SnapshotsEnabled": {"shape": "BooleanObject", "documentation": "<p>Describes whether snapshots are enabled for a Flink-based Kinesis Data Analytics application.</p>"}}, "documentation": "<p>Describes whether snapshots are enabled for a Flink-based Kinesis Data Analytics application.</p>"}, "ApplicationSnapshotConfigurationDescription": {"type": "structure", "required": ["SnapshotsEnabled"], "members": {"SnapshotsEnabled": {"shape": "BooleanObject", "documentation": "<p>Describes whether snapshots are enabled for a Flink-based Kinesis Data Analytics application.</p>"}}, "documentation": "<p>Describes whether snapshots are enabled for a Flink-based Kinesis Data Analytics application.</p>"}, "ApplicationSnapshotConfigurationUpdate": {"type": "structure", "required": ["SnapshotsEnabledUpdate"], "members": {"SnapshotsEnabledUpdate": {"shape": "BooleanObject", "documentation": "<p>Describes updates to whether snapshots are enabled for an application.</p>"}}, "documentation": "<p>Describes updates to whether snapshots are enabled for a Flink-based Kinesis Data Analytics application.</p>"}, "ApplicationStatus": {"type": "string", "enum": ["DELETING", "STARTING", "STOPPING", "READY", "RUNNING", "UPDATING", "AUTOSCALING", "FORCE_STOPPING", "ROLLING_BACK", "MAINTENANCE", "ROLLED_BACK"]}, "ApplicationSummaries": {"type": "list", "member": {"shape": "ApplicationSummary"}}, "ApplicationSummary": {"type": "structure", "required": ["ApplicationName", "ApplicationARN", "ApplicationStatus", "ApplicationVersionId", "RuntimeEnvironment"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of the application.</p>"}, "ApplicationARN": {"shape": "ResourceARN", "documentation": "<p>The ARN of the application.</p>"}, "ApplicationStatus": {"shape": "ApplicationStatus", "documentation": "<p>The status of the application.</p>"}, "ApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>Provides the current application version.</p>"}, "RuntimeEnvironment": {"shape": "RuntimeEnvironment", "documentation": "<p>The runtime environment for the application.</p>"}, "ApplicationMode": {"shape": "ApplicationMode", "documentation": "<p>For a Kinesis Data Analytics for Apache Flink application, the mode is <code>STREAMING</code>. For a Kinesis Data Analytics Studio notebook, it is <code>INTERACTIVE</code>.</p>"}}, "documentation": "<p>Provides application summary information, including the application Amazon Resource Name (ARN), name, and status.</p>"}, "ApplicationVersionId": {"type": "long", "max": 999999999, "min": 1}, "ApplicationVersionSummaries": {"type": "list", "member": {"shape": "ApplicationVersionSummary"}}, "ApplicationVersionSummary": {"type": "structure", "required": ["ApplicationVersionId", "ApplicationStatus"], "members": {"ApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>The ID of the application version. Kinesis Data Analytics updates the <code>ApplicationVersionId</code> each time you update the application.</p>"}, "ApplicationStatus": {"shape": "ApplicationStatus", "documentation": "<p>The status of the application.</p>"}}, "documentation": "<p>The summary of the application version.</p>"}, "ArtifactType": {"type": "string", "enum": ["UDF", "DEPENDENCY_JAR"]}, "AuthorizedUrl": {"type": "string", "max": 2048, "min": 1}, "BasePath": {"type": "string", "max": 1024, "min": 1, "pattern": "[a-zA-Z0-9/!-_.*'()]+"}, "BooleanObject": {"type": "boolean"}, "BucketARN": {"type": "string", "max": 2048, "min": 1, "pattern": "arn:.*"}, "CSVMappingParameters": {"type": "structure", "required": ["RecordRowDelimiter", "RecordColumnDelimiter"], "members": {"RecordRowDelimiter": {"shape": "RecordRowDelimiter", "documentation": "<p>The row delimiter. For example, in a CSV format, <i>'\\n'</i> is the typical row delimiter.</p>"}, "RecordColumnDelimiter": {"shape": "RecordColumnDelimiter", "documentation": "<p>The column delimiter. For example, in a CSV format, a comma (\",\") is the typical column delimiter.</p>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application, provides additional mapping information when the record format uses delimiters, such as CSV. For example, the following sample records use CSV format, where the records use the <i>'\\n'</i> as the row delimiter and a comma (\",\") as the column delimiter: </p> <p> <code>\"name1\", \"address1\"</code> </p> <p> <code>\"name2\", \"address2\"</code> </p>"}, "CatalogConfiguration": {"type": "structure", "required": ["GlueDataCatalogConfiguration"], "members": {"GlueDataCatalogConfiguration": {"shape": "GlueDataCatalogConfiguration", "documentation": "<p>The configuration parameters for the default Amazon Glue database. You use this database for Apache Flink SQL queries and table API transforms that you write in a Kinesis Data Analytics Studio notebook.</p>"}}, "documentation": "<p>The configuration parameters for the default Amazon Glue database. You use this database for SQL queries that you write in a Kinesis Data Analytics Studio notebook.</p>"}, "CatalogConfigurationDescription": {"type": "structure", "required": ["GlueDataCatalogConfigurationDescription"], "members": {"GlueDataCatalogConfigurationDescription": {"shape": "GlueDataCatalogConfigurationDescription", "documentation": "<p>The configuration parameters for the default Amazon Glue database. You use this database for SQL queries that you write in a Kinesis Data Analytics Studio notebook.</p>"}}, "documentation": "<p>The configuration parameters for the default Amazon Glue database. You use this database for Apache Flink SQL queries and table API transforms that you write in a Kinesis Data Analytics Studio notebook.</p>"}, "CatalogConfigurationUpdate": {"type": "structure", "required": ["GlueDataCatalogConfigurationUpdate"], "members": {"GlueDataCatalogConfigurationUpdate": {"shape": "GlueDataCatalogConfigurationUpdate", "documentation": "<p>Updates to the configuration parameters for the default Amazon Glue database. You use this database for SQL queries that you write in a Kinesis Data Analytics Studio notebook.</p>"}}, "documentation": "<p>Updates to the configuration parameters for the default Amazon Glue database. You use this database for SQL queries that you write in a Kinesis Data Analytics Studio notebook.</p>"}, "CheckpointConfiguration": {"type": "structure", "required": ["ConfigurationType"], "members": {"ConfigurationType": {"shape": "ConfigurationType", "documentation": "<p>Describes whether the application uses Kinesis Data Analytics' default checkpointing behavior. You must set this property to <code>CUSTOM</code> in order to set the <code>CheckpointingEnabled</code>, <code>CheckpointInterval</code>, or <code>MinPauseBetweenCheckpoints</code> parameters.</p> <note> <p>If this value is set to <code>DEFAULT</code>, the application will use the following values, even if they are set to other values using APIs or application code:</p> <ul> <li> <p> <b>CheckpointingEnabled:</b> true</p> </li> <li> <p> <b>CheckpointInterval:</b> 60000</p> </li> <li> <p> <b>MinPauseBetweenCheckpoints:</b> 5000</p> </li> </ul> </note>"}, "CheckpointingEnabled": {"shape": "BooleanObject", "documentation": "<p>Describes whether checkpointing is enabled for a Flink-based Kinesis Data Analytics application.</p> <note> <p>If <code>CheckpointConfiguration.ConfigurationType</code> is <code>DEFAULT</code>, the application will use a <code>CheckpointingEnabled</code> value of <code>true</code>, even if this value is set to another value using this API or in application code.</p> </note>"}, "CheckpointInterval": {"shape": "CheckpointInterval", "documentation": "<p>Describes the interval in milliseconds between checkpoint operations. </p> <note> <p>If <code>CheckpointConfiguration.ConfigurationType</code> is <code>DEFAULT</code>, the application will use a <code>CheckpointInterval</code> value of 60000, even if this value is set to another value using this API or in application code.</p> </note>"}, "MinPauseBetweenCheckpoints": {"shape": "MinPauseBetweenCheckpoints", "documentation": "<p>Describes the minimum time in milliseconds after a checkpoint operation completes that a new checkpoint operation can start. If a checkpoint operation takes longer than the <code>CheckpointInterval</code>, the application otherwise performs continual checkpoint operations. For more information, see <a href=\"https://ci.apache.org/projects/flink/flink-docs-release-1.8/ops/state/large_state_tuning.html#tuning-checkpointing\"> Tuning Checkpointing</a> in the <a href=\"https://ci.apache.org/projects/flink/flink-docs-release-1.8/\">Apache Flink Documentation</a>.</p> <note> <p>If <code>CheckpointConfiguration.ConfigurationType</code> is <code>DEFAULT</code>, the application will use a <code>MinPauseBetweenCheckpoints</code> value of 5000, even if this value is set using this API or in application code.</p> </note>"}}, "documentation": "<p>Describes an application's checkpointing configuration. Checkpointing is the process of persisting application state for fault tolerance. For more information, see <a href=\"https://ci.apache.org/projects/flink/flink-docs-release-1.8/concepts/programming-model.html#checkpoints-for-fault-tolerance\"> Checkpoints for Fault Tolerance</a> in the <a href=\"https://ci.apache.org/projects/flink/flink-docs-release-1.8/\">Apache Flink Documentation</a>.</p>"}, "CheckpointConfigurationDescription": {"type": "structure", "members": {"ConfigurationType": {"shape": "ConfigurationType", "documentation": "<p>Describes whether the application uses the default checkpointing behavior in Kinesis Data Analytics. </p> <note> <p>If this value is set to <code>DEFAULT</code>, the application will use the following values, even if they are set to other values using APIs or application code:</p> <ul> <li> <p> <b>CheckpointingEnabled:</b> true</p> </li> <li> <p> <b>CheckpointInterval:</b> 60000</p> </li> <li> <p> <b>MinPauseBetweenCheckpoints:</b> 5000</p> </li> </ul> </note>"}, "CheckpointingEnabled": {"shape": "BooleanObject", "documentation": "<p>Describes whether checkpointing is enabled for a Flink-based Kinesis Data Analytics application.</p> <note> <p>If <code>CheckpointConfiguration.ConfigurationType</code> is <code>DEFAULT</code>, the application will use a <code>CheckpointingEnabled</code> value of <code>true</code>, even if this value is set to another value using this API or in application code.</p> </note>"}, "CheckpointInterval": {"shape": "CheckpointInterval", "documentation": "<p>Describes the interval in milliseconds between checkpoint operations. </p> <note> <p>If <code>CheckpointConfiguration.ConfigurationType</code> is <code>DEFAULT</code>, the application will use a <code>CheckpointInterval</code> value of 60000, even if this value is set to another value using this API or in application code.</p> </note>"}, "MinPauseBetweenCheckpoints": {"shape": "MinPauseBetweenCheckpoints", "documentation": "<p>Describes the minimum time in milliseconds after a checkpoint operation completes that a new checkpoint operation can start. </p> <note> <p>If <code>CheckpointConfiguration.ConfigurationType</code> is <code>DEFAULT</code>, the application will use a <code>MinPauseBetweenCheckpoints</code> value of 5000, even if this value is set using this API or in application code.</p> </note>"}}, "documentation": "<p>Describes checkpointing parameters for a Flink-based Kinesis Data Analytics application.</p>"}, "CheckpointConfigurationUpdate": {"type": "structure", "members": {"ConfigurationTypeUpdate": {"shape": "ConfigurationType", "documentation": "<p>Describes updates to whether the application uses the default checkpointing behavior of Kinesis Data Analytics. You must set this property to <code>CUSTOM</code> in order to set the <code>CheckpointingEnabled</code>, <code>CheckpointInterval</code>, or <code>MinPauseBetweenCheckpoints</code> parameters. </p> <note> <p>If this value is set to <code>DEFAULT</code>, the application will use the following values, even if they are set to other values using APIs or application code:</p> <ul> <li> <p> <b>CheckpointingEnabled:</b> true</p> </li> <li> <p> <b>CheckpointInterval:</b> 60000</p> </li> <li> <p> <b>MinPauseBetweenCheckpoints:</b> 5000</p> </li> </ul> </note>"}, "CheckpointingEnabledUpdate": {"shape": "BooleanObject", "documentation": "<p>Describes updates to whether checkpointing is enabled for an application.</p> <note> <p>If <code>CheckpointConfiguration.ConfigurationType</code> is <code>DEFAULT</code>, the application will use a <code>CheckpointingEnabled</code> value of <code>true</code>, even if this value is set to another value using this API or in application code.</p> </note>"}, "CheckpointIntervalUpdate": {"shape": "CheckpointInterval", "documentation": "<p>Describes updates to the interval in milliseconds between checkpoint operations.</p> <note> <p>If <code>CheckpointConfiguration.ConfigurationType</code> is <code>DEFAULT</code>, the application will use a <code>CheckpointInterval</code> value of 60000, even if this value is set to another value using this API or in application code.</p> </note>"}, "MinPauseBetweenCheckpointsUpdate": {"shape": "MinPauseBetweenCheckpoints", "documentation": "<p>Describes updates to the minimum time in milliseconds after a checkpoint operation completes that a new checkpoint operation can start.</p> <note> <p>If <code>CheckpointConfiguration.ConfigurationType</code> is <code>DEFAULT</code>, the application will use a <code>MinPauseBetweenCheckpoints</code> value of 5000, even if this value is set using this API or in application code.</p> </note>"}}, "documentation": "<p>Describes updates to the checkpointing parameters for a Flink-based Kinesis Data Analytics application.</p>"}, "CheckpointInterval": {"type": "long", "min": 1}, "CloudWatchLoggingOption": {"type": "structure", "required": ["LogStreamARN"], "members": {"LogStreamARN": {"shape": "LogStreamARN", "documentation": "<p>The ARN of the CloudWatch log to receive application messages.</p>"}}, "documentation": "<p>Provides a description of Amazon CloudWatch logging options, including the log stream Amazon Resource Name (ARN). </p>"}, "CloudWatchLoggingOptionDescription": {"type": "structure", "required": ["LogStreamARN"], "members": {"CloudWatchLoggingOptionId": {"shape": "Id", "documentation": "<p>The ID of the CloudWatch logging option description.</p>"}, "LogStreamARN": {"shape": "LogStreamARN", "documentation": "<p>The Amazon Resource Name (ARN) of the CloudWatch log to receive application messages.</p>"}, "RoleARN": {"shape": "RoleARN", "documentation": "<p>The IAM ARN of the role to use to send application messages. </p> <note> <p>Provided for backward compatibility. Applications created with the current API version have an application-level service execution role rather than a resource-level role.</p> </note>"}}, "documentation": "<p>Describes the Amazon CloudWatch logging option.</p>"}, "CloudWatchLoggingOptionDescriptions": {"type": "list", "member": {"shape": "CloudWatchLoggingOptionDescription"}}, "CloudWatchLoggingOptionUpdate": {"type": "structure", "required": ["CloudWatchLoggingOptionId"], "members": {"CloudWatchLoggingOptionId": {"shape": "Id", "documentation": "<p>The ID of the CloudWatch logging option to update</p>"}, "LogStreamARNUpdate": {"shape": "LogStreamARN", "documentation": "<p>The Amazon Resource Name (ARN) of the CloudWatch log to receive application messages.</p>"}}, "documentation": "<p>Describes the Amazon CloudWatch logging option updates.</p>"}, "CloudWatchLoggingOptionUpdates": {"type": "list", "member": {"shape": "CloudWatchLoggingOptionUpdate"}}, "CloudWatchLoggingOptions": {"type": "list", "member": {"shape": "CloudWatchLoggingOption"}}, "CodeContent": {"type": "structure", "members": {"TextContent": {"shape": "TextContent", "documentation": "<p>The text-format code for a Flink-based Kinesis Data Analytics application.</p>"}, "ZipFileContent": {"shape": "ZipFileContent", "documentation": "<p>The zip-format code for a Flink-based Kinesis Data Analytics application.</p>"}, "S3ContentLocation": {"shape": "S3ContentLocation", "documentation": "<p>Information about the Amazon S3 bucket that contains the application code.</p>"}}, "documentation": "<p>Specifies either the application code, or the location of the application code, for a Flink-based Kinesis Data Analytics application. </p>"}, "CodeContentDescription": {"type": "structure", "members": {"TextContent": {"shape": "TextContent", "documentation": "<p>The text-format code</p>"}, "CodeMD5": {"shape": "CodeMD5", "documentation": "<p>The checksum that can be used to validate zip-format code.</p>"}, "CodeSize": {"shape": "CodeSize", "documentation": "<p>The size in bytes of the application code. Can be used to validate zip-format code.</p>"}, "S3ApplicationCodeLocationDescription": {"shape": "S3ApplicationCodeLocationDescription", "documentation": "<p>The S3 bucket Amazon Resource Name (ARN), file key, and object version of the application code stored in Amazon S3.</p>"}}, "documentation": "<p>Describes details about the code of a Kinesis Data Analytics application.</p>"}, "CodeContentType": {"type": "string", "enum": ["PLAINTEXT", "ZIPFILE"]}, "CodeContentUpdate": {"type": "structure", "members": {"TextContentUpdate": {"shape": "TextContent", "documentation": "<p>Describes an update to the text code for an application.</p>"}, "ZipFileContentUpdate": {"shape": "ZipFileContent", "documentation": "<p>Describes an update to the zipped code for an application.</p>"}, "S3ContentLocationUpdate": {"shape": "S3ContentLocationUpdate", "documentation": "<p>Describes an update to the location of code for an application.</p>"}}, "documentation": "<p>Describes an update to the code of an application. Not supported for Apache Zeppelin.</p>"}, "CodeMD5": {"type": "string", "max": 128, "min": 128}, "CodeSize": {"type": "long", "max": 52428800, "min": 0}, "CodeValidationException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The user-provided application code (query) is not valid. This can be a simple syntax error.</p>", "exception": true}, "ConcurrentModificationException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Exception thrown as a result of concurrent modifications to an application. This error can be the result of attempting to modify an application without using the current application ID.</p>", "exception": true}, "ConditionalToken": {"type": "string", "max": 512, "min": 1, "pattern": "[a-zA-Z0-9-_+/=]+"}, "ConfigurationType": {"type": "string", "enum": ["DEFAULT", "CUSTOM"]}, "CreateApplicationPresignedUrlRequest": {"type": "structure", "required": ["ApplicationName", "UrlType"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of the application.</p>"}, "UrlType": {"shape": "UrlType", "documentation": "<p>The type of the extension for which to create and return a URL. Currently, the only valid extension URL type is <code>FLINK_DASHBOARD_URL</code>. </p>"}, "SessionExpirationDurationInSeconds": {"shape": "SessionExpirationDurationInSeconds", "documentation": "<p>The duration in seconds for which the returned URL will be valid.</p>"}}}, "CreateApplicationPresignedUrlResponse": {"type": "structure", "members": {"AuthorizedUrl": {"shape": "AuthorizedUrl", "documentation": "<p>The URL of the extension.</p>"}}}, "CreateApplicationRequest": {"type": "structure", "required": ["ApplicationName", "RuntimeEnvironment", "ServiceExecutionRole"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of your application (for example, <code>sample-app</code>).</p>"}, "ApplicationDescription": {"shape": "ApplicationDescription", "documentation": "<p>A summary description of the application.</p>"}, "RuntimeEnvironment": {"shape": "RuntimeEnvironment", "documentation": "<p>The runtime environment for the application.</p>"}, "ServiceExecutionRole": {"shape": "RoleARN", "documentation": "<p>The IAM role used by the application to access Kinesis data streams, Kinesis Data Firehose delivery streams, Amazon S3 objects, and other external resources.</p>"}, "ApplicationConfiguration": {"shape": "ApplicationConfiguration", "documentation": "<p>Use this parameter to configure the application.</p>"}, "CloudWatchLoggingOptions": {"shape": "CloudWatchLoggingOptions", "documentation": "<p>Use this parameter to configure an Amazon CloudWatch log stream to monitor application configuration errors. </p>"}, "Tags": {"shape": "Tags", "documentation": "<p>A list of one or more tags to assign to the application. A tag is a key-value pair that identifies an application. Note that the maximum number of application tags includes system tags. The maximum number of user-defined application tags is 50. For more information, see <a href=\"https://docs.aws.amazon.com/kinesisanalytics/latest/java/how-tagging.html\">Using Tagging</a>.</p>"}, "ApplicationMode": {"shape": "ApplicationMode", "documentation": "<p>Use the <code>STREAMING</code> mode to create a Kinesis Data Analytics For Flink application. To create a Kinesis Data Analytics Studio notebook, use the <code>INTERACTIVE</code> mode.</p>"}}}, "CreateApplicationResponse": {"type": "structure", "required": ["ApplicationDetail"], "members": {"ApplicationDetail": {"shape": "ApplicationDetail", "documentation": "<p>In response to your <code>CreateApplication</code> request, Kinesis Data Analytics returns a response with details of the application it created.</p>"}}}, "CreateApplicationSnapshotRequest": {"type": "structure", "required": ["ApplicationName", "SnapshotName"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of an existing application</p>"}, "SnapshotName": {"shape": "SnapshotName", "documentation": "<p>An identifier for the application snapshot.</p>"}}}, "CreateApplicationSnapshotResponse": {"type": "structure", "members": {}}, "CustomArtifactConfiguration": {"type": "structure", "required": ["ArtifactType"], "members": {"ArtifactType": {"shape": "ArtifactType", "documentation": "<p> <code>UDF</code> stands for user-defined functions. This type of artifact must be in an S3 bucket. A <code>DEPENDENCY_JAR</code> can be in either Maven or an S3 bucket.</p>"}, "S3ContentLocation": {"shape": "S3ContentLocation"}, "MavenReference": {"shape": "MavenReference", "documentation": "<p>The parameters required to fully specify a Maven reference.</p>"}}, "documentation": "<p>Specifies dependency JARs, as well as JAR files that contain user-defined functions (UDF).</p>"}, "CustomArtifactConfigurationDescription": {"type": "structure", "members": {"ArtifactType": {"shape": "ArtifactType", "documentation": "<p> <code>UDF</code> stands for user-defined functions. This type of artifact must be in an S3 bucket. A <code>DEPENDENCY_JAR</code> can be in either Maven or an S3 bucket.</p>"}, "S3ContentLocationDescription": {"shape": "S3ContentLocation"}, "MavenReferenceDescription": {"shape": "MavenReference", "documentation": "<p>The parameters that are required to specify a Maven dependency.</p>"}}, "documentation": "<p>Specifies a dependency JAR or a JAR of user-defined functions.</p>"}, "CustomArtifactsConfigurationDescriptionList": {"type": "list", "member": {"shape": "CustomArtifactConfigurationDescription"}, "max": 50}, "CustomArtifactsConfigurationList": {"type": "list", "member": {"shape": "CustomArtifactConfiguration"}, "max": 50}, "DatabaseARN": {"type": "string", "max": 2048, "min": 1, "pattern": "arn:.*"}, "DeleteApplicationCloudWatchLoggingOptionRequest": {"type": "structure", "required": ["ApplicationName", "CloudWatchLoggingOptionId"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The application name.</p>"}, "CurrentApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>The version ID of the application. You must provide the <code>CurrentApplicationVersionId</code> or the <code>ConditionalToken</code>. You can retrieve the application version ID using <a>DescribeApplication</a>. For better concurrency support, use the <code>ConditionalToken</code> parameter instead of <code>CurrentApplicationVersionId</code>.</p>"}, "CloudWatchLoggingOptionId": {"shape": "Id", "documentation": "<p>The <code>CloudWatchLoggingOptionId</code> of the Amazon CloudWatch logging option to delete. You can get the <code>CloudWatchLoggingOptionId</code> by using the <a>DescribeApplication</a> operation. </p>"}, "ConditionalToken": {"shape": "ConditionalToken", "documentation": "<p>A value you use to implement strong concurrency for application updates. You must provide the <code>CurrentApplicationVersionId</code> or the <code>ConditionalToken</code>. You get the application's current <code>ConditionalToken</code> using <a>DescribeApplication</a>. For better concurrency support, use the <code>ConditionalToken</code> parameter instead of <code>CurrentApplicationVersionId</code>.</p>"}}}, "DeleteApplicationCloudWatchLoggingOptionResponse": {"type": "structure", "members": {"ApplicationARN": {"shape": "ResourceARN", "documentation": "<p>The application's Amazon Resource Name (ARN).</p>"}, "ApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>The version ID of the application. Kinesis Data Analytics updates the <code>ApplicationVersionId</code> each time you change the CloudWatch logging options.</p>"}, "CloudWatchLoggingOptionDescriptions": {"shape": "CloudWatchLoggingOptionDescriptions", "documentation": "<p>The descriptions of the remaining CloudWatch logging options for the application.</p>"}}}, "DeleteApplicationInputProcessingConfigurationRequest": {"type": "structure", "required": ["ApplicationName", "CurrentApplicationVersionId", "InputId"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of the application.</p>"}, "CurrentApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>The application version. You can use the <a>DescribeApplication</a> operation to get the current application version. If the version specified is not the current version, the <code>ConcurrentModificationException</code> is returned. </p>"}, "InputId": {"shape": "Id", "documentation": "<p>The ID of the input configuration from which to delete the input processing configuration. You can get a list of the input IDs for an application by using the <a>DescribeApplication</a> operation.</p>"}}}, "DeleteApplicationInputProcessingConfigurationResponse": {"type": "structure", "members": {"ApplicationARN": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the application.</p>"}, "ApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>The current application version ID.</p>"}}}, "DeleteApplicationOutputRequest": {"type": "structure", "required": ["ApplicationName", "CurrentApplicationVersionId", "OutputId"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The application name.</p>"}, "CurrentApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>The application version. You can use the <a>DescribeApplication</a> operation to get the current application version. If the version specified is not the current version, the <code>ConcurrentModificationException</code> is returned. </p>"}, "OutputId": {"shape": "Id", "documentation": "<p>The ID of the configuration to delete. Each output configuration that is added to the application (either when the application is created or later) using the <a>AddApplicationOutput</a> operation has a unique ID. You need to provide the ID to uniquely identify the output configuration that you want to delete from the application configuration. You can use the <a>DescribeApplication</a> operation to get the specific <code>OutputId</code>. </p>"}}}, "DeleteApplicationOutputResponse": {"type": "structure", "members": {"ApplicationARN": {"shape": "ResourceARN", "documentation": "<p>The application Amazon Resource Name (ARN).</p>"}, "ApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>The current application version ID.</p>"}}}, "DeleteApplicationReferenceDataSourceRequest": {"type": "structure", "required": ["ApplicationName", "CurrentApplicationVersionId", "ReferenceId"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of an existing application.</p>"}, "CurrentApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>The current application version. You can use the <a>DescribeApplication</a> operation to get the current application version. If the version specified is not the current version, the <code>ConcurrentModificationException</code> is returned.</p>"}, "ReferenceId": {"shape": "Id", "documentation": "<p>The ID of the reference data source. When you add a reference data source to your application using the <a>AddApplicationReferenceDataSource</a>, Kinesis Data Analytics assigns an ID. You can use the <a>DescribeApplication</a> operation to get the reference ID. </p>"}}}, "DeleteApplicationReferenceDataSourceResponse": {"type": "structure", "members": {"ApplicationARN": {"shape": "ResourceARN", "documentation": "<p>The application Amazon Resource Name (ARN).</p>"}, "ApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>The updated version ID of the application.</p>"}}}, "DeleteApplicationRequest": {"type": "structure", "required": ["ApplicationName", "CreateTimestamp"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of the application to delete.</p>"}, "CreateTimestamp": {"shape": "Timestamp", "documentation": "<p>Use the <code>DescribeApplication</code> operation to get this value.</p>"}}}, "DeleteApplicationResponse": {"type": "structure", "members": {}}, "DeleteApplicationSnapshotRequest": {"type": "structure", "required": ["ApplicationName", "SnapshotName", "SnapshotCreationTimestamp"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of an existing application.</p>"}, "SnapshotName": {"shape": "SnapshotName", "documentation": "<p>The identifier for the snapshot delete.</p>"}, "SnapshotCreationTimestamp": {"shape": "Timestamp", "documentation": "<p>The creation timestamp of the application snapshot to delete. You can retrieve this value using or .</p>"}}}, "DeleteApplicationSnapshotResponse": {"type": "structure", "members": {}}, "DeleteApplicationVpcConfigurationRequest": {"type": "structure", "required": ["ApplicationName", "VpcConfigurationId"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of an existing application.</p>"}, "CurrentApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>The current application version ID. You must provide the <code>CurrentApplicationVersionId</code> or the <code>ConditionalToken</code>. You can retrieve the application version ID using <a>DescribeApplication</a>. For better concurrency support, use the <code>ConditionalToken</code> parameter instead of <code>CurrentApplicationVersionId</code>.</p>"}, "VpcConfigurationId": {"shape": "Id", "documentation": "<p>The ID of the VPC configuration to delete.</p>"}, "ConditionalToken": {"shape": "ConditionalToken", "documentation": "<p>A value you use to implement strong concurrency for application updates. You must provide the <code>CurrentApplicationVersionId</code> or the <code>ConditionalToken</code>. You get the application's current <code>ConditionalToken</code> using <a>DescribeApplication</a>. For better concurrency support, use the <code>ConditionalToken</code> parameter instead of <code>CurrentApplicationVersionId</code>.</p>"}}}, "DeleteApplicationVpcConfigurationResponse": {"type": "structure", "members": {"ApplicationARN": {"shape": "ResourceARN", "documentation": "<p>The ARN of the Kinesis Data Analytics application.</p>"}, "ApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>The updated version ID of the application.</p>"}}}, "DeployAsApplicationConfiguration": {"type": "structure", "required": ["S3ContentLocation"], "members": {"S3ContentLocation": {"shape": "S3ContentBaseLocation", "documentation": "<p>The description of an Amazon S3 object that contains the Amazon Data Analytics application, including the Amazon Resource Name (ARN) of the S3 bucket, the name of the Amazon S3 object that contains the data, and the version number of the Amazon S3 object that contains the data. </p>"}}, "documentation": "<p>The information required to deploy a Kinesis Data Analytics Studio notebook as an application with durable state.</p>"}, "DeployAsApplicationConfigurationDescription": {"type": "structure", "required": ["S3ContentLocationDescription"], "members": {"S3ContentLocationDescription": {"shape": "S3ContentBaseLocationDescription", "documentation": "<p>The location that holds the data required to specify an Amazon Data Analytics application.</p>"}}, "documentation": "<p>The configuration information required to deploy an Amazon Data Analytics Studio notebook as an application with durable state.</p>"}, "DeployAsApplicationConfigurationUpdate": {"type": "structure", "members": {"S3ContentLocationUpdate": {"shape": "S3ContentBaseLocationUpdate", "documentation": "<p>Updates to the location that holds the data required to specify an Amazon Data Analytics application.</p>"}}, "documentation": "<p>Updates to the configuration information required to deploy an Amazon Data Analytics Studio notebook as an application with durable state.</p>"}, "DescribeApplicationRequest": {"type": "structure", "required": ["ApplicationName"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of the application.</p>"}, "IncludeAdditionalDetails": {"shape": "BooleanObject", "documentation": "<p>Displays verbose information about a Kinesis Data Analytics application, including the application's job plan.</p>"}}}, "DescribeApplicationResponse": {"type": "structure", "required": ["ApplicationDetail"], "members": {"ApplicationDetail": {"shape": "ApplicationDetail", "documentation": "<p>Provides a description of the application, such as the application's Amazon Resource Name (ARN), status, and latest version.</p>"}}}, "DescribeApplicationSnapshotRequest": {"type": "structure", "required": ["ApplicationName", "SnapshotName"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of an existing application.</p>"}, "SnapshotName": {"shape": "SnapshotName", "documentation": "<p>The identifier of an application snapshot. You can retrieve this value using .</p>"}}}, "DescribeApplicationSnapshotResponse": {"type": "structure", "required": ["SnapshotDetails"], "members": {"SnapshotDetails": {"shape": "SnapshotDetails", "documentation": "<p>An object containing information about the application snapshot.</p>"}}}, "DescribeApplicationVersionRequest": {"type": "structure", "required": ["ApplicationName", "ApplicationVersionId"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of the application for which you want to get the version description.</p>"}, "ApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>The ID of the application version for which you want to get the description.</p>"}}}, "DescribeApplicationVersionResponse": {"type": "structure", "members": {"ApplicationVersionDetail": {"shape": "ApplicationDetail"}}}, "DestinationSchema": {"type": "structure", "required": ["RecordFormatType"], "members": {"RecordFormatType": {"shape": "RecordFormatType", "documentation": "<p>Specifies the format of the records on the output stream.</p>"}}, "documentation": "<p>Describes the data format when records are written to the destination in a SQL-based Kinesis Data Analytics application. </p>"}, "DiscoverInputSchemaRequest": {"type": "structure", "required": ["ServiceExecutionRole"], "members": {"ResourceARN": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the streaming source.</p>"}, "ServiceExecutionRole": {"shape": "RoleARN", "documentation": "<p>The ARN of the role that is used to access the streaming source.</p>"}, "InputStartingPositionConfiguration": {"shape": "InputStartingPositionConfiguration", "documentation": "<p>The point at which you want Kinesis Data Analytics to start reading records from the specified streaming source discovery purposes.</p>"}, "S3Configuration": {"shape": "S3Configuration", "documentation": "<p>Specify this parameter to discover a schema from data in an Amazon S3 object.</p>"}, "InputProcessingConfiguration": {"shape": "InputProcessingConfiguration", "documentation": "<p>The <a>InputProcessingConfiguration</a> to use to preprocess the records before discovering the schema of the records.</p>"}}}, "DiscoverInputSchemaResponse": {"type": "structure", "members": {"InputSchema": {"shape": "SourceSchema", "documentation": "<p>The schema inferred from the streaming source. It identifies the format of the data in the streaming source and how each data element maps to corresponding columns in the in-application stream that you can create.</p>"}, "ParsedInputRecords": {"shape": "ParsedInputRecords", "documentation": "<p>An array of elements, where each element corresponds to a row in a stream record (a stream record can have more than one row).</p>"}, "ProcessedInputRecords": {"shape": "ProcessedInputRecords", "documentation": "<p>The stream data that was modified by the processor specified in the <code>InputProcessingConfiguration</code> parameter.</p>"}, "RawInputRecords": {"shape": "RawInputRecords", "documentation": "<p>The raw stream data that was sampled to infer the schema.</p>"}}}, "EnvironmentProperties": {"type": "structure", "required": ["PropertyGroups"], "members": {"PropertyGroups": {"shape": "PropertyGroups", "documentation": "<p>Describes the execution property groups.</p>"}}, "documentation": "<p>Describes execution properties for a Flink-based Kinesis Data Analytics application.</p>"}, "EnvironmentPropertyDescriptions": {"type": "structure", "members": {"PropertyGroupDescriptions": {"shape": "PropertyGroups", "documentation": "<p>Describes the execution property groups.</p>"}}, "documentation": "<p>Describes the execution properties for an Apache Flink runtime.</p>"}, "EnvironmentPropertyUpdates": {"type": "structure", "required": ["PropertyGroups"], "members": {"PropertyGroups": {"shape": "PropertyGroups", "documentation": "<p>Describes updates to the execution property groups.</p>"}}, "documentation": "<p>Describes updates to the execution property groups for a Flink-based Kinesis Data Analytics application or a Studio notebook.</p>"}, "ErrorMessage": {"type": "string"}, "FileKey": {"type": "string", "max": 1024, "min": 1}, "FlinkApplicationConfiguration": {"type": "structure", "members": {"CheckpointConfiguration": {"shape": "CheckpointConfiguration", "documentation": "<p>Describes an application's checkpointing configuration. Checkpointing is the process of persisting application state for fault tolerance. For more information, see <a href=\"https://ci.apache.org/projects/flink/flink-docs-release-1.8/concepts/programming-model.html#checkpoints-for-fault-tolerance\"> Checkpoints for Fault Tolerance</a> in the <a href=\"https://ci.apache.org/projects/flink/flink-docs-release-1.8/\">Apache Flink Documentation</a>. </p>"}, "MonitoringConfiguration": {"shape": "MonitoringConfiguration", "documentation": "<p>Describes configuration parameters for Amazon CloudWatch logging for an application.</p>"}, "ParallelismConfiguration": {"shape": "ParallelismConfiguration", "documentation": "<p>Describes parameters for how an application executes multiple tasks simultaneously.</p>"}}, "documentation": "<p>Describes configuration parameters for a Flink-based Kinesis Data Analytics application or a Studio notebook.</p>"}, "FlinkApplicationConfigurationDescription": {"type": "structure", "members": {"CheckpointConfigurationDescription": {"shape": "CheckpointConfigurationDescription", "documentation": "<p>Describes an application's checkpointing configuration. Checkpointing is the process of persisting application state for fault tolerance.</p>"}, "MonitoringConfigurationDescription": {"shape": "MonitoringConfigurationDescription", "documentation": "<p>Describes configuration parameters for Amazon CloudWatch logging for an application.</p>"}, "ParallelismConfigurationDescription": {"shape": "ParallelismConfigurationDescription", "documentation": "<p>Describes parameters for how an application executes multiple tasks simultaneously.</p>"}, "JobPlanDescription": {"shape": "JobPlanDescription", "documentation": "<p>The job plan for an application. For more information about the job plan, see <a href=\"https://ci.apache.org/projects/flink/flink-docs-release-1.8/internals/job_scheduling.html\">Jobs and Scheduling</a> in the <a href=\"https://ci.apache.org/projects/flink/flink-docs-release-1.8/\">Apache Flink Documentation</a>. To retrieve the job plan for the application, use the <a>DescribeApplicationRequest$IncludeAdditionalDetails</a> parameter of the <a>DescribeApplication</a> operation.</p>"}}, "documentation": "<p>Describes configuration parameters for a Flink-based Kinesis Data Analytics application.</p>"}, "FlinkApplicationConfigurationUpdate": {"type": "structure", "members": {"CheckpointConfigurationUpdate": {"shape": "CheckpointConfigurationUpdate", "documentation": "<p>Describes updates to an application's checkpointing configuration. Checkpointing is the process of persisting application state for fault tolerance.</p>"}, "MonitoringConfigurationUpdate": {"shape": "MonitoringConfigurationUpdate", "documentation": "<p>Describes updates to the configuration parameters for Amazon CloudWatch logging for an application.</p>"}, "ParallelismConfigurationUpdate": {"shape": "ParallelismConfigurationUpdate", "documentation": "<p>Describes updates to the parameters for how an application executes multiple tasks simultaneously.</p>"}}, "documentation": "<p>Describes updates to the configuration parameters for a Flink-based Kinesis Data Analytics application.</p>"}, "FlinkRunConfiguration": {"type": "structure", "members": {"AllowNonRestoredState": {"shape": "BooleanObject", "documentation": "<p>When restoring from a snapshot, specifies whether the runtime is allowed to skip a state that cannot be mapped to the new program. This will happen if the program is updated between snapshots to remove stateful parameters, and state data in the snapshot no longer corresponds to valid application data. For more information, see <a href=\"https://ci.apache.org/projects/flink/flink-docs-release-1.8/ops/state/savepoints.html#allowing-non-restored-state\"> Allowing Non-Restored State</a> in the <a href=\"https://ci.apache.org/projects/flink/flink-docs-release-1.8/\">Apache Flink documentation</a>.</p> <note> <p>This value defaults to <code>false</code>. If you update your application without specifying this parameter, <code>AllowNonRestoredState</code> will be set to <code>false</code>, even if it was previously set to <code>true</code>.</p> </note>"}}, "documentation": "<p>Describes the starting parameters for a Flink-based Kinesis Data Analytics application.</p>"}, "GlueDataCatalogConfiguration": {"type": "structure", "required": ["DatabaseARN"], "members": {"DatabaseARN": {"shape": "DatabaseARN", "documentation": "<p>The Amazon Resource Name (ARN) of the database.</p>"}}, "documentation": "<p>The configuration of the Glue Data Catalog that you use for Apache Flink SQL queries and table API transforms that you write in an application.</p>"}, "GlueDataCatalogConfigurationDescription": {"type": "structure", "required": ["DatabaseARN"], "members": {"DatabaseARN": {"shape": "DatabaseARN", "documentation": "<p>The Amazon Resource Name (ARN) of the database.</p>"}}, "documentation": "<p>The configuration of the Glue Data Catalog that you use for Apache Flink SQL queries and table API transforms that you write in an application.</p>"}, "GlueDataCatalogConfigurationUpdate": {"type": "structure", "required": ["DatabaseARNUpdate"], "members": {"DatabaseARNUpdate": {"shape": "DatabaseARN", "documentation": "<p>The updated Amazon Resource Name (ARN) of the database.</p>"}}, "documentation": "<p>Updates to the configuration of the Glue Data Catalog that you use for SQL queries that you write in a Kinesis Data Analytics Studio notebook.</p>"}, "Id": {"type": "string", "max": 50, "min": 1, "pattern": "[a-zA-Z0-9_.-]+"}, "InAppStreamName": {"type": "string", "max": 32, "min": 1, "pattern": "[^-\\s<>&]*"}, "InAppStreamNames": {"type": "list", "member": {"shape": "InAppStreamName"}}, "InAppTableName": {"type": "string", "max": 32, "min": 1}, "Input": {"type": "structure", "required": ["NamePrefix", "InputSchema"], "members": {"NamePrefix": {"shape": "InAppStreamName", "documentation": "<p>The name prefix to use when creating an in-application stream. Suppose that you specify a prefix \"<code>MyInApplicationStream</code>.\" Kinesis Data Analytics then creates one or more (as per the <code>InputParallelism</code> count you specified) in-application streams with the names \"<code>MyInApplicationStream_001</code>,\" \"<code>MyInApplicationStream_002</code>,\" and so on. </p>"}, "InputProcessingConfiguration": {"shape": "InputProcessingConfiguration", "documentation": "<p>The <a>InputProcessingConfiguration</a> for the input. An input processor transforms records as they are received from the stream, before the application's SQL code executes. Currently, the only input processing configuration available is <a>InputLambdaProcessor</a>. </p>"}, "KinesisStreamsInput": {"shape": "KinesisStreamsInput", "documentation": "<p>If the streaming source is an Amazon Kinesis data stream, identifies the stream's Amazon Resource Name (ARN). </p>"}, "KinesisFirehoseInput": {"shape": "KinesisFirehoseInput", "documentation": "<p>If the streaming source is an Amazon Kinesis Data Firehose delivery stream, identifies the delivery stream's ARN.</p>"}, "InputParallelism": {"shape": "InputParallelism", "documentation": "<p>Describes the number of in-application streams to create. </p>"}, "InputSchema": {"shape": "SourceSchema", "documentation": "<p>Describes the format of the data in the streaming source, and how each data element maps to corresponding columns in the in-application stream that is being created.</p> <p>Also used to describe the format of the reference data source.</p>"}}, "documentation": "<p>When you configure the application input for a SQL-based Kinesis Data Analytics application, you specify the streaming source, the in-application stream name that is created, and the mapping between the two. </p>"}, "InputDescription": {"type": "structure", "members": {"InputId": {"shape": "Id", "documentation": "<p>The input ID that is associated with the application input. This is the ID that Kinesis Data Analytics assigns to each input configuration that you add to your application. </p>"}, "NamePrefix": {"shape": "InAppStreamName", "documentation": "<p>The in-application name prefix.</p>"}, "InAppStreamNames": {"shape": "InAppStreamNames", "documentation": "<p>Returns the in-application stream names that are mapped to the stream source. </p>"}, "InputProcessingConfigurationDescription": {"shape": "InputProcessingConfigurationDescription", "documentation": "<p>The description of the preprocessor that executes on records in this input before the application's code is run. </p>"}, "KinesisStreamsInputDescription": {"shape": "KinesisStreamsInputDescription", "documentation": "<p>If a Kinesis data stream is configured as a streaming source, provides the Kinesis data stream's Amazon Resource Name (ARN). </p>"}, "KinesisFirehoseInputDescription": {"shape": "KinesisFirehoseInputDescription", "documentation": "<p>If a Kinesis Data Firehose delivery stream is configured as a streaming source, provides the delivery stream's ARN. </p>"}, "InputSchema": {"shape": "SourceSchema", "documentation": "<p>Describes the format of the data in the streaming source, and how each data element maps to corresponding columns in the in-application stream that is being created. </p>"}, "InputParallelism": {"shape": "InputParallelism", "documentation": "<p>Describes the configured parallelism (number of in-application streams mapped to the streaming source). </p>"}, "InputStartingPositionConfiguration": {"shape": "InputStartingPositionConfiguration", "documentation": "<p>The point at which the application is configured to read from the input stream.</p>"}}, "documentation": "<p>Describes the application input configuration for a SQL-based Kinesis Data Analytics application. </p>"}, "InputDescriptions": {"type": "list", "member": {"shape": "InputDescription"}}, "InputLambdaProcessor": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "ResourceARN", "documentation": "<p>The ARN of the Amazon Lambda function that operates on records in the stream.</p> <note> <p>To specify an earlier version of the Lambda function than the latest, include the Lambda function version in the Lambda function ARN. For more information about Lambda ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html#arn-syntax-lambda\">Example ARNs: Amazon Lambda</a> </p> </note>"}}, "documentation": "<p>An object that contains the Amazon Resource Name (ARN) of the Amazon Lambda function that is used to preprocess records in the stream in a SQL-based Kinesis Data Analytics application. </p>"}, "InputLambdaProcessorDescription": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "ResourceARN", "documentation": "<p>The ARN of the Amazon Lambda function that is used to preprocess the records in the stream.</p> <note> <p>To specify an earlier version of the Lambda function than the latest, include the Lambda function version in the Lambda function ARN. For more information about Lambda ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html#arn-syntax-lambda\">Example ARNs: Amazon Lambda</a> </p> </note>"}, "RoleARN": {"shape": "RoleARN", "documentation": "<p>The ARN of the IAM role that is used to access the Amazon Lambda function.</p> <note> <p>Provided for backward compatibility. Applications that are created with the current API version have an application-level service execution role rather than a resource-level role.</p> </note>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application, an object that contains the Amazon Resource Name (ARN) of the Amazon Lambda function that is used to preprocess records in the stream.</p>"}, "InputLambdaProcessorUpdate": {"type": "structure", "required": ["ResourceARNUpdate"], "members": {"ResourceARNUpdate": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the new Amazon Lambda function that is used to preprocess the records in the stream.</p> <note> <p>To specify an earlier version of the Lambda function than the latest, include the Lambda function version in the Lambda function ARN. For more information about Lambda ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html#arn-syntax-lambda\">Example ARNs: Amazon Lambda</a> </p> </note>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application, represents an update to the <a>InputLambdaProcessor</a> that is used to preprocess the records in the stream.</p>"}, "InputParallelism": {"type": "structure", "members": {"Count": {"shape": "InputParallelismCount", "documentation": "<p>The number of in-application streams to create.</p>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application, describes the number of in-application streams to create for a given streaming source. </p>"}, "InputParallelismCount": {"type": "integer", "max": 64, "min": 1}, "InputParallelismUpdate": {"type": "structure", "required": ["CountUpdate"], "members": {"CountUpdate": {"shape": "InputParallelismCount", "documentation": "<p>The number of in-application streams to create for the specified streaming source.</p>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application, provides updates to the parallelism count.</p>"}, "InputProcessingConfiguration": {"type": "structure", "required": ["InputLambdaProcessor"], "members": {"InputLambdaProcessor": {"shape": "InputLambdaProcessor", "documentation": "<p>The <a>InputLambdaProcessor</a> that is used to preprocess the records in the stream before being processed by your application code.</p>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application, describes a processor that is used to preprocess the records in the stream before being processed by your application code. Currently, the only input processor available is <a href=\"https://docs.aws.amazon.com/lambda/\">Amazon Lambda</a>.</p>"}, "InputProcessingConfigurationDescription": {"type": "structure", "members": {"InputLambdaProcessorDescription": {"shape": "InputLambdaProcessorDescription", "documentation": "<p>Provides configuration information about the associated <a>InputLambdaProcessorDescription</a> </p>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application, provides the configuration information about an input processor. Currently, the only input processor available is <a href=\"https://docs.aws.amazon.com/lambda/\">Amazon Lambda</a>.</p>"}, "InputProcessingConfigurationUpdate": {"type": "structure", "required": ["InputLambdaProcessorUpdate"], "members": {"InputLambdaProcessorUpdate": {"shape": "InputLambdaProcessorUpdate", "documentation": "<p>Provides update information for an <a>InputLambdaProcessor</a>.</p>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application, describes updates to an <a>InputProcessingConfiguration</a>.</p>"}, "InputSchemaUpdate": {"type": "structure", "members": {"RecordFormatUpdate": {"shape": "RecordFormat", "documentation": "<p>Specifies the format of the records on the streaming source.</p>"}, "RecordEncodingUpdate": {"shape": "RecordEncoding", "documentation": "<p>Specifies the encoding of the records in the streaming source; for example, UTF-8.</p>"}, "RecordColumnUpdates": {"shape": "RecordColumns", "documentation": "<p>A list of <code>RecordColumn</code> objects. Each object describes the mapping of the streaming source element to the corresponding column in the in-application stream.</p>"}}, "documentation": "<p>Describes updates for an SQL-based Kinesis Data Analytics application's input schema.</p>"}, "InputStartingPosition": {"type": "string", "enum": ["NOW", "TRIM_HORIZON", "LAST_STOPPED_POINT"]}, "InputStartingPositionConfiguration": {"type": "structure", "members": {"InputStartingPosition": {"shape": "InputStartingPosition", "documentation": "<p>The starting position on the stream.</p> <ul> <li> <p> <code>NOW</code> - Start reading just after the most recent record in the stream, and start at the request timestamp that the customer issued.</p> </li> <li> <p> <code>TRIM_HORIZON</code> - Start reading at the last untrimmed record in the stream, which is the oldest record available in the stream. This option is not available for an Amazon Kinesis Data Firehose delivery stream.</p> </li> <li> <p> <code>LAST_STOPPED_POINT</code> - Resume reading from where the application last stopped reading.</p> </li> </ul>"}}, "documentation": "<p>Describes the point at which the application reads from the streaming source.</p>"}, "InputUpdate": {"type": "structure", "required": ["InputId"], "members": {"InputId": {"shape": "Id", "documentation": "<p>The input ID of the application input to be updated.</p>"}, "NamePrefixUpdate": {"shape": "InAppStreamName", "documentation": "<p>The name prefix for in-application streams that Kinesis Data Analytics creates for the specific streaming source.</p>"}, "InputProcessingConfigurationUpdate": {"shape": "InputProcessingConfigurationUpdate", "documentation": "<p>Describes updates to an <a>InputProcessingConfiguration</a>.</p>"}, "KinesisStreamsInputUpdate": {"shape": "KinesisStreamsInputUpdate", "documentation": "<p>If a Kinesis data stream is the streaming source to be updated, provides an updated stream Amazon Resource Name (ARN).</p>"}, "KinesisFirehoseInputUpdate": {"shape": "KinesisFirehoseInputUpdate", "documentation": "<p>If a Kinesis Data Firehose delivery stream is the streaming source to be updated, provides an updated stream ARN.</p>"}, "InputSchemaUpdate": {"shape": "InputSchemaUpdate", "documentation": "<p>Describes the data format on the streaming source, and how record elements on the streaming source map to columns of the in-application stream that is created.</p>"}, "InputParallelismUpdate": {"shape": "InputParallelismUpdate", "documentation": "<p>Describes the parallelism updates (the number of in-application streams Kinesis Data Analytics creates for the specific streaming source).</p>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application, describes updates to a specific input configuration (identified by the <code>InputId</code> of an application). </p>"}, "InputUpdates": {"type": "list", "member": {"shape": "InputUpdate"}}, "Inputs": {"type": "list", "member": {"shape": "Input"}}, "InvalidApplicationConfigurationException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The user-provided application configuration is not valid.</p>", "exception": true}, "InvalidArgumentException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The specified input parameter value is not valid.</p>", "exception": true}, "InvalidRequestException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The request JSON is not valid for the operation.</p>", "exception": true}, "JSONMappingParameters": {"type": "structure", "required": ["RecordRowPath"], "members": {"RecordRowPath": {"shape": "RecordRowPath", "documentation": "<p>The path to the top-level parent that contains the records.</p>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application, provides additional mapping information when JSON is the record format on the streaming source.</p>"}, "JobPlanDescription": {"type": "string"}, "KinesisAnalyticsARN": {"type": "string", "max": 2048, "min": 1, "pattern": "arn:.*"}, "KinesisFirehoseInput": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the delivery stream.</p>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application, identifies a Kinesis Data Firehose delivery stream as the streaming source. You provide the delivery stream's Amazon Resource Name (ARN).</p>"}, "KinesisFirehoseInputDescription": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the delivery stream.</p>"}, "RoleARN": {"shape": "RoleARN", "documentation": "<p>The ARN of the IAM role that Kinesis Data Analytics assumes to access the stream.</p> <note> <p>Provided for backward compatibility. Applications that are created with the current API version have an application-level service execution role rather than a resource-level role.</p> </note>"}}, "documentation": "<p>Describes the Amazon Kinesis Data Firehose delivery stream that is configured as the streaming source in the application input configuration. </p>"}, "KinesisFirehoseInputUpdate": {"type": "structure", "required": ["ResourceARNUpdate"], "members": {"ResourceARNUpdate": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the input delivery stream to read.</p>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application, when updating application input configuration, provides information about a Kinesis Data Firehose delivery stream as the streaming source.</p>"}, "KinesisFirehoseOutput": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "ResourceARN", "documentation": "<p>The ARN of the destination delivery stream to write to.</p>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application, when configuring application output, identifies a Kinesis Data Firehose delivery stream as the destination. You provide the stream Amazon Resource Name (ARN) of the delivery stream. </p>"}, "KinesisFirehoseOutputDescription": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the delivery stream.</p>"}, "RoleARN": {"shape": "RoleARN", "documentation": "<p>The ARN of the IAM role that Kinesis Data Analytics can assume to access the stream.</p> <note> <p>Provided for backward compatibility. Applications that are created with the current API version have an application-level service execution role rather than a resource-level role.</p> </note>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application's output, describes the Kinesis Data Firehose delivery stream that is configured as its destination.</p>"}, "KinesisFirehoseOutputUpdate": {"type": "structure", "required": ["ResourceARNUpdate"], "members": {"ResourceARNUpdate": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the delivery stream to write to. </p>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application, when updating an output configuration using the <a>UpdateApplication</a> operation, provides information about a Kinesis Data Firehose delivery stream that is configured as the destination.</p>"}, "KinesisStreamsInput": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "ResourceARN", "documentation": "<p>The ARN of the input Kinesis data stream to read.</p>"}}, "documentation": "<p> Identifies a Kinesis data stream as the streaming source. You provide the stream's Amazon Resource Name (ARN).</p>"}, "KinesisStreamsInputDescription": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the Kinesis data stream.</p>"}, "RoleARN": {"shape": "RoleARN", "documentation": "<p>The ARN of the IAM role that Kinesis Data Analytics can assume to access the stream.</p> <note> <p>Provided for backward compatibility. Applications that are created with the current API version have an application-level service execution role rather than a resource-level role.</p> </note>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application, describes the Kinesis data stream that is configured as the streaming source in the application input configuration. </p>"}, "KinesisStreamsInputUpdate": {"type": "structure", "required": ["ResourceARNUpdate"], "members": {"ResourceARNUpdate": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the input Kinesis data stream to read.</p>"}}, "documentation": "<p>When you update the input configuration for a SQL-based Kinesis Data Analytics application, provides information about a Kinesis stream as the streaming source.</p>"}, "KinesisStreamsOutput": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "ResourceARN", "documentation": "<p>The ARN of the destination Kinesis data stream to write to.</p>"}}, "documentation": "<p>When you configure a SQL-based Kinesis Data Analytics application's output, identifies a Kinesis data stream as the destination. You provide the stream Amazon Resource Name (ARN). </p>"}, "KinesisStreamsOutputDescription": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the Kinesis data stream.</p>"}, "RoleARN": {"shape": "RoleARN", "documentation": "<p>The ARN of the IAM role that Kinesis Data Analytics can assume to access the stream.</p> <note> <p>Provided for backward compatibility. Applications that are created with the current API version have an application-level service execution role rather than a resource-level role.</p> </note>"}}, "documentation": "<p>For an SQL-based Kinesis Data Analytics application's output, describes the Kinesis data stream that is configured as its destination. </p>"}, "KinesisStreamsOutputUpdate": {"type": "structure", "required": ["ResourceARNUpdate"], "members": {"ResourceARNUpdate": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the Kinesis data stream where you want to write the output.</p>"}}, "documentation": "<p>When you update a SQL-based Kinesis Data Analytics application's output configuration using the <a>UpdateApplication</a> operation, provides information about a Kinesis data stream that is configured as the destination.</p>"}, "LambdaOutput": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the destination Lambda function to write to.</p> <note> <p>To specify an earlier version of the Lambda function than the latest, include the Lambda function version in the Lambda function ARN. For more information about Lambda ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html#arn-syntax-lambda\">Example ARNs: Amazon Lambda</a> </p> </note>"}}, "documentation": "<p>When you configure a SQL-based Kinesis Data Analytics application's output, identifies an Amazon Lambda function as the destination. You provide the function Amazon Resource Name (ARN) of the Lambda function. </p>"}, "LambdaOutputDescription": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the destination Lambda function.</p>"}, "RoleARN": {"shape": "RoleARN", "documentation": "<p>The ARN of the IAM role that Kinesis Data Analytics can assume to write to the destination function.</p> <note> <p>Provided for backward compatibility. Applications that are created with the current API version have an application-level service execution role rather than a resource-level role.</p> </note>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application's output, describes the Amazon Lambda function that is configured as its destination. </p>"}, "LambdaOutputUpdate": {"type": "structure", "required": ["ResourceARNUpdate"], "members": {"ResourceARNUpdate": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the destination Amazon Lambda function.</p> <note> <p>To specify an earlier version of the Lambda function than the latest, include the Lambda function version in the Lambda function ARN. For more information about Lambda ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html#arn-syntax-lambda\">Example ARNs: Amazon Lambda</a> </p> </note>"}}, "documentation": "<p>When you update an SQL-based Kinesis Data Analytics application's output configuration using the <a>UpdateApplication</a> operation, provides information about an Amazon Lambda function that is configured as the destination.</p>"}, "LimitExceededException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The number of allowed resources has been exceeded.</p>", "exception": true}, "ListApplicationSnapshotsRequest": {"type": "structure", "required": ["ApplicationName"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of an existing application.</p>"}, "Limit": {"shape": "ListSnapshotsInputLimit", "documentation": "<p>The maximum number of application snapshots to list.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Use this parameter if you receive a <code>NextToken</code> response in a previous request that indicates that there is more output available. Set it to the value of the previous call's <code>NextToken</code> response to indicate where the output should continue from. </p>"}}}, "ListApplicationSnapshotsResponse": {"type": "structure", "members": {"SnapshotSummaries": {"shape": "SnapshotSummaries", "documentation": "<p>A collection of objects containing information about the application snapshots.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results, or <code>null</code> if there are no additional results.</p>"}}}, "ListApplicationVersionsInputLimit": {"type": "integer", "max": 50, "min": 1}, "ListApplicationVersionsRequest": {"type": "structure", "required": ["ApplicationName"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of the application for which you want to list all versions.</p>"}, "Limit": {"shape": "ListApplicationVersionsInputLimit", "documentation": "<p>The maximum number of versions to list in this invocation of the operation.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If a previous invocation of this operation returned a pagination token, pass it into this value to retrieve the next set of results. For more information about pagination, see <a href=\"https://docs.aws.amazon.com/cli/latest/userguide/pagination.html\">Using the Amazon Command Line Interface's Pagination Options</a>.</p>"}}}, "ListApplicationVersionsResponse": {"type": "structure", "members": {"ApplicationVersionSummaries": {"shape": "ApplicationVersionSummaries", "documentation": "<p>A list of the application versions and the associated configuration summaries. The list includes application versions that were rolled back.</p> <p>To get the complete description of a specific application version, invoke the <a>DescribeApplicationVersion</a> operation.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The pagination token for the next set of results, or <code>null</code> if there are no additional results. To retrieve the next set of items, pass this token into a subsequent invocation of this operation. For more information about pagination, see <a href=\"https://docs.aws.amazon.com/cli/latest/userguide/pagination.html\">Using the Amazon Command Line Interface's Pagination Options</a>.</p>"}}}, "ListApplicationsInputLimit": {"type": "integer", "max": 50, "min": 1}, "ListApplicationsRequest": {"type": "structure", "members": {"Limit": {"shape": "ListApplicationsInputLimit", "documentation": "<p>The maximum number of applications to list.</p>"}, "NextToken": {"shape": "ApplicationName", "documentation": "<p>If a previous command returned a pagination token, pass it into this value to retrieve the next set of results. For more information about pagination, see <a href=\"https://docs.aws.amazon.com/cli/latest/userguide/pagination.html\">Using the Amazon Command Line Interface's Pagination Options</a>.</p>"}}}, "ListApplicationsResponse": {"type": "structure", "required": ["ApplicationSummaries"], "members": {"ApplicationSummaries": {"shape": "ApplicationSummaries", "documentation": "<p>A list of <code>ApplicationSummary</code> objects.</p>"}, "NextToken": {"shape": "ApplicationName", "documentation": "<p>The pagination token for the next set of results, or <code>null</code> if there are no additional results. Pass this token into a subsequent command to retrieve the next set of items For more information about pagination, see <a href=\"https://docs.aws.amazon.com/cli/latest/userguide/pagination.html\">Using the Amazon Command Line Interface's Pagination Options</a>.</p>"}}}, "ListSnapshotsInputLimit": {"type": "integer", "max": 50, "min": 1}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "KinesisAnalyticsARN", "documentation": "<p>The ARN of the application for which to retrieve tags.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "Tags", "documentation": "<p>The key-value tags assigned to the application.</p>"}}}, "LogLevel": {"type": "string", "enum": ["INFO", "WARN", "ERROR", "DEBUG"]}, "LogStreamARN": {"type": "string", "max": 2048, "min": 1, "pattern": "arn:.*"}, "MappingParameters": {"type": "structure", "members": {"JSONMappingParameters": {"shape": "JSONMappingParameters", "documentation": "<p>Provides additional mapping information when JSON is the record format on the streaming source.</p>"}, "CSVMappingParameters": {"shape": "CSVMappingParameters", "documentation": "<p>Provides additional mapping information when the record format uses delimiters (for example, CSV).</p>"}}, "documentation": "<p>When you configure a SQL-based Kinesis Data Analytics application's input at the time of creating or updating an application, provides additional mapping information specific to the record format (such as JSON, CSV, or record fields delimited by some delimiter) on the streaming source.</p>"}, "MavenArtifactId": {"type": "string", "max": 256, "min": 1, "pattern": "[a-zA-Z0-9_.-]+"}, "MavenGroupId": {"type": "string", "max": 256, "min": 1, "pattern": "[a-zA-Z0-9_.-]+"}, "MavenReference": {"type": "structure", "required": ["GroupId", "ArtifactId", "Version"], "members": {"GroupId": {"shape": "MavenGroupId", "documentation": "<p>The group ID of the Maven reference.</p>"}, "ArtifactId": {"shape": "MavenArtifactId", "documentation": "<p>The artifact ID of the Maven reference.</p>"}, "Version": {"shape": "MavenVersion", "documentation": "<p>The version of the <PERSON>ven reference.</p>"}}, "documentation": "<p>The information required to specify a Maven reference. You can use Maven references to specify dependency JAR files.</p>"}, "MavenVersion": {"type": "string", "max": 256, "min": 1, "pattern": "[a-zA-Z0-9_.-]+"}, "MetricsLevel": {"type": "string", "enum": ["APPLICATION", "TASK", "OPERATOR", "PARALLELISM"]}, "MinPauseBetweenCheckpoints": {"type": "long", "min": 0}, "MonitoringConfiguration": {"type": "structure", "required": ["ConfigurationType"], "members": {"ConfigurationType": {"shape": "ConfigurationType", "documentation": "<p>Describes whether to use the default CloudWatch logging configuration for an application. You must set this property to <code>CUSTOM</code> in order to set the <code>LogLevel</code> or <code>MetricsLevel</code> parameters.</p>"}, "MetricsLevel": {"shape": "MetricsLevel", "documentation": "<p>Describes the granularity of the CloudWatch Logs for an application. The <code>Parallelism</code> level is not recommended for applications with a Parallelism over 64 due to excessive costs.</p>"}, "LogLevel": {"shape": "LogLevel", "documentation": "<p>Describes the verbosity of the CloudWatch Logs for an application.</p>"}}, "documentation": "<p>Describes configuration parameters for Amazon CloudWatch logging for an application. For more information about CloudWatch logging, see <a href=\"https://docs.aws.amazon.com/kinesisanalytics/latest/java/monitoring-overview.html\">Monitoring</a>.</p>"}, "MonitoringConfigurationDescription": {"type": "structure", "members": {"ConfigurationType": {"shape": "ConfigurationType", "documentation": "<p>Describes whether to use the default CloudWatch logging configuration for an application.</p>"}, "MetricsLevel": {"shape": "MetricsLevel", "documentation": "<p>Describes the granularity of the CloudWatch Logs for an application.</p>"}, "LogLevel": {"shape": "LogLevel", "documentation": "<p>Describes the verbosity of the CloudWatch Logs for an application.</p>"}}, "documentation": "<p>Describes configuration parameters for CloudWatch logging for an application.</p>"}, "MonitoringConfigurationUpdate": {"type": "structure", "members": {"ConfigurationTypeUpdate": {"shape": "ConfigurationType", "documentation": "<p>Describes updates to whether to use the default CloudWatch logging configuration for an application. You must set this property to <code>CUSTOM</code> in order to set the <code>LogLevel</code> or <code>MetricsLevel</code> parameters.</p>"}, "MetricsLevelUpdate": {"shape": "MetricsLevel", "documentation": "<p>Describes updates to the granularity of the CloudWatch Logs for an application. The <code>Parallelism</code> level is not recommended for applications with a Parallelism over 64 due to excessive costs.</p>"}, "LogLevelUpdate": {"shape": "LogLevel", "documentation": "<p>Describes updates to the verbosity of the CloudWatch Logs for an application.</p>"}}, "documentation": "<p>Describes updates to configuration parameters for Amazon CloudWatch logging for an application.</p>"}, "NextToken": {"type": "string", "max": 512, "min": 1}, "ObjectVersion": {"type": "string", "max": 1024, "min": 0}, "Output": {"type": "structure", "required": ["Name", "DestinationSchema"], "members": {"Name": {"shape": "InAppStreamName", "documentation": "<p>The name of the in-application stream.</p>"}, "KinesisStreamsOutput": {"shape": "KinesisStreamsOutput", "documentation": "<p>Identifies a Kinesis data stream as the destination.</p>"}, "KinesisFirehoseOutput": {"shape": "KinesisFirehoseOutput", "documentation": "<p>Identifies a Kinesis Data Firehose delivery stream as the destination.</p>"}, "LambdaOutput": {"shape": "LambdaOutput", "documentation": "<p>Identifies an Amazon Lambda function as the destination.</p>"}, "DestinationSchema": {"shape": "DestinationSchema", "documentation": "<p>Describes the data format when records are written to the destination. </p>"}}, "documentation": "<p> Describes a SQL-based Kinesis Data Analytics application's output configuration, in which you identify an in-application stream and a destination where you want the in-application stream data to be written. The destination can be a Kinesis data stream or a Kinesis Data Firehose delivery stream. </p> <p/>"}, "OutputDescription": {"type": "structure", "members": {"OutputId": {"shape": "Id", "documentation": "<p>A unique identifier for the output configuration.</p>"}, "Name": {"shape": "InAppStreamName", "documentation": "<p>The name of the in-application stream that is configured as output.</p>"}, "KinesisStreamsOutputDescription": {"shape": "KinesisStreamsOutputDescription", "documentation": "<p>Describes the Kinesis data stream that is configured as the destination where output is written.</p>"}, "KinesisFirehoseOutputDescription": {"shape": "KinesisFirehoseOutputDescription", "documentation": "<p>Describes the Kinesis Data Firehose delivery stream that is configured as the destination where output is written.</p>"}, "LambdaOutputDescription": {"shape": "LambdaOutputDescription", "documentation": "<p>Describes the Lambda function that is configured as the destination where output is written.</p>"}, "DestinationSchema": {"shape": "DestinationSchema", "documentation": "<p>The data format used for writing data to the destination.</p>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application, describes the application output configuration, which includes the in-application stream name and the destination where the stream data is written. The destination can be a Kinesis data stream or a Kinesis Data Firehose delivery stream. </p>"}, "OutputDescriptions": {"type": "list", "member": {"shape": "OutputDescription"}}, "OutputUpdate": {"type": "structure", "required": ["OutputId"], "members": {"OutputId": {"shape": "Id", "documentation": "<p>Identifies the specific output configuration that you want to update.</p>"}, "NameUpdate": {"shape": "InAppStreamName", "documentation": "<p>If you want to specify a different in-application stream for this output configuration, use this field to specify the new in-application stream name.</p>"}, "KinesisStreamsOutputUpdate": {"shape": "KinesisStreamsOutputUpdate", "documentation": "<p>Describes a Kinesis data stream as the destination for the output.</p>"}, "KinesisFirehoseOutputUpdate": {"shape": "KinesisFirehoseOutputUpdate", "documentation": "<p>Describes a Kinesis Data Firehose delivery stream as the destination for the output.</p>"}, "LambdaOutputUpdate": {"shape": "LambdaOutputUpdate", "documentation": "<p>Describes an Amazon Lambda function as the destination for the output.</p>"}, "DestinationSchemaUpdate": {"shape": "DestinationSchema", "documentation": "<p>Describes the data format when records are written to the destination. </p>"}}, "documentation": "<p> For a SQL-based Kinesis Data Analytics application, describes updates to the output configuration identified by the <code>OutputId</code>. </p>"}, "OutputUpdates": {"type": "list", "member": {"shape": "OutputUpdate"}}, "Outputs": {"type": "list", "member": {"shape": "Output"}}, "Parallelism": {"type": "integer", "min": 1}, "ParallelismConfiguration": {"type": "structure", "required": ["ConfigurationType"], "members": {"ConfigurationType": {"shape": "ConfigurationType", "documentation": "<p>Describes whether the application uses the default parallelism for the Kinesis Data Analytics service. You must set this property to <code>CUSTOM</code> in order to change your application's <code>AutoScalingEnabled</code>, <code>Parallelism</code>, or <code>ParallelismPerKPU</code> properties.</p>"}, "Parallelism": {"shape": "Parallelism", "documentation": "<p>Describes the initial number of parallel tasks that a Flink-based Kinesis Data Analytics application can perform. If <code>AutoScalingEnabled</code> is set to True, Kinesis Data Analytics increases the <code>CurrentParallelism</code> value in response to application load. The service can increase the <code>CurrentParallelism</code> value up to the maximum parallelism, which is <code>ParalellismPerKPU</code> times the maximum KPUs for the application. The maximum KPUs for an application is 32 by default, and can be increased by requesting a limit increase. If application load is reduced, the service can reduce the <code>CurrentParallelism</code> value down to the <code>Parallelism</code> setting.</p>"}, "ParallelismPerKPU": {"shape": "ParallelismPerKPU", "documentation": "<p>Describes the number of parallel tasks that a Flink-based Kinesis Data Analytics application can perform per Kinesis Processing Unit (KPU) used by the application. For more information about KPUs, see <a href=\"http://aws.amazon.com/kinesis/data-analytics/pricing/\">Amazon Kinesis Data Analytics Pricing</a>.</p>"}, "AutoScalingEnabled": {"shape": "BooleanObject", "documentation": "<p>Describes whether the Kinesis Data Analytics service can increase the parallelism of the application in response to increased throughput.</p>"}}, "documentation": "<p>Describes parameters for how a Flink-based Kinesis Data Analytics application executes multiple tasks simultaneously. For more information about parallelism, see <a href=\"https://ci.apache.org/projects/flink/flink-docs-release-1.8/dev/parallel.html\">Parallel Execution</a> in the <a href=\"https://ci.apache.org/projects/flink/flink-docs-release-1.8/\">Apache Flink Documentation</a>.</p>"}, "ParallelismConfigurationDescription": {"type": "structure", "members": {"ConfigurationType": {"shape": "ConfigurationType", "documentation": "<p>Describes whether the application uses the default parallelism for the Kinesis Data Analytics service. </p>"}, "Parallelism": {"shape": "Parallelism", "documentation": "<p>Describes the initial number of parallel tasks that a Flink-based Kinesis Data Analytics application can perform. If <code>AutoScalingEnabled</code> is set to True, then Kinesis Data Analytics can increase the <code>CurrentParallelism</code> value in response to application load. The service can increase <code>CurrentParallelism</code> up to the maximum parallelism, which is <code>ParalellismPerKPU</code> times the maximum KPUs for the application. The maximum KPUs for an application is 32 by default, and can be increased by requesting a limit increase. If application load is reduced, the service can reduce the <code>CurrentParallelism</code> value down to the <code>Parallelism</code> setting.</p>"}, "ParallelismPerKPU": {"shape": "ParallelismPerKPU", "documentation": "<p>Describes the number of parallel tasks that a Flink-based Kinesis Data Analytics application can perform per Kinesis Processing Unit (KPU) used by the application.</p>"}, "CurrentParallelism": {"shape": "Parallelism", "documentation": "<p>Describes the current number of parallel tasks that a Flink-based Kinesis Data Analytics application can perform. If <code>AutoScalingEnabled</code> is set to True, Kinesis Data Analytics can increase this value in response to application load. The service can increase this value up to the maximum parallelism, which is <code>ParalellismPerKPU</code> times the maximum KPUs for the application. The maximum KPUs for an application is 32 by default, and can be increased by requesting a limit increase. If application load is reduced, the service can reduce the <code>CurrentParallelism</code> value down to the <code>Parallelism</code> setting.</p>"}, "AutoScalingEnabled": {"shape": "BooleanObject", "documentation": "<p>Describes whether the Kinesis Data Analytics service can increase the parallelism of the application in response to increased throughput.</p>"}}, "documentation": "<p>Describes parameters for how a Flink-based Kinesis Data Analytics application executes multiple tasks simultaneously.</p>"}, "ParallelismConfigurationUpdate": {"type": "structure", "members": {"ConfigurationTypeUpdate": {"shape": "ConfigurationType", "documentation": "<p>Describes updates to whether the application uses the default parallelism for the Kinesis Data Analytics service, or if a custom parallelism is used. You must set this property to <code>CUSTOM</code> in order to change your application's <code>AutoScalingEnabled</code>, <code>Parallelism</code>, or <code>ParallelismPerKPU</code> properties.</p>"}, "ParallelismUpdate": {"shape": "Parallelism", "documentation": "<p>Describes updates to the initial number of parallel tasks an application can perform. If <code>AutoScalingEnabled</code> is set to True, then Kinesis Data Analytics can increase the <code>CurrentParallelism</code> value in response to application load. The service can increase <code>CurrentParallelism</code> up to the maximum parallelism, which is <code>ParalellismPerKPU</code> times the maximum KPUs for the application. The maximum KPUs for an application is 32 by default, and can be increased by requesting a limit increase. If application load is reduced, the service will reduce <code>CurrentParallelism</code> down to the <code>Parallelism</code> setting.</p>"}, "ParallelismPerKPUUpdate": {"shape": "ParallelismPerKPU", "documentation": "<p>Describes updates to the number of parallel tasks an application can perform per Kinesis Processing Unit (KPU) used by the application.</p>"}, "AutoScalingEnabledUpdate": {"shape": "BooleanObject", "documentation": "<p>Describes updates to whether the Kinesis Data Analytics service can increase the parallelism of a Flink-based Kinesis Data Analytics application in response to increased throughput.</p>"}}, "documentation": "<p>Describes updates to parameters for how an application executes multiple tasks simultaneously.</p>"}, "ParallelismPerKPU": {"type": "integer", "min": 1}, "ParsedInputRecord": {"type": "list", "member": {"shape": "ParsedInputRecordField"}}, "ParsedInputRecordField": {"type": "string"}, "ParsedInputRecords": {"type": "list", "member": {"shape": "ParsedInputRecord"}}, "ProcessedInputRecord": {"type": "string"}, "ProcessedInputRecords": {"type": "list", "member": {"shape": "ProcessedInputRecord"}}, "PropertyGroup": {"type": "structure", "required": ["PropertyGroupId", "PropertyMap"], "members": {"PropertyGroupId": {"shape": "Id", "documentation": "<p>Describes the key of an application execution property key-value pair.</p>"}, "PropertyMap": {"shape": "PropertyMap", "documentation": "<p>Describes the value of an application execution property key-value pair.</p>"}}, "documentation": "<p>Property key-value pairs passed into an application.</p>"}, "PropertyGroups": {"type": "list", "member": {"shape": "PropertyGroup"}, "max": 50}, "PropertyKey": {"type": "string", "max": 2048, "min": 1}, "PropertyMap": {"type": "map", "key": {"shape": "PropertyKey"}, "value": {"shape": "PropertyValue"}, "max": 50, "min": 1}, "PropertyValue": {"type": "string", "max": 2048, "min": 1}, "RawInputRecord": {"type": "string"}, "RawInputRecords": {"type": "list", "member": {"shape": "RawInputRecord"}}, "RecordColumn": {"type": "structure", "required": ["Name", "SqlType"], "members": {"Name": {"shape": "RecordColumnName", "documentation": "<p>The name of the column that is created in the in-application input stream or reference table.</p>"}, "Mapping": {"shape": "RecordColumnMapping", "documentation": "<p>A reference to the data element in the streaming input or the reference data source.</p>"}, "SqlType": {"shape": "RecordColumnSqlType", "documentation": "<p>The type of column created in the in-application input stream or reference table.</p>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application, describes the mapping of each data element in the streaming source to the corresponding column in the in-application stream.</p> <p>Also used to describe the format of the reference data source.</p>"}, "RecordColumnDelimiter": {"type": "string", "max": 1024, "min": 1}, "RecordColumnMapping": {"type": "string", "max": 65535, "min": 0}, "RecordColumnName": {"type": "string", "max": 256, "min": 1, "pattern": "[^-\\s<>&]*"}, "RecordColumnSqlType": {"type": "string", "max": 100, "min": 1}, "RecordColumns": {"type": "list", "member": {"shape": "RecordColumn"}, "max": 1000, "min": 1}, "RecordEncoding": {"type": "string", "max": 5, "min": 5, "pattern": "UTF-8"}, "RecordFormat": {"type": "structure", "required": ["RecordFormatType"], "members": {"RecordFormatType": {"shape": "RecordFormatType", "documentation": "<p>The type of record format.</p>"}, "MappingParameters": {"shape": "MappingParameters", "documentation": "<p>When you configure application input at the time of creating or updating an application, provides additional mapping information specific to the record format (such as JSON, CSV, or record fields delimited by some delimiter) on the streaming source.</p>"}}, "documentation": "<p> For a SQL-based Kinesis Data Analytics application, describes the record format and relevant mapping information that should be applied to schematize the records on the stream. </p>"}, "RecordFormatType": {"type": "string", "enum": ["JSON", "CSV"]}, "RecordRowDelimiter": {"type": "string", "max": 1024, "min": 1}, "RecordRowPath": {"type": "string", "max": 65535, "min": 1, "pattern": "^(?=^\\$)(?=^\\S+$).*$"}, "ReferenceDataSource": {"type": "structure", "required": ["TableName", "ReferenceSchema"], "members": {"TableName": {"shape": "InAppTableName", "documentation": "<p>The name of the in-application table to create.</p>"}, "S3ReferenceDataSource": {"shape": "S3ReferenceDataSource", "documentation": "<p>Identifies the S3 bucket and object that contains the reference data. A Kinesis Data Analytics application loads reference data only once. If the data changes, you call the <a>UpdateApplication</a> operation to trigger reloading of data into your application. </p>"}, "ReferenceSchema": {"shape": "SourceSchema", "documentation": "<p>Describes the format of the data in the streaming source, and how each data element maps to corresponding columns created in the in-application stream.</p>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application, describes the reference data source by providing the source information (Amazon S3 bucket name and object key name), the resulting in-application table name that is created, and the necessary schema to map the data elements in the Amazon S3 object to the in-application table.</p>"}, "ReferenceDataSourceDescription": {"type": "structure", "required": ["ReferenceId", "TableName", "S3ReferenceDataSourceDescription"], "members": {"ReferenceId": {"shape": "Id", "documentation": "<p>The ID of the reference data source. This is the ID that Kinesis Data Analytics assigns when you add the reference data source to your application using the <a>CreateApplication</a> or <a>UpdateApplication</a> operation.</p>"}, "TableName": {"shape": "InAppTableName", "documentation": "<p>The in-application table name created by the specific reference data source configuration.</p>"}, "S3ReferenceDataSourceDescription": {"shape": "S3ReferenceDataSourceDescription", "documentation": "<p>Provides the Amazon S3 bucket name, the object key name that contains the reference data. </p>"}, "ReferenceSchema": {"shape": "SourceSchema", "documentation": "<p>Describes the format of the data in the streaming source, and how each data element maps to corresponding columns created in the in-application stream.</p>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application, describes the reference data source configured for an application.</p>"}, "ReferenceDataSourceDescriptions": {"type": "list", "member": {"shape": "ReferenceDataSourceDescription"}}, "ReferenceDataSourceUpdate": {"type": "structure", "required": ["ReferenceId"], "members": {"ReferenceId": {"shape": "Id", "documentation": "<p>The ID of the reference data source that is being updated. You can use the <a>DescribeApplication</a> operation to get this value.</p>"}, "TableNameUpdate": {"shape": "InAppTableName", "documentation": "<p>The in-application table name that is created by this update.</p>"}, "S3ReferenceDataSourceUpdate": {"shape": "S3ReferenceDataSourceUpdate", "documentation": "<p>Describes the S3 bucket name, object key name, and IAM role that Kinesis Data Analytics can assume to read the Amazon S3 object on your behalf and populate the in-application reference table.</p>"}, "ReferenceSchemaUpdate": {"shape": "SourceSchema", "documentation": "<p>Describes the format of the data in the streaming source, and how each data element maps to corresponding columns created in the in-application stream. </p>"}}, "documentation": "<p>When you update a reference data source configuration for a SQL-based Kinesis Data Analytics application, this object provides all the updated values (such as the source bucket name and object key name), the in-application table name that is created, and updated mapping information that maps the data in the Amazon S3 object to the in-application reference table that is created.</p>"}, "ReferenceDataSourceUpdates": {"type": "list", "member": {"shape": "ReferenceDataSourceUpdate"}}, "ReferenceDataSources": {"type": "list", "member": {"shape": "ReferenceDataSource"}}, "ResourceARN": {"type": "string", "max": 2048, "min": 1, "pattern": "arn:.*"}, "ResourceInUseException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The application is not available for this operation.</p>", "exception": true}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Specified application can't be found.</p>", "exception": true}, "ResourceProvisionedThroughputExceededException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Discovery failed to get a record from the streaming source because of the Kinesis Streams <code>ProvisionedThroughputExceededException</code>. For more information, see <a href=\"http://docs.aws.amazon.com/kinesis/latest/APIReference/API_GetRecords.html\">GetRecords</a> in the Amazon Kinesis Streams API Reference.</p>", "exception": true}, "RoleARN": {"type": "string", "max": 2048, "min": 1, "pattern": "arn:.*"}, "RollbackApplicationRequest": {"type": "structure", "required": ["ApplicationName", "CurrentApplicationVersionId"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of the application.</p>"}, "CurrentApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>The current application version ID. You can retrieve the application version ID using <a>DescribeApplication</a>.</p>"}}}, "RollbackApplicationResponse": {"type": "structure", "required": ["ApplicationDetail"], "members": {"ApplicationDetail": {"shape": "ApplicationDetail"}}}, "RunConfiguration": {"type": "structure", "members": {"FlinkRunConfiguration": {"shape": "FlinkRunConfiguration", "documentation": "<p>Describes the starting parameters for a Flink-based Kinesis Data Analytics application.</p>"}, "SqlRunConfigurations": {"shape": "SqlRunConfigurations", "documentation": "<p>Describes the starting parameters for a SQL-based Kinesis Data Analytics application application.</p>"}, "ApplicationRestoreConfiguration": {"shape": "ApplicationRestoreConfiguration", "documentation": "<p>Describes the restore behavior of a restarting application.</p>"}}, "documentation": "<p>Describes the starting parameters for an Kinesis Data Analytics application.</p>"}, "RunConfigurationDescription": {"type": "structure", "members": {"ApplicationRestoreConfigurationDescription": {"shape": "ApplicationRestoreConfiguration", "documentation": "<p>Describes the restore behavior of a restarting application.</p>"}, "FlinkRunConfigurationDescription": {"shape": "FlinkRunConfiguration"}}, "documentation": "<p>Describes the starting properties for a Kinesis Data Analytics application.</p>"}, "RunConfigurationUpdate": {"type": "structure", "members": {"FlinkRunConfiguration": {"shape": "FlinkRunConfiguration", "documentation": "<p>Describes the starting parameters for a Flink-based Kinesis Data Analytics application.</p>"}, "ApplicationRestoreConfiguration": {"shape": "ApplicationRestoreConfiguration", "documentation": "<p>Describes updates to the restore behavior of a restarting application.</p>"}}, "documentation": "<p>Describes the updates to the starting parameters for a Kinesis Data Analytics application.</p>"}, "RuntimeEnvironment": {"type": "string", "enum": ["SQL-1_0", "FLINK-1_6", "FLINK-1_8", "ZEPPELIN-FLINK-1_0", "FLINK-1_11", "FLINK-1_13", "ZEPPELIN-FLINK-2_0", "FLINK-1_15", "ZEPPELIN-FLINK-3_0"]}, "S3ApplicationCodeLocationDescription": {"type": "structure", "required": ["BucketARN", "<PERSON><PERSON>ey"], "members": {"BucketARN": {"shape": "BucketARN", "documentation": "<p>The Amazon Resource Name (ARN) for the S3 bucket containing the application code.</p>"}, "FileKey": {"shape": "<PERSON><PERSON>ey", "documentation": "<p>The file key for the object containing the application code.</p>"}, "ObjectVersion": {"shape": "ObjectVersion", "documentation": "<p>The version of the object containing the application code.</p>"}}, "documentation": "<p>Describes the location of an application's code stored in an S3 bucket.</p>"}, "S3Configuration": {"type": "structure", "required": ["BucketARN", "<PERSON><PERSON>ey"], "members": {"BucketARN": {"shape": "BucketARN", "documentation": "<p>The ARN of the S3 bucket that contains the data.</p>"}, "FileKey": {"shape": "<PERSON><PERSON>ey", "documentation": "<p>The name of the object that contains the data.</p>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application, provides a description of an Amazon S3 data source, including the Amazon Resource Name (ARN) of the S3 bucket and the name of the Amazon S3 object that contains the data.</p>"}, "S3ContentBaseLocation": {"type": "structure", "required": ["BucketARN"], "members": {"BucketARN": {"shape": "BucketARN", "documentation": "<p>The Amazon Resource Name (ARN) of the S3 bucket.</p>"}, "BasePath": {"shape": "BasePath", "documentation": "<p>The base path for the S3 bucket.</p>"}}, "documentation": "<p>The S3 bucket that holds the application information.</p>"}, "S3ContentBaseLocationDescription": {"type": "structure", "required": ["BucketARN"], "members": {"BucketARN": {"shape": "BucketARN", "documentation": "<p>The Amazon Resource Name (ARN) of the S3 bucket.</p>"}, "BasePath": {"shape": "BasePath", "documentation": "<p>The base path for the S3 bucket.</p>"}}, "documentation": "<p>The description of the S3 base location that holds the application.</p>"}, "S3ContentBaseLocationUpdate": {"type": "structure", "members": {"BucketARNUpdate": {"shape": "BucketARN", "documentation": "<p>The updated Amazon Resource Name (ARN) of the S3 bucket.</p>"}, "BasePathUpdate": {"shape": "BasePath", "documentation": "<p>The updated S3 bucket path.</p>"}}, "documentation": "<p>The information required to update the S3 base location that holds the application.</p>"}, "S3ContentLocation": {"type": "structure", "required": ["BucketARN", "<PERSON><PERSON>ey"], "members": {"BucketARN": {"shape": "BucketARN", "documentation": "<p>The Amazon Resource Name (ARN) for the S3 bucket containing the application code.</p>"}, "FileKey": {"shape": "<PERSON><PERSON>ey", "documentation": "<p>The file key for the object containing the application code.</p>"}, "ObjectVersion": {"shape": "ObjectVersion", "documentation": "<p>The version of the object containing the application code.</p>"}}, "documentation": "<p>For a Kinesis Data Analytics application provides a description of an Amazon S3 object, including the Amazon Resource Name (ARN) of the S3 bucket, the name of the Amazon S3 object that contains the data, and the version number of the Amazon S3 object that contains the data. </p>"}, "S3ContentLocationUpdate": {"type": "structure", "members": {"BucketARNUpdate": {"shape": "BucketARN", "documentation": "<p>The new Amazon Resource Name (ARN) for the S3 bucket containing the application code.</p>"}, "FileKeyUpdate": {"shape": "<PERSON><PERSON>ey", "documentation": "<p>The new file key for the object containing the application code.</p>"}, "ObjectVersionUpdate": {"shape": "ObjectVersion", "documentation": "<p>The new version of the object containing the application code.</p>"}}, "documentation": "<p>Describes an update for the Amazon S3 code content location for an application.</p>"}, "S3ReferenceDataSource": {"type": "structure", "members": {"BucketARN": {"shape": "BucketARN", "documentation": "<p>The Amazon Resource Name (ARN) of the S3 bucket.</p>"}, "FileKey": {"shape": "<PERSON><PERSON>ey", "documentation": "<p>The object key name containing the reference data.</p>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application, identifies the Amazon S3 bucket and object that contains the reference data.</p> <p>A Kinesis Data Analytics application loads reference data only once. If the data changes, you call the <a>UpdateApplication</a> operation to trigger reloading of data into your application. </p>"}, "S3ReferenceDataSourceDescription": {"type": "structure", "required": ["BucketARN", "<PERSON><PERSON>ey"], "members": {"BucketARN": {"shape": "BucketARN", "documentation": "<p>The Amazon Resource Name (ARN) of the S3 bucket.</p>"}, "FileKey": {"shape": "<PERSON><PERSON>ey", "documentation": "<p>Amazon S3 object key name.</p>"}, "ReferenceRoleARN": {"shape": "RoleARN", "documentation": "<p>The ARN of the IAM role that Kinesis Data Analytics can assume to read the Amazon S3 object on your behalf to populate the in-application reference table. </p> <note> <p>Provided for backward compatibility. Applications that are created with the current API version have an application-level service execution role rather than a resource-level role.</p> </note>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application, provides the bucket name and object key name that stores the reference data.</p>"}, "S3ReferenceDataSourceUpdate": {"type": "structure", "members": {"BucketARNUpdate": {"shape": "BucketARN", "documentation": "<p>The Amazon Resource Name (ARN) of the S3 bucket.</p>"}, "FileKeyUpdate": {"shape": "<PERSON><PERSON>ey", "documentation": "<p>The object key name.</p>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application, describes the Amazon S3 bucket name and object key name for an in-application reference table. </p>"}, "SecurityGroupId": {"type": "string"}, "SecurityGroupIds": {"type": "list", "member": {"shape": "SecurityGroupId"}, "max": 5, "min": 1}, "ServiceUnavailableException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The service cannot complete the request.</p>", "exception": true, "fault": true}, "SessionExpirationDurationInSeconds": {"type": "long", "max": 43200, "min": 1800}, "SnapshotDetails": {"type": "structure", "required": ["SnapshotName", "SnapshotStatus", "ApplicationVersionId"], "members": {"SnapshotName": {"shape": "SnapshotName", "documentation": "<p>The identifier for the application snapshot.</p>"}, "SnapshotStatus": {"shape": "SnapshotStatus", "documentation": "<p>The status of the application snapshot.</p>"}, "ApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>The current application version ID when the snapshot was created.</p>"}, "SnapshotCreationTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp of the application snapshot.</p>"}}, "documentation": "<p>Provides details about a snapshot of application state.</p>"}, "SnapshotName": {"type": "string", "max": 256, "min": 1, "pattern": "[a-zA-Z0-9_.-]+"}, "SnapshotStatus": {"type": "string", "enum": ["CREATING", "READY", "DELETING", "FAILED"]}, "SnapshotSummaries": {"type": "list", "member": {"shape": "SnapshotDetails"}}, "SourceSchema": {"type": "structure", "required": ["RecordFormat", "RecordColumns"], "members": {"RecordFormat": {"shape": "RecordFormat", "documentation": "<p>Specifies the format of the records on the streaming source.</p>"}, "RecordEncoding": {"shape": "RecordEncoding", "documentation": "<p>Specifies the encoding of the records in the streaming source. For example, UTF-8.</p>"}, "RecordColumns": {"shape": "RecordColumns", "documentation": "<p>A list of <code>RecordColumn</code> objects. </p>"}}, "documentation": "<p>For a SQL-based Kinesis Data Analytics application, describes the format of the data in the streaming source, and how each data element maps to corresponding columns created in the in-application stream. </p>"}, "SqlApplicationConfiguration": {"type": "structure", "members": {"Inputs": {"shape": "Inputs", "documentation": "<p>The array of <a>Input</a> objects describing the input streams used by the application.</p>"}, "Outputs": {"shape": "Outputs", "documentation": "<p>The array of <a>Output</a> objects describing the destination streams used by the application.</p>"}, "ReferenceDataSources": {"shape": "ReferenceDataSources", "documentation": "<p>The array of <a>ReferenceDataSource</a> objects describing the reference data sources used by the application.</p>"}}, "documentation": "<p>Describes the inputs, outputs, and reference data sources for a SQL-based Kinesis Data Analytics application.</p>"}, "SqlApplicationConfigurationDescription": {"type": "structure", "members": {"InputDescriptions": {"shape": "InputDescriptions", "documentation": "<p>The array of <a>InputDescription</a> objects describing the input streams used by the application.</p>"}, "OutputDescriptions": {"shape": "OutputDescriptions", "documentation": "<p>The array of <a>OutputDescription</a> objects describing the destination streams used by the application.</p>"}, "ReferenceDataSourceDescriptions": {"shape": "ReferenceDataSourceDescriptions", "documentation": "<p>The array of <a>ReferenceDataSourceDescription</a> objects describing the reference data sources used by the application.</p>"}}, "documentation": "<p>Describes the inputs, outputs, and reference data sources for a SQL-based Kinesis Data Analytics application.</p>"}, "SqlApplicationConfigurationUpdate": {"type": "structure", "members": {"InputUpdates": {"shape": "InputUpdates", "documentation": "<p>The array of <a>InputUpdate</a> objects describing the new input streams used by the application.</p>"}, "OutputUpdates": {"shape": "OutputUpdates", "documentation": "<p>The array of <a>OutputUpdate</a> objects describing the new destination streams used by the application.</p>"}, "ReferenceDataSourceUpdates": {"shape": "ReferenceDataSourceUpdates", "documentation": "<p>The array of <a>ReferenceDataSourceUpdate</a> objects describing the new reference data sources used by the application.</p>"}}, "documentation": "<p>Describes updates to the input streams, destination streams, and reference data sources for a SQL-based Kinesis Data Analytics application.</p>"}, "SqlRunConfiguration": {"type": "structure", "required": ["InputId", "InputStartingPositionConfiguration"], "members": {"InputId": {"shape": "Id", "documentation": "<p>The input source ID. You can get this ID by calling the <a>DescribeApplication</a> operation. </p>"}, "InputStartingPositionConfiguration": {"shape": "InputStartingPositionConfiguration", "documentation": "<p>The point at which you want the application to start processing records from the streaming source. </p>"}}, "documentation": "<p>Describes the starting parameters for a SQL-based Kinesis Data Analytics application.</p>"}, "SqlRunConfigurations": {"type": "list", "member": {"shape": "SqlRunConfiguration"}}, "StartApplicationRequest": {"type": "structure", "required": ["ApplicationName"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of the application.</p>"}, "RunConfiguration": {"shape": "RunConfiguration", "documentation": "<p>Identifies the run configuration (start parameters) of a Kinesis Data Analytics application.</p>"}}}, "StartApplicationResponse": {"type": "structure", "members": {}}, "StopApplicationRequest": {"type": "structure", "required": ["ApplicationName"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of the running application to stop.</p>"}, "Force": {"shape": "BooleanObject", "documentation": "<p>Set to <code>true</code> to force the application to stop. If you set <code>Force</code> to <code>true</code>, Kinesis Data Analytics stops the application without taking a snapshot. </p> <note> <p>Force-stopping your application may lead to data loss or duplication. To prevent data loss or duplicate processing of data during application restarts, we recommend you to take frequent snapshots of your application.</p> </note> <p>You can only force stop a Flink-based Kinesis Data Analytics application. You can't force stop a SQL-based Kinesis Data Analytics application.</p> <p>The application must be in the <code>STARTING</code>, <code>UPDATING</code>, <code>STOPPING</code>, <code>AUTOSCALING</code>, or <code>RUNNING</code> status. </p>"}}}, "StopApplicationResponse": {"type": "structure", "members": {}}, "SubnetId": {"type": "string"}, "SubnetIds": {"type": "list", "member": {"shape": "SubnetId"}, "max": 16, "min": 1}, "Tag": {"type": "structure", "required": ["Key"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>The key of the key-value tag.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The value of the key-value tag. The value is optional.</p>"}}, "documentation": "<p>A key-value pair (the value is optional) that you can define and assign to Amazon resources. If you specify a tag that already exists, the tag value is replaced with the value that you specify in the request. Note that the maximum number of application tags includes system tags. The maximum number of user-defined application tags is 50. For more information, see <a href=\"https://docs.aws.amazon.com/kinesisanalytics/latest/java/how-tagging.html\">Using Tagging</a>.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagKeys": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 1}, "TagResourceRequest": {"type": "structure", "required": ["ResourceARN", "Tags"], "members": {"ResourceARN": {"shape": "KinesisAnalyticsARN", "documentation": "<p>The ARN of the application to assign the tags.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>The key-value tags to assign to the application.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "Tags": {"type": "list", "member": {"shape": "Tag"}, "max": 200, "min": 1}, "TextContent": {"type": "string", "max": 102400, "min": 0}, "Timestamp": {"type": "timestamp"}, "TooManyTagsException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>Application created with too many tags, or too many tags added to an application. Note that the maximum number of application tags includes system tags. The maximum number of user-defined application tags is 50.</p>", "exception": true}, "UnableToDetectSchemaException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}, "RawInputRecords": {"shape": "RawInputRecords", "documentation": "<p>Raw stream data that was sampled to infer the schema.</p>"}, "ProcessedInputRecords": {"shape": "ProcessedInputRecords", "documentation": "<p>Stream data that was modified by the processor specified in the <code>InputProcessingConfiguration</code> parameter. </p>"}}, "documentation": "<p>The data format is not valid. Kinesis Data Analytics cannot detect the schema for the given streaming source.</p>", "exception": true}, "UnsupportedOperationException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The request was rejected because a specified parameter is not supported or a specified resource is not valid for this operation. </p>", "exception": true}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceARN", "TagKeys"], "members": {"ResourceARN": {"shape": "KinesisAnalyticsARN", "documentation": "<p>The ARN of the Kinesis Data Analytics application from which to remove the tags.</p>"}, "TagKeys": {"shape": "TagKeys", "documentation": "<p>A list of keys of tags to remove from the specified application.</p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateApplicationMaintenanceConfigurationRequest": {"type": "structure", "required": ["ApplicationName", "ApplicationMaintenanceConfigurationUpdate"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of the application for which you want to update the maintenance configuration.</p>"}, "ApplicationMaintenanceConfigurationUpdate": {"shape": "ApplicationMaintenanceConfigurationUpdate", "documentation": "<p>Describes the application maintenance configuration update.</p>"}}}, "UpdateApplicationMaintenanceConfigurationResponse": {"type": "structure", "members": {"ApplicationARN": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the application.</p>"}, "ApplicationMaintenanceConfigurationDescription": {"shape": "ApplicationMaintenanceConfigurationDescription", "documentation": "<p>The application maintenance configuration description after the update.</p>"}}}, "UpdateApplicationRequest": {"type": "structure", "required": ["ApplicationName"], "members": {"ApplicationName": {"shape": "ApplicationName", "documentation": "<p>The name of the application to update.</p>"}, "CurrentApplicationVersionId": {"shape": "ApplicationVersionId", "documentation": "<p>The current application version ID. You must provide the <code>CurrentApplicationVersionId</code> or the <code>ConditionalToken</code>.You can retrieve the application version ID using <a>DescribeApplication</a>. For better concurrency support, use the <code>ConditionalToken</code> parameter instead of <code>CurrentApplicationVersionId</code>.</p>"}, "ApplicationConfigurationUpdate": {"shape": "ApplicationConfigurationUpdate", "documentation": "<p>Describes application configuration updates.</p>"}, "ServiceExecutionRoleUpdate": {"shape": "RoleARN", "documentation": "<p>Describes updates to the service execution role.</p>"}, "RunConfigurationUpdate": {"shape": "RunConfigurationUpdate", "documentation": "<p>Describes updates to the application's starting parameters.</p>"}, "CloudWatchLoggingOptionUpdates": {"shape": "CloudWatchLoggingOptionUpdates", "documentation": "<p>Describes application Amazon CloudWatch logging option updates. You can only update existing CloudWatch logging options with this action. To add a new CloudWatch logging option, use <a>AddApplicationCloudWatchLoggingOption</a>.</p>"}, "ConditionalToken": {"shape": "ConditionalToken", "documentation": "<p>A value you use to implement strong concurrency for application updates. You must provide the <code>CurrentApplicationVersionId</code> or the <code>ConditionalToken</code>. You get the application's current <code>ConditionalToken</code> using <a>DescribeApplication</a>. For better concurrency support, use the <code>ConditionalToken</code> parameter instead of <code>CurrentApplicationVersionId</code>.</p>"}}}, "UpdateApplicationResponse": {"type": "structure", "required": ["ApplicationDetail"], "members": {"ApplicationDetail": {"shape": "ApplicationDetail", "documentation": "<p>Describes application updates.</p>"}}}, "UrlType": {"type": "string", "enum": ["FLINK_DASHBOARD_URL", "ZEPPELIN_UI_URL"]}, "VpcConfiguration": {"type": "structure", "required": ["SubnetIds", "SecurityGroupIds"], "members": {"SubnetIds": {"shape": "SubnetIds", "documentation": "<p>The array of <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_Subnet.html\">Subnet</a> IDs used by the VPC configuration.</p>"}, "SecurityGroupIds": {"shape": "SecurityGroupIds", "documentation": "<p>The array of <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_SecurityGroup.html\">SecurityGroup</a> IDs used by the VPC configuration.</p>"}}, "documentation": "<p>Describes the parameters of a VPC used by the application.</p>"}, "VpcConfigurationDescription": {"type": "structure", "required": ["VpcConfigurationId", "VpcId", "SubnetIds", "SecurityGroupIds"], "members": {"VpcConfigurationId": {"shape": "Id", "documentation": "<p>The ID of the VPC configuration.</p>"}, "VpcId": {"shape": "VpcId", "documentation": "<p>The ID of the associated VPC.</p>"}, "SubnetIds": {"shape": "SubnetIds", "documentation": "<p>The array of <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_Subnet.html\">Subnet</a> IDs used by the VPC configuration.</p>"}, "SecurityGroupIds": {"shape": "SecurityGroupIds", "documentation": "<p>The array of <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_SecurityGroup.html\">SecurityGroup</a> IDs used by the VPC configuration.</p>"}}, "documentation": "<p>Describes the parameters of a VPC used by the application.</p>"}, "VpcConfigurationDescriptions": {"type": "list", "member": {"shape": "VpcConfigurationDescription"}}, "VpcConfigurationUpdate": {"type": "structure", "required": ["VpcConfigurationId"], "members": {"VpcConfigurationId": {"shape": "Id", "documentation": "<p>Describes an update to the ID of the VPC configuration.</p>"}, "SubnetIdUpdates": {"shape": "SubnetIds", "documentation": "<p>Describes updates to the array of <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_Subnet.html\">Subnet</a> IDs used by the VPC configuration.</p>"}, "SecurityGroupIdUpdates": {"shape": "SecurityGroupIds", "documentation": "<p>Describes updates to the array of <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_SecurityGroup.html\">SecurityGroup</a> IDs used by the VPC configuration.</p>"}}, "documentation": "<p>Describes updates to the VPC configuration used by the application.</p>"}, "VpcConfigurationUpdates": {"type": "list", "member": {"shape": "VpcConfigurationUpdate"}}, "VpcConfigurations": {"type": "list", "member": {"shape": "VpcConfiguration"}}, "VpcId": {"type": "string"}, "ZeppelinApplicationConfiguration": {"type": "structure", "members": {"MonitoringConfiguration": {"shape": "ZeppelinMonitoringConfiguration", "documentation": "<p>The monitoring configuration of a Kinesis Data Analytics Studio notebook.</p>"}, "CatalogConfiguration": {"shape": "CatalogConfiguration", "documentation": "<p>The Amazon Glue Data Catalog that you use in queries in a Kinesis Data Analytics Studio notebook.</p>"}, "DeployAsApplicationConfiguration": {"shape": "DeployAsApplicationConfiguration", "documentation": "<p>The information required to deploy a Kinesis Data Analytics Studio notebook as an application with durable state.</p>"}, "CustomArtifactsConfiguration": {"shape": "CustomArtifactsConfigurationList", "documentation": "<p>Custom artifacts are dependency JARs and user-defined functions (UDF).</p>"}}, "documentation": "<p>The configuration of a Kinesis Data Analytics Studio notebook.</p>"}, "ZeppelinApplicationConfigurationDescription": {"type": "structure", "required": ["MonitoringConfigurationDescription"], "members": {"MonitoringConfigurationDescription": {"shape": "ZeppelinMonitoringConfigurationDescription", "documentation": "<p>The monitoring configuration of a Kinesis Data Analytics Studio notebook.</p>"}, "CatalogConfigurationDescription": {"shape": "CatalogConfigurationDescription", "documentation": "<p>The Amazon Glue Data Catalog that is associated with the Kinesis Data Analytics Studio notebook.</p>"}, "DeployAsApplicationConfigurationDescription": {"shape": "DeployAsApplicationConfigurationDescription", "documentation": "<p>The parameters required to deploy a Kinesis Data Analytics Studio notebook as an application with durable state.</p>"}, "CustomArtifactsConfigurationDescription": {"shape": "CustomArtifactsConfigurationDescriptionList", "documentation": "<p>Custom artifacts are dependency JARs and user-defined functions (UDF).</p>"}}, "documentation": "<p>The configuration of a Kinesis Data Analytics Studio notebook.</p>"}, "ZeppelinApplicationConfigurationUpdate": {"type": "structure", "members": {"MonitoringConfigurationUpdate": {"shape": "ZeppelinMonitoringConfigurationUpdate", "documentation": "<p>Updates to the monitoring configuration of a Kinesis Data Analytics Studio notebook.</p>"}, "CatalogConfigurationUpdate": {"shape": "CatalogConfigurationUpdate", "documentation": "<p>Updates to the configuration of the Amazon Glue Data Catalog that is associated with the Kinesis Data Analytics Studio notebook.</p>"}, "DeployAsApplicationConfigurationUpdate": {"shape": "DeployAsApplicationConfigurationUpdate"}, "CustomArtifactsConfigurationUpdate": {"shape": "CustomArtifactsConfigurationList", "documentation": "<p>Updates to the customer artifacts. Custom artifacts are dependency JAR files and user-defined functions (UDF).</p>"}}, "documentation": "<p>Updates to the configuration of Kinesis Data Analytics Studio notebook.</p>"}, "ZeppelinMonitoringConfiguration": {"type": "structure", "required": ["LogLevel"], "members": {"LogLevel": {"shape": "LogLevel", "documentation": "<p>The verbosity of the CloudWatch Logs for an application.</p>"}}, "documentation": "<p>Describes configuration parameters for Amazon CloudWatch logging for a Kinesis Data Analytics Studio notebook. For more information about CloudWatch logging, see <a href=\"https://docs.aws.amazon.com/kinesisanalytics/latest/java/monitoring-overview.html\">Monitoring</a>.</p>"}, "ZeppelinMonitoringConfigurationDescription": {"type": "structure", "members": {"LogLevel": {"shape": "LogLevel", "documentation": "<p>Describes the verbosity of the CloudWatch Logs for an application.</p>"}}, "documentation": "<p>The monitoring configuration for Apache Zeppelin within a Kinesis Data Analytics Studio notebook.</p>"}, "ZeppelinMonitoringConfigurationUpdate": {"type": "structure", "required": ["LogLevelUpdate"], "members": {"LogLevelUpdate": {"shape": "LogLevel", "documentation": "<p>Updates to the logging level for Apache Zeppelin within a Kinesis Data Analytics Studio notebook.</p>"}}, "documentation": "<p>Updates to the monitoring configuration for Apache Zeppelin within a Kinesis Data Analytics Studio notebook.</p>"}, "ZipFileContent": {"type": "blob", "max": 52428800, "min": 0}}, "documentation": "<p>Amazon Kinesis Data Analytics is a fully managed service that you can use to process and analyze streaming data using Java, SQL, or Scala. The service enables you to quickly author and run Java, SQL, or Scala code against streaming sources to perform time series analytics, feed real-time dashboards, and create real-time metrics.</p>"}