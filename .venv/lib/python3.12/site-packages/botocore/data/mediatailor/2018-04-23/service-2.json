{"version": "2.0", "metadata": {"apiVersion": "2018-04-23", "endpointPrefix": "api.mediatailor", "jsonVersion": "1.1", "protocol": "rest-json", "serviceAbbreviation": "MediaTailor", "serviceFullName": "AWS MediaTailor", "serviceId": "MediaTailor", "signatureVersion": "v4", "signingName": "mediatailor", "uid": "mediatailor-2018-04-23"}, "operations": {"ConfigureLogsForChannel": {"name": "ConfigureLogsForChannel", "http": {"method": "PUT", "requestUri": "/configureLogs/channel", "responseCode": 200}, "input": {"shape": "ConfigureLogsForChannelRequest"}, "output": {"shape": "ConfigureLogsForChannelResponse"}, "documentation": "<p>Configures Amazon CloudWatch log settings for a channel.</p>"}, "ConfigureLogsForPlaybackConfiguration": {"name": "ConfigureLogsForPlaybackConfiguration", "http": {"method": "PUT", "requestUri": "/configureLogs/playbackConfiguration", "responseCode": 200}, "input": {"shape": "ConfigureLogsForPlaybackConfigurationRequest"}, "output": {"shape": "ConfigureLogsForPlaybackConfigurationResponse"}, "documentation": "<p>Amazon CloudWatch log settings for a playback configuration.</p>", "idempotent": true}, "CreateChannel": {"name": "CreateChannel", "http": {"method": "POST", "requestUri": "/channel/{ChannelName}", "responseCode": 200}, "input": {"shape": "CreateChannelRequest"}, "output": {"shape": "CreateChannelResponse"}, "documentation": "<p>Creates a channel. For information about MediaTailor channels, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/channel-assembly-channels.html\">Working with channels</a> in the <i>MediaTailor User Guide</i>.</p>", "idempotent": true}, "CreateLiveSource": {"name": "CreateLiveSource", "http": {"method": "POST", "requestUri": "/sourceLocation/{SourceLocationName}/liveSource/{LiveSourceName}", "responseCode": 200}, "input": {"shape": "CreateLiveSourceRequest"}, "output": {"shape": "CreateLiveSourceResponse"}, "documentation": "<p>The live source configuration.</p>", "idempotent": true}, "CreatePrefetchSchedule": {"name": "CreatePrefetchSchedule", "http": {"method": "POST", "requestUri": "/prefetchSchedule/{PlaybackConfigurationName}/{Name}", "responseCode": 200}, "input": {"shape": "CreatePrefetchScheduleRequest"}, "output": {"shape": "CreatePrefetchScheduleResponse"}, "documentation": "<p>Creates a prefetch schedule for a playback configuration. A prefetch schedule allows you to tell MediaTailor to fetch and prepare certain ads before an ad break happens. For more information about ad prefetching, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/prefetching-ads.html\">Using ad prefetching</a> in the <i>MediaTailor User Guide</i>.</p>", "idempotent": true}, "CreateProgram": {"name": "CreateProgram", "http": {"method": "POST", "requestUri": "/channel/{ChannelName}/program/{ProgramName}", "responseCode": 200}, "input": {"shape": "CreateProgramRequest"}, "output": {"shape": "CreateProgramResponse"}, "documentation": "<p>Creates a program within a channel. For information about programs, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/channel-assembly-programs.html\">Working with programs</a> in the <i>MediaTailor User Guide</i>.</p>", "idempotent": true}, "CreateSourceLocation": {"name": "CreateSourceLocation", "http": {"method": "POST", "requestUri": "/sourceLocation/{SourceLocationName}", "responseCode": 200}, "input": {"shape": "CreateSourceLocationRequest"}, "output": {"shape": "CreateSourceLocationResponse"}, "documentation": "<p>Creates a source location. A source location is a container for sources. For more information about source locations, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/channel-assembly-source-locations.html\">Working with source locations</a> in the <i>MediaTailor User Guide</i>.</p>", "idempotent": true}, "CreateVodSource": {"name": "CreateVodSource", "http": {"method": "POST", "requestUri": "/sourceLocation/{SourceLocationName}/vodSource/{VodSourceName}", "responseCode": 200}, "input": {"shape": "CreateVodSourceRequest"}, "output": {"shape": "CreateVodSourceResponse"}, "documentation": "<p>The VOD source configuration parameters.</p>", "idempotent": true}, "DeleteChannel": {"name": "DeleteChannel", "http": {"method": "DELETE", "requestUri": "/channel/{ChannelName}", "responseCode": 200}, "input": {"shape": "DeleteChannelRequest"}, "output": {"shape": "DeleteChannelResponse"}, "documentation": "<p>Deletes a channel. For information about MediaTailor channels, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/channel-assembly-channels.html\">Working with channels</a> in the <i>MediaTailor User Guide</i>.</p>", "idempotent": true}, "DeleteChannelPolicy": {"name": "DeleteChannelPolicy", "http": {"method": "DELETE", "requestUri": "/channel/{ChannelName}/policy", "responseCode": 200}, "input": {"shape": "DeleteChannelPolicyRequest"}, "output": {"shape": "DeleteChannelPolicyResponse"}, "documentation": "<p>The channel policy to delete.</p>", "idempotent": true}, "DeleteLiveSource": {"name": "DeleteLiveSource", "http": {"method": "DELETE", "requestUri": "/sourceLocation/{SourceLocationName}/liveSource/{LiveSourceName}", "responseCode": 200}, "input": {"shape": "DeleteLiveSourceRequest"}, "output": {"shape": "DeleteLiveSourceResponse"}, "documentation": "<p>The live source to delete.</p>", "idempotent": true}, "DeletePlaybackConfiguration": {"name": "DeletePlaybackConfiguration", "http": {"method": "DELETE", "requestUri": "/playbackConfiguration/{Name}", "responseCode": 204}, "input": {"shape": "DeletePlaybackConfigurationRequest"}, "output": {"shape": "DeletePlaybackConfigurationResponse"}, "documentation": "<p>Deletes a playback configuration. For information about MediaTailor configurations, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/configurations.html\">Working with configurations in AWS Elemental MediaTailor</a>.</p>", "idempotent": true}, "DeletePrefetchSchedule": {"name": "DeletePrefetchSchedule", "http": {"method": "DELETE", "requestUri": "/prefetchSchedule/{PlaybackConfigurationName}/{Name}", "responseCode": 204}, "input": {"shape": "DeletePrefetchScheduleRequest"}, "output": {"shape": "DeletePrefetchScheduleResponse"}, "documentation": "<p>Deletes a prefetch schedule for a specific playback configuration. If you call <code>DeletePrefetchSchedule</code> on an expired prefetch schedule, MediaTailor returns an HTTP 404 status code. For more information about ad prefetching, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/prefetching-ads.html\">Using ad prefetching</a> in the <i>MediaTailor User Guide</i>.</p>", "idempotent": true}, "DeleteProgram": {"name": "DeleteProgram", "http": {"method": "DELETE", "requestUri": "/channel/{ChannelName}/program/{ProgramName}", "responseCode": 200}, "input": {"shape": "DeleteProgramRequest"}, "output": {"shape": "DeleteProgramResponse"}, "documentation": "<p>Deletes a program within a channel. For information about programs, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/channel-assembly-programs.html\">Working with programs</a> in the <i>MediaTailor User Guide</i>.</p>", "idempotent": true}, "DeleteSourceLocation": {"name": "DeleteSourceLocation", "http": {"method": "DELETE", "requestUri": "/sourceLocation/{SourceLocationName}", "responseCode": 200}, "input": {"shape": "DeleteSourceLocationRequest"}, "output": {"shape": "DeleteSourceLocationResponse"}, "documentation": "<p>Deletes a source location. A source location is a container for sources. For more information about source locations, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/channel-assembly-source-locations.html\">Working with source locations</a> in the <i>MediaTailor User Guide</i>.</p>", "idempotent": true}, "DeleteVodSource": {"name": "DeleteVodSource", "http": {"method": "DELETE", "requestUri": "/sourceLocation/{SourceLocationName}/vodSource/{VodSourceName}", "responseCode": 200}, "input": {"shape": "DeleteVodSourceRequest"}, "output": {"shape": "DeleteVodSourceResponse"}, "documentation": "<p>The video on demand (VOD) source to delete.</p>", "idempotent": true}, "DescribeChannel": {"name": "DescribeChannel", "http": {"method": "GET", "requestUri": "/channel/{ChannelName}", "responseCode": 200}, "input": {"shape": "DescribeChannelRequest"}, "output": {"shape": "DescribeChannelResponse"}, "documentation": "<p>Describes a channel. For information about MediaTailor channels, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/channel-assembly-channels.html\">Working with channels</a> in the <i>MediaTailor User Guide</i>.</p>"}, "DescribeLiveSource": {"name": "DescribeLiveSource", "http": {"method": "GET", "requestUri": "/sourceLocation/{SourceLocationName}/liveSource/{LiveSourceName}", "responseCode": 200}, "input": {"shape": "DescribeLiveSourceRequest"}, "output": {"shape": "DescribeLiveSourceResponse"}, "documentation": "<p>The live source to describe.</p>"}, "DescribeProgram": {"name": "DescribeProgram", "http": {"method": "GET", "requestUri": "/channel/{ChannelName}/program/{ProgramName}", "responseCode": 200}, "input": {"shape": "DescribeProgramRequest"}, "output": {"shape": "DescribeProgramResponse"}, "documentation": "<p>Describes a program within a channel. For information about programs, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/channel-assembly-programs.html\">Working with programs</a> in the <i>MediaTailor User Guide</i>.</p>"}, "DescribeSourceLocation": {"name": "DescribeSourceLocation", "http": {"method": "GET", "requestUri": "/sourceLocation/{SourceLocationName}", "responseCode": 200}, "input": {"shape": "DescribeSourceLocationRequest"}, "output": {"shape": "DescribeSourceLocationResponse"}, "documentation": "<p>Describes a source location. A source location is a container for sources. For more information about source locations, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/channel-assembly-source-locations.html\">Working with source locations</a> in the <i>MediaTailor User Guide</i>.</p>"}, "DescribeVodSource": {"name": "DescribeVodSource", "http": {"method": "GET", "requestUri": "/sourceLocation/{SourceLocationName}/vodSource/{VodSourceName}", "responseCode": 200}, "input": {"shape": "DescribeVodSourceRequest"}, "output": {"shape": "DescribeVodSourceResponse"}, "documentation": "<p>Provides details about a specific video on demand (VOD) source in a specific source location.</p>"}, "GetChannelPolicy": {"name": "GetChannelPolicy", "http": {"method": "GET", "requestUri": "/channel/{ChannelName}/policy", "responseCode": 200}, "input": {"shape": "GetChannelPolicyRequest"}, "output": {"shape": "GetChannelPolicyResponse"}, "documentation": "<p>Returns the channel's IAM policy. IAM policies are used to control access to your channel.</p>"}, "GetChannelSchedule": {"name": "GetChannelSchedule", "http": {"method": "GET", "requestUri": "/channel/{ChannelName}/schedule", "responseCode": 200}, "input": {"shape": "GetChannelScheduleRequest"}, "output": {"shape": "GetChannelScheduleResponse"}, "documentation": "<p>Retrieves information about your channel's schedule.</p>"}, "GetPlaybackConfiguration": {"name": "GetPlaybackConfiguration", "http": {"method": "GET", "requestUri": "/playbackConfiguration/{Name}", "responseCode": 200}, "input": {"shape": "GetPlaybackConfigurationRequest"}, "output": {"shape": "GetPlaybackConfigurationResponse"}, "documentation": "<p>Retrieves a playback configuration. For information about MediaTailor configurations, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/configurations.html\">Working with configurations in AWS Elemental MediaTailor</a>.</p>"}, "GetPrefetchSchedule": {"name": "GetPrefetchSchedule", "http": {"method": "GET", "requestUri": "/prefetchSchedule/{PlaybackConfigurationName}/{Name}", "responseCode": 200}, "input": {"shape": "GetPrefetchScheduleRequest"}, "output": {"shape": "GetPrefetchScheduleResponse"}, "documentation": "<p>Retrieves a prefetch schedule for a playback configuration. A prefetch schedule allows you to tell MediaTailor to fetch and prepare certain ads before an ad break happens. For more information about ad prefetching, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/prefetching-ads.html\">Using ad prefetching</a> in the <i>MediaTailor User Guide</i>.</p>"}, "ListAlerts": {"name": "ListAlerts", "http": {"method": "GET", "requestUri": "/alerts", "responseCode": 200}, "input": {"shape": "ListAlertsRequest"}, "output": {"shape": "ListAlertsResponse"}, "documentation": "<p>Lists the alerts that are associated with a MediaTailor channel assembly resource.</p>"}, "ListChannels": {"name": "ListChannels", "http": {"method": "GET", "requestUri": "/channels", "responseCode": 200}, "input": {"shape": "ListChannelsRequest"}, "output": {"shape": "ListChannelsResponse"}, "documentation": "<p>Retrieves information about the channels that are associated with the current AWS account.</p>"}, "ListLiveSources": {"name": "ListLiveSources", "http": {"method": "GET", "requestUri": "/sourceLocation/{SourceLocationName}/liveSources", "responseCode": 200}, "input": {"shape": "ListLiveSourcesRequest"}, "output": {"shape": "ListLiveSourcesResponse"}, "documentation": "<p>Lists the live sources contained in a source location. A source represents a piece of content.</p>"}, "ListPlaybackConfigurations": {"name": "ListPlaybackConfigurations", "http": {"method": "GET", "requestUri": "/playbackConfigurations", "responseCode": 200}, "input": {"shape": "ListPlaybackConfigurationsRequest"}, "output": {"shape": "ListPlaybackConfigurationsResponse"}, "documentation": "<p>Retrieves existing playback configurations. For information about MediaTailor configurations, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/configurations.html\">Working with Configurations in AWS Elemental MediaTailor</a>.</p>"}, "ListPrefetchSchedules": {"name": "ListPrefetchSchedules", "http": {"method": "POST", "requestUri": "/prefetchSchedule/{PlaybackConfigurationName}", "responseCode": 200}, "input": {"shape": "ListPrefetchSchedulesRequest"}, "output": {"shape": "ListPrefetchSchedulesResponse"}, "documentation": "<p>Lists the prefetch schedules for a playback configuration.</p>"}, "ListSourceLocations": {"name": "ListSourceLocations", "http": {"method": "GET", "requestUri": "/sourceLocations", "responseCode": 200}, "input": {"shape": "ListSourceLocationsRequest"}, "output": {"shape": "ListSourceLocationsResponse"}, "documentation": "<p>Lists the source locations for a channel. A source location defines the host server URL, and contains a list of sources.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{ResourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "BadRequestException"}], "documentation": "<p>A list of tags that are associated with this resource. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>"}, "ListVodSources": {"name": "ListVodSources", "http": {"method": "GET", "requestUri": "/sourceLocation/{SourceLocationName}/vodSources", "responseCode": 200}, "input": {"shape": "ListVodSourcesRequest"}, "output": {"shape": "ListVodSourcesResponse"}, "documentation": "<p>Lists the VOD sources contained in a source location. A source represents a piece of content.</p>"}, "PutChannelPolicy": {"name": "PutChannelPolicy", "http": {"method": "PUT", "requestUri": "/channel/{ChannelName}/policy", "responseCode": 200}, "input": {"shape": "PutChannelPolicyRequest"}, "output": {"shape": "PutChannelPolicyResponse"}, "documentation": "<p>Creates an IAM policy for the channel. IAM policies are used to control access to your channel.</p>", "idempotent": true}, "PutPlaybackConfiguration": {"name": "PutPlaybackConfiguration", "http": {"method": "PUT", "requestUri": "/playbackConfiguration", "responseCode": 200}, "input": {"shape": "PutPlaybackConfigurationRequest"}, "output": {"shape": "PutPlaybackConfigurationResponse"}, "documentation": "<p>Creates a playback configuration. For information about MediaTailor configurations, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/configurations.html\">Working with configurations in AWS Elemental MediaTailor</a>.</p>", "idempotent": true}, "StartChannel": {"name": "StartChannel", "http": {"method": "PUT", "requestUri": "/channel/{ChannelName}/start", "responseCode": 200}, "input": {"shape": "StartChannelRequest"}, "output": {"shape": "StartChannelResponse"}, "documentation": "<p>Starts a channel. For information about MediaTailor channels, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/channel-assembly-channels.html\">Working with channels</a> in the <i>MediaTailor User Guide</i>.</p>", "idempotent": true}, "StopChannel": {"name": "StopChannel", "http": {"method": "PUT", "requestUri": "/channel/{ChannelName}/stop", "responseCode": 200}, "input": {"shape": "StopChannelRequest"}, "output": {"shape": "StopChannelResponse"}, "documentation": "<p>Stops a channel. For information about MediaTailor channels, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/channel-assembly-channels.html\">Working with channels</a> in the <i>MediaTailor User Guide</i>.</p>", "idempotent": true}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{ResourceArn}", "responseCode": 204}, "input": {"shape": "TagResourceRequest"}, "errors": [{"shape": "BadRequestException"}], "documentation": "<p>The resource to tag. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{ResourceArn}", "responseCode": 204}, "input": {"shape": "UntagResourceRequest"}, "errors": [{"shape": "BadRequestException"}], "documentation": "<p>The resource to untag.</p>", "idempotent": true}, "UpdateChannel": {"name": "UpdateChannel", "http": {"method": "PUT", "requestUri": "/channel/{ChannelName}", "responseCode": 200}, "input": {"shape": "UpdateChannelRequest"}, "output": {"shape": "UpdateChannelResponse"}, "documentation": "<p>Updates a channel. For information about MediaTailor channels, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/channel-assembly-channels.html\">Working with channels</a> in the <i>MediaTailor User Guide</i>.</p>", "idempotent": true}, "UpdateLiveSource": {"name": "UpdateLiveSource", "http": {"method": "PUT", "requestUri": "/sourceLocation/{SourceLocationName}/liveSource/{LiveSourceName}", "responseCode": 200}, "input": {"shape": "UpdateLiveSourceRequest"}, "output": {"shape": "UpdateLiveSourceResponse"}, "documentation": "<p>Updates a live source's configuration.</p>", "idempotent": true}, "UpdateProgram": {"name": "UpdateProgram", "http": {"method": "PUT", "requestUri": "/channel/{ChannelName}/program/{ProgramName}", "responseCode": 200}, "input": {"shape": "UpdateProgramRequest"}, "output": {"shape": "UpdateProgramResponse"}, "documentation": "<p>Updates a program within a channel.</p>", "idempotent": true}, "UpdateSourceLocation": {"name": "UpdateSourceLocation", "http": {"method": "PUT", "requestUri": "/sourceLocation/{SourceLocationName}", "responseCode": 200}, "input": {"shape": "UpdateSourceLocationRequest"}, "output": {"shape": "UpdateSourceLocationResponse"}, "documentation": "<p>Updates a source location. A source location is a container for sources. For more information about source locations, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/channel-assembly-source-locations.html\">Working with source locations</a> in the <i>MediaTailor User Guide</i>.</p>", "idempotent": true}, "UpdateVodSource": {"name": "UpdateVodSource", "http": {"method": "PUT", "requestUri": "/sourceLocation/{SourceLocationName}/vodSource/{VodSourceName}", "responseCode": 200}, "input": {"shape": "UpdateVodSourceRequest"}, "output": {"shape": "UpdateVodSourceResponse"}, "documentation": "<p>Updates a VOD source's configuration.</p>", "idempotent": true}}, "shapes": {"AccessConfiguration": {"type": "structure", "members": {"AccessType": {"shape": "AccessType", "documentation": "<p>The type of authentication used to access content from <code>HttpConfiguration::BaseUrl</code> on your source location.</p> <p> <code>S3_SIGV4</code> - AWS Signature Version 4 authentication for Amazon S3 hosted virtual-style access. If your source location base URL is an Amazon S3 bucket, MediaTailor can use AWS Signature Version 4 (SigV4) authentication to access the bucket where your source content is stored. Your MediaTailor source location baseURL must follow the S3 virtual hosted-style request URL format. For example, https://bucket-name.s3.Region.amazonaws.com/key-name.</p> <p>Before you can use <code>S3_SIGV4</code>, you must meet these requirements:</p> <p>• You must allow MediaTailor to access your S3 bucket by granting mediatailor.amazonaws.com principal access in IAM. For information about configuring access in IAM, see Access management in the IAM User Guide.</p> <p>• The mediatailor.amazonaws.com service principal must have permissions to read all top level manifests referenced by the VodSource packaging configurations.</p> <p>• The caller of the API must have s3:GetObject IAM permissions to read all top level manifests referenced by your MediaTailor VodSource packaging configurations.</p> <p> <code>AUTODETECT_SIGV4</code> - AWS Signature Version 4 authentication for a set of supported services: MediaPackage Version 2 and Amazon S3 hosted virtual-style access. If your source location base URL is a MediaPackage Version 2 endpoint or an Amazon S3 bucket, MediaTailor can use AWS Signature Version 4 (SigV4) authentication to access the resource where your source content is stored.</p> <p>Before you can use <code>AUTODETECT_SIGV4</code> with a MediaPackage Version 2 endpoint, you must meet these requirements:</p> <p>• You must grant MediaTailor access to your MediaPackage endpoint by granting <code>mediatailor.amazonaws.com</code> principal access in an Origin Access policy on the endpoint.</p> <p>• Your MediaTailor source location base URL must be a MediaPackage V2 endpoint.</p> <p>• The caller of the API must have <code>mediapackagev2:GetObject</code> IAM permissions to read all top level manifests referenced by the MediaTailor source packaging configurations.</p> <p>Before you can use <code>AUTODETECT_SIGV4</code> with an Amazon S3 bucket, you must meet these requirements:</p> <p>• You must grant MediaTailor access to your S3 bucket by granting <code>mediatailor.amazonaws.com</code> principal access in IAM. For more information about configuring access in IAM, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access.html\">Access management</a> in the <i>IAM User Guide.</i>.</p> <p>• The <code>mediatailor.amazonaws.com</code> service principal must have permissions to read all top-level manifests referenced by the <code>VodSource</code> packaging configurations.</p> <p>• The caller of the API must have <code>s3:GetObject</code> IAM permissions to read all top level manifests referenced by your MediaTailor <code>VodSource</code> packaging configurations.</p>"}, "SecretsManagerAccessTokenConfiguration": {"shape": "SecretsManagerAccessTokenConfiguration", "documentation": "<p>AWS Secrets Manager access token configuration parameters.</p>"}}, "documentation": "<p>Access configuration parameters.</p>"}, "AccessType": {"type": "string", "enum": ["S3_SIGV4", "SECRETS_MANAGER_ACCESS_TOKEN", "AUTODETECT_SIGV4"]}, "AdBreak": {"type": "structure", "members": {"AdBreakMetadata": {"shape": "AdBreakMetadataList", "documentation": "<p>Defines a list of key/value pairs that MediaTailor generates within the <code>EXT-X-ASSET</code>tag for <code>SCTE35_ENHANCED</code> output.</p>"}, "MessageType": {"shape": "MessageType", "documentation": "<p>The SCTE-35 ad insertion type. Accepted value: <code>SPLICE_INSERT</code>, <code>TIME_SIGNAL</code>.</p>"}, "OffsetMillis": {"shape": "__long", "documentation": "<p>How long (in milliseconds) after the beginning of the program that an ad starts. This value must fall within 100ms of a segment boundary, otherwise the ad break will be skipped.</p>"}, "Slate": {"shape": "SlateSource", "documentation": "<p>Ad break slate configuration.</p>"}, "SpliceInsertMessage": {"shape": "SpliceInsertMessage", "documentation": "<p>This defines the SCTE-35 <code>splice_insert()</code> message inserted around the ad. For information about using <code>splice_insert()</code>, see the SCTE-35 specficiaiton, section *******.</p>"}, "TimeSignalMessage": {"shape": "TimeSignalMessage", "documentation": "<p>Defines the SCTE-35 <code>time_signal</code> message inserted around the ad.</p> <p>Programs on a channel's schedule can be configured with one or more ad breaks. You can attach a <code>splice_insert</code> SCTE-35 message to the ad break. This message provides basic metadata about the ad break.</p> <p>See section 9.7.4 of the 2022 SCTE-35 specification for more information.</p>"}}, "documentation": "<p>Ad break configuration parameters.</p>"}, "AdBreakMetadataList": {"type": "list", "member": {"shape": "KeyValuePair"}}, "AdBreakOpportunities": {"type": "list", "member": {"shape": "AdBreakOpportunity"}, "documentation": "<p>The list of ad break opportunities detected within the VOD source.</p>"}, "AdBreakOpportunity": {"type": "structure", "required": ["OffsetMillis"], "members": {"OffsetMillis": {"shape": "__long", "documentation": "<p>The offset in milliseconds from the start of the VOD source at which an ad marker was detected.</p>"}}, "documentation": "<p>A location at which a zero-duration ad marker was detected in a VOD source manifest.</p>"}, "AdMarkerPassthrough": {"type": "structure", "members": {"Enabled": {"shape": "__boolean", "documentation": "<p>Enables ad marker passthrough for your configuration.</p>"}}, "documentation": "<p>For HLS, when set to <code>true</code>, MediaTailor passes through <code>EXT-X-CUE-IN</code>, <code>EXT-X-CUE-OUT</code>, and <code>EXT-X-SPLICEPOINT-SCTE35</code> ad markers from the origin manifest to the MediaTailor personalized manifest.</p> <p>No logic is applied to these ad markers. For example, if <code>EXT-X-CUE-OUT</code> has a value of <code>60</code>, but no ads are filled for that ad break, MediaTailor will not set the value to <code>0</code>.</p>"}, "AdMarkupType": {"type": "string", "enum": ["DATERANGE", "SCTE35_ENHANCED"]}, "Alert": {"type": "structure", "required": ["AlertCode", "AlertM<PERSON>age", "LastModifiedTime", "RelatedResourceArns", "ResourceArn"], "members": {"AlertCode": {"shape": "__string", "documentation": "<p>The code for the alert. For example, <code>NOT_PROCESSED</code>.</p>"}, "AlertMessage": {"shape": "__string", "documentation": "<p>If an alert is generated for a resource, an explanation of the reason for the alert.</p>"}, "Category": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The category that MediaTailor assigns to the alert.</p>"}, "LastModifiedTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp when the alert was last modified.</p>"}, "RelatedResourceArns": {"shape": "__listOf__string", "documentation": "<p>The Amazon Resource Names (ARNs) related to this alert.</p>"}, "ResourceArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>"}}, "documentation": "<p>Alert configuration parameters.</p>"}, "AlertCategory": {"type": "string", "enum": ["SCHEDULING_ERROR", "PLAYBACK_WARNING", "INFO"]}, "AvailMatchingCriteria": {"type": "structure", "required": ["DynamicVariable", "Operator"], "members": {"DynamicVariable": {"shape": "__string", "documentation": "<p>The dynamic variable(s) that MediaTailor should use as avail matching criteria. MediaTailor only places the prefetched ads into the avail if the avail matches the criteria defined by the dynamic variable. For information about dynamic variables, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/variables.html\">Using dynamic ad variables</a> in the <i>MediaTailor User Guide</i>.</p> <p>You can include up to 100 dynamic variables.</p>"}, "Operator": {"shape": "Operator", "documentation": "<p>For the <code>DynamicVariable</code> specified in <code>AvailMatchingCriteria</code>, the Operator that is used for the comparison.</p>"}}, "documentation": "<p>MediaTailor only places (consumes) prefetched ads if the ad break meets the criteria defined by the dynamic variables. This gives you granular control over which ad break to place the prefetched ads into.</p> <p>As an example, let's say that you set <code>DynamicVariable</code> to <code>scte.event_id</code> and <code>Operator</code> to <code>EQUALS</code>, and your playback configuration has an ADS URL of <code>https://my.ads.server.com/path?&amp;podId=[scte.avail_num]&amp;event=[scte.event_id]&amp;duration=[session.avail_duration_secs]</code>. And the prefetch request to the ADS contains these values <code>https://my.ads.server.com/path?&amp;podId=3&amp;event=my-awesome-event&amp;duration=30</code>. MediaTailor will only insert the prefetched ads into the ad break if has a SCTE marker with an event id of <code>my-awesome-event</code>, since it must match the event id that MediaTailor uses to query the ADS.</p> <p>You can specify up to five <code>AvailMatchingCriteria</code>. If you specify multiple <code>AvailMatchingCriteria</code>, MediaTailor combines them to match using a logical <code>AND</code>. You can model logical <code>OR</code> combinations by creating multiple prefetch schedules.</p>"}, "AvailSuppression": {"type": "structure", "members": {"FillPolicy": {"shape": "FillPolicy", "documentation": "<p>Defines the policy to apply to the avail suppression mode. <code>BEHIND_LIVE_EDGE</code> will always use the full avail suppression policy. <code>AFTER_LIVE_EDGE</code> mode can be used to invoke partial ad break fills when a session starts mid-break.</p>"}, "Mode": {"shape": "Mode", "documentation": "<p>Sets the ad suppression mode. By default, ad suppression is off and all ad breaks are filled with ads or slate. When Mode is set to <code>BEHIND_LIVE_EDGE</code>, ad suppression is active and MediaTailor won't fill ad breaks on or behind the ad suppression Value time in the manifest lookback window. When Mode is set to <code>AFTER_LIVE_EDGE</code>, ad suppression is active and MediaTailor won't fill ad breaks that are within the live edge plus the avail suppression value.</p>"}, "Value": {"shape": "__string", "documentation": "<p>A live edge offset time in HH:MM:SS. MediaTailor won't fill ad breaks on or behind this time in the manifest lookback window. If Value is set to 00:00:00, it is in sync with the live edge, and MediaTailor won't fill any ad breaks on or behind the live edge. If you set a Value time, MediaTailor won't fill any ad breaks on or behind this time in the manifest lookback window. For example, if you set 00:45:00, then MediaTailor will fill ad breaks that occur within 45 minutes behind the live edge, but won't fill ad breaks on or behind 45 minutes behind the live edge.</p>"}}, "documentation": "<p>The configuration for avail suppression, also known as ad suppression. For more information about ad suppression, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/ad-behavior.html\">Ad Suppression</a>.</p>"}, "BadRequestException": {"type": "structure", "members": {"Message": {"shape": "__string"}}, "documentation": "<p>A request contains unexpected data.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "Bumper": {"type": "structure", "members": {"EndUrl": {"shape": "__string", "documentation": "<p>The URL for the end bumper asset.</p>"}, "StartUrl": {"shape": "__string", "documentation": "<p>The URL for the start bumper asset.</p>"}}, "documentation": "<p>The configuration for bumpers. Bumpers are short audio or video clips that play at the start or before the end of an ad break. To learn more about bumpers, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/bumpers.html\">Bumpers</a>.</p>"}, "CdnConfiguration": {"type": "structure", "members": {"AdSegmentUrlPrefix": {"shape": "__string", "documentation": "<p>A non-default content delivery network (CDN) to serve ad segments. By default, AWS Elemental MediaTailor uses Amazon CloudFront with default cache settings as its CDN for ad segments. To set up an alternate CDN, create a rule in your CDN for the origin ads.mediatailor.<i>&lt;region&gt;</i>.amazonaws.com. Then specify the rule's name in this <code>AdSegmentUrlPrefix</code>. When AWS Elemental MediaTailor serves a manifest, it reports your CDN as the source for ad segments.</p>"}, "ContentSegmentUrlPrefix": {"shape": "__string", "documentation": "<p>A content delivery network (CDN) to cache content segments, so that content requests don’t always have to go to the origin server. First, create a rule in your CDN for the content segment origin server. Then specify the rule's name in this <code>ContentSegmentUrlPrefix</code>. When AWS Elemental MediaTailor serves a manifest, it reports your CDN as the source for content segments.</p>"}}, "documentation": "<p>The configuration for using a content delivery network (CDN), like Amazon CloudFront, for content and ad segment management.</p>"}, "Channel": {"type": "structure", "required": ["<PERSON><PERSON>", "ChannelName", "ChannelState", "LogConfiguration", "Outputs", "PlaybackMode", "Tier"], "members": {"Arn": {"shape": "__string", "documentation": "<p>The ARN of the channel.</p>"}, "ChannelName": {"shape": "__string", "documentation": "<p>The name of the channel.</p>"}, "ChannelState": {"shape": "__string", "documentation": "<p>Returns the state whether the channel is running or not.</p>"}, "CreationTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp of when the channel was created.</p>"}, "FillerSlate": {"shape": "SlateSource", "documentation": "<p>The slate used to fill gaps between programs in the schedule. You must configure filler slate if your channel uses the <code>LINEAR</code> <code>PlaybackMode</code>. MediaTailor doesn't support filler slate for channels using the <code>LOOP</code> <code>PlaybackMode</code>.</p>"}, "LastModifiedTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp of when the channel was last modified.</p>"}, "LogConfiguration": {"shape": "LogConfigurationForChannel", "documentation": "<p>The log configuration.</p>"}, "Outputs": {"shape": "ResponseOutputs", "documentation": "<p>The channel's output properties.</p>"}, "PlaybackMode": {"shape": "__string", "documentation": "<p>The type of playback mode for this channel.</p> <p> <code>LINEAR</code> - Programs play back-to-back only once.</p> <p> <code>LOOP</code> - Programs play back-to-back in an endless loop. When the last program in the schedule plays, playback loops back to the first program in the schedule.</p>"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags to assign to the channel. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}, "Tier": {"shape": "__string", "documentation": "<p>The tier for this channel. STANDARD tier channels can contain live programs.</p>"}}, "documentation": "<p>The configuration parameters for a channel. For information about MediaTailor channels, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/channel-assembly-channels.html\">Working with channels</a> in the <i>MediaTailor User Guide</i>.</p>"}, "ChannelState": {"type": "string", "enum": ["RUNNING", "STOPPED"]}, "ClipRange": {"type": "structure", "required": ["EndOffsetMillis"], "members": {"EndOffsetMillis": {"shape": "__long", "documentation": "<p>The end offset of the clip range, in milliseconds, starting from the beginning of the VOD source associated with the program.</p>"}}, "documentation": "<p>Clip range configuration for the VOD source associated with the program.</p>"}, "ConfigurationAliasesRequest": {"type": "map", "key": {"shape": "__string", "documentation": "<p>The dynamic variable that has aliases.</p>"}, "value": {"shape": "__mapOf__string", "documentation": "<p>Map of aliases to the value to be used at request time.</p>"}, "documentation": "<p>The predefined aliases for dynamic variables.</p>"}, "ConfigurationAliasesResponse": {"type": "map", "key": {"shape": "__string", "documentation": "<p>The dynamic variable that has aliases.</p>"}, "value": {"shape": "__mapOf__string", "documentation": "<p>Map of aliases to the value to be used at request time.</p>"}, "documentation": "<p>The predefined aliases for dynamic variables.</p>"}, "ConfigureLogsForChannelRequest": {"type": "structure", "required": ["ChannelName", "LogTypes"], "members": {"ChannelName": {"shape": "__string", "documentation": "<p>The name of the channel.</p>"}, "LogTypes": {"shape": "LogTypes", "documentation": "<p>The types of logs to collect.</p>"}}}, "ConfigureLogsForChannelResponse": {"type": "structure", "members": {"ChannelName": {"shape": "__string", "documentation": "<p>The name of the channel.</p>"}, "LogTypes": {"shape": "LogTypes", "documentation": "<p>The types of logs collected.</p>"}}}, "ConfigureLogsForPlaybackConfigurationRequest": {"type": "structure", "required": ["PercentEnabled", "PlaybackConfigurationName"], "members": {"PercentEnabled": {"shape": "__integer", "documentation": "<p>The percentage of session logs that MediaTailor sends to your Cloudwatch Logs account. For example, if your playback configuration has 1000 sessions and percentEnabled is set to <code>60</code>, MediaTailor sends logs for 600 of the sessions to CloudWatch Logs. MediaTailor decides at random which of the playback configuration sessions to send logs for. If you want to view logs for a specific session, you can use the <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/debug-log-mode.html\">debug log mode</a>.</p> <p>Valid values: <code>0</code> - <code>100</code> </p>"}, "PlaybackConfigurationName": {"shape": "__string", "documentation": "<p>The name of the playback configuration.</p>"}}, "documentation": "<p>Configures Amazon CloudWatch log settings for a playback configuration.</p>"}, "ConfigureLogsForPlaybackConfigurationResponse": {"type": "structure", "required": ["PercentEnabled"], "members": {"PercentEnabled": {"shape": "__integer", "documentation": "<p>The percentage of session logs that MediaTailor sends to your Cloudwatch Logs account.</p>"}, "PlaybackConfigurationName": {"shape": "__string", "documentation": "<p>The name of the playback configuration.</p>"}}}, "CreateChannelRequest": {"type": "structure", "required": ["ChannelName", "Outputs", "PlaybackMode"], "members": {"ChannelName": {"shape": "__string", "documentation": "<p>The name of the channel.</p>", "location": "uri", "locationName": "ChannelName"}, "FillerSlate": {"shape": "SlateSource", "documentation": "<p>The slate used to fill gaps between programs in the schedule. You must configure filler slate if your channel uses the <code>LINEAR</code> <code>PlaybackMode</code>. MediaTailor doesn't support filler slate for channels using the <code>LOOP</code> <code>PlaybackMode</code>.</p>"}, "Outputs": {"shape": "RequestOutputs", "documentation": "<p>The channel's output properties.</p>"}, "PlaybackMode": {"shape": "PlaybackMode", "documentation": "<p>The type of playback mode to use for this channel.</p> <p> <code>LINEAR</code> - The programs in the schedule play once back-to-back in the schedule.</p> <p> <code>LOOP</code> - The programs in the schedule play back-to-back in an endless loop. When the last program in the schedule stops playing, playback loops back to the first program in the schedule.</p>"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags to assign to the channel. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}, "Tier": {"shape": "Tier", "documentation": "<p>The tier of the channel.</p>"}}}, "CreateChannelResponse": {"type": "structure", "members": {"Arn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) to assign to the channel.</p>"}, "ChannelName": {"shape": "__string", "documentation": "<p>The name to assign to the channel.</p>"}, "ChannelState": {"shape": "ChannelState", "documentation": "<p>Indicates whether the channel is in a running state or not.</p>"}, "CreationTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp of when the channel was created.</p>"}, "FillerSlate": {"shape": "SlateSource", "documentation": "<p>Contains information about the slate used to fill gaps between programs in the schedule.</p>"}, "LastModifiedTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp of when the channel was last modified.</p>"}, "Outputs": {"shape": "ResponseOutputs", "documentation": "<p>The output properties to assign to the channel.</p>"}, "PlaybackMode": {"shape": "__string", "documentation": "<p>The playback mode to assign to the channel.</p>"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags to assign to the channel. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}, "Tier": {"shape": "__string", "documentation": "<p>The tier of the channel.</p>"}}}, "CreateLiveSourceRequest": {"type": "structure", "required": ["HttpPackageConfigurations", "LiveSourceName", "SourceLocationName"], "members": {"HttpPackageConfigurations": {"shape": "HttpPackageConfigurations", "documentation": "<p>A list of HTTP package configuration parameters for this live source.</p>"}, "LiveSourceName": {"shape": "__string", "documentation": "<p>The name of the live source.</p>", "location": "uri", "locationName": "LiveSourceName"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location.</p>", "location": "uri", "locationName": "SourceLocationName"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags to assign to the live source. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}}}, "CreateLiveSourceResponse": {"type": "structure", "members": {"Arn": {"shape": "__string", "documentation": "<p>The ARN to assign to the live source.</p>"}, "CreationTime": {"shape": "__timestampUnix", "documentation": "<p>The time the live source was created.</p>"}, "HttpPackageConfigurations": {"shape": "HttpPackageConfigurations", "documentation": "<p>A list of HTTP package configuration parameters for this live source.</p>"}, "LastModifiedTime": {"shape": "__timestampUnix", "documentation": "<p>The time the live source was last modified.</p>"}, "LiveSourceName": {"shape": "__string", "documentation": "<p>The name to assign to the live source.</p>"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name to assign to the source location of the live source.</p>"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags to assign to the live source. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}}}, "CreatePrefetchScheduleRequest": {"type": "structure", "required": ["Consumption", "Name", "PlaybackConfigurationName", "Retrieval"], "members": {"Consumption": {"shape": "PrefetchConsumption", "documentation": "<p>The configuration settings for MediaTailor's <i>consumption</i> of the prefetched ads from the ad decision server. Each consumption configuration contains an end time and an optional start time that define the <i>consumption window</i>. Prefetch schedules automatically expire no earlier than seven days after the end time.</p>"}, "Name": {"shape": "__string", "documentation": "<p>The name to assign to the schedule request.</p>", "location": "uri", "locationName": "Name"}, "PlaybackConfigurationName": {"shape": "__string", "documentation": "<p>The name to assign to the playback configuration.</p>", "location": "uri", "locationName": "PlaybackConfigurationName"}, "Retrieval": {"shape": "PrefetchRetrieval", "documentation": "<p>The configuration settings for retrieval of prefetched ads from the ad decision server. Only one set of prefetched ads will be retrieved and subsequently consumed for each ad break.</p>"}, "StreamId": {"shape": "__string", "documentation": "<p>An optional stream identifier that MediaTailor uses to prefetch ads for multiple streams that use the same playback configuration. If <code>StreamId</code> is specified, MediaTailor returns all of the prefetch schedules with an exact match on <code>StreamId</code>. If not specified, MediaTailor returns all of the prefetch schedules for the playback configuration, regardless of <code>StreamId</code>.</p>"}}}, "CreatePrefetchScheduleResponse": {"type": "structure", "members": {"Arn": {"shape": "__string", "documentation": "<p>The ARN to assign to the prefetch schedule.</p>"}, "Consumption": {"shape": "PrefetchConsumption", "documentation": "<p>The configuration settings for MediaTailor's <i>consumption</i> of the prefetched ads from the ad decision server. Each consumption configuration contains an end time and an optional start time that define the <i>consumption window</i>. Prefetch schedules automatically expire no earlier than seven days after the end time.</p>"}, "Name": {"shape": "__string", "documentation": "<p>The name to assign to the prefetch schedule.</p>"}, "PlaybackConfigurationName": {"shape": "__string", "documentation": "<p>The name to assign to the playback configuration.</p>"}, "Retrieval": {"shape": "PrefetchRetrieval", "documentation": "<p>The configuration settings for retrieval of prefetched ads from the ad decision server. Only one set of prefetched ads will be retrieved and subsequently consumed for each ad break.</p>"}, "StreamId": {"shape": "__string", "documentation": "<p>An optional stream identifier that MediaTailor uses to prefetch ads for multiple streams that use the same playback configuration. If <code>StreamId</code> is specified, MediaTailor returns all of the prefetch schedules with an exact match on <code>StreamId</code>. If not specified, MediaTailor returns all of the prefetch schedules for the playback configuration, regardless of <code>StreamId</code>.</p>"}}}, "CreateProgramRequest": {"type": "structure", "required": ["ChannelName", "ProgramName", "ScheduleConfiguration", "SourceLocationName"], "members": {"AdBreaks": {"shape": "__listOfAdBreak", "documentation": "<p>The ad break configuration settings.</p>"}, "ChannelName": {"shape": "__string", "documentation": "<p>The name of the channel for this Program.</p>", "location": "uri", "locationName": "ChannelName"}, "LiveSourceName": {"shape": "__string", "documentation": "<p>The name of the LiveSource for this Program.</p>"}, "ProgramName": {"shape": "__string", "documentation": "<p>The name of the <PERSON>.</p>", "location": "uri", "locationName": "ProgramName"}, "ScheduleConfiguration": {"shape": "ScheduleConfiguration", "documentation": "<p>The schedule configuration settings.</p>"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location.</p>"}, "VodSourceName": {"shape": "__string", "documentation": "<p>The name that's used to refer to a VOD source.</p>"}}}, "CreateProgramResponse": {"type": "structure", "members": {"AdBreaks": {"shape": "__listOfAdBreak", "documentation": "<p>The ad break configuration settings.</p>"}, "Arn": {"shape": "__string", "documentation": "<p>The ARN to assign to the program.</p>"}, "ChannelName": {"shape": "__string", "documentation": "<p>The name to assign to the channel for this program.</p>"}, "ClipRange": {"shape": "ClipRange", "documentation": "<p>The clip range configuration settings.</p>"}, "CreationTime": {"shape": "__timestampUnix", "documentation": "<p>The time the program was created.</p>"}, "DurationMillis": {"shape": "__long", "documentation": "<p>The duration of the live program in milliseconds.</p>"}, "LiveSourceName": {"shape": "__string", "documentation": "<p>The name of the LiveSource for this Program.</p>"}, "ProgramName": {"shape": "__string", "documentation": "<p>The name to assign to this program.</p>"}, "ScheduledStartTime": {"shape": "__timestampUnix", "documentation": "<p>The scheduled start time for this Program.</p>"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name to assign to the source location for this program.</p>"}, "VodSourceName": {"shape": "__string", "documentation": "<p>The name that's used to refer to a VOD source.</p>"}}}, "CreateSourceLocationRequest": {"type": "structure", "required": ["HttpConfiguration", "SourceLocationName"], "members": {"AccessConfiguration": {"shape": "AccessConfiguration", "documentation": "<p>Access configuration parameters. Configures the type of authentication used to access content from your source location.</p>"}, "DefaultSegmentDeliveryConfiguration": {"shape": "DefaultSegmentDeliveryConfiguration", "documentation": "<p>The optional configuration for the server that serves segments.</p>"}, "HttpConfiguration": {"shape": "HttpConfiguration", "documentation": "<p>The source's HTTP package configurations.</p>"}, "SegmentDeliveryConfigurations": {"shape": "__listOfSegmentDeliveryConfiguration", "documentation": "<p>A list of the segment delivery configurations associated with this resource.</p>"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name associated with the source location.</p>", "location": "uri", "locationName": "SourceLocationName"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags to assign to the source location. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}}}, "CreateSourceLocationResponse": {"type": "structure", "members": {"AccessConfiguration": {"shape": "AccessConfiguration", "documentation": "<p>Access configuration parameters. Configures the type of authentication used to access content from your source location.</p>"}, "Arn": {"shape": "__string", "documentation": "<p>The ARN to assign to the source location.</p>"}, "CreationTime": {"shape": "__timestampUnix", "documentation": "<p>The time the source location was created.</p>"}, "DefaultSegmentDeliveryConfiguration": {"shape": "DefaultSegmentDeliveryConfiguration", "documentation": "<p>The optional configuration for the server that serves segments.</p>"}, "HttpConfiguration": {"shape": "HttpConfiguration", "documentation": "<p>The source's HTTP package configurations.</p>"}, "LastModifiedTime": {"shape": "__timestampUnix", "documentation": "<p>The time the source location was last modified.</p>"}, "SegmentDeliveryConfigurations": {"shape": "__listOfSegmentDeliveryConfiguration", "documentation": "<p>The segment delivery configurations for the source location. For information about MediaTailor configurations, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/configurations.html\">Working with configurations in AWS Elemental MediaTailor</a>.</p>"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name to assign to the source location.</p>"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags to assign to the source location. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}}}, "CreateVodSourceRequest": {"type": "structure", "required": ["HttpPackageConfigurations", "SourceLocationName", "VodSourceName"], "members": {"HttpPackageConfigurations": {"shape": "HttpPackageConfigurations", "documentation": "<p>A list of HTTP package configuration parameters for this VOD source.</p>"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location for this VOD source.</p>", "location": "uri", "locationName": "SourceLocationName"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags to assign to the VOD source. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}, "VodSourceName": {"shape": "__string", "documentation": "<p>The name associated with the VOD source.&gt;</p>", "location": "uri", "locationName": "VodSourceName"}}}, "CreateVodSourceResponse": {"type": "structure", "members": {"Arn": {"shape": "__string", "documentation": "<p>The ARN to assign to this VOD source.</p>"}, "CreationTime": {"shape": "__timestampUnix", "documentation": "<p>The time the VOD source was created.</p>"}, "HttpPackageConfigurations": {"shape": "HttpPackageConfigurations", "documentation": "<p>A list of HTTP package configuration parameters for this VOD source.</p>"}, "LastModifiedTime": {"shape": "__timestampUnix", "documentation": "<p>The time the VOD source was last modified.</p>"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name to assign to the source location for this VOD source.</p>"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags to assign to the VOD source. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}, "VodSourceName": {"shape": "__string", "documentation": "<p>The name to assign to the VOD source.</p>"}}}, "DashConfiguration": {"type": "structure", "members": {"ManifestEndpointPrefix": {"shape": "__string", "documentation": "<p>The URL generated by MediaTailor to initiate a playback session. The session uses server-side reporting. This setting is ignored in PUT operations.</p>"}, "MpdLocation": {"shape": "__string", "documentation": "<p>The setting that controls whether MediaTailor includes the Location tag in DASH manifests. MediaTailor populates the Location tag with the URL for manifest update requests, to be used by players that don't support sticky redirects. Disable this if you have CDN routing rules set up for accessing MediaTailor manifests, and you are either using client-side reporting or your players support sticky HTTP redirects. Valid values are <code>DISABLED</code> and <code>EMT_DEFAULT</code>. The <code>EMT_DEFAULT</code> setting enables the inclusion of the tag and is the default value.</p>"}, "OriginManifestType": {"shape": "OriginManifestType", "documentation": "<p>The setting that controls whether MediaTailor handles manifests from the origin server as multi-period manifests or single-period manifests. If your origin server produces single-period manifests, set this to <code>SINGLE_PERIOD</code>. The default setting is <code>MULTI_PERIOD</code>. For multi-period manifests, omit this setting or set it to <code>MULTI_PERIOD</code>.</p>"}}, "documentation": "<p>The configuration for DASH content.</p>"}, "DashConfigurationForPut": {"type": "structure", "members": {"MpdLocation": {"shape": "__string", "documentation": "<p>The setting that controls whether MediaTailor includes the Location tag in DASH manifests. MediaTailor populates the Location tag with the URL for manifest update requests, to be used by players that don't support sticky redirects. Disable this if you have CDN routing rules set up for accessing MediaTailor manifests, and you are either using client-side reporting or your players support sticky HTTP redirects. Valid values are <code>DISABLED</code> and <code>EMT_DEFAULT</code>. The <code>EMT_DEFAULT</code> setting enables the inclusion of the tag and is the default value.</p>"}, "OriginManifestType": {"shape": "OriginManifestType", "documentation": "<p>The setting that controls whether MediaTailor handles manifests from the origin server as multi-period manifests or single-period manifests. If your origin server produces single-period manifests, set this to <code>SINGLE_PERIOD</code>. The default setting is <code>MULTI_PERIOD</code>. For multi-period manifests, omit this setting or set it to <code>MULTI_PERIOD</code>.</p>"}}, "documentation": "<p>The configuration for DASH PUT operations.</p>"}, "DashPlaylistSettings": {"type": "structure", "members": {"ManifestWindowSeconds": {"shape": "__integer", "documentation": "<p>The total duration (in seconds) of each manifest. Minimum value: <code>30</code> seconds. Maximum value: <code>3600</code> seconds.</p>"}, "MinBufferTimeSeconds": {"shape": "__integer", "documentation": "<p>Minimum amount of content (measured in seconds) that a player must keep available in the buffer. Minimum value: <code>2</code> seconds. Maximum value: <code>60</code> seconds.</p>"}, "MinUpdatePeriodSeconds": {"shape": "__integer", "documentation": "<p>Minimum amount of time (in seconds) that the player should wait before requesting updates to the manifest. Minimum value: <code>2</code> seconds. Maximum value: <code>60</code> seconds.</p>"}, "SuggestedPresentationDelaySeconds": {"shape": "__integer", "documentation": "<p>Amount of time (in seconds) that the player should be from the live point at the end of the manifest. Minimum value: <code>2</code> seconds. Maximum value: <code>60</code> seconds.</p>"}}, "documentation": "<p>Dash manifest configuration parameters.</p>"}, "DefaultSegmentDeliveryConfiguration": {"type": "structure", "members": {"BaseUrl": {"shape": "__string", "documentation": "<p>The hostname of the server that will be used to serve segments. This string must include the protocol, such as <b>https://</b>.</p>"}}, "documentation": "<p>The optional configuration for a server that serves segments. Use this if you want the segment delivery server to be different from the source location server. For example, you can configure your source location server to be an origination server, such as MediaPackage, and the segment delivery server to be a content delivery network (CDN), such as CloudFront. If you don't specify a segment delivery server, then the source location server is used.</p>"}, "DeleteChannelPolicyRequest": {"type": "structure", "required": ["ChannelName"], "members": {"ChannelName": {"shape": "__string", "documentation": "<p>The name of the channel associated with this channel policy.</p>", "location": "uri", "locationName": "ChannelName"}}}, "DeleteChannelPolicyResponse": {"type": "structure", "members": {}}, "DeleteChannelRequest": {"type": "structure", "required": ["ChannelName"], "members": {"ChannelName": {"shape": "__string", "documentation": "<p>The name of the channel.</p>", "location": "uri", "locationName": "ChannelName"}}}, "DeleteChannelResponse": {"type": "structure", "members": {}}, "DeleteLiveSourceRequest": {"type": "structure", "required": ["LiveSourceName", "SourceLocationName"], "members": {"LiveSourceName": {"shape": "__string", "documentation": "<p>The name of the live source.</p>", "location": "uri", "locationName": "LiveSourceName"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location associated with this Live Source.</p>", "location": "uri", "locationName": "SourceLocationName"}}}, "DeleteLiveSourceResponse": {"type": "structure", "members": {}}, "DeletePlaybackConfigurationRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "__string", "documentation": "<p>The name of the playback configuration.</p>", "location": "uri", "locationName": "Name"}}}, "DeletePlaybackConfigurationResponse": {"type": "structure", "members": {}}, "DeletePrefetchScheduleRequest": {"type": "structure", "required": ["Name", "PlaybackConfigurationName"], "members": {"Name": {"shape": "__string", "documentation": "<p>The name of the prefetch schedule. If the action is successful, the service sends back an HTTP 204 response with an empty HTTP body.</p>", "location": "uri", "locationName": "Name"}, "PlaybackConfigurationName": {"shape": "__string", "documentation": "<p>The name of the playback configuration for this prefetch schedule.</p>", "location": "uri", "locationName": "PlaybackConfigurationName"}}}, "DeletePrefetchScheduleResponse": {"type": "structure", "members": {}}, "DeleteProgramRequest": {"type": "structure", "required": ["ChannelName", "ProgramName"], "members": {"ChannelName": {"shape": "__string", "documentation": "<p>The name of the channel.</p>", "location": "uri", "locationName": "ChannelName"}, "ProgramName": {"shape": "__string", "documentation": "<p>The name of the program.</p>", "location": "uri", "locationName": "ProgramName"}}}, "DeleteProgramResponse": {"type": "structure", "members": {}}, "DeleteSourceLocationRequest": {"type": "structure", "required": ["SourceLocationName"], "members": {"SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location.</p>", "location": "uri", "locationName": "SourceLocationName"}}}, "DeleteSourceLocationResponse": {"type": "structure", "members": {}}, "DeleteVodSourceRequest": {"type": "structure", "required": ["SourceLocationName", "VodSourceName"], "members": {"SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location associated with this VOD Source.</p>", "location": "uri", "locationName": "SourceLocationName"}, "VodSourceName": {"shape": "__string", "documentation": "<p>The name of the VOD source.</p>", "location": "uri", "locationName": "VodSourceName"}}}, "DeleteVodSourceResponse": {"type": "structure", "members": {}}, "DescribeChannelRequest": {"type": "structure", "required": ["ChannelName"], "members": {"ChannelName": {"shape": "__string", "documentation": "<p>The name of the channel.</p>", "location": "uri", "locationName": "ChannelName"}}}, "DescribeChannelResponse": {"type": "structure", "required": ["LogConfiguration"], "members": {"Arn": {"shape": "__string", "documentation": "<p>The ARN of the channel.</p>"}, "ChannelName": {"shape": "__string", "documentation": "<p>The name of the channel.</p>"}, "ChannelState": {"shape": "ChannelState", "documentation": "<p>Indicates whether the channel is in a running state or not.</p>"}, "CreationTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp of when the channel was created.</p>"}, "FillerSlate": {"shape": "SlateSource", "documentation": "<p>Contains information about the slate used to fill gaps between programs in the schedule.</p>"}, "LastModifiedTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp of when the channel was last modified.</p>"}, "LogConfiguration": {"shape": "LogConfigurationForChannel", "documentation": "<p>The log configuration for the channel.</p>"}, "Outputs": {"shape": "ResponseOutputs", "documentation": "<p>The channel's output properties.</p>"}, "PlaybackMode": {"shape": "__string", "documentation": "<p>The channel's playback mode.</p>"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags assigned to the channel. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}, "Tier": {"shape": "__string", "documentation": "<p>The channel's tier.</p>"}}}, "DescribeLiveSourceRequest": {"type": "structure", "required": ["LiveSourceName", "SourceLocationName"], "members": {"LiveSourceName": {"shape": "__string", "documentation": "<p>The name of the live source.</p>", "location": "uri", "locationName": "LiveSourceName"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location associated with this Live Source.</p>", "location": "uri", "locationName": "SourceLocationName"}}}, "DescribeLiveSourceResponse": {"type": "structure", "members": {"Arn": {"shape": "__string", "documentation": "<p>The ARN of the live source.</p>"}, "CreationTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp that indicates when the live source was created.</p>"}, "HttpPackageConfigurations": {"shape": "HttpPackageConfigurations", "documentation": "<p>The HTTP package configurations.</p>"}, "LastModifiedTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp that indicates when the live source was modified.</p>"}, "LiveSourceName": {"shape": "__string", "documentation": "<p>The name of the live source.</p>"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location associated with the live source.</p>"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags assigned to the live source. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}}}, "DescribeProgramRequest": {"type": "structure", "required": ["ChannelName", "ProgramName"], "members": {"ChannelName": {"shape": "__string", "documentation": "<p>The name of the channel associated with this Program.</p>", "location": "uri", "locationName": "ChannelName"}, "ProgramName": {"shape": "__string", "documentation": "<p>The name of the program.</p>", "location": "uri", "locationName": "ProgramName"}}}, "DescribeProgramResponse": {"type": "structure", "members": {"AdBreaks": {"shape": "__listOfAdBreak", "documentation": "<p>The ad break configuration settings.</p>"}, "Arn": {"shape": "__string", "documentation": "<p>The ARN of the program.</p>"}, "ChannelName": {"shape": "__string", "documentation": "<p>The name of the channel that the program belongs to.</p>"}, "ClipRange": {"shape": "ClipRange", "documentation": "<p>The clip range configuration settings.</p>"}, "CreationTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp of when the program was created.</p>"}, "DurationMillis": {"shape": "<PERSON>", "documentation": "<p>The duration of the live program in milliseconds.</p>"}, "LiveSourceName": {"shape": "__string", "documentation": "<p>The name of the LiveSource for this Program.</p>"}, "ProgramName": {"shape": "__string", "documentation": "<p>The name of the program.</p>"}, "ScheduledStartTime": {"shape": "__timestampUnix", "documentation": "<p>The date and time that the program is scheduled to start in ISO 8601 format and Coordinated Universal Time (UTC). For example, the value 2021-03-27T17:48:16.751Z represents March 27, 2021 at 17:48:16.751 UTC.</p>"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The source location name.</p>"}, "VodSourceName": {"shape": "__string", "documentation": "<p>The name that's used to refer to a VOD source.</p>"}}}, "DescribeSourceLocationRequest": {"type": "structure", "required": ["SourceLocationName"], "members": {"SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location.</p>", "location": "uri", "locationName": "SourceLocationName"}}}, "DescribeSourceLocationResponse": {"type": "structure", "members": {"AccessConfiguration": {"shape": "AccessConfiguration", "documentation": "<p>The access configuration for the source location.</p>"}, "Arn": {"shape": "__string", "documentation": "<p>The ARN of the source location.</p>"}, "CreationTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp that indicates when the source location was created.</p>"}, "DefaultSegmentDeliveryConfiguration": {"shape": "DefaultSegmentDeliveryConfiguration", "documentation": "<p>The default segment delivery configuration settings.</p>"}, "HttpConfiguration": {"shape": "HttpConfiguration", "documentation": "<p>The HTTP package configuration settings for the source location.</p>"}, "LastModifiedTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp that indicates when the source location was last modified.</p>"}, "SegmentDeliveryConfigurations": {"shape": "__listOfSegmentDeliveryConfiguration", "documentation": "<p>A list of the segment delivery configurations associated with this resource.</p>"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location.</p>"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags assigned to the source location. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}}}, "DescribeVodSourceRequest": {"type": "structure", "required": ["SourceLocationName", "VodSourceName"], "members": {"SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location associated with this VOD Source.</p>", "location": "uri", "locationName": "SourceLocationName"}, "VodSourceName": {"shape": "__string", "documentation": "<p>The name of the VOD Source.</p>", "location": "uri", "locationName": "VodSourceName"}}}, "DescribeVodSourceResponse": {"type": "structure", "members": {"AdBreakOpportunities": {"shape": "AdBreakOpportunities", "documentation": "<p>The ad break opportunities within the VOD source.</p>"}, "Arn": {"shape": "__string", "documentation": "<p>The ARN of the VOD source.</p>"}, "CreationTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp that indicates when the VOD source was created.</p>"}, "HttpPackageConfigurations": {"shape": "HttpPackageConfigurations", "documentation": "<p>The HTTP package configurations.</p>"}, "LastModifiedTime": {"shape": "__timestampUnix", "documentation": "<p>The last modified time of the VOD source.</p>"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location associated with the VOD source.</p>"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags assigned to the VOD source. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}, "VodSourceName": {"shape": "__string", "documentation": "<p>The name of the VOD source.</p>"}}}, "FillPolicy": {"type": "string", "enum": ["FULL_AVAIL_ONLY", "PARTIAL_AVAIL"]}, "GetChannelPolicyRequest": {"type": "structure", "required": ["ChannelName"], "members": {"ChannelName": {"shape": "__string", "documentation": "<p>The name of the channel associated with this Channel Policy.</p>", "location": "uri", "locationName": "ChannelName"}}}, "GetChannelPolicyResponse": {"type": "structure", "members": {"Policy": {"shape": "__string", "documentation": "<p>The IAM policy for the channel. IAM policies are used to control access to your channel.</p>"}}}, "GetChannelScheduleRequest": {"type": "structure", "required": ["ChannelName"], "members": {"ChannelName": {"shape": "__string", "documentation": "<p>The name of the channel associated with this Channel Schedule.</p>", "location": "uri", "locationName": "ChannelName"}, "DurationMinutes": {"shape": "__string", "documentation": "<p>The duration in minutes of the channel schedule.</p>", "location": "querystring", "locationName": "durationMinutes"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of channel schedules that you want MediaTailor to return in response to the current request. If there are more than <code>MaxResults</code> channel schedules, use the value of <code>NextToken</code> in the response to get the next page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "__string", "documentation": "<p>(Optional) If the playback configuration has more than <code>MaxResults</code> channel schedules, use <code>NextToken</code> to get the second and subsequent pages of results.</p> <p>For the first <code>GetChannelScheduleRequest</code> request, omit this value.</p> <p>For the second and subsequent requests, get the value of <code>NextToken</code> from the previous response and specify that value for <code>NextToken</code> in the request.</p> <p>If the previous response didn't include a <code>NextToken</code> element, there are no more channel schedules to get.</p>", "location": "querystring", "locationName": "nextToken"}}}, "GetChannelScheduleResponse": {"type": "structure", "members": {"Items": {"shape": "__listOfScheduleEntry", "documentation": "<p>A list of schedule entries for the channel.</p>"}, "NextToken": {"shape": "__string", "documentation": "<p>Pagination token returned by the list request when results exceed the maximum allowed. Use the token to fetch the next page of results.</p>"}}}, "GetPlaybackConfigurationRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "__string", "documentation": "<p>The identifier for the playback configuration.</p>", "location": "uri", "locationName": "Name"}}}, "GetPlaybackConfigurationResponse": {"type": "structure", "members": {"AdDecisionServerUrl": {"shape": "__string", "documentation": "<p>The URL for the ad decision server (ADS). This includes the specification of static parameters and placeholders for dynamic parameters. AWS Elemental MediaTailor substitutes player-specific and session-specific parameters as needed when calling the ADS. Alternately, for testing, you can provide a static VAST URL. The maximum length is 25,000 characters.</p>"}, "AvailSuppression": {"shape": "AvailSuppression", "documentation": "<p>The configuration for avail suppression, also known as ad suppression. For more information about ad suppression, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/ad-behavior.html\">Ad Suppression</a>.</p>"}, "Bumper": {"shape": "Bumper", "documentation": "<p>The configuration for bumpers. Bumpers are short audio or video clips that play at the start or before the end of an ad break. To learn more about bumpers, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/bumpers.html\">Bumpers</a>.</p>"}, "CdnConfiguration": {"shape": "CdnConfiguration", "documentation": "<p>The configuration for using a content delivery network (CDN), like Amazon CloudFront, for content and ad segment management.</p>"}, "ConfigurationAliases": {"shape": "ConfigurationAliasesResponse", "documentation": "<p>The player parameters and aliases used as dynamic variables during session initialization. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/variables-domain.html\">Domain Variables</a>.</p>"}, "DashConfiguration": {"shape": "DashConfiguration", "documentation": "<p>The configuration for DASH content.</p>"}, "HlsConfiguration": {"shape": "HlsConfiguration", "documentation": "<p>The configuration for HLS content.</p>"}, "LivePreRollConfiguration": {"shape": "LivePreRollConfiguration", "documentation": "<p>The configuration for pre-roll ad insertion.</p>"}, "LogConfiguration": {"shape": "LogConfiguration", "documentation": "<p>The Amazon CloudWatch log settings for a playback configuration.</p>"}, "ManifestProcessingRules": {"shape": "ManifestProcessingRules", "documentation": "<p>The configuration for manifest processing rules. Manifest processing rules enable customization of the personalized manifests created by MediaTailor.</p>"}, "Name": {"shape": "__string", "documentation": "<p>The identifier for the playback configuration.</p>"}, "PersonalizationThresholdSeconds": {"shape": "__integerMin1", "documentation": "<p>Defines the maximum duration of underfilled ad time (in seconds) allowed in an ad break. If the duration of underfilled ad time exceeds the personalization threshold, then the personalization of the ad break is abandoned and the underlying content is shown. This feature applies to <i>ad replacement</i> in live and VOD streams, rather than ad insertion, because it relies on an underlying content stream. For more information about ad break behavior, including ad replacement and insertion, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/ad-behavior.html\">Ad Behavior in AWS Elemental MediaTailor</a>.</p>"}, "PlaybackConfigurationArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) for the playback configuration.</p>"}, "PlaybackEndpointPrefix": {"shape": "__string", "documentation": "<p>The URL that the player accesses to get a manifest from AWS Elemental MediaTailor. This session will use server-side reporting.</p>"}, "SessionInitializationEndpointPrefix": {"shape": "__string", "documentation": "<p>The URL that the player uses to initialize a session that uses client-side reporting.</p>"}, "SlateAdUrl": {"shape": "__string", "documentation": "<p>The URL for a high-quality video asset to transcode and use to fill in time that's not used by ads. AWS Elemental MediaTailor shows the slate to fill in gaps in media content. Configuring the slate is optional for non-VPAID playback configurations. For VPAID, the slate is required because MediaTailor provides it in the slots designated for dynamic ad content. The slate must be a high-quality asset that contains both audio and video.</p>"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags assigned to the playback configuration. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}, "TranscodeProfileName": {"shape": "__string", "documentation": "<p>The name that is used to associate this playback configuration with a custom transcode profile. This overrides the dynamic transcoding defaults of MediaTailor. Use this only if you have already set up custom profiles with the help of AWS Support.</p>"}, "VideoContentSourceUrl": {"shape": "__string", "documentation": "<p>The URL prefix for the parent manifest for the stream, minus the asset ID. The maximum length is 512 characters.</p>"}}}, "GetPrefetchScheduleRequest": {"type": "structure", "required": ["Name", "PlaybackConfigurationName"], "members": {"Name": {"shape": "__string", "documentation": "<p>The name of the prefetch schedule. The name must be unique among all prefetch schedules that are associated with the specified playback configuration.</p>", "location": "uri", "locationName": "Name"}, "PlaybackConfigurationName": {"shape": "__string", "documentation": "<p>Returns information about the prefetch schedule for a specific playback configuration. If you call <code>GetPrefetchSchedule</code> on an expired prefetch schedule, MediaTailor returns an HTTP 404 status code.</p>", "location": "uri", "locationName": "PlaybackConfigurationName"}}}, "GetPrefetchScheduleResponse": {"type": "structure", "members": {"Arn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the prefetch schedule.</p>"}, "Consumption": {"shape": "PrefetchConsumption", "documentation": "<p>Consumption settings determine how, and when, MediaTailor places the prefetched ads into ad breaks. Ad consumption occurs within a span of time that you define, called a <i>consumption window</i>. You can designate which ad breaks that MediaTailor fills with prefetch ads by setting avail matching criteria.</p>"}, "Name": {"shape": "__string", "documentation": "<p>The name of the prefetch schedule. The name must be unique among all prefetch schedules that are associated with the specified playback configuration.</p>"}, "PlaybackConfigurationName": {"shape": "__string", "documentation": "<p>The name of the playback configuration to create the prefetch schedule for.</p>"}, "Retrieval": {"shape": "PrefetchRetrieval", "documentation": "<p>A complex type that contains settings for prefetch retrieval from the ad decision server (ADS).</p>"}, "StreamId": {"shape": "__string", "documentation": "<p>An optional stream identifier that you can specify in order to prefetch for multiple streams that use the same playback configuration.</p>"}}}, "HlsConfiguration": {"type": "structure", "members": {"ManifestEndpointPrefix": {"shape": "__string", "documentation": "<p>The URL that is used to initiate a playback session for devices that support Apple HLS. The session uses server-side reporting.</p>"}}, "documentation": "<p>The configuration for HLS content.</p>"}, "HlsPlaylistSettings": {"type": "structure", "members": {"AdMarkupType": {"shape": "adMarkupTypes", "documentation": "<p>Determines the type of SCTE 35 tags to use in ad markup. Specify <code>DATERANGE</code> to use <code>DATERANGE</code> tags (for live or VOD content). Specify <code>SCTE35_ENHANCED</code> to use <code>EXT-X-CUE-OUT</code> and <code>EXT-X-CUE-IN</code> tags (for VOD content only).</p>"}, "ManifestWindowSeconds": {"shape": "__integer", "documentation": "<p>The total duration (in seconds) of each manifest. Minimum value: <code>30</code> seconds. Maximum value: <code>3600</code> seconds.</p>"}}, "documentation": "<p>HLS playlist configuration parameters.</p>"}, "HttpConfiguration": {"type": "structure", "required": ["BaseUrl"], "members": {"BaseUrl": {"shape": "__string", "documentation": "<p>The base URL for the source location host server. This string must include the protocol, such as <b>https://</b>.</p>"}}, "documentation": "<p>The HTTP configuration for the source location.</p>"}, "HttpPackageConfiguration": {"type": "structure", "required": ["Path", "SourceGroup", "Type"], "members": {"Path": {"shape": "__string", "documentation": "<p>The relative path to the URL for this VOD source. This is combined with <code>SourceLocation::HttpConfiguration::BaseUrl</code> to form a valid URL.</p>"}, "SourceGroup": {"shape": "__string", "documentation": "<p>The name of the source group. This has to match one of the <code>Channel::Outputs::SourceGroup</code>.</p>"}, "Type": {"shape": "Type", "documentation": "<p>The streaming protocol for this package configuration. Supported values are <code>HLS</code> and <code>DASH</code>.</p>"}}, "documentation": "<p>The HTTP package configuration properties for the requested VOD source.</p>"}, "HttpPackageConfigurations": {"type": "list", "member": {"shape": "HttpPackageConfiguration"}, "documentation": "<p>The VOD source's HTTP package configuration settings.</p>"}, "Integer": {"type": "integer", "box": true}, "KeyValuePair": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "String", "documentation": "<p>For <code>SCTE35_ENHANCED</code> output, defines a key. MediaTailor takes this key, and its associated value, and generates the key/value pair within the <code>EXT-X-ASSET</code>tag. If you specify a key, you must also specify a corresponding value.</p>"}, "Value": {"shape": "String", "documentation": "<p>For <code>SCTE35_ENHANCED</code> output, defines a value. MediaTailor; takes this value, and its associated key, and generates the key/value pair within the <code>EXT-X-ASSET</code>tag. If you specify a value, you must also specify a corresponding key.</p>"}}, "documentation": "<p>For <code>SCTE35_ENHANCED</code> output, defines a key and corresponding value. MediaTailor generates these pairs within the <code>EXT-X-ASSET</code>tag.</p>"}, "ListAlertsRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of alerts that you want MediaTailor to return in response to the current request. If there are more than <code>MaxResults</code> alerts, use the value of <code>NextToken</code> in the response to get the next page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "__string", "documentation": "<p>Pagination token returned by the list request when results exceed the maximum allowed. Use the token to fetch the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "ResourceArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>", "location": "querystring", "locationName": "resourceArn"}}}, "ListAlertsResponse": {"type": "structure", "members": {"Items": {"shape": "__list<PERSON>fAlert", "documentation": "<p>A list of alerts that are associated with this resource.</p>"}, "NextToken": {"shape": "__string", "documentation": "<p>Pagination token returned by the list request when results exceed the maximum allowed. Use the token to fetch the next page of results.</p>"}}}, "ListChannelsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of channels that you want MediaTailor to return in response to the current request. If there are more than <code>MaxResults</code> channels, use the value of <code>NextToken</code> in the response to get the next page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "__string", "documentation": "<p>Pagination token returned by the list request when results exceed the maximum allowed. Use the token to fetch the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListChannelsResponse": {"type": "structure", "members": {"Items": {"shape": "__listOfChannel", "documentation": "<p>A list of channels that are associated with this account.</p>"}, "NextToken": {"shape": "__string", "documentation": "<p>Pagination token returned by the list request when results exceed the maximum allowed. Use the token to fetch the next page of results.</p>"}}}, "ListLiveSourcesRequest": {"type": "structure", "required": ["SourceLocationName"], "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of live sources that you want MediaTailor to return in response to the current request. If there are more than <code>MaxResults</code> live sources, use the value of <code>NextToken</code> in the response to get the next page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "__string", "documentation": "<p>Pagination token returned by the list request when results exceed the maximum allowed. Use the token to fetch the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location associated with this Live Sources list.</p>", "location": "uri", "locationName": "SourceLocationName"}}}, "ListLiveSourcesResponse": {"type": "structure", "members": {"Items": {"shape": "__listOfLiveSource", "documentation": "<p>Lists the live sources.</p>"}, "NextToken": {"shape": "__string", "documentation": "<p>Pagination token returned by the list request when results exceed the maximum allowed. Use the token to fetch the next page of results.</p>"}}}, "ListPlaybackConfigurationsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of playback configurations that you want MediaTailor to return in response to the current request. If there are more than <code>MaxResults</code> playback configurations, use the value of <code>NextToken</code> in the response to get the next page of results.</p>", "location": "querystring", "locationName": "MaxResults"}, "NextToken": {"shape": "__string", "documentation": "<p>Pagination token returned by the list request when results exceed the maximum allowed. Use the token to fetch the next page of results.</p>", "location": "querystring", "locationName": "NextToken"}}}, "ListPlaybackConfigurationsResponse": {"type": "structure", "members": {"Items": {"shape": "__listOfPlaybackConfiguration", "documentation": "<p>Array of playback configurations. This might be all the available configurations or a subset, depending on the settings that you provide and the total number of configurations stored.</p>"}, "NextToken": {"shape": "__string", "documentation": "<p>Pagination token returned by the GET list request when results exceed the maximum allowed. Use the token to fetch the next page of results.</p>"}}}, "ListPrefetchSchedulesRequest": {"type": "structure", "required": ["PlaybackConfigurationName"], "members": {"MaxResults": {"shape": "__integerMin1Max100", "documentation": "<p>The maximum number of prefetch schedules that you want MediaTailor to return in response to the current request. If there are more than <code>MaxResults</code> prefetch schedules, use the value of <code>NextToken</code> in the response to get the next page of results.</p>"}, "NextToken": {"shape": "__string", "documentation": "<p>(Optional) If the playback configuration has more than <code>MaxResults</code> prefetch schedules, use <code>NextToken</code> to get the second and subsequent pages of results.</p> <p> For the first <code>ListPrefetchSchedulesRequest</code> request, omit this value.</p> <p> For the second and subsequent requests, get the value of <code>NextToken</code> from the previous response and specify that value for <code>NextToken</code> in the request.</p> <p> If the previous response didn't include a <code>NextToken</code> element, there are no more prefetch schedules to get.</p>"}, "PlaybackConfigurationName": {"shape": "__string", "documentation": "<p>Retrieves the prefetch schedule(s) for a specific playback configuration.</p>", "location": "uri", "locationName": "PlaybackConfigurationName"}, "StreamId": {"shape": "__string", "documentation": "<p>An optional filtering parameter whereby MediaTailor filters the prefetch schedules to include only specific streams.</p>"}}}, "ListPrefetchSchedulesResponse": {"type": "structure", "members": {"Items": {"shape": "__listOfPrefetchSchedule", "documentation": "<p>Lists the prefetch schedules. An empty <code>Items</code> list doesn't mean there aren't more items to fetch, just that that page was empty.</p>"}, "NextToken": {"shape": "__string", "documentation": "<p>Pagination token returned by the list request when results exceed the maximum allowed. Use the token to fetch the next page of results.</p>"}}}, "ListSourceLocationsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p> The maximum number of source locations that you want MediaTailor to return in response to the current request. If there are more than <code>MaxResults</code> source locations, use the value of <code>NextToken</code> in the response to get the next page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "__string", "documentation": "<p>Pagination token returned by the list request when results exceed the maximum allowed. Use the token to fetch the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListSourceLocationsResponse": {"type": "structure", "members": {"Items": {"shape": "__listOfSourceLocation", "documentation": "<p>A list of source locations.</p>"}, "NextToken": {"shape": "__string", "documentation": "<p>Pagination token returned by the list request when results exceed the maximum allowed. Use the token to fetch the next page of results.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) associated with this resource.</p>", "location": "uri", "locationName": "ResourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags associated with this resource. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}}}, "ListVodSourcesRequest": {"type": "structure", "required": ["SourceLocationName"], "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p> The maximum number of VOD sources that you want MediaTailor to return in response to the current request. If there are more than <code>MaxResults</code> VOD sources, use the value of <code>NextToken</code> in the response to get the next page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "__string", "documentation": "<p>Pagination token returned by the list request when results exceed the maximum allowed. Use the token to fetch the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location associated with this VOD Source list.</p>", "location": "uri", "locationName": "SourceLocationName"}}}, "ListVodSourcesResponse": {"type": "structure", "members": {"Items": {"shape": "__listOfVodSource", "documentation": "<p>Lists the VOD sources.</p>"}, "NextToken": {"shape": "__string", "documentation": "<p>Pagination token returned by the list request when results exceed the maximum allowed. Use the token to fetch the next page of results.</p>"}}}, "LivePreRollConfiguration": {"type": "structure", "members": {"AdDecisionServerUrl": {"shape": "__string", "documentation": "<p>The URL for the ad decision server (ADS) for pre-roll ads. This includes the specification of static parameters and placeholders for dynamic parameters. AWS Elemental MediaTailor substitutes player-specific and session-specific parameters as needed when calling the ADS. Alternately, for testing, you can provide a static VAST URL. The maximum length is 25,000 characters.</p>"}, "MaxDurationSeconds": {"shape": "__integer", "documentation": "<p>The maximum allowed duration for the pre-roll ad avail. AWS Elemental MediaTailor won't play pre-roll ads to exceed this duration, regardless of the total duration of ads that the ADS returns.</p>"}}, "documentation": "<p>The configuration for pre-roll ad insertion.</p>"}, "LiveSource": {"type": "structure", "required": ["<PERSON><PERSON>", "HttpPackageConfigurations", "LiveSourceName", "SourceLocationName"], "members": {"Arn": {"shape": "__string", "documentation": "<p>The ARN for the live source.</p>"}, "CreationTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp that indicates when the live source was created.</p>"}, "HttpPackageConfigurations": {"shape": "HttpPackageConfigurations", "documentation": "<p>The HTTP package configurations for the live source.</p>"}, "LastModifiedTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp that indicates when the live source was last modified.</p>"}, "LiveSourceName": {"shape": "__string", "documentation": "<p>The name that's used to refer to a live source.</p>"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location.</p>"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags assigned to the live source. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}}, "documentation": "<p>Live source configuration parameters.</p>"}, "LogConfiguration": {"type": "structure", "required": ["PercentEnabled"], "members": {"PercentEnabled": {"shape": "__integer", "documentation": "<p>The percentage of session logs that MediaTailor sends to your Cloudwatch Logs account. For example, if your playback configuration has 1000 sessions and <code>percentEnabled</code> is set to <code>60</code>, MediaTailor sends logs for 600 of the sessions to CloudWatch Logs. MediaTailor decides at random which of the playback configuration sessions to send logs for. If you want to view logs for a specific session, you can use the <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/debug-log-mode.html\">debug log mode</a>.</p> <p>Valid values: <code>0</code> - <code>100</code> </p>"}}, "documentation": "<p>Returns Amazon CloudWatch log settings for a playback configuration.</p>"}, "LogConfigurationForChannel": {"type": "structure", "members": {"LogTypes": {"shape": "LogTypes", "documentation": "<p>The log types.</p>"}}, "documentation": "<p>The log configuration for the channel.</p>"}, "LogType": {"type": "string", "enum": ["AS_RUN"]}, "LogTypes": {"type": "list", "member": {"shape": "LogType"}}, "Long": {"type": "long", "box": true}, "ManifestProcessingRules": {"type": "structure", "members": {"AdMarkerPassthrough": {"shape": "AdMarkerPassthrough", "documentation": "<p>For HLS, when set to <code>true</code>, MediaTailor passes through <code>EXT-X-CUE-IN</code>, <code>EXT-X-CUE-OUT</code>, and <code>EXT-X-SPLICEPOINT-SCTE35</code> ad markers from the origin manifest to the MediaTailor personalized manifest.</p> <p>No logic is applied to these ad markers. For example, if <code>EXT-X-CUE-OUT</code> has a value of <code>60</code>, but no ads are filled for that ad break, MediaTailor will not set the value to <code>0</code>.</p>"}}, "documentation": "<p>The configuration for manifest processing rules. Manifest processing rules enable customization of the personalized manifests created by MediaTailor.</p>"}, "MaxResults": {"type": "integer", "max": 100, "min": 1}, "MessageType": {"type": "string", "enum": ["SPLICE_INSERT", "TIME_SIGNAL"]}, "Mode": {"type": "string", "enum": ["OFF", "BEHIND_LIVE_EDGE", "AFTER_LIVE_EDGE"]}, "Operator": {"type": "string", "enum": ["EQUALS"]}, "OriginManifestType": {"type": "string", "enum": ["SINGLE_PERIOD", "MULTI_PERIOD"]}, "PlaybackConfiguration": {"type": "structure", "members": {"AdDecisionServerUrl": {"shape": "__string", "documentation": "<p>The URL for the ad decision server (ADS). This includes the specification of static parameters and placeholders for dynamic parameters. AWS Elemental MediaTailor substitutes player-specific and session-specific parameters as needed when calling the ADS. Alternately, for testing you can provide a static VAST URL. The maximum length is 25,000 characters.</p>"}, "AvailSuppression": {"shape": "AvailSuppression", "documentation": "<p>The configuration for avail suppression, also known as ad suppression. For more information about ad suppression, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/ad-behavior.html\">Ad Suppression</a>.</p>"}, "Bumper": {"shape": "Bumper", "documentation": "<p>The configuration for bumpers. Bumpers are short audio or video clips that play at the start or before the end of an ad break. To learn more about bumpers, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/bumpers.html\">Bumpers</a>.</p>"}, "CdnConfiguration": {"shape": "CdnConfiguration", "documentation": "<p>The configuration for using a content delivery network (CDN), like Amazon CloudFront, for content and ad segment management.</p>"}, "ConfigurationAliases": {"shape": "ConfigurationAliasesResponse", "documentation": "<p>The player parameters and aliases used as dynamic variables during session initialization. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/variables-domain.html\">Domain Variables</a>.</p>"}, "DashConfiguration": {"shape": "DashConfiguration", "documentation": "<p>The configuration for a DASH source.</p>"}, "HlsConfiguration": {"shape": "HlsConfiguration", "documentation": "<p>The configuration for HLS content.</p>"}, "LivePreRollConfiguration": {"shape": "LivePreRollConfiguration", "documentation": "<p>The configuration for pre-roll ad insertion.</p>"}, "LogConfiguration": {"shape": "LogConfiguration", "documentation": "<p>The Amazon CloudWatch log settings for a playback configuration.</p>"}, "ManifestProcessingRules": {"shape": "ManifestProcessingRules", "documentation": "<p>The configuration for manifest processing rules. Manifest processing rules enable customization of the personalized manifests created by MediaTailor.</p>"}, "Name": {"shape": "__string", "documentation": "<p>The identifier for the playback configuration.</p>"}, "PersonalizationThresholdSeconds": {"shape": "__integerMin1", "documentation": "<p>Defines the maximum duration of underfilled ad time (in seconds) allowed in an ad break. If the duration of underfilled ad time exceeds the personalization threshold, then the personalization of the ad break is abandoned and the underlying content is shown. This feature applies to <i>ad replacement</i> in live and VOD streams, rather than ad insertion, because it relies on an underlying content stream. For more information about ad break behavior, including ad replacement and insertion, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/ad-behavior.html\">Ad Behavior in AWS Elemental MediaTailor</a>.</p>"}, "PlaybackConfigurationArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) for the playback configuration.</p>"}, "PlaybackEndpointPrefix": {"shape": "__string", "documentation": "<p>The URL that the player accesses to get a manifest from AWS Elemental MediaTailor.</p>"}, "SessionInitializationEndpointPrefix": {"shape": "__string", "documentation": "<p>The URL that the player uses to initialize a session that uses client-side reporting.</p>"}, "SlateAdUrl": {"shape": "__string", "documentation": "<p>The URL for a video asset to transcode and use to fill in time that's not used by ads. AWS Elemental MediaTailor shows the slate to fill in gaps in media content. Configuring the slate is optional for non-VPAID playback configurations. For VPAID, the slate is required because MediaTailor provides it in the slots designated for dynamic ad content. The slate must be a high-quality asset that contains both audio and video.</p>"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags to assign to the playback configuration. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}, "TranscodeProfileName": {"shape": "__string", "documentation": "<p>The name that is used to associate this playback configuration with a custom transcode profile. This overrides the dynamic transcoding defaults of MediaTailor. Use this only if you have already set up custom profiles with the help of AWS Support.</p>"}, "VideoContentSourceUrl": {"shape": "__string", "documentation": "<p>The URL prefix for the parent manifest for the stream, minus the asset ID. The maximum length is 512 characters.</p>"}}, "documentation": "<p>A playback configuration. For information about MediaTailor configurations, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/configurations.html\">Working with configurations in AWS Elemental MediaTailor</a>.</p>"}, "PlaybackMode": {"type": "string", "enum": ["LOOP", "LINEAR"]}, "PrefetchConsumption": {"type": "structure", "required": ["EndTime"], "members": {"AvailMatchingCriteria": {"shape": "__listOfAvailMatchingCriteria", "documentation": "<p>If you only want MediaTailor to insert prefetched ads into avails (ad breaks) that match specific dynamic variables, such as <code>scte.event_id</code>, set the avail matching criteria.</p>"}, "EndTime": {"shape": "__timestampUnix", "documentation": "<p>The time when MediaTailor no longer considers the prefetched ads for use in an ad break. MediaTailor automatically deletes prefetch schedules no less than seven days after the end time. If you'd like to manually delete the prefetch schedule, you can call <code>DeletePrefetchSchedule</code>.</p>"}, "StartTime": {"shape": "__timestampUnix", "documentation": "<p>The time when prefetched ads are considered for use in an ad break. If you don't specify <code>StartTime</code>, the prefetched ads are available after MediaTailor retrives them from the ad decision server.</p>"}}, "documentation": "<p>A complex type that contains settings that determine how and when that MediaTailor places prefetched ads into upcoming ad breaks.</p>"}, "PrefetchRetrieval": {"type": "structure", "required": ["EndTime"], "members": {"DynamicVariables": {"shape": "__mapOf__string", "documentation": "<p>The dynamic variables to use for substitution during prefetch requests to the ad decision server (ADS).</p> <p>You initially configure <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/variables.html\">dynamic variables</a> for the ADS URL when you set up your playback configuration. When you specify <code>DynamicVariables</code> for prefetch retrieval, MediaTailor includes the dynamic variables in the request to the ADS.</p>"}, "EndTime": {"shape": "__timestampUnix", "documentation": "<p>The time when prefetch retrieval ends for the ad break. Prefetching will be attempted for manifest requests that occur at or before this time.</p>"}, "StartTime": {"shape": "__timestampUnix", "documentation": "<p>The time when prefetch retrievals can start for this break. Ad prefetching will be attempted for manifest requests that occur at or after this time. Defaults to the current time. If not specified, the prefetch retrieval starts as soon as possible.</p>"}}, "documentation": "<p>A complex type that contains settings governing when MediaTailor prefetches ads, and which dynamic variables that MediaTailor includes in the request to the ad decision server.</p>"}, "PrefetchSchedule": {"type": "structure", "required": ["<PERSON><PERSON>", "Consumption", "Name", "PlaybackConfigurationName", "Retrieval"], "members": {"Arn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the prefetch schedule.</p>"}, "Consumption": {"shape": "PrefetchConsumption", "documentation": "<p>Consumption settings determine how, and when, MediaTailor places the prefetched ads into ad breaks. Ad consumption occurs within a span of time that you define, called a <i>consumption window</i>. You can designate which ad breaks that MediaTailor fills with prefetch ads by setting avail matching criteria.</p>"}, "Name": {"shape": "__string", "documentation": "<p>The name of the prefetch schedule. The name must be unique among all prefetch schedules that are associated with the specified playback configuration.</p>"}, "PlaybackConfigurationName": {"shape": "__string", "documentation": "<p>The name of the playback configuration to create the prefetch schedule for.</p>"}, "Retrieval": {"shape": "PrefetchRetrieval", "documentation": "<p>A complex type that contains settings for prefetch retrieval from the ad decision server (ADS).</p>"}, "StreamId": {"shape": "__string", "documentation": "<p>An optional stream identifier that you can specify in order to prefetch for multiple streams that use the same playback configuration.</p>"}}, "documentation": "<p>A prefetch schedule allows you to tell MediaTailor to fetch and prepare certain ads before an ad break happens. For more information about ad prefetching, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/prefetching-ads.html\">Using ad prefetching</a> in the <i>MediaTailor User Guide</i>.</p>"}, "PutChannelPolicyRequest": {"type": "structure", "required": ["ChannelName", "Policy"], "members": {"ChannelName": {"shape": "__string", "documentation": "<p>The channel name associated with this Channel Policy.</p>", "location": "uri", "locationName": "ChannelName"}, "Policy": {"shape": "__string", "documentation": "<p>Adds an IAM role that determines the permissions of your channel.</p>"}}}, "PutChannelPolicyResponse": {"type": "structure", "members": {}}, "PutPlaybackConfigurationRequest": {"type": "structure", "required": ["Name"], "members": {"AdDecisionServerUrl": {"shape": "__string", "documentation": "<p>The URL for the ad decision server (ADS). This includes the specification of static parameters and placeholders for dynamic parameters. AWS Elemental MediaTailor substitutes player-specific and session-specific parameters as needed when calling the ADS. Alternately, for testing you can provide a static VAST URL. The maximum length is 25,000 characters.</p>"}, "AvailSuppression": {"shape": "AvailSuppression", "documentation": "<p>The configuration for avail suppression, also known as ad suppression. For more information about ad suppression, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/ad-behavior.html\">Ad Suppression</a>.</p>"}, "Bumper": {"shape": "Bumper", "documentation": "<p>The configuration for bumpers. Bumpers are short audio or video clips that play at the start or before the end of an ad break. To learn more about bumpers, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/bumpers.html\">Bumpers</a>.</p>"}, "CdnConfiguration": {"shape": "CdnConfiguration", "documentation": "<p>The configuration for using a content delivery network (CDN), like Amazon CloudFront, for content and ad segment management.</p>"}, "ConfigurationAliases": {"shape": "ConfigurationAliasesRequest", "documentation": "<p>The player parameters and aliases used as dynamic variables during session initialization. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/variables-domain.html\">Domain Variables</a>.</p>"}, "DashConfiguration": {"shape": "DashConfigurationForPut", "documentation": "<p>The configuration for DASH content.</p>"}, "LivePreRollConfiguration": {"shape": "LivePreRollConfiguration", "documentation": "<p>The configuration for pre-roll ad insertion.</p>"}, "ManifestProcessingRules": {"shape": "ManifestProcessingRules", "documentation": "<p>The configuration for manifest processing rules. Manifest processing rules enable customization of the personalized manifests created by MediaTailor.</p>"}, "Name": {"shape": "__string", "documentation": "<p>The identifier for the playback configuration.</p>"}, "PersonalizationThresholdSeconds": {"shape": "__integerMin1", "documentation": "<p>Defines the maximum duration of underfilled ad time (in seconds) allowed in an ad break. If the duration of underfilled ad time exceeds the personalization threshold, then the personalization of the ad break is abandoned and the underlying content is shown. This feature applies to <i>ad replacement</i> in live and VOD streams, rather than ad insertion, because it relies on an underlying content stream. For more information about ad break behavior, including ad replacement and insertion, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/ad-behavior.html\">Ad Behavior in AWS Elemental MediaTailor</a>.</p>"}, "SlateAdUrl": {"shape": "__string", "documentation": "<p>The URL for a high-quality video asset to transcode and use to fill in time that's not used by ads. AWS Elemental MediaTailor shows the slate to fill in gaps in media content. Configuring the slate is optional for non-VPAID configurations. For VPAID, the slate is required because MediaTailor provides it in the slots that are designated for dynamic ad content. The slate must be a high-quality asset that contains both audio and video.</p>"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags to assign to the playback configuration. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}, "TranscodeProfileName": {"shape": "__string", "documentation": "<p>The name that is used to associate this playback configuration with a custom transcode profile. This overrides the dynamic transcoding defaults of MediaTailor. Use this only if you have already set up custom profiles with the help of AWS Support.</p>"}, "VideoContentSourceUrl": {"shape": "__string", "documentation": "<p>The URL prefix for the parent manifest for the stream, minus the asset ID. The maximum length is 512 characters.</p>"}}}, "PutPlaybackConfigurationResponse": {"type": "structure", "members": {"AdDecisionServerUrl": {"shape": "__string", "documentation": "<p>The URL for the ad decision server (ADS). This includes the specification of static parameters and placeholders for dynamic parameters. AWS Elemental MediaTailor substitutes player-specific and session-specific parameters as needed when calling the ADS. Alternately, for testing you can provide a static VAST URL. The maximum length is 25,000 characters.</p>"}, "AvailSuppression": {"shape": "AvailSuppression", "documentation": "<p>The configuration for avail suppression, also known as ad suppression. For more information about ad suppression, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/ad-behavior.html\">Ad Suppression</a>.</p>"}, "Bumper": {"shape": "Bumper", "documentation": "<p>The configuration for bumpers. Bumpers are short audio or video clips that play at the start or before the end of an ad break. To learn more about bumpers, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/bumpers.html\">Bumpers</a>.</p>"}, "CdnConfiguration": {"shape": "CdnConfiguration", "documentation": "<p>The configuration for using a content delivery network (CDN), like Amazon CloudFront, for content and ad segment management.</p>"}, "ConfigurationAliases": {"shape": "ConfigurationAliasesResponse", "documentation": "<p>The player parameters and aliases used as dynamic variables during session initialization. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/variables-domain.html\">Domain Variables</a>.</p>"}, "DashConfiguration": {"shape": "DashConfiguration", "documentation": "<p>The configuration for DASH content.</p>"}, "HlsConfiguration": {"shape": "HlsConfiguration", "documentation": "<p>The configuration for HLS content.</p>"}, "LivePreRollConfiguration": {"shape": "LivePreRollConfiguration", "documentation": "<p>The configuration for pre-roll ad insertion.</p>"}, "LogConfiguration": {"shape": "LogConfiguration", "documentation": "<p>The Amazon CloudWatch log settings for a playback configuration.</p>"}, "ManifestProcessingRules": {"shape": "ManifestProcessingRules", "documentation": "<p>The configuration for manifest processing rules. Manifest processing rules enable customization of the personalized manifests created by MediaTailor.</p>"}, "Name": {"shape": "__string", "documentation": "<p>The identifier for the playback configuration.</p>"}, "PersonalizationThresholdSeconds": {"shape": "__integerMin1", "documentation": "<p>Defines the maximum duration of underfilled ad time (in seconds) allowed in an ad break. If the duration of underfilled ad time exceeds the personalization threshold, then the personalization of the ad break is abandoned and the underlying content is shown. This feature applies to <i>ad replacement</i> in live and VOD streams, rather than ad insertion, because it relies on an underlying content stream. For more information about ad break behavior, including ad replacement and insertion, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/ad-behavior.html\">Ad Behavior in AWS Elemental MediaTailor</a>.</p>"}, "PlaybackConfigurationArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) associated with the playback configuration.</p>"}, "PlaybackEndpointPrefix": {"shape": "__string", "documentation": "<p>The playback endpoint prefix associated with the playback configuration.</p>"}, "SessionInitializationEndpointPrefix": {"shape": "__string", "documentation": "<p>The session initialization endpoint prefix associated with the playback configuration.</p>"}, "SlateAdUrl": {"shape": "__string", "documentation": "<p>The URL for a high-quality video asset to transcode and use to fill in time that's not used by ads. AWS Elemental MediaTailor shows the slate to fill in gaps in media content. Configuring the slate is optional for non-VPAID configurations. For VPAID, the slate is required because MediaTailor provides it in the slots that are designated for dynamic ad content. The slate must be a high-quality asset that contains both audio and video.</p>"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags to assign to the playback configuration. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}, "TranscodeProfileName": {"shape": "__string", "documentation": "<p>The name that is used to associate this playback configuration with a custom transcode profile. This overrides the dynamic transcoding defaults of MediaTailor. Use this only if you have already set up custom profiles with the help of AWS Support.</p>"}, "VideoContentSourceUrl": {"shape": "__string", "documentation": "<p>The URL prefix for the parent manifest for the stream, minus the asset ID. The maximum length is 512 characters.</p>"}}}, "RelativePosition": {"type": "string", "enum": ["BEFORE_PROGRAM", "AFTER_PROGRAM"]}, "RequestOutputItem": {"type": "structure", "required": ["ManifestName", "SourceGroup"], "members": {"DashPlaylistSettings": {"shape": "DashPlaylistSettings", "documentation": "<p>DASH manifest configuration parameters.</p>"}, "HlsPlaylistSettings": {"shape": "HlsPlaylistSettings", "documentation": "<p>HLS playlist configuration parameters.</p>"}, "ManifestName": {"shape": "__string", "documentation": "<p>The name of the manifest for the channel. The name appears in the <code>PlaybackUrl</code>.</p>"}, "SourceGroup": {"shape": "__string", "documentation": "<p>A string used to match which <code>HttpPackageConfiguration</code> is used for each <code>VodSource</code>.</p>"}}, "documentation": "<p>The output configuration for this channel.</p>"}, "RequestOutputs": {"type": "list", "member": {"shape": "RequestOutputItem"}, "documentation": "<p>An object that represents an object in the CreateChannel request.</p>"}, "ResponseOutputItem": {"type": "structure", "required": ["ManifestName", "PlaybackUrl", "SourceGroup"], "members": {"DashPlaylistSettings": {"shape": "DashPlaylistSettings", "documentation": "<p>DASH manifest configuration settings.</p>"}, "HlsPlaylistSettings": {"shape": "HlsPlaylistSettings", "documentation": "<p>HLS manifest configuration settings.</p>"}, "ManifestName": {"shape": "__string", "documentation": "<p>The name of the manifest for the channel that will appear in the channel output's playback URL.</p>"}, "PlaybackUrl": {"shape": "__string", "documentation": "<p>The URL used for playback by content players.</p>"}, "SourceGroup": {"shape": "__string", "documentation": "<p>A string used to associate a package configuration source group with a channel output.</p>"}}, "documentation": "<p>The output item response.</p>"}, "ResponseOutputs": {"type": "list", "member": {"shape": "ResponseOutputItem"}}, "ScheduleAdBreak": {"type": "structure", "members": {"ApproximateDurationSeconds": {"shape": "__long", "documentation": "<p>The approximate duration of the ad break, in seconds.</p>"}, "ApproximateStartTime": {"shape": "__timestampUnix", "documentation": "<p>The approximate time that the ad will start playing.</p>"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location containing the VOD source used for the ad break.</p>"}, "VodSourceName": {"shape": "__string", "documentation": "<p>The name of the VOD source used for the ad break.</p>"}}, "documentation": "<p>The schedule's ad break properties.</p>"}, "ScheduleConfiguration": {"type": "structure", "required": ["Transition"], "members": {"ClipRange": {"shape": "ClipRange", "documentation": "<p>Program clip range configuration.</p>"}, "Transition": {"shape": "Transition", "documentation": "<p>Program transition configurations.</p>"}}, "documentation": "<p>Schedule configuration parameters. A channel must be stopped before changes can be made to the schedule.</p>"}, "ScheduleEntry": {"type": "structure", "required": ["<PERSON><PERSON>", "ChannelName", "ProgramName", "SourceLocationName"], "members": {"ApproximateDurationSeconds": {"shape": "__long", "documentation": "<p>The approximate duration of this program, in seconds.</p>"}, "ApproximateStartTime": {"shape": "__timestampUnix", "documentation": "<p>The approximate time that the program will start playing.</p>"}, "Arn": {"shape": "__string", "documentation": "<p>The ARN of the program.</p>"}, "ChannelName": {"shape": "__string", "documentation": "<p>The name of the channel that uses this schedule.</p>"}, "LiveSourceName": {"shape": "__string", "documentation": "<p>The name of the live source used for the program.</p>"}, "ProgramName": {"shape": "__string", "documentation": "<p>The name of the program.</p>"}, "ScheduleAdBreaks": {"shape": "__listOfScheduleAdBreak", "documentation": "<p>The schedule's ad break properties.</p>"}, "ScheduleEntryType": {"shape": "ScheduleEntryType", "documentation": "<p>The type of schedule entry.</p>"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location.</p>"}, "VodSourceName": {"shape": "__string", "documentation": "<p>The name of the VOD source.</p>"}}, "documentation": "<p>The properties for a schedule.</p>"}, "ScheduleEntryType": {"type": "string", "enum": ["PROGRAM", "FILLER_SLATE"]}, "SecretsManagerAccessTokenConfiguration": {"type": "structure", "members": {"HeaderName": {"shape": "__string", "documentation": "<p>The name of the HTTP header used to supply the access token in requests to the source location.</p>"}, "SecretArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the AWS Secrets Manager secret that contains the access token.</p>"}, "SecretStringKey": {"shape": "__string", "documentation": "<p>The AWS Secrets Manager <a href=\"https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_CreateSecret.html#SecretsManager-CreateSecret-request-SecretString.html\">SecretString</a> key associated with the access token. MediaTailor uses the key to look up SecretString key and value pair containing the access token.</p>"}}, "documentation": "<p>AWS Secrets Manager access token configuration parameters. For information about Secrets Manager access token authentication, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/channel-assembly-access-configuration-access-token.html\">Working with AWS Secrets Manager access token authentication</a>.</p>"}, "SegmentDeliveryConfiguration": {"type": "structure", "members": {"BaseUrl": {"shape": "__string", "documentation": "<p>The base URL of the host or path of the segment delivery server that you're using to serve segments. This is typically a content delivery network (CDN). The URL can be absolute or relative. To use an absolute URL include the protocol, such as <code>https://example.com/some/path</code>. To use a relative URL specify the relative path, such as <code>/some/path*</code>.</p>"}, "Name": {"shape": "__string", "documentation": "<p>A unique identifier used to distinguish between multiple segment delivery configurations in a source location.</p>"}}, "documentation": "<p>The segment delivery configuration settings.</p>"}, "SegmentationDescriptor": {"type": "structure", "members": {"SegmentNum": {"shape": "Integer", "documentation": "<p>The segment number to assign to the <code>segmentation_descriptor.segment_num</code> message, as defined in section ******** of the 2022 SCTE-35 specification Values must be between 0 and 256, inclusive. The default value is 0.</p>"}, "SegmentationEventId": {"shape": "Integer", "documentation": "<p>The Event Identifier to assign to the <code>segmentation_descriptor.segmentation_event_id</code> message, as defined in section ******** of the 2022 SCTE-35 specification. The default value is 1.</p>"}, "SegmentationTypeId": {"shape": "Integer", "documentation": "<p>The Type Identifier to assign to the <code>segmentation_descriptor.segmentation_type_id</code> message, as defined in section ******** of the 2022 SCTE-35 specification. Values must be between 0 and 256, inclusive. The default value is 48.</p>"}, "SegmentationUpid": {"shape": "String", "documentation": "<p>The Upid to assign to the <code>segmentation_descriptor.segmentation_upid</code> message, as defined in section ******** of the 2022 SCTE-35 specification. The value must be a hexadecimal string containing only the characters 0 though 9 and A through F. The default value is \"\" (an empty string).</p>"}, "SegmentationUpidType": {"shape": "Integer", "documentation": "<p>The Upid Type to assign to the <code>segmentation_descriptor.segmentation_upid_type</code> message, as defined in section ******** of the 2022 SCTE-35 specification. Values must be between 0 and 256, inclusive. The default value is 14.</p>"}, "SegmentsExpected": {"shape": "Integer", "documentation": "<p>The number of segments expected, which is assigned to the <code>segmentation_descriptor.segments_expectedS</code> message, as defined in section ******** of the 2022 SCTE-35 specification Values must be between 0 and 256, inclusive. The default value is 0.</p>"}, "SubSegmentNum": {"shape": "Integer", "documentation": "<p>The sub-segment number to assign to the <code>segmentation_descriptor.sub_segment_num</code> message, as defined in section ******** of the 2022 SCTE-35 specification. Values must be between 0 and 256, inclusive. The defualt value is null.</p>"}, "SubSegmentsExpected": {"shape": "Integer", "documentation": "<p>The number of sub-segments expected, which is assigned to the <code>segmentation_descriptor.sub_segments_expected</code> message, as defined in section ******** of the 2022 SCTE-35 specification. Values must be between 0 and 256, inclusive. The default value is null.</p>"}}, "documentation": "<p>The <code>segmentation_descriptor</code> message can contain advanced metadata fields, like content identifiers, to convey a wide range of information about the ad break. MediaTailor writes the ad metadata in the egress manifest as part of the <code>EXT-X-DATERANGE</code> or <code>EventStream</code> ad marker's SCTE-35 data.</p> <p> <code>segmentation_descriptor</code> messages must be sent with the <code>time_signal</code> message type.</p> <p>See the <code>segmentation_descriptor()</code> table of the 2022 SCTE-35 specification for more information.</p>"}, "SegmentationDescriptorList": {"type": "list", "member": {"shape": "SegmentationDescriptor"}}, "SlateSource": {"type": "structure", "members": {"SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location where the slate VOD source is stored.</p>"}, "VodSourceName": {"shape": "__string", "documentation": "<p>The slate VOD source name. The VOD source must already exist in a source location before it can be used for slate.</p>"}}, "documentation": "<p>Slate VOD source configuration.</p>"}, "SourceLocation": {"type": "structure", "required": ["<PERSON><PERSON>", "HttpConfiguration", "SourceLocationName"], "members": {"AccessConfiguration": {"shape": "AccessConfiguration", "documentation": "<p>The access configuration for the source location.</p>"}, "Arn": {"shape": "__string", "documentation": "<p>The ARN of the SourceLocation.</p>"}, "CreationTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp that indicates when the source location was created.</p>"}, "DefaultSegmentDeliveryConfiguration": {"shape": "DefaultSegmentDeliveryConfiguration", "documentation": "<p>The default segment delivery configuration.</p>"}, "HttpConfiguration": {"shape": "HttpConfiguration", "documentation": "<p>The HTTP configuration for the source location.</p>"}, "LastModifiedTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp that indicates when the source location was last modified.</p>"}, "SegmentDeliveryConfigurations": {"shape": "__listOfSegmentDeliveryConfiguration", "documentation": "<p>The segment delivery configurations for the source location.</p>"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location.</p>"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags assigned to the source location. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}}, "documentation": "<p>A source location is a container for sources. For more information about source locations, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/channel-assembly-source-locations.html\">Working with source locations</a> in the <i>MediaTailor User Guide</i>.</p>"}, "SpliceInsertMessage": {"type": "structure", "members": {"AvailNum": {"shape": "__integer", "documentation": "<p>This is written to <code>splice_insert.avail_num</code>, as defined in section ******* of the SCTE-35 specification. The default value is <code>0</code>. Values must be between <code>0</code> and <code>256</code>, inclusive.</p>"}, "AvailsExpected": {"shape": "__integer", "documentation": "<p>This is written to <code>splice_insert.avails_expected</code>, as defined in section ******* of the SCTE-35 specification. The default value is <code>0</code>. Values must be between <code>0</code> and <code>256</code>, inclusive.</p>"}, "SpliceEventId": {"shape": "__integer", "documentation": "<p>This is written to <code>splice_insert.splice_event_id</code>, as defined in section ******* of the SCTE-35 specification. The default value is <code>1</code>.</p>"}, "UniqueProgramId": {"shape": "__integer", "documentation": "<p>This is written to <code>splice_insert.unique_program_id</code>, as defined in section ******* of the SCTE-35 specification. The default value is <code>0</code>. Values must be between <code>0</code> and <code>256</code>, inclusive.</p>"}}, "documentation": "<p>Splice insert message configuration.</p>"}, "StartChannelRequest": {"type": "structure", "required": ["ChannelName"], "members": {"ChannelName": {"shape": "__string", "documentation": "<p>The name of the channel.</p>", "location": "uri", "locationName": "ChannelName"}}}, "StartChannelResponse": {"type": "structure", "members": {}}, "StopChannelRequest": {"type": "structure", "required": ["ChannelName"], "members": {"ChannelName": {"shape": "__string", "documentation": "<p>The name of the channel.</p>", "location": "uri", "locationName": "ChannelName"}}}, "StopChannelResponse": {"type": "structure", "members": {}}, "String": {"type": "string"}, "TagResourceRequest": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) associated with the resource.</p>", "location": "uri", "locationName": "ResourceArn"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags to assign to the resource. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}}}, "Tier": {"type": "string", "enum": ["BASIC", "STANDARD"]}, "TimeSignalMessage": {"type": "structure", "members": {"SegmentationDescriptors": {"shape": "SegmentationDescriptorList", "documentation": "<p>The configurations for the SCTE-35 <code>segmentation_descriptor</code> message(s) sent with the <code>time_signal</code> message.</p>"}}, "documentation": "<p>The SCTE-35 <code>time_signal</code> message can be sent with one or more <code>segmentation_descriptor</code> messages. A <code>time_signal</code> message can be sent only if a single <code>segmentation_descriptor</code> message is sent.</p> <p>The <code>time_signal</code> message contains only the <code>splice_time</code> field which is constructed using a given presentation timestamp. When sending a <code>time_signal</code> message, the <code>splice_command_type</code> field in the <code>splice_info_section</code> message is set to 6 (0x06).</p> <p>See the <code>time_signal()</code> table of the 2022 SCTE-35 specification for more information.</p>"}, "Transition": {"type": "structure", "required": ["RelativePosition", "Type"], "members": {"DurationMillis": {"shape": "__long", "documentation": "<p>The duration of the live program in seconds.</p>"}, "RelativePosition": {"shape": "RelativePosition", "documentation": "<p>The position where this program will be inserted relative to the <code>RelativePosition</code>.</p>"}, "RelativeProgram": {"shape": "__string", "documentation": "<p>The name of the program that this program will be inserted next to, as defined by <code>RelativePosition</code>.</p>"}, "ScheduledStartTimeMillis": {"shape": "__long", "documentation": "<p>The date and time that the program is scheduled to start, in epoch milliseconds.</p>"}, "Type": {"shape": "__string", "documentation": "<p>Defines when the program plays in the schedule. You can set the value to <code>ABSOLUTE</code> or <code>RELATIVE</code>.</p> <p> <code>ABSOLUTE</code> - The program plays at a specific wall clock time. This setting can only be used for channels using the <code>LINEAR</code> <code>PlaybackMode</code>.</p> <p>Note the following considerations when using <code>ABSOLUTE</code> transitions:</p> <p>If the preceding program in the schedule has a duration that extends past the wall clock time, MediaTailor truncates the preceding program on a common segment boundary.</p> <p>If there are gaps in playback, MediaTailor plays the <code>FillerSlate</code> you configured for your linear channel.</p> <p> <code>RELATIVE</code> - The program is inserted into the schedule either before or after a program that you specify via <code>RelativePosition</code>.</p>"}}, "documentation": "<p>Program transition configuration.</p>"}, "Type": {"type": "string", "enum": ["DASH", "HLS"]}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the resource to untag.</p>", "location": "uri", "locationName": "ResourceArn"}, "TagKeys": {"shape": "__listOf__string", "documentation": "<p>The tag keys associated with the resource.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UpdateChannelRequest": {"type": "structure", "required": ["ChannelName", "Outputs"], "members": {"ChannelName": {"shape": "__string", "documentation": "<p>The name of the channel.</p>", "location": "uri", "locationName": "ChannelName"}, "FillerSlate": {"shape": "SlateSource", "documentation": "<p>The slate used to fill gaps between programs in the schedule. You must configure filler slate if your channel uses the <code>LINEAR</code> <code>PlaybackMode</code>. MediaTailor doesn't support filler slate for channels using the <code>LOOP</code> <code>PlaybackMode</code>.</p>"}, "Outputs": {"shape": "RequestOutputs", "documentation": "<p>The channel's output properties.</p>"}}}, "UpdateChannelResponse": {"type": "structure", "members": {"Arn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) associated with the channel.</p>"}, "ChannelName": {"shape": "__string", "documentation": "<p>The name of the channel.</p>"}, "ChannelState": {"shape": "ChannelState", "documentation": "<p>Returns the state whether the channel is running or not.</p>"}, "CreationTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp of when the channel was created.</p>"}, "FillerSlate": {"shape": "SlateSource", "documentation": "<p>The slate used to fill gaps between programs in the schedule. You must configure filler slate if your channel uses the <code>LINEAR</code> <code>PlaybackMode</code>. MediaTailor doesn't support filler slate for channels using the <code>LOOP</code> <code>PlaybackMode</code>.</p>"}, "LastModifiedTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp that indicates when the channel was last modified.</p>"}, "Outputs": {"shape": "ResponseOutputs", "documentation": "<p>The channel's output properties.</p>"}, "PlaybackMode": {"shape": "__string", "documentation": "<p>The type of playback mode for this channel.</p> <p> <code>LINEAR</code> - Programs play back-to-back only once.</p> <p> <code>LOOP</code> - Programs play back-to-back in an endless loop. When the last program in the schedule plays, playback loops back to the first program in the schedule.</p>"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags to assign to the channel. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}, "Tier": {"shape": "__string", "documentation": "<p>The tier associated with this Channel.</p>"}}}, "UpdateLiveSourceRequest": {"type": "structure", "required": ["HttpPackageConfigurations", "LiveSourceName", "SourceLocationName"], "members": {"HttpPackageConfigurations": {"shape": "HttpPackageConfigurations", "documentation": "<p>A list of HTTP package configurations for the live source on this account.</p>"}, "LiveSourceName": {"shape": "__string", "documentation": "<p>The name of the live source.</p>", "location": "uri", "locationName": "LiveSourceName"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location associated with this Live Source.</p>", "location": "uri", "locationName": "SourceLocationName"}}}, "UpdateLiveSourceResponse": {"type": "structure", "members": {"Arn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) associated with this live source.</p>"}, "CreationTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp that indicates when the live source was created.</p>"}, "HttpPackageConfigurations": {"shape": "HttpPackageConfigurations", "documentation": "<p>A list of HTTP package configurations for the live source on this account.</p>"}, "LastModifiedTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp that indicates when the live source was last modified.</p>"}, "LiveSourceName": {"shape": "__string", "documentation": "<p>The name of the live source.</p>"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location associated with the live source.</p>"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags to assign to the live source. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}}}, "UpdateProgramRequest": {"type": "structure", "required": ["ChannelName", "ProgramName", "ScheduleConfiguration"], "members": {"AdBreaks": {"shape": "__listOfAdBreak", "documentation": "<p>The ad break configuration settings.</p>"}, "ChannelName": {"shape": "__string", "documentation": "<p>The name of the channel for this Program.</p>", "location": "uri", "locationName": "ChannelName"}, "ProgramName": {"shape": "__string", "documentation": "<p>The name of the <PERSON>.</p>", "location": "uri", "locationName": "ProgramName"}, "ScheduleConfiguration": {"shape": "UpdateProgramScheduleConfiguration", "documentation": "<p>The schedule configuration settings.</p>"}}}, "UpdateProgramResponse": {"type": "structure", "members": {"AdBreaks": {"shape": "__listOfAdBreak", "documentation": "<p>The ad break configuration settings.</p>"}, "Arn": {"shape": "__string", "documentation": "<p>The ARN to assign to the program.</p>"}, "ChannelName": {"shape": "__string", "documentation": "<p>The name to assign to the channel for this program.</p>"}, "ClipRange": {"shape": "ClipRange", "documentation": "<p>The clip range configuration settings.</p>"}, "CreationTime": {"shape": "__timestampUnix", "documentation": "<p>The time the program was created.</p>"}, "DurationMillis": {"shape": "__long", "documentation": "<p>The duration of the live program in milliseconds.</p>"}, "LiveSourceName": {"shape": "__string", "documentation": "<p>The name of the LiveSource for this Program.</p>"}, "ProgramName": {"shape": "__string", "documentation": "<p>The name to assign to this program.</p>"}, "ScheduledStartTime": {"shape": "__timestampUnix", "documentation": "<p>The scheduled start time for this Program.</p>"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name to assign to the source location for this program.</p>"}, "VodSourceName": {"shape": "__string", "documentation": "<p>The name that's used to refer to a VOD source.</p>"}}}, "UpdateProgramScheduleConfiguration": {"type": "structure", "members": {"ClipRange": {"shape": "ClipRange", "documentation": "<p>Program clip range configuration.</p>"}, "Transition": {"shape": "UpdateProgramTransition", "documentation": "<p>Program transition configuration.</p>"}}, "documentation": "<p>Schedule configuration parameters.</p>"}, "UpdateProgramTransition": {"type": "structure", "members": {"DurationMillis": {"shape": "__long", "documentation": "<p>The duration of the live program in seconds.</p>"}, "ScheduledStartTimeMillis": {"shape": "__long", "documentation": "<p>The date and time that the program is scheduled to start, in epoch milliseconds.</p>"}}, "documentation": "<p>Program transition configuration.</p>"}, "UpdateSourceLocationRequest": {"type": "structure", "required": ["HttpConfiguration", "SourceLocationName"], "members": {"AccessConfiguration": {"shape": "AccessConfiguration", "documentation": "<p>Access configuration parameters. Configures the type of authentication used to access content from your source location.</p>"}, "DefaultSegmentDeliveryConfiguration": {"shape": "DefaultSegmentDeliveryConfiguration", "documentation": "<p>The optional configuration for the host server that serves segments.</p>"}, "HttpConfiguration": {"shape": "HttpConfiguration", "documentation": "<p>The HTTP configuration for the source location.</p>"}, "SegmentDeliveryConfigurations": {"shape": "__listOfSegmentDeliveryConfiguration", "documentation": "<p>A list of the segment delivery configurations associated with this resource.</p>"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location.</p>", "location": "uri", "locationName": "SourceLocationName"}}}, "UpdateSourceLocationResponse": {"type": "structure", "members": {"AccessConfiguration": {"shape": "AccessConfiguration", "documentation": "<p>Access configuration parameters. Configures the type of authentication used to access content from your source location.</p>"}, "Arn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) associated with the source location.</p>"}, "CreationTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp that indicates when the source location was created.</p>"}, "DefaultSegmentDeliveryConfiguration": {"shape": "DefaultSegmentDeliveryConfiguration", "documentation": "<p>The optional configuration for the host server that serves segments.</p>"}, "HttpConfiguration": {"shape": "HttpConfiguration", "documentation": "<p>The HTTP configuration for the source location.</p>"}, "LastModifiedTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp that indicates when the source location was last modified.</p>"}, "SegmentDeliveryConfigurations": {"shape": "__listOfSegmentDeliveryConfiguration", "documentation": "<p>The segment delivery configurations for the source location. For information about MediaTailor configurations, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/configurations.html\">Working with configurations in AWS Elemental MediaTailor</a>.</p>"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location.</p>"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags to assign to the source location. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}}}, "UpdateVodSourceRequest": {"type": "structure", "required": ["HttpPackageConfigurations", "SourceLocationName", "VodSourceName"], "members": {"HttpPackageConfigurations": {"shape": "HttpPackageConfigurations", "documentation": "<p>A list of HTTP package configurations for the VOD source on this account.</p>"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location associated with this VOD Source.</p>", "location": "uri", "locationName": "SourceLocationName"}, "VodSourceName": {"shape": "__string", "documentation": "<p>The name of the VOD source.</p>", "location": "uri", "locationName": "VodSourceName"}}}, "UpdateVodSourceResponse": {"type": "structure", "members": {"Arn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) associated with the VOD source.</p>"}, "CreationTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp that indicates when the VOD source was created.</p>"}, "HttpPackageConfigurations": {"shape": "HttpPackageConfigurations", "documentation": "<p>A list of HTTP package configurations for the VOD source on this account.</p>"}, "LastModifiedTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp that indicates when the VOD source was last modified.</p>"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location associated with the VOD source.</p>"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags to assign to the VOD source. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}, "VodSourceName": {"shape": "__string", "documentation": "<p>The name of the VOD source.</p>"}}}, "VodSource": {"type": "structure", "required": ["<PERSON><PERSON>", "HttpPackageConfigurations", "SourceLocationName", "VodSourceName"], "members": {"Arn": {"shape": "__string", "documentation": "<p>The ARN for the VOD source.</p>"}, "CreationTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp that indicates when the VOD source was created.</p>"}, "HttpPackageConfigurations": {"shape": "HttpPackageConfigurations", "documentation": "<p>The HTTP package configurations for the VOD source.</p>"}, "LastModifiedTime": {"shape": "__timestampUnix", "documentation": "<p>The timestamp that indicates when the VOD source was last modified.</p>"}, "SourceLocationName": {"shape": "__string", "documentation": "<p>The name of the source location that the VOD source is associated with.</p>"}, "Tags": {"shape": "__mapOf__string", "documentation": "<p>The tags assigned to the VOD source. Tags are key-value pairs that you can associate with Amazon resources to help with organization, access control, and cost tracking. For more information, see <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/tagging.html\">Tagging AWS Elemental MediaTailor Resources</a>.</p>", "locationName": "tags"}, "VodSourceName": {"shape": "__string", "documentation": "<p>The name of the VOD source.</p>"}}, "documentation": "<p>VOD source configuration parameters.</p>"}, "__boolean": {"type": "boolean"}, "__integer": {"type": "integer"}, "__integerMin1": {"type": "integer", "min": 1}, "__integerMin1Max100": {"type": "integer", "max": 100, "min": 1}, "__listOfAdBreak": {"type": "list", "member": {"shape": "AdBreak"}}, "__listOfAlert": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}}, "__listOfAvailMatchingCriteria": {"type": "list", "member": {"shape": "AvailMatchingCriteria"}}, "__listOfChannel": {"type": "list", "member": {"shape": "Channel"}}, "__listOfLiveSource": {"type": "list", "member": {"shape": "LiveSource"}}, "__listOfPlaybackConfiguration": {"type": "list", "member": {"shape": "PlaybackConfiguration"}}, "__listOfPrefetchSchedule": {"type": "list", "member": {"shape": "PrefetchSchedule"}}, "__listOfScheduleAdBreak": {"type": "list", "member": {"shape": "ScheduleAdBreak"}}, "__listOfScheduleEntry": {"type": "list", "member": {"shape": "ScheduleEntry"}}, "__listOfSegmentDeliveryConfiguration": {"type": "list", "member": {"shape": "SegmentDeliveryConfiguration"}}, "__listOfSourceLocation": {"type": "list", "member": {"shape": "SourceLocation"}}, "__listOfVodSource": {"type": "list", "member": {"shape": "VodSource"}}, "__listOf__string": {"type": "list", "member": {"shape": "__string"}}, "__long": {"type": "long"}, "__mapOf__string": {"type": "map", "key": {"shape": "__string"}, "value": {"shape": "__string"}}, "__string": {"type": "string"}, "__timestampUnix": {"type": "timestamp", "timestampFormat": "unixTimestamp"}, "adMarkupTypes": {"type": "list", "member": {"shape": "AdMarkupType"}}}, "documentation": "<p>Use the AWS Elemental MediaTailor SDKs and CLI to configure scalable ad insertion and linear channels. With MediaTailor, you can assemble existing content into a linear stream and serve targeted ads to viewers while maintaining broadcast quality in over-the-top (OTT) video applications. For information about using the service, including detailed information about the settings covered in this guide, see the <a href=\"https://docs.aws.amazon.com/mediatailor/latest/ug/\">AWS Elemental MediaTailor User Guide</a>.</p> <p>Through the SDKs and the CLI you manage AWS Elemental MediaTailor configurations and channels the same as you do through the console. For example, you specify ad insertion behavior and mapping information for the origin server and the ad decision server (ADS).</p>"}