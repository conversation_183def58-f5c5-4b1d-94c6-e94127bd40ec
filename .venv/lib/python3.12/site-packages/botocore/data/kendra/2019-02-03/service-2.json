{"version": "2.0", "metadata": {"apiVersion": "2019-02-03", "endpointPrefix": "kendra", "jsonVersion": "1.1", "protocol": "json", "serviceAbbreviation": "kendra", "serviceFullName": "AWSKendraFrontendService", "serviceId": "kendra", "signatureVersion": "v4", "signingName": "kendra", "targetPrefix": "AWSKendraFrontendService", "uid": "kendra-2019-02-03"}, "operations": {"AssociateEntitiesToExperience": {"name": "AssociateEntitiesToExperience", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AssociateEntitiesToExperienceRequest"}, "output": {"shape": "AssociateEntitiesToExperienceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceAlreadyExistException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Grants users or groups in your IAM Identity Center identity source access to your Amazon Kendra experience. You can create an Amazon Kendra experience such as a search application. For more information on creating a search application experience, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/deploying-search-experience-no-code.html\">Building a search experience with no code</a>.</p>"}, "AssociatePersonasToEntities": {"name": "AssociatePersonasToEntities", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AssociatePersonasToEntitiesRequest"}, "output": {"shape": "AssociatePersonasToEntitiesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceAlreadyExistException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Defines the specific permissions of users or groups in your IAM Identity Center identity source with access to your Amazon Kendra experience. You can create an Amazon Kendra experience such as a search application. For more information on creating a search application experience, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/deploying-search-experience-no-code.html\">Building a search experience with no code</a>.</p>"}, "BatchDeleteDocument": {"name": "BatchDeleteDocument", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "BatchDeleteDocumentRequest"}, "output": {"shape": "BatchDeleteDocumentResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Removes one or more documents from an index. The documents must have been added with the <code>BatchPutDocument</code> API.</p> <p>The documents are deleted asynchronously. You can see the progress of the deletion by using Amazon Web Services CloudWatch. Any error messages related to the processing of the batch are sent to your Amazon Web Services CloudWatch log. You can also use the <code>BatchGetDocumentStatus</code> API to monitor the progress of deleting your documents.</p> <p>Deleting documents from an index using <code>BatchDeleteDocument</code> could take up to an hour or more, depending on the number of documents you want to delete.</p>"}, "BatchDeleteFeaturedResultsSet": {"name": "BatchDeleteFeaturedResultsSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "BatchDeleteFeaturedResultsSetRequest"}, "output": {"shape": "BatchDeleteFeaturedResultsSetResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Removes one or more sets of featured results. Features results are placed above all other results for certain queries. If there's an exact match of a query, then one or more specific documents are featured in the search results.</p>"}, "BatchGetDocumentStatus": {"name": "BatchGetDocumentStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "BatchGetDocumentStatusRequest"}, "output": {"shape": "BatchGetDocumentStatusResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns the indexing status for one or more documents submitted with the <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_BatchPutDocument.html\"> BatchPutDocument</a> API.</p> <p>When you use the <code>BatchPutDocument</code> API, documents are indexed asynchronously. You can use the <code>BatchGetDocumentStatus</code> API to get the current status of a list of documents so that you can determine if they have been successfully indexed.</p> <p>You can also use the <code>BatchGetDocumentStatus</code> API to check the status of the <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_BatchDeleteDocument.html\"> BatchDeleteDocument</a> API. When a document is deleted from the index, Amazon Kendra returns <code>NOT_FOUND</code> as the status.</p>"}, "BatchPutDocument": {"name": "BatchPutDocument", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "BatchPutDocumentRequest"}, "output": {"shape": "BatchPutDocumentResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "InternalServerException"}], "documentation": "<p>Adds one or more documents to an index.</p> <p>The <code>BatchPutDocument</code> API enables you to ingest inline documents or a set of documents stored in an Amazon S3 bucket. Use this API to ingest your text and unstructured text into an index, add custom attributes to the documents, and to attach an access control list to the documents added to the index.</p> <p>The documents are indexed asynchronously. You can see the progress of the batch using Amazon Web Services CloudWatch. Any error messages related to processing the batch are sent to your Amazon Web Services CloudWatch log. You can also use the <code>BatchGetDocumentStatus</code> API to monitor the progress of indexing your documents.</p> <p>For an example of ingesting inline documents using Python and Java SDKs, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/in-adding-binary-doc.html\">Adding files directly to an index</a>.</p>"}, "ClearQuerySuggestions": {"name": "ClearQuerySuggestions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ClearQuerySuggestionsRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Clears existing query suggestions from an index.</p> <p>This deletes existing suggestions only, not the queries in the query log. After you clear suggestions, Amazon Kendra learns new suggestions based on new queries added to the query log from the time you cleared suggestions. If you do not see any new suggestions, then please allow Amazon Kendra to collect enough queries to learn new suggestions.</p> <p> <code>ClearQuerySuggestions</code> is currently not supported in the Amazon Web Services GovCloud (US-West) region.</p>"}, "CreateAccessControlConfiguration": {"name": "CreateAccessControlConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateAccessControlConfigurationRequest"}, "output": {"shape": "CreateAccessControlConfigurationResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates an access configuration for your documents. This includes user and group access information for your documents. This is useful for user context filtering, where search results are filtered based on the user or their group access to documents.</p> <p>You can use this to re-configure your existing document level access control without indexing all of your documents again. For example, your index contains top-secret company documents that only certain employees or users should access. One of these users leaves the company or switches to a team that should be blocked from accessing top-secret documents. The user still has access to top-secret documents because the user had access when your documents were previously indexed. You can create a specific access control configuration for the user with deny access. You can later update the access control configuration to allow access if the user returns to the company and re-joins the 'top-secret' team. You can re-configure access control for your documents as circumstances change.</p> <p>To apply your access control configuration to certain documents, you call the <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_BatchPutDocument.html\">BatchPutDocument</a> API with the <code>AccessControlConfigurationId</code> included in the <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_Document.html\">Document</a> object. If you use an S3 bucket as a data source, you update the <code>.metadata.json</code> with the <code>AccessControlConfigurationId</code> and synchronize your data source. Amazon Kendra currently only supports access control configuration for S3 data sources and documents indexed using the <code>BatchPutDocument</code> API.</p>"}, "CreateDataSource": {"name": "CreateDataSource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateDataSourceRequest"}, "output": {"shape": "CreateDataSourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceAlreadyExistException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a data source connector that you want to use with an Amazon Kendra index.</p> <p>You specify a name, data source connector type and description for your data source. You also specify configuration information for the data source connector.</p> <p> <code>CreateDataSource</code> is a synchronous operation. The operation returns 200 if the data source was successfully created. Otherwise, an exception is raised.</p> <p>For an example of creating an index and data source using the Python SDK, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/gs-python.html\">Getting started with Python SDK</a>. For an example of creating an index and data source using the Java SDK, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/gs-java.html\">Getting started with Java SDK</a>.</p>"}, "CreateExperience": {"name": "CreateExperience", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateExperienceRequest"}, "output": {"shape": "CreateExperienceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates an Amazon Kendra experience such as a search application. For more information on creating a search application experience, including using the Python and Java SDKs, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/deploying-search-experience-no-code.html\">Building a search experience with no code</a>.</p>"}, "CreateFaq": {"name": "CreateFaq", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateFaqRequest"}, "output": {"shape": "CreateFaqResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a set of frequently ask questions (FAQs) using a specified FAQ file stored in an Amazon S3 bucket.</p> <p>Adding FAQs to an index is an asynchronous operation.</p> <p>For an example of adding an FAQ to an index using Python and Java SDKs, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/in-creating-faq.html#using-faq-file\">Using your FAQ file</a>.</p>"}, "CreateFeaturedResultsSet": {"name": "CreateFeaturedResultsSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateFeaturedResultsSetRequest"}, "output": {"shape": "CreateFeaturedResultsSetResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "FeaturedResultsConflictException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a set of featured results to display at the top of the search results page. Featured results are placed above all other results for certain queries. You map specific queries to specific documents for featuring in the results. If a query contains an exact match, then one or more specific documents are featured in the search results.</p> <p>You can create up to 50 sets of featured results per index. You can request to increase this limit by contacting <a href=\"http://aws.amazon.com/contact-us/\">Support</a>.</p>"}, "CreateIndex": {"name": "CreateIndex", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateIndexRequest"}, "output": {"shape": "CreateIndexResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceAlreadyExistException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates an Amazon Kendra index. Index creation is an asynchronous API. To determine if index creation has completed, check the <code>Status</code> field returned from a call to <code>DescribeIndex</code>. The <code>Status</code> field is set to <code>ACTIVE</code> when the index is ready to use.</p> <p>Once the index is active, you can index your documents using the <code>BatchPutDocument</code> API or using one of the supported <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/data-sources.html\">data sources</a>.</p> <p>For an example of creating an index and data source using the Python SDK, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/gs-python.html\">Getting started with Python SDK</a>. For an example of creating an index and data source using the Java SDK, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/gs-java.html\">Getting started with Java SDK</a>.</p>"}, "CreateQuerySuggestionsBlockList": {"name": "CreateQuerySuggestionsBlockList", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateQuerySuggestionsBlockListRequest"}, "output": {"shape": "CreateQuerySuggestionsBlockListResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a block list to exlcude certain queries from suggestions.</p> <p>Any query that contains words or phrases specified in the block list is blocked or filtered out from being shown as a suggestion.</p> <p>You need to provide the file location of your block list text file in your S3 bucket. In your text file, enter each block word or phrase on a separate line.</p> <p>For information on the current quota limits for block lists, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/quotas.html\">Quotas for Amazon Kendra</a>.</p> <p> <code>CreateQuerySuggestionsBlockList</code> is currently not supported in the Amazon Web Services GovCloud (US-West) region.</p> <p>For an example of creating a block list for query suggestions using the Python SDK, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/query-suggestions.html#query-suggestions-blocklist\">Query suggestions block list</a>.</p>"}, "CreateThesaurus": {"name": "CreateThesaurus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateThesaurusRequest"}, "output": {"shape": "CreateThesaurusResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a thesaurus for an index. The thesaurus contains a list of synonyms in Solr format.</p> <p>For an example of adding a thesaurus file to an index, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/index-synonyms-adding-thesaurus-file.html\">Adding custom synonyms to an index</a>.</p>"}, "DeleteAccessControlConfiguration": {"name": "DeleteAccessControlConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteAccessControlConfigurationRequest"}, "output": {"shape": "DeleteAccessControlConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an access control configuration that you created for your documents in an index. This includes user and group access information for your documents. This is useful for user context filtering, where search results are filtered based on the user or their group access to documents.</p>"}, "DeleteDataSource": {"name": "DeleteDataSource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDataSourceRequest"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an Amazon Kendra data source connector. An exception is not thrown if the data source is already being deleted. While the data source is being deleted, the <code>Status</code> field returned by a call to the <code>DescribeDataSource</code> API is set to <code>DELETING</code>. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/delete-data-source.html\">Deleting Data Sources</a>.</p> <p>Deleting an entire data source or re-syncing your index after deleting specific documents from a data source could take up to an hour or more, depending on the number of documents you want to delete.</p>"}, "DeleteExperience": {"name": "DeleteExperience", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteExperienceRequest"}, "output": {"shape": "DeleteExperienceResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes your Amazon Kendra experience such as a search application. For more information on creating a search application experience, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/deploying-search-experience-no-code.html\">Building a search experience with no code</a>.</p>"}, "DeleteFaq": {"name": "DeleteFaq", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteFaqRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Removes an FAQ from an index.</p>"}, "DeleteIndex": {"name": "DeleteIndex", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteIndexRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an existing Amazon Kendra index. An exception is not thrown if the index is already being deleted. While the index is being deleted, the <code>Status</code> field returned by a call to the <code>DescribeIndex</code> API is set to <code>DELETING</code>.</p>"}, "DeletePrincipalMapping": {"name": "DeletePrincipalMapping", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeletePrincipalMappingRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes a group so that all users and sub groups that belong to the group can no longer access documents only available to that group.</p> <p>For example, after deleting the group \"Summer Interns\", all interns who belonged to that group no longer see intern-only documents in their search results.</p> <p>If you want to delete or replace users or sub groups of a group, you need to use the <code>PutPrincipalMapping</code> operation. For example, if a user in the group \"Engineering\" leaves the engineering team and another user takes their place, you provide an updated list of users or sub groups that belong to the \"Engineering\" group when calling <code>PutPrincipalMapping</code>. You can update your internal list of users or sub groups and input this list when calling <code>PutPrincipalMapping</code>.</p> <p> <code>DeletePrincipalMapping</code> is currently not supported in the Amazon Web Services GovCloud (US-West) region.</p>"}, "DeleteQuerySuggestionsBlockList": {"name": "DeleteQuerySuggestionsBlockList", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteQuerySuggestionsBlockListRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes a block list used for query suggestions for an index.</p> <p>A deleted block list might not take effect right away. Amazon Kendra needs to refresh the entire suggestions list to add back the queries that were previously blocked.</p> <p> <code>DeleteQuerySuggestionsBlockList</code> is currently not supported in the Amazon Web Services GovCloud (US-West) region.</p>"}, "DeleteThesaurus": {"name": "DeleteThesaurus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteThesaurusRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an existing Amazon Kendra thesaurus. </p>"}, "DescribeAccessControlConfiguration": {"name": "DescribeAccessControlConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeAccessControlConfigurationRequest"}, "output": {"shape": "DescribeAccessControlConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets information about an access control configuration that you created for your documents in an index. This includes user and group access information for your documents. This is useful for user context filtering, where search results are filtered based on the user or their group access to documents.</p>"}, "DescribeDataSource": {"name": "DescribeDataSource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeDataSourceRequest"}, "output": {"shape": "DescribeDataSourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets information about an Amazon Kendra data source connector.</p>"}, "DescribeExperience": {"name": "DescribeExperience", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeExperienceRequest"}, "output": {"shape": "DescribeExperienceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets information about your Amazon Kendra experience such as a search application. For more information on creating a search application experience, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/deploying-search-experience-no-code.html\">Building a search experience with no code</a>.</p>"}, "DescribeFaq": {"name": "DescribeFaq", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeFaqRequest"}, "output": {"shape": "DescribeFaqResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets information about an FAQ list.</p>"}, "DescribeFeaturedResultsSet": {"name": "DescribeFeaturedResultsSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeFeaturedResultsSetRequest"}, "output": {"shape": "DescribeFeaturedResultsSetResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets information about a set of featured results. Features results are placed above all other results for certain queries. If there's an exact match of a query, then one or more specific documents are featured in the search results.</p>"}, "DescribeIndex": {"name": "DescribeIndex", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeIndexRequest"}, "output": {"shape": "DescribeIndexResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets information about an existing Amazon Kendra index.</p>"}, "DescribePrincipalMapping": {"name": "DescribePrincipalMapping", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribePrincipalMappingRequest"}, "output": {"shape": "DescribePrincipalMappingResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Describes the processing of <code>PUT</code> and <code>DELETE</code> actions for mapping users to their groups. This includes information on the status of actions currently processing or yet to be processed, when actions were last updated, when actions were received by Amazon Kendra, the latest action that should process and apply after other actions, and useful error messages if an action could not be processed.</p> <p> <code>DescribePrincipalMapping</code> is currently not supported in the Amazon Web Services GovCloud (US-West) region.</p>"}, "DescribeQuerySuggestionsBlockList": {"name": "DescribeQuerySuggestionsBlockList", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeQuerySuggestionsBlockListRequest"}, "output": {"shape": "DescribeQuerySuggestionsBlockListResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets information about a block list used for query suggestions for an index.</p> <p>This is used to check the current settings that are applied to a block list.</p> <p> <code>DescribeQuerySuggestionsBlockList</code> is currently not supported in the Amazon Web Services GovCloud (US-West) region.</p>"}, "DescribeQuerySuggestionsConfig": {"name": "DescribeQuerySuggestionsConfig", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeQuerySuggestionsConfigRequest"}, "output": {"shape": "DescribeQuerySuggestionsConfigResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets information on the settings of query suggestions for an index.</p> <p>This is used to check the current settings applied to query suggestions.</p> <p> <code>DescribeQuerySuggestionsConfig</code> is currently not supported in the Amazon Web Services GovCloud (US-West) region.</p>"}, "DescribeThesaurus": {"name": "DescribeThesaurus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeThesaurusRequest"}, "output": {"shape": "DescribeThesaurusResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets information about an existing Amazon Kendra thesaurus.</p>"}, "DisassociateEntitiesFromExperience": {"name": "DisassociateEntitiesFromExperience", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DisassociateEntitiesFromExperienceRequest"}, "output": {"shape": "DisassociateEntitiesFromExperienceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Prevents users or groups in your IAM Identity Center identity source from accessing your Amazon Kendra experience. You can create an Amazon Kendra experience such as a search application. For more information on creating a search application experience, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/deploying-search-experience-no-code.html\">Building a search experience with no code</a>.</p>"}, "DisassociatePersonasFromEntities": {"name": "DisassociatePersonasFromEntities", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DisassociatePersonasFromEntitiesRequest"}, "output": {"shape": "DisassociatePersonasFromEntitiesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Removes the specific permissions of users or groups in your IAM Identity Center identity source with access to your Amazon Kendra experience. You can create an Amazon Kendra experience such as a search application. For more information on creating a search application experience, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/deploying-search-experience-no-code.html\">Building a search experience with no code</a>.</p>"}, "GetQuerySuggestions": {"name": "GetQuerySuggestions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetQuerySuggestionsRequest"}, "output": {"shape": "GetQuerySuggestionsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Fetches the queries that are suggested to your users.</p> <p> <code>GetQuerySuggestions</code> is currently not supported in the Amazon Web Services GovCloud (US-West) region.</p>"}, "GetSnapshots": {"name": "GetSnapshots", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetSnapshotsRequest"}, "output": {"shape": "GetSnapshotsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves search metrics data. The data provides a snapshot of how your users interact with your search application and how effective the application is.</p>"}, "ListAccessControlConfigurations": {"name": "ListAccessControlConfigurations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAccessControlConfigurationsRequest"}, "output": {"shape": "ListAccessControlConfigurationsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists one or more access control configurations for an index. This includes user and group access information for your documents. This is useful for user context filtering, where search results are filtered based on the user or their group access to documents.</p>"}, "ListDataSourceSyncJobs": {"name": "ListDataSourceSyncJobs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListDataSourceSyncJobsRequest"}, "output": {"shape": "ListDataSourceSyncJobsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets statistics about synchronizing a data source connector.</p>"}, "ListDataSources": {"name": "ListDataSources", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListDataSourcesRequest"}, "output": {"shape": "ListDataSourcesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the data source connectors that you have created.</p>"}, "ListEntityPersonas": {"name": "ListEntityPersonas", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListEntityPersonasRequest"}, "output": {"shape": "ListEntityPersonasResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists specific permissions of users and groups with access to your Amazon Kendra experience.</p>"}, "ListExperienceEntities": {"name": "ListExperienceEntities", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListExperienceEntitiesRequest"}, "output": {"shape": "ListExperienceEntitiesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists users or groups in your IAM Identity Center identity source that are granted access to your Amazon Kendra experience. You can create an Amazon Kendra experience such as a search application. For more information on creating a search application experience, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/deploying-search-experience-no-code.html\">Building a search experience with no code</a>.</p>"}, "ListExperiences": {"name": "ListExperiences", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListExperiencesRequest"}, "output": {"shape": "ListExperiencesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists one or more Amazon Kendra experiences. You can create an Amazon Kendra experience such as a search application. For more information on creating a search application experience, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/deploying-search-experience-no-code.html\">Building a search experience with no code</a>.</p>"}, "ListFaqs": {"name": "ListFaqs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListFaqsRequest"}, "output": {"shape": "ListFaqsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets a list of FAQ lists associated with an index.</p>"}, "ListFeaturedResultsSets": {"name": "ListFeaturedResultsSets", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListFeaturedResultsSetsRequest"}, "output": {"shape": "ListFeaturedResultsSetsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists all your sets of featured results for a given index. Features results are placed above all other results for certain queries. If there's an exact match of a query, then one or more specific documents are featured in the search results.</p>"}, "ListGroupsOlderThanOrderingId": {"name": "ListGroupsOlderThanOrderingId", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListGroupsOlderThanOrderingIdRequest"}, "output": {"shape": "ListGroupsOlderThanOrderingIdResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Provides a list of groups that are mapped to users before a given ordering or timestamp identifier.</p> <p> <code>ListGroupsOlderThanOrderingId</code> is currently not supported in the Amazon Web Services GovCloud (US-West) region.</p>"}, "ListIndices": {"name": "ListIndices", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListIndicesRequest"}, "output": {"shape": "ListIndicesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the Amazon Kendra indexes that you created.</p>"}, "ListQuerySuggestionsBlockLists": {"name": "ListQuerySuggestionsBlockLists", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListQuerySuggestionsBlockListsRequest"}, "output": {"shape": "ListQuerySuggestionsBlockListsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the block lists used for query suggestions for an index.</p> <p>For information on the current quota limits for block lists, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/quotas.html\">Quotas for Amazon Kendra</a>.</p> <p> <code>ListQuerySuggestionsBlockLists</code> is currently not supported in the Amazon Web Services GovCloud (US-West) region.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceUnavailableException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets a list of tags associated with a specified resource. Indexes, FAQs, and data sources can have tags associated with them.</p>"}, "ListThesauri": {"name": "ListThesauri", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListThesauriRequest"}, "output": {"shape": "ListThesauriResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the thesauri for an index.</p>"}, "PutPrincipalMapping": {"name": "PutPrincipalMapping", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutPrincipalMappingRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "InternalServerException"}], "documentation": "<p>Maps users to their groups so that you only need to provide the user ID when you issue the query.</p> <p>You can also map sub groups to groups. For example, the group \"Company Intellectual Property Teams\" includes sub groups \"Research\" and \"Engineering\". These sub groups include their own list of users or people who work in these teams. Only users who work in research and engineering, and therefore belong in the intellectual property group, can see top-secret company documents in their search results.</p> <p>This is useful for user context filtering, where search results are filtered based on the user or their group access to documents. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/user-context-filter.html\">Filtering on user context</a>.</p> <p>If more than five <code>PUT</code> actions for a group are currently processing, a validation exception is thrown.</p>"}, "Query": {"name": "Query", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "QueryRequest"}, "output": {"shape": "QueryResult"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "InternalServerException"}], "documentation": "<p>Searches an index given an input query.</p> <note> <p>If you are working with large language models (LLMs) or implementing retrieval augmented generation (RAG) systems, you can use Amazon Kendra's <a href=\"https://docs.aws.amazon.com/kendra/latest/APIReference/API_Retrieve.html\">Retrieve</a> API, which can return longer semantically relevant passages. We recommend using the <code>Retrieve</code> API instead of filing a service limit increase to increase the <code>Query</code> API document excerpt length.</p> </note> <p>You can configure boosting or relevance tuning at the query level to override boosting at the index level, filter based on document fields/attributes and faceted search, and filter based on the user or their group access to documents. You can also include certain fields in the response that might provide useful additional information.</p> <p>A query response contains three types of results.</p> <ul> <li> <p>Relevant suggested answers. The answers can be either a text excerpt or table excerpt. The answer can be highlighted in the excerpt.</p> </li> <li> <p>Matching FAQs or questions-answer from your FAQ file.</p> </li> <li> <p>Relevant documents. This result type includes an excerpt of the document with the document title. The searched terms can be highlighted in the excerpt.</p> </li> </ul> <p>You can specify that the query return only one type of result using the <code>QueryResultTypeFilter</code> parameter. Each query returns the 100 most relevant results. If you filter result type to only question-answers, a maximum of four results are returned. If you filter result type to only answers, a maximum of three results are returned.</p>"}, "Retrieve": {"name": "Retrieve", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RetrieveRequest"}, "output": {"shape": "RetrieveResult"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves relevant passages or text excerpts given an input query.</p> <p>This API is similar to the <a href=\"https://docs.aws.amazon.com/kendra/latest/APIReference/API_Query.html\">Query</a> API. However, by default, the <code>Query</code> API only returns excerpt passages of up to 100 token words. With the <code>Retrieve</code> API, you can retrieve longer passages of up to 200 token words and up to 100 semantically relevant passages. This doesn't include question-answer or FAQ type responses from your index. The passages are text excerpts that can be semantically extracted from multiple documents and multiple parts of the same document. If in extreme cases your documents produce zero passages using the <code>Retrieve</code> API, you can alternatively use the <code>Query</code> API and its types of responses.</p> <p>You can also do the following:</p> <ul> <li> <p>Override boosting at the index level</p> </li> <li> <p>Filter based on document fields or attributes</p> </li> <li> <p>Filter based on the user or their group access to documents</p> </li> <li> <p>View the confidence score bucket for a retrieved passage result. The confidence bucket provides a relative ranking that indicates how confident Amazon Kendra is that the response is relevant to the query.</p> <note> <p>Confidence score buckets are currently available only for English.</p> </note> </li> </ul> <p>You can also include certain fields in the response that might provide useful additional information.</p> <p>The <code>Retrieve</code> API shares the number of <a href=\"https://docs.aws.amazon.com/kendra/latest/APIReference/API_CapacityUnitsConfiguration.html\">query capacity units</a> that you set for your index. For more information on what's included in a single capacity unit and the default base capacity for an index, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/adjusting-capacity.html\">Adjusting capacity</a>.</p>"}, "StartDataSourceSyncJob": {"name": "StartDataSourceSyncJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartDataSourceSyncJobRequest"}, "output": {"shape": "StartDataSourceSyncJobResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Starts a synchronization job for a data source connector. If a synchronization job is already in progress, Amazon Kendra returns a <code>ResourceInUseException</code> exception.</p> <p>Re-syncing your data source with your index after modifying, adding, or deleting documents from your data source respository could take up to an hour or more, depending on the number of documents to sync.</p>"}, "StopDataSourceSyncJob": {"name": "StopDataSourceSyncJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StopDataSourceSyncJobRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Stops a synchronization job that is currently running. You can't stop a scheduled synchronization job.</p>"}, "SubmitFeedback": {"name": "SubmitFeedback", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "SubmitFeedbackRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceUnavailableException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Enables you to provide feedback to Amazon Kendra to improve the performance of your index.</p> <p> <code>SubmitFeedback</code> is currently not supported in the Amazon Web Services GovCloud (US-West) region.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceUnavailableException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Adds the specified tag to the specified index, FAQ, or data source resource. If the tag already exists, the existing value is replaced with the new value.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceUnavailableException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Removes a tag from an index, FAQ, or a data source.</p>"}, "UpdateAccessControlConfiguration": {"name": "UpdateAccessControlConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateAccessControlConfigurationRequest"}, "output": {"shape": "UpdateAccessControlConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates an access control configuration for your documents in an index. This includes user and group access information for your documents. This is useful for user context filtering, where search results are filtered based on the user or their group access to documents.</p> <p>You can update an access control configuration you created without indexing all of your documents again. For example, your index contains top-secret company documents that only certain employees or users should access. You created an 'allow' access control configuration for one user who recently joined the 'top-secret' team, switching from a team with 'deny' access to top-secret documents. However, the user suddenly returns to their previous team and should no longer have access to top secret documents. You can update the access control configuration to re-configure access control for your documents as circumstances change.</p> <p>You call the <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_BatchPutDocument.html\">BatchPutDocument</a> API to apply the updated access control configuration, with the <code>AccessControlConfigurationId</code> included in the <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_Document.html\">Document</a> object. If you use an S3 bucket as a data source, you synchronize your data source to apply the <code>AccessControlConfigurationId</code> in the <code>.metadata.json</code> file. Amazon Kendra currently only supports access control configuration for S3 data sources and documents indexed using the <code>BatchPutDocument</code> API.</p>"}, "UpdateDataSource": {"name": "UpdateDataSource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateDataSourceRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates an existing Amazon Kendra data source connector.</p>"}, "UpdateExperience": {"name": "UpdateExperience", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateExperienceRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates your Amazon Kendra experience such as a search application. For more information on creating a search application experience, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/deploying-search-experience-no-code.html\">Building a search experience with no code</a>.</p>"}, "UpdateFeaturedResultsSet": {"name": "UpdateFeaturedResultsSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateFeaturedResultsSetRequest"}, "output": {"shape": "UpdateFeaturedResultsSetResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "FeaturedResultsConflictException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates a set of featured results. Features results are placed above all other results for certain queries. You map specific queries to specific documents for featuring in the results. If a query contains an exact match of a query, then one or more specific documents are featured in the search results.</p>"}, "UpdateIndex": {"name": "UpdateIndex", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateIndexRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates an existing Amazon Kendra index.</p>"}, "UpdateQuerySuggestionsBlockList": {"name": "UpdateQuerySuggestionsBlockList", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateQuerySuggestionsBlockListRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates a block list used for query suggestions for an index.</p> <p>Updates to a block list might not take effect right away. Amazon Kendra needs to refresh the entire suggestions list to apply any updates to the block list. Other changes not related to the block list apply immediately.</p> <p>If a block list is updating, then you need to wait for the first update to finish before submitting another update.</p> <p>Amazon Kendra supports partial updates, so you only need to provide the fields you want to update.</p> <p> <code>UpdateQuerySuggestionsBlockList</code> is currently not supported in the Amazon Web Services GovCloud (US-West) region.</p>"}, "UpdateQuerySuggestionsConfig": {"name": "UpdateQuerySuggestionsConfig", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateQuerySuggestionsConfigRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates the settings of query suggestions for an index.</p> <p>Amazon Kendra supports partial updates, so you only need to provide the fields you want to update.</p> <p>If an update is currently processing, you need to wait for the update to finish before making another update.</p> <p>Updates to query suggestions settings might not take effect right away. The time for your updated settings to take effect depends on the updates made and the number of search queries in your index.</p> <p>You can still enable/disable query suggestions at any time.</p> <p> <code>UpdateQuerySuggestionsConfig</code> is currently not supported in the Amazon Web Services GovCloud (US-West) region.</p>"}, "UpdateThesaurus": {"name": "UpdateThesaurus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateThesaurusRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates a thesaurus for an index.</p>"}}, "shapes": {"AccessControlConfigurationId": {"type": "string", "max": 36, "min": 1, "pattern": "[a-zA-Z0-9-]+"}, "AccessControlConfigurationName": {"type": "string", "max": 200, "min": 1, "pattern": "[\\S\\s]*"}, "AccessControlConfigurationSummary": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "AccessControlConfigurationId", "documentation": "<p>The identifier of the access control configuration.</p>"}}, "documentation": "<p>Summary information on an access control configuration that you created for your documents in an index.</p>"}, "AccessControlConfigurationSummaryList": {"type": "list", "member": {"shape": "AccessControlConfigurationSummary"}}, "AccessControlListConfiguration": {"type": "structure", "members": {"KeyPath": {"shape": "S3ObjectKey", "documentation": "<p>Path to the Amazon S3 bucket that contains the ACL files.</p>"}}, "documentation": "<p>Access Control List files for the documents in a data source. For the format of the file, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/s3-acl.html\">Access control for S3 data sources</a>.</p>"}, "AccessDeniedException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>You don't have sufficient access to perform this action. Please ensure you have the required permission policies and user accounts and try again.</p>", "exception": true}, "AclConfiguration": {"type": "structure", "required": ["AllowedGroupsColumnName"], "members": {"AllowedGroupsColumnName": {"shape": "ColumnName", "documentation": "<p>A list of groups, separated by semi-colons, that filters a query response based on user context. The document is only returned to users that are in one of the groups specified in the <code>UserContext</code> field of the <code>Query</code> API.</p>"}}, "documentation": "<p>Provides information about the column that should be used for filtering the query response by groups.</p>"}, "AdditionalResultAttribute": {"type": "structure", "required": ["Key", "ValueType", "Value"], "members": {"Key": {"shape": "String", "documentation": "<p>The key that identifies the attribute.</p>"}, "ValueType": {"shape": "AdditionalResultAttributeValueType", "documentation": "<p>The data type of the <code>Value</code> property.</p>"}, "Value": {"shape": "AdditionalResultAttributeValue", "documentation": "<p>An object that contains the attribute value.</p>"}}, "documentation": "<p>An attribute returned from an index query.</p>"}, "AdditionalResultAttributeList": {"type": "list", "member": {"shape": "AdditionalResultAttribute"}}, "AdditionalResultAttributeValue": {"type": "structure", "members": {"TextWithHighlightsValue": {"shape": "TextWithHighlights", "documentation": "<p>The text associated with the attribute and information about the highlight to apply to the text.</p>"}}, "documentation": "<p>An attribute returned with a document from a search.</p>"}, "AdditionalResultAttributeValueType": {"type": "string", "enum": ["TEXT_WITH_HIGHLIGHTS_VALUE"]}, "AlfrescoConfiguration": {"type": "structure", "required": ["SiteUrl", "SiteId", "SecretArn", "SslCertificateS3Path"], "members": {"SiteUrl": {"shape": "SiteUrl", "documentation": "<p>The URL of the Alfresco site. For example, <i>https://hostname:8080</i>.</p>"}, "SiteId": {"shape": "SiteId", "documentation": "<p>The identifier of the Alfresco site. For example, <i>my-site</i>.</p>"}, "SecretArn": {"shape": "SecretArn", "documentation": "<p>The Amazon Resource Name (ARN) of an Secrets Manager secret that contains the key-value pairs required to connect to your Alfresco data source. The secret must contain a JSON structure with the following keys:</p> <ul> <li> <p>username—The user name of the Alfresco account.</p> </li> <li> <p>password—The password of the Alfresco account.</p> </li> </ul>"}, "SslCertificateS3Path": {"shape": "S3Path", "documentation": "<p>The path to the SSL certificate stored in an Amazon S3 bucket. You use this to connect to Alfresco if you require a secure SSL connection.</p> <p>You can simply generate a self-signed X509 certificate on any computer using OpenSSL. For an example of using OpenSSL to create an X509 certificate, see <a href=\"https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/configuring-https-ssl.html\">Create and sign an X509 certificate</a>.</p>"}, "CrawlSystemFolders": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to index shared files.</p>"}, "CrawlComments": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to index comments of blogs and other content.</p>"}, "EntityFilter": {"shape": "En<PERSON>ty<PERSON><PERSON><PERSON>", "documentation": "<p>Specify whether to index document libraries, wikis, or blogs. You can specify one or more of these options.</p>"}, "DocumentLibraryFieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map attributes or field names of Alfresco document libraries to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Alfresco fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\"> Mapping data source fields</a>. The Alfresco data source field names must exist in your Alfresco custom metadata.</p>"}, "BlogFieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map attributes or field names of Alfresco blogs to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Alfresco fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\"> Mapping data source fields</a>. The Alfresco data source field names must exist in your Alfresco custom metadata.</p>"}, "WikiFieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map attributes or field names of Alfresco wikis to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Alfresco fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\"> Mapping data source fields</a>. The Alfresco data source field names must exist in your Alfresco custom metadata.</p>"}, "InclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to include certain files in your Alfresco data source. Files that match the patterns are included in the index. Files that don't match the patterns are excluded from the index. If a file matches both an inclusion pattern and an exclusion pattern, the exclusion pattern takes precedence and the file isn't included in the index.</p>"}, "ExclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to exclude certain files in your Alfresco data source. Files that match the patterns are excluded from the index. Files that don't match the patterns are included in the index. If a file matches both an inclusion pattern and an exclusion pattern, the exclusion pattern takes precedence and the file isn't included in the index.</p>"}, "VpcConfiguration": {"shape": "DataSourceVpcConfiguration", "documentation": "<p>Configuration information for an Amazon Virtual Private Cloud to connect to your Alfresco. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/vpc-configuration.html\">Configuring a VPC</a>.</p>"}}, "documentation": "<p>Provides the configuration information to connect to Alfresco as your data source.</p> <note> <p>Support for <code>AlfrescoConfiguration</code> ended May 2023. We recommend migrating to or using the Alfresco data source template schema / <a href=\"https://docs.aws.amazon.com/kendra/latest/APIReference/API_TemplateConfiguration.html\">TemplateConfiguration</a> API.</p> </note>"}, "AlfrescoEntity": {"type": "string", "enum": ["wiki", "blog", "documentLibrary"]}, "AmazonResourceName": {"type": "string", "max": 1011, "min": 1}, "AssociateEntitiesToExperienceFailedEntityList": {"type": "list", "member": {"shape": "FailedEntity"}, "max": 20, "min": 1}, "AssociateEntitiesToExperienceRequest": {"type": "structure", "required": ["Id", "IndexId", "EntityList"], "members": {"Id": {"shape": "ExperienceId", "documentation": "<p>The identifier of your Amazon Kendra experience.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for your Amazon Kendra experience.</p>"}, "EntityList": {"shape": "AssociateEntityList", "documentation": "<p>Lists users or groups in your IAM Identity Center identity source.</p>"}}}, "AssociateEntitiesToExperienceResponse": {"type": "structure", "members": {"FailedEntityList": {"shape": "AssociateEntitiesToExperienceFailedEntityList", "documentation": "<p>Lists the users or groups in your IAM Identity Center identity source that failed to properly configure with your Amazon Kendra experience.</p>"}}}, "AssociateEntityList": {"type": "list", "member": {"shape": "EntityConfiguration"}, "max": 20, "min": 1}, "AssociatePersonasToEntitiesRequest": {"type": "structure", "required": ["Id", "IndexId", "Personas"], "members": {"Id": {"shape": "ExperienceId", "documentation": "<p>The identifier of your Amazon Kendra experience.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for your Amazon Kendra experience.</p>"}, "Personas": {"shape": "EntityPersonaConfigurationList", "documentation": "<p>The personas that define the specific permissions of users or groups in your IAM Identity Center identity source. The available personas or access roles are <code>Owner</code> and <code>Viewer</code>. For more information on these personas, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/deploying-search-experience-no-code.html#access-search-experience\">Providing access to your search page</a>.</p>"}}}, "AssociatePersonasToEntitiesResponse": {"type": "structure", "members": {"FailedEntityList": {"shape": "FailedEntityList", "documentation": "<p>Lists the users or groups in your IAM Identity Center identity source that failed to properly configure with your Amazon Kendra experience.</p>"}}}, "AttributeFilter": {"type": "structure", "members": {"AndAllFilters": {"shape": "AttributeFilterList", "documentation": "<p>Performs a logical <code>AND</code> operation on all filters that you specify.</p>"}, "OrAllFilters": {"shape": "AttributeFilterList", "documentation": "<p>Performs a logical <code>OR</code> operation on all filters that you specify.</p>"}, "NotFilter": {"shape": "Attribute<PERSON>ilter", "documentation": "<p>Performs a logical <code>NOT</code> operation on all filters that you specify.</p>"}, "EqualsTo": {"shape": "DocumentAttribute", "documentation": "<p>Performs an equals operation on document attributes/fields and their values.</p>"}, "ContainsAll": {"shape": "DocumentAttribute", "documentation": "<p>Returns true when a document contains all of the specified document attributes/fields. This filter is only applicable to <a href=\"https://docs.aws.amazon.com/kendra/latest/APIReference/API_DocumentAttributeValue.html\">StringListValue</a>.</p>"}, "ContainsAny": {"shape": "DocumentAttribute", "documentation": "<p>Returns true when a document contains any of the specified document attributes/fields. This filter is only applicable to <a href=\"https://docs.aws.amazon.com/kendra/latest/APIReference/API_DocumentAttributeValue.html\">StringListValue</a>.</p>"}, "GreaterThan": {"shape": "DocumentAttribute", "documentation": "<p>Performs a greater than operation on document attributes/fields and their values. Use with the <a href=\"https://docs.aws.amazon.com/kendra/latest/APIReference/API_DocumentAttributeValue.html\">document attribute type</a> <code>Date</code> or <code>Long</code>.</p>"}, "GreaterThanOrEquals": {"shape": "DocumentAttribute", "documentation": "<p>Performs a greater or equals than operation on document attributes/fields and their values. Use with the <a href=\"https://docs.aws.amazon.com/kendra/latest/APIReference/API_DocumentAttributeValue.html\">document attribute type</a> <code>Date</code> or <code>Long</code>.</p>"}, "LessThan": {"shape": "DocumentAttribute", "documentation": "<p>Performs a less than operation on document attributes/fields and their values. Use with the <a href=\"https://docs.aws.amazon.com/kendra/latest/APIReference/API_DocumentAttributeValue.html\">document attribute type</a> <code>Date</code> or <code>Long</code>.</p>"}, "LessThanOrEquals": {"shape": "DocumentAttribute", "documentation": "<p>Performs a less than or equals operation on document attributes/fields and their values. Use with the <a href=\"https://docs.aws.amazon.com/kendra/latest/APIReference/API_DocumentAttributeValue.html\">document attribute type</a> <code>Date</code> or <code>Long</code>.</p>"}}, "documentation": "<p>Filters the search results based on document attributes or fields.</p> <p>You can filter results using attributes for your particular documents. The attributes must exist in your index. For example, if your documents include the custom attribute \"Department\", you can filter documents that belong to the \"HR\" department. You would use the <code>EqualsTo</code> operation to filter results or documents with \"Department\" equals to \"HR\".</p> <p>You can use <code>AndAllFilters</code> and <code>AndOrFilters</code> in combination with each other or with other operations such as <code>EqualsTo</code>. For example:</p> <p> <code>AndAllFilters</code> </p> <ul> <li> <p> <code>EqualsTo</code>: \"Department\", \"HR\"</p> </li> <li> <p> <code>AndOrFilters</code> </p> <ul> <li> <p> <code>ContainsAny</code>: \"Project Name\", [\"new hires\", \"new hiring\"]</p> </li> </ul> </li> </ul> <p>This example filters results or documents that belong to the HR department <i>and</i> belong to projects that contain \"new hires\" <i>or</i> \"new hiring\" in the project name (must use <code>ContainAny</code> with <code>StringListValue</code>). This example is filtering with a depth of 2.</p> <p>You cannot filter more than a depth of 2, otherwise you receive a <code>ValidationException</code> exception with the message \"AttributeFilter cannot have a depth of more than 2.\" Also, if you use more than 10 attribute filters in a given list for <code>AndAllFilters</code> or <code>OrAllFilters</code>, you receive a <code>ValidationException</code> with the message \"AttributeFilter cannot have a length of more than 10\".</p> <p>For examples of using <code>AttributeFilter</code>, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/filtering.html#search-filtering\">Using document attributes to filter search results</a>.</p>"}, "AttributeFilterList": {"type": "list", "member": {"shape": "Attribute<PERSON>ilter"}}, "AttributeSuggestionsDescribeConfig": {"type": "structure", "members": {"SuggestableConfigList": {"shape": "SuggestableConfigList", "documentation": "<p>The list of fields/attributes that you want to set as suggestible for query suggestions.</p>"}, "AttributeSuggestionsMode": {"shape": "AttributeSuggestionsMode", "documentation": "<p>The mode is set to either <code>ACTIVE</code> or <code>INACTIVE</code>. If the <code>Mode</code> for query history is set to <code>ENABLED</code> when calling <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_UpdateQuerySuggestionsConfig.html\">UpdateQuerySuggestionsConfig</a> and <code>AttributeSuggestionsMode</code> to use fields/attributes is set to <code>ACTIVE</code>, and you haven't set your <code>SuggestionTypes</code> preference to <code>DOCUMENT_ATTRIBUTES</code>, then Amazon Kendra uses the query history.</p>"}}, "documentation": "<p>Gets information on the configuration of document fields/attributes that you want to base query suggestions on. To change your configuration, use <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_AttributeSuggestionsUpdateConfig.html\">AttributeSuggestionsUpdateConfig</a> and then call <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_UpdateQuerySuggestionsConfig.html\">UpdateQuerySuggestionsConfig</a>.</p>"}, "AttributeSuggestionsGetConfig": {"type": "structure", "members": {"SuggestionAttributes": {"shape": "DocumentAttributeKeyList", "documentation": "<p>The list of document field/attribute keys or field names to use for query suggestions. If the content within any of the fields match what your user starts typing as their query, then the field content is returned as a query suggestion.</p>"}, "AdditionalResponseAttributes": {"shape": "DocumentAttributeKeyList", "documentation": "<p>The list of additional document field/attribute keys or field names to include in the response. You can use additional fields to provide extra information in the response. Additional fields are not used to based suggestions on.</p>"}, "AttributeFilter": {"shape": "Attribute<PERSON>ilter", "documentation": "<p>Filters the search results based on document fields/attributes.</p>"}, "UserContext": {"shape": "UserContext", "documentation": "<p>Applies user context filtering so that only users who are given access to certain documents see these document in their search results.</p>"}}, "documentation": "<p>Provides the configuration information for the document fields/attributes that you want to base query suggestions on.</p>"}, "AttributeSuggestionsMode": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}, "AttributeSuggestionsUpdateConfig": {"type": "structure", "members": {"SuggestableConfigList": {"shape": "SuggestableConfigList", "documentation": "<p>The list of fields/attributes that you want to set as suggestible for query suggestions.</p>"}, "AttributeSuggestionsMode": {"shape": "AttributeSuggestionsMode", "documentation": "<p>You can set the mode to <code>ACTIVE</code> or <code>INACTIVE</code>. You must also set <code>SuggestionTypes</code> as either <code>QUERY</code> or <code>DOCUMENT_ATTRIBUTES</code> and then call <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_GetQuerySuggestions.html\">GetQuerySuggestions</a>. If <code>Mode</code> to use query history is set to <code>ENABLED</code> when calling <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_UpdateQuerySuggestionsConfig.html\">UpdateQuerySuggestionsConfig</a> and <code>AttributeSuggestionsMode</code> to use fields/attributes is set to <code>ACTIVE</code>, and you haven't set your <code>SuggestionTypes</code> preference to <code>DOCUMENT_ATTRIBUTES</code>, then <PERSON> Kendra uses the query history.</p>"}}, "documentation": "<p>Updates the configuration information for the document fields/attributes that you want to base query suggestions on.</p> <p>To deactivate using documents fields for query suggestions, set the mode to <code>INACTIVE</code>. You must also set <code>SuggestionTypes</code> as either <code>QUERY</code> or <code>DOCUMENT_ATTRIBUTES</code> and then call <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_GetQuerySuggestions.html\">GetQuerySuggestions</a>. If you set to <code>QUERY</code>, then Amazon Kendra uses the query history to base suggestions on. If you set to <code>DOCUMENT_ATTRIBUTES</code>, then Amazon Kendra uses the contents of document fields to base suggestions on.</p>"}, "AuthenticationConfiguration": {"type": "structure", "members": {"BasicAuthentication": {"shape": "BasicAuthenticationConfigurationList", "documentation": "<p>The list of configuration information that's required to connect to and crawl a website host using basic authentication credentials.</p> <p>The list includes the name and port number of the website host.</p>"}}, "documentation": "<p>Provides the configuration information to connect to websites that require user authentication.</p>"}, "BasicAuthenticationConfiguration": {"type": "structure", "required": ["Host", "Port", "Credentials"], "members": {"Host": {"shape": "Host", "documentation": "<p>The name of the website host you want to connect to using authentication credentials.</p> <p>For example, the host name of https://a.example.com/page1.html is \"a.example.com\".</p>"}, "Port": {"shape": "Port", "documentation": "<p>The port number of the website host you want to connect to using authentication credentials.</p> <p>For example, the port for https://a.example.com/page1.html is 443, the standard port for HTTPS.</p>"}, "Credentials": {"shape": "SecretArn", "documentation": "<p>Your secret ARN, which you can create in <a href=\"https://docs.aws.amazon.com/secretsmanager/latest/userguide/intro.html\">Secrets Manager</a> </p> <p>You use a secret if basic authentication credentials are required to connect to a website. The secret stores your credentials of user name and password.</p>"}}, "documentation": "<p>Provides the configuration information to connect to websites that require basic user authentication.</p>"}, "BasicAuthenticationConfigurationList": {"type": "list", "member": {"shape": "BasicAuthenticationConfiguration"}, "max": 10, "min": 0}, "BatchDeleteDocumentRequest": {"type": "structure", "required": ["IndexId", "DocumentIdList"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index that contains the documents to delete.</p>"}, "DocumentIdList": {"shape": "DocumentIdList", "documentation": "<p>One or more identifiers for documents to delete from the index.</p>"}, "DataSourceSyncJobMetricTarget": {"shape": "DataSourceSyncJobMetricTarget"}}}, "BatchDeleteDocumentResponse": {"type": "structure", "members": {"FailedDocuments": {"shape": "BatchDeleteDocumentResponseFailedDocuments", "documentation": "<p>A list of documents that could not be removed from the index. Each entry contains an error message that indicates why the document couldn't be removed from the index.</p>"}}}, "BatchDeleteDocumentResponseFailedDocument": {"type": "structure", "members": {"Id": {"shape": "DocumentId", "documentation": "<p>The identifier of the document that couldn't be removed from the index.</p>"}, "ErrorCode": {"shape": "ErrorCode", "documentation": "<p>The error code for why the document couldn't be removed from the index.</p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>An explanation for why the document couldn't be removed from the index.</p>"}}, "documentation": "<p>Provides information about documents that could not be removed from an index by the <code>BatchDeleteDocument</code> API.</p>"}, "BatchDeleteDocumentResponseFailedDocuments": {"type": "list", "member": {"shape": "BatchDeleteDocumentResponseFailedDocument"}}, "BatchDeleteFeaturedResultsSetError": {"type": "structure", "required": ["Id", "ErrorCode", "ErrorMessage"], "members": {"Id": {"shape": "FeaturedResultsSetId", "documentation": "<p>The identifier of the set of featured results that couldn't be removed from the index.</p>"}, "ErrorCode": {"shape": "ErrorCode", "documentation": "<p>The error code for why the set of featured results couldn't be removed from the index.</p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>An explanation for why the set of featured results couldn't be removed from the index.</p>"}}, "documentation": "<p>Provides information about a set of featured results that couldn't be removed from an index by the <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_BatchDeleteFeaturedResultsSet.html\">BatchDeleteFeaturedResultsSet</a> API.</p>"}, "BatchDeleteFeaturedResultsSetErrors": {"type": "list", "member": {"shape": "BatchDeleteFeaturedResultsSetError"}}, "BatchDeleteFeaturedResultsSetRequest": {"type": "structure", "required": ["IndexId", "FeaturedResultsSetIds"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index used for featuring results.</p>"}, "FeaturedResultsSetIds": {"shape": "FeaturedResultsSetIdList", "documentation": "<p>The identifiers of the featured results sets that you want to delete.</p>"}}}, "BatchDeleteFeaturedResultsSetResponse": {"type": "structure", "required": ["Errors"], "members": {"Errors": {"shape": "BatchDeleteFeaturedResultsSetErrors", "documentation": "<p>The list of errors for the featured results set IDs, explaining why they couldn't be removed from the index.</p>"}}}, "BatchGetDocumentStatusRequest": {"type": "structure", "required": ["IndexId", "DocumentInfoList"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index to add documents to. The index ID is returned by the <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_CreateIndex.html\">CreateIndex </a> API.</p>"}, "DocumentInfoList": {"shape": "DocumentInfoList", "documentation": "<p>A list of <code>DocumentInfo</code> objects that identify the documents for which to get the status. You identify the documents by their document ID and optional attributes.</p>"}}}, "BatchGetDocumentStatusResponse": {"type": "structure", "members": {"Errors": {"shape": "BatchGetDocumentStatusResponseErrors", "documentation": "<p>A list of documents that Amazon Kendra couldn't get the status for. The list includes the ID of the document and the reason that the status couldn't be found.</p>"}, "DocumentStatusList": {"shape": "DocumentStatusList", "documentation": "<p>The status of documents. The status indicates if the document is waiting to be indexed, is in the process of indexing, has completed indexing, or failed indexing. If a document failed indexing, the status provides the reason why.</p>"}}}, "BatchGetDocumentStatusResponseError": {"type": "structure", "members": {"DocumentId": {"shape": "DocumentId", "documentation": "<p>The identifier of the document whose status could not be retrieved.</p>"}, "ErrorCode": {"shape": "ErrorCode", "documentation": "<p>Indicates the source of the error.</p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>States that the API could not get the status of a document. This could be because the request is not valid or there is a system error.</p>"}}, "documentation": "<p>Provides a response when the status of a document could not be retrieved.</p>"}, "BatchGetDocumentStatusResponseErrors": {"type": "list", "member": {"shape": "BatchGetDocumentStatusResponseError"}}, "BatchPutDocumentRequest": {"type": "structure", "required": ["IndexId", "Documents"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index to add the documents to. You need to create the index first using the <code>CreateIndex</code> API.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of an IAM role with permission to access your S3 bucket. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/iam-roles.html\">IAM access roles for Amazon Kendra</a>.</p>"}, "Documents": {"shape": "DocumentList", "documentation": "<p>One or more documents to add to the index.</p> <p>Documents have the following file size limits.</p> <ul> <li> <p>50 MB total size for any file</p> </li> <li> <p>5 MB extracted text for any file</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/quotas.html\">Quotas</a>.</p>"}, "CustomDocumentEnrichmentConfiguration": {"shape": "CustomDocumentEnrichmentConfiguration", "documentation": "<p>Configuration information for altering your document metadata and content during the document ingestion process when you use the <code>BatchPutDocument</code> API.</p> <p>For more information on how to create, modify and delete document metadata, or make other content alterations when you ingest documents into Amazon Kendra, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/custom-document-enrichment.html\">Customizing document metadata during the ingestion process</a>.</p>"}}}, "BatchPutDocumentResponse": {"type": "structure", "members": {"FailedDocuments": {"shape": "BatchPutDocumentResponseFailedDocuments", "documentation": "<p>A list of documents that were not added to the index because the document failed a validation check. Each document contains an error message that indicates why the document couldn't be added to the index.</p> <p>If there was an error adding a document to an index the error is reported in your Amazon Web Services CloudWatch log. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/cloudwatch-logs.html\">Monitoring Amazon Kendra with Amazon CloudWatch logs</a>.</p>"}}}, "BatchPutDocumentResponseFailedDocument": {"type": "structure", "members": {"Id": {"shape": "DocumentId", "documentation": "<p>The identifier of the document.</p>"}, "ErrorCode": {"shape": "ErrorCode", "documentation": "<p>The type of error that caused the document to fail to be indexed.</p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>A description of the reason why the document could not be indexed.</p>"}}, "documentation": "<p>Provides information about a document that could not be indexed.</p>"}, "BatchPutDocumentResponseFailedDocuments": {"type": "list", "member": {"shape": "BatchPutDocumentResponseFailedDocument"}}, "Blob": {"type": "blob"}, "Boolean": {"type": "boolean"}, "BoxConfiguration": {"type": "structure", "required": ["EnterpriseId", "SecretArn"], "members": {"EnterpriseId": {"shape": "EnterpriseId", "documentation": "<p>The identifier of the Box Enterprise platform. You can find the enterprise ID in the Box Developer Console settings or when you create an app in Box and download your authentication credentials. For example, <i>801234567</i>.</p>"}, "SecretArn": {"shape": "SecretArn", "documentation": "<p>The Amazon Resource Name (ARN) of an Secrets Manager secret that contains the key-value pairs required to connect to your Box platform. The secret must contain a JSON structure with the following keys:</p> <ul> <li> <p>clientID—The identifier of the client OAuth 2.0 authentication application created in Box.</p> </li> <li> <p>clientSecret—A set of characters known only to the OAuth 2.0 authentication application created in Box.</p> </li> <li> <p>publicKeyId—The identifier of the public key contained within an identity certificate.</p> </li> <li> <p>privateKey—A set of characters that make up an encryption key.</p> </li> <li> <p>passphrase—A set of characters that act like a password.</p> </li> </ul> <p>You create an application in Box to generate the keys or credentials required for the secret. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/data-source-box.html\">Using a Box data source</a>.</p>"}, "UseChangeLog": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to use the Slack change log to determine which documents require updating in the index. Depending on the data source change log's size, it may take longer for Amazon Kendra to use the change log than to scan all of your documents.</p>"}, "CrawlComments": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to index comments.</p>"}, "CrawlTasks": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to index the contents of tasks.</p>"}, "CrawlWebLinks": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to index web links.</p>"}, "FileFieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map attributes or field names of Box files to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Box fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The Box field names must exist in your Box custom metadata.</p>"}, "TaskFieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map attributes or field names of Box tasks to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Box fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The Box field names must exist in your Box custom metadata.</p>"}, "CommentFieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map attributes or field names of Box comments to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Box fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The Box field names must exist in your Box custom metadata.</p>"}, "WebLinkFieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map attributes or field names of Box web links to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Box fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The Box field names must exist in your Box custom metadata.</p>"}, "InclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to include certain files and folders in your Box platform. Files and folders that match the patterns are included in the index. Files and folders that don't match the patterns are excluded from the index. If a file or folder matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the file or folder isn't included in the index.</p>"}, "ExclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to exclude certain files and folders from your Box platform. Files and folders that match the patterns are excluded from the index.Files and folders that don't match the patterns are included in the index. If a file or folder matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the file or folder isn't included in the index.</p>"}, "VpcConfiguration": {"shape": "DataSourceVpcConfiguration", "documentation": "<p>Configuration information for an Amazon VPC to connect to your Box. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/vpc-configuration.html\">Configuring a VPC</a>.</p>"}}, "documentation": "<p>Provides the configuration information to connect to Box as your data source.</p>"}, "CapacityUnitsConfiguration": {"type": "structure", "required": ["StorageCapacityUnits", "QueryCapacityUnits"], "members": {"StorageCapacityUnits": {"shape": "StorageCapacityUnit", "documentation": "<p>The amount of extra storage capacity for an index. A single capacity unit provides 30 GB of storage space or 100,000 documents, whichever is reached first. You can add up to 100 extra capacity units.</p>"}, "QueryCapacityUnits": {"shape": "QueryCapacityUnit", "documentation": "<p>The amount of extra query capacity for an index and <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_GetQuerySuggestions.html\">GetQuerySuggestions</a> capacity.</p> <p>A single extra capacity unit for an index provides 0.1 queries per second or approximately 8,000 queries per day. You can add up to 100 extra capacity units.</p> <p> <code>GetQuerySuggestions</code> capacity is five times the provisioned query capacity for an index, or the base capacity of 2.5 calls per second, whichever is higher. For example, the base capacity for an index is 0.1 queries per second, and <code>GetQuerySuggestions</code> capacity has a base of 2.5 calls per second. If you add another 0.1 queries per second to total 0.2 queries per second for an index, the <code>GetQuerySuggestions</code> capacity is 2.5 calls per second (higher than five times 0.2 queries per second).</p>"}}, "documentation": "<p>Specifies additional capacity units configured for your Enterprise Edition index. You can add and remove capacity units to fit your usage requirements.</p>"}, "ChangeDetectingColumns": {"type": "list", "member": {"shape": "ColumnName"}, "max": 5, "min": 1}, "ClaimRegex": {"type": "string", "max": 100, "min": 1, "pattern": "^\\P{C}*$"}, "ClearQuerySuggestionsRequest": {"type": "structure", "required": ["IndexId"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index you want to clear query suggestions from.</p>"}}}, "ClickFeedback": {"type": "structure", "required": ["ResultId", "ClickTime"], "members": {"ResultId": {"shape": "ResultId", "documentation": "<p>The identifier of the search result that was clicked.</p>"}, "ClickTime": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the result was clicked.</p>"}}, "documentation": "<p>Gathers information about when a particular result was clicked by a user. Your application uses the <code>SubmitFeedback</code> API to provide click information.</p>"}, "ClickFeedbackList": {"type": "list", "member": {"shape": "ClickFeedback"}}, "ClientTokenName": {"type": "string", "max": 100, "min": 1}, "CollapseConfiguration": {"type": "structure", "required": ["DocumentAttributeKey"], "members": {"DocumentAttributeKey": {"shape": "DocumentAttributeKey", "documentation": "<p>The document attribute used to group search results. You can use any attribute that has the <code>Sortable</code> flag set to true. You can also sort by any of the following built-in attributes:\"_category\",\"_created_at\", \"_last_updated_at\", \"_version\", \"_view_count\".</p>"}, "SortingConfigurations": {"shape": "SortingConfigurationList", "documentation": "<p>A prioritized list of document attributes/fields that determine the primary document among those in a collapsed group.</p>"}, "MissingAttributeKeyStrategy": {"shape": "MissingAttributeKeyStrategy", "documentation": "<p>Specifies the behavior for documents without a value for the collapse attribute.</p> <p>Amazon Kendra offers three customization options:</p> <ul> <li> <p>Choose to <code>COLLAPSE</code> all documents with null or missing values in one group. This is the default configuration.</p> </li> <li> <p>Choose to <code>IGNORE</code> documents with null or missing values. Ignored documents will not appear in query results.</p> </li> <li> <p>Choose to <code>EXPAND</code> each document with a null or missing value into a group of its own.</p> </li> </ul>"}, "Expand": {"shape": "Boolean", "documentation": "<p>Specifies whether to expand the collapsed results.</p>"}, "ExpandConfiguration": {"shape": "ExpandConfiguration", "documentation": "<p>Provides configuration information to customize expansion options for a collapsed group.</p>"}}, "documentation": "<p>Specifies how to group results by document attribute value, and how to display them collapsed/expanded under a designated primary document for each group.</p>"}, "CollapsedResultDetail": {"type": "structure", "required": ["DocumentAttribute"], "members": {"DocumentAttribute": {"shape": "DocumentAttribute", "documentation": "<p>The value of the document attribute that results are collapsed on.</p>"}, "ExpandedResults": {"shape": "ExpandedResultList", "documentation": "<p>A list of results in the collapsed group.</p>"}}, "documentation": "<p>Provides details about a collapsed group of search results.</p>"}, "ColumnConfiguration": {"type": "structure", "required": ["DocumentIdColumnName", "DocumentDataColumnName", "ChangeDetectingColumns"], "members": {"DocumentIdColumnName": {"shape": "ColumnName", "documentation": "<p>The column that provides the document's identifier.</p>"}, "DocumentDataColumnName": {"shape": "ColumnName", "documentation": "<p>The column that contains the contents of the document.</p>"}, "DocumentTitleColumnName": {"shape": "ColumnName", "documentation": "<p>The column that contains the title of the document.</p>"}, "FieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>An array of objects that map database column names to the corresponding fields in an index. You must first create the fields in the index using the <code>UpdateIndex</code> API.</p>"}, "ChangeDetectingColumns": {"shape": "ChangeDetectingColumns", "documentation": "<p>One to five columns that indicate when a document in the database has changed.</p>"}}, "documentation": "<p>Provides information about how Amazon Kendra should use the columns of a database in an index.</p>"}, "ColumnName": {"type": "string", "max": 100, "min": 1, "pattern": "^[a-zA-Z][a-zA-Z0-9_]*$"}, "ConditionOperator": {"type": "string", "enum": ["GreaterThan", "GreaterThanOrEquals", "<PERSON><PERSON><PERSON>", "LessThanOrEquals", "Equals", "NotEquals", "Contains", "NotContains", "Exists", "NotExists", "BeginsWith"]}, "ConflictException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>A conflict occurred with the request. Please fix any inconsistences with your resources and try again.</p>", "exception": true}, "ConflictingItem": {"type": "structure", "members": {"QueryText": {"shape": "QueryText", "documentation": "<p>The text of the conflicting query.</p>"}, "SetName": {"shape": "String", "documentation": "<p>The name for the set of featured results that the conflicting query belongs to.</p>"}, "SetId": {"shape": "String", "documentation": "<p>The identifier of the set of featured results that the conflicting query belongs to.</p>"}}, "documentation": "<p>Information about a conflicting query used across different sets of featured results. When you create a featured results set, you must check that the queries are unique per featured results set for each index.</p>"}, "ConflictingItems": {"type": "list", "member": {"shape": "ConflictingItem"}}, "ConfluenceAttachmentConfiguration": {"type": "structure", "members": {"CrawlAttachments": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to index attachments of pages and blogs in Confluence.</p>"}, "AttachmentFieldMappings": {"shape": "ConfluenceAttachmentFieldMappingsList", "documentation": "<p>Maps attributes or field names of Confluence attachments to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Confluence fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The Confluence data source field names must exist in your Confluence custom metadata.</p> <p>If you specify the <code>AttachentFieldMappings</code> parameter, you must specify at least one field mapping.</p>"}}, "documentation": "<p>Configuration of attachment settings for the Confluence data source. Attachment settings are optional, if you don't specify settings attachments, Amazon Kendra won't index them.</p>"}, "ConfluenceAttachmentFieldMappingsList": {"type": "list", "member": {"shape": "ConfluenceAttachmentToIndexFieldMapping"}, "max": 11, "min": 1}, "ConfluenceAttachmentFieldName": {"type": "string", "enum": ["AUTHOR", "CONTENT_TYPE", "CREATED_DATE", "DISPLAY_URL", "FILE_SIZE", "ITEM_TYPE", "PARENT_ID", "SPACE_KEY", "SPACE_NAME", "URL", "VERSION"]}, "ConfluenceAttachmentToIndexFieldMapping": {"type": "structure", "members": {"DataSourceFieldName": {"shape": "ConfluenceAttachmentFieldName", "documentation": "<p>The name of the field in the data source. </p> <p>You must first create the index field using the <code>UpdateIndex</code> API. </p>"}, "DateFieldFormat": {"shape": "DataSourceDateFieldFormat", "documentation": "<p>The format for date fields in the data source. If the field specified in <code>DataSourceFieldName</code> is a date field you must specify the date format. If the field is not a date field, an exception is thrown.</p>"}, "IndexFieldName": {"shape": "IndexFieldName", "documentation": "<p>The name of the index field to map to the Confluence data source field. The index field type must match the Confluence field type.</p>"}}, "documentation": "<p>Maps attributes or field names of Confluence attachments to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Confluence fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The Confuence data source field names must exist in your Confluence custom metadata.</p>"}, "ConfluenceAuthenticationType": {"type": "string", "enum": ["HTTP_BASIC", "PAT"]}, "ConfluenceBlogConfiguration": {"type": "structure", "members": {"BlogFieldMappings": {"shape": "ConfluenceBlogFieldMappingsList", "documentation": "<p>Maps attributes or field names of Confluence blogs to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Confluence fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The Confluence data source field names must exist in your Confluence custom metadata.</p> <p>If you specify the <code>BlogFieldMappings</code> parameter, you must specify at least one field mapping.</p>"}}, "documentation": "<p>Configuration of blog settings for the Confluence data source. Blogs are always indexed unless filtered from the index by the <code>ExclusionPatterns</code> or <code>InclusionPatterns</code> fields in the <code>ConfluenceConfiguration</code> object.</p>"}, "ConfluenceBlogFieldMappingsList": {"type": "list", "member": {"shape": "ConfluenceBlogToIndexFieldMapping"}, "max": 9, "min": 1}, "ConfluenceBlogFieldName": {"type": "string", "enum": ["AUTHOR", "DISPLAY_URL", "ITEM_TYPE", "LABELS", "PUBLISH_DATE", "SPACE_KEY", "SPACE_NAME", "URL", "VERSION"]}, "ConfluenceBlogToIndexFieldMapping": {"type": "structure", "members": {"DataSourceFieldName": {"shape": "ConfluenceBlogFieldName", "documentation": "<p>The name of the field in the data source. </p>"}, "DateFieldFormat": {"shape": "DataSourceDateFieldFormat", "documentation": "<p>The format for date fields in the data source. If the field specified in <code>DataSourceFieldName</code> is a date field you must specify the date format. If the field is not a date field, an exception is thrown.</p>"}, "IndexFieldName": {"shape": "IndexFieldName", "documentation": "<p>The name of the index field to map to the Confluence data source field. The index field type must match the Confluence field type.</p>"}}, "documentation": "<p>Maps attributes or field names of Confluence blog to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Confluence fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The Confluence data source field names must exist in your Confluence custom metadata.</p>"}, "ConfluenceConfiguration": {"type": "structure", "required": ["ServerUrl", "SecretArn", "Version"], "members": {"ServerUrl": {"shape": "Url", "documentation": "<p>The URL of your Confluence instance. Use the full URL of the server. For example, <i>https://server.example.com:port/</i>. You can also use an IP address, for example, <i>https://*************/</i>.</p>"}, "SecretArn": {"shape": "SecretArn", "documentation": "<p>The Amazon Resource Name (ARN) of an Secrets Manager secret that contains the user name and password required to connect to the Confluence instance. If you use Confluence Cloud, you use a generated API token as the password.</p> <p>You can also provide authentication credentials in the form of a personal access token. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/data-source-confluence.html\">Using a Confluence data source</a>.</p>"}, "Version": {"shape": "ConfluenceVersion", "documentation": "<p>The version or the type of Confluence installation to connect to.</p>"}, "SpaceConfiguration": {"shape": "ConfluenceSpaceConfiguration", "documentation": "<p>Configuration information for indexing Confluence spaces.</p>"}, "PageConfiguration": {"shape": "ConfluencePageConfiguration", "documentation": "<p>Configuration information for indexing Confluence pages.</p>"}, "BlogConfiguration": {"shape": "ConfluenceBlogConfiguration", "documentation": "<p>Configuration information for indexing Confluence blogs.</p>"}, "AttachmentConfiguration": {"shape": "ConfluenceAttachmentConfiguration", "documentation": "<p>Configuration information for indexing attachments to Confluence blogs and pages.</p>"}, "VpcConfiguration": {"shape": "DataSourceVpcConfiguration", "documentation": "<p>Configuration information for an Amazon Virtual Private Cloud to connect to your Confluence. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/vpc-configuration.html\">Configuring a VPC</a>.</p>"}, "InclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to include certain blog posts, pages, spaces, or attachments in your Confluence. Content that matches the patterns are included in the index. Content that doesn't match the patterns is excluded from the index. If content matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the content isn't included in the index.</p>"}, "ExclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to exclude certain blog posts, pages, spaces, or attachments in your Confluence. Content that matches the patterns are excluded from the index. Content that doesn't match the patterns is included in the index. If content matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the content isn't included in the index.</p>"}, "ProxyConfiguration": {"shape": "ProxyConfiguration", "documentation": "<p>Configuration information to connect to your Confluence URL instance via a web proxy. You can use this option for Confluence Server.</p> <p>You must provide the website host name and port number. For example, the host name of <i>https://a.example.com/page1.html</i> is \"a.example.com\" and the port is 443, the standard port for HTTPS.</p> <p>Web proxy credentials are optional and you can use them to connect to a web proxy server that requires basic authentication of user name and password. To store web proxy credentials, you use a secret in Secrets Manager.</p> <p>It is recommended that you follow best security practices when configuring your web proxy. This includes setting up throttling, setting up logging and monitoring, and applying security patches on a regular basis. If you use your web proxy with multiple data sources, sync jobs that occur at the same time could strain the load on your proxy. It is recommended you prepare your proxy beforehand for any security and load requirements.</p>"}, "AuthenticationType": {"shape": "ConfluenceAuthenticationType", "documentation": "<p>Whether you want to connect to Confluence using basic authentication of user name and password, or a personal access token. You can use a personal access token for Confluence Server.</p>"}}, "documentation": "<p>Provides the configuration information to connect to Confluence as your data source.</p>"}, "ConfluencePageConfiguration": {"type": "structure", "members": {"PageFieldMappings": {"shape": "ConfluencePageFieldMappingsList", "documentation": "<p>Maps attributes or field names of Confluence pages to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Confluence fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The Confluence data source field names must exist in your Confluence custom metadata.</p> <p>If you specify the <code>PageFieldMappings</code> parameter, you must specify at least one field mapping.</p>"}}, "documentation": "<p>Configuration of the page settings for the Confluence data source.</p>"}, "ConfluencePageFieldMappingsList": {"type": "list", "member": {"shape": "ConfluencePageToIndexFieldMapping"}, "max": 12, "min": 1}, "ConfluencePageFieldName": {"type": "string", "enum": ["AUTHOR", "CONTENT_STATUS", "CREATED_DATE", "DISPLAY_URL", "ITEM_TYPE", "LABELS", "MODIFIED_DATE", "PARENT_ID", "SPACE_KEY", "SPACE_NAME", "URL", "VERSION"]}, "ConfluencePageToIndexFieldMapping": {"type": "structure", "members": {"DataSourceFieldName": {"shape": "ConfluencePageFieldName", "documentation": "<p>The name of the field in the data source.</p>"}, "DateFieldFormat": {"shape": "DataSourceDateFieldFormat", "documentation": "<p>The format for date fields in the data source. If the field specified in <code>DataSourceFieldName</code> is a date field you must specify the date format. If the field is not a date field, an exception is thrown.</p>"}, "IndexFieldName": {"shape": "IndexFieldName", "documentation": "<p>The name of the index field to map to the Confluence data source field. The index field type must match the Confluence field type.</p>"}}, "documentation": "<p>Maps attributes or field names of Confluence pages to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Confluence fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The Confluence data source field names must exist in your Confluence custom metadata.</p>"}, "ConfluenceSpaceConfiguration": {"type": "structure", "members": {"CrawlPersonalSpaces": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to index personal spaces. You can add restrictions to items in personal spaces. If personal spaces are indexed, queries without user context information may return restricted items from a personal space in their results. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/user-context-filter.html\">Filtering on user context</a>.</p>"}, "CrawlArchivedSpaces": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to index archived spaces.</p>"}, "IncludeSpaces": {"shape": "ConfluenceSpaceList", "documentation": "<p>A list of space keys for Confluence spaces. If you include a key, the blogs, documents, and attachments in the space are indexed. Spaces that aren't in the list aren't indexed. A space in the list must exist. Otherwise, Amazon Kendra logs an error when the data source is synchronized. If a space is in both the <code>IncludeSpaces</code> and the <code>ExcludeSpaces</code> list, the space is excluded.</p>"}, "ExcludeSpaces": {"shape": "ConfluenceSpaceList", "documentation": "<p>A list of space keys of Confluence spaces. If you include a key, the blogs, documents, and attachments in the space are not indexed. If a space is in both the <code>ExcludeSpaces</code> and the <code>IncludeSpaces</code> list, the space is excluded.</p>"}, "SpaceFieldMappings": {"shape": "ConfluenceSpaceFieldMappingsList", "documentation": "<p>Maps attributes or field names of Confluence spaces to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Confluence fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The Confluence data source field names must exist in your Confluence custom metadata.</p> <p>If you specify the <code>SpaceFieldMappings</code> parameter, you must specify at least one field mapping.</p>"}}, "documentation": "<p>Configuration information for indexing Confluence spaces.</p>"}, "ConfluenceSpaceFieldMappingsList": {"type": "list", "member": {"shape": "ConfluenceSpaceToIndexFieldMapping"}, "max": 4, "min": 1}, "ConfluenceSpaceFieldName": {"type": "string", "enum": ["DISPLAY_URL", "ITEM_TYPE", "SPACE_KEY", "URL"]}, "ConfluenceSpaceIdentifier": {"type": "string", "max": 255, "min": 1, "pattern": "^\\P{C}*$"}, "ConfluenceSpaceList": {"type": "list", "member": {"shape": "ConfluenceSpaceIdentifier"}, "min": 1}, "ConfluenceSpaceToIndexFieldMapping": {"type": "structure", "members": {"DataSourceFieldName": {"shape": "ConfluenceSpaceFieldName", "documentation": "<p>The name of the field in the data source. </p>"}, "DateFieldFormat": {"shape": "DataSourceDateFieldFormat", "documentation": "<p>The format for date fields in the data source. If the field specified in <code>DataSourceFieldName</code> is a date field you must specify the date format. If the field is not a date field, an exception is thrown.</p>"}, "IndexFieldName": {"shape": "IndexFieldName", "documentation": "<p>The name of the index field to map to the Confluence data source field. The index field type must match the Confluence field type.</p>"}}, "documentation": "<p>Maps attributes or field names of Confluence spaces to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Confluence fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The Confluence data source field names must exist in your Confluence custom metadata.</p>"}, "ConfluenceVersion": {"type": "string", "enum": ["CLOUD", "SERVER"]}, "ConnectionConfiguration": {"type": "structure", "required": ["DatabaseHost", "DatabasePort", "DatabaseName", "TableName", "SecretArn"], "members": {"DatabaseHost": {"shape": "DatabaseHost", "documentation": "<p>The name of the host for the database. Can be either a string (host.subdomain.domain.tld) or an IPv4 or IPv6 address.</p>"}, "DatabasePort": {"shape": "DatabasePort", "documentation": "<p>The port that the database uses for connections.</p>"}, "DatabaseName": {"shape": "DatabaseName", "documentation": "<p>The name of the database containing the document data.</p>"}, "TableName": {"shape": "TableName", "documentation": "<p>The name of the table that contains the document data.</p>"}, "SecretArn": {"shape": "SecretArn", "documentation": "<p>The Amazon Resource Name (ARN) of credentials stored in Secrets Manager. The credentials should be a user/password pair. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/data-source-database.html\">Using a Database Data Source</a>. For more information about Secrets Manager, see <a href=\"https://docs.aws.amazon.com/secretsmanager/latest/userguide/intro.html\"> What Is Secrets Manager</a> in the <i> Secrets Manager </i> user guide.</p>"}}, "documentation": "<p>Provides the configuration information that's required to connect to a database.</p>"}, "Content": {"type": "string"}, "ContentSourceConfiguration": {"type": "structure", "members": {"DataSourceIds": {"shape": "DataSourceIdList", "documentation": "<p>The identifier of the data sources you want to use for your Amazon Kendra experience.</p>"}, "FaqIds": {"shape": "FaqIdsList", "documentation": "<p>The identifier of the FAQs that you want to use for your Amazon Kendra experience.</p>"}, "DirectPutContent": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to use documents you indexed directly using the <code>BatchPutDocument</code> API.</p>"}}, "documentation": "<p>Provides the configuration information for your content sources, such as data sources, FAQs, and content indexed directly via <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_BatchPutDocument.html\">BatchPutDocument</a>.</p>"}, "ContentType": {"type": "string", "enum": ["PDF", "HTML", "MS_WORD", "PLAIN_TEXT", "PPT", "RTF", "XML", "XSLT", "MS_EXCEL", "CSV", "JSON", "MD"]}, "Correction": {"type": "structure", "members": {"BeginOffset": {"shape": "Integer", "documentation": "<p>The zero-based location in the response string or text where the corrected word starts.</p>"}, "EndOffset": {"shape": "Integer", "documentation": "<p>The zero-based location in the response string or text where the corrected word ends.</p>"}, "Term": {"shape": "String", "documentation": "<p>The string or text of a misspelled word in a query.</p>"}, "CorrectedTerm": {"shape": "String", "documentation": "<p>The string or text of a corrected misspelled word in a query.</p>"}}, "documentation": "<p>A corrected misspelled word in a query.</p>"}, "CorrectionList": {"type": "list", "member": {"shape": "Correction"}}, "CrawlDepth": {"type": "integer", "max": 10, "min": 0}, "CreateAccessControlConfigurationRequest": {"type": "structure", "required": ["IndexId", "Name"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index to create an access control configuration for your documents.</p>"}, "Name": {"shape": "AccessControlConfigurationName", "documentation": "<p>A name for the access control configuration.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description for the access control configuration.</p>"}, "AccessControlList": {"shape": "PrincipalList", "documentation": "<p>Information on principals (users and/or groups) and which documents they should have access to. This is useful for user context filtering, where search results are filtered based on the user or their group access to documents.</p>"}, "HierarchicalAccessControlList": {"shape": "HierarchicalPrincipalList", "documentation": "<p>The list of <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_Principal.html\">principal</a> lists that define the hierarchy for which documents users should have access to.</p>"}, "ClientToken": {"shape": "ClientTokenName", "documentation": "<p>A token that you provide to identify the request to create an access control configuration. Multiple calls to the <code>CreateAccessControlConfiguration</code> API with the same client token will create only one access control configuration.</p>", "idempotencyToken": true}}}, "CreateAccessControlConfigurationResponse": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "AccessControlConfigurationId", "documentation": "<p>The identifier of the access control configuration for your documents in an index.</p>"}}}, "CreateDataSourceRequest": {"type": "structure", "required": ["Name", "IndexId", "Type"], "members": {"Name": {"shape": "DataSourceName", "documentation": "<p>A name for the data source connector.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index you want to use with the data source connector.</p>"}, "Type": {"shape": "DataSourceType", "documentation": "<p>The type of data source repository. For example, <code>SHAREPOINT</code>.</p>"}, "Configuration": {"shape": "DataSourceConfiguration", "documentation": "<p>Configuration information to connect to your data source repository.</p> <p>You can't specify the <code>Configuration</code> parameter when the <code>Type</code> parameter is set to <code>CUSTOM</code>. If you do, you receive a <code>ValidationException</code> exception.</p> <p>The <code>Configuration</code> parameter is required for all other data sources.</p>"}, "VpcConfiguration": {"shape": "DataSourceVpcConfiguration", "documentation": "<p>Configuration information for an Amazon Virtual Private Cloud to connect to your data source. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/vpc-configuration.html\">Configuring a VPC</a>.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description for the data source connector.</p>"}, "Schedule": {"shape": "ScanSchedule", "documentation": "<p>Sets the frequency for Amazon Kendra to check the documents in your data source repository and update the index. If you don't set a schedule Amazon Kendra will not periodically update the index. You can call the <code>StartDataSourceSyncJob</code> API to update the index.</p> <p>Specify a <code>cron-</code> format schedule string or an empty string to indicate that the index is updated on demand.</p> <p>You can't specify the <code>Schedule</code> parameter when the <code>Type</code> parameter is set to <code>CUSTOM</code>. If you do, you receive a <code>ValidationException</code> exception.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of an IAM role with permission to access the data source and required resources. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/iam-roles.html\">IAM access roles for Amazon Kendra.</a>.</p> <p>You can't specify the <code>RoleArn</code> parameter when the <code>Type</code> parameter is set to <code>CUSTOM</code>. If you do, you receive a <code>ValidationException</code> exception.</p> <p>The <code>RoleArn</code> parameter is required for all other data sources.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>A list of key-value pairs that identify or categorize the data source connector. You can also use tags to help control access to the data source connector. Tag keys and values can consist of Unicode letters, digits, white space, and any of the following symbols: _ . : / = + - @.</p>"}, "ClientToken": {"shape": "ClientTokenName", "documentation": "<p>A token that you provide to identify the request to create a data source connector. Multiple calls to the <code>CreateDataSource</code> API with the same client token will create only one data source connector.</p>", "idempotencyToken": true}, "LanguageCode": {"shape": "LanguageCode", "documentation": "<p>The code for a language. This allows you to support a language for all documents when creating the data source connector. English is supported by default. For more information on supported languages, including their codes, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/in-adding-languages.html\">Adding documents in languages other than English</a>.</p>"}, "CustomDocumentEnrichmentConfiguration": {"shape": "CustomDocumentEnrichmentConfiguration", "documentation": "<p>Configuration information for altering document metadata and content during the document ingestion process.</p> <p>For more information on how to create, modify and delete document metadata, or make other content alterations when you ingest documents into Amazon Kendra, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/custom-document-enrichment.html\">Customizing document metadata during the ingestion process</a>.</p>"}}}, "CreateDataSourceResponse": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source connector.</p>"}}}, "CreateExperienceRequest": {"type": "structure", "required": ["Name", "IndexId"], "members": {"Name": {"shape": "ExperienceName", "documentation": "<p>A name for your Amazon Kendra experience.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for your Amazon Kendra experience.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of an IAM role with permission to access <code>Query</code> API, <code>GetQuerySuggestions</code> API, and other required APIs. The role also must include permission to access IAM Identity Center that stores your user and group information. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/iam-roles.html\">IAM access roles for Amazon Kendra</a>.</p>"}, "Configuration": {"shape": "ExperienceConfiguration", "documentation": "<p>Configuration information for your Amazon Kendra experience. This includes <code>ContentSourceConfiguration</code>, which specifies the data source IDs and/or FAQ IDs, and <code>UserIdentityConfiguration</code>, which specifies the user or group information to grant access to your Amazon Kendra experience.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description for your Amazon Kendra experience.</p>"}, "ClientToken": {"shape": "ClientTokenName", "documentation": "<p>A token that you provide to identify the request to create your Amazon Kendra experience. Multiple calls to the <code>CreateExperience</code> API with the same client token creates only one Amazon Kendra experience.</p>", "idempotencyToken": true}}}, "CreateExperienceResponse": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "ExperienceId", "documentation": "<p>The identifier of your Amazon Kendra experience.</p>"}}}, "CreateFaqRequest": {"type": "structure", "required": ["IndexId", "Name", "S3Path", "RoleArn"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for the FAQ.</p>"}, "Name": {"shape": "FaqName", "documentation": "<p>A name for the FAQ.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description for the FAQ.</p>"}, "S3Path": {"shape": "S3Path", "documentation": "<p>The path to the FAQ file in S3.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of an IAM role with permission to access the S3 bucket that contains the FAQs. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/iam-roles.html\">IAM access roles for Amazon Kendra</a>.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>A list of key-value pairs that identify the FAQ. You can use the tags to identify and organize your resources and to control access to resources.</p>"}, "FileFormat": {"shape": "FaqFileFormat", "documentation": "<p>The format of the FAQ input file. You can choose between a basic CSV format, a CSV format that includes customs attributes in a header, and a JSON format that includes custom attributes.</p> <p>The default format is CSV.</p> <p>The format must match the format of the file stored in the S3 bucket identified in the <code>S3Path</code> parameter.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/in-creating-faq.html\">Adding questions and answers</a>.</p>"}, "ClientToken": {"shape": "ClientTokenName", "documentation": "<p>A token that you provide to identify the request to create a FAQ. Multiple calls to the <code>CreateFaqRequest</code> API with the same client token will create only one FAQ. </p>", "idempotencyToken": true}, "LanguageCode": {"shape": "LanguageCode", "documentation": "<p>The code for a language. This allows you to support a language for the FAQ document. English is supported by default. For more information on supported languages, including their codes, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/in-adding-languages.html\">Adding documents in languages other than English</a>.</p>"}}}, "CreateFaqResponse": {"type": "structure", "members": {"Id": {"shape": "FaqId", "documentation": "<p>The identifier of the FAQ.</p>"}}}, "CreateFeaturedResultsSetRequest": {"type": "structure", "required": ["IndexId", "FeaturedResultsSetName"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index that you want to use for featuring results.</p>"}, "FeaturedResultsSetName": {"shape": "FeaturedResultsSetName", "documentation": "<p>A name for the set of featured results.</p>"}, "Description": {"shape": "FeaturedResultsSetDescription", "documentation": "<p>A description for the set of featured results.</p>"}, "ClientToken": {"shape": "ClientTokenName", "documentation": "<p>A token that you provide to identify the request to create a set of featured results. Multiple calls to the <code>CreateFeaturedResultsSet</code> API with the same client token will create only one featured results set.</p>"}, "Status": {"shape": "FeaturedResultsSetStatus", "documentation": "<p>The current status of the set of featured results. When the value is <code>ACTIVE</code>, featured results are ready for use. You can still configure your settings before setting the status to <code>ACTIVE</code>. You can set the status to <code>ACTIVE</code> or <code>INACTIVE</code> using the <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_UpdateFeaturedResultsSet.html\">UpdateFeaturedResultsSet</a> API. The queries you specify for featured results must be unique per featured results set for each index, whether the status is <code>ACTIVE</code> or <code>INACTIVE</code>.</p>"}, "QueryTexts": {"shape": "QueryTextList", "documentation": "<p>A list of queries for featuring results. For more information on the list of queries, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_FeaturedResultsSet.html\">FeaturedResultsSet</a>.</p>"}, "FeaturedDocuments": {"shape": "FeaturedDocumentList", "documentation": "<p>A list of document IDs for the documents you want to feature at the top of the search results page. For more information on the list of documents, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_FeaturedResultsSet.html\">FeaturedResultsSet</a>.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>A list of key-value pairs that identify or categorize the featured results set. You can also use tags to help control access to the featured results set. Tag keys and values can consist of Unicode letters, digits, white space, and any of the following symbols:_ . : / = + - @.</p>"}}}, "CreateFeaturedResultsSetResponse": {"type": "structure", "members": {"FeaturedResultsSet": {"shape": "FeaturedResultsSet", "documentation": "<p>Information on the set of featured results. This includes the identifier of the featured results set, whether the featured results set is active or inactive, when the featured results set was created, and more.</p>"}}}, "CreateIndexRequest": {"type": "structure", "required": ["Name", "RoleArn"], "members": {"Name": {"shape": "IndexName", "documentation": "<p>A name for the index.</p>"}, "Edition": {"shape": "IndexEdition", "documentation": "<p>The Amazon Kendra edition to use for the index. Choose <code>DEVELOPER_EDITION</code> for indexes intended for development, testing, or proof of concept. Use <code>ENTERPRISE_EDITION</code> for production. Once you set the edition for an index, it can't be changed.</p> <p>The <code>Edition</code> parameter is optional. If you don't supply a value, the default is <code>ENTERPRISE_EDITION</code>.</p> <p>For more information on quota limits for Enterprise and Developer editions, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/quotas.html\">Quotas</a>.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of an IAM role with permission to access your Amazon CloudWatch logs and metrics. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/iam-roles.html\">IAM access roles for Amazon Kendra</a>.</p>"}, "ServerSideEncryptionConfiguration": {"shape": "ServerSideEncryptionConfiguration", "documentation": "<p>The identifier of the KMS customer managed key (CMK) that's used to encrypt data indexed by Amazon Kendra. Amazon Kendra doesn't support asymmetric CMKs.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description for the index.</p>"}, "ClientToken": {"shape": "ClientTokenName", "documentation": "<p>A token that you provide to identify the request to create an index. Multiple calls to the <code>CreateIndex</code> API with the same client token will create only one index.</p>", "idempotencyToken": true}, "Tags": {"shape": "TagList", "documentation": "<p>A list of key-value pairs that identify or categorize the index. You can also use tags to help control access to the index. Tag keys and values can consist of Unicode letters, digits, white space, and any of the following symbols: _ . : / = + - @.</p>"}, "UserTokenConfigurations": {"shape": "UserTokenConfigurationList", "documentation": "<p>The user token configuration.</p>"}, "UserContextPolicy": {"shape": "UserContextPolicy", "documentation": "<p>The user context policy.</p> <dl> <dt>ATTRIBUTE_FILTER</dt> <dd> <p>All indexed content is searchable and displayable for all users. If you want to filter search results on user context, you can use the attribute filters of <code>_user_id</code> and <code>_group_ids</code> or you can provide user and group information in <code>UserContext</code>. </p> </dd> <dt>USER_TOKEN</dt> <dd> <p>Enables token-based user access control to filter search results on user context. All documents with no access control and all documents accessible to the user will be searchable and displayable. </p> </dd> </dl>"}, "UserGroupResolutionConfiguration": {"shape": "UserGroupResolutionConfiguration", "documentation": "<p>Gets users and groups from IAM Identity Center identity source. To configure this, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_UserGroupResolutionConfiguration.html\">UserGroupResolutionConfiguration</a>.</p>"}}}, "CreateIndexResponse": {"type": "structure", "members": {"Id": {"shape": "IndexId", "documentation": "<p>The identifier of the index. Use this identifier when you query an index, set up a data source, or index a document.</p>"}}}, "CreateQuerySuggestionsBlockListRequest": {"type": "structure", "required": ["IndexId", "Name", "SourceS3Path", "RoleArn"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index you want to create a query suggestions block list for.</p>"}, "Name": {"shape": "QuerySuggestionsBlockListName", "documentation": "<p>A name for the block list.</p> <p>For example, the name 'offensive-words', which includes all offensive words that could appear in user queries and need to be blocked from suggestions.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description for the block list.</p> <p>For example, the description \"List of all offensive words that can appear in user queries and need to be blocked from suggestions.\"</p>"}, "SourceS3Path": {"shape": "S3Path", "documentation": "<p>The S3 path to your block list text file in your S3 bucket.</p> <p>Each block word or phrase should be on a separate line in a text file.</p> <p>For information on the current quota limits for block lists, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/quotas.html\">Quotas for Amazon Kendra</a>.</p>"}, "ClientToken": {"shape": "ClientTokenName", "documentation": "<p>A token that you provide to identify the request to create a query suggestions block list.</p>", "idempotencyToken": true}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of an IAM role with permission to access your S3 bucket that contains the block list text file. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/iam-roles.html\">IAM access roles for Amazon Kendra</a>.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>A list of key-value pairs that identify or categorize the block list. Tag keys and values can consist of Unicode letters, digits, white space, and any of the following symbols: _ . : / = + - @.</p>"}}}, "CreateQuerySuggestionsBlockListResponse": {"type": "structure", "members": {"Id": {"shape": "QuerySuggestionsBlockListId", "documentation": "<p>The identifier of the block list.</p>"}}}, "CreateThesaurusRequest": {"type": "structure", "required": ["IndexId", "Name", "RoleArn", "SourceS3Path"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for the thesaurus.</p>"}, "Name": {"shape": "ThesaurusName", "documentation": "<p>A name for the thesaurus.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description for the thesaurus.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of an IAM role with permission to access your S3 bucket that contains the thesaurus file. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/iam-roles.html\">IAM access roles for Amazon Kendra</a>.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>A list of key-value pairs that identify or categorize the thesaurus. You can also use tags to help control access to the thesaurus. Tag keys and values can consist of Unicode letters, digits, white space, and any of the following symbols: _ . : / = + - @.</p>"}, "SourceS3Path": {"shape": "S3Path", "documentation": "<p>The path to the thesaurus file in S3.</p>"}, "ClientToken": {"shape": "ClientTokenName", "documentation": "<p>A token that you provide to identify the request to create a thesaurus. Multiple calls to the <code>CreateThesaurus</code> API with the same client token will create only one thesaurus. </p>", "idempotencyToken": true}}}, "CreateThesaurusResponse": {"type": "structure", "members": {"Id": {"shape": "ThesaurusId", "documentation": "<p>The identifier of the thesaurus. </p>"}}}, "CustomDocumentEnrichmentConfiguration": {"type": "structure", "members": {"InlineConfigurations": {"shape": "InlineCustomDocumentEnrichmentConfigurationList", "documentation": "<p>Configuration information to alter document attributes or metadata fields and content when ingesting documents into Amazon Kendra.</p>"}, "PreExtractionHookConfiguration": {"shape": "HookConfiguration", "documentation": "<p>Configuration information for invoking a Lambda function in Lambda on the original or raw documents before extracting their metadata and text. You can use a Lambda function to apply advanced logic for creating, modifying, or deleting document metadata and content. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/custom-document-enrichment.html#advanced-data-manipulation\">Advanced data manipulation</a>.</p>"}, "PostExtractionHookConfiguration": {"shape": "HookConfiguration", "documentation": "<p>Configuration information for invoking a Lambda function in Lambda on the structured documents with their metadata and text extracted. You can use a Lambda function to apply advanced logic for creating, modifying, or deleting document metadata and content. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/custom-document-enrichment.html#advanced-data-manipulation\">Advanced data manipulation</a>.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of a role with permission to run <code>PreExtractionHookConfiguration</code> and <code>PostExtractionHookConfiguration</code> for altering document metadata and content during the document ingestion process. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/iam-roles.html\">IAM roles for Amazon Kendra</a>.</p>"}}, "documentation": "<p>Provides the configuration information for altering document metadata and content during the document ingestion process.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/custom-document-enrichment.html\">Customizing document metadata during the ingestion process</a>.</p>"}, "DataSourceConfiguration": {"type": "structure", "members": {"S3Configuration": {"shape": "S3DataSourceConfiguration", "documentation": "<p>Provides the configuration information to connect to an Amazon S3 bucket as your data source.</p>"}, "SharePointConfiguration": {"shape": "SharePointConfiguration", "documentation": "<p>Provides the configuration information to connect to Microsoft SharePoint as your data source.</p>"}, "DatabaseConfiguration": {"shape": "DatabaseConfiguration", "documentation": "<p>Provides the configuration information to connect to a database as your data source.</p>"}, "SalesforceConfiguration": {"shape": "SalesforceConfiguration", "documentation": "<p>Provides the configuration information to connect to Salesforce as your data source.</p>"}, "OneDriveConfiguration": {"shape": "OneDriveConfiguration", "documentation": "<p>Provides the configuration information to connect to Microsoft OneDrive as your data source.</p>"}, "ServiceNowConfiguration": {"shape": "ServiceNowConfiguration", "documentation": "<p>Provides the configuration information to connect to ServiceNow as your data source.</p>"}, "ConfluenceConfiguration": {"shape": "ConfluenceConfiguration", "documentation": "<p>Provides the configuration information to connect to Confluence as your data source.</p>"}, "GoogleDriveConfiguration": {"shape": "GoogleDriveConfiguration", "documentation": "<p>Provides the configuration information to connect to Google Drive as your data source.</p>"}, "WebCrawlerConfiguration": {"shape": "WebCrawlerConfiguration"}, "WorkDocsConfiguration": {"shape": "WorkDocsConfiguration", "documentation": "<p>Provides the configuration information to connect to Amazon WorkDocs as your data source.</p>"}, "FsxConfiguration": {"shape": "FsxConfiguration", "documentation": "<p>Provides the configuration information to connect to Amazon FSx as your data source.</p>"}, "SlackConfiguration": {"shape": "SlackConfiguration", "documentation": "<p>Provides the configuration information to connect to Slack as your data source.</p>"}, "BoxConfiguration": {"shape": "BoxConfiguration", "documentation": "<p>Provides the configuration information to connect to Box as your data source.</p>"}, "QuipConfiguration": {"shape": "QuipConfiguration", "documentation": "<p>Provides the configuration information to connect to Quip as your data source.</p>"}, "JiraConfiguration": {"shape": "JiraConfiguration", "documentation": "<p>Provides the configuration information to connect to <PERSON><PERSON> as your data source.</p>"}, "GitHubConfiguration": {"shape": "GitHubConfiguration", "documentation": "<p>Provides the configuration information to connect to GitHub as your data source.</p>"}, "AlfrescoConfiguration": {"shape": "AlfrescoConfiguration", "documentation": "<p>Provides the configuration information to connect to Alfresco as your data source.</p> <p>Support for <code>AlfrescoConfiguration</code> ended May 2023. We recommend migrating to or using the Alfresco data source template schema / <a href=\"https://docs.aws.amazon.com/kendra/latest/APIReference/API_TemplateConfiguration.html\">TemplateConfiguration</a> API.</p>", "deprecated": true, "deprecatedMessage": "Deprecated AlfrescoConfiguration in favor of TemplateConfiguration"}, "TemplateConfiguration": {"shape": "TemplateConfiguration", "documentation": "<p>Provides a template for the configuration information to connect to your data source.</p>"}}, "documentation": "<p>Provides the configuration information for an Amazon Kendra data source.</p>"}, "DataSourceDateFieldFormat": {"type": "string", "max": 40, "min": 4, "pattern": "^(?!\\s).*(?<!\\s)$"}, "DataSourceFieldName": {"type": "string", "max": 100, "min": 1, "pattern": "^[a-zA-Z][a-zA-Z0-9_.]*$"}, "DataSourceGroup": {"type": "structure", "required": ["GroupId", "DataSourceId"], "members": {"GroupId": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The identifier of the group you want to add to your list of groups. This is for filtering search results based on the groups' access to documents.</p>"}, "DataSourceId": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source group you want to add to your list of data source groups. This is for filtering search results based on the groups' access to documents in that data source.</p>"}}, "documentation": "<p>Data source information for user context filtering.</p>"}, "DataSourceGroups": {"type": "list", "member": {"shape": "DataSourceGroup"}, "max": 2048, "min": 1}, "DataSourceId": {"type": "string", "max": 100, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_-]*"}, "DataSourceIdList": {"type": "list", "member": {"shape": "DataSourceId"}, "max": 100, "min": 1}, "DataSourceInclusionsExclusionsStrings": {"type": "list", "member": {"shape": "DataSourceInclusionsExclusionsStringsMember"}, "max": 250, "min": 0}, "DataSourceInclusionsExclusionsStringsMember": {"type": "string", "max": 300, "min": 1}, "DataSourceName": {"type": "string", "max": 1000, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_-]*"}, "DataSourceStatus": {"type": "string", "enum": ["CREATING", "DELETING", "FAILED", "UPDATING", "ACTIVE"]}, "DataSourceSummary": {"type": "structure", "members": {"Name": {"shape": "DataSourceName", "documentation": "<p>The name of the data source.</p>"}, "Id": {"shape": "DataSourceId", "documentation": "<p>The identifier for the data source.</p>"}, "Type": {"shape": "DataSourceType", "documentation": "<p>The type of the data source.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the data source connector was created.</p>"}, "UpdatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the data source connector was last updated.</p>"}, "Status": {"shape": "DataSourceStatus", "documentation": "<p>The status of the data source. When the status is <code>ACTIVE</code> the data source is ready to use.</p>"}, "LanguageCode": {"shape": "LanguageCode", "documentation": "<p>The code for a language. This shows a supported language for all documents in the data source. English is supported by default. For more information on supported languages, including their codes, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/in-adding-languages.html\">Adding documents in languages other than English</a>.</p>"}}, "documentation": "<p>Summary information for a Amazon Kendra data source.</p>"}, "DataSourceSummaryList": {"type": "list", "member": {"shape": "DataSourceSummary"}}, "DataSourceSyncJob": {"type": "structure", "members": {"ExecutionId": {"shape": "String", "documentation": "<p>A identifier for the synchronization job.</p>"}, "StartTime": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the synchronization job started.</p>"}, "EndTime": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the synchronization job completed.</p>"}, "Status": {"shape": "DataSourceSyncJobStatus", "documentation": "<p>The execution status of the synchronization job. When the <code>Status</code> field is set to <code>SUCCEEDED</code>, the synchronization job is done. If the status code is set to <code>FAILED</code>, the <code>ErrorCode</code> and <code>ErrorMessage</code> fields give you the reason for the failure.</p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>If the <code>Status</code> field is set to <code>ERROR</code>, the <code>ErrorMessage</code> field contains a description of the error that caused the synchronization to fail.</p>"}, "ErrorCode": {"shape": "ErrorCode", "documentation": "<p>If the <code>Status</code> field is set to <code>FAILED</code>, the <code>ErrorCode</code> field indicates the reason the synchronization failed.</p>"}, "DataSourceErrorCode": {"shape": "String", "documentation": "<p>If the reason that the synchronization failed is due to an error with the underlying data source, this field contains a code that identifies the error.</p>"}, "Metrics": {"shape": "DataSourceSyncJobMetrics", "documentation": "<p>Maps a batch delete document request to a specific data source sync job. This is optional and should only be supplied when documents are deleted by a data source connector.</p>"}}, "documentation": "<p>Provides information about a data source synchronization job.</p>"}, "DataSourceSyncJobHistoryList": {"type": "list", "member": {"shape": "DataSourceSyncJob"}}, "DataSourceSyncJobId": {"type": "string", "max": 100, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_-]*"}, "DataSourceSyncJobMetricTarget": {"type": "structure", "required": ["DataSourceId"], "members": {"DataSourceId": {"shape": "DataSourceId", "documentation": "<p>The ID of the data source that is running the sync job.</p>"}, "DataSourceSyncJobId": {"shape": "DataSourceSyncJobId", "documentation": "<p>The ID of the sync job that is running on the data source.</p> <p>If the ID of a sync job is not provided and there is a sync job running, then the ID of this sync job is used and metrics are generated for this sync job.</p> <p>If the ID of a sync job is not provided and there is no sync job running, then no metrics are generated and documents are indexed/deleted at the index level without sync job metrics included.</p>"}}, "documentation": "<p>Maps a particular data source sync job to a particular data source.</p>"}, "DataSourceSyncJobMetrics": {"type": "structure", "members": {"DocumentsAdded": {"shape": "MetricValue", "documentation": "<p>The number of documents added from the data source up to now in the data source sync.</p>"}, "DocumentsModified": {"shape": "MetricValue", "documentation": "<p>The number of documents modified in the data source up to now in the data source sync run.</p>"}, "DocumentsDeleted": {"shape": "MetricValue", "documentation": "<p>The number of documents deleted from the data source up to now in the data source sync run.</p>"}, "DocumentsFailed": {"shape": "MetricValue", "documentation": "<p>The number of documents that failed to sync from the data source up to now in the data source sync run.</p>"}, "DocumentsScanned": {"shape": "MetricValue", "documentation": "<p>The current number of documents crawled by the current sync job in the data source.</p>"}}, "documentation": "<p>Maps a batch delete document request to a specific data source sync job. This is optional and should only be supplied when documents are deleted by a data source connector.</p>"}, "DataSourceSyncJobStatus": {"type": "string", "enum": ["FAILED", "SUCCEEDED", "SYNCING", "INCOMPLETE", "STOPPING", "ABORTED", "SYNCING_INDEXING"]}, "DataSourceToIndexFieldMapping": {"type": "structure", "required": ["DataSourceFieldName", "IndexFieldName"], "members": {"DataSourceFieldName": {"shape": "DataSourceFieldName", "documentation": "<p>The name of the field in the data source. You must first create the index field using the <code>UpdateIndex</code> API.</p>"}, "DateFieldFormat": {"shape": "DataSourceDateFieldFormat", "documentation": "<p>The format for date fields in the data source. If the field specified in <code>DataSourceFieldName</code> is a date field, you must specify the date format. If the field is not a date field, an exception is thrown.</p>"}, "IndexFieldName": {"shape": "IndexFieldName", "documentation": "<p>The name of the index field to map to the data source field. The index field type must match the data source field type.</p>"}}, "documentation": "<p>Maps attributes or field names of the documents synced from the data source to Amazon Kendra index field names. You can set up field mappings for each data source when calling <a href=\"https://docs.aws.amazon.com/kendra/latest/APIReference/API_CreateDataSource.html\">CreateDataSource</a> or <a href=\"https://docs.aws.amazon.com/kendra/latest/APIReference/API_UpdateDataSource.html\">UpdateDataSource</a> API. To create custom fields, use the <code>UpdateIndex</code> API to first create an index field and then map to the data source field. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>.</p>"}, "DataSourceToIndexFieldMappingList": {"type": "list", "member": {"shape": "DataSourceToIndexFieldMapping"}, "max": 100, "min": 1}, "DataSourceType": {"type": "string", "enum": ["S3", "SHAREPOINT", "DATABASE", "SALESFORCE", "ONEDRIVE", "SERVICENOW", "CUSTOM", "CONFLUENCE", "GOOGLEDRIVE", "WEBCRAWLER", "WORKDOCS", "FSX", "SLACK", "BOX", "QUIP", "JIRA", "GITHUB", "ALFRESCO", "TEMPLATE"]}, "DataSourceVpcConfiguration": {"type": "structure", "required": ["SubnetIds", "SecurityGroupIds"], "members": {"SubnetIds": {"shape": "SubnetIdList", "documentation": "<p>A list of identifiers for subnets within your Amazon VPC. The subnets should be able to connect to each other in the VPC, and they should have outgoing access to the Internet through a NAT device.</p>"}, "SecurityGroupIds": {"shape": "SecurityGroupIdList", "documentation": "<p>A list of identifiers of security groups within your Amazon VPC. The security groups should enable Amazon Kendra to connect to the data source.</p>"}}, "documentation": "<p>Provides the configuration information to connect to an Amazon VPC.</p>"}, "DatabaseConfiguration": {"type": "structure", "required": ["DatabaseEngineType", "ConnectionConfiguration", "ColumnConfiguration"], "members": {"DatabaseEngineType": {"shape": "DatabaseEngineType", "documentation": "<p>The type of database engine that runs the database.</p>"}, "ConnectionConfiguration": {"shape": "ConnectionConfiguration", "documentation": "<p>Configuration information that's required to connect to a database.</p>"}, "VpcConfiguration": {"shape": "DataSourceVpcConfiguration"}, "ColumnConfiguration": {"shape": "ColumnConfiguration", "documentation": "<p>Information about where the index should get the document information from the database.</p>"}, "AclConfiguration": {"shape": "AclConfiguration", "documentation": "<p>Information about the database column that provides information for user context filtering.</p>"}, "SqlConfiguration": {"shape": "SqlConfiguration", "documentation": "<p>Provides information about how Amazon Kendra uses quote marks around SQL identifiers when querying a database data source.</p>"}}, "documentation": "<p>Provides the configuration information to an <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/data-source-database.html\">Amazon Kendra supported database</a>.</p>"}, "DatabaseEngineType": {"type": "string", "enum": ["RDS_AURORA_MYSQL", "RDS_AURORA_POSTGRESQL", "RDS_MYSQL", "RDS_POSTGRESQL"]}, "DatabaseHost": {"type": "string", "max": 253, "min": 1}, "DatabaseName": {"type": "string", "max": 100, "min": 1, "pattern": "^[a-zA-Z][a-zA-Z0-9_]*$"}, "DatabasePort": {"type": "integer", "max": 65535, "min": 1}, "DeleteAccessControlConfigurationRequest": {"type": "structure", "required": ["IndexId", "Id"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for an access control configuration.</p>"}, "Id": {"shape": "AccessControlConfigurationId", "documentation": "<p>The identifier of the access control configuration you want to delete.</p>"}}}, "DeleteAccessControlConfigurationResponse": {"type": "structure", "members": {}}, "DeleteDataSourceRequest": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source connector you want to delete.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index used with the data source connector.</p>"}}}, "DeleteExperienceRequest": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {"shape": "ExperienceId", "documentation": "<p>The identifier of your Amazon Kendra experience you want to delete.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for your Amazon Kendra experience.</p>"}}}, "DeleteExperienceResponse": {"type": "structure", "members": {}}, "DeleteFaqRequest": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {"shape": "FaqId", "documentation": "<p>The identifier of the FAQ you want to remove.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for the FAQ.</p>"}}}, "DeleteIndexRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "IndexId", "documentation": "<p>The identifier of the index you want to delete.</p>"}}}, "DeletePrincipalMappingRequest": {"type": "structure", "required": ["IndexId", "GroupId"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index you want to delete a group from.</p>"}, "DataSourceId": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source you want to delete a group from.</p> <p>A group can be tied to multiple data sources. You can delete a group from accessing documents in a certain data source. For example, the groups \"Research\", \"Engineering\", and \"Sales and Marketing\" are all tied to the company's documents stored in the data sources Confluence and Salesforce. You want to delete \"Research\" and \"Engineering\" groups from Salesforce, so that these groups cannot access customer-related documents stored in Salesforce. Only \"Sales and Marketing\" should access documents in the Salesforce data source.</p>"}, "GroupId": {"shape": "GroupId", "documentation": "<p>The identifier of the group you want to delete.</p>"}, "OrderingId": {"shape": "PrincipalOrderingId", "documentation": "<p>The timestamp identifier you specify to ensure Amazon Kendra does not override the latest <code>DELETE</code> action with previous actions. The highest number ID, which is the ordering ID, is the latest action you want to process and apply on top of other actions with lower number IDs. This prevents previous actions with lower number IDs from possibly overriding the latest action.</p> <p>The ordering ID can be the Unix time of the last update you made to a group members list. You would then provide this list when calling <code>PutPrincipalMapping</code>. This ensures your <code>DELETE</code> action for that updated group with the latest members list doesn't get overwritten by earlier <code>DELETE</code> actions for the same group which are yet to be processed.</p> <p>The default ordering ID is the current Unix time in milliseconds that the action was received by Amazon Kendra. </p>"}}}, "DeleteQuerySuggestionsBlockListRequest": {"type": "structure", "required": ["IndexId", "Id"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for the block list.</p>"}, "Id": {"shape": "QuerySuggestionsBlockListId", "documentation": "<p>The identifier of the block list you want to delete.</p>"}}}, "DeleteThesaurusRequest": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {"shape": "ThesaurusId", "documentation": "<p>The identifier of the thesaurus you want to delete.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for the thesaurus.</p>"}}}, "DescribeAccessControlConfigurationRequest": {"type": "structure", "required": ["IndexId", "Id"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for an access control configuration.</p>"}, "Id": {"shape": "AccessControlConfigurationId", "documentation": "<p>The identifier of the access control configuration you want to get information on.</p>"}}}, "DescribeAccessControlConfigurationResponse": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "AccessControlConfigurationName", "documentation": "<p>The name for the access control configuration.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description for the access control configuration.</p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>The error message containing details if there are issues processing the access control configuration.</p>"}, "AccessControlList": {"shape": "PrincipalList", "documentation": "<p>Information on principals (users and/or groups) and which documents they should have access to. This is useful for user context filtering, where search results are filtered based on the user or their group access to documents.</p>"}, "HierarchicalAccessControlList": {"shape": "HierarchicalPrincipalList", "documentation": "<p>The list of <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_Principal.html\">principal</a> lists that define the hierarchy for which documents users should have access to.</p>"}}}, "DescribeDataSourceRequest": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source connector.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index used with the data source connector.</p>"}}}, "DescribeDataSourceResponse": {"type": "structure", "members": {"Id": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source connector.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index used with the data source connector.</p>"}, "Name": {"shape": "DataSourceName", "documentation": "<p>The name for the data source connector.</p>"}, "Type": {"shape": "DataSourceType", "documentation": "<p>The type of the data source. For example, <code>SHAREPOINT</code>.</p>"}, "Configuration": {"shape": "DataSourceConfiguration", "documentation": "<p>Configuration details for the data source connector. This shows how the data source is configured. The configuration options for a data source depend on the data source provider.</p>"}, "VpcConfiguration": {"shape": "DataSourceVpcConfiguration", "documentation": "<p>Configuration information for an Amazon Virtual Private Cloud to connect to your data source. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/vpc-configuration.html\">Configuring a VPC</a>.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the data source connector was created.</p>"}, "UpdatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the data source connector was last updated.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description for the data source connector.</p>"}, "Status": {"shape": "DataSourceStatus", "documentation": "<p>The current status of the data source connector. When the status is <code>ACTIVE</code> the data source is ready to use. When the status is <code>FAILED</code>, the <code>ErrorMessage</code> field contains the reason that the data source failed.</p>"}, "Schedule": {"shape": "ScanSchedule", "documentation": "<p>The schedule for Amazon Kendra to update the index.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the role with permission to access the data source and required resources.</p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>When the <code>Status</code> field value is <code>FAILED</code>, the <code>ErrorMessage</code> field contains a description of the error that caused the data source to fail.</p>"}, "LanguageCode": {"shape": "LanguageCode", "documentation": "<p>The code for a language. This shows a supported language for all documents in the data source. English is supported by default. For more information on supported languages, including their codes, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/in-adding-languages.html\">Adding documents in languages other than English</a>.</p>"}, "CustomDocumentEnrichmentConfiguration": {"shape": "CustomDocumentEnrichmentConfiguration", "documentation": "<p>Configuration information for altering document metadata and content during the document ingestion process when you describe a data source.</p> <p>For more information on how to create, modify and delete document metadata, or make other content alterations when you ingest documents into Amazon Kendra, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/custom-document-enrichment.html\">Customizing document metadata during the ingestion process</a>.</p>"}}}, "DescribeExperienceRequest": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {"shape": "ExperienceId", "documentation": "<p>The identifier of your Amazon Kendra experience you want to get information on.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for your Amazon Kendra experience.</p>"}}}, "DescribeExperienceResponse": {"type": "structure", "members": {"Id": {"shape": "ExperienceId", "documentation": "<p>Shows the identifier of your Amazon Kendra experience.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>Shows the identifier of the index for your Amazon Kendra experience.</p>"}, "Name": {"shape": "ExperienceName", "documentation": "<p>Shows the name of your Amazon Kendra experience.</p>"}, "Endpoints": {"shape": "ExperienceEndpoints", "documentation": "<p>Shows the endpoint URLs for your Amazon Kendra experiences. The URLs are unique and fully hosted by Amazon Web Services.</p>"}, "Configuration": {"shape": "ExperienceConfiguration", "documentation": "<p>Shows the configuration information for your Amazon Kendra experience. This includes <code>ContentSourceConfiguration</code>, which specifies the data source IDs and/or FAQ IDs, and <code>UserIdentityConfiguration</code>, which specifies the user or group information to grant access to your Amazon Kendra experience.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when your Amazon Kendra experience was created.</p>"}, "UpdatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when your Amazon Kendra experience was last updated.</p>"}, "Description": {"shape": "Description", "documentation": "<p>Shows the description for your Amazon Kendra experience.</p>"}, "Status": {"shape": "ExperienceStatus", "documentation": "<p>The current processing status of your Amazon Kendra experience. When the status is <code>ACTIVE</code>, your Amazon Kendra experience is ready to use. When the status is <code>FAILED</code>, the <code>ErrorMessage</code> field contains the reason that this failed.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>Shows the Amazon Resource Name (ARN) of a role with permission to access <code>Query</code> API, <code>QuerySuggestions</code> API, <code>SubmitFeedback</code> API, and IAM Identity Center that stores your user and group information.</p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>The reason your Amazon Kendra experience could not properly process.</p>"}}}, "DescribeFaqRequest": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {"shape": "FaqId", "documentation": "<p>The identifier of the FAQ you want to get information on.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for the FAQ.</p>"}}}, "DescribeFaqResponse": {"type": "structure", "members": {"Id": {"shape": "FaqId", "documentation": "<p>The identifier of the FAQ.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for the FAQ.</p>"}, "Name": {"shape": "FaqName", "documentation": "<p>The name that you gave the FAQ when it was created.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the FAQ that you provided when it was created.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the FAQ was created.</p>"}, "UpdatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the FAQ was last updated.</p>"}, "S3Path": {"shape": "S3Path"}, "Status": {"shape": "FaqStatus", "documentation": "<p>The status of the FAQ. It is ready to use when the status is <code>ACTIVE</code>.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the role that provides access to the S3 bucket containing the input files for the FAQ.</p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>If the <code>Status</code> field is <code>FAILED</code>, the <code>ErrorMessage</code> field contains the reason why the FAQ failed.</p>"}, "FileFormat": {"shape": "FaqFileFormat", "documentation": "<p>The file format used by the input files for the FAQ.</p>"}, "LanguageCode": {"shape": "LanguageCode", "documentation": "<p>The code for a language. This shows a supported language for the FAQ document. English is supported by default. For more information on supported languages, including their codes, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/in-adding-languages.html\">Adding documents in languages other than English</a>.</p>"}}}, "DescribeFeaturedResultsSetRequest": {"type": "structure", "required": ["IndexId", "FeaturedResultsSetId"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index used for featuring results.</p>"}, "FeaturedResultsSetId": {"shape": "FeaturedResultsSetId", "documentation": "<p>The identifier of the set of featured results that you want to get information on.</p>"}}}, "DescribeFeaturedResultsSetResponse": {"type": "structure", "members": {"FeaturedResultsSetId": {"shape": "FeaturedResultsSetId", "documentation": "<p>The identifier of the set of featured results.</p>"}, "FeaturedResultsSetName": {"shape": "FeaturedResultsSetName", "documentation": "<p>The name for the set of featured results.</p>"}, "Description": {"shape": "FeaturedResultsSetDescription", "documentation": "<p>The description for the set of featured results.</p>"}, "Status": {"shape": "FeaturedResultsSetStatus", "documentation": "<p>The current status of the set of featured results. When the value is <code>ACTIVE</code>, featured results are ready for use. You can still configure your settings before setting the status to <code>ACTIVE</code>. You can set the status to <code>ACTIVE</code> or <code>INACTIVE</code> using the <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_UpdateFeaturedResultsSet.html\">UpdateFeaturedResultsSet</a> API. The queries you specify for featured results must be unique per featured results set for each index, whether the status is <code>ACTIVE</code> or <code>INACTIVE</code>.</p>"}, "QueryTexts": {"shape": "QueryTextList", "documentation": "<p>The list of queries for featuring results. For more information on the list of queries, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_FeaturedResultsSet.html\">FeaturedResultsSet</a>.</p>"}, "FeaturedDocumentsWithMetadata": {"shape": "FeaturedDocumentWithMetadataList", "documentation": "<p>The list of document IDs for the documents you want to feature with their metadata information. For more information on the list of featured documents, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_FeaturedResultsSet.html\">FeaturedResultsSet</a>.</p>"}, "FeaturedDocumentsMissing": {"shape": "FeaturedDocumentMissingList", "documentation": "<p>The list of document IDs that don't exist but you have specified as featured documents. Amazon Kendra cannot feature these documents if they don't exist in the index. You can check the status of a document and its ID or check for documents with status errors using the <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_BatchGetDocumentStatus.html\">BatchGetDocumentStatus</a> API.</p>"}, "LastUpdatedTimestamp": {"shape": "<PERSON>", "documentation": "<p>The timestamp when the set of featured results was last updated.</p>"}, "CreationTimestamp": {"shape": "<PERSON>", "documentation": "<p>The Unix timestamp when the set of the featured results was created.</p>"}}}, "DescribeIndexRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "IndexId", "documentation": "<p>The identifier of the index you want to get information on.</p>"}}}, "DescribeIndexResponse": {"type": "structure", "members": {"Name": {"shape": "IndexName", "documentation": "<p>The name of the index.</p>"}, "Id": {"shape": "IndexId", "documentation": "<p>The identifier of the index.</p>"}, "Edition": {"shape": "IndexEdition", "documentation": "<p>The Amazon Kendra edition used for the index. You decide the edition when you create the index.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role that gives Amazon Kendra permission to write to your Amazon Cloudwatch logs.</p>"}, "ServerSideEncryptionConfiguration": {"shape": "ServerSideEncryptionConfiguration", "documentation": "<p>The identifier of the KMScustomer master key (CMK) that is used to encrypt your data. Amazon Kendra doesn't support asymmetric CMKs.</p>"}, "Status": {"shape": "IndexStatus", "documentation": "<p>The current status of the index. When the value is <code>ACTIVE</code>, the index is ready for use. If the <code>Status</code> field value is <code>FAILED</code>, the <code>ErrorMessage</code> field contains a message that explains why.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description for the index.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the index was created.</p>"}, "UpdatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix when the index was last updated.</p>"}, "DocumentMetadataConfigurations": {"shape": "DocumentMetadataConfigurationList", "documentation": "<p>Configuration information for document metadata or fields. Document metadata are fields or attributes associated with your documents. For example, the company department name associated with each document.</p>"}, "IndexStatistics": {"shape": "IndexStatistics", "documentation": "<p>Provides information about the number of FAQ questions and answers and the number of text documents indexed.</p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>When the <code>Status</code> field value is <code>FAILED</code>, the <code>ErrorMessage</code> field contains a message that explains why.</p>"}, "CapacityUnits": {"shape": "CapacityUnitsConfiguration", "documentation": "<p>For Enterprise Edition indexes, you can choose to use additional capacity to meet the needs of your application. This contains the capacity units used for the index. A query or document storage capacity of zero indicates that the index is using the default capacity. For more information on the default capacity for an index and adjusting this, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/adjusting-capacity.html\">Adjusting capacity</a>.</p>"}, "UserTokenConfigurations": {"shape": "UserTokenConfigurationList", "documentation": "<p>The user token configuration for the Amazon Kendra index.</p>"}, "UserContextPolicy": {"shape": "UserContextPolicy", "documentation": "<p>The user context policy for the Amazon Kendra index.</p>"}, "UserGroupResolutionConfiguration": {"shape": "UserGroupResolutionConfiguration", "documentation": "<p>Whether you have enabled the configuration for fetching access levels of groups and users from an IAM Identity Center identity source.</p>"}}}, "DescribePrincipalMappingRequest": {"type": "structure", "required": ["IndexId", "GroupId"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index required to check the processing of <code>PUT</code> and <code>DELETE</code> actions for mapping users to their groups.</p>"}, "DataSourceId": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source to check the processing of <code>PUT</code> and <code>DELETE</code> actions for mapping users to their groups.</p>"}, "GroupId": {"shape": "GroupId", "documentation": "<p>The identifier of the group required to check the processing of <code>PUT</code> and <code>DELETE</code> actions for mapping users to their groups.</p>"}}}, "DescribePrincipalMappingResponse": {"type": "structure", "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>Shows the identifier of the index to see information on the processing of <code>PUT</code> and <code>DELETE</code> actions for mapping users to their groups.</p>"}, "DataSourceId": {"shape": "DataSourceId", "documentation": "<p>Shows the identifier of the data source to see information on the processing of <code>PUT</code> and <code>DELETE</code> actions for mapping users to their groups.</p>"}, "GroupId": {"shape": "GroupId", "documentation": "<p>Shows the identifier of the group to see information on the processing of <code>PUT</code> and <code>DELETE</code> actions for mapping users to their groups.</p>"}, "GroupOrderingIdSummaries": {"shape": "GroupOrderingIdSummaries", "documentation": "<p>Shows the following information on the processing of <code>PUT</code> and <code>DELETE</code> actions for mapping users to their groups:</p> <ul> <li> <p>Status—the status can be either <code>PROCESSING</code>, <code>SUCCEEDED</code>, <code>DELETING</code>, <code>DELETED</code>, or <code>FAILED</code>.</p> </li> <li> <p>Last updated—the last date-time an action was updated.</p> </li> <li> <p>Received—the last date-time an action was received or submitted.</p> </li> <li> <p>Ordering ID—the latest action that should process and apply after other actions.</p> </li> <li> <p>Failure reason—the reason an action could not be processed.</p> </li> </ul>"}}}, "DescribeQuerySuggestionsBlockListRequest": {"type": "structure", "required": ["IndexId", "Id"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for the block list.</p>"}, "Id": {"shape": "QuerySuggestionsBlockListId", "documentation": "<p>The identifier of the block list you want to get information on.</p>"}}}, "DescribeQuerySuggestionsBlockListResponse": {"type": "structure", "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for the block list.</p>"}, "Id": {"shape": "QuerySuggestionsBlockListId", "documentation": "<p>The identifier of the block list.</p>"}, "Name": {"shape": "QuerySuggestionsBlockListName", "documentation": "<p>The name of the block list.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description for the block list.</p>"}, "Status": {"shape": "QuerySuggestionsBlockListStatus", "documentation": "<p>The current status of the block list. When the value is <code>ACTIVE</code>, the block list is ready for use.</p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>The error message containing details if there are issues processing the block list.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when a block list for query suggestions was created.</p>"}, "UpdatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when a block list for query suggestions was last updated.</p>"}, "SourceS3Path": {"shape": "S3Path", "documentation": "<p>Shows the current S3 path to your block list text file in your S3 bucket.</p> <p>Each block word or phrase should be on a separate line in a text file.</p> <p>For information on the current quota limits for block lists, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/quotas.html\">Quotas for Amazon Kendra</a>.</p>"}, "ItemCount": {"shape": "Integer", "documentation": "<p>The current number of valid, non-empty words or phrases in the block list text file.</p>"}, "FileSizeBytes": {"shape": "<PERSON>", "documentation": "<p>The current size of the block list text file in S3.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The IAM (Identity and Access Management) role used by Amazon Kendra to access the block list text file in S3.</p> <p>The role needs S3 read permissions to your file in S3 and needs to give STS (Security Token Service) assume role permissions to Amazon Kendra.</p>"}}}, "DescribeQuerySuggestionsConfigRequest": {"type": "structure", "required": ["IndexId"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index with query suggestions that you want to get information on.</p>"}}}, "DescribeQuerySuggestionsConfigResponse": {"type": "structure", "members": {"Mode": {"shape": "Mode", "documentation": "<p>Whether query suggestions are currently in <code>ENABLED</code> mode or <code>LEARN_ONLY</code> mode.</p> <p>By default, Amazon Kendra enables query suggestions.<code>LEARN_ONLY</code> turns off query suggestions for your users. You can change the mode using the <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_UpdateQuerySuggestionsConfig.html\">UpdateQuerySuggestionsConfig</a> API.</p>"}, "Status": {"shape": "QuerySuggestionsStatus", "documentation": "<p>Whether the status of query suggestions settings is currently <code>ACTIVE</code> or <code>UPDATING</code>.</p> <p>Active means the current settings apply and Updating means your changed settings are in the process of applying.</p>"}, "QueryLogLookBackWindowInDays": {"shape": "Integer", "documentation": "<p>How recent your queries are in your query log time window (in days).</p>"}, "IncludeQueriesWithoutUserInformation": {"shape": "ObjectBoolean", "documentation": "<p> <code>TRUE</code> to use all queries, otherwise use only queries that include user information to generate the query suggestions.</p>"}, "MinimumNumberOfQueryingUsers": {"shape": "MinimumNumberOfQueryingUsers", "documentation": "<p>The minimum number of unique users who must search a query in order for the query to be eligible to suggest to your users.</p>"}, "MinimumQueryCount": {"shape": "MinimumQueryCount", "documentation": "<p>The minimum number of times a query must be searched in order for the query to be eligible to suggest to your users.</p>"}, "LastSuggestionsBuildTime": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when query suggestions for an index was last updated.</p> <p>Amazon Kendra automatically updates suggestions every 24 hours, after you change a setting or after you apply a <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/query-suggestions.html#query-suggestions-blocklist\">block list</a>.</p>"}, "LastClearTime": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when query suggestions for an index was last cleared.</p> <p>After you clear suggestions, Amazon Kendra learns new suggestions based on new queries added to the query log from the time you cleared suggestions. Amazon Kendra only considers re-occurences of a query from the time you cleared suggestions. </p>"}, "TotalSuggestionsCount": {"shape": "Integer", "documentation": "<p>The current total count of query suggestions for an index.</p> <p>This count can change when you update your query suggestions settings, if you filter out certain queries from suggestions using a block list, and as the query log accumulates more queries for Amazon Kendra to learn from.</p> <p>If the count is much lower than you expected, it could be because Amazon Kendra needs more queries in the query history to learn from or your current query suggestions settings are too strict.</p>"}, "AttributeSuggestionsConfig": {"shape": "AttributeSuggestionsDescribeConfig", "documentation": "<p>Configuration information for the document fields/attributes that you want to base query suggestions on.</p>"}}}, "DescribeThesaurusRequest": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {"shape": "ThesaurusId", "documentation": "<p>The identifier of the thesaurus you want to get information on.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for the thesaurus.</p>"}}}, "DescribeThesaurusResponse": {"type": "structure", "members": {"Id": {"shape": "ThesaurusId", "documentation": "<p>The identifier of the thesaurus.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for the thesaurus.</p>"}, "Name": {"shape": "ThesaurusName", "documentation": "<p>The thesaurus name.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The thesaurus description.</p>"}, "Status": {"shape": "ThesaurusStatus", "documentation": "<p>The current status of the thesaurus. When the value is <code>ACTIVE</code>, queries are able to use the thesaurus. If the <code>Status</code> field value is <code>FAILED</code>, the <code>ErrorMessage</code> field provides more information. </p> <p>If the status is <code>ACTIVE_BUT_UPDATE_FAILED</code>, it means that Amazon Kendra could not ingest the new thesaurus file. The old thesaurus file is still active. </p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>When the <code>Status</code> field value is <code>FAILED</code>, the <code>ErrorMessage</code> field provides more information. </p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the thesaurus was created.</p>"}, "UpdatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the thesaurus was last updated.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>An IAM role that gives Amazon Kendra permissions to access thesaurus file specified in <code>SourceS3Path</code>. </p>"}, "SourceS3Path": {"shape": "S3Path"}, "FileSizeBytes": {"shape": "<PERSON>", "documentation": "<p>The size of the thesaurus file in bytes.</p>"}, "TermCount": {"shape": "<PERSON>", "documentation": "<p>The number of unique terms in the thesaurus file. For example, the synonyms <code>a,b,c</code> and <code>a=&gt;d</code>, the term count would be 4. </p>"}, "SynonymRuleCount": {"shape": "<PERSON>", "documentation": "<p>The number of synonym rules in the thesaurus file.</p>"}}}, "Description": {"type": "string", "max": 1000, "min": 0, "pattern": "^\\P{C}*$"}, "DisassociateEntitiesFromExperienceRequest": {"type": "structure", "required": ["Id", "IndexId", "EntityList"], "members": {"Id": {"shape": "ExperienceId", "documentation": "<p>The identifier of your Amazon Kendra experience.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for your Amazon Kendra experience.</p>"}, "EntityList": {"shape": "DisassociateEntityList", "documentation": "<p>Lists users or groups in your IAM Identity Center identity source.</p>"}}}, "DisassociateEntitiesFromExperienceResponse": {"type": "structure", "members": {"FailedEntityList": {"shape": "FailedEntityList", "documentation": "<p>Lists the users or groups in your IAM Identity Center identity source that failed to properly remove access to your Amazon Kendra experience.</p>"}}}, "DisassociateEntityList": {"type": "list", "member": {"shape": "EntityConfiguration"}, "max": 40, "min": 1}, "DisassociatePersonasFromEntitiesRequest": {"type": "structure", "required": ["Id", "IndexId", "EntityIds"], "members": {"Id": {"shape": "ExperienceId", "documentation": "<p>The identifier of your Amazon Kendra experience.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for your Amazon Kendra experience.</p>"}, "EntityIds": {"shape": "EntityIdsList", "documentation": "<p>The identifiers of users or groups in your IAM Identity Center identity source. For example, user IDs could be user emails.</p>"}}}, "DisassociatePersonasFromEntitiesResponse": {"type": "structure", "members": {"FailedEntityList": {"shape": "FailedEntityList", "documentation": "<p>Lists the users or groups in your IAM Identity Center identity source that failed to properly remove access to your Amazon Kendra experience.</p>"}}}, "Document": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "DocumentId", "documentation": "<p>A identifier of the document in the index.</p> <p>Note, each document ID must be unique per index. You cannot create a data source to index your documents with their unique IDs and then use the <code>BatchPutDocument</code> API to index the same documents, or vice versa. You can delete a data source and then use the <code>BatchPutDocument</code> API to index the same documents, or vice versa.</p>"}, "Title": {"shape": "Title", "documentation": "<p>The title of the document.</p>"}, "Blob": {"shape": "Blob", "documentation": "<p>The contents of the document. </p> <p>Documents passed to the <code>Blob</code> parameter must be base64 encoded. Your code might not need to encode the document file bytes if you're using an Amazon Web Services SDK to call Amazon Kendra APIs. If you are calling the Amazon Kendra endpoint directly using REST, you must base64 encode the contents before sending.</p>"}, "S3Path": {"shape": "S3Path"}, "Attributes": {"shape": "DocumentAttributeList", "documentation": "<p>Custom attributes to apply to the document. Use the custom attributes to provide additional information for searching, to provide facets for refining searches, and to provide additional information in the query response.</p> <p>For example, 'DataSourceId' and 'DataSourceSyncJobId' are custom attributes that provide information on the synchronization of documents running on a data source. Note, 'DataSourceSyncJobId' could be an optional custom attribute as Amazon Kendra will use the ID of a running sync job.</p>"}, "AccessControlList": {"shape": "PrincipalList", "documentation": "<p>Information on principals (users and/or groups) and which documents they should have access to. This is useful for user context filtering, where search results are filtered based on the user or their group access to documents.</p>"}, "HierarchicalAccessControlList": {"shape": "HierarchicalPrincipalList", "documentation": "<p>The list of <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_Principal.html\">principal</a> lists that define the hierarchy for which documents users should have access to.</p>"}, "ContentType": {"shape": "ContentType", "documentation": "<p>The file type of the document in the <code>Blob</code> field.</p> <p>If you want to index snippets or subsets of HTML documents instead of the entirety of the HTML documents, you must add the <code>HTML</code> start and closing tags (<code>&lt;HTML&gt;content&lt;/HTML&gt;</code>) around the content.</p>"}, "AccessControlConfigurationId": {"shape": "AccessControlConfigurationId", "documentation": "<p>The identifier of the access control configuration that you want to apply to the document.</p>"}}, "documentation": "<p>A document in an index.</p>"}, "DocumentAttribute": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "DocumentAttributeKey", "documentation": "<p>The identifier for the attribute.</p>"}, "Value": {"shape": "DocumentAttributeValue", "documentation": "<p>The value of the attribute.</p>"}}, "documentation": "<p>A document attribute or metadata field. To create custom document attributes, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/custom-attributes.html\">Custom attributes</a>.</p>"}, "DocumentAttributeCondition": {"type": "structure", "required": ["ConditionDocumentAttributeKey", "Operator"], "members": {"ConditionDocumentAttributeKey": {"shape": "DocumentAttributeKey", "documentation": "<p>The identifier of the document attribute used for the condition.</p> <p>For example, 'Source_URI' could be an identifier for the attribute or metadata field that contains source URIs associated with the documents.</p> <p>Amazon Kendra currently does not support <code>_document_body</code> as an attribute key used for the condition.</p>"}, "Operator": {"shape": "ConditionOperator", "documentation": "<p>The condition operator.</p> <p>For example, you can use 'Contains' to partially match a string.</p>"}, "ConditionOnValue": {"shape": "DocumentAttributeValue", "documentation": "<p>The value used by the operator.</p> <p>For example, you can specify the value 'financial' for strings in the 'Source_URI' field that partially match or contain this value.</p>"}}, "documentation": "<p>The condition used for the target document attribute or metadata field when ingesting documents into Amazon Kendra. You use this with <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_DocumentAttributeTarget.html\">DocumentAttributeTarget to apply the condition</a>.</p> <p>For example, you can create the 'Department' target field and have it prefill department names associated with the documents based on information in the 'Source_URI' field. Set the condition that if the 'Source_URI' field contains 'financial' in its URI value, then prefill the target field 'Department' with the target value 'Finance' for the document.</p> <p>Amazon Kendra cannot create a target field if it has not already been created as an index field. After you create your index field, you can create a document metadata field using <code>DocumentAttributeTarget</code>. Amazon Kendra then will map your newly created metadata field to your index field.</p>"}, "DocumentAttributeKey": {"type": "string", "max": 200, "min": 1, "pattern": "[a-zA-Z0-9_][a-zA-Z0-9_-]*"}, "DocumentAttributeKeyList": {"type": "list", "member": {"shape": "DocumentAttributeKey"}, "max": 100, "min": 1}, "DocumentAttributeList": {"type": "list", "member": {"shape": "DocumentAttribute"}}, "DocumentAttributeStringListValue": {"type": "list", "member": {"shape": "String"}}, "DocumentAttributeStringValue": {"type": "string", "max": 2048, "min": 1}, "DocumentAttributeTarget": {"type": "structure", "members": {"TargetDocumentAttributeKey": {"shape": "DocumentAttributeKey", "documentation": "<p>The identifier of the target document attribute or metadata field.</p> <p>For example, 'Department' could be an identifier for the target attribute or metadata field that includes the department names associated with the documents.</p>"}, "TargetDocumentAttributeValueDeletion": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to delete the existing target value for your specified target attribute key. You cannot create a target value and set this to <code>TRUE</code>. To create a target value (<code>TargetDocumentAttributeValue</code>), set this to <code>FALSE</code>.</p>"}, "TargetDocumentAttributeValue": {"shape": "DocumentAttributeValue", "documentation": "<p>The target value you want to create for the target attribute.</p> <p>For example, 'Finance' could be the target value for the target attribute key 'Department'.</p>"}}, "documentation": "<p>The target document attribute or metadata field you want to alter when ingesting documents into Amazon Kendra.</p> <p>For example, you can delete customer identification numbers associated with the documents, stored in the document metadata field called 'Customer_ID'. You set the target key as 'Customer_ID' and the deletion flag to <code>TRUE</code>. This removes all customer ID values in the field 'Customer_ID'. This would scrub personally identifiable information from each document's metadata.</p> <p>Amazon Kendra cannot create a target field if it has not already been created as an index field. After you create your index field, you can create a document metadata field using <code>DocumentAttributeTarget</code>. Amazon Kendra then will map your newly created metadata field to your index field.</p> <p>You can also use this with <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_DocumentAttributeCondition.html\">DocumentAttributeCondition</a>.</p>"}, "DocumentAttributeValue": {"type": "structure", "members": {"StringValue": {"shape": "DocumentAttributeStringValue", "documentation": "<p>A string, such as \"department\".</p>"}, "StringListValue": {"shape": "DocumentAttributeStringListValue", "documentation": "<p>A list of strings. The default maximum length or number of strings is 10.</p>"}, "LongValue": {"shape": "<PERSON>", "documentation": "<p>A long integer value.</p>"}, "DateValue": {"shape": "Timestamp", "documentation": "<p>A date expressed as an ISO 8601 string.</p> <p>It is important for the time zone to be included in the ISO 8601 date-time format. For example, 2012-03-25T12:30:10+01:00 is the ISO 8601 date-time format for March 25th 2012 at 12:30PM (plus 10 seconds) in Central European Time.</p>"}}, "documentation": "<p>The value of a document attribute. You can only provide one value for a document attribute.</p>"}, "DocumentAttributeValueCountPair": {"type": "structure", "members": {"DocumentAttributeValue": {"shape": "DocumentAttributeValue", "documentation": "<p>The value of the attribute/field. For example, \"HR\".</p>"}, "Count": {"shape": "Integer", "documentation": "<p>The number of documents in the response that have the attribute/field value for the key.</p>"}, "FacetResults": {"shape": "FacetResultList", "documentation": "<p>Contains the results of a document attribute/field that is a nested facet. A <code>FacetResult</code> contains the counts for each facet nested within a facet.</p> <p>For example, the document attribute or facet \"Department\" includes a value called \"Engineering\". In addition, the document attribute or facet \"SubDepartment\" includes the values \"Frontend\" and \"Backend\" for documents assigned to \"Engineering\". You can display nested facets in the search results so that documents can be searched not only by department but also by a sub department within a department. The counts for documents that belong to \"Frontend\" and \"Backend\" within \"Engineering\" are returned for a query.</p> <p/> <p/>"}}, "documentation": "<p>Provides the count of documents that match a particular document attribute or field when doing a faceted search.</p>"}, "DocumentAttributeValueCountPairList": {"type": "list", "member": {"shape": "DocumentAttributeValueCountPair"}}, "DocumentAttributeValueType": {"type": "string", "enum": ["STRING_VALUE", "STRING_LIST_VALUE", "LONG_VALUE", "DATE_VALUE"]}, "DocumentId": {"type": "string", "max": 2048, "min": 1}, "DocumentIdList": {"type": "list", "member": {"shape": "DocumentId"}, "max": 10, "min": 1}, "DocumentInfo": {"type": "structure", "required": ["DocumentId"], "members": {"DocumentId": {"shape": "DocumentId", "documentation": "<p>The identifier of the document.</p>"}, "Attributes": {"shape": "DocumentAttributeList", "documentation": "<p>Attributes that identify a specific version of a document to check.</p> <p>The only valid attributes are:</p> <ul> <li> <p>version</p> </li> <li> <p>datasourceId</p> </li> <li> <p>jobExecutionId</p> </li> </ul> <p>The attributes follow these rules:</p> <ul> <li> <p> <code>dataSourceId</code> and <code>jobExecutionId</code> must be used together.</p> </li> <li> <p> <code>version</code> is ignored if <code>dataSourceId</code> and <code>jobExecutionId</code> are not provided.</p> </li> <li> <p>If <code>dataSourceId</code> and <code>jobExecutionId</code> are provided, but <code>version</code> is not, the version defaults to \"0\".</p> </li> </ul>"}}, "documentation": "<p>Identifies a document for which to retrieve status information</p>"}, "DocumentInfoList": {"type": "list", "member": {"shape": "DocumentInfo"}, "max": 10, "min": 1}, "DocumentList": {"type": "list", "member": {"shape": "Document"}, "max": 10, "min": 1}, "DocumentMetadataBoolean": {"type": "boolean"}, "DocumentMetadataConfiguration": {"type": "structure", "required": ["Name", "Type"], "members": {"Name": {"shape": "DocumentMetadataConfigurationName", "documentation": "<p>The name of the index field.</p>"}, "Type": {"shape": "DocumentAttributeValueType", "documentation": "<p>The data type of the index field. </p>"}, "Relevance": {"shape": "Relevance", "documentation": "<p>Provides tuning parameters to determine how the field affects the search results.</p>"}, "Search": {"shape": "Search", "documentation": "<p>Provides information about how the field is used during a search.</p>"}}, "documentation": "<p>Specifies the properties, such as relevance tuning and searchability, of an index field.</p>"}, "DocumentMetadataConfigurationList": {"type": "list", "member": {"shape": "DocumentMetadataConfiguration"}, "max": 500, "min": 0}, "DocumentMetadataConfigurationName": {"type": "string", "max": 30, "min": 1}, "DocumentRelevanceConfiguration": {"type": "structure", "required": ["Name", "Relevance"], "members": {"Name": {"shape": "DocumentMetadataConfigurationName", "documentation": "<p>The name of the index field.</p>"}, "Relevance": {"shape": "Relevance", "documentation": "<p>Provides information for tuning the relevance of a field in a search. When a query includes terms that match the field, the results are given a boost in the response based on these tuning parameters.</p>"}}, "documentation": "<p>Overrides the document relevance properties of a custom index field.</p>"}, "DocumentRelevanceOverrideConfigurationList": {"type": "list", "member": {"shape": "DocumentRelevanceConfiguration"}, "max": 500, "min": 0}, "DocumentStatus": {"type": "string", "enum": ["NOT_FOUND", "PROCESSING", "INDEXED", "UPDATED", "FAILED", "UPDATE_FAILED"]}, "DocumentStatusList": {"type": "list", "member": {"shape": "Status"}}, "DocumentTitle": {"type": "string"}, "DocumentsMetadataConfiguration": {"type": "structure", "members": {"S3Prefix": {"shape": "S3ObjectKey", "documentation": "<p>A prefix used to filter metadata configuration files in the Amazon Web Services S3 bucket. The S3 bucket might contain multiple metadata files. Use <code>S3Prefix</code> to include only the desired metadata files.</p>"}}, "documentation": "<p>Document metadata files that contain information such as the document access control information, source URI, document author, and custom attributes. Each metadata file contains metadata about a single document.</p>"}, "Domain": {"type": "string", "max": 63, "min": 1, "pattern": "^(?!-)[A-Za-z0-9-].*(?<!-)$"}, "Duration": {"type": "string", "max": 10, "min": 1, "pattern": "[0-9]+[s]"}, "Endpoint": {"type": "string", "max": 2048, "min": 1, "pattern": "^\\P{C}*$"}, "EndpointType": {"type": "string", "enum": ["HOME"]}, "EnterpriseId": {"type": "string", "max": 64, "min": 1, "pattern": "^[A-Z0-9]*$"}, "EntityConfiguration": {"type": "structure", "required": ["EntityId", "EntityType"], "members": {"EntityId": {"shape": "EntityId", "documentation": "<p>The identifier of a user or group in your IAM Identity Center identity source. For example, a user ID could be an email.</p>"}, "EntityType": {"shape": "EntityType", "documentation": "<p>Specifies whether you are configuring a <code>User</code> or a <code>Group</code>.</p>"}}, "documentation": "<p>Provides the configuration information for users or groups in your IAM Identity Center identity source to grant access your Amazon Kendra experience.</p>"}, "EntityDisplayData": {"type": "structure", "members": {"UserName": {"shape": "NameType", "documentation": "<p>The name of the user.</p>"}, "GroupName": {"shape": "NameType", "documentation": "<p>The name of the group.</p>"}, "IdentifiedUserName": {"shape": "NameType", "documentation": "<p>The user name of the user.</p>"}, "FirstName": {"shape": "NameType", "documentation": "<p>The first name of the user.</p>"}, "LastName": {"shape": "NameType", "documentation": "<p>The last name of the user.</p>"}}, "documentation": "<p>Information about the user entity.</p>"}, "EntityFilter": {"type": "list", "member": {"shape": "AlfrescoEntity"}, "max": 3, "min": 1}, "EntityId": {"type": "string", "max": 47, "min": 1, "pattern": "^([0-9a-f]{10}-|)[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}$"}, "EntityIdsList": {"type": "list", "member": {"shape": "EntityId"}, "max": 25, "min": 1}, "EntityPersonaConfiguration": {"type": "structure", "required": ["EntityId", "<PERSON>a"], "members": {"EntityId": {"shape": "EntityId", "documentation": "<p>The identifier of a user or group in your IAM Identity Center identity source. For example, a user ID could be an email.</p>"}, "Persona": {"shape": "<PERSON>a", "documentation": "<p>The persona that defines the specific permissions of the user or group in your IAM Identity Center identity source. The available personas or access roles are <code>Owner</code> and <code>Viewer</code>. For more information on these personas, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/deploying-search-experience-no-code.html#access-search-experience\">Providing access to your search page</a>.</p>"}}, "documentation": "<p>Provides the configuration information for users or groups in your IAM Identity Center identity source for access to your Amazon Kendra experience. Specific permissions are defined for each user or group once they are granted access to your Amazon Kendra experience.</p>"}, "EntityPersonaConfigurationList": {"type": "list", "member": {"shape": "EntityPersonaConfiguration"}, "max": 25, "min": 1}, "EntityType": {"type": "string", "enum": ["USER", "GROUP"]}, "ErrorCode": {"type": "string", "enum": ["InternalError", "InvalidRequest"]}, "ErrorMessage": {"type": "string", "max": 2048, "min": 1, "pattern": "^\\P{C}*$"}, "ExcludeMimeTypesList": {"type": "list", "member": {"shape": "MimeType"}, "max": 30, "min": 0}, "ExcludeSharedDrivesList": {"type": "list", "member": {"shape": "SharedDriveId"}, "max": 100, "min": 0}, "ExcludeUserAccountsList": {"type": "list", "member": {"shape": "UserAccount"}, "max": 100, "min": 0}, "ExpandConfiguration": {"type": "structure", "members": {"MaxResultItemsToExpand": {"shape": "Integer", "documentation": "<p>The number of collapsed search result groups to expand. If you set this value to 10, for example, only the first 10 out of 100 result groups will have expand functionality. </p>"}, "MaxExpandedResultsPerItem": {"shape": "Integer", "documentation": "<p>The number of expanded results to show per collapsed primary document. For instance, if you set this value to 3, then at most 3 results per collapsed group will be displayed.</p>"}}, "documentation": "<p>Specifies the configuration information needed to customize how collapsed search result groups expand.</p>"}, "ExpandedResultItem": {"type": "structure", "members": {"Id": {"shape": "ResultId", "documentation": "<p>The identifier for the expanded result.</p>"}, "DocumentId": {"shape": "DocumentId", "documentation": "<p>The idenitifier of the document.</p>"}, "DocumentTitle": {"shape": "TextWithHighlights"}, "DocumentExcerpt": {"shape": "TextWithHighlights"}, "DocumentURI": {"shape": "Url", "documentation": "<p>The URI of the original location of the document.</p>"}, "DocumentAttributes": {"shape": "DocumentAttributeList", "documentation": "<p>An array of document attributes assigned to a document in the search results. For example, the document author (\"_author\") or the source URI (\"_source_uri\") of the document.</p>"}}, "documentation": "<p> A single expanded result in a collapsed group of search results.</p> <p>An expanded result item contains information about an expanded result document within a collapsed group of search results. This includes the original location of the document, a list of attributes assigned to the document, and relevant text from the document that satisfies the query. </p>"}, "ExpandedResultList": {"type": "list", "member": {"shape": "ExpandedResultItem"}}, "ExperienceConfiguration": {"type": "structure", "members": {"ContentSourceConfiguration": {"shape": "ContentSourceConfiguration", "documentation": "<p>The identifiers of your data sources and FAQs. Or, you can specify that you want to use documents indexed via the <code>BatchPutDocument</code> API. This is the content you want to use for your Amazon Kendra experience.</p>"}, "UserIdentityConfiguration": {"shape": "UserIdentityConfiguration", "documentation": "<p>The IAM Identity Center field name that contains the identifiers of your users, such as their emails.</p>"}}, "documentation": "<p>Provides the configuration information for your Amazon Kendra experience. This includes the data source IDs and/or FAQ IDs, and user or group information to grant access to your Amazon Kendra experience.</p>"}, "ExperienceEndpoint": {"type": "structure", "members": {"EndpointType": {"shape": "EndpointType", "documentation": "<p>The type of endpoint for your Amazon Kendra experience. The type currently available is <code>HOME</code>, which is a unique and fully hosted URL to the home page of your Amazon Kendra experience.</p>"}, "Endpoint": {"shape": "Endpoint", "documentation": "<p>The endpoint of your Amazon Kendra experience.</p>"}}, "documentation": "<p>Provides the configuration information for the endpoint for your Amazon Kendra experience.</p>"}, "ExperienceEndpoints": {"type": "list", "member": {"shape": "ExperienceEndpoint"}, "max": 2, "min": 1}, "ExperienceEntitiesSummary": {"type": "structure", "members": {"EntityId": {"shape": "EntityId", "documentation": "<p>The identifier of a user or group in your IAM Identity Center identity source. For example, a user ID could be an email.</p>"}, "EntityType": {"shape": "EntityType", "documentation": "<p>Shows the type as <code>User</code> or <code>Group</code>.</p>"}, "DisplayData": {"shape": "EntityDisplayData", "documentation": "<p>Information about the user entity.</p>"}}, "documentation": "<p>Summary information for users or groups in your IAM Identity Center identity source with granted access to your Amazon Kendra experience. You can create an Amazon Kendra experience such as a search application. For more information on creating a search application experience, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/deploying-search-experience-no-code.html\">Building a search experience with no code</a>.</p>"}, "ExperienceEntitiesSummaryList": {"type": "list", "member": {"shape": "ExperienceEntitiesSummary"}}, "ExperienceId": {"type": "string", "max": 36, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_-]*"}, "ExperienceName": {"type": "string", "max": 1000, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_-]*"}, "ExperienceStatus": {"type": "string", "enum": ["CREATING", "ACTIVE", "DELETING", "FAILED"]}, "ExperiencesSummary": {"type": "structure", "members": {"Name": {"shape": "ExperienceName", "documentation": "<p>The name of your Amazon Kendra experience.</p>"}, "Id": {"shape": "ExperienceId", "documentation": "<p>The identifier of your Amazon Kendra experience.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when your Amazon Kendra experience was created.</p>"}, "Status": {"shape": "ExperienceStatus", "documentation": "<p>The processing status of your Amazon Kendra experience.</p>"}, "Endpoints": {"shape": "ExperienceEndpoints", "documentation": "<p>The endpoint URLs for your Amazon Kendra experiences. The URLs are unique and fully hosted by Amazon Web Services.</p>"}}, "documentation": "<p>Summary information for your Amazon Kendra experience. You can create an Amazon Kendra experience such as a search application. For more information on creating a search application experience, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/deploying-search-experience-no-code.html\">Building a search experience with no code</a>.</p>"}, "ExperiencesSummaryList": {"type": "list", "member": {"shape": "ExperiencesSummary"}}, "Facet": {"type": "structure", "members": {"DocumentAttributeKey": {"shape": "DocumentAttributeKey", "documentation": "<p>The unique key for the document attribute.</p>"}, "Facets": {"shape": "FacetList", "documentation": "<p>An array of document attributes that are nested facets within a facet.</p> <p>For example, the document attribute or facet \"Department\" includes a value called \"Engineering\". In addition, the document attribute or facet \"SubDepartment\" includes the values \"Frontend\" and \"Backend\" for documents assigned to \"Engineering\". You can display nested facets in the search results so that documents can be searched not only by department but also by a sub department within a department. This helps your users further narrow their search.</p> <p>You can only have one nested facet within a facet. If you want to increase this limit, contact <a href=\"http://aws.amazon.com/contact-us/\">Support</a>.</p>"}, "MaxResults": {"shape": "TopDocumentAttributeValueCountPairsSize", "documentation": "<p>Maximum number of facet values per facet. The default is 10. You can use this to limit the number of facet values to less than 10. If you want to increase the default, contact <a href=\"http://aws.amazon.com/contact-us/\">Support</a>.</p>"}}, "documentation": "<p>Information about a document attribute or field. You can use document attributes as facets.</p> <p>For example, the document attribute or facet \"Department\" includes the values \"HR\", \"Engineering\", and \"Accounting\". You can display these values in the search results so that documents can be searched by department.</p> <p>You can display up to 10 facet values per facet for a query. If you want to increase this limit, contact <a href=\"http://aws.amazon.com/contact-us/\">Support</a>.</p>"}, "FacetList": {"type": "list", "member": {"shape": "Facet"}}, "FacetResult": {"type": "structure", "members": {"DocumentAttributeKey": {"shape": "DocumentAttributeKey", "documentation": "<p>The key for the facet values. This is the same as the <code>DocumentAttributeKey</code> provided in the query.</p>"}, "DocumentAttributeValueType": {"shape": "DocumentAttributeValueType", "documentation": "<p>The data type of the facet value. This is the same as the type defined for the index field when it was created.</p>"}, "DocumentAttributeValueCountPairs": {"shape": "DocumentAttributeValueCountPairList", "documentation": "<p>An array of key/value pairs, where the key is the value of the attribute and the count is the number of documents that share the key value.</p>"}}, "documentation": "<p>The facet values for the documents in the response.</p>"}, "FacetResultList": {"type": "list", "member": {"shape": "FacetResult"}}, "FailedEntity": {"type": "structure", "members": {"EntityId": {"shape": "EntityId", "documentation": "<p>The identifier of the user or group in your IAM Identity Center identity source. For example, a user ID could be an email.</p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>The reason the user or group in your IAM Identity Center identity source failed to properly configure with your Amazon Kendra experience.</p>"}}, "documentation": "<p>Information on the users or groups in your IAM Identity Center identity source that failed to properly configure with your Amazon Kendra experience.</p>"}, "FailedEntityList": {"type": "list", "member": {"shape": "FailedEntity"}, "max": 25, "min": 1}, "FailureReason": {"type": "string", "max": 2048, "min": 1, "pattern": "^\\P{C}*$"}, "FaqFileFormat": {"type": "string", "enum": ["CSV", "CSV_WITH_HEADER", "JSON"]}, "FaqId": {"type": "string", "max": 100, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_-]*"}, "FaqIdsList": {"type": "list", "member": {"shape": "FaqId"}, "max": 100, "min": 1}, "FaqName": {"type": "string", "max": 100, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_-]*"}, "FaqStatistics": {"type": "structure", "required": ["IndexedQuestionAnswersCount"], "members": {"IndexedQuestionAnswersCount": {"shape": "IndexedQuestionAnswersCount", "documentation": "<p>The total number of FAQ questions and answers contained in the index.</p>"}}, "documentation": "<p>Provides statistical information about the FAQ questions and answers contained in an index.</p>"}, "FaqStatus": {"type": "string", "enum": ["CREATING", "UPDATING", "ACTIVE", "DELETING", "FAILED"]}, "FaqSummary": {"type": "structure", "members": {"Id": {"shape": "FaqId", "documentation": "<p>The identifier of the FAQ.</p>"}, "Name": {"shape": "FaqName", "documentation": "<p>The name that you assigned the FAQ when you created or updated the FAQ.</p>"}, "Status": {"shape": "FaqStatus", "documentation": "<p>The current status of the FAQ. When the status is <code>ACTIVE</code> the FAQ is ready for use.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the FAQ was created.</p>"}, "UpdatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the FAQ was last updated.</p>"}, "FileFormat": {"shape": "FaqFileFormat", "documentation": "<p>The file type used to create the FAQ. </p>"}, "LanguageCode": {"shape": "LanguageCode", "documentation": "<p>The code for a language. This shows a supported language for the FAQ document as part of the summary information for FAQs. English is supported by default. For more information on supported languages, including their codes, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/in-adding-languages.html\">Adding documents in languages other than English</a>.</p>"}}, "documentation": "<p>Summary information for frequently asked questions and answers included in an index.</p>"}, "FaqSummaryItems": {"type": "list", "member": {"shape": "FaqSummary"}}, "FeaturedDocument": {"type": "structure", "members": {"Id": {"shape": "DocumentId", "documentation": "<p>The identifier of the document to feature in the search results. You can use the <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_Query.html\">Query</a> API to search for specific documents with their document IDs included in the result items, or you can use the console.</p>"}}, "documentation": "<p>A featured document. This document is displayed at the top of the search results page, placed above all other results for certain queries. If there's an exact match of a query, then the document is featured in the search results.</p>"}, "FeaturedDocumentList": {"type": "list", "member": {"shape": "FeaturedDocument"}}, "FeaturedDocumentMissing": {"type": "structure", "members": {"Id": {"shape": "DocumentId", "documentation": "<p>The identifier of the document that doesn't exist but you have specified as a featured document.</p>"}}, "documentation": "<p>A document ID doesn't exist but you have specified as a featured document. Amazon Kendra cannot feature the document if it doesn't exist in the index. You can check the status of a document and its ID or check for documents with status errors using the <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_BatchGetDocumentStatus.html\">BatchGetDocumentStatus</a> API.</p>"}, "FeaturedDocumentMissingList": {"type": "list", "member": {"shape": "FeaturedDocumentMissing"}}, "FeaturedDocumentWithMetadata": {"type": "structure", "members": {"Id": {"shape": "DocumentId", "documentation": "<p>The identifier of the featured document with its metadata. You can use the <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_Query.html\">Query</a> API to search for specific documents with their document IDs included in the result items, or you can use the console.</p>"}, "Title": {"shape": "String", "documentation": "<p>The main title of the featured document.</p>"}, "URI": {"shape": "Url", "documentation": "<p>The source URI location of the featured document.</p>"}}, "documentation": "<p>A featured document with its metadata information. This document is displayed at the top of the search results page, placed above all other results for certain queries. If there's an exact match of a query, then the document is featured in the search results.</p>"}, "FeaturedDocumentWithMetadataList": {"type": "list", "member": {"shape": "FeaturedDocumentWithMetadata"}}, "FeaturedResultsConflictException": {"type": "structure", "members": {"Message": {"shape": "String", "documentation": "<p>An explanation for the conflicting queries.</p>"}, "ConflictingItems": {"shape": "ConflictingItems", "documentation": "<p>A list of the conflicting queries, including the query text, the name for the featured results set, and the identifier of the featured results set.</p>"}}, "documentation": "<p>An error message with a list of conflicting queries used across different sets of featured results. This occurred with the request for a new featured results set. Check that the queries you specified for featured results are unique per featured results set for each index.</p>", "exception": true}, "FeaturedResultsItem": {"type": "structure", "members": {"Id": {"shape": "ResultId", "documentation": "<p>The identifier of the featured result.</p>"}, "Type": {"shape": "QueryResultType", "documentation": "<p>The type of document within the featured result response. For example, a response could include a question-answer type that's relevant to the query.</p>"}, "AdditionalAttributes": {"shape": "AdditionalResultAttributeList", "documentation": "<p>One or more additional attributes associated with the featured result.</p>"}, "DocumentId": {"shape": "DocumentId", "documentation": "<p>The identifier of the featured document.</p>"}, "DocumentTitle": {"shape": "TextWithHighlights"}, "DocumentExcerpt": {"shape": "TextWithHighlights"}, "DocumentURI": {"shape": "Url", "documentation": "<p>The source URI location of the featured document.</p>"}, "DocumentAttributes": {"shape": "DocumentAttributeList", "documentation": "<p>An array of document attributes assigned to a featured document in the search results. For example, the document author (<code>_author</code>) or the source URI (<code>_source_uri</code>) of the document.</p>"}, "FeedbackToken": {"shape": "FeedbackToken", "documentation": "<p>A token that identifies a particular featured result from a particular query. Use this token to provide click-through feedback for the result. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/submitting-feedback.html\">Submitting feedback</a>.</p>"}}, "documentation": "<p>A single featured result item. A featured result is displayed at the top of the search results page, placed above all other results for certain queries. If there's an exact match of a query, then certain documents are featured in the search results.</p>"}, "FeaturedResultsItemList": {"type": "list", "member": {"shape": "FeaturedResultsItem"}}, "FeaturedResultsSet": {"type": "structure", "members": {"FeaturedResultsSetId": {"shape": "FeaturedResultsSetId", "documentation": "<p>The identifier of the set of featured results.</p>"}, "FeaturedResultsSetName": {"shape": "FeaturedResultsSetName", "documentation": "<p>The name for the set of featured results.</p>"}, "Description": {"shape": "FeaturedResultsSetDescription", "documentation": "<p>The description for the set of featured results.</p>"}, "Status": {"shape": "FeaturedResultsSetStatus", "documentation": "<p>The current status of the set of featured results. When the value is <code>ACTIVE</code>, featured results are ready for use. You can still configure your settings before setting the status to <code>ACTIVE</code>. You can set the status to <code>ACTIVE</code> or <code>INACTIVE</code> using the <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_UpdateFeaturedResultsSet.html\">UpdateFeaturedResultsSet</a> API. The queries you specify for featured results must be unique per featured results set for each index, whether the status is <code>ACTIVE</code> or <code>INACTIVE</code>.</p>"}, "QueryTexts": {"shape": "QueryTextList", "documentation": "<p>The list of queries for featuring results.</p> <p>Specific queries are mapped to specific documents for featuring in the results. If a query contains an exact match, then one or more specific documents are featured in the results. The exact match applies to the full query. For example, if you only specify '<PERSON>', queries such as 'How does k<PERSON> semantically rank results?' will not render the featured results. Featured results are designed for specific queries, rather than queries that are too broad in scope.</p>"}, "FeaturedDocuments": {"shape": "FeaturedDocumentList", "documentation": "<p>The list of document IDs for the documents you want to feature at the top of the search results page. You can use the <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_Query.html\">Query</a> API to search for specific documents with their document IDs included in the result items, or you can use the console.</p> <p>You can add up to four featured documents. You can request to increase this limit by contacting <a href=\"http://aws.amazon.com/contact-us/\">Support</a>.</p> <p>Specific queries are mapped to specific documents for featuring in the results. If a query contains an exact match, then one or more specific documents are featured in the results. The exact match applies to the full query. For example, if you only specify 'Kendra', queries such as 'How does kendra semantically rank results?' will not render the featured results. Featured results are designed for specific queries, rather than queries that are too broad in scope.</p>"}, "LastUpdatedTimestamp": {"shape": "<PERSON>", "documentation": "<p>The Unix timestamp when the set of featured results was last updated.</p>"}, "CreationTimestamp": {"shape": "<PERSON>", "documentation": "<p>The Unix timestamp when the set of featured results was created.</p>"}}, "documentation": "<p>A set of featured results that are displayed at the top of your search results. Featured results are placed above all other results for certain queries. If there's an exact match of a query, then one or more specific documents are featured in the search results.</p>"}, "FeaturedResultsSetDescription": {"type": "string", "max": 1000, "min": 0, "pattern": "^\\P{C}*$"}, "FeaturedResultsSetId": {"type": "string", "max": 36, "min": 36, "pattern": "^[a-zA-Z-0-9]*"}, "FeaturedResultsSetIdList": {"type": "list", "member": {"shape": "FeaturedResultsSetId"}, "max": 50, "min": 1}, "FeaturedResultsSetName": {"type": "string", "max": 1000, "min": 1, "pattern": "[a-zA-Z0-9][ a-zA-Z0-9_-]*"}, "FeaturedResultsSetStatus": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}, "FeaturedResultsSetSummary": {"type": "structure", "members": {"FeaturedResultsSetId": {"shape": "FeaturedResultsSetId", "documentation": "<p>The identifier of the set of featured results.</p>"}, "FeaturedResultsSetName": {"shape": "FeaturedResultsSetName", "documentation": "<p>The name for the set of featured results.</p>"}, "Status": {"shape": "FeaturedResultsSetStatus", "documentation": "<p>The current status of the set of featured results. When the value is <code>ACTIVE</code>, featured results are ready for use. You can still configure your settings before setting the status to <code>ACTIVE</code>. You can set the status to <code>ACTIVE</code> or <code>INACTIVE</code> using the <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_UpdateFeaturedResultsSet.html\">UpdateFeaturedResultsSet</a> API. The queries you specify for featured results must be unique per featured results set for each index, whether the status is <code>ACTIVE</code> or <code>INACTIVE</code>.</p>"}, "LastUpdatedTimestamp": {"shape": "<PERSON>", "documentation": "<p>The Unix timestamp when the set of featured results was last updated.</p>"}, "CreationTimestamp": {"shape": "<PERSON>", "documentation": "<p>The Unix timestamp when the set of featured results was created.</p>"}}, "documentation": "<p>Summary information for a set of featured results. Featured results are placed above all other results for certain queries. If there's an exact match of a query, then one or more specific documents are featured in the search results.</p>"}, "FeaturedResultsSetSummaryItems": {"type": "list", "member": {"shape": "FeaturedResultsSetSummary"}}, "FeedbackToken": {"type": "string", "max": 2048, "min": 1, "pattern": "^\\P{C}*.\\P{C}*$"}, "FileSystemId": {"type": "string", "max": 21, "min": 11, "pattern": "^(fs-[0-9a-f]{8,})$"}, "FolderId": {"type": "string", "max": 500, "min": 1}, "FolderIdList": {"type": "list", "member": {"shape": "FolderId"}}, "FsxConfiguration": {"type": "structure", "required": ["FileSystemId", "FileSystemType", "VpcConfiguration"], "members": {"FileSystemId": {"shape": "FileSystemId", "documentation": "<p>The identifier of the Amazon FSx file system.</p> <p>You can find your file system ID on the file system dashboard in the Amazon FSx console. For information on how to create a file system in Amazon FSx console, using Windows File Server as an example, see <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/getting-started-step1.html\">Amazon FSx Getting started guide</a>.</p>"}, "FileSystemType": {"shape": "FsxFileSystemType", "documentation": "<p>The Amazon FSx file system type. Windows is currently the only supported type.</p>"}, "VpcConfiguration": {"shape": "DataSourceVpcConfiguration", "documentation": "<p>Configuration information for an Amazon Virtual Private Cloud to connect to your Amazon FSx. Your Amazon FSx instance must reside inside your VPC.</p>"}, "SecretArn": {"shape": "SecretArn", "documentation": "<p>The Amazon Resource Name (ARN) of an Secrets Manager secret that contains the key-value pairs required to connect to your Amazon FSx file system. Windows is currently the only supported type. The secret must contain a JSON structure with the following keys:</p> <ul> <li> <p>username—The Active Directory user name, along with the Domain Name System (DNS) domain name. For example, <i><EMAIL></i>. The Active Directory user account must have read and mounting access to the Amazon FSx file system for Windows.</p> </li> <li> <p>password—The password of the Active Directory user account with read and mounting access to the Amazon FSx Windows file system.</p> </li> </ul>"}, "InclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to include certain files in your Amazon FSx file system. Files that match the patterns are included in the index. Files that don't match the patterns are excluded from the index. If a file matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the file isn't included in the index.</p>"}, "ExclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to exclude certain files in your Amazon FSx file system. Files that match the patterns are excluded from the index. Files that don't match the patterns are included in the index. If a file matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the file isn't included in the index.</p>"}, "FieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map Amazon FSx data source attributes or field names to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Amazon FSx fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The Amazon FSx data source field names must exist in your Amazon FSx custom metadata.</p>"}}, "documentation": "<p>Provides the configuration information to connect to Amazon FSx as your data source.</p>"}, "FsxFileSystemType": {"type": "string", "enum": ["WINDOWS"]}, "GetQuerySuggestionsRequest": {"type": "structure", "required": ["IndexId", "QueryText"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index you want to get query suggestions from.</p>"}, "QueryText": {"shape": "SuggestionQueryText", "documentation": "<p>The text of a user's query to generate query suggestions.</p> <p>A query is suggested if the query prefix matches what a user starts to type as their query.</p> <p>Amazon Kendra does not show any suggestions if a user types fewer than two characters or more than 60 characters. A query must also have at least one search result and contain at least one word of more than four characters.</p>"}, "MaxSuggestionsCount": {"shape": "Integer", "documentation": "<p>The maximum number of query suggestions you want to show to your users.</p>"}, "SuggestionTypes": {"shape": "SuggestionTypes", "documentation": "<p>The suggestions type to base query suggestions on. The suggestion types are query history or document fields/attributes. You can set one type or the other.</p> <p>If you set query history as your suggestions type, Amazon Kendra suggests queries relevant to your users based on popular queries in the query history.</p> <p>If you set document fields/attributes as your suggestions type, Amazon Kendra suggests queries relevant to your users based on the contents of document fields.</p>"}, "AttributeSuggestionsConfig": {"shape": "AttributeSuggestionsGetConfig", "documentation": "<p>Configuration information for the document fields/attributes that you want to base query suggestions on.</p>"}}}, "GetQuerySuggestionsResponse": {"type": "structure", "members": {"QuerySuggestionsId": {"shape": "QuerySuggestionsId", "documentation": "<p>The identifier for a list of query suggestions for an index.</p>"}, "Suggestions": {"shape": "SuggestionList", "documentation": "<p>A list of query suggestions for an index.</p>"}}}, "GetSnapshotsRequest": {"type": "structure", "required": ["IndexId", "Interval", "MetricType"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index to get search metrics data.</p>"}, "Interval": {"shape": "Interval", "documentation": "<p>The time interval or time window to get search metrics data. The time interval uses the time zone of your index. You can view data in the following time windows:</p> <ul> <li> <p> <code>THIS_WEEK</code>: The current week, starting on the Sunday and ending on the day before the current date.</p> </li> <li> <p> <code>ONE_WEEK_AGO</code>: The previous week, starting on the Sunday and ending on the following Saturday.</p> </li> <li> <p> <code>TWO_WEEKS_AGO</code>: The week before the previous week, starting on the Sunday and ending on the following Saturday.</p> </li> <li> <p> <code>THIS_MONTH</code>: The current month, starting on the first day of the month and ending on the day before the current date.</p> </li> <li> <p> <code>ONE_MONTH_AGO</code>: The previous month, starting on the first day of the month and ending on the last day of the month.</p> </li> <li> <p> <code>TWO_MONTHS_AGO</code>: The month before the previous month, starting on the first day of the month and ending on last day of the month.</p> </li> </ul>"}, "MetricType": {"shape": "MetricType", "documentation": "<p>The metric you want to retrieve. You can specify only one metric per call.</p> <p>For more information about the metrics you can view, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/search-analytics.html\">Gaining insights with search analytics</a>.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the previous response was incomplete (because there is more data to retrieve), Amazon Kendra returns a pagination token in the response. You can use this pagination token to retrieve the next set of search metrics data.</p>"}, "MaxResults": {"shape": "Integer", "documentation": "<p>The maximum number of returned data for the metric.</p>"}}}, "GetSnapshotsResponse": {"type": "structure", "members": {"SnapShotTimeFilter": {"shape": "TimeRange", "documentation": "<p>The Unix timestamp for the beginning and end of the time window for the search metrics data.</p>"}, "SnapshotsDataHeader": {"shape": "SnapshotsDataHeaderFields", "documentation": "<p>The column headers for the search metrics data.</p>"}, "SnapshotsData": {"shape": "SnapshotsDataRecords", "documentation": "<p>The search metrics data. The data returned depends on the metric type you requested.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Kendra returns this token, which you can use in a later request to retrieve the next set of search metrics data.</p>"}}}, "GitHubConfiguration": {"type": "structure", "required": ["SecretArn"], "members": {"SaaSConfiguration": {"shape": "SaaSConfiguration", "documentation": "<p>Configuration information to connect to GitHub Enterprise Cloud (SaaS).</p>"}, "OnPremiseConfiguration": {"shape": "OnPremiseConfiguration", "documentation": "<p>Configuration information to connect to GitHub Enterprise Server (on premises).</p>"}, "Type": {"shape": "Type", "documentation": "<p>The type of GitHub service you want to connect to—GitHub Enterprise Cloud (SaaS) or GitHub Enterprise Server (on premises).</p>"}, "SecretArn": {"shape": "SecretArn", "documentation": "<p>The Amazon Resource Name (ARN) of an Secrets Manager secret that contains the key-value pairs required to connect to your GitHub. The secret must contain a JSON structure with the following keys:</p> <ul> <li> <p>personalToken—The access token created in GitHub. For more information on creating a token in GitHub, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/data-source-github.html\">Using a GitHub data source</a>.</p> </li> </ul>"}, "UseChangeLog": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to use the GitHub change log to determine which documents require updating in the index. Depending on the GitHub change log's size, it may take longer for Amazon Kendra to use the change log than to scan all of your documents in GitHub.</p>"}, "GitHubDocumentCrawlProperties": {"shape": "GitHubDocumentCrawlProperties", "documentation": "<p>Configuration information to include certain types of GitHub content. You can configure to index repository files only, or also include issues and pull requests, comments, and comment attachments.</p>"}, "RepositoryFilter": {"shape": "RepositoryNames", "documentation": "<p>A list of names of the specific repositories you want to index.</p>"}, "InclusionFolderNamePatterns": {"shape": "StringList", "documentation": "<p>A list of regular expression patterns to include certain folder names in your GitHub repository or repositories. Folder names that match the patterns are included in the index. Folder names that don't match the patterns are excluded from the index. If a folder matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the folder isn't included in the index.</p>"}, "InclusionFileTypePatterns": {"shape": "StringList", "documentation": "<p>A list of regular expression patterns to include certain file types in your GitHub repository or repositories. File types that match the patterns are included in the index. File types that don't match the patterns are excluded from the index. If a file matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the file isn't included in the index.</p>"}, "InclusionFileNamePatterns": {"shape": "StringList", "documentation": "<p>A list of regular expression patterns to include certain file names in your GitHub repository or repositories. File names that match the patterns are included in the index. File names that don't match the patterns are excluded from the index. If a file matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the file isn't included in the index.</p>"}, "ExclusionFolderNamePatterns": {"shape": "StringList", "documentation": "<p>A list of regular expression patterns to exclude certain folder names in your GitHub repository or repositories. Folder names that match the patterns are excluded from the index. Folder names that don't match the patterns are included in the index. If a folder matches both an exclusion and inclusion pattern, the exclusion pattern takes precedence and the folder isn't included in the index.</p>"}, "ExclusionFileTypePatterns": {"shape": "StringList", "documentation": "<p>A list of regular expression patterns to exclude certain file types in your GitHub repository or repositories. File types that match the patterns are excluded from the index. File types that don't match the patterns are included in the index. If a file matches both an exclusion and inclusion pattern, the exclusion pattern takes precedence and the file isn't included in the index.</p>"}, "ExclusionFileNamePatterns": {"shape": "StringList", "documentation": "<p>A list of regular expression patterns to exclude certain file names in your GitHub repository or repositories. File names that match the patterns are excluded from the index. File names that don't match the patterns are included in the index. If a file matches both an exclusion and inclusion pattern, the exclusion pattern takes precedence and the file isn't included in the index.</p>"}, "VpcConfiguration": {"shape": "DataSourceVpcConfiguration", "documentation": "<p>Configuration information of an Amazon Virtual Private Cloud to connect to your GitHub. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/vpc-configuration.html\">Configuring a VPC</a>.</p>"}, "GitHubRepositoryConfigurationFieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map GitHub repository attributes or field names to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to GitHub fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The GitHub data source field names must exist in your GitHub custom metadata.</p>"}, "GitHubCommitConfigurationFieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map attributes or field names of GitHub commits to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to GitHub fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The GitHub data source field names must exist in your GitHub custom metadata.</p>"}, "GitHubIssueDocumentConfigurationFieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map attributes or field names of GitHub issues to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to GitHub fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The GitHub data source field names must exist in your GitHub custom metadata.</p>"}, "GitHubIssueCommentConfigurationFieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map attributes or field names of GitHub issue comments to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to GitHub fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The GitHub data source field names must exist in your GitHub custom metadata.</p>"}, "GitHubIssueAttachmentConfigurationFieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map attributes or field names of GitHub issue attachments to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to GitHub fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The GitHub data source field names must exist in your GitHub custom metadata.</p>"}, "GitHubPullRequestCommentConfigurationFieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map attributes or field names of GitHub pull request comments to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to GitHub fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The GitHub data source field names must exist in your GitHub custom metadata.</p>"}, "GitHubPullRequestDocumentConfigurationFieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map attributes or field names of GitHub pull requests to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to GitHub fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The GitHub data source field names must exist in your GitHub custom metadata.</p>"}, "GitHubPullRequestDocumentAttachmentConfigurationFieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map attributes or field names of GitHub pull request attachments to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to GitHub fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The GitHub data source field names must exist in your GitHub custom metadata.</p>"}}, "documentation": "<p>Provides the configuration information to connect to GitHub as your data source.</p>"}, "GitHubDocumentCrawlProperties": {"type": "structure", "members": {"CrawlRepositoryDocuments": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to index all files with a repository.</p>"}, "CrawlIssue": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to index all issues within a repository.</p>"}, "CrawlIssueComment": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to index all comments on issues.</p>"}, "CrawlIssueCommentAttachment": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to include all comment attachments for issues.</p>"}, "CrawlPullRequest": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to index all pull requests within a repository.</p>"}, "CrawlPullRequestComment": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to index all comments on pull requests.</p>"}, "CrawlPullRequestCommentAttachment": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to include all comment attachments for pull requests.</p>"}}, "documentation": "<p>Provides the configuration information to include certain types of GitHub content. You can configure to index repository files only, or also include issues and pull requests, comments, and comment attachments.</p>"}, "GoogleDriveConfiguration": {"type": "structure", "required": ["SecretArn"], "members": {"SecretArn": {"shape": "SecretArn", "documentation": "<p>The Amazon Resource Name (ARN) of a Secrets Managersecret that contains the credentials required to connect to Google Drive. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/data-source-google-drive.html\">Using a Google Workspace Drive data source</a>.</p>"}, "InclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to include certain items in your Google Drive, including shared drives and users' My Drives. Items that match the patterns are included in the index. Items that don't match the patterns are excluded from the index. If an item matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the item isn't included in the index.</p>"}, "ExclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to exclude certain items in your Google Drive, including shared drives and users' My Drives. Items that match the patterns are excluded from the index. Items that don't match the patterns are included in the index. If an item matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the item isn't included in the index.</p>"}, "FieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>Maps Google Drive data source attributes or field names to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Google Drive fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The Google Drive data source field names must exist in your Google Drive custom metadata.</p>"}, "ExcludeMimeTypes": {"shape": "ExcludeMimeTypesList", "documentation": "<p>A list of MIME types to exclude from the index. All documents matching the specified MIME type are excluded. </p> <p>For a list of MIME types, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/data-source-google-drive.html\">Using a Google Workspace Drive data source</a>.</p>"}, "ExcludeUserAccounts": {"shape": "ExcludeUserAccountsList", "documentation": "<p>A list of email addresses of the users. Documents owned by these users are excluded from the index. Documents shared with excluded users are indexed unless they are excluded in another way.</p>"}, "ExcludeSharedDrives": {"shape": "ExcludeSharedDrivesList", "documentation": "<p>A list of identifiers or shared drives to exclude from the index. All files and folders stored on the shared drive are excluded.</p>"}}, "documentation": "<p>Provides the configuration information to connect to Google Drive as your data source.</p>"}, "GroupAttributeField": {"type": "string", "max": 100, "min": 1, "pattern": "^\\P{C}*$"}, "GroupId": {"type": "string", "max": 1024, "min": 1, "pattern": "^\\P{C}*$"}, "GroupMembers": {"type": "structure", "members": {"MemberGroups": {"shape": "MemberGroups", "documentation": "<p>A list of sub groups that belong to a group. For example, the sub groups \"Research\", \"Engineering\", and \"Sales and Marketing\" all belong to the group \"Company\".</p>"}, "MemberUsers": {"shape": "MemberUsers", "documentation": "<p>A list of users that belong to a group. For example, a list of interns all belong to the \"Interns\" group.</p>"}, "S3PathforGroupMembers": {"shape": "S3Path", "documentation": "<p>If you have more than 1000 users and/or sub groups for a single group, you need to provide the path to the S3 file that lists your users and sub groups for a group. Your sub groups can contain more than 1000 users, but the list of sub groups that belong to a group (and/or users) must be no more than 1000.</p> <p>You can download this <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/samples/group_members.zip\">example S3 file</a> that uses the correct format for listing group members. Note, <code>dataSourceId</code> is optional. The value of <code>type</code> for a group is always <code>GROUP</code> and for a user it is always <code>USER</code>.</p>"}}, "documentation": "<p>A list of users or sub groups that belong to a group. This is useful for user context filtering, where search results are filtered based on the user or their group access to documents.</p>"}, "GroupOrderingIdSummaries": {"type": "list", "member": {"shape": "GroupOrderingIdSummary"}, "max": 10}, "GroupOrderingIdSummary": {"type": "structure", "members": {"Status": {"shape": "PrincipalMappingStatus", "documentation": "<p>The current processing status of actions for mapping users to their groups. The status can be either <code>PROCESSING</code>, <code>SUCCEEDED</code>, <code>DELETING</code>, <code>DELETED</code>, or <code>FAILED</code>.</p>"}, "LastUpdatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when an action was last updated. An action can be a <code>PUT</code> or <code>DELETE</code> action for mapping users to their groups.</p>"}, "ReceivedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when an action was received by Amazon Kendra. An action can be a <code>PUT</code> or <code>DELETE</code> action for mapping users to their groups.</p>"}, "OrderingId": {"shape": "PrincipalOrderingId", "documentation": "<p>The order in which actions should complete processing. An action can be a <code>PUT</code> or <code>DELETE</code> action for mapping users to their groups.</p>"}, "FailureReason": {"shape": "FailureReason", "documentation": "<p>The reason an action could not be processed. An action can be a <code>PUT</code> or <code>DELETE</code> action for mapping users to their groups.</p>"}}, "documentation": "<p>Summary information on the processing of <code>PUT</code> and <code>DELETE</code> actions for mapping users to their groups.</p>"}, "GroupSummary": {"type": "structure", "members": {"GroupId": {"shape": "GroupId", "documentation": "<p>The identifier of the group you want group summary information on.</p>"}, "OrderingId": {"shape": "PrincipalOrderingId", "documentation": "<p>The timestamp identifier used for the latest <code>PUT</code> or <code>DELETE</code> action.</p>"}}, "documentation": "<p>Summary information for groups.</p>"}, "Groups": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON>"}, "max": 2048, "min": 1}, "HierarchicalPrincipal": {"type": "structure", "required": ["PrincipalList"], "members": {"PrincipalList": {"shape": "PrincipalList", "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_Principal.html\">principal</a> lists that define the hierarchy for which documents users should have access to. Each hierarchical list specifies which user or group has allow or deny access for each document.</p>"}}, "documentation": "<p> Information to define the hierarchy for which documents users should have access to. </p>"}, "HierarchicalPrincipalList": {"type": "list", "member": {"shape": "HierarchicalPrincipal"}, "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_Principal.html\">principal</a> lists that define the hierarchy for which documents users should have access to. Each hierarchical list specifies which user or group has allow or deny access for each document.</p>", "max": 30, "min": 1}, "Highlight": {"type": "structure", "required": ["BeginOffset", "EndOffset"], "members": {"BeginOffset": {"shape": "Integer", "documentation": "<p>The zero-based location in the response string where the highlight starts.</p>"}, "EndOffset": {"shape": "Integer", "documentation": "<p>The zero-based location in the response string where the highlight ends.</p>"}, "TopAnswer": {"shape": "Boolean", "documentation": "<p>Indicates whether the response is the best response. True if this is the best response; otherwise, false.</p>"}, "Type": {"shape": "HighlightType", "documentation": "<p>The highlight type. </p>"}}, "documentation": "<p>Provides information that you can use to highlight a search result so that your users can quickly identify terms in the response.</p>"}, "HighlightList": {"type": "list", "member": {"shape": "Highlight"}}, "HighlightType": {"type": "string", "enum": ["STANDARD", "THESAURUS_SYNONYM"]}, "HookConfiguration": {"type": "structure", "required": ["LambdaArn", "S3Bucket"], "members": {"InvocationCondition": {"shape": "DocumentAttributeCondition", "documentation": "<p>The condition used for when a Lambda function should be invoked.</p> <p>For example, you can specify a condition that if there are empty date-time values, then Amazon Kendra should invoke a function that inserts the current date-time.</p>"}, "LambdaArn": {"shape": "LambdaArn", "documentation": "<p>The Amazon Resource Name (ARN) of a role with permission to run a Lambda function during ingestion. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/iam-roles.html\">IAM roles for Amazon Kendra</a>.</p>"}, "S3Bucket": {"shape": "S3BucketName", "documentation": "<p>Stores the original, raw documents or the structured, parsed documents before and after altering them. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/custom-document-enrichment.html#cde-data-contracts-lambda\">Data contracts for Lambda functions</a>.</p>"}}, "documentation": "<p>Provides the configuration information for invoking a Lambda function in Lambda to alter document metadata and content when ingesting documents into Amazon Kendra. You can configure your Lambda function using <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_CustomDocumentEnrichmentConfiguration.html\">PreExtractionHookConfiguration</a> if you want to apply advanced alterations on the original or raw documents. If you want to apply advanced alterations on the Amazon Kendra structured documents, you must configure your Lambda function using <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_CustomDocumentEnrichmentConfiguration.html\">PostExtractionHookConfiguration</a>. You can only invoke one Lambda function. However, this function can invoke other functions it requires.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/custom-document-enrichment.html\">Customizing document metadata during the ingestion process</a>.</p>"}, "Host": {"type": "string", "max": 253, "min": 1, "pattern": "([^\\s]*)"}, "IdentityAttributeName": {"type": "string", "max": 1000, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_-]*"}, "Importance": {"type": "integer", "max": 10, "min": 1}, "IndexConfigurationSummary": {"type": "structure", "required": ["CreatedAt", "UpdatedAt", "Status"], "members": {"Name": {"shape": "IndexName", "documentation": "<p>The name of the index.</p>"}, "Id": {"shape": "IndexId", "documentation": "<p>A identifier for the index. Use this to identify the index when you are using APIs such as <code>Query</code>, <code>DescribeIndex</code>, <code>UpdateIndex</code>, and <code>DeleteIndex</code>.</p>"}, "Edition": {"shape": "IndexEdition", "documentation": "<p>Indicates whether the index is a Enterprise Edition index or a Developer Edition index. </p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the index was created.</p>"}, "UpdatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the index was last updated.</p>"}, "Status": {"shape": "IndexStatus", "documentation": "<p>The current status of the index. When the status is <code>ACTIVE</code>, the index is ready to search.</p>"}}, "documentation": "<p>Summary information on the configuration of an index.</p>"}, "IndexConfigurationSummaryList": {"type": "list", "member": {"shape": "IndexConfigurationSummary"}}, "IndexEdition": {"type": "string", "enum": ["DEVELOPER_EDITION", "ENTERPRISE_EDITION"]}, "IndexFieldName": {"type": "string", "max": 30, "min": 1, "pattern": "^\\P{C}*$"}, "IndexId": {"type": "string", "max": 36, "min": 36, "pattern": "[a-zA-Z0-9][a-zA-Z0-9-]*"}, "IndexName": {"type": "string", "max": 1000, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_-]*"}, "IndexStatistics": {"type": "structure", "required": ["FaqStatistics", "TextDocumentStatistics"], "members": {"FaqStatistics": {"shape": "FaqStatistics", "documentation": "<p>The number of question and answer topics in the index.</p>"}, "TextDocumentStatistics": {"shape": "TextDocumentStatistics", "documentation": "<p>The number of text documents indexed.</p>"}}, "documentation": "<p>Provides information about the number of documents and the number of questions and answers in an index.</p>"}, "IndexStatus": {"type": "string", "enum": ["CREATING", "ACTIVE", "DELETING", "FAILED", "UPDATING", "SYSTEM_UPDATING"]}, "IndexedQuestionAnswersCount": {"type": "integer", "min": 0}, "IndexedTextBytes": {"type": "long", "min": 0}, "IndexedTextDocumentsCount": {"type": "integer", "min": 0}, "InlineCustomDocumentEnrichmentConfiguration": {"type": "structure", "members": {"Condition": {"shape": "DocumentAttributeCondition", "documentation": "<p>Configuration of the condition used for the target document attribute or metadata field when ingesting documents into Amazon Kendra.</p>"}, "Target": {"shape": "DocumentAttributeTarget", "documentation": "<p>Configuration of the target document attribute or metadata field when ingesting documents into Amazon Kendra. You can also include a value.</p>"}, "DocumentContentDeletion": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to delete content if the condition used for the target attribute is met.</p>"}}, "documentation": "<p>Provides the configuration information for applying basic logic to alter document metadata and content when ingesting documents into Amazon Kendra. To apply advanced logic, to go beyond what you can do with basic logic, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_HookConfiguration.html\">HookConfiguration</a>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/custom-document-enrichment.html\">Customizing document metadata during the ingestion process</a>.</p>"}, "InlineCustomDocumentEnrichmentConfigurationList": {"type": "list", "member": {"shape": "InlineCustomDocumentEnrichmentConfiguration"}, "max": 100, "min": 0}, "Integer": {"type": "integer"}, "InternalServerException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>An issue occurred with the internal server used for your Amazon Kendra service. Please wait a few minutes and try again, or contact <a href=\"http://aws.amazon.com/contact-us/\">Support</a> for help.</p>", "exception": true, "fault": true}, "Interval": {"type": "string", "enum": ["THIS_MONTH", "THIS_WEEK", "ONE_WEEK_AGO", "TWO_WEEKS_AGO", "ONE_MONTH_AGO", "TWO_MONTHS_AGO"]}, "InvalidRequestException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The input to the request is not valid. Please provide the correct input and try again.</p>", "exception": true}, "IssueSubEntity": {"type": "string", "enum": ["COMMENTS", "ATTACHMENTS", "WORKLOGS"]}, "IssueSubEntityFilter": {"type": "list", "member": {"shape": "IssueSubEntity"}, "max": 3, "min": 0}, "IssueType": {"type": "list", "member": {"shape": "String"}}, "Issuer": {"type": "string", "max": 65, "min": 1, "pattern": "^\\P{C}*$"}, "JiraAccountUrl": {"type": "string", "max": 2048, "min": 1, "pattern": "^https:\\/\\/[a-zA-Z0-9_\\-\\.]+(\\.atlassian\\.net\\/)$"}, "JiraConfiguration": {"type": "structure", "required": ["JiraAccountUrl", "SecretArn"], "members": {"JiraAccountUrl": {"shape": "JiraAccountUrl", "documentation": "<p>The URL of the Jira account. For example, <i>company.atlassian.net</i>.</p>"}, "SecretArn": {"shape": "SecretArn", "documentation": "<p>The Amazon Resource Name (ARN) of a secret in Secrets Manager contains the key-value pairs required to connect to your Jira data source. The secret must contain a JSON structure with the following keys:</p> <ul> <li> <p>jiraId—The Jira user name or email.</p> </li> <li> <p>jiraCredentials—The Jira API token. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/data-source-jira.html\">Using a Jira data source</a>.</p> </li> </ul>"}, "UseChangeLog": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to use the Jira change log to determine which documents require updating in the index. Depending on the change log's size, it may take longer for Amazon Kendra to use the change log than to scan all of your documents in Jira.</p>"}, "Project": {"shape": "Project", "documentation": "<p>Specify which projects to crawl in your Jira data source. You can specify one or more Jira project IDs.</p>"}, "IssueType": {"shape": "IssueType", "documentation": "<p>Specify which issue types to crawl in your Jira data source. You can specify one or more of these options to crawl.</p>"}, "Status": {"shape": "JiraStatus", "documentation": "<p>Specify which statuses to crawl in your Jira data source. You can specify one or more of these options to crawl.</p>"}, "IssueSubEntityFilter": {"shape": "IssueSubEntityFilter", "documentation": "<p>Specify whether to crawl comments, attachments, and work logs. You can specify one or more of these options.</p>"}, "AttachmentFieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map attributes or field names of Jira attachments to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Jira fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\"> Mapping data source fields</a>. The Jira data source field names must exist in your Jira custom metadata.</p>"}, "CommentFieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map attributes or field names of Jira comments to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Jira fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\"> Mapping data source fields</a>. The Jira data source field names must exist in your Jira custom metadata.</p>"}, "IssueFieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map attributes or field names of Jira issues to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Jira fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\"> Mapping data source fields</a>. The Jira data source field names must exist in your Jira custom metadata.</p>"}, "ProjectFieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map attributes or field names of Jira projects to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Jira fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\"> Mapping data source fields</a>. The Jira data source field names must exist in your Jira custom metadata.</p>"}, "WorkLogFieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map attributes or field names of Jira work logs to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Jira fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\"> Mapping data source fields</a>. The Jira data source field names must exist in your Jira custom metadata.</p>"}, "InclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to include certain file paths, file names, and file types in your Jira data source. Files that match the patterns are included in the index. Files that don't match the patterns are excluded from the index. If a file matches both an inclusion pattern and an exclusion pattern, the exclusion pattern takes precedence and the file isn't included in the index.</p>"}, "ExclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to exclude certain file paths, file names, and file types in your Jira data source. Files that match the patterns are excluded from the index. Files that don’t match the patterns are included in the index. If a file matches both an inclusion pattern and an exclusion pattern, the exclusion pattern takes precedence and the file isn't included in the index.</p>"}, "VpcConfiguration": {"shape": "DataSourceVpcConfiguration", "documentation": "<p>Configuration information for an Amazon Virtual Private Cloud to connect to your Jira. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/vpc-configuration.html\">Configuring a VPC</a>.</p>"}}, "documentation": "<p>Provides the configuration information to connect to <PERSON><PERSON> as your data source.</p>"}, "JiraStatus": {"type": "list", "member": {"shape": "String"}}, "JsonTokenTypeConfiguration": {"type": "structure", "required": ["UserNameAttributeField", "GroupAttributeField"], "members": {"UserNameAttributeField": {"shape": "String", "documentation": "<p>The user name attribute field.</p>"}, "GroupAttributeField": {"shape": "String", "documentation": "<p>The group attribute field.</p>"}}, "documentation": "<p>Provides the configuration information for the JSON token type.</p>"}, "JwtTokenTypeConfiguration": {"type": "structure", "required": ["KeyLocation"], "members": {"KeyLocation": {"shape": "KeyLocation", "documentation": "<p>The location of the key.</p>"}, "URL": {"shape": "Url", "documentation": "<p>The signing key URL.</p>"}, "SecretManagerArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (arn) of the secret.</p>"}, "UserNameAttributeField": {"shape": "UserNameAttributeField", "documentation": "<p>The user name attribute field.</p>"}, "GroupAttributeField": {"shape": "GroupAttributeField", "documentation": "<p>The group attribute field.</p>"}, "Issuer": {"shape": "Issuer", "documentation": "<p>The issuer of the token.</p>"}, "ClaimRegex": {"shape": "ClaimRegex", "documentation": "<p>The regular expression that identifies the claim.</p>"}}, "documentation": "<p>Provides the configuration information for the JWT token type.</p>"}, "KeyLocation": {"type": "string", "enum": ["URL", "SECRET_MANAGER"]}, "KmsKeyId": {"type": "string", "max": 2048, "min": 1, "sensitive": true}, "LambdaArn": {"type": "string", "max": 2048, "min": 1, "pattern": "/arn:aws[a-zA-Z-]*:lambda:[a-z]+-[a-z]+-[0-9]:[0-9]{12}:function:[a-zA-Z0-9-_]+(\\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})?(:[a-zA-Z0-9-_]+)?/"}, "LanguageCode": {"type": "string", "documentation": "<p>The code for a language. The default language is English. For more information on supported languages, including their codes, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/in-adding-languages.html\">Adding documents in languages other than English</a>.</p>", "max": 10, "min": 2, "pattern": "[a-zA-Z-]*"}, "ListAccessControlConfigurationsRequest": {"type": "structure", "required": ["IndexId"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for the access control configuration.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>If the previous response was incomplete (because there's more data to retrieve), Amazon Kendra returns a pagination token in the response. You can use this pagination token to retrieve the next set of access control configurations.</p>"}, "MaxResults": {"shape": "MaxResultsIntegerForListAccessControlConfigurationsRequest", "documentation": "<p>The maximum number of access control configurations to return.</p>"}}}, "ListAccessControlConfigurationsResponse": {"type": "structure", "required": ["AccessControlConfigurations"], "members": {"NextToken": {"shape": "String", "documentation": "<p>If the response is truncated, Amazon Kendra returns this token, which you can use in the subsequent request to retrieve the next set of access control configurations.</p>"}, "AccessControlConfigurations": {"shape": "AccessControlConfigurationSummaryList", "documentation": "<p>The details of your access control configurations.</p>"}}}, "ListDataSourceSyncJobsRequest": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source connector.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index used with the data source connector.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the previous response was incomplete (because there is more data to retrieve), Amazon Kendra returns a pagination token in the response. You can use this pagination token to retrieve the next set of jobs.</p>"}, "MaxResults": {"shape": "MaxResultsIntegerForListDataSourceSyncJobsRequest", "documentation": "<p>The maximum number of synchronization jobs to return in the response. If there are fewer results in the list, this response contains only the actual results.</p>"}, "StartTimeFilter": {"shape": "TimeRange", "documentation": "<p>When specified, the synchronization jobs returned in the list are limited to jobs between the specified dates.</p>"}, "StatusFilter": {"shape": "DataSourceSyncJobStatus", "documentation": "<p>Only returns synchronization jobs with the <code>Status</code> field equal to the specified status.</p>"}}}, "ListDataSourceSyncJobsResponse": {"type": "structure", "members": {"History": {"shape": "DataSourceSyncJobHistoryList", "documentation": "<p>A history of synchronization jobs for the data source connector.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Kendra returns this token that you can use in the subsequent request to retrieve the next set of jobs.</p>"}}}, "ListDataSourcesRequest": {"type": "structure", "required": ["IndexId"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index used with one or more data source connectors.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the previous response was incomplete (because there is more data to retrieve), Amazon Kendra returns a pagination token in the response. You can use this pagination token to retrieve the next set of data source connectors. </p>"}, "MaxResults": {"shape": "MaxResultsIntegerForListDataSourcesRequest", "documentation": "<p>The maximum number of data source connectors to return.</p>"}}}, "ListDataSourcesResponse": {"type": "structure", "members": {"SummaryItems": {"shape": "DataSourceSummaryList", "documentation": "<p>An array of summary information for one or more data source connector.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Kendra returns this token that you can use in the subsequent request to retrieve the next set of data source connectors.</p>"}}}, "ListEntityPersonasRequest": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {"shape": "ExperienceId", "documentation": "<p>The identifier of your Amazon Kendra experience.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for your Amazon Kendra experience.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the previous response was incomplete (because there is more data to retrieve), Amazon Kendra returns a pagination token in the response. You can use this pagination token to retrieve the next set of users or groups.</p>"}, "MaxResults": {"shape": "MaxResultsIntegerForListEntityPersonasRequest", "documentation": "<p>The maximum number of returned users or groups.</p>"}}}, "ListEntityPersonasResponse": {"type": "structure", "members": {"SummaryItems": {"shape": "PersonasSummaryList", "documentation": "<p>An array of summary information for one or more users or groups.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Kendra returns this token, which you can use in a later request to retrieve the next set of users or groups.</p>"}}}, "ListExperienceEntitiesRequest": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {"shape": "ExperienceId", "documentation": "<p>The identifier of your Amazon Kendra experience.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for your Amazon Kendra experience.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the previous response was incomplete (because there is more data to retrieve), Amazon Kendra returns a pagination token in the response. You can use this pagination token to retrieve the next set of users or groups.</p>"}}}, "ListExperienceEntitiesResponse": {"type": "structure", "members": {"SummaryItems": {"shape": "ExperienceEntitiesSummaryList", "documentation": "<p>An array of summary information for one or more users or groups.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Kendra returns this token, which you can use in a later request to retrieve the next set of users or groups.</p>"}}}, "ListExperiencesRequest": {"type": "structure", "required": ["IndexId"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for your Amazon Kendra experience.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the previous response was incomplete (because there is more data to retrieve), Amazon Kendra returns a pagination token in the response. You can use this pagination token to retrieve the next set of Amazon Kendra experiences.</p>"}, "MaxResults": {"shape": "MaxResultsIntegerForListExperiencesRequest", "documentation": "<p>The maximum number of returned Amazon Kendra experiences.</p>"}}}, "ListExperiencesResponse": {"type": "structure", "members": {"SummaryItems": {"shape": "ExperiencesSummaryList", "documentation": "<p>An array of summary information for one or more Amazon Kendra experiences.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Kendra returns this token, which you can use in a later request to retrieve the next set of Amazon Kendra experiences.</p>"}}}, "ListFaqsRequest": {"type": "structure", "required": ["IndexId"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The index that contains the FAQ lists.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the previous response was incomplete (because there is more data to retrieve), Amazon Kendra returns a pagination token in the response. You can use this pagination token to retrieve the next set of FAQs.</p>"}, "MaxResults": {"shape": "MaxResultsIntegerForListFaqsRequest", "documentation": "<p>The maximum number of FAQs to return in the response. If there are fewer results in the list, this response contains only the actual results.</p>"}}}, "ListFaqsResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Kendra returns this token that you can use in the subsequent request to retrieve the next set of FAQs.</p>"}, "FaqSummaryItems": {"shape": "FaqSummaryItems", "documentation": "<p>information about the FAQs associated with the specified index.</p>"}}}, "ListFeaturedResultsSetsRequest": {"type": "structure", "required": ["IndexId"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index used for featuring results.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Kendra returns a pagination token in the response. You can use this pagination token to retrieve the next set of featured results sets.</p>"}, "MaxResults": {"shape": "MaxResultsIntegerForListFeaturedResultsSetsRequest", "documentation": "<p>The maximum number of featured results sets to return.</p>"}}}, "ListFeaturedResultsSetsResponse": {"type": "structure", "members": {"FeaturedResultsSetSummaryItems": {"shape": "FeaturedResultsSetSummaryItems", "documentation": "<p>An array of summary information for one or more featured results sets.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Kendra returns a pagination token in the response.</p>"}}}, "ListGroupsOlderThanOrderingIdRequest": {"type": "structure", "required": ["IndexId", "OrderingId"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for getting a list of groups mapped to users before a given ordering or timestamp identifier.</p>"}, "DataSourceId": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source for getting a list of groups mapped to users before a given ordering timestamp identifier.</p>"}, "OrderingId": {"shape": "PrincipalOrderingId", "documentation": "<p>The timestamp identifier used for the latest <code>PUT</code> or <code>DELETE</code> action for mapping users to their groups.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p> If the previous response was incomplete (because there is more data to retrieve), Amazon Kendra returns a pagination token in the response. You can use this pagination token to retrieve the next set of groups that are mapped to users before a given ordering or timestamp identifier. </p>"}, "MaxResults": {"shape": "MaxResultsIntegerForListPrincipalsRequest", "documentation": "<p> The maximum number of returned groups that are mapped to users before a given ordering or timestamp identifier. </p>"}}}, "ListGroupsOlderThanOrderingIdResponse": {"type": "structure", "members": {"GroupsSummaries": {"shape": "ListOfGroupSummaries", "documentation": "<p> Summary information for list of groups that are mapped to users before a given ordering or timestamp identifier. </p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p> If the response is truncated, Amazon Kendra returns this token that you can use in the subsequent request to retrieve the next set of groups that are mapped to users before a given ordering or timestamp identifier. </p>"}}}, "ListIndicesRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>If the previous response was incomplete (because there is more data to retrieve), Amazon Kendra returns a pagination token in the response. You can use this pagination token to retrieve the next set of indexes. </p>"}, "MaxResults": {"shape": "MaxResultsIntegerForListIndicesRequest", "documentation": "<p>The maximum number of indices to return.</p>"}}}, "ListIndicesResponse": {"type": "structure", "members": {"IndexConfigurationSummaryItems": {"shape": "IndexConfigurationSummaryList", "documentation": "<p>An array of summary information on the configuration of one or more indexes.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Kendra returns this token that you can use in the subsequent request to retrieve the next set of indexes.</p>"}}}, "ListOfGroupSummaries": {"type": "list", "member": {"shape": "GroupSummary"}}, "ListQuerySuggestionsBlockListsRequest": {"type": "structure", "required": ["IndexId"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for a list of all block lists that exist for that index.</p> <p>For information on the current quota limits for block lists, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/quotas.html\">Quotas for Amazon Kendra</a>.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the previous response was incomplete (because there is more data to retrieve), Amazon Kendra returns a pagination token in the response. You can use this pagination token to retrieve the next set of block lists (<code>BlockListSummaryItems</code>).</p>"}, "MaxResults": {"shape": "MaxResultsIntegerForListQuerySuggestionsBlockLists", "documentation": "<p>The maximum number of block lists to return.</p>"}}}, "ListQuerySuggestionsBlockListsResponse": {"type": "structure", "members": {"BlockListSummaryItems": {"shape": "QuerySuggestionsBlockListSummaryItems", "documentation": "<p>Summary items for a block list.</p> <p>This includes summary items on the block list ID, block list name, when the block list was created, when the block list was last updated, and the count of block words/phrases in the block list.</p> <p>For information on the current quota limits for block lists, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/quotas.html\">Quotas for Amazon Kendra</a>.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Kendra returns this token that you can use in the subsequent request to retrieve the next set of block lists.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) of the index, FAQ, or data source to get a list of tags for.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "TagList", "documentation": "<p>A list of tags associated with the index, FAQ, or data source.</p>"}}}, "ListThesauriRequest": {"type": "structure", "required": ["IndexId"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index with one or more thesauri.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the previous response was incomplete (because there is more data to retrieve), Amazon Kendra returns a pagination token in the response. You can use this pagination token to retrieve the next set of thesauri (<code>ThesaurusSummaryItems</code>). </p>"}, "MaxResults": {"shape": "MaxResultsIntegerForListThesauriRequest", "documentation": "<p>The maximum number of thesauri to return.</p>"}}}, "ListThesauriResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Kendra returns this token that you can use in the subsequent request to retrieve the next set of thesauri. </p>"}, "ThesaurusSummaryItems": {"shape": "ThesaurusSummaryItems", "documentation": "<p>An array of summary information for a thesaurus or multiple thesauri.</p>"}}}, "Long": {"type": "long"}, "LookBackPeriod": {"type": "integer", "max": 168, "min": 0}, "MaxContentSizePerPageInMegaBytes": {"type": "float", "max": 50, "min": 1e-06}, "MaxLinksPerPage": {"type": "integer", "max": 1000, "min": 1}, "MaxResultsIntegerForListAccessControlConfigurationsRequest": {"type": "integer", "max": 100, "min": 1}, "MaxResultsIntegerForListDataSourceSyncJobsRequest": {"type": "integer", "max": 10, "min": 1}, "MaxResultsIntegerForListDataSourcesRequest": {"type": "integer", "max": 100, "min": 1}, "MaxResultsIntegerForListEntityPersonasRequest": {"type": "integer", "max": 100, "min": 1}, "MaxResultsIntegerForListExperiencesRequest": {"type": "integer", "max": 100, "min": 1}, "MaxResultsIntegerForListFaqsRequest": {"type": "integer", "max": 100, "min": 1}, "MaxResultsIntegerForListFeaturedResultsSetsRequest": {"type": "integer", "max": 100, "min": 1}, "MaxResultsIntegerForListIndicesRequest": {"type": "integer", "max": 100, "min": 1}, "MaxResultsIntegerForListPrincipalsRequest": {"type": "integer", "max": 10, "min": 1}, "MaxResultsIntegerForListQuerySuggestionsBlockLists": {"type": "integer", "max": 100, "min": 1}, "MaxResultsIntegerForListThesauriRequest": {"type": "integer", "max": 100, "min": 1}, "MaxUrlsPerMinuteCrawlRate": {"type": "integer", "max": 300, "min": 1}, "MemberGroup": {"type": "structure", "required": ["GroupId"], "members": {"GroupId": {"shape": "GroupId", "documentation": "<p>The identifier of the sub group you want to map to a group.</p>"}, "DataSourceId": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source for the sub group you want to map to a group.</p>"}}, "documentation": "<p>The sub groups that belong to a group.</p>"}, "MemberGroups": {"type": "list", "member": {"shape": "MemberGroup"}, "max": 1000, "min": 1}, "MemberUser": {"type": "structure", "required": ["UserId"], "members": {"UserId": {"shape": "UserId", "documentation": "<p>The identifier of the user you want to map to a group.</p>"}}, "documentation": "<p>The users that belong to a group.</p>"}, "MemberUsers": {"type": "list", "member": {"shape": "MemberUser"}, "max": 1000, "min": 1}, "MetricType": {"type": "string", "enum": ["QUERIES_BY_COUNT", "QUERIES_BY_ZERO_CLICK_RATE", "QUERIES_BY_ZERO_RESULT_RATE", "DOCS_BY_CLICK_COUNT", "AGG_QUERY_DOC_METRICS", "TREND_QUERY_DOC_METRICS"]}, "MetricValue": {"type": "string", "pattern": "(([1-9][0-9]*)|0)"}, "MimeType": {"type": "string", "max": 256, "min": 1, "pattern": "^\\P{C}*$"}, "MinimumNumberOfQueryingUsers": {"type": "integer", "max": 10000, "min": 1}, "MinimumQueryCount": {"type": "integer", "max": 10000, "min": 1}, "MissingAttributeKeyStrategy": {"type": "string", "enum": ["IGNORE", "COLLAPSE", "EXPAND"]}, "Mode": {"type": "string", "enum": ["ENABLED", "LEARN_ONLY"]}, "NameType": {"type": "string", "max": 100, "min": 1, "pattern": "^[\\S\\s]*$", "sensitive": true}, "NextToken": {"type": "string", "max": 800, "min": 1}, "ObjectBoolean": {"type": "boolean"}, "OnPremiseConfiguration": {"type": "structure", "required": ["HostUrl", "OrganizationName", "SslCertificateS3Path"], "members": {"HostUrl": {"shape": "Url", "documentation": "<p>The GitHub host URL or API endpoint URL. For example, <i>https://on-prem-host-url/api/v3/</i> </p>"}, "OrganizationName": {"shape": "OrganizationName", "documentation": "<p>The name of the organization of the GitHub Enterprise Server (in-premise) account you want to connect to. You can find your organization name by logging into GitHub desktop and selecting <b>Your organizations</b> under your profile picture dropdown.</p>"}, "SslCertificateS3Path": {"shape": "S3Path", "documentation": "<p>The path to the SSL certificate stored in an Amazon S3 bucket. You use this to connect to GitHub if you require a secure SSL connection.</p> <p>You can simply generate a self-signed X509 certificate on any computer using OpenSSL. For an example of using OpenSSL to create an X509 certificate, see <a href=\"https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/configuring-https-ssl.html\">Create and sign an X509 certificate</a>.</p>"}}, "documentation": "<p>Provides the configuration information to connect to GitHub Enterprise Server (on premises).</p>"}, "OneDriveConfiguration": {"type": "structure", "required": ["TenantDomain", "SecretArn", "OneDriveUsers"], "members": {"TenantDomain": {"shape": "TenantDomain", "documentation": "<p>The Azure Active Directory domain of the organization. </p>"}, "SecretArn": {"shape": "SecretArn", "documentation": "<p>The Amazon Resource Name (ARN) of an Secrets Managersecret that contains the user name and password to connect to OneDrive. The user name should be the application ID for the OneDrive application, and the password is the application key for the OneDrive application.</p>"}, "OneDriveUsers": {"shape": "OneDriveUsers", "documentation": "<p>A list of user accounts whose documents should be indexed.</p>"}, "InclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to include certain documents in your OneDrive. Documents that match the patterns are included in the index. Documents that don't match the patterns are excluded from the index. If a document matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the document isn't included in the index.</p> <p>The pattern is applied to the file name.</p>"}, "ExclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to exclude certain documents in your OneDrive. Documents that match the patterns are excluded from the index. Documents that don't match the patterns are included in the index. If a document matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the document isn't included in the index.</p> <p>The pattern is applied to the file name.</p>"}, "FieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map OneDrive data source attributes or field names to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to OneDrive fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The OneDrive data source field names must exist in your OneDrive custom metadata.</p>"}, "DisableLocalGroups": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to disable local groups information.</p>"}}, "documentation": "<p>Provides the configuration information to connect to OneDrive as your data source.</p>"}, "OneDriveUser": {"type": "string", "max": 256, "min": 1, "pattern": "^(?!\\s).+@([a-zA-Z0-9_\\-\\.]+)\\.([a-zA-Z]{2,5})$"}, "OneDriveUserList": {"type": "list", "member": {"shape": "OneDriveUser"}, "max": 100, "min": 1}, "OneDriveUsers": {"type": "structure", "members": {"OneDriveUserList": {"shape": "OneDriveUserList", "documentation": "<p>A list of users whose documents should be indexed. Specify the user names in email format, for example, <code>username@tenantdomain</code>. If you need to index the documents of more than 100 users, use the <code>OneDriveUserS3Path</code> field to specify the location of a file containing a list of users.</p>"}, "OneDriveUserS3Path": {"shape": "S3Path", "documentation": "<p>The S3 bucket location of a file containing a list of users whose documents should be indexed.</p>"}}, "documentation": "<p>User accounts whose documents should be indexed.</p>"}, "Order": {"type": "string", "enum": ["ASCENDING", "DESCENDING"]}, "OrganizationId": {"type": "string", "max": 12, "min": 12, "pattern": "d-[0-9a-fA-F]{10}"}, "OrganizationName": {"type": "string", "max": 60, "min": 1, "pattern": "^[A-Za-z0-9_.-]+$"}, "Persona": {"type": "string", "enum": ["OWNER", "VIEWER"]}, "PersonasSummary": {"type": "structure", "members": {"EntityId": {"shape": "EntityId", "documentation": "<p>The identifier of a user or group in your IAM Identity Center identity source. For example, a user ID could be an email.</p>"}, "Persona": {"shape": "<PERSON>a", "documentation": "<p>The persona that defines the specific permissions of the user or group in your IAM Identity Center identity source. The available personas or access roles are <code>Owner</code> and <code>Viewer</code>. For more information on these personas, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/deploying-search-experience-no-code.html#access-search-experience\">Providing access to your search page</a>.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the summary information was created.</p>"}, "UpdatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the summary information was last updated.</p>"}}, "documentation": "<p>Summary information for users or groups in your IAM Identity Center identity source. This applies to users and groups with specific permissions that define their level of access to your Amazon Kendra experience. You can create an Amazon Kendra experience such as a search application. For more information on creating a search application experience, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/deploying-search-experience-no-code.html\">Building a search experience with no code</a>.</p>"}, "PersonasSummaryList": {"type": "list", "member": {"shape": "PersonasSummary"}}, "Port": {"type": "integer", "max": 65535, "min": 1}, "Principal": {"type": "structure", "required": ["Name", "Type", "Access"], "members": {"Name": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The name of the user or group.</p>"}, "Type": {"shape": "PrincipalType", "documentation": "<p>The type of principal.</p>"}, "Access": {"shape": "ReadAccessType", "documentation": "<p>Whether to allow or deny document access to the principal.</p>"}, "DataSourceId": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source the principal should access documents from.</p>"}}, "documentation": "<p>Provides user and group information for <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/user-context-filter.html\">user context filtering</a>.</p>"}, "PrincipalList": {"type": "list", "member": {"shape": "Principal"}}, "PrincipalMappingStatus": {"type": "string", "enum": ["FAILED", "SUCCEEDED", "PROCESSING", "DELETING", "DELETED"]}, "PrincipalName": {"type": "string", "max": 200, "min": 1, "pattern": "^\\P{C}*$"}, "PrincipalOrderingId": {"type": "long", "max": 32535158400000, "min": 0}, "PrincipalType": {"type": "string", "enum": ["USER", "GROUP"]}, "PrivateChannelFilter": {"type": "list", "member": {"shape": "String"}}, "Project": {"type": "list", "member": {"shape": "String"}}, "ProxyConfiguration": {"type": "structure", "required": ["Host", "Port"], "members": {"Host": {"shape": "Host", "documentation": "<p>The name of the website host you want to connect to via a web proxy server.</p> <p>For example, the host name of https://a.example.com/page1.html is \"a.example.com\".</p>"}, "Port": {"shape": "Port", "documentation": "<p>The port number of the website host you want to connect to via a web proxy server. </p> <p>For example, the port for https://a.example.com/page1.html is 443, the standard port for HTTPS.</p>"}, "Credentials": {"shape": "SecretArn", "documentation": "<p>Your secret ARN, which you can create in <a href=\"https://docs.aws.amazon.com/secretsmanager/latest/userguide/intro.html\">Secrets Manager</a> </p> <p>The credentials are optional. You use a secret if web proxy credentials are required to connect to a website host. Amazon Kendra currently support basic authentication to connect to a web proxy server. The secret stores your credentials.</p>"}}, "documentation": "<p>Provides the configuration information for a web proxy to connect to website hosts.</p>"}, "PublicChannelFilter": {"type": "list", "member": {"shape": "String"}}, "PutPrincipalMappingRequest": {"type": "structure", "required": ["IndexId", "GroupId", "GroupMembers"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index you want to map users to their groups.</p>"}, "DataSourceId": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source you want to map users to their groups.</p> <p>This is useful if a group is tied to multiple data sources, but you only want the group to access documents of a certain data source. For example, the groups \"Research\", \"Engineering\", and \"Sales and Marketing\" are all tied to the company's documents stored in the data sources Confluence and Salesforce. However, \"Sales and Marketing\" team only needs access to customer-related documents stored in Salesforce.</p>"}, "GroupId": {"shape": "GroupId", "documentation": "<p>The identifier of the group you want to map its users to.</p>"}, "GroupMembers": {"shape": "GroupMembers", "documentation": "<p>The list that contains your users or sub groups that belong the same group.</p> <p>For example, the group \"Company\" includes the user \"CEO\" and the sub groups \"Research\", \"Engineering\", and \"Sales and Marketing\".</p> <p>If you have more than 1000 users and/or sub groups for a single group, you need to provide the path to the S3 file that lists your users and sub groups for a group. Your sub groups can contain more than 1000 users, but the list of sub groups that belong to a group (and/or users) must be no more than 1000.</p>"}, "OrderingId": {"shape": "PrincipalOrderingId", "documentation": "<p>The timestamp identifier you specify to ensure Amazon Kendra does not override the latest <code>PUT</code> action with previous actions. The highest number ID, which is the ordering ID, is the latest action you want to process and apply on top of other actions with lower number IDs. This prevents previous actions with lower number IDs from possibly overriding the latest action.</p> <p>The ordering ID can be the Unix time of the last update you made to a group members list. You would then provide this list when calling <code>PutPrincipalMapping</code>. This ensures your <code>PUT</code> action for that updated group with the latest members list doesn't get overwritten by earlier <code>PUT</code> actions for the same group which are yet to be processed.</p> <p>The default ordering ID is the current Unix time in milliseconds that the action was received by Amazon Kendra.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of a role that has access to the S3 file that contains your list of users or sub groups that belong to a group.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/iam-roles.html#iam-roles-ds\">IAM roles for Amazon Kendra</a>.</p>"}}}, "QueryCapacityUnit": {"type": "integer", "min": 0}, "QueryId": {"type": "string", "max": 36, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9-]*"}, "QueryIdentifiersEnclosingOption": {"type": "string", "enum": ["DOUBLE_QUOTES", "NONE"]}, "QueryRequest": {"type": "structure", "required": ["IndexId"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for the search.</p>"}, "QueryText": {"shape": "QueryText", "documentation": "<p>The input query text for the search. Amazon Kendra truncates queries at 30 token words, which excludes punctuation and stop words. Truncation still applies if you use Boolean or more advanced, complex queries. </p>"}, "AttributeFilter": {"shape": "Attribute<PERSON>ilter", "documentation": "<p>Filters search results by document fields/attributes. You can only provide one attribute filter; however, the <code>AndAllFilters</code>, <code>NotFilter</code>, and <code>OrAllFilters</code> parameters contain a list of other filters.</p> <p>The <code>AttributeFilter</code> parameter means you can create a set of filtering rules that a document must satisfy to be included in the query results.</p>"}, "Facets": {"shape": "FacetList", "documentation": "<p>An array of documents fields/attributes for faceted search. Amazon Kendra returns a count for each field key specified. This helps your users narrow their search.</p>"}, "RequestedDocumentAttributes": {"shape": "DocumentAttributeKeyList", "documentation": "<p>An array of document fields/attributes to include in the response. You can limit the response to include certain document fields. By default, all document attributes are included in the response.</p>"}, "QueryResultTypeFilter": {"shape": "QueryResultType", "documentation": "<p>Sets the type of query result or response. Only results for the specified type are returned.</p>"}, "DocumentRelevanceOverrideConfigurations": {"shape": "DocumentRelevanceOverrideConfigurationList", "documentation": "<p>Overrides relevance tuning configurations of fields/attributes set at the index level.</p> <p>If you use this API to override the relevance tuning configured at the index level, but there is no relevance tuning configured at the index level, then Amazon Kendra does not apply any relevance tuning.</p> <p>If there is relevance tuning configured for fields at the index level, and you use this API to override only some of these fields, then for the fields you did not override, the importance is set to 1.</p>"}, "PageNumber": {"shape": "Integer", "documentation": "<p>Query results are returned in pages the size of the <code>PageSize</code> parameter. By default, Amazon Kendra returns the first page of results. Use this parameter to get result pages after the first one.</p>"}, "PageSize": {"shape": "Integer", "documentation": "<p>Sets the number of results that are returned in each page of results. The default page size is 10. The maximum number of results returned is 100. If you ask for more than 100 results, only 100 are returned.</p>"}, "SortingConfiguration": {"shape": "SortingConfiguration", "documentation": "<p>Provides information that determines how the results of the query are sorted. You can set the field that Amazon Kendra should sort the results on, and specify whether the results should be sorted in ascending or descending order. In the case of ties in sorting the results, the results are sorted by relevance.</p> <p>If you don't provide sorting configuration, the results are sorted by the relevance that Amazon Kendra determines for the result.</p>"}, "SortingConfigurations": {"shape": "SortingConfigurationList", "documentation": "<p>Provides configuration information to determine how the results of a query are sorted.</p> <p>You can set upto 3 fields that Amazon Kendra should sort the results on, and specify whether the results should be sorted in ascending or descending order. The sort field quota can be increased.</p> <p>If you don't provide a sorting configuration, the results are sorted by the relevance that Amazon Kendra determines for the result. In the case of ties in sorting the results, the results are sorted by relevance. </p>"}, "UserContext": {"shape": "UserContext", "documentation": "<p>The user context token or user and group information.</p>"}, "VisitorId": {"shape": "VisitorId", "documentation": "<p>Provides an identifier for a specific user. The <code>VisitorId</code> should be a unique identifier, such as a GUID. Don't use personally identifiable information, such as the user's email address, as the <code>VisitorId</code>.</p>"}, "SpellCorrectionConfiguration": {"shape": "SpellCorrectionConfiguration", "documentation": "<p>Enables suggested spell corrections for queries.</p>"}, "CollapseConfiguration": {"shape": "CollapseConfiguration", "documentation": "<p>Provides configuration to determine how to group results by document attribute value, and how to display them (collapsed or expanded) under a designated primary document for each group.</p>"}}}, "QueryResult": {"type": "structure", "members": {"QueryId": {"shape": "QueryId", "documentation": "<p>The identifier for the search. You also use <code>QueryId</code> to identify the search when using the <a href=\"https://docs.aws.amazon.com/kendra/latest/APIReference/API_SubmitFeedback.html\">SubmitFeedback</a> API.</p>"}, "ResultItems": {"shape": "QueryResultItemList", "documentation": "<p>The results of the search.</p>"}, "FacetResults": {"shape": "FacetResultList", "documentation": "<p>Contains the facet results. A <code>FacetResult</code> contains the counts for each field/attribute key that was specified in the <code>Facets</code> input parameter.</p>"}, "TotalNumberOfResults": {"shape": "Integer", "documentation": "<p>The total number of items found by the search. However, you can only retrieve up to 100 items. For example, if the search found 192 items, you can only retrieve the first 100 of the items.</p>"}, "Warnings": {"shape": "WarningList", "documentation": "<p>A list of warning codes and their messages on problems with your query.</p> <p>Amazon Kendra currently only supports one type of warning, which is a warning on invalid syntax used in the query. For examples of invalid query syntax, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/searching-example.html#searching-index-query-syntax\">Searching with advanced query syntax</a>.</p>"}, "SpellCorrectedQueries": {"shape": "SpellCorrectedQueryList", "documentation": "<p>A list of information related to suggested spell corrections for a query.</p>"}, "FeaturedResultsItems": {"shape": "FeaturedResultsItemList", "documentation": "<p>The list of featured result items. Featured results are displayed at the top of the search results page, placed above all other results for certain queries. If there's an exact match of a query, then certain documents are featured in the search results.</p>"}}}, "QueryResultFormat": {"type": "string", "enum": ["TABLE", "TEXT"]}, "QueryResultItem": {"type": "structure", "members": {"Id": {"shape": "ResultId", "documentation": "<p>The identifier for the query result.</p>"}, "Type": {"shape": "QueryResultType", "documentation": "<p>The type of document within the response. For example, a response could include a question-answer that's relevant to the query.</p>"}, "Format": {"shape": "QueryResultFormat", "documentation": "<p>If the <code>Type</code> of document within the response is <code>ANSWER</code>, then it is either a <code>TABLE</code> answer or <code>TEXT</code> answer. If it's a table answer, a table excerpt is returned in <code>TableExcerpt</code>. If it's a text answer, a text excerpt is returned in <code>DocumentExcerpt</code>.</p>"}, "AdditionalAttributes": {"shape": "AdditionalResultAttributeList", "documentation": "<p>One or more additional fields/attributes associated with the query result.</p>"}, "DocumentId": {"shape": "DocumentId", "documentation": "<p>The identifier for the document.</p>"}, "DocumentTitle": {"shape": "TextWithHighlights", "documentation": "<p>The title of the document. Contains the text of the title and information for highlighting the relevant terms in the title.</p>"}, "DocumentExcerpt": {"shape": "TextWithHighlights", "documentation": "<p>An extract of the text in the document. Contains information about highlighting the relevant terms in the excerpt.</p>"}, "DocumentURI": {"shape": "Url", "documentation": "<p>The URI of the original location of the document.</p>"}, "DocumentAttributes": {"shape": "DocumentAttributeList", "documentation": "<p>An array of document fields/attributes assigned to a document in the search results. For example, the document author (<code>_author</code>) or the source URI (<code>_source_uri</code>) of the document.</p>"}, "ScoreAttributes": {"shape": "ScoreAttributes", "documentation": "<p>Indicates the confidence level of Amazon Kendra providing a relevant result for the query. Each result is placed into a bin that indicates the confidence, <code>VERY_HIGH</code>, <code>HIGH</code>, <code>MEDIUM</code> and <code>LOW</code>. You can use the score to determine if a response meets the confidence needed for your application.</p> <p>The field is only set to <code>LOW</code> when the <code>Type</code> field is set to <code>DOCUMENT</code> and Amazon Kendra is not confident that the result is relevant to the query.</p>"}, "FeedbackToken": {"shape": "FeedbackToken", "documentation": "<p>A token that identifies a particular result from a particular query. Use this token to provide click-through feedback for the result. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/submitting-feedback.html\">Submitting feedback</a>.</p>"}, "TableExcerpt": {"shape": "TableExcerpt", "documentation": "<p>An excerpt from a table within a document.</p>"}, "CollapsedResultDetail": {"shape": "CollapsedResultDetail", "documentation": "<p>Provides details about a collapsed group of search results.</p>"}}, "documentation": "<p>A single query result.</p> <p>A query result contains information about a document returned by the query. This includes the original location of the document, a list of attributes assigned to the document, and relevant text from the document that satisfies the query.</p>"}, "QueryResultItemList": {"type": "list", "member": {"shape": "QueryResultItem"}}, "QueryResultType": {"type": "string", "enum": ["DOCUMENT", "QUESTION_ANSWER", "ANSWER"]}, "QuerySuggestionsBlockListId": {"type": "string", "max": 36, "min": 36, "pattern": "[a-zA-Z0-9][a-zA-Z0-9-]*"}, "QuerySuggestionsBlockListName": {"type": "string", "max": 100, "min": 1, "pattern": "^[a-zA-Z0-9](-*[a-zA-Z0-9])*"}, "QuerySuggestionsBlockListStatus": {"type": "string", "enum": ["ACTIVE", "CREATING", "DELETING", "UPDATING", "ACTIVE_BUT_UPDATE_FAILED", "FAILED"]}, "QuerySuggestionsBlockListSummary": {"type": "structure", "members": {"Id": {"shape": "QuerySuggestionsBlockListId", "documentation": "<p>The identifier of a block list.</p>"}, "Name": {"shape": "QuerySuggestionsBlockListName", "documentation": "<p>The name of the block list.</p>"}, "Status": {"shape": "QuerySuggestionsBlockListStatus", "documentation": "<p>The status of the block list.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the block list was created.</p>"}, "UpdatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the block list was last updated.</p>"}, "ItemCount": {"shape": "Integer", "documentation": "<p>The number of items in the block list file.</p>"}}, "documentation": "<p>Summary information on a query suggestions block list.</p> <p>This includes information on the block list ID, block list name, when the block list was created, when the block list was last updated, and the count of block words/phrases in the block list.</p> <p>For information on the current quota limits for block lists, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/quotas.html\">Quotas for Amazon Kendra</a>.</p>"}, "QuerySuggestionsBlockListSummaryItems": {"type": "list", "member": {"shape": "QuerySuggestionsBlockListSummary"}}, "QuerySuggestionsId": {"type": "string", "max": 36, "min": 1}, "QuerySuggestionsStatus": {"type": "string", "enum": ["ACTIVE", "UPDATING"]}, "QueryText": {"type": "string"}, "QueryTextList": {"type": "list", "member": {"shape": "QueryText"}, "max": 49, "min": 0}, "QuipConfiguration": {"type": "structure", "required": ["Domain", "SecretArn"], "members": {"Domain": {"shape": "Domain", "documentation": "<p>The Quip site domain. For example, <i>https://quip-company.quipdomain.com/browse</i>. The domain in this example is \"quipdomain\".</p>"}, "SecretArn": {"shape": "SecretArn", "documentation": "<p>The Amazon Resource Name (ARN) of an Secrets Manager secret that contains the key-value pairs that are required to connect to your Quip. The secret must contain a JSON structure with the following keys:</p> <ul> <li> <p>accessToken—The token created in Quip. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/data-source-slack.html\">Using a Quip data source</a>.</p> </li> </ul>"}, "CrawlFileComments": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to index file comments.</p>"}, "CrawlChatRooms": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to index the contents of chat rooms.</p>"}, "CrawlAttachments": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to index attachments.</p>"}, "FolderIds": {"shape": "FolderIdList", "documentation": "<p>The identifiers of the Quip folders you want to index. You can find the folder ID in your browser URL when you access your folder in Quip. For example, <i>https://quip-company.quipdomain.com/zlLuOVNSarTL/folder-name</i>. The folder ID in this example is \"zlLuOVNSarTL\".</p>"}, "ThreadFieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map attributes or field names of Quip threads to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Quip fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The Quip field names must exist in your Quip custom metadata.</p>"}, "MessageFieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map attributes or field names of Quip messages to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Quip fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The Quip field names must exist in your Quip custom metadata.</p>"}, "AttachmentFieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map attributes or field names of Quip attachments to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Quip fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The Quip field names must exist in your Quip custom metadata.</p>"}, "InclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to include certain files in your Quip file system. Files that match the patterns are included in the index. Files that don't match the patterns are excluded from the index. If a file matches both an inclusion pattern and an exclusion pattern, the exclusion pattern takes precedence, and the file isn't included in the index.</p>"}, "ExclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to exclude certain files in your Quip file system. Files that match the patterns are excluded from the index. Files that don’t match the patterns are included in the index. If a file matches both an inclusion pattern and an exclusion pattern, the exclusion pattern takes precedence, and the file isn't included in the index.</p>"}, "VpcConfiguration": {"shape": "DataSourceVpcConfiguration", "documentation": "<p>Configuration information for an Amazon Virtual Private Cloud (VPC) to connect to your Quip. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/vpc-configuration.html\">Configuring a VPC</a>.</p>"}}, "documentation": "<p>Provides the configuration information to connect to Quip as your data source.</p>"}, "ReadAccessType": {"type": "string", "enum": ["ALLOW", "DENY"]}, "Relevance": {"type": "structure", "members": {"Freshness": {"shape": "DocumentMetadataBoolean", "documentation": "<p>Indicates that this field determines how \"fresh\" a document is. For example, if document 1 was created on November 5, and document 2 was created on October 31, document 1 is \"fresher\" than document 2. You can only set the <code>Freshness</code> field on one <code>DATE</code> type field. Only applies to <code>DATE</code> fields.</p>"}, "Importance": {"shape": "Importance", "documentation": "<p>The relative importance of the field in the search. Larger numbers provide more of a boost than smaller numbers.</p>"}, "Duration": {"shape": "Duration", "documentation": "<p>Specifies the time period that the boost applies to. For example, to make the boost apply to documents with the field value within the last month, you would use \"2628000s\". Once the field value is beyond the specified range, the effect of the boost drops off. The higher the importance, the faster the effect drops off. If you don't specify a value, the default is 3 months. The value of the field is a numeric string followed by the character \"s\", for example \"86400s\" for one day, or \"604800s\" for one week. </p> <p>Only applies to <code>DATE</code> fields.</p>"}, "RankOrder": {"shape": "Order", "documentation": "<p>Determines how values should be interpreted.</p> <p>When the <code>RankOrder</code> field is <code>ASCENDING</code>, higher numbers are better. For example, a document with a rating score of 10 is higher ranking than a document with a rating score of 1.</p> <p>When the <code>RankOrder</code> field is <code>DESCENDING</code>, lower numbers are better. For example, in a task tracking application, a priority 1 task is more important than a priority 5 task.</p> <p>Only applies to <code>LONG</code> and <code>DOUBLE</code> fields.</p>"}, "ValueImportanceMap": {"shape": "ValueImportanceMap", "documentation": "<p>A list of values that should be given a different boost when they appear in the result list. For example, if you are boosting a field called \"department,\" query terms that match the department field are boosted in the result. However, you can add entries from the department field to boost documents with those values higher. </p> <p>For example, you can add entries to the map with names of departments. If you add \"HR\",5 and \"Legal\",3 those departments are given special attention when they appear in the metadata of a document. When those terms appear they are given the specified importance instead of the regular importance for the boost.</p>"}}, "documentation": "<p>Provides information for tuning the relevance of a field in a search. When a query includes terms that match the field, the results are given a boost in the response based on these tuning parameters.</p>"}, "RelevanceFeedback": {"type": "structure", "required": ["ResultId", "RelevanceValue"], "members": {"ResultId": {"shape": "ResultId", "documentation": "<p>The identifier of the search result that the user provided relevance feedback for.</p>"}, "RelevanceValue": {"shape": "RelevanceType", "documentation": "<p>Whether the document was relevant or not relevant to the search.</p>"}}, "documentation": "<p>Provides feedback on how relevant a document is to a search. Your application uses the <code>SubmitFeedback</code> API to provide relevance information.</p>"}, "RelevanceFeedbackList": {"type": "list", "member": {"shape": "RelevanceFeedback"}}, "RelevanceType": {"type": "string", "enum": ["RELEVANT", "NOT_RELEVANT"]}, "RepositoryName": {"type": "string", "max": 64, "min": 1, "pattern": "^[A-Za-z0-9_.-]+$"}, "RepositoryNames": {"type": "list", "member": {"shape": "RepositoryName"}}, "ResourceAlreadyExistException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The resource you want to use already exists. Please check you have provided the correct resource and try again.</p>", "exception": true}, "ResourceInUseException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The resource you want to use is currently in use. Please check you have provided the correct resource and try again.</p>", "exception": true}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The resource you want to use doesn’t exist. Please check you have provided the correct resource and try again.</p>", "exception": true}, "ResourceUnavailableException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The resource you want to use isn't available. Please check you have provided the correct resource and try again.</p>", "exception": true}, "ResultId": {"type": "string", "max": 73, "min": 1}, "RetrieveRequest": {"type": "structure", "required": ["IndexId", "QueryText"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index to retrieve relevant passages for the search.</p>"}, "QueryText": {"shape": "QueryText", "documentation": "<p>The input query text to retrieve relevant passages for the search. Amazon Kendra truncates queries at 30 token words, which excludes punctuation and stop words. Truncation still applies if you use Boolean or more advanced, complex queries.</p>"}, "AttributeFilter": {"shape": "Attribute<PERSON>ilter", "documentation": "<p>Filters search results by document fields/attributes. You can only provide one attribute filter; however, the <code>AndAllFilters</code>, <code>NotFilter</code>, and <code>OrAllFilters</code> parameters contain a list of other filters.</p> <p>The <code>AttributeFilter</code> parameter means you can create a set of filtering rules that a document must satisfy to be included in the query results.</p>"}, "RequestedDocumentAttributes": {"shape": "DocumentAttributeKeyList", "documentation": "<p>A list of document fields/attributes to include in the response. You can limit the response to include certain document fields. By default, all document fields are included in the response.</p>"}, "DocumentRelevanceOverrideConfigurations": {"shape": "DocumentRelevanceOverrideConfigurationList", "documentation": "<p>Overrides relevance tuning configurations of fields/attributes set at the index level.</p> <p>If you use this API to override the relevance tuning configured at the index level, but there is no relevance tuning configured at the index level, then Amazon Kendra does not apply any relevance tuning.</p> <p>If there is relevance tuning configured for fields at the index level, and you use this API to override only some of these fields, then for the fields you did not override, the importance is set to 1.</p>"}, "PageNumber": {"shape": "Integer", "documentation": "<p>Retrieved relevant passages are returned in pages the size of the <code>PageSize</code> parameter. By default, Amazon Kendra returns the first page of results. Use this parameter to get result pages after the first one.</p>"}, "PageSize": {"shape": "Integer", "documentation": "<p>Sets the number of retrieved relevant passages that are returned in each page of results. The default page size is 10. The maximum number of results returned is 100. If you ask for more than 100 results, only 100 are returned.</p>"}, "UserContext": {"shape": "UserContext", "documentation": "<p>The user context token or user and group information.</p>"}}}, "RetrieveResult": {"type": "structure", "members": {"QueryId": {"shape": "QueryId", "documentation": "<p>The identifier of query used for the search. You also use <code>QueryId</code> to identify the search when using the <a href=\"https://docs.aws.amazon.com/kendra/latest/APIReference/API_SubmitFeedback.html\">Submitfeedback</a> API.</p>"}, "ResultItems": {"shape": "RetrieveResultItemList", "documentation": "<p>The results of the retrieved relevant passages for the search.</p>"}}}, "RetrieveResultItem": {"type": "structure", "members": {"Id": {"shape": "ResultId", "documentation": "<p>The identifier of the relevant passage result.</p>"}, "DocumentId": {"shape": "DocumentId", "documentation": "<p>The identifier of the document.</p>"}, "DocumentTitle": {"shape": "DocumentTitle", "documentation": "<p>The title of the document.</p>"}, "Content": {"shape": "Content", "documentation": "<p>The contents of the relevant passage.</p>"}, "DocumentURI": {"shape": "Url", "documentation": "<p>The URI of the original location of the document.</p>"}, "DocumentAttributes": {"shape": "DocumentAttributeList", "documentation": "<p>An array of document fields/attributes assigned to a document in the search results. For example, the document author (<code>_author</code>) or the source URI (<code>_source_uri</code>) of the document.</p>"}, "ScoreAttributes": {"shape": "ScoreAttributes", "documentation": "<p>The confidence score bucket for a retrieved passage result. The confidence bucket provides a relative ranking that indicates how confident Amazon Kendra is that the response is relevant to the query.</p>"}}, "documentation": "<p>A single retrieved relevant passage result.</p>"}, "RetrieveResultItemList": {"type": "list", "member": {"shape": "RetrieveResultItem"}}, "RoleArn": {"type": "string", "max": 1284, "min": 0, "pattern": "arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}"}, "S3BucketName": {"type": "string", "max": 63, "min": 3, "pattern": "[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9]"}, "S3DataSourceConfiguration": {"type": "structure", "required": ["BucketName"], "members": {"BucketName": {"shape": "S3BucketName", "documentation": "<p>The name of the bucket that contains the documents.</p>"}, "InclusionPrefixes": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of S3 prefixes for the documents that should be included in the index.</p>"}, "InclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of glob patterns for documents that should be indexed. If a document that matches an inclusion pattern also matches an exclusion pattern, the document is not indexed.</p> <p>Some <a href=\"https://docs.aws.amazon.com/cli/latest/reference/s3/#use-of-exclude-and-include-filters\">examples</a> are:</p> <ul> <li> <p> <i>*.txt</i> will include all text files in a directory (files with the extension .txt).</p> </li> <li> <p> <i>**/*.txt</i> will include all text files in a directory and its subdirectories.</p> </li> <li> <p> <i>*tax*</i> will include all files in a directory that contain 'tax' in the file name, such as 'tax', 'taxes', 'income_tax'.</p> </li> </ul>"}, "ExclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of glob patterns for documents that should not be indexed. If a document that matches an inclusion prefix or inclusion pattern also matches an exclusion pattern, the document is not indexed.</p> <p>Some <a href=\"https://docs.aws.amazon.com/cli/latest/reference/s3/#use-of-exclude-and-include-filters\">examples</a> are:</p> <ul> <li> <p> <i>*.png , *.jpg</i> will exclude all PNG and JPEG image files in a directory (files with the extensions .png and .jpg).</p> </li> <li> <p> <i>*internal*</i> will exclude all files in a directory that contain 'internal' in the file name, such as 'internal', 'internal_only', 'company_internal'.</p> </li> <li> <p> <i>**/*internal*</i> will exclude all internal-related files in a directory and its subdirectories.</p> </li> </ul>"}, "DocumentsMetadataConfiguration": {"shape": "DocumentsMetadataConfiguration"}, "AccessControlListConfiguration": {"shape": "AccessControlListConfiguration", "documentation": "<p>Provides the path to the S3 bucket that contains the user context filtering files for the data source. For the format of the file, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/s3-acl.html\">Access control for S3 data sources</a>.</p>"}}, "documentation": "<p>Provides the configuration information to connect to an Amazon S3 bucket.</p>"}, "S3ObjectKey": {"type": "string", "max": 1024, "min": 1}, "S3Path": {"type": "structure", "required": ["Bucket", "Key"], "members": {"Bucket": {"shape": "S3BucketName", "documentation": "<p>The name of the S3 bucket that contains the file.</p>"}, "Key": {"shape": "S3ObjectKey", "documentation": "<p>The name of the file.</p>"}}, "documentation": "<p>Information required to find a specific file in an Amazon S3 bucket.</p>"}, "SaaSConfiguration": {"type": "structure", "required": ["OrganizationName", "HostUrl"], "members": {"OrganizationName": {"shape": "OrganizationName", "documentation": "<p>The name of the organization of the GitHub Enterprise Cloud (SaaS) account you want to connect to. You can find your organization name by logging into GitHub desktop and selecting <b>Your organizations</b> under your profile picture dropdown.</p>"}, "HostUrl": {"shape": "Url", "documentation": "<p>The GitHub host URL or API endpoint URL. For example, <i>https://api.github.com</i>.</p>"}}, "documentation": "<p>Provides the configuration information to connect to GitHub Enterprise Cloud (SaaS).</p>"}, "SalesforceChatterFeedConfiguration": {"type": "structure", "required": ["DocumentDataFieldName"], "members": {"DocumentDataFieldName": {"shape": "DataSourceFieldName", "documentation": "<p>The name of the column in the Salesforce FeedItem table that contains the content to index. Typically this is the <code>Body</code> column.</p>"}, "DocumentTitleFieldName": {"shape": "DataSourceFieldName", "documentation": "<p>The name of the column in the Salesforce FeedItem table that contains the title of the document. This is typically the <code>Title</code> column.</p>"}, "FieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>Maps fields from a Salesforce chatter feed into Amazon Kendra index fields.</p>"}, "IncludeFilterTypes": {"shape": "SalesforceChatterFeedIncludeFilterTypes", "documentation": "<p>Filters the documents in the feed based on status of the user. When you specify <code>ACTIVE_USERS</code> only documents from users who have an active account are indexed. When you specify <code>STANDARD_USER</code> only documents for Salesforce standard users are documented. You can specify both.</p>"}}, "documentation": "<p>The configuration information for syncing a Salesforce chatter feed. The contents of the object comes from the Salesforce FeedItem table.</p>"}, "SalesforceChatterFeedIncludeFilterType": {"type": "string", "enum": ["ACTIVE_USER", "STANDARD_USER"]}, "SalesforceChatterFeedIncludeFilterTypes": {"type": "list", "member": {"shape": "SalesforceChatterFeedIncludeFilterType"}, "max": 2, "min": 1}, "SalesforceConfiguration": {"type": "structure", "required": ["ServerUrl", "SecretArn"], "members": {"ServerUrl": {"shape": "Url", "documentation": "<p>The instance URL for the Salesforce site that you want to index.</p>"}, "SecretArn": {"shape": "SecretArn", "documentation": "<p>The Amazon Resource Name (ARN) of an Secrets Managersecret that contains the key/value pairs required to connect to your Salesforce instance. The secret must contain a JSON structure with the following keys:</p> <ul> <li> <p>authenticationUrl - The OAUTH endpoint that Amazon Kendra connects to get an OAUTH token. </p> </li> <li> <p>consumerKey - The application public key generated when you created your Salesforce application.</p> </li> <li> <p>consumerSecret - The application private key generated when you created your Salesforce application.</p> </li> <li> <p>password - The password associated with the user logging in to the Salesforce instance.</p> </li> <li> <p>securityToken - The token associated with the user logging in to the Salesforce instance.</p> </li> <li> <p>username - The user name of the user logging in to the Salesforce instance.</p> </li> </ul>"}, "StandardObjectConfigurations": {"shape": "SalesforceStandardObjectConfigurationList", "documentation": "<p>Configuration of the Salesforce standard objects that Amazon Kendra indexes.</p>"}, "KnowledgeArticleConfiguration": {"shape": "SalesforceKnowledgeArticleConfiguration", "documentation": "<p>Configuration information for the knowledge article types that Amazon Kendra indexes. Amazon Kendra indexes standard knowledge articles and the standard fields of knowledge articles, or the custom fields of custom knowledge articles, but not both.</p>"}, "ChatterFeedConfiguration": {"shape": "SalesforceChatterFeedConfiguration", "documentation": "<p>Configuration information for Salesforce chatter feeds.</p>"}, "CrawlAttachments": {"shape": "Boolean", "documentation": "<p>Indicates whether Amazon Kendra should index attachments to Salesforce objects.</p>"}, "StandardObjectAttachmentConfiguration": {"shape": "SalesforceStandardObjectAttachmentConfiguration", "documentation": "<p>Configuration information for processing attachments to Salesforce standard objects. </p>"}, "IncludeAttachmentFilePatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to include certain documents in your Salesforce. Documents that match the patterns are included in the index. Documents that don't match the patterns are excluded from the index. If a document matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the document isn't included in the index.</p> <p>The pattern is applied to the name of the attached file.</p>"}, "ExcludeAttachmentFilePatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to exclude certain documents in your Salesforce. Documents that match the patterns are excluded from the index. Documents that don't match the patterns are included in the index. If a document matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the document isn't included in the index.</p> <p>The pattern is applied to the name of the attached file.</p>"}}, "documentation": "<p>Provides the configuration information to connect to Salesforce as your data source.</p>"}, "SalesforceCustomKnowledgeArticleTypeConfiguration": {"type": "structure", "required": ["Name", "DocumentDataFieldName"], "members": {"Name": {"shape": "SalesforceCustomKnowledgeArticleTypeName", "documentation": "<p>The name of the configuration.</p>"}, "DocumentDataFieldName": {"shape": "DataSourceFieldName", "documentation": "<p>The name of the field in the custom knowledge article that contains the document data to index.</p>"}, "DocumentTitleFieldName": {"shape": "DataSourceFieldName", "documentation": "<p>The name of the field in the custom knowledge article that contains the document title.</p>"}, "FieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>Maps attributes or field names of the custom knowledge article to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Salesforce fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The Salesforce data source field names must exist in your Salesforce custom metadata.</p>"}}, "documentation": "<p>Provides the configuration information for indexing Salesforce custom articles.</p>"}, "SalesforceCustomKnowledgeArticleTypeConfigurationList": {"type": "list", "member": {"shape": "SalesforceCustomKnowledgeArticleTypeConfiguration"}, "max": 10, "min": 1}, "SalesforceCustomKnowledgeArticleTypeName": {"type": "string", "max": 100, "min": 1, "pattern": "^[a-zA-Z][a-zA-Z0-9_]*$"}, "SalesforceKnowledgeArticleConfiguration": {"type": "structure", "required": ["IncludedStates"], "members": {"IncludedStates": {"shape": "SalesforceKnowledgeArticleStateList", "documentation": "<p>Specifies the document states that should be included when Amazon Kendra indexes knowledge articles. You must specify at least one state.</p>"}, "StandardKnowledgeArticleTypeConfiguration": {"shape": "SalesforceStandardKnowledgeArticleTypeConfiguration", "documentation": "<p>Configuration information for standard Salesforce knowledge articles.</p>"}, "CustomKnowledgeArticleTypeConfigurations": {"shape": "SalesforceCustomKnowledgeArticleTypeConfigurationList", "documentation": "<p>Configuration information for custom Salesforce knowledge articles.</p>"}}, "documentation": "<p>Provides the configuration information for the knowledge article types that Amazon Kendra indexes. Amazon Kendra indexes standard knowledge articles and the standard fields of knowledge articles, or the custom fields of custom knowledge articles, but not both </p>"}, "SalesforceKnowledgeArticleState": {"type": "string", "enum": ["DRAFT", "PUBLISHED", "ARCHIVED"]}, "SalesforceKnowledgeArticleStateList": {"type": "list", "member": {"shape": "SalesforceKnowledgeArticleState"}, "max": 3, "min": 1}, "SalesforceStandardKnowledgeArticleTypeConfiguration": {"type": "structure", "required": ["DocumentDataFieldName"], "members": {"DocumentDataFieldName": {"shape": "DataSourceFieldName", "documentation": "<p>The name of the field that contains the document data to index.</p>"}, "DocumentTitleFieldName": {"shape": "DataSourceFieldName", "documentation": "<p>The name of the field that contains the document title.</p>"}, "FieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>Maps attributes or field names of the knowledge article to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Salesforce fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The Salesforce data source field names must exist in your Salesforce custom metadata.</p>"}}, "documentation": "<p>Provides the configuration information for standard Salesforce knowledge articles.</p>"}, "SalesforceStandardObjectAttachmentConfiguration": {"type": "structure", "members": {"DocumentTitleFieldName": {"shape": "DataSourceFieldName", "documentation": "<p>The name of the field used for the document title.</p>"}, "FieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>One or more objects that map fields in attachments to Amazon Kendra index fields.</p>"}}, "documentation": "<p>Provides the configuration information for processing attachments to Salesforce standard objects.</p>"}, "SalesforceStandardObjectConfiguration": {"type": "structure", "required": ["Name", "DocumentDataFieldName"], "members": {"Name": {"shape": "SalesforceStandardObjectName", "documentation": "<p>The name of the standard object.</p>"}, "DocumentDataFieldName": {"shape": "DataSourceFieldName", "documentation": "<p>The name of the field in the standard object table that contains the document contents.</p>"}, "DocumentTitleFieldName": {"shape": "DataSourceFieldName", "documentation": "<p>The name of the field in the standard object table that contains the document title.</p>"}, "FieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>Maps attributes or field names of the standard object to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Salesforce fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The Salesforce data source field names must exist in your Salesforce custom metadata.</p>"}}, "documentation": "<p>Provides the configuration information for indexing a single standard object.</p>"}, "SalesforceStandardObjectConfigurationList": {"type": "list", "member": {"shape": "SalesforceStandardObjectConfiguration"}, "max": 17, "min": 1}, "SalesforceStandardObjectName": {"type": "string", "enum": ["ACCOUNT", "CAMPAIGN", "CASE", "CONTACT", "CONTRACT", "DOCUMENT", "GROUP", "IDEA", "LEAD", "OPPORTUNITY", "PARTNER", "PRICEBOOK", "PRODUCT", "PROFILE", "SOLUTION", "TASK", "USER"]}, "ScanSchedule": {"type": "string"}, "ScoreAttributes": {"type": "structure", "members": {"ScoreConfidence": {"shape": "ScoreConfidence", "documentation": "<p>A relative ranking for how relevant the response is to the query.</p>"}}, "documentation": "<p>Provides a relative ranking that indicates how confident Amazon Kendra is that the response is relevant to the query.</p>"}, "ScoreConfidence": {"type": "string", "documentation": "Enumeration for query score confidence.", "enum": ["VERY_HIGH", "HIGH", "MEDIUM", "LOW", "NOT_AVAILABLE"]}, "Search": {"type": "structure", "members": {"Facetable": {"shape": "Boolean", "documentation": "<p>Indicates that the field can be used to create search facets, a count of results for each value in the field. The default is <code>false</code> .</p>"}, "Searchable": {"shape": "Boolean", "documentation": "<p>Determines whether the field is used in the search. If the <code>Searchable</code> field is <code>true</code>, you can use relevance tuning to manually tune how Amazon Kendra weights the field in the search. The default is <code>true</code> for string fields and <code>false</code> for number and date fields.</p>"}, "Displayable": {"shape": "Boolean", "documentation": "<p>Determines whether the field is returned in the query response. The default is <code>true</code>.</p>"}, "Sortable": {"shape": "Boolean", "documentation": "<p>Determines whether the field can be used to sort the results of a query. If you specify sorting on a field that does not have <code>Sortable</code> set to <code>true</code>, Amazon Kendra returns an exception. The default is <code>false</code>.</p>"}}, "documentation": "<p>Provides information about how a custom index field is used during a search.</p>"}, "SecretArn": {"type": "string", "max": 1284, "min": 1, "pattern": "arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}"}, "SecurityGroupIdList": {"type": "list", "member": {"shape": "VpcSecurityGroupId"}, "max": 10, "min": 1}, "SeedUrl": {"type": "string", "max": 2048, "min": 1, "pattern": "^(https?):\\/\\/([^\\s]*)"}, "SeedUrlConfiguration": {"type": "structure", "required": ["SeedUrls"], "members": {"SeedUrls": {"shape": "SeedUrlList", "documentation": "<p>The list of seed or starting point URLs of the websites you want to crawl.</p> <p>The list can include a maximum of 100 seed URLs.</p>"}, "WebCrawlerMode": {"shape": "WebCrawlerMode", "documentation": "<p>You can choose one of the following modes:</p> <ul> <li> <p> <code>HOST_ONLY</code>—crawl only the website host names. For example, if the seed URL is \"abc.example.com\", then only URLs with host name \"abc.example.com\" are crawled.</p> </li> <li> <p> <code>SUBDOMAINS</code>—crawl the website host names with subdomains. For example, if the seed URL is \"abc.example.com\", then \"a.abc.example.com\" and \"b.abc.example.com\" are also crawled.</p> </li> <li> <p> <code>EVERYTHING</code>—crawl the website host names with subdomains and other domains that the web pages link to.</p> </li> </ul> <p>The default mode is set to <code>HOST_ONLY</code>.</p>"}}, "documentation": "<p>Provides the configuration information for the seed or starting point URLs to crawl.</p> <p> <i>When selecting websites to index, you must adhere to the <a href=\"https://aws.amazon.com/aup/\">Amazon Acceptable Use Policy</a> and all other Amazon terms. Remember that you must only use Amazon Kendra Web Crawler to index your own web pages, or web pages that you have authorization to index.</i> </p>"}, "SeedUrlList": {"type": "list", "member": {"shape": "SeedUrl"}, "max": 100, "min": 0}, "ServerSideEncryptionConfiguration": {"type": "structure", "members": {"KmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>The identifier of the KMS key. Amazon Kendra doesn't support asymmetric keys.</p>"}}, "documentation": "<p>Provides the identifier of the KMS key used to encrypt data indexed by Amazon Kendra. Amazon Kendra doesn't support asymmetric keys.</p>"}, "ServiceNowAuthenticationType": {"type": "string", "enum": ["HTTP_BASIC", "OAUTH2"]}, "ServiceNowBuildVersionType": {"type": "string", "enum": ["LONDON", "OTHERS"]}, "ServiceNowConfiguration": {"type": "structure", "required": ["HostUrl", "SecretArn", "ServiceNowBuildVersion"], "members": {"HostUrl": {"shape": "ServiceNowHostUrl", "documentation": "<p>The ServiceNow instance that the data source connects to. The host endpoint should look like the following: <i>{instance}.service-now.com.</i> </p>"}, "SecretArn": {"shape": "SecretArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Secrets Manager secret that contains the user name and password required to connect to the ServiceNow instance. You can also provide OAuth authentication credentials of user name, password, client ID, and client secret. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/data-source-servicenow.html\">Using a ServiceNow data source</a>.</p>"}, "ServiceNowBuildVersion": {"shape": "ServiceNowBuildVersionType", "documentation": "<p>The identifier of the release that the ServiceNow host is running. If the host is not running the <code>LONDON</code> release, use <code>OTHERS</code>.</p>"}, "KnowledgeArticleConfiguration": {"shape": "ServiceNowKnowledgeArticleConfiguration", "documentation": "<p>Configuration information for crawling knowledge articles in the ServiceNow site.</p>"}, "ServiceCatalogConfiguration": {"shape": "ServiceNowServiceCatalogConfiguration", "documentation": "<p>Configuration information for crawling service catalogs in the ServiceNow site.</p>"}, "AuthenticationType": {"shape": "ServiceNowAuthenticationType", "documentation": "<p>The type of authentication used to connect to the ServiceNow instance. If you choose <code>HTTP_BASIC</code>, Amazon Kendra is authenticated using the user name and password provided in the Secrets Manager secret in the <code>SecretArn</code> field. If you choose <code>OAUTH2</code>, Amazon Kendra is authenticated using the credentials of client ID, client secret, user name and password.</p> <p>When you use <code>OAUTH2</code> authentication, you must generate a token and a client secret using the ServiceNow console. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/data-source-servicenow.html\">Using a ServiceNow data source</a>.</p>"}}, "documentation": "<p>Provides the configuration information to connect to ServiceNow as your data source.</p>"}, "ServiceNowHostUrl": {"type": "string", "max": 2048, "min": 1, "pattern": "^(?!(^(https?|ftp|file):\\/\\/))[a-z0-9-]+(\\.service-now\\.com)$"}, "ServiceNowKnowledgeArticleConfiguration": {"type": "structure", "required": ["DocumentDataFieldName"], "members": {"CrawlAttachments": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to index attachments to knowledge articles.</p>"}, "IncludeAttachmentFilePatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns applied to include knowledge article attachments. Attachments that match the patterns are included in the index. Items that don't match the patterns are excluded from the index. If an item matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the item isn't included in the index.</p>"}, "ExcludeAttachmentFilePatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns applied to exclude certain knowledge article attachments. Attachments that match the patterns are excluded from the index. Items that don't match the patterns are included in the index. If an item matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the item isn't included in the index.</p>"}, "DocumentDataFieldName": {"shape": "DataSourceFieldName", "documentation": "<p>The name of the ServiceNow field that is mapped to the index document contents field in the Amazon Kendra index.</p>"}, "DocumentTitleFieldName": {"shape": "DataSourceFieldName", "documentation": "<p>The name of the ServiceNow field that is mapped to the index document title field.</p>"}, "FieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>Maps attributes or field names of knoweldge articles to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to ServiceNow fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The ServiceNow data source field names must exist in your ServiceNow custom metadata.</p>"}, "FilterQuery": {"shape": "ServiceNowKnowledgeArticleFilterQuery", "documentation": "<p>A query that selects the knowledge articles to index. The query can return articles from multiple knowledge bases, and the knowledge bases can be public or private.</p> <p>The query string must be one generated by the ServiceNow console. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/servicenow-query.html\">Specifying documents to index with a query</a>. </p>"}}, "documentation": "<p>Provides the configuration information for crawling knowledge articles in the ServiceNow site.</p>"}, "ServiceNowKnowledgeArticleFilterQuery": {"type": "string", "max": 2048, "min": 1, "pattern": "^\\P{C}*$"}, "ServiceNowServiceCatalogConfiguration": {"type": "structure", "required": ["DocumentDataFieldName"], "members": {"CrawlAttachments": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to index attachments to service catalog items.</p>"}, "IncludeAttachmentFilePatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to include certain attachments of catalogs in your ServiceNow. Item that match the patterns are included in the index. Items that don't match the patterns are excluded from the index. If an item matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the item isn't included in the index.</p> <p>The regex is applied to the file name of the attachment.</p>"}, "ExcludeAttachmentFilePatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to exclude certain attachments of catalogs in your ServiceNow. Item that match the patterns are excluded from the index. Items that don't match the patterns are included in the index. If an item matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the item isn't included in the index.</p> <p>The regex is applied to the file name of the attachment.</p>"}, "DocumentDataFieldName": {"shape": "DataSourceFieldName", "documentation": "<p>The name of the ServiceNow field that is mapped to the index document contents field in the Amazon Kendra index.</p>"}, "DocumentTitleFieldName": {"shape": "DataSourceFieldName", "documentation": "<p>The name of the ServiceNow field that is mapped to the index document title field.</p>"}, "FieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>Maps attributes or field names of catalogs to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to ServiceNow fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The ServiceNow data source field names must exist in your ServiceNow custom metadata.</p>"}}, "documentation": "<p>Provides the configuration information for crawling service catalog items in the ServiceNow site</p>"}, "ServiceQuotaExceededException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>You have exceeded the set limits for your Amazon Kendra service. Please see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/quotas.html\">Quotas</a> for more information, or contact <a href=\"http://aws.amazon.com/contact-us/\">Support</a> to inquire about an increase of limits.</p>", "exception": true}, "SharePointConfiguration": {"type": "structure", "required": ["SharePointVersion", "Urls", "SecretArn"], "members": {"SharePointVersion": {"shape": "SharePointVersion", "documentation": "<p>The version of Microsoft SharePoint that you use.</p>"}, "Urls": {"shape": "SharePointUrlList", "documentation": "<p>The Microsoft SharePoint site URLs for the documents you want to index.</p>"}, "SecretArn": {"shape": "SecretArn", "documentation": "<p>The Amazon Resource Name (ARN) of an Secrets Manager secret that contains the user name and password required to connect to the SharePoint instance. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/data-source-sharepoint.html\">Microsoft SharePoint</a>.</p>"}, "CrawlAttachments": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to index document attachments.</p>"}, "UseChangeLog": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to use the SharePoint change log to determine which documents require updating in the index. Depending on the change log's size, it may take longer for Amazon Kendra to use the change log than to scan all of your documents in SharePoint.</p>"}, "InclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to include certain documents in your SharePoint. Documents that match the patterns are included in the index. Documents that don't match the patterns are excluded from the index. If a document matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the document isn't included in the index.</p> <p>The regex applies to the display URL of the SharePoint document.</p>"}, "ExclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to exclude certain documents in your SharePoint. Documents that match the patterns are excluded from the index. Documents that don't match the patterns are included in the index. If a document matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the document isn't included in the index.</p> <p>The regex applies to the display URL of the SharePoint document.</p>"}, "VpcConfiguration": {"shape": "DataSourceVpcConfiguration", "documentation": "<p>Configuration information for an Amazon Virtual Private Cloud to connect to your Microsoft SharePoint. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/vpc-configuration.html\">Configuring a VPC</a>.</p>"}, "FieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map SharePoint data source attributes or field names to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to SharePoint fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The SharePoint data source field names must exist in your SharePoint custom metadata.</p>"}, "DocumentTitleFieldName": {"shape": "DataSourceFieldName", "documentation": "<p>The Microsoft SharePoint attribute field that contains the title of the document.</p>"}, "DisableLocalGroups": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to disable local groups information.</p>"}, "SslCertificateS3Path": {"shape": "S3Path", "documentation": "<p>The path to the SSL certificate stored in an Amazon S3 bucket. You use this to connect to SharePoint Server if you require a secure SSL connection.</p> <p>You can generate a self-signed X509 certificate on any computer using OpenSSL. For an example of using OpenSSL to create an X509 certificate, see <a href=\"https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/configuring-https-ssl.html\">Create and sign an X509 certificate</a>.</p>"}, "AuthenticationType": {"shape": "SharePointOnlineAuthenticationType", "documentation": "<p>Whether you want to connect to SharePoint Online using basic authentication of user name and password, or OAuth authentication of user name, password, client ID, and client secret, or AD App-only authentication of client secret.</p>"}, "ProxyConfiguration": {"shape": "ProxyConfiguration", "documentation": "<p>Configuration information to connect to your Microsoft SharePoint site URLs via instance via a web proxy. You can use this option for SharePoint Server.</p> <p>You must provide the website host name and port number. For example, the host name of <i>https://a.example.com/page1.html</i> is \"a.example.com\" and the port is 443, the standard port for HTTPS.</p> <p>Web proxy credentials are optional and you can use them to connect to a web proxy server that requires basic authentication of user name and password. To store web proxy credentials, you use a secret in Secrets Manager.</p> <p>It is recommended that you follow best security practices when configuring your web proxy. This includes setting up throttling, setting up logging and monitoring, and applying security patches on a regular basis. If you use your web proxy with multiple data sources, sync jobs that occur at the same time could strain the load on your proxy. It is recommended you prepare your proxy beforehand for any security and load requirements.</p>"}}, "documentation": "<p>Provides the configuration information to connect to Microsoft SharePoint as your data source.</p>"}, "SharePointOnlineAuthenticationType": {"type": "string", "enum": ["HTTP_BASIC", "OAUTH2"]}, "SharePointUrlList": {"type": "list", "member": {"shape": "Url"}, "max": 100, "min": 1}, "SharePointVersion": {"type": "string", "enum": ["SHAREPOINT_2013", "SHAREPOINT_2016", "SHAREPOINT_ONLINE", "SHAREPOINT_2019"]}, "SharedDriveId": {"type": "string", "max": 256, "min": 1, "pattern": "^\\P{C}*$"}, "SinceCrawlDate": {"type": "string", "max": 10, "min": 10, "pattern": "(20\\d{2})-(0?[1-9]|1[0-2])-(0?[1-9]|1\\d|2\\d|3[01])"}, "SiteId": {"type": "string", "max": 128, "min": 1, "pattern": "^[A-Za-z0-9-]+$"}, "SiteMap": {"type": "string", "max": 2048, "min": 1, "pattern": "^(https?):\\/\\/([^\\s]*)"}, "SiteMapsConfiguration": {"type": "structure", "required": ["SiteMaps"], "members": {"SiteMaps": {"shape": "SiteMapsList", "documentation": "<p>The list of sitemap URLs of the websites you want to crawl.</p> <p>The list can include a maximum of three sitemap URLs.</p>"}}, "documentation": "<p>Provides the configuration information for the sitemap URLs to crawl.</p> <p> <i>When selecting websites to index, you must adhere to the <a href=\"https://aws.amazon.com/aup/\">Amazon Acceptable Use Policy</a> and all other Amazon terms. Remember that you must only use Amazon Kendra Web Crawler to index your own web pages, or web pages that you have authorization to index.</i> </p>"}, "SiteMapsList": {"type": "list", "member": {"shape": "SiteMap"}, "max": 3, "min": 0}, "SiteUrl": {"type": "string", "max": 2048, "min": 1, "pattern": "^https:\\/\\/[a-zA-Z0-9_\\-\\.]+$"}, "SlackConfiguration": {"type": "structure", "required": ["TeamId", "SecretArn", "SlackEntityList", "SinceCrawlDate"], "members": {"TeamId": {"shape": "TeamId", "documentation": "<p>The identifier of the team in the Slack workspace. For example, <i>T0123456789</i>.</p> <p>You can find your team ID in the URL of the main page of your Slack workspace. When you log in to Slack via a browser, you are directed to the URL of the main page. For example, <i>https://app.slack.com/client/<b>T0123456789</b>/...</i>.</p>"}, "SecretArn": {"shape": "SecretArn", "documentation": "<p>The Amazon Resource Name (ARN) of an Secrets Manager secret that contains the key-value pairs required to connect to your Slack workspace team. The secret must contain a JSON structure with the following keys:</p> <ul> <li> <p>slackToken—The user or bot token created in Slack. For more information on creating a token in Slack, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/data-source-slack.html#slack-authentication\">Authentication for a Slack data source</a>.</p> </li> </ul>"}, "VpcConfiguration": {"shape": "DataSourceVpcConfiguration", "documentation": "<p>Configuration information for an Amazon Virtual Private Cloud to connect to your Slack. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/vpc-configuration.html\">Configuring a VPC</a>.</p>"}, "SlackEntityList": {"shape": "SlackEntityList", "documentation": "<p>Specify whether to index public channels, private channels, group messages, and direct messages. You can specify one or more of these options.</p>"}, "UseChangeLog": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to use the Slack change log to determine which documents require updating in the index. Depending on the Slack change log's size, it may take longer for Amazon Kendra to use the change log than to scan all of your documents in Slack.</p>"}, "CrawlBotMessage": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to index bot messages from your Slack workspace team.</p>"}, "ExcludeArchived": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to exclude archived messages to index from your Slack workspace team.</p>"}, "SinceCrawlDate": {"shape": "SinceCrawlDate", "documentation": "<p>The date to start crawling your data from your Slack workspace team. The date must follow this format: <code>yyyy-mm-dd</code>.</p>"}, "LookBackPeriod": {"shape": "LookBackPeriod", "documentation": "<p>The number of hours for change log to look back from when you last synchronized your data. You can look back up to 7 days or 168 hours.</p> <p>Change log updates your index only if new content was added since you last synced your data. Updated or deleted content from before you last synced does not get updated in your index. To capture updated or deleted content before you last synced, set the <code>LookBackPeriod</code> to the number of hours you want change log to look back.</p>"}, "PrivateChannelFilter": {"shape": "PrivateChannelFilter", "documentation": "<p>The list of private channel names from your Slack workspace team. You use this if you want to index specific private channels, not all private channels. You can also use regular expression patterns to filter private channels.</p>"}, "PublicChannelFilter": {"shape": "PublicChannelFilter", "documentation": "<p>The list of public channel names to index from your Slack workspace team. You use this if you want to index specific public channels, not all public channels. You can also use regular expression patterns to filter public channels.</p>"}, "InclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to include certain attached files in your Slack workspace team. Files that match the patterns are included in the index. Files that don't match the patterns are excluded from the index. If a file matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the file isn't included in the index.</p>"}, "ExclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to exclude certain attached files in your Slack workspace team. Files that match the patterns are excluded from the index. Files that don’t match the patterns are included in the index. If a file matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the file isn't included in the index.</p>"}, "FieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map Slack data source attributes or field names to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Slack fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The Slack data source field names must exist in your Slack custom metadata.</p>"}}, "documentation": "<p>Provides the configuration information to connect to Slack as your data source.</p>"}, "SlackEntity": {"type": "string", "enum": ["PUBLIC_CHANNEL", "PRIVATE_CHANNEL", "GROUP_MESSAGE", "DIRECT_MESSAGE"]}, "SlackEntityList": {"type": "list", "member": {"shape": "SlackEntity"}, "max": 4, "min": 1}, "SnapshotsDataHeaderFields": {"type": "list", "member": {"shape": "String"}}, "SnapshotsDataRecord": {"type": "list", "member": {"shape": "String"}}, "SnapshotsDataRecords": {"type": "list", "member": {"shape": "SnapshotsDataRecord"}}, "SortOrder": {"type": "string", "enum": ["DESC", "ASC"]}, "SortingConfiguration": {"type": "structure", "required": ["DocumentAttributeKey", "SortOrder"], "members": {"DocumentAttributeKey": {"shape": "DocumentAttributeKey", "documentation": "<p>The name of the document attribute used to sort the response. You can use any field that has the <code>Sortable</code> flag set to true.</p> <p>You can also sort by any of the following built-in attributes:</p> <ul> <li> <p>_category</p> </li> <li> <p>_created_at</p> </li> <li> <p>_last_updated_at</p> </li> <li> <p>_version</p> </li> <li> <p>_view_count</p> </li> </ul>"}, "SortOrder": {"shape": "SortOrder", "documentation": "<p>The order that the results should be returned in. In case of ties, the relevance assigned to the result by Amazon Kendra is used as the tie-breaker.</p>"}}, "documentation": "<p>Specifies the document attribute to use to sort the response to a Amazon Kendra query. You can specify a single attribute for sorting. The attribute must have the <code>Sortable</code> flag set to <code>true</code>, otherwise Amazon Kendra returns an exception.</p> <p>You can sort attributes of the following types.</p> <ul> <li> <p>Date value</p> </li> <li> <p>Long value</p> </li> <li> <p>String value</p> </li> </ul> <p>You can't sort attributes of the following type.</p> <ul> <li> <p>String list value</p> </li> </ul>"}, "SortingConfigurationList": {"type": "list", "member": {"shape": "SortingConfiguration"}, "min": 1}, "SourceDocument": {"type": "structure", "members": {"DocumentId": {"shape": "String", "documentation": "<p>The identifier of the document used for a query suggestion.</p>"}, "SuggestionAttributes": {"shape": "DocumentAttributeKeyList", "documentation": "<p>The document fields/attributes used for a query suggestion.</p>"}, "AdditionalAttributes": {"shape": "DocumentAttributeList", "documentation": "<p>The additional fields/attributes to include in the response. You can use additional fields to provide extra information in the response. Additional fields are not used to based suggestions on.</p>"}}, "documentation": "<p>The document ID and its fields/attributes that are used for a query suggestion, if document fields set to use for query suggestions.</p>"}, "SourceDocuments": {"type": "list", "member": {"shape": "SourceDocument"}}, "SpellCorrectedQuery": {"type": "structure", "members": {"SuggestedQueryText": {"shape": "SuggestedQueryText", "documentation": "<p>The query with the suggested spell corrections.</p>"}, "Corrections": {"shape": "CorrectionList", "documentation": "<p>The corrected misspelled word or words in a query.</p>"}}, "documentation": "<p>A query with suggested spell corrections. </p>"}, "SpellCorrectedQueryList": {"type": "list", "member": {"shape": "SpellCorrectedQuery"}}, "SpellCorrectionConfiguration": {"type": "structure", "required": ["IncludeQuerySpellCheckSuggestions"], "members": {"IncludeQuerySpellCheckSuggestions": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to suggest spell corrections for queries.</p>"}}, "documentation": "<p>Provides the configuration information for suggested query spell corrections.</p> <p>Suggested spell corrections are based on words that appear in your indexed documents and how closely a corrected word matches a misspelled word.</p> <p>This feature is designed with certain defaults or limits. For information on the current limits and how to request more support for some limits, see the <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/query-spell-check.html\">Spell Checker documentation</a>.</p>"}, "SqlConfiguration": {"type": "structure", "members": {"QueryIdentifiersEnclosingOption": {"shape": "QueryIdentifiersEnclosingOption", "documentation": "<p>Determines whether Amazon Kendra encloses SQL identifiers for tables and column names in double quotes (\") when making a database query.</p> <p>By default, Amazon Kendra passes SQL identifiers the way that they are entered into the data source configuration. It does not change the case of identifiers or enclose them in quotes.</p> <p>PostgreSQL internally converts uppercase characters to lower case characters in identifiers unless they are quoted. Choosing this option encloses identifiers in quotes so that PostgreSQL does not convert the character's case.</p> <p>For MySQL databases, you must enable the <code>ansi_quotes</code> option when you set this field to <code>DOUBLE_QUOTES</code>.</p>"}}, "documentation": "<p>Provides the configuration information to use a SQL database.</p>"}, "StartDataSourceSyncJobRequest": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source connector to synchronize.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index used with the data source connector.</p>"}}}, "StartDataSourceSyncJobResponse": {"type": "structure", "members": {"ExecutionId": {"shape": "String", "documentation": "<p>Identifies a particular synchronization job.</p>"}}}, "Status": {"type": "structure", "members": {"DocumentId": {"shape": "DocumentId", "documentation": "<p>The identifier of the document.</p>"}, "DocumentStatus": {"shape": "DocumentStatus", "documentation": "<p>The current status of a document.</p> <p>If the document was submitted for deletion, the status is <code>NOT_FOUND</code> after the document is deleted.</p>"}, "FailureCode": {"shape": "String", "documentation": "<p>Indicates the source of the error.</p>"}, "FailureReason": {"shape": "String", "documentation": "<p>Provides detailed information about why the document couldn't be indexed. Use this information to correct the error before you resubmit the document for indexing.</p>"}}, "documentation": "<p>Provides information about the status of documents submitted for indexing.</p>"}, "StopDataSourceSyncJobRequest": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source connector for which to stop the synchronization jobs.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index used with the data source connector.</p>"}}}, "StorageCapacityUnit": {"type": "integer", "min": 0}, "String": {"type": "string", "max": 2048, "min": 1}, "StringList": {"type": "list", "member": {"shape": "String"}}, "SubmitFeedbackRequest": {"type": "structure", "required": ["IndexId", "QueryId"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index that was queried.</p>"}, "QueryId": {"shape": "QueryId", "documentation": "<p>The identifier of the specific query for which you are submitting feedback. The query ID is returned in the response to the <code>Query</code> API.</p>"}, "ClickFeedbackItems": {"shape": "ClickFeedbackList", "documentation": "<p>Tells Amazon Kendra that a particular search result link was chosen by the user. </p>"}, "RelevanceFeedbackItems": {"shape": "RelevanceFeedbackList", "documentation": "<p>Provides Amazon Kendra with relevant or not relevant feedback for whether a particular item was relevant to the search.</p>"}}}, "SubnetId": {"type": "string", "max": 200, "min": 1, "pattern": "[\\-0-9a-zA-Z]+"}, "SubnetIdList": {"type": "list", "member": {"shape": "SubnetId"}, "max": 6, "min": 1}, "SuggestableConfig": {"type": "structure", "members": {"AttributeName": {"shape": "DocumentAttributeKey", "documentation": "<p>The name of the document field/attribute.</p>"}, "Suggestable": {"shape": "ObjectBoolean", "documentation": "<p> <code>TRUE</code> means the document field/attribute is suggestible, so the contents within the field can be used for query suggestions.</p>"}}, "documentation": "<p>Provides the configuration information for a document field/attribute that you want to base query suggestions on.</p>"}, "SuggestableConfigList": {"type": "list", "member": {"shape": "SuggestableConfig"}}, "SuggestedQueryText": {"type": "string", "max": 1000, "min": 1}, "Suggestion": {"type": "structure", "members": {"Id": {"shape": "ResultId", "documentation": "<p>The UUID (universally unique identifier) of a single query suggestion.</p>"}, "Value": {"shape": "SuggestionValue", "documentation": "<p>The value for the UUID (universally unique identifier) of a single query suggestion.</p> <p>The value is the text string of a suggestion.</p>"}, "SourceDocuments": {"shape": "SourceDocuments", "documentation": "<p>The list of document IDs and their fields/attributes that are used for a single query suggestion, if document fields set to use for query suggestions.</p>"}}, "documentation": "<p>A single query suggestion.</p>"}, "SuggestionHighlight": {"type": "structure", "members": {"BeginOffset": {"shape": "Integer", "documentation": "<p>The zero-based location in the response string where the highlight starts.</p>"}, "EndOffset": {"shape": "Integer", "documentation": "<p>The zero-based location in the response string where the highlight ends.</p>"}}, "documentation": "<p>The text highlights for a single query suggestion.</p>"}, "SuggestionHighlightList": {"type": "list", "member": {"shape": "SuggestionHighlight"}}, "SuggestionList": {"type": "list", "member": {"shape": "Suggestion"}}, "SuggestionQueryText": {"type": "string", "pattern": "^\\P{C}*$"}, "SuggestionTextWithHighlights": {"type": "structure", "members": {"Text": {"shape": "String", "documentation": "<p>The query suggestion text to display to the user.</p>"}, "Highlights": {"shape": "SuggestionHighlightList", "documentation": "<p>The beginning and end of the query suggestion text that should be highlighted.</p>"}}, "documentation": "<p>Provides text and information about where to highlight the query suggestion text.</p>"}, "SuggestionType": {"type": "string", "enum": ["QUERY", "DOCUMENT_ATTRIBUTES"]}, "SuggestionTypes": {"type": "list", "member": {"shape": "SuggestionType"}}, "SuggestionValue": {"type": "structure", "members": {"Text": {"shape": "SuggestionTextWithHighlights", "documentation": "<p>The <code>SuggestionTextWithHighlights</code> structure that contains the query suggestion text and highlights.</p>"}}, "documentation": "<p>The <code>SuggestionTextWithHighlights</code> structure information.</p>"}, "TableCell": {"type": "structure", "members": {"Value": {"shape": "String", "documentation": "<p>The actual value or content within a table cell. A table cell could contain a date value of a year, or a string value of text, for example.</p>"}, "TopAnswer": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> if the response of the table cell is the top answer. This is the cell value or content with the highest confidence score or is the most relevant to the query.</p>"}, "Highlighted": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> means that the table cell has a high enough confidence and is relevant to the query, so the value or content should be highlighted.</p>"}, "Header": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> means that the table cell should be treated as a header.</p>"}}, "documentation": "<p>Provides information about a table cell in a table excerpt.</p>"}, "TableCellList": {"type": "list", "member": {"shape": "TableCell"}}, "TableExcerpt": {"type": "structure", "members": {"Rows": {"shape": "TableRowList", "documentation": "<p>A list of rows in the table excerpt.</p>"}, "TotalNumberOfRows": {"shape": "Integer", "documentation": "<p>A count of the number of rows in the original table within the document.</p>"}}, "documentation": "<p>An excerpt from a table within a document. The table excerpt displays up to five columns and three rows, depending on how many table cells are relevant to the query and how many columns are available in the original table. The top most relevant cell is displayed in the table excerpt, along with the next most relevant cells.</p>"}, "TableName": {"type": "string", "max": 100, "min": 1, "pattern": "^[a-zA-Z][a-zA-Z0-9_]*$"}, "TableRow": {"type": "structure", "members": {"Cells": {"shape": "TableCellList", "documentation": "<p>A list of table cells in a row.</p>"}}, "documentation": "<p>Information about a row in a table excerpt.</p>"}, "TableRowList": {"type": "list", "member": {"shape": "TableRow"}}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>The key for the tag. Keys are not case sensitive and must be unique for the index, FAQ, or data source.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The value associated with the tag. The value may be an empty string but it can't be null.</p>"}}, "documentation": "<p>A list of key/value pairs that identify an index, FAQ, or data source. Tag keys and values can consist of Unicode letters, digits, white space, and any of the following symbols: _ . : / = + - @.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 0}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "max": 200, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["ResourceARN", "Tags"], "members": {"ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) of the index, FAQ, or data source to tag.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>A list of tag keys to add to the index, FAQ, or data source. If a tag already exists, the existing value is replaced with the new value.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "TeamId": {"type": "string", "max": 64, "min": 1, "pattern": "[A-Z0-9]*"}, "Template": {"type": "structure", "members": {}, "documentation": "<p>The template schema used for the data source, where templates schemas are supported.</p> <p>See <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/ds-schemas.html\">Data source template schemas</a>.</p>", "document": true}, "TemplateConfiguration": {"type": "structure", "members": {"Template": {"shape": "Template", "documentation": "<p>The template schema used for the data source, where templates schemas are supported.</p> <p>See <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/ds-schemas.html\">Data source template schemas</a>.</p>"}}, "documentation": "<p>Provides a template for the configuration information to connect to your data source.</p>"}, "TenantDomain": {"type": "string", "max": 256, "min": 1, "pattern": "^([a-zA-Z0-9]+(-[a-zA-Z0-9]+)*\\.)+[a-z]{2,}$"}, "TextDocumentStatistics": {"type": "structure", "required": ["IndexedTextDocumentsCount", "IndexedTextBytes"], "members": {"IndexedTextDocumentsCount": {"shape": "IndexedTextDocumentsCount", "documentation": "<p>The number of text documents indexed.</p>"}, "IndexedTextBytes": {"shape": "IndexedTextBytes", "documentation": "<p>The total size, in bytes, of the indexed documents.</p>"}}, "documentation": "<p>Provides information about text documents indexed in an index.</p>"}, "TextWithHighlights": {"type": "structure", "members": {"Text": {"shape": "String", "documentation": "<p>The text to display to the user.</p>"}, "Highlights": {"shape": "HighlightList", "documentation": "<p>The beginning and end of the text that should be highlighted.</p>"}}, "documentation": "<p>Provides text and information about where to highlight the text.</p>"}, "ThesaurusId": {"type": "string", "max": 100, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_-]*"}, "ThesaurusName": {"type": "string", "max": 100, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_-]*"}, "ThesaurusStatus": {"type": "string", "enum": ["CREATING", "ACTIVE", "DELETING", "UPDATING", "ACTIVE_BUT_UPDATE_FAILED", "FAILED"]}, "ThesaurusSummary": {"type": "structure", "members": {"Id": {"shape": "ThesaurusId", "documentation": "<p>The identifier of the thesaurus.</p>"}, "Name": {"shape": "ThesaurusName", "documentation": "<p>The name of the thesaurus.</p>"}, "Status": {"shape": "ThesaurusStatus", "documentation": "<p>The status of the thesaurus.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the thesaurus was created.</p>"}, "UpdatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the thesaurus was last updated.</p>"}}, "documentation": "<p>An array of summary information for a thesaurus or multiple thesauri.</p>"}, "ThesaurusSummaryItems": {"type": "list", "member": {"shape": "ThesaurusSummary"}}, "ThrottlingException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The request was denied due to request throttling. Please reduce the number of requests and try again.</p>", "exception": true}, "TimeRange": {"type": "structure", "members": {"StartTime": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp for the beginning of the time range.</p>"}, "EndTime": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp for the end of the time range.</p>"}}, "documentation": "<p>Provides a range of time.</p>"}, "Timestamp": {"type": "timestamp"}, "Title": {"type": "string"}, "Token": {"type": "string", "max": 100000, "min": 1, "pattern": "^\\P{C}*$"}, "TopDocumentAttributeValueCountPairsSize": {"type": "integer", "max": 5000, "min": 0}, "Type": {"type": "string", "enum": ["SAAS", "ON_PREMISE"]}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceARN", "TagKeys"], "members": {"ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) of the index, FAQ, or data source to remove the tag from.</p>"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>A list of tag keys to remove from the index, FAQ, or data source. If a tag key does not exist on the resource, it is ignored.</p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateAccessControlConfigurationRequest": {"type": "structure", "required": ["IndexId", "Id"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for an access control configuration.</p>"}, "Id": {"shape": "AccessControlConfigurationId", "documentation": "<p>The identifier of the access control configuration you want to update.</p>"}, "Name": {"shape": "AccessControlConfigurationName", "documentation": "<p>A new name for the access control configuration.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A new description for the access control configuration.</p>"}, "AccessControlList": {"shape": "PrincipalList", "documentation": "<p>Information you want to update on principals (users and/or groups) and which documents they should have access to. This is useful for user context filtering, where search results are filtered based on the user or their group access to documents.</p>"}, "HierarchicalAccessControlList": {"shape": "HierarchicalPrincipalList", "documentation": "<p>The updated list of <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_Principal.html\">principal</a> lists that define the hierarchy for which documents users should have access to.</p>"}}}, "UpdateAccessControlConfigurationResponse": {"type": "structure", "members": {}}, "UpdateDataSourceRequest": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source connector you want to update.</p>"}, "Name": {"shape": "DataSourceName", "documentation": "<p>A new name for the data source connector.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index used with the data source connector.</p>"}, "Configuration": {"shape": "DataSourceConfiguration", "documentation": "<p>Configuration information you want to update for the data source connector.</p>"}, "VpcConfiguration": {"shape": "DataSourceVpcConfiguration", "documentation": "<p>Configuration information for an Amazon Virtual Private Cloud to connect to your data source. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/vpc-configuration.html\">Configuring a VPC</a>.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A new description for the data source connector.</p>"}, "Schedule": {"shape": "ScanSchedule", "documentation": "<p>The sync schedule you want to update for the data source connector.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of a role with permission to access the data source and required resources. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/iam-roles.html\">IAM roles for Amazon Kendra</a>.</p>"}, "LanguageCode": {"shape": "LanguageCode", "documentation": "<p>The code for a language you want to update for the data source connector. This allows you to support a language for all documents when updating the data source. English is supported by default. For more information on supported languages, including their codes, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/in-adding-languages.html\">Adding documents in languages other than English</a>.</p>"}, "CustomDocumentEnrichmentConfiguration": {"shape": "CustomDocumentEnrichmentConfiguration", "documentation": "<p>Configuration information you want to update for altering document metadata and content during the document ingestion process.</p> <p>For more information on how to create, modify and delete document metadata, or make other content alterations when you ingest documents into Amazon Kendra, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/custom-document-enrichment.html\">Customizing document metadata during the ingestion process</a>.</p>"}}}, "UpdateExperienceRequest": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {"shape": "ExperienceId", "documentation": "<p>The identifier of your Amazon Kendra experience you want to update.</p>"}, "Name": {"shape": "ExperienceName", "documentation": "<p>A new name for your Amazon Kendra experience.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for your Amazon Kendra experience.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of a role with permission to access <code>Query</code> API, <code>QuerySuggestions</code> API, <code>SubmitFeedback</code> API, and IAM Identity Center that stores your user and group information. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/iam-roles.html\">IAM roles for Amazon Kendra</a>.</p>"}, "Configuration": {"shape": "ExperienceConfiguration", "documentation": "<p>Configuration information you want to update for your Amazon Kendra experience.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A new description for your Amazon Kendra experience.</p>"}}}, "UpdateFeaturedResultsSetRequest": {"type": "structure", "required": ["IndexId", "FeaturedResultsSetId"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index used for featuring results.</p>"}, "FeaturedResultsSetId": {"shape": "FeaturedResultsSetId", "documentation": "<p>The identifier of the set of featured results that you want to update.</p>"}, "FeaturedResultsSetName": {"shape": "FeaturedResultsSetName", "documentation": "<p>A new name for the set of featured results.</p>"}, "Description": {"shape": "FeaturedResultsSetDescription", "documentation": "<p>A new description for the set of featured results.</p>"}, "Status": {"shape": "FeaturedResultsSetStatus", "documentation": "<p>You can set the status to <code>ACTIVE</code> or <code>INACTIVE</code>. When the value is <code>ACTIVE</code>, featured results are ready for use. You can still configure your settings before setting the status to <code>ACTIVE</code>. The queries you specify for featured results must be unique per featured results set for each index, whether the status is <code>ACTIVE</code> or <code>INACTIVE</code>.</p>"}, "QueryTexts": {"shape": "QueryTextList", "documentation": "<p>A list of queries for featuring results. For more information on the list of queries, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_FeaturedResultsSet.html\">FeaturedResultsSet</a>.</p>"}, "FeaturedDocuments": {"shape": "FeaturedDocumentList", "documentation": "<p>A list of document IDs for the documents you want to feature at the top of the search results page. For more information on the list of featured documents, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_FeaturedResultsSet.html\">FeaturedResultsSet</a>.</p>"}}}, "UpdateFeaturedResultsSetResponse": {"type": "structure", "members": {"FeaturedResultsSet": {"shape": "FeaturedResultsSet", "documentation": "<p>Information on the set of featured results. This includes the identifier of the featured results set, whether the featured results set is active or inactive, when the featured results set was last updated, and more.</p>"}}}, "UpdateIndexRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "IndexId", "documentation": "<p>The identifier of the index you want to update.</p>"}, "Name": {"shape": "IndexName", "documentation": "<p>The name of the index you want to update.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>An Identity and Access Management (IAM) role that gives Amazon Kendra permission to access Amazon CloudWatch logs and metrics.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A new description for the index.</p>"}, "DocumentMetadataConfigurationUpdates": {"shape": "DocumentMetadataConfigurationList", "documentation": "<p>The document metadata configuration you want to update for the index. Document metadata are fields or attributes associated with your documents. For example, the company department name associated with each document.</p>"}, "CapacityUnits": {"shape": "CapacityUnitsConfiguration", "documentation": "<p>Sets the number of additional document storage and query capacity units that should be used by the index. You can change the capacity of the index up to 5 times per day, or make 5 API calls.</p> <p>If you are using extra storage units, you can't reduce the storage capacity below what is required to meet the storage needs for your index.</p>"}, "UserTokenConfigurations": {"shape": "UserTokenConfigurationList", "documentation": "<p>The user token configuration.</p>"}, "UserContextPolicy": {"shape": "UserContextPolicy", "documentation": "<p>The user context policy.</p>"}, "UserGroupResolutionConfiguration": {"shape": "UserGroupResolutionConfiguration", "documentation": "<p>Enables fetching access levels of groups and users from an IAM Identity Center identity source. To configure this, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_UserGroupResolutionConfiguration.html\">UserGroupResolutionConfiguration</a>.</p>"}}}, "UpdateQuerySuggestionsBlockListRequest": {"type": "structure", "required": ["IndexId", "Id"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for the block list.</p>"}, "Id": {"shape": "QuerySuggestionsBlockListId", "documentation": "<p>The identifier of the block list you want to update.</p>"}, "Name": {"shape": "QuerySuggestionsBlockListName", "documentation": "<p>A new name for the block list.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A new description for the block list.</p>"}, "SourceS3Path": {"shape": "S3Path", "documentation": "<p>The S3 path where your block list text file sits in S3.</p> <p>If you update your block list and provide the same path to the block list text file in S3, then Amazon Kendra reloads the file to refresh the block list. Amazon Kendra does not automatically refresh your block list. You need to call the <code>UpdateQuerySuggestionsBlockList</code> API to refresh you block list.</p> <p>If you update your block list, then Amazon Kendra asynchronously refreshes all query suggestions with the latest content in the S3 file. This means changes might not take effect immediately.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The IAM (Identity and Access Management) role used to access the block list text file in S3.</p>"}}}, "UpdateQuerySuggestionsConfigRequest": {"type": "structure", "required": ["IndexId"], "members": {"IndexId": {"shape": "IndexId", "documentation": "<p> The identifier of the index with query suggestions you want to update.</p>"}, "Mode": {"shape": "Mode", "documentation": "<p>Set the mode to <code>ENABLED</code> or <code>LEARN_ONLY</code>.</p> <p>By default, Amazon Kendra enables query suggestions. <code>LEARN_ONLY</code> mode allows you to turn off query suggestions. You can to update this at any time.</p> <p>In <code>LEARN_ONLY</code> mode, Amazon Kendra continues to learn from new queries to keep suggestions up to date for when you are ready to switch to ENABLED mode again.</p>"}, "QueryLogLookBackWindowInDays": {"shape": "Integer", "documentation": "<p>How recent your queries are in your query log time window.</p> <p>The time window is the number of days from current day to past days.</p> <p>By default, Amazon Kendra sets this to 180.</p>"}, "IncludeQueriesWithoutUserInformation": {"shape": "ObjectBoolean", "documentation": "<p> <code>TRUE</code> to include queries without user information (i.e. all queries, irrespective of the user), otherwise <code>FALSE</code> to only include queries with user information.</p> <p>If you pass user information to Amazon Kendra along with the queries, you can set this flag to <code>FALSE</code> and instruct Amazon Kendra to only consider queries with user information.</p> <p>If you set to <code>FALSE</code>, Amazon Kendra only considers queries searched at least <code>MinimumQueryCount</code> times across <code>MinimumNumberOfQueryingUsers</code> unique users for suggestions.</p> <p>If you set to <code>TRUE</code>, Amazon Kendra ignores all user information and learns from all queries.</p>"}, "MinimumNumberOfQueryingUsers": {"shape": "MinimumNumberOfQueryingUsers", "documentation": "<p>The minimum number of unique users who must search a query in order for the query to be eligible to suggest to your users.</p> <p>Increasing this number might decrease the number of suggestions. However, this ensures a query is searched by many users and is truly popular to suggest to users.</p> <p>How you tune this setting depends on your specific needs.</p>"}, "MinimumQueryCount": {"shape": "MinimumQueryCount", "documentation": "<p>The the minimum number of times a query must be searched in order to be eligible to suggest to your users.</p> <p>Decreasing this number increases the number of suggestions. However, this affects the quality of suggestions as it sets a low bar for a query to be considered popular to suggest to users.</p> <p>How you tune this setting depends on your specific needs.</p>"}, "AttributeSuggestionsConfig": {"shape": "AttributeSuggestionsUpdateConfig", "documentation": "<p>Configuration information for the document fields/attributes that you want to base query suggestions on.</p>"}}}, "UpdateThesaurusRequest": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {"shape": "ThesaurusId", "documentation": "<p>The identifier of the thesaurus you want to update.</p>"}, "Name": {"shape": "ThesaurusName", "documentation": "<p>A new name for the thesaurus.</p>"}, "IndexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for the thesaurus.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A new description for the thesaurus.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>An IAM role that gives Amazon Kendra permissions to access thesaurus file specified in <code>SourceS3Path</code>.</p>"}, "SourceS3Path": {"shape": "S3Path"}}}, "Url": {"type": "string", "max": 2048, "min": 1, "pattern": "^(https?|ftp|file):\\/\\/([^\\s]*)"}, "Urls": {"type": "structure", "members": {"SeedUrlConfiguration": {"shape": "SeedUrlConfiguration", "documentation": "<p>Configuration of the seed or starting point URLs of the websites you want to crawl.</p> <p>You can choose to crawl only the website host names, or the website host names with subdomains, or the website host names with subdomains and other domains that the web pages link to.</p> <p>You can list up to 100 seed URLs.</p>"}, "SiteMapsConfiguration": {"shape": "SiteMapsConfiguration", "documentation": "<p>Configuration of the sitemap URLs of the websites you want to crawl.</p> <p>Only URLs belonging to the same website host names are crawled. You can list up to three sitemap URLs.</p>"}}, "documentation": "<p>Provides the configuration information of the URLs to crawl.</p> <p>You can only crawl websites that use the secure communication protocol, Hypertext Transfer Protocol Secure (HTTPS). If you receive an error when crawling a website, it could be that the website is blocked from crawling.</p> <p> <i>When selecting websites to index, you must adhere to the <a href=\"https://aws.amazon.com/aup/\">Amazon Acceptable Use Policy</a> and all other Amazon terms. Remember that you must only use Amazon Kendra Web Crawler to index your own web pages, or web pages that you have authorization to index.</i> </p>"}, "UserAccount": {"type": "string", "max": 256, "min": 1, "pattern": "^\\P{C}*$"}, "UserContext": {"type": "structure", "members": {"Token": {"shape": "Token", "documentation": "<p>The user context token for filtering search results for a user. It must be a JWT or a JSON token.</p>"}, "UserId": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The identifier of the user you want to filter search results based on their access to documents.</p>"}, "Groups": {"shape": "Groups", "documentation": "<p>The list of groups you want to filter search results based on the groups' access to documents.</p>"}, "DataSourceGroups": {"shape": "DataSourceGroups", "documentation": "<p>The list of data source groups you want to filter search results based on groups' access to documents in that data source.</p>"}}, "documentation": "<p>Provides information about the user context for an Amazon Kendra index.</p> <p>User context filtering is a kind of personalized search with the benefit of controlling access to documents. For example, not all teams that search the company portal for information should access top-secret company documents, nor are these documents relevant to all users. Only specific users or groups of teams given access to top-secret documents should see these documents in their search results.</p> <p>You provide one of the following:</p> <ul> <li> <p>User token</p> </li> <li> <p>User ID, the groups the user belongs to, and any data sources the groups can access.</p> </li> </ul> <p>If you provide both, an exception is thrown.</p>"}, "UserContextPolicy": {"type": "string", "enum": ["ATTRIBUTE_FILTER", "USER_TOKEN"]}, "UserGroupResolutionConfiguration": {"type": "structure", "required": ["UserGroupResolutionMode"], "members": {"UserGroupResolutionMode": {"shape": "UserGroupResolutionMode", "documentation": "<p>The identity store provider (mode) you want to use to get users and groups. IAM Identity Center is currently the only available mode. Your users and groups must exist in an IAM Identity Center identity source in order to use this mode.</p>"}}, "documentation": "<p>Provides the configuration information to get users and groups from an IAM Identity Center identity source. This is useful for user context filtering, where search results are filtered based on the user or their group access to documents. You can also use the <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/API_PutPrincipalMapping.html\">PutPrincipalMapping</a> API to map users to their groups so that you only need to provide the user ID when you issue the query.</p> <p>To set up an IAM Identity Center identity source in the console to use with Amazon Kendra, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/getting-started-aws-sso.html\">Getting started with an IAM Identity Center identity source</a>. You must also grant the required permissions to use IAM Identity Center with Amazon Kendra. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/iam-roles.html#iam-roles-aws-sso\">IAM roles for IAM Identity Center</a>.</p> <p>Amazon Kendra currently does not support using <code>UserGroupResolutionConfiguration</code> with an Amazon Web Services organization member account for your IAM Identity Center identify source. You must create your index in the management account for the organization in order to use <code>UserGroupResolutionConfiguration</code>.</p>"}, "UserGroupResolutionMode": {"type": "string", "enum": ["AWS_SSO", "NONE"]}, "UserId": {"type": "string", "max": 1024, "min": 1, "pattern": "^\\P{C}*$"}, "UserIdentityConfiguration": {"type": "structure", "members": {"IdentityAttributeName": {"shape": "IdentityAttributeName", "documentation": "<p>The IAM Identity Center field name that contains the identifiers of your users, such as their emails. This is used for <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/user-context-filter.html\">user context filtering</a> and for granting access to your Amazon Kendra experience. You must set up IAM Identity Center with Amazon Kendra. You must include your users and groups in your Access Control List when you ingest documents into your index. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/getting-started-aws-sso.html\">Getting started with an IAM Identity Center identity source</a>.</p>"}}, "documentation": "<p>Provides the configuration information for the identifiers of your users.</p>"}, "UserNameAttributeField": {"type": "string", "max": 100, "min": 1, "pattern": "^\\P{C}*$"}, "UserTokenConfiguration": {"type": "structure", "members": {"JwtTokenTypeConfiguration": {"shape": "JwtTokenTypeConfiguration", "documentation": "<p>Information about the JWT token type configuration.</p>"}, "JsonTokenTypeConfiguration": {"shape": "JsonTokenTypeConfiguration", "documentation": "<p>Information about the JSON token type configuration.</p>"}}, "documentation": "<p>Provides the configuration information for a token.</p>"}, "UserTokenConfigurationList": {"type": "list", "member": {"shape": "UserTokenConfiguration"}, "max": 1}, "ValidationException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The input fails to satisfy the constraints set by the Amazon Kendra service. Please provide the correct input and try again.</p>", "exception": true}, "ValueImportanceMap": {"type": "map", "key": {"shape": "ValueImportanceMapKey"}, "value": {"shape": "Importance"}}, "ValueImportanceMapKey": {"type": "string", "max": 50, "min": 1}, "VisitorId": {"type": "string", "max": 256, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_-]*"}, "VpcSecurityGroupId": {"type": "string", "max": 200, "min": 1, "pattern": "[-0-9a-zA-Z]+"}, "Warning": {"type": "structure", "members": {"Message": {"shape": "WarningMessage", "documentation": "<p>The message that explains the problem with the query.</p>"}, "Code": {"shape": "WarningCode", "documentation": "<p>The code used to show the type of warning for the query.</p>"}}, "documentation": "<p>The warning code and message that explains a problem with a query.</p>"}, "WarningCode": {"type": "string", "enum": ["QUERY_LANGUAGE_INVALID_SYNTAX"]}, "WarningList": {"type": "list", "member": {"shape": "Warning"}, "max": 1, "min": 1}, "WarningMessage": {"type": "string", "max": 2048, "min": 1, "pattern": "^\\P{C}*$"}, "WebCrawlerConfiguration": {"type": "structure", "required": ["Urls"], "members": {"Urls": {"shape": "Urls", "documentation": "<p>Specifies the seed or starting point URLs of the websites or the sitemap URLs of the websites you want to crawl.</p> <p>You can include website subdomains. You can list up to 100 seed URLs and up to three sitemap URLs.</p> <p>You can only crawl websites that use the secure communication protocol, Hypertext Transfer Protocol Secure (HTTPS). If you receive an error when crawling a website, it could be that the website is blocked from crawling.</p> <p> <i>When selecting websites to index, you must adhere to the <a href=\"https://aws.amazon.com/aup/\">Amazon Acceptable Use Policy</a> and all other Amazon terms. Remember that you must only use Amazon Kendra Web Crawler to index your own web pages, or web pages that you have authorization to index.</i> </p>"}, "CrawlDepth": {"shape": "CrawlDepth", "documentation": "<p>The 'depth' or number of levels from the seed level to crawl. For example, the seed URL page is depth 1 and any hyperlinks on this page that are also crawled are depth 2.</p>"}, "MaxLinksPerPage": {"shape": "MaxLinksPerPage", "documentation": "<p>The maximum number of URLs on a web page to include when crawling a website. This number is per web page.</p> <p>As a website’s web pages are crawled, any URLs the web pages link to are also crawled. URLs on a web page are crawled in order of appearance.</p> <p>The default maximum links per page is 100.</p>"}, "MaxContentSizePerPageInMegaBytes": {"shape": "MaxContentSizePerPageInMegaBytes", "documentation": "<p>The maximum size (in MB) of a web page or attachment to crawl.</p> <p>Files larger than this size (in MB) are skipped/not crawled.</p> <p>The default maximum size of a web page or attachment is set to 50 MB.</p>"}, "MaxUrlsPerMinuteCrawlRate": {"shape": "MaxUrlsPerMinuteCrawlRate", "documentation": "<p>The maximum number of URLs crawled per website host per minute.</p> <p>A minimum of one URL is required.</p> <p>The default maximum number of URLs crawled per website host per minute is 300.</p>"}, "UrlInclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to include certain URLs to crawl. URLs that match the patterns are included in the index. URLs that don't match the patterns are excluded from the index. If a URL matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the URL file isn't included in the index.</p>"}, "UrlExclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to exclude certain URLs to crawl. URLs that match the patterns are excluded from the index. URLs that don't match the patterns are included in the index. If a URL matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the URL file isn't included in the index.</p>"}, "ProxyConfiguration": {"shape": "ProxyConfiguration", "documentation": "<p>Configuration information required to connect to your internal websites via a web proxy.</p> <p>You must provide the website host name and port number. For example, the host name of https://a.example.com/page1.html is \"a.example.com\" and the port is 443, the standard port for HTTPS.</p> <p>Web proxy credentials are optional and you can use them to connect to a web proxy server that requires basic authentication. To store web proxy credentials, you use a secret in <a href=\"https://docs.aws.amazon.com/secretsmanager/latest/userguide/intro.html\">Secrets Manager</a>.</p>"}, "AuthenticationConfiguration": {"shape": "AuthenticationConfiguration", "documentation": "<p>Configuration information required to connect to websites using authentication.</p> <p>You can connect to websites using basic authentication of user name and password. You use a secret in <a href=\"https://docs.aws.amazon.com/secretsmanager/latest/userguide/intro.html\">Secrets Manager</a> to store your authentication credentials.</p> <p>You must provide the website host name and port number. For example, the host name of https://a.example.com/page1.html is \"a.example.com\" and the port is 443, the standard port for HTTPS.</p>"}}, "documentation": "<p>Provides the configuration information required for Amazon Kendra Web Crawler.</p>"}, "WebCrawlerMode": {"type": "string", "enum": ["HOST_ONLY", "SUBDOMAINS", "EVERYTHING"]}, "WorkDocsConfiguration": {"type": "structure", "required": ["OrganizationId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier of the directory corresponding to your Amazon WorkDocs site repository.</p> <p>You can find the organization ID in the <a href=\"https://console.aws.amazon.com/directoryservicev2/\">Directory Service</a> by going to <b>Active Directory</b>, then <b>Directories</b>. Your Amazon WorkDocs site directory has an ID, which is the organization ID. You can also set up a new Amazon WorkDocs directory in the Directory Service console and enable a Amazon WorkDocs site for the directory in the Amazon WorkDocs console.</p>"}, "CrawlComments": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to include comments on documents in your index. Including comments in your index means each comment is a document that can be searched on.</p> <p>The default is set to <code>FALSE</code>.</p>"}, "UseChangeLog": {"shape": "Boolean", "documentation": "<p> <code>TRUE</code> to use the Amazon WorkDocs change log to determine which documents require updating in the index. Depending on the change log's size, it may take longer for Amazon Kendra to use the change log than to scan all of your documents in Amazon WorkDocs.</p>"}, "InclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to include certain files in your Amazon WorkDocs site repository. Files that match the patterns are included in the index. Files that don't match the patterns are excluded from the index. If a file matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the file isn't included in the index.</p>"}, "ExclusionPatterns": {"shape": "DataSourceInclusionsExclusionsStrings", "documentation": "<p>A list of regular expression patterns to exclude certain files in your Amazon WorkDocs site repository. Files that match the patterns are excluded from the index. Files that don’t match the patterns are included in the index. If a file matches both an inclusion and exclusion pattern, the exclusion pattern takes precedence and the file isn't included in the index.</p>"}, "FieldMappings": {"shape": "DataSourceToIndexFieldMappingList", "documentation": "<p>A list of <code>DataSourceToIndexFieldMapping</code> objects that map Amazon WorkDocs data source attributes or field names to Amazon Kendra index field names. To create custom fields, use the <code>UpdateIndex</code> API before you map to Amazon WorkDocs fields. For more information, see <a href=\"https://docs.aws.amazon.com/kendra/latest/dg/field-mapping.html\">Mapping data source fields</a>. The Amazon WorkDocs data source field names must exist in your Amazon WorkDocs custom metadata.</p>"}}, "documentation": "<p>Provides the configuration information to connect to Amazon WorkDocs as your data source.</p> <p>Amazon WorkDocs connector is available in Oregon, North Virginia, Sydney, Singapore and Ireland regions.</p>"}}, "documentation": "<p>Amazon Kendra is a service for indexing large document sets.</p>"}