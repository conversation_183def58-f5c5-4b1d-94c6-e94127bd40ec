{"version": "2.0", "metadata": {"apiVersion": "2016-11-23", "endpointPrefix": "states", "jsonVersion": "1.0", "protocol": "json", "serviceAbbreviation": "AWS SFN", "serviceFullName": "AWS Step Functions", "serviceId": "SFN", "signatureVersion": "v4", "targetPrefix": "AWSStepFunctions", "uid": "states-2016-11-23"}, "operations": {"CreateActivity": {"name": "CreateActivity", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateActivityInput"}, "output": {"shape": "CreateActivityOutput"}, "errors": [{"shape": "ActivityLimitExceeded"}, {"shape": "InvalidName"}, {"shape": "TooManyTags"}], "documentation": "<p>Creates an activity. An activity is a task that you write in any programming language and host on any machine that has access to Step Functions. Activities must poll Step Functions using the <code>GetActivityTask</code> API action and respond using <code>SendTask*</code> API actions. This function lets Step Functions know the existence of your activity and returns an identifier for use in a state machine and when polling from the activity.</p> <note> <p>This operation is eventually consistent. The results are best effort and may not reflect very recent updates and changes.</p> </note> <note> <p> <code>CreateActivity</code> is an idempotent API. Subsequent requests won’t create a duplicate resource if it was already created. <code>CreateActivity</code>'s idempotency check is based on the activity <code>name</code>. If a following request has different <code>tags</code> values, Step Functions will ignore these differences and treat it as an idempotent request of the previous. In this case, <code>tags</code> will not be updated, even if they are different.</p> </note>", "idempotent": true}, "CreateStateMachine": {"name": "CreateStateMachine", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateStateMachineInput"}, "output": {"shape": "CreateStateMachineOutput"}, "errors": [{"shape": "InvalidArn"}, {"shape": "InvalidDefinition"}, {"shape": "InvalidName"}, {"shape": "InvalidLoggingConfiguration"}, {"shape": "InvalidTracingConfiguration"}, {"shape": "StateMachineAlreadyExists"}, {"shape": "StateMachineDeleting"}, {"shape": "StateMachineLimitExceeded"}, {"shape": "StateMachineTypeNotSupported"}, {"shape": "TooManyTags"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates a state machine. A state machine consists of a collection of states that can do work (<code>Task</code> states), determine to which states to transition next (<code>Choice</code> states), stop an execution with an error (<code>Fail</code> states), and so on. State machines are specified using a JSON-based, structured language. For more information, see <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-amazon-states-language.html\">Amazon States Language</a> in the Step Functions User Guide.</p> <p>If you set the <code>publish</code> parameter of this API action to <code>true</code>, it publishes version <code>1</code> as the first revision of the state machine.</p> <note> <p>This operation is eventually consistent. The results are best effort and may not reflect very recent updates and changes.</p> </note> <note> <p> <code>CreateStateMachine</code> is an idempotent API. Subsequent requests won’t create a duplicate resource if it was already created. <code>CreateStateMachine</code>'s idempotency check is based on the state machine <code>name</code>, <code>definition</code>, <code>type</code>, <code>LoggingConfiguration</code>, and <code>TracingConfiguration</code>. The check is also based on the <code>publish</code> and <code>versionDescription</code> parameters. If a following request has a different <code>roleArn</code> or <code>tags</code>, Step Functions will ignore these differences and treat it as an idempotent request of the previous. In this case, <code>roleArn</code> and <code>tags</code> will not be updated, even if they are different.</p> </note>", "idempotent": true}, "CreateStateMachineAlias": {"name": "CreateStateMachineAlias", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateStateMachineAliasInput"}, "output": {"shape": "CreateStateMachineAliasOutput"}, "errors": [{"shape": "InvalidArn"}, {"shape": "InvalidName"}, {"shape": "ValidationException"}, {"shape": "StateMachineDeleting"}, {"shape": "ResourceNotFound"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates an <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-state-machine-alias.html\">alias</a> for a state machine that points to one or two <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-state-machine-version.html\">versions</a> of the same state machine. You can set your application to call <a>StartExecution</a> with an alias and update the version the alias uses without changing the client's code.</p> <p>You can also map an alias to split <a>StartExecution</a> requests between two versions of a state machine. To do this, add a second <code>RoutingConfig</code> object in the <code>routingConfiguration</code> parameter. You must also specify the percentage of execution run requests each version should receive in both <code>RoutingConfig</code> objects. Step Functions randomly chooses which version runs a given execution based on the percentage you specify.</p> <p>To create an alias that points to a single version, specify a single <code>RoutingConfig</code> object with a <code>weight</code> set to 100.</p> <p>You can create up to 100 aliases for each state machine. You must delete unused aliases using the <a>DeleteStateMachineAlias</a> API action.</p> <p> <code>CreateStateMachineAlias</code> is an idempotent API. Step Functions bases the idempotency check on the <code>stateMachineArn</code>, <code>description</code>, <code>name</code>, and <code>routingConfiguration</code> parameters. Requests that contain the same values for these parameters return a successful idempotent response without creating a duplicate resource.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>DescribeStateMachineAlias</a> </p> </li> <li> <p> <a>ListStateMachineAliases</a> </p> </li> <li> <p> <a>UpdateStateMachineAlias</a> </p> </li> <li> <p> <a>DeleteStateMachineAlias</a> </p> </li> </ul>"}, "DeleteActivity": {"name": "DeleteActivity", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteActivityInput"}, "output": {"shape": "DeleteActivityOutput"}, "errors": [{"shape": "InvalidArn"}], "documentation": "<p>Deletes an activity.</p>"}, "DeleteStateMachine": {"name": "DeleteStateMachine", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteStateMachineInput"}, "output": {"shape": "DeleteStateMachineOutput"}, "errors": [{"shape": "InvalidArn"}, {"shape": "ValidationException"}], "documentation": "<p>Deletes a state machine. This is an asynchronous operation: It sets the state machine's status to <code>DELETING</code> and begins the deletion process. </p> <p>A qualified state machine ARN can either refer to a <i>Distributed Map state</i> defined within a state machine, a version ARN, or an alias ARN.</p> <p>The following are some examples of qualified and unqualified state machine ARNs:</p> <ul> <li> <p>The following qualified state machine ARN refers to a <i>Distributed Map state</i> with a label <code>mapStateLabel</code> in a state machine named <code>myStateMachine</code>.</p> <p> <code>arn:partition:states:region:account-id:stateMachine:myStateMachine/mapStateLabel</code> </p> <note> <p>If you provide a qualified state machine ARN that refers to a <i>Distributed Map state</i>, the request fails with <code>ValidationException</code>.</p> </note> </li> <li> <p>The following unqualified state machine ARN refers to a state machine named <code>myStateMachine</code>.</p> <p> <code>arn:partition:states:region:account-id:stateMachine:myStateMachine</code> </p> </li> </ul> <p>This API action also deletes all <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-state-machine-version.html\">versions</a> and <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-state-machine-alias.html\">aliases</a> associated with a state machine.</p> <note> <p>For <code>EXPRESS</code> state machines, the deletion happens eventually (usually in less than a minute). Running executions may emit logs after <code>DeleteStateMachine</code> API is called.</p> </note>"}, "DeleteStateMachineAlias": {"name": "DeleteStateMachineAlias", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteStateMachineAliasInput"}, "output": {"shape": "DeleteStateMachineAliasOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "InvalidArn"}, {"shape": "ResourceNotFound"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes a state machine <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-state-machine-alias.html\">alias</a>.</p> <p>After you delete a state machine alias, you can't use it to start executions. When you delete a state machine alias, Step Functions doesn't delete the state machine versions that alias references.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>CreateStateMachineAlias</a> </p> </li> <li> <p> <a>DescribeStateMachineAlias</a> </p> </li> <li> <p> <a>ListStateMachineAliases</a> </p> </li> <li> <p> <a>UpdateStateMachineAlias</a> </p> </li> </ul>"}, "DeleteStateMachineVersion": {"name": "DeleteStateMachineVersion", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteStateMachineVersionInput"}, "output": {"shape": "DeleteStateMachineVersionOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "InvalidArn"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes a state machine <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-state-machine-version.html\">version</a>. After you delete a version, you can't call <a>StartExecution</a> using that version's ARN or use the version with a state machine <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-state-machine-alias.html\">alias</a>.</p> <note> <p>Deleting a state machine version won't terminate its in-progress executions.</p> </note> <note> <p>You can't delete a state machine version currently referenced by one or more aliases. Before you delete a version, you must either delete the aliases or update them to point to another state machine version.</p> </note> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>PublishStateMachineVersion</a> </p> </li> <li> <p> <a>ListStateMachineVersions</a> </p> </li> </ul>"}, "DescribeActivity": {"name": "DescribeActivity", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeActivityInput"}, "output": {"shape": "DescribeActivityOutput"}, "errors": [{"shape": "ActivityDoesNotExist"}, {"shape": "InvalidArn"}], "documentation": "<p>Describes an activity.</p> <note> <p>This operation is eventually consistent. The results are best effort and may not reflect very recent updates and changes.</p> </note>"}, "DescribeExecution": {"name": "DescribeExecution", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeExecutionInput"}, "output": {"shape": "DescribeExecutionOutput"}, "errors": [{"shape": "ExecutionDoesNotExist"}, {"shape": "InvalidArn"}], "documentation": "<p>Provides information about a state machine execution, such as the state machine associated with the execution, the execution input and output, and relevant execution metadata. Use this API action to return the Map Run Amazon Resource Name (ARN) if the execution was dispatched by a Map Run.</p> <p>If you specify a version or alias ARN when you call the <a>StartExecution</a> API action, <code>DescribeExecution</code> returns that ARN.</p> <note> <p>This operation is eventually consistent. The results are best effort and may not reflect very recent updates and changes.</p> </note> <p>Executions of an <code>EXPRESS</code> state machinearen't supported by <code>DescribeExecution</code> unless a Map Run dispatched them.</p>"}, "DescribeMapRun": {"name": "DescribeMapRun", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeMapRunInput"}, "output": {"shape": "DescribeMapRunOutput"}, "errors": [{"shape": "ResourceNotFound"}, {"shape": "InvalidArn"}], "documentation": "<p>Provides information about a Map Run's configuration, progress, and results. For more information, see <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-examine-map-run.html\">Examining Map Run</a> in the <i>Step Functions Developer Guide</i>.</p>"}, "DescribeStateMachine": {"name": "DescribeStateMachine", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeStateMachineInput"}, "output": {"shape": "DescribeStateMachineOutput"}, "errors": [{"shape": "InvalidArn"}, {"shape": "StateMachineDoesNotExist"}], "documentation": "<p>Provides information about a state machine's definition, its IAM role Amazon Resource Name (ARN), and configuration.</p> <p>A qualified state machine ARN can either refer to a <i>Distributed Map state</i> defined within a state machine, a version ARN, or an alias ARN.</p> <p>The following are some examples of qualified and unqualified state machine ARNs:</p> <ul> <li> <p>The following qualified state machine ARN refers to a <i>Distributed Map state</i> with a label <code>mapStateLabel</code> in a state machine named <code>myStateMachine</code>.</p> <p> <code>arn:partition:states:region:account-id:stateMachine:myStateMachine/mapStateLabel</code> </p> <note> <p>If you provide a qualified state machine ARN that refers to a <i>Distributed Map state</i>, the request fails with <code>ValidationException</code>.</p> </note> </li> <li> <p>The following qualified state machine ARN refers to an alias named <code>PROD</code>.</p> <p> <code>arn:&lt;partition&gt;:states:&lt;region&gt;:&lt;account-id&gt;:stateMachine:&lt;myStateMachine:PROD&gt;</code> </p> <note> <p>If you provide a qualified state machine ARN that refers to a version ARN or an alias ARN, the request starts execution for that version or alias.</p> </note> </li> <li> <p>The following unqualified state machine ARN refers to a state machine named <code>myStateMachine</code>.</p> <p> <code>arn:&lt;partition&gt;:states:&lt;region&gt;:&lt;account-id&gt;:stateMachine:&lt;myStateMachine&gt;</code> </p> </li> </ul> <p>This API action returns the details for a state machine version if the <code>stateMachineArn</code> you specify is a state machine version ARN.</p> <note> <p>This operation is eventually consistent. The results are best effort and may not reflect very recent updates and changes.</p> </note>"}, "DescribeStateMachineAlias": {"name": "DescribeStateMachineAlias", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeStateMachineAliasInput"}, "output": {"shape": "DescribeStateMachineAliasOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "InvalidArn"}, {"shape": "ResourceNotFound"}], "documentation": "<p>Returns details about a state machine <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-state-machine-alias.html\">alias</a>.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>CreateStateMachineAlias</a> </p> </li> <li> <p> <a>ListStateMachineAliases</a> </p> </li> <li> <p> <a>UpdateStateMachineAlias</a> </p> </li> <li> <p> <a>DeleteStateMachineAlias</a> </p> </li> </ul>"}, "DescribeStateMachineForExecution": {"name": "DescribeStateMachineForExecution", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeStateMachineForExecutionInput"}, "output": {"shape": "DescribeStateMachineForExecutionOutput"}, "errors": [{"shape": "ExecutionDoesNotExist"}, {"shape": "InvalidArn"}], "documentation": "<p>Provides information about a state machine's definition, its execution role ARN, and configuration. If a Map Run dispatched the execution, this action returns the Map Run Amazon Resource Name (ARN) in the response. The state machine returned is the state machine associated with the Map Run.</p> <note> <p>This operation is eventually consistent. The results are best effort and may not reflect very recent updates and changes.</p> </note> <p>This API action is not supported by <code>EXPRESS</code> state machines.</p>"}, "GetActivityTask": {"name": "GetActivityTask", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetActivityTaskInput"}, "output": {"shape": "GetActivityTaskOutput"}, "errors": [{"shape": "ActivityDoesNotExist"}, {"shape": "ActivityWorkerLimitExceeded"}, {"shape": "InvalidArn"}], "documentation": "<p>Used by workers to retrieve a task (with the specified activity ARN) which has been scheduled for execution by a running state machine. This initiates a long poll, where the service holds the HTTP connection open and responds as soon as a task becomes available (i.e. an execution of a task of this type is needed.) The maximum time the service holds on to the request before responding is 60 seconds. If no task is available within 60 seconds, the poll returns a <code>taskToken</code> with a null string.</p> <note> <p>This API action isn't logged in CloudTrail.</p> </note> <important> <p>Workers should set their client side socket timeout to at least 65 seconds (5 seconds higher than the maximum time the service may hold the poll request).</p> <p>Polling with <code>GetActivityTask</code> can cause latency in some implementations. See <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/bp-activity-pollers.html\">Avoid Latency When Polling for Activity Tasks</a> in the Step Functions Developer Guide.</p> </important>"}, "GetExecutionHistory": {"name": "GetExecutionHistory", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetExecutionHistoryInput"}, "output": {"shape": "GetExecutionHistoryOutput"}, "errors": [{"shape": "ExecutionDoesNotExist"}, {"shape": "InvalidArn"}, {"shape": "InvalidToken"}], "documentation": "<p>Returns the history of the specified execution as a list of events. By default, the results are returned in ascending order of the <code>timeStamp</code> of the events. Use the <code>reverseOrder</code> parameter to get the latest events first.</p> <p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return an <i>HTTP 400 InvalidToken</i> error.</p> <p>This API action is not supported by <code>EXPRESS</code> state machines.</p>"}, "ListActivities": {"name": "ListActivities", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListActivitiesInput"}, "output": {"shape": "ListActivitiesOutput"}, "errors": [{"shape": "InvalidToken"}], "documentation": "<p>Lists the existing activities.</p> <p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return an <i>HTTP 400 InvalidToken</i> error.</p> <note> <p>This operation is eventually consistent. The results are best effort and may not reflect very recent updates and changes.</p> </note>"}, "ListExecutions": {"name": "ListExecutions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListExecutionsInput"}, "output": {"shape": "ListExecutionsOutput"}, "errors": [{"shape": "InvalidArn"}, {"shape": "InvalidToken"}, {"shape": "StateMachineDoesNotExist"}, {"shape": "StateMachineTypeNotSupported"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFound"}], "documentation": "<p>Lists all executions of a state machine or a Map Run. You can list all executions related to a state machine by specifying a state machine Amazon Resource Name (ARN), or those related to a Map Run by specifying a Map Run ARN.</p> <p>You can also provide a state machine <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-state-machine-alias.html\">alias</a> ARN or <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-state-machine-version.html\">version</a> ARN to list the executions associated with a specific alias or version.</p> <p>Results are sorted by time, with the most recent execution first.</p> <p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return an <i>HTTP 400 InvalidToken</i> error.</p> <note> <p>This operation is eventually consistent. The results are best effort and may not reflect very recent updates and changes.</p> </note> <p>This API action is not supported by <code>EXPRESS</code> state machines.</p>"}, "ListMapRuns": {"name": "ListMapRuns", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListMapRunsInput"}, "output": {"shape": "ListMapRunsOutput"}, "errors": [{"shape": "ExecutionDoesNotExist"}, {"shape": "InvalidArn"}, {"shape": "InvalidToken"}], "documentation": "<p>Lists all Map Runs that were started by a given state machine execution. Use this API action to obtain Map Run ARNs, and then call <code>DescribeMapRun</code> to obtain more information, if needed.</p>"}, "ListStateMachineAliases": {"name": "ListStateMachineAliases", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListStateMachineAliasesInput"}, "output": {"shape": "ListStateMachineAliasesOutput"}, "errors": [{"shape": "InvalidArn"}, {"shape": "InvalidToken"}, {"shape": "ResourceNotFound"}, {"shape": "StateMachineDoesNotExist"}, {"shape": "StateMachineDeleting"}], "documentation": "<p>Lists <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-state-machine-alias.html\">aliases</a> for a specified state machine ARN. Results are sorted by time, with the most recently created aliases listed first. </p> <p>To list aliases that reference a state machine <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-state-machine-version.html\">version</a>, you can specify the version ARN in the <code>stateMachineArn</code> parameter.</p> <p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return an <i>HTTP 400 InvalidToken</i> error.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>CreateStateMachineAlias</a> </p> </li> <li> <p> <a>DescribeStateMachineAlias</a> </p> </li> <li> <p> <a>UpdateStateMachineAlias</a> </p> </li> <li> <p> <a>DeleteStateMachineAlias</a> </p> </li> </ul>"}, "ListStateMachineVersions": {"name": "ListStateMachineVersions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListStateMachineVersionsInput"}, "output": {"shape": "ListStateMachineVersionsOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "InvalidArn"}, {"shape": "InvalidToken"}], "documentation": "<p>Lists <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-state-machine-version.html\">versions</a> for the specified state machine Amazon Resource Name (ARN).</p> <p>The results are sorted in descending order of the version creation time.</p> <p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return an <i>HTTP 400 InvalidToken</i> error.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>PublishStateMachineVersion</a> </p> </li> <li> <p> <a>DeleteStateMachineVersion</a> </p> </li> </ul>"}, "ListStateMachines": {"name": "ListStateMachines", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListStateMachinesInput"}, "output": {"shape": "ListStateMachinesOutput"}, "errors": [{"shape": "InvalidToken"}], "documentation": "<p>Lists the existing state machines.</p> <p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return an <i>HTTP 400 InvalidToken</i> error.</p> <note> <p>This operation is eventually consistent. The results are best effort and may not reflect very recent updates and changes.</p> </note>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceInput"}, "output": {"shape": "ListTagsForResourceOutput"}, "errors": [{"shape": "InvalidArn"}, {"shape": "ResourceNotFound"}], "documentation": "<p>List tags for a given resource.</p> <p>Tags may only contain Unicode letters, digits, white space, or these symbols: <code>_ . : / = + - @</code>.</p>"}, "PublishStateMachineVersion": {"name": "PublishStateMachineVersion", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PublishStateMachineVersionInput"}, "output": {"shape": "PublishStateMachineVersionOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "StateMachineDeleting"}, {"shape": "StateMachineDoesNotExist"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "InvalidArn"}], "documentation": "<p>Creates a <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-state-machine-version.html\">version</a> from the current revision of a state machine. Use versions to create immutable snapshots of your state machine. You can start executions from versions either directly or with an alias. To create an alias, use <a>CreateStateMachineAlias</a>.</p> <p>You can publish up to 1000 versions for each state machine. You must manually delete unused versions using the <a>DeleteStateMachineVersion</a> API action.</p> <p> <code>PublishStateMachineVersion</code> is an idempotent API. It doesn't create a duplicate state machine version if it already exists for the current revision. Step Functions bases <code>PublishStateMachineVersion</code>'s idempotency check on the <code>stateMachineArn</code>, <code>name</code>, and <code>revisionId</code> parameters. Requests with the same parameters return a successful idempotent response. If you don't specify a <code>revisionId</code>, Step Functions checks for a previously published version of the state machine's current revision.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>DeleteStateMachineVersion</a> </p> </li> <li> <p> <a>ListStateMachineVersions</a> </p> </li> </ul>", "idempotent": true}, "SendTaskFailure": {"name": "SendTaskFailure", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "SendTaskFailureInput"}, "output": {"shape": "SendTaskFailureOutput"}, "errors": [{"shape": "TaskDoesNotExist"}, {"shape": "InvalidToken"}, {"shape": "TaskTimedOut"}], "documentation": "<p>Used by activity workers and task states using the <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/connect-to-resource.html#connect-wait-token\">callback</a> pattern to report that the task identified by the <code>taskToken</code> failed.</p>"}, "SendTaskHeartbeat": {"name": "SendTaskHeartbeat", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "SendTaskHeartbeatInput"}, "output": {"shape": "SendTaskHeartbeatOutput"}, "errors": [{"shape": "TaskDoesNotExist"}, {"shape": "InvalidToken"}, {"shape": "TaskTimedOut"}], "documentation": "<p>Used by activity workers and task states using the <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/connect-to-resource.html#connect-wait-token\">callback</a> pattern to report to Step Functions that the task represented by the specified <code>taskToken</code> is still making progress. This action resets the <code>Heartbeat</code> clock. The <code>Heartbeat</code> threshold is specified in the state machine's Amazon States Language definition (<code>HeartbeatSeconds</code>). This action does not in itself create an event in the execution history. However, if the task times out, the execution history contains an <code>ActivityTimedOut</code> entry for activities, or a <code>TaskTimedOut</code> entry for for tasks using the <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/connect-to-resource.html#connect-sync\">job run</a> or <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/connect-to-resource.html#connect-wait-token\">callback</a> pattern.</p> <note> <p>The <code>Timeout</code> of a task, defined in the state machine's Amazon States Language definition, is its maximum allowed duration, regardless of the number of <a>SendTaskHeartbeat</a> requests received. Use <code>HeartbeatSeconds</code> to configure the timeout interval for heartbeats.</p> </note>"}, "SendTaskSuccess": {"name": "SendTaskSuccess", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "SendTaskSuccessInput"}, "output": {"shape": "SendTaskSuccessOutput"}, "errors": [{"shape": "TaskDoesNotExist"}, {"shape": "InvalidOutput"}, {"shape": "InvalidToken"}, {"shape": "TaskTimedOut"}], "documentation": "<p>Used by activity workers and task states using the <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/connect-to-resource.html#connect-wait-token\">callback</a> pattern to report that the task identified by the <code>taskToken</code> completed successfully.</p>"}, "StartExecution": {"name": "StartExecution", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartExecutionInput"}, "output": {"shape": "StartExecutionOutput"}, "errors": [{"shape": "ExecutionLimitExceeded"}, {"shape": "ExecutionAlreadyExists"}, {"shape": "InvalidArn"}, {"shape": "InvalidExecutionInput"}, {"shape": "InvalidName"}, {"shape": "StateMachineDoesNotExist"}, {"shape": "StateMachineDeleting"}, {"shape": "ValidationException"}], "documentation": "<p>Starts a state machine execution.</p> <p>A qualified state machine ARN can either refer to a <i>Distributed Map state</i> defined within a state machine, a version ARN, or an alias ARN.</p> <p>The following are some examples of qualified and unqualified state machine ARNs:</p> <ul> <li> <p>The following qualified state machine ARN refers to a <i>Distributed Map state</i> with a label <code>mapStateLabel</code> in a state machine named <code>myStateMachine</code>.</p> <p> <code>arn:partition:states:region:account-id:stateMachine:myStateMachine/mapStateLabel</code> </p> <note> <p>If you provide a qualified state machine ARN that refers to a <i>Distributed Map state</i>, the request fails with <code>ValidationException</code>.</p> </note> </li> <li> <p>The following qualified state machine ARN refers to an alias named <code>PROD</code>.</p> <p> <code>arn:&lt;partition&gt;:states:&lt;region&gt;:&lt;account-id&gt;:stateMachine:&lt;myStateMachine:PROD&gt;</code> </p> <note> <p>If you provide a qualified state machine ARN that refers to a version ARN or an alias ARN, the request starts execution for that version or alias.</p> </note> </li> <li> <p>The following unqualified state machine ARN refers to a state machine named <code>myStateMachine</code>.</p> <p> <code>arn:&lt;partition&gt;:states:&lt;region&gt;:&lt;account-id&gt;:stateMachine:&lt;myStateMachine&gt;</code> </p> </li> </ul> <p>If you start an execution with an unqualified state machine ARN, Step Functions uses the latest revision of the state machine for the execution.</p> <p>To start executions of a state machine <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-state-machine-version.html\">version</a>, call <code>StartExecution</code> and provide the version ARN or the ARN of an <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-state-machine-alias.html\">alias</a> that points to the version.</p> <note> <p> <code>StartExecution</code> is idempotent for <code>STANDARD</code> workflows. For a <code>STANDARD</code> workflow, if you call <code>StartExecution</code> with the same name and input as a running execution, the call succeeds and return the same response as the original request. If the execution is closed or if the input is different, it returns a <code>400 ExecutionAlreadyExists</code> error. You can reuse names after 90 days. </p> <p> <code>StartExecution</code> isn't idempotent for <code>EXPRESS</code> workflows. </p> </note>", "idempotent": true}, "StartSyncExecution": {"name": "StartSyncExecution", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartSyncExecutionInput"}, "output": {"shape": "StartSyncExecutionOutput"}, "errors": [{"shape": "InvalidArn"}, {"shape": "InvalidExecutionInput"}, {"shape": "InvalidName"}, {"shape": "StateMachineDoesNotExist"}, {"shape": "StateMachineDeleting"}, {"shape": "StateMachineTypeNotSupported"}], "documentation": "<p>Starts a Synchronous Express state machine execution. <code>StartSyncExecution</code> is not available for <code>STANDARD</code> workflows.</p> <note> <p> <code>StartSyncExecution</code> will return a <code>200 OK</code> response, even if your execution fails, because the status code in the API response doesn't reflect function errors. Error codes are reserved for errors that prevent your execution from running, such as permissions errors, limit errors, or issues with your state machine code and configuration. </p> </note> <note> <p>This API action isn't logged in CloudTrail.</p> </note>", "endpoint": {"hostPrefix": "sync-"}}, "StopExecution": {"name": "StopExecution", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StopExecutionInput"}, "output": {"shape": "StopExecutionOutput"}, "errors": [{"shape": "ExecutionDoesNotExist"}, {"shape": "InvalidArn"}, {"shape": "ValidationException"}], "documentation": "<p>Stops an execution.</p> <p>This API action is not supported by <code>EXPRESS</code> state machines.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceInput"}, "output": {"shape": "TagResourceOutput"}, "errors": [{"shape": "InvalidArn"}, {"shape": "ResourceNotFound"}, {"shape": "TooManyTags"}], "documentation": "<p>Add a tag to a Step Functions resource.</p> <p>An array of key-value pairs. For more information, see <a href=\"https://docs.aws.amazon.com/awsaccountbilling/latest/aboutv2/cost-alloc-tags.html\">Using Cost Allocation Tags</a> in the <i>Amazon Web Services Billing and Cost Management User Guide</i>, and <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access_iam-tags.html\">Controlling Access Using IAM Tags</a>.</p> <p>Tags may only contain Unicode letters, digits, white space, or these symbols: <code>_ . : / = + - @</code>.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceInput"}, "output": {"shape": "UntagResourceOutput"}, "errors": [{"shape": "InvalidArn"}, {"shape": "ResourceNotFound"}], "documentation": "<p>Remove a tag from a Step Functions resource</p>"}, "UpdateMapRun": {"name": "UpdateMapRun", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateMapRunInput"}, "output": {"shape": "UpdateMapRunOutput"}, "errors": [{"shape": "ResourceNotFound"}, {"shape": "InvalidArn"}, {"shape": "ValidationException"}], "documentation": "<p>Updates an in-progress Map Run's configuration to include changes to the settings that control maximum concurrency and Map Run failure.</p>"}, "UpdateStateMachine": {"name": "UpdateStateMachine", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateStateMachineInput"}, "output": {"shape": "UpdateStateMachineOutput"}, "errors": [{"shape": "InvalidArn"}, {"shape": "InvalidDefinition"}, {"shape": "InvalidLoggingConfiguration"}, {"shape": "InvalidTracingConfiguration"}, {"shape": "MissingRequiredParameter"}, {"shape": "StateMachineDeleting"}, {"shape": "StateMachineDoesNotExist"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}], "documentation": "<p>Updates an existing state machine by modifying its <code>definition</code>, <code>roleArn</code>, or <code>loggingConfiguration</code>. Running executions will continue to use the previous <code>definition</code> and <code>roleArn</code>. You must include at least one of <code>definition</code> or <code>roleArn</code> or you will receive a <code>MissingRequiredParameter</code> error.</p> <p>A qualified state machine ARN refers to a <i>Distributed Map state</i> defined within a state machine. For example, the qualified state machine ARN <code>arn:partition:states:region:account-id:stateMachine:stateMachineName/mapStateLabel</code> refers to a <i>Distributed Map state</i> with a label <code>mapStateLabel</code> in the state machine named <code>stateMachineName</code>.</p> <p>A qualified state machine ARN can either refer to a <i>Distributed Map state</i> defined within a state machine, a version ARN, or an alias ARN.</p> <p>The following are some examples of qualified and unqualified state machine ARNs:</p> <ul> <li> <p>The following qualified state machine ARN refers to a <i>Distributed Map state</i> with a label <code>mapStateLabel</code> in a state machine named <code>myStateMachine</code>.</p> <p> <code>arn:partition:states:region:account-id:stateMachine:myStateMachine/mapStateLabel</code> </p> <note> <p>If you provide a qualified state machine ARN that refers to a <i>Distributed Map state</i>, the request fails with <code>ValidationException</code>.</p> </note> </li> <li> <p>The following qualified state machine ARN refers to an alias named <code>PROD</code>.</p> <p> <code>arn:&lt;partition&gt;:states:&lt;region&gt;:&lt;account-id&gt;:stateMachine:&lt;myStateMachine:PROD&gt;</code> </p> <note> <p>If you provide a qualified state machine ARN that refers to a version ARN or an alias ARN, the request starts execution for that version or alias.</p> </note> </li> <li> <p>The following unqualified state machine ARN refers to a state machine named <code>myStateMachine</code>.</p> <p> <code>arn:&lt;partition&gt;:states:&lt;region&gt;:&lt;account-id&gt;:stateMachine:&lt;myStateMachine&gt;</code> </p> </li> </ul> <p>After you update your state machine, you can set the <code>publish</code> parameter to <code>true</code> in the same action to publish a new <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-state-machine-version.html\">version</a>. This way, you can opt-in to strict versioning of your state machine.</p> <note> <p>Step Functions assigns monotonically increasing integers for state machine versions, starting at version number 1.</p> </note> <note> <p>All <code>StartExecution</code> calls within a few seconds use the updated <code>definition</code> and <code>roleArn</code>. Executions started immediately after you call <code>UpdateStateMachine</code> may use the previous state machine <code>definition</code> and <code>roleArn</code>. </p> </note>", "idempotent": true}, "UpdateStateMachineAlias": {"name": "UpdateStateMachineAlias", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateStateMachineAliasInput"}, "output": {"shape": "UpdateStateMachineAliasOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "InvalidArn"}, {"shape": "ResourceNotFound"}, {"shape": "ConflictException"}], "documentation": "<p>Updates the configuration of an existing state machine <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-state-machine-alias.html\">alias</a> by modifying its <code>description</code> or <code>routingConfiguration</code>.</p> <p>You must specify at least one of the <code>description</code> or <code>routingConfiguration</code> parameters to update a state machine alias.</p> <note> <p> <code>UpdateStateMachineAlias</code> is an idempotent API. Step Functions bases the idempotency check on the <code>stateMachineAliasArn</code>, <code>description</code>, and <code>routingConfiguration</code> parameters. Requests with the same parameters return an idempotent response.</p> </note> <note> <p>This operation is eventually consistent. All <a>StartExecution</a> requests made within a few seconds use the latest alias configuration. Executions started immediately after calling <code>UpdateStateMachineAlias</code> may use the previous routing configuration.</p> </note> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>CreateStateMachineAlias</a> </p> </li> <li> <p> <a>DescribeStateMachineAlias</a> </p> </li> <li> <p> <a>ListStateMachineAliases</a> </p> </li> <li> <p> <a>DeleteStateMachineAlias</a> </p> </li> </ul>"}}, "shapes": {"ActivityDoesNotExist": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The specified activity does not exist.</p>", "exception": true}, "ActivityFailedEventDetails": {"type": "structure", "members": {"error": {"shape": "SensitiveError", "documentation": "<p>The error code of the failure.</p>"}, "cause": {"shape": "SensitiveCause", "documentation": "<p>A more detailed explanation of the cause of the failure.</p>"}}, "documentation": "<p>Contains details about an activity that failed during an execution.</p>"}, "ActivityLimitExceeded": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The maximum number of activities has been reached. Existing activities must be deleted before a new activity can be created.</p>", "exception": true}, "ActivityList": {"type": "list", "member": {"shape": "ActivityListItem"}}, "ActivityListItem": {"type": "structure", "required": ["activityArn", "name", "creationDate"], "members": {"activityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the activity.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the activity.</p> <p>A name must <i>not</i> contain:</p> <ul> <li> <p>white space</p> </li> <li> <p>brackets <code>&lt; &gt; { } [ ]</code> </p> </li> <li> <p>wildcard characters <code>? *</code> </p> </li> <li> <p>special characters <code>\" # % \\ ^ | ~ ` $ &amp; , ; : /</code> </p> </li> <li> <p>control characters (<code>U+0000-001F</code>, <code>U+007F-009F</code>)</p> </li> </ul> <p>To enable logging with CloudWatch Logs, the name should only contain 0-9, A-Z, a-z, - and _.</p>"}, "creationDate": {"shape": "Timestamp", "documentation": "<p>The date the activity is created.</p>"}}, "documentation": "<p>Contains details about an activity.</p>"}, "ActivityScheduleFailedEventDetails": {"type": "structure", "members": {"error": {"shape": "SensitiveError", "documentation": "<p>The error code of the failure.</p>"}, "cause": {"shape": "SensitiveCause", "documentation": "<p>A more detailed explanation of the cause of the failure.</p>"}}, "documentation": "<p>Contains details about an activity schedule failure that occurred during an execution.</p>"}, "ActivityScheduledEventDetails": {"type": "structure", "required": ["resource"], "members": {"resource": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the scheduled activity.</p>"}, "input": {"shape": "SensitiveData", "documentation": "<p>The JSON data input to the activity task. Length constraints apply to the payload size, and are expressed as bytes in UTF-8 encoding.</p>"}, "inputDetails": {"shape": "HistoryEventExecutionDataDetails", "documentation": "<p>Contains details about the input for an execution history event.</p>"}, "timeoutInSeconds": {"shape": "TimeoutInSeconds", "documentation": "<p>The maximum allowed duration of the activity task.</p>", "box": true}, "heartbeatInSeconds": {"shape": "TimeoutInSeconds", "documentation": "<p>The maximum allowed duration between two heartbeats for the activity task.</p>", "box": true}}, "documentation": "<p>Contains details about an activity scheduled during an execution.</p>"}, "ActivityStartedEventDetails": {"type": "structure", "members": {"workerName": {"shape": "Identity", "documentation": "<p>The name of the worker that the task is assigned to. These names are provided by the workers when calling <a>GetActivityTask</a>.</p>"}}, "documentation": "<p>Contains details about the start of an activity during an execution.</p>"}, "ActivitySucceededEventDetails": {"type": "structure", "members": {"output": {"shape": "SensitiveData", "documentation": "<p>The JSON data output by the activity task. Length constraints apply to the payload size, and are expressed as bytes in UTF-8 encoding.</p>"}, "outputDetails": {"shape": "HistoryEventExecutionDataDetails", "documentation": "<p>Contains details about the output of an execution history event.</p>"}}, "documentation": "<p>Contains details about an activity that successfully terminated during an execution.</p>"}, "ActivityTimedOutEventDetails": {"type": "structure", "members": {"error": {"shape": "SensitiveError", "documentation": "<p>The error code of the failure.</p>"}, "cause": {"shape": "SensitiveCause", "documentation": "<p>A more detailed explanation of the cause of the timeout.</p>"}}, "documentation": "<p>Contains details about an activity timeout that occurred during an execution.</p>"}, "ActivityWorkerLimitExceeded": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The maximum number of workers concurrently polling for activity tasks has been reached.</p>", "exception": true}, "AliasDescription": {"type": "string", "max": 256, "sensitive": true}, "Arn": {"type": "string", "max": 256, "min": 1}, "BilledDuration": {"type": "long", "min": 0}, "BilledMemoryUsed": {"type": "long", "min": 0}, "BillingDetails": {"type": "structure", "members": {"billedMemoryUsedInMB": {"shape": "BilledMemoryUsed", "documentation": "<p>Billed memory consumption of your workflow, in MB.</p>"}, "billedDurationInMilliseconds": {"shape": "BilledDuration", "documentation": "<p>Billed duration of your workflow, in milliseconds.</p>"}}, "documentation": "<p>An object that describes workflow billing details.</p>"}, "CharacterRestrictedName": {"type": "string", "max": 80, "min": 1, "pattern": "^(?=.*[a-zA-Z_\\-\\.])[a-zA-Z0-9_\\-\\.]+$"}, "CloudWatchEventsExecutionDataDetails": {"type": "structure", "members": {"included": {"shape": "includedDetails", "documentation": "<p>Indicates whether input or output was included in the response. Always <code>true</code> for API calls. </p>"}}, "documentation": "<p>Provides details about execution input or output.</p>"}, "CloudWatchLogsLogGroup": {"type": "structure", "members": {"logGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the the CloudWatch log group to which you want your logs emitted to. The ARN must end with <code>:*</code> </p>"}}, "documentation": "<p/>"}, "ConflictException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>Updating or deleting a resource can cause an inconsistent state. This error occurs when there're concurrent requests for <a>DeleteStateMachineVersion</a>, <a>PublishStateMachineVersion</a>, or <a>UpdateStateMachine</a> with the <code>publish</code> parameter set to <code>true</code>.</p> <p>HTTP Status Code: 409</p>", "exception": true}, "ConnectorParameters": {"type": "string", "max": 262144, "min": 0, "sensitive": true}, "CreateActivityInput": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "Name", "documentation": "<p>The name of the activity to create. This name must be unique for your Amazon Web Services account and region for 90 days. For more information, see <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/limits.html#service-limits-state-machine-executions\"> Limits Related to State Machine Executions</a> in the <i>Step Functions Developer Guide</i>.</p> <p>A name must <i>not</i> contain:</p> <ul> <li> <p>white space</p> </li> <li> <p>brackets <code>&lt; &gt; { } [ ]</code> </p> </li> <li> <p>wildcard characters <code>? *</code> </p> </li> <li> <p>special characters <code>\" # % \\ ^ | ~ ` $ &amp; , ; : /</code> </p> </li> <li> <p>control characters (<code>U+0000-001F</code>, <code>U+007F-009F</code>)</p> </li> </ul> <p>To enable logging with CloudWatch Logs, the name should only contain 0-9, A-Z, a-z, - and _.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>The list of tags to add to a resource.</p> <p>An array of key-value pairs. For more information, see <a href=\"https://docs.aws.amazon.com/awsaccountbilling/latest/aboutv2/cost-alloc-tags.html\">Using Cost Allocation Tags</a> in the <i>Amazon Web Services Billing and Cost Management User Guide</i>, and <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access_iam-tags.html\">Controlling Access Using IAM Tags</a>.</p> <p>Tags may only contain Unicode letters, digits, white space, or these symbols: <code>_ . : / = + - @</code>.</p>"}}}, "CreateActivityOutput": {"type": "structure", "required": ["activityArn", "creationDate"], "members": {"activityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the created activity.</p>"}, "creationDate": {"shape": "Timestamp", "documentation": "<p>The date the activity is created.</p>"}}}, "CreateStateMachineAliasInput": {"type": "structure", "required": ["name", "routingConfiguration"], "members": {"description": {"shape": "AliasDescription", "documentation": "<p>A description for the state machine alias.</p>"}, "name": {"shape": "CharacterRestrictedName", "documentation": "<p>The name of the state machine alias.</p> <p>To avoid conflict with version ARNs, don't use an integer in the name of the alias.</p>"}, "routingConfiguration": {"shape": "RoutingConfigurationList", "documentation": "<p>The routing configuration of a state machine alias. The routing configuration shifts execution traffic between two state machine versions. <code>routingConfiguration</code> contains an array of <code>RoutingConfig</code> objects that specify up to two state machine versions. Step Functions then randomly choses which version to run an execution with based on the weight assigned to each <code>RoutingConfig</code>.</p>"}}}, "CreateStateMachineAliasOutput": {"type": "structure", "required": ["stateMachineAliasArn", "creationDate"], "members": {"stateMachineAliasArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the created state machine alias.</p>"}, "creationDate": {"shape": "Timestamp", "documentation": "<p>The date the state machine alias was created.</p>"}}}, "CreateStateMachineInput": {"type": "structure", "required": ["name", "definition", "roleArn"], "members": {"name": {"shape": "Name", "documentation": "<p>The name of the state machine. </p> <p>A name must <i>not</i> contain:</p> <ul> <li> <p>white space</p> </li> <li> <p>brackets <code>&lt; &gt; { } [ ]</code> </p> </li> <li> <p>wildcard characters <code>? *</code> </p> </li> <li> <p>special characters <code>\" # % \\ ^ | ~ ` $ &amp; , ; : /</code> </p> </li> <li> <p>control characters (<code>U+0000-001F</code>, <code>U+007F-009F</code>)</p> </li> </ul> <p>To enable logging with CloudWatch Logs, the name should only contain 0-9, A-Z, a-z, - and _.</p>"}, "definition": {"shape": "Definition", "documentation": "<p>The Amazon States Language definition of the state machine. See <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-amazon-states-language.html\">Amazon States Language</a>.</p>"}, "roleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role to use for this state machine.</p>"}, "type": {"shape": "StateMachineType", "documentation": "<p>Determines whether a Standard or Express state machine is created. The default is <code>STANDARD</code>. You cannot update the <code>type</code> of a state machine once it has been created.</p>"}, "loggingConfiguration": {"shape": "LoggingConfiguration", "documentation": "<p>Defines what execution history events are logged and where they are logged.</p> <note> <p>By default, the <code>level</code> is set to <code>OFF</code>. For more information see <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/cloudwatch-log-level.html\">Log Levels</a> in the Step Functions User Guide.</p> </note>"}, "tags": {"shape": "TagList", "documentation": "<p>Tags to be added when creating a state machine.</p> <p>An array of key-value pairs. For more information, see <a href=\"https://docs.aws.amazon.com/awsaccountbilling/latest/aboutv2/cost-alloc-tags.html\">Using Cost Allocation Tags</a> in the <i>Amazon Web Services Billing and Cost Management User Guide</i>, and <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access_iam-tags.html\">Controlling Access Using IAM Tags</a>.</p> <p>Tags may only contain Unicode letters, digits, white space, or these symbols: <code>_ . : / = + - @</code>.</p>"}, "tracingConfiguration": {"shape": "TracingConfiguration", "documentation": "<p>Selects whether X-Ray tracing is enabled.</p>"}, "publish": {"shape": "Publish", "documentation": "<p>Set to <code>true</code> to publish the first version of the state machine during creation. The default is <code>false</code>.</p>"}, "versionDescription": {"shape": "VersionDescription", "documentation": "<p>Sets description about the state machine version. You can only set the description if the <code>publish</code> parameter is set to <code>true</code>. Otherwise, if you set <code>versionDescription</code>, but <code>publish</code> to <code>false</code>, this API action throws <code>ValidationException</code>.</p>"}}}, "CreateStateMachineOutput": {"type": "structure", "required": ["stateMachineArn", "creationDate"], "members": {"stateMachineArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the created state machine.</p>"}, "creationDate": {"shape": "Timestamp", "documentation": "<p>The date the state machine is created.</p>"}, "stateMachineVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the created state machine version. If you do not set the <code>publish</code> parameter to <code>true</code>, this field returns null value.</p>"}}}, "Definition": {"type": "string", "max": 1048576, "min": 1, "sensitive": true}, "DeleteActivityInput": {"type": "structure", "required": ["activityArn"], "members": {"activityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the activity to delete.</p>"}}}, "DeleteActivityOutput": {"type": "structure", "members": {}}, "DeleteStateMachineAliasInput": {"type": "structure", "required": ["stateMachineAliasArn"], "members": {"stateMachineAliasArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the state machine alias to delete.</p>"}}}, "DeleteStateMachineAliasOutput": {"type": "structure", "members": {}}, "DeleteStateMachineInput": {"type": "structure", "required": ["stateMachineArn"], "members": {"stateMachineArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the state machine to delete.</p>"}}}, "DeleteStateMachineOutput": {"type": "structure", "members": {}}, "DeleteStateMachineVersionInput": {"type": "structure", "required": ["stateMachineVersionArn"], "members": {"stateMachineVersionArn": {"shape": "LongArn", "documentation": "<p>The Amazon Resource Name (ARN) of the state machine version to delete.</p>"}}}, "DeleteStateMachineVersionOutput": {"type": "structure", "members": {}}, "DescribeActivityInput": {"type": "structure", "required": ["activityArn"], "members": {"activityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the activity to describe.</p>"}}}, "DescribeActivityOutput": {"type": "structure", "required": ["activityArn", "name", "creationDate"], "members": {"activityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the activity.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the activity.</p> <p>A name must <i>not</i> contain:</p> <ul> <li> <p>white space</p> </li> <li> <p>brackets <code>&lt; &gt; { } [ ]</code> </p> </li> <li> <p>wildcard characters <code>? *</code> </p> </li> <li> <p>special characters <code>\" # % \\ ^ | ~ ` $ &amp; , ; : /</code> </p> </li> <li> <p>control characters (<code>U+0000-001F</code>, <code>U+007F-009F</code>)</p> </li> </ul> <p>To enable logging with CloudWatch Logs, the name should only contain 0-9, A-Z, a-z, - and _.</p>"}, "creationDate": {"shape": "Timestamp", "documentation": "<p>The date the activity is created.</p>"}}}, "DescribeExecutionInput": {"type": "structure", "required": ["executionArn"], "members": {"executionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the execution to describe.</p>"}}}, "DescribeExecutionOutput": {"type": "structure", "required": ["executionArn", "stateMachineArn", "status", "startDate"], "members": {"executionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the execution.</p>"}, "stateMachineArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the executed stated machine.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the execution.</p> <p>A name must <i>not</i> contain:</p> <ul> <li> <p>white space</p> </li> <li> <p>brackets <code>&lt; &gt; { } [ ]</code> </p> </li> <li> <p>wildcard characters <code>? *</code> </p> </li> <li> <p>special characters <code>\" # % \\ ^ | ~ ` $ &amp; , ; : /</code> </p> </li> <li> <p>control characters (<code>U+0000-001F</code>, <code>U+007F-009F</code>)</p> </li> </ul> <p>To enable logging with CloudWatch Logs, the name should only contain 0-9, A-Z, a-z, - and _.</p>"}, "status": {"shape": "ExecutionStatus", "documentation": "<p>The current status of the execution.</p>"}, "startDate": {"shape": "Timestamp", "documentation": "<p>The date the execution is started.</p>"}, "stopDate": {"shape": "Timestamp", "documentation": "<p>If the execution ended, the date the execution stopped.</p>"}, "input": {"shape": "SensitiveData", "documentation": "<p>The string that contains the JSON input data of the execution. Length constraints apply to the payload size, and are expressed as bytes in UTF-8 encoding.</p>"}, "inputDetails": {"shape": "CloudWatchEventsExecutionDataDetails"}, "output": {"shape": "SensitiveData", "documentation": "<p>The JSON output data of the execution. Length constraints apply to the payload size, and are expressed as bytes in UTF-8 encoding.</p> <note> <p>This field is set only if the execution succeeds. If the execution fails, this field is null.</p> </note>"}, "outputDetails": {"shape": "CloudWatchEventsExecutionDataDetails"}, "traceHeader": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The X-Ray trace header that was passed to the execution.</p>"}, "mapRunArn": {"shape": "LongArn", "documentation": "<p>The Amazon Resource Name (ARN) that identifies a Map Run, which dispatched this execution.</p>"}, "error": {"shape": "SensitiveError", "documentation": "<p>The error string if the state machine execution failed.</p>"}, "cause": {"shape": "SensitiveCause", "documentation": "<p>The cause string if the state machine execution failed.</p>"}, "stateMachineVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the state machine version associated with the execution. The version ARN is a combination of state machine ARN and the version number separated by a colon (:). For example, <code>stateMachineARN:1</code>.</p> <p>If you start an execution from a <code>StartExecution</code> request without specifying a state machine version or alias ARN, Step Functions returns a null value.</p>"}, "stateMachineAliasArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the state machine alias associated with the execution. The alias ARN is a combination of state machine ARN and the alias name separated by a colon (:). For example, <code>stateMachineARN:PROD</code>.</p> <p>If you start an execution from a <code>StartExecution</code> request with a state machine version ARN, this field will be null.</p>"}}}, "DescribeMapRunInput": {"type": "structure", "required": ["mapRunArn"], "members": {"mapRunArn": {"shape": "LongArn", "documentation": "<p>The Amazon Resource Name (ARN) that identifies a Map Run.</p>"}}}, "DescribeMapRunOutput": {"type": "structure", "required": ["mapRunArn", "executionArn", "status", "startDate", "maxConcurrency", "toleratedFailurePercentage", "toleratedFailureCount", "itemCounts", "executionCounts"], "members": {"mapRunArn": {"shape": "LongArn", "documentation": "<p>The Amazon Resource Name (ARN) that identifies a Map Run.</p>"}, "executionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the execution in which the Map Run was started.</p>"}, "status": {"shape": "MapRunStatus", "documentation": "<p>The current status of the Map Run.</p>"}, "startDate": {"shape": "Timestamp", "documentation": "<p>The date when the Map Run was started.</p>"}, "stopDate": {"shape": "Timestamp", "documentation": "<p>The date when the Map Run was stopped.</p>"}, "maxConcurrency": {"shape": "MaxConcurrency", "documentation": "<p>The maximum number of child workflow executions configured to run in parallel for the Map Run at the same time.</p>"}, "toleratedFailurePercentage": {"shape": "ToleratedFailurePercentage", "documentation": "<p>The maximum percentage of failed child workflow executions before the Map Run fails.</p>"}, "toleratedFailureCount": {"shape": "ToleratedFailureCount", "documentation": "<p>The maximum number of failed child workflow executions before the Map Run fails.</p>"}, "itemCounts": {"shape": "MapRunItemCounts", "documentation": "<p>A JSON object that contains information about the total number of items, and the item count for each processing status, such as <code>pending</code> and <code>failed</code>.</p>"}, "executionCounts": {"shape": "MapRunExecutionCounts", "documentation": "<p>A JSON object that contains information about the total number of child workflow executions for the Map Run, and the count of child workflow executions for each status, such as <code>failed</code> and <code>succeeded</code>.</p>"}}}, "DescribeStateMachineAliasInput": {"type": "structure", "required": ["stateMachineAliasArn"], "members": {"stateMachineAliasArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the state machine alias.</p>"}}}, "DescribeStateMachineAliasOutput": {"type": "structure", "members": {"stateMachineAliasArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the state machine alias.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the state machine alias.</p>"}, "description": {"shape": "AliasDescription", "documentation": "<p>A description of the alias.</p>"}, "routingConfiguration": {"shape": "RoutingConfigurationList", "documentation": "<p>The routing configuration of the alias.</p>"}, "creationDate": {"shape": "Timestamp", "documentation": "<p>The date the state machine alias was created.</p>"}, "updateDate": {"shape": "Timestamp", "documentation": "<p>The date the state machine alias was last updated.</p> <p>For a newly created state machine, this is the same as the creation date.</p>"}}}, "DescribeStateMachineForExecutionInput": {"type": "structure", "required": ["executionArn"], "members": {"executionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the execution you want state machine information for.</p>"}}}, "DescribeStateMachineForExecutionOutput": {"type": "structure", "required": ["stateMachineArn", "name", "definition", "roleArn", "updateDate"], "members": {"stateMachineArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the state machine associated with the execution.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the state machine associated with the execution.</p>"}, "definition": {"shape": "Definition", "documentation": "<p>The Amazon States Language definition of the state machine. See <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-amazon-states-language.html\">Amazon States Language</a>.</p>"}, "roleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role of the State Machine for the execution. </p>"}, "updateDate": {"shape": "Timestamp", "documentation": "<p>The date and time the state machine associated with an execution was updated. For a newly created state machine, this is the creation date.</p>"}, "loggingConfiguration": {"shape": "LoggingConfiguration"}, "tracingConfiguration": {"shape": "TracingConfiguration", "documentation": "<p>Selects whether X-Ray tracing is enabled.</p>"}, "mapRunArn": {"shape": "LongArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Map Run that started the child workflow execution. This field is returned only if the <code>executionArn</code> is a child workflow execution that was started by a Distributed Map state.</p>"}, "label": {"shape": "MapRunLabel", "documentation": "<p>A user-defined or an auto-generated string that identifies a <code>Map</code> state. This ﬁeld is returned only if the <code>executionArn</code> is a child workflow execution that was started by a Distributed Map state.</p>"}, "revisionId": {"shape": "RevisionId", "documentation": "<p>The revision identifier for the state machine. The first revision ID when you create the state machine is null.</p> <p>Use the state machine <code>revisionId</code> parameter to compare the revision of a state machine with the configuration of the state machine used for executions without performing a diff of the properties, such as <code>definition</code> and <code>roleArn</code>.</p>"}}}, "DescribeStateMachineInput": {"type": "structure", "required": ["stateMachineArn"], "members": {"stateMachineArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the state machine for which you want the information.</p> <p>If you specify a state machine version ARN, this API returns details about that version. The version ARN is a combination of state machine ARN and the version number separated by a colon (:). For example, <code>stateMachineARN:1</code>.</p>"}}}, "DescribeStateMachineOutput": {"type": "structure", "required": ["stateMachineArn", "name", "definition", "roleArn", "type", "creationDate"], "members": {"stateMachineArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the state machine.</p> <p>If you specified a state machine version ARN in your request, the API returns the version ARN. The version ARN is a combination of state machine ARN and the version number separated by a colon (:). For example, <code>stateMachineARN:1</code>.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the state machine.</p> <p>A name must <i>not</i> contain:</p> <ul> <li> <p>white space</p> </li> <li> <p>brackets <code>&lt; &gt; { } [ ]</code> </p> </li> <li> <p>wildcard characters <code>? *</code> </p> </li> <li> <p>special characters <code>\" # % \\ ^ | ~ ` $ &amp; , ; : /</code> </p> </li> <li> <p>control characters (<code>U+0000-001F</code>, <code>U+007F-009F</code>)</p> </li> </ul> <p>To enable logging with CloudWatch Logs, the name should only contain 0-9, A-Z, a-z, - and _.</p>"}, "status": {"shape": "StateMachineStatus", "documentation": "<p>The current status of the state machine.</p>"}, "definition": {"shape": "Definition", "documentation": "<p>The Amazon States Language definition of the state machine. See <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-amazon-states-language.html\">Amazon States Language</a>.</p>"}, "roleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role used when creating this state machine. (The IAM role maintains security by granting Step Functions access to Amazon Web Services resources.)</p>"}, "type": {"shape": "StateMachineType", "documentation": "<p>The <code>type</code> of the state machine (<code>STANDARD</code> or <code>EXPRESS</code>).</p>"}, "creationDate": {"shape": "Timestamp", "documentation": "<p>The date the state machine is created.</p> <p>For a state machine version, <code>creationDate</code> is the date the version was created.</p>"}, "loggingConfiguration": {"shape": "LoggingConfiguration"}, "tracingConfiguration": {"shape": "TracingConfiguration", "documentation": "<p>Selects whether X-Ray tracing is enabled.</p>"}, "label": {"shape": "MapRunLabel", "documentation": "<p>A user-defined or an auto-generated string that identifies a <code>Map</code> state. This parameter is present only if the <code>stateMachineArn</code> specified in input is a qualified state machine ARN.</p>"}, "revisionId": {"shape": "RevisionId", "documentation": "<p>The revision identifier for the state machine.</p> <p>Use the <code>revisionId</code> parameter to compare between versions of a state machine configuration used for executions without performing a diff of the properties, such as <code>definition</code> and <code>roleArn</code>.</p>"}, "description": {"shape": "VersionDescription", "documentation": "<p>The description of the state machine version.</p>"}}}, "Enabled": {"type": "boolean"}, "ErrorMessage": {"type": "string"}, "EventId": {"type": "long"}, "ExecutionAbortedEventDetails": {"type": "structure", "members": {"error": {"shape": "SensitiveError", "documentation": "<p>The error code of the failure.</p>"}, "cause": {"shape": "SensitiveCause", "documentation": "<p>A more detailed explanation of the cause of the failure.</p>"}}, "documentation": "<p>Contains details about an abort of an execution.</p>"}, "ExecutionAlreadyExists": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The execution has the same <code>name</code> as another execution (but a different <code>input</code>).</p> <note> <p>Executions with the same <code>name</code> and <code>input</code> are considered idempotent.</p> </note>", "exception": true}, "ExecutionDoesNotExist": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The specified execution does not exist.</p>", "exception": true}, "ExecutionFailedEventDetails": {"type": "structure", "members": {"error": {"shape": "SensitiveError", "documentation": "<p>The error code of the failure.</p>"}, "cause": {"shape": "SensitiveCause", "documentation": "<p>A more detailed explanation of the cause of the failure.</p>"}}, "documentation": "<p>Contains details about an execution failure event.</p>"}, "ExecutionLimitExceeded": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The maximum number of running executions has been reached. Running executions must end or be stopped before a new execution can be started.</p>", "exception": true}, "ExecutionList": {"type": "list", "member": {"shape": "ExecutionListItem"}}, "ExecutionListItem": {"type": "structure", "required": ["executionArn", "stateMachineArn", "name", "status", "startDate"], "members": {"executionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the execution.</p>"}, "stateMachineArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the state machine that ran the execution.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the execution.</p> <p>A name must <i>not</i> contain:</p> <ul> <li> <p>white space</p> </li> <li> <p>brackets <code>&lt; &gt; { } [ ]</code> </p> </li> <li> <p>wildcard characters <code>? *</code> </p> </li> <li> <p>special characters <code>\" # % \\ ^ | ~ ` $ &amp; , ; : /</code> </p> </li> <li> <p>control characters (<code>U+0000-001F</code>, <code>U+007F-009F</code>)</p> </li> </ul> <p>To enable logging with CloudWatch Logs, the name should only contain 0-9, A-Z, a-z, - and _.</p>"}, "status": {"shape": "ExecutionStatus", "documentation": "<p>The current status of the execution.</p>"}, "startDate": {"shape": "Timestamp", "documentation": "<p>The date the execution started.</p>"}, "stopDate": {"shape": "Timestamp", "documentation": "<p>If the execution already ended, the date the execution stopped.</p>"}, "mapRunArn": {"shape": "LongArn", "documentation": "<p>The Amazon Resource Name (ARN) of a Map Run. This field is returned only if <code>mapRunArn</code> was specified in the <code>ListExecutions</code> API action. If <code>stateMachineArn</code> was specified in <code>ListExecutions</code>, the <code>mapRunArn</code> isn't returned.</p>"}, "itemCount": {"shape": "UnsignedInteger", "documentation": "<p>The total number of items processed in a child workflow execution. This field is returned only if <code>mapRunArn</code> was specified in the <code>ListExecutions</code> API action. If <code>stateMachineArn</code> was specified in <code>ListExecutions</code>, the <code>itemCount</code> field isn't returned.</p>", "box": true}, "stateMachineVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the state machine version associated with the execution.</p> <p>If the state machine execution was started with an unqualified ARN, it returns null.</p> <p>If the execution was started using a <code>stateMachineAliasArn</code>, both the <code>stateMachineAliasArn</code> and <code>stateMachineVersionArn</code> parameters contain the respective values.</p>"}, "stateMachineAliasArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the state machine alias used to start an execution.</p> <p>If the state machine execution was started with an unqualified ARN or a version ARN, it returns null.</p>"}}, "documentation": "<p>Contains details about an execution.</p>"}, "ExecutionStartedEventDetails": {"type": "structure", "members": {"input": {"shape": "SensitiveData", "documentation": "<p>The JSON data input to the execution. Length constraints apply to the payload size, and are expressed as bytes in UTF-8 encoding.</p>"}, "inputDetails": {"shape": "HistoryEventExecutionDataDetails", "documentation": "<p>Contains details about the input for an execution history event.</p>"}, "roleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role used for executing Lambda tasks.</p>"}, "stateMachineAliasArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that identifies a state machine alias used for starting the state machine execution.</p>"}, "stateMachineVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that identifies a state machine version used for starting the state machine execution.</p>"}}, "documentation": "<p>Contains details about the start of the execution.</p>"}, "ExecutionStatus": {"type": "string", "enum": ["RUNNING", "SUCCEEDED", "FAILED", "TIMED_OUT", "ABORTED"]}, "ExecutionSucceededEventDetails": {"type": "structure", "members": {"output": {"shape": "SensitiveData", "documentation": "<p>The JSON data output by the execution. Length constraints apply to the payload size, and are expressed as bytes in UTF-8 encoding.</p>"}, "outputDetails": {"shape": "HistoryEventExecutionDataDetails", "documentation": "<p>Contains details about the output of an execution history event.</p>"}}, "documentation": "<p>Contains details about the successful termination of the execution.</p>"}, "ExecutionTimedOutEventDetails": {"type": "structure", "members": {"error": {"shape": "SensitiveError", "documentation": "<p>The error code of the failure.</p>"}, "cause": {"shape": "SensitiveCause", "documentation": "<p>A more detailed explanation of the cause of the timeout.</p>"}}, "documentation": "<p>Contains details about the execution timeout that occurred during the execution.</p>"}, "GetActivityTaskInput": {"type": "structure", "required": ["activityArn"], "members": {"activityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the activity to retrieve tasks from (assigned when you create the task using <a>CreateActivity</a>.)</p>"}, "workerName": {"shape": "Name", "documentation": "<p>You can provide an arbitrary name in order to identify the worker that the task is assigned to. This name is used when it is logged in the execution history.</p>"}}}, "GetActivityTaskOutput": {"type": "structure", "members": {"taskToken": {"shape": "TaskToken", "documentation": "<p>A token that identifies the scheduled task. This token must be copied and included in subsequent calls to <a>SendTaskHeartbeat</a>, <a>SendTaskSuccess</a> or <a>SendTaskFailure</a> in order to report the progress or completion of the task.</p>"}, "input": {"shape": "SensitiveDataJobInput", "documentation": "<p>The string that contains the JSON input data for the task. Length constraints apply to the payload size, and are expressed as bytes in UTF-8 encoding.</p>"}}}, "GetExecutionHistoryInput": {"type": "structure", "required": ["executionArn"], "members": {"executionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the execution.</p>"}, "maxResults": {"shape": "PageSize", "documentation": "<p>The maximum number of results that are returned per call. You can use <code>nextToken</code> to obtain further pages of results. The default is 100 and the maximum allowed page size is 1000. A value of 0 uses the default.</p> <p>This is only an upper limit. The actual number of results returned per call might be fewer than the specified maximum.</p>"}, "reverseOrder": {"shape": "ReverseOrder", "documentation": "<p>Lists events in descending order of their <code>timeStamp</code>.</p>"}, "nextToken": {"shape": "PageToken", "documentation": "<p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return an <i>HTTP 400 InvalidToken</i> error.</p>"}, "includeExecutionData": {"shape": "IncludeExecutionDataGetExecutionHistory", "documentation": "<p>You can select whether execution data (input or output of a history event) is returned. The default is <code>true</code>.</p>"}}}, "GetExecutionHistoryOutput": {"type": "structure", "required": ["events"], "members": {"events": {"shape": "HistoryEventList", "documentation": "<p>The list of events that occurred in the execution.</p>"}, "nextToken": {"shape": "PageToken", "documentation": "<p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return an <i>HTTP 400 InvalidToken</i> error.</p>"}}}, "HistoryEvent": {"type": "structure", "required": ["timestamp", "type", "id"], "members": {"timestamp": {"shape": "Timestamp", "documentation": "<p>The date and time the event occurred.</p>"}, "type": {"shape": "HistoryEventType", "documentation": "<p>The type of the event.</p>"}, "id": {"shape": "EventId", "documentation": "<p>The id of the event. Events are numbered sequentially, starting at one.</p>"}, "previousEventId": {"shape": "EventId", "documentation": "<p>The id of the previous event.</p>"}, "activityFailedEventDetails": {"shape": "ActivityFailedEventDetails"}, "activityScheduleFailedEventDetails": {"shape": "ActivityScheduleFailedEventDetails", "documentation": "<p>Contains details about an activity schedule event that failed during an execution.</p>"}, "activityScheduledEventDetails": {"shape": "ActivityScheduledEventDetails"}, "activityStartedEventDetails": {"shape": "ActivityStartedEventDetails"}, "activitySucceededEventDetails": {"shape": "ActivitySucceededEventDetails"}, "activityTimedOutEventDetails": {"shape": "ActivityTimedOutEventDetails"}, "taskFailedEventDetails": {"shape": "TaskFailedEventDetails", "documentation": "<p>Contains details about the failure of a task.</p>"}, "taskScheduledEventDetails": {"shape": "TaskScheduledEventDetails", "documentation": "<p>Contains details about a task that was scheduled.</p>"}, "taskStartFailedEventDetails": {"shape": "TaskStartFailedEventDetails", "documentation": "<p>Contains details about a task that failed to start.</p>"}, "taskStartedEventDetails": {"shape": "TaskStartedEventDetails", "documentation": "<p>Contains details about a task that was started.</p>"}, "taskSubmitFailedEventDetails": {"shape": "TaskSubmitFailedEventDetails", "documentation": "<p>Contains details about a task that where the submit failed.</p>"}, "taskSubmittedEventDetails": {"shape": "TaskSubmittedEventDetails", "documentation": "<p>Contains details about a submitted task.</p>"}, "taskSucceededEventDetails": {"shape": "TaskSucceededEventDetails", "documentation": "<p>Contains details about a task that succeeded.</p>"}, "taskTimedOutEventDetails": {"shape": "TaskTimedOutEventDetails", "documentation": "<p>Contains details about a task that timed out.</p>"}, "executionFailedEventDetails": {"shape": "ExecutionFailedEventDetails"}, "executionStartedEventDetails": {"shape": "ExecutionStartedEventDetails"}, "executionSucceededEventDetails": {"shape": "ExecutionSucceededEventDetails"}, "executionAbortedEventDetails": {"shape": "ExecutionAbortedEventDetails"}, "executionTimedOutEventDetails": {"shape": "ExecutionTimedOutEventDetails"}, "mapStateStartedEventDetails": {"shape": "MapStateStartedEventDetails", "documentation": "<p>Contains details about Map state that was started.</p>"}, "mapIterationStartedEventDetails": {"shape": "MapIterationEventDetails", "documentation": "<p>Contains details about an iteration of a Map state that was started.</p>"}, "mapIterationSucceededEventDetails": {"shape": "MapIterationEventDetails", "documentation": "<p>Contains details about an iteration of a Map state that succeeded.</p>"}, "mapIterationFailedEventDetails": {"shape": "MapIterationEventDetails", "documentation": "<p>Contains details about an iteration of a Map state that failed.</p>"}, "mapIterationAbortedEventDetails": {"shape": "MapIterationEventDetails", "documentation": "<p>Contains details about an iteration of a Map state that was aborted.</p>"}, "lambdaFunctionFailedEventDetails": {"shape": "LambdaFunctionFailedEventDetails"}, "lambdaFunctionScheduleFailedEventDetails": {"shape": "LambdaFunctionScheduleFailedEventDetails"}, "lambdaFunctionScheduledEventDetails": {"shape": "LambdaFunctionScheduledEventDetails"}, "lambdaFunctionStartFailedEventDetails": {"shape": "LambdaFunctionStartFailedEventDetails", "documentation": "<p>Contains details about a lambda function that failed to start during an execution.</p>"}, "lambdaFunctionSucceededEventDetails": {"shape": "LambdaFunctionSucceededEventDetails", "documentation": "<p>Contains details about a Lambda function that terminated successfully during an execution.</p>"}, "lambdaFunctionTimedOutEventDetails": {"shape": "LambdaFunctionTimedOutEventDetails"}, "stateEnteredEventDetails": {"shape": "StateEnteredEventDetails"}, "stateExitedEventDetails": {"shape": "StateExitedEventDetails"}, "mapRunStartedEventDetails": {"shape": "MapRunStartedEventDetails", "documentation": "<p>Contains details, such as <code>mapRunArn</code>, and the start date and time of a Map Run. <code>mapRunArn</code> is the Amazon Resource Name (ARN) of the Map Run that was started.</p>"}, "mapRunFailedEventDetails": {"shape": "MapRunFailedEventDetails", "documentation": "<p>Contains error and cause details about a Map Run that failed.</p>"}}, "documentation": "<p>Contains details about the events of an execution.</p>"}, "HistoryEventExecutionDataDetails": {"type": "structure", "members": {"truncated": {"shape": "truncated", "documentation": "<p>Indicates whether input or output was truncated in the response. Always <code>false</code> for API calls.</p>"}}, "documentation": "<p>Provides details about input or output in an execution history event.</p>"}, "HistoryEventList": {"type": "list", "member": {"shape": "HistoryEvent"}, "documentation": "<p>Contains details about the events that occurred during an execution.</p>"}, "HistoryEventType": {"type": "string", "enum": ["ActivityFailed", "ActivityScheduled", "ActivityScheduleFailed", "ActivityStarted", "ActivitySucceeded", "ActivityTimedOut", "ChoiceStateEntered", "ChoiceStateExited", "ExecutionAborted", "ExecutionFailed", "ExecutionStarted", "ExecutionSucceeded", "ExecutionTimedOut", "FailStateEntered", "LambdaFunctionFailed", "LambdaFunctionScheduled", "LambdaFunctionScheduleFailed", "LambdaFunctionStarted", "LambdaFunctionStartFailed", "LambdaFunctionSucceeded", "LambdaFunctionTimedOut", "MapIterationAborted", "MapIterationFailed", "MapIterationStarted", "MapIterationSucceeded", "MapStateAborted", "MapStateEntered", "MapStateExited", "MapStateFailed", "MapStateStarted", "MapStateSucceeded", "ParallelStateAborted", "ParallelStateEntered", "ParallelStateExited", "ParallelStateFailed", "ParallelStateStarted", "ParallelStateSucceeded", "PassStateEntered", "PassStateExited", "SucceedStateEntered", "SucceedStateExited", "TaskFailed", "TaskScheduled", "TaskStarted", "TaskStartFailed", "TaskStateAborted", "TaskStateEntered", "TaskStateExited", "TaskSubmitFailed", "TaskSubmitted", "TaskSucceeded", "TaskTimedOut", "WaitStateAborted", "WaitStateEntered", "WaitStateExited", "MapRunAborted", "MapRunFailed", "MapRunStarted", "MapRunSucceeded"]}, "Identity": {"type": "string", "max": 256}, "IncludeExecutionData": {"type": "boolean"}, "IncludeExecutionDataGetExecutionHistory": {"type": "boolean", "box": true}, "InvalidArn": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The provided Amazon Resource Name (ARN) is not valid.</p>", "exception": true}, "InvalidDefinition": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The provided Amazon States Language definition is not valid.</p>", "exception": true}, "InvalidExecutionInput": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The provided JSON input data is not valid.</p>", "exception": true}, "InvalidLoggingConfiguration": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p/>", "exception": true}, "InvalidName": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The provided name is not valid.</p>", "exception": true}, "InvalidOutput": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The provided JSON output data is not valid.</p>", "exception": true}, "InvalidToken": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The provided token is not valid.</p>", "exception": true}, "InvalidTracingConfiguration": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>Your <code>tracingConfiguration</code> key does not match, or <code>enabled</code> has not been set to <code>true</code> or <code>false</code>.</p>", "exception": true}, "LambdaFunctionFailedEventDetails": {"type": "structure", "members": {"error": {"shape": "SensitiveError", "documentation": "<p>The error code of the failure.</p>"}, "cause": {"shape": "SensitiveCause", "documentation": "<p>A more detailed explanation of the cause of the failure.</p>"}}, "documentation": "<p>Contains details about a Lambda function that failed during an execution.</p>"}, "LambdaFunctionScheduleFailedEventDetails": {"type": "structure", "members": {"error": {"shape": "SensitiveError", "documentation": "<p>The error code of the failure.</p>"}, "cause": {"shape": "SensitiveCause", "documentation": "<p>A more detailed explanation of the cause of the failure.</p>"}}, "documentation": "<p>Contains details about a failed Lambda function schedule event that occurred during an execution.</p>"}, "LambdaFunctionScheduledEventDetails": {"type": "structure", "required": ["resource"], "members": {"resource": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the scheduled Lambda function.</p>"}, "input": {"shape": "SensitiveData", "documentation": "<p>The JSON data input to the Lambda function. Length constraints apply to the payload size, and are expressed as bytes in UTF-8 encoding.</p>"}, "inputDetails": {"shape": "HistoryEventExecutionDataDetails", "documentation": "<p>Contains details about input for an execution history event.</p>"}, "timeoutInSeconds": {"shape": "TimeoutInSeconds", "documentation": "<p>The maximum allowed duration of the Lambda function.</p>", "box": true}, "taskCredentials": {"shape": "TaskCredentials", "documentation": "<p>The credentials that Step Functions uses for the task.</p>"}}, "documentation": "<p>Contains details about a Lambda function scheduled during an execution.</p>"}, "LambdaFunctionStartFailedEventDetails": {"type": "structure", "members": {"error": {"shape": "SensitiveError", "documentation": "<p>The error code of the failure.</p>"}, "cause": {"shape": "SensitiveCause", "documentation": "<p>A more detailed explanation of the cause of the failure.</p>"}}, "documentation": "<p>Contains details about a lambda function that failed to start during an execution.</p>"}, "LambdaFunctionSucceededEventDetails": {"type": "structure", "members": {"output": {"shape": "SensitiveData", "documentation": "<p>The JSON data output by the Lambda function. Length constraints apply to the payload size, and are expressed as bytes in UTF-8 encoding.</p>"}, "outputDetails": {"shape": "HistoryEventExecutionDataDetails", "documentation": "<p>Contains details about the output of an execution history event.</p>"}}, "documentation": "<p>Contains details about a Lambda function that successfully terminated during an execution.</p>"}, "LambdaFunctionTimedOutEventDetails": {"type": "structure", "members": {"error": {"shape": "SensitiveError", "documentation": "<p>The error code of the failure.</p>"}, "cause": {"shape": "SensitiveCause", "documentation": "<p>A more detailed explanation of the cause of the timeout.</p>"}}, "documentation": "<p>Contains details about a Lambda function timeout that occurred during an execution.</p>"}, "ListActivitiesInput": {"type": "structure", "members": {"maxResults": {"shape": "PageSize", "documentation": "<p>The maximum number of results that are returned per call. You can use <code>nextToken</code> to obtain further pages of results. The default is 100 and the maximum allowed page size is 1000. A value of 0 uses the default.</p> <p>This is only an upper limit. The actual number of results returned per call might be fewer than the specified maximum.</p>"}, "nextToken": {"shape": "PageToken", "documentation": "<p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return an <i>HTTP 400 InvalidToken</i> error.</p>"}}}, "ListActivitiesOutput": {"type": "structure", "required": ["activities"], "members": {"activities": {"shape": "ActivityList", "documentation": "<p>The list of activities.</p>"}, "nextToken": {"shape": "PageToken", "documentation": "<p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return an <i>HTTP 400 InvalidToken</i> error.</p>"}}}, "ListExecutionsInput": {"type": "structure", "members": {"stateMachineArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the state machine whose executions is listed.</p> <p>You can specify either a <code>mapRunArn</code> or a <code>stateMachineArn</code>, but not both.</p> <p>You can also return a list of executions associated with a specific <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-state-machine-alias.html\">alias</a> or <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-state-machine-version.html\">version</a>, by specifying an alias <PERSON><PERSON> or a version ARN in the <code>stateMachineArn</code> parameter.</p>"}, "statusFilter": {"shape": "ExecutionStatus", "documentation": "<p>If specified, only list the executions whose current execution status matches the given filter.</p>"}, "maxResults": {"shape": "PageSize", "documentation": "<p>The maximum number of results that are returned per call. You can use <code>nextToken</code> to obtain further pages of results. The default is 100 and the maximum allowed page size is 1000. A value of 0 uses the default.</p> <p>This is only an upper limit. The actual number of results returned per call might be fewer than the specified maximum.</p>"}, "nextToken": {"shape": "ListExecutionsPageToken", "documentation": "<p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return an <i>HTTP 400 InvalidToken</i> error.</p>"}, "mapRunArn": {"shape": "LongArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Map Run that started the child workflow executions. If the <code>mapRunArn</code> field is specified, a list of all of the child workflow executions started by a Map Run is returned. For more information, see <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-examine-map-run.html\">Examining Map Run</a> in the <i>Step Functions Developer Guide</i>.</p> <p>You can specify either a <code>mapRunArn</code> or a <code>stateMachineArn</code>, but not both.</p>"}}}, "ListExecutionsOutput": {"type": "structure", "required": ["executions"], "members": {"executions": {"shape": "ExecutionList", "documentation": "<p>The list of matching executions.</p>"}, "nextToken": {"shape": "ListExecutionsPageToken", "documentation": "<p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return an <i>HTTP 400 InvalidToken</i> error.</p>"}}}, "ListExecutionsPageToken": {"type": "string", "max": 3096, "min": 1}, "ListMapRunsInput": {"type": "structure", "required": ["executionArn"], "members": {"executionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the execution for which the Map Runs must be listed.</p>"}, "maxResults": {"shape": "PageSize", "documentation": "<p>The maximum number of results that are returned per call. You can use <code>nextToken</code> to obtain further pages of results. The default is 100 and the maximum allowed page size is 1000. A value of 0 uses the default.</p> <p>This is only an upper limit. The actual number of results returned per call might be fewer than the specified maximum.</p>"}, "nextToken": {"shape": "PageToken", "documentation": "<p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return an <i>HTTP 400 InvalidToken</i> error.</p>"}}}, "ListMapRunsOutput": {"type": "structure", "required": ["mapRuns"], "members": {"mapRuns": {"shape": "MapRunList", "documentation": "<p>An array that lists information related to a Map Run, such as the Amazon Resource Name (ARN) of the Map Run and the ARN of the state machine that started the Map Run.</p>"}, "nextToken": {"shape": "PageToken", "documentation": "<p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return an <i>HTTP 400 InvalidToken</i> error.</p>"}}}, "ListStateMachineAliasesInput": {"type": "structure", "required": ["stateMachineArn"], "members": {"stateMachineArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the state machine for which you want to list aliases.</p> <p>If you specify a state machine version ARN, this API returns a list of aliases for that version.</p>"}, "nextToken": {"shape": "PageToken", "documentation": "<p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return an <i>HTTP 400 InvalidToken</i> error.</p>"}, "maxResults": {"shape": "PageSize", "documentation": "<p>The maximum number of results that are returned per call. You can use <code>nextToken</code> to obtain further pages of results. The default is 100 and the maximum allowed page size is 1000. A value of 0 uses the default.</p> <p>This is only an upper limit. The actual number of results returned per call might be fewer than the specified maximum.</p>"}}}, "ListStateMachineAliasesOutput": {"type": "structure", "required": ["stateMachineAliases"], "members": {"stateMachineAliases": {"shape": "StateMachineAliasList", "documentation": "<p>Aliases for the state machine.</p>"}, "nextToken": {"shape": "PageToken", "documentation": "<p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return an <i>HTTP 400 InvalidToken</i> error.</p>"}}}, "ListStateMachineVersionsInput": {"type": "structure", "required": ["stateMachineArn"], "members": {"stateMachineArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the state machine.</p>"}, "nextToken": {"shape": "PageToken", "documentation": "<p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return an <i>HTTP 400 InvalidToken</i> error.</p>"}, "maxResults": {"shape": "PageSize", "documentation": "<p>The maximum number of results that are returned per call. You can use <code>nextToken</code> to obtain further pages of results. The default is 100 and the maximum allowed page size is 1000. A value of 0 uses the default.</p> <p>This is only an upper limit. The actual number of results returned per call might be fewer than the specified maximum.</p>"}}}, "ListStateMachineVersionsOutput": {"type": "structure", "required": ["stateMachineVersions"], "members": {"stateMachineVersions": {"shape": "StateMachineVersionList", "documentation": "<p>Versions for the state machine.</p>"}, "nextToken": {"shape": "PageToken", "documentation": "<p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return an <i>HTTP 400 InvalidToken</i> error.</p>"}}}, "ListStateMachinesInput": {"type": "structure", "members": {"maxResults": {"shape": "PageSize", "documentation": "<p>The maximum number of results that are returned per call. You can use <code>nextToken</code> to obtain further pages of results. The default is 100 and the maximum allowed page size is 1000. A value of 0 uses the default.</p> <p>This is only an upper limit. The actual number of results returned per call might be fewer than the specified maximum.</p>"}, "nextToken": {"shape": "PageToken", "documentation": "<p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return an <i>HTTP 400 InvalidToken</i> error.</p>"}}}, "ListStateMachinesOutput": {"type": "structure", "required": ["stateMachines"], "members": {"stateMachines": {"shape": "StateMachineList"}, "nextToken": {"shape": "PageToken", "documentation": "<p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return an <i>HTTP 400 InvalidToken</i> error.</p>"}}}, "ListTagsForResourceInput": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) for the Step Functions state machine or activity.</p>"}}}, "ListTagsForResourceOutput": {"type": "structure", "members": {"tags": {"shape": "TagList", "documentation": "<p>An array of tags associated with the resource.</p>"}}}, "LogDestination": {"type": "structure", "members": {"cloudWatchLogsLogGroup": {"shape": "CloudWatchLogsLogGroup", "documentation": "<p>An object describing a CloudWatch log group. For more information, see <a href=\"https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-logs-loggroup.html\">AWS::Logs::LogGroup</a> in the CloudFormation User Guide.</p>"}}, "documentation": "<p/>"}, "LogDestinationList": {"type": "list", "member": {"shape": "LogDestination"}}, "LogLevel": {"type": "string", "enum": ["ALL", "ERROR", "FATAL", "OFF"]}, "LoggingConfiguration": {"type": "structure", "members": {"level": {"shape": "LogLevel", "documentation": "<p>Defines which category of execution history events are logged.</p>"}, "includeExecutionData": {"shape": "IncludeExecutionData", "documentation": "<p>Determines whether execution data is included in your log. When set to <code>false</code>, data is excluded.</p>"}, "destinations": {"shape": "LogDestinationList", "documentation": "<p>An array of objects that describes where your execution history events will be logged. Limited to size 1. Required, if your log level is not set to <code>OFF</code>.</p>"}}, "documentation": "<p>The <code>LoggingConfiguration</code> data type is used to set CloudWatch Logs options.</p>"}, "LongArn": {"type": "string", "max": 2000, "min": 1}, "MapIterationEventDetails": {"type": "structure", "members": {"name": {"shape": "Name", "documentation": "<p>The name of the iteration’s parent Map state.</p>"}, "index": {"shape": "UnsignedInteger", "documentation": "<p>The index of the array belonging to the Map state iteration.</p>"}}, "documentation": "<p>Contains details about an iteration of a Map state.</p>"}, "MapRunExecutionCounts": {"type": "structure", "required": ["pending", "running", "succeeded", "failed", "timedOut", "aborted", "total", "results<PERSON><PERSON>ten"], "members": {"pending": {"shape": "UnsignedLong", "documentation": "<p>The total number of child workflow executions that were started by a Map Run, but haven't started executing yet. </p>"}, "running": {"shape": "UnsignedLong", "documentation": "<p>The total number of child workflow executions that were started by a Map Run and are currently in-progress.</p>"}, "succeeded": {"shape": "UnsignedLong", "documentation": "<p>The total number of child workflow executions that were started by a Map Run and have completed successfully.</p>"}, "failed": {"shape": "UnsignedLong", "documentation": "<p>The total number of child workflow executions that were started by a Map Run, but have failed.</p>"}, "timedOut": {"shape": "UnsignedLong", "documentation": "<p>The total number of child workflow executions that were started by a Map Run and have timed out.</p>"}, "aborted": {"shape": "UnsignedLong", "documentation": "<p>The total number of child workflow executions that were started by a Map Run and were running, but were either stopped by the user or by Step Functions because the Map Run failed. </p>"}, "total": {"shape": "UnsignedLong", "documentation": "<p>The total number of child workflow executions that were started by a Map Run.</p>"}, "resultsWritten": {"shape": "UnsignedLong", "documentation": "<p>Returns the count of child workflow executions whose results were written by <code>ResultWriter</code>. For more information, see <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/input-output-resultwriter.html\">ResultWriter</a> in the <i>Step Functions Developer Guide</i>.</p>"}}, "documentation": "<p>Contains details about all of the child workflow executions started by a Map Run.</p>"}, "MapRunFailedEventDetails": {"type": "structure", "members": {"error": {"shape": "SensitiveError", "documentation": "<p>The error code of the Map Run failure.</p>"}, "cause": {"shape": "SensitiveCause", "documentation": "<p>A more detailed explanation of the cause of the failure.</p>"}}, "documentation": "<p>Contains details about a Map Run failure event that occurred during a state machine execution.</p>"}, "MapRunItemCounts": {"type": "structure", "required": ["pending", "running", "succeeded", "failed", "timedOut", "aborted", "total", "results<PERSON><PERSON>ten"], "members": {"pending": {"shape": "UnsignedLong", "documentation": "<p>The total number of items to process in child workflow executions that haven't started running yet.</p>"}, "running": {"shape": "UnsignedLong", "documentation": "<p>The total number of items being processed in child workflow executions that are currently in-progress.</p>"}, "succeeded": {"shape": "UnsignedLong", "documentation": "<p>The total number of items processed in child workflow executions that have completed successfully.</p>"}, "failed": {"shape": "UnsignedLong", "documentation": "<p>The total number of items processed in child workflow executions that have failed.</p>"}, "timedOut": {"shape": "UnsignedLong", "documentation": "<p>The total number of items processed in child workflow executions that have timed out.</p>"}, "aborted": {"shape": "UnsignedLong", "documentation": "<p>The total number of items processed in child workflow executions that were either stopped by the user or by Step Functions, because the Map Run failed.</p>"}, "total": {"shape": "UnsignedLong", "documentation": "<p>The total number of items processed in all the child workflow executions started by a Map Run.</p>"}, "resultsWritten": {"shape": "UnsignedLong", "documentation": "<p>Returns the count of items whose results were written by <code>ResultWriter</code>. For more information, see <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/input-output-resultwriter.html\">ResultWriter</a> in the <i>Step Functions Developer Guide</i>.</p>"}}, "documentation": "<p>Contains details about items that were processed in all of the child workflow executions that were started by a Map Run.</p>"}, "MapRunLabel": {"type": "string"}, "MapRunList": {"type": "list", "member": {"shape": "MapRunListItem"}}, "MapRunListItem": {"type": "structure", "required": ["executionArn", "mapRunArn", "stateMachineArn", "startDate"], "members": {"executionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The <code>executionArn</code> of the execution from which the Map Run was started.</p>"}, "mapRunArn": {"shape": "LongArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Map Run.</p>"}, "stateMachineArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the executed state machine.</p>"}, "startDate": {"shape": "Timestamp", "documentation": "<p>The date on which the Map Run started.</p>"}, "stopDate": {"shape": "Timestamp", "documentation": "<p>The date on which the Map Run stopped.</p>"}}, "documentation": "<p>Contains details about a specific Map Run.</p>"}, "MapRunStartedEventDetails": {"type": "structure", "members": {"mapRunArn": {"shape": "LongArn", "documentation": "<p>The Amazon Resource Name (ARN) of a Map Run that was started.</p>"}}, "documentation": "<p>Contains details about a Map Run that was started during a state machine execution.</p>"}, "MapRunStatus": {"type": "string", "enum": ["RUNNING", "SUCCEEDED", "FAILED", "ABORTED"]}, "MapStateStartedEventDetails": {"type": "structure", "members": {"length": {"shape": "UnsignedInteger", "documentation": "<p>The size of the array for Map state iterations.</p>"}}, "documentation": "<p>Details about a Map state that was started.</p>"}, "MaxConcurrency": {"type": "integer", "min": 0}, "MissingRequiredParameter": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>Request is missing a required parameter. This error occurs if both <code>definition</code> and <code>roleArn</code> are not specified.</p>", "exception": true}, "Name": {"type": "string", "max": 80, "min": 1}, "PageSize": {"type": "integer", "max": 1000, "min": 0}, "PageToken": {"type": "string", "max": 1024, "min": 1}, "Publish": {"type": "boolean"}, "PublishStateMachineVersionInput": {"type": "structure", "required": ["stateMachineArn"], "members": {"stateMachineArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the state machine.</p>"}, "revisionId": {"shape": "RevisionId", "documentation": "<p>Only publish the state machine version if the current state machine's revision ID matches the specified ID.</p> <p>Use this option to avoid publishing a version if the state machine changed since you last updated it. If the specified revision ID doesn't match the state machine's current revision ID, the API returns <code>ConflictException</code>.</p> <note> <p>To specify an initial revision ID for a state machine with no revision ID assigned, specify the string <code>INITIAL</code> for the <code>revisionId</code> parameter. For example, you can specify a <code>revisionID</code> of <code>INITIAL</code> when you create a state machine using the <a>CreateStateMachine</a> API action.</p> </note>"}, "description": {"shape": "VersionDescription", "documentation": "<p>An optional description of the state machine version.</p>"}}}, "PublishStateMachineVersionOutput": {"type": "structure", "required": ["creationDate", "stateMachineVersionArn"], "members": {"creationDate": {"shape": "Timestamp", "documentation": "<p>The date the version was created.</p>"}, "stateMachineVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) (ARN) that identifies the state machine version.</p>"}}}, "ResourceNotFound": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}, "resourceName": {"shape": "<PERSON><PERSON>"}}, "documentation": "<p>Could not find the referenced resource.</p>", "exception": true}, "ReverseOrder": {"type": "boolean"}, "RevisionId": {"type": "string"}, "RoutingConfigurationList": {"type": "list", "member": {"shape": "RoutingConfigurationListItem"}, "max": 2, "min": 1}, "RoutingConfigurationListItem": {"type": "structure", "required": ["stateMachineVersionArn", "weight"], "members": {"stateMachineVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that identifies one or two state machine versions defined in the routing configuration.</p> <p>If you specify the ARN of a second version, it must belong to the same state machine as the first version.</p>"}, "weight": {"shape": "VersionWeight", "documentation": "<p>The percentage of traffic you want to route to the second state machine version. The sum of the weights in the routing configuration must be equal to 100.</p>"}}, "documentation": "<p>Contains details about the routing configuration of a state machine alias. In a routing configuration, you define an array of objects that specify up to two state machine versions. You also specify the percentage of traffic to be routed to each version.</p>"}, "SendTaskFailureInput": {"type": "structure", "required": ["taskToken"], "members": {"taskToken": {"shape": "TaskToken", "documentation": "<p>The token that represents this task. Task tokens are generated by Step Functions when tasks are assigned to a worker, or in the <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/input-output-contextobject.html\">context object</a> when a workflow enters a task state. See <a>GetActivityTaskOutput$taskToken</a>.</p>"}, "error": {"shape": "SensitiveError", "documentation": "<p>The error code of the failure.</p>"}, "cause": {"shape": "SensitiveCause", "documentation": "<p>A more detailed explanation of the cause of the failure.</p>"}}}, "SendTaskFailureOutput": {"type": "structure", "members": {}}, "SendTaskHeartbeatInput": {"type": "structure", "required": ["taskToken"], "members": {"taskToken": {"shape": "TaskToken", "documentation": "<p>The token that represents this task. Task tokens are generated by Step Functions when tasks are assigned to a worker, or in the <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/input-output-contextobject.html\">context object</a> when a workflow enters a task state. See <a>GetActivityTaskOutput$taskToken</a>.</p>"}}}, "SendTaskHeartbeatOutput": {"type": "structure", "members": {}}, "SendTaskSuccessInput": {"type": "structure", "required": ["taskToken", "output"], "members": {"taskToken": {"shape": "TaskToken", "documentation": "<p>The token that represents this task. Task tokens are generated by Step Functions when tasks are assigned to a worker, or in the <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/input-output-contextobject.html\">context object</a> when a workflow enters a task state. See <a>GetActivityTaskOutput$taskToken</a>.</p>"}, "output": {"shape": "SensitiveData", "documentation": "<p>The JSON output of the task. Length constraints apply to the payload size, and are expressed as bytes in UTF-8 encoding.</p>"}}}, "SendTaskSuccessOutput": {"type": "structure", "members": {}}, "SensitiveCause": {"type": "string", "max": 32768, "min": 0, "sensitive": true}, "SensitiveData": {"type": "string", "max": 262144, "sensitive": true}, "SensitiveDataJobInput": {"type": "string", "max": 262144, "sensitive": true}, "SensitiveError": {"type": "string", "max": 256, "min": 0, "sensitive": true}, "ServiceQuotaExceededException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The request would cause a service quota to be exceeded.</p> <p>HTTP Status Code: 402</p>", "exception": true}, "StartExecutionInput": {"type": "structure", "required": ["stateMachineArn"], "members": {"stateMachineArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the state machine to execute.</p> <p>The <code>stateMachineArn</code> parameter accepts one of the following inputs:</p> <ul> <li> <p> <b>An unqualified state machine ARN</b> – Refers to a state machine ARN that isn't qualified with a version or alias ARN. The following is an example of an unqualified state machine ARN.</p> <p> <code>arn:&lt;partition&gt;:states:&lt;region&gt;:&lt;account-id&gt;:stateMachine:&lt;myStateMachine&gt;</code> </p> <p>Step Functions doesn't associate state machine executions that you start with an unqualified ARN with a version. This is true even if that version uses the same revision that the execution used.</p> </li> <li> <p> <b>A state machine version ARN</b> – Refers to a version ARN, which is a combination of state machine ARN and the version number separated by a colon (:). The following is an example of the ARN for version 10. </p> <p> <code>arn:&lt;partition&gt;:states:&lt;region&gt;:&lt;account-id&gt;:stateMachine:&lt;myStateMachine&gt;:10</code> </p> <p>Step Functions doesn't associate executions that you start with a version ARN with any aliases that point to that version.</p> </li> <li> <p> <b>A state machine alias ARN</b> – Refers to an alias ARN, which is a combination of state machine ARN and the alias name separated by a colon (:). The following is an example of the ARN for an alias named <code>PROD</code>.</p> <p> <code>arn:&lt;partition&gt;:states:&lt;region&gt;:&lt;account-id&gt;:stateMachine:&lt;myStateMachine:PROD&gt;</code> </p> <p>Step Functions associates executions that you start with an alias ARN with that alias and the state machine version used for that execution.</p> </li> </ul>"}, "name": {"shape": "Name", "documentation": "<p>Optional name of the execution. This name must be unique for your Amazon Web Services account, Region, and state machine for 90 days. For more information, see <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/limits.html#service-limits-state-machine-executions\"> Limits Related to State Machine Executions</a> in the <i>Step Functions Developer Guide</i>.</p> <p>A name must <i>not</i> contain:</p> <ul> <li> <p>white space</p> </li> <li> <p>brackets <code>&lt; &gt; { } [ ]</code> </p> </li> <li> <p>wildcard characters <code>? *</code> </p> </li> <li> <p>special characters <code>\" # % \\ ^ | ~ ` $ &amp; , ; : /</code> </p> </li> <li> <p>control characters (<code>U+0000-001F</code>, <code>U+007F-009F</code>)</p> </li> </ul> <p>To enable logging with CloudWatch Logs, the name should only contain 0-9, A-Z, a-z, - and _.</p>"}, "input": {"shape": "SensitiveData", "documentation": "<p>The string that contains the JSON input data for the execution, for example:</p> <p> <code>\"input\": \"{\\\"first_name\\\" : \\\"test\\\"}\"</code> </p> <note> <p>If you don't include any JSON input data, you still must include the two braces, for example: <code>\"input\": \"{}\"</code> </p> </note> <p>Length constraints apply to the payload size, and are expressed as bytes in UTF-8 encoding.</p>"}, "traceHeader": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Passes the X-Ray trace header. The trace header can also be passed in the request payload.</p>"}}}, "StartExecutionOutput": {"type": "structure", "required": ["executionArn", "startDate"], "members": {"executionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the execution.</p>"}, "startDate": {"shape": "Timestamp", "documentation": "<p>The date the execution is started.</p>"}}}, "StartSyncExecutionInput": {"type": "structure", "required": ["stateMachineArn"], "members": {"stateMachineArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the state machine to execute.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the execution.</p>"}, "input": {"shape": "SensitiveData", "documentation": "<p>The string that contains the JSON input data for the execution, for example:</p> <p> <code>\"input\": \"{\\\"first_name\\\" : \\\"test\\\"}\"</code> </p> <note> <p>If you don't include any JSON input data, you still must include the two braces, for example: <code>\"input\": \"{}\"</code> </p> </note> <p>Length constraints apply to the payload size, and are expressed as bytes in UTF-8 encoding.</p>"}, "traceHeader": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Passes the X-Ray trace header. The trace header can also be passed in the request payload.</p>"}}}, "StartSyncExecutionOutput": {"type": "structure", "required": ["executionArn", "startDate", "stopDate", "status"], "members": {"executionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the execution.</p>"}, "stateMachineArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the state machine.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the execution.</p>"}, "startDate": {"shape": "Timestamp", "documentation": "<p>The date the execution is started.</p>"}, "stopDate": {"shape": "Timestamp", "documentation": "<p>If the execution has already ended, the date the execution stopped.</p>"}, "status": {"shape": "SyncExecutionStatus", "documentation": "<p>The current status of the execution.</p>"}, "error": {"shape": "SensitiveError", "documentation": "<p>The error code of the failure.</p>"}, "cause": {"shape": "SensitiveCause", "documentation": "<p>A more detailed explanation of the cause of the failure.</p>"}, "input": {"shape": "SensitiveData", "documentation": "<p>The string that contains the JSON input data of the execution. Length constraints apply to the payload size, and are expressed as bytes in UTF-8 encoding.</p>"}, "inputDetails": {"shape": "CloudWatchEventsExecutionDataDetails"}, "output": {"shape": "SensitiveData", "documentation": "<p>The JSON output data of the execution. Length constraints apply to the payload size, and are expressed as bytes in UTF-8 encoding.</p> <note> <p>This field is set only if the execution succeeds. If the execution fails, this field is null.</p> </note>"}, "outputDetails": {"shape": "CloudWatchEventsExecutionDataDetails"}, "traceHeader": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The X-Ray trace header that was passed to the execution.</p>"}, "billingDetails": {"shape": "BillingDetails", "documentation": "<p>An object that describes workflow billing details, including billed duration and memory use.</p>"}}}, "StateEnteredEventDetails": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "Name", "documentation": "<p>The name of the state.</p>"}, "input": {"shape": "SensitiveData", "documentation": "<p>The string that contains the JSON input data for the state. Length constraints apply to the payload size, and are expressed as bytes in UTF-8 encoding.</p>"}, "inputDetails": {"shape": "HistoryEventExecutionDataDetails", "documentation": "<p>Contains details about the input for an execution history event.</p>"}}, "documentation": "<p>Contains details about a state entered during an execution.</p>"}, "StateExitedEventDetails": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "Name", "documentation": "<p>The name of the state.</p> <p>A name must <i>not</i> contain:</p> <ul> <li> <p>white space</p> </li> <li> <p>brackets <code>&lt; &gt; { } [ ]</code> </p> </li> <li> <p>wildcard characters <code>? *</code> </p> </li> <li> <p>special characters <code>\" # % \\ ^ | ~ ` $ &amp; , ; : /</code> </p> </li> <li> <p>control characters (<code>U+0000-001F</code>, <code>U+007F-009F</code>)</p> </li> </ul> <p>To enable logging with CloudWatch Logs, the name should only contain 0-9, A-Z, a-z, - and _.</p>"}, "output": {"shape": "SensitiveData", "documentation": "<p>The JSON output data of the state. Length constraints apply to the payload size, and are expressed as bytes in UTF-8 encoding.</p>"}, "outputDetails": {"shape": "HistoryEventExecutionDataDetails", "documentation": "<p>Contains details about the output of an execution history event.</p>"}}, "documentation": "<p>Contains details about an exit from a state during an execution.</p>"}, "StateMachineAliasList": {"type": "list", "member": {"shape": "StateMachineAliasListItem"}}, "StateMachineAliasListItem": {"type": "structure", "required": ["stateMachineAliasArn", "creationDate"], "members": {"stateMachineAliasArn": {"shape": "LongArn", "documentation": "<p>The Amazon Resource Name (ARN) that identifies a state machine alias. The alias ARN is a combination of state machine ARN and the alias name separated by a colon (:). For example, <code>stateMachineARN:PROD</code>.</p>"}, "creationDate": {"shape": "Timestamp", "documentation": "<p>The creation date of a state machine alias.</p>"}}, "documentation": "<p>Contains details about a specific state machine alias.</p>"}, "StateMachineAlreadyExists": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>A state machine with the same name but a different definition or role ARN already exists.</p>", "exception": true}, "StateMachineDeleting": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The specified state machine is being deleted.</p>", "exception": true}, "StateMachineDoesNotExist": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The specified state machine does not exist.</p>", "exception": true}, "StateMachineLimitExceeded": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The maximum number of state machines has been reached. Existing state machines must be deleted before a new state machine can be created.</p>", "exception": true}, "StateMachineList": {"type": "list", "member": {"shape": "StateMachineListItem"}}, "StateMachineListItem": {"type": "structure", "required": ["stateMachineArn", "name", "type", "creationDate"], "members": {"stateMachineArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the state machine.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the state machine.</p> <p>A name must <i>not</i> contain:</p> <ul> <li> <p>white space</p> </li> <li> <p>brackets <code>&lt; &gt; { } [ ]</code> </p> </li> <li> <p>wildcard characters <code>? *</code> </p> </li> <li> <p>special characters <code>\" # % \\ ^ | ~ ` $ &amp; , ; : /</code> </p> </li> <li> <p>control characters (<code>U+0000-001F</code>, <code>U+007F-009F</code>)</p> </li> </ul> <p>To enable logging with CloudWatch Logs, the name should only contain 0-9, A-Z, a-z, - and _.</p>"}, "type": {"shape": "StateMachineType", "documentation": "<p/>"}, "creationDate": {"shape": "Timestamp", "documentation": "<p>The date the state machine is created.</p>"}}, "documentation": "<p>Contains details about the state machine.</p>"}, "StateMachineStatus": {"type": "string", "enum": ["ACTIVE", "DELETING"]}, "StateMachineType": {"type": "string", "enum": ["STANDARD", "EXPRESS"]}, "StateMachineTypeNotSupported": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p/>", "exception": true}, "StateMachineVersionList": {"type": "list", "member": {"shape": "StateMachineVersionListItem"}}, "StateMachineVersionListItem": {"type": "structure", "required": ["stateMachineVersionArn", "creationDate"], "members": {"stateMachineVersionArn": {"shape": "LongArn", "documentation": "<p>The Amazon Resource Name (ARN) that identifies a state machine version. The version ARN is a combination of state machine ARN and the version number separated by a colon (:). For example, <code>stateMachineARN:1</code>.</p>"}, "creationDate": {"shape": "Timestamp", "documentation": "<p>The creation date of a state machine version.</p>"}}, "documentation": "<p>Contains details about a specific state machine version.</p>"}, "StopExecutionInput": {"type": "structure", "required": ["executionArn"], "members": {"executionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the execution to stop.</p>"}, "error": {"shape": "SensitiveError", "documentation": "<p>The error code of the failure.</p>"}, "cause": {"shape": "SensitiveCause", "documentation": "<p>A more detailed explanation of the cause of the failure.</p>"}}}, "StopExecutionOutput": {"type": "structure", "required": ["stopDate"], "members": {"stopDate": {"shape": "Timestamp", "documentation": "<p>The date the execution is stopped.</p>"}}}, "SyncExecutionStatus": {"type": "string", "enum": ["SUCCEEDED", "FAILED", "TIMED_OUT"]}, "Tag": {"type": "structure", "members": {"key": {"shape": "TagKey", "documentation": "<p>The key of a tag.</p>"}, "value": {"shape": "TagValue", "documentation": "<p>The value of a tag.</p>"}}, "documentation": "<p>Tags are key-value pairs that can be associated with Step Functions state machines and activities.</p> <p>An array of key-value pairs. For more information, see <a href=\"https://docs.aws.amazon.com/awsaccountbilling/latest/aboutv2/cost-alloc-tags.html\">Using Cost Allocation Tags</a> in the <i>Amazon Web Services Billing and Cost Management User Guide</i>, and <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access_iam-tags.html\">Controlling Access Using IAM Tags</a>.</p> <p>Tags may only contain Unicode letters, digits, white space, or these symbols: <code>_ . : / = + - @</code>.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}}, "TagList": {"type": "list", "member": {"shape": "Tag"}}, "TagResourceInput": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) for the Step Functions state machine or activity.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>The list of tags to add to a resource.</p> <p>Tags may only contain Unicode letters, digits, white space, or these symbols: <code>_ . : / = + - @</code>.</p>"}}}, "TagResourceOutput": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "TaskCredentials": {"type": "structure", "members": {"roleArn": {"shape": "LongArn", "documentation": "<p>The ARN of an IAM role that Step Functions assumes for the task. The role can allow cross-account access to resources.</p>"}}, "documentation": "<p>Contains details about the credentials that Step Functions uses for a task.</p>"}, "TaskDoesNotExist": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "exception": true}, "TaskFailedEventDetails": {"type": "structure", "required": ["resourceType", "resource"], "members": {"resourceType": {"shape": "Name", "documentation": "<p>The service name of the resource in a task state.</p>"}, "resource": {"shape": "Name", "documentation": "<p>The action of the resource called by a task state.</p>"}, "error": {"shape": "SensitiveError", "documentation": "<p>The error code of the failure.</p>"}, "cause": {"shape": "SensitiveCause", "documentation": "<p>A more detailed explanation of the cause of the failure.</p>"}}, "documentation": "<p>Contains details about a task failure event.</p>"}, "TaskScheduledEventDetails": {"type": "structure", "required": ["resourceType", "resource", "region", "parameters"], "members": {"resourceType": {"shape": "Name", "documentation": "<p>The service name of the resource in a task state.</p>"}, "resource": {"shape": "Name", "documentation": "<p>The action of the resource called by a task state.</p>"}, "region": {"shape": "Name", "documentation": "<p>The region of the scheduled task</p>"}, "parameters": {"shape": "ConnectorParameters", "documentation": "<p>The JSON data passed to the resource referenced in a task state. Length constraints apply to the payload size, and are expressed as bytes in UTF-8 encoding.</p>"}, "timeoutInSeconds": {"shape": "TimeoutInSeconds", "documentation": "<p>The maximum allowed duration of the task.</p>", "box": true}, "heartbeatInSeconds": {"shape": "TimeoutInSeconds", "documentation": "<p>The maximum allowed duration between two heartbeats for the task.</p>", "box": true}, "taskCredentials": {"shape": "TaskCredentials", "documentation": "<p>The credentials that Step Functions uses for the task.</p>"}}, "documentation": "<p>Contains details about a task scheduled during an execution.</p>"}, "TaskStartFailedEventDetails": {"type": "structure", "required": ["resourceType", "resource"], "members": {"resourceType": {"shape": "Name", "documentation": "<p>The service name of the resource in a task state.</p>"}, "resource": {"shape": "Name", "documentation": "<p>The action of the resource called by a task state.</p>"}, "error": {"shape": "SensitiveError", "documentation": "<p>The error code of the failure.</p>"}, "cause": {"shape": "SensitiveCause", "documentation": "<p>A more detailed explanation of the cause of the failure.</p>"}}, "documentation": "<p>Contains details about a task that failed to start during an execution.</p>"}, "TaskStartedEventDetails": {"type": "structure", "required": ["resourceType", "resource"], "members": {"resourceType": {"shape": "Name", "documentation": "<p>The service name of the resource in a task state.</p>"}, "resource": {"shape": "Name", "documentation": "<p>The action of the resource called by a task state.</p>"}}, "documentation": "<p>Contains details about the start of a task during an execution.</p>"}, "TaskSubmitFailedEventDetails": {"type": "structure", "required": ["resourceType", "resource"], "members": {"resourceType": {"shape": "Name", "documentation": "<p>The service name of the resource in a task state.</p>"}, "resource": {"shape": "Name", "documentation": "<p>The action of the resource called by a task state.</p>"}, "error": {"shape": "SensitiveError", "documentation": "<p>The error code of the failure.</p>"}, "cause": {"shape": "SensitiveCause", "documentation": "<p>A more detailed explanation of the cause of the failure.</p>"}}, "documentation": "<p>Contains details about a task that failed to submit during an execution.</p>"}, "TaskSubmittedEventDetails": {"type": "structure", "required": ["resourceType", "resource"], "members": {"resourceType": {"shape": "Name", "documentation": "<p>The service name of the resource in a task state.</p>"}, "resource": {"shape": "Name", "documentation": "<p>The action of the resource called by a task state.</p>"}, "output": {"shape": "SensitiveData", "documentation": "<p>The response from a resource when a task has started. Length constraints apply to the payload size, and are expressed as bytes in UTF-8 encoding.</p>"}, "outputDetails": {"shape": "HistoryEventExecutionDataDetails", "documentation": "<p>Contains details about the output of an execution history event.</p>"}}, "documentation": "<p>Contains details about a task submitted to a resource .</p>"}, "TaskSucceededEventDetails": {"type": "structure", "required": ["resourceType", "resource"], "members": {"resourceType": {"shape": "Name", "documentation": "<p>The service name of the resource in a task state.</p>"}, "resource": {"shape": "Name", "documentation": "<p>The action of the resource called by a task state.</p>"}, "output": {"shape": "SensitiveData", "documentation": "<p>The full JSON response from a resource when a task has succeeded. This response becomes the output of the related task. Length constraints apply to the payload size, and are expressed as bytes in UTF-8 encoding.</p>"}, "outputDetails": {"shape": "HistoryEventExecutionDataDetails", "documentation": "<p>Contains details about the output of an execution history event.</p>"}}, "documentation": "<p>Contains details about the successful completion of a task state.</p>"}, "TaskTimedOut": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "exception": true}, "TaskTimedOutEventDetails": {"type": "structure", "required": ["resourceType", "resource"], "members": {"resourceType": {"shape": "Name", "documentation": "<p>The service name of the resource in a task state.</p>"}, "resource": {"shape": "Name", "documentation": "<p>The action of the resource called by a task state.</p>"}, "error": {"shape": "SensitiveError", "documentation": "<p>The error code of the failure.</p>"}, "cause": {"shape": "SensitiveCause", "documentation": "<p>A more detailed explanation of the cause of the failure.</p>"}}, "documentation": "<p>Contains details about a resource timeout that occurred during an execution.</p>"}, "TaskToken": {"type": "string", "max": 1024, "min": 1}, "TimeoutInSeconds": {"type": "long"}, "Timestamp": {"type": "timestamp"}, "ToleratedFailureCount": {"type": "long", "min": 0}, "ToleratedFailurePercentage": {"type": "float", "max": 100, "min": 0}, "TooManyTags": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}, "resourceName": {"shape": "<PERSON><PERSON>"}}, "documentation": "<p>You've exceeded the number of tags allowed for a resource. See the <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/limits.html\"> Limits Topic</a> in the Step Functions Developer Guide.</p>", "exception": true}, "TraceHeader": {"type": "string", "max": 256, "min": 0, "pattern": "\\p{ASCII}*"}, "TracingConfiguration": {"type": "structure", "members": {"enabled": {"shape": "Enabled", "documentation": "<p>When set to <code>true</code>, X-Ray tracing is enabled.</p>"}}, "documentation": "<p>Selects whether or not the state machine's X-Ray tracing is enabled. Default is <code>false</code> </p>"}, "UnsignedInteger": {"type": "integer", "min": 0}, "UnsignedLong": {"type": "long", "min": 0}, "UntagResourceInput": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) for the Step Functions state machine or activity.</p>"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>The list of tags to remove from the resource.</p>"}}}, "UntagResourceOutput": {"type": "structure", "members": {}}, "UpdateMapRunInput": {"type": "structure", "required": ["mapRunArn"], "members": {"mapRunArn": {"shape": "LongArn", "documentation": "<p>The Amazon Resource Name (ARN) of a Map Run.</p>"}, "maxConcurrency": {"shape": "MaxConcurrency", "documentation": "<p>The maximum number of child workflow executions that can be specified to run in parallel for the Map Run at the same time.</p>", "box": true}, "toleratedFailurePercentage": {"shape": "ToleratedFailurePercentage", "documentation": "<p>The maximum percentage of failed items before the Map Run fails.</p>", "box": true}, "toleratedFailureCount": {"shape": "ToleratedFailureCount", "documentation": "<p>The maximum number of failed items before the Map Run fails.</p>", "box": true}}}, "UpdateMapRunOutput": {"type": "structure", "members": {}}, "UpdateStateMachineAliasInput": {"type": "structure", "required": ["stateMachineAliasArn"], "members": {"stateMachineAliasArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the state machine alias.</p>"}, "description": {"shape": "AliasDescription", "documentation": "<p>A description of the state machine alias.</p>"}, "routingConfiguration": {"shape": "RoutingConfigurationList", "documentation": "<p>The routing configuration of the state machine alias.</p> <p>An array of <code>RoutingConfig</code> objects that specifies up to two state machine versions that the alias starts executions for.</p>"}}}, "UpdateStateMachineAliasOutput": {"type": "structure", "required": ["updateDate"], "members": {"updateDate": {"shape": "Timestamp", "documentation": "<p>The date and time the state machine alias was updated.</p>"}}}, "UpdateStateMachineInput": {"type": "structure", "required": ["stateMachineArn"], "members": {"stateMachineArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the state machine.</p>"}, "definition": {"shape": "Definition", "documentation": "<p>The Amazon States Language definition of the state machine. See <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/concepts-amazon-states-language.html\">Amazon States Language</a>.</p>"}, "roleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role of the state machine.</p>"}, "loggingConfiguration": {"shape": "LoggingConfiguration", "documentation": "<p>Use the <code>LoggingConfiguration</code> data type to set CloudWatch Logs options.</p>"}, "tracingConfiguration": {"shape": "TracingConfiguration", "documentation": "<p>Selects whether X-Ray tracing is enabled.</p>"}, "publish": {"shape": "Publish", "documentation": "<p>Specifies whether the state machine version is published. The default is <code>false</code>. To publish a version after updating the state machine, set <code>publish</code> to <code>true</code>.</p>"}, "versionDescription": {"shape": "VersionDescription", "documentation": "<p>An optional description of the state machine version to publish.</p> <p>You can only specify the <code>versionDescription</code> parameter if you've set <code>publish</code> to <code>true</code>.</p>"}}}, "UpdateStateMachineOutput": {"type": "structure", "required": ["updateDate"], "members": {"updateDate": {"shape": "Timestamp", "documentation": "<p>The date and time the state machine was updated.</p>"}, "revisionId": {"shape": "RevisionId", "documentation": "<p>The revision identifier for the updated state machine.</p>"}, "stateMachineVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the published state machine version.</p> <p>If the <code>publish</code> parameter isn't set to <code>true</code>, this field returns null.</p>"}}}, "ValidationException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}, "reason": {"shape": "ValidationExceptionReason", "documentation": "<p>The input does not satisfy the constraints specified by an Amazon Web Services service.</p>"}}, "documentation": "<p>The input does not satisfy the constraints specified by an Amazon Web Services service.</p>", "exception": true}, "ValidationExceptionReason": {"type": "string", "enum": ["API_DOES_NOT_SUPPORT_LABELED_ARNS", "MISSING_REQUIRED_PARAMETER", "CANNOT_UPDATE_COMPLETED_MAP_RUN", "INVALID_ROUTING_CONFIGURATION"]}, "VersionDescription": {"type": "string", "max": 256, "sensitive": true}, "VersionWeight": {"type": "integer", "max": 100, "min": 0}, "includedDetails": {"type": "boolean"}, "truncated": {"type": "boolean"}}, "documentation": "<fullname>Step Functions</fullname> <p>Step Functions is a service that lets you coordinate the components of distributed applications and microservices using visual workflows.</p> <p>You can use Step Functions to build applications from individual components, each of which performs a discrete function, or <i>task</i>, allowing you to scale and change applications quickly. Step Functions provides a console that helps visualize the components of your application as a series of steps. Step Functions automatically triggers and tracks each step, and retries steps when there are errors, so your application executes predictably and in the right order every time. Step Functions logs the state of each step, so you can quickly diagnose and debug any issues.</p> <p>Step Functions manages operations and underlying infrastructure to ensure your application is available at any scale. You can run tasks on Amazon Web Services, your own servers, or any system that has access to Amazon Web Services. You can access and use Step Functions using the console, the Amazon Web Services SDKs, or an HTTP API. For more information about Step Functions, see the <i> <a href=\"https://docs.aws.amazon.com/step-functions/latest/dg/welcome.html\">Step Functions Developer Guide</a> </i>.</p>"}