{"version": "2.0", "metadata": {"apiVersion": "2020-06-08", "endpointPrefix": "inspector2", "jsonVersion": "1.1", "protocol": "rest-json", "serviceAbbreviation": "Inspector2", "serviceFullName": "Inspector2", "serviceId": "Inspector2", "signatureVersion": "v4", "signingName": "inspector2", "uid": "inspector2-2020-06-08"}, "operations": {"AssociateMember": {"name": "AssociateMember", "http": {"method": "POST", "requestUri": "/members/associate", "responseCode": 200}, "input": {"shape": "AssociateMemberRequest"}, "output": {"shape": "AssociateMemberResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Associates an Amazon Web Services account with an Amazon Inspector delegated administrator. An HTTP 200 response indicates the association was successfully started, but doesn’t indicate whether it was completed. You can check if the association completed by using <a href=\"https://docs.aws.amazon.com/inspector/v2/APIReference/API_ListMembers.html\">ListMembers</a> for multiple accounts or <a href=\"https://docs.aws.amazon.com/inspector/v2/APIReference/API_GetMember.html\">GetMembers</a> for a single account.</p>"}, "BatchGetAccountStatus": {"name": "BatchGetAccountStatus", "http": {"method": "POST", "requestUri": "/status/batch/get", "responseCode": 200}, "input": {"shape": "BatchGetAccountStatusRequest"}, "output": {"shape": "BatchGetAccountStatusResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves the Amazon Inspector status of multiple Amazon Web Services accounts within your environment.</p>"}, "BatchGetCodeSnippet": {"name": "BatchGetCodeSnippet", "http": {"method": "POST", "requestUri": "/codesnippet/batchget", "responseCode": 200}, "input": {"shape": "BatchGetCodeSnippetRequest"}, "output": {"shape": "BatchGetCodeSnippetResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves code snippets from findings that Amazon Inspector detected code vulnerabilities in.</p>"}, "BatchGetFindingDetails": {"name": "BatchGetFindingDetails", "http": {"method": "POST", "requestUri": "/findings/details/batch/get", "responseCode": 200}, "input": {"shape": "BatchGetFindingDetailsRequest"}, "output": {"shape": "BatchGetFindingDetailsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets vulnerability details for findings.</p>"}, "BatchGetFreeTrialInfo": {"name": "BatchGetFreeTrialInfo", "http": {"method": "POST", "requestUri": "/freetrialinfo/batchget", "responseCode": 200}, "input": {"shape": "BatchGetFreeTrialInfoRequest"}, "output": {"shape": "BatchGetFreeTrialInfoResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets free trial status for multiple Amazon Web Services accounts.</p>"}, "BatchGetMemberEc2DeepInspectionStatus": {"name": "BatchGetMemberEc2DeepInspectionStatus", "http": {"method": "POST", "requestUri": "/ec2deepinspectionstatus/member/batch/get", "responseCode": 200}, "input": {"shape": "BatchGetMemberEc2DeepInspectionStatusRequest"}, "output": {"shape": "BatchGetMemberEc2DeepInspectionStatusResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves Amazon Inspector deep inspection activation status of multiple member accounts within your organization. You must be the delegated administrator of an organization in Amazon Inspector to use this API.</p>"}, "BatchUpdateMemberEc2DeepInspectionStatus": {"name": "BatchUpdateMemberEc2DeepInspectionStatus", "http": {"method": "POST", "requestUri": "/ec2deepinspectionstatus/member/batch/update", "responseCode": 200}, "input": {"shape": "BatchUpdateMemberEc2DeepInspectionStatusRequest"}, "output": {"shape": "BatchUpdateMemberEc2DeepInspectionStatusResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Activates or deactivates Amazon Inspector deep inspection for the provided member accounts in your organization. You must be the delegated administrator of an organization in Amazon Inspector to use this API.</p>"}, "CancelFindingsReport": {"name": "CancelFindingsReport", "http": {"method": "POST", "requestUri": "/reporting/cancel", "responseCode": 200}, "input": {"shape": "CancelFindingsReportRequest"}, "output": {"shape": "CancelFindingsReportResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Cancels the given findings report.</p>"}, "CancelSbomExport": {"name": "CancelSbomExport", "http": {"method": "POST", "requestUri": "/sbomexport/cancel", "responseCode": 200}, "input": {"shape": "CancelSbomExportRequest"}, "output": {"shape": "CancelSbomExportResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Cancels a software bill of materials (SBOM) report.</p>", "idempotent": true}, "CreateFilter": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/filters/create", "responseCode": 200}, "input": {"shape": "CreateFilterRequest"}, "output": {"shape": "CreateFilterResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "BadRequestException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a filter resource using specified filter criteria. When the filter action is set to <code>SUPPRESS</code> this action creates a suppression rule.</p>"}, "CreateFindingsReport": {"name": "CreateFindingsReport", "http": {"method": "POST", "requestUri": "/reporting/create", "responseCode": 200}, "input": {"shape": "CreateFindingsReportRequest"}, "output": {"shape": "CreateFindingsReportResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a finding report. By default only <code>ACTIVE</code> findings are returned in the report. To see <code>SUPRESSED</code> or <code>CLOSED</code> findings you must specify a value for the <code>findingStatus</code> filter criteria. </p>"}, "CreateSbomExport": {"name": "CreateSbomExport", "http": {"method": "POST", "requestUri": "/sbomexport/create", "responseCode": 200}, "input": {"shape": "CreateSbomExportRequest"}, "output": {"shape": "CreateSbomExportResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a software bill of materials (SBOM) report.</p>", "idempotent": true}, "DeleteFilter": {"name": "DeleteFilter", "http": {"method": "POST", "requestUri": "/filters/delete", "responseCode": 200}, "input": {"shape": "DeleteFilterRequest"}, "output": {"shape": "DeleteFilterResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes a filter resource.</p>"}, "DescribeOrganizationConfiguration": {"name": "DescribeOrganizationConfiguration", "http": {"method": "POST", "requestUri": "/organizationconfiguration/describe", "responseCode": 200}, "input": {"shape": "DescribeOrganizationConfigurationRequest"}, "output": {"shape": "DescribeOrganizationConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Describe Amazon Inspector configuration settings for an Amazon Web Services organization.</p>"}, "Disable": {"name": "Disable", "http": {"method": "POST", "requestUri": "/disable", "responseCode": 200}, "input": {"shape": "DisableRequest"}, "output": {"shape": "DisableResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Disables Amazon Inspector scans for one or more Amazon Web Services accounts. Disabling all scan types in an account disables the Amazon Inspector service.</p>"}, "DisableDelegatedAdminAccount": {"name": "DisableDelegatedAdminAccount", "http": {"method": "POST", "requestUri": "/delegatedadminaccounts/disable", "responseCode": 200}, "input": {"shape": "DisableDelegatedAdminAccountRequest"}, "output": {"shape": "DisableDelegatedAdminAccountResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Disables the Amazon Inspector delegated administrator for your organization.</p>"}, "DisassociateMember": {"name": "DisassociateMember", "http": {"method": "POST", "requestUri": "/members/disassociate", "responseCode": 200}, "input": {"shape": "DisassociateMemberRequest"}, "output": {"shape": "DisassociateMemberResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Disassociates a member account from an Amazon Inspector delegated administrator.</p>"}, "Enable": {"name": "Enable", "http": {"method": "POST", "requestUri": "/enable", "responseCode": 200}, "input": {"shape": "EnableRequest"}, "output": {"shape": "EnableResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Enables Amazon Inspector scans for one or more Amazon Web Services accounts.</p>"}, "EnableDelegatedAdminAccount": {"name": "EnableDelegatedAdminAccount", "http": {"method": "POST", "requestUri": "/delegatedadminaccounts/enable", "responseCode": 200}, "input": {"shape": "EnableDelegatedAdminAccountRequest"}, "output": {"shape": "EnableDelegatedAdminAccountResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Enables the Amazon Inspector delegated administrator for your Organizations organization.</p>"}, "GetConfiguration": {"name": "GetConfiguration", "http": {"method": "POST", "requestUri": "/configuration/get", "responseCode": 200}, "input": {"shape": "GetConfigurationRequest"}, "output": {"shape": "GetConfigurationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves setting configurations for Inspector scans.</p>"}, "GetDelegatedAdminAccount": {"name": "GetDelegatedAdminAccount", "http": {"method": "POST", "requestUri": "/delegatedadminaccounts/get", "responseCode": 200}, "input": {"shape": "GetDelegatedAdminAccountRequest"}, "output": {"shape": "GetDelegatedAdminAccountResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves information about the Amazon Inspector delegated administrator for your organization.</p>"}, "GetEc2DeepInspectionConfiguration": {"name": "GetEc2DeepInspectionConfiguration", "http": {"method": "POST", "requestUri": "/ec2deepinspectionconfiguration/get", "responseCode": 200}, "input": {"shape": "GetEc2DeepInspectionConfigurationRequest"}, "output": {"shape": "GetEc2DeepInspectionConfigurationResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves the activation status of Amazon Inspector deep inspection and custom paths associated with your account. </p>"}, "GetEncryptionKey": {"name": "GetEncryptionKey", "http": {"method": "GET", "requestUri": "/encryptionkey/get", "responseCode": 200}, "input": {"shape": "GetEncryptionKeyRequest"}, "output": {"shape": "GetEncryptionKeyResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets an encryption key.</p>"}, "GetFindingsReportStatus": {"name": "GetFindingsReportStatus", "http": {"method": "POST", "requestUri": "/reporting/status/get", "responseCode": 200}, "input": {"shape": "GetFindingsReportStatusRequest"}, "output": {"shape": "GetFindingsReportStatusResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the status of a findings report.</p>"}, "GetMember": {"name": "GetMember", "http": {"method": "POST", "requestUri": "/members/get", "responseCode": 200}, "input": {"shape": "GetMemberRequest"}, "output": {"shape": "GetMemberResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets member information for your organization.</p>"}, "GetSbomExport": {"name": "GetSbomExport", "http": {"method": "POST", "requestUri": "/sbomexport/get", "responseCode": 200}, "input": {"shape": "GetSbomExportRequest"}, "output": {"shape": "GetSbomExportResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets details of a software bill of materials (SBOM) report.</p>", "idempotent": true}, "ListAccountPermissions": {"name": "ListAccountPermissions", "http": {"method": "POST", "requestUri": "/accountpermissions/list", "responseCode": 200}, "input": {"shape": "ListAccountPermissionsRequest"}, "output": {"shape": "ListAccountPermissionsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the permissions an account has to configure Amazon Inspector.</p>"}, "ListCoverage": {"name": "ListCoverage", "http": {"method": "POST", "requestUri": "/coverage/list", "responseCode": 200}, "input": {"shape": "ListCoverageRequest"}, "output": {"shape": "ListCoverageResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists coverage details for you environment.</p>"}, "ListCoverageStatistics": {"name": "ListCoverageStatistics", "http": {"method": "POST", "requestUri": "/coverage/statistics/list", "responseCode": 200}, "input": {"shape": "ListCoverageStatisticsRequest"}, "output": {"shape": "ListCoverageStatisticsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists Amazon Inspector coverage statistics for your environment.</p>"}, "ListDelegatedAdminAccounts": {"name": "ListDelegatedAdminAccounts", "http": {"method": "POST", "requestUri": "/delegatedadminaccounts/list", "responseCode": 200}, "input": {"shape": "ListDelegatedAdminAccountsRequest"}, "output": {"shape": "ListDelegatedAdminAccountsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists information about the Amazon Inspector delegated administrator of your organization.</p>"}, "ListFilters": {"name": "ListFilters", "http": {"method": "POST", "requestUri": "/filters/list", "responseCode": 200}, "input": {"shape": "ListFiltersRequest"}, "output": {"shape": "ListFiltersResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the filters associated with your account.</p>"}, "ListFindingAggregations": {"name": "ListFindingAggregations", "http": {"method": "POST", "requestUri": "/findings/aggregation/list", "responseCode": 200}, "input": {"shape": "ListFindingAggregationsRequest"}, "output": {"shape": "ListFindingAggregationsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists aggregated finding data for your environment based on specific criteria.</p>"}, "ListFindings": {"name": "ListFindings", "http": {"method": "POST", "requestUri": "/findings/list", "responseCode": 200}, "input": {"shape": "ListFindingsRequest"}, "output": {"shape": "ListFindingsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists findings for your environment.</p>"}, "ListMembers": {"name": "ListMembers", "http": {"method": "POST", "requestUri": "/members/list", "responseCode": 200}, "input": {"shape": "ListMembersRequest"}, "output": {"shape": "ListMembersResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>List members associated with the Amazon Inspector delegated administrator for your organization.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists all tags attached to a given resource.</p>"}, "ListUsageTotals": {"name": "ListUsageTotals", "http": {"method": "POST", "requestUri": "/usage/list", "responseCode": 200}, "input": {"shape": "ListUsageTotalsRequest"}, "output": {"shape": "ListUsageTotalsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the Amazon Inspector usage totals over the last 30 days.</p>"}, "ResetEncryptionKey": {"name": "ResetEncryptionKey", "http": {"method": "PUT", "requestUri": "/encryptionkey/reset", "responseCode": 200}, "input": {"shape": "ResetEncryptionKeyRequest"}, "output": {"shape": "ResetEncryptionKeyResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Resets an encryption key. After the key is reset your resources will be encrypted by an Amazon Web Services owned key.</p>", "idempotent": true}, "SearchVulnerabilities": {"name": "SearchVulnerabilities", "http": {"method": "POST", "requestUri": "/vulnerabilities/search", "responseCode": 200}, "input": {"shape": "SearchVulnerabilitiesRequest"}, "output": {"shape": "SearchVulnerabilitiesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists Amazon Inspector coverage details for a specific vulnerability.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Adds tags to a resource.</p>", "idempotent": true}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Removes tags from a resource.</p>"}, "UpdateConfiguration": {"name": "UpdateConfiguration", "http": {"method": "POST", "requestUri": "/configuration/update", "responseCode": 200}, "input": {"shape": "UpdateConfigurationRequest"}, "output": {"shape": "UpdateConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates setting configurations for your Amazon Inspector account. When you use this API as an Amazon Inspector delegated administrator this updates the setting for all accounts you manage. Member accounts in an organization cannot update this setting.</p>"}, "UpdateEc2DeepInspectionConfiguration": {"name": "UpdateEc2DeepInspectionConfiguration", "http": {"method": "POST", "requestUri": "/ec2deepinspectionconfiguration/update", "responseCode": 200}, "input": {"shape": "UpdateEc2DeepInspectionConfigurationRequest"}, "output": {"shape": "UpdateEc2DeepInspectionConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Activates, deactivates Amazon Inspector deep inspection, or updates custom paths for your account. </p>"}, "UpdateEncryptionKey": {"name": "UpdateEncryptionKey", "http": {"method": "PUT", "requestUri": "/encryptionkey/update", "responseCode": 200}, "input": {"shape": "UpdateEncryptionKeyRequest"}, "output": {"shape": "UpdateEncryptionKeyResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates an encryption key. A <code>ResourceNotFoundException</code> means that an AWS owned key is being used for encryption.</p>", "idempotent": true}, "UpdateFilter": {"name": "UpdateFilter", "http": {"method": "POST", "requestUri": "/filters/update", "responseCode": 200}, "input": {"shape": "UpdateFilterRequest"}, "output": {"shape": "UpdateFilterResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Specifies the action that is to be applied to the findings that match the filter.</p>"}, "UpdateOrgEc2DeepInspectionConfiguration": {"name": "UpdateOrgEc2DeepInspectionConfiguration", "http": {"method": "POST", "requestUri": "/ec2deepinspectionconfiguration/org/update", "responseCode": 200}, "input": {"shape": "UpdateOrgEc2DeepInspectionConfigurationRequest"}, "output": {"shape": "UpdateOrgEc2DeepInspectionConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates the Amazon Inspector deep inspection custom paths for your organization. You must be an Amazon Inspector delegated administrator to use this API.</p>"}, "UpdateOrganizationConfiguration": {"name": "UpdateOrganizationConfiguration", "http": {"method": "POST", "requestUri": "/organizationconfiguration/update", "responseCode": 200}, "input": {"shape": "UpdateOrganizationConfigurationRequest"}, "output": {"shape": "UpdateOrganizationConfigurationResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates the configurations for your Amazon Inspector organization.</p>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>You do not have sufficient access to perform this action.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "Account": {"type": "structure", "required": ["accountId", "resourceStatus", "status"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The ID of the Amazon Web Services account.</p>"}, "resourceStatus": {"shape": "ResourceStatus", "documentation": "<p>Details of the status of Amazon Inspector scans by resource type.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of Amazon Inspector for the account.</p>"}}, "documentation": "<p>An Amazon Web Services account within your environment that Amazon Inspector has been enabled for.</p>"}, "AccountAggregation": {"type": "structure", "members": {"findingType": {"shape": "AggregationFindingType", "documentation": "<p>The type of finding.</p>"}, "resourceType": {"shape": "AggregationResourceType", "documentation": "<p>The type of resource.</p>"}, "sortBy": {"shape": "AccountSortBy", "documentation": "<p>The value to sort by.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The sort order (ascending or descending).</p>"}}, "documentation": "<p>An object that contains details about an aggregation response based on Amazon Web Services accounts.</p>"}, "AccountAggregationResponse": {"type": "structure", "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID.</p>"}, "severityCounts": {"shape": "SeverityCounts", "documentation": "<p>The number of findings by severity.</p>"}}, "documentation": "<p>An aggregation of findings by Amazon Web Services account ID.</p>"}, "AccountId": {"type": "string", "max": 12, "min": 12, "pattern": "^\\d{12}$"}, "AccountIdSet": {"type": "list", "member": {"shape": "AccountId"}, "max": 100, "min": 0}, "AccountList": {"type": "list", "member": {"shape": "Account"}}, "AccountSortBy": {"type": "string", "enum": ["CRITICAL", "HIGH", "ALL"]}, "AccountState": {"type": "structure", "required": ["accountId", "resourceState", "state"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID.</p>"}, "resourceState": {"shape": "ResourceState", "documentation": "<p>An object detailing which resources Amazon Inspector is enabled to scan for the account.</p>"}, "state": {"shape": "State", "documentation": "<p>An object detailing the status of Amazon Inspector for the account.</p>"}}, "documentation": "<p>An object with details the status of an Amazon Web Services account within your Amazon Inspector environment.</p>"}, "AccountStateList": {"type": "list", "member": {"shape": "AccountState"}, "max": 100, "min": 0}, "AggCounts": {"type": "long"}, "AggregationFindingType": {"type": "string", "enum": ["NETWORK_REACHABILITY", "PACKAGE_VULNERABILITY", "CODE_VULNERABILITY"]}, "AggregationRequest": {"type": "structure", "members": {"accountAggregation": {"shape": "AccountAggregation", "documentation": "<p>An object that contains details about an aggregation request based on Amazon Web Services account IDs.</p>"}, "amiAggregation": {"shape": "AmiAggregation", "documentation": "<p>An object that contains details about an aggregation request based on Amazon Machine Images (AMIs).</p>"}, "awsEcrContainerAggregation": {"shape": "AwsEcrContainerAggregation", "documentation": "<p>An object that contains details about an aggregation request based on Amazon ECR container images.</p>"}, "ec2InstanceAggregation": {"shape": "Ec2InstanceAggregation", "documentation": "<p>An object that contains details about an aggregation request based on Amazon EC2 instances.</p>"}, "findingTypeAggregation": {"shape": "FindingTypeAggregation", "documentation": "<p>An object that contains details about an aggregation request based on finding types.</p>"}, "imageLayerAggregation": {"shape": "ImageLayerAggregation", "documentation": "<p>An object that contains details about an aggregation request based on container image layers.</p>"}, "lambdaFunctionAggregation": {"shape": "LambdaFunctionAggregation", "documentation": "<p>Returns an object with findings aggregated by AWS Lambda function.</p>"}, "lambdaLayerAggregation": {"shape": "LambdaLayerAggregation", "documentation": "<p>Returns an object with findings aggregated by AWS Lambda layer.</p>"}, "packageAggregation": {"shape": "PackageAggregation", "documentation": "<p>An object that contains details about an aggregation request based on operating system package type.</p>"}, "repositoryAggregation": {"shape": "RepositoryAggregation", "documentation": "<p>An object that contains details about an aggregation request based on Amazon ECR repositories.</p>"}, "titleAggregation": {"shape": "TitleAggregation", "documentation": "<p>An object that contains details about an aggregation request based on finding title.</p>"}}, "documentation": "<p>Contains details about an aggregation request.</p>", "union": true}, "AggregationResourceType": {"type": "string", "enum": ["AWS_EC2_INSTANCE", "AWS_ECR_CONTAINER_IMAGE", "AWS_LAMBDA_FUNCTION"]}, "AggregationResponse": {"type": "structure", "members": {"accountAggregation": {"shape": "AccountAggregationResponse", "documentation": "<p>An object that contains details about an aggregation response based on Amazon Web Services account IDs.</p>"}, "amiAggregation": {"shape": "AmiAggregationResponse", "documentation": "<p>An object that contains details about an aggregation response based on Amazon Machine Images (AMIs).</p>"}, "awsEcrContainerAggregation": {"shape": "AwsEcrContainerAggregationResponse", "documentation": "<p>An object that contains details about an aggregation response based on Amazon ECR container images.</p>"}, "ec2InstanceAggregation": {"shape": "Ec2InstanceAggregationResponse", "documentation": "<p>An object that contains details about an aggregation response based on Amazon EC2 instances.</p>"}, "findingTypeAggregation": {"shape": "FindingTypeAggregationResponse", "documentation": "<p>An object that contains details about an aggregation response based on finding types.</p>"}, "imageLayerAggregation": {"shape": "ImageLayerAggregationResponse", "documentation": "<p>An object that contains details about an aggregation response based on container image layers.</p>"}, "lambdaFunctionAggregation": {"shape": "LambdaFunctionAggregationResponse", "documentation": "<p>An aggregation of findings by AWS Lambda function.</p>"}, "lambdaLayerAggregation": {"shape": "LambdaLayerAggregationResponse", "documentation": "<p>An aggregation of findings by AWS Lambda layer.</p>"}, "packageAggregation": {"shape": "PackageAggregationResponse", "documentation": "<p>An object that contains details about an aggregation response based on operating system package type.</p>"}, "repositoryAggregation": {"shape": "RepositoryAggregationResponse", "documentation": "<p>An object that contains details about an aggregation response based on Amazon ECR repositories.</p>"}, "titleAggregation": {"shape": "TitleAggregationResponse", "documentation": "<p>An object that contains details about an aggregation response based on finding title.</p>"}}, "documentation": "<p>A structure that contains details about the results of an aggregation type.</p>", "union": true}, "AggregationResponseList": {"type": "list", "member": {"shape": "AggregationResponse"}}, "AggregationType": {"type": "string", "enum": ["FINDING_TYPE", "PACKAGE", "TITLE", "REPOSITORY", "AMI", "AWS_EC2_INSTANCE", "AWS_ECR_CONTAINER", "IMAGE_LAYER", "ACCOUNT", "AWS_LAMBDA_FUNCTION", "LAMBDA_LAYER"]}, "AmiAggregation": {"type": "structure", "members": {"amis": {"shape": "StringFilterList", "documentation": "<p>The IDs of AMIs to aggregate findings for.</p>"}, "sortBy": {"shape": "AmiSortBy", "documentation": "<p>The value to sort results by.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The order to sort results by.</p>"}}, "documentation": "<p>The details that define an aggregation based on Amazon machine images (AMIs).</p>"}, "AmiAggregationResponse": {"type": "structure", "required": ["ami"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID for the AMI.</p>"}, "affectedInstances": {"shape": "<PERSON>", "documentation": "<p>The IDs of Amazon EC2 instances using this AMI.</p>"}, "ami": {"shape": "AmiId", "documentation": "<p>The ID of the AMI that findings were aggregated for.</p>"}, "severityCounts": {"shape": "SeverityCounts", "documentation": "<p>An object that contains the count of matched findings per severity.</p>"}}, "documentation": "<p>A response that contains the results of a finding aggregation by AMI.</p>"}, "AmiId": {"type": "string", "pattern": "^ami-([a-z0-9]{8}|[a-z0-9]{17}|\\*)$"}, "AmiSortBy": {"type": "string", "enum": ["CRITICAL", "HIGH", "ALL", "AFFECTED_INSTANCES"]}, "Architecture": {"type": "string", "enum": ["X86_64", "ARM64"]}, "ArchitectureList": {"type": "list", "member": {"shape": "Architecture"}, "max": 1, "min": 1}, "Arn": {"type": "string", "max": 1011, "min": 1}, "AssociateMemberRequest": {"type": "structure", "required": ["accountId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the member account to be associated.</p>"}}}, "AssociateMemberResponse": {"type": "structure", "required": ["accountId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the successfully associated member account.</p>"}}}, "AtigData": {"type": "structure", "members": {"firstSeen": {"shape": "FirstSeen", "documentation": "<p>The date and time this vulnerability was first observed.</p>"}, "lastSeen": {"shape": "LastSeen", "documentation": "<p>The date and time this vulnerability was last observed.</p>"}, "targets": {"shape": "Targets", "documentation": "<p>The commercial sectors this vulnerability targets.</p>"}, "ttps": {"shape": "Ttps", "documentation": "<p>The <a href=\"https://attack.mitre.org/\">MITRE ATT&amp;CK</a> tactics, techniques, and procedures (TTPs) associated with vulnerability.</p>"}}, "documentation": "<p>The Amazon Web Services Threat Intel Group (ATIG) details for a specific vulnerability.</p>"}, "AutoEnable": {"type": "structure", "required": ["ec2", "ecr"], "members": {"ec2": {"shape": "Boolean", "documentation": "<p>Represents whether Amazon EC2 scans are automatically enabled for new members of your Amazon Inspector organization.</p>"}, "ecr": {"shape": "Boolean", "documentation": "<p>Represents whether Amazon ECR scans are automatically enabled for new members of your Amazon Inspector organization.</p>"}, "lambda": {"shape": "Boolean", "documentation": "<p>Represents whether AWS Lambda standard scans are automatically enabled for new members of your Amazon Inspector organization. </p>"}, "lambdaCode": {"shape": "Boolean", "documentation": "<p>Represents whether AWS Lambda code scans are automatically enabled for new members of your Amazon Inspector organization. <pre><code> &lt;/p&gt; </code></pre>"}}, "documentation": "<p>Represents which scan types are automatically enabled for new members of your Amazon Inspector organization.</p>"}, "AwsEc2InstanceDetails": {"type": "structure", "members": {"iamInstanceProfileArn": {"shape": "NonEmptyString", "documentation": "<p>The IAM instance profile ARN of the Amazon EC2 instance.</p>"}, "imageId": {"shape": "NonEmptyString", "documentation": "<p>The image ID of the Amazon EC2 instance.</p>"}, "ipV4Addresses": {"shape": "IpV4AddressList", "documentation": "<p>The IPv4 addresses of the Amazon EC2 instance.</p>"}, "ipV6Addresses": {"shape": "IpV6AddressList", "documentation": "<p>The IPv6 addresses of the Amazon EC2 instance.</p>"}, "keyName": {"shape": "NonEmptyString", "documentation": "<p>The name of the key pair used to launch the Amazon EC2 instance.</p>"}, "launchedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date and time the Amazon EC2 instance was launched at.</p>"}, "platform": {"shape": "Platform", "documentation": "<p>The platform of the Amazon EC2 instance.</p>"}, "subnetId": {"shape": "NonEmptyString", "documentation": "<p>The subnet ID of the Amazon EC2 instance.</p>"}, "type": {"shape": "NonEmptyString", "documentation": "<p>The type of the Amazon EC2 instance.</p>"}, "vpcId": {"shape": "NonEmptyString", "documentation": "<p>The VPC ID of the Amazon EC2 instance.</p>"}}, "documentation": "<p>Details of the Amazon EC2 instance involved in a finding.</p>"}, "AwsEcrContainerAggregation": {"type": "structure", "members": {"architectures": {"shape": "StringFilterList", "documentation": "<p>The architecture of the containers.</p>"}, "imageShas": {"shape": "StringFilterList", "documentation": "<p>The image SHA values.</p>"}, "imageTags": {"shape": "StringFilterList", "documentation": "<p>The image tags.</p>"}, "repositories": {"shape": "StringFilterList", "documentation": "<p>The container repositories.</p>"}, "resourceIds": {"shape": "StringFilterList", "documentation": "<p>The container resource IDs.</p>"}, "sortBy": {"shape": "AwsEcrContainerSortBy", "documentation": "<p>The value to sort by.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The sort order (ascending or descending).</p>"}}, "documentation": "<p>An aggregation of information about Amazon ECR containers.</p>"}, "AwsEcrContainerAggregationResponse": {"type": "structure", "required": ["resourceId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the account that owns the container.</p>"}, "architecture": {"shape": "String", "documentation": "<p>The architecture of the container.</p>"}, "imageSha": {"shape": "String", "documentation": "<p>The SHA value of the container image.</p>"}, "imageTags": {"shape": "StringList", "documentation": "<p>The container image stags.</p>"}, "repository": {"shape": "String", "documentation": "<p>The container repository.</p>"}, "resourceId": {"shape": "NonEmptyString", "documentation": "<p>The resource ID of the container.</p>"}, "severityCounts": {"shape": "SeverityCounts", "documentation": "<p>The number of finding by severity.</p>"}}, "documentation": "<p>An aggregation of information about Amazon ECR containers.</p>"}, "AwsEcrContainerImageDetails": {"type": "structure", "required": ["imageHash", "registry", "repositoryName"], "members": {"architecture": {"shape": "NonEmptyString", "documentation": "<p>The architecture of the Amazon ECR container image.</p>"}, "author": {"shape": "String", "documentation": "<p>The image author of the Amazon ECR container image.</p>"}, "imageHash": {"shape": "ImageHash", "documentation": "<p>The image hash of the Amazon ECR container image.</p>"}, "imageTags": {"shape": "ImageTagList", "documentation": "<p>The image tags attached to the Amazon ECR container image.</p>"}, "platform": {"shape": "Platform", "documentation": "<p>The platform of the Amazon ECR container image.</p>"}, "pushedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date and time the Amazon ECR container image was pushed.</p>"}, "registry": {"shape": "NonEmptyString", "documentation": "<p>The registry for the Amazon ECR container image.</p>"}, "repositoryName": {"shape": "NonEmptyString", "documentation": "<p>The name of the repository the Amazon ECR container image resides in.</p>"}}, "documentation": "<p>The image details of the Amazon ECR container image.</p>"}, "AwsEcrContainerSortBy": {"type": "string", "enum": ["CRITICAL", "HIGH", "ALL"]}, "AwsLambdaFunctionDetails": {"type": "structure", "required": ["codeSha256", "executionRoleArn", "functionName", "runtime", "version"], "members": {"architectures": {"shape": "ArchitectureList", "documentation": "<p>The instruction set architecture that the AWS Lambda function supports. Architecture is a string array with one of the valid values. The default architecture value is <code>x86_64</code>.</p>"}, "codeSha256": {"shape": "NonEmptyString", "documentation": "<p>The SHA256 hash of the AWS Lambda function's deployment package.</p>"}, "executionRoleArn": {"shape": "ExecutionRoleArn", "documentation": "<p>The AWS Lambda function's execution role.</p>"}, "functionName": {"shape": "FunctionName", "documentation": "<p>The name of the AWS Lambda function.</p>"}, "lastModifiedAt": {"shape": "Timestamp", "documentation": "<p>The date and time that a user last updated the configuration, in <a href=\"https://www.iso.org/iso-8601-date-and-time-format.html\">ISO 8601 format</a> </p>"}, "layers": {"shape": "LayerList", "documentation": "<p>The AWS Lambda function's <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-layers.html\"> layers</a>. A Lambda function can have up to five layers.</p>"}, "packageType": {"shape": "PackageType", "documentation": "<p>The type of deployment package. Set to <code>Image</code> for container image and set <code>Zip</code> for .zip file archive.</p>"}, "runtime": {"shape": "Runtime", "documentation": "<p>The runtime environment for the AWS Lambda function.</p>"}, "version": {"shape": "Version", "documentation": "<p>The version of the AWS Lambda function.</p>"}, "vpcConfig": {"shape": "LambdaVpcConfig", "documentation": "<p>The AWS Lambda function's networking configuration.</p>"}}, "documentation": "<p> A summary of information about the AWS Lambda function.</p>"}, "BadRequestException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>One or more tags submitted as part of the request is not valid.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "BatchGetAccountStatusRequest": {"type": "structure", "members": {"accountIds": {"shape": "AccountIdSet", "documentation": "<p>The 12-digit Amazon Web Services account IDs of the accounts to retrieve Amazon Inspector status for.</p>"}}}, "BatchGetAccountStatusResponse": {"type": "structure", "required": ["accounts"], "members": {"accounts": {"shape": "AccountStateList", "documentation": "<p>An array of objects that provide details on the status of Amazon Inspector for each of the requested accounts.</p>"}, "failedAccounts": {"shape": "FailedAccountList", "documentation": "<p>An array of objects detailing any accounts that failed to enable Amazon Inspector and why.</p>"}}}, "BatchGetCodeSnippetRequest": {"type": "structure", "required": ["findingArns"], "members": {"findingArns": {"shape": "BatchGetCodeSnippetRequestFindingArnsList", "documentation": "<p>An array of finding ARNs for the findings you want to retrieve code snippets from.</p>"}}}, "BatchGetCodeSnippetRequestFindingArnsList": {"type": "list", "member": {"shape": "FindingArn"}, "max": 10, "min": 1}, "BatchGetCodeSnippetResponse": {"type": "structure", "members": {"codeSnippetResults": {"shape": "CodeSnippetResultList", "documentation": "<p>The retrieved code snippets associated with the provided finding ARNs.</p>"}, "errors": {"shape": "CodeSnippetErrorList", "documentation": "<p>Any errors Amazon Inspector encountered while trying to retrieve the requested code snippets.</p>"}}}, "BatchGetFindingDetailsRequest": {"type": "structure", "required": ["findingArns"], "members": {"findingArns": {"shape": "FindingArnList", "documentation": "<p>A list of finding ARNs.</p>"}}}, "BatchGetFindingDetailsResponse": {"type": "structure", "members": {"errors": {"shape": "FindingDetailsErrorList", "documentation": "<p>Error information for findings that details could not be returned for.</p>"}, "findingDetails": {"shape": "FindingDetails", "documentation": "<p>A finding's vulnerability details.</p>"}}}, "BatchGetFreeTrialInfoRequest": {"type": "structure", "required": ["accountIds"], "members": {"accountIds": {"shape": "BatchGetFreeTrialInfoRequestAccountIdsList", "documentation": "<p>The account IDs to get free trial status for.</p>"}}}, "BatchGetFreeTrialInfoRequestAccountIdsList": {"type": "list", "member": {"shape": "MeteringAccountId"}, "max": 100, "min": 1}, "BatchGetFreeTrialInfoResponse": {"type": "structure", "required": ["accounts", "failedAccounts"], "members": {"accounts": {"shape": "FreeTrialAccountInfoList", "documentation": "<p>An array of objects that provide Amazon Inspector free trial details for each of the requested accounts. </p>"}, "failedAccounts": {"shape": "FreeTrialInfoErrorList", "documentation": "<p>An array of objects detailing any accounts that free trial data could not be returned for.</p>"}}}, "BatchGetMemberEc2DeepInspectionStatusRequest": {"type": "structure", "members": {"accountIds": {"shape": "AccountIdSet", "documentation": "<p>The unique identifiers for the Amazon Web Services accounts to retrieve Amazon Inspector deep inspection activation status for. <pre><code> &lt;/p&gt; </code></pre>"}}}, "BatchGetMemberEc2DeepInspectionStatusResponse": {"type": "structure", "members": {"accountIds": {"shape": "MemberAccountEc2DeepInspectionStatusStateList", "documentation": "<p>An array of objects that provide details on the activation status of Amazon Inspector deep inspection for each of the requested accounts. <pre><code> &lt;/p&gt; </code></pre>"}, "failedAccountIds": {"shape": "FailedMemberAccountEc2DeepInspectionStatusStateList", "documentation": "<p>An array of objects that provide details on any accounts that failed to activate Amazon Inspector deep inspection and why. <pre><code> &lt;/p&gt; </code></pre>"}}}, "BatchUpdateMemberEc2DeepInspectionStatusRequest": {"type": "structure", "required": ["accountIds"], "members": {"accountIds": {"shape": "MemberAccountEc2DeepInspectionStatusList", "documentation": "<p>The unique identifiers for the Amazon Web Services accounts to change Amazon Inspector deep inspection status for.</p>"}}}, "BatchUpdateMemberEc2DeepInspectionStatusResponse": {"type": "structure", "members": {"accountIds": {"shape": "MemberAccountEc2DeepInspectionStatusStateList", "documentation": "<p>An array of objects that provide details for each of the accounts that Amazon Inspector deep inspection status was successfully changed for. </p>"}, "failedAccountIds": {"shape": "FailedMemberAccountEc2DeepInspectionStatusStateList", "documentation": "<p>An array of objects that provide details for each of the accounts that Amazon Inspector deep inspection status could not be successfully changed for. </p>"}}}, "Boolean": {"type": "boolean", "box": true}, "CancelFindingsReportRequest": {"type": "structure", "required": ["reportId"], "members": {"reportId": {"shape": "ReportId", "documentation": "<p>The ID of the report to be canceled.</p>"}}}, "CancelFindingsReportResponse": {"type": "structure", "required": ["reportId"], "members": {"reportId": {"shape": "ReportId", "documentation": "<p>The ID of the canceled report.</p>"}}}, "CancelSbomExportRequest": {"type": "structure", "required": ["reportId"], "members": {"reportId": {"shape": "ReportId", "documentation": "<p>The report ID of the SBOM export to cancel.</p>"}}}, "CancelSbomExportResponse": {"type": "structure", "members": {"reportId": {"shape": "ReportId", "documentation": "<p>The report ID of the canceled SBOM export.</p>"}}}, "CisaAction": {"type": "string", "min": 0}, "CisaData": {"type": "structure", "members": {"action": {"shape": "CisaAction", "documentation": "<p>The remediation action recommended by CISA for this vulnerability.</p>"}, "dateAdded": {"shape": "CisaDateAdded", "documentation": "<p>The date and time CISA added this vulnerability to their catalogue.</p>"}, "dateDue": {"shape": "CisaDateDue", "documentation": "<p>The date and time CISA expects a fix to have been provided vulnerability.</p>"}}, "documentation": "<p>The Cybersecurity and Infrastructure Security Agency (CISA) details for a specific vulnerability.</p>"}, "CisaDateAdded": {"type": "timestamp"}, "CisaDateDue": {"type": "timestamp"}, "ClientToken": {"type": "string", "max": 64, "min": 1}, "CodeFilePath": {"type": "structure", "required": ["endLine", "fileName", "filePath", "startLine"], "members": {"endLine": {"shape": "Integer", "documentation": "<p>The line number of the last line of code that a vulnerability was found in.</p>"}, "fileName": {"shape": "NonEmptyString", "documentation": "<p>The name of the file the code vulnerability was found in.</p>"}, "filePath": {"shape": "NonEmptyString", "documentation": "<p>The file path to the code that a vulnerability was found in.</p>"}, "startLine": {"shape": "Integer", "documentation": "<p>The line number of the first line of code that a vulnerability was found in.</p>"}}, "documentation": "<p>Contains information on where a code vulnerability is located in your Lambda function.</p>"}, "CodeLine": {"type": "structure", "required": ["content", "lineNumber"], "members": {"content": {"shape": "CodeLineContentString", "documentation": "<p>The content of a line of code</p>"}, "lineNumber": {"shape": "Integer", "documentation": "<p>The line number that a section of code is located at.</p>"}}, "documentation": "<p>Contains information on the lines of code associated with a code snippet.</p>"}, "CodeLineContentString": {"type": "string", "max": 240, "min": 0}, "CodeLineList": {"type": "list", "member": {"shape": "CodeLine"}, "max": 20, "min": 1}, "CodeSnippetError": {"type": "structure", "required": ["errorCode", "errorMessage", "findingArn"], "members": {"errorCode": {"shape": "CodeSnippetErrorCode", "documentation": "<p>The error code for the error that prevented a code snippet from being retrieved.</p>"}, "errorMessage": {"shape": "NonEmptyString", "documentation": "<p>The error message received when Amazon Inspector failed to retrieve a code snippet.</p>"}, "findingArn": {"shape": "FindingArn", "documentation": "<p>The ARN of the finding that a code snippet couldn't be retrieved for.</p>"}}, "documentation": "<p>Contains information about any errors encountered while trying to retrieve a code snippet.</p>"}, "CodeSnippetErrorCode": {"type": "string", "enum": ["INTERNAL_ERROR", "ACCESS_DENIED", "CODE_SNIPPET_NOT_FOUND", "INVALID_INPUT"]}, "CodeSnippetErrorList": {"type": "list", "member": {"shape": "CodeSnippetError"}}, "CodeSnippetResult": {"type": "structure", "members": {"codeSnippet": {"shape": "CodeLineList", "documentation": "<p>Contains information on the retrieved code snippet.</p>"}, "endLine": {"shape": "Integer", "documentation": "<p>The line number of the last line of a code snippet.</p>"}, "findingArn": {"shape": "FindingArn", "documentation": "<p>The ARN of a finding that the code snippet is associated with.</p>"}, "startLine": {"shape": "Integer", "documentation": "<p>The line number of the first line of a code snippet.</p>"}, "suggestedFixes": {"shape": "SuggestedFixes", "documentation": "<p>Details of a suggested code fix.</p>"}}, "documentation": "<p>Contains information on a code snippet retrieved by Amazon Inspector from a code vulnerability finding.</p>"}, "CodeSnippetResultList": {"type": "list", "member": {"shape": "CodeSnippetResult"}}, "CodeVulnerabilityDetails": {"type": "structure", "required": ["cwes", "detectorId", "detectorName", "filePath"], "members": {"cwes": {"shape": "CweList", "documentation": "<p>The Common Weakness Enumeration (CWE) item associated with the detected vulnerability.</p>"}, "detectorId": {"shape": "NonEmptyString", "documentation": "<p>The ID for the Amazon CodeGuru detector associated with the finding. For more information on detectors see <a href=\"https://docs.aws.amazon.com/codeguru/detector-library\">Amazon CodeGuru Detector Library</a>.</p>"}, "detectorName": {"shape": "NonEmptyString", "documentation": "<p>The name of the detector used to identify the code vulnerability. For more information on detectors see <a href=\"https://docs.aws.amazon.com/codeguru/detector-library\">CodeGuru Detector Library</a>.</p>"}, "detectorTags": {"shape": "DetectorTagList", "documentation": "<p>The detector tag associated with the vulnerability. Detector tags group related vulnerabilities by common themes or tactics. For a list of available tags by programming language, see <a href=\"https://docs.aws.amazon.com/codeguru/detector-library/java/tags/\">Java tags</a>, or <a href=\"https://docs.aws.amazon.com/codeguru/detector-library/python/tags/\">Python tags</a>. </p>"}, "filePath": {"shape": "CodeFilePath", "documentation": "<p>Contains information on where the code vulnerability is located in your code.</p>"}, "referenceUrls": {"shape": "ReferenceUrls", "documentation": "<p>A URL containing supporting documentation about the code vulnerability detected.</p>"}, "ruleId": {"shape": "NonEmptyString", "documentation": "<p>The identifier for a rule that was used to detect the code vulnerability.</p>"}, "sourceLambdaLayerArn": {"shape": "LambdaLayerArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Lambda layer that the code vulnerability was detected in.</p>"}}, "documentation": "<p>Contains information on the code vulnerability identified in your Lambda function.</p>"}, "Component": {"type": "string"}, "ComponentType": {"type": "string"}, "ConflictException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "String"}, "resourceId": {"shape": "String", "documentation": "<p>The ID of the conflicting resource.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>The type of the conflicting resource.</p>"}}, "documentation": "<p>A conflict occurred.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "Counts": {"type": "structure", "members": {"count": {"shape": "AggCounts", "documentation": "<p>The number of resources.</p>"}, "groupKey": {"shape": "GroupKey", "documentation": "<p>The key associated with this group</p>"}}, "documentation": "<p>a structure that contains information on the count of resources within a group.</p>"}, "CountsList": {"type": "list", "member": {"shape": "Counts"}, "max": 5, "min": 1}, "CoverageDateFilter": {"type": "structure", "members": {"endInclusive": {"shape": "DateTimeTimestamp", "documentation": "<p>A timestamp representing the end of the time period to filter results by.</p>"}, "startInclusive": {"shape": "DateTimeTimestamp", "documentation": "<p>A timestamp representing the start of the time period to filter results by.</p>"}}, "documentation": "<p>Contains details of a coverage date filter.</p>"}, "CoverageDateFilterList": {"type": "list", "member": {"shape": "CoverageDateFilter"}, "max": 10, "min": 1}, "CoverageFilterCriteria": {"type": "structure", "members": {"accountId": {"shape": "CoverageStringFilterList", "documentation": "<p>An array of Amazon Web Services account IDs to return coverage statistics for.</p>"}, "ec2InstanceTags": {"shape": "CoverageMapFilterList", "documentation": "<p>The Amazon EC2 instance tags to filter on.</p>"}, "ecrImageTags": {"shape": "CoverageStringFilterList", "documentation": "<p>The Amazon ECR image tags to filter on.</p>"}, "ecrRepositoryName": {"shape": "CoverageStringFilterList", "documentation": "<p>The Amazon ECR repository name to filter on.</p>"}, "lambdaFunctionName": {"shape": "CoverageStringFilterList", "documentation": "<p>Returns coverage statistics for AWS Lambda functions filtered by function names.</p>"}, "lambdaFunctionRuntime": {"shape": "CoverageStringFilterList", "documentation": "<p>Returns coverage statistics for AWS Lambda functions filtered by runtime.</p>"}, "lambdaFunctionTags": {"shape": "CoverageMapFilterList", "documentation": "<p>Returns coverage statistics for AWS Lambda functions filtered by tag.</p>"}, "lastScannedAt": {"shape": "CoverageDateFilterList", "documentation": "<p>Filters Amazon Web Services resources based on whether Amazon Inspector has checked them for vulnerabilities within the specified time range.</p>"}, "resourceId": {"shape": "CoverageStringFilterList", "documentation": "<p>An array of Amazon Web Services resource IDs to return coverage statistics for.</p>"}, "resourceType": {"shape": "CoverageStringFilterList", "documentation": "<p>An array of Amazon Web Services resource types to return coverage statistics for. The values can be <code>AWS_EC2_INSTANCE</code>, <code>AWS_LAMBDA_FUNCTION</code> or <code>AWS_ECR_REPOSITORY</code>.</p>"}, "scanStatusCode": {"shape": "CoverageStringFilterList", "documentation": "<p>The scan status code to filter on. Valid values are: <code>ValidationException</code>, <code>InternalServerException</code>, <code>ResourceNotFoundException</code>, <code>BadRequestException</code>, and <code>ThrottlingException</code>.</p>"}, "scanStatusReason": {"shape": "CoverageStringFilterList", "documentation": "<p>The scan status reason to filter on.</p>"}, "scanType": {"shape": "CoverageStringFilterList", "documentation": "<p>An array of Amazon Inspector scan types to return coverage statistics for.</p>"}}, "documentation": "<p>A structure that identifies filter criteria for <code>GetCoverageStatistics</code>.</p>"}, "CoverageMapComparison": {"type": "string", "enum": ["EQUALS"]}, "CoverageMapFilter": {"type": "structure", "required": ["comparison", "key"], "members": {"comparison": {"shape": "CoverageMapComparison", "documentation": "<p>The operator to compare coverage on.</p>"}, "key": {"shape": "NonEmptyString", "documentation": "<p>The tag key associated with the coverage map filter.</p>"}, "value": {"shape": "NonEmptyString", "documentation": "<p>The tag value associated with the coverage map filter.</p>"}}, "documentation": "<p>Contains details of a coverage map filter.</p>"}, "CoverageMapFilterList": {"type": "list", "member": {"shape": "CoverageMapFilter"}, "max": 10, "min": 1}, "CoverageResourceType": {"type": "string", "enum": ["AWS_EC2_INSTANCE", "AWS_ECR_CONTAINER_IMAGE", "AWS_ECR_REPOSITORY", "AWS_LAMBDA_FUNCTION"]}, "CoverageStringComparison": {"type": "string", "enum": ["EQUALS", "NOT_EQUALS"]}, "CoverageStringFilter": {"type": "structure", "required": ["comparison", "value"], "members": {"comparison": {"shape": "CoverageStringComparison", "documentation": "<p>The operator to compare strings on.</p>"}, "value": {"shape": "CoverageStringInput", "documentation": "<p>The value to compare strings on.</p>"}}, "documentation": "<p>Contains details of a coverage string filter.</p>"}, "CoverageStringFilterList": {"type": "list", "member": {"shape": "CoverageStringFilter"}, "max": 10, "min": 1}, "CoverageStringInput": {"type": "string", "max": 1024, "min": 1}, "CoveredResource": {"type": "structure", "required": ["accountId", "resourceId", "resourceType", "scanType"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the covered resource.</p>"}, "lastScannedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date and time the resource was last checked for vulnerabilities.</p>"}, "resourceId": {"shape": "ResourceId", "documentation": "<p>The ID of the covered resource.</p>"}, "resourceMetadata": {"shape": "ResourceScanMetadata", "documentation": "<p>An object that contains details about the metadata.</p>"}, "resourceType": {"shape": "CoverageResourceType", "documentation": "<p>The type of the covered resource.</p>"}, "scanStatus": {"shape": "ScanStatus", "documentation": "<p>The status of the scan covering the resource.</p>"}, "scanType": {"shape": "ScanType", "documentation": "<p>The Amazon Inspector scan type covering the resource.</p>"}}, "documentation": "<p>An object that contains details about a resource covered by Amazon Inspector.</p>"}, "CoveredResources": {"type": "list", "member": {"shape": "CoveredResource"}}, "CreateFilterRequest": {"type": "structure", "required": ["action", "filterCriteria", "name"], "members": {"action": {"shape": "FilterAction", "documentation": "<p>Defines the action that is to be applied to the findings that match the filter.</p>"}, "description": {"shape": "FilterDescription", "documentation": "<p>A description of the filter.</p>"}, "filterCriteria": {"shape": "FilterCriteria", "documentation": "<p>Defines the criteria to be used in the filter for querying findings.</p>"}, "name": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name of the filter. Minimum length of 3. Maximum length of 64. Valid characters include alphanumeric characters, dot (.), underscore (_), and dash (-). Spaces are not allowed.</p>"}, "reason": {"shape": "FilterReason", "documentation": "<p>The reason for creating the filter.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>A list of tags for the filter.</p>"}}}, "CreateFilterResponse": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "FilterArn", "documentation": "<p>The Amazon Resource Number (ARN) of the successfully created filter.</p>"}}}, "CreateFindingsReportRequest": {"type": "structure", "required": ["reportFormat", "s3Destination"], "members": {"filterCriteria": {"shape": "FilterCriteria", "documentation": "<p>The filter criteria to apply to the results of the finding report.</p>"}, "reportFormat": {"shape": "ReportFormat", "documentation": "<p>The format to generate the report in.</p>"}, "s3Destination": {"shape": "Destination", "documentation": "<p>The Amazon S3 export destination for the report.</p>"}}}, "CreateFindingsReportResponse": {"type": "structure", "members": {"reportId": {"shape": "ReportId", "documentation": "<p>The ID of the report.</p>"}}}, "CreateSbomExportRequest": {"type": "structure", "required": ["reportFormat", "s3Destination"], "members": {"reportFormat": {"shape": "SbomReportFormat", "documentation": "<p>The output format for the software bill of materials (SBOM) report.</p>"}, "resourceFilterCriteria": {"shape": "ResourceFilterCriteria", "documentation": "<p>The resource filter criteria for the software bill of materials (SBOM) report.</p>"}, "s3Destination": {"shape": "Destination"}}}, "CreateSbomExportResponse": {"type": "structure", "members": {"reportId": {"shape": "ReportId", "documentation": "<p>The report ID for the software bill of materials (SBOM) report.</p>"}}}, "Currency": {"type": "string", "enum": ["USD"]}, "Cvss2": {"type": "structure", "members": {"baseScore": {"shape": "Cvss2BaseScore", "documentation": "<p>The CVSS v2 base score for the vulnerability.</p>"}, "scoringVector": {"shape": "Cvss2ScoringVector", "documentation": "<p>The scoring vector associated with the CVSS v2 score.</p>"}}, "documentation": "<p>The Common Vulnerability Scoring System (CVSS) version 2 details for the vulnerability.</p>"}, "Cvss2BaseScore": {"type": "double"}, "Cvss2ScoringVector": {"type": "string", "min": 0}, "Cvss3": {"type": "structure", "members": {"baseScore": {"shape": "Cvss3BaseScore", "documentation": "<p>The CVSS v3 base score for the vulnerability.</p>"}, "scoringVector": {"shape": "Cvss3ScoringVector", "documentation": "<p>The scoring vector associated with the CVSS v3 score.</p>"}}, "documentation": "<p>The Common Vulnerability Scoring System (CVSS) version 3 details for the vulnerability.</p>"}, "Cvss3BaseScore": {"type": "double"}, "Cvss3ScoringVector": {"type": "string", "min": 0}, "CvssScore": {"type": "structure", "required": ["baseScore", "scoringVector", "source", "version"], "members": {"baseScore": {"shape": "Double", "documentation": "<p>The base CVSS score used for the finding.</p>"}, "scoringVector": {"shape": "NonEmptyString", "documentation": "<p>The vector string of the CVSS score.</p>"}, "source": {"shape": "NonEmptyString", "documentation": "<p>The source of the CVSS score.</p>"}, "version": {"shape": "NonEmptyString", "documentation": "<p>The version of CVSS used for the score.</p>"}}, "documentation": "<p>The CVSS score for a finding.</p>"}, "CvssScoreAdjustment": {"type": "structure", "required": ["metric", "reason"], "members": {"metric": {"shape": "NonEmptyString", "documentation": "<p>The metric used to adjust the CVSS score.</p>"}, "reason": {"shape": "NonEmptyString", "documentation": "<p>The reason the CVSS score has been adjustment.</p>"}}, "documentation": "<p>Details on adjustments Amazon Inspector made to the CVSS score for a finding.</p>"}, "CvssScoreAdjustmentList": {"type": "list", "member": {"shape": "CvssScoreAdjustment"}}, "CvssScoreDetails": {"type": "structure", "required": ["score", "scoreSource", "scoringVector", "version"], "members": {"adjustments": {"shape": "CvssScoreAdjustmentList", "documentation": "<p>An object that contains details about adjustment Amazon Inspector made to the CVSS score.</p>"}, "cvssSource": {"shape": "NonEmptyString", "documentation": "<p>The source of the CVSS data.</p>"}, "score": {"shape": "Double", "documentation": "<p>The CVSS score.</p>"}, "scoreSource": {"shape": "NonEmptyString", "documentation": "<p>The source for the CVSS score.</p>"}, "scoringVector": {"shape": "NonEmptyString", "documentation": "<p>The vector for the CVSS score.</p>"}, "version": {"shape": "NonEmptyString", "documentation": "<p>The CVSS version used in scoring.</p>"}}, "documentation": "<p>Information about the CVSS score.</p>"}, "CvssScoreList": {"type": "list", "member": {"shape": "CvssScore"}}, "Cwe": {"type": "string", "min": 0}, "CweList": {"type": "list", "member": {"shape": "NonEmptyString"}, "max": 10, "min": 1}, "Cwes": {"type": "list", "member": {"shape": "Cwe"}, "min": 0}, "DateFilter": {"type": "structure", "members": {"endInclusive": {"shape": "Timestamp", "documentation": "<p>A timestamp representing the end of the time period filtered on.</p>"}, "startInclusive": {"shape": "Timestamp", "documentation": "<p>A timestamp representing the start of the time period filtered on.</p>"}}, "documentation": "<p>Contains details on the time range used to filter findings.</p>"}, "DateFilterList": {"type": "list", "member": {"shape": "DateFilter"}, "max": 10, "min": 1}, "DateTimeTimestamp": {"type": "timestamp"}, "DelegatedAdmin": {"type": "structure", "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the Amazon Inspector delegated administrator for your organization.</p>"}, "relationshipStatus": {"shape": "RelationshipStatus", "documentation": "<p>The status of the Amazon Inspector delegated administrator.</p>"}}, "documentation": "<p>Details of the Amazon Inspector delegated administrator for your organization.</p>"}, "DelegatedAdminAccount": {"type": "structure", "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the Amazon Inspector delegated administrator for your organization.</p>"}, "status": {"shape": "DelegatedAdminStatus", "documentation": "<p>The status of the Amazon Inspector delegated administrator.</p>"}}, "documentation": "<p>Details of the Amazon Inspector delegated administrator for your organization.</p>"}, "DelegatedAdminAccountList": {"type": "list", "member": {"shape": "DelegatedAdminAccount"}, "max": 5, "min": 0}, "DelegatedAdminStatus": {"type": "string", "enum": ["ENABLED", "DISABLE_IN_PROGRESS"]}, "DeleteFilterRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "FilterArn", "documentation": "<p>The Amazon Resource Number (ARN) of the filter to be deleted.</p>"}}}, "DeleteFilterResponse": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "FilterArn", "documentation": "<p>The Amazon Resource Number (ARN) of the filter that has been deleted.</p>"}}}, "DescribeOrganizationConfigurationRequest": {"type": "structure", "members": {}}, "DescribeOrganizationConfigurationResponse": {"type": "structure", "members": {"autoEnable": {"shape": "AutoEnable", "documentation": "<p>The scan types are automatically enabled for new members of your organization.</p>"}, "maxAccountLimitReached": {"shape": "Boolean", "documentation": "<p>Represents whether your organization has reached the maximum Amazon Web Services account limit for Amazon Inspector.</p>"}}}, "Destination": {"type": "structure", "required": ["bucketName", "kmsKeyArn"], "members": {"bucketName": {"shape": "String", "documentation": "<p>The name of the Amazon S3 bucket to export findings to.</p>"}, "keyPrefix": {"shape": "String", "documentation": "<p>The prefix that the findings will be written under.</p>"}, "kmsKeyArn": {"shape": "String", "documentation": "<p>The ARN of the KMS key used to encrypt data when exporting findings.</p>"}}, "documentation": "<p>Contains details of the Amazon S3 bucket and KMS key used to export findings.</p>"}, "DetectionPlatforms": {"type": "list", "member": {"shape": "NonEmptyString"}, "max": 100, "min": 0}, "DetectorTagList": {"type": "list", "member": {"shape": "NonEmptyString"}, "max": 10, "min": 1}, "DisableDelegatedAdminAccountRequest": {"type": "structure", "required": ["delegatedAdminAccountId"], "members": {"delegatedAdminAccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the current Amazon Inspector delegated administrator.</p>"}}}, "DisableDelegatedAdminAccountResponse": {"type": "structure", "required": ["delegatedAdminAccountId"], "members": {"delegatedAdminAccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the successfully disabled delegated administrator.</p>"}}}, "DisableRequest": {"type": "structure", "members": {"accountIds": {"shape": "AccountIdSet", "documentation": "<p>An array of account IDs you want to disable Amazon Inspector scans for.</p>"}, "resourceTypes": {"shape": "DisableResourceTypeList", "documentation": "<p>The resource scan types you want to disable.</p>"}}}, "DisableResourceTypeList": {"type": "list", "member": {"shape": "ResourceScanType"}, "max": 3, "min": 0}, "DisableResponse": {"type": "structure", "required": ["accounts"], "members": {"accounts": {"shape": "AccountList", "documentation": "<p>Information on the accounts that have had Amazon Inspector scans successfully disabled. Details are provided for each account.</p>"}, "failedAccounts": {"shape": "FailedAccountList", "documentation": "<p>Information on any accounts for which Amazon Inspector scans could not be disabled. Details are provided for each account.</p>"}}}, "DisassociateMemberRequest": {"type": "structure", "required": ["accountId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the member account to disassociate.</p>"}}}, "DisassociateMemberResponse": {"type": "structure", "required": ["accountId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the successfully disassociated member.</p>"}}}, "Double": {"type": "double", "box": true}, "Ec2DeepInspectionStatus": {"type": "string", "enum": ["ACTIVATED", "DEACTIVATED", "PENDING", "FAILED"]}, "Ec2InstanceAggregation": {"type": "structure", "members": {"amis": {"shape": "StringFilterList", "documentation": "<p>The AMI IDs associated with the Amazon EC2 instances to aggregate findings for.</p>"}, "instanceIds": {"shape": "StringFilterList", "documentation": "<p>The Amazon EC2 instance IDs to aggregate findings for.</p>"}, "instanceTags": {"shape": "MapFilterList", "documentation": "<p>The Amazon EC2 instance tags to aggregate findings for.</p>"}, "operatingSystems": {"shape": "StringFilterList", "documentation": "<p>The operating system types to aggregate findings for. Valid values must be uppercase and underscore separated, examples are <code>ORACLE_LINUX_7</code> and <code>ALPINE_LINUX_3_8</code>.</p>"}, "sortBy": {"shape": "Ec2InstanceSortBy", "documentation": "<p>The value to sort results by.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The order to sort results by.</p>"}}, "documentation": "<p>The details that define an aggregation based on Amazon EC2 instances.</p>"}, "Ec2InstanceAggregationResponse": {"type": "structure", "required": ["instanceId"], "members": {"accountId": {"shape": "String", "documentation": "<p>The Amazon Web Services account for the Amazon EC2 instance.</p>"}, "ami": {"shape": "AmiId", "documentation": "<p>The Amazon Machine Image (AMI) of the Amazon EC2 instance.</p>"}, "instanceId": {"shape": "NonEmptyString", "documentation": "<p>The Amazon EC2 instance ID.</p>"}, "instanceTags": {"shape": "TagMap", "documentation": "<p>The tags attached to the instance.</p>"}, "networkFindings": {"shape": "<PERSON>", "documentation": "<p>The number of network findings for the Amazon EC2 instance.</p>"}, "operatingSystem": {"shape": "String", "documentation": "<p>The operating system of the Amazon EC2 instance.</p>"}, "severityCounts": {"shape": "SeverityCounts", "documentation": "<p>An object that contains the count of matched findings per severity.</p>"}}, "documentation": "<p>A response that contains the results of a finding aggregation by Amazon EC2 instance.</p>"}, "Ec2InstanceSortBy": {"type": "string", "enum": ["NETWORK_FINDINGS", "CRITICAL", "HIGH", "ALL"]}, "Ec2Metadata": {"type": "structure", "members": {"amiId": {"shape": "AmiId", "documentation": "<p>The ID of the Amazon Machine Image (AMI) used to launch the instance.</p>"}, "platform": {"shape": "Ec2Platform", "documentation": "<p>The platform of the instance.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags attached to the instance.</p>"}}, "documentation": "<p>Meta data details of an Amazon EC2 instance.</p>"}, "Ec2Platform": {"type": "string", "enum": ["WINDOWS", "LINUX", "UNKNOWN", "MACOS"]}, "EcrConfiguration": {"type": "structure", "required": ["rescanDuration"], "members": {"rescanDuration": {"shape": "EcrRescanDuration", "documentation": "<p>The ECR automated re-scan duration defines how long an ECR image will be actively scanned by Amazon Inspector. When the number of days since an image was last pushed exceeds the automated re-scan duration the monitoring state of that image becomes <code>inactive</code> and all associated findings are scheduled for closure.</p>"}}, "documentation": "<p>Details about the ECR automated re-scan duration setting for your environment.</p>"}, "EcrConfigurationState": {"type": "structure", "members": {"rescanDurationState": {"shape": "EcrRescanDurationState", "documentation": "<p>An object that contains details about the state of the ECR automated re-scan setting.</p>"}}, "documentation": "<p>Details about the state of the ECR scans for your environment.</p>"}, "EcrContainerImageMetadata": {"type": "structure", "members": {"tags": {"shape": "TagList", "documentation": "<p>Tags associated with the Amazon ECR image metadata.</p>"}}, "documentation": "<p>Information on the Amazon ECR image metadata associated with a finding.</p>"}, "EcrRepositoryMetadata": {"type": "structure", "members": {"name": {"shape": "String", "documentation": "<p>The name of the Amazon ECR repository.</p>"}, "scanFrequency": {"shape": "EcrScanFrequency", "documentation": "<p>The frequency of scans.</p>"}}, "documentation": "<p>Information on the Amazon ECR repository metadata associated with a finding.</p>"}, "EcrRescanDuration": {"type": "string", "enum": ["LIFETIME", "DAYS_30", "DAYS_180"]}, "EcrRescanDurationState": {"type": "structure", "members": {"rescanDuration": {"shape": "EcrRescanDuration", "documentation": "<p>The ECR automated re-scan duration defines how long an ECR image will be actively scanned by Amazon Inspector. When the number of days since an image was last pushed exceeds the automated re-scan duration the monitoring state of that image becomes <code>inactive</code> and all associated findings are scheduled for closure.</p>"}, "status": {"shape": "EcrRescanDurationStatus", "documentation": "<p>The status of changes to the ECR automated re-scan duration.</p>"}, "updatedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>A timestamp representing when the last time the ECR scan duration setting was changed.</p>"}}, "documentation": "<p>Details about the state of any changes to the ECR automated re-scan duration setting.</p>"}, "EcrRescanDurationStatus": {"type": "string", "enum": ["SUCCESS", "PENDING", "FAILED"]}, "EcrScanFrequency": {"type": "string", "enum": ["MANUAL", "SCAN_ON_PUSH", "CONTINUOUS_SCAN"]}, "EnableDelegatedAdminAccountRequest": {"type": "structure", "required": ["delegatedAdminAccountId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token for the request.</p>", "idempotencyToken": true}, "delegatedAdminAccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the Amazon Inspector delegated administrator.</p>"}}}, "EnableDelegatedAdminAccountResponse": {"type": "structure", "required": ["delegatedAdminAccountId"], "members": {"delegatedAdminAccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the successfully Amazon Inspector delegated administrator.</p>"}}}, "EnableRequest": {"type": "structure", "required": ["resourceTypes"], "members": {"accountIds": {"shape": "AccountIdSet", "documentation": "<p>A list of account IDs you want to enable Amazon Inspector scans for.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token for the request.</p>", "idempotencyToken": true}, "resourceTypes": {"shape": "EnableResourceTypeList", "documentation": "<p>The resource scan types you want to enable.</p>"}}}, "EnableResourceTypeList": {"type": "list", "member": {"shape": "ResourceScanType"}, "max": 3, "min": 1}, "EnableResponse": {"type": "structure", "required": ["accounts"], "members": {"accounts": {"shape": "AccountList", "documentation": "<p>Information on the accounts that have had Amazon Inspector scans successfully enabled. Details are provided for each account.</p>"}, "failedAccounts": {"shape": "FailedAccountList", "documentation": "<p>Information on any accounts for which Amazon Inspector scans could not be enabled. Details are provided for each account.</p>"}}}, "Epss": {"type": "structure", "members": {"score": {"shape": "EpssScore", "documentation": "<p>The Exploit Prediction Scoring System (EPSS) score.</p>"}}, "documentation": "<p>Details about the Exploit Prediction Scoring System (EPSS) score.</p>"}, "EpssDetails": {"type": "structure", "members": {"score": {"shape": "EpssScoreValue", "documentation": "<p>The EPSS score.</p>"}}, "documentation": "<p>Details about the Exploit Prediction Scoring System (EPSS) score for a finding.</p>"}, "EpssScore": {"type": "double"}, "EpssScoreValue": {"type": "double", "max": 1.0, "min": 0.0}, "ErrorCode": {"type": "string", "enum": ["ALREADY_ENABLED", "ENABLE_IN_PROGRESS", "DISABLE_IN_PROGRESS", "SUSPEND_IN_PROGRESS", "RESOURCE_NOT_FOUND", "ACCESS_DENIED", "INTERNAL_ERROR", "SSM_UNAVAILABLE", "SSM_THROTTLED", "EVENTBRIDGE_UNAVAILABLE", "EVENTBRIDGE_THROTTLED", "RESOURCE_SCAN_NOT_DISABLED", "DISASSOCIATE_ALL_MEMBERS", "ACCOUNT_IS_ISOLATED"]}, "ErrorMessage": {"type": "string"}, "Evidence": {"type": "structure", "members": {"evidenceDetail": {"shape": "EvidenceDetail", "documentation": "<p>The evidence details.</p>"}, "evidenceRule": {"shape": "EvidenceRule", "documentation": "<p>The evidence rule.</p>"}, "severity": {"shape": "EvidenceSeverity", "documentation": "<p>The evidence severity.</p>"}}, "documentation": "<p>Details of the evidence for a vulnerability identified in a finding.</p>"}, "EvidenceDetail": {"type": "string", "min": 0}, "EvidenceList": {"type": "list", "member": {"shape": "Evidence"}}, "EvidenceRule": {"type": "string", "min": 0}, "EvidenceSeverity": {"type": "string", "min": 0}, "ExecutionRoleArn": {"type": "string", "pattern": "^arn:(aws[a-zA-Z-]*)?:iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+$"}, "ExploitAvailable": {"type": "string", "enum": ["YES", "NO"]}, "ExploitObserved": {"type": "structure", "members": {"firstSeen": {"shape": "FirstSeen", "documentation": "<p>The date an time when the exploit was first seen.</p>"}, "lastSeen": {"shape": "LastSeen", "documentation": "<p>The date an time when the exploit was last seen.</p>"}}, "documentation": "<p>Contains information on when this exploit was observed.</p>"}, "ExploitabilityDetails": {"type": "structure", "members": {"lastKnownExploitAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date and time of the last exploit associated with a finding discovered in your environment.</p>"}}, "documentation": "<p>The details of an exploit available for a finding discovered in your environment.</p>"}, "ExternalReportStatus": {"type": "string", "enum": ["SUCCEEDED", "IN_PROGRESS", "CANCELLED", "FAILED"]}, "FailedAccount": {"type": "structure", "required": ["accountId", "errorCode", "errorMessage"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID.</p>"}, "errorCode": {"shape": "ErrorCode", "documentation": "<p>The error code explaining why the account failed to enable Amazon Inspector.</p>"}, "errorMessage": {"shape": "NonEmptyString", "documentation": "<p>The error message received when the account failed to enable Amazon Inspector.</p>"}, "resourceStatus": {"shape": "ResourceStatus", "documentation": "<p>An object detailing which resources Amazon Inspector is enabled to scan for the account.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of Amazon Inspector for the account.</p>"}}, "documentation": "<p>An object with details on why an account failed to enable Amazon Inspector.</p>"}, "FailedAccountList": {"type": "list", "member": {"shape": "FailedAccount"}, "max": 100, "min": 0}, "FailedMemberAccountEc2DeepInspectionStatusState": {"type": "structure", "required": ["accountId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The unique identifier for the Amazon Web Services account of the organization member that failed to activate Amazon Inspector deep inspection.</p>"}, "ec2ScanStatus": {"shape": "Status", "documentation": "<p>The status of EC2 scanning in the account that failed to activate Amazon Inspector deep inspection.</p>"}, "errorMessage": {"shape": "NonEmptyString", "documentation": "<p>The error message explaining why the account failed to activate Amazon Inspector deep inspection.</p>"}}, "documentation": "<p>An object that contains details about a member account in your organization that failed to activate Amazon Inspector deep inspection.</p>"}, "FailedMemberAccountEc2DeepInspectionStatusStateList": {"type": "list", "member": {"shape": "FailedMemberAccountEc2DeepInspectionStatusState"}, "max": 100, "min": 0}, "FilePath": {"type": "string", "max": 1024, "min": 1}, "Filter": {"type": "structure", "required": ["action", "arn", "createdAt", "criteria", "name", "ownerId", "updatedAt"], "members": {"action": {"shape": "FilterAction", "documentation": "<p>The action that is to be applied to the findings that match the filter.</p>"}, "arn": {"shape": "FilterArn", "documentation": "<p>The Amazon Resource Number (ARN) associated with this filter.</p>"}, "createdAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date and time this filter was created at.</p>"}, "criteria": {"shape": "FilterCriteria", "documentation": "<p>Details on the filter criteria associated with this filter.</p>"}, "description": {"shape": "FilterDescription", "documentation": "<p>A description of the filter.</p>"}, "name": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name of the filter.</p>"}, "ownerId": {"shape": "OwnerId", "documentation": "<p>The Amazon Web Services account ID of the account that created the filter.</p>"}, "reason": {"shape": "FilterReason", "documentation": "<p>The reason for the filter.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags attached to the filter.</p>"}, "updatedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date and time the filter was last updated at.</p>"}}, "documentation": "<p>Details about a filter.</p>"}, "FilterAction": {"type": "string", "enum": ["NONE", "SUPPRESS"]}, "FilterArn": {"type": "string", "max": 128, "min": 1}, "FilterArnList": {"type": "list", "member": {"shape": "FilterArn"}}, "FilterCriteria": {"type": "structure", "members": {"awsAccountId": {"shape": "StringFilterList", "documentation": "<p>Details of the Amazon Web Services account IDs used to filter findings.</p>"}, "codeVulnerabilityDetectorName": {"shape": "StringFilterList", "documentation": "<p>The name of the detector used to identify a code vulnerability in a Lambda function used to filter findings.</p>"}, "codeVulnerabilityDetectorTags": {"shape": "StringFilterList", "documentation": "<p>The detector type tag associated with the vulnerability used to filter findings. Detector tags group related vulnerabilities by common themes or tactics. For a list of available tags by programming language, see <a href=\"https://docs.aws.amazon.com/codeguru/detector-library/java/tags/\">Java tags</a>, or <a href=\"https://docs.aws.amazon.com/codeguru/detector-library/python/tags/\">Python tags</a>. </p>"}, "codeVulnerabilityFilePath": {"shape": "StringFilterList", "documentation": "<p>The file path to the file in a Lambda function that contains a code vulnerability used to filter findings.</p>"}, "componentId": {"shape": "StringFilterList", "documentation": "<p>Details of the component IDs used to filter findings.</p>"}, "componentType": {"shape": "StringFilterList", "documentation": "<p>Details of the component types used to filter findings.</p>"}, "ec2InstanceImageId": {"shape": "StringFilterList", "documentation": "<p>Details of the Amazon EC2 instance image IDs used to filter findings.</p>"}, "ec2InstanceSubnetId": {"shape": "StringFilterList", "documentation": "<p>Details of the Amazon EC2 instance subnet IDs used to filter findings.</p>"}, "ec2InstanceVpcId": {"shape": "StringFilterList", "documentation": "<p>Details of the Amazon EC2 instance VPC IDs used to filter findings.</p>"}, "ecrImageArchitecture": {"shape": "StringFilterList", "documentation": "<p>Details of the Amazon ECR image architecture types used to filter findings.</p>"}, "ecrImageHash": {"shape": "StringFilterList", "documentation": "<p>Details of the Amazon ECR image hashes used to filter findings.</p>"}, "ecrImagePushedAt": {"shape": "DateFilterList", "documentation": "<p>Details on the Amazon ECR image push date and time used to filter findings.</p>"}, "ecrImageRegistry": {"shape": "StringFilterList", "documentation": "<p>Details on the Amazon ECR registry used to filter findings.</p>"}, "ecrImageRepositoryName": {"shape": "StringFilterList", "documentation": "<p>Details on the name of the Amazon ECR repository used to filter findings.</p>"}, "ecrImageTags": {"shape": "StringFilterList", "documentation": "<p>The tags attached to the Amazon ECR container image.</p>"}, "epssScore": {"shape": "NumberFilterList", "documentation": "<p>The EPSS score used to filter findings.</p>"}, "exploitAvailable": {"shape": "StringFilterList", "documentation": "<p>Filters the list of AWS Lambda findings by the availability of exploits.</p>"}, "findingArn": {"shape": "StringFilterList", "documentation": "<p>Details on the finding ARNs used to filter findings.</p>"}, "findingStatus": {"shape": "StringFilterList", "documentation": "<p>Details on the finding status types used to filter findings.</p>"}, "findingType": {"shape": "StringFilterList", "documentation": "<p>Details on the finding types used to filter findings.</p>"}, "firstObservedAt": {"shape": "DateFilterList", "documentation": "<p>Details on the date and time a finding was first seen used to filter findings.</p>"}, "fixAvailable": {"shape": "StringFilterList", "documentation": "<p>Details on whether a fix is available through a version update. This value can be <code>YES</code>, <code>NO</code>, or <code>PARTIAL</code>. A <code>PARTIAL</code> fix means that some, but not all, of the packages identified in the finding have fixes available through updated versions.</p>"}, "inspectorScore": {"shape": "NumberFilterList", "documentation": "<p>The Amazon Inspector score to filter on.</p>"}, "lambdaFunctionExecutionRoleArn": {"shape": "StringFilterList", "documentation": "<p>Filters the list of AWS Lambda functions by execution role.</p>"}, "lambdaFunctionLastModifiedAt": {"shape": "DateFilterList", "documentation": "<p>Filters the list of AWS Lambda functions by the date and time that a user last updated the configuration, in <a href=\"https://www.iso.org/iso-8601-date-and-time-format.html\">ISO 8601 format</a> </p>"}, "lambdaFunctionLayers": {"shape": "StringFilterList", "documentation": "<p>Filters the list of AWS Lambda functions by the function's <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-layers.html\"> layers</a>. A Lambda function can have up to five layers.</p>"}, "lambdaFunctionName": {"shape": "StringFilterList", "documentation": "<p>Filters the list of AWS Lambda functions by the name of the function.</p>"}, "lambdaFunctionRuntime": {"shape": "StringFilterList", "documentation": "<p>Filters the list of AWS Lambda functions by the runtime environment for the Lambda function.</p>"}, "lastObservedAt": {"shape": "DateFilterList", "documentation": "<p>Details on the date and time a finding was last seen used to filter findings.</p>"}, "networkProtocol": {"shape": "StringFilterList", "documentation": "<p>Details on network protocol used to filter findings.</p>"}, "portRange": {"shape": "PortRangeFilterList", "documentation": "<p>Details on the port ranges used to filter findings.</p>"}, "relatedVulnerabilities": {"shape": "StringFilterList", "documentation": "<p>Details on the related vulnerabilities used to filter findings.</p>"}, "resourceId": {"shape": "StringFilterList", "documentation": "<p>Details on the resource IDs used to filter findings.</p>"}, "resourceTags": {"shape": "MapFilterList", "documentation": "<p>Details on the resource tags used to filter findings.</p>"}, "resourceType": {"shape": "StringFilterList", "documentation": "<p>Details on the resource types used to filter findings.</p>"}, "severity": {"shape": "StringFilterList", "documentation": "<p>Details on the severity used to filter findings.</p>"}, "title": {"shape": "StringFilterList", "documentation": "<p>Details on the finding title used to filter findings.</p>"}, "updatedAt": {"shape": "DateFilterList", "documentation": "<p>Details on the date and time a finding was last updated at used to filter findings.</p>"}, "vendorSeverity": {"shape": "StringFilterList", "documentation": "<p>Details on the vendor severity used to filter findings.</p>"}, "vulnerabilityId": {"shape": "StringFilterList", "documentation": "<p>Details on the vulnerability ID used to filter findings.</p>"}, "vulnerabilitySource": {"shape": "StringFilterList", "documentation": "<p>Details on the vulnerability type used to filter findings.</p>"}, "vulnerablePackages": {"shape": "PackageFilterList", "documentation": "<p>Details on the vulnerable packages used to filter findings.</p>"}}, "documentation": "<p>Details on the criteria used to define the filter.</p>"}, "FilterDescription": {"type": "string", "max": 512, "min": 1}, "FilterList": {"type": "list", "member": {"shape": "Filter"}}, "FilterName": {"type": "string", "max": 128, "min": 1}, "FilterReason": {"type": "string", "max": 512, "min": 1}, "Finding": {"type": "structure", "required": ["awsAccountId", "description", "findingArn", "firstObservedAt", "lastObservedAt", "remediation", "resources", "severity", "status", "type"], "members": {"awsAccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID associated with the finding.</p>"}, "codeVulnerabilityDetails": {"shape": "CodeVulnerabilityDetails", "documentation": "<p>Details about the code vulnerability identified in a Lambda function used to filter findings.</p>"}, "description": {"shape": "FindingDescription", "documentation": "<p>The description of the finding.</p>"}, "epss": {"shape": "EpssDetails", "documentation": "<p>The finding's EPSS score.</p>"}, "exploitAvailable": {"shape": "ExploitAvailable", "documentation": "<p>If a finding discovered in your environment has an exploit available.</p>"}, "exploitabilityDetails": {"shape": "ExploitabilityDetails", "documentation": "<p>The details of an exploit available for a finding discovered in your environment.</p>"}, "findingArn": {"shape": "FindingArn", "documentation": "<p>The Amazon Resource Number (ARN) of the finding.</p>"}, "firstObservedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date and time that the finding was first observed.</p>"}, "fixAvailable": {"shape": "FixAvailable", "documentation": "<p>Details on whether a fix is available through a version update. This value can be <code>YES</code>, <code>NO</code>, or <code>PARTIAL</code>. A <code>PARTIAL</code> fix means that some, but not all, of the packages identified in the finding have fixes available through updated versions.</p>"}, "inspectorScore": {"shape": "Double", "documentation": "<p>The Amazon Inspector score given to the finding.</p>"}, "inspectorScoreDetails": {"shape": "InspectorScoreDetails", "documentation": "<p>An object that contains details of the Amazon Inspector score.</p>"}, "lastObservedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date and time that the finding was last observed.</p>"}, "networkReachabilityDetails": {"shape": "NetworkReachabilityDetails", "documentation": "<p>An object that contains the details of a network reachability finding.</p>"}, "packageVulnerabilityDetails": {"shape": "PackageVulnerabilityDetails", "documentation": "<p>An object that contains the details of a package vulnerability finding.</p>"}, "remediation": {"shape": "Remediation", "documentation": "<p>An object that contains the details about how to remediate a finding.</p>"}, "resources": {"shape": "ResourceList", "documentation": "<p>Contains information on the resources involved in a finding. The <code>resource</code> value determines the valid values for <code>type</code> in your request. For more information, see <a href=\"https://docs.aws.amazon.com/inspector/latest/user/findings-types.html\">Finding types</a> in the Amazon Inspector user guide.</p>"}, "severity": {"shape": "Severity", "documentation": "<p>The severity of the finding. <code>UNTRIAGED</code> applies to <code>PACKAGE_VULNERABILITY</code> type findings that the vendor has not assigned a severity yet. For more information, see <a href=\"https://docs.aws.amazon.com/inspector/latest/user/findings-understanding-severity.html\">Severity levels for findings</a> in the Amazon Inspector user guide.</p>"}, "status": {"shape": "FindingStatus", "documentation": "<p>The status of the finding.</p>"}, "title": {"shape": "Finding<PERSON>itle", "documentation": "<p>The title of the finding.</p>"}, "type": {"shape": "FindingType", "documentation": "<p>The type of the finding. The <code>type</code> value determines the valid values for <code>resource</code> in your request. For more information, see <a href=\"https://docs.aws.amazon.com/inspector/latest/user/findings-types.html\">Finding types</a> in the Amazon Inspector user guide.</p>"}, "updatedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date and time the finding was last updated at.</p>"}}, "documentation": "<p>Details about an Amazon Inspector finding.</p>"}, "FindingArn": {"type": "string", "max": 100, "min": 1, "pattern": "^arn:(aws[a-zA-Z-]*)?:inspector2:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:finding/[a-f0-9]{32}$"}, "FindingArnList": {"type": "list", "member": {"shape": "FindingArn"}, "max": 10, "min": 1}, "FindingDescription": {"type": "string", "max": 1024, "min": 1}, "FindingDetail": {"type": "structure", "members": {"cisaData": {"shape": "CisaData"}, "cwes": {"shape": "Cwes", "documentation": "<p>The Common Weakness Enumerations (CWEs) associated with the vulnerability.</p>"}, "epssScore": {"shape": "Double", "documentation": "<p>The Exploit Prediction Scoring System (EPSS) score of the vulnerability.</p>"}, "evidences": {"shape": "EvidenceList", "documentation": "<p>Information on the evidence of the vulnerability.</p>"}, "exploitObserved": {"shape": "ExploitObserved"}, "findingArn": {"shape": "FindingArn", "documentation": "<p>The finding ARN that the vulnerability details are associated with.</p>"}, "referenceUrls": {"shape": "VulnerabilityReferenceUrls", "documentation": "<p>The reference URLs for the vulnerability data.</p>"}, "riskScore": {"shape": "RiskScore", "documentation": "<p>The risk score of the vulnerability.</p>"}, "tools": {"shape": "Tools", "documentation": "<p>The known malware tools or kits that can exploit the vulnerability.</p>"}, "ttps": {"shape": "Ttps", "documentation": "<p>The MITRE adversary tactics, techniques, or procedures (TTPs) associated with the vulnerability.</p>"}}, "documentation": "<p>Details of the vulnerability identified in a finding.</p>"}, "FindingDetails": {"type": "list", "member": {"shape": "FindingDetail"}, "min": 0}, "FindingDetailsError": {"type": "structure", "required": ["errorCode", "errorMessage", "findingArn"], "members": {"errorCode": {"shape": "FindingDetailsErrorCode", "documentation": "<p>The error code.</p>"}, "errorMessage": {"shape": "NonEmptyString", "documentation": "<p>The error message.</p>"}, "findingArn": {"shape": "FindingArn", "documentation": "<p>The finding ARN that returned an error.</p>"}}, "documentation": "<p>Details about an error encountered when trying to return vulnerability data for a finding.</p>"}, "FindingDetailsErrorCode": {"type": "string", "enum": ["INTERNAL_ERROR", "ACCESS_DENIED", "FINDING_DETAILS_NOT_FOUND", "INVALID_INPUT"]}, "FindingDetailsErrorList": {"type": "list", "member": {"shape": "FindingDetailsError"}}, "FindingList": {"type": "list", "member": {"shape": "Finding"}, "max": 25, "min": 0}, "FindingStatus": {"type": "string", "enum": ["ACTIVE", "SUPPRESSED", "CLOSED"]}, "FindingTitle": {"type": "string", "max": 1024, "min": 1}, "FindingType": {"type": "string", "enum": ["NETWORK_REACHABILITY", "PACKAGE_VULNERABILITY", "CODE_VULNERABILITY"]}, "FindingTypeAggregation": {"type": "structure", "members": {"findingType": {"shape": "AggregationFindingType", "documentation": "<p>The finding type to aggregate.</p>"}, "resourceType": {"shape": "AggregationResourceType", "documentation": "<p>The resource type to aggregate.</p>"}, "sortBy": {"shape": "FindingTypeSortBy", "documentation": "<p>The value to sort results by.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The order to sort results by.</p>"}}, "documentation": "<p>The details that define an aggregation based on finding type.</p>"}, "FindingTypeAggregationResponse": {"type": "structure", "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The ID of the Amazon Web Services account associated with the findings.</p>"}, "severityCounts": {"shape": "SeverityCounts", "documentation": "<p>The value to sort results by.</p>"}}, "documentation": "<p>A response that contains the results of a finding type aggregation.</p>"}, "FindingTypeSortBy": {"type": "string", "enum": ["CRITICAL", "HIGH", "ALL"]}, "FirstSeen": {"type": "timestamp"}, "FixAvailable": {"type": "string", "enum": ["YES", "NO", "PARTIAL"]}, "FreeTrialAccountInfo": {"type": "structure", "required": ["accountId", "freeTrialInfo"], "members": {"accountId": {"shape": "MeteringAccountId", "documentation": "<p>The account associated with the Amazon Inspector free trial information.</p>"}, "freeTrialInfo": {"shape": "FreeTrialInfoList", "documentation": "<p>Contains information about the Amazon Inspector free trial for an account.</p>"}}, "documentation": "<p>Information about the Amazon Inspector free trial for an account.</p>"}, "FreeTrialAccountInfoList": {"type": "list", "member": {"shape": "FreeTrialAccountInfo"}}, "FreeTrialInfo": {"type": "structure", "required": ["end", "start", "status", "type"], "members": {"end": {"shape": "Timestamp", "documentation": "<p>The date and time that the Amazon Inspector free trail ends for a given account.</p>"}, "start": {"shape": "Timestamp", "documentation": "<p>The date and time that the Amazon Inspector free trail started for a given account.</p>"}, "status": {"shape": "FreeTrialStatus", "documentation": "<p>The order to sort results by.</p>"}, "type": {"shape": "FreeTrialType", "documentation": "<p>The type of scan covered by the Amazon Inspector free trail.</p>"}}, "documentation": "<p>An object that contains information about the Amazon Inspector free trial for an account.</p>"}, "FreeTrialInfoError": {"type": "structure", "required": ["accountId", "code", "message"], "members": {"accountId": {"shape": "MeteringAccountId", "documentation": "<p>The account associated with the Amazon Inspector free trial information.</p>"}, "code": {"shape": "FreeTrialInfoErrorCode", "documentation": "<p>The error code.</p>"}, "message": {"shape": "String", "documentation": "<p>The error message returned.</p>"}}, "documentation": "<p>Information about an error received while accessing free trail data for an account.</p>"}, "FreeTrialInfoErrorCode": {"type": "string", "enum": ["ACCESS_DENIED", "INTERNAL_ERROR"]}, "FreeTrialInfoErrorList": {"type": "list", "member": {"shape": "FreeTrialInfoError"}}, "FreeTrialInfoList": {"type": "list", "member": {"shape": "FreeTrialInfo"}}, "FreeTrialStatus": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}, "FreeTrialType": {"type": "string", "enum": ["EC2", "ECR", "LAMBDA", "LAMBDA_CODE"]}, "FunctionName": {"type": "string", "pattern": "^[a-zA-Z0-9-_\\.]+(:(\\$LATEST|[a-zA-Z0-9-_]+))?$"}, "GetConfigurationRequest": {"type": "structure", "members": {}}, "GetConfigurationResponse": {"type": "structure", "members": {"ecrConfiguration": {"shape": "EcrConfigurationState", "documentation": "<p>Specifies how the ECR automated re-scan duration is currently configured for your environment.</p>"}}}, "GetDelegatedAdminAccountRequest": {"type": "structure", "members": {}}, "GetDelegatedAdminAccountResponse": {"type": "structure", "members": {"delegatedAdmin": {"shape": "DelegatedAdmin", "documentation": "<p>The Amazon Web Services account ID of the Amazon Inspector delegated administrator.</p>"}}}, "GetEc2DeepInspectionConfigurationRequest": {"type": "structure", "members": {}}, "GetEc2DeepInspectionConfigurationResponse": {"type": "structure", "members": {"errorMessage": {"shape": "NonEmptyString", "documentation": "<p>An error message explaining why Amazon Inspector deep inspection configurations could not be retrieved for your account.</p>"}, "orgPackagePaths": {"shape": "PathList", "documentation": "<p>The Amazon Inspector deep inspection custom paths for your organization.</p>"}, "packagePaths": {"shape": "PathList", "documentation": "<p>The Amazon Inspector deep inspection custom paths for your account.</p>"}, "status": {"shape": "Ec2DeepInspectionStatus", "documentation": "<p>The activation status of Amazon Inspector deep inspection in your account.</p>"}}}, "GetEncryptionKeyRequest": {"type": "structure", "required": ["resourceType", "scanType"], "members": {"resourceType": {"shape": "ResourceType", "documentation": "<p>The resource type the key encrypts.</p>", "location": "querystring", "locationName": "resourceType"}, "scanType": {"shape": "ScanType", "documentation": "<p>The scan type the key encrypts.</p>", "location": "querystring", "locationName": "scanType"}}}, "GetEncryptionKeyResponse": {"type": "structure", "required": ["kmsKeyId"], "members": {"kmsKeyId": {"shape": "KmsKeyArn", "documentation": "<p>A kms key ID.</p>"}}}, "GetFindingsReportStatusRequest": {"type": "structure", "members": {"reportId": {"shape": "ReportId", "documentation": "<p>The ID of the report to retrieve the status of.</p>"}}}, "GetFindingsReportStatusResponse": {"type": "structure", "members": {"destination": {"shape": "Destination", "documentation": "<p>The destination of the report.</p>"}, "errorCode": {"shape": "ReportingErrorCode", "documentation": "<p>The error code of the report.</p>"}, "errorMessage": {"shape": "ErrorMessage", "documentation": "<p>The error message of the report.</p>"}, "filterCriteria": {"shape": "FilterCriteria", "documentation": "<p>The filter criteria associated with the report.</p>"}, "reportId": {"shape": "ReportId", "documentation": "<p>The ID of the report.</p>"}, "status": {"shape": "ExternalReportStatus", "documentation": "<p>The status of the report.</p>"}}}, "GetMemberRequest": {"type": "structure", "required": ["accountId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the member account to retrieve information on.</p>"}}}, "GetMemberResponse": {"type": "structure", "members": {"member": {"shape": "Member", "documentation": "<p>Details of the retrieved member account.</p>"}}}, "GetSbomExportRequest": {"type": "structure", "required": ["reportId"], "members": {"reportId": {"shape": "ReportId", "documentation": "<p>The report ID of the SBOM export to get details for.</p>"}}}, "GetSbomExportResponse": {"type": "structure", "members": {"errorCode": {"shape": "ReportingErrorCode", "documentation": "<p>An error code.</p>"}, "errorMessage": {"shape": "NonEmptyString", "documentation": "<p>An error message.</p>"}, "filterCriteria": {"shape": "ResourceFilterCriteria", "documentation": "<p>Contains details about the resource filter criteria used for the software bill of materials (SBOM) report.</p>"}, "format": {"shape": "SbomReportFormat", "documentation": "<p>The format of the software bill of materials (SBOM) report.</p>"}, "reportId": {"shape": "ReportId", "documentation": "<p>The report ID of the software bill of materials (SBOM) report.</p>"}, "s3Destination": {"shape": "Destination"}, "status": {"shape": "ExternalReportStatus", "documentation": "<p>The status of the software bill of materials (SBOM) report.</p>"}}}, "GroupKey": {"type": "string", "enum": ["SCAN_STATUS_CODE", "SCAN_STATUS_REASON", "ACCOUNT_ID", "RESOURCE_TYPE", "ECR_REPOSITORY_NAME"]}, "ImageHash": {"type": "string", "max": 71, "min": 71, "pattern": "^sha256:[a-z0-9]{64}$"}, "ImageLayerAggregation": {"type": "structure", "members": {"layerHashes": {"shape": "StringFilterList", "documentation": "<p>The hashes associated with the layers.</p>"}, "repositories": {"shape": "StringFilterList", "documentation": "<p>The repository associated with the container image hosting the layers.</p>"}, "resourceIds": {"shape": "StringFilterList", "documentation": "<p>The ID of the container image layer.</p>"}, "sortBy": {"shape": "ImageLayerSortBy", "documentation": "<p>The value to sort results by.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The order to sort results by.</p>"}}, "documentation": "<p>The details that define an aggregation based on container image layers.</p>"}, "ImageLayerAggregationResponse": {"type": "structure", "required": ["accountId", "layerHash", "repository", "resourceId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The ID of the Amazon Web Services account that owns the container image hosting the layer image.</p>"}, "layerHash": {"shape": "NonEmptyString", "documentation": "<p>The layer hash.</p>"}, "repository": {"shape": "NonEmptyString", "documentation": "<p>The repository the layer resides in.</p>"}, "resourceId": {"shape": "NonEmptyString", "documentation": "<p>The resource ID of the container image layer.</p>"}, "severityCounts": {"shape": "SeverityCounts", "documentation": "<p>An object that represents the count of matched findings per severity.</p>"}}, "documentation": "<p>A response that contains the results of a finding aggregation by image layer.</p>"}, "ImageLayerSortBy": {"type": "string", "enum": ["CRITICAL", "HIGH", "ALL"]}, "ImageTagList": {"type": "list", "member": {"shape": "NonEmptyString"}}, "InspectorScoreDetails": {"type": "structure", "members": {"adjustedCvss": {"shape": "CvssScoreDetails", "documentation": "<p>An object that contains details about the CVSS score given to a finding.</p>"}}, "documentation": "<p>Information about the Amazon Inspector score given to a finding.</p>"}, "Integer": {"type": "integer", "box": true}, "InternalServerException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "retryAfterSeconds": {"shape": "Integer", "documentation": "<p>The number of seconds to wait before retrying the request.</p>", "location": "header", "locationName": "Retry-After"}}, "documentation": "<p>The request has failed due to an internal failure of the Amazon Inspector service.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "IpV4Address": {"type": "string", "max": 15, "min": 7, "pattern": "^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$"}, "IpV4AddressList": {"type": "list", "member": {"shape": "IpV4Address"}}, "IpV6Address": {"type": "string", "max": 47, "min": 1}, "IpV6AddressList": {"type": "list", "member": {"shape": "IpV6Address"}}, "KmsKeyArn": {"type": "string", "pattern": "^arn:aws(-(us-gov|cn))?:kms:([a-z0-9][-.a-z0-9]{0,62})?:[0-9]{12}?:key/(([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})|(mrk-[0-9a-zA-Z]{32}))$"}, "LambdaFunctionAggregation": {"type": "structure", "members": {"functionNames": {"shape": "StringFilterList", "documentation": "<p>The AWS Lambda function names to include in the aggregation results.</p>"}, "functionTags": {"shape": "MapFilterList", "documentation": "<p>The tags to include in the aggregation results.</p>"}, "resourceIds": {"shape": "StringFilterList", "documentation": "<p>The resource IDs to include in the aggregation results.</p>"}, "runtimes": {"shape": "StringFilterList", "documentation": "<p>Returns findings aggregated by AWS Lambda function runtime environments.</p>"}, "sortBy": {"shape": "LambdaFunctionSortBy", "documentation": "<p>The finding severity to use for sorting the results.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The order to use for sorting the results.</p>"}}, "documentation": "<p>The details that define a findings aggregation based on AWS Lambda functions.</p>"}, "LambdaFunctionAggregationResponse": {"type": "structure", "required": ["resourceId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The ID of the AWS account that owns the AWS Lambda function. </p>"}, "functionName": {"shape": "String", "documentation": "<p>The AWS Lambda function names included in the aggregation results.</p>"}, "lambdaTags": {"shape": "TagMap", "documentation": "<p>The tags included in the aggregation results.</p>"}, "lastModifiedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date that the AWS Lambda function included in the aggregation results was last changed.</p>"}, "resourceId": {"shape": "NonEmptyString", "documentation": "<p>The resource IDs included in the aggregation results.</p>"}, "runtime": {"shape": "String", "documentation": "<p>The runtimes included in the aggregation results.</p>"}, "severityCounts": {"shape": "SeverityCounts"}}, "documentation": "<p>A response that contains the results of an AWS Lambda function finding aggregation.</p>"}, "LambdaFunctionMetadata": {"type": "structure", "members": {"functionName": {"shape": "String", "documentation": "<p>The name of a function.</p>"}, "functionTags": {"shape": "TagMap", "documentation": "<p>The resource tags on an AWS Lambda function.</p>"}, "layers": {"shape": "LambdaLayerList", "documentation": "<p>The layers for an AWS Lambda function. A Lambda function can have up to five layers.</p>"}, "runtime": {"shape": "Runtime", "documentation": "<p>An AWS Lambda function's runtime.</p>"}}, "documentation": "<p>The AWS Lambda function metadata.</p>"}, "LambdaFunctionSortBy": {"type": "string", "enum": ["CRITICAL", "HIGH", "ALL"]}, "LambdaLayerAggregation": {"type": "structure", "members": {"functionNames": {"shape": "StringFilterList", "documentation": "<p>The names of the AWS Lambda functions associated with the layers.</p>"}, "layerArns": {"shape": "StringFilterList", "documentation": "<p>The Amazon Resource Name (ARN) of the AWS Lambda function layer. </p>"}, "resourceIds": {"shape": "StringFilterList", "documentation": "<p>The resource IDs for the AWS Lambda function layers.</p>"}, "sortBy": {"shape": "LambdaLayerSortBy", "documentation": "<p>The finding severity to use for sorting the results.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The order to use for sorting the results.</p>"}}, "documentation": "<p>The details that define a findings aggregation based on an AWS Lambda function's layers.</p>"}, "LambdaLayerAggregationResponse": {"type": "structure", "required": ["accountId", "functionName", "layerArn", "resourceId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The account ID of the AWS Lambda function layer.</p>"}, "functionName": {"shape": "NonEmptyString", "documentation": "<p>The names of the AWS Lambda functions associated with the layers.</p>"}, "layerArn": {"shape": "NonEmptyString", "documentation": "<p>The Amazon Resource Name (ARN) of the AWS Lambda function layer.</p>"}, "resourceId": {"shape": "NonEmptyString", "documentation": "<p>The Resource ID of the AWS Lambda function layer.</p>"}, "severityCounts": {"shape": "SeverityCounts"}}, "documentation": "<p>A response that contains the results of an AWS Lambda function layer finding aggregation.</p>"}, "LambdaLayerArn": {"type": "string", "pattern": "^arn:[a-zA-Z0-9-]+:lambda:[a-zA-Z0-9-]+:\\d{12}:layer:[a-zA-Z0-9-_]+:[0-9]+$"}, "LambdaLayerList": {"type": "list", "member": {"shape": "String"}, "max": 5, "min": 0}, "LambdaLayerSortBy": {"type": "string", "enum": ["CRITICAL", "HIGH", "ALL"]}, "LambdaVpcConfig": {"type": "structure", "members": {"securityGroupIds": {"shape": "SecurityGroupIdList", "documentation": "<p>The VPC security groups and subnets that are attached to an AWS Lambda function. For more information, see <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-vpc.html\">VPC Settings</a>.</p>"}, "subnetIds": {"shape": "SubnetIdList", "documentation": "<p>A list of VPC subnet IDs.</p>"}, "vpcId": {"shape": "VpcId", "documentation": "<p>The ID of the VPC.</p>"}}, "documentation": "<p>The VPC security groups and subnets that are attached to an AWS Lambda function. For more information, see <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-vpc.html\">VPC Settings</a>.</p>"}, "LastSeen": {"type": "timestamp"}, "LayerList": {"type": "list", "member": {"shape": "LambdaLayerArn"}, "max": 5, "min": 1}, "ListAccountPermissionsMaxResults": {"type": "integer", "box": true, "max": 1024, "min": 1}, "ListAccountPermissionsRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListAccountPermissionsMaxResults", "documentation": "<p>The maximum number of results the response can return. If your request would return more than the maximum the response will return a <code>nextToken</code> value, use this value when you call the action again to get the remaining results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. If your response returns more than the <code>maxResults</code> maximum value it will also return a <code>nextToken</code> value. For subsequent calls, use the NextToken value returned from the previous request to continue listing results after the first page.</p>"}, "service": {"shape": "Service", "documentation": "<p>The service scan type to check permissions for.</p>"}}}, "ListAccountPermissionsResponse": {"type": "structure", "required": ["permissions"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the <code>NextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}, "permissions": {"shape": "Permissions", "documentation": "<p>Contains details on the permissions an account has to configure Amazon Inspector.</p>"}}}, "ListCoverageMaxResults": {"type": "integer", "box": true, "max": 200, "min": 1}, "ListCoverageRequest": {"type": "structure", "members": {"filterCriteria": {"shape": "CoverageFilterCriteria", "documentation": "<p>An object that contains details on the filters to apply to the coverage data for your environment.</p>"}, "maxResults": {"shape": "ListCoverageMaxResults", "documentation": "<p>The maximum number of results the response can return. If your request would return more than the maximum the response will return a <code>nextToken</code> value, use this value when you call the action again to get the remaining results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. If your response returns more than the <code>maxResults</code> maximum value it will also return a <code>nextToken</code> value. For subsequent calls, use the <code>nextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}}}, "ListCoverageResponse": {"type": "structure", "members": {"coveredResources": {"shape": "CoveredResources", "documentation": "<p>An object that contains details on the covered resources in your environment.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the <code>NextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}}}, "ListCoverageStatisticsRequest": {"type": "structure", "members": {"filterCriteria": {"shape": "CoverageFilterCriteria", "documentation": "<p>An object that contains details on the filters to apply to the coverage data for your environment.</p>"}, "groupBy": {"shape": "GroupKey", "documentation": "<p>The value to group the results by.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the <code>NextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}}}, "ListCoverageStatisticsResponse": {"type": "structure", "required": ["totalCounts"], "members": {"countsByGroup": {"shape": "CountsList", "documentation": "<p>An array with the number for each group.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the <code>NextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}, "totalCounts": {"shape": "<PERSON>", "documentation": "<p>The total number for all groups.</p>"}}}, "ListDelegatedAdminAccountsRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListDelegatedAdminMaxResults", "documentation": "<p>The maximum number of results the response can return. If your request would return more than the maximum the response will return a <code>nextToken</code> value, use this value when you call the action again to get the remaining results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. If your response returns more than the <code>maxResults</code> maximum value it will also return a <code>nextToken</code> value. For subsequent calls, use the <code>nextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}}}, "ListDelegatedAdminAccountsResponse": {"type": "structure", "members": {"delegatedAdminAccounts": {"shape": "DelegatedAdminAccountList", "documentation": "<p>Details of the Amazon Inspector delegated administrator of your organization.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the <code>NextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}}}, "ListDelegatedAdminMaxResults": {"type": "integer", "box": true, "max": 5, "min": 1}, "ListFilterMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListFiltersRequest": {"type": "structure", "members": {"action": {"shape": "FilterAction", "documentation": "<p>The action the filter applies to matched findings.</p>"}, "arns": {"shape": "FilterArnList", "documentation": "<p>The Amazon resource number (ARN) of the filter.</p>"}, "maxResults": {"shape": "ListFilterMaxResults", "documentation": "<p>The maximum number of results the response can return. If your request would return more than the maximum the response will return a <code>nextToken</code> value, use this value when you call the action again to get the remaining results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. If your response returns more than the <code>maxResults</code> maximum value it will also return a <code>nextToken</code> value. For subsequent calls, use the <code>nextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}}}, "ListFiltersResponse": {"type": "structure", "required": ["filters"], "members": {"filters": {"shape": "FilterList", "documentation": "<p>Contains details on the filters associated with your account.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the <code>NextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}}}, "ListFindingAggregationsMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListFindingAggregationsRequest": {"type": "structure", "required": ["aggregationType"], "members": {"accountIds": {"shape": "StringFilterList", "documentation": "<p>The Amazon Web Services account IDs to retrieve finding aggregation data for.</p>"}, "aggregationRequest": {"shape": "AggregationRequest", "documentation": "<p>Details of the aggregation request that is used to filter your aggregation results.</p>"}, "aggregationType": {"shape": "AggregationType", "documentation": "<p>The type of the aggregation request.</p>"}, "maxResults": {"shape": "ListFindingAggregationsMaxResults", "documentation": "<p>The maximum number of results the response can return. If your request would return more than the maximum the response will return a <code>nextToken</code> value, use this value when you call the action again to get the remaining results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. If your response returns more than the <code>maxResults</code> maximum value it will also return a <code>nextToken</code> value. For subsequent calls, use the <code>nextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}}}, "ListFindingAggregationsResponse": {"type": "structure", "required": ["aggregationType"], "members": {"aggregationType": {"shape": "AggregationType", "documentation": "<p>The type of aggregation to perform.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the <code>NextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}, "responses": {"shape": "AggregationResponseList", "documentation": "<p>Objects that contain the results of an aggregation operation.</p>"}}}, "ListFindingsMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListFindingsRequest": {"type": "structure", "members": {"filterCriteria": {"shape": "FilterCriteria", "documentation": "<p>Details on the filters to apply to your finding results.</p>"}, "maxResults": {"shape": "ListFindingsMaxResults", "documentation": "<p>The maximum number of results the response can return. If your request would return more than the maximum the response will return a <code>nextToken</code> value, use this value when you call the action again to get the remaining results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. If your response returns more than the <code>maxResults</code> maximum value it will also return a <code>nextToken</code> value. For subsequent calls, use the <code>nextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}, "sortCriteria": {"shape": "SortCriteria", "documentation": "<p>Details on the sort criteria to apply to your finding results.</p>"}}}, "ListFindingsResponse": {"type": "structure", "members": {"findings": {"shape": "FindingList", "documentation": "<p>Contains details on the findings in your environment.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the <code>NextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}}}, "ListMembersMaxResults": {"type": "integer", "box": true, "max": 50, "min": 1}, "ListMembersRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListMembersMaxResults", "documentation": "<p>The maximum number of results the response can return. If your request would return more than the maximum the response will return a <code>nextToken</code> value, use this value when you call the action again to get the remaining results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. If your response returns more than the <code>maxResults</code> maximum value it will also return a <code>nextToken</code> value. For subsequent calls, use the <code>nextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}, "onlyAssociated": {"shape": "Boolean", "documentation": "<p>Specifies whether to list only currently associated members if <code>True</code> or to list all members within the organization if <code>False</code>.</p>"}}}, "ListMembersResponse": {"type": "structure", "members": {"members": {"shape": "MemberList", "documentation": "<p>An object that contains details for each member account.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The pagination parameter to be used on the next list operation to retrieve more items.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon resource number (ARN) of the resource to list tags of.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagMap", "documentation": "<p>The tags associated with the resource.</p>"}}}, "ListUsageTotalsMaxResults": {"type": "integer", "box": true, "max": 500, "min": 1}, "ListUsageTotalsNextToken": {"type": "string", "min": 1}, "ListUsageTotalsRequest": {"type": "structure", "members": {"accountIds": {"shape": "UsageAccountIdList", "documentation": "<p>The Amazon Web Services account IDs to retrieve usage totals for.</p>"}, "maxResults": {"shape": "ListUsageTotalsMaxResults", "documentation": "<p>The maximum number of results the response can return. If your request would return more than the maximum the response will return a <code>nextToken</code> value, use this value when you call the action again to get the remaining results.</p>"}, "nextToken": {"shape": "ListUsageTotalsNextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. If your response returns more than the <code>maxResults</code> maximum value it will also return a <code>nextToken</code> value. For subsequent calls, use the <code>nextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}}}, "ListUsageTotalsResponse": {"type": "structure", "members": {"nextToken": {"shape": "ListUsageTotalsNextToken", "documentation": "<p>The pagination parameter to be used on the next list operation to retrieve more items.</p>"}, "totals": {"shape": "UsageTotalList", "documentation": "<p>An object with details on the total usage for the requested account.</p>"}}}, "Long": {"type": "long", "box": true}, "MapComparison": {"type": "string", "enum": ["EQUALS"]}, "MapFilter": {"type": "structure", "required": ["comparison", "key"], "members": {"comparison": {"shape": "MapComparison", "documentation": "<p>The operator to use when comparing values in the filter.</p>"}, "key": {"shape": "MapKey", "documentation": "<p>The tag key used in the filter.</p>"}, "value": {"shape": "MapValue", "documentation": "<p>The tag value used in the filter.</p>"}}, "documentation": "<p>An object that describes details of a map filter.</p>"}, "MapFilterList": {"type": "list", "member": {"shape": "MapFilter"}, "max": 10, "min": 1}, "MapKey": {"type": "string", "max": 128, "min": 1}, "MapValue": {"type": "string", "max": 256, "min": 0}, "Member": {"type": "structure", "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the member account.</p>"}, "delegatedAdminAccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the Amazon Inspector delegated administrator for this member account.</p>"}, "relationshipStatus": {"shape": "RelationshipStatus", "documentation": "<p>The status of the member account.</p>"}, "updatedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>A timestamp showing when the status of this member was last updated.</p>"}}, "documentation": "<p>Details on a member account in your organization.</p>"}, "MemberAccountEc2DeepInspectionStatus": {"type": "structure", "required": ["accountId", "activateDeepInspection"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The unique identifier for the Amazon Web Services account of the organization member.</p>"}, "activateDeepInspection": {"shape": "Boolean", "documentation": "<p>Whether Amazon Inspector deep inspection is active in the account. If <code>TRUE</code> Amazon Inspector deep inspection is active, if <code>FALSE</code> it is not active.</p>"}}, "documentation": "<p>An object that contains details about the status of Amazon Inspector deep inspection for a member account in your organization.</p>"}, "MemberAccountEc2DeepInspectionStatusList": {"type": "list", "member": {"shape": "MemberAccountEc2DeepInspectionStatus"}, "max": 100, "min": 0}, "MemberAccountEc2DeepInspectionStatusState": {"type": "structure", "required": ["accountId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The unique identifier for the Amazon Web Services account of the organization member</p>"}, "errorMessage": {"shape": "NonEmptyString", "documentation": "<p>The error message explaining why the account failed to activate Amazon Inspector deep inspection.</p>"}, "status": {"shape": "Ec2DeepInspectionStatus", "documentation": "<p>The state of Amazon Inspector deep inspection in the member account.</p>"}}, "documentation": "<p>An object that contains details about the state of Amazon Inspector deep inspection for a member account.</p>"}, "MemberAccountEc2DeepInspectionStatusStateList": {"type": "list", "member": {"shape": "MemberAccountEc2DeepInspectionStatusState"}, "max": 100, "min": 0}, "MemberList": {"type": "list", "member": {"shape": "Member"}, "max": 50, "min": 0}, "MeteringAccountId": {"type": "string", "pattern": "[0-9]{12}"}, "MonthlyCostEstimate": {"type": "double", "min": 0}, "NetworkPath": {"type": "structure", "members": {"steps": {"shape": "StepList", "documentation": "<p>The details on the steps in the network path.</p>"}}, "documentation": "<p>Information on the network path associated with a finding.</p>"}, "NetworkProtocol": {"type": "string", "enum": ["TCP", "UDP"]}, "NetworkReachabilityDetails": {"type": "structure", "required": ["networkPath", "openPortRange", "protocol"], "members": {"networkPath": {"shape": "NetworkPath", "documentation": "<p>An object that contains details about a network path associated with a finding.</p>"}, "openPortRange": {"shape": "PortRange", "documentation": "<p>An object that contains details about the open port range associated with a finding.</p>"}, "protocol": {"shape": "NetworkProtocol", "documentation": "<p>The protocol associated with a finding.</p>"}}, "documentation": "<p>Contains the details of a network reachability finding.</p>"}, "NextToken": {"type": "string", "max": 1000000, "min": 0}, "NonEmptyString": {"type": "string", "min": 1}, "NonEmptyStringList": {"type": "list", "member": {"shape": "NonEmptyString"}}, "NumberFilter": {"type": "structure", "members": {"lowerInclusive": {"shape": "Double", "documentation": "<p>The lowest number to be included in the filter.</p>"}, "upperInclusive": {"shape": "Double", "documentation": "<p>The highest number to be included in the filter.</p>"}}, "documentation": "<p>An object that describes the details of a number filter.</p>"}, "NumberFilterList": {"type": "list", "member": {"shape": "NumberFilter"}, "max": 10, "min": 1}, "Operation": {"type": "string", "enum": ["ENABLE_SCANNING", "DISABLE_SCANNING", "ENABLE_REPOSITORY", "DISABLE_REPOSITORY"]}, "OwnerId": {"type": "string", "max": 34, "min": 12, "pattern": "(^\\d{12}$)|(^o-[a-z0-9]{10,32}$)"}, "PackageAggregation": {"type": "structure", "members": {"packageNames": {"shape": "StringFilterList", "documentation": "<p>The names of packages to aggregate findings on.</p>"}, "sortBy": {"shape": "PackageSortBy", "documentation": "<p>The value to sort results by.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The order to sort results by.</p>"}}, "documentation": "<p>The details that define an aggregation based on operating system package type.</p>"}, "PackageAggregationResponse": {"type": "structure", "required": ["packageName"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The ID of the Amazon Web Services account associated with the findings.</p>"}, "packageName": {"shape": "NonEmptyString", "documentation": "<p>The name of the operating system package.</p>"}, "severityCounts": {"shape": "SeverityCounts", "documentation": "<p>An object that contains the count of matched findings per severity.</p>"}}, "documentation": "<p>A response that contains the results of a finding aggregation by image layer.</p>"}, "PackageArchitecture": {"type": "string", "max": 64, "min": 1}, "PackageEpoch": {"type": "integer"}, "PackageFilter": {"type": "structure", "members": {"architecture": {"shape": "StringFilter", "documentation": "<p>An object that contains details on the package architecture type to filter on.</p>"}, "epoch": {"shape": "NumberFilter", "documentation": "<p>An object that contains details on the package epoch to filter on.</p>"}, "name": {"shape": "StringFilter", "documentation": "<p>An object that contains details on the name of the package to filter on.</p>"}, "release": {"shape": "StringFilter", "documentation": "<p>An object that contains details on the package release to filter on.</p>"}, "sourceLambdaLayerArn": {"shape": "StringFilter"}, "sourceLayerHash": {"shape": "StringFilter", "documentation": "<p>An object that contains details on the source layer hash to filter on.</p>"}, "version": {"shape": "StringFilter", "documentation": "<p>The package version to filter on.</p>"}}, "documentation": "<p>Contains information on the details of a package filter.</p>"}, "PackageFilterList": {"type": "list", "member": {"shape": "PackageFilter"}, "max": 10, "min": 1}, "PackageManager": {"type": "string", "enum": ["BUNDLER", "CARGO", "COMPOSER", "NPM", "NUGET", "PIPENV", "POETRY", "YARN", "GOBINARY", "GOMOD", "JAR", "OS", "PIP", "PYTHONPKG", "NODEPKG", "POM", "GEMSPEC"]}, "PackageName": {"type": "string", "max": 1024, "min": 1}, "PackageRelease": {"type": "string", "max": 1024, "min": 1}, "PackageSortBy": {"type": "string", "enum": ["CRITICAL", "HIGH", "ALL"]}, "PackageType": {"type": "string", "enum": ["IMAGE", "ZIP"]}, "PackageVersion": {"type": "string", "max": 1024, "min": 1}, "PackageVulnerabilityDetails": {"type": "structure", "required": ["source", "vulnerabilityId"], "members": {"cvss": {"shape": "CvssScoreList", "documentation": "<p>An object that contains details about the CVSS score of a finding.</p>"}, "referenceUrls": {"shape": "NonEmptyStringList", "documentation": "<p>One or more URLs that contain details about this vulnerability type.</p>"}, "relatedVulnerabilities": {"shape": "VulnerabilityIdList", "documentation": "<p>One or more vulnerabilities related to the one identified in this finding.</p>"}, "source": {"shape": "NonEmptyString", "documentation": "<p>The source of the vulnerability information.</p>"}, "sourceUrl": {"shape": "NonEmptyString", "documentation": "<p>A URL to the source of the vulnerability information.</p>"}, "vendorCreatedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date and time that this vulnerability was first added to the vendor's database.</p>"}, "vendorSeverity": {"shape": "NonEmptyString", "documentation": "<p>The severity the vendor has given to this vulnerability type.</p>"}, "vendorUpdatedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date and time the vendor last updated this vulnerability in their database.</p>"}, "vulnerabilityId": {"shape": "VulnerabilityId", "documentation": "<p>The ID given to this vulnerability.</p>"}, "vulnerablePackages": {"shape": "VulnerablePackageList", "documentation": "<p>The packages impacted by this vulnerability.</p>"}}, "documentation": "<p>Information about a package vulnerability finding.</p>"}, "Path": {"type": "string", "max": 512, "min": 1, "pattern": "^(?:/(?:\\.[-\\w]+|[-\\w]+(?:\\.[-\\w]+)?))+/?$"}, "PathList": {"type": "list", "member": {"shape": "Path"}, "max": 5, "min": 0}, "Permission": {"type": "structure", "required": ["operation", "service"], "members": {"operation": {"shape": "Operation", "documentation": "<p>The operations that can be performed with the given permissions.</p>"}, "service": {"shape": "Service", "documentation": "<p>The services that the permissions allow an account to perform the given operations for.</p>"}}, "documentation": "<p>Contains information on the permissions an account has within Amazon Inspector.</p>"}, "Permissions": {"type": "list", "member": {"shape": "Permission"}, "max": 1024, "min": 0}, "Platform": {"type": "string", "max": 1024, "min": 1}, "Port": {"type": "integer", "box": true, "max": 65535, "min": 0}, "PortRange": {"type": "structure", "required": ["begin", "end"], "members": {"begin": {"shape": "Port", "documentation": "<p>The beginning port in a port range.</p>"}, "end": {"shape": "Port", "documentation": "<p>The ending port in a port range.</p>"}}, "documentation": "<p>Details about the port range associated with a finding.</p>"}, "PortRangeFilter": {"type": "structure", "members": {"beginInclusive": {"shape": "Port", "documentation": "<p>The port number the port range begins at.</p>"}, "endInclusive": {"shape": "Port", "documentation": "<p>The port number the port range ends at.</p>"}}, "documentation": "<p>An object that describes the details of a port range filter.</p>"}, "PortRangeFilterList": {"type": "list", "member": {"shape": "PortRangeFilter"}, "max": 10, "min": 1}, "Recommendation": {"type": "structure", "members": {"Url": {"shape": "NonEmptyString", "documentation": "<p>The URL address to the CVE remediation recommendations.</p>"}, "text": {"shape": "NonEmptyString", "documentation": "<p>The recommended course of action to remediate the finding.</p>"}}, "documentation": "<p>Details about the recommended course of action to remediate the finding.</p>"}, "ReferenceUrls": {"type": "list", "member": {"shape": "NonEmptyString"}, "max": 10, "min": 1}, "RelatedVulnerabilities": {"type": "list", "member": {"shape": "RelatedVulnerability"}, "max": 100, "min": 0}, "RelatedVulnerability": {"type": "string", "min": 0}, "RelationshipStatus": {"type": "string", "enum": ["CREATED", "INVITED", "DISABLED", "ENABLED", "REMOVED", "RESIGNED", "DELETED", "EMAIL_VERIFICATION_IN_PROGRESS", "EMAIL_VERIFICATION_FAILED", "REGION_DISABLED", "ACCOUNT_SUSPENDED", "CANNOT_CREATE_DETECTOR_IN_ORG_MASTER"]}, "Remediation": {"type": "structure", "members": {"recommendation": {"shape": "Recommendation", "documentation": "<p>An object that contains information about the recommended course of action to remediate the finding.</p>"}}, "documentation": "<p>Information on how to remediate a finding.</p>"}, "ReportFormat": {"type": "string", "enum": ["CSV", "JSON"]}, "ReportId": {"type": "string", "pattern": "\\b[a-f0-9]{8}\\b-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-\\b[a-f0-9]{12}\\b"}, "ReportingErrorCode": {"type": "string", "enum": ["INTERNAL_ERROR", "INVALID_PERMISSIONS", "NO_FINDINGS_FOUND", "BUCKET_NOT_FOUND", "INCOMPATIBLE_BUCKET_REGION", "MALFORMED_KMS_KEY"]}, "RepositoryAggregation": {"type": "structure", "members": {"repositories": {"shape": "StringFilterList", "documentation": "<p>The names of repositories to aggregate findings on.</p>"}, "sortBy": {"shape": "RepositorySortBy", "documentation": "<p>The value to sort results by.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The order to sort results by.</p>"}}, "documentation": "<p>The details that define an aggregation based on repository.</p>"}, "RepositoryAggregationResponse": {"type": "structure", "required": ["repository"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The ID of the Amazon Web Services account associated with the findings.</p>"}, "affectedImages": {"shape": "<PERSON>", "documentation": "<p>The number of container images impacted by the findings.</p>"}, "repository": {"shape": "NonEmptyString", "documentation": "<p>The name of the repository associated with the findings.</p>"}, "severityCounts": {"shape": "SeverityCounts", "documentation": "<p>An object that represent the count of matched findings per severity.</p>"}}, "documentation": "<p>A response that contains details on the results of a finding aggregation by repository.</p>"}, "RepositorySortBy": {"type": "string", "enum": ["CRITICAL", "HIGH", "ALL", "AFFECTED_IMAGES"]}, "ResetEncryptionKeyRequest": {"type": "structure", "required": ["resourceType", "scanType"], "members": {"resourceType": {"shape": "ResourceType", "documentation": "<p>The resource type the key encrypts.</p>"}, "scanType": {"shape": "ScanType", "documentation": "<p>The scan type the key encrypts.</p>"}}}, "ResetEncryptionKeyResponse": {"type": "structure", "members": {}}, "Resource": {"type": "structure", "required": ["id", "type"], "members": {"details": {"shape": "ResourceDetails", "documentation": "<p>An object that contains details about the resource involved in a finding.</p>"}, "id": {"shape": "NonEmptyString", "documentation": "<p>The ID of the resource.</p>"}, "partition": {"shape": "NonEmptyString", "documentation": "<p>The partition of the resource.</p>"}, "region": {"shape": "NonEmptyString", "documentation": "<p>The Amazon Web Services Region the impacted resource is located in.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags attached to the resource.</p>"}, "type": {"shape": "ResourceType", "documentation": "<p>The type of resource.</p>"}}, "documentation": "<p>Details about the resource involved in a finding.</p>"}, "ResourceDetails": {"type": "structure", "members": {"awsEc2Instance": {"shape": "AwsEc2InstanceDetails", "documentation": "<p>An object that contains details about the Amazon EC2 instance involved in the finding.</p>"}, "awsEcrContainerImage": {"shape": "AwsEcrContainerImageDetails", "documentation": "<p>An object that contains details about the Amazon ECR container image involved in the finding.</p>"}, "awsLambdaFunction": {"shape": "AwsLambdaFunctionDetails", "documentation": "<p>A summary of the information about an AWS Lambda function affected by a finding.</p>"}}, "documentation": "<p>Contains details about the resource involved in the finding.</p>"}, "ResourceFilterCriteria": {"type": "structure", "members": {"accountId": {"shape": "ResourceStringFilterList", "documentation": "<p>The account IDs used as resource filter criteria.</p>"}, "ec2InstanceTags": {"shape": "ResourceMapFilterList", "documentation": "<p>The EC2 instance tags used as resource filter criteria.</p>"}, "ecrImageTags": {"shape": "ResourceStringFilterList", "documentation": "<p>The ECR image tags used as resource filter criteria.</p>"}, "ecrRepositoryName": {"shape": "ResourceStringFilterList", "documentation": "<p>The ECR repository names used as resource filter criteria.</p>"}, "lambdaFunctionName": {"shape": "ResourceStringFilterList", "documentation": "<p>The AWS Lambda function name used as resource filter criteria.</p>"}, "lambdaFunctionTags": {"shape": "ResourceMapFilterList", "documentation": "<p>The AWS Lambda function tags used as resource filter criteria.</p>"}, "resourceId": {"shape": "ResourceStringFilterList", "documentation": "<p>The resource IDs used as resource filter criteria.</p>"}, "resourceType": {"shape": "ResourceStringFilterList", "documentation": "<p>The resource types used as resource filter criteria.</p>"}}, "documentation": "<p>The resource filter criteria for a Software bill of materials (SBOM) report.</p>"}, "ResourceId": {"type": "string", "max": 341, "min": 10, "pattern": "(^arn:.*:ecr:.*:\\d{12}:repository\\/(?:[a-z0-9]+(?:[._-][a-z0-9]+)*\\/)*[a-z0-9]+(?:[._-][a-z0-9]+)*(\\/sha256:[a-z0-9]{64})?$)|(^i-([a-z0-9]{8}|[a-z0-9]{17}|\\\\*)$|(^arn:(aws[a-zA-Z-]*)?:lambda:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:function:[a-zA-Z0-9-_\\.]+(:(\\$LATEST|[a-zA-Z0-9-_]+))?$))"}, "ResourceList": {"type": "list", "member": {"shape": "Resource"}, "max": 10, "min": 1}, "ResourceMapComparison": {"type": "string", "enum": ["EQUALS"]}, "ResourceMapFilter": {"type": "structure", "required": ["comparison", "key"], "members": {"comparison": {"shape": "ResourceMapComparison", "documentation": "<p>The filter's comparison.</p>"}, "key": {"shape": "NonEmptyString", "documentation": "<p>The filter's key.</p>"}, "value": {"shape": "NonEmptyString", "documentation": "<p>The filter's value.</p>"}}, "documentation": "<p>A resource map filter for a software bill of material report.</p>"}, "ResourceMapFilterList": {"type": "list", "member": {"shape": "ResourceMapFilter"}, "max": 10, "min": 1}, "ResourceNotFoundException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation tried to access an invalid resource. Make sure the resource is specified correctly.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "ResourceScanMetadata": {"type": "structure", "members": {"ec2": {"shape": "Ec2Metadata", "documentation": "<p>An object that contains metadata details for an Amazon EC2 instance.</p>"}, "ecrImage": {"shape": "EcrContainerImageMetadata", "documentation": "<p>An object that contains details about the container metadata for an Amazon ECR image.</p>"}, "ecrRepository": {"shape": "EcrRepositoryMetadata", "documentation": "<p>An object that contains details about the repository an Amazon ECR image resides in.</p>"}, "lambdaFunction": {"shape": "LambdaFunctionMetadata", "documentation": "<p>An object that contains metadata details for an AWS Lambda function.</p>"}}, "documentation": "<p>An object that contains details about the metadata for an Amazon ECR resource.</p>"}, "ResourceScanType": {"type": "string", "enum": ["EC2", "ECR", "LAMBDA", "LAMBDA_CODE"]}, "ResourceState": {"type": "structure", "required": ["ec2", "ecr"], "members": {"ec2": {"shape": "State", "documentation": "<p>An object detailing the state of Amazon Inspector scanning for Amazon EC2 resources.</p>"}, "ecr": {"shape": "State", "documentation": "<p>An object detailing the state of Amazon Inspector scanning for Amazon ECR resources.</p>"}, "lambda": {"shape": "State"}, "lambdaCode": {"shape": "State"}}, "documentation": "<p>Details the state of Amazon Inspector for each resource type Amazon Inspector scans.</p>"}, "ResourceStatus": {"type": "structure", "required": ["ec2", "ecr"], "members": {"ec2": {"shape": "Status", "documentation": "<p>The status of Amazon Inspector scanning for Amazon EC2 resources.</p>"}, "ecr": {"shape": "Status", "documentation": "<p>The status of Amazon Inspector scanning for Amazon ECR resources.</p>"}, "lambda": {"shape": "Status", "documentation": "<p>The status of Amazon Inspector scanning for AWS Lambda function.</p>"}, "lambdaCode": {"shape": "Status", "documentation": "<p>The status of Amazon Inspector scanning for custom application code for Amazon Web Services Lambda functions. </p>"}}, "documentation": "<p>Details the status of Amazon Inspector for each resource type Amazon Inspector scans.</p>"}, "ResourceStringComparison": {"type": "string", "enum": ["EQUALS", "NOT_EQUALS"]}, "ResourceStringFilter": {"type": "structure", "required": ["comparison", "value"], "members": {"comparison": {"shape": "ResourceStringComparison", "documentation": "<p>The filter's comparison.</p>"}, "value": {"shape": "ResourceStringInput", "documentation": "<p>The filter's value.</p>"}}, "documentation": "<p>A resource string filter for a software bill of materials report.</p>"}, "ResourceStringFilterList": {"type": "list", "member": {"shape": "ResourceStringFilter"}, "max": 10, "min": 1}, "ResourceStringInput": {"type": "string", "max": 1024, "min": 1}, "ResourceType": {"type": "string", "enum": ["AWS_EC2_INSTANCE", "AWS_ECR_CONTAINER_IMAGE", "AWS_ECR_REPOSITORY", "AWS_LAMBDA_FUNCTION"]}, "RiskScore": {"type": "integer", "box": true}, "Runtime": {"type": "string", "enum": ["NODEJS", "NODEJS_12_X", "NODEJS_14_X", "NODEJS_16_X", "JAVA_8", "JAVA_8_AL2", "JAVA_11", "PYTHON_3_7", "PYTHON_3_8", "PYTHON_3_9", "UNSUPPORTED", "NODEJS_18_X", "GO_1_X", "JAVA_17", "PYTHON_3_10"]}, "SbomReportFormat": {"type": "string", "enum": ["CYCLONEDX_1_4", "SPDX_2_3"]}, "ScanStatus": {"type": "structure", "required": ["reason", "statusCode"], "members": {"reason": {"shape": "ScanStatusReason", "documentation": "<p>The scan status. Possible return values and descriptions are: </p> <p> <code>PENDING_INITIAL_SCAN</code> - This resource has been identified for scanning, results will be available soon.</p> <p> <code>ACCESS_DENIED</code> - Resource access policy restricting Amazon Inspector access. Please update the IAM policy.</p> <p> <code>INTERNAL_ERROR</code> - Amazon Inspector has encountered an internal error for this resource. Amazon Inspector service will automatically resolve the issue and resume the scanning. No action required from the user.</p> <p> <code>UNMANAGED_EC2_INSTANCE</code> - The EC2 instance is not managed by SSM, please use the following SSM automation to remediate the issue: <a href=\"https://docs.aws.amazon.com/systems-manager-automation-runbooks/latest/userguide/automation-awssupport-troubleshoot-managed-instance.html\">https://docs.aws.amazon.com/systems-manager-automation-runbooks/latest/userguide/automation-awssupport-troubleshoot-managed-instance.html</a>. Once the instance becomes managed by SSM, Inspector will automatically begin scanning this instance. </p> <p> <code>UNSUPPORTED_OS</code> - Amazon Inspector does not support this OS, architecture, or image manifest type at this time. To see a complete list of supported operating systems see: <a href=\" https://docs.aws.amazon.com/inspector/latest/user/supported.html\">https://docs.aws.amazon.com/inspector/latest/user/supported.html</a>.</p> <p> <code>SCAN_ELIGIBILITY_EXPIRED</code> - The configured scan duration has lapsed for this image.</p> <p> <code>RESOURCE_TERMINATED</code> - This resource has been terminated. The findings and coverage associated with this resource are in the process of being cleaned up.</p> <p> <code>SUCCESSFUL</code> - The scan was successful.</p> <p> <code>NO_RESOURCES_FOUND</code> - Reserved for future use.</p> <p> <code>IMAGE_SIZE_EXCEEDED</code> - Reserved for future use.</p> <p> <code>SCAN_FREQUENCY_MANUAL</code> - This image will not be covered by Amazon Inspector due to the repository scan frequency configuration.</p> <p> <code>SCAN_FREQUENCY_SCAN_ON_PUSH </code>- This image will be scanned one time and will not new findings because of the scan frequency configuration.</p> <p> <code>EC2_INSTANCE_STOPPED</code> - This EC2 instance is in a stopped state, therefore, Amazon Inspector will pause scanning. The existing findings will continue to exist until the instance is terminated. Once the instance is re-started, Inspector will automatically start scanning the instance again. Please note that you will not be charged for this instance while it’s in a stopped state.</p> <p> <code>PENDING_DISABLE</code> - This resource is pending cleanup during disablement. The customer will not be billed while a resource is in the pending disable status.</p> <p> <code>NO INVENTORY</code> - Amazon Inspector couldn’t find software application inventory to scan for vulnerabilities. This might be caused due to required Amazon Inspector associations being deleted or failing to run on your resource. Please verify the status of <code>InspectorInventoryCollection-do-not-delete</code> association in the SSM console for the resource. Additionally, you can verify the instance’s inventory in the SSM Fleet Manager console.</p> <p> <code>STALE_INVENTORY</code> - Amazon Inspector wasn’t able to collect an updated software application inventory in the last 7 days. Please confirm the required Amazon Inspector associations still exist and you can still see an updated inventory in the SSM console.</p> <p> <code>EXCLUDED_BY_TAG</code> - This resource was not scanned because it has been excluded by a tag.</p> <p> <code>UNSUPPORTED_RUNTIME</code> - The function was not scanned because it has an unsupported runtime. To see a complete list of supported runtimes see: <a href=\" https://docs.aws.amazon.com/inspector/latest/user/supported.html\">https://docs.aws.amazon.com/inspector/latest/user/supported.html</a>.</p> <p> <code>UNSUPPORTED_MEDIA_TYPE </code>- The ECR image has an unsupported media type.</p> <p> <code>UNSUPPORTED_CONFIG_FILE</code> - Reserved for future use.</p> <p> <code>DEEP_INSPECTION_PACKAGE_COLLECTION_LIMIT_EXCEEDED</code> - The instance has exceeded the 5000 package limit for Amazon Inspector Deep inspection. To resume Deep inspection for this instance you can try to adjust the custom paths associated with the account.</p> <p> <code>DEEP_INSPECTION_DAILY_SSM_INVENTORY_LIMIT_EXCEEDED</code> - The SSM agent couldn't send inventory to Amazon Inspector because the SSM quota for Inventory data collected per instance per day has already been reached for this instance.</p> <p> <code>DEEP_INSPECTION_COLLECTION_TIME_LIMIT_EXCEEDED</code> - Amazon Inspector failed to extract the package inventory because the package collection time exceeding the maximum threshold of 15 minutes.</p> <p> <code>DEEP_INSPECTION_NO_INVENTORY</code> The Amazon Inspector plugin hasn't yet been able to collect an inventory of packages for this instance. This is usually the result of a pending scan, however, if this status persists after 6 hours, use SSM to ensure that the required Amazon Inspector associations exist and are running for the instance.</p> <p/>"}, "statusCode": {"shape": "ScanStatusCode", "documentation": "<p>The status code of the scan.</p>"}}, "documentation": "<p>The status of the scan.</p>"}, "ScanStatusCode": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}, "ScanStatusReason": {"type": "string", "enum": ["PENDING_INITIAL_SCAN", "ACCESS_DENIED", "INTERNAL_ERROR", "UNMANAGED_EC2_INSTANCE", "UNSUPPORTED_OS", "SCAN_ELIGIBILITY_EXPIRED", "RESOURCE_TERMINATED", "SUCCESSFUL", "NO_RESOURCES_FOUND", "IMAGE_SIZE_EXCEEDED", "SCAN_FREQUENCY_MANUAL", "SCAN_FREQUENCY_SCAN_ON_PUSH", "EC2_INSTANCE_STOPPED", "PENDING_DISABLE", "NO_INVENTORY", "STALE_INVENTORY", "EXCLUDED_BY_TAG", "UNSUPPORTED_RUNTIME", "UNSUPPORTED_MEDIA_TYPE", "UNSUPPORTED_CONFIG_FILE", "DEEP_INSPECTION_PACKAGE_COLLECTION_LIMIT_EXCEEDED", "DEEP_INSPECTION_DAILY_SSM_INVENTORY_LIMIT_EXCEEDED", "DEEP_INSPECTION_COLLECTION_TIME_LIMIT_EXCEEDED", "DEEP_INSPECTION_NO_INVENTORY"]}, "ScanType": {"type": "string", "enum": ["NETWORK", "PACKAGE", "CODE"]}, "SearchVulnerabilitiesFilterCriteria": {"type": "structure", "required": ["vulnerabilityIds"], "members": {"vulnerabilityIds": {"shape": "VulnIdList", "documentation": "<p>The IDs for specific vulnerabilities.</p>"}}, "documentation": "<p>Details on the criteria used to define the filter for a vulnerability search. </p>"}, "SearchVulnerabilitiesRequest": {"type": "structure", "required": ["filterCriteria"], "members": {"filterCriteria": {"shape": "SearchVulnerabilitiesFilterCriteria", "documentation": "<p>The criteria used to filter the results of a vulnerability search.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the <code>NextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}}}, "SearchVulnerabilitiesResponse": {"type": "structure", "required": ["vulnerabilities"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The pagination parameter to be used on the next list operation to retrieve more items.</p>"}, "vulnerabilities": {"shape": "Vulnerabilities", "documentation": "<p>Details about the listed vulnerability.</p>"}}}, "SecurityGroupId": {"type": "string", "pattern": "^sg-([a-z0-9]{8}|[a-z0-9]{17}|\\*)$"}, "SecurityGroupIdList": {"type": "list", "member": {"shape": "SecurityGroupId"}, "max": 5, "min": 0}, "Service": {"type": "string", "enum": ["EC2", "ECR", "LAMBDA"]}, "ServiceQuotaExceededException": {"type": "structure", "required": ["message", "resourceId"], "members": {"message": {"shape": "String"}, "resourceId": {"shape": "String", "documentation": "<p>The ID of the resource that exceeds a service quota.</p>"}}, "documentation": "<p>You have exceeded your service quota. To perform the requested action, remove some of the relevant resources, or use Service Quotas to request a service quota increase.</p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "Severity": {"type": "string", "enum": ["INFORMATIONAL", "LOW", "MEDIUM", "HIGH", "CRITICAL", "UNTRIAGED"]}, "SeverityCounts": {"type": "structure", "members": {"all": {"shape": "<PERSON>", "documentation": "<p>The total count of findings from all severities.</p>"}, "critical": {"shape": "<PERSON>", "documentation": "<p>The total count of critical severity findings.</p>"}, "high": {"shape": "<PERSON>", "documentation": "<p>The total count of high severity findings.</p>"}, "medium": {"shape": "<PERSON>", "documentation": "<p>The total count of medium severity findings.</p>"}}, "documentation": "<p>An object that contains the counts of aggregated finding per severity.</p>"}, "SortCriteria": {"type": "structure", "required": ["field", "sortOrder"], "members": {"field": {"shape": "SortField", "documentation": "<p>The finding detail field by which results are sorted.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The order by which findings are sorted.</p>"}}, "documentation": "<p>Details about the criteria used to sort finding results.</p>"}, "SortField": {"type": "string", "enum": ["AWS_ACCOUNT_ID", "FINDING_TYPE", "SEVERITY", "FIRST_OBSERVED_AT", "LAST_OBSERVED_AT", "FINDING_STATUS", "RESOURCE_TYPE", "ECR_IMAGE_PUSHED_AT", "ECR_IMAGE_REPOSITORY_NAME", "ECR_IMAGE_REGISTRY", "NETWORK_PROTOCOL", "COMPONENT_TYPE", "VULNERABILITY_ID", "VULNERABILITY_SOURCE", "INSPECTOR_SCORE", "VENDOR_SEVERITY", "EPSS_SCORE"]}, "SortOrder": {"type": "string", "enum": ["ASC", "DESC"]}, "SourceLayerHash": {"type": "string", "max": 71, "min": 71, "pattern": "^sha256:[a-z0-9]{64}$"}, "State": {"type": "structure", "required": ["errorCode", "errorMessage", "status"], "members": {"errorCode": {"shape": "ErrorCode", "documentation": "<p>The error code explaining why the account failed to enable Amazon Inspector.</p>"}, "errorMessage": {"shape": "NonEmptyString", "documentation": "<p>The error message received when the account failed to enable Amazon Inspector.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of Amazon Inspector for the account.</p>"}}, "documentation": "<p>An object that described the state of Amazon Inspector scans for an account.</p>"}, "Status": {"type": "string", "enum": ["ENABLING", "ENABLED", "DISABLING", "DISABLED", "SUSPENDING", "SUSPENDED"]}, "Step": {"type": "structure", "required": ["componentId", "componentType"], "members": {"componentId": {"shape": "Component", "documentation": "<p>The component ID.</p>"}, "componentType": {"shape": "ComponentType", "documentation": "<p>The component type.</p>"}}, "documentation": "<p>Details about the step associated with a finding.</p>"}, "StepList": {"type": "list", "member": {"shape": "Step"}, "max": 30, "min": 1}, "String": {"type": "string"}, "StringComparison": {"type": "string", "enum": ["EQUALS", "PREFIX", "NOT_EQUALS"]}, "StringFilter": {"type": "structure", "required": ["comparison", "value"], "members": {"comparison": {"shape": "StringComparison", "documentation": "<p>The operator to use when comparing values in the filter.</p>"}, "value": {"shape": "StringInput", "documentation": "<p>The value to filter on.</p>"}}, "documentation": "<p>An object that describes the details of a string filter.</p>"}, "StringFilterList": {"type": "list", "member": {"shape": "StringFilter"}, "max": 10, "min": 1}, "StringInput": {"type": "string", "max": 1024, "min": 1}, "StringList": {"type": "list", "member": {"shape": "NonEmptyString"}}, "SubnetId": {"type": "string", "pattern": "^subnet-([a-z0-9]{8}|[a-z0-9]{17}|\\*)$"}, "SubnetIdList": {"type": "list", "member": {"shape": "SubnetId"}, "max": 16, "min": 0}, "SuggestedFix": {"type": "structure", "members": {"code": {"shape": "SuggestedFixCodeString", "documentation": "<p>The fix's code.</p>"}, "description": {"shape": "SuggestedFixDescriptionString", "documentation": "<p>The fix's description.</p>"}}, "documentation": "<p>A suggested fix for a vulnerability in your Lambda function code.</p>"}, "SuggestedFixCodeString": {"type": "string", "max": 2500, "min": 1}, "SuggestedFixDescriptionString": {"type": "string", "max": 1000, "min": 1}, "SuggestedFixes": {"type": "list", "member": {"shape": "SuggestedFix"}, "max": 5, "min": 1}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^(?!aws:)[a-zA-Z+-=._:/]+$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 0}, "TagList": {"type": "list", "member": {"shape": "String"}}, "TagMap": {"type": "map", "key": {"shape": "MapKey"}, "value": {"shape": "MapValue"}}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the resource to apply a tag to.</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags to be added to a resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "Target": {"type": "string", "max": 50, "min": 0}, "Targets": {"type": "list", "member": {"shape": "Target"}, "min": 0}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "retryAfterSeconds": {"shape": "Integer", "documentation": "<p>The number of seconds to wait before retrying the request.</p>", "location": "header", "locationName": "Retry-After"}}, "documentation": "<p>The limit on the number of requests per second was exceeded.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true, "retryable": {"throttling": true}}, "Timestamp": {"type": "timestamp"}, "TitleAggregation": {"type": "structure", "members": {"findingType": {"shape": "AggregationFindingType", "documentation": "<p>The type of finding to aggregate on.</p>"}, "resourceType": {"shape": "AggregationResourceType", "documentation": "<p>The resource type to aggregate on.</p>"}, "sortBy": {"shape": "TitleSortBy", "documentation": "<p>The value to sort results by.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The order to sort results by.</p>"}, "titles": {"shape": "StringFilterList", "documentation": "<p>The finding titles to aggregate on.</p>"}, "vulnerabilityIds": {"shape": "StringFilterList", "documentation": "<p>The vulnerability IDs of the findings.</p>"}}, "documentation": "<p>The details that define an aggregation based on finding title.</p>"}, "TitleAggregationResponse": {"type": "structure", "required": ["title"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The ID of the Amazon Web Services account associated with the findings.</p>"}, "severityCounts": {"shape": "SeverityCounts", "documentation": "<p>An object that represent the count of matched findings per severity.</p>"}, "title": {"shape": "NonEmptyString", "documentation": "<p>The title that the findings were aggregated on.</p>"}, "vulnerabilityId": {"shape": "String", "documentation": "<p>The vulnerability ID of the finding.</p>"}}, "documentation": "<p>A response that contains details on the results of a finding aggregation by title.</p>"}, "TitleSortBy": {"type": "string", "enum": ["CRITICAL", "HIGH", "ALL"]}, "Tool": {"type": "string", "min": 0}, "Tools": {"type": "list", "member": {"shape": "Tool"}}, "Ttp": {"type": "string", "max": 30, "min": 0}, "Ttps": {"type": "list", "member": {"shape": "Ttp"}, "min": 0}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) for the resource to remove tags from.</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>The tag keys to remove from the resource.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateConfigurationRequest": {"type": "structure", "required": ["ecrConfiguration"], "members": {"ecrConfiguration": {"shape": "EcrConfiguration", "documentation": "<p>Specifies how the ECR automated re-scan will be updated for your environment.</p>"}}}, "UpdateConfigurationResponse": {"type": "structure", "members": {}}, "UpdateEc2DeepInspectionConfigurationRequest": {"type": "structure", "members": {"activateDeepInspection": {"shape": "Boolean", "documentation": "<p>Specify <code>TRUE</code> to activate Amazon Inspector deep inspection in your account, or <code>FALSE</code> to deactivate. Member accounts in an organization cannot deactivate deep inspection, instead the delegated administrator for the organization can deactivate a member account using <a href=\"https://docs.aws.amazon.com/inspector/v2/APIReference/API_BatchUpdateMemberEc2DeepInspectionStatus.html\">BatchUpdateMemberEc2DeepInspectionStatus</a>.</p>"}, "packagePaths": {"shape": "PathList", "documentation": "<p>The Amazon Inspector deep inspection custom paths you are adding for your account.</p>"}}}, "UpdateEc2DeepInspectionConfigurationResponse": {"type": "structure", "members": {"errorMessage": {"shape": "NonEmptyString", "documentation": "<p>An error message explaining why new Amazon Inspector deep inspection custom paths could not be added.</p>"}, "orgPackagePaths": {"shape": "PathList", "documentation": "<p>The current Amazon Inspector deep inspection custom paths for the organization.</p>"}, "packagePaths": {"shape": "PathList", "documentation": "<p>The current Amazon Inspector deep inspection custom paths for your account.</p>"}, "status": {"shape": "Ec2DeepInspectionStatus", "documentation": "<p>The status of Amazon Inspector deep inspection in your account.</p>"}}}, "UpdateEncryptionKeyRequest": {"type": "structure", "required": ["kmsKeyId", "resourceType", "scanType"], "members": {"kmsKeyId": {"shape": "KmsKeyArn", "documentation": "<p>A KMS key ID for the encryption key.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The resource type for the encryption key.</p>"}, "scanType": {"shape": "ScanType", "documentation": "<p>The scan type for the encryption key.</p>"}}}, "UpdateEncryptionKeyResponse": {"type": "structure", "members": {}}, "UpdateFilterRequest": {"type": "structure", "required": ["filterArn"], "members": {"action": {"shape": "FilterAction", "documentation": "<p>Specifies the action that is to be applied to the findings that match the filter.</p>"}, "description": {"shape": "FilterDescription", "documentation": "<p>A description of the filter.</p>"}, "filterArn": {"shape": "FilterArn", "documentation": "<p>The Amazon Resource Number (ARN) of the filter to update.</p>"}, "filterCriteria": {"shape": "FilterCriteria", "documentation": "<p>Defines the criteria to be update in the filter.</p>"}, "name": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name of the filter.</p>"}, "reason": {"shape": "FilterReason", "documentation": "<p>The reason the filter was updated.</p>"}}}, "UpdateFilterResponse": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "FilterArn", "documentation": "<p>The Amazon Resource Number (ARN) of the successfully updated filter.</p>"}}}, "UpdateOrgEc2DeepInspectionConfigurationRequest": {"type": "structure", "required": ["orgPackagePaths"], "members": {"orgPackagePaths": {"shape": "PathList", "documentation": "<p>The Amazon Inspector deep inspection custom paths you are adding for your organization.</p>"}}}, "UpdateOrgEc2DeepInspectionConfigurationResponse": {"type": "structure", "members": {}}, "UpdateOrganizationConfigurationRequest": {"type": "structure", "required": ["autoEnable"], "members": {"autoEnable": {"shape": "AutoEnable", "documentation": "<p>Defines which scan types are enabled automatically for new members of your Amazon Inspector organization.</p>"}}}, "UpdateOrganizationConfigurationResponse": {"type": "structure", "required": ["autoEnable"], "members": {"autoEnable": {"shape": "AutoEnable", "documentation": "<p>The updated status of scan types automatically enabled for new members of your Amazon Inspector organization.</p>"}}}, "Usage": {"type": "structure", "members": {"currency": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The currency type used when calculating usage data.</p>"}, "estimatedMonthlyCost": {"shape": "MonthlyCostEstimate", "documentation": "<p>The estimated monthly cost of Amazon Inspector.</p>"}, "total": {"shape": "UsageValue", "documentation": "<p>The total of usage.</p>"}, "type": {"shape": "UsageType", "documentation": "<p>The type scan.</p>"}}, "documentation": "<p>Contains usage information about the cost of Amazon Inspector operation.</p>"}, "UsageAccountId": {"type": "string", "pattern": "[0-9]{12}"}, "UsageAccountIdList": {"type": "list", "member": {"shape": "UsageAccountId"}, "max": 7000, "min": 1}, "UsageList": {"type": "list", "member": {"shape": "Usage"}}, "UsageTotal": {"type": "structure", "members": {"accountId": {"shape": "MeteringAccountId", "documentation": "<p>The account ID of the account that usage data was retrieved for.</p>"}, "usage": {"shape": "UsageList", "documentation": "<p>An object representing the total usage for an account.</p>"}}, "documentation": "<p>The total of usage for an account ID.</p>"}, "UsageTotalList": {"type": "list", "member": {"shape": "UsageTotal"}}, "UsageType": {"type": "string", "enum": ["EC2_INSTANCE_HOURS", "ECR_INITIAL_SCAN", "ECR_RESCAN", "LAMBDA_FUNCTION_HOURS", "LAMBDA_FUNCTION_CODE_HOURS"]}, "UsageValue": {"type": "double", "min": 0}, "ValidationException": {"type": "structure", "required": ["message", "reason"], "members": {"fields": {"shape": "ValidationExceptionFields", "documentation": "<p>The fields that failed validation.</p>"}, "message": {"shape": "String"}, "reason": {"shape": "ValidationExceptionReason", "documentation": "<p>The reason for the validation failure.</p>"}}, "documentation": "<p>The request has failed validation due to missing required fields or having invalid inputs.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "ValidationExceptionField": {"type": "structure", "required": ["message", "name"], "members": {"message": {"shape": "String", "documentation": "<p>The validation exception message.</p>"}, "name": {"shape": "String", "documentation": "<p>The name of the validation exception.</p>"}}, "documentation": "<p>An object that describes a validation exception.</p>"}, "ValidationExceptionFields": {"type": "list", "member": {"shape": "ValidationExceptionField"}}, "ValidationExceptionReason": {"type": "string", "enum": ["CANNOT_PARSE", "FIELD_VALIDATION_FAILED", "OTHER"]}, "VendorCreatedAt": {"type": "timestamp"}, "VendorSeverity": {"type": "string", "max": 64, "min": 1}, "VendorUpdatedAt": {"type": "timestamp"}, "Version": {"type": "string", "pattern": "^\\$LATEST|[0-9]+$"}, "VpcId": {"type": "string", "pattern": "^vpc-([a-z0-9]{8}|[a-z0-9]{17}|\\*)$"}, "VulnId": {"type": "string", "pattern": "^CVE-[12][0-9]{3}-[0-9]{1,10}$"}, "VulnIdList": {"type": "list", "member": {"shape": "VulnId"}, "max": 1, "min": 1}, "Vulnerabilities": {"type": "list", "member": {"shape": "Vulnerability"}, "max": 1, "min": 0}, "Vulnerability": {"type": "structure", "required": ["id"], "members": {"atigData": {"shape": "AtigData", "documentation": "<p>An object that contains information about the Amazon Web Services Threat Intel Group (ATIG) details for the vulnerability.</p>"}, "cisaData": {"shape": "CisaData", "documentation": "<p>An object that contains the Cybersecurity and Infrastructure Security Agency (CISA) details for the vulnerability.</p>"}, "cvss2": {"shape": "Cvss2", "documentation": "<p>An object that contains the Common Vulnerability Scoring System (CVSS) Version 2 details for the vulnerability.</p>"}, "cvss3": {"shape": "Cvss3", "documentation": "<p>An object that contains the Common Vulnerability Scoring System (CVSS) Version 3 details for the vulnerability.</p>"}, "cwes": {"shape": "Cwes", "documentation": "<p>The Common Weakness Enumeration (CWE) associated with the vulnerability.</p>"}, "description": {"shape": "VulnerabilityDescription", "documentation": "<p>A description of the vulnerability.</p>"}, "detectionPlatforms": {"shape": "DetectionPlatforms", "documentation": "<p>Platforms that the vulnerability can be detected on.</p>"}, "epss": {"shape": "Epss", "documentation": "<p>An object that contains the Exploit Prediction Scoring System (EPSS) score for a vulnerability.</p>"}, "exploitObserved": {"shape": "ExploitObserved", "documentation": "<p>An object that contains details on when the exploit was observed.</p>"}, "id": {"shape": "NonEmptyString", "documentation": "<p>The ID for the specific vulnerability.</p>"}, "referenceUrls": {"shape": "VulnerabilityReferenceUrls", "documentation": "<p>Links to various resources with more information on this vulnerability. </p>"}, "relatedVulnerabilities": {"shape": "RelatedVulnerabilities", "documentation": "<p>A list of related vulnerabilities.</p>"}, "source": {"shape": "VulnerabilitySource", "documentation": "<p>The source of the vulnerability information. Possible results are <code>RHEL</code>, <code>AMAZON_CVE</code>, <code>DEBIAN</code> or <code>NVD</code>.</p>"}, "sourceUrl": {"shape": "VulnerabilitySourceUrl", "documentation": "<p>A link to the official source material for this vulnerability.</p>"}, "vendorCreatedAt": {"shape": "VendorCreatedAt", "documentation": "<p>The date and time when the vendor created this vulnerability.</p>"}, "vendorSeverity": {"shape": "Vendor<PERSON><PERSON><PERSON>", "documentation": "<p>The severity assigned by the vendor.</p>"}, "vendorUpdatedAt": {"shape": "VendorUpdatedAt", "documentation": "<p>The date and time when the vendor last updated this vulnerability.</p>"}}, "documentation": "<p>Contains details about a specific vulnerability Amazon Inspector can detect.</p>"}, "VulnerabilityDescription": {"type": "string"}, "VulnerabilityId": {"type": "string", "max": 128, "min": 1}, "VulnerabilityIdList": {"type": "list", "member": {"shape": "VulnerabilityId"}}, "VulnerabilityReferenceUrl": {"type": "string", "min": 0}, "VulnerabilityReferenceUrls": {"type": "list", "member": {"shape": "VulnerabilityReferenceUrl"}, "max": 100, "min": 0}, "VulnerabilitySource": {"type": "string", "enum": ["NVD"]}, "VulnerabilitySourceUrl": {"type": "string", "min": 0}, "VulnerablePackage": {"type": "structure", "required": ["name", "version"], "members": {"arch": {"shape": "PackageArchitecture", "documentation": "<p>The architecture of the vulnerable package.</p>"}, "epoch": {"shape": "PackageEpoch", "documentation": "<p>The epoch of the vulnerable package.</p>"}, "filePath": {"shape": "FilePath", "documentation": "<p>The file path of the vulnerable package.</p>"}, "fixedInVersion": {"shape": "PackageVersion", "documentation": "<p>The version of the package that contains the vulnerability fix.</p>"}, "name": {"shape": "PackageName", "documentation": "<p>The name of the vulnerable package.</p>"}, "packageManager": {"shape": "PackageManager", "documentation": "<p>The package manager of the vulnerable package.</p>"}, "release": {"shape": "PackageRelease", "documentation": "<p>The release of the vulnerable package.</p>"}, "remediation": {"shape": "VulnerablePackageRemediation", "documentation": "<p>The code to run in your environment to update packages with a fix available.</p>"}, "sourceLambdaLayerArn": {"shape": "LambdaLayerArn", "documentation": "<p>The Amazon Resource Number (ARN) of the AWS Lambda function affected by a finding.</p>"}, "sourceLayerHash": {"shape": "SourceLayerHash", "documentation": "<p>The source layer hash of the vulnerable package.</p>"}, "version": {"shape": "PackageVersion", "documentation": "<p>The version of the vulnerable package.</p>"}}, "documentation": "<p>Information on the vulnerable package identified by a finding.</p>"}, "VulnerablePackageList": {"type": "list", "member": {"shape": "VulnerablePackage"}}, "VulnerablePackageRemediation": {"type": "string", "max": 1024, "min": 1}}, "documentation": "<p>Amazon Inspector is a vulnerability discovery service that automates continuous scanning for security vulnerabilities within your Amazon EC2, Amazon ECR, and Amazon Web Services Lambda environments.</p>"}