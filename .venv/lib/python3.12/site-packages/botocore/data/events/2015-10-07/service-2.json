{"version": "2.0", "metadata": {"apiVersion": "2015-10-07", "endpointPrefix": "events", "jsonVersion": "1.1", "protocol": "json", "serviceFullName": "Amazon EventBridge", "serviceId": "EventBridge", "signatureVersion": "v4", "targetPrefix": "AWSEvents", "uid": "eventbridge-2015-10-07"}, "operations": {"ActivateEventSource": {"name": "ActivateEventSource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ActivateEventSourceRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InvalidStateException"}, {"shape": "InternalException"}, {"shape": "OperationDisabledException"}], "documentation": "<p>Activates a partner event source that has been deactivated. Once activated, your matching event bus will start receiving events from the event source.</p>"}, "CancelReplay": {"name": "CancelReplay", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CancelReplayRequest"}, "output": {"shape": "CancelReplayResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConcurrentModificationException"}, {"shape": "IllegalStatusException"}, {"shape": "InternalException"}], "documentation": "<p>Cancels the specified replay.</p>"}, "CreateApiDestination": {"name": "CreateApiDestination", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateApiDestinationRequest"}, "output": {"shape": "CreateApiDestinationResponse"}, "errors": [{"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "InternalException"}], "documentation": "<p>Creates an API destination, which is an HTTP invocation endpoint configured as a target for events.</p>"}, "CreateArchive": {"name": "CreateArchive", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateArchiveRequest"}, "output": {"shape": "CreateArchiveResponse"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}, {"shape": "LimitExceededException"}, {"shape": "InvalidEventPatternException"}], "documentation": "<p>Creates an archive of events with the specified settings. When you create an archive, incoming events might not immediately start being sent to the archive. Allow a short period of time for changes to take effect. If you do not specify a pattern to filter events sent to the archive, all events are sent to the archive except replayed events. Replayed events are not sent to an archive.</p>"}, "CreateConnection": {"name": "CreateConnection", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateConnectionRequest"}, "output": {"shape": "CreateConnectionResponse"}, "errors": [{"shape": "ResourceAlreadyExistsException"}, {"shape": "LimitExceededException"}, {"shape": "InternalException"}], "documentation": "<p>Creates a connection. A connection defines the authorization type and credentials to use for authorization with an API destination HTTP endpoint.</p>"}, "CreateEndpoint": {"name": "CreateEndpoint", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateEndpointRequest"}, "output": {"shape": "CreateEndpointResponse"}, "errors": [{"shape": "ResourceAlreadyExistsException"}, {"shape": "LimitExceededException"}, {"shape": "InternalException"}], "documentation": "<p>Creates a global endpoint. Global endpoints improve your application's availability by making it regional-fault tolerant. To do this, you define a primary and secondary Region with event buses in each Region. You also create a Amazon Route 53 health check that will tell EventBridge to route events to the secondary Region when an \"unhealthy\" state is encountered and events will be routed back to the primary Region when the health check reports a \"healthy\" state.</p>"}, "CreateEventBus": {"name": "CreateEventBus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateEventBusRequest"}, "output": {"shape": "CreateEventBusResponse"}, "errors": [{"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidStateException"}, {"shape": "InternalException"}, {"shape": "ConcurrentModificationException"}, {"shape": "LimitExceededException"}, {"shape": "OperationDisabledException"}], "documentation": "<p>Creates a new event bus within your account. This can be a custom event bus which you can use to receive events from your custom applications and services, or it can be a partner event bus which can be matched to a partner event source.</p>"}, "CreatePartnerEventSource": {"name": "CreatePartnerEventSource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreatePartnerEventSourceRequest"}, "output": {"shape": "CreatePartnerEventSourceResponse"}, "errors": [{"shape": "ResourceAlreadyExistsException"}, {"shape": "InternalException"}, {"shape": "ConcurrentModificationException"}, {"shape": "LimitExceededException"}, {"shape": "OperationDisabledException"}], "documentation": "<p>Called by an SaaS partner to create a partner event source. This operation is not used by Amazon Web Services customers.</p> <p>Each partner event source can be used by one Amazon Web Services account to create a matching partner event bus in that Amazon Web Services account. A SaaS partner must create one partner event source for each Amazon Web Services account that wants to receive those event types. </p> <p>A partner event source creates events based on resources within the SaaS partner's service or application.</p> <p>An Amazon Web Services account that creates a partner event bus that matches the partner event source can use that event bus to receive events from the partner, and then process them using Amazon Web Services Events rules and targets.</p> <p>Partner event source names follow this format:</p> <p> <code> <i>partner_name</i>/<i>event_namespace</i>/<i>event_name</i> </code> </p> <p> <i>partner_name</i> is determined during partner registration and identifies the partner to Amazon Web Services customers. <i>event_namespace</i> is determined by the partner and is a way for the partner to categorize their events. <i>event_name</i> is determined by the partner, and should uniquely identify an event-generating resource within the partner system. The combination of <i>event_namespace</i> and <i>event_name</i> should help Amazon Web Services customers decide whether to create an event bus to receive these events.</p>"}, "DeactivateEventSource": {"name": "DeactivateEventSource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeactivateEventSourceRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InvalidStateException"}, {"shape": "InternalException"}, {"shape": "OperationDisabledException"}], "documentation": "<p>You can use this operation to temporarily stop receiving events from the specified partner event source. The matching event bus is not deleted. </p> <p>When you deactivate a partner event source, the source goes into PENDING state. If it remains in PENDING state for more than two weeks, it is deleted.</p> <p>To activate a deactivated partner event source, use <a href=\"https://docs.aws.amazon.com/eventbridge/latest/APIReference/API_ActivateEventSource.html\">ActivateEventSource</a>.</p>"}, "DeauthorizeConnection": {"name": "DeauthorizeConnection", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeauthorizeConnectionRequest"}, "output": {"shape": "DeauthorizeConnectionResponse"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}], "documentation": "<p>Removes all authorization parameters from the connection. This lets you remove the secret from the connection so you can reuse it without having to create a new connection.</p>"}, "DeleteApiDestination": {"name": "DeleteApiDestination", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteApiDestinationRequest"}, "output": {"shape": "DeleteApiDestinationResponse"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}], "documentation": "<p>Deletes the specified API destination.</p>"}, "DeleteArchive": {"name": "DeleteArchive", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteArchiveRequest"}, "output": {"shape": "DeleteArchiveResponse"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}], "documentation": "<p>Deletes the specified archive.</p>"}, "DeleteConnection": {"name": "DeleteConnection", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteConnectionRequest"}, "output": {"shape": "DeleteConnectionResponse"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}], "documentation": "<p>Deletes a connection.</p>"}, "DeleteEndpoint": {"name": "DeleteEndpoint", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteEndpointRequest"}, "output": {"shape": "DeleteEndpointResponse"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}], "documentation": "<p>Delete an existing global endpoint. For more information about global endpoints, see <a href=\"https://docs.aws.amazon.com/eventbridge/latest/userguide/eb-global-endpoints.html\">Making applications Regional-fault tolerant with global endpoints and event replication</a> in the Amazon EventBridge User Guide.</p>"}, "DeleteEventBus": {"name": "DeleteEventBus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteEventBusRequest"}, "errors": [{"shape": "InternalException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Deletes the specified custom event bus or partner event bus. All rules associated with this event bus need to be deleted. You can't delete your account's default event bus.</p>"}, "DeletePartnerEventSource": {"name": "DeletePartnerEventSource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeletePartnerEventSourceRequest"}, "errors": [{"shape": "InternalException"}, {"shape": "ConcurrentModificationException"}, {"shape": "OperationDisabledException"}], "documentation": "<p>This operation is used by SaaS partners to delete a partner event source. This operation is not used by Amazon Web Services customers.</p> <p>When you delete an event source, the status of the corresponding partner event bus in the Amazon Web Services customer account becomes DELETED.</p> <p/>"}, "DeleteRule": {"name": "DeleteRule", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteRuleRequest"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "ManagedRuleException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes the specified rule.</p> <p>Before you can delete the rule, you must remove all targets, using <a href=\"https://docs.aws.amazon.com/eventbridge/latest/APIReference/API_RemoveTargets.html\">RemoveTargets</a>.</p> <p>When you delete a rule, incoming events might continue to match to the deleted rule. Allow a short period of time for changes to take effect.</p> <p>If you call delete rule multiple times for the same rule, all calls will succeed. When you call delete rule for a non-existent custom eventbus, <code>ResourceNotFoundException</code> is returned.</p> <p>Managed rules are rules created and managed by another Amazon Web Services service on your behalf. These rules are created by those other Amazon Web Services services to support functionality in those services. You can delete these rules using the <code>Force</code> option, but you should do so only if you are sure the other service is not still using that rule.</p>"}, "DescribeApiDestination": {"name": "DescribeApiDestination", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeApiDestinationRequest"}, "output": {"shape": "DescribeApiDestinationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}], "documentation": "<p>Retrieves details about an API destination.</p>"}, "DescribeArchive": {"name": "DescribeArchive", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeArchiveRequest"}, "output": {"shape": "DescribeArchiveResponse"}, "errors": [{"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}], "documentation": "<p>Retrieves details about an archive.</p>"}, "DescribeConnection": {"name": "DescribeConnection", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeConnectionRequest"}, "output": {"shape": "DescribeConnectionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}], "documentation": "<p>Retrieves details about a connection.</p>"}, "DescribeEndpoint": {"name": "DescribeEndpoint", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeEndpointRequest"}, "output": {"shape": "DescribeEndpointResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}], "documentation": "<p>Get the information about an existing global endpoint. For more information about global endpoints, see <a href=\"https://docs.aws.amazon.com/eventbridge/latest/userguide/eb-global-endpoints.html\">Making applications Regional-fault tolerant with global endpoints and event replication</a> in the Amazon EventBridge User Guide..</p>"}, "DescribeEventBus": {"name": "DescribeEventBus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeEventBusRequest"}, "output": {"shape": "DescribeEventBusResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}], "documentation": "<p>Displays details about an event bus in your account. This can include the external Amazon Web Services accounts that are permitted to write events to your default event bus, and the associated policy. For custom event buses and partner event buses, it displays the name, ARN, policy, state, and creation time.</p> <p> To enable your account to receive events from other accounts on its default event bus, use <a href=\"https://docs.aws.amazon.com/eventbridge/latest/APIReference/API_PutPermission.html\">PutPermission</a>.</p> <p>For more information about partner event buses, see <a href=\"https://docs.aws.amazon.com/eventbridge/latest/APIReference/API_CreateEventBus.html\">CreateEventBus</a>.</p>"}, "DescribeEventSource": {"name": "DescribeEventSource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeEventSourceRequest"}, "output": {"shape": "DescribeEventSourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}, {"shape": "OperationDisabledException"}], "documentation": "<p>This operation lists details about a partner event source that is shared with your account.</p>"}, "DescribePartnerEventSource": {"name": "DescribePartnerEventSource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribePartnerEventSourceRequest"}, "output": {"shape": "DescribePartnerEventSourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}, {"shape": "OperationDisabledException"}], "documentation": "<p>An SaaS partner can use this operation to list details about a partner event source that they have created. Amazon Web Services customers do not use this operation. Instead, Amazon Web Services customers can use <a href=\"https://docs.aws.amazon.com/eventbridge/latest/APIReference/API_DescribeEventSource.html\">DescribeEventSource</a> to see details about a partner event source that is shared with them.</p>"}, "DescribeReplay": {"name": "DescribeReplay", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeReplayRequest"}, "output": {"shape": "DescribeReplayResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}], "documentation": "<p>Retrieves details about a replay. Use <code>DescribeReplay</code> to determine the progress of a running replay. A replay processes events to replay based on the time in the event, and replays them using 1 minute intervals. If you use <code>StartReplay</code> and specify an <code>EventStartTime</code> and an <code>EventEndTime</code> that covers a 20 minute time range, the events are replayed from the first minute of that 20 minute range first. Then the events from the second minute are replayed. You can use <code>DescribeReplay</code> to determine the progress of a replay. The value returned for <code>EventLastReplayedTime</code> indicates the time within the specified time range associated with the last event replayed.</p>"}, "DescribeRule": {"name": "DescribeRule", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeRuleRequest"}, "output": {"shape": "DescribeRuleResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}], "documentation": "<p>Describes the specified rule.</p> <p>DescribeRule does not list the targets of a rule. To see the targets associated with a rule, use <a href=\"https://docs.aws.amazon.com/eventbridge/latest/APIReference/API_ListTargetsByRule.html\">ListTargetsByRule</a>.</p>"}, "DisableRule": {"name": "DisableRule", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DisableRuleRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ManagedRuleException"}, {"shape": "InternalException"}], "documentation": "<p>Disables the specified rule. A disabled rule won't match any events, and won't self-trigger if it has a schedule expression.</p> <p>When you disable a rule, incoming events might continue to match to the disabled rule. Allow a short period of time for changes to take effect.</p>"}, "EnableRule": {"name": "EnableRule", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "EnableRuleRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ManagedRuleException"}, {"shape": "InternalException"}], "documentation": "<p>Enables the specified rule. If the rule does not exist, the operation fails.</p> <p>When you enable a rule, incoming events might not immediately start matching to a newly enabled rule. Allow a short period of time for changes to take effect.</p>"}, "ListApiDestinations": {"name": "ListApiDestinations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListApiDestinationsRequest"}, "output": {"shape": "ListApiDestinationsResponse"}, "errors": [{"shape": "InternalException"}], "documentation": "<p>Retrieves a list of API destination in the account in the current Region.</p>"}, "ListArchives": {"name": "ListArchives", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListArchivesRequest"}, "output": {"shape": "ListArchivesResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}], "documentation": "<p>Lists your archives. You can either list all the archives or you can provide a prefix to match to the archive names. Filter parameters are exclusive.</p>"}, "ListConnections": {"name": "ListConnections", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListConnectionsRequest"}, "output": {"shape": "ListConnectionsResponse"}, "errors": [{"shape": "InternalException"}], "documentation": "<p>Retrieves a list of connections from the account.</p>"}, "ListEndpoints": {"name": "ListEndpoints", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListEndpointsRequest"}, "output": {"shape": "ListEndpointsResponse"}, "errors": [{"shape": "InternalException"}], "documentation": "<p>List the global endpoints associated with this account. For more information about global endpoints, see <a href=\"https://docs.aws.amazon.com/eventbridge/latest/userguide/eb-global-endpoints.html\">Making applications Regional-fault tolerant with global endpoints and event replication</a> in the Amazon EventBridge User Guide..</p>"}, "ListEventBuses": {"name": "ListEventBuses", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListEventBusesRequest"}, "output": {"shape": "ListEventBusesResponse"}, "errors": [{"shape": "InternalException"}], "documentation": "<p>Lists all the event buses in your account, including the default event bus, custom event buses, and partner event buses.</p>"}, "ListEventSources": {"name": "ListEventSources", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListEventSourcesRequest"}, "output": {"shape": "ListEventSourcesResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "OperationDisabledException"}], "documentation": "<p>You can use this to see all the partner event sources that have been shared with your Amazon Web Services account. For more information about partner event sources, see <a href=\"https://docs.aws.amazon.com/eventbridge/latest/APIReference/API_CreateEventBus.html\">CreateEventBus</a>.</p>"}, "ListPartnerEventSourceAccounts": {"name": "ListPartnerEventSourceAccounts", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListPartnerEventSourceAccountsRequest"}, "output": {"shape": "ListPartnerEventSourceAccountsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}, {"shape": "OperationDisabledException"}], "documentation": "<p>An SaaS partner can use this operation to display the Amazon Web Services account ID that a particular partner event source name is associated with. This operation is not used by Amazon Web Services customers.</p>"}, "ListPartnerEventSources": {"name": "ListPartnerEventSources", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListPartnerEventSourcesRequest"}, "output": {"shape": "ListPartnerEventSourcesResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "OperationDisabledException"}], "documentation": "<p>An SaaS partner can use this operation to list all the partner event source names that they have created. This operation is not used by Amazon Web Services customers.</p>"}, "ListReplays": {"name": "ListReplays", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListReplaysRequest"}, "output": {"shape": "ListReplaysResponse"}, "errors": [{"shape": "InternalException"}], "documentation": "<p>Lists your replays. You can either list all the replays or you can provide a prefix to match to the replay names. Filter parameters are exclusive.</p>"}, "ListRuleNamesByTarget": {"name": "ListRuleNamesByTarget", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListRuleNamesByTargetRequest"}, "output": {"shape": "ListRuleNamesByTargetResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists the rules for the specified target. You can see which of the rules in Amazon EventBridge can invoke a specific target in your account.</p>"}, "ListRules": {"name": "ListRules", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListRulesRequest"}, "output": {"shape": "ListRulesResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists your Amazon EventBridge rules. You can either list all the rules or you can provide a prefix to match to the rule names.</p> <p>ListRules does not list the targets of a rule. To see the targets associated with a rule, use <a href=\"https://docs.aws.amazon.com/eventbridge/latest/APIReference/API_ListTargetsByRule.html\">ListTargetsByRule</a>.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}], "documentation": "<p>Displays the tags associated with an EventBridge resource. In EventBridge, rules and event buses can be tagged.</p>"}, "ListTargetsByRule": {"name": "ListTargetsByRule", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTargetsByRuleRequest"}, "output": {"shape": "ListTargetsByRuleResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}], "documentation": "<p>Lists the targets assigned to the specified rule.</p>"}, "PutEvents": {"name": "PutEvents", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutEventsRequest"}, "output": {"shape": "PutEventsResponse"}, "errors": [{"shape": "InternalException"}], "documentation": "<p>Sends custom events to Amazon EventBridge so that they can be matched to rules.</p> <note> <p>PutEvents will only process nested JSON up to 1100 levels deep.</p> </note>"}, "PutPartnerEvents": {"name": "PutPartnerEvents", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutPartnerEventsRequest"}, "output": {"shape": "PutPartnerEventsResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "OperationDisabledException"}], "documentation": "<p>This is used by SaaS partners to write events to a customer's partner event bus. Amazon Web Services customers do not use this operation.</p>"}, "PutPermission": {"name": "PutPermission", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutPermissionRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "PolicyLengthExceededException"}, {"shape": "InternalException"}, {"shape": "ConcurrentModificationException"}, {"shape": "OperationDisabledException"}], "documentation": "<p>Running <code>PutPermission</code> permits the specified Amazon Web Services account or Amazon Web Services organization to put events to the specified <i>event bus</i>. Amazon EventBridge (CloudWatch Events) rules in your account are triggered by these events arriving to an event bus in your account. </p> <p>For another account to send events to your account, that external account must have an EventBridge rule with your account's event bus as a target.</p> <p>To enable multiple Amazon Web Services accounts to put events to your event bus, run <code>PutPermission</code> once for each of these accounts. Or, if all the accounts are members of the same Amazon Web Services organization, you can run <code>PutPermission</code> once specifying <code>Principal</code> as \"*\" and specifying the Amazon Web Services organization ID in <code>Condition</code>, to grant permissions to all accounts in that organization.</p> <p>If you grant permissions using an organization, then accounts in that organization must specify a <code>RoleArn</code> with proper permissions when they use <code>PutTarget</code> to add your account's event bus as a target. For more information, see <a href=\"https://docs.aws.amazon.com/eventbridge/latest/userguide/eventbridge-cross-account-event-delivery.html\">Sending and Receiving Events Between Amazon Web Services Accounts</a> in the <i>Amazon EventBridge User Guide</i>.</p> <p>The permission policy on the event bus cannot exceed 10 KB in size.</p>"}, "PutRule": {"name": "PutRule", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutRuleRequest"}, "output": {"shape": "PutRuleResponse"}, "errors": [{"shape": "InvalidEventPatternException"}, {"shape": "LimitExceededException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ManagedRuleException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Creates or updates the specified rule. Rules are enabled by default, or based on value of the state. You can disable a rule using <a href=\"https://docs.aws.amazon.com/eventbridge/latest/APIReference/API_DisableRule.html\">DisableRule</a>.</p> <p>A single rule watches for events from a single event bus. Events generated by Amazon Web Services services go to your account's default event bus. Events generated by SaaS partner services or applications go to the matching partner event bus. If you have custom applications or services, you can specify whether their events go to your default event bus or a custom event bus that you have created. For more information, see <a href=\"https://docs.aws.amazon.com/eventbridge/latest/APIReference/API_CreateEventBus.html\">CreateEventBus</a>.</p> <p>If you are updating an existing rule, the rule is replaced with what you specify in this <code>PutRule</code> command. If you omit arguments in <code>PutRule</code>, the old values for those arguments are not kept. Instead, they are replaced with null values.</p> <p>When you create or update a rule, incoming events might not immediately start matching to new or updated rules. Allow a short period of time for changes to take effect.</p> <p>A rule must contain at least an EventPattern or ScheduleExpression. Rules with EventPatterns are triggered when a matching event is observed. Rules with ScheduleExpressions self-trigger based on the given schedule. A rule can have both an EventPattern and a ScheduleExpression, in which case the rule triggers on matching events as well as on a schedule.</p> <p>When you initially create a rule, you can optionally assign one or more tags to the rule. Tags can help you organize and categorize your resources. You can also use them to scope user permissions, by granting a user permission to access or change only rules with certain tag values. To use the <code>PutRule</code> operation and assign tags, you must have both the <code>events:PutRule</code> and <code>events:TagResource</code> permissions.</p> <p>If you are updating an existing rule, any tags you specify in the <code>PutRule</code> operation are ignored. To update the tags of an existing rule, use <a href=\"https://docs.aws.amazon.com/eventbridge/latest/APIReference/API_TagResource.html\">TagResource</a> and <a href=\"https://docs.aws.amazon.com/eventbridge/latest/APIReference/API_UntagResource.html\">UntagResource</a>.</p> <p>Most services in Amazon Web Services treat : or / as the same character in Amazon Resource Names (ARNs). However, EventBridge uses an exact match in event patterns and rules. Be sure to use the correct ARN characters when creating event patterns so that they match the ARN syntax in the event you want to match.</p> <p>In EventBridge, it is possible to create rules that lead to infinite loops, where a rule is fired repeatedly. For example, a rule might detect that ACLs have changed on an S3 bucket, and trigger software to change them to the desired state. If the rule is not written carefully, the subsequent change to the ACLs fires the rule again, creating an infinite loop.</p> <p>To prevent this, write the rules so that the triggered actions do not re-fire the same rule. For example, your rule could fire only if ACLs are found to be in a bad state, instead of after any change. </p> <p>An infinite loop can quickly cause higher than expected charges. We recommend that you use budgeting, which alerts you when charges exceed your specified limit. For more information, see <a href=\"https://docs.aws.amazon.com/awsaccountbilling/latest/aboutv2/budgets-managing-costs.html\">Managing Your Costs with Budgets</a>.</p>"}, "PutTargets": {"name": "PutTargets", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutTargetsRequest"}, "output": {"shape": "PutTargetsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConcurrentModificationException"}, {"shape": "LimitExceededException"}, {"shape": "ManagedRuleException"}, {"shape": "InternalException"}], "documentation": "<p>Adds the specified targets to the specified rule, or updates the targets if they are already associated with the rule.</p> <p>Targets are the resources that are invoked when a rule is triggered.</p> <note> <p>Each rule can have up to five (5) targets associated with it at one time.</p> </note> <p>You can configure the following as targets for Events:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/eventbridge/latest/userguide/eb-api-destinations.html\">API destination</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/eventbridge/latest/userguide/eb-api-gateway-target.html\">API Gateway</a> </p> </li> <li> <p>Batch job queue</p> </li> <li> <p>CloudWatch group</p> </li> <li> <p>CodeBuild project</p> </li> <li> <p>CodePipeline</p> </li> <li> <p>EC2 <code>CreateSnapshot</code> API call</p> </li> <li> <p>EC2 Image Builder</p> </li> <li> <p>EC2 <code>RebootInstances</code> API call</p> </li> <li> <p>EC2 <code>StopInstances</code> API call</p> </li> <li> <p>EC2 <code>TerminateInstances</code> API call</p> </li> <li> <p>ECS task</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/eventbridge/latest/userguide/eb-cross-account.html\">Event bus in a different account or Region</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/eventbridge/latest/userguide/eb-bus-to-bus.html\">Event bus in the same account and Region</a> </p> </li> <li> <p>Firehose delivery stream</p> </li> <li> <p>Glue workflow</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/incident-manager/latest/userguide/incident-creation.html#incident-tracking-auto-eventbridge\">Incident Manager response plan</a> </p> </li> <li> <p>Inspector assessment template</p> </li> <li> <p>Kinesis stream</p> </li> <li> <p>Lambda function</p> </li> <li> <p>Redshift cluster</p> </li> <li> <p>Redshift Serverless workgroup</p> </li> <li> <p>SageMaker Pipeline</p> </li> <li> <p>SNS topic</p> </li> <li> <p>SQS queue</p> </li> <li> <p>Step Functions state machine</p> </li> <li> <p>Systems Manager Automation</p> </li> <li> <p>Systems Manager OpsItem</p> </li> <li> <p>Systems Manager Run Command</p> </li> </ul> <p>Creating rules with built-in targets is supported only in the Amazon Web Services Management Console. The built-in targets are <code>EC2 CreateSnapshot API call</code>, <code>EC2 RebootInstances API call</code>, <code>EC2 StopInstances API call</code>, and <code>EC2 TerminateInstances API call</code>. </p> <p>For some target types, <code>PutTargets</code> provides target-specific parameters. If the target is a Kinesis data stream, you can optionally specify which shard the event goes to by using the <code>KinesisParameters</code> argument. To invoke a command on multiple EC2 instances with one rule, you can use the <code>RunCommandParameters</code> field.</p> <p>To be able to make API calls against the resources that you own, Amazon EventBridge needs the appropriate permissions. For Lambda and Amazon SNS resources, EventBridge relies on resource-based policies. For EC2 instances, Kinesis Data Streams, Step Functions state machines and API Gateway APIs, EventBridge relies on IAM roles that you specify in the <code>RoleARN</code> argument in <code>PutTargets</code>. For more information, see <a href=\"https://docs.aws.amazon.com/eventbridge/latest/userguide/auth-and-access-control-eventbridge.html\">Authentication and Access Control</a> in the <i>Amazon EventBridge User Guide</i>.</p> <p>If another Amazon Web Services account is in the same region and has granted you permission (using <code>PutPermission</code>), you can send events to that account. Set that account's event bus as a target of the rules in your account. To send the matched events to the other account, specify that account's event bus as the <code>Arn</code> value when you run <code>PutTargets</code>. If your account sends events to another account, your account is charged for each sent event. Each event sent to another account is charged as a custom event. The account receiving the event is not charged. For more information, see <a href=\"http://aws.amazon.com/eventbridge/pricing/\">Amazon EventBridge Pricing</a>.</p> <note> <p> <code>Input</code>, <code>InputPath</code>, and <code>InputTransformer</code> are not available with <code>PutTarget</code> if the target is an event bus of a different Amazon Web Services account.</p> </note> <p>If you are setting the event bus of another account as the target, and that account granted permission to your account through an organization instead of directly by the account ID, then you must specify a <code>RoleArn</code> with proper permissions in the <code>Target</code> structure. For more information, see <a href=\"https://docs.aws.amazon.com/eventbridge/latest/userguide/eventbridge-cross-account-event-delivery.html\">Sending and Receiving Events Between Amazon Web Services Accounts</a> in the <i>Amazon EventBridge User Guide</i>.</p> <p>For more information about enabling cross-account events, see <a href=\"https://docs.aws.amazon.com/eventbridge/latest/APIReference/API_PutPermission.html\">PutPermission</a>.</p> <p> <b>Input</b>, <b>InputPath</b>, and <b>InputTransformer</b> are mutually exclusive and optional parameters of a target. When a rule is triggered due to a matched event:</p> <ul> <li> <p>If none of the following arguments are specified for a target, then the entire event is passed to the target in JSON format (unless the target is Amazon EC2 Run Command or Amazon ECS task, in which case nothing from the event is passed to the target).</p> </li> <li> <p>If <b>Input</b> is specified in the form of valid JSON, then the matched event is overridden with this constant.</p> </li> <li> <p>If <b>InputPath</b> is specified in the form of JSONPath (for example, <code>$.detail</code>), then only the part of the event specified in the path is passed to the target (for example, only the detail part of the event is passed).</p> </li> <li> <p>If <b>InputTransformer</b> is specified, then one or more specified JSONPaths are extracted from the event and used as values in a template that you specify as the input to the target.</p> </li> </ul> <p>When you specify <code>InputPath</code> or <code>InputTransformer</code>, you must use JSON dot notation, not bracket notation.</p> <p>When you add targets to a rule and the associated rule triggers soon after, new or updated targets might not be immediately invoked. Allow a short period of time for changes to take effect.</p> <p>This action can partially fail if too many requests are made at the same time. If that happens, <code>FailedEntryCount</code> is non-zero in the response and each entry in <code>FailedEntries</code> provides the ID of the failed target and the error code.</p>"}, "RemovePermission": {"name": "RemovePermission", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RemovePermissionRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}, {"shape": "ConcurrentModificationException"}, {"shape": "OperationDisabledException"}], "documentation": "<p>Revokes the permission of another Amazon Web Services account to be able to put events to the specified event bus. Specify the account to revoke by the <code>StatementId</code> value that you associated with the account when you granted it permission with <code>PutPermission</code>. You can find the <code>StatementId</code> by using <a href=\"https://docs.aws.amazon.com/eventbridge/latest/APIReference/API_DescribeEventBus.html\">DescribeEventBus</a>.</p>"}, "RemoveTargets": {"name": "RemoveTargets", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RemoveTargetsRequest"}, "output": {"shape": "RemoveTargetsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ManagedRuleException"}, {"shape": "InternalException"}], "documentation": "<p>Removes the specified targets from the specified rule. When the rule is triggered, those targets are no longer be invoked.</p> <note> <p>A successful execution of <code>RemoveTargets</code> doesn't guarantee all targets are removed from the rule, it means that the target(s) listed in the request are removed.</p> </note> <p>When you remove a target, when the associated rule triggers, removed targets might continue to be invoked. Allow a short period of time for changes to take effect.</p> <p>This action can partially fail if too many requests are made at the same time. If that happens, <code>FailedEntryCount</code> is non-zero in the response and each entry in <code>FailedEntries</code> provides the ID of the failed target and the error code.</p>"}, "StartReplay": {"name": "StartReplay", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartReplayRequest"}, "output": {"shape": "StartReplayResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "InvalidEventPatternException"}, {"shape": "LimitExceededException"}, {"shape": "InternalException"}], "documentation": "<p>Starts the specified replay. Events are not necessarily replayed in the exact same order that they were added to the archive. A replay processes events to replay based on the time in the event, and replays them using 1 minute intervals. If you specify an <code>EventStartTime</code> and an <code>EventEndTime</code> that covers a 20 minute time range, the events are replayed from the first minute of that 20 minute range first. Then the events from the second minute are replayed. You can use <code>DescribeReplay</code> to determine the progress of a replay. The value returned for <code>EventLastReplayedTime</code> indicates the time within the specified time range associated with the last event replayed.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InternalException"}, {"shape": "ManagedRuleException"}], "documentation": "<p>Assigns one or more tags (key-value pairs) to the specified EventBridge resource. Tags can help you organize and categorize your resources. You can also use them to scope user permissions by granting a user permission to access or change only resources with certain tag values. In EventBridge, rules and event buses can be tagged.</p> <p>Tags don't have any semantic meaning to Amazon Web Services and are interpreted strictly as strings of characters.</p> <p>You can use the <code>TagResource</code> action with a resource that already has tags. If you specify a new tag key, this tag is appended to the list of tags associated with the resource. If you specify a tag key that is already associated with the resource, the new tag value that you specify replaces the previous value for that tag.</p> <p>You can associate as many as 50 tags with a resource.</p>"}, "TestEventPattern": {"name": "TestEventPattern", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TestEventPatternRequest"}, "output": {"shape": "TestEventPatternResponse"}, "errors": [{"shape": "InvalidEventPatternException"}, {"shape": "InternalException"}], "documentation": "<p>Tests whether the specified event pattern matches the provided event.</p> <p>Most services in Amazon Web Services treat : or / as the same character in Amazon Resource Names (ARNs). However, EventBridge uses an exact match in event patterns and rules. Be sure to use the correct ARN characters when creating event patterns so that they match the ARN syntax in the event you want to match.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ManagedRuleException"}], "documentation": "<p>Removes one or more tags from the specified EventBridge resource. In Amazon EventBridge (CloudWatch Events), rules and event buses can be tagged.</p>"}, "UpdateApiDestination": {"name": "UpdateApiDestination", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateApiDestinationRequest"}, "output": {"shape": "UpdateApiDestinationResponse"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Updates an API destination.</p>"}, "UpdateArchive": {"name": "UpdateArchive", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateArchiveRequest"}, "output": {"shape": "UpdateArchiveResponse"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}, {"shape": "LimitExceededException"}, {"shape": "InvalidEventPatternException"}], "documentation": "<p>Updates the specified archive.</p>"}, "UpdateConnection": {"name": "UpdateConnection", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateConnectionRequest"}, "output": {"shape": "UpdateConnectionResponse"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Updates settings for a connection.</p>"}, "UpdateEndpoint": {"name": "UpdateEndpoint", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateEndpointRequest"}, "output": {"shape": "UpdateEndpointResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InternalException"}], "documentation": "<p>Update an existing endpoint. For more information about global endpoints, see <a href=\"https://docs.aws.amazon.com/eventbridge/latest/userguide/eb-global-endpoints.html\">Making applications Regional-fault tolerant with global endpoints and event replication</a> in the Amazon EventBridge User Guide..</p>"}}, "shapes": {"AccountId": {"type": "string", "max": 12, "min": 12, "pattern": "\\d{12}"}, "Action": {"type": "string", "max": 64, "min": 1, "pattern": "events:[a-zA-Z]+"}, "ActivateEventSourceRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "EventSourceName", "documentation": "<p>The name of the partner event source to activate.</p>"}}}, "ApiDestination": {"type": "structure", "members": {"ApiDestinationArn": {"shape": "ApiDestinationArn", "documentation": "<p>The ARN of the API destination.</p>"}, "Name": {"shape": "ApiDestinationName", "documentation": "<p>The name of the API destination.</p>"}, "ApiDestinationState": {"shape": "ApiDestinationState", "documentation": "<p>The state of the API destination.</p>"}, "ConnectionArn": {"shape": "ConnectionArn", "documentation": "<p>The ARN of the connection specified for the API destination.</p>"}, "InvocationEndpoint": {"shape": "HttpsEndpoint", "documentation": "<p>The URL to the endpoint for the API destination.</p>"}, "HttpMethod": {"shape": "ApiDestinationHttpMethod", "documentation": "<p>The method to use to connect to the HTTP endpoint.</p>"}, "InvocationRateLimitPerSecond": {"shape": "ApiDestinationInvocationRateLimitPerSecond", "documentation": "<p>The maximum number of invocations per second to send to the HTTP endpoint.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the API destination was created.</p>"}, "LastModifiedTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the API destination was last modified.</p>"}}, "documentation": "<p>Contains details about an API destination.</p>"}, "ApiDestinationArn": {"type": "string", "max": 1600, "min": 1, "pattern": "^arn:aws([a-z]|\\-)*:events:([a-z]|\\d|\\-)*:([0-9]{12})?:api-destination\\/[\\.\\-_A-Za-z0-9]+\\/[\\-A-Za-z0-9]+$"}, "ApiDestinationDescription": {"type": "string", "max": 512, "pattern": ".*"}, "ApiDestinationHttpMethod": {"type": "string", "enum": ["POST", "GET", "HEAD", "OPTIONS", "PUT", "PATCH", "DELETE"]}, "ApiDestinationInvocationRateLimitPerSecond": {"type": "integer", "min": 1}, "ApiDestinationName": {"type": "string", "max": 64, "min": 1, "pattern": "[\\.\\-_A-Za-z0-9]+"}, "ApiDestinationResponseList": {"type": "list", "member": {"shape": "ApiDestination"}}, "ApiDestinationState": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}, "Archive": {"type": "structure", "members": {"ArchiveName": {"shape": "ArchiveName", "documentation": "<p>The name of the archive.</p>"}, "EventSourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the event bus associated with the archive. Only events from this event bus are sent to the archive.</p>"}, "State": {"shape": "ArchiveState", "documentation": "<p>The current state of the archive.</p>"}, "StateReason": {"shape": "ArchiveStateReason", "documentation": "<p>A description for the reason that the archive is in the current state.</p>"}, "RetentionDays": {"shape": "RetentionDays", "documentation": "<p>The number of days to retain events in the archive before they are deleted.</p>"}, "SizeBytes": {"shape": "<PERSON>", "documentation": "<p>The size of the archive, in bytes.</p>"}, "EventCount": {"shape": "<PERSON>", "documentation": "<p>The number of events in the archive.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>The time stamp for the time that the archive was created.</p>"}}, "documentation": "<p>An <code>Archive</code> object that contains details about an archive.</p>"}, "ArchiveArn": {"type": "string", "max": 1600, "min": 1, "pattern": "^arn:aws([a-z]|\\-)*:events:([a-z]|\\d|\\-)*:([0-9]{12})?:.+\\/.+$"}, "ArchiveDescription": {"type": "string", "max": 512, "pattern": ".*"}, "ArchiveName": {"type": "string", "max": 48, "min": 1, "pattern": "[\\.\\-_A-Za-z0-9]+"}, "ArchiveResponseList": {"type": "list", "member": {"shape": "Archive"}}, "ArchiveState": {"type": "string", "enum": ["ENABLED", "DISABLED", "CREATING", "UPDATING", "CREATE_FAILED", "UPDATE_FAILED"]}, "ArchiveStateReason": {"type": "string", "max": 512, "pattern": ".*"}, "Arn": {"type": "string", "max": 1600, "min": 1}, "AssignPublicIp": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "AuthHeaderParameters": {"type": "string", "max": 512, "min": 1, "pattern": "^[ \\t]*[^\\x00-\\x1F:\\x7F]+([ \\t]+[^\\x00-\\x1F:\\x7F]+)*[ \\t]*$"}, "AuthHeaderParametersSensitive": {"type": "string", "max": 512, "min": 1, "pattern": "^[ \\t]*[^\\x00-\\x1F:\\x7F]+([ \\t]+[^\\x00-\\x1F:\\x7F]+)*[ \\t]*$", "sensitive": true}, "AwsVpcConfiguration": {"type": "structure", "required": ["Subnets"], "members": {"Subnets": {"shape": "StringList", "documentation": "<p>Specifies the subnets associated with the task. These subnets must all be in the same VPC. You can specify as many as 16 subnets.</p>"}, "SecurityGroups": {"shape": "StringList", "documentation": "<p>Specifies the security groups associated with the task. These security groups must all be in the same VPC. You can specify as many as five security groups. If you do not specify a security group, the default security group for the VPC is used.</p>"}, "AssignPublicIp": {"shape": "AssignPublicIp", "documentation": "<p>Specifies whether the task's elastic network interface receives a public IP address. You can specify <code>ENABLED</code> only when <code>LaunchType</code> in <code>EcsParameters</code> is set to <code>FARGATE</code>.</p>"}}, "documentation": "<p>This structure specifies the VPC subnets and security groups for the task, and whether a public IP address is to be used. This structure is relevant only for ECS tasks that use the <code>awsvpc</code> network mode.</p>"}, "BatchArrayProperties": {"type": "structure", "members": {"Size": {"shape": "Integer", "documentation": "<p>The size of the array, if this is an array batch job. Valid values are integers between 2 and 10,000.</p>"}}, "documentation": "<p>The array properties for the submitted job, such as the size of the array. The array size can be between 2 and 10,000. If you specify array properties for a job, it becomes an array job. This parameter is used only if the target is an Batch job.</p>"}, "BatchParameters": {"type": "structure", "required": ["JobDefinition", "JobName"], "members": {"JobDefinition": {"shape": "String", "documentation": "<p>The ARN or name of the job definition to use if the event target is an Batch job. This job definition must already exist.</p>"}, "JobName": {"shape": "String", "documentation": "<p>The name to use for this execution of the job, if the target is an Batch job.</p>"}, "ArrayProperties": {"shape": "BatchArrayProperties", "documentation": "<p>The array properties for the submitted job, such as the size of the array. The array size can be between 2 and 10,000. If you specify array properties for a job, it becomes an array job. This parameter is used only if the target is an Batch job.</p>"}, "RetryStrategy": {"shape": "BatchRetryStrategy", "documentation": "<p>The retry strategy to use for failed jobs, if the target is an Batch job. The retry strategy is the number of times to retry the failed job execution. Valid values are 1–10. When you specify a retry strategy here, it overrides the retry strategy defined in the job definition.</p>"}}, "documentation": "<p>The custom parameters to be used when the target is an Batch job.</p>"}, "BatchRetryStrategy": {"type": "structure", "members": {"Attempts": {"shape": "Integer", "documentation": "<p>The number of times to attempt to retry, if the job fails. Valid values are 1–10.</p>"}}, "documentation": "<p>The retry strategy to use for failed jobs, if the target is an Batch job. If you specify a retry strategy here, it overrides the retry strategy defined in the job definition.</p>"}, "Boolean": {"type": "boolean"}, "CancelReplayRequest": {"type": "structure", "required": ["ReplayName"], "members": {"ReplayName": {"shape": "ReplayName", "documentation": "<p>The name of the replay to cancel.</p>"}}}, "CancelReplayResponse": {"type": "structure", "members": {"ReplayArn": {"shape": "ReplayArn", "documentation": "<p>The ARN of the replay to cancel.</p>"}, "State": {"shape": "ReplayState", "documentation": "<p>The current state of the replay.</p>"}, "StateReason": {"shape": "ReplayStateReason", "documentation": "<p>The reason that the replay is in the current state.</p>"}}}, "CapacityProvider": {"type": "string", "max": 255, "min": 1}, "CapacityProviderStrategy": {"type": "list", "member": {"shape": "CapacityProviderStrategyItem"}, "max": 6}, "CapacityProviderStrategyItem": {"type": "structure", "required": ["capacityProvider"], "members": {"capacityProvider": {"shape": "CapacityProvider", "documentation": "<p>The short name of the capacity provider.</p>"}, "weight": {"shape": "CapacityProviderStrategyItemWeight", "documentation": "<p>The weight value designates the relative percentage of the total number of tasks launched that should use the specified capacity provider. The weight value is taken into consideration after the base value, if defined, is satisfied.</p>"}, "base": {"shape": "CapacityProviderStrategyItemBase", "documentation": "<p>The base value designates how many tasks, at a minimum, to run on the specified capacity provider. Only one capacity provider in a capacity provider strategy can have a base defined. If no value is specified, the default value of 0 is used. </p>"}}, "documentation": "<p>The details of a capacity provider strategy. To learn more, see <a href=\"https://docs.aws.amazon.com/AmazonECS/latest/APIReference/API_CapacityProviderStrategyItem.html\">CapacityProviderStrategyItem</a> in the Amazon ECS API Reference.</p>"}, "CapacityProviderStrategyItemBase": {"type": "integer", "max": 100000, "min": 0}, "CapacityProviderStrategyItemWeight": {"type": "integer", "max": 1000, "min": 0}, "ConcurrentModificationException": {"type": "structure", "members": {}, "documentation": "<p>There is concurrent modification on a rule, target, archive, or replay.</p>", "exception": true}, "Condition": {"type": "structure", "required": ["Type", "Key", "Value"], "members": {"Type": {"shape": "String", "documentation": "<p>Specifies the type of condition. Currently the only supported value is <code>StringEquals</code>.</p>"}, "Key": {"shape": "String", "documentation": "<p>Specifies the key for the condition. Currently the only supported key is <code>aws:PrincipalOrgID</code>.</p>"}, "Value": {"shape": "String", "documentation": "<p>Specifies the value for the key. Currently, this must be the ID of the organization.</p>"}}, "documentation": "<p>A JSON string which you can use to limit the event bus permissions you are granting to only accounts that fulfill the condition. Currently, the only supported condition is membership in a certain Amazon Web Services organization. The string must contain <code>Type</code>, <code>Key</code>, and <code>Value</code> fields. The <code>Value</code> field specifies the ID of the Amazon Web Services organization. Following is an example value for <code>Condition</code>:</p> <p> <code>'{\"Type\" : \"StringEquals\", \"Key\": \"aws:PrincipalOrgID\", \"Value\": \"o-**********\"}'</code> </p>"}, "Connection": {"type": "structure", "members": {"ConnectionArn": {"shape": "ConnectionArn", "documentation": "<p>The ARN of the connection.</p>"}, "Name": {"shape": "ConnectionName", "documentation": "<p>The name of the connection.</p>"}, "ConnectionState": {"shape": "ConnectionState", "documentation": "<p>The state of the connection.</p>"}, "StateReason": {"shape": "ConnectionStateReason", "documentation": "<p>The reason that the connection is in the connection state.</p>"}, "AuthorizationType": {"shape": "ConnectionAuthorizationType", "documentation": "<p>The authorization type specified for the connection.</p> <note> <p>OAUTH tokens are refreshed when a 401 or 407 response is returned.</p> </note>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the connection was created.</p>"}, "LastModifiedTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the connection was last modified.</p>"}, "LastAuthorizedTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the connection was last authorized.</p>"}}, "documentation": "<p>Contains information about a connection.</p>"}, "ConnectionApiKeyAuthResponseParameters": {"type": "structure", "members": {"ApiKeyName": {"shape": "AuthHeaderParameters", "documentation": "<p>The name of the header to use for the <code>APIKeyValue</code> used for authorization.</p>"}}, "documentation": "<p>Contains the authorization parameters for the connection if API Key is specified as the authorization type.</p>"}, "ConnectionArn": {"type": "string", "max": 1600, "min": 1, "pattern": "^arn:aws([a-z]|\\-)*:events:([a-z]|\\d|\\-)*:([0-9]{12})?:connection\\/[\\.\\-_A-Za-z0-9]+\\/[\\-A-Za-z0-9]+$"}, "ConnectionAuthResponseParameters": {"type": "structure", "members": {"BasicAuthParameters": {"shape": "ConnectionBasicAuthResponseParameters", "documentation": "<p>The authorization parameters for Basic authorization.</p>"}, "OAuthParameters": {"shape": "ConnectionOAuthResponseParameters", "documentation": "<p>The OAuth parameters to use for authorization.</p>"}, "ApiKeyAuthParameters": {"shape": "ConnectionApiKeyAuthResponseParameters", "documentation": "<p>The API Key parameters to use for authorization.</p>"}, "InvocationHttpParameters": {"shape": "ConnectionHttpParameters", "documentation": "<p>Additional parameters for the connection that are passed through with every invocation to the HTTP endpoint.</p>"}}, "documentation": "<p>Contains the authorization parameters to use for the connection.</p>"}, "ConnectionAuthorizationType": {"type": "string", "enum": ["BASIC", "OAUTH_CLIENT_CREDENTIALS", "API_KEY"]}, "ConnectionBasicAuthResponseParameters": {"type": "structure", "members": {"Username": {"shape": "AuthHeaderParameters", "documentation": "<p>The user name to use for Basic authorization.</p>"}}, "documentation": "<p>Contains the authorization parameters for the connection if Basic is specified as the authorization type.</p>"}, "ConnectionBodyParameter": {"type": "structure", "members": {"Key": {"shape": "String", "documentation": "<p>The key for the parameter.</p>"}, "Value": {"shape": "SensitiveString", "documentation": "<p>The value associated with the key.</p>"}, "IsValueSecret": {"shape": "Boolean", "documentation": "<p>Specified whether the value is secret.</p>"}}, "documentation": "<p>Additional parameter included in the body. You can include up to 100 additional body parameters per request. An event payload cannot exceed 64 KB.</p>"}, "ConnectionBodyParametersList": {"type": "list", "member": {"shape": "ConnectionBodyParameter"}, "max": 100, "min": 0}, "ConnectionDescription": {"type": "string", "max": 512, "pattern": ".*"}, "ConnectionHeaderParameter": {"type": "structure", "members": {"Key": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The key for the parameter.</p>"}, "Value": {"shape": "HeaderValueSensitive", "documentation": "<p>The value associated with the key.</p>"}, "IsValueSecret": {"shape": "Boolean", "documentation": "<p>Specified whether the value is a secret.</p>"}}, "documentation": "<p>Additional parameter included in the header. You can include up to 100 additional header parameters per request. An event payload cannot exceed 64 KB.</p>"}, "ConnectionHeaderParametersList": {"type": "list", "member": {"shape": "ConnectionHeaderParameter"}, "max": 100, "min": 0}, "ConnectionHttpParameters": {"type": "structure", "members": {"HeaderParameters": {"shape": "ConnectionHeaderParametersList", "documentation": "<p>Contains additional header parameters for the connection.</p>"}, "QueryStringParameters": {"shape": "ConnectionQueryStringParametersList", "documentation": "<p>Contains additional query string parameters for the connection.</p>"}, "BodyParameters": {"shape": "ConnectionBodyParametersList", "documentation": "<p>Contains additional body string parameters for the connection.</p>"}}, "documentation": "<p>Contains additional parameters for the connection.</p>"}, "ConnectionName": {"type": "string", "max": 64, "min": 1, "pattern": "[\\.\\-_A-Za-z0-9]+"}, "ConnectionOAuthClientResponseParameters": {"type": "structure", "members": {"ClientID": {"shape": "AuthHeaderParameters", "documentation": "<p>The client ID associated with the response to the connection request.</p>"}}, "documentation": "<p>Contains the client response parameters for the connection when OAuth is specified as the authorization type.</p>"}, "ConnectionOAuthHttpMethod": {"type": "string", "enum": ["GET", "POST", "PUT"]}, "ConnectionOAuthResponseParameters": {"type": "structure", "members": {"ClientParameters": {"shape": "ConnectionOAuthClientResponseParameters", "documentation": "<p>A <code>ConnectionOAuthClientResponseParameters</code> object that contains details about the client parameters returned when OAuth is specified as the authorization type.</p>"}, "AuthorizationEndpoint": {"shape": "HttpsEndpoint", "documentation": "<p>The URL to the HTTP endpoint that authorized the request.</p>"}, "HttpMethod": {"shape": "ConnectionOAuthHttpMethod", "documentation": "<p>The method used to connect to the HTTP endpoint.</p>"}, "OAuthHttpParameters": {"shape": "ConnectionHttpParameters", "documentation": "<p>The additional HTTP parameters used for the OAuth authorization request.</p>"}}, "documentation": "<p>Contains the response parameters when OAuth is specified as the authorization type.</p>"}, "ConnectionQueryStringParameter": {"type": "structure", "members": {"Key": {"shape": "QueryString<PERSON>ey", "documentation": "<p>The key for a query string parameter.</p>"}, "Value": {"shape": "QueryStringValueSensitive", "documentation": "<p>The value associated with the key for the query string parameter.</p>"}, "IsValueSecret": {"shape": "Boolean", "documentation": "<p>Specifies whether the value is secret.</p>"}}, "documentation": "<p>Additional query string parameter for the connection. You can include up to 100 additional query string parameters per request. Each additional parameter counts towards the event payload size, which cannot exceed 64 KB.</p>"}, "ConnectionQueryStringParametersList": {"type": "list", "member": {"shape": "ConnectionQueryStringParameter"}, "max": 100, "min": 0}, "ConnectionResponseList": {"type": "list", "member": {"shape": "Connection"}}, "ConnectionState": {"type": "string", "enum": ["CREATING", "UPDATING", "DELETING", "AUTHORIZED", "DEAUTHORIZED", "AUTHORIZING", "DEAUTHORIZING"]}, "ConnectionStateReason": {"type": "string", "max": 512, "pattern": ".*"}, "CreateApiDestinationRequest": {"type": "structure", "required": ["Name", "ConnectionArn", "InvocationEndpoint", "HttpMethod"], "members": {"Name": {"shape": "ApiDestinationName", "documentation": "<p>The name for the API destination to create.</p>"}, "Description": {"shape": "ApiDestinationDescription", "documentation": "<p>A description for the API destination to create.</p>"}, "ConnectionArn": {"shape": "ConnectionArn", "documentation": "<p>The ARN of the connection to use for the API destination. The destination endpoint must support the authorization type specified for the connection.</p>"}, "InvocationEndpoint": {"shape": "HttpsEndpoint", "documentation": "<p>The URL to the HTTP invocation endpoint for the API destination.</p>"}, "HttpMethod": {"shape": "ApiDestinationHttpMethod", "documentation": "<p>The method to use for the request to the HTTP invocation endpoint.</p>"}, "InvocationRateLimitPerSecond": {"shape": "ApiDestinationInvocationRateLimitPerSecond", "documentation": "<p>The maximum number of requests per second to send to the HTTP invocation endpoint.</p>"}}}, "CreateApiDestinationResponse": {"type": "structure", "members": {"ApiDestinationArn": {"shape": "ApiDestinationArn", "documentation": "<p>The ARN of the API destination that was created by the request.</p>"}, "ApiDestinationState": {"shape": "ApiDestinationState", "documentation": "<p>The state of the API destination that was created by the request.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>A time stamp indicating the time that the API destination was created.</p>"}, "LastModifiedTime": {"shape": "Timestamp", "documentation": "<p>A time stamp indicating the time that the API destination was last modified.</p>"}}}, "CreateArchiveRequest": {"type": "structure", "required": ["ArchiveName", "EventSourceArn"], "members": {"ArchiveName": {"shape": "ArchiveName", "documentation": "<p>The name for the archive to create.</p>"}, "EventSourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the event bus that sends events to the archive.</p>"}, "Description": {"shape": "ArchiveDescription", "documentation": "<p>A description for the archive.</p>"}, "EventPattern": {"shape": "EventPattern", "documentation": "<p>An event pattern to use to filter events sent to the archive.</p>"}, "RetentionDays": {"shape": "RetentionDays", "documentation": "<p>The number of days to retain events for. Default value is 0. If set to 0, events are retained indefinitely</p>"}}}, "CreateArchiveResponse": {"type": "structure", "members": {"ArchiveArn": {"shape": "ArchiveArn", "documentation": "<p>The ARN of the archive that was created.</p>"}, "State": {"shape": "ArchiveState", "documentation": "<p>The state of the archive that was created.</p>"}, "StateReason": {"shape": "ArchiveStateReason", "documentation": "<p>The reason that the archive is in the state.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>The time at which the archive was created.</p>"}}}, "CreateConnectionApiKeyAuthRequestParameters": {"type": "structure", "required": ["ApiKeyName", "ApiKeyValue"], "members": {"ApiKeyName": {"shape": "AuthHeaderParameters", "documentation": "<p>The name of the API key to use for authorization.</p>"}, "ApiKeyValue": {"shape": "AuthHeaderParametersSensitive", "documentation": "<p>The value for the API key to use for authorization.</p>"}}, "documentation": "<p>Contains the API key authorization parameters for the connection.</p>"}, "CreateConnectionAuthRequestParameters": {"type": "structure", "members": {"BasicAuthParameters": {"shape": "CreateConnectionBasicAuthRequestParameters", "documentation": "<p>A <code>CreateConnectionBasicAuthRequestParameters</code> object that contains the Basic authorization parameters to use for the connection.</p>"}, "OAuthParameters": {"shape": "CreateConnectionOAuthRequestParameters", "documentation": "<p>A <code>CreateConnectionOAuthRequestParameters</code> object that contains the OAuth authorization parameters to use for the connection.</p>"}, "ApiKeyAuthParameters": {"shape": "CreateConnectionApiKeyAuthRequestParameters", "documentation": "<p>A <code>CreateConnectionApiKeyAuthRequestParameters</code> object that contains the API key authorization parameters to use for the connection.</p>"}, "InvocationHttpParameters": {"shape": "ConnectionHttpParameters", "documentation": "<p>A <code>ConnectionHttpParameters</code> object that contains the API key authorization parameters to use for the connection. Note that if you include additional parameters for the target of a rule via <code>HttpParameters</code>, including query strings, the parameters added for the connection take precedence.</p>"}}, "documentation": "<p>Contains the authorization parameters for the connection.</p>"}, "CreateConnectionBasicAuthRequestParameters": {"type": "structure", "required": ["Username", "Password"], "members": {"Username": {"shape": "AuthHeaderParameters", "documentation": "<p>The user name to use for Basic authorization.</p>"}, "Password": {"shape": "AuthHeaderParametersSensitive", "documentation": "<p>The password associated with the user name to use for Basic authorization.</p>"}}, "documentation": "<p>Contains the Basic authorization parameters to use for the connection.</p>"}, "CreateConnectionOAuthClientRequestParameters": {"type": "structure", "required": ["ClientID", "ClientSecret"], "members": {"ClientID": {"shape": "AuthHeaderParameters", "documentation": "<p>The client ID to use for OAuth authorization for the connection.</p>"}, "ClientSecret": {"shape": "AuthHeaderParametersSensitive", "documentation": "<p>The client secret associated with the client ID to use for OAuth authorization for the connection.</p>"}}, "documentation": "<p>Contains the Basic authorization parameters to use for the connection.</p>"}, "CreateConnectionOAuthRequestParameters": {"type": "structure", "required": ["ClientParameters", "AuthorizationEndpoint", "HttpMethod"], "members": {"ClientParameters": {"shape": "CreateConnectionOAuthClientRequestParameters", "documentation": "<p>A <code>CreateConnectionOAuthClientRequestParameters</code> object that contains the client parameters for OAuth authorization.</p>"}, "AuthorizationEndpoint": {"shape": "HttpsEndpoint", "documentation": "<p>The URL to the authorization endpoint when OAuth is specified as the authorization type.</p>"}, "HttpMethod": {"shape": "ConnectionOAuthHttpMethod", "documentation": "<p>The method to use for the authorization request.</p>"}, "OAuthHttpParameters": {"shape": "ConnectionHttpParameters", "documentation": "<p>A <code>ConnectionHttpParameters</code> object that contains details about the additional parameters to use for the connection.</p>"}}, "documentation": "<p>Contains the OAuth authorization parameters to use for the connection.</p>"}, "CreateConnectionRequest": {"type": "structure", "required": ["Name", "AuthorizationType", "AuthParameters"], "members": {"Name": {"shape": "ConnectionName", "documentation": "<p>The name for the connection to create.</p>"}, "Description": {"shape": "ConnectionDescription", "documentation": "<p>A description for the connection to create.</p>"}, "AuthorizationType": {"shape": "ConnectionAuthorizationType", "documentation": "<p>The type of authorization to use for the connection.</p> <note> <p>OAUTH tokens are refreshed when a 401 or 407 response is returned.</p> </note>"}, "AuthParameters": {"shape": "CreateConnectionAuthRequestParameters", "documentation": "<p>A <code>CreateConnectionAuthRequestParameters</code> object that contains the authorization parameters to use to authorize with the endpoint. </p>"}}}, "CreateConnectionResponse": {"type": "structure", "members": {"ConnectionArn": {"shape": "ConnectionArn", "documentation": "<p>The ARN of the connection that was created by the request.</p>"}, "ConnectionState": {"shape": "ConnectionState", "documentation": "<p>The state of the connection that was created by the request.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the connection was created.</p>"}, "LastModifiedTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the connection was last updated.</p>"}}}, "CreateEndpointRequest": {"type": "structure", "required": ["Name", "RoutingConfig", "EventBuses"], "members": {"Name": {"shape": "EndpointName", "documentation": "<p>The name of the global endpoint. For example, <code>\"Name\":\"us-east-2-custom_bus_A-endpoint\"</code>.</p>"}, "Description": {"shape": "EndpointDescription", "documentation": "<p>A description of the global endpoint.</p>"}, "RoutingConfig": {"shape": "RoutingConfig", "documentation": "<p>Configure the routing policy, including the health check and secondary Region..</p>"}, "ReplicationConfig": {"shape": "ReplicationConfig", "documentation": "<p>Enable or disable event replication. The default state is <code>ENABLED</code> which means you must supply a <code>RoleArn</code>. If you don't have a <code>RoleArn</code> or you don't want event replication enabled, set the state to <code>DISABLED</code>.</p>"}, "EventBuses": {"shape": "EndpointEventBusList", "documentation": "<p>Define the event buses used. </p> <important> <p>The names of the event buses must be identical in each Region.</p> </important>"}, "RoleArn": {"shape": "IamRoleArn", "documentation": "<p>The ARN of the role used for replication.</p>"}}}, "CreateEndpointResponse": {"type": "structure", "members": {"Name": {"shape": "EndpointName", "documentation": "<p>The name of the endpoint that was created by this request.</p>"}, "Arn": {"shape": "EndpointArn", "documentation": "<p>The ARN of the endpoint that was created by this request.</p>"}, "RoutingConfig": {"shape": "RoutingConfig", "documentation": "<p>The routing configuration defined by this request.</p>"}, "ReplicationConfig": {"shape": "ReplicationConfig", "documentation": "<p>Whether event replication was enabled or disabled by this request.</p>"}, "EventBuses": {"shape": "EndpointEventBusList", "documentation": "<p>The event buses used by this request.</p>"}, "RoleArn": {"shape": "IamRoleArn", "documentation": "<p>The ARN of the role used by event replication for this request.</p>"}, "State": {"shape": "EndpointState", "documentation": "<p>The state of the endpoint that was created by this request.</p>"}}}, "CreateEventBusRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "EventBusName", "documentation": "<p>The name of the new event bus. </p> <p>Custom event bus names can't contain the <code>/</code> character, but you can use the <code>/</code> character in partner event bus names. In addition, for partner event buses, the name must exactly match the name of the partner event source that this event bus is matched to.</p> <p>You can't use the name <code>default</code> for a custom event bus, as this name is already used for your account's default event bus.</p>"}, "EventSourceName": {"shape": "EventSourceName", "documentation": "<p>If you are creating a partner event bus, this specifies the partner event source that the new event bus will be matched with.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>Tags to associate with the event bus.</p>"}}}, "CreateEventBusResponse": {"type": "structure", "members": {"EventBusArn": {"shape": "String", "documentation": "<p>The ARN of the new event bus.</p>"}}}, "CreatePartnerEventSourceRequest": {"type": "structure", "required": ["Name", "Account"], "members": {"Name": {"shape": "EventSourceName", "documentation": "<p>The name of the partner event source. This name must be unique and must be in the format <code> <i>partner_name</i>/<i>event_namespace</i>/<i>event_name</i> </code>. The Amazon Web Services account that wants to use this partner event source must create a partner event bus with a name that matches the name of the partner event source.</p>"}, "Account": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID that is permitted to create a matching partner event bus for this partner event source.</p>"}}}, "CreatePartnerEventSourceResponse": {"type": "structure", "members": {"EventSourceArn": {"shape": "String", "documentation": "<p>The ARN of the partner event source.</p>"}}}, "CreatedBy": {"type": "string", "max": 128, "min": 1}, "Database": {"type": "string", "documentation": "Redshift Database", "max": 64, "min": 1}, "DbUser": {"type": "string", "documentation": "Database user name", "max": 128, "min": 1}, "DeactivateEventSourceRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "EventSourceName", "documentation": "<p>The name of the partner event source to deactivate.</p>"}}}, "DeadLetterConfig": {"type": "structure", "members": {"Arn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the SQS queue specified as the target for the dead-letter queue.</p>"}}, "documentation": "<p>A <code>DeadLetterConfig</code> object that contains information about a dead-letter queue configuration.</p>"}, "DeauthorizeConnectionRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "ConnectionName", "documentation": "<p>The name of the connection to remove authorization from.</p>"}}}, "DeauthorizeConnectionResponse": {"type": "structure", "members": {"ConnectionArn": {"shape": "ConnectionArn", "documentation": "<p>The ARN of the connection that authorization was removed from.</p>"}, "ConnectionState": {"shape": "ConnectionState", "documentation": "<p>The state of the connection.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the connection was created.</p>"}, "LastModifiedTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the connection was last updated.</p>"}, "LastAuthorizedTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the connection was last authorized.</p>"}}}, "DeleteApiDestinationRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "ApiDestinationName", "documentation": "<p>The name of the destination to delete.</p>"}}}, "DeleteApiDestinationResponse": {"type": "structure", "members": {}}, "DeleteArchiveRequest": {"type": "structure", "required": ["ArchiveName"], "members": {"ArchiveName": {"shape": "ArchiveName", "documentation": "<p>The name of the archive to delete.</p>"}}}, "DeleteArchiveResponse": {"type": "structure", "members": {}}, "DeleteConnectionRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "ConnectionName", "documentation": "<p>The name of the connection to delete.</p>"}}}, "DeleteConnectionResponse": {"type": "structure", "members": {"ConnectionArn": {"shape": "ConnectionArn", "documentation": "<p>The ARN of the connection that was deleted.</p>"}, "ConnectionState": {"shape": "ConnectionState", "documentation": "<p>The state of the connection before it was deleted.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the connection was created.</p>"}, "LastModifiedTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the connection was last modified before it was deleted.</p>"}, "LastAuthorizedTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the connection was last authorized before it wa deleted.</p>"}}}, "DeleteEndpointRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "EndpointName", "documentation": "<p>The name of the endpoint you want to delete. For example, <code>\"Name\":\"us-east-2-custom_bus_A-endpoint\"</code>..</p>"}}}, "DeleteEndpointResponse": {"type": "structure", "members": {}}, "DeleteEventBusRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "EventBusName", "documentation": "<p>The name of the event bus to delete.</p>"}}}, "DeletePartnerEventSourceRequest": {"type": "structure", "required": ["Name", "Account"], "members": {"Name": {"shape": "EventSourceName", "documentation": "<p>The name of the event source to delete.</p>"}, "Account": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the Amazon Web Services customer that the event source was created for.</p>"}}}, "DeleteRuleRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "RuleName", "documentation": "<p>The name of the rule.</p>"}, "EventBusName": {"shape": "EventBusNameOrArn", "documentation": "<p>The name or ARN of the event bus associated with the rule. If you omit this, the default event bus is used.</p>"}, "Force": {"shape": "Boolean", "documentation": "<p>If this is a managed rule, created by an Amazon Web Services service on your behalf, you must specify <code>Force</code> as <code>True</code> to delete the rule. This parameter is ignored for rules that are not managed rules. You can check whether a rule is a managed rule by using <code>DescribeRule</code> or <code>ListRules</code> and checking the <code>ManagedBy</code> field of the response.</p>"}}}, "DescribeApiDestinationRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "ApiDestinationName", "documentation": "<p>The name of the API destination to retrieve.</p>"}}}, "DescribeApiDestinationResponse": {"type": "structure", "members": {"ApiDestinationArn": {"shape": "ApiDestinationArn", "documentation": "<p>The ARN of the API destination retrieved.</p>"}, "Name": {"shape": "ApiDestinationName", "documentation": "<p>The name of the API destination retrieved.</p>"}, "Description": {"shape": "ApiDestinationDescription", "documentation": "<p>The description for the API destination retrieved.</p>"}, "ApiDestinationState": {"shape": "ApiDestinationState", "documentation": "<p>The state of the API destination retrieved.</p>"}, "ConnectionArn": {"shape": "ConnectionArn", "documentation": "<p>The ARN of the connection specified for the API destination retrieved.</p>"}, "InvocationEndpoint": {"shape": "HttpsEndpoint", "documentation": "<p>The URL to use to connect to the HTTP endpoint.</p>"}, "HttpMethod": {"shape": "ApiDestinationHttpMethod", "documentation": "<p>The method to use to connect to the HTTP endpoint.</p>"}, "InvocationRateLimitPerSecond": {"shape": "ApiDestinationInvocationRateLimitPerSecond", "documentation": "<p>The maximum number of invocations per second to specified for the API destination. Note that if you set the invocation rate maximum to a value lower the rate necessary to send all events received on to the destination HTTP endpoint, some events may not be delivered within the 24-hour retry window. If you plan to set the rate lower than the rate necessary to deliver all events, consider using a dead-letter queue to catch events that are not delivered within 24 hours.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the API destination was created.</p>"}, "LastModifiedTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the API destination was last modified.</p>"}}}, "DescribeArchiveRequest": {"type": "structure", "required": ["ArchiveName"], "members": {"ArchiveName": {"shape": "ArchiveName", "documentation": "<p>The name of the archive to retrieve.</p>"}}}, "DescribeArchiveResponse": {"type": "structure", "members": {"ArchiveArn": {"shape": "ArchiveArn", "documentation": "<p>The ARN of the archive.</p>"}, "ArchiveName": {"shape": "ArchiveName", "documentation": "<p>The name of the archive.</p>"}, "EventSourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the event source associated with the archive.</p>"}, "Description": {"shape": "ArchiveDescription", "documentation": "<p>The description of the archive.</p>"}, "EventPattern": {"shape": "EventPattern", "documentation": "<p>The event pattern used to filter events sent to the archive.</p>"}, "State": {"shape": "ArchiveState", "documentation": "<p>The state of the archive.</p>"}, "StateReason": {"shape": "ArchiveStateReason", "documentation": "<p>The reason that the archive is in the state.</p>"}, "RetentionDays": {"shape": "RetentionDays", "documentation": "<p>The number of days to retain events for in the archive.</p>"}, "SizeBytes": {"shape": "<PERSON>", "documentation": "<p>The size of the archive in bytes.</p>"}, "EventCount": {"shape": "<PERSON>", "documentation": "<p>The number of events in the archive.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>The time at which the archive was created.</p>"}}}, "DescribeConnectionRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "ConnectionName", "documentation": "<p>The name of the connection to retrieve.</p>"}}}, "DescribeConnectionResponse": {"type": "structure", "members": {"ConnectionArn": {"shape": "ConnectionArn", "documentation": "<p>The ARN of the connection retrieved.</p>"}, "Name": {"shape": "ConnectionName", "documentation": "<p>The name of the connection retrieved.</p>"}, "Description": {"shape": "ConnectionDescription", "documentation": "<p>The description for the connection retrieved.</p>"}, "ConnectionState": {"shape": "ConnectionState", "documentation": "<p>The state of the connection retrieved.</p>"}, "StateReason": {"shape": "ConnectionStateReason", "documentation": "<p>The reason that the connection is in the current connection state.</p>"}, "AuthorizationType": {"shape": "ConnectionAuthorizationType", "documentation": "<p>The type of authorization specified for the connection.</p>"}, "SecretArn": {"shape": "SecretsManagerSecretArn", "documentation": "<p>The ARN of the secret created from the authorization parameters specified for the connection.</p>"}, "AuthParameters": {"shape": "ConnectionAuthResponseParameters", "documentation": "<p>The parameters to use for authorization for the connection.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the connection was created.</p>"}, "LastModifiedTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the connection was last modified.</p>"}, "LastAuthorizedTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the connection was last authorized.</p>"}}}, "DescribeEndpointRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "EndpointName", "documentation": "<p>The name of the endpoint you want to get information about. For example, <code>\"Name\":\"us-east-2-custom_bus_A-endpoint\"</code>.</p>"}, "HomeRegion": {"shape": "HomeRegion", "documentation": "<p>The primary Region of the endpoint you want to get information about. For example <code>\"HomeRegion\": \"us-east-1\"</code>.</p>"}}}, "DescribeEndpointResponse": {"type": "structure", "members": {"Name": {"shape": "EndpointName", "documentation": "<p>The name of the endpoint you asked for information about.</p>"}, "Description": {"shape": "EndpointDescription", "documentation": "<p>The description of the endpoint you asked for information about.</p>"}, "Arn": {"shape": "EndpointArn", "documentation": "<p>The ARN of the endpoint you asked for information about.</p>"}, "RoutingConfig": {"shape": "RoutingConfig", "documentation": "<p>The routing configuration of the endpoint you asked for information about.</p>"}, "ReplicationConfig": {"shape": "ReplicationConfig", "documentation": "<p>Whether replication is enabled or disabled for the endpoint you asked for information about.</p>"}, "EventBuses": {"shape": "EndpointEventBusList", "documentation": "<p>The event buses being used by the endpoint you asked for information about.</p>"}, "RoleArn": {"shape": "IamRoleArn", "documentation": "<p>The ARN of the role used by the endpoint you asked for information about.</p>"}, "EndpointId": {"shape": "EndpointId", "documentation": "<p>The ID of the endpoint you asked for information about.</p>"}, "EndpointUrl": {"shape": "EndpointUrl", "documentation": "<p>The URL of the endpoint you asked for information about.</p>"}, "State": {"shape": "EndpointState", "documentation": "<p>The current state of the endpoint you asked for information about.</p>"}, "StateReason": {"shape": "EndpointStateReason", "documentation": "<p>The reason the endpoint you asked for information about is in its current state.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>The time the endpoint you asked for information about was created.</p>"}, "LastModifiedTime": {"shape": "Timestamp", "documentation": "<p>The last time the endpoint you asked for information about was modified.</p>"}}}, "DescribeEventBusRequest": {"type": "structure", "members": {"Name": {"shape": "EventBusNameOrArn", "documentation": "<p>The name or ARN of the event bus to show details for. If you omit this, the default event bus is displayed.</p>"}}}, "DescribeEventBusResponse": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>The name of the event bus. Currently, this is always <code>default</code>.</p>"}, "Arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the account permitted to write events to the current account.</p>"}, "Policy": {"shape": "String", "documentation": "<p>The policy that enables the external account to send events to your account.</p>"}}}, "DescribeEventSourceRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "EventSourceName", "documentation": "<p>The name of the partner event source to display the details of.</p>"}}}, "DescribeEventSourceResponse": {"type": "structure", "members": {"Arn": {"shape": "String", "documentation": "<p>The ARN of the partner event source.</p>"}, "CreatedBy": {"shape": "String", "documentation": "<p>The name of the SaaS partner that created the event source.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>The date and time that the event source was created.</p>"}, "ExpirationTime": {"shape": "Timestamp", "documentation": "<p>The date and time that the event source will expire if you do not create a matching event bus.</p>"}, "Name": {"shape": "String", "documentation": "<p>The name of the partner event source.</p>"}, "State": {"shape": "EventSourceState", "documentation": "<p>The state of the event source. If it is ACTIVE, you have already created a matching event bus for this event source, and that event bus is active. If it is PENDING, either you haven't yet created a matching event bus, or that event bus is deactivated. If it is DELETED, you have created a matching event bus, but the event source has since been deleted.</p>"}}}, "DescribePartnerEventSourceRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "EventSourceName", "documentation": "<p>The name of the event source to display.</p>"}}}, "DescribePartnerEventSourceResponse": {"type": "structure", "members": {"Arn": {"shape": "String", "documentation": "<p>The ARN of the event source.</p>"}, "Name": {"shape": "String", "documentation": "<p>The name of the event source.</p>"}}}, "DescribeReplayRequest": {"type": "structure", "required": ["ReplayName"], "members": {"ReplayName": {"shape": "ReplayName", "documentation": "<p>The name of the replay to retrieve.</p>"}}}, "DescribeReplayResponse": {"type": "structure", "members": {"ReplayName": {"shape": "ReplayName", "documentation": "<p>The name of the replay.</p>"}, "ReplayArn": {"shape": "ReplayArn", "documentation": "<p>The ARN of the replay.</p>"}, "Description": {"shape": "ReplayDescription", "documentation": "<p>The description of the replay.</p>"}, "State": {"shape": "ReplayState", "documentation": "<p>The current state of the replay.</p>"}, "StateReason": {"shape": "ReplayStateReason", "documentation": "<p>The reason that the replay is in the current state.</p>"}, "EventSourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the archive events were replayed from.</p>"}, "Destination": {"shape": "ReplayDestination", "documentation": "<p>A <code>ReplayDestination</code> object that contains details about the replay.</p>"}, "EventStartTime": {"shape": "Timestamp", "documentation": "<p>The time stamp of the first event that was last replayed from the archive.</p>"}, "EventEndTime": {"shape": "Timestamp", "documentation": "<p>The time stamp for the last event that was replayed from the archive.</p>"}, "EventLastReplayedTime": {"shape": "Timestamp", "documentation": "<p>The time that the event was last replayed.</p>"}, "ReplayStartTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the replay started.</p>"}, "ReplayEndTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the replay stopped.</p>"}}}, "DescribeRuleRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "RuleName", "documentation": "<p>The name of the rule.</p>"}, "EventBusName": {"shape": "EventBusNameOrArn", "documentation": "<p>The name or ARN of the event bus associated with the rule. If you omit this, the default event bus is used.</p>"}}}, "DescribeRuleResponse": {"type": "structure", "members": {"Name": {"shape": "RuleName", "documentation": "<p>The name of the rule.</p>"}, "Arn": {"shape": "RuleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the rule.</p>"}, "EventPattern": {"shape": "EventPattern", "documentation": "<p>The event pattern. For more information, see <a href=\"https://docs.aws.amazon.com/eventbridge/latest/userguide/eventbridge-and-event-patterns.html\">Events and Event Patterns</a> in the <i>Amazon EventBridge User Guide</i>.</p>"}, "ScheduleExpression": {"shape": "ScheduleExpression", "documentation": "<p>The scheduling expression. For example, \"cron(0 20 * * ? *)\", \"rate(5 minutes)\".</p>"}, "State": {"shape": "RuleState", "documentation": "<p>Specifies whether the rule is enabled or disabled.</p>"}, "Description": {"shape": "RuleDescription", "documentation": "<p>The description of the rule.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role associated with the rule.</p>"}, "ManagedBy": {"shape": "ManagedBy", "documentation": "<p>If this is a managed rule, created by an Amazon Web Services service on your behalf, this field displays the principal name of the Amazon Web Services service that created the rule.</p>"}, "EventBusName": {"shape": "EventBusName", "documentation": "<p>The name of the event bus associated with the rule.</p>"}, "CreatedBy": {"shape": "CreatedBy", "documentation": "<p>The account ID of the user that created the rule. If you use <code>PutRule</code> to put a rule on an event bus in another account, the other account is the owner of the rule, and the rule ARN includes the account ID for that account. However, the value for <code>CreatedBy</code> is the account ID as the account that created the rule in the other account.</p>"}}}, "DisableRuleRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "RuleName", "documentation": "<p>The name of the rule.</p>"}, "EventBusName": {"shape": "EventBusNameOrArn", "documentation": "<p>The name or ARN of the event bus associated with the rule. If you omit this, the default event bus is used.</p>"}}}, "EcsParameters": {"type": "structure", "required": ["TaskDefinitionArn"], "members": {"TaskDefinitionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the task definition to use if the event target is an Amazon ECS task. </p>"}, "TaskCount": {"shape": "LimitMin1", "documentation": "<p>The number of tasks to create based on <code>TaskDefinition</code>. The default is 1.</p>"}, "LaunchType": {"shape": "LaunchType", "documentation": "<p>Specifies the launch type on which your task is running. The launch type that you specify here must match one of the launch type (compatibilities) of the target task. The <code>FARGATE</code> value is supported only in the Regions where Fargate with Amazon ECS is supported. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonECS/latest/developerguide/AWS-Fargate.html\">Fargate on Amazon ECS</a> in the <i>Amazon Elastic Container Service Developer Guide</i>.</p>"}, "NetworkConfiguration": {"shape": "NetworkConfiguration", "documentation": "<p>Use this structure if the Amazon ECS task uses the <code>awsvpc</code> network mode. This structure specifies the VPC subnets and security groups associated with the task, and whether a public IP address is to be used. This structure is required if <code>LaunchType</code> is <code>FARGATE</code> because the <code>awsvpc</code> mode is required for Fargate tasks.</p> <p>If you specify <code>NetworkConfiguration</code> when the target ECS task does not use the <code>awsvpc</code> network mode, the task fails.</p>"}, "PlatformVersion": {"shape": "String", "documentation": "<p>Specifies the platform version for the task. Specify only the numeric portion of the platform version, such as <code>1.1.0</code>.</p> <p>This structure is used only if <code>LaunchType</code> is <code>FARGATE</code>. For more information about valid platform versions, see <a href=\"https://docs.aws.amazon.com/AmazonECS/latest/developerguide/platform_versions.html\">Fargate Platform Versions</a> in the <i>Amazon Elastic Container Service Developer Guide</i>.</p>"}, "Group": {"shape": "String", "documentation": "<p>Specifies an ECS task group for the task. The maximum length is 255 characters.</p>"}, "CapacityProviderStrategy": {"shape": "CapacityProviderStrategy", "documentation": "<p>The capacity provider strategy to use for the task.</p> <p>If a <code>capacityProviderStrategy</code> is specified, the <code>launchType</code> parameter must be omitted. If no <code>capacityProviderStrategy</code> or launchType is specified, the <code>defaultCapacityProviderStrategy</code> for the cluster is used. </p>"}, "EnableECSManagedTags": {"shape": "Boolean", "documentation": "<p>Specifies whether to enable Amazon ECS managed tags for the task. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonECS/latest/developerguide/ecs-using-tags.html\">Tagging Your Amazon ECS Resources</a> in the Amazon Elastic Container Service Developer Guide. </p>"}, "EnableExecuteCommand": {"shape": "Boolean", "documentation": "<p>Whether or not to enable the execute command functionality for the containers in this task. If true, this enables execute command functionality on all containers in the task.</p>"}, "PlacementConstraints": {"shape": "PlacementConstraints", "documentation": "<p>An array of placement constraint objects to use for the task. You can specify up to 10 constraints per task (including constraints in the task definition and those specified at runtime).</p>"}, "PlacementStrategy": {"shape": "PlacementStrategies", "documentation": "<p>The placement strategy objects to use for the task. You can specify a maximum of five strategy rules per task. </p>"}, "PropagateTags": {"shape": "PropagateTags", "documentation": "<p>Specifies whether to propagate the tags from the task definition to the task. If no value is specified, the tags are not propagated. Tags can only be propagated to the task during task creation. To add tags to a task after task creation, use the TagResource API action. </p>"}, "ReferenceId": {"shape": "ReferenceId", "documentation": "<p>The reference ID to use for the task.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The metadata that you apply to the task to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define. To learn more, see <a href=\"https://docs.aws.amazon.com/AmazonECS/latest/APIReference/API_RunTask.html#ECS-RunTask-request-tags\">RunTask</a> in the Amazon ECS API Reference.</p>"}}, "documentation": "<p>The custom parameters to be used when the target is an Amazon ECS task.</p>"}, "EnableRuleRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "RuleName", "documentation": "<p>The name of the rule.</p>"}, "EventBusName": {"shape": "EventBusNameOrArn", "documentation": "<p>The name or ARN of the event bus associated with the rule. If you omit this, the default event bus is used.</p>"}}}, "Endpoint": {"type": "structure", "members": {"Name": {"shape": "EndpointName", "documentation": "<p>The name of the endpoint.</p>"}, "Description": {"shape": "EndpointDescription", "documentation": "<p>A description for the endpoint.</p>"}, "Arn": {"shape": "EndpointArn", "documentation": "<p>The ARN of the endpoint.</p>"}, "RoutingConfig": {"shape": "RoutingConfig", "documentation": "<p>The routing configuration of the endpoint.</p>"}, "ReplicationConfig": {"shape": "ReplicationConfig", "documentation": "<p>Whether event replication was enabled or disabled for this endpoint. The default state is <code>ENABLED</code> which means you must supply a <code>RoleArn</code>. If you don't have a <code>RoleArn</code> or you don't want event replication enabled, set the state to <code>DISABLED</code>.</p>"}, "EventBuses": {"shape": "EndpointEventBusList", "documentation": "<p>The event buses being used by the endpoint.</p>"}, "RoleArn": {"shape": "IamRoleArn", "documentation": "<p>The ARN of the role used by event replication for the endpoint.</p>"}, "EndpointId": {"shape": "EndpointId", "documentation": "<p>The URL subdomain of the endpoint. For example, if the URL for Endpoint is https://abcde.veo.endpoints.event.amazonaws.com, then the EndpointId is <code>abcde.veo</code>.</p>"}, "EndpointUrl": {"shape": "EndpointUrl", "documentation": "<p>The URL of the endpoint.</p>"}, "State": {"shape": "EndpointState", "documentation": "<p>The current state of the endpoint.</p>"}, "StateReason": {"shape": "EndpointStateReason", "documentation": "<p>The reason the endpoint is in its current state.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>The time the endpoint was created.</p>"}, "LastModifiedTime": {"shape": "Timestamp", "documentation": "<p>The last time the endpoint was modified.</p>"}}, "documentation": "<p>A global endpoint used to improve your application's availability by making it regional-fault tolerant. For more information about global endpoints, see <a href=\"https://docs.aws.amazon.com/eventbridge/latest/userguide/eb-global-endpoints.html\">Making applications Regional-fault tolerant with global endpoints and event replication</a> in the Amazon EventBridge User Guide.</p>"}, "EndpointArn": {"type": "string", "max": 1600, "min": 1, "pattern": "^arn:aws([a-z]|\\-)*:events:([a-z]|\\d|\\-)*:([0-9]{12})?:endpoint\\/[/\\.\\-_A-Za-z0-9]+$"}, "EndpointDescription": {"type": "string", "max": 512, "pattern": ".*"}, "EndpointEventBus": {"type": "structure", "required": ["EventBusArn"], "members": {"EventBusArn": {"shape": "NonPartnerEventBusArn", "documentation": "<p>The ARN of the event bus the endpoint is associated with.</p>"}}, "documentation": "<p>The event buses the endpoint is associated with.</p>"}, "EndpointEventBusList": {"type": "list", "member": {"shape": "EndpointEventBus"}, "max": 2, "min": 2}, "EndpointId": {"type": "string", "max": 50, "min": 1, "pattern": "^[A-Za-z0-9\\-]+[\\.][A-Za-z0-9\\-]+$"}, "EndpointList": {"type": "list", "member": {"shape": "Endpoint"}}, "EndpointName": {"type": "string", "max": 64, "min": 1, "pattern": "[\\.\\-_A-Za-z0-9]+"}, "EndpointState": {"type": "string", "enum": ["ACTIVE", "CREATING", "UPDATING", "DELETING", "CREATE_FAILED", "UPDATE_FAILED", "DELETE_FAILED"]}, "EndpointStateReason": {"type": "string", "max": 512, "min": 1, "pattern": ".*"}, "EndpointUrl": {"type": "string", "max": 256, "min": 1, "pattern": "^(https://)?[\\.\\-a-z0-9]+$"}, "ErrorCode": {"type": "string"}, "ErrorMessage": {"type": "string"}, "EventBus": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>The name of the event bus.</p>"}, "Arn": {"shape": "String", "documentation": "<p>The ARN of the event bus.</p>"}, "Policy": {"shape": "String", "documentation": "<p>The permissions policy of the event bus, describing which other Amazon Web Services accounts can write events to this event bus.</p>"}}, "documentation": "<p>An event bus receives events from a source, uses rules to evaluate them, applies any configured input transformation, and routes them to the appropriate target(s). Your account's default event bus receives events from Amazon Web Services services. A custom event bus can receive events from your custom applications and services. A partner event bus receives events from an event source created by an SaaS partner. These events come from the partners services or applications.</p>"}, "EventBusList": {"type": "list", "member": {"shape": "EventBus"}}, "EventBusName": {"type": "string", "max": 256, "min": 1, "pattern": "[/\\.\\-_A-Za-z0-9]+"}, "EventBusNameOrArn": {"type": "string", "max": 1600, "min": 1, "pattern": "(arn:aws[\\w-]*:events:[a-z]{2}-[a-z]+-[\\w-]+:[0-9]{12}:event-bus\\/)?[/\\.\\-_A-Za-z0-9]+"}, "EventId": {"type": "string", "max": 64}, "EventPattern": {"type": "string", "max": 4096}, "EventResource": {"type": "string", "max": 2048}, "EventResourceList": {"type": "list", "member": {"shape": "EventResource"}}, "EventSource": {"type": "structure", "members": {"Arn": {"shape": "String", "documentation": "<p>The ARN of the event source.</p>"}, "CreatedBy": {"shape": "String", "documentation": "<p>The name of the partner that created the event source.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>The date and time the event source was created.</p>"}, "ExpirationTime": {"shape": "Timestamp", "documentation": "<p>The date and time that the event source will expire, if the Amazon Web Services account doesn't create a matching event bus for it.</p>"}, "Name": {"shape": "String", "documentation": "<p>The name of the event source.</p>"}, "State": {"shape": "EventSourceState", "documentation": "<p>The state of the event source. If it is ACTIVE, you have already created a matching event bus for this event source, and that event bus is active. If it is PENDING, either you haven't yet created a matching event bus, or that event bus is deactivated. If it is DELETED, you have created a matching event bus, but the event source has since been deleted.</p>"}}, "documentation": "<p>A partner event source is created by an SaaS partner. If a customer creates a partner event bus that matches this event source, that Amazon Web Services account can receive events from the partner's applications or services.</p>"}, "EventSourceList": {"type": "list", "member": {"shape": "EventSource"}}, "EventSourceName": {"type": "string", "max": 256, "min": 1, "pattern": "aws\\.partner(/[\\.\\-_A-Za-z0-9]+){2,}"}, "EventSourceNamePrefix": {"type": "string", "max": 256, "min": 1, "pattern": "[/\\.\\-_A-Za-z0-9]+"}, "EventSourceState": {"type": "string", "enum": ["PENDING", "ACTIVE", "DELETED"]}, "EventTime": {"type": "timestamp"}, "FailoverConfig": {"type": "structure", "required": ["Primary", "Secondary"], "members": {"Primary": {"shape": "Primary", "documentation": "<p>The main Region of the endpoint.</p>"}, "Secondary": {"shape": "Secondary", "documentation": "<p>The Region that events are routed to when failover is triggered or event replication is enabled.</p>"}}, "documentation": "<p>The failover configuration for an endpoint. This includes what triggers failover and what happens when it's triggered.</p>"}, "HeaderKey": {"type": "string", "max": 512, "pattern": "^[!#$%&'*+-.^_`|~0-9a-zA-Z]+$"}, "HeaderParametersMap": {"type": "map", "key": {"shape": "<PERSON><PERSON><PERSON><PERSON>"}, "value": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "HeaderValue": {"type": "string", "max": 512, "pattern": "^[ \\t]*[\\x20-\\x7E]+([ \\t]+[\\x20-\\x7E]+)*[ \\t]*$"}, "HeaderValueSensitive": {"type": "string", "max": 512, "pattern": "^[ \\t]*[\\x20-\\x7E]+([ \\t]+[\\x20-\\x7E]+)*[ \\t]*$", "sensitive": true}, "HealthCheck": {"type": "string", "max": 1600, "min": 1, "pattern": "^arn:aws([a-z]|\\-)*:route53:::healthcheck/[\\-a-z0-9]+$"}, "HomeRegion": {"type": "string", "max": 20, "min": 9, "pattern": "^[\\-a-z0-9]+$"}, "HttpParameters": {"type": "structure", "members": {"PathParameterValues": {"shape": "PathParameterList", "documentation": "<p>The path parameter values to be used to populate API Gateway API or EventBridge ApiDestination path wildcards (\"*\").</p>"}, "HeaderParameters": {"shape": "HeaderParametersMap", "documentation": "<p>The headers that need to be sent as part of request invoking the API Gateway API or EventBridge ApiDestination.</p>"}, "QueryStringParameters": {"shape": "QueryStringParametersMap", "documentation": "<p>The query string keys/values that need to be sent as part of request invoking the API Gateway API or EventBridge ApiDestination.</p>"}}, "documentation": "<p>These are custom parameter to be used when the target is an API Gateway APIs or EventBridge ApiDestinations. In the latter case, these are merged with any InvocationParameters specified on the Connection, with any values from the Connection taking precedence.</p>"}, "HttpsEndpoint": {"type": "string", "max": 2048, "min": 1, "pattern": "^((%[0-9A-Fa-f]{2}|[-()_.!~*';/?:@\\x26=+$,A-Za-z0-9])+)([).!';/?:,])?$"}, "IamRoleArn": {"type": "string", "max": 256, "min": 1, "pattern": "^arn:aws[a-z-]*:iam::\\d{12}:role\\/[\\w+=,.@/-]+$"}, "IllegalStatusException": {"type": "structure", "members": {}, "documentation": "<p>An error occurred because a replay can be canceled only when the state is Running or Starting.</p>", "exception": true}, "InputTransformer": {"type": "structure", "required": ["InputTemplate"], "members": {"InputPathsMap": {"shape": "TransformerPaths", "documentation": "<p>Map of JSON paths to be extracted from the event. You can then insert these in the template in <code>InputTemplate</code> to produce the output you want to be sent to the target.</p> <p> <code>InputPathsMap</code> is an array key-value pairs, where each value is a valid JSON path. You can have as many as 100 key-value pairs. You must use JSON dot notation, not bracket notation.</p> <p>The keys cannot start with \"Amazon Web Services.\" </p>"}, "InputTemplate": {"shape": "TransformerInput", "documentation": "<p>Input template where you specify placeholders that will be filled with the values of the keys from <code>InputPathsMap</code> to customize the data sent to the target. Enclose each <code>InputPathsMaps</code> value in brackets: &lt;<i>value</i>&gt; </p> <p>If <code>InputTemplate</code> is a JSON object (surrounded by curly braces), the following restrictions apply:</p> <ul> <li> <p>The placeholder cannot be used as an object key.</p> </li> </ul> <p>The following example shows the syntax for using <code>InputPathsMap</code> and <code>InputTemplate</code>.</p> <p> <code> \"InputTransformer\":</code> </p> <p> <code>{</code> </p> <p> <code>\"InputPathsMap\": {\"instance\": \"$.detail.instance\",\"status\": \"$.detail.status\"},</code> </p> <p> <code>\"InputTemplate\": \"&lt;instance&gt; is in state &lt;status&gt;\"</code> </p> <p> <code>}</code> </p> <p>To have the <code>InputTemplate</code> include quote marks within a JSON string, escape each quote marks with a slash, as in the following example:</p> <p> <code> \"InputTransformer\":</code> </p> <p> <code>{</code> </p> <p> <code>\"InputPathsMap\": {\"instance\": \"$.detail.instance\",\"status\": \"$.detail.status\"},</code> </p> <p> <code>\"InputTemplate\": \"&lt;instance&gt; is in state \\\"&lt;status&gt;\\\"\"</code> </p> <p> <code>}</code> </p> <p>The <code>InputTemplate</code> can also be valid JSON with varibles in quotes or out, as in the following example:</p> <p> <code> \"InputTransformer\":</code> </p> <p> <code>{</code> </p> <p> <code>\"InputPathsMap\": {\"instance\": \"$.detail.instance\",\"status\": \"$.detail.status\"},</code> </p> <p> <code>\"InputTemplate\": '{\"myInstance\": &lt;instance&gt;,\"myStatus\": \"&lt;instance&gt; is in state \\\"&lt;status&gt;\\\"\"}'</code> </p> <p> <code>}</code> </p>"}}, "documentation": "<p>Contains the parameters needed for you to provide custom input to a target based on one or more pieces of data extracted from the event.</p>"}, "InputTransformerPathKey": {"type": "string", "max": 256, "min": 1, "pattern": "[A-Za-z0-9\\_\\-]+"}, "Integer": {"type": "integer"}, "InternalException": {"type": "structure", "members": {}, "documentation": "<p>This exception occurs due to unexpected causes.</p>", "exception": true, "fault": true}, "InvalidEventPatternException": {"type": "structure", "members": {}, "documentation": "<p>The event pattern is not valid.</p>", "exception": true}, "InvalidStateException": {"type": "structure", "members": {}, "documentation": "<p>The specified state is not a valid state for an event source.</p>", "exception": true}, "KinesisParameters": {"type": "structure", "required": ["PartitionKeyPath"], "members": {"PartitionKeyPath": {"shape": "TargetPartitionKeyPath", "documentation": "<p>The JSON path to be extracted from the event and used as the partition key. For more information, see <a href=\"https://docs.aws.amazon.com/streams/latest/dev/key-concepts.html#partition-key\">Amazon Kinesis Streams Key Concepts</a> in the <i>Amazon Kinesis Streams Developer Guide</i>.</p>"}}, "documentation": "<p>This object enables you to specify a JSON path to extract from the event and use as the partition key for the Amazon Kinesis data stream, so that you can control the shard to which the event goes. If you do not include this parameter, the default is to use the <code>eventId</code> as the partition key.</p>"}, "LaunchType": {"type": "string", "enum": ["EC2", "FARGATE", "EXTERNAL"]}, "LimitExceededException": {"type": "structure", "members": {}, "documentation": "<p>The request failed because it attempted to create resource beyond the allowed service quota.</p>", "exception": true}, "LimitMax100": {"type": "integer", "max": 100, "min": 1}, "LimitMin1": {"type": "integer", "min": 1}, "ListApiDestinationsRequest": {"type": "structure", "members": {"NamePrefix": {"shape": "ApiDestinationName", "documentation": "<p>A name prefix to filter results returned. Only API destinations with a name that starts with the prefix are returned.</p>"}, "ConnectionArn": {"shape": "ConnectionArn", "documentation": "<p>The ARN of the connection specified for the API destination.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token returned by a previous call to retrieve the next set of results.</p>"}, "Limit": {"shape": "LimitMax100", "documentation": "<p>The maximum number of API destinations to include in the response.</p>"}}}, "ListApiDestinationsResponse": {"type": "structure", "members": {"ApiDestinations": {"shape": "ApiDestinationResponseList", "documentation": "<p>An array of <code>ApiDestination</code> objects that include information about an API destination.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A token you can use in a subsequent request to retrieve the next set of results.</p>"}}}, "ListArchivesRequest": {"type": "structure", "members": {"NamePrefix": {"shape": "ArchiveName", "documentation": "<p>A name prefix to filter the archives returned. Only archives with name that match the prefix are returned.</p>"}, "EventSourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the event source associated with the archive.</p>"}, "State": {"shape": "ArchiveState", "documentation": "<p>The state of the archive.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token returned by a previous call to retrieve the next set of results.</p>"}, "Limit": {"shape": "LimitMax100", "documentation": "<p>The maximum number of results to return.</p>"}}}, "ListArchivesResponse": {"type": "structure", "members": {"Archives": {"shape": "ArchiveResponseList", "documentation": "<p>An array of <code>Archive</code> objects that include details about an archive.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token returned by a previous call to retrieve the next set of results.</p>"}}}, "ListConnectionsRequest": {"type": "structure", "members": {"NamePrefix": {"shape": "ConnectionName", "documentation": "<p>A name prefix to filter results returned. Only connections with a name that starts with the prefix are returned.</p>"}, "ConnectionState": {"shape": "ConnectionState", "documentation": "<p>The state of the connection.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token returned by a previous call to retrieve the next set of results.</p>"}, "Limit": {"shape": "LimitMax100", "documentation": "<p>The maximum number of connections to return.</p>"}}}, "ListConnectionsResponse": {"type": "structure", "members": {"Connections": {"shape": "ConnectionResponseList", "documentation": "<p>An array of connections objects that include details about the connections.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A token you can use in a subsequent request to retrieve the next set of results.</p>"}}}, "ListEndpointsRequest": {"type": "structure", "members": {"NamePrefix": {"shape": "EndpointName", "documentation": "<p>A value that will return a subset of the endpoints associated with this account. For example, <code>\"NamePrefix\": \"ABC\"</code> will return all endpoints with \"ABC\" in the name.</p>"}, "HomeRegion": {"shape": "HomeRegion", "documentation": "<p>The primary Region of the endpoints associated with this account. For example <code>\"HomeRegion\": \"us-east-1\"</code>.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return an HTTP 400 InvalidToken error.</p>"}, "MaxResults": {"shape": "LimitMax100", "documentation": "<p>The maximum number of results returned by the call.</p>"}}}, "ListEndpointsResponse": {"type": "structure", "members": {"Endpoints": {"shape": "EndpointList", "documentation": "<p>The endpoints returned by the call.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return an HTTP 400 InvalidToken error.</p>"}}}, "ListEventBusesRequest": {"type": "structure", "members": {"NamePrefix": {"shape": "EventBusName", "documentation": "<p>Specifying this limits the results to only those event buses with names that start with the specified prefix.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token returned by a previous call to retrieve the next set of results.</p>"}, "Limit": {"shape": "LimitMax100", "documentation": "<p>Specifying this limits the number of results returned by this operation. The operation also returns a NextToken which you can use in a subsequent operation to retrieve the next set of results.</p>"}}}, "ListEventBusesResponse": {"type": "structure", "members": {"EventBuses": {"shape": "EventBusList", "documentation": "<p>This list of event buses.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A token you can use in a subsequent operation to retrieve the next set of results.</p>"}}}, "ListEventSourcesRequest": {"type": "structure", "members": {"NamePrefix": {"shape": "EventSourceNamePrefix", "documentation": "<p>Specifying this limits the results to only those partner event sources with names that start with the specified prefix.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token returned by a previous call to retrieve the next set of results.</p>"}, "Limit": {"shape": "LimitMax100", "documentation": "<p>Specifying this limits the number of results returned by this operation. The operation also returns a NextToken which you can use in a subsequent operation to retrieve the next set of results.</p>"}}}, "ListEventSourcesResponse": {"type": "structure", "members": {"EventSources": {"shape": "EventSourceList", "documentation": "<p>The list of event sources.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A token you can use in a subsequent operation to retrieve the next set of results.</p>"}}}, "ListPartnerEventSourceAccountsRequest": {"type": "structure", "required": ["EventSourceName"], "members": {"EventSourceName": {"shape": "EventSourceName", "documentation": "<p>The name of the partner event source to display account information about.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token returned by a previous call to this operation. Specifying this retrieves the next set of results.</p>"}, "Limit": {"shape": "LimitMax100", "documentation": "<p>Specifying this limits the number of results returned by this operation. The operation also returns a NextToken which you can use in a subsequent operation to retrieve the next set of results.</p>"}}}, "ListPartnerEventSourceAccountsResponse": {"type": "structure", "members": {"PartnerEventSourceAccounts": {"shape": "PartnerEventSourceAccountList", "documentation": "<p>The list of partner event sources returned by the operation.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A token you can use in a subsequent operation to retrieve the next set of results.</p>"}}}, "ListPartnerEventSourcesRequest": {"type": "structure", "required": ["NamePrefix"], "members": {"NamePrefix": {"shape": "PartnerEventSourceNamePrefix", "documentation": "<p>If you specify this, the results are limited to only those partner event sources that start with the string you specify.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token returned by a previous call to this operation. Specifying this retrieves the next set of results.</p>"}, "Limit": {"shape": "LimitMax100", "documentation": "<p>pecifying this limits the number of results returned by this operation. The operation also returns a NextToken which you can use in a subsequent operation to retrieve the next set of results.</p>"}}}, "ListPartnerEventSourcesResponse": {"type": "structure", "members": {"PartnerEventSources": {"shape": "PartnerEventSourceList", "documentation": "<p>The list of partner event sources returned by the operation.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A token you can use in a subsequent operation to retrieve the next set of results.</p>"}}}, "ListReplaysRequest": {"type": "structure", "members": {"NamePrefix": {"shape": "ReplayName", "documentation": "<p>A name prefix to filter the replays returned. Only replays with name that match the prefix are returned.</p>"}, "State": {"shape": "ReplayState", "documentation": "<p>The state of the replay.</p>"}, "EventSourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the archive from which the events are replayed.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token returned by a previous call to retrieve the next set of results.</p>"}, "Limit": {"shape": "LimitMax100", "documentation": "<p>The maximum number of replays to retrieve.</p>"}}}, "ListReplaysResponse": {"type": "structure", "members": {"Replays": {"shape": "ReplayList", "documentation": "<p>An array of <code>Replay</code> objects that contain information about the replay.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token returned by a previous call to retrieve the next set of results.</p>"}}}, "ListRuleNamesByTargetRequest": {"type": "structure", "required": ["TargetArn"], "members": {"TargetArn": {"shape": "TargetArn", "documentation": "<p>The Amazon Resource Name (ARN) of the target resource.</p>"}, "EventBusName": {"shape": "EventBusNameOrArn", "documentation": "<p>The name or ARN of the event bus to list rules for. If you omit this, the default event bus is used.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token returned by a previous call to retrieve the next set of results.</p>"}, "Limit": {"shape": "LimitMax100", "documentation": "<p>The maximum number of results to return.</p>"}}}, "ListRuleNamesByTargetResponse": {"type": "structure", "members": {"RuleNames": {"shape": "RuleNameList", "documentation": "<p>The names of the rules that can invoke the given target.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Indicates whether there are additional results to retrieve. If there are no more results, the value is null.</p>"}}}, "ListRulesRequest": {"type": "structure", "members": {"NamePrefix": {"shape": "RuleName", "documentation": "<p>The prefix matching the rule name.</p>"}, "EventBusName": {"shape": "EventBusNameOrArn", "documentation": "<p>The name or ARN of the event bus to list the rules for. If you omit this, the default event bus is used.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token returned by a previous call to retrieve the next set of results.</p>"}, "Limit": {"shape": "LimitMax100", "documentation": "<p>The maximum number of results to return.</p>"}}}, "ListRulesResponse": {"type": "structure", "members": {"Rules": {"shape": "RuleResponseList", "documentation": "<p>The rules that match the specified criteria.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Indicates whether there are additional results to retrieve. If there are no more results, the value is null.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the EventBridge resource for which you want to view tags.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "TagList", "documentation": "<p>The list of tag keys and values associated with the resource you specified</p>"}}}, "ListTargetsByRuleRequest": {"type": "structure", "required": ["Rule"], "members": {"Rule": {"shape": "RuleName", "documentation": "<p>The name of the rule.</p>"}, "EventBusName": {"shape": "EventBusNameOrArn", "documentation": "<p>The name or ARN of the event bus associated with the rule. If you omit this, the default event bus is used.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token returned by a previous call to retrieve the next set of results.</p>"}, "Limit": {"shape": "LimitMax100", "documentation": "<p>The maximum number of results to return.</p>"}}}, "ListTargetsByRuleResponse": {"type": "structure", "members": {"Targets": {"shape": "TargetList", "documentation": "<p>The targets assigned to the rule.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Indicates whether there are additional results to retrieve. If there are no more results, the value is null.</p>"}}}, "Long": {"type": "long"}, "ManagedBy": {"type": "string", "max": 128, "min": 1}, "ManagedRuleException": {"type": "structure", "members": {}, "documentation": "<p>This rule was created by an Amazon Web Services service on behalf of your account. It is managed by that service. If you see this error in response to <code>DeleteRule</code> or <code>RemoveTargets</code>, you can use the <code>Force</code> parameter in those calls to delete the rule or remove targets from the rule. You cannot modify these managed rules by using <code>DisableRule</code>, <code>EnableRule</code>, <code>PutTargets</code>, <code>PutRule</code>, <code>TagResource</code>, or <code>UntagResource</code>. </p>", "exception": true}, "MaximumEventAgeInSeconds": {"type": "integer", "max": 86400, "min": 60}, "MaximumRetryAttempts": {"type": "integer", "max": 185, "min": 0}, "MessageGroupId": {"type": "string", "max": 100}, "NetworkConfiguration": {"type": "structure", "members": {"awsvpcConfiguration": {"shape": "AwsVpcConfiguration", "documentation": "<p>Use this structure to specify the VPC subnets and security groups for the task, and whether a public IP address is to be used. This structure is relevant only for ECS tasks that use the <code>awsvpc</code> network mode.</p>"}}, "documentation": "<p>This structure specifies the network configuration for an ECS task.</p>"}, "NextToken": {"type": "string", "max": 2048, "min": 1}, "NonPartnerEventBusArn": {"type": "string", "max": 512, "min": 1, "pattern": "^arn:aws[a-z-]*:events:[a-z]{2}-[a-z-]+-\\d+:\\d{12}:event-bus/[\\w.-]+$"}, "NonPartnerEventBusName": {"type": "string", "max": 256, "min": 1, "pattern": "[\\.\\-_A-Za-z0-9]+"}, "NonPartnerEventBusNameOrArn": {"type": "string", "max": 1600, "min": 1, "pattern": "(arn:aws[\\w-]*:events:[a-z]{2}-[a-z]+-[\\w-]+:[0-9]{12}:event-bus\\/)?[\\.\\-_A-Za-z0-9]+"}, "OperationDisabledException": {"type": "structure", "members": {}, "documentation": "<p>The operation you are attempting is not available in this region.</p>", "exception": true}, "PartnerEventSource": {"type": "structure", "members": {"Arn": {"shape": "String", "documentation": "<p>The ARN of the partner event source.</p>"}, "Name": {"shape": "String", "documentation": "<p>The name of the partner event source.</p>"}}, "documentation": "<p>A partner event source is created by an SaaS partner. If a customer creates a partner event bus that matches this event source, that Amazon Web Services account can receive events from the partner's applications or services.</p>"}, "PartnerEventSourceAccount": {"type": "structure", "members": {"Account": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID that the partner event source was offered to.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>The date and time the event source was created.</p>"}, "ExpirationTime": {"shape": "Timestamp", "documentation": "<p>The date and time that the event source will expire, if the Amazon Web Services account doesn't create a matching event bus for it.</p>"}, "State": {"shape": "EventSourceState", "documentation": "<p>The state of the event source. If it is ACTIVE, you have already created a matching event bus for this event source, and that event bus is active. If it is PENDING, either you haven't yet created a matching event bus, or that event bus is deactivated. If it is DELETED, you have created a matching event bus, but the event source has since been deleted.</p>"}}, "documentation": "<p>The Amazon Web Services account that a partner event source has been offered to.</p>"}, "PartnerEventSourceAccountList": {"type": "list", "member": {"shape": "PartnerEventSourceAccount"}}, "PartnerEventSourceList": {"type": "list", "member": {"shape": "PartnerEventSource"}}, "PartnerEventSourceNamePrefix": {"type": "string", "max": 256, "min": 1, "pattern": "aws\\.partner/[\\.\\-_A-Za-z0-9]+/[/\\.\\-_A-Za-z0-9]*"}, "PathParameter": {"type": "string", "pattern": "^(?!\\s*$).+"}, "PathParameterList": {"type": "list", "member": {"shape": "PathParameter"}}, "PlacementConstraint": {"type": "structure", "members": {"type": {"shape": "PlacementConstraintType", "documentation": "<p>The type of constraint. Use distinctInstance to ensure that each task in a particular group is running on a different container instance. Use memberOf to restrict the selection to a group of valid candidates. </p>"}, "expression": {"shape": "PlacementConstraintExpression", "documentation": "<p>A cluster query language expression to apply to the constraint. You cannot specify an expression if the constraint type is <code>distinctInstance</code>. To learn more, see <a href=\"https://docs.aws.amazon.com/AmazonECS/latest/developerguide/cluster-query-language.html\">Cluster Query Language</a> in the Amazon Elastic Container Service Developer Guide. </p>"}}, "documentation": "<p>An object representing a constraint on task placement. To learn more, see <a href=\"https://docs.aws.amazon.com/AmazonECS/latest/developerguide/task-placement-constraints.html\">Task Placement Constraints</a> in the Amazon Elastic Container Service Developer Guide.</p>"}, "PlacementConstraintExpression": {"type": "string", "max": 2000}, "PlacementConstraintType": {"type": "string", "enum": ["distinctInstance", "memberOf"]}, "PlacementConstraints": {"type": "list", "member": {"shape": "PlacementConstraint"}, "max": 10}, "PlacementStrategies": {"type": "list", "member": {"shape": "PlacementStrategy"}, "max": 5}, "PlacementStrategy": {"type": "structure", "members": {"type": {"shape": "PlacementStrategyType", "documentation": "<p>The type of placement strategy. The random placement strategy randomly places tasks on available candidates. The spread placement strategy spreads placement across available candidates evenly based on the field parameter. The binpack strategy places tasks on available candidates that have the least available amount of the resource that is specified with the field parameter. For example, if you binpack on memory, a task is placed on the instance with the least amount of remaining memory (but still enough to run the task). </p>"}, "field": {"shape": "PlacementStrategyField", "documentation": "<p>The field to apply the placement strategy against. For the spread placement strategy, valid values are instanceId (or host, which has the same effect), or any platform or custom attribute that is applied to a container instance, such as attribute:ecs.availability-zone. For the binpack placement strategy, valid values are cpu and memory. For the random placement strategy, this field is not used. </p>"}}, "documentation": "<p>The task placement strategy for a task or service. To learn more, see <a href=\"https://docs.aws.amazon.com/AmazonECS/latest/developerguide/task-placement-strategies.html\">Task Placement Strategies</a> in the Amazon Elastic Container Service Service Developer Guide.</p>"}, "PlacementStrategyField": {"type": "string", "max": 255}, "PlacementStrategyType": {"type": "string", "enum": ["random", "spread", "binpack"]}, "PolicyLengthExceededException": {"type": "structure", "members": {}, "documentation": "<p>The event bus policy is too long. For more information, see the limits.</p>", "exception": true}, "Primary": {"type": "structure", "required": ["HealthCheck"], "members": {"HealthCheck": {"shape": "HealthCheck", "documentation": "<p>The ARN of the health check used by the endpoint to determine whether failover is triggered.</p>"}}, "documentation": "<p>The primary Region of the endpoint.</p>"}, "Principal": {"type": "string", "max": 12, "min": 1, "pattern": "(\\d{12}|\\*)"}, "PropagateTags": {"type": "string", "enum": ["TASK_DEFINITION"]}, "PutEventsRequest": {"type": "structure", "required": ["Entries"], "members": {"Entries": {"shape": "PutEventsRequestEntryList", "documentation": "<p>The entry that defines an event in your system. You can specify several parameters for the entry such as the source and type of the event, resources associated with the event, and so on.</p>"}, "EndpointId": {"shape": "EndpointId", "documentation": "<p>The URL subdomain of the endpoint. For example, if the URL for Endpoint is https://abcde.veo.endpoints.event.amazonaws.com, then the EndpointId is <code>abcde.veo</code>.</p> <important> <p>When using Java, you must include <code>auth-crt</code> on the class path.</p> </important>", "contextParam": {"name": "EndpointId"}}}}, "PutEventsRequestEntry": {"type": "structure", "members": {"Time": {"shape": "EventTime", "documentation": "<p>The time stamp of the event, per <a href=\"https://www.rfc-editor.org/rfc/rfc3339.txt\">RFC3339</a>. If no time stamp is provided, the time stamp of the <a href=\"https://docs.aws.amazon.com/eventbridge/latest/APIReference/API_PutEvents.html\">PutEvents</a> call is used.</p>"}, "Source": {"shape": "String", "documentation": "<p>The source of the event.</p>"}, "Resources": {"shape": "EventResourceList", "documentation": "<p>Amazon Web Services resources, identified by Amazon Resource Name (ARN), which the event primarily concerns. Any number, including zero, may be present.</p>"}, "DetailType": {"shape": "String", "documentation": "<p>Free-form string, with a maximum of 128 characters, used to decide what fields to expect in the event detail.</p>"}, "Detail": {"shape": "String", "documentation": "<p>A valid JSON object. There is no other schema imposed. The JSON object may contain fields and nested subobjects.</p>"}, "EventBusName": {"shape": "NonPartnerEventBusNameOrArn", "documentation": "<p>The name or ARN of the event bus to receive the event. Only the rules that are associated with this event bus are used to match the event. If you omit this, the default event bus is used.</p> <note> <p>If you're using a global endpoint with a custom bus, you must enter the name, not the ARN, of the event bus in either the primary or secondary Region here and the corresponding event bus in the other Region will be determined based on the endpoint referenced by the <code>EndpointId</code>.</p> </note>"}, "TraceHeader": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>An X-Ray trace header, which is an http header (X-Amzn-Trace-Id) that contains the trace-id associated with the event.</p> <p>To learn more about X-Ray trace headers, see <a href=\"https://docs.aws.amazon.com/xray/latest/devguide/xray-concepts.html#xray-concepts-tracingheader\">Tracing header</a> in the X-Ray Developer Guide.</p>"}}, "documentation": "<p>Represents an event to be submitted.</p>"}, "PutEventsRequestEntryList": {"type": "list", "member": {"shape": "PutEventsRequestEntry"}, "max": 10, "min": 1}, "PutEventsResponse": {"type": "structure", "members": {"FailedEntryCount": {"shape": "Integer", "documentation": "<p>The number of failed entries.</p>"}, "Entries": {"shape": "PutEventsResultEntryList", "documentation": "<p>The successfully and unsuccessfully ingested events results. If the ingestion was successful, the entry has the event ID in it. Otherwise, you can use the error code and error message to identify the problem with the entry.</p> <p>For each record, the index of the response element is the same as the index in the request array.</p>"}}}, "PutEventsResultEntry": {"type": "structure", "members": {"EventId": {"shape": "EventId", "documentation": "<p>The ID of the event.</p>"}, "ErrorCode": {"shape": "ErrorCode", "documentation": "<p>The error code that indicates why the event submission failed.</p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>The error message that explains why the event submission failed.</p>"}}, "documentation": "<p>Represents an event that failed to be submitted. For information about the errors that are common to all actions, see <a href=\"https://docs.aws.amazon.com/eventbridge/latest/APIReference/CommonErrors.html\">Common Errors</a>.</p>"}, "PutEventsResultEntryList": {"type": "list", "member": {"shape": "PutEventsResultEntry"}}, "PutPartnerEventsRequest": {"type": "structure", "required": ["Entries"], "members": {"Entries": {"shape": "PutPartnerEventsRequestEntryList", "documentation": "<p>The list of events to write to the event bus.</p>"}}}, "PutPartnerEventsRequestEntry": {"type": "structure", "members": {"Time": {"shape": "EventTime", "documentation": "<p>The date and time of the event.</p>"}, "Source": {"shape": "EventSourceName", "documentation": "<p>The event source that is generating the entry.</p>"}, "Resources": {"shape": "EventResourceList", "documentation": "<p>Amazon Web Services resources, identified by Amazon Resource Name (ARN), which the event primarily concerns. Any number, including zero, may be present.</p>"}, "DetailType": {"shape": "String", "documentation": "<p>A free-form string, with a maximum of 128 characters, used to decide what fields to expect in the event detail.</p>"}, "Detail": {"shape": "String", "documentation": "<p>A valid JSON string. There is no other schema imposed. The JSON string may contain fields and nested subobjects.</p>"}}, "documentation": "<p>The details about an event generated by an SaaS partner.</p>"}, "PutPartnerEventsRequestEntryList": {"type": "list", "member": {"shape": "PutPartnerEventsRequestEntry"}, "max": 20, "min": 1}, "PutPartnerEventsResponse": {"type": "structure", "members": {"FailedEntryCount": {"shape": "Integer", "documentation": "<p>The number of events from this operation that could not be written to the partner event bus.</p>"}, "Entries": {"shape": "PutPartnerEventsResultEntryList", "documentation": "<p>The list of events from this operation that were successfully written to the partner event bus.</p>"}}}, "PutPartnerEventsResultEntry": {"type": "structure", "members": {"EventId": {"shape": "EventId", "documentation": "<p>The ID of the event.</p>"}, "ErrorCode": {"shape": "ErrorCode", "documentation": "<p>The error code that indicates why the event submission failed.</p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>The error message that explains why the event submission failed.</p>"}}, "documentation": "<p>Represents an event that a partner tried to generate, but failed.</p>"}, "PutPartnerEventsResultEntryList": {"type": "list", "member": {"shape": "PutPartnerEventsResultEntry"}}, "PutPermissionRequest": {"type": "structure", "members": {"EventBusName": {"shape": "NonPartnerEventBusName", "documentation": "<p>The name of the event bus associated with the rule. If you omit this, the default event bus is used.</p>"}, "Action": {"shape": "Action", "documentation": "<p>The action that you are enabling the other account to perform.</p>"}, "Principal": {"shape": "Principal", "documentation": "<p>The 12-digit Amazon Web Services account ID that you are permitting to put events to your default event bus. Specify \"*\" to permit any account to put events to your default event bus.</p> <p>If you specify \"*\" without specifying <code>Condition</code>, avoid creating rules that may match undesirable events. To create more secure rules, make sure that the event pattern for each rule contains an <code>account</code> field with a specific account ID from which to receive events. Rules with an account field do not match any events sent from other accounts.</p>"}, "StatementId": {"shape": "StatementId", "documentation": "<p>An identifier string for the external account that you are granting permissions to. If you later want to revoke the permission for this external account, specify this <code>StatementId</code> when you run <a href=\"https://docs.aws.amazon.com/eventbridge/latest/APIReference/API_RemovePermission.html\">RemovePermission</a>.</p> <note> <p>Each <code>StatementId</code> must be unique.</p> </note>"}, "Condition": {"shape": "Condition", "documentation": "<p>This parameter enables you to limit the permission to accounts that fulfill a certain condition, such as being a member of a certain Amazon Web Services organization. For more information about Amazon Web Services Organizations, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_introduction.html\">What Is Amazon Web Services Organizations</a> in the <i>Amazon Web Services Organizations User Guide</i>.</p> <p>If you specify <code>Condition</code> with an Amazon Web Services organization ID, and specify \"*\" as the value for <code>Principal</code>, you grant permission to all the accounts in the named organization.</p> <p>The <code>Condition</code> is a JSON string which must contain <code>Type</code>, <code>Key</code>, and <code>Value</code> fields.</p>"}, "Policy": {"shape": "String", "documentation": "<p>A JSON string that describes the permission policy statement. You can include a <code>Policy</code> parameter in the request instead of using the <code>StatementId</code>, <code>Action</code>, <code>Principal</code>, or <code>Condition</code> parameters.</p>"}}}, "PutRuleRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "RuleName", "documentation": "<p>The name of the rule that you are creating or updating.</p>"}, "ScheduleExpression": {"shape": "ScheduleExpression", "documentation": "<p>The scheduling expression. For example, \"cron(0 20 * * ? *)\" or \"rate(5 minutes)\".</p>"}, "EventPattern": {"shape": "EventPattern", "documentation": "<p>The event pattern. For more information, see <a href=\"https://docs.aws.amazon.com/eventbridge/latest/userguide/eb-event-patterns.html\">Amazon EventBridge event patterns</a> in the <i>Amazon EventBridge User Guide</i>.</p>"}, "State": {"shape": "RuleState", "documentation": "<p>Indicates whether the rule is enabled or disabled.</p>"}, "Description": {"shape": "RuleDescription", "documentation": "<p>A description of the rule.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role associated with the rule.</p> <p>If you're setting an event bus in another account as the target and that account granted permission to your account through an organization instead of directly by the account ID, you must specify a <code>RoleArn</code> with proper permissions in the <code>Target</code> structure, instead of here in this parameter.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The list of key-value pairs to associate with the rule.</p>"}, "EventBusName": {"shape": "EventBusNameOrArn", "documentation": "<p>The name or ARN of the event bus to associate with this rule. If you omit this, the default event bus is used.</p>"}}}, "PutRuleResponse": {"type": "structure", "members": {"RuleArn": {"shape": "RuleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the rule.</p>"}}}, "PutTargetsRequest": {"type": "structure", "required": ["Rule", "Targets"], "members": {"Rule": {"shape": "RuleName", "documentation": "<p>The name of the rule.</p>"}, "EventBusName": {"shape": "EventBusNameOrArn", "documentation": "<p>The name or ARN of the event bus associated with the rule. If you omit this, the default event bus is used.</p>"}, "Targets": {"shape": "TargetList", "documentation": "<p>The targets to update or add to the rule.</p>"}}}, "PutTargetsResponse": {"type": "structure", "members": {"FailedEntryCount": {"shape": "Integer", "documentation": "<p>The number of failed entries.</p>"}, "FailedEntries": {"shape": "PutTargetsResultEntryList", "documentation": "<p>The failed target entries.</p>"}}}, "PutTargetsResultEntry": {"type": "structure", "members": {"TargetId": {"shape": "TargetId", "documentation": "<p>The ID of the target.</p>"}, "ErrorCode": {"shape": "ErrorCode", "documentation": "<p>The error code that indicates why the target addition failed. If the value is <code>ConcurrentModificationException</code>, too many requests were made at the same time.</p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>The error message that explains why the target addition failed.</p>"}}, "documentation": "<p>Represents a target that failed to be added to a rule.</p>"}, "PutTargetsResultEntryList": {"type": "list", "member": {"shape": "PutTargetsResultEntry"}}, "QueryStringKey": {"type": "string", "max": 512, "pattern": "[^\\x00-\\x1F\\x7F]+"}, "QueryStringParametersMap": {"type": "map", "key": {"shape": "QueryString<PERSON>ey"}, "value": {"shape": "QueryStringValue"}}, "QueryStringValue": {"type": "string", "max": 512, "pattern": "[^\\x00-\\x09\\x0B\\x0C\\x0E-\\x1F\\x7F]+"}, "QueryStringValueSensitive": {"type": "string", "max": 512, "pattern": "[^\\x00-\\x09\\x0B\\x0C\\x0E-\\x1F\\x7F]+", "sensitive": true}, "RedshiftDataParameters": {"type": "structure", "required": ["Database"], "members": {"SecretManagerArn": {"shape": "RedshiftSecretManagerArn", "documentation": "<p>The name or ARN of the secret that enables access to the database. Required when authenticating using Amazon Web Services Secrets Manager.</p>"}, "Database": {"shape": "Database", "documentation": "<p>The name of the database. Required when authenticating using temporary credentials.</p>"}, "DbUser": {"shape": "DbUser", "documentation": "<p>The database user name. Required when authenticating using temporary credentials.</p> <p>Do not provide this parameter when connecting to a Redshift Serverless workgroup.</p>"}, "Sql": {"shape": "Sql", "documentation": "<p>The SQL statement text to run.</p>"}, "StatementName": {"shape": "StatementName", "documentation": "<p>The name of the SQL statement. You can name the SQL statement when you create it to identify the query.</p>"}, "WithEvent": {"shape": "Boolean", "documentation": "<p>Indicates whether to send an event back to EventBridge after the SQL statement runs.</p>"}, "Sqls": {"shape": "Sqls"}}, "documentation": "<p>These are custom parameters to be used when the target is a Amazon Redshift cluster or Redshift Serverless workgroup to invoke the Amazon Redshift Data API ExecuteStatement based on EventBridge events.</p>"}, "RedshiftSecretManagerArn": {"type": "string", "documentation": "Optional SecretManager ARN which stores the database credentials", "max": 1600, "min": 1, "pattern": "(^arn:aws([a-z]|\\-)*:secretsmanager:[a-z0-9-.]+:.*)|(\\$(\\.[\\w_-]+(\\[(\\d+|\\*)\\])*)*)"}, "ReferenceId": {"type": "string", "max": 1024}, "RemovePermissionRequest": {"type": "structure", "members": {"StatementId": {"shape": "StatementId", "documentation": "<p>The statement ID corresponding to the account that is no longer allowed to put events to the default event bus.</p>"}, "RemoveAllPermissions": {"shape": "Boolean", "documentation": "<p>Specifies whether to remove all permissions.</p>"}, "EventBusName": {"shape": "NonPartnerEventBusName", "documentation": "<p>The name of the event bus to revoke permissions for. If you omit this, the default event bus is used.</p>"}}}, "RemoveTargetsRequest": {"type": "structure", "required": ["Rule", "Ids"], "members": {"Rule": {"shape": "RuleName", "documentation": "<p>The name of the rule.</p>"}, "EventBusName": {"shape": "EventBusNameOrArn", "documentation": "<p>The name or ARN of the event bus associated with the rule. If you omit this, the default event bus is used.</p>"}, "Ids": {"shape": "TargetIdList", "documentation": "<p>The IDs of the targets to remove from the rule.</p>"}, "Force": {"shape": "Boolean", "documentation": "<p>If this is a managed rule, created by an Amazon Web Services service on your behalf, you must specify <code>Force</code> as <code>True</code> to remove targets. This parameter is ignored for rules that are not managed rules. You can check whether a rule is a managed rule by using <code>DescribeRule</code> or <code>ListRules</code> and checking the <code>ManagedBy</code> field of the response.</p>"}}}, "RemoveTargetsResponse": {"type": "structure", "members": {"FailedEntryCount": {"shape": "Integer", "documentation": "<p>The number of failed entries.</p>"}, "FailedEntries": {"shape": "RemoveTargetsResultEntryList", "documentation": "<p>The failed target entries.</p>"}}}, "RemoveTargetsResultEntry": {"type": "structure", "members": {"TargetId": {"shape": "TargetId", "documentation": "<p>The ID of the target.</p>"}, "ErrorCode": {"shape": "ErrorCode", "documentation": "<p>The error code that indicates why the target removal failed. If the value is <code>ConcurrentModificationException</code>, too many requests were made at the same time.</p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>The error message that explains why the target removal failed.</p>"}}, "documentation": "<p>Represents a target that failed to be removed from a rule.</p>"}, "RemoveTargetsResultEntryList": {"type": "list", "member": {"shape": "RemoveTargetsResultEntry"}}, "Replay": {"type": "structure", "members": {"ReplayName": {"shape": "ReplayName", "documentation": "<p>The name of the replay.</p>"}, "EventSourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the archive to replay event from.</p>"}, "State": {"shape": "ReplayState", "documentation": "<p>The current state of the replay.</p>"}, "StateReason": {"shape": "ReplayStateReason", "documentation": "<p>A description of why the replay is in the current state.</p>"}, "EventStartTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time to start replaying events. This is determined by the time in the event as described in <a href=\"https://docs.aws.amazon.com/eventbridge/latest/APIReference/API_PutEventsRequestEntry.html#eventbridge-Type-PutEventsRequestEntry-Time\">Time</a>.</p>"}, "EventEndTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time to start replaying events. Any event with a creation time prior to the <code>EventEndTime</code> specified is replayed.</p>"}, "EventLastReplayedTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the last event was replayed.</p>"}, "ReplayStartTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the replay started.</p>"}, "ReplayEndTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the replay completed.</p>"}}, "documentation": "<p>A <code>Replay</code> object that contains details about a replay.</p>"}, "ReplayArn": {"type": "string", "max": 1600, "min": 1, "pattern": "^arn:aws([a-z]|\\-)*:events:([a-z]|\\d|\\-)*:([0-9]{12})?:.+\\/[\\.\\-_A-Za-z0-9]+$"}, "ReplayDescription": {"type": "string", "max": 512, "pattern": ".*"}, "ReplayDestination": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"Arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the event bus to replay event to. You can replay events only to the event bus specified to create the archive.</p>"}, "FilterArns": {"shape": "ReplayDestinationFilters", "documentation": "<p>A list of ARNs for rules to replay events to.</p>"}}, "documentation": "<p>A <code>ReplayDestination</code> object that contains details about a replay.</p>"}, "ReplayDestinationFilters": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}}, "ReplayList": {"type": "list", "member": {"shape": "Replay"}}, "ReplayName": {"type": "string", "max": 64, "min": 1, "pattern": "[\\.\\-_A-Za-z0-9]+"}, "ReplayState": {"type": "string", "enum": ["STARTING", "RUNNING", "CANCELLING", "COMPLETED", "CANCELLED", "FAILED"]}, "ReplayStateReason": {"type": "string", "max": 512, "pattern": ".*"}, "ReplicationConfig": {"type": "structure", "members": {"State": {"shape": "ReplicationState", "documentation": "<p>The state of event replication.</p>"}}, "documentation": "<p>Endpoints can replicate all events to the secondary Region.</p>"}, "ReplicationState": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "ResourceAlreadyExistsException": {"type": "structure", "members": {}, "documentation": "<p>The resource you are trying to create already exists.</p>", "exception": true}, "ResourceArn": {"type": "string", "max": 1600, "min": 1}, "ResourceNotFoundException": {"type": "structure", "members": {}, "documentation": "<p>An entity that you specified does not exist.</p>", "exception": true}, "RetentionDays": {"type": "integer", "min": 0}, "RetryPolicy": {"type": "structure", "members": {"MaximumRetryAttempts": {"shape": "MaximumRetryAttempts", "documentation": "<p>The maximum number of retry attempts to make before the request fails. Retry attempts continue until either the maximum number of attempts is made or until the duration of the <code>MaximumEventAgeInSeconds</code> is met.</p>"}, "MaximumEventAgeInSeconds": {"shape": "MaximumEventAgeInSeconds", "documentation": "<p>The maximum amount of time, in seconds, to continue to make retry attempts.</p>"}}, "documentation": "<p>A <code>RetryPolicy</code> object that includes information about the retry policy settings.</p>"}, "RoleArn": {"type": "string", "max": 1600, "min": 1}, "Route": {"type": "string", "max": 20, "min": 9, "pattern": "^[\\-a-z0-9]+$"}, "RoutingConfig": {"type": "structure", "required": ["FailoverConfig"], "members": {"FailoverConfig": {"shape": "FailoverConfig", "documentation": "<p>The failover configuration for an endpoint. This includes what triggers failover and what happens when it's triggered.</p>"}}, "documentation": "<p>The routing configuration of the endpoint.</p>"}, "Rule": {"type": "structure", "members": {"Name": {"shape": "RuleName", "documentation": "<p>The name of the rule.</p>"}, "Arn": {"shape": "RuleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the rule.</p>"}, "EventPattern": {"shape": "EventPattern", "documentation": "<p>The event pattern of the rule. For more information, see <a href=\"https://docs.aws.amazon.com/eventbridge/latest/userguide/eventbridge-and-event-patterns.html\">Events and Event Patterns</a> in the <i>Amazon EventBridge User Guide</i>.</p>"}, "State": {"shape": "RuleState", "documentation": "<p>The state of the rule.</p>"}, "Description": {"shape": "RuleDescription", "documentation": "<p>The description of the rule.</p>"}, "ScheduleExpression": {"shape": "ScheduleExpression", "documentation": "<p>The scheduling expression. For example, \"cron(0 20 * * ? *)\", \"rate(5 minutes)\". For more information, see <a href=\"https://docs.aws.amazon.com/eventbridge/latest/userguide/eb-create-rule-schedule.html\">Creating an Amazon EventBridge rule that runs on a schedule</a>.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the role that is used for target invocation.</p> <p>If you're setting an event bus in another account as the target and that account granted permission to your account through an organization instead of directly by the account ID, you must specify a <code>RoleArn</code> with proper permissions in the <code>Target</code> structure, instead of here in this parameter.</p>"}, "ManagedBy": {"shape": "ManagedBy", "documentation": "<p>If the rule was created on behalf of your account by an Amazon Web Services service, this field displays the principal name of the service that created the rule.</p>"}, "EventBusName": {"shape": "EventBusName", "documentation": "<p>The name or ARN of the event bus associated with the rule. If you omit this, the default event bus is used.</p>"}}, "documentation": "<p>Contains information about a rule in Amazon EventBridge.</p>"}, "RuleArn": {"type": "string", "max": 1600, "min": 1}, "RuleDescription": {"type": "string", "max": 512}, "RuleName": {"type": "string", "max": 64, "min": 1, "pattern": "[\\.\\-_A-Za-z0-9]+"}, "RuleNameList": {"type": "list", "member": {"shape": "RuleName"}}, "RuleResponseList": {"type": "list", "member": {"shape": "Rule"}}, "RuleState": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "RunCommandParameters": {"type": "structure", "required": ["RunCommandTargets"], "members": {"RunCommandTargets": {"shape": "RunCommandTargets", "documentation": "<p>Currently, we support including only one RunCommandTarget block, which specifies either an array of InstanceIds or a tag.</p>"}}, "documentation": "<p>This parameter contains the criteria (either InstanceIds or a tag) used to specify which EC2 instances are to be sent the command. </p>"}, "RunCommandTarget": {"type": "structure", "required": ["Key", "Values"], "members": {"Key": {"shape": "RunCommandTargetKey", "documentation": "<p>Can be either <code>tag:</code> <i>tag-key</i> or <code>InstanceIds</code>.</p>"}, "Values": {"shape": "RunCommandTargetValues", "documentation": "<p>If <code>Key</code> is <code>tag:</code> <i>tag-key</i>, <code>Values</code> is a list of tag values. If <code>Key</code> is <code>InstanceIds</code>, <code>Values</code> is a list of Amazon EC2 instance IDs.</p>"}}, "documentation": "<p>Information about the EC2 instances that are to be sent the command, specified as key-value pairs. Each <code>RunCommandTarget</code> block can include only one key, but this key may specify multiple values.</p>"}, "RunCommandTargetKey": {"type": "string", "max": 128, "min": 1, "pattern": "^[\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*$"}, "RunCommandTargetValue": {"type": "string", "max": 256, "min": 1}, "RunCommandTargetValues": {"type": "list", "member": {"shape": "RunCommandTargetValue"}, "max": 50, "min": 1}, "RunCommandTargets": {"type": "list", "member": {"shape": "RunCommandTarget"}, "max": 5, "min": 1}, "SageMakerPipelineParameter": {"type": "structure", "required": ["Name", "Value"], "members": {"Name": {"shape": "SageMakerPipelineParameterName", "documentation": "<p>Name of parameter to start execution of a SageMaker Model Building Pipeline.</p>"}, "Value": {"shape": "SageMakerPipelineParameterValue", "documentation": "<p>Value of parameter to start execution of a SageMaker Model Building Pipeline.</p>"}}, "documentation": "<p>Name/Value pair of a parameter to start execution of a SageMaker Model Building Pipeline.</p>"}, "SageMakerPipelineParameterList": {"type": "list", "member": {"shape": "SageMakerPipelineParameter"}, "max": 200, "min": 0}, "SageMakerPipelineParameterName": {"type": "string", "max": 256, "min": 1, "pattern": "^[a-zA-Z0-9](-*[a-zA-Z0-9])*$"}, "SageMakerPipelineParameterValue": {"type": "string", "max": 1024}, "SageMakerPipelineParameters": {"type": "structure", "members": {"PipelineParameterList": {"shape": "SageMakerPipelineParameterList", "documentation": "<p>List of Parameter names and values for SageMaker Model Building Pipeline execution.</p>"}}, "documentation": "<p>These are custom parameters to use when the target is a SageMaker Model Building Pipeline that starts based on EventBridge events.</p>"}, "ScheduleExpression": {"type": "string", "max": 256}, "Secondary": {"type": "structure", "required": ["Route"], "members": {"Route": {"shape": "Route", "documentation": "<p>Defines the secondary Region.</p>"}}, "documentation": "<p>The secondary Region that processes events when failover is triggered or replication is enabled.</p>"}, "SecretsManagerSecretArn": {"type": "string", "max": 2048, "min": 20, "pattern": "^arn:aws([a-z]|\\-)*:secretsmanager:([a-z]|\\d|\\-)*:([0-9]{12})?:secret:[\\/_+=\\.@\\-A-Za-z0-9]+$"}, "SensitiveString": {"type": "string", "sensitive": true}, "Sql": {"type": "string", "documentation": "A single Redshift SQL", "max": 100000, "min": 1, "sensitive": true}, "Sqls": {"type": "list", "member": {"shape": "Sql"}, "documentation": "A list of SQLs.", "max": 40, "min": 0, "sensitive": true}, "SqsParameters": {"type": "structure", "members": {"MessageGroupId": {"shape": "MessageGroupId", "documentation": "<p>The FIFO message group ID to use as the target.</p>"}}, "documentation": "<p>This structure includes the custom parameter to be used when the target is an SQS FIFO queue.</p>"}, "StartReplayRequest": {"type": "structure", "required": ["ReplayName", "EventSourceArn", "EventStartTime", "EventEndTime", "Destination"], "members": {"ReplayName": {"shape": "ReplayName", "documentation": "<p>The name of the replay to start.</p>"}, "Description": {"shape": "ReplayDescription", "documentation": "<p>A description for the replay to start.</p>"}, "EventSourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the archive to replay events from.</p>"}, "EventStartTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time to start replaying events. Only events that occurred between the <code>EventStartTime</code> and <code>EventEndTime</code> are replayed.</p>"}, "EventEndTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time to stop replaying events. Only events that occurred between the <code>EventStartTime</code> and <code>EventEndTime</code> are replayed.</p>"}, "Destination": {"shape": "ReplayDestination", "documentation": "<p>A <code>ReplayDestination</code> object that includes details about the destination for the replay.</p>"}}}, "StartReplayResponse": {"type": "structure", "members": {"ReplayArn": {"shape": "ReplayArn", "documentation": "<p>The ARN of the replay.</p>"}, "State": {"shape": "ReplayState", "documentation": "<p>The state of the replay.</p>"}, "StateReason": {"shape": "ReplayStateReason", "documentation": "<p>The reason that the replay is in the state.</p>"}, "ReplayStartTime": {"shape": "Timestamp", "documentation": "<p>The time at which the replay started.</p>"}}}, "StatementId": {"type": "string", "max": 64, "min": 1, "pattern": "[a-zA-Z0-9-_]+"}, "StatementName": {"type": "string", "documentation": "A name for Redshift DataAPI statement which can be used as filter of ListStatement.", "max": 500, "min": 1}, "String": {"type": "string"}, "StringList": {"type": "list", "member": {"shape": "String"}}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>A string you can use to assign a value. The combination of tag keys and values can help you organize and categorize your resources.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The value for the specified tag key.</p>"}}, "documentation": "<p>A key-value pair associated with an Amazon Web Services resource. In EventBridge, rules and event buses support tagging.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}}, "TagList": {"type": "list", "member": {"shape": "Tag"}}, "TagResourceRequest": {"type": "structure", "required": ["ResourceARN", "Tags"], "members": {"ResourceARN": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the EventBridge resource that you're adding tags to.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The list of key-value pairs to associate with the resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "Target": {"type": "structure", "required": ["Id", "<PERSON><PERSON>"], "members": {"Id": {"shape": "TargetId", "documentation": "<p>The ID of the target within the specified rule. Use this ID to reference the target when updating the rule. We recommend using a memorable and unique string.</p>"}, "Arn": {"shape": "TargetArn", "documentation": "<p>The Amazon Resource Name (ARN) of the target.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role to be used for this target when the rule is triggered. If one rule triggers multiple targets, you can use a different IAM role for each target.</p>"}, "Input": {"shape": "TargetInput", "documentation": "<p>Valid JSON text passed to the target. In this case, nothing from the event itself is passed to the target. For more information, see <a href=\"http://www.rfc-editor.org/rfc/rfc7159.txt\">The JavaScript Object Notation (JSON) Data Interchange Format</a>.</p>"}, "InputPath": {"shape": "TargetInputPath", "documentation": "<p>The value of the JSONPath that is used for extracting part of the matched event when passing it to the target. You may use JSON dot notation or bracket notation. For more information about JSON paths, see <a href=\"http://goessner.net/articles/JsonPath/\">JSONPath</a>.</p>"}, "InputTransformer": {"shape": "InputTransformer", "documentation": "<p>Settings to enable you to provide custom input to a target based on certain event data. You can extract one or more key-value pairs from the event and then use that data to send customized input to the target.</p>"}, "KinesisParameters": {"shape": "KinesisParameters", "documentation": "<p>The custom parameter you can use to control the shard assignment, when the target is a Kinesis data stream. If you do not include this parameter, the default is to use the <code>eventId</code> as the partition key.</p>"}, "RunCommandParameters": {"shape": "RunCommandParameters", "documentation": "<p>Parameters used when you are using the rule to invoke Amazon EC2 Run Command.</p>"}, "EcsParameters": {"shape": "EcsParameters", "documentation": "<p>Contains the Amazon ECS task definition and task count to be used, if the event target is an Amazon ECS task. For more information about Amazon ECS tasks, see <a href=\"https://docs.aws.amazon.com/AmazonECS/latest/developerguide/task_defintions.html\">Task Definitions </a> in the <i>Amazon EC2 Container Service Developer Guide</i>.</p>"}, "BatchParameters": {"shape": "BatchParameters", "documentation": "<p>If the event target is an Batch job, this contains the job definition, job name, and other parameters. For more information, see <a href=\"https://docs.aws.amazon.com/batch/latest/userguide/jobs.html\">Jobs</a> in the <i>Batch User Guide</i>.</p>"}, "SqsParameters": {"shape": "SqsParameters", "documentation": "<p>Contains the message group ID to use when the target is a FIFO queue.</p> <p>If you specify an SQS FIFO queue as a target, the queue must have content-based deduplication enabled.</p>"}, "HttpParameters": {"shape": "HttpParameters", "documentation": "<p>Contains the HTTP parameters to use when the target is a API Gateway endpoint or EventBridge ApiDestination.</p> <p>If you specify an API Gateway API or EventBridge ApiDestination as a target, you can use this parameter to specify headers, path parameters, and query string keys/values as part of your target invoking request. If you're using ApiDestinations, the corresponding Connection can also have these values configured. In case of any conflicting keys, values from the Connection take precedence.</p>"}, "RedshiftDataParameters": {"shape": "RedshiftDataParameters", "documentation": "<p>Contains the Amazon Redshift Data API parameters to use when the target is a Amazon Redshift cluster.</p> <p>If you specify a Amazon Redshift Cluster as a Target, you can use this to specify parameters to invoke the Amazon Redshift Data API ExecuteStatement based on EventBridge events.</p>"}, "SageMakerPipelineParameters": {"shape": "SageMakerPipelineParameters", "documentation": "<p>Contains the SageMaker Model Building Pipeline parameters to start execution of a SageMaker Model Building Pipeline.</p> <p>If you specify a SageMaker Model Building Pipeline as a target, you can use this to specify parameters to start a pipeline execution based on EventBridge events.</p>"}, "DeadLetterConfig": {"shape": "DeadLetterConfig", "documentation": "<p>The <code>DeadLetterConfig</code> that defines the target queue to send dead-letter queue events to.</p>"}, "RetryPolicy": {"shape": "RetryPolicy", "documentation": "<p>The <code>RetryPolicy</code> object that contains the retry policy configuration to use for the dead-letter queue.</p>"}}, "documentation": "<p>Targets are the resources to be invoked when a rule is triggered. For a complete list of services and resources that can be set as a target, see <a href=\"https://docs.aws.amazon.com/eventbridge/latest/APIReference/API_PutTargets.html\">PutTargets</a>.</p> <p>If you are setting the event bus of another account as the target, and that account granted permission to your account through an organization instead of directly by the account ID, then you must specify a <code>RoleArn</code> with proper permissions in the <code>Target</code> structure. For more information, see <a href=\"https://docs.aws.amazon.com/eventbridge/latest/userguide/eventbridge-cross-account-event-delivery.html\">Sending and Receiving Events Between Amazon Web Services Accounts</a> in the <i>Amazon EventBridge User Guide</i>.</p>"}, "TargetArn": {"type": "string", "max": 1600, "min": 1}, "TargetId": {"type": "string", "max": 64, "min": 1, "pattern": "[\\.\\-_A-Za-z0-9]+"}, "TargetIdList": {"type": "list", "member": {"shape": "TargetId"}, "max": 100, "min": 1}, "TargetInput": {"type": "string", "max": 8192}, "TargetInputPath": {"type": "string", "max": 256}, "TargetList": {"type": "list", "member": {"shape": "Target"}, "max": 100, "min": 1}, "TargetPartitionKeyPath": {"type": "string", "max": 256}, "TestEventPatternRequest": {"type": "structure", "required": ["EventPattern", "Event"], "members": {"EventPattern": {"shape": "EventPattern", "documentation": "<p>The event pattern. For more information, see <a href=\"https://docs.aws.amazon.com/eventbridge/latest/userguide/eventbridge-and-event-patterns.html\">Events and Event Patterns</a> in the <i>Amazon EventBridge User Guide</i>.</p>"}, "Event": {"shape": "String", "documentation": "<p>The event, in JSON format, to test against the event pattern. The JSON must follow the format specified in <a href=\"https://docs.aws.amazon.com/eventbridge/latest/userguide/aws-events.html\">Amazon Web Services Events</a>, and the following fields are mandatory:</p> <ul> <li> <p> <code>id</code> </p> </li> <li> <p> <code>account</code> </p> </li> <li> <p> <code>source</code> </p> </li> <li> <p> <code>time</code> </p> </li> <li> <p> <code>region</code> </p> </li> <li> <p> <code>resources</code> </p> </li> <li> <p> <code>detail-type</code> </p> </li> </ul>"}}}, "TestEventPatternResponse": {"type": "structure", "members": {"Result": {"shape": "Boolean", "documentation": "<p>Indicates whether the event matches the event pattern.</p>"}}}, "Timestamp": {"type": "timestamp"}, "TraceHeader": {"type": "string", "max": 500, "min": 1}, "TransformerInput": {"type": "string", "max": 8192, "min": 1}, "TransformerPaths": {"type": "map", "key": {"shape": "InputTransformerPathKey"}, "value": {"shape": "TargetInputPath"}, "max": 100}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceARN", "TagKeys"], "members": {"ResourceARN": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the EventBridge resource from which you are removing tags.</p>"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>The list of tag keys to remove from the resource.</p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateApiDestinationRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "ApiDestinationName", "documentation": "<p>The name of the API destination to update.</p>"}, "Description": {"shape": "ApiDestinationDescription", "documentation": "<p>The name of the API destination to update.</p>"}, "ConnectionArn": {"shape": "ConnectionArn", "documentation": "<p>The ARN of the connection to use for the API destination.</p>"}, "InvocationEndpoint": {"shape": "HttpsEndpoint", "documentation": "<p>The URL to the endpoint to use for the API destination.</p>"}, "HttpMethod": {"shape": "ApiDestinationHttpMethod", "documentation": "<p>The method to use for the API destination.</p>"}, "InvocationRateLimitPerSecond": {"shape": "ApiDestinationInvocationRateLimitPerSecond", "documentation": "<p>The maximum number of invocations per second to send to the API destination.</p>"}}}, "UpdateApiDestinationResponse": {"type": "structure", "members": {"ApiDestinationArn": {"shape": "ApiDestinationArn", "documentation": "<p>The ARN of the API destination that was updated.</p>"}, "ApiDestinationState": {"shape": "ApiDestinationState", "documentation": "<p>The state of the API destination that was updated.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the API destination was created.</p>"}, "LastModifiedTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the API destination was last modified.</p>"}}}, "UpdateArchiveRequest": {"type": "structure", "required": ["ArchiveName"], "members": {"ArchiveName": {"shape": "ArchiveName", "documentation": "<p>The name of the archive to update.</p>"}, "Description": {"shape": "ArchiveDescription", "documentation": "<p>The description for the archive.</p>"}, "EventPattern": {"shape": "EventPattern", "documentation": "<p>The event pattern to use to filter events sent to the archive.</p>"}, "RetentionDays": {"shape": "RetentionDays", "documentation": "<p>The number of days to retain events in the archive.</p>"}}}, "UpdateArchiveResponse": {"type": "structure", "members": {"ArchiveArn": {"shape": "ArchiveArn", "documentation": "<p>The ARN of the archive.</p>"}, "State": {"shape": "ArchiveState", "documentation": "<p>The state of the archive.</p>"}, "StateReason": {"shape": "ArchiveStateReason", "documentation": "<p>The reason that the archive is in the current state.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>The time at which the archive was updated.</p>"}}}, "UpdateConnectionApiKeyAuthRequestParameters": {"type": "structure", "members": {"ApiKeyName": {"shape": "AuthHeaderParameters", "documentation": "<p>The name of the API key to use for authorization.</p>"}, "ApiKeyValue": {"shape": "AuthHeaderParametersSensitive", "documentation": "<p>The value associated with teh API key to use for authorization.</p>"}}, "documentation": "<p>Contains the API key authorization parameters to use to update the connection.</p>"}, "UpdateConnectionAuthRequestParameters": {"type": "structure", "members": {"BasicAuthParameters": {"shape": "UpdateConnectionBasicAuthRequestParameters", "documentation": "<p>A <code>UpdateConnectionBasicAuthRequestParameters</code> object that contains the authorization parameters for Basic authorization.</p>"}, "OAuthParameters": {"shape": "UpdateConnectionOAuthRequestParameters", "documentation": "<p>A <code>UpdateConnectionOAuthRequestParameters</code> object that contains the authorization parameters for OAuth authorization.</p>"}, "ApiKeyAuthParameters": {"shape": "UpdateConnectionApiKeyAuthRequestParameters", "documentation": "<p>A <code>UpdateConnectionApiKeyAuthRequestParameters</code> object that contains the authorization parameters for API key authorization.</p>"}, "InvocationHttpParameters": {"shape": "ConnectionHttpParameters", "documentation": "<p>A <code>ConnectionHttpParameters</code> object that contains the additional parameters to use for the connection.</p>"}}, "documentation": "<p>Contains the additional parameters to use for the connection.</p>"}, "UpdateConnectionBasicAuthRequestParameters": {"type": "structure", "members": {"Username": {"shape": "AuthHeaderParameters", "documentation": "<p>The user name to use for Basic authorization.</p>"}, "Password": {"shape": "AuthHeaderParametersSensitive", "documentation": "<p>The password associated with the user name to use for Basic authorization.</p>"}}, "documentation": "<p>Contains the Basic authorization parameters for the connection.</p>"}, "UpdateConnectionOAuthClientRequestParameters": {"type": "structure", "members": {"ClientID": {"shape": "AuthHeaderParameters", "documentation": "<p>The client ID to use for OAuth authorization.</p>"}, "ClientSecret": {"shape": "AuthHeaderParametersSensitive", "documentation": "<p>The client secret assciated with the client ID to use for OAuth authorization.</p>"}}, "documentation": "<p>Contains the OAuth authorization parameters to use for the connection.</p>"}, "UpdateConnectionOAuthRequestParameters": {"type": "structure", "members": {"ClientParameters": {"shape": "UpdateConnectionOAuthClientRequestParameters", "documentation": "<p>A <code>UpdateConnectionOAuthClientRequestParameters</code> object that contains the client parameters to use for the connection when OAuth is specified as the authorization type.</p>"}, "AuthorizationEndpoint": {"shape": "HttpsEndpoint", "documentation": "<p>The URL to the authorization endpoint when OAuth is specified as the authorization type.</p>"}, "HttpMethod": {"shape": "ConnectionOAuthHttpMethod", "documentation": "<p>The method used to connect to the HTTP endpoint.</p>"}, "OAuthHttpParameters": {"shape": "ConnectionHttpParameters", "documentation": "<p>The additional HTTP parameters used for the OAuth authorization request.</p>"}}, "documentation": "<p>Contains the OAuth request parameters to use for the connection.</p>"}, "UpdateConnectionRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "ConnectionName", "documentation": "<p>The name of the connection to update.</p>"}, "Description": {"shape": "ConnectionDescription", "documentation": "<p>A description for the connection.</p>"}, "AuthorizationType": {"shape": "ConnectionAuthorizationType", "documentation": "<p>The type of authorization to use for the connection.</p>"}, "AuthParameters": {"shape": "UpdateConnectionAuthRequestParameters", "documentation": "<p>The authorization parameters to use for the connection.</p>"}}}, "UpdateConnectionResponse": {"type": "structure", "members": {"ConnectionArn": {"shape": "ConnectionArn", "documentation": "<p>The ARN of the connection that was updated.</p>"}, "ConnectionState": {"shape": "ConnectionState", "documentation": "<p>The state of the connection that was updated.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the connection was created.</p>"}, "LastModifiedTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the connection was last modified.</p>"}, "LastAuthorizedTime": {"shape": "Timestamp", "documentation": "<p>A time stamp for the time that the connection was last authorized.</p>"}}}, "UpdateEndpointRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "EndpointName", "documentation": "<p>The name of the endpoint you want to update.</p>"}, "Description": {"shape": "EndpointDescription", "documentation": "<p>A description for the endpoint.</p>"}, "RoutingConfig": {"shape": "RoutingConfig", "documentation": "<p>Configure the routing policy, including the health check and secondary Region.</p>"}, "ReplicationConfig": {"shape": "ReplicationConfig", "documentation": "<p>Whether event replication was enabled or disabled by this request.</p>"}, "EventBuses": {"shape": "EndpointEventBusList", "documentation": "<p>Define event buses used for replication.</p>"}, "RoleArn": {"shape": "IamRoleArn", "documentation": "<p>The ARN of the role used by event replication for this request.</p>"}}}, "UpdateEndpointResponse": {"type": "structure", "members": {"Name": {"shape": "EndpointName", "documentation": "<p>The name of the endpoint you updated in this request.</p>"}, "Arn": {"shape": "EndpointArn", "documentation": "<p>The ARN of the endpoint you updated in this request.</p>"}, "RoutingConfig": {"shape": "RoutingConfig", "documentation": "<p>The routing configuration you updated in this request.</p>"}, "ReplicationConfig": {"shape": "ReplicationConfig", "documentation": "<p>Whether event replication was enabled or disabled for the endpoint you updated in this request.</p>"}, "EventBuses": {"shape": "EndpointEventBusList", "documentation": "<p>The event buses used for replication for the endpoint you updated in this request.</p>"}, "RoleArn": {"shape": "IamRoleArn", "documentation": "<p>The ARN of the role used by event replication for the endpoint you updated in this request.</p>"}, "EndpointId": {"shape": "EndpointId", "documentation": "<p>The ID of the endpoint you updated in this request.</p>"}, "EndpointUrl": {"shape": "EndpointUrl", "documentation": "<p>The URL of the endpoint you updated in this request.</p>"}, "State": {"shape": "EndpointState", "documentation": "<p>The state of the endpoint you updated in this request.</p>"}}}}, "documentation": "<p>Amazon EventBridge helps you to respond to state changes in your Amazon Web Services resources. When your resources change state, they automatically send events to an event stream. You can create rules that match selected events in the stream and route them to targets to take action. You can also use rules to take action on a predetermined schedule. For example, you can configure rules to:</p> <ul> <li> <p>Automatically invoke an Lambda function to update DNS entries when an event notifies you that Amazon EC2 instance enters the running state.</p> </li> <li> <p>Direct specific API records from CloudTrail to an Amazon Kinesis data stream for detailed analysis of potential security or availability risks.</p> </li> <li> <p>Periodically invoke a built-in target to create a snapshot of an Amazon EBS volume.</p> </li> </ul> <p>For more information about the features of Amazon EventBridge, see the <a href=\"https://docs.aws.amazon.com/eventbridge/latest/userguide\">Amazon EventBridge User Guide</a>.</p>"}