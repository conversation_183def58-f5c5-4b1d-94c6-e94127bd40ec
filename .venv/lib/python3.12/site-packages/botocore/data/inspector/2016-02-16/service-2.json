{"version": "2.0", "metadata": {"apiVersion": "2016-02-16", "endpointPrefix": "inspector", "jsonVersion": "1.1", "protocol": "json", "serviceFullName": "Amazon Inspector", "serviceId": "Inspector", "signatureVersion": "v4", "targetPrefix": "InspectorService", "uid": "inspector-2016-02-16"}, "operations": {"AddAttributesToFindings": {"name": "AddAttributesToFindings", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AddAttributesToFindingsRequest"}, "output": {"shape": "AddAttributesToFindingsResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}, {"shape": "ServiceTemporarilyUnavailableException"}], "documentation": "<p>Assigns attributes (key and value pairs) to the findings that are specified by the ARNs of the findings.</p>"}, "CreateAssessmentTarget": {"name": "CreateAs<PERSON><PERSON>ent<PERSON>arget", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateAssessmentTargetRequest"}, "output": {"shape": "CreateAssessmentTargetResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "LimitExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}, {"shape": "InvalidCrossAccountRoleException"}, {"shape": "ServiceTemporarilyUnavailableException"}], "documentation": "<p>Creates a new assessment target using the ARN of the resource group that is generated by <a>CreateResourceGroup</a>. If resourceGroupArn is not specified, all EC2 instances in the current AWS account and region are included in the assessment target. If the <a href=\"https://docs.aws.amazon.com/inspector/latest/userguide/inspector_slr.html\">service-linked role</a> isn’t already registered, this action also creates and registers a service-linked role to grant Amazon Inspector access to AWS Services needed to perform security assessments. You can create up to 50 assessment targets per AWS account. You can run up to 500 concurrent agents per AWS account. For more information, see <a href=\"https://docs.aws.amazon.com/inspector/latest/userguide/inspector_applications.html\"> Amazon Inspector Assessment Targets</a>.</p>"}, "CreateAssessmentTemplate": {"name": "CreateAssessmentTemplate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateAssessmentTemplateRequest"}, "output": {"shape": "CreateAssessmentTemplateResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "LimitExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}, {"shape": "ServiceTemporarilyUnavailableException"}], "documentation": "<p>Creates an assessment template for the assessment target that is specified by the ARN of the assessment target. If the <a href=\"https://docs.aws.amazon.com/inspector/latest/userguide/inspector_slr.html\">service-linked role</a> isn’t already registered, this action also creates and registers a service-linked role to grant Amazon Inspector access to AWS Services needed to perform security assessments.</p>"}, "CreateExclusionsPreview": {"name": "CreateExclusionsPreview", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateExclusionsPreviewRequest"}, "output": {"shape": "CreateExclusionsPreviewResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "PreviewGenerationInProgressException"}, {"shape": "InternalException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}, {"shape": "ServiceTemporarilyUnavailableException"}], "documentation": "<p>Starts the generation of an exclusions preview for the specified assessment template. The exclusions preview lists the potential exclusions (ExclusionPreview) that Inspector can detect before it runs the assessment. </p>"}, "CreateResourceGroup": {"name": "CreateResourceGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateResourceGroupRequest"}, "output": {"shape": "CreateResourceGroupResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "LimitExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceTemporarilyUnavailableException"}], "documentation": "<p>Creates a resource group using the specified set of tags (key and value pairs) that are used to select the EC2 instances to be included in an Amazon Inspector assessment target. The created resource group is then used to create an Amazon Inspector assessment target. For more information, see <a>CreateAssessmentTarget</a>.</p>"}, "DeleteAssessmentRun": {"name": "DeleteAssessmentRun", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteAssessmentRunRequest"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AssessmentRunInProgressException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}, {"shape": "ServiceTemporarilyUnavailableException"}], "documentation": "<p>Deletes the assessment run that is specified by the ARN of the assessment run.</p>"}, "DeleteAssessmentTarget": {"name": "DeleteAssessmentTarget", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteAssessmentTargetRequest"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AssessmentRunInProgressException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}, {"shape": "ServiceTemporarilyUnavailableException"}], "documentation": "<p>Deletes the assessment target that is specified by the ARN of the assessment target.</p>"}, "DeleteAssessmentTemplate": {"name": "DeleteAssessmentTemplate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteAssessmentTemplateRequest"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AssessmentRunInProgressException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}, {"shape": "ServiceTemporarilyUnavailableException"}], "documentation": "<p>Deletes the assessment template that is specified by the ARN of the assessment template.</p>"}, "DescribeAssessmentRuns": {"name": "DescribeAssessmentRuns", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeAssessmentRunsRequest"}, "output": {"shape": "DescribeAssessmentRunsResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Describes the assessment runs that are specified by the ARNs of the assessment runs.</p>"}, "DescribeAssessmentTargets": {"name": "DescribeAssessmentTargets", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeAssessmentTargetsRequest"}, "output": {"shape": "DescribeAssessmentTargetsResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Describes the assessment targets that are specified by the ARNs of the assessment targets.</p>"}, "DescribeAssessmentTemplates": {"name": "DescribeAssessmentTemplates", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeAssessmentTemplatesRequest"}, "output": {"shape": "DescribeAssessmentTemplatesResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Describes the assessment templates that are specified by the ARNs of the assessment templates.</p>"}, "DescribeCrossAccountAccessRole": {"name": "DescribeCrossAccountAccessRole", "http": {"method": "POST", "requestUri": "/"}, "output": {"shape": "DescribeCrossAccountAccessRoleResponse"}, "errors": [{"shape": "InternalException"}], "documentation": "<p>Describes the IAM role that enables Amazon Inspector to access your AWS account.</p>"}, "DescribeExclusions": {"name": "DescribeExclusions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeExclusionsRequest"}, "output": {"shape": "DescribeExclusionsResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Describes the exclusions that are specified by the exclusions' ARNs.</p>"}, "DescribeFindings": {"name": "DescribeFindings", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeFindingsRequest"}, "output": {"shape": "DescribeFindingsResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Describes the findings that are specified by the ARNs of the findings.</p>"}, "DescribeResourceGroups": {"name": "DescribeResourceGroups", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeResourceGroupsRequest"}, "output": {"shape": "DescribeResourceGroupsResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Describes the resource groups that are specified by the ARNs of the resource groups.</p>"}, "DescribeRulesPackages": {"name": "DescribeRulesPackages", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeRulesPackagesRequest"}, "output": {"shape": "DescribeRulesPackagesResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Describes the rules packages that are specified by the ARNs of the rules packages.</p>"}, "GetAssessmentReport": {"name": "GetAssessmentReport", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetAssessmentReportRequest"}, "output": {"shape": "GetAssessmentReportResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}, {"shape": "AssessmentRunInProgressException"}, {"shape": "UnsupportedFeatureException"}, {"shape": "ServiceTemporarilyUnavailableException"}], "documentation": "<p>Produces an assessment report that includes detailed and comprehensive results of a specified assessment run. </p>"}, "GetExclusionsPreview": {"name": "GetExclusionsPreview", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetExclusionsPreviewRequest"}, "output": {"shape": "GetExclusionsPreviewResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "InternalException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Retrieves the exclusions preview (a list of ExclusionPreview objects) specified by the preview token. You can obtain the preview token by running the CreateExclusionsPreview API.</p>"}, "GetTelemetryMetadata": {"name": "GetTelemetryMetadata", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetTelemetryMetadataRequest"}, "output": {"shape": "GetTelemetryMetadataResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Information about the data that is collected for the specified assessment run.</p>"}, "ListAssessmentRunAgents": {"name": "ListAssessmentRunAgents", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAssessmentRunAgentsRequest"}, "output": {"shape": "ListAssessmentRunAgentsResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Lists the agents of the assessment runs that are specified by the ARNs of the assessment runs.</p>"}, "ListAssessmentRuns": {"name": "ListAssessmentRuns", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAssessmentRunsRequest"}, "output": {"shape": "ListAssessmentRunsResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Lists the assessment runs that correspond to the assessment templates that are specified by the ARNs of the assessment templates.</p>"}, "ListAssessmentTargets": {"name": "ListAssessmentTargets", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAssessmentTargetsRequest"}, "output": {"shape": "ListAssessmentTargetsResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the ARNs of the assessment targets within this AWS account. For more information about assessment targets, see <a href=\"https://docs.aws.amazon.com/inspector/latest/userguide/inspector_applications.html\">Amazon Inspector Assessment Targets</a>.</p>"}, "ListAssessmentTemplates": {"name": "ListAssessmentTemplates", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAssessmentTemplatesRequest"}, "output": {"shape": "ListAssessmentTemplatesResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Lists the assessment templates that correspond to the assessment targets that are specified by the ARNs of the assessment targets.</p>"}, "ListEventSubscriptions": {"name": "ListEventSubscriptions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListEventSubscriptionsRequest"}, "output": {"shape": "ListEventSubscriptionsResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Lists all the event subscriptions for the assessment template that is specified by the ARN of the assessment template. For more information, see <a>SubscribeToEvent</a> and <a>UnsubscribeFromEvent</a>.</p>"}, "ListExclusions": {"name": "ListExclusions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListExclusionsRequest"}, "output": {"shape": "ListExclusionsResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>List exclusions that are generated by the assessment run.</p>"}, "ListFindings": {"name": "ListFindings", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListFindingsRequest"}, "output": {"shape": "ListFindingsResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Lists findings that are generated by the assessment runs that are specified by the ARNs of the assessment runs.</p>"}, "ListRulesPackages": {"name": "ListRulesPackages", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListRulesPackagesRequest"}, "output": {"shape": "ListRulesPackagesResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists all available Amazon Inspector rules packages.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Lists all tags associated with an assessment template.</p>"}, "PreviewAgents": {"name": "PreviewAgents", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PreviewAgentsRequest"}, "output": {"shape": "PreviewAgentsResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}, {"shape": "InvalidCrossAccountRoleException"}], "documentation": "<p>Previews the agents installed on the EC2 instances that are part of the specified assessment target.</p>"}, "RegisterCrossAccountAccessRole": {"name": "RegisterCrossAccountAccessRole", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RegisterCrossAccountAccessRoleRequest"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidCrossAccountRoleException"}, {"shape": "ServiceTemporarilyUnavailableException"}], "documentation": "<p>Registers the IAM role that grants Amazon Inspector access to AWS Services needed to perform security assessments.</p>"}, "RemoveAttributesFromFindings": {"name": "RemoveAttributesFromFindings", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RemoveAttributesFromFindingsRequest"}, "output": {"shape": "RemoveAttributesFromFindingsResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}, {"shape": "ServiceTemporarilyUnavailableException"}], "documentation": "<p>Removes entire attributes (key and value pairs) from the findings that are specified by the ARNs of the findings where an attribute with the specified key exists.</p>"}, "SetTagsForResource": {"name": "SetTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "SetTagsForResourceRequest"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}, {"shape": "ServiceTemporarilyUnavailableException"}], "documentation": "<p>Sets tags (key and value pairs) to the assessment template that is specified by the ARN of the assessment template.</p>"}, "StartAssessmentRun": {"name": "StartAssessmentRun", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartAssessmentRunRequest"}, "output": {"shape": "StartAssessmentRunResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "LimitExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}, {"shape": "InvalidCrossAccountRoleException"}, {"shape": "AgentsAlreadyRunningAssessmentException"}, {"shape": "ServiceTemporarilyUnavailableException"}], "documentation": "<p>Starts the assessment run specified by the ARN of the assessment template. For this API to function properly, you must not exceed the limit of running up to 500 concurrent agents per AWS account.</p>"}, "StopAssessmentRun": {"name": "StopAssessmentRun", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StopAssessmentRunRequest"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}, {"shape": "ServiceTemporarilyUnavailableException"}], "documentation": "<p>Stops the assessment run that is specified by the ARN of the assessment run.</p>"}, "SubscribeToEvent": {"name": "SubscribeToEvent", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "SubscribeToEventRequest"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "LimitExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}, {"shape": "ServiceTemporarilyUnavailableException"}], "documentation": "<p>Enables the process of sending Amazon Simple Notification Service (SNS) notifications about a specified event to a specified SNS topic.</p>"}, "UnsubscribeFromEvent": {"name": "UnsubscribeFromEvent", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UnsubscribeFromEventRequest"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}, {"shape": "ServiceTemporarilyUnavailableException"}], "documentation": "<p>Disables the process of sending Amazon Simple Notification Service (SNS) notifications about a specified event to a specified SNS topic.</p>"}, "UpdateAssessmentTarget": {"name": "UpdateAssessmentTarget", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateAssessmentTargetRequest"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}, {"shape": "ServiceTemporarilyUnavailableException"}], "documentation": "<p>Updates the assessment target that is specified by the ARN of the assessment target.</p> <p>If resourceGroupArn is not specified, all EC2 instances in the current AWS account and region are included in the assessment target.</p>"}}, "shapes": {"AccessDeniedErrorCode": {"type": "string", "enum": ["ACCESS_DENIED_TO_ASSESSMENT_TARGET", "ACCESS_DENIED_TO_ASSESSMENT_TEMPLATE", "ACCESS_DENIED_TO_ASSESSMENT_RUN", "ACCESS_DENIED_TO_FINDING", "ACCESS_DENIED_TO_RESOURCE_GROUP", "ACCESS_DENIED_TO_RULES_PACKAGE", "ACCESS_DENIED_TO_SNS_TOPIC", "ACCESS_DENIED_TO_IAM_ROLE"]}, "AccessDeniedException": {"type": "structure", "required": ["message", "errorCode", "canRetry"], "members": {"message": {"shape": "ErrorMessage", "documentation": "<p>Details of the exception error.</p>"}, "errorCode": {"shape": "AccessDeniedErrorCode", "documentation": "<p>Code that indicates the type of error that is generated.</p>"}, "canRetry": {"shape": "Bool", "documentation": "<p>You can immediately retry your request.</p>"}}, "documentation": "<p>You do not have required permissions to access the requested resource.</p>", "exception": true}, "AddAttributesToFindingsRequest": {"type": "structure", "required": ["findingArns", "attributes"], "members": {"findingArns": {"shape": "AddRemoveAttributesFindingArnList", "documentation": "<p>The ARNs that specify the findings that you want to assign attributes to.</p>"}, "attributes": {"shape": "UserAttributeList", "documentation": "<p>The array of attributes that you want to assign to specified findings.</p>"}}}, "AddAttributesToFindingsResponse": {"type": "structure", "required": ["failedItems"], "members": {"failedItems": {"shape": "FailedItems", "documentation": "<p>Attribute details that cannot be described. An error code is provided for each failed item.</p>"}}}, "AddRemoveAttributesFindingArnList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}, "max": 10, "min": 1}, "AgentAlreadyRunningAssessment": {"type": "structure", "required": ["agentId", "assessmentRunArn"], "members": {"agentId": {"shape": "AgentId", "documentation": "<p>ID of the agent that is running on an EC2 instance that is already participating in another started assessment run.</p>"}, "assessmentRunArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment run that has already been started.</p>"}}, "documentation": "<p>Used in the exception error that is thrown if you start an assessment run for an assessment target that includes an EC2 instance that is already participating in another started assessment run.</p>"}, "AgentAlreadyRunningAssessmentList": {"type": "list", "member": {"shape": "AgentAlreadyRunningAssessment"}, "max": 10, "min": 1}, "AgentFilter": {"type": "structure", "required": ["agentHealths", "agentHealthCodes"], "members": {"agentHealths": {"shape": "AgentHealthList", "documentation": "<p>The current health state of the agent. Values can be set to <b>HEALTHY</b> or <b>UNHEALTHY</b>.</p>"}, "agentHealthCodes": {"shape": "AgentHealthCodeList", "documentation": "<p>The detailed health state of the agent. Values can be set to <b>IDLE</b>, <b>RUNNING</b>, <b>SHUTDOWN</b>, <b>UNHEALTHY</b>, <b>THROTTLED</b>, and <b>UNKNOWN</b>. </p>"}}, "documentation": "<p>Contains information about an Amazon Inspector agent. This data type is used as a request parameter in the <a>ListAssessmentRunAgents</a> action.</p>"}, "AgentHealth": {"type": "string", "enum": ["HEALTHY", "UNHEALTHY", "UNKNOWN"]}, "AgentHealthCode": {"type": "string", "enum": ["IDLE", "RUNNING", "SHUTDOWN", "UNHEALTHY", "THROTTLED", "UNKNOWN"]}, "AgentHealthCodeList": {"type": "list", "member": {"shape": "AgentHealthCode"}, "max": 10, "min": 0}, "AgentHealthList": {"type": "list", "member": {"shape": "AgentHealth"}, "max": 10, "min": 0}, "AgentId": {"type": "string", "max": 128, "min": 1}, "AgentIdList": {"type": "list", "member": {"shape": "AgentId"}, "max": 99, "min": 0}, "AgentPreview": {"type": "structure", "required": ["agentId"], "members": {"hostname": {"shape": "Hostname", "documentation": "<p>The hostname of the EC2 instance on which the Amazon Inspector Agent is installed.</p>"}, "agentId": {"shape": "AgentId", "documentation": "<p>The ID of the EC2 instance where the agent is installed.</p>"}, "autoScalingGroup": {"shape": "AutoScalingGroup", "documentation": "<p>The Auto Scaling group for the EC2 instance where the agent is installed.</p>"}, "agentHealth": {"shape": "AgentHealth", "documentation": "<p>The health status of the Amazon Inspector Agent.</p>"}, "agentVersion": {"shape": "AgentVersion", "documentation": "<p>The version of the Amazon Inspector Agent.</p>"}, "operatingSystem": {"shape": "OperatingSystem", "documentation": "<p>The operating system running on the EC2 instance on which the Amazon Inspector Agent is installed.</p>"}, "kernelVersion": {"shape": "KernelVersion", "documentation": "<p>The kernel version of the operating system running on the EC2 instance on which the Amazon Inspector Agent is installed.</p>"}, "ipv4Address": {"shape": "Ipv4Address", "documentation": "<p>The IP address of the EC2 instance on which the Amazon Inspector Agent is installed.</p>"}}, "documentation": "<p>Used as a response element in the <a>PreviewAgents</a> action.</p>"}, "AgentPreviewList": {"type": "list", "member": {"shape": "AgentPreview"}, "max": 100, "min": 0}, "AgentVersion": {"type": "string", "max": 128, "min": 1}, "AgentsAlreadyRunningAssessmentException": {"type": "structure", "required": ["message", "agents", "agentsTruncated", "canRetry"], "members": {"message": {"shape": "ErrorMessage", "documentation": "<p>Details of the exception error.</p>"}, "agents": {"shape": "AgentAlreadyRunningAssessmentList", "documentation": "<p/>"}, "agentsTruncated": {"shape": "Bool", "documentation": "<p/>"}, "canRetry": {"shape": "Bool", "documentation": "<p>You can immediately retry your request.</p>"}}, "documentation": "<p>You started an assessment run, but one of the instances is already participating in another assessment run.</p>", "exception": true}, "AmiId": {"type": "string", "max": 256, "min": 0}, "Arn": {"type": "string", "max": 300, "min": 1}, "ArnCount": {"type": "integer"}, "AssessmentRulesPackageArnList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}, "max": 50, "min": 1}, "AssessmentRun": {"type": "structure", "required": ["arn", "name", "assessmentTemplateArn", "state", "durationInSeconds", "rulesPackageArns", "userAttributesForFindings", "createdAt", "stateChangedAt", "dataCollected", "stateChanges", "notifications", "findingCounts"], "members": {"arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment run.</p>"}, "name": {"shape": "AssessmentRunName", "documentation": "<p>The auto-generated name for the assessment run.</p>"}, "assessmentTemplateArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment template that is associated with the assessment run.</p>"}, "state": {"shape": "AssessmentRunState", "documentation": "<p>The state of the assessment run.</p>"}, "durationInSeconds": {"shape": "AssessmentRunDuration", "documentation": "<p>The duration of the assessment run.</p>"}, "rulesPackageArns": {"shape": "AssessmentRulesPackageArnList", "documentation": "<p>The rules packages selected for the assessment run.</p>"}, "userAttributesForFindings": {"shape": "UserAttributeList", "documentation": "<p>The user-defined attributes that are assigned to every generated finding.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time when <a>StartAssessmentRun</a> was called.</p>"}, "startedAt": {"shape": "Timestamp", "documentation": "<p>The time when <a>StartAssessmentRun</a> was called.</p>"}, "completedAt": {"shape": "Timestamp", "documentation": "<p>The assessment run completion time that corresponds to the rules packages evaluation completion time or failure.</p>"}, "stateChangedAt": {"shape": "Timestamp", "documentation": "<p>The last time when the assessment run's state changed.</p>"}, "dataCollected": {"shape": "Bool", "documentation": "<p>A Boolean value (true or false) that specifies whether the process of collecting data from the agents is completed.</p>"}, "stateChanges": {"shape": "AssessmentRunStateChangeList", "documentation": "<p>A list of the assessment run state changes.</p>"}, "notifications": {"shape": "AssessmentRunNotificationList", "documentation": "<p>A list of notifications for the event subscriptions. A notification about a particular generated finding is added to this list only once.</p>"}, "findingCounts": {"shape": "AssessmentRunFindingCounts", "documentation": "<p>Provides a total count of generated findings per severity.</p>"}}, "documentation": "<p>A snapshot of an Amazon Inspector assessment run that contains the findings of the assessment run .</p> <p>Used as the response element in the <a>DescribeAssessmentRuns</a> action.</p>"}, "AssessmentRunAgent": {"type": "structure", "required": ["agentId", "assessmentRunArn", "agentHealth", "agentHealthCode", "telemetryMetadata"], "members": {"agentId": {"shape": "AgentId", "documentation": "<p>The AWS account of the EC2 instance where the agent is installed.</p>"}, "assessmentRunArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment run that is associated with the agent.</p>"}, "agentHealth": {"shape": "AgentHealth", "documentation": "<p>The current health state of the agent.</p>"}, "agentHealthCode": {"shape": "AgentHealthCode", "documentation": "<p>The detailed health state of the agent.</p>"}, "agentHealthDetails": {"shape": "Message", "documentation": "<p>The description for the agent health code.</p>"}, "autoScalingGroup": {"shape": "AutoScalingGroup", "documentation": "<p>The Auto Scaling group of the EC2 instance that is specified by the agent ID.</p>"}, "telemetryMetadata": {"shape": "TelemetryMetadataList", "documentation": "<p>The Amazon Inspector application data metrics that are collected by the agent.</p>"}}, "documentation": "<p>Contains information about an Amazon Inspector agent. This data type is used as a response element in the <a>ListAssessmentRunAgents</a> action.</p>"}, "AssessmentRunAgentList": {"type": "list", "member": {"shape": "AssessmentRunAgent"}, "max": 500, "min": 0}, "AssessmentRunDuration": {"type": "integer", "max": 86400, "min": 180}, "AssessmentRunFilter": {"type": "structure", "members": {"namePattern": {"shape": "NamePattern", "documentation": "<p>For a record to match a filter, an explicit value or a string containing a wildcard that is specified for this data type property must match the value of the <b>assessmentRunName</b> property of the <a>AssessmentRun</a> data type.</p>"}, "states": {"shape": "AssessmentRunStateList", "documentation": "<p>For a record to match a filter, one of the values specified for this data type property must be the exact match of the value of the <b>assessmentRunState</b> property of the <a>AssessmentRun</a> data type.</p>"}, "durationRange": {"shape": "DurationRang<PERSON>", "documentation": "<p>For a record to match a filter, the value that is specified for this data type property must inclusively match any value between the specified minimum and maximum values of the <b>durationInSeconds</b> property of the <a>AssessmentRun</a> data type.</p>"}, "rulesPackageArns": {"shape": "FilterRulesPackageArnList", "documentation": "<p>For a record to match a filter, the value that is specified for this data type property must be contained in the list of values of the <b>rulesPackages</b> property of the <a>AssessmentRun</a> data type.</p>"}, "startTimeRange": {"shape": "TimestampRange", "documentation": "<p>For a record to match a filter, the value that is specified for this data type property must inclusively match any value between the specified minimum and maximum values of the <b>startTime</b> property of the <a>AssessmentRun</a> data type.</p>"}, "completionTimeRange": {"shape": "TimestampRange", "documentation": "<p>For a record to match a filter, the value that is specified for this data type property must inclusively match any value between the specified minimum and maximum values of the <b>completedAt</b> property of the <a>AssessmentRun</a> data type.</p>"}, "stateChangeTimeRange": {"shape": "TimestampRange", "documentation": "<p>For a record to match a filter, the value that is specified for this data type property must match the <b>stateChangedAt</b> property of the <a>AssessmentRun</a> data type.</p>"}}, "documentation": "<p>Used as the request parameter in the <a>ListAssessmentRuns</a> action.</p>"}, "AssessmentRunFindingCounts": {"type": "map", "key": {"shape": "Severity"}, "value": {"shape": "FindingCount"}}, "AssessmentRunInProgressArnList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}, "max": 10, "min": 1}, "AssessmentRunInProgressException": {"type": "structure", "required": ["message", "assessmentRunArns", "assessmentRunArnsTruncated", "canRetry"], "members": {"message": {"shape": "ErrorMessage", "documentation": "<p>Details of the exception error.</p>"}, "assessmentRunArns": {"shape": "AssessmentRunInProgressArnList", "documentation": "<p>The ARNs of the assessment runs that are currently in progress.</p>"}, "assessmentRunArnsTruncated": {"shape": "Bool", "documentation": "<p>Boolean value that indicates whether the ARN list of the assessment runs is truncated.</p>"}, "canRetry": {"shape": "Bool", "documentation": "<p>You can immediately retry your request.</p>"}}, "documentation": "<p>You cannot perform a specified action if an assessment run is currently in progress.</p>", "exception": true}, "AssessmentRunList": {"type": "list", "member": {"shape": "AssessmentRun"}, "max": 10, "min": 0}, "AssessmentRunName": {"type": "string", "max": 140, "min": 1}, "AssessmentRunNotification": {"type": "structure", "required": ["date", "event", "error"], "members": {"date": {"shape": "Timestamp", "documentation": "<p>The date of the notification.</p>"}, "event": {"shape": "InspectorEvent", "documentation": "<p>The event for which a notification is sent.</p>"}, "message": {"shape": "Message", "documentation": "<p>The message included in the notification.</p>"}, "error": {"shape": "Bool", "documentation": "<p>The Boolean value that specifies whether the notification represents an error.</p>"}, "snsTopicArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The SNS topic to which the SNS notification is sent.</p>"}, "snsPublishStatusCode": {"shape": "AssessmentRunNotificationSnsStatusCode", "documentation": "<p>The status code of the SNS notification.</p>"}}, "documentation": "<p>Used as one of the elements of the <a>AssessmentRun</a> data type.</p>"}, "AssessmentRunNotificationList": {"type": "list", "member": {"shape": "AssessmentRunNotification"}, "max": 50, "min": 0}, "AssessmentRunNotificationSnsStatusCode": {"type": "string", "enum": ["SUCCESS", "TOPIC_DOES_NOT_EXIST", "ACCESS_DENIED", "INTERNAL_ERROR"]}, "AssessmentRunState": {"type": "string", "enum": ["CREATED", "START_DATA_COLLECTION_PENDING", "START_DATA_COLLECTION_IN_PROGRESS", "COLLECTING_DATA", "STOP_DATA_COLLECTION_PENDING", "DATA_COLLECTED", "START_EVALUATING_RULES_PENDING", "EVALUATING_RULES", "FAILED", "ERROR", "COMPLETED", "COMPLETED_WITH_ERRORS", "CANCELED"]}, "AssessmentRunStateChange": {"type": "structure", "required": ["stateChangedAt", "state"], "members": {"stateChangedAt": {"shape": "Timestamp", "documentation": "<p>The last time the assessment run state changed.</p>"}, "state": {"shape": "AssessmentRunState", "documentation": "<p>The assessment run state.</p>"}}, "documentation": "<p>Used as one of the elements of the <a>AssessmentRun</a> data type.</p>"}, "AssessmentRunStateChangeList": {"type": "list", "member": {"shape": "AssessmentRunStateChange"}, "max": 50, "min": 0}, "AssessmentRunStateList": {"type": "list", "member": {"shape": "AssessmentRunState"}, "max": 50, "min": 0}, "AssessmentTarget": {"type": "structure", "required": ["arn", "name", "createdAt", "updatedAt"], "members": {"arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN that specifies the Amazon Inspector assessment target.</p>"}, "name": {"shape": "AssessmentTargetName", "documentation": "<p>The name of the Amazon Inspector assessment target.</p>"}, "resourceGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN that specifies the resource group that is associated with the assessment target.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time at which the assessment target is created.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The time at which <a>UpdateAssessmentTarget</a> is called.</p>"}}, "documentation": "<p>Contains information about an Amazon Inspector application. This data type is used as the response element in the <a>DescribeAssessmentTargets</a> action.</p>"}, "AssessmentTargetFilter": {"type": "structure", "members": {"assessmentTargetNamePattern": {"shape": "NamePattern", "documentation": "<p>For a record to match a filter, an explicit value or a string that contains a wildcard that is specified for this data type property must match the value of the <b>assessmentTargetName</b> property of the <a>AssessmentTarget</a> data type.</p>"}}, "documentation": "<p>Used as the request parameter in the <a>ListAssessmentTargets</a> action.</p>"}, "AssessmentTargetList": {"type": "list", "member": {"shape": "Assessment<PERSON>arget"}, "max": 10, "min": 0}, "AssessmentTargetName": {"type": "string", "max": 140, "min": 1}, "AssessmentTemplate": {"type": "structure", "required": ["arn", "name", "assessmentTargetArn", "durationInSeconds", "rulesPackageArns", "userAttributesForFindings", "assessmentRunCount", "createdAt"], "members": {"arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment template.</p>"}, "name": {"shape": "AssessmentTemplateName", "documentation": "<p>The name of the assessment template.</p>"}, "assessmentTargetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment target that corresponds to this assessment template.</p>"}, "durationInSeconds": {"shape": "AssessmentRunDuration", "documentation": "<p>The duration in seconds specified for this assessment template. The default value is 3600 seconds (one hour). The maximum value is 86400 seconds (one day).</p>"}, "rulesPackageArns": {"shape": "AssessmentTemplateRulesPackageArnList", "documentation": "<p>The rules packages that are specified for this assessment template.</p>"}, "userAttributesForFindings": {"shape": "UserAttributeList", "documentation": "<p>The user-defined attributes that are assigned to every generated finding from the assessment run that uses this assessment template.</p>"}, "lastAssessmentRunArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the most recent assessment run associated with this assessment template. This value exists only when the value of assessmentRunCount is greaterpa than zero.</p>"}, "assessmentRunCount": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The number of existing assessment runs associated with this assessment template. This value can be zero or a positive integer.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time at which the assessment template is created.</p>"}}, "documentation": "<p>Contains information about an Amazon Inspector assessment template. This data type is used as the response element in the <a>DescribeAssessmentTemplates</a> action.</p>"}, "AssessmentTemplateFilter": {"type": "structure", "members": {"namePattern": {"shape": "NamePattern", "documentation": "<p>For a record to match a filter, an explicit value or a string that contains a wildcard that is specified for this data type property must match the value of the <b>assessmentTemplateName</b> property of the <a>AssessmentTemplate</a> data type.</p>"}, "durationRange": {"shape": "DurationRang<PERSON>", "documentation": "<p>For a record to match a filter, the value specified for this data type property must inclusively match any value between the specified minimum and maximum values of the <b>durationInSeconds</b> property of the <a>AssessmentTemplate</a> data type.</p>"}, "rulesPackageArns": {"shape": "FilterRulesPackageArnList", "documentation": "<p>For a record to match a filter, the values that are specified for this data type property must be contained in the list of values of the <b>rulesPackageArns</b> property of the <a>AssessmentTemplate</a> data type.</p>"}}, "documentation": "<p>Used as the request parameter in the <a>ListAssessmentTemplates</a> action.</p>"}, "AssessmentTemplateList": {"type": "list", "member": {"shape": "AssessmentTemplate"}, "max": 10, "min": 0}, "AssessmentTemplateName": {"type": "string", "max": 140, "min": 1}, "AssessmentTemplateRulesPackageArnList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}, "max": 50, "min": 0}, "AssetAttributes": {"type": "structure", "required": ["schemaVersion"], "members": {"schemaVersion": {"shape": "NumericVersion", "documentation": "<p>The schema version of this data type.</p>"}, "agentId": {"shape": "AgentId", "documentation": "<p>The ID of the agent that is installed on the EC2 instance where the finding is generated.</p>"}, "autoScalingGroup": {"shape": "AutoScalingGroup", "documentation": "<p>The Auto Scaling group of the EC2 instance where the finding is generated.</p>"}, "amiId": {"shape": "AmiId", "documentation": "<p>The ID of the Amazon Machine Image (AMI) that is installed on the EC2 instance where the finding is generated.</p>"}, "hostname": {"shape": "Hostname", "documentation": "<p>The hostname of the EC2 instance where the finding is generated.</p>"}, "ipv4Addresses": {"shape": "Ipv4AddressList", "documentation": "<p>The list of IP v4 addresses of the EC2 instance where the finding is generated.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>The tags related to the EC2 instance where the finding is generated.</p>"}, "networkInterfaces": {"shape": "NetworkInterfaces", "documentation": "<p>An array of the network interfaces interacting with the EC2 instance where the finding is generated.</p>"}}, "documentation": "<p>A collection of attributes of the host from which the finding is generated.</p>"}, "AssetType": {"type": "string", "enum": ["ec2-instance"]}, "Attribute": {"type": "structure", "required": ["key"], "members": {"key": {"shape": "AttributeKey", "documentation": "<p>The attribute key.</p>"}, "value": {"shape": "AttributeValue", "documentation": "<p>The value assigned to the attribute key.</p>"}}, "documentation": "<p>This data type is used as a request parameter in the <a>AddAttributesToFindings</a> and <a>CreateAssessmentTemplate</a> actions.</p>"}, "AttributeKey": {"type": "string", "max": 128, "min": 1}, "AttributeList": {"type": "list", "member": {"shape": "Attribute"}, "max": 50, "min": 0}, "AttributeValue": {"type": "string", "max": 256, "min": 1}, "AutoScalingGroup": {"type": "string", "max": 256, "min": 1}, "AutoScalingGroupList": {"type": "list", "member": {"shape": "AutoScalingGroup"}, "max": 20, "min": 0}, "BatchDescribeArnList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}, "max": 10, "min": 1}, "BatchDescribeExclusionsArnList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}, "max": 100, "min": 1}, "Bool": {"type": "boolean"}, "CreateAssessmentTargetRequest": {"type": "structure", "required": ["assessmentTargetName"], "members": {"assessmentTargetName": {"shape": "AssessmentTargetName", "documentation": "<p>The user-defined name that identifies the assessment target that you want to create. The name must be unique within the AWS account.</p>"}, "resourceGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN that specifies the resource group that is used to create the assessment target. If resourceGroupArn is not specified, all EC2 instances in the current AWS account and region are included in the assessment target.</p>"}}}, "CreateAssessmentTargetResponse": {"type": "structure", "required": ["assessmentTargetArn"], "members": {"assessmentTargetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN that specifies the assessment target that is created.</p>"}}}, "CreateAssessmentTemplateRequest": {"type": "structure", "required": ["assessmentTargetArn", "assessmentTemplateName", "durationInSeconds", "rulesPackageArns"], "members": {"assessmentTargetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN that specifies the assessment target for which you want to create the assessment template.</p>"}, "assessmentTemplateName": {"shape": "AssessmentTemplateName", "documentation": "<p>The user-defined name that identifies the assessment template that you want to create. You can create several assessment templates for an assessment target. The names of the assessment templates that correspond to a particular assessment target must be unique.</p>"}, "durationInSeconds": {"shape": "AssessmentRunDuration", "documentation": "<p>The duration of the assessment run in seconds.</p>"}, "rulesPackageArns": {"shape": "AssessmentTemplateRulesPackageArnList", "documentation": "<p>The ARNs that specify the rules packages that you want to attach to the assessment template.</p>"}, "userAttributesForFindings": {"shape": "UserAttributeList", "documentation": "<p>The user-defined attributes that are assigned to every finding that is generated by the assessment run that uses this assessment template. An attribute is a key and value pair (an <a>Attribute</a> object). Within an assessment template, each key must be unique.</p>"}}}, "CreateAssessmentTemplateResponse": {"type": "structure", "required": ["assessmentTemplateArn"], "members": {"assessmentTemplateArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN that specifies the assessment template that is created.</p>"}}}, "CreateExclusionsPreviewRequest": {"type": "structure", "required": ["assessmentTemplateArn"], "members": {"assessmentTemplateArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN that specifies the assessment template for which you want to create an exclusions preview.</p>"}}}, "CreateExclusionsPreviewResponse": {"type": "structure", "required": ["previewToken"], "members": {"previewToken": {"shape": "UUID", "documentation": "<p>Specifies the unique identifier of the requested exclusions preview. You can use the unique identifier to retrieve the exclusions preview when running the GetExclusionsPreview API.</p>"}}}, "CreateResourceGroupRequest": {"type": "structure", "required": ["resourceGroupTags"], "members": {"resourceGroupTags": {"shape": "ResourceGroupTags", "documentation": "<p>A collection of keys and an array of possible values, '[{\"key\":\"key1\",\"values\":[\"Value1\",\"Value2\"]},{\"key\":\"Key2\",\"values\":[\"Value3\"]}]'.</p> <p>For example,'[{\"key\":\"Name\",\"values\":[\"TestEC2Instance\"]}]'.</p>"}}}, "CreateResourceGroupResponse": {"type": "structure", "required": ["resourceGroupArn"], "members": {"resourceGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN that specifies the resource group that is created.</p>"}}}, "DeleteAssessmentRunRequest": {"type": "structure", "required": ["assessmentRunArn"], "members": {"assessmentRunArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN that specifies the assessment run that you want to delete.</p>"}}}, "DeleteAssessmentTargetRequest": {"type": "structure", "required": ["assessmentTargetArn"], "members": {"assessmentTargetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN that specifies the assessment target that you want to delete.</p>"}}}, "DeleteAssessmentTemplateRequest": {"type": "structure", "required": ["assessmentTemplateArn"], "members": {"assessmentTemplateArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN that specifies the assessment template that you want to delete.</p>"}}}, "DescribeAssessmentRunsRequest": {"type": "structure", "required": ["assessmentRunArns"], "members": {"assessmentRunArns": {"shape": "BatchDescribeArnList", "documentation": "<p>The ARN that specifies the assessment run that you want to describe.</p>"}}}, "DescribeAssessmentRunsResponse": {"type": "structure", "required": ["assessmentRuns", "failedItems"], "members": {"assessmentRuns": {"shape": "AssessmentRunList", "documentation": "<p>Information about the assessment run.</p>"}, "failedItems": {"shape": "FailedItems", "documentation": "<p>Assessment run details that cannot be described. An error code is provided for each failed item.</p>"}}}, "DescribeAssessmentTargetsRequest": {"type": "structure", "required": ["assessmentTargetArns"], "members": {"assessmentTargetArns": {"shape": "BatchDescribeArnList", "documentation": "<p>The ARNs that specifies the assessment targets that you want to describe.</p>"}}}, "DescribeAssessmentTargetsResponse": {"type": "structure", "required": ["assessmentTargets", "failedItems"], "members": {"assessmentTargets": {"shape": "AssessmentTargetList", "documentation": "<p>Information about the assessment targets.</p>"}, "failedItems": {"shape": "FailedItems", "documentation": "<p>Assessment target details that cannot be described. An error code is provided for each failed item.</p>"}}}, "DescribeAssessmentTemplatesRequest": {"type": "structure", "required": ["assessmentTemplateArns"], "members": {"assessmentTemplateArns": {"shape": "BatchDescribeArnList"}}}, "DescribeAssessmentTemplatesResponse": {"type": "structure", "required": ["assessmentTemplates", "failedItems"], "members": {"assessmentTemplates": {"shape": "AssessmentTemplateList", "documentation": "<p>Information about the assessment templates.</p>"}, "failedItems": {"shape": "FailedItems", "documentation": "<p>Assessment template details that cannot be described. An error code is provided for each failed item.</p>"}}}, "DescribeCrossAccountAccessRoleResponse": {"type": "structure", "required": ["roleArn", "valid", "registeredAt"], "members": {"roleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN that specifies the IAM role that Amazon Inspector uses to access your AWS account.</p>"}, "valid": {"shape": "Bool", "documentation": "<p>A Boolean value that specifies whether the IAM role has the necessary policies attached to enable Amazon Inspector to access your AWS account.</p>"}, "registeredAt": {"shape": "Timestamp", "documentation": "<p>The date when the cross-account access role was registered.</p>"}}}, "DescribeExclusionsRequest": {"type": "structure", "required": ["exclusionArns"], "members": {"exclusionArns": {"shape": "BatchDescribeExclusionsArnList", "documentation": "<p>The list of ARNs that specify the exclusions that you want to describe.</p>"}, "locale": {"shape": "Locale", "documentation": "<p>The locale into which you want to translate the exclusion's title, description, and recommendation.</p>"}}}, "DescribeExclusionsResponse": {"type": "structure", "required": ["exclusions", "failedItems"], "members": {"exclusions": {"shape": "ExclusionMap", "documentation": "<p>Information about the exclusions.</p>"}, "failedItems": {"shape": "FailedItems", "documentation": "<p>Exclusion details that cannot be described. An error code is provided for each failed item.</p>"}}}, "DescribeFindingsRequest": {"type": "structure", "required": ["findingArns"], "members": {"findingArns": {"shape": "BatchDescribeArnList", "documentation": "<p>The ARN that specifies the finding that you want to describe.</p>"}, "locale": {"shape": "Locale", "documentation": "<p>The locale into which you want to translate a finding description, recommendation, and the short description that identifies the finding.</p>"}}}, "DescribeFindingsResponse": {"type": "structure", "required": ["findings", "failedItems"], "members": {"findings": {"shape": "FindingList", "documentation": "<p>Information about the finding.</p>"}, "failedItems": {"shape": "FailedItems", "documentation": "<p>Finding details that cannot be described. An error code is provided for each failed item.</p>"}}}, "DescribeResourceGroupsRequest": {"type": "structure", "required": ["resourceGroupArns"], "members": {"resourceGroupArns": {"shape": "BatchDescribeArnList", "documentation": "<p>The ARN that specifies the resource group that you want to describe.</p>"}}}, "DescribeResourceGroupsResponse": {"type": "structure", "required": ["resourceGroups", "failedItems"], "members": {"resourceGroups": {"shape": "ResourceGroupList", "documentation": "<p>Information about a resource group.</p>"}, "failedItems": {"shape": "FailedItems", "documentation": "<p>Resource group details that cannot be described. An error code is provided for each failed item.</p>"}}}, "DescribeRulesPackagesRequest": {"type": "structure", "required": ["rulesPackageArns"], "members": {"rulesPackageArns": {"shape": "BatchDescribeArnList", "documentation": "<p>The ARN that specifies the rules package that you want to describe.</p>"}, "locale": {"shape": "Locale", "documentation": "<p>The locale that you want to translate a rules package description into.</p>"}}}, "DescribeRulesPackagesResponse": {"type": "structure", "required": ["rulesPackages", "failedItems"], "members": {"rulesPackages": {"shape": "RulesPackageList", "documentation": "<p>Information about the rules package.</p>"}, "failedItems": {"shape": "FailedItems", "documentation": "<p>Rules package details that cannot be described. An error code is provided for each failed item.</p>"}}}, "DurationRange": {"type": "structure", "members": {"minSeconds": {"shape": "AssessmentRunDuration", "documentation": "<p>The minimum value of the duration range. Must be greater than zero.</p>"}, "maxSeconds": {"shape": "AssessmentRunDuration", "documentation": "<p>The maximum value of the duration range. Must be less than or equal to 604800 seconds (1 week).</p>"}}, "documentation": "<p>This data type is used in the <a>AssessmentTemplateFilter</a> data type.</p>"}, "ErrorMessage": {"type": "string", "max": 1000, "min": 0}, "EventSubscription": {"type": "structure", "required": ["event", "subscribedAt"], "members": {"event": {"shape": "InspectorEvent", "documentation": "<p>The event for which Amazon Simple Notification Service (SNS) notifications are sent.</p>"}, "subscribedAt": {"shape": "Timestamp", "documentation": "<p>The time at which <a>SubscribeToEvent</a> is called.</p>"}}, "documentation": "<p>This data type is used in the <a>Subscription</a> data type.</p>"}, "EventSubscriptionList": {"type": "list", "member": {"shape": "EventSubscription"}, "max": 50, "min": 1}, "Exclusion": {"type": "structure", "required": ["arn", "title", "description", "recommendation", "scopes"], "members": {"arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN that specifies the exclusion.</p>"}, "title": {"shape": "Text", "documentation": "<p>The name of the exclusion.</p>"}, "description": {"shape": "Text", "documentation": "<p>The description of the exclusion.</p>"}, "recommendation": {"shape": "Text", "documentation": "<p>The recommendation for the exclusion.</p>"}, "scopes": {"shape": "ScopeList", "documentation": "<p>The AWS resources for which the exclusion pertains.</p>"}, "attributes": {"shape": "AttributeList", "documentation": "<p>The system-defined attributes for the exclusion.</p>"}}, "documentation": "<p>Contains information about what was excluded from an assessment run.</p>"}, "ExclusionMap": {"type": "map", "key": {"shape": "<PERSON><PERSON>"}, "value": {"shape": "Exclusion"}, "max": 100, "min": 1}, "ExclusionPreview": {"type": "structure", "required": ["title", "description", "recommendation", "scopes"], "members": {"title": {"shape": "Text", "documentation": "<p>The name of the exclusion preview.</p>"}, "description": {"shape": "Text", "documentation": "<p>The description of the exclusion preview.</p>"}, "recommendation": {"shape": "Text", "documentation": "<p>The recommendation for the exclusion preview.</p>"}, "scopes": {"shape": "ScopeList", "documentation": "<p>The AWS resources for which the exclusion preview pertains.</p>"}, "attributes": {"shape": "AttributeList", "documentation": "<p>The system-defined attributes for the exclusion preview.</p>"}}, "documentation": "<p>Contains information about what is excluded from an assessment run given the current state of the assessment template.</p>"}, "ExclusionPreviewList": {"type": "list", "member": {"shape": "ExclusionPreview"}, "max": 100, "min": 0}, "FailedItemDetails": {"type": "structure", "required": ["failureCode", "retryable"], "members": {"failureCode": {"shape": "FailedItemErrorCode", "documentation": "<p>The status code of a failed item.</p>"}, "retryable": {"shape": "Bool", "documentation": "<p>Indicates whether you can immediately retry a request for this item for a specified resource.</p>"}}, "documentation": "<p>Includes details about the failed items.</p>"}, "FailedItemErrorCode": {"type": "string", "enum": ["INVALID_ARN", "DUPLICATE_ARN", "ITEM_DOES_NOT_EXIST", "ACCESS_DENIED", "LIMIT_EXCEEDED", "INTERNAL_ERROR"]}, "FailedItems": {"type": "map", "key": {"shape": "<PERSON><PERSON>"}, "value": {"shape": "FailedItemDetails"}}, "FilterRulesPackageArnList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}, "max": 50, "min": 0}, "Finding": {"type": "structure", "required": ["arn", "attributes", "userAttributes", "createdAt", "updatedAt"], "members": {"arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN that specifies the finding.</p>"}, "schemaVersion": {"shape": "NumericVersion", "documentation": "<p>The schema version of this data type.</p>"}, "service": {"shape": "ServiceName", "documentation": "<p>The data element is set to \"Inspector\".</p>"}, "serviceAttributes": {"shape": "InspectorServiceAttributes", "documentation": "<p>This data type is used in the <a>Finding</a> data type.</p>"}, "assetType": {"shape": "AssetType", "documentation": "<p>The type of the host from which the finding is generated.</p>"}, "assetAttributes": {"shape": "AssetAttributes", "documentation": "<p>A collection of attributes of the host from which the finding is generated.</p>"}, "id": {"shape": "FindingId", "documentation": "<p>The ID of the finding.</p>"}, "title": {"shape": "Text", "documentation": "<p>The name of the finding.</p>"}, "description": {"shape": "Text", "documentation": "<p>The description of the finding.</p>"}, "recommendation": {"shape": "Text", "documentation": "<p>The recommendation for the finding.</p>"}, "severity": {"shape": "Severity", "documentation": "<p>The finding severity. Values can be set to High, Medium, Low, and Informational.</p>"}, "numericSeverity": {"shape": "NumericSeverity", "documentation": "<p>The numeric value of the finding severity.</p>"}, "confidence": {"shape": "IocConfidence", "documentation": "<p>This data element is currently not used.</p>"}, "indicatorOfCompromise": {"shape": "Bool", "documentation": "<p>This data element is currently not used.</p>"}, "attributes": {"shape": "AttributeList", "documentation": "<p>The system-defined attributes for the finding.</p>"}, "userAttributes": {"shape": "UserAttributeList", "documentation": "<p>The user-defined attributes that are assigned to the finding.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the finding was generated.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The time when <a>AddAttributesToFindings</a> is called.</p>"}}, "documentation": "<p>Contains information about an Amazon Inspector finding. This data type is used as the response element in the <a>DescribeFindings</a> action.</p>"}, "FindingCount": {"type": "integer"}, "FindingFilter": {"type": "structure", "members": {"agentIds": {"shape": "AgentIdList", "documentation": "<p>For a record to match a filter, one of the values that is specified for this data type property must be the exact match of the value of the <b>agentId</b> property of the <a>Finding</a> data type.</p>"}, "autoScalingGroups": {"shape": "AutoScalingGroupList", "documentation": "<p>For a record to match a filter, one of the values that is specified for this data type property must be the exact match of the value of the <b>autoScalingGroup</b> property of the <a>Finding</a> data type.</p>"}, "ruleNames": {"shape": "RuleNameList", "documentation": "<p>For a record to match a filter, one of the values that is specified for this data type property must be the exact match of the value of the <b>ruleName</b> property of the <a>Finding</a> data type.</p>"}, "severities": {"shape": "SeverityList", "documentation": "<p>For a record to match a filter, one of the values that is specified for this data type property must be the exact match of the value of the <b>severity</b> property of the <a>Finding</a> data type.</p>"}, "rulesPackageArns": {"shape": "FilterRulesPackageArnList", "documentation": "<p>For a record to match a filter, one of the values that is specified for this data type property must be the exact match of the value of the <b>rulesPackageArn</b> property of the <a>Finding</a> data type.</p>"}, "attributes": {"shape": "AttributeList", "documentation": "<p>For a record to match a filter, the list of values that are specified for this data type property must be contained in the list of values of the <b>attributes</b> property of the <a>Finding</a> data type.</p>"}, "userAttributes": {"shape": "AttributeList", "documentation": "<p>For a record to match a filter, the value that is specified for this data type property must be contained in the list of values of the <b>userAttributes</b> property of the <a>Finding</a> data type.</p>"}, "creationTimeRange": {"shape": "TimestampRange", "documentation": "<p>The time range during which the finding is generated.</p>"}}, "documentation": "<p>This data type is used as a request parameter in the <a>ListFindings</a> action.</p>"}, "FindingId": {"type": "string", "max": 128, "min": 0}, "FindingList": {"type": "list", "member": {"shape": "Finding"}, "max": 100, "min": 0}, "GetAssessmentReportRequest": {"type": "structure", "required": ["assessmentRunArn", "reportFileFormat", "reportType"], "members": {"assessmentRunArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN that specifies the assessment run for which you want to generate a report.</p>"}, "reportFileFormat": {"shape": "ReportFileFormat", "documentation": "<p>Specifies the file format (html or pdf) of the assessment report that you want to generate.</p>"}, "reportType": {"shape": "ReportType", "documentation": "<p>Specifies the type of the assessment report that you want to generate. There are two types of assessment reports: a finding report and a full report. For more information, see <a href=\"https://docs.aws.amazon.com/inspector/latest/userguide/inspector_reports.html\">Assessment Reports</a>. </p>"}}}, "GetAssessmentReportResponse": {"type": "structure", "required": ["status"], "members": {"status": {"shape": "ReportStatus", "documentation": "<p>Specifies the status of the request to generate an assessment report. </p>"}, "url": {"shape": "Url", "documentation": "<p>Specifies the URL where you can find the generated assessment report. This parameter is only returned if the report is successfully generated.</p>"}}}, "GetExclusionsPreviewRequest": {"type": "structure", "required": ["assessmentTemplateArn", "previewToken"], "members": {"assessmentTemplateArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN that specifies the assessment template for which the exclusions preview was requested.</p>"}, "previewToken": {"shape": "UUID", "documentation": "<p>The unique identifier associated of the exclusions preview.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the GetExclusionsPreviewRequest action. Subsequent calls to the action fill nextToken in the request with the value of nextToken from the previous response to continue listing data.</p>"}, "maxResults": {"shape": "ListMaxResults", "documentation": "<p>You can use this parameter to indicate the maximum number of items you want in the response. The default value is 100. The maximum value is 500.</p>"}, "locale": {"shape": "Locale", "documentation": "<p>The locale into which you want to translate the exclusion's title, description, and recommendation.</p>"}}}, "GetExclusionsPreviewResponse": {"type": "structure", "required": ["previewStatus"], "members": {"previewStatus": {"shape": "PreviewStatus", "documentation": "<p>Specifies the status of the request to generate an exclusions preview.</p>"}, "exclusionPreviews": {"shape": "ExclusionPreviewList", "documentation": "<p>Information about the exclusions included in the preview.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>When a response is generated, if there is more data to be listed, this parameters is present in the response and contains the value to use for the nextToken parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to null.</p>"}}}, "GetTelemetryMetadataRequest": {"type": "structure", "required": ["assessmentRunArn"], "members": {"assessmentRunArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN that specifies the assessment run that has the telemetry data that you want to obtain.</p>"}}}, "GetTelemetryMetadataResponse": {"type": "structure", "required": ["telemetryMetadata"], "members": {"telemetryMetadata": {"shape": "TelemetryMetadataList", "documentation": "<p>Telemetry details.</p>"}}}, "Hostname": {"type": "string", "max": 256, "min": 0}, "InspectorEvent": {"type": "string", "enum": ["ASSESSMENT_RUN_STARTED", "ASSESSMENT_RUN_COMPLETED", "ASSESSMENT_RUN_STATE_CHANGED", "FINDING_REPORTED", "OTHER"]}, "InspectorServiceAttributes": {"type": "structure", "required": ["schemaVersion"], "members": {"schemaVersion": {"shape": "NumericVersion", "documentation": "<p>The schema version of this data type.</p>"}, "assessmentRunArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment run during which the finding is generated.</p>"}, "rulesPackageArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the rules package that is used to generate the finding.</p>"}}, "documentation": "<p>This data type is used in the <a>Finding</a> data type.</p>"}, "InternalException": {"type": "structure", "required": ["message", "canRetry"], "members": {"message": {"shape": "ErrorMessage", "documentation": "<p>Details of the exception error.</p>"}, "canRetry": {"shape": "Bool", "documentation": "<p>You can immediately retry your request.</p>"}}, "documentation": "<p>Internal server error.</p>", "exception": true, "fault": true}, "InvalidCrossAccountRoleErrorCode": {"type": "string", "enum": ["ROLE_DOES_NOT_EXIST_OR_INVALID_TRUST_RELATIONSHIP", "ROLE_DOES_NOT_HAVE_CORRECT_POLICY"]}, "InvalidCrossAccountRoleException": {"type": "structure", "required": ["message", "errorCode", "canRetry"], "members": {"message": {"shape": "ErrorMessage", "documentation": "<p>Details of the exception error.</p>"}, "errorCode": {"shape": "InvalidCrossAccountRoleErrorCode", "documentation": "<p>Code that indicates the type of error that is generated.</p>"}, "canRetry": {"shape": "Bool", "documentation": "<p>You can immediately retry your request.</p>"}}, "documentation": "<p>Amazon Inspector cannot assume the cross-account role that it needs to list your EC2 instances during the assessment run.</p>", "exception": true}, "InvalidInputErrorCode": {"type": "string", "enum": ["INVALID_ASSESSMENT_TARGET_ARN", "INVALID_ASSESSMENT_TEMPLATE_ARN", "INVALID_ASSESSMENT_RUN_ARN", "INVALID_FINDING_ARN", "INVALID_RESOURCE_GROUP_ARN", "INVALID_RULES_PACKAGE_ARN", "INVALID_RESOURCE_ARN", "INVALID_SNS_TOPIC_ARN", "INVALID_IAM_ROLE_ARN", "INVALID_ASSESSMENT_TARGET_NAME", "INVALID_ASSESSMENT_TARGET_NAME_PATTERN", "INVALID_ASSESSMENT_TEMPLATE_NAME", "INVALID_ASSESSMENT_TEMPLATE_NAME_PATTERN", "INVALID_ASSESSMENT_TEMPLATE_DURATION", "INVALID_ASSESSMENT_TEMPLATE_DURATION_RANGE", "INVALID_ASSESSMENT_RUN_DURATION_RANGE", "INVALID_ASSESSMENT_RUN_START_TIME_RANGE", "INVALID_ASSESSMENT_RUN_COMPLETION_TIME_RANGE", "INVALID_ASSESSMENT_RUN_STATE_CHANGE_TIME_RANGE", "INVALID_ASSESSMENT_RUN_STATE", "INVALID_TAG", "INVALID_TAG_KEY", "INVALID_TAG_VALUE", "INVALID_RESOURCE_GROUP_TAG_KEY", "INVALID_RESOURCE_GROUP_TAG_VALUE", "INVALID_ATTRIBUTE", "INVALID_USER_ATTRIBUTE", "INVALID_USER_ATTRIBUTE_KEY", "INVALID_USER_ATTRIBUTE_VALUE", "INVALID_PAGINATION_TOKEN", "INVALID_MAX_RESULTS", "INVALID_AGENT_ID", "INVALID_AUTO_SCALING_GROUP", "INVALID_RULE_NAME", "INVALID_SEVERITY", "INVALID_LOCALE", "INVALID_EVENT", "ASSESSMENT_TARGET_NAME_ALREADY_TAKEN", "ASSESSMENT_TEMPLATE_NAME_ALREADY_TAKEN", "INVALID_NUMBER_OF_ASSESSMENT_TARGET_ARNS", "INVALID_NUMBER_OF_ASSESSMENT_TEMPLATE_ARNS", "INVALID_NUMBER_OF_ASSESSMENT_RUN_ARNS", "INVALID_NUMBER_OF_FINDING_ARNS", "INVALID_NUMBER_OF_RESOURCE_GROUP_ARNS", "INVALID_NUMBER_OF_RULES_PACKAGE_ARNS", "INVALID_NUMBER_OF_ASSESSMENT_RUN_STATES", "INVALID_NUMBER_OF_TAGS", "INVALID_NUMBER_OF_RESOURCE_GROUP_TAGS", "INVALID_NUMBER_OF_ATTRIBUTES", "INVALID_NUMBER_OF_USER_ATTRIBUTES", "INVALID_NUMBER_OF_AGENT_IDS", "INVALID_NUMBER_OF_AUTO_SCALING_GROUPS", "INVALID_NUMBER_OF_RULE_NAMES", "INVALID_NUMBER_OF_SEVERITIES"]}, "InvalidInputException": {"type": "structure", "required": ["message", "errorCode", "canRetry"], "members": {"message": {"shape": "ErrorMessage", "documentation": "<p>Details of the exception error.</p>"}, "errorCode": {"shape": "InvalidInputErrorCode", "documentation": "<p>Code that indicates the type of error that is generated.</p>"}, "canRetry": {"shape": "Bool", "documentation": "<p>You can immediately retry your request.</p>"}}, "documentation": "<p>The request was rejected because an invalid or out-of-range value was supplied for an input parameter.</p>", "exception": true}, "IocConfidence": {"type": "integer", "max": 10, "min": 0}, "Ipv4Address": {"type": "string", "max": 15, "min": 7}, "Ipv4AddressList": {"type": "list", "member": {"shape": "Ipv4Address"}, "max": 50, "min": 0}, "Ipv6Addresses": {"type": "list", "member": {"shape": "Text"}}, "KernelVersion": {"type": "string", "max": 128, "min": 1}, "LimitExceededErrorCode": {"type": "string", "enum": ["ASSESSMENT_TARGET_LIMIT_EXCEEDED", "ASSESSMENT_TEMPLATE_LIMIT_EXCEEDED", "ASSESSMENT_RUN_LIMIT_EXCEEDED", "RESOURCE_GROUP_LIMIT_EXCEEDED", "EVENT_SUBSCRIPTION_LIMIT_EXCEEDED"]}, "LimitExceededException": {"type": "structure", "required": ["message", "errorCode", "canRetry"], "members": {"message": {"shape": "ErrorMessage", "documentation": "<p>Details of the exception error.</p>"}, "errorCode": {"shape": "LimitExceededErrorCode", "documentation": "<p>Code that indicates the type of error that is generated.</p>"}, "canRetry": {"shape": "Bool", "documentation": "<p>You can immediately retry your request.</p>"}}, "documentation": "<p>The request was rejected because it attempted to create resources beyond the current AWS account limits. The error code describes the limit exceeded.</p>", "exception": true}, "ListAssessmentRunAgentsRequest": {"type": "structure", "required": ["assessmentRunArn"], "members": {"assessmentRunArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN that specifies the assessment run whose agents you want to list.</p>"}, "filter": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>You can use this parameter to specify a subset of data to be included in the action's response.</p> <p>For a record to match a filter, all specified filter attributes must match. When multiple values are specified for a filter attribute, any of the values can match.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the <b>ListAssessmentRunAgents</b> action. Subsequent calls to the action fill <b>nextToken</b> in the request with the value of <b>NextToken</b> from the previous response to continue listing data.</p>"}, "maxResults": {"shape": "ListMaxResults", "documentation": "<p>You can use this parameter to indicate the maximum number of items that you want in the response. The default value is 10. The maximum value is 500.</p>"}}}, "ListAssessmentRunAgentsResponse": {"type": "structure", "required": ["assessmentRunAgents"], "members": {"assessmentRunAgents": {"shape": "AssessmentRunAgentList", "documentation": "<p>A list of ARNs that specifies the agents returned by the action.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the <b>nextToken</b> parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to null.</p>"}}}, "ListAssessmentRunsRequest": {"type": "structure", "members": {"assessmentTemplateArns": {"shape": "ListParentArnList", "documentation": "<p>The ARNs that specify the assessment templates whose assessment runs you want to list.</p>"}, "filter": {"shape": "AssessmentRunFilter", "documentation": "<p>You can use this parameter to specify a subset of data to be included in the action's response.</p> <p>For a record to match a filter, all specified filter attributes must match. When multiple values are specified for a filter attribute, any of the values can match.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the <b>ListAssessmentRuns</b> action. Subsequent calls to the action fill <b>nextToken</b> in the request with the value of <b>NextToken</b> from the previous response to continue listing data.</p>"}, "maxResults": {"shape": "ListMaxResults", "documentation": "<p>You can use this parameter to indicate the maximum number of items that you want in the response. The default value is 10. The maximum value is 500.</p>"}}}, "ListAssessmentRunsResponse": {"type": "structure", "required": ["assessmentRunArns"], "members": {"assessmentRunArns": {"shape": "ListReturnedArnList", "documentation": "<p>A list of ARNs that specifies the assessment runs that are returned by the action.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the <b>nextToken</b> parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to null.</p>"}}}, "ListAssessmentTargetsRequest": {"type": "structure", "members": {"filter": {"shape": "AssessmentTargetFilter", "documentation": "<p>You can use this parameter to specify a subset of data to be included in the action's response.</p> <p>For a record to match a filter, all specified filter attributes must match. When multiple values are specified for a filter attribute, any of the values can match.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the <b>ListAssessmentTargets</b> action. Subsequent calls to the action fill <b>nextToken</b> in the request with the value of <b>NextToken</b> from the previous response to continue listing data.</p>"}, "maxResults": {"shape": "ListMaxResults", "documentation": "<p>You can use this parameter to indicate the maximum number of items you want in the response. The default value is 10. The maximum value is 500.</p>"}}}, "ListAssessmentTargetsResponse": {"type": "structure", "required": ["assessmentTargetArns"], "members": {"assessmentTargetArns": {"shape": "ListReturnedArnList", "documentation": "<p>A list of ARNs that specifies the assessment targets that are returned by the action.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the <b>nextToken</b> parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to null.</p>"}}}, "ListAssessmentTemplatesRequest": {"type": "structure", "members": {"assessmentTargetArns": {"shape": "ListParentArnList", "documentation": "<p>A list of ARNs that specifies the assessment targets whose assessment templates you want to list.</p>"}, "filter": {"shape": "AssessmentTemplateFilter", "documentation": "<p>You can use this parameter to specify a subset of data to be included in the action's response.</p> <p>For a record to match a filter, all specified filter attributes must match. When multiple values are specified for a filter attribute, any of the values can match.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the <b>ListAssessmentTemplates</b> action. Subsequent calls to the action fill <b>nextToken</b> in the request with the value of <b>NextToken</b> from the previous response to continue listing data.</p>"}, "maxResults": {"shape": "ListMaxResults", "documentation": "<p>You can use this parameter to indicate the maximum number of items you want in the response. The default value is 10. The maximum value is 500.</p>"}}}, "ListAssessmentTemplatesResponse": {"type": "structure", "required": ["assessmentTemplateArns"], "members": {"assessmentTemplateArns": {"shape": "ListReturnedArnList", "documentation": "<p>A list of ARNs that specifies the assessment templates returned by the action.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the <b>nextToken</b> parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to null.</p>"}}}, "ListEventSubscriptionsMaxResults": {"type": "integer"}, "ListEventSubscriptionsRequest": {"type": "structure", "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment template for which you want to list the existing event subscriptions.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the <b>ListEventSubscriptions</b> action. Subsequent calls to the action fill <b>nextToken</b> in the request with the value of <b>NextToken</b> from the previous response to continue listing data.</p>"}, "maxResults": {"shape": "ListEventSubscriptionsMaxResults", "documentation": "<p>You can use this parameter to indicate the maximum number of items you want in the response. The default value is 10. The maximum value is 500.</p>"}}}, "ListEventSubscriptionsResponse": {"type": "structure", "required": ["subscriptions"], "members": {"subscriptions": {"shape": "SubscriptionList", "documentation": "<p>Details of the returned event subscriptions.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the <b>nextToken</b> parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to null.</p>"}}}, "ListExclusionsRequest": {"type": "structure", "required": ["assessmentRunArn"], "members": {"assessmentRunArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment run that generated the exclusions that you want to list.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the ListExclusionsRequest action. Subsequent calls to the action fill nextToken in the request with the value of nextToken from the previous response to continue listing data.</p>"}, "maxResults": {"shape": "ListMaxResults", "documentation": "<p>You can use this parameter to indicate the maximum number of items you want in the response. The default value is 100. The maximum value is 500.</p>"}}}, "ListExclusionsResponse": {"type": "structure", "required": ["exclusionArns"], "members": {"exclusionArns": {"shape": "ListReturnedArnList", "documentation": "<p>A list of exclusions' ARNs returned by the action.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>When a response is generated, if there is more data to be listed, this parameters is present in the response and contains the value to use for the nextToken parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to null.</p>"}}}, "ListFindingsRequest": {"type": "structure", "members": {"assessmentRunArns": {"shape": "ListParentArnList", "documentation": "<p>The ARNs of the assessment runs that generate the findings that you want to list.</p>"}, "filter": {"shape": "<PERSON><PERSON><PERSON>er", "documentation": "<p>You can use this parameter to specify a subset of data to be included in the action's response.</p> <p>For a record to match a filter, all specified filter attributes must match. When multiple values are specified for a filter attribute, any of the values can match.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the <b>ListFindings</b> action. Subsequent calls to the action fill <b>nextToken</b> in the request with the value of <b>NextToken</b> from the previous response to continue listing data.</p>"}, "maxResults": {"shape": "ListMaxResults", "documentation": "<p>You can use this parameter to indicate the maximum number of items you want in the response. The default value is 10. The maximum value is 500.</p>"}}}, "ListFindingsResponse": {"type": "structure", "required": ["findingArns"], "members": {"findingArns": {"shape": "ListReturnedArnList", "documentation": "<p>A list of ARNs that specifies the findings returned by the action.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the <b>nextToken</b> parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to null.</p>"}}}, "ListMaxResults": {"type": "integer"}, "ListParentArnList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}, "max": 50, "min": 0}, "ListReturnedArnList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}, "max": 100, "min": 0}, "ListRulesPackagesRequest": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the <b>ListRulesPackages</b> action. Subsequent calls to the action fill <b>nextToken</b> in the request with the value of <b>NextToken</b> from the previous response to continue listing data.</p>"}, "maxResults": {"shape": "ListMaxResults", "documentation": "<p>You can use this parameter to indicate the maximum number of items you want in the response. The default value is 10. The maximum value is 500.</p>"}}}, "ListRulesPackagesResponse": {"type": "structure", "required": ["rulesPackageArns"], "members": {"rulesPackageArns": {"shape": "ListReturnedArnList", "documentation": "<p>The list of ARNs that specifies the rules packages returned by the action.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the <b>nextToken</b> parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to null.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN that specifies the assessment template whose tags you want to list.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "required": ["tags"], "members": {"tags": {"shape": "TagList", "documentation": "<p>A collection of key and value pairs.</p>"}}}, "Locale": {"type": "string", "enum": ["EN_US"]}, "Long": {"type": "long"}, "Message": {"type": "string", "max": 1000, "min": 0}, "MessageType": {"type": "string", "max": 300, "min": 1}, "NamePattern": {"type": "string", "max": 140, "min": 1}, "NetworkInterface": {"type": "structure", "members": {"networkInterfaceId": {"shape": "Text", "documentation": "<p>The ID of the network interface.</p>"}, "subnetId": {"shape": "Text", "documentation": "<p>The ID of a subnet associated with the network interface.</p>"}, "vpcId": {"shape": "Text", "documentation": "<p>The ID of a VPC associated with the network interface.</p>"}, "privateDnsName": {"shape": "Text", "documentation": "<p>The name of a private DNS associated with the network interface.</p>"}, "privateIpAddress": {"shape": "Text", "documentation": "<p>The private IP address associated with the network interface.</p>"}, "privateIpAddresses": {"shape": "PrivateIpAddresses", "documentation": "<p>A list of the private IP addresses associated with the network interface. Includes the privateDnsName and privateIpAddress.</p>"}, "publicDnsName": {"shape": "Text", "documentation": "<p>The name of a public DNS associated with the network interface.</p>"}, "publicIp": {"shape": "Text", "documentation": "<p>The public IP address from which the network interface is reachable.</p>"}, "ipv6Addresses": {"shape": "Ipv6Addresses", "documentation": "<p>The IP addresses associated with the network interface.</p>"}, "securityGroups": {"shape": "SecurityGroups", "documentation": "<p>A list of the security groups associated with the network interface. Includes the groupId and groupName.</p>"}}, "documentation": "<p>Contains information about the network interfaces interacting with an EC2 instance. This data type is used as one of the elements of the <a>AssetAttributes</a> data type.</p>"}, "NetworkInterfaces": {"type": "list", "member": {"shape": "NetworkInterface"}}, "NoSuchEntityErrorCode": {"type": "string", "enum": ["ASSESSMENT_TARGET_DOES_NOT_EXIST", "ASSESSMENT_TEMPLATE_DOES_NOT_EXIST", "ASSESSMENT_RUN_DOES_NOT_EXIST", "FINDING_DOES_NOT_EXIST", "RESOURCE_GROUP_DOES_NOT_EXIST", "RULES_PACKAGE_DOES_NOT_EXIST", "SNS_TOPIC_DOES_NOT_EXIST", "IAM_ROLE_DOES_NOT_EXIST"]}, "NoSuchEntityException": {"type": "structure", "required": ["message", "errorCode", "canRetry"], "members": {"message": {"shape": "ErrorMessage", "documentation": "<p>Details of the exception error.</p>"}, "errorCode": {"shape": "NoSuchEntityErrorCode", "documentation": "<p>Code that indicates the type of error that is generated.</p>"}, "canRetry": {"shape": "Bool", "documentation": "<p>You can immediately retry your request.</p>"}}, "documentation": "<p>The request was rejected because it referenced an entity that does not exist. The error code describes the entity.</p>", "exception": true}, "NumericSeverity": {"type": "double", "max": 10.0, "min": 0.0}, "NumericVersion": {"type": "integer", "min": 0}, "OperatingSystem": {"type": "string", "max": 256, "min": 1}, "PaginationToken": {"type": "string", "max": 300, "min": 1}, "PreviewAgentsMaxResults": {"type": "integer"}, "PreviewAgentsRequest": {"type": "structure", "required": ["previewAgentsArn"], "members": {"previewAgentsArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment target whose agents you want to preview.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the <b>PreviewAgents</b> action. Subsequent calls to the action fill <b>nextToken</b> in the request with the value of <b>NextToken</b> from the previous response to continue listing data.</p>"}, "maxResults": {"shape": "PreviewAgentsMaxResults", "documentation": "<p>You can use this parameter to indicate the maximum number of items you want in the response. The default value is 10. The maximum value is 500.</p>"}}}, "PreviewAgentsResponse": {"type": "structure", "required": ["agentPreviews"], "members": {"agentPreviews": {"shape": "AgentPreviewList", "documentation": "<p>The resulting list of agents.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the <b>nextToken</b> parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to null.</p>"}}}, "PreviewGenerationInProgressException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The request is rejected. The specified assessment template is currently generating an exclusions preview.</p>", "exception": true}, "PreviewStatus": {"type": "string", "enum": ["WORK_IN_PROGRESS", "COMPLETED"]}, "PrivateIp": {"type": "structure", "members": {"privateDnsName": {"shape": "Text", "documentation": "<p>The DNS name of the private IP address.</p>"}, "privateIpAddress": {"shape": "Text", "documentation": "<p>The full IP address of the network inteface.</p>"}}, "documentation": "<p>Contains information about a private IP address associated with a network interface. This data type is used as a response element in the <a>DescribeFindings</a> action.</p>"}, "PrivateIpAddresses": {"type": "list", "member": {"shape": "PrivateIp"}}, "ProviderName": {"type": "string", "max": 1000, "min": 0}, "RegisterCrossAccountAccessRoleRequest": {"type": "structure", "required": ["roleArn"], "members": {"roleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the IAM role that grants Amazon Inspector access to AWS Services needed to perform security assessments. </p>"}}}, "RemoveAttributesFromFindingsRequest": {"type": "structure", "required": ["findingArns", "<PERSON><PERSON><PERSON><PERSON>"], "members": {"findingArns": {"shape": "AddRemoveAttributesFindingArnList", "documentation": "<p>The ARNs that specify the findings that you want to remove attributes from.</p>"}, "attributeKeys": {"shape": "UserAttributeKeyList", "documentation": "<p>The array of attribute keys that you want to remove from specified findings.</p>"}}}, "RemoveAttributesFromFindingsResponse": {"type": "structure", "required": ["failedItems"], "members": {"failedItems": {"shape": "FailedItems", "documentation": "<p>Attributes details that cannot be described. An error code is provided for each failed item.</p>"}}}, "ReportFileFormat": {"type": "string", "enum": ["HTML", "PDF"]}, "ReportStatus": {"type": "string", "enum": ["WORK_IN_PROGRESS", "FAILED", "COMPLETED"]}, "ReportType": {"type": "string", "enum": ["FINDING", "FULL"]}, "ResourceGroup": {"type": "structure", "required": ["arn", "tags", "createdAt"], "members": {"arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the resource group.</p>"}, "tags": {"shape": "ResourceGroupTags", "documentation": "<p>The tags (key and value pairs) of the resource group. This data type property is used in the <a>CreateResourceGroup</a> action.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time at which resource group is created.</p>"}}, "documentation": "<p>Contains information about a resource group. The resource group defines a set of tags that, when queried, identify the AWS resources that make up the assessment target. This data type is used as the response element in the <a>DescribeResourceGroups</a> action.</p>"}, "ResourceGroupList": {"type": "list", "member": {"shape": "ResourceGroup"}, "max": 10, "min": 0}, "ResourceGroupTag": {"type": "structure", "required": ["key"], "members": {"key": {"shape": "TagKey", "documentation": "<p>A tag key.</p>"}, "value": {"shape": "TagValue", "documentation": "<p>The value assigned to a tag key.</p>"}}, "documentation": "<p>This data type is used as one of the elements of the <a>ResourceGroup</a> data type.</p>"}, "ResourceGroupTags": {"type": "list", "member": {"shape": "ResourceGroupTag"}, "max": 10, "min": 1}, "RuleName": {"type": "string", "max": 1000}, "RuleNameList": {"type": "list", "member": {"shape": "RuleName"}, "max": 50, "min": 0}, "RulesPackage": {"type": "structure", "required": ["arn", "name", "version", "provider"], "members": {"arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the rules package.</p>"}, "name": {"shape": "RulesPackageName", "documentation": "<p>The name of the rules package.</p>"}, "version": {"shape": "Version", "documentation": "<p>The version ID of the rules package.</p>"}, "provider": {"shape": "ProviderName", "documentation": "<p>The provider of the rules package.</p>"}, "description": {"shape": "Text", "documentation": "<p>The description of the rules package.</p>"}}, "documentation": "<p>Contains information about an Amazon Inspector rules package. This data type is used as the response element in the <a>DescribeRulesPackages</a> action.</p>"}, "RulesPackageList": {"type": "list", "member": {"shape": "RulesPackage"}, "max": 10, "min": 0}, "RulesPackageName": {"type": "string", "max": 1000, "min": 0}, "Scope": {"type": "structure", "members": {"key": {"shape": "ScopeType", "documentation": "<p>The type of the scope.</p>"}, "value": {"shape": "ScopeValue", "documentation": "<p>The resource identifier for the specified scope type.</p>"}}, "documentation": "<p>This data type contains key-value pairs that identify various Amazon resources.</p>"}, "ScopeList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}, "min": 1}, "ScopeType": {"type": "string", "enum": ["INSTANCE_ID", "RULES_PACKAGE_ARN"]}, "ScopeValue": {"type": "string"}, "SecurityGroup": {"type": "structure", "members": {"groupName": {"shape": "Text", "documentation": "<p>The name of the security group.</p>"}, "groupId": {"shape": "Text", "documentation": "<p>The ID of the security group.</p>"}}, "documentation": "<p>Contains information about a security group associated with a network interface. This data type is used as one of the elements of the <a>NetworkInterface</a> data type.</p>"}, "SecurityGroups": {"type": "list", "member": {"shape": "SecurityGroup"}}, "ServiceName": {"type": "string", "max": 128, "min": 0}, "ServiceTemporarilyUnavailableException": {"type": "structure", "required": ["message", "canRetry"], "members": {"message": {"shape": "ErrorMessage", "documentation": "<p>Details of the exception error.</p>"}, "canRetry": {"shape": "Bool", "documentation": "<p>You can wait and then retry your request.</p>"}}, "documentation": "<p>The serice is temporary unavailable.</p>", "exception": true}, "SetTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment template that you want to set tags to.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>A collection of key and value pairs that you want to set to the assessment template.</p>"}}}, "Severity": {"type": "string", "enum": ["Low", "Medium", "High", "Informational", "Undefined"]}, "SeverityList": {"type": "list", "member": {"shape": "Severity"}, "max": 50, "min": 0}, "StartAssessmentRunRequest": {"type": "structure", "required": ["assessmentTemplateArn"], "members": {"assessmentTemplateArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment template of the assessment run that you want to start.</p>"}, "assessmentRunName": {"shape": "AssessmentRunName", "documentation": "<p>You can specify the name for the assessment run. The name must be unique for the assessment template whose ARN is used to start the assessment run.</p>"}}}, "StartAssessmentRunResponse": {"type": "structure", "required": ["assessmentRunArn"], "members": {"assessmentRunArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment run that has been started.</p>"}}}, "StopAction": {"type": "string", "enum": ["START_EVALUATION", "SKIP_EVALUATION"]}, "StopAssessmentRunRequest": {"type": "structure", "required": ["assessmentRunArn"], "members": {"assessmentRunArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment run that you want to stop.</p>"}, "stopAction": {"shape": "StopAction", "documentation": "<p>An input option that can be set to either START_EVALUATION or SKIP_EVALUATION. START_EVALUATION (the default value), stops the AWS agent from collecting data and begins the results evaluation and the findings generation process. SKIP_EVALUATION cancels the assessment run immediately, after which no findings are generated.</p>"}}}, "SubscribeToEventRequest": {"type": "structure", "required": ["resourceArn", "event", "topicArn"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment template that is used during the event for which you want to receive SNS notifications.</p>"}, "event": {"shape": "InspectorEvent", "documentation": "<p>The event for which you want to receive SNS notifications.</p>"}, "topicArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the SNS topic to which the SNS notifications are sent.</p>"}}}, "Subscription": {"type": "structure", "required": ["resourceArn", "topicArn", "eventSubscriptions"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment template that is used during the event for which the SNS notification is sent.</p>"}, "topicArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the Amazon Simple Notification Service (SNS) topic to which the SNS notifications are sent.</p>"}, "eventSubscriptions": {"shape": "EventSubscriptionList", "documentation": "<p>The list of existing event subscriptions.</p>"}}, "documentation": "<p>This data type is used as a response element in the <a>ListEventSubscriptions</a> action.</p>"}, "SubscriptionList": {"type": "list", "member": {"shape": "Subscription"}, "max": 50, "min": 0}, "Tag": {"type": "structure", "required": ["key"], "members": {"key": {"shape": "TagKey", "documentation": "<p>A tag key.</p>"}, "value": {"shape": "TagValue", "documentation": "<p>A value assigned to a tag key.</p>"}}, "documentation": "<p>A key and value pair. This data type is used as a request parameter in the <a>SetTagsForResource</a> action and a response element in the <a>ListTagsForResource</a> action.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "max": 10, "min": 0}, "TagValue": {"type": "string", "max": 256, "min": 1}, "Tags": {"type": "list", "member": {"shape": "Tag"}}, "TelemetryMetadata": {"type": "structure", "required": ["messageType", "count"], "members": {"messageType": {"shape": "MessageType", "documentation": "<p>A specific type of behavioral data that is collected by the agent.</p>"}, "count": {"shape": "<PERSON>", "documentation": "<p>The count of messages that the agent sends to the Amazon Inspector service.</p>"}, "dataSize": {"shape": "<PERSON>", "documentation": "<p>The data size of messages that the agent sends to the Amazon Inspector service.</p>"}}, "documentation": "<p>The metadata about the Amazon Inspector application data metrics collected by the agent. This data type is used as the response element in the <a>GetTelemetryMetadata</a> action.</p>"}, "TelemetryMetadataList": {"type": "list", "member": {"shape": "TelemetryMetadata"}, "max": 5000, "min": 0}, "Text": {"type": "string", "max": 20000, "min": 0}, "Timestamp": {"type": "timestamp"}, "TimestampRange": {"type": "structure", "members": {"beginDate": {"shape": "Timestamp", "documentation": "<p>The minimum value of the timestamp range.</p>"}, "endDate": {"shape": "Timestamp", "documentation": "<p>The maximum value of the timestamp range.</p>"}}, "documentation": "<p>This data type is used in the <a>AssessmentRunFilter</a> data type.</p>"}, "UUID": {"type": "string", "pattern": "[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"}, "UnsubscribeFromEventRequest": {"type": "structure", "required": ["resourceArn", "event", "topicArn"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment template that is used during the event for which you want to stop receiving SNS notifications.</p>"}, "event": {"shape": "InspectorEvent", "documentation": "<p>The event for which you want to stop receiving SNS notifications.</p>"}, "topicArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the SNS topic to which SNS notifications are sent.</p>"}}}, "UnsupportedFeatureException": {"type": "structure", "required": ["message", "canRetry"], "members": {"message": {"shape": "ErrorMessage"}, "canRetry": {"shape": "Bool"}}, "documentation": "<p>Used by the <a>GetAssessmentReport</a> API. The request was rejected because you tried to generate a report for an assessment run that existed before reporting was supported in Amazon Inspector. You can only generate reports for assessment runs that took place or will take place after generating reports in Amazon Inspector became available.</p>", "exception": true}, "UpdateAssessmentTargetRequest": {"type": "structure", "required": ["assessmentTargetArn", "assessmentTargetName"], "members": {"assessmentTargetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment target that you want to update.</p>"}, "assessmentTargetName": {"shape": "AssessmentTargetName", "documentation": "<p>The name of the assessment target that you want to update.</p>"}, "resourceGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the resource group that is used to specify the new resource group to associate with the assessment target.</p>"}}}, "Url": {"type": "string", "max": 2048}, "UserAttributeKeyList": {"type": "list", "member": {"shape": "AttributeKey"}, "max": 10, "min": 0}, "UserAttributeList": {"type": "list", "member": {"shape": "Attribute"}, "max": 10, "min": 0}, "Version": {"type": "string", "max": 1000, "min": 0}}, "documentation": "<fullname>Amazon Inspector</fullname> <p>Amazon Inspector enables you to analyze the behavior of your AWS resources and to identify potential security issues. For more information, see <a href=\"https://docs.aws.amazon.com/inspector/latest/userguide/inspector_introduction.html\"> Amazon Inspector User Guide</a>.</p>"}