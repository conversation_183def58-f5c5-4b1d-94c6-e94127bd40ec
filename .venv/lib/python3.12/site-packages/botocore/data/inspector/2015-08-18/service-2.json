{"version": "2.0", "metadata": {"apiVersion": "2015-08-18", "endpointPrefix": "inspector", "jsonVersion": "1.1", "protocol": "json", "serviceFullName": "Amazon Inspector", "serviceId": "Inspector", "signatureVersion": "v4", "targetPrefix": "InspectorService"}, "operations": {"AddAttributesToFindings": {"name": "AddAttributesToFindings", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AddAttributesToFindingsRequest"}, "output": {"shape": "AddAttributesToFindingsResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Assigns attributes (key and value pair) to the findings specified by the findings' ARNs.</p>"}, "AttachAssessmentAndRulesPackage": {"name": "AttachAssessmentAndRulesPackage", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AttachAssessmentAndRulesPackageRequest"}, "output": {"shape": "AttachAssessmentAndRulesPackageResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Attaches the rules package specified by the rules package ARN to the assessment specified by the assessment ARN.</p>"}, "CreateApplication": {"name": "CreateApplication", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateApplicationRequest"}, "output": {"shape": "CreateApplicationResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Creates a new application using the resource group ARN generated by <a>CreateResourceGroup</a>. You can create up to 50 applications per AWS account. You can run up to 500 concurrent agents per AWS account. For more information, see <a href=\"https://docs.aws.amazon.com/inspector/latest/userguide//inspector_applications.html\"> Inspector Applications.</a></p>"}, "CreateAssessment": {"name": "CreateAssessment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateAssessmentRequest"}, "output": {"shape": "CreateAssessmentResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Creates an assessment for the application specified by the application ARN. You can create up to 500 assessments per AWS account.</p>"}, "CreateResourceGroup": {"name": "CreateResourceGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateResourceGroupRequest"}, "output": {"shape": "CreateResourceGroupResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates a resource group using the specified set of tags (key and value pairs) that are used to select the EC2 instances to be included in an Inspector application. The created resource group is then used to create an Inspector application.</p>"}, "DeleteApplication": {"name": "DeleteApplication", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteApplicationRequest"}, "output": {"shape": "DeleteApplicationResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "OperationInProgressException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Deletes the application specified by the application ARN.</p>"}, "DeleteAssessment": {"name": "DeleteAssessment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteAssessmentRequest"}, "output": {"shape": "DeleteAssessmentResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "OperationInProgressException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Deletes the assessment specified by the assessment ARN.</p>"}, "DeleteRun": {"name": "DeleteRun", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteRunRequest"}, "output": {"shape": "DeleteRunResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Deletes the assessment run specified by the run ARN.</p>"}, "DescribeApplication": {"name": "DescribeApplication", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeApplicationRequest"}, "output": {"shape": "DescribeApplicationResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Describes the application specified by the application ARN.</p>"}, "DescribeAssessment": {"name": "DescribeAssessment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeAssessmentRequest"}, "output": {"shape": "DescribeAssessmentResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Describes the assessment specified by the assessment ARN.</p>"}, "DescribeCrossAccountAccessRole": {"name": "DescribeCrossAccountAccessRole", "http": {"method": "POST", "requestUri": "/"}, "output": {"shape": "DescribeCrossAccountAccessRoleResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Describes the IAM role that enables Inspector to access your AWS account.</p>"}, "DescribeFinding": {"name": "DescribeFinding", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeFindingRequest"}, "output": {"shape": "DescribeFindingResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Describes the finding specified by the finding <PERSON>N.</p>"}, "DescribeResourceGroup": {"name": "DescribeResourceGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeResourceGroupRequest"}, "output": {"shape": "DescribeResourceGroupResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Describes the resource group specified by the resource group ARN.</p>"}, "DescribeRulesPackage": {"name": "DescribeRulesPackage", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeRulesPackageRequest"}, "output": {"shape": "DescribeRulesPackageResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Describes the rules package specified by the rules package ARN.</p>"}, "DescribeRun": {"name": "DescribeRun", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeRunRequest"}, "output": {"shape": "DescribeRunResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Describes the assessment run specified by the run ARN.</p>"}, "DetachAssessmentAndRulesPackage": {"name": "DetachAssessmentAndRulesPackage", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DetachAssessmentAndRulesPackageRequest"}, "output": {"shape": "DetachAssessmentAndRulesPackageResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Detaches the rules package specified by the rules package ARN from the assessment specified by the assessment ARN.</p>"}, "GetAssessmentTelemetry": {"name": "GetAssessmentTelemetry", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetAssessmentTelemetryRequest"}, "output": {"shape": "GetAssessmentTelemetryResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Returns the metadata about the telemetry (application behavioral data) for the assessment specified by the assessment ARN.</p>"}, "ListApplications": {"name": "ListApplications", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListApplicationsRequest"}, "output": {"shape": "ListApplicationsResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the ARNs of the applications within this AWS account. For more information about applications, see <a href=\"https://docs.aws.amazon.com/inspector/latest/userguide//inspector_applications.html\">Inspector Applications</a>.</p>"}, "ListAssessmentAgents": {"name": "ListAssessmentAgents", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAssessmentAgentsRequest"}, "output": {"shape": "ListAssessmentAgentsResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Lists the agents of the assessment specified by the assessment ARN.</p>"}, "ListAssessments": {"name": "ListAssessments", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAssessmentsRequest"}, "output": {"shape": "ListAssessmentsResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Lists the assessments corresponding to applications specified by the applications' ARNs.</p>"}, "ListAttachedAssessments": {"name": "ListAttachedAssessments", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAttachedAssessmentsRequest"}, "output": {"shape": "ListAttachedAssessmentsResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Lists the assessments attached to the rules package specified by the rules package ARN.</p>"}, "ListAttachedRulesPackages": {"name": "ListAttachedRulesPackages", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAttachedRulesPackagesRequest"}, "output": {"shape": "ListAttachedRulesPackagesResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Lists the rules packages attached to the assessment specified by the assessment ARN.</p>"}, "ListFindings": {"name": "ListFindings", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListFindingsRequest"}, "output": {"shape": "ListFindingsResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Lists findings generated by the assessment run specified by the run ARNs.</p>"}, "ListRulesPackages": {"name": "ListRulesPackages", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListRulesPackagesRequest"}, "output": {"shape": "ListRulesPackagesResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists all available Inspector rules packages.</p>"}, "ListRuns": {"name": "ListRuns", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListRunsRequest"}, "output": {"shape": "ListRunsResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Lists the assessment runs associated with the assessments specified by the assessment ARNs.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Lists all tags associated with a resource.</p>"}, "LocalizeText": {"name": "LocalizeText", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "LocalizeTextRequest"}, "output": {"shape": "LocalizeTextResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Translates a textual identifier into a user-readable text in a specified locale.</p>"}, "PreviewAgentsForResourceGroup": {"name": "PreviewAgentsForResourceGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PreviewAgentsForResourceGroupRequest"}, "output": {"shape": "PreviewAgentsForResourceGroupResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}, {"shape": "InvalidCrossAccountRoleException"}], "documentation": "<p>Previews the agents installed on the EC2 instances that are included in the application created with the specified resource group.</p>"}, "RegisterCrossAccountAccessRole": {"name": "RegisterCrossAccountAccessRole", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RegisterCrossAccountAccessRoleRequest"}, "output": {"shape": "RegisterCrossAccountAccessRoleResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidCrossAccountRoleException"}], "documentation": "<p>Register the role that Inspector uses to list your EC2 instances during the assessment.</p>"}, "RemoveAttributesFromFindings": {"name": "RemoveAttributesFromFindings", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RemoveAttributesFromFindingsRequest"}, "output": {"shape": "RemoveAttributesFromFindingsResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Removes the entire attribute (key and value pair) from the findings specified by the finding ARNs where an attribute with the specified key exists.</p>"}, "RunAssessment": {"name": "RunAssessment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RunAssessmentRequest"}, "output": {"shape": "RunAssessmentResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Starts the analysis of the application’s behavior against selected rule packages for the assessment specified by the assessment ARN.</p>"}, "SetTagsForResource": {"name": "SetTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "SetTagsForResourceRequest"}, "output": {"shape": "SetTagsForResourceResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Sets tags (key and value pairs) to the assessment specified by the assessment ARN.</p>"}, "StartDataCollection": {"name": "StartDataCollection", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartDataCollectionRequest"}, "output": {"shape": "StartDataCollectionResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}, {"shape": "InvalidCrossAccountRoleException"}], "documentation": "<p>Starts data collection for the assessment specified by the assessment ARN. For this API to function properly, you must not exceed the limit of running up to 500 concurrent agents per AWS account.</p>"}, "StopDataCollection": {"name": "StopDataCollection", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StopDataCollectionRequest"}, "output": {"shape": "StopDataCollectionResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Stop data collection for the assessment specified by the assessment ARN.</p>"}, "UpdateApplication": {"name": "UpdateApplication", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateApplicationRequest"}, "output": {"shape": "UpdateApplicationResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Updates application specified by the application ARN.</p>"}, "UpdateAssessment": {"name": "UpdateAssessment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateAssessmentRequest"}, "output": {"shape": "UpdateAssessmentResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "InvalidInputException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchEntityException"}], "documentation": "<p>Updates the assessment specified by the assessment ARN.</p>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {}, "exception": true}, "AddAttributesToFindingsRequest": {"type": "structure", "required": ["findingArns", "attributes"], "members": {"findingArns": {"shape": "ArnList", "documentation": "<p>The ARNs specifying the findings that you want to assign attributes to.</p>"}, "attributes": {"shape": "AttributeList", "documentation": "<p>The array of attributes that you want to assign to specified findings.</p>"}}}, "AddAttributesToFindingsResponse": {"type": "structure", "members": {"message": {"shape": "Message", "documentation": "<p>Confirmation details of the action performed.</p>"}}}, "Agent": {"type": "structure", "members": {"agentId": {"shape": "AgentId", "documentation": "<p>The EC2 instance ID where the agent is installed.</p>"}, "assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment that is associated with the agent.</p>"}, "agentHealth": {"shape": "AgentHealth", "documentation": "<p>The current health state of the agent. Values can be set to <i>HEALTHY</i> or <i>UNHEALTHY</i>.</p>"}, "agentHealthCode": {"shape": "AgentHealthCode", "documentation": "<p>The detailed health state of the agent. Values can be set to <i>RUNNING</i>, <i>HEALTHY</i>, <i>UNHEALTHY</i>, <i>UNKNOWN</i>, <i>BLACKLISTED</i>, <i>SHUTDOWN</i>, <i>THROTTLED</i>. </p>"}, "agentHealthDetails": {"shape": "AgentHealthDetails", "documentation": "<p>The description for the agent health code.</p>"}, "autoScalingGroup": {"shape": "AutoScalingGroup", "documentation": "<p>This data type property is currently not used.</p>"}, "accountId": {"shape": "AwsAccount", "documentation": "<p>AWS account of the EC2 instance where the agent is installed.</p>"}, "telemetry": {"shape": "TelemetryList", "documentation": "<p>The Inspector application data metrics collected by the agent. </p>"}}, "documentation": "<p>Contains information about an Inspector agent. This data type is used as a response element in the <a>ListAssessmentAgents</a> action.</p>"}, "AgentHealth": {"type": "string"}, "AgentHealthCode": {"type": "string"}, "AgentHealthDetails": {"type": "string"}, "AgentHealthList": {"type": "list", "member": {"shape": "AgentHealth"}}, "AgentId": {"type": "string"}, "AgentList": {"type": "list", "member": {"shape": "Agent"}}, "AgentPreview": {"type": "structure", "members": {"agentId": {"shape": "AgentId", "documentation": "<p>The id of the EC2 instance where the agent is intalled.</p>"}, "autoScalingGroup": {"shape": "AutoScalingGroup", "documentation": "<p>The autoscaling group for the EC2 instance where the agent is installed.</p>"}}, "documentation": "<p>This data type is used as a response element in the <a>PreviewAgentsForResourceGroup</a> action.</p>"}, "AgentPreviewList": {"type": "list", "member": {"shape": "AgentPreview"}}, "AgentsFilter": {"type": "structure", "members": {"agentHealthList": {"shape": "AgentHealthList", "documentation": "<p>For a record to match a filter, the value specified for this data type property must be the exact match of the value of the <b>agentHealth</b> property of the <a>Agent</a> data type.</p>"}}, "documentation": "<p>This data type is used as a response element in the <a>ListAssessmentAgents</a> action.</p>"}, "Application": {"type": "structure", "members": {"applicationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the Inspector application. </p>"}, "applicationName": {"shape": "Name", "documentation": "<p>The name of the Inspector application. </p>"}, "resourceGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the resource group that is associated with the application. </p>"}}, "documentation": "<p>Contains information about an Inspector application.</p> <p>This data type is used as the response element in the <a>DescribeApplication</a> action.</p>"}, "ApplicationsFilter": {"type": "structure", "members": {"applicationNamePatterns": {"shape": "NamePatternList", "documentation": "<p>For a record to match a filter, an explicit value or a string containing a wildcard specified for this data type property must match the value of the <b>applicationName</b> property of the <a>Application</a> data type.</p>"}}, "documentation": "<p>This data type is used as the request parameter in the <a>ListApplications</a> action.</p>"}, "Arn": {"type": "string"}, "ArnList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}}, "Assessment": {"type": "structure", "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment.</p>"}, "assessmentName": {"shape": "Name", "documentation": "<p>The name of the assessment.</p>"}, "applicationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the application that corresponds to this assessment.</p>"}, "assessmentState": {"shape": "AssessmentState", "documentation": "<p>The state of the assessment. Values can be set to <i>Created</i>, <i>Collecting Data</i>, <i>Stopping</i>, and <i>Completed</i>.</p>"}, "failureMessage": {"shape": "FailureMessage", "documentation": "<p>This data type property is not currently used.</p>"}, "dataCollected": {"shape": "Bool", "documentation": "<p>Boolean value (true or false) specifying whether the data collection process is completed.</p>"}, "startTime": {"shape": "Timestamp", "documentation": "<p>The assessment start time.</p>"}, "endTime": {"shape": "Timestamp", "documentation": "<p>The assessment end time.</p>"}, "durationInSeconds": {"shape": "Duration", "documentation": "<p>The assessment duration in seconds. The default value is 3600 seconds (one hour). The maximum value is 86400 seconds (one day).</p>"}, "userAttributesForFindings": {"shape": "AttributeList", "documentation": "<p>The user-defined attributes that are assigned to every generated finding.</p>"}}, "documentation": "<p>Contains information about an Inspector assessment.</p> <p>This data type is used as the response element in the <a>DescribeAssessment</a> action.</p>"}, "AssessmentState": {"type": "string"}, "AssessmentStateList": {"type": "list", "member": {"shape": "AssessmentState"}}, "AssessmentsFilter": {"type": "structure", "members": {"assessmentNamePatterns": {"shape": "NamePatternList", "documentation": "<p>For a record to match a filter, an explicit value or a string containing a wildcard specified for this data type property must match the value of the <b>assessmentName</b> property of the <a>Assessment</a> data type.</p>"}, "assessmentStates": {"shape": "AssessmentStateList", "documentation": "<p>For a record to match a filter, the value specified for this data type property must be the exact match of the value of the <b>assessmentState</b> property of the <a>Assessment</a> data type.</p>"}, "dataCollected": {"shape": "Bool", "documentation": "<p>For a record to match a filter, the value specified for this data type property must be the exact match of the value of the <b>dataCollected</b> property of the <a>Assessment</a> data type.</p>"}, "startTimeRange": {"shape": "TimestampRange", "documentation": "<p>For a record to match a filter, the value specified for this data type property must inclusively match any value between the specified minimum and maximum values of the <b>startTime</b> property of the <a>Assessment</a> data type.</p>"}, "endTimeRange": {"shape": "TimestampRange", "documentation": "<p>For a record to match a filter, the value specified for this data type property must inclusively match any value between the specified minimum and maximum values of the <b>endTime</b> property of the <a>Assessment</a> data type.</p>"}, "durationRange": {"shape": "DurationRang<PERSON>", "documentation": "<p>For a record to match a filter, the value specified for this data type property must inclusively match any value between the specified minimum and maximum values of the <b>durationInSeconds</b> property of the <a>Assessment</a> data type.</p>"}}, "documentation": "<p>This data type is used as the request parameter in the <a>ListAssessments</a> and <a>ListAttachedAssessments</a> actions.</p>"}, "AttachAssessmentAndRulesPackageRequest": {"type": "structure", "required": ["assessmentArn", "rulesPackageArn"], "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the assessment to which you want to attach a rules package.</p>"}, "rulesPackageArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the rules package that you want to attach to the assessment.</p>"}}}, "AttachAssessmentAndRulesPackageResponse": {"type": "structure", "members": {"message": {"shape": "Message", "documentation": "<p>Confirmation details of the action performed.</p>"}}}, "Attribute": {"type": "structure", "members": {"key": {"shape": "AttributeKey", "documentation": "<p>The attribute key.</p>"}, "value": {"shape": "AttributeValue", "documentation": "<p>The value assigned to the attribute key.</p>"}}, "documentation": "<p>This data type is used as a response element in the <a>AddAttributesToFindings</a> action and a request parameter in the <a>CreateAssessment</a> action.</p>"}, "AttributeKey": {"type": "string"}, "AttributeKeyList": {"type": "list", "member": {"shape": "AttributeKey"}}, "AttributeList": {"type": "list", "member": {"shape": "Attribute"}}, "AttributeValue": {"type": "string"}, "AutoScalingGroup": {"type": "string"}, "AwsAccount": {"type": "string"}, "Bool": {"type": "boolean"}, "CreateApplicationRequest": {"type": "structure", "required": ["applicationName", "resourceGroupArn"], "members": {"applicationName": {"shape": "Name", "documentation": "<p>The user-defined name identifying the application that you want to create. The name must be unique within the AWS account.</p>"}, "resourceGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the resource group that is used to create the application.</p>"}}}, "CreateApplicationResponse": {"type": "structure", "members": {"applicationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the application that is created.</p>"}}}, "CreateAssessmentRequest": {"type": "structure", "required": ["applicationArn", "assessmentName", "durationInSeconds"], "members": {"applicationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the application for which you want to create an assessment.</p>"}, "assessmentName": {"shape": "Name", "documentation": "<p>The user-defined name identifying the assessment that you want to create. You can create several assessments for an application. The names of the assessments corresponding to a particular application must be unique.</p>"}, "durationInSeconds": {"shape": "Duration", "documentation": "<p>The duration of the assessment in seconds. The default value is 3600 seconds (one hour). The maximum value is 86400 seconds (one day).</p>"}, "userAttributesForFindings": {"shape": "AttributeList", "documentation": "<p>The user-defined attributes that are assigned to every finding generated by running this assessment.</p>"}}}, "CreateAssessmentResponse": {"type": "structure", "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the assessment that is created.</p>"}}}, "CreateResourceGroupRequest": {"type": "structure", "required": ["resourceGroupTags"], "members": {"resourceGroupTags": {"shape": "ResourceGroupTags", "documentation": "<p>A collection of keys and an array of possible values in JSON format.</p> <p>For example, [{ \"key1\" : [\"Value1\",\"Value2\"]},{\"Key2\": [\"Value3\"]}]</p>"}}}, "CreateResourceGroupResponse": {"type": "structure", "members": {"resourceGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the resource group that is created.</p>"}}}, "DeleteApplicationRequest": {"type": "structure", "required": ["applicationArn"], "members": {"applicationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the application that you want to delete.</p>"}}}, "DeleteApplicationResponse": {"type": "structure", "members": {"message": {"shape": "Message", "documentation": "<p>Confirmation details of the action performed.</p>"}}}, "DeleteAssessmentRequest": {"type": "structure", "required": ["assessmentArn"], "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the assessment that you want to delete.</p>"}}}, "DeleteAssessmentResponse": {"type": "structure", "members": {"message": {"shape": "Message", "documentation": "<p>Confirmation details of the action performed.</p>"}}}, "DeleteRunRequest": {"type": "structure", "required": ["runArn"], "members": {"runArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the assessment run that you want to delete.</p>"}}}, "DeleteRunResponse": {"type": "structure", "members": {"message": {"shape": "Message", "documentation": "<p>Confirmation details of the action performed.</p>"}}}, "DescribeApplicationRequest": {"type": "structure", "required": ["applicationArn"], "members": {"applicationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the application that you want to describe.</p>"}}}, "DescribeApplicationResponse": {"type": "structure", "members": {"application": {"shape": "Application", "documentation": "<p>Information about the application.</p>"}}}, "DescribeAssessmentRequest": {"type": "structure", "required": ["assessmentArn"], "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the assessment that you want to describe.</p>"}}}, "DescribeAssessmentResponse": {"type": "structure", "members": {"assessment": {"shape": "Assessment", "documentation": "<p>Information about the assessment.</p>"}}}, "DescribeCrossAccountAccessRoleResponse": {"type": "structure", "members": {"roleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the IAM role that Inspector uses to access your AWS account.</p>"}, "valid": {"shape": "Bool", "documentation": "<p>A Boolean value that specifies whether the IAM role has the necessary policies attached to enable Inspector to access your AWS account.</p>"}}}, "DescribeFindingRequest": {"type": "structure", "required": ["findingArn"], "members": {"findingArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the finding that you want to describe.</p>"}}}, "DescribeFindingResponse": {"type": "structure", "members": {"finding": {"shape": "Finding", "documentation": "<p>Information about the finding.</p>"}}}, "DescribeResourceGroupRequest": {"type": "structure", "required": ["resourceGroupArn"], "members": {"resourceGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the resource group that you want to describe.</p>"}}}, "DescribeResourceGroupResponse": {"type": "structure", "members": {"resourceGroup": {"shape": "ResourceGroup", "documentation": "<p>Information about the resource group.</p>"}}}, "DescribeRulesPackageRequest": {"type": "structure", "required": ["rulesPackageArn"], "members": {"rulesPackageArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the rules package that you want to describe.</p>"}}}, "DescribeRulesPackageResponse": {"type": "structure", "members": {"rulesPackage": {"shape": "RulesPackage", "documentation": "<p>Information about the rules package.</p>"}}}, "DescribeRunRequest": {"type": "structure", "required": ["runArn"], "members": {"runArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the assessment run that you want to describe.</p>"}}}, "DescribeRunResponse": {"type": "structure", "members": {"run": {"shape": "Run", "documentation": "<p>Information about the assessment run.</p>"}}}, "DetachAssessmentAndRulesPackageRequest": {"type": "structure", "required": ["assessmentArn", "rulesPackageArn"], "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the assessment from which you want to detach a rules package.</p>"}, "rulesPackageArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the rules package that you want to detach from the assessment.</p>"}}}, "DetachAssessmentAndRulesPackageResponse": {"type": "structure", "members": {"message": {"shape": "Message", "documentation": "<p>Confirmation details of the action performed.</p>"}}}, "Duration": {"type": "integer"}, "DurationRange": {"type": "structure", "members": {"minimum": {"shape": "Duration", "documentation": "<p>The minimum value of the duration range. Must be greater than zero.</p>"}, "maximum": {"shape": "Duration", "documentation": "<p>The maximum value of the duration range. Must be less than or equal to 604800 seconds (1 week).</p>"}}, "documentation": "<p>This data type is used in the <a>AssessmentsFilter</a> data type.</p>"}, "FailureMessage": {"type": "string"}, "Finding": {"type": "structure", "members": {"findingArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the finding.</p>"}, "runArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment run that generated the finding.</p>"}, "rulesPackageArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the rules package that is used to generate the finding.</p>"}, "ruleName": {"shape": "Name", "documentation": "<p>The rule name that is used to generate the finding.</p>"}, "agentId": {"shape": "AgentId", "documentation": "<p>The EC2 instance ID where the agent is installed that is used during the assessment that generates the finding. </p>"}, "autoScalingGroup": {"shape": "AutoScalingGroup", "documentation": "<p>The autoscaling group of the EC2 instance where the agent is installed that is used during the assessment that generates the finding.</p>"}, "severity": {"shape": "Severity", "documentation": "<p>The finding severity. Values can be set to <i>High</i>, <i>Medium</i>, <i>Low</i>, and <i>Informational</i>.</p>"}, "finding": {"shape": "LocalizedText", "documentation": "<p>A short description that identifies the finding.</p>"}, "description": {"shape": "LocalizedText", "documentation": "<p>The description of the finding.</p>"}, "recommendation": {"shape": "LocalizedText", "documentation": "<p>The recommendation for the finding. </p>"}, "attributes": {"shape": "AttributeList", "documentation": "<p>The system-defined attributes for the finding. </p>"}, "userAttributes": {"shape": "AttributeList", "documentation": "<p>The user-defined attributes that are assigned to the finding.</p>"}}, "documentation": "<p>Contains information about an Inspector finding.</p> <p>This data type is used as the response element in the <a>DescribeFinding</a> action.</p>"}, "FindingsFilter": {"type": "structure", "members": {"rulesPackageArns": {"shape": "ArnList", "documentation": "<p>For a record to match a filter, the value specified for this data type property must be the exact match of the value of the <b>rulesPackageArn</b> property of the <a>Finding</a> data type.</p>"}, "ruleNames": {"shape": "NameList", "documentation": "<p>For a record to match a filter, the value specified for this data type property must be the exact match of the value of the <b>ruleName</b> property of the <a>Finding</a> data type.</p>"}, "severities": {"shape": "SeverityList", "documentation": "<p>For a record to match a filter, the value specified for this data type property must be the exact match of the value of the <b>severity</b> property of the <a>Finding</a> data type.</p>"}, "attributes": {"shape": "AttributeList", "documentation": "<p>For a record to match a filter, the value specified for this data type property must be the exact match of the value of the <b>attributes</b> property of the <a>Finding</a> data type.</p>"}, "userAttributes": {"shape": "AttributeList", "documentation": "<p>For a record to match a filter, the value specified for this data type property must be the exact match of the value of the <b>userAttributes</b> property of the <a>Finding</a> data type.</p>"}}, "documentation": "<p>This data type is used as a request parameter in the <a>ListFindings</a> action.</p>"}, "GetAssessmentTelemetryRequest": {"type": "structure", "required": ["assessmentArn"], "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the assessment the telemetry of which you want to obtain.</p>"}}}, "GetAssessmentTelemetryResponse": {"type": "structure", "members": {"telemetry": {"shape": "TelemetryList", "documentation": "<p>Telemetry details.</p>"}}}, "Integer": {"type": "integer"}, "InternalException": {"type": "structure", "members": {}, "exception": true, "fault": true}, "InvalidCrossAccountRoleException": {"type": "structure", "members": {}, "exception": true}, "InvalidInputException": {"type": "structure", "members": {}, "exception": true}, "ListApplicationsRequest": {"type": "structure", "members": {"filter": {"shape": "ApplicationsFilter", "documentation": "<p>You can use this parameter to specify a subset of data to be included in the action's response.</p> <p>For a record to match a filter, all specified filter attributes must match. When multiple values are specified for a filter attribute, any of the values can match.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to 'null' on your first call to the <b>ListApplications</b> action. Subsequent calls to the action fill <b>nextToken</b> in the request with the value of <b>NextToken</b> from previous response to continue listing data.</p>"}, "maxResults": {"shape": "Integer", "documentation": "<p>You can use this parameter to indicate the maximum number of items you want in the response. The default value is 10. The maximum value is 500.</p>"}}}, "ListApplicationsResponse": {"type": "structure", "members": {"applicationArnList": {"shape": "ArnList", "documentation": "<p>A list of ARNs specifying the applications returned by the action.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the <b>nextToken</b> parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to 'null'.</p>"}}}, "ListAssessmentAgentsRequest": {"type": "structure", "required": ["assessmentArn"], "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the assessment whose agents you want to list.</p>"}, "filter": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>You can use this parameter to specify a subset of data to be included in the action's response.</p> <p>For a record to match a filter, all specified filter attributes must match. When multiple values are specified for a filter attribute, any of the values can match.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to 'null' on your first call to the <b>ListAssessmentAgents</b> action. Subsequent calls to the action fill <b>nextToken</b> in the request with the value of <b>NextToken</b> from previous response to continue listing data.</p>"}, "maxResults": {"shape": "Integer", "documentation": "<p>You can use this parameter to indicate the maximum number of items you want in the response. The default value is 10. The maximum value is 500.</p>"}}}, "ListAssessmentAgentsResponse": {"type": "structure", "members": {"agentList": {"shape": "AgentList", "documentation": "<p>A list of ARNs specifying the agents returned by the action.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the <b>nextToken</b> parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to 'null'.</p>"}}}, "ListAssessmentsRequest": {"type": "structure", "members": {"applicationArns": {"shape": "ArnList", "documentation": "<p>A list of ARNs specifying the applications the assessments of which you want to list.</p>"}, "filter": {"shape": "AssessmentsFilter", "documentation": "<p>You can use this parameter to specify a subset of data to be included in the action's response.</p> <p>For a record to match a filter, all specified filter attributes must match. When multiple values are specified for a filter attribute, any of the values can match.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to 'null' on your first call to the <b>ListAssessments</b> action. Subsequent calls to the action fill <b>nextToken</b> in the request with the value of <b>NextToken</b> from previous response to continue listing data.</p>"}, "maxResults": {"shape": "Integer", "documentation": "<p>You can use this parameter to indicate the maximum number of items you want in the response. The default value is 10. The maximum value is 500.</p>"}}}, "ListAssessmentsResponse": {"type": "structure", "members": {"assessmentArnList": {"shape": "ArnList", "documentation": "<p>A list of ARNs specifying the assessments returned by the action.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the <b>nextToken</b> parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to 'null'.</p>"}}}, "ListAttachedAssessmentsRequest": {"type": "structure", "required": ["rulesPackageArn"], "members": {"rulesPackageArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the rules package whose assessments you want to list.</p>"}, "filter": {"shape": "AssessmentsFilter", "documentation": "<p>You can use this parameter to specify a subset of data to be included in the action's response.</p> <p>For a record to match a filter, all specified filter attributes must match. When multiple values are specified for a filter attribute, any of the values can match.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to 'null' on your first call to the <b>ListAttachedAssessments</b> action. Subsequent calls to the action fill <b>nextToken</b> in the request with the value of <b>NextToken</b> from previous response to continue listing data.</p>"}, "maxResults": {"shape": "Integer", "documentation": "<p>You can use this parameter to indicate the maximum number of items you want in the response. The default value is 10. The maximum value is 500.</p>"}}}, "ListAttachedAssessmentsResponse": {"type": "structure", "members": {"assessmentArnList": {"shape": "ArnList", "documentation": "<p>A list of ARNs specifying the assessments returned by the action.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the <b>nextToken</b> parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to 'null'.</p>"}}}, "ListAttachedRulesPackagesRequest": {"type": "structure", "required": ["assessmentArn"], "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the assessment whose rules packages you want to list.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to 'null' on your first call to the <b>ListAttachedRulesPackages</b> action. Subsequent calls to the action fill <b>nextToken</b> in the request with the value of <b>NextToken</b> from previous response to continue listing data.</p>"}, "maxResults": {"shape": "Integer", "documentation": "<p>You can use this parameter to indicate the maximum number of items you want in the response. The default value is 10. The maximum value is 500.</p>"}}}, "ListAttachedRulesPackagesResponse": {"type": "structure", "members": {"rulesPackageArnList": {"shape": "ArnList", "documentation": "<p>A list of ARNs specifying the rules packages returned by the action.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the <b>nextToken</b> parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to 'null'.</p>"}}}, "ListFindingsRequest": {"type": "structure", "members": {"runArns": {"shape": "ArnList", "documentation": "<p>The ARNs of the assessment runs that generate the findings that you want to list.</p>"}, "filter": {"shape": "FindingsFilter", "documentation": "<p>You can use this parameter to specify a subset of data to be included in the action's response.</p> <p>For a record to match a filter, all specified filter attributes must match. When multiple values are specified for a filter attribute, any of the values can match.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to 'null' on your first call to the <b>ListFindings</b> action. Subsequent calls to the action fill <b>nextToken</b> in the request with the value of <b>NextToken</b> from previous response to continue listing data.</p>"}, "maxResults": {"shape": "Integer", "documentation": "<p>You can use this parameter to indicate the maximum number of items you want in the response. The default value is 10. The maximum value is 500.</p>"}}}, "ListFindingsResponse": {"type": "structure", "members": {"findingArnList": {"shape": "ArnList", "documentation": "<p>A list of ARNs specifying the findings returned by the action.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the <b>nextToken</b> parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to 'null'.</p>"}}}, "ListRulesPackagesRequest": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to 'null' on your first call to the <b>ListRulesPackages</b> action. Subsequent calls to the action fill <b>nextToken</b> in the request with the value of <b>NextToken</b> from previous response to continue listing data.</p>"}, "maxResults": {"shape": "Integer", "documentation": "<p>You can use this parameter to indicate the maximum number of items you want in the response. The default value is 10. The maximum value is 500.</p>"}}}, "ListRulesPackagesResponse": {"type": "structure", "members": {"rulesPackageArnList": {"shape": "ArnList", "documentation": "<p>The list of ARNs specifying the rules packages returned by the action.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the <b>nextToken</b> parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to 'null'.</p>"}}}, "ListRunsRequest": {"type": "structure", "members": {"assessmentArns": {"shape": "ArnList", "documentation": "<p>The ARNs specifying the assessments whose runs you want to list.</p>"}, "filter": {"shape": "RunsFilter", "documentation": "<p>You can use this parameter to specify a subset of data to be included in the action's response.</p> <p>For a record to match a filter, all specified filter attributes must match. When multiple values are specified for a filter attribute, any of the values can match.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to 'null' on your first call to the <b>ListRuns</b> action. Subsequent calls to the action fill <b>nextToken</b> in the request with the value of <b>NextToken</b> from previous response to continue listing data.</p>"}, "maxResults": {"shape": "Integer", "documentation": "<p>You can use this parameter to indicate the maximum number of items you want in the response. The default value is 10. The maximum value is 500.</p>"}}}, "ListRunsResponse": {"type": "structure", "members": {"runArnList": {"shape": "ArnList", "documentation": "<p>A list of ARNs specifying the assessment runs returned by the action.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the <b>nextToken</b> parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to 'null'.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the resource whose tags you want to list.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tagList": {"shape": "TagList", "documentation": "<p>A collection of key and value pairs.</p>"}}}, "Locale": {"type": "string"}, "LocalizeTextRequest": {"type": "structure", "required": ["localizedTexts", "locale"], "members": {"localizedTexts": {"shape": "LocalizedTextList", "documentation": "<p>A list of textual identifiers.</p>"}, "locale": {"shape": "Locale", "documentation": "<p>The locale that you want to translate a textual identifier into.</p>"}}}, "LocalizeTextResponse": {"type": "structure", "members": {"message": {"shape": "Message", "documentation": "<p>Confirmation details of the action performed.</p>"}, "results": {"shape": "TextList", "documentation": "<p>The resulting list of user-readable texts.</p>"}}}, "LocalizedFacility": {"type": "string"}, "LocalizedText": {"type": "structure", "members": {"key": {"shape": "LocalizedTextKey", "documentation": "<p>The facility and id properties of the <a>LocalizedTextKey</a> data type.</p>"}, "parameters": {"shape": "ParameterList", "documentation": "<p>Values for the dynamic elements of the string specified by the textual identifier.</p>"}}, "documentation": "<p>The textual identifier. This data type is used as the request parameter in the <a>LocalizeText</a> action.</p>"}, "LocalizedTextId": {"type": "string"}, "LocalizedTextKey": {"type": "structure", "members": {"facility": {"shape": "LocalizedFacility", "documentation": "<p>The module response source of the text.</p>"}, "id": {"shape": "LocalizedTextId", "documentation": "<p>Part of the module response source of the text.</p>"}}, "documentation": "<p>This data type is used in the <a>LocalizedText</a> data type.</p>"}, "LocalizedTextList": {"type": "list", "member": {"shape": "LocalizedText"}}, "Long": {"type": "long"}, "Message": {"type": "string"}, "MessageType": {"type": "string"}, "MessageTypeTelemetry": {"type": "structure", "members": {"messageType": {"shape": "MessageType", "documentation": "<p>A specific type of behavioral data that is collected by the agent.</p>"}, "count": {"shape": "<PERSON>", "documentation": "<p>The number of times that the behavioral data is collected by the agent during an assessment.</p>"}, "dataSize": {"shape": "<PERSON>", "documentation": "<p>The total size of the behavioral data that is collected by the agent during an assessment.</p>"}}, "documentation": "<p>This data type is used in the <a>Telemetry</a> data type.</p> <p>This is metadata about the behavioral data collected by the Inspector agent on your EC2 instances during an assessment and passed to the Inspector service for analysis. </p>"}, "MessageTypeTelemetryList": {"type": "list", "member": {"shape": "MessageTypeTelemetry"}}, "Name": {"type": "string"}, "NameList": {"type": "list", "member": {"shape": "Name"}}, "NamePattern": {"type": "string"}, "NamePatternList": {"type": "list", "member": {"shape": "NamePattern"}}, "NoSuchEntityException": {"type": "structure", "members": {}, "exception": true}, "OperationInProgressException": {"type": "structure", "members": {}, "exception": true}, "PaginationToken": {"type": "string"}, "Parameter": {"type": "structure", "members": {"name": {"shape": "ParameterName", "documentation": "<p>The name of the variable that is being replaced.</p>"}, "value": {"shape": "ParameterValue", "documentation": "<p>The value assigned to the variable that is being replaced. </p>"}}, "documentation": "<p>This data type is used in the <a>LocalizedText</a> data type.</p>"}, "ParameterList": {"type": "list", "member": {"shape": "Parameter"}}, "ParameterName": {"type": "string"}, "ParameterValue": {"type": "string"}, "PreviewAgentsForResourceGroupRequest": {"type": "structure", "required": ["resourceGroupArn"], "members": {"resourceGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the resource group that is used to create an application.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to 'null' on your first call to the <b>PreviewAgentsForResourceGroup</b> action. Subsequent calls to the action fill <b>nextToken</b> in the request with the value of <b>NextToken</b> from previous response to continue listing data.</p>"}, "maxResults": {"shape": "Integer", "documentation": "<p>You can use this parameter to indicate the maximum number of items you want in the response. The default value is 10. The maximum value is 500.</p>"}}}, "PreviewAgentsForResourceGroupResponse": {"type": "structure", "members": {"agentPreviewList": {"shape": "AgentPreviewList", "documentation": "<p>The resulting list of agents.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the <b>nextToken</b> parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to 'null'.</p>"}}}, "RegisterCrossAccountAccessRoleRequest": {"type": "structure", "required": ["roleArn"], "members": {"roleArn": {"shape": "<PERSON><PERSON>", "documentation": "The ARN of the IAM role that Inspector uses to list your EC2 instances during the assessment."}}}, "RegisterCrossAccountAccessRoleResponse": {"type": "structure", "members": {"message": {"shape": "Message", "documentation": "<p>Confirmation details of the action performed.</p>"}}}, "RemoveAttributesFromFindingsRequest": {"type": "structure", "required": ["findingArns", "<PERSON><PERSON><PERSON><PERSON>"], "members": {"findingArns": {"shape": "ArnList", "documentation": "<p>The ARNs specifying the findings that you want to remove attributes from.</p>"}, "attributeKeys": {"shape": "AttributeKeyList", "documentation": "<p>The array of attribute keys that you want to remove from specified findings.</p>"}}}, "RemoveAttributesFromFindingsResponse": {"type": "structure", "members": {"message": {"shape": "Message", "documentation": "<p>Confirmation details of the action performed.</p>"}}}, "ResourceGroup": {"type": "structure", "members": {"resourceGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the resource group. </p>"}, "resourceGroupTags": {"shape": "ResourceGroupTags", "documentation": "<p>The tags (key and value pairs) of the resource group.</p> <p>This data type property is used in the <a>CreateResourceGroup</a> action.</p> <p>A collection of keys and an array of possible values in JSON format.</p> <p>For example, [{ \"key1\" : [\"Value1\",\"Value2\"]},{\"Key2\": [\"Value3\"]}]</p>"}}, "documentation": "<p>Contains information about a resource group. The resource group defines a set of tags that, when queried, identify the AWS resources that comprise the application.</p> <p>This data type is used as the response element in the <a>DescribeResourceGroup</a> action.</p>"}, "ResourceGroupTags": {"type": "string"}, "RulesPackage": {"type": "structure", "members": {"rulesPackageArn": {"shape": "<PERSON><PERSON>", "documentation": "<p> The ARN of the rules package.</p>"}, "rulesPackageName": {"shape": "Name", "documentation": "<p>The name of the rules package.</p>"}, "version": {"shape": "Version", "documentation": "<p>The version id of the rules package.</p>"}, "provider": {"shape": "Name", "documentation": "<p>The provider of the rules package.</p>"}, "description": {"shape": "LocalizedText", "documentation": "<p>The description of the rules package.</p>"}}, "documentation": "<p>Contains information about an Inspector rules package.</p> <p>This data type is used as the response element in the <a>DescribeRulesPackage</a> action.</p>"}, "Run": {"type": "structure", "members": {"runArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the run.</p>"}, "runName": {"shape": "Name", "documentation": "<p>The auto-generated name for the run. </p>"}, "assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment that is associated with the run.</p>"}, "runState": {"shape": "RunState", "documentation": "<p>The state of the run. Values can be set to <i>DataCollectionComplete</i>, <i>EvaluatingPolicies</i>, <i>EvaluatingPoliciesErrorCanRetry</i>, <i>Completed</i>, <i>Failed</i>, <i>TombStoned</i>.</p>"}, "rulesPackages": {"shape": "ArnList", "documentation": "<p>Rules packages selected for the run of the assessment.</p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p>Run creation time that corresponds to the data collection completion time or failure.</p>"}, "completionTime": {"shape": "Timestamp", "documentation": "<p>Run completion time that corresponds to the rules packages evaluation completion time or failure.</p>"}}, "documentation": "<p>A snapshot of an Inspector assessment that contains the assessment's findings.</p> <p>This data type is used as the response element in the <a>DescribeRun</a> action.</p>"}, "RunAssessmentRequest": {"type": "structure", "required": ["assessmentArn", "runName"], "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment that you want to run.</p>"}, "runName": {"shape": "Name", "documentation": "<p>A name specifying the run of the assessment.</p>"}}}, "RunAssessmentResponse": {"type": "structure", "members": {"runArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN specifying the run of the assessment.</p>"}}}, "RunState": {"type": "string"}, "RunStateList": {"type": "list", "member": {"shape": "RunState"}}, "RunsFilter": {"type": "structure", "members": {"runNamePatterns": {"shape": "NamePatternList", "documentation": "<p>For a record to match a filter, an explicit value or a string containing a wildcard specified for this data type property must match the value of the <b>runName</b> property of the <a>Run</a> data type.</p>"}, "runStates": {"shape": "RunStateList", "documentation": "<p>For a record to match a filter, the value specified for this data type property must be the exact match of the value of the <b>runState</b> property of the <a>Run</a> data type.</p>"}, "rulesPackages": {"shape": "ArnList", "documentation": "<p>For a record to match a filter, the value specified for this data type property must match a list of values of the <b>rulesPackages</b> property of the <a>Run</a> data type.</p>"}, "creationTime": {"shape": "TimestampRange", "documentation": "<p>For a record to match a filter, the value specified for this data type property must inclusively match any value between the specified minimum and maximum values of the <b>creationTime</b> property of the <a>Run</a> data type.</p>"}, "completionTime": {"shape": "TimestampRange", "documentation": "<p>For a record to match a filter, the value specified for this data type property must inclusively match any value between the specified minimum and maximum values of the <b>completionTime</b> property of the <a>Run</a> data type.</p>"}}, "documentation": "<p>This data type is used as the request parameter in the <a>ListRuns</a> action.</p>"}, "SetTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment that you want to set tags to.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>A collection of key and value pairs that you want to set to an assessment.</p>"}}}, "SetTagsForResourceResponse": {"type": "structure", "members": {"message": {"shape": "Message", "documentation": "<p>Confirmation details of the action performed.</p>"}}}, "Severity": {"type": "string"}, "SeverityList": {"type": "list", "member": {"shape": "Severity"}}, "StartDataCollectionRequest": {"type": "structure", "required": ["assessmentArn"], "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment for which you want to start the data collection process.</p>"}}}, "StartDataCollectionResponse": {"type": "structure", "members": {"message": {"shape": "Message", "documentation": "<p>Confirmation details of the action performed.</p>"}}}, "StopDataCollectionRequest": {"type": "structure", "required": ["assessmentArn"], "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the assessment for which you want to stop the data collection process.</p>"}}}, "StopDataCollectionResponse": {"type": "structure", "members": {"message": {"shape": "Message", "documentation": "<p>Confirmation details of the action performed.</p>"}}}, "Tag": {"type": "structure", "members": {"Key": {"shape": "TagKey", "documentation": "<p>The tag key.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The value assigned to a tag key.</p>"}}, "documentation": "<p>A key and value pair.</p> <p>This data type is used as a request parameter in the <a>SetTagsForResource</a> action and a response element in the <a>ListTagsForResource</a> action.</p>"}, "TagKey": {"type": "string"}, "TagList": {"type": "list", "member": {"shape": "Tag"}}, "TagValue": {"type": "string"}, "Telemetry": {"type": "structure", "members": {"status": {"shape": "TelemetryStatus", "documentation": "<p>The category of the individual metrics that together constitute the telemetry that Inspector received from the agent.</p>"}, "messageTypeTelemetries": {"shape": "MessageTypeTelemetryList", "documentation": "<p>Counts of individual metrics received by Inspector from the agent.</p>"}}, "documentation": "<p>The metadata about the Inspector application data metrics collected by the agent.</p> <p>This data type is used as the response element in the <a>GetAssessmentTelemetry</a> action.</p>"}, "TelemetryList": {"type": "list", "member": {"shape": "Telemetry"}}, "TelemetryStatus": {"type": "string"}, "Text": {"type": "string"}, "TextList": {"type": "list", "member": {"shape": "Text"}}, "Timestamp": {"type": "timestamp"}, "TimestampRange": {"type": "structure", "members": {"minimum": {"shape": "Timestamp", "documentation": "<p>The minimum value of the timestamp range.</p>"}, "maximum": {"shape": "Timestamp", "documentation": "<p>The maximum value of the timestamp range.</p>"}}, "documentation": "<p>This data type is used in the <a>AssessmentsFilter</a> and <a>RunsFilter</a> data types.</p>"}, "UpdateApplicationRequest": {"type": "structure", "required": ["applicationArn", "applicationName", "resourceGroupArn"], "members": {"applicationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Application ARN that you want to update.</p>"}, "applicationName": {"shape": "Name", "documentation": "<p>Application name that you want to update.</p>"}, "resourceGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The resource group ARN that you want to update.</p>"}}}, "UpdateApplicationResponse": {"type": "structure", "members": {"message": {"shape": "Message", "documentation": "<p>Confirmation details of the action performed.</p>"}}}, "UpdateAssessmentRequest": {"type": "structure", "required": ["assessmentArn", "assessmentName", "durationInSeconds"], "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Asessment ARN that you want to update.</p>"}, "assessmentName": {"shape": "Name", "documentation": "<p>Assessment name that you want to update.</p>"}, "durationInSeconds": {"shape": "Duration", "documentation": "<p>Assessment duration in seconds that you want to update. The default value is 3600 seconds (one hour). The maximum value is 86400 seconds (one day).</p>"}}}, "UpdateAssessmentResponse": {"type": "structure", "members": {"message": {"shape": "Message", "documentation": "<p>Confirmation details of the action performed.</p>"}}}, "Version": {"type": "string"}}, "documentation": "<fullname>Amazon Inspector</fullname> <p>Amazon Inspector enables you to analyze the behavior of the applications you run in AWS and to identify potential security issues. For more information, see <a href=\"https://docs.aws.amazon.com/inspector/latest/userguide/inspector_introduction.html\"> Amazon Inspector User Guide</a>.</p>"}