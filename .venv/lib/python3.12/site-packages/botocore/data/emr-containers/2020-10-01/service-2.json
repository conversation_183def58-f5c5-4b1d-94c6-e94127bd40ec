{"version": "2.0", "metadata": {"apiVersion": "2020-10-01", "endpointPrefix": "emr-containers", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "Amazon EMR Containers", "serviceId": "EMR containers", "signatureVersion": "v4", "signingName": "emr-containers", "uid": "emr-containers-2020-10-01"}, "operations": {"CancelJobRun": {"name": "CancelJobRun", "http": {"method": "DELETE", "requestUri": "/virtualclusters/{virtualClusterId}/jobruns/{jobRunId}"}, "input": {"shape": "CancelJobRunRequest"}, "output": {"shape": "CancelJobRunResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Cancels a job run. A job run is a unit of work, such as a Spark jar, PySpark script, or SparkSQL query, that you submit to Amazon EMR on EKS.</p>"}, "CreateJobTemplate": {"name": "CreateJobTemplate", "http": {"method": "POST", "requestUri": "/jobtemplates"}, "input": {"shape": "CreateJobTemplateRequest"}, "output": {"shape": "CreateJobTemplateResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a job template. Job template stores values of StartJobRun API request in a template and can be used to start a job run. Job template allows two use cases: avoid repeating recurring StartJobRun API request values, enforcing certain values in StartJobRun API request.</p>"}, "CreateManagedEndpoint": {"name": "CreateManagedEndpoint", "http": {"method": "POST", "requestUri": "/virtualclusters/{virtualClusterId}/endpoints"}, "input": {"shape": "CreateManagedEndpointRequest"}, "output": {"shape": "CreateManagedEndpointResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a managed endpoint. A managed endpoint is a gateway that connects Amazon EMR Studio to Amazon EMR on EKS so that Amazon EMR Studio can communicate with your virtual cluster.</p>"}, "CreateVirtualCluster": {"name": "CreateVirtualCluster", "http": {"method": "POST", "requestUri": "/virtualclusters"}, "input": {"shape": "CreateVirtualClusterRequest"}, "output": {"shape": "CreateVirtualClusterResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a virtual cluster. Virtual cluster is a managed entity on Amazon EMR on EKS. You can create, describe, list and delete virtual clusters. They do not consume any additional resource in your system. A single virtual cluster maps to a single Kubernetes namespace. Given this relationship, you can model virtual clusters the same way you model Kubernetes namespaces to meet your requirements.</p>"}, "DeleteJobTemplate": {"name": "DeleteJobTemplate", "http": {"method": "DELETE", "requestUri": "/jobtemplates/{templateId}"}, "input": {"shape": "DeleteJobTemplateRequest"}, "output": {"shape": "DeleteJobTemplateResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes a job template. Job template stores values of StartJobRun API request in a template and can be used to start a job run. Job template allows two use cases: avoid repeating recurring StartJobRun API request values, enforcing certain values in StartJobRun API request.</p>"}, "DeleteManagedEndpoint": {"name": "DeleteManagedEndpoint", "http": {"method": "DELETE", "requestUri": "/virtualclusters/{virtualClusterId}/endpoints/{endpointId}"}, "input": {"shape": "DeleteManagedEndpointRequest"}, "output": {"shape": "DeleteManagedEndpointResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes a managed endpoint. A managed endpoint is a gateway that connects Amazon EMR Studio to Amazon EMR on EKS so that Amazon EMR Studio can communicate with your virtual cluster.</p>"}, "DeleteVirtualCluster": {"name": "DeleteVirtualCluster", "http": {"method": "DELETE", "requestUri": "/virtualclusters/{virtualClusterId}"}, "input": {"shape": "DeleteVirtualClusterRequest"}, "output": {"shape": "DeleteVirtualClusterResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes a virtual cluster. Virtual cluster is a managed entity on Amazon EMR on EKS. You can create, describe, list and delete virtual clusters. They do not consume any additional resource in your system. A single virtual cluster maps to a single Kubernetes namespace. Given this relationship, you can model virtual clusters the same way you model Kubernetes namespaces to meet your requirements.</p>"}, "DescribeJobRun": {"name": "DescribeJobRun", "http": {"method": "GET", "requestUri": "/virtualclusters/{virtualClusterId}/jobruns/{jobRunId}"}, "input": {"shape": "DescribeJobRunRequest"}, "output": {"shape": "DescribeJobRunResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Displays detailed information about a job run. A job run is a unit of work, such as a Spark jar, PySpark script, or SparkSQL query, that you submit to Amazon EMR on EKS.</p>"}, "DescribeJobTemplate": {"name": "DescribeJobTemplate", "http": {"method": "GET", "requestUri": "/jobtemplates/{templateId}"}, "input": {"shape": "DescribeJobTemplateRequest"}, "output": {"shape": "DescribeJobTemplateResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Displays detailed information about a specified job template. Job template stores values of StartJobRun API request in a template and can be used to start a job run. Job template allows two use cases: avoid repeating recurring StartJobRun API request values, enforcing certain values in StartJobRun API request.</p>"}, "DescribeManagedEndpoint": {"name": "DescribeManagedEndpoint", "http": {"method": "GET", "requestUri": "/virtualclusters/{virtualClusterId}/endpoints/{endpointId}"}, "input": {"shape": "DescribeManagedEndpointRequest"}, "output": {"shape": "DescribeManagedEndpointResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Displays detailed information about a managed endpoint. A managed endpoint is a gateway that connects Amazon EMR Studio to Amazon EMR on EKS so that Amazon EMR Studio can communicate with your virtual cluster.</p>"}, "DescribeVirtualCluster": {"name": "DescribeVirtualCluster", "http": {"method": "GET", "requestUri": "/virtualclusters/{virtualClusterId}"}, "input": {"shape": "DescribeVirtualClusterRequest"}, "output": {"shape": "DescribeVirtualClusterResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Displays detailed information about a specified virtual cluster. Virtual cluster is a managed entity on Amazon EMR on EKS. You can create, describe, list and delete virtual clusters. They do not consume any additional resource in your system. A single virtual cluster maps to a single Kubernetes namespace. Given this relationship, you can model virtual clusters the same way you model Kubernetes namespaces to meet your requirements.</p>"}, "GetManagedEndpointSessionCredentials": {"name": "GetManagedEndpointSessionCredentials", "http": {"method": "POST", "requestUri": "/virtualclusters/{virtualClusterId}/endpoints/{endpointId}/credentials"}, "input": {"shape": "GetManagedEndpointSessionCredentialsRequest"}, "output": {"shape": "GetManagedEndpointSessionCredentialsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "RequestThrottledException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Generate a session token to connect to a managed endpoint. </p>"}, "ListJobRuns": {"name": "ListJobRuns", "http": {"method": "GET", "requestUri": "/virtualclusters/{virtualClusterId}/jobruns"}, "input": {"shape": "ListJobRunsRequest"}, "output": {"shape": "ListJobRunsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists job runs based on a set of parameters. A job run is a unit of work, such as a Spark jar, PySpark script, or SparkSQL query, that you submit to Amazon EMR on EKS.</p>"}, "ListJobTemplates": {"name": "ListJobTemplates", "http": {"method": "GET", "requestUri": "/jobtemplates"}, "input": {"shape": "ListJobTemplatesRequest"}, "output": {"shape": "ListJobTemplatesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists job templates based on a set of parameters. Job template stores values of StartJobRun API request in a template and can be used to start a job run. Job template allows two use cases: avoid repeating recurring StartJobRun API request values, enforcing certain values in StartJobRun API request.</p>"}, "ListManagedEndpoints": {"name": "ListManagedEndpoints", "http": {"method": "GET", "requestUri": "/virtualclusters/{virtualClusterId}/endpoints"}, "input": {"shape": "ListManagedEndpointsRequest"}, "output": {"shape": "ListManagedEndpointsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists managed endpoints based on a set of parameters. A managed endpoint is a gateway that connects Amazon EMR Studio to Amazon EMR on EKS so that Amazon EMR Studio can communicate with your virtual cluster.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists the tags assigned to the resources.</p>"}, "ListVirtualClusters": {"name": "ListVirtualClusters", "http": {"method": "GET", "requestUri": "/virtualclusters"}, "input": {"shape": "ListVirtualClustersRequest"}, "output": {"shape": "ListVirtualClustersResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists information about the specified virtual cluster. Virtual cluster is a managed entity on Amazon EMR on EKS. You can create, describe, list and delete virtual clusters. They do not consume any additional resource in your system. A single virtual cluster maps to a single Kubernetes namespace. Given this relationship, you can model virtual clusters the same way you model Kubernetes namespaces to meet your requirements.</p>"}, "StartJobRun": {"name": "StartJobRun", "http": {"method": "POST", "requestUri": "/virtualclusters/{virtualClusterId}/jobruns"}, "input": {"shape": "StartJobRunRequest"}, "output": {"shape": "StartJobRunResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Starts a job run. A job run is a unit of work, such as a Spark jar, PySpark script, or SparkSQL query, that you submit to Amazon EMR on EKS.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Assigns tags to resources. A tag is a label that you assign to an Amazon Web Services resource. Each tag consists of a key and an optional value, both of which you define. Tags enable you to categorize your Amazon Web Services resources by attributes such as purpose, owner, or environment. When you have many resources of the same type, you can quickly identify a specific resource based on the tags you've assigned to it. For example, you can define a set of tags for your Amazon EMR on EKS clusters to help you track each cluster's owner and stack level. We recommend that you devise a consistent set of tag keys for each resource type. You can then search and filter the resources based on the tags that you add.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Removes tags from resources.</p>"}}, "shapes": {"ACMCertArn": {"type": "string", "max": 2048, "min": 44, "pattern": "^arn:(aws[a-zA-Z0-9-]*):acm:.+:(\\d{12}):certificate/.+$"}, "Base64Encoded": {"type": "string", "max": 5000, "pattern": "^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{4}|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)?$"}, "CancelJobRunRequest": {"type": "structure", "required": ["id", "virtualClusterId"], "members": {"id": {"shape": "ResourceIdString", "documentation": "<p>The ID of the job run to cancel.</p>", "location": "uri", "locationName": "jobRunId"}, "virtualClusterId": {"shape": "ResourceIdString", "documentation": "<p>The ID of the virtual cluster for which the job run will be canceled.</p>", "location": "uri", "locationName": "virtualClusterId"}}}, "CancelJobRunResponse": {"type": "structure", "members": {"id": {"shape": "ResourceIdString", "documentation": "<p>The output contains the ID of the cancelled job run.</p>"}, "virtualClusterId": {"shape": "ResourceIdString", "documentation": "<p>The output contains the virtual cluster ID for which the job run is cancelled.</p>"}}}, "Certificate": {"type": "structure", "members": {"certificateArn": {"shape": "ACMCertArn", "documentation": "<p>The ARN of the certificate generated for managed endpoint.</p>"}, "certificateData": {"shape": "Base64Encoded", "documentation": "<p>The base64 encoded PEM certificate data generated for managed endpoint.</p>"}}, "documentation": "<p>The entity representing certificate data generated for managed endpoint.</p>"}, "ClientToken": {"type": "string", "max": 64, "min": 1, "pattern": ".*\\S.*"}, "CloudWatchMonitoringConfiguration": {"type": "structure", "required": ["logGroupName"], "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group for log publishing.</p>"}, "logStreamNamePrefix": {"shape": "String256", "documentation": "<p>The specified name prefix for log streams.</p>"}}, "documentation": "<p>A configuration for CloudWatch monitoring. You can configure your jobs to send log information to CloudWatch Logs.</p>"}, "ClusterId": {"type": "string", "max": 100, "min": 1, "pattern": "^[0-9A-Za-z][A-Za-z0-9\\-_]*"}, "Configuration": {"type": "structure", "required": ["classification"], "members": {"classification": {"shape": "String1024", "documentation": "<p>The classification within a configuration.</p>"}, "properties": {"shape": "SensitivePropertiesMap", "documentation": "<p>A set of properties specified within a configuration classification.</p>"}, "configurations": {"shape": "ConfigurationList", "documentation": "<p>A list of additional configurations to apply within a configuration object.</p>"}}, "documentation": "<p>A configuration specification to be used when provisioning virtual clusters, which can include configurations for applications and software bundled with Amazon EMR on EKS. A configuration consists of a classification, properties, and optional nested configurations. A classification refers to an application-specific configuration file. Properties are the settings you want to change in that file.</p>"}, "ConfigurationList": {"type": "list", "member": {"shape": "Configuration"}, "max": 100}, "ConfigurationOverrides": {"type": "structure", "members": {"applicationConfiguration": {"shape": "ConfigurationList", "documentation": "<p>The configurations for the application running by the job run. </p>"}, "monitoringConfiguration": {"shape": "MonitoringConfiguration", "documentation": "<p>The configurations for monitoring.</p>"}}, "documentation": "<p>A configuration specification to be used to override existing configurations.</p>"}, "ContainerInfo": {"type": "structure", "members": {"eksInfo": {"shape": "EksInfo", "documentation": "<p>The information about the Amazon EKS cluster.</p>"}}, "documentation": "<p>The information about the container used for a job run or a managed endpoint.</p>", "union": true}, "ContainerLogRotationConfiguration": {"type": "structure", "required": ["rotationSize", "max<PERSON><PERSON>ToKeep"], "members": {"rotationSize": {"shape": "RotationSize", "documentation": "<p>The file size at which to rotate logs. Minimum of 2KB, Maximum of 2GB.</p>"}, "maxFilesToKeep": {"shape": "MaxFilesToKeep", "documentation": "<p>The number of files to keep in container after rotation.</p>"}}, "documentation": "<p>The settings for container log rotation.</p>"}, "ContainerProvider": {"type": "structure", "required": ["type", "id"], "members": {"type": {"shape": "ContainerProviderType", "documentation": "<p>The type of the container provider. Amazon EKS is the only supported type as of now.</p>"}, "id": {"shape": "ClusterId", "documentation": "<p>The ID of the container cluster.</p>"}, "info": {"shape": "ContainerInfo", "documentation": "<p>The information about the container cluster.</p>"}}, "documentation": "<p>The information about the container provider.</p>"}, "ContainerProviderType": {"type": "string", "enum": ["EKS"]}, "CreateJobTemplateRequest": {"type": "structure", "required": ["name", "clientToken", "jobTemplateData"], "members": {"name": {"shape": "ResourceNameString", "documentation": "<p>The specified name of the job template.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The client token of the job template.</p>", "idempotencyToken": true}, "jobTemplateData": {"shape": "JobTemplateData", "documentation": "<p>The job template data which holds values of StartJobRun API request.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags that are associated with the job template.</p>"}, "kmsKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The KMS key ARN used to encrypt the job template.</p>"}}}, "CreateJobTemplateResponse": {"type": "structure", "members": {"id": {"shape": "ResourceIdString", "documentation": "<p>This output display the created job template ID.</p>"}, "name": {"shape": "ResourceNameString", "documentation": "<p>This output displays the name of the created job template.</p>"}, "arn": {"shape": "JobTemplateArn", "documentation": "<p>This output display the ARN of the created job template.</p>"}, "createdAt": {"shape": "Date", "documentation": "<p>This output displays the date and time when the job template was created.</p>"}}}, "CreateManagedEndpointRequest": {"type": "structure", "required": ["name", "virtualClusterId", "type", "releaseLabel", "executionRoleArn", "clientToken"], "members": {"name": {"shape": "ResourceNameString", "documentation": "<p>The name of the managed endpoint.</p>"}, "virtualClusterId": {"shape": "ResourceIdString", "documentation": "<p>The ID of the virtual cluster for which a managed endpoint is created.</p>", "location": "uri", "locationName": "virtualClusterId"}, "type": {"shape": "EndpointType", "documentation": "<p>The type of the managed endpoint.</p>"}, "releaseLabel": {"shape": "ReleaseLabel", "documentation": "<p>The Amazon EMR release version.</p>"}, "executionRoleArn": {"shape": "IAMRoleArn", "documentation": "<p>The ARN of the execution role.</p>"}, "certificateArn": {"shape": "ACMCertArn", "documentation": "<p>The certificate ARN provided by users for the managed endpoint. This field is under deprecation and will be removed in future releases.</p>", "deprecated": true, "deprecatedMessage": "Customer provided certificate-arn is deprecated and would be removed in future."}, "configurationOverrides": {"shape": "ConfigurationOverrides", "documentation": "<p>The configuration settings that will be used to override existing configurations.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The client idempotency token for this create call.</p>", "idempotencyToken": true}, "tags": {"shape": "TagMap", "documentation": "<p>The tags of the managed endpoint. </p>"}}}, "CreateManagedEndpointResponse": {"type": "structure", "members": {"id": {"shape": "ResourceIdString", "documentation": "<p>The output contains the ID of the managed endpoint.</p>"}, "name": {"shape": "ResourceNameString", "documentation": "<p>The output contains the name of the managed endpoint.</p>"}, "arn": {"shape": "EndpointArn", "documentation": "<p>The output contains the ARN of the managed endpoint.</p>"}, "virtualClusterId": {"shape": "ResourceIdString", "documentation": "<p>The output contains the ID of the virtual cluster.</p>"}}}, "CreateVirtualClusterRequest": {"type": "structure", "required": ["name", "containerProvider", "clientToken"], "members": {"name": {"shape": "ResourceNameString", "documentation": "<p>The specified name of the virtual cluster.</p>"}, "containerProvider": {"shape": "Container<PERSON><PERSON><PERSON>", "documentation": "<p>The container provider of the virtual cluster.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The client token of the virtual cluster.</p>", "idempotencyToken": true}, "tags": {"shape": "TagMap", "documentation": "<p>The tags assigned to the virtual cluster.</p>"}}}, "CreateVirtualClusterResponse": {"type": "structure", "members": {"id": {"shape": "ResourceIdString", "documentation": "<p>This output contains the virtual cluster ID.</p>"}, "name": {"shape": "ResourceNameString", "documentation": "<p>This output contains the name of the virtual cluster.</p>"}, "arn": {"shape": "VirtualClusterArn", "documentation": "<p>This output contains the ARN of virtual cluster.</p>"}}}, "CredentialType": {"type": "string", "max": 64, "min": 1, "pattern": "^.*\\S.*$"}, "Credentials": {"type": "structure", "members": {"token": {"shape": "Token", "documentation": "<p>The actual session token being returned.</p>"}}, "documentation": "<p>The structure containing the session token being returned.</p>", "union": true}, "Date": {"type": "timestamp", "timestampFormat": "iso8601"}, "DeleteJobTemplateRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "ResourceIdString", "documentation": "<p>The ID of the job template that will be deleted.</p>", "location": "uri", "locationName": "templateId"}}}, "DeleteJobTemplateResponse": {"type": "structure", "members": {"id": {"shape": "ResourceIdString", "documentation": "<p>This output contains the ID of the job template that was deleted.</p>"}}}, "DeleteManagedEndpointRequest": {"type": "structure", "required": ["id", "virtualClusterId"], "members": {"id": {"shape": "ResourceIdString", "documentation": "<p>The ID of the managed endpoint.</p>", "location": "uri", "locationName": "endpointId"}, "virtualClusterId": {"shape": "ResourceIdString", "documentation": "<p>The ID of the endpoint's virtual cluster.</p>", "location": "uri", "locationName": "virtualClusterId"}}}, "DeleteManagedEndpointResponse": {"type": "structure", "members": {"id": {"shape": "ResourceIdString", "documentation": "<p>The output displays the ID of the managed endpoint.</p>"}, "virtualClusterId": {"shape": "ResourceIdString", "documentation": "<p>The output displays the ID of the endpoint's virtual cluster.</p>"}}}, "DeleteVirtualClusterRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "ResourceIdString", "documentation": "<p>The ID of the virtual cluster that will be deleted.</p>", "location": "uri", "locationName": "virtualClusterId"}}}, "DeleteVirtualClusterResponse": {"type": "structure", "members": {"id": {"shape": "ResourceIdString", "documentation": "<p>This output contains the ID of the virtual cluster that will be deleted. </p>"}}}, "DescribeJobRunRequest": {"type": "structure", "required": ["id", "virtualClusterId"], "members": {"id": {"shape": "ResourceIdString", "documentation": "<p>The ID of the job run request. </p>", "location": "uri", "locationName": "jobRunId"}, "virtualClusterId": {"shape": "ResourceIdString", "documentation": "<p>The ID of the virtual cluster for which the job run is submitted.</p>", "location": "uri", "locationName": "virtualClusterId"}}}, "DescribeJobRunResponse": {"type": "structure", "members": {"jobRun": {"shape": "JobRun", "documentation": "<p>The output displays information about a job run.</p>"}}}, "DescribeJobTemplateRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "ResourceIdString", "documentation": "<p>The ID of the job template that will be described.</p>", "location": "uri", "locationName": "templateId"}}}, "DescribeJobTemplateResponse": {"type": "structure", "members": {"jobTemplate": {"shape": "JobTemplate", "documentation": "<p>This output displays information about the specified job template.</p>"}}}, "DescribeManagedEndpointRequest": {"type": "structure", "required": ["id", "virtualClusterId"], "members": {"id": {"shape": "ResourceIdString", "documentation": "<p>This output displays ID of the managed endpoint.</p>", "location": "uri", "locationName": "endpointId"}, "virtualClusterId": {"shape": "ResourceIdString", "documentation": "<p>The ID of the endpoint's virtual cluster.</p>", "location": "uri", "locationName": "virtualClusterId"}}}, "DescribeManagedEndpointResponse": {"type": "structure", "members": {"endpoint": {"shape": "Endpoint", "documentation": "<p>This output displays information about a managed endpoint.</p>"}}}, "DescribeVirtualClusterRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "ResourceIdString", "documentation": "<p>The ID of the virtual cluster that will be described.</p>", "location": "uri", "locationName": "virtualClusterId"}}}, "DescribeVirtualClusterResponse": {"type": "structure", "members": {"virtualCluster": {"shape": "VirtualCluster", "documentation": "<p>This output displays information about the specified virtual cluster.</p>"}}}, "EksInfo": {"type": "structure", "members": {"namespace": {"shape": "KubernetesNamespace", "documentation": "<p>The namespaces of the Amazon EKS cluster.</p>"}}, "documentation": "<p>The information about the Amazon EKS cluster.</p>"}, "Endpoint": {"type": "structure", "members": {"id": {"shape": "ResourceIdString", "documentation": "<p>The ID of the endpoint.</p>"}, "name": {"shape": "ResourceNameString", "documentation": "<p>The name of the endpoint.</p>"}, "arn": {"shape": "EndpointArn", "documentation": "<p>The ARN of the endpoint.</p>"}, "virtualClusterId": {"shape": "ResourceIdString", "documentation": "<p>The ID of the endpoint's virtual cluster.</p>"}, "type": {"shape": "EndpointType", "documentation": "<p>The type of the endpoint.</p>"}, "state": {"shape": "EndpointState", "documentation": "<p>The state of the endpoint.</p>"}, "releaseLabel": {"shape": "ReleaseLabel", "documentation": "<p>The EMR release version to be used for the endpoint.</p>"}, "executionRoleArn": {"shape": "IAMRoleArn", "documentation": "<p>The execution role ARN of the endpoint.</p>"}, "certificateArn": {"shape": "ACMCertArn", "documentation": "<p>The certificate ARN of the endpoint. This field is under deprecation and will be removed in future.</p>", "deprecated": true, "deprecatedMessage": "Customer provided certificate-arn is deprecated and would be removed in future."}, "certificateAuthority": {"shape": "Certificate", "documentation": "<p>The certificate generated by emr control plane on customer behalf to secure the managed endpoint.</p>"}, "configurationOverrides": {"shape": "ConfigurationOverrides", "documentation": "<p>The configuration settings that are used to override existing configurations for endpoints.</p>"}, "serverUrl": {"shape": "UriString", "documentation": "<p>The server URL of the endpoint.</p>"}, "createdAt": {"shape": "Date", "documentation": "<p>The date and time when the endpoint was created.</p>"}, "securityGroup": {"shape": "String256", "documentation": "<p>The security group configuration of the endpoint. </p>"}, "subnetIds": {"shape": "SubnetIds", "documentation": "<p>The subnet IDs of the endpoint. </p>"}, "stateDetails": {"shape": "String256", "documentation": "<p> Additional details of the endpoint state. </p>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p> The reasons why the endpoint has failed. </p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags of the endpoint. </p>"}}, "documentation": "<p>This entity represents the endpoint that is managed by Amazon EMR on EKS.</p>"}, "EndpointArn": {"type": "string", "max": 1024, "min": 60, "pattern": "^arn:(aws[a-zA-Z0-9-]*):emr-containers:.+:(\\d{12}):\\/virtualclusters\\/[0-9a-zA-Z]+\\/endpoints\\/[0-9a-zA-Z]+$"}, "EndpointState": {"type": "string", "enum": ["CREATING", "ACTIVE", "TERMINATING", "TERMINATED", "TERMINATED_WITH_ERRORS"]}, "EndpointStates": {"type": "list", "member": {"shape": "EndpointState"}, "max": 10}, "EndpointType": {"type": "string", "max": 64, "min": 1, "pattern": ".*\\S.*"}, "EndpointTypes": {"type": "list", "member": {"shape": "EndpointType"}, "max": 10}, "Endpoints": {"type": "list", "member": {"shape": "Endpoint"}}, "EntryPointArgument": {"type": "string", "max": 10280, "min": 1, "pattern": ".*\\S.*", "sensitive": true}, "EntryPointArguments": {"type": "list", "member": {"shape": "EntryPointArgument"}}, "EntryPointPath": {"type": "string", "max": 256, "min": 1, "pattern": ".*\\S.*", "sensitive": true}, "FailureReason": {"type": "string", "enum": ["INTERNAL_ERROR", "USER_ERROR", "VALIDATION_ERROR", "CLUSTER_UNAVAILABLE"]}, "GetManagedEndpointSessionCredentialsRequest": {"type": "structure", "required": ["executionRoleArn", "credentialType", "endpointIdentifier", "virtualClusterIdentifier"], "members": {"endpointIdentifier": {"shape": "String2048", "documentation": "<p>The ARN of the managed endpoint for which the request is submitted. </p>", "location": "uri", "locationName": "endpointId"}, "virtualClusterIdentifier": {"shape": "String2048", "documentation": "<p>The ARN of the Virtual Cluster which the Managed Endpoint belongs to. </p>", "location": "uri", "locationName": "virtualClusterId"}, "executionRoleArn": {"shape": "IAMRoleArn", "documentation": "<p>The IAM Execution Role ARN that will be used by the job run. </p>"}, "credentialType": {"shape": "CredentialType", "documentation": "<p>Type of the token requested. Currently supported and default value of this field is “TOKEN.”</p>"}, "durationInSeconds": {"shape": "JavaInteger", "documentation": "<p>Duration in seconds for which the session token is valid. The default duration is 15 minutes and the maximum is 12 hours.</p>"}, "logContext": {"shape": "LogContext", "documentation": "<p>String identifier used to separate sections of the execution logs uploaded to S3.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The client idempotency token of the job run request.</p>", "idempotencyToken": true}}}, "GetManagedEndpointSessionCredentialsResponse": {"type": "structure", "members": {"id": {"shape": "ResourceIdString", "documentation": "<p>The identifier of the session token returned.</p>"}, "credentials": {"shape": "Credentials", "documentation": "<p>The structure containing the session credentials.</p>"}, "expiresAt": {"shape": "Date", "documentation": "<p>The date and time when the session token will expire.</p>"}}}, "IAMRoleArn": {"type": "string", "max": 2048, "min": 20, "pattern": "^arn:(aws[a-zA-Z0-9-]*):iam::(\\d{12})?:(role((\\u002F)|(\\u002F[\\u0021-\\u007F]+\\u002F))[\\w+=,.@-]+)$"}, "InternalServerException": {"type": "structure", "members": {"message": {"shape": "String1024"}}, "documentation": "<p>This is an internal server exception.</p>", "error": {"httpStatusCode": 500}, "exception": true}, "JavaInteger": {"type": "integer"}, "JobArn": {"type": "string", "max": 1024, "min": 60, "pattern": "^arn:(aws[a-zA-Z0-9-]*):emr-containers:.+:(\\d{12}):\\/virtualclusters\\/[0-9a-zA-Z]+\\/jobruns\\/[0-9a-zA-Z]+$"}, "JobDriver": {"type": "structure", "members": {"sparkSubmitJobDriver": {"shape": "SparkSubmitJobDriver", "documentation": "<p>The job driver parameters specified for spark submit.</p>"}, "sparkSqlJobDriver": {"shape": "SparkSqlJobDriver", "documentation": "<p>The job driver for job type.</p>"}}, "documentation": "<p>Specify the driver that the job runs on. Exactly one of the two available job drivers is required, either sparkSqlJobDriver or sparkSubmitJobDriver.</p>"}, "JobRun": {"type": "structure", "members": {"id": {"shape": "ResourceIdString", "documentation": "<p>The ID of the job run.</p>"}, "name": {"shape": "ResourceNameString", "documentation": "<p>The name of the job run.</p>"}, "virtualClusterId": {"shape": "ResourceIdString", "documentation": "<p>The ID of the job run's virtual cluster.</p>"}, "arn": {"shape": "JobArn", "documentation": "<p>The ARN of job run.</p>"}, "state": {"shape": "JobRunState", "documentation": "<p>The state of the job run. </p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The client token used to start a job run.</p>"}, "executionRoleArn": {"shape": "IAMRoleArn", "documentation": "<p>The execution role ARN of the job run.</p>"}, "releaseLabel": {"shape": "ReleaseLabel", "documentation": "<p>The release version of Amazon EMR.</p>"}, "configurationOverrides": {"shape": "ConfigurationOverrides", "documentation": "<p>The configuration settings that are used to override default configuration.</p>"}, "jobDriver": {"shape": "JobDriver", "documentation": "<p>Parameters of job driver for the job run.</p>"}, "createdAt": {"shape": "Date", "documentation": "<p>The date and time when the job run was created.</p>"}, "createdBy": {"shape": "RequestIdentityUserArn", "documentation": "<p>The user who created the job run.</p>"}, "finishedAt": {"shape": "Date", "documentation": "<p>The date and time when the job run has finished.</p>"}, "stateDetails": {"shape": "String256", "documentation": "<p>Additional details of the job run state.</p>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p>The reasons why the job run has failed.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The assigned tags of the job run.</p>"}, "retryPolicyConfiguration": {"shape": "RetryPolicyConfiguration", "documentation": "<p>The configuration of the retry policy that the job runs on.</p>"}, "retryPolicyExecution": {"shape": "RetryPolicyExecution", "documentation": "<p>The current status of the retry policy executed on the job.</p>"}}, "documentation": "<p>This entity describes a job run. A job run is a unit of work, such as a Spark jar, PySpark script, or SparkSQL query, that you submit to Amazon EMR on EKS. </p>"}, "JobRunState": {"type": "string", "enum": ["PENDING", "SUBMITTED", "RUNNING", "FAILED", "CANCELLED", "CANCEL_PENDING", "COMPLETED"]}, "JobRunStates": {"type": "list", "member": {"shape": "JobRunState"}, "max": 10}, "JobRuns": {"type": "list", "member": {"shape": "JobRun"}}, "JobTemplate": {"type": "structure", "required": ["jobTemplateData"], "members": {"name": {"shape": "ResourceNameString", "documentation": "<p>The name of the job template.</p>"}, "id": {"shape": "ResourceIdString", "documentation": "<p>The ID of the job template.</p>"}, "arn": {"shape": "JobTemplateArn", "documentation": "<p>The ARN of the job template.</p>"}, "createdAt": {"shape": "Date", "documentation": "<p> The date and time when the job template was created.</p>"}, "createdBy": {"shape": "RequestIdentityUserArn", "documentation": "<p> The user who created the job template.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags assigned to the job template.</p>"}, "jobTemplateData": {"shape": "JobTemplateData", "documentation": "<p>The job template data which holds values of StartJobRun API request.</p>"}, "kmsKeyArn": {"shape": "KmsKeyArn", "documentation": "<p> The KMS key ARN used to encrypt the job template.</p>"}, "decryptionError": {"shape": "String2048", "documentation": "<p>The error message in case the decryption of job template fails.</p>"}}, "documentation": "<p>This entity describes a job template. Job template stores values of StartJobRun API request in a template and can be used to start a job run. Job template allows two use cases: avoid repeating recurring StartJobRun API request values, enforcing certain values in StartJobRun API request.</p>"}, "JobTemplateArn": {"type": "string", "max": 1024, "min": 60, "pattern": "^arn:(aws[a-zA-Z0-9-]*):emr-containers:.+:(\\d{12}):\\/jobtemplates\\/[0-9a-zA-Z]+$"}, "JobTemplateData": {"type": "structure", "required": ["executionRoleArn", "releaseLabel", "jobDriver"], "members": {"executionRoleArn": {"shape": "ParametricIAMRoleArn", "documentation": "<p>The execution role ARN of the job run.</p>"}, "releaseLabel": {"shape": "ParametricReleaseLabel", "documentation": "<p> The release version of Amazon EMR.</p>"}, "configurationOverrides": {"shape": "ParametricConfigurationOverrides", "documentation": "<p> The configuration settings that are used to override defaults configuration.</p>"}, "jobDriver": {"shape": "JobDriver"}, "parameterConfiguration": {"shape": "TemplateParameterConfigurationMap", "documentation": "<p>The configuration of parameters existing in the job template.</p>"}, "jobTags": {"shape": "TagMap", "documentation": "<p>The tags assigned to jobs started using the job template.</p>"}}, "documentation": "<p>The values of StartJobRun API requests used in job runs started using the job template.</p>"}, "JobTemplates": {"type": "list", "member": {"shape": "JobTemplate"}}, "KmsKeyArn": {"type": "string", "max": 2048, "min": 3, "pattern": "^(arn:(aws[a-zA-Z0-9-]*):kms:.+:(\\d{12})?:key\\/[(0-9a-zA-Z)-?]+|\\$\\{[a-zA-Z]\\w*\\})$"}, "KubernetesNamespace": {"type": "string", "max": 63, "min": 1, "pattern": "[a-z0-9]([-a-z0-9]*[a-z0-9])?"}, "ListJobRunsRequest": {"type": "structure", "required": ["virtualClusterId"], "members": {"virtualClusterId": {"shape": "ResourceIdString", "documentation": "<p>The ID of the virtual cluster for which to list the job run. </p>", "location": "uri", "locationName": "virtualClusterId"}, "createdBefore": {"shape": "Date", "documentation": "<p>The date and time before which the job runs were submitted.</p>", "location": "querystring", "locationName": "createdBefore"}, "createdAfter": {"shape": "Date", "documentation": "<p>The date and time after which the job runs were submitted.</p>", "location": "querystring", "locationName": "createdAfter"}, "name": {"shape": "ResourceNameString", "documentation": "<p>The name of the job run.</p>", "location": "querystring", "locationName": "name"}, "states": {"shape": "JobRunStates", "documentation": "<p>The states of the job run.</p>", "location": "querystring", "locationName": "states"}, "maxResults": {"shape": "JavaInteger", "documentation": "<p>The maximum number of job runs that can be listed.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of job runs to return.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListJobRunsResponse": {"type": "structure", "members": {"jobRuns": {"shape": "JobRuns", "documentation": "<p>This output lists information about the specified job runs.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>This output displays the token for the next set of job runs.</p>"}}}, "ListJobTemplatesRequest": {"type": "structure", "members": {"createdAfter": {"shape": "Date", "documentation": "<p>The date and time after which the job templates were created.</p>", "location": "querystring", "locationName": "createdAfter"}, "createdBefore": {"shape": "Date", "documentation": "<p> The date and time before which the job templates were created.</p>", "location": "querystring", "locationName": "createdBefore"}, "maxResults": {"shape": "JavaInteger", "documentation": "<p> The maximum number of job templates that can be listed.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p> The token for the next set of job templates to return.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListJobTemplatesResponse": {"type": "structure", "members": {"templates": {"shape": "JobTemplates", "documentation": "<p>This output lists information about the specified job templates.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p> This output displays the token for the next set of job templates.</p>"}}}, "ListManagedEndpointsRequest": {"type": "structure", "required": ["virtualClusterId"], "members": {"virtualClusterId": {"shape": "ResourceIdString", "documentation": "<p>The ID of the virtual cluster.</p>", "location": "uri", "locationName": "virtualClusterId"}, "createdBefore": {"shape": "Date", "documentation": "<p>The date and time before which the endpoints are created.</p>", "location": "querystring", "locationName": "createdBefore"}, "createdAfter": {"shape": "Date", "documentation": "<p> The date and time after which the endpoints are created.</p>", "location": "querystring", "locationName": "createdAfter"}, "types": {"shape": "EndpointTypes", "documentation": "<p>The types of the managed endpoints.</p>", "location": "querystring", "locationName": "types"}, "states": {"shape": "EndpointStates", "documentation": "<p>The states of the managed endpoints.</p>", "location": "querystring", "locationName": "states"}, "maxResults": {"shape": "JavaInteger", "documentation": "<p>The maximum number of managed endpoints that can be listed.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p> The token for the next set of managed endpoints to return. </p>", "location": "querystring", "locationName": "nextToken"}}}, "ListManagedEndpointsResponse": {"type": "structure", "members": {"endpoints": {"shape": "Endpoints", "documentation": "<p>The managed endpoints to be listed.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p> The token for the next set of endpoints to return. </p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "RsiArn", "documentation": "<p>The ARN of tagged resources.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagMap", "documentation": "<p>The tags assigned to resources.</p>"}}}, "ListVirtualClustersRequest": {"type": "structure", "members": {"containerProviderId": {"shape": "String1024", "documentation": "<p>The container provider ID of the virtual cluster.</p>", "location": "querystring", "locationName": "containerProviderId"}, "containerProviderType": {"shape": "ContainerProviderType", "documentation": "<p>The container provider type of the virtual cluster. Amazon EKS is the only supported type as of now.</p>", "location": "querystring", "locationName": "containerProviderType"}, "createdAfter": {"shape": "Date", "documentation": "<p>The date and time after which the virtual clusters are created.</p>", "location": "querystring", "locationName": "createdAfter"}, "createdBefore": {"shape": "Date", "documentation": "<p>The date and time before which the virtual clusters are created.</p>", "location": "querystring", "locationName": "createdBefore"}, "states": {"shape": "VirtualClusterStates", "documentation": "<p>The states of the requested virtual clusters.</p>", "location": "querystring", "locationName": "states"}, "maxResults": {"shape": "JavaInteger", "documentation": "<p>The maximum number of virtual clusters that can be listed.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of virtual clusters to return. </p>", "location": "querystring", "locationName": "nextToken"}}}, "ListVirtualClustersResponse": {"type": "structure", "members": {"virtualClusters": {"shape": "VirtualClusters", "documentation": "<p>This output lists the specified virtual clusters.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>This output displays the token for the next set of virtual clusters.</p>"}}}, "LogContext": {"type": "string", "max": 63, "min": 3, "pattern": "^((?!.*-s3alias)(?!xn--.*)[a-z0-9][-a-z0-9.]*)?[a-z0-9]$"}, "LogGroupName": {"type": "string", "max": 512, "min": 1, "pattern": "[\\.\\-_/#A-Za-z0-9]+"}, "MaxFilesToKeep": {"type": "integer", "max": 50, "min": 1}, "MonitoringConfiguration": {"type": "structure", "members": {"persistentAppUI": {"shape": "PersistentAppUI", "documentation": "<p>Monitoring configurations for the persistent application UI. </p>"}, "cloudWatchMonitoringConfiguration": {"shape": "CloudWatchMonitoringConfiguration", "documentation": "<p>Monitoring configurations for CloudWatch.</p>"}, "s3MonitoringConfiguration": {"shape": "S3MonitoringConfiguration", "documentation": "<p>Amazon S3 configuration for monitoring log publishing.</p>"}, "containerLogRotationConfiguration": {"shape": "ContainerLogRotationConfiguration", "documentation": "<p>Enable or disable container log rotation.</p>"}}, "documentation": "<p>Configuration setting for monitoring.</p>"}, "NextToken": {"type": "string", "max": 1024, "min": 1, "pattern": ".*\\S.*"}, "ParametricCloudWatchMonitoringConfiguration": {"type": "structure", "members": {"logGroupName": {"shape": "TemplateParameter", "documentation": "<p> The name of the log group for log publishing.</p>"}, "logStreamNamePrefix": {"shape": "String256", "documentation": "<p> The specified name prefix for log streams.</p>"}}, "documentation": "<p> A configuration for CloudWatch monitoring. You can configure your jobs to send log information to CloudWatch Logs. This data type allows job template parameters to be specified within.</p>"}, "ParametricConfigurationOverrides": {"type": "structure", "members": {"applicationConfiguration": {"shape": "ConfigurationList", "documentation": "<p> The configurations for the application running by the job run.</p>"}, "monitoringConfiguration": {"shape": "ParametricMonitoringConfiguration", "documentation": "<p> The configurations for monitoring. </p>"}}, "documentation": "<p> A configuration specification to be used to override existing configurations. This data type allows job template parameters to be specified within.</p>"}, "ParametricIAMRoleArn": {"type": "string", "max": 2048, "min": 4, "pattern": "(^arn:(aws[a-zA-Z0-9-]*):iam::(\\d{12})?:(role((\\u002F)|(\\u002F[\\u0021-\\u007F]+\\u002F))[\\w+=,.@-]+)$)|([\\.\\-_\\#A-Za-z0-9\\$\\{\\}]+)"}, "ParametricMonitoringConfiguration": {"type": "structure", "members": {"persistentAppUI": {"shape": "TemplateParameter", "documentation": "<p> Monitoring configurations for the persistent application UI.</p>"}, "cloudWatchMonitoringConfiguration": {"shape": "ParametricCloudWatchMonitoringConfiguration", "documentation": "<p> Monitoring configurations for CloudWatch.</p>"}, "s3MonitoringConfiguration": {"shape": "ParametricS3MonitoringConfiguration", "documentation": "<p> Amazon S3 configuration for monitoring log publishing.</p>"}}, "documentation": "<p> Configuration setting for monitoring. This data type allows job template parameters to be specified within.</p>"}, "ParametricReleaseLabel": {"type": "string", "max": 64, "min": 1, "pattern": "([\\.\\-_/A-Za-z0-9]+|\\$\\{[a-zA-Z]\\w*\\})"}, "ParametricS3MonitoringConfiguration": {"type": "structure", "members": {"logUri": {"shape": "UriString", "documentation": "<p>Amazon S3 destination URI for log publishing.</p>"}}, "documentation": "<p> Amazon S3 configuration for monitoring log publishing. You can configure your jobs to send log information to Amazon S3. This data type allows job template parameters to be specified within.</p>"}, "PersistentAppUI": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "ReleaseLabel": {"type": "string", "max": 64, "min": 1, "pattern": "[\\.\\-_/A-Za-z0-9]+"}, "RequestIdentityUserArn": {"type": "string", "max": 2048, "min": 20, "pattern": "^arn:(aws[a-zA-Z0-9-]*):(iam|sts)::(\\d{12})?:[\\w/+=,.@-]+$"}, "RequestThrottledException": {"type": "structure", "members": {"message": {"shape": "String1024"}}, "documentation": "<p>The request throttled.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ResourceIdString": {"type": "string", "max": 64, "min": 1, "pattern": "[0-9a-z]+"}, "ResourceNameString": {"type": "string", "max": 64, "min": 1, "pattern": "[\\.\\-_/#A-Za-z0-9]+"}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "String1024"}}, "documentation": "<p>The specified resource was not found.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "RetryPolicyConfiguration": {"type": "structure", "required": ["maxAttempts"], "members": {"maxAttempts": {"shape": "JavaInteger", "documentation": "<p>The maximum number of attempts on the job's driver.</p>"}}, "documentation": "<p>The configuration of the retry policy that the job runs on.</p>"}, "RetryPolicyExecution": {"type": "structure", "required": ["currentAttemptCount"], "members": {"currentAttemptCount": {"shape": "JavaInteger", "documentation": "<p>The current number of attempts made on the driver of the job.</p>"}}, "documentation": "<p>The current status of the retry policy executed on the job.</p>"}, "RotationSize": {"type": "string", "max": 12, "min": 3, "pattern": "^\\d+(\\.\\d+)?[KMG][Bb]?$"}, "RsiArn": {"type": "string", "max": 500, "min": 60, "pattern": "^arn:(aws[a-zA-Z0-9-]*):emr-containers:.+:(\\d{12}):/virtualclusters/.+$"}, "S3MonitoringConfiguration": {"type": "structure", "required": ["logUri"], "members": {"logUri": {"shape": "UriString", "documentation": "<p>Amazon S3 destination URI for log publishing.</p>"}}, "documentation": "<p> Amazon S3 configuration for monitoring log publishing. You can configure your jobs to send log information to Amazon S3.</p>"}, "SensitivePropertiesMap": {"type": "map", "key": {"shape": "String1024"}, "value": {"shape": "String1024"}, "max": 100, "sensitive": true}, "SparkSqlJobDriver": {"type": "structure", "members": {"entryPoint": {"shape": "EntryPointPath", "documentation": "<p>The SQL file to be executed.</p>"}, "sparkSqlParameters": {"shape": "SparkSqlParameters", "documentation": "<p>The Spark parameters to be included in the Spark SQL command.</p>"}}, "documentation": "<p>The job driver for job type.</p>"}, "SparkSqlParameters": {"type": "string", "max": 102400, "min": 1, "pattern": ".*\\S.*", "sensitive": true}, "SparkSubmitJobDriver": {"type": "structure", "required": ["entryPoint"], "members": {"entryPoint": {"shape": "EntryPointPath", "documentation": "<p>The entry point of job application.</p>"}, "entryPointArguments": {"shape": "EntryPointArguments", "documentation": "<p>The arguments for job application.</p>"}, "sparkSubmitParameters": {"shape": "SparkSubmitParameters", "documentation": "<p>The Spark submit parameters that are used for job runs.</p>"}}, "documentation": "<p>The information about job driver for Spark submit.</p>"}, "SparkSubmitParameters": {"type": "string", "max": 102400, "min": 1, "pattern": ".*\\S.*", "sensitive": true}, "StartJobRunRequest": {"type": "structure", "required": ["virtualClusterId", "clientToken"], "members": {"name": {"shape": "ResourceNameString", "documentation": "<p>The name of the job run.</p>"}, "virtualClusterId": {"shape": "ResourceIdString", "documentation": "<p>The virtual cluster ID for which the job run request is submitted.</p>", "location": "uri", "locationName": "virtualClusterId"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The client idempotency token of the job run request. </p>", "idempotencyToken": true}, "executionRoleArn": {"shape": "IAMRoleArn", "documentation": "<p>The execution role ARN for the job run.</p>"}, "releaseLabel": {"shape": "ReleaseLabel", "documentation": "<p>The Amazon EMR release version to use for the job run.</p>"}, "jobDriver": {"shape": "JobDriver", "documentation": "<p>The job driver for the job run.</p>"}, "configurationOverrides": {"shape": "ConfigurationOverrides", "documentation": "<p>The configuration overrides for the job run.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags assigned to job runs.</p>"}, "jobTemplateId": {"shape": "ResourceIdString", "documentation": "<p>The job template ID to be used to start the job run.</p>"}, "jobTemplateParameters": {"shape": "TemplateParameterInputMap", "documentation": "<p>The values of job template parameters to start a job run.</p>"}, "retryPolicyConfiguration": {"shape": "RetryPolicyConfiguration", "documentation": "<p>The retry policy configuration for the job run.</p>"}}}, "StartJobRunResponse": {"type": "structure", "members": {"id": {"shape": "ResourceIdString", "documentation": "<p>This output displays the started job run ID.</p>"}, "name": {"shape": "ResourceNameString", "documentation": "<p>This output displays the name of the started job run.</p>"}, "arn": {"shape": "JobArn", "documentation": "<p>This output lists the ARN of job run.</p>"}, "virtualClusterId": {"shape": "ResourceIdString", "documentation": "<p>This output displays the virtual cluster ID for which the job run was submitted.</p>"}}}, "String1024": {"type": "string", "max": 1024, "min": 1, "pattern": ".*\\S.*"}, "String128": {"type": "string", "max": 128, "min": 1, "pattern": ".*\\S.*"}, "String2048": {"type": "string", "max": 2048, "min": 1, "pattern": ".*\\S.*"}, "String256": {"type": "string", "max": 256, "min": 1, "pattern": ".*\\S.*"}, "StringEmpty256": {"type": "string", "max": 256, "min": 0, "pattern": ".*\\S.*"}, "SubnetIds": {"type": "list", "member": {"shape": "String256"}}, "TagKeyList": {"type": "list", "member": {"shape": "String128"}, "max": 50, "min": 0}, "TagMap": {"type": "map", "key": {"shape": "String128"}, "value": {"shape": "StringEmpty256"}, "max": 50, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "RsiArn", "documentation": "<p>The ARN of resources.</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags assigned to resources.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TemplateParameter": {"type": "string", "max": 512, "min": 1, "pattern": "[\\.\\-_/#A-Za-z0-9\\$\\{\\}]+"}, "TemplateParameterConfiguration": {"type": "structure", "members": {"type": {"shape": "TemplateParameterDataType", "documentation": "<p>The type of the job template parameter. Allowed values are: ‘STRING’, ‘NUMBER’.</p>"}, "defaultValue": {"shape": "String1024", "documentation": "<p>The default value for the job template parameter.</p>"}}, "documentation": "<p>The configuration of a job template parameter.</p>"}, "TemplateParameterConfigurationMap": {"type": "map", "key": {"shape": "TemplateParameterName"}, "value": {"shape": "TemplateParameterConfiguration"}, "max": 20}, "TemplateParameterDataType": {"type": "string", "enum": ["NUMBER", "STRING"]}, "TemplateParameterInputMap": {"type": "map", "key": {"shape": "TemplateParameterName"}, "value": {"shape": "String1024"}, "max": 20}, "TemplateParameterName": {"type": "string", "max": 512, "min": 1, "pattern": "[\\.\\-_\\#A-Za-z0-9]+"}, "Token": {"type": "string", "min": 1, "pattern": "^.*\\S.*$", "sensitive": true}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "RsiArn", "documentation": "<p>The ARN of resources.</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>The tag keys of the resources.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UriString": {"type": "string", "max": 10280, "min": 1, "pattern": "[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDBFF-\\uDC00\\uDFFF\\r\\n\\t]*"}, "ValidationException": {"type": "structure", "members": {"message": {"shape": "String1024"}}, "documentation": "<p>There are invalid parameters in the client request.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "VirtualCluster": {"type": "structure", "members": {"id": {"shape": "ResourceIdString", "documentation": "<p>The ID of the virtual cluster.</p>"}, "name": {"shape": "ResourceNameString", "documentation": "<p>The name of the virtual cluster.</p>"}, "arn": {"shape": "VirtualClusterArn", "documentation": "<p>The ARN of the virtual cluster.</p>"}, "state": {"shape": "VirtualClusterState", "documentation": "<p>The state of the virtual cluster.</p>"}, "containerProvider": {"shape": "Container<PERSON><PERSON><PERSON>", "documentation": "<p>The container provider of the virtual cluster.</p>"}, "createdAt": {"shape": "Date", "documentation": "<p>The date and time when the virtual cluster is created.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The assigned tags of the virtual cluster.</p>"}}, "documentation": "<p>This entity describes a virtual cluster. A virtual cluster is a Kubernetes namespace that Amazon EMR is registered with. Amazon EMR uses virtual clusters to run jobs and host endpoints. Multiple virtual clusters can be backed by the same physical cluster. However, each virtual cluster maps to one namespace on an Amazon EKS cluster. Virtual clusters do not create any active resources that contribute to your bill or that require lifecycle management outside the service.</p>"}, "VirtualClusterArn": {"type": "string", "max": 1024, "min": 60, "pattern": "^arn:(aws[a-zA-Z0-9-]*):emr-containers:.+:(\\d{12}):\\/virtualclusters\\/[0-9a-zA-Z]+$"}, "VirtualClusterState": {"type": "string", "enum": ["RUNNING", "TERMINATING", "TERMINATED", "ARRESTED"]}, "VirtualClusterStates": {"type": "list", "member": {"shape": "VirtualClusterState"}, "max": 10}, "VirtualClusters": {"type": "list", "member": {"shape": "VirtualCluster"}}}, "documentation": "<p>Amazon EMR on EKS provides a deployment option for Amazon EMR that allows you to run open-source big data frameworks on Amazon Elastic Kubernetes Service (Amazon EKS). With this deployment option, you can focus on running analytics workloads while Amazon EMR on EKS builds, configures, and manages containers for open-source applications. For more information about Amazon EMR on EKS concepts and tasks, see <a href=\"https://docs.aws.amazon.com/emr/latest/EMR-on-EKS-DevelopmentGuide/emr-eks.html\">What is shared id=\"EMR-EKS\"/&gt;</a>.</p> <p> <i>Amazon EMR containers</i> is the API name for Amazon EMR on EKS. The <code>emr-containers</code> prefix is used in the following scenarios: </p> <ul> <li> <p>It is the prefix in the CLI commands for Amazon EMR on EKS. For example, <code>aws emr-containers start-job-run</code>.</p> </li> <li> <p>It is the prefix before IAM policy actions for Amazon EMR on EKS. For example, <code>\"Action\": [ \"emr-containers:StartJobRun\"]</code>. For more information, see <a href=\"https://docs.aws.amazon.com/emr/latest/EMR-on-EKS-DevelopmentGuide/security_iam_service-with-iam.html#security_iam_service-with-iam-id-based-policies-actions\">Policy actions for Amazon EMR on EKS</a>.</p> </li> <li> <p>It is the prefix used in Amazon EMR on EKS service endpoints. For example, <code>emr-containers.us-east-2.amazonaws.com</code>. For more information, see <a href=\"https://docs.aws.amazon.com/emr/latest/EMR-on-EKS-DevelopmentGuide/service-quotas.html#service-endpoints\">Amazon EMR on EKSService Endpoints</a>.</p> </li> </ul>"}