{"version": "2.0", "metadata": {"apiVersion": "2023-08-01", "endpointPrefix": "neptune-db", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "Amazon NeptuneData", "serviceId": "neptunedata", "signatureVersion": "v4", "signingName": "neptune-db", "uid": "neptunedata-2023-08-01"}, "operations": {"CancelGremlinQuery": {"name": "CancelGremlinQuery", "http": {"method": "DELETE", "requestUri": "/gremlin/status/{queryId}", "responseCode": 200}, "input": {"shape": "CancelGremlinQueryInput"}, "output": {"shape": "CancelGremlinQueryOutput"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientTimeoutException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedOperationException"}, {"shape": "FailureByQueryException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ParsingException"}, {"shape": "ConstraintViolationException"}, {"shape": "TimeLimitExceededException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}, {"shape": "MissingParameterException"}], "documentation": "<p>Cancels a Gremlin query. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/gremlin-api-status-cancel.html\">Gremlin query cancellation</a> for more information.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#cancelquery\">neptune-db:CancelQuery</a> IAM action in that cluster.</p>", "idempotent": true}, "CancelLoaderJob": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "http": {"method": "DELETE", "requestUri": "/loader/{loadId}", "responseCode": 200}, "input": {"shape": "CancelLoaderJobInput"}, "output": {"shape": "CancelLoaderJobOutput"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InvalidParameterException"}, {"shape": "BulkLoadIdNotFoundException"}, {"shape": "ClientTimeoutException"}, {"shape": "LoadUrlAccessDeniedException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedOperationException"}, {"shape": "InternalFailureException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}], "documentation": "<p>Cancels a specified load job. This is an HTTP <code>DELETE</code> request. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/load-api-reference-status.htm\">Neptune Loader Get-Status API</a> for more information.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#cancelloaderjob\">neptune-db:CancelLoaderJob</a> IAM action in that cluster..</p>", "idempotent": true}, "CancelMLDataProcessingJob": {"name": "CancelMLDataProcessingJob", "http": {"method": "DELETE", "requestUri": "/ml/dataprocessing/{id}", "responseCode": 200}, "input": {"shape": "CancelMLDataProcessingJobInput"}, "output": {"shape": "CancelMLDataProcessingJobOutput"}, "errors": [{"shape": "UnsupportedOperationException"}, {"shape": "BadRequestException"}, {"shape": "MLResourceNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientTimeoutException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Cancels a Neptune ML data processing job. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/machine-learning-api-dataprocessing.html\">The <code>dataprocessing</code> command</a>.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#cancelmldataprocessingjob\">neptune-db:CancelMLDataProcessingJob</a> IAM action in that cluster.</p>", "idempotent": true}, "CancelMLModelTrainingJob": {"name": "CancelMLModelTrainingJob", "http": {"method": "DELETE", "requestUri": "/ml/modeltraining/{id}", "responseCode": 200}, "input": {"shape": "CancelMLModelTrainingJobInput"}, "output": {"shape": "CancelMLModelTrainingJobOutput"}, "errors": [{"shape": "UnsupportedOperationException"}, {"shape": "BadRequestException"}, {"shape": "MLResourceNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientTimeoutException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Cancels a Neptune ML model training job. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/machine-learning-api-modeltraining.html\">Model training using the <code>modeltraining</code> command</a>.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#cancelmlmodeltrainingjob\">neptune-db:CancelMLModelTrainingJob</a> IAM action in that cluster.</p>", "idempotent": true}, "CancelMLModelTransformJob": {"name": "CancelMLModelTransformJob", "http": {"method": "DELETE", "requestUri": "/ml/modeltransform/{id}", "responseCode": 200}, "input": {"shape": "CancelMLModelTransformJobInput"}, "output": {"shape": "CancelMLModelTransformJobOutput"}, "errors": [{"shape": "UnsupportedOperationException"}, {"shape": "BadRequestException"}, {"shape": "MLResourceNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientTimeoutException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Cancels a specified model transform job. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/machine-learning-model-transform.html\">Use a trained model to generate new model artifacts</a>.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#cancelmlmodeltransformjob\">neptune-db:CancelMLModelTransformJob</a> IAM action in that cluster.</p>", "idempotent": true}, "CancelOpenCypherQuery": {"name": "CancelOpenCypherQuery", "http": {"method": "DELETE", "requestUri": "/opencypher/status/{queryId}", "responseCode": 200}, "input": {"shape": "CancelOpenCypherQueryInput"}, "output": {"shape": "CancelOpenCypherQueryOutput"}, "errors": [{"shape": "InvalidNumericDataException"}, {"shape": "BadRequestException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientTimeoutException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedOperationException"}, {"shape": "FailureByQueryException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ParsingException"}, {"shape": "ConstraintViolationException"}, {"shape": "TimeLimitExceededException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}, {"shape": "MissingParameterException"}], "documentation": "<p>Cancels a specified openCypher query. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/access-graph-opencypher-status.html\">Neptune openCypher status endpoint</a> for more information.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#cancelquery\">neptune-db:CancelQuery</a> IAM action in that cluster.</p>", "idempotent": true}, "CreateMLEndpoint": {"name": "CreateMLEndpoint", "http": {"method": "POST", "requestUri": "/ml/endpoints", "responseCode": 200}, "input": {"shape": "CreateMLEndpointInput"}, "output": {"shape": "CreateMLEndpointOutput"}, "errors": [{"shape": "UnsupportedOperationException"}, {"shape": "BadRequestException"}, {"shape": "MLResourceNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientTimeoutException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Creates a new Neptune ML inference endpoint that lets you query one specific model that the model-training process constructed. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/machine-learning-api-endpoints.html\">Managing inference endpoints using the endpoints command</a>.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#createmlendpoint\">neptune-db:CreateMLEndpoint</a> IAM action in that cluster.</p>"}, "DeleteMLEndpoint": {"name": "DeleteMLEndpoint", "http": {"method": "DELETE", "requestUri": "/ml/endpoints/{id}", "responseCode": 200}, "input": {"shape": "DeleteMLEndpointInput"}, "output": {"shape": "DeleteMLEndpointOutput"}, "errors": [{"shape": "UnsupportedOperationException"}, {"shape": "BadRequestException"}, {"shape": "MLResourceNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientTimeoutException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Cancels the creation of a Neptune ML inference endpoint. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/machine-learning-api-endpoints.html\">Managing inference endpoints using the endpoints command</a>.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#deletemlendpoint\">neptune-db:DeleteMLEndpoint</a> IAM action in that cluster.</p>", "idempotent": true}, "DeletePropertygraphStatistics": {"name": "DeletePropertygraphStatistics", "http": {"method": "DELETE", "requestUri": "/propertygraph/statistics", "responseCode": 200}, "output": {"shape": "DeletePropertygraphStatisticsOutput"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InvalidParameterException"}, {"shape": "StatisticsNotAvailableException"}, {"shape": "ClientTimeoutException"}, {"shape": "AccessDeniedException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedOperationException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ReadOnlyViolationException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}], "documentation": "<p>Deletes statistics for Gremlin and openCypher (property graph) data.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#deletestatistics\">neptune-db:DeleteStatistics</a> IAM action in that cluster.</p>", "idempotent": true}, "DeleteSparqlStatistics": {"name": "DeleteSparqlStatistics", "http": {"method": "DELETE", "requestUri": "/sparql/statistics", "responseCode": 200}, "output": {"shape": "DeleteSparqlStatisticsOutput"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InvalidParameterException"}, {"shape": "StatisticsNotAvailableException"}, {"shape": "ClientTimeoutException"}, {"shape": "AccessDeniedException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedOperationException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ReadOnlyViolationException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}], "documentation": "<p>Deletes SPARQL statistics</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#deletestatistics\">neptune-db:DeleteStatistics</a> IAM action in that cluster.</p>", "idempotent": true}, "ExecuteFastReset": {"name": "ExecuteFastReset", "http": {"method": "POST", "requestUri": "/system", "responseCode": 200}, "input": {"shape": "ExecuteFastResetInput"}, "output": {"shape": "ExecuteFastResetOutput"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ClientTimeoutException"}, {"shape": "AccessDeniedException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedOperationException"}, {"shape": "ServerShutdownException"}, {"shape": "PreconditionsFailedException"}, {"shape": "MethodNotAllowedException"}, {"shape": "ReadOnlyViolationException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}], "documentation": "<p>The fast reset REST API lets you reset a Neptune graph quicky and easily, removing all of its data.</p> <p>Neptune fast reset is a two-step process. First you call <code>ExecuteFastReset</code> with <code>action</code> set to <code>initiateDatabaseReset</code>. This returns a UUID token which you then include when calling <code>ExecuteFastReset</code> again with <code>action</code> set to <code>performDatabaseReset</code>. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/manage-console-fast-reset.html\">Empty an Amazon Neptune DB cluster using the fast reset API</a>.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#resetdatabase\">neptune-db:ResetDatabase</a> IAM action in that cluster.</p>", "idempotent": true}, "ExecuteGremlinExplainQuery": {"name": "ExecuteGremlinExplainQuery", "http": {"method": "POST", "requestUri": "/gremlin/explain", "responseCode": 200}, "input": {"shape": "ExecuteGremlinExplainQueryInput"}, "output": {"shape": "ExecuteGremlinExplainQueryOutput"}, "errors": [{"shape": "QueryTooLargeException"}, {"shape": "BadRequestException"}, {"shape": "QueryLimitExceededException"}, {"shape": "InvalidParameterException"}, {"shape": "QueryLimitException"}, {"shape": "ClientTimeoutException"}, {"shape": "CancelledByUserException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedOperationException"}, {"shape": "FailureByQueryException"}, {"shape": "MemoryLimitExceededException"}, {"shape": "PreconditionsFailedException"}, {"shape": "MalformedQueryException"}, {"shape": "ParsingException"}, {"shape": "ConstraintViolationException"}, {"shape": "TimeLimitExceededException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}, {"shape": "MissingParameterException"}], "documentation": "<p>Executes a Gremlin Explain query.</p> <p>Amazon Neptune has added a Gremlin feature named <code>explain</code> that provides is a self-service tool for understanding the execution approach being taken by the Neptune engine for the query. You invoke it by adding an <code>explain</code> parameter to an HTTP call that submits a Gremlin query.</p> <p>The explain feature provides information about the logical structure of query execution plans. You can use this information to identify potential evaluation and execution bottlenecks and to tune your query, as explained in <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/gremlin-traversal-tuning.html\">Tuning Gremlin queries</a>. You can also use query hints to improve query execution plans.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows one of the following IAM actions in that cluster, depending on the query:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#readdataviaquery\">neptune-db:ReadDataViaQuery</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#writedataviaquery\">neptune-db:WriteDataViaQuery</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#deletedataviaquery\">neptune-db:DeleteDataViaQuery</a> </p> </li> </ul> <p>Note that the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-data-condition-keys.html#iam-neptune-condition-keys\">neptune-db:QueryLanguage:Gremlin</a> IAM condition key can be used in the policy document to restrict the use of Gremlin queries (see <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-data-condition-keys.html\">Condition keys available in Neptune IAM data-access policy statements</a>).</p>"}, "ExecuteGremlinProfileQuery": {"name": "ExecuteGremlinProfileQuery", "http": {"method": "POST", "requestUri": "/gremlin/profile", "responseCode": 200}, "input": {"shape": "ExecuteGremlinProfileQueryInput"}, "output": {"shape": "ExecuteGremlinProfileQueryOutput"}, "errors": [{"shape": "QueryTooLargeException"}, {"shape": "BadRequestException"}, {"shape": "QueryLimitExceededException"}, {"shape": "InvalidParameterException"}, {"shape": "QueryLimitException"}, {"shape": "ClientTimeoutException"}, {"shape": "CancelledByUserException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedOperationException"}, {"shape": "FailureByQueryException"}, {"shape": "MemoryLimitExceededException"}, {"shape": "PreconditionsFailedException"}, {"shape": "MalformedQueryException"}, {"shape": "ParsingException"}, {"shape": "ConstraintViolationException"}, {"shape": "TimeLimitExceededException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}, {"shape": "MissingParameterException"}], "documentation": "<p>Executes a Gremlin Profile query, which runs a specified traversal, collects various metrics about the run, and produces a profile report as output. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/gremlin-profile-api.html\">Gremlin profile API in Neptune</a> for details.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#readdataviaquery\">neptune-db:ReadDataViaQuery</a> IAM action in that cluster.</p> <p>Note that the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-data-condition-keys.html#iam-neptune-condition-keys\">neptune-db:QueryLanguage:Gremlin</a> IAM condition key can be used in the policy document to restrict the use of Gremlin queries (see <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-data-condition-keys.html\">Condition keys available in Neptune IAM data-access policy statements</a>).</p>"}, "ExecuteGremlinQuery": {"name": "ExecuteGremlinQuery", "http": {"method": "POST", "requestUri": "/gremlin", "responseCode": 200}, "input": {"shape": "ExecuteGremlinQueryInput"}, "output": {"shape": "ExecuteGremlinQueryOutput"}, "errors": [{"shape": "QueryTooLargeException"}, {"shape": "BadRequestException"}, {"shape": "QueryLimitExceededException"}, {"shape": "InvalidParameterException"}, {"shape": "QueryLimitException"}, {"shape": "ClientTimeoutException"}, {"shape": "CancelledByUserException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedOperationException"}, {"shape": "FailureByQueryException"}, {"shape": "MemoryLimitExceededException"}, {"shape": "PreconditionsFailedException"}, {"shape": "MalformedQueryException"}, {"shape": "ParsingException"}, {"shape": "ConstraintViolationException"}, {"shape": "TimeLimitExceededException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}, {"shape": "MissingParameterException"}], "documentation": "<p>This commands executes a Gremlin query. Amazon Neptune is compatible with Apache TinkerPop3 and Gremlin, so you can use the Gremlin traversal language to query the graph, as described under <a href=\"https://tinkerpop.apache.org/docs/current/reference/#graph\">The Graph</a> in the Apache TinkerPop3 documentation. More details can also be found in <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/access-graph-gremlin.html\">Accessing a Neptune graph with Gremlin</a>.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that enables one of the following IAM actions in that cluster, depending on the query:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#readdataviaquery\">neptune-db:ReadDataViaQuery</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#writedataviaquery\">neptune-db:WriteDataViaQuery</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#deletedataviaquery\">neptune-db:DeleteDataViaQuery</a> </p> </li> </ul> <p>Note that the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-data-condition-keys.html#iam-neptune-condition-keys\">neptune-db:QueryLanguage:Gremlin</a> IAM condition key can be used in the policy document to restrict the use of Gremlin queries (see <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-data-condition-keys.html\">Condition keys available in Neptune IAM data-access policy statements</a>).</p>"}, "ExecuteOpenCypherExplainQuery": {"name": "ExecuteOpenCypherExplainQuery", "http": {"method": "POST", "requestUri": "/opencypher/explain", "responseCode": 200}, "input": {"shape": "ExecuteOpenCypherExplainQueryInput"}, "output": {"shape": "ExecuteOpenCypherExplainQueryOutput"}, "errors": [{"shape": "QueryTooLargeException"}, {"shape": "InvalidNumericDataException"}, {"shape": "BadRequestException"}, {"shape": "QueryLimitExceededException"}, {"shape": "InvalidParameterException"}, {"shape": "QueryLimitException"}, {"shape": "ClientTimeoutException"}, {"shape": "CancelledByUserException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedOperationException"}, {"shape": "FailureByQueryException"}, {"shape": "MemoryLimitExceededException"}, {"shape": "PreconditionsFailedException"}, {"shape": "MalformedQueryException"}, {"shape": "ParsingException"}, {"shape": "ConstraintViolationException"}, {"shape": "TimeLimitExceededException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}, {"shape": "MissingParameterException"}], "documentation": "<p>Executes an openCypher <code>explain</code> request. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/access-graph-opencypher-explain.html\">The openCypher explain feature</a> for more information.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#readdataviaquery\">neptune-db:ReadDataViaQuery</a> IAM action in that cluster.</p> <p>Note that the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-data-condition-keys.html#iam-neptune-condition-keys\">neptune-db:QueryLanguage:OpenCypher</a> IAM condition key can be used in the policy document to restrict the use of openCypher queries (see <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-data-condition-keys.html\">Condition keys available in Neptune IAM data-access policy statements</a>).</p>"}, "ExecuteOpenCypherQuery": {"name": "ExecuteOpenCypherQuery", "http": {"method": "POST", "requestUri": "/opencypher", "responseCode": 200}, "input": {"shape": "ExecuteOpenCypherQueryInput"}, "output": {"shape": "ExecuteOpenCypherQueryOutput"}, "errors": [{"shape": "QueryTooLargeException"}, {"shape": "InvalidNumericDataException"}, {"shape": "BadRequestException"}, {"shape": "QueryLimitExceededException"}, {"shape": "InvalidParameterException"}, {"shape": "QueryLimitException"}, {"shape": "ClientTimeoutException"}, {"shape": "CancelledByUserException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedOperationException"}, {"shape": "FailureByQueryException"}, {"shape": "MemoryLimitExceededException"}, {"shape": "PreconditionsFailedException"}, {"shape": "MalformedQueryException"}, {"shape": "ParsingException"}, {"shape": "ConstraintViolationException"}, {"shape": "TimeLimitExceededException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}, {"shape": "MissingParameterException"}], "documentation": "<p>Executes an openCypher query. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/access-graph-opencypher.html\">Accessing the Neptune Graph with openCypher</a> for more information.</p> <p>Neptune supports building graph applications using openCypher, which is currently one of the most popular query languages among developers working with graph databases. Developers, business analysts, and data scientists like openCypher's declarative, SQL-inspired syntax because it provides a familiar structure in which to querying property graphs.</p> <p>The openCypher language was originally developed by Neo4j, then open-sourced in 2015 and contributed to the <a href=\"https://opencypher.org/\">openCypher project</a> under an Apache 2 open-source license.</p> <p>Note that when invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows one of the following IAM actions in that cluster, depending on the query:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#readdataviaquery\">neptune-db:ReadDataViaQuery</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#writedataviaquery\">neptune-db:WriteDataViaQuery</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#deletedataviaquery\">neptune-db:DeleteDataViaQuery</a> </p> </li> </ul> <p>Note also that the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-data-condition-keys.html#iam-neptune-condition-keys\">neptune-db:QueryLanguage:OpenCypher</a> IAM condition key can be used in the policy document to restrict the use of openCypher queries (see <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-data-condition-keys.html\">Condition keys available in Neptune IAM data-access policy statements</a>).</p>"}, "GetEngineStatus": {"name": "GetEngineStatus", "http": {"method": "GET", "requestUri": "/status", "responseCode": 200}, "output": {"shape": "GetEngineStatusOutput"}, "errors": [{"shape": "UnsupportedOperationException"}, {"shape": "InternalFailureException"}, {"shape": "ClientTimeoutException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Retrieves the status of the graph database on the host.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#getenginestatus\">neptune-db:GetEngineStatus</a> IAM action in that cluster.</p>"}, "GetGremlinQueryStatus": {"name": "GetGremlinQueryStatus", "http": {"method": "GET", "requestUri": "/gremlin/status/{queryId}", "responseCode": 200}, "input": {"shape": "GetGremlinQueryStatusInput"}, "output": {"shape": "GetGremlinQueryStatusOutput"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientTimeoutException"}, {"shape": "AccessDeniedException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedOperationException"}, {"shape": "FailureByQueryException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ParsingException"}, {"shape": "ReadOnlyViolationException"}, {"shape": "ConstraintViolationException"}, {"shape": "TimeLimitExceededException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}, {"shape": "MissingParameterException"}], "documentation": "<p>Gets the status of a specified Gremlin query.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#getquerystatus\">neptune-db:GetQueryStatus</a> IAM action in that cluster.</p> <p>Note that the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-data-condition-keys.html#iam-neptune-condition-keys\">neptune-db:QueryLanguage:Gremlin</a> IAM condition key can be used in the policy document to restrict the use of Gremlin queries (see <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-data-condition-keys.html\">Condition keys available in Neptune IAM data-access policy statements</a>).</p>"}, "GetLoaderJobStatus": {"name": "GetLoaderJobStatus", "http": {"method": "GET", "requestUri": "/loader/{loadId}", "responseCode": 200}, "input": {"shape": "GetLoaderJobStatusInput"}, "output": {"shape": "GetLoaderJobStatusOutput"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InvalidParameterException"}, {"shape": "BulkLoadIdNotFoundException"}, {"shape": "ClientTimeoutException"}, {"shape": "LoadUrlAccessDeniedException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedOperationException"}, {"shape": "InternalFailureException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}], "documentation": "<p>Gets status information about a specified load job. Neptune keeps track of the most recent 1,024 bulk load jobs, and stores the last 10,000 error details per job.</p> <p>See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/load-api-reference-status.htm\">Neptune Loader Get-Status API</a> for more information.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#getloaderjobstatus\">neptune-db:GetLoaderJobStatus</a> IAM action in that cluster..</p>"}, "GetMLDataProcessingJob": {"name": "GetMLDataProcessingJob", "http": {"method": "GET", "requestUri": "/ml/dataprocessing/{id}", "responseCode": 200}, "input": {"shape": "GetMLDataProcessingJobInput"}, "output": {"shape": "GetMLDataProcessingJobOutput"}, "errors": [{"shape": "UnsupportedOperationException"}, {"shape": "BadRequestException"}, {"shape": "MLResourceNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientTimeoutException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Retrieves information about a specified data processing job. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/machine-learning-api-dataprocessing.html\">The <code>dataprocessing</code> command</a>.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#getmldataprocessingjobstatus\">neptune-db:neptune-db:GetMLDataProcessingJobStatus</a> IAM action in that cluster.</p>"}, "GetMLEndpoint": {"name": "GetMLEndpoint", "http": {"method": "GET", "requestUri": "/ml/endpoints/{id}", "responseCode": 200}, "input": {"shape": "GetMLEndpointInput"}, "output": {"shape": "GetMLEndpointOutput"}, "errors": [{"shape": "UnsupportedOperationException"}, {"shape": "BadRequestException"}, {"shape": "MLResourceNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientTimeoutException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Retrieves details about an inference endpoint. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/machine-learning-api-endpoints.html\">Managing inference endpoints using the endpoints command</a>.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#getmlendpointstatus\">neptune-db:GetMLEndpointStatus</a> IAM action in that cluster.</p>"}, "GetMLModelTrainingJob": {"name": "GetMLModelTrainingJob", "http": {"method": "GET", "requestUri": "/ml/modeltraining/{id}", "responseCode": 200}, "input": {"shape": "GetMLModelTrainingJobInput"}, "output": {"shape": "GetMLModelTrainingJobOutput"}, "errors": [{"shape": "UnsupportedOperationException"}, {"shape": "BadRequestException"}, {"shape": "MLResourceNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientTimeoutException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Retrieves information about a Neptune ML model training job. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/machine-learning-api-modeltraining.html\">Model training using the <code>modeltraining</code> command</a>.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#getmlmodeltrainingjobstatus\">neptune-db:GetMLModelTrainingJobStatus</a> IAM action in that cluster.</p>"}, "GetMLModelTransformJob": {"name": "GetMLModelTransformJob", "http": {"method": "GET", "requestUri": "/ml/modeltransform/{id}", "responseCode": 200}, "input": {"shape": "GetMLModelTransformJobInput"}, "output": {"shape": "GetMLModelTransformJobOutput"}, "errors": [{"shape": "UnsupportedOperationException"}, {"shape": "BadRequestException"}, {"shape": "MLResourceNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientTimeoutException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Gets information about a specified model transform job. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/machine-learning-model-transform.html\">Use a trained model to generate new model artifacts</a>.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#getmlmodeltransformjobstatus\">neptune-db:GetMLModelTransformJobStatus</a> IAM action in that cluster.</p>"}, "GetOpenCypherQueryStatus": {"name": "GetOpenCypherQueryStatus", "http": {"method": "GET", "requestUri": "/opencypher/status/{queryId}", "responseCode": 200}, "input": {"shape": "GetOpenCypherQueryStatusInput"}, "output": {"shape": "GetOpenCypherQueryStatusOutput"}, "errors": [{"shape": "InvalidNumericDataException"}, {"shape": "BadRequestException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientTimeoutException"}, {"shape": "AccessDeniedException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedOperationException"}, {"shape": "FailureByQueryException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ParsingException"}, {"shape": "ReadOnlyViolationException"}, {"shape": "ConstraintViolationException"}, {"shape": "TimeLimitExceededException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}, {"shape": "MissingParameterException"}], "documentation": "<p>Retrieves the status of a specified openCypher query.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#getquerystatus\">neptune-db:GetQueryStatus</a> IAM action in that cluster.</p> <p>Note that the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-data-condition-keys.html#iam-neptune-condition-keys\">neptune-db:QueryLanguage:OpenCypher</a> IAM condition key can be used in the policy document to restrict the use of openCypher queries (see <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-data-condition-keys.html\">Condition keys available in Neptune IAM data-access policy statements</a>).</p>"}, "GetPropertygraphStatistics": {"name": "GetPropertygraphStatistics", "http": {"method": "GET", "requestUri": "/propertygraph/statistics", "responseCode": 200}, "output": {"shape": "GetPropertygraphStatisticsOutput"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InvalidParameterException"}, {"shape": "StatisticsNotAvailableException"}, {"shape": "ClientTimeoutException"}, {"shape": "AccessDeniedException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedOperationException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ReadOnlyViolationException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}], "documentation": "<p>Gets property graph statistics (Gremlin and openCypher).</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#getstatisticsstatus\">neptune-db:GetStatisticsStatus</a> IAM action in that cluster.</p>"}, "GetPropertygraphStream": {"name": "GetPropertygraphStream", "http": {"method": "GET", "requestUri": "/propertygraph/stream", "responseCode": 200}, "input": {"shape": "GetPropertygraphStreamInput"}, "output": {"shape": "GetPropertygraphStreamOutput"}, "errors": [{"shape": "UnsupportedOperationException"}, {"shape": "ExpiredStreamException"}, {"shape": "InvalidParameterException"}, {"shape": "MemoryLimitExceededException"}, {"shape": "StreamRecordsNotFoundException"}, {"shape": "ClientTimeoutException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ThrottlingException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Gets a stream for a property graph.</p> <p>With the Neptune Streams feature, you can generate a complete sequence of change-log entries that record every change made to your graph data as it happens. <code>GetPropertygraphStream</code> lets you collect these change-log entries for a property graph.</p> <p>The Neptune streams feature needs to be enabled on your Neptune DBcluster. To enable streams, set the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/parameters.html#parameters-db-cluster-parameters-neptune_streams\">neptune_streams</a> DB cluster parameter to <code>1</code>.</p> <p>See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/streams.html\">Capturing graph changes in real time using Neptune streams</a>.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#getstreamrecords\">neptune-db:GetStreamRecords</a> IAM action in that cluster.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that enables one of the following IAM actions, depending on the query:</p> <p>Note that you can restrict property-graph queries using the following IAM context keys:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-data-condition-keys.html#iam-neptune-condition-keys\">neptune-db:QueryLanguage:Gremlin</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-data-condition-keys.html#iam-neptune-condition-keys\">neptune-db:QueryLanguage:OpenCypher</a> </p> </li> </ul> <p>See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-data-condition-keys.html\">Condition keys available in Neptune IAM data-access policy statements</a>).</p>"}, "GetPropertygraphSummary": {"name": "GetPropertygraphSummary", "http": {"method": "GET", "requestUri": "/propertygraph/statistics/summary", "responseCode": 200}, "input": {"shape": "GetPropertygraphSummaryInput"}, "output": {"shape": "GetPropertygraphSummaryOutput"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InvalidParameterException"}, {"shape": "StatisticsNotAvailableException"}, {"shape": "ClientTimeoutException"}, {"shape": "AccessDeniedException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedOperationException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ReadOnlyViolationException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}], "documentation": "<p>Gets a graph summary for a property graph.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#getgraphsummary\">neptune-db:GetGraphSummary</a> IAM action in that cluster.</p>"}, "GetRDFGraphSummary": {"name": "GetRDFGraphSummary", "http": {"method": "GET", "requestUri": "/rdf/statistics/summary", "responseCode": 200}, "input": {"shape": "GetRDFGraphSummaryInput"}, "output": {"shape": "GetRDFGraphSummaryOutput"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InvalidParameterException"}, {"shape": "StatisticsNotAvailableException"}, {"shape": "ClientTimeoutException"}, {"shape": "AccessDeniedException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedOperationException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ReadOnlyViolationException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}], "documentation": "<p>Gets a graph summary for an RDF graph.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#getgraphsummary\">neptune-db:GetGraphSummary</a> IAM action in that cluster.</p>"}, "GetSparqlStatistics": {"name": "GetSparqlStatistics", "http": {"method": "GET", "requestUri": "/sparql/statistics", "responseCode": 200}, "output": {"shape": "GetSparqlStatisticsOutput"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InvalidParameterException"}, {"shape": "StatisticsNotAvailableException"}, {"shape": "ClientTimeoutException"}, {"shape": "AccessDeniedException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedOperationException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ReadOnlyViolationException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}], "documentation": "<p>Gets RDF statistics (SPARQL).</p>"}, "GetSparqlStream": {"name": "GetSparqlStream", "http": {"method": "GET", "requestUri": "/sparql/stream", "responseCode": 200}, "input": {"shape": "GetSparqlStreamInput"}, "output": {"shape": "GetSparqlStreamOutput"}, "errors": [{"shape": "UnsupportedOperationException"}, {"shape": "ExpiredStreamException"}, {"shape": "InvalidParameterException"}, {"shape": "MemoryLimitExceededException"}, {"shape": "StreamRecordsNotFoundException"}, {"shape": "ClientTimeoutException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ThrottlingException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Gets a stream for an RDF graph.</p> <p>With the Neptune Streams feature, you can generate a complete sequence of change-log entries that record every change made to your graph data as it happens. <code>GetSparqlStream</code> lets you collect these change-log entries for an RDF graph.</p> <p>The Neptune streams feature needs to be enabled on your Neptune DBcluster. To enable streams, set the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/parameters.html#parameters-db-cluster-parameters-neptune_streams\">neptune_streams</a> DB cluster parameter to <code>1</code>.</p> <p>See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/streams.html\">Capturing graph changes in real time using Neptune streams</a>.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#getstreamrecords\">neptune-db:GetStreamRecords</a> IAM action in that cluster.</p> <p>Note that the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-data-condition-keys.html#iam-neptune-condition-keys\">neptune-db:QueryLanguage:Sparql</a> IAM condition key can be used in the policy document to restrict the use of SPARQL queries (see <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-data-condition-keys.html\">Condition keys available in Neptune IAM data-access policy statements</a>).</p>"}, "ListGremlinQueries": {"name": "ListGremlinQueries", "http": {"method": "GET", "requestUri": "/gremlin/status", "responseCode": 200}, "input": {"shape": "ListGremlinQueriesInput"}, "output": {"shape": "ListGremlinQueriesOutput"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientTimeoutException"}, {"shape": "AccessDeniedException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedOperationException"}, {"shape": "FailureByQueryException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ParsingException"}, {"shape": "ReadOnlyViolationException"}, {"shape": "ConstraintViolationException"}, {"shape": "TimeLimitExceededException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}, {"shape": "MissingParameterException"}], "documentation": "<p>Lists active Gremlin queries. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/gremlin-api-status.html\">Gremlin query status API</a> for details about the output.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#getquerystatus\">neptune-db:GetQueryStatus</a> IAM action in that cluster.</p> <p>Note that the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-data-condition-keys.html#iam-neptune-condition-keys\">neptune-db:QueryLanguage:Gremlin</a> IAM condition key can be used in the policy document to restrict the use of Gremlin queries (see <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-data-condition-keys.html\">Condition keys available in Neptune IAM data-access policy statements</a>).</p>"}, "ListLoaderJobs": {"name": "ListLoaderJobs", "http": {"method": "GET", "requestUri": "/loader", "responseCode": 200}, "input": {"shape": "ListLoaderJobsInput"}, "output": {"shape": "ListLoaderJobsOutput"}, "errors": [{"shape": "UnsupportedOperationException"}, {"shape": "BadRequestException"}, {"shape": "InvalidParameterException"}, {"shape": "BulkLoadIdNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ClientTimeoutException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "LoadUrlAccessDeniedException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Retrieves a list of the <code>loadIds</code> for all active loader jobs.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#listloaderjobs\">neptune-db:ListLoaderJobs</a> IAM action in that cluster..</p>"}, "ListMLDataProcessingJobs": {"name": "ListMLDataProcessingJobs", "http": {"method": "GET", "requestUri": "/ml/dataprocessing", "responseCode": 200}, "input": {"shape": "ListMLDataProcessingJobsInput"}, "output": {"shape": "ListMLDataProcessingJobsOutput"}, "errors": [{"shape": "UnsupportedOperationException"}, {"shape": "BadRequestException"}, {"shape": "MLResourceNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientTimeoutException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Returns a list of Neptune ML data processing jobs. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/machine-learning-api-dataprocessing.html#machine-learning-api-dataprocessing-list-jobs\">Listing active data-processing jobs using the Neptune ML dataprocessing command</a>.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#listmldataprocessingjobs\">neptune-db:ListMLDataProcessingJobs</a> IAM action in that cluster.</p>"}, "ListMLEndpoints": {"name": "ListMLEndpoints", "http": {"method": "GET", "requestUri": "/ml/endpoints", "responseCode": 200}, "input": {"shape": "ListMLEndpointsInput"}, "output": {"shape": "ListMLEndpointsOutput"}, "errors": [{"shape": "UnsupportedOperationException"}, {"shape": "BadRequestException"}, {"shape": "MLResourceNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientTimeoutException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Lists existing inference endpoints. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/machine-learning-api-endpoints.html\">Managing inference endpoints using the endpoints command</a>.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#listmlendpoints\">neptune-db:ListMLEndpoints</a> IAM action in that cluster.</p>"}, "ListMLModelTrainingJobs": {"name": "ListMLModelTrainingJobs", "http": {"method": "GET", "requestUri": "/ml/modeltraining", "responseCode": 200}, "input": {"shape": "ListMLModelTrainingJobsInput"}, "output": {"shape": "ListMLModelTrainingJobsOutput"}, "errors": [{"shape": "UnsupportedOperationException"}, {"shape": "BadRequestException"}, {"shape": "MLResourceNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientTimeoutException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Lists Neptune ML model-training jobs. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/machine-learning-api-modeltraining.html\">Model training using the <code>modeltraining</code> command</a>.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#neptune-db:listmlmodeltrainingjobs\">neptune-db:neptune-db:ListMLModelTrainingJobs</a> IAM action in that cluster.</p>"}, "ListMLModelTransformJobs": {"name": "ListMLModelTransformJobs", "http": {"method": "GET", "requestUri": "/ml/modeltransform", "responseCode": 200}, "input": {"shape": "ListMLModelTransformJobsInput"}, "output": {"shape": "ListMLModelTransformJobsOutput"}, "errors": [{"shape": "UnsupportedOperationException"}, {"shape": "BadRequestException"}, {"shape": "MLResourceNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientTimeoutException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Returns a list of model transform job IDs. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/machine-learning-model-transform.html\">Use a trained model to generate new model artifacts</a>.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#listmlmodeltransformjobs\">neptune-db:ListMLModelTransformJobs</a> IAM action in that cluster.</p>"}, "ListOpenCypherQueries": {"name": "ListOpenCypherQueries", "http": {"method": "GET", "requestUri": "/opencypher/status", "responseCode": 200}, "input": {"shape": "ListOpenCypherQueriesInput"}, "output": {"shape": "ListOpenCypherQueriesOutput"}, "errors": [{"shape": "InvalidNumericDataException"}, {"shape": "BadRequestException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientTimeoutException"}, {"shape": "AccessDeniedException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedOperationException"}, {"shape": "FailureByQueryException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ParsingException"}, {"shape": "ReadOnlyViolationException"}, {"shape": "ConstraintViolationException"}, {"shape": "TimeLimitExceededException"}, {"shape": "InvalidArgumentException"}, {"shape": "ConcurrentModificationException"}, {"shape": "MissingParameterException"}], "documentation": "<p>Lists active openCypher queries. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/access-graph-opencypher-status.html\">Neptune openCypher status endpoint</a> for more information.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#getquerystatus\">neptune-db:GetQueryStatus</a> IAM action in that cluster.</p> <p>Note that the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-data-condition-keys.html#iam-neptune-condition-keys\">neptune-db:QueryLanguage:OpenCypher</a> IAM condition key can be used in the policy document to restrict the use of openCypher queries (see <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-data-condition-keys.html\">Condition keys available in Neptune IAM data-access policy statements</a>).</p>"}, "ManagePropertygraphStatistics": {"name": "ManagePropertygraphStatistics", "http": {"method": "POST", "requestUri": "/propertygraph/statistics", "responseCode": 200}, "input": {"shape": "ManagePropertygraphStatisticsInput"}, "output": {"shape": "ManagePropertygraphStatisticsOutput"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InvalidParameterException"}, {"shape": "StatisticsNotAvailableException"}, {"shape": "ClientTimeoutException"}, {"shape": "AccessDeniedException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedOperationException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ReadOnlyViolationException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}], "documentation": "<p>Manages the generation and use of property graph statistics.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#managestatistics\">neptune-db:ManageStatistics</a> IAM action in that cluster.</p>", "idempotent": true}, "ManageSparqlStatistics": {"name": "ManageSparqlStatistics", "http": {"method": "POST", "requestUri": "/sparql/statistics", "responseCode": 200}, "input": {"shape": "ManageSparqlStatisticsInput"}, "output": {"shape": "ManageSparqlStatisticsOutput"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InvalidParameterException"}, {"shape": "StatisticsNotAvailableException"}, {"shape": "ClientTimeoutException"}, {"shape": "AccessDeniedException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedOperationException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ReadOnlyViolationException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}], "documentation": "<p>Manages the generation and use of RDF graph statistics.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#managestatistics\">neptune-db:ManageStatistics</a> IAM action in that cluster.</p>", "idempotent": true}, "StartLoaderJob": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/loader", "responseCode": 200}, "input": {"shape": "StartLoaderJobInput"}, "output": {"shape": "StartLoaderJobOutput"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InvalidParameterException"}, {"shape": "BulkLoadIdNotFoundException"}, {"shape": "ClientTimeoutException"}, {"shape": "LoadUrlAccessDeniedException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedOperationException"}, {"shape": "InternalFailureException"}, {"shape": "S3Exception"}, {"shape": "PreconditionsFailedException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}], "documentation": "<p>Starts a Neptune bulk loader job to load data from an Amazon S3 bucket into a Neptune DB instance. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/bulk-load.html\">Using the Amazon Neptune Bulk Loader to Ingest Data</a>.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#startloaderjob\">neptune-db:StartLoaderJob</a> IAM action in that cluster.</p>", "idempotent": true}, "StartMLDataProcessingJob": {"name": "StartMLDataProcessingJob", "http": {"method": "POST", "requestUri": "/ml/dataprocessing", "responseCode": 200}, "input": {"shape": "StartMLDataProcessingJobInput"}, "output": {"shape": "StartMLDataProcessingJobOutput"}, "errors": [{"shape": "UnsupportedOperationException"}, {"shape": "BadRequestException"}, {"shape": "MLResourceNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientTimeoutException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Creates a new Neptune ML data processing job for processing the graph data exported from Neptune for training. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/machine-learning-api-dataprocessing.html\">The <code>dataprocessing</code> command</a>.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#startmlmodeldataprocessingjob\">neptune-db:StartMLModelDataProcessingJob</a> IAM action in that cluster.</p>"}, "StartMLModelTrainingJob": {"name": "StartMLModelTrainingJob", "http": {"method": "POST", "requestUri": "/ml/modeltraining", "responseCode": 200}, "input": {"shape": "StartMLModelTrainingJobInput"}, "output": {"shape": "StartMLModelTrainingJobOutput"}, "errors": [{"shape": "UnsupportedOperationException"}, {"shape": "BadRequestException"}, {"shape": "MLResourceNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientTimeoutException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Creates a new Neptune ML model training job. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/machine-learning-api-modeltraining.html\">Model training using the <code>modeltraining</code> command</a>.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#startmlmodeltrainingjob\">neptune-db:StartMLModelTrainingJob</a> IAM action in that cluster.</p>"}, "StartMLModelTransformJob": {"name": "StartMLModelTransformJob", "http": {"method": "POST", "requestUri": "/ml/modeltransform", "responseCode": 200}, "input": {"shape": "StartMLModelTransformJobInput"}, "output": {"shape": "StartMLModelTransformJobOutput"}, "errors": [{"shape": "UnsupportedOperationException"}, {"shape": "BadRequestException"}, {"shape": "MLResourceNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientTimeoutException"}, {"shape": "PreconditionsFailedException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidArgumentException"}, {"shape": "MissingParameterException"}, {"shape": "IllegalArgumentException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Creates a new model transform job. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/machine-learning-model-transform.html\">Use a trained model to generate new model artifacts</a>.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#startmlmodeltransformjob\">neptune-db:StartMLModelTransformJob</a> IAM action in that cluster.</p>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request in question.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised in case of an authentication or authorization failure.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "Action": {"type": "string", "enum": ["initiateDatabaseReset", "performDatabaseReset"]}, "BadRequestException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the bad request.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when a request is submitted that cannot be processed.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "Blob": {"type": "blob"}, "Boolean": {"type": "boolean", "box": true}, "BulkLoadIdNotFoundException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The bulk-load job ID that could not be found.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when a specified bulk-load job ID cannot be found.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true, "retryable": {"throttling": false}}, "CancelGremlinQueryInput": {"type": "structure", "required": ["queryId"], "members": {"queryId": {"shape": "String", "documentation": "<p>The unique identifier that identifies the query to be canceled.</p>", "location": "uri", "locationName": "queryId"}}}, "CancelGremlinQueryOutput": {"type": "structure", "members": {"status": {"shape": "String", "documentation": "<p>The status of the cancelation</p>"}}}, "CancelLoaderJobInput": {"type": "structure", "required": ["loadId"], "members": {"loadId": {"shape": "String", "documentation": "<p>The ID of the load job to be deleted.</p>", "location": "uri", "locationName": "loadId"}}}, "CancelLoaderJobOutput": {"type": "structure", "members": {"status": {"shape": "String", "documentation": "<p>The cancellation status.</p>"}}}, "CancelMLDataProcessingJobInput": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "String", "documentation": "<p>The unique identifier of the data-processing job.</p>", "location": "uri", "locationName": "id"}, "neptuneIamRoleArn": {"shape": "String", "documentation": "<p>The ARN of an IAM role that provides Neptune access to SageMaker and Amazon S3 resources. This must be listed in your DB cluster parameter group or an error will occur.</p>", "location": "querystring", "locationName": "neptuneIamRoleArn"}, "clean": {"shape": "Boolean", "documentation": "<p>If set to <code>TRUE</code>, this flag specifies that all Neptune ML S3 artifacts should be deleted when the job is stopped. The default is <code>FALSE</code>.</p>", "location": "querystring", "locationName": "clean"}}}, "CancelMLDataProcessingJobOutput": {"type": "structure", "members": {"status": {"shape": "String", "documentation": "<p>The status of the cancellation request.</p>"}}}, "CancelMLModelTrainingJobInput": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "String", "documentation": "<p>The unique identifier of the model-training job to be canceled.</p>", "location": "uri", "locationName": "id"}, "neptuneIamRoleArn": {"shape": "String", "documentation": "<p>The ARN of an IAM role that provides Neptune access to SageMaker and Amazon S3 resources. This must be listed in your DB cluster parameter group or an error will occur.</p>", "location": "querystring", "locationName": "neptuneIamRoleArn"}, "clean": {"shape": "Boolean", "documentation": "<p>If set to <code>TRUE</code>, this flag specifies that all Amazon S3 artifacts should be deleted when the job is stopped. The default is <code>FALSE</code>.</p>", "location": "querystring", "locationName": "clean"}}}, "CancelMLModelTrainingJobOutput": {"type": "structure", "members": {"status": {"shape": "String", "documentation": "<p>The status of the cancellation.</p>"}}}, "CancelMLModelTransformJobInput": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "String", "documentation": "<p>The unique ID of the model transform job to be canceled.</p>", "location": "uri", "locationName": "id"}, "neptuneIamRoleArn": {"shape": "String", "documentation": "<p>The ARN of an IAM role that provides Neptune access to SageMaker and Amazon S3 resources. This must be listed in your DB cluster parameter group or an error will occur.</p>", "location": "querystring", "locationName": "neptuneIamRoleArn"}, "clean": {"shape": "Boolean", "documentation": "<p>If this flag is set to <code>TRUE</code>, all Neptune ML S3 artifacts should be deleted when the job is stopped. The default is <code>FALSE</code>.</p>", "location": "querystring", "locationName": "clean"}}}, "CancelMLModelTransformJobOutput": {"type": "structure", "members": {"status": {"shape": "String", "documentation": "<p>the status of the cancelation.</p>"}}}, "CancelOpenCypherQueryInput": {"type": "structure", "required": ["queryId"], "members": {"queryId": {"shape": "String", "documentation": "<p>The unique ID of the openCypher query to cancel.</p>", "location": "uri", "locationName": "queryId"}, "silent": {"shape": "Boolean", "documentation": "<p>If set to <code>TRUE</code>, causes the cancelation of the openCypher query to happen silently.</p>", "location": "querystring", "locationName": "silent"}}}, "CancelOpenCypherQueryOutput": {"type": "structure", "members": {"status": {"shape": "String", "documentation": "<p>The cancellation status of the openCypher query.</p>"}, "payload": {"shape": "Boolean", "documentation": "<p>The cancelation payload for the openCypher query.</p>"}}}, "CancelledByUserException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request in question.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when a user cancelled a request.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "Classes": {"type": "list", "member": {"shape": "String"}}, "ClientTimeoutException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request in question.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when a request timed out in the client.</p>", "error": {"httpStatusCode": 408, "senderFault": true}, "exception": true, "retryable": {"throttling": false}}, "ConcurrentModificationException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request in question.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when a request attempts to modify data that is concurrently being modified by another process.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "ConstraintViolationException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request in question.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when a value in a request field did not satisfy required constraints.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true, "retryable": {"throttling": false}}, "CreateMLEndpointInput": {"type": "structure", "members": {"id": {"shape": "String", "documentation": "<p>A unique identifier for the new inference endpoint. The default is an autogenerated timestamped name.</p>"}, "mlModelTrainingJobId": {"shape": "String", "documentation": "<p>The job Id of the completed model-training job that has created the model that the inference endpoint will point to. You must supply either the <code>mlModelTrainingJobId</code> or the <code>mlModelTransformJobId</code>.</p>"}, "mlModelTransformJobId": {"shape": "String", "documentation": "<p>The job Id of the completed model-transform job. You must supply either the <code>mlModelTrainingJobId</code> or the <code>mlModelTransformJobId</code>.</p>"}, "update": {"shape": "Boolean", "documentation": "<p>If set to <code>true</code>, <code>update</code> indicates that this is an update request. The default is <code>false</code>. You must supply either the <code>mlModelTrainingJobId</code> or the <code>mlModelTransformJobId</code>.</p>"}, "neptuneIamRoleArn": {"shape": "String", "documentation": "<p>The ARN of an IAM role providing Neptune access to SageMaker and Amazon S3 resources. This must be listed in your DB cluster parameter group or an error will be thrown.</p>"}, "modelName": {"shape": "String", "documentation": "<p>Model type for training. By default the Neptune ML model is automatically based on the <code>modelType</code> used in data processing, but you can specify a different model type here. The default is <code>rgcn</code> for heterogeneous graphs and <code>kge</code> for knowledge graphs. The only valid value for heterogeneous graphs is <code>rgcn</code>. Valid values for knowledge graphs are: <code>kge</code>, <code>transe</code>, <code>distmult</code>, and <code>rotate</code>.</p>"}, "instanceType": {"shape": "String", "documentation": "<p>The type of Neptune ML instance to use for online servicing. The default is <code>ml.m5.xlarge</code>. Choosing the ML instance for an inference endpoint depends on the task type, the graph size, and your budget.</p>"}, "instanceCount": {"shape": "Integer", "documentation": "<p>The minimum number of Amazon EC2 instances to deploy to an endpoint for prediction. The default is 1</p>"}, "volumeEncryptionKMSKey": {"shape": "String", "documentation": "<p>The Amazon Key Management Service (Amazon KMS) key that SageMaker uses to encrypt data on the storage volume attached to the ML compute instances that run the training job. The default is None.</p>"}}}, "CreateMLEndpointOutput": {"type": "structure", "members": {"id": {"shape": "String", "documentation": "<p>The unique ID of the new inference endpoint.</p>"}, "arn": {"shape": "String", "documentation": "<p>The ARN for the new inference endpoint.</p>"}, "creationTimeInMillis": {"shape": "<PERSON>", "documentation": "<p>The endpoint creation time, in milliseconds.</p>"}}}, "CustomModelTrainingParameters": {"type": "structure", "required": ["sourceS3DirectoryPath"], "members": {"sourceS3DirectoryPath": {"shape": "String", "documentation": "<p>The path to the Amazon S3 location where the Python module implementing your model is located. This must point to a valid existing Amazon S3 location that contains, at a minimum, a training script, a transform script, and a <code>model-hpo-configuration.json</code> file.</p>"}, "trainingEntryPointScript": {"shape": "String", "documentation": "<p>The name of the entry point in your module of a script that performs model training and takes hyperparameters as command-line arguments, including fixed hyperparameters. The default is <code>training.py</code>.</p>"}, "transformEntryPointScript": {"shape": "String", "documentation": "<p>The name of the entry point in your module of a script that should be run after the best model from the hyperparameter search has been identified, to compute the model artifacts necessary for model deployment. It should be able to run with no command-line arguments.The default is <code>transform.py</code>.</p>"}}, "documentation": "<p>Contains custom model training parameters. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/machine-learning-custom-models.html\">Custom models in Neptune ML</a>.</p>"}, "CustomModelTransformParameters": {"type": "structure", "required": ["sourceS3DirectoryPath"], "members": {"sourceS3DirectoryPath": {"shape": "String", "documentation": "<p>The path to the Amazon S3 location where the Python module implementing your model is located. This must point to a valid existing Amazon S3 location that contains, at a minimum, a training script, a transform script, and a <code>model-hpo-configuration.json</code> file.</p>"}, "transformEntryPointScript": {"shape": "String", "documentation": "<p>The name of the entry point in your module of a script that should be run after the best model from the hyperparameter search has been identified, to compute the model artifacts necessary for model deployment. It should be able to run with no command-line arguments. The default is <code>transform.py</code>.</p>"}}, "documentation": "<p>Contains custom model transform parameters. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/machine-learning-model-transform.html\">Use a trained model to generate new model artifacts</a>.</p>"}, "DeleteMLEndpointInput": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "String", "documentation": "<p>The unique identifier of the inference endpoint.</p>", "location": "uri", "locationName": "id"}, "neptuneIamRoleArn": {"shape": "String", "documentation": "<p>The ARN of an IAM role providing Neptune access to SageMaker and Amazon S3 resources. This must be listed in your DB cluster parameter group or an error will be thrown.</p>", "location": "querystring", "locationName": "neptuneIamRoleArn"}, "clean": {"shape": "Boolean", "documentation": "<p>If this flag is set to <code>TRUE</code>, all Neptune ML S3 artifacts should be deleted when the job is stopped. The default is <code>FALSE</code>.</p>", "location": "querystring", "locationName": "clean"}}}, "DeleteMLEndpointOutput": {"type": "structure", "members": {"status": {"shape": "String", "documentation": "<p>The status of the cancellation.</p>"}}}, "DeletePropertygraphStatisticsOutput": {"type": "structure", "members": {"statusCode": {"shape": "Integer", "documentation": "<p>The HTTP response code: 200 if the delete was successful, or 204 if there were no statistics to delete.</p>", "location": "statusCode"}, "status": {"shape": "String", "documentation": "<p>The cancel status.</p>"}, "payload": {"shape": "DeleteStatisticsValueMap", "documentation": "<p>The deletion payload.</p>"}}}, "DeleteSparqlStatisticsOutput": {"type": "structure", "members": {"statusCode": {"shape": "Integer", "documentation": "<p>The HTTP response code: 200 if the delete was successful, or 204 if there were no statistics to delete.</p>", "location": "statusCode"}, "status": {"shape": "String", "documentation": "<p>The cancel status.</p>"}, "payload": {"shape": "DeleteStatisticsValueMap", "documentation": "<p>The deletion payload.</p>"}}}, "DeleteStatisticsValueMap": {"type": "structure", "members": {"active": {"shape": "Boolean", "documentation": "<p>The current status of the statistics.</p>"}, "statisticsId": {"shape": "String", "documentation": "<p>The ID of the statistics generation run that is currently occurring.</p>"}}, "documentation": "<p>The payload for DeleteStatistics.</p>"}, "Document": {"type": "structure", "members": {}, "document": true}, "DocumentValuedMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "Document"}}, "EdgeLabels": {"type": "list", "member": {"shape": "String"}}, "EdgeProperties": {"type": "list", "member": {"shape": "String"}}, "EdgeStructure": {"type": "structure", "members": {"count": {"shape": "<PERSON>", "documentation": "<p>The number of edges that have this specific structure.</p>"}, "edgeProperties": {"shape": "EdgeProperties", "documentation": "<p>A list of edge properties present in this specific structure.</p>"}}, "documentation": "<p>An edge structure.</p>"}, "EdgeStructures": {"type": "list", "member": {"shape": "EdgeStructure"}}, "Encoding": {"type": "string", "enum": ["gzip"]}, "ExecuteFastResetInput": {"type": "structure", "required": ["action"], "members": {"action": {"shape": "Action", "documentation": "<p>The fast reset action. One of the following values:</p> <ul> <li> <p> <b> <code>initiateDatabaseReset</code> </b>   –   This action generates a unique token needed to actually perform the fast reset.</p> </li> <li> <p> <b> <code>performDatabaseReset</code> </b>   –   This action uses the token generated by the <code>initiateDatabaseReset</code> action to actually perform the fast reset.</p> <p/> </li> </ul>"}, "token": {"shape": "String", "documentation": "<p>The fast-reset token to initiate the reset.</p>"}}}, "ExecuteFastResetOutput": {"type": "structure", "required": ["status"], "members": {"status": {"shape": "String", "documentation": "<p>The <code>status</code> is only returned for the <code>performDatabaseReset</code> action, and indicates whether or not the fast reset rquest is accepted.</p>"}, "payload": {"shape": "FastResetToken", "documentation": "<p>The <code>payload</code> is only returned by the <code>initiateDatabaseReset</code> action, and contains the unique token to use with the <code>performDatabaseReset</code> action to make the reset occur.</p>"}}}, "ExecuteGremlinExplainQueryInput": {"type": "structure", "required": ["grem<PERSON><PERSON><PERSON><PERSON>"], "members": {"gremlinQuery": {"shape": "String", "documentation": "<p>The Gremlin explain query string.</p>", "locationName": "gremlin"}}}, "ExecuteGremlinExplainQueryOutput": {"type": "structure", "members": {"output": {"shape": "ReportAsText", "documentation": "<p>A text blob containing the Gremlin explain result, as described in <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/gremlin-traversal-tuning.html\">Tuning Gremlin queries</a>.</p>"}}, "payload": "output"}, "ExecuteGremlinProfileQueryInput": {"type": "structure", "required": ["grem<PERSON><PERSON><PERSON><PERSON>"], "members": {"gremlinQuery": {"shape": "String", "documentation": "<p>The Gremlin query string to profile.</p>", "locationName": "gremlin"}, "results": {"shape": "Boolean", "documentation": "<p>If this flag is set to <code>TRUE</code>, the query results are gathered and displayed as part of the profile report. If <code>FALSE</code>, only the result count is displayed.</p>", "locationName": "profile.results"}, "chop": {"shape": "Integer", "documentation": "<p>If non-zero, causes the results string to be truncated at that number of characters. If set to zero, the string contains all the results.</p>", "locationName": "profile.chop"}, "serializer": {"shape": "String", "documentation": "<p>If non-null, the gathered results are returned in a serialized response message in the format specified by this parameter. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/gremlin-profile-api.html\">Gremlin profile API in Neptune</a> for more information.</p>", "locationName": "profile.serializer"}, "indexOps": {"shape": "Boolean", "documentation": "<p>If this flag is set to <code>TRUE</code>, the results include a detailed report of all index operations that took place during query execution and serialization.</p>", "locationName": "profile.indexOps"}}}, "ExecuteGremlinProfileQueryOutput": {"type": "structure", "members": {"output": {"shape": "ReportAsText", "documentation": "<p>A text blob containing the Gremlin Profile result. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/gremlin-profile-api.html\">Gremlin profile API in Neptune</a> for details.</p>"}}, "payload": "output"}, "ExecuteGremlinQueryInput": {"type": "structure", "required": ["grem<PERSON><PERSON><PERSON><PERSON>"], "members": {"gremlinQuery": {"shape": "String", "documentation": "<p>Using this API, you can run Gremlin queries in string format much as you can using the HTTP endpoint. The interface is compatible with whatever Gremlin version your DB cluster is using (see the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/access-graph-gremlin-client.html#best-practices-gremlin-java-latest\">Tinkerpop client section</a> to determine which Gremlin releases your engine version supports).</p>", "locationName": "gremlin"}, "serializer": {"shape": "String", "documentation": "<p>If non-null, the query results are returned in a serialized response message in the format specified by this parameter. See the <a href=\"https://tinkerpop.apache.org/docs/current/reference/#_graphson\">GraphSON</a> section in the TinkerPop documentation for a list of the formats that are currently supported.</p>", "location": "header", "locationName": "accept"}}}, "ExecuteGremlinQueryOutput": {"type": "structure", "members": {"requestId": {"shape": "String", "documentation": "<p>The unique identifier of the Gremlin query.</p>"}, "status": {"shape": "GremlinQueryStatusAttributes", "documentation": "<p>The status of the Gremlin query.</p>"}, "result": {"shape": "Document", "documentation": "<p>The Gremlin query output from the server.</p>"}, "meta": {"shape": "Document", "documentation": "<p><PERSON><PERSON><PERSON> about the Gremlin query.</p>"}}}, "ExecuteOpenCypherExplainQueryInput": {"type": "structure", "required": ["openCypherQuery", "explainMode"], "members": {"openCypherQuery": {"shape": "String", "documentation": "<p>The openCypher query string.</p>", "locationName": "query"}, "parameters": {"shape": "String", "documentation": "<p>The openCypher query parameters.</p>"}, "explainMode": {"shape": "OpenCypherExplainMode", "documentation": "<p>The openCypher <code>explain</code> mode. Can be one of: <code>static</code>, <code>dynamic</code>, or <code>details</code>.</p>", "locationName": "explain"}}}, "ExecuteOpenCypherExplainQueryOutput": {"type": "structure", "required": ["results"], "members": {"results": {"shape": "Blob", "documentation": "<p>A text blob containing the openCypher <code>explain</code> results.</p>"}}, "payload": "results"}, "ExecuteOpenCypherQueryInput": {"type": "structure", "required": ["openCypherQuery"], "members": {"openCypherQuery": {"shape": "String", "documentation": "<p>The openCypher query string to be executed.</p>", "locationName": "query"}, "parameters": {"shape": "String", "documentation": "<p>The openCypher query parameters for query execution. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/opencypher-parameterized-queries.html\">Examples of openCypher parameterized queries</a> for more information.</p>"}}}, "ExecuteOpenCypherQueryOutput": {"type": "structure", "required": ["results"], "members": {"results": {"shape": "Document", "documentation": "<p>The openCypherquery results.</p>"}}}, "ExpiredStreamException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request in question.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when a request attempts to access an stream that has expired.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "FailureByQueryException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request in question.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when a request fails.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "FastResetToken": {"type": "structure", "members": {"token": {"shape": "String", "documentation": "<p>A UUID generated by the database in the <code>initiateDatabaseReset</code> action, and then consumed by the <code>performDatabaseReset</code> to reset the database.</p>"}}, "documentation": "<p>A structure containing the fast reset token used to initiate a fast reset.</p>"}, "Format": {"type": "string", "enum": ["csv", "opencypher", "ntriples", "nquads", "rdfxml", "turtle"]}, "GetEngineStatusOutput": {"type": "structure", "members": {"status": {"shape": "String", "documentation": "<p>Set to <code>healthy</code> if the instance is not experiencing problems. If the instance is recovering from a crash or from being rebooted and there are active transactions running from the latest server shutdown, status is set to <code>recovery</code>.</p>"}, "startTime": {"shape": "String", "documentation": "<p>Set to the UTC time at which the current server process started.</p>"}, "dbEngineVersion": {"shape": "String", "documentation": "<p>Set to the Neptune engine version running on your DB cluster. If this engine version has been manually patched since it was released, the version number is prefixed by <code>Patch-</code>.</p>"}, "role": {"shape": "String", "documentation": "<p>Set to <code>reader</code> if the instance is a read-replica, or to <code>writer</code> if the instance is the primary instance.</p>"}, "dfeQueryEngine": {"shape": "String", "documentation": "<p>Set to <code>enabled</code> if the DFE engine is fully enabled, or to <code>viaQueryHint</code> (the default) if the DFE engine is only used with queries that have the <code>useDFE</code> query hint set to <code>true</code>.</p>"}, "gremlin": {"shape": "QueryLanguageVersion", "documentation": "<p>Contains information about the Gremlin query language available on your cluster. Specifically, it contains a version field that specifies the current TinkerPop version being used by the engine.</p>"}, "sparql": {"shape": "QueryLanguageVersion", "documentation": "<p>Contains information about the SPARQL query language available on your cluster. Specifically, it contains a version field that specifies the current SPARQL version being used by the engine.</p>"}, "opencypher": {"shape": "QueryLanguageVersion", "documentation": "<p>Contains information about the openCypher query language available on your cluster. Specifically, it contains a version field that specifies the current operCypher version being used by the engine.</p>"}, "labMode": {"shape": "StringValuedMap", "documentation": "<p>Contains Lab Mode settings being used by the engine.</p>"}, "rollingBackTrxCount": {"shape": "Integer", "documentation": "<p>If there are transactions being rolled back, this field is set to the number of such transactions. If there are none, the field doesn't appear at all.</p>"}, "rollingBackTrxEarliestStartTime": {"shape": "String", "documentation": "<p>Set to the start time of the earliest transaction being rolled back. If no transactions are being rolled back, the field doesn't appear at all.</p>"}, "features": {"shape": "DocumentValuedMap", "documentation": "<p>Contains status information about the features enabled on your DB cluster.</p>"}, "settings": {"shape": "StringValuedMap", "documentation": "<p>Contains information about the current settings on your DB cluster. For example, contains the current cluster query timeout setting (<code>clusterQueryTimeoutInMs</code>).</p>"}}}, "GetGremlinQueryStatusInput": {"type": "structure", "required": ["queryId"], "members": {"queryId": {"shape": "String", "documentation": "<p>The unique identifier that identifies the Gremlin query.</p>", "location": "uri", "locationName": "queryId"}}}, "GetGremlinQueryStatusOutput": {"type": "structure", "members": {"queryId": {"shape": "String", "documentation": "<p>The ID of the query for which status is being returned.</p>"}, "queryString": {"shape": "String", "documentation": "<p>The Gremlin query string.</p>"}, "queryEvalStats": {"shape": "QueryEvalStats", "documentation": "<p>The evaluation status of the Gremlin query.</p>"}}}, "GetLoaderJobStatusInput": {"type": "structure", "required": ["loadId"], "members": {"loadId": {"shape": "String", "documentation": "<p>The load ID of the load job to get the status of.</p>", "location": "uri", "locationName": "loadId"}, "details": {"shape": "Boolean", "documentation": "<p>Flag indicating whether or not to include details beyond the overall status (<code>TRUE</code> or <code>FALSE</code>; the default is <code>FALSE</code>).</p>", "location": "querystring", "locationName": "details"}, "errors": {"shape": "Boolean", "documentation": "<p>Flag indicating whether or not to include a list of errors encountered (<code>TRUE</code> or <code>FALSE</code>; the default is <code>FALSE</code>).</p> <p>The list of errors is paged. The <code>page</code> and <code>errorsPerPage</code> parameters allow you to page through all the errors.</p>", "location": "querystring", "locationName": "errors"}, "page": {"shape": "PositiveInteger", "documentation": "<p>The error page number (a positive integer; the default is <code>1</code>). Only valid when the <code>errors</code> parameter is set to <code>TRUE</code>.</p>", "location": "querystring", "locationName": "page"}, "errorsPerPage": {"shape": "PositiveInteger", "documentation": "<p>The number of errors returned in each page (a positive integer; the default is <code>10</code>). Only valid when the <code>errors</code> parameter set to <code>TRUE</code>.</p>", "location": "querystring", "locationName": "errorsPerPage"}}}, "GetLoaderJobStatusOutput": {"type": "structure", "required": ["status", "payload"], "members": {"status": {"shape": "String", "documentation": "<p>The HTTP response code for the request.</p>"}, "payload": {"shape": "Document", "documentation": "<p>Status information about the load job, in a layout that could look like this:</p>"}}}, "GetMLDataProcessingJobInput": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "String", "documentation": "<p>The unique identifier of the data-processing job to be retrieved.</p>", "location": "uri", "locationName": "id"}, "neptuneIamRoleArn": {"shape": "String", "documentation": "<p>The ARN of an IAM role that provides Neptune access to SageMaker and Amazon S3 resources. This must be listed in your DB cluster parameter group or an error will occur.</p>", "location": "querystring", "locationName": "neptuneIamRoleArn"}}}, "GetMLDataProcessingJobOutput": {"type": "structure", "members": {"status": {"shape": "String", "documentation": "<p>Status of the data processing job.</p>"}, "id": {"shape": "String", "documentation": "<p>The unique identifier of this data-processing job.</p>"}, "processingJob": {"shape": "MlResourceDefinition", "documentation": "<p>Definition of the data processing job.</p>"}}}, "GetMLEndpointInput": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "String", "documentation": "<p>The unique identifier of the inference endpoint.</p>", "location": "uri", "locationName": "id"}, "neptuneIamRoleArn": {"shape": "String", "documentation": "<p>The ARN of an IAM role that provides Neptune access to SageMaker and Amazon S3 resources. This must be listed in your DB cluster parameter group or an error will occur.</p>", "location": "querystring", "locationName": "neptuneIamRoleArn"}}}, "GetMLEndpointOutput": {"type": "structure", "members": {"status": {"shape": "String", "documentation": "<p>The status of the inference endpoint.</p>"}, "id": {"shape": "String", "documentation": "<p>The unique identifier of the inference endpoint.</p>"}, "endpoint": {"shape": "MlResourceDefinition", "documentation": "<p>The endpoint definition.</p>"}, "endpointConfig": {"shape": "MlConfigDefinition", "documentation": "<p>The endpoint configuration</p>"}}}, "GetMLModelTrainingJobInput": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "String", "documentation": "<p>The unique identifier of the model-training job to retrieve.</p>", "location": "uri", "locationName": "id"}, "neptuneIamRoleArn": {"shape": "String", "documentation": "<p>The ARN of an IAM role that provides Neptune access to SageMaker and Amazon S3 resources. This must be listed in your DB cluster parameter group or an error will occur.</p>", "location": "querystring", "locationName": "neptuneIamRoleArn"}}}, "GetMLModelTrainingJobOutput": {"type": "structure", "members": {"status": {"shape": "String", "documentation": "<p>The status of the model training job.</p>"}, "id": {"shape": "String", "documentation": "<p>The unique identifier of this model-training job.</p>"}, "processingJob": {"shape": "MlResourceDefinition", "documentation": "<p>The data processing job.</p>"}, "hpoJob": {"shape": "MlResourceDefinition", "documentation": "<p>The HPO job.</p>"}, "modelTransformJob": {"shape": "MlResourceDefinition", "documentation": "<p>The model transform job.</p>"}, "mlModels": {"shape": "MlModels", "documentation": "<p>A list of the configurations of the ML models being used.</p>"}}}, "GetMLModelTransformJobInput": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "String", "documentation": "<p>The unique identifier of the model-transform job to be reetrieved.</p>", "location": "uri", "locationName": "id"}, "neptuneIamRoleArn": {"shape": "String", "documentation": "<p>The ARN of an IAM role that provides Neptune access to SageMaker and Amazon S3 resources. This must be listed in your DB cluster parameter group or an error will occur.</p>", "location": "querystring", "locationName": "neptuneIamRoleArn"}}}, "GetMLModelTransformJobOutput": {"type": "structure", "members": {"status": {"shape": "String", "documentation": "<p>The status of the model-transform job.</p>"}, "id": {"shape": "String", "documentation": "<p>The unique identifier of the model-transform job to be retrieved.</p>"}, "baseProcessingJob": {"shape": "MlResourceDefinition", "documentation": "<p>The base data processing job.</p>"}, "remoteModelTransformJob": {"shape": "MlResourceDefinition", "documentation": "<p>The remote model transform job.</p>"}, "models": {"shape": "Models", "documentation": "<p>A list of the configuration information for the models being used.</p>"}}}, "GetOpenCypherQueryStatusInput": {"type": "structure", "required": ["queryId"], "members": {"queryId": {"shape": "String", "documentation": "<p>The unique ID of the openCypher query for which to retrieve the query status.</p>", "location": "uri", "locationName": "queryId"}}}, "GetOpenCypherQueryStatusOutput": {"type": "structure", "members": {"queryId": {"shape": "String", "documentation": "<p>The unique ID of the query for which status is being returned.</p>"}, "queryString": {"shape": "String", "documentation": "<p>The openCypher query string.</p>"}, "queryEvalStats": {"shape": "QueryEvalStats", "documentation": "<p>The openCypher query evaluation status.</p>"}}}, "GetPropertygraphStatisticsOutput": {"type": "structure", "required": ["status", "payload"], "members": {"status": {"shape": "String", "documentation": "<p>The HTTP return code of the request. If the request succeeded, the code is 200. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/neptune-dfe-statistics.html#neptune-dfe-statistics-errors\">Common error codes for DFE statistics request</a> for a list of common errors.</p>"}, "payload": {"shape": "Statistics", "documentation": "<p>Statistics for property-graph data.</p>"}}}, "GetPropertygraphStreamInput": {"type": "structure", "members": {"limit": {"shape": "GetPropertygraphStreamInputLimitLong", "documentation": "<p>Specifies the maximum number of records to return. There is also a size limit of 10 MB on the response that can't be modified and that takes precedence over the number of records specified in the <code>limit</code> parameter. The response does include a threshold-breaching record if the 10 MB limit was reached.</p> <p>The range for <code>limit</code> is 1 to 100,000, with a default of 10.</p>", "location": "querystring", "locationName": "limit"}, "iteratorType": {"shape": "IteratorType", "documentation": "<p>Can be one of:</p> <ul> <li> <p> <code>AT_SEQUENCE_NUMBER</code>   –   Indicates that reading should start from the event sequence number specified jointly by the <code>commitNum</code> and <code>opNum</code> parameters.</p> </li> <li> <p> <code>AFTER_SEQUENCE_NUMBER</code>   –   Indicates that reading should start right after the event sequence number specified jointly by the <code>commitNum</code> and <code>opNum</code> parameters.</p> </li> <li> <p> <code>TRIM_HORIZON</code>   –   Indicates that reading should start at the last untrimmed record in the system, which is the oldest unexpired (not yet deleted) record in the change-log stream.</p> </li> <li> <p> <code>LATEST</code>   –   Indicates that reading should start at the most recent record in the system, which is the latest unexpired (not yet deleted) record in the change-log stream.</p> </li> </ul>", "location": "querystring", "locationName": "iteratorType"}, "commitNum": {"shape": "<PERSON>", "documentation": "<p>The commit number of the starting record to read from the change-log stream. This parameter is required when <code>iteratorType</code> is<code>AT_SEQUENCE_NUMBER</code> or <code>AFTER_SEQUENCE_NUMBER</code>, and ignored when <code>iteratorType</code> is <code>TRIM_HORIZON</code> or <code>LATEST</code>.</p>", "location": "querystring", "locationName": "commitNum"}, "opNum": {"shape": "<PERSON>", "documentation": "<p>The operation sequence number within the specified commit to start reading from in the change-log stream data. The default is <code>1</code>.</p>", "location": "querystring", "locationName": "opNum"}, "encoding": {"shape": "Encoding", "documentation": "<p>If set to TRUE, <PERSON> compresses the response using gzip encoding.</p>", "location": "header", "locationName": "Accept-Encoding"}}}, "GetPropertygraphStreamInputLimitLong": {"type": "long", "box": true, "max": 100000, "min": 1}, "GetPropertygraphStreamOutput": {"type": "structure", "required": ["lastEventId", "lastTrxTimestampInMillis", "format", "records", "totalRecords"], "members": {"lastEventId": {"shape": "StringValuedMap", "documentation": "<p>Sequence identifier of the last change in the stream response.</p> <p>An event ID is composed of two fields: a <code>commitNum</code>, which identifies a transaction that changed the graph, and an <code>opNum</code>, which identifies a specific operation within that transaction:</p>"}, "lastTrxTimestampInMillis": {"shape": "<PERSON>", "documentation": "<p>The time at which the commit for the transaction was requested, in milliseconds from the Unix epoch.</p>", "locationName": "lastTrxTimestamp"}, "format": {"shape": "String", "documentation": "<p>Serialization format for the change records being returned. Currently, the only supported value is <code>PG_JSON</code>.</p>"}, "records": {"shape": "PropertygraphRecordsList", "documentation": "<p>An array of serialized change-log stream records included in the response.</p>"}, "totalRecords": {"shape": "Integer", "documentation": "<p>The total number of records in the response.</p>"}}}, "GetPropertygraphSummaryInput": {"type": "structure", "members": {"mode": {"shape": "GraphSummaryType", "documentation": "<p>Mode can take one of two values: <code>BASIC</code> (the default), and <code>DETAILED</code>.</p>", "location": "querystring", "locationName": "mode"}}}, "GetPropertygraphSummaryOutput": {"type": "structure", "members": {"statusCode": {"shape": "Integer", "documentation": "<p>The HTTP return code of the request. If the request succeeded, the code is 200.</p>", "location": "statusCode"}, "payload": {"shape": "PropertygraphSummaryValueMap", "documentation": "<p>Payload containing the property graph summary response.</p>"}}}, "GetRDFGraphSummaryInput": {"type": "structure", "members": {"mode": {"shape": "GraphSummaryType", "documentation": "<p>Mode can take one of two values: <code>BASIC</code> (the default), and <code>DETAILED</code>.</p>", "location": "querystring", "locationName": "mode"}}}, "GetRDFGraphSummaryOutput": {"type": "structure", "members": {"statusCode": {"shape": "Integer", "documentation": "<p>The HTTP return code of the request. If the request succeeded, the code is 200.</p>", "location": "statusCode"}, "payload": {"shape": "RDFGraphSummaryValueMap", "documentation": "<p>Payload for an RDF graph summary response</p>"}}}, "GetSparqlStatisticsOutput": {"type": "structure", "required": ["status", "payload"], "members": {"status": {"shape": "String", "documentation": "<p>The HTTP return code of the request. If the request succeeded, the code is 200. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/neptune-dfe-statistics.html#neptune-dfe-statistics-errors\">Common error codes for DFE statistics request</a> for a list of common errors.</p> <p>When invoking this operation in a Neptune cluster that has IAM authentication enabled, the IAM user or role making the request must have a policy attached that allows the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/iam-dp-actions.html#getstatisticsstatus\">neptune-db:GetStatisticsStatus</a> IAM action in that cluster.</p>"}, "payload": {"shape": "Statistics", "documentation": "<p>Statistics for RDF data.</p>"}}}, "GetSparqlStreamInput": {"type": "structure", "members": {"limit": {"shape": "GetSparqlStreamInputLimitLong", "documentation": "<p>Specifies the maximum number of records to return. There is also a size limit of 10 MB on the response that can't be modified and that takes precedence over the number of records specified in the <code>limit</code> parameter. The response does include a threshold-breaching record if the 10 MB limit was reached.</p> <p>The range for <code>limit</code> is 1 to 100,000, with a default of 10.</p>", "location": "querystring", "locationName": "limit"}, "iteratorType": {"shape": "IteratorType", "documentation": "<p>Can be one of:</p> <ul> <li> <p> <code>AT_SEQUENCE_NUMBER</code>   –   Indicates that reading should start from the event sequence number specified jointly by the <code>commitNum</code> and <code>opNum</code> parameters.</p> </li> <li> <p> <code>AFTER_SEQUENCE_NUMBER</code>   –   Indicates that reading should start right after the event sequence number specified jointly by the <code>commitNum</code> and <code>opNum</code> parameters.</p> </li> <li> <p> <code>TRIM_HORIZON</code>   –   Indicates that reading should start at the last untrimmed record in the system, which is the oldest unexpired (not yet deleted) record in the change-log stream.</p> </li> <li> <p> <code>LATEST</code>   –   Indicates that reading should start at the most recent record in the system, which is the latest unexpired (not yet deleted) record in the change-log stream.</p> </li> </ul>", "location": "querystring", "locationName": "iteratorType"}, "commitNum": {"shape": "<PERSON>", "documentation": "<p>The commit number of the starting record to read from the change-log stream. This parameter is required when <code>iteratorType</code> is<code>AT_SEQUENCE_NUMBER</code> or <code>AFTER_SEQUENCE_NUMBER</code>, and ignored when <code>iteratorType</code> is <code>TRIM_HORIZON</code> or <code>LATEST</code>.</p>", "location": "querystring", "locationName": "commitNum"}, "opNum": {"shape": "<PERSON>", "documentation": "<p>The operation sequence number within the specified commit to start reading from in the change-log stream data. The default is <code>1</code>.</p>", "location": "querystring", "locationName": "opNum"}, "encoding": {"shape": "Encoding", "documentation": "<p>If set to TRUE, <PERSON> compresses the response using gzip encoding.</p>", "location": "header", "locationName": "Accept-Encoding"}}}, "GetSparqlStreamInputLimitLong": {"type": "long", "box": true, "max": 100000, "min": 1}, "GetSparqlStreamOutput": {"type": "structure", "required": ["lastEventId", "lastTrxTimestampInMillis", "format", "records", "totalRecords"], "members": {"lastEventId": {"shape": "StringValuedMap", "documentation": "<p>Sequence identifier of the last change in the stream response.</p> <p>An event ID is composed of two fields: a <code>commitNum</code>, which identifies a transaction that changed the graph, and an <code>opNum</code>, which identifies a specific operation within that transaction:</p>"}, "lastTrxTimestampInMillis": {"shape": "<PERSON>", "documentation": "<p>The time at which the commit for the transaction was requested, in milliseconds from the Unix epoch.</p>", "locationName": "lastTrxTimestamp"}, "format": {"shape": "String", "documentation": "<p>Serialization format for the change records being returned. Currently, the only supported value is <code>NQUADS</code>.</p>"}, "records": {"shape": "SparqlRecordsList", "documentation": "<p>An array of serialized change-log stream records included in the response.</p>"}, "totalRecords": {"shape": "Integer", "documentation": "<p>The total number of records in the response.</p>"}}}, "GraphSummaryType": {"type": "string", "enum": ["basic", "detailed"]}, "GremlinQueries": {"type": "list", "member": {"shape": "GremlinQueryStatus"}}, "GremlinQueryStatus": {"type": "structure", "members": {"queryId": {"shape": "String", "documentation": "<p>The ID of the Gremlin query.</p>"}, "queryString": {"shape": "String", "documentation": "<p>The query string of the Gremlin query.</p>"}, "queryEvalStats": {"shape": "QueryEvalStats", "documentation": "<p>The query statistics of the Gremlin query.</p>"}}, "documentation": "<p>Captures the status of a Gremlin query (see the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/gremlin-api-status.html\">Gremlin query status API</a> page).</p>"}, "GremlinQueryStatusAttributes": {"type": "structure", "members": {"message": {"shape": "String", "documentation": "<p>The status message.</p>"}, "code": {"shape": "Integer", "documentation": "<p>The HTTP response code returned fro the Gremlin query request..</p>"}, "attributes": {"shape": "Document", "documentation": "<p>Attributes of the Gremlin query status.</p>"}}, "documentation": "<p>Contains status components of a Gremlin query.</p>"}, "IllegalArgumentException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request in question.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when an argument in a request is not supported.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "Integer": {"type": "integer", "box": true}, "InternalFailureException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request in question.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when the processing of the request failed unexpectedly.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "InvalidArgumentException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request in question.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when an argument in a request has an invalid value.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "InvalidNumericDataException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request in question.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when invalid numerical data is encountered when servicing a request.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "InvalidParameterException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request that includes an invalid parameter.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when a parameter value is not valid.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "IteratorType": {"type": "string", "enum": ["AT_SEQUENCE_NUMBER", "AFTER_SEQUENCE_NUMBER", "TRIM_HORIZON", "LATEST"]}, "ListGremlinQueriesInput": {"type": "structure", "members": {"includeWaiting": {"shape": "Boolean", "documentation": "<p>If set to <code>TRUE</code>, the list returned includes waiting queries. The default is <code>FALSE</code>;</p>", "location": "querystring", "locationName": "includeWaiting"}}}, "ListGremlinQueriesOutput": {"type": "structure", "members": {"acceptedQueryCount": {"shape": "Integer", "documentation": "<p>The number of queries that have been accepted but not yet completed, including queries in the queue.</p>"}, "runningQueryCount": {"shape": "Integer", "documentation": "<p>The number of Gremlin queries currently running.</p>"}, "queries": {"shape": "GremlinQueries", "documentation": "<p>A list of the current queries.</p>"}}}, "ListLoaderJobsInput": {"type": "structure", "members": {"limit": {"shape": "ListLoaderJobsInputLimitInteger", "documentation": "<p>The number of load IDs to list. Must be a positive integer greater than zero and not more than <code>100</code> (which is the default).</p>", "location": "querystring", "locationName": "limit"}, "includeQueuedLoads": {"shape": "Boolean", "documentation": "<p>An optional parameter that can be used to exclude the load IDs of queued load requests when requesting a list of load IDs by setting the parameter to <code>FALSE</code>. The default value is <code>TRUE</code>.</p>", "location": "querystring", "locationName": "includeQueuedLoads"}}}, "ListLoaderJobsInputLimitInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListLoaderJobsOutput": {"type": "structure", "required": ["status", "payload"], "members": {"status": {"shape": "String", "documentation": "<p>Returns the status of the job list request.</p>"}, "payload": {"shape": "LoaderIdResult", "documentation": "<p>The requested list of job IDs.</p>"}}}, "ListMLDataProcessingJobsInput": {"type": "structure", "members": {"maxItems": {"shape": "ListMLDataProcessingJobsInputMaxItemsInteger", "documentation": "<p>The maximum number of items to return (from 1 to 1024; the default is 10).</p>", "location": "querystring", "locationName": "maxItems"}, "neptuneIamRoleArn": {"shape": "String", "documentation": "<p>The ARN of an IAM role that provides Neptune access to SageMaker and Amazon S3 resources. This must be listed in your DB cluster parameter group or an error will occur.</p>", "location": "querystring", "locationName": "neptuneIamRoleArn"}}}, "ListMLDataProcessingJobsInputMaxItemsInteger": {"type": "integer", "box": true, "max": 1024, "min": 1}, "ListMLDataProcessingJobsOutput": {"type": "structure", "members": {"ids": {"shape": "StringList", "documentation": "<p>A page listing data processing job IDs.</p>"}}}, "ListMLEndpointsInput": {"type": "structure", "members": {"maxItems": {"shape": "ListMLEndpointsInputMaxItemsInteger", "documentation": "<p>The maximum number of items to return (from 1 to 1024; the default is 10.</p>", "location": "querystring", "locationName": "maxItems"}, "neptuneIamRoleArn": {"shape": "String", "documentation": "<p>The ARN of an IAM role that provides Neptune access to SageMaker and Amazon S3 resources. This must be listed in your DB cluster parameter group or an error will occur.</p>", "location": "querystring", "locationName": "neptuneIamRoleArn"}}}, "ListMLEndpointsInputMaxItemsInteger": {"type": "integer", "box": true, "max": 1024, "min": 1}, "ListMLEndpointsOutput": {"type": "structure", "members": {"ids": {"shape": "StringList", "documentation": "<p>A page from the list of inference endpoint IDs.</p>"}}}, "ListMLModelTrainingJobsInput": {"type": "structure", "members": {"maxItems": {"shape": "ListMLModelTrainingJobsInputMaxItemsInteger", "documentation": "<p>The maximum number of items to return (from 1 to 1024; the default is 10).</p>", "location": "querystring", "locationName": "maxItems"}, "neptuneIamRoleArn": {"shape": "String", "documentation": "<p>The ARN of an IAM role that provides Neptune access to SageMaker and Amazon S3 resources. This must be listed in your DB cluster parameter group or an error will occur.</p>", "location": "querystring", "locationName": "neptuneIamRoleArn"}}}, "ListMLModelTrainingJobsInputMaxItemsInteger": {"type": "integer", "box": true, "max": 1024, "min": 1}, "ListMLModelTrainingJobsOutput": {"type": "structure", "members": {"ids": {"shape": "StringList", "documentation": "<p>A page of the list of model training job IDs.</p>"}}}, "ListMLModelTransformJobsInput": {"type": "structure", "members": {"maxItems": {"shape": "ListMLModelTransformJobsInputMaxItemsInteger", "documentation": "<p>The maximum number of items to return (from 1 to 1024; the default is 10).</p>", "location": "querystring", "locationName": "maxItems"}, "neptuneIamRoleArn": {"shape": "String", "documentation": "<p>The ARN of an IAM role that provides Neptune access to SageMaker and Amazon S3 resources. This must be listed in your DB cluster parameter group or an error will occur.</p>", "location": "querystring", "locationName": "neptuneIamRoleArn"}}}, "ListMLModelTransformJobsInputMaxItemsInteger": {"type": "integer", "box": true, "max": 1024, "min": 1}, "ListMLModelTransformJobsOutput": {"type": "structure", "members": {"ids": {"shape": "StringList", "documentation": "<p>A page from the list of model transform IDs.</p>"}}}, "ListOpenCypherQueriesInput": {"type": "structure", "members": {"includeWaiting": {"shape": "Boolean", "documentation": "<p> When set to <code>TRUE</code> and other parameters are not present, causes status information to be returned for waiting queries as well as for running queries.</p>", "location": "querystring", "locationName": "includeWaiting"}}}, "ListOpenCypherQueriesOutput": {"type": "structure", "members": {"acceptedQueryCount": {"shape": "Integer", "documentation": "<p>The number of queries that have been accepted but not yet completed, including queries in the queue.</p>"}, "runningQueryCount": {"shape": "Integer", "documentation": "<p>The number of currently running openCypher queries.</p>"}, "queries": {"shape": "OpenCypherQueries", "documentation": "<p>A list of current openCypher queries.</p>"}}}, "LoadUrlAccessDeniedException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request in question.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when access is denied to a specified load URL.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "LoaderIdResult": {"type": "structure", "members": {"loadIds": {"shape": "StringList", "documentation": "<p>A list of load IDs.</p>"}}, "documentation": "<p>Contains a list of load IDs.</p>"}, "Long": {"type": "long", "box": true}, "LongValuedMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "<PERSON>"}}, "LongValuedMapList": {"type": "list", "member": {"shape": "LongValuedMap"}}, "MLResourceNotFoundException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request in question.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when a specified machine-learning resource could not be found.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "MalformedQueryException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the malformed query request.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when a query is submitted that is syntactically incorrect or does not pass additional validation.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "ManagePropertygraphStatisticsInput": {"type": "structure", "members": {"mode": {"shape": "StatisticsAutoGenerationMode", "documentation": "<p>The statistics generation mode. One of: <code>DISABLE_AUTOCOMPUTE</code>, <code>ENABLE_AUTOCOMPUTE</code>, or <code>REFRESH</code>, the last of which manually triggers DFE statistics generation.</p>"}}}, "ManagePropertygraphStatisticsOutput": {"type": "structure", "required": ["status"], "members": {"status": {"shape": "String", "documentation": "<p>The HTTP return code of the request. If the request succeeded, the code is 200.</p>"}, "payload": {"shape": "RefreshStatisticsIdMap", "documentation": "<p>This is only returned for refresh mode.</p>"}}}, "ManageSparqlStatisticsInput": {"type": "structure", "members": {"mode": {"shape": "StatisticsAutoGenerationMode", "documentation": "<p>The statistics generation mode. One of: <code>DISABLE_AUTOCOMPUTE</code>, <code>ENABLE_AUTOCOMPUTE</code>, or <code>REFRESH</code>, the last of which manually triggers DFE statistics generation.</p>"}}}, "ManageSparqlStatisticsOutput": {"type": "structure", "required": ["status"], "members": {"status": {"shape": "String", "documentation": "<p>The HTTP return code of the request. If the request succeeded, the code is 200.</p>"}, "payload": {"shape": "RefreshStatisticsIdMap", "documentation": "<p>This is only returned for refresh mode.</p>"}}}, "MemoryLimitExceededException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request that failed.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when a request fails because of insufficient memory resources. The request can be retried.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "MethodNotAllowedException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request in question.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when the HTTP method used by a request is not supported by the endpoint being used.</p>", "error": {"httpStatusCode": 405, "senderFault": true}, "exception": true}, "MissingParameterException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request in which the parameter is missing.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when a required parameter is missing.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "MlConfigDefinition": {"type": "structure", "members": {"name": {"shape": "String", "documentation": "<p>The configuration name.</p>"}, "arn": {"shape": "String", "documentation": "<p>The ARN for the configuration.</p>"}}, "documentation": "<p>Contains a Neptune ML configuration.</p>"}, "MlModels": {"type": "list", "member": {"shape": "MlConfigDefinition"}}, "MlResourceDefinition": {"type": "structure", "members": {"name": {"shape": "String", "documentation": "<p>The resource name.</p>"}, "arn": {"shape": "String", "documentation": "<p>The resource ARN.</p>"}, "status": {"shape": "String", "documentation": "<p>The resource status.</p>"}, "outputLocation": {"shape": "String", "documentation": "<p>The output location.</p>"}, "failureReason": {"shape": "String", "documentation": "<p>The failure reason, in case of a failure.</p>"}, "cloudwatchLogUrl": {"shape": "String", "documentation": "<p>The CloudWatch log URL for the resource.</p>"}}, "documentation": "<p>Defines a Neptune ML resource.</p>"}, "Mode": {"type": "string", "enum": ["RESUME", "NEW", "AUTO"]}, "Models": {"type": "list", "member": {"shape": "MlConfigDefinition"}}, "NodeLabels": {"type": "list", "member": {"shape": "String"}}, "NodeProperties": {"type": "list", "member": {"shape": "String"}}, "NodeStructure": {"type": "structure", "members": {"count": {"shape": "<PERSON>", "documentation": "<p>Number of nodes that have this specific structure.</p>"}, "nodeProperties": {"shape": "NodeProperties", "documentation": "<p>A list of the node properties present in this specific structure.</p>"}, "distinctOutgoingEdgeLabels": {"shape": "OutgoingEdgeLabels", "documentation": "<p>A list of distinct outgoing edge labels present in this specific structure.</p>"}}, "documentation": "<p>A node structure.</p>"}, "NodeStructures": {"type": "list", "member": {"shape": "NodeStructure"}}, "OpenCypherExplainMode": {"type": "string", "enum": ["static", "dynamic", "details"]}, "OpenCypherQueries": {"type": "list", "member": {"shape": "GremlinQueryStatus"}}, "OutgoingEdgeLabels": {"type": "list", "member": {"shape": "String"}}, "Parallelism": {"type": "string", "enum": ["LOW", "MEDIUM", "HIGH", "OVERSUBSCRIBE"]}, "ParsingException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request in question.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when a parsing issue is encountered.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "PositiveInteger": {"type": "integer", "box": true, "min": 1}, "PreconditionsFailedException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request in question.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when a precondition for processing a request is not satisfied.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "Predicates": {"type": "list", "member": {"shape": "String"}}, "PropertygraphData": {"type": "structure", "required": ["id", "type", "key", "value"], "members": {"id": {"shape": "String", "documentation": "<p>The ID of the Gremlin or openCypher element.</p>"}, "type": {"shape": "String", "documentation": "<p>The type of this Gremlin or openCypher element. Must be one of:</p> <ul> <li> <p> <b> <code>v1</code> </b>   -   Vertex label for Gremlin, or node label for openCypher.</p> </li> <li> <p> <b> <code>vp</code> </b>   -   Vertex properties for Gremlin, or node properties for openCypher.</p> </li> <li> <p> <b> <code>e</code> </b>   -   Edge and edge label for Gremlin, or relationship and relationship type for openCypher.</p> </li> <li> <p> <b> <code>ep</code> </b>   -   Edge properties for Gremlin, or relationship properties for openCypher.</p> </li> </ul>"}, "key": {"shape": "String", "documentation": "<p>The property name. For element labels, this is <code>label</code>.</p>"}, "value": {"shape": "Document", "documentation": "<p>This is a JSON object that contains a value field for the value itself, and a datatype field for the JSON data type of that value:</p>"}, "from": {"shape": "String", "documentation": "<p>If this is an edge (type = <code>e</code>), the ID of the corresponding <code>from</code> vertex or source node.</p>"}, "to": {"shape": "String", "documentation": "<p>If this is an edge (type = <code>e</code>), the ID of the corresponding <code>to</code> vertex or target node.</p>"}}, "documentation": "<p>A Gremlin or openCypher change record.</p>"}, "PropertygraphRecord": {"type": "structure", "required": ["commitTimestampInMillis", "eventId", "data", "op"], "members": {"commitTimestampInMillis": {"shape": "<PERSON>", "documentation": "<p>The time at which the commit for the transaction was requested, in milliseconds from the Unix epoch.</p>", "locationName": "commitTimestamp"}, "eventId": {"shape": "StringValuedMap", "documentation": "<p>The sequence identifier of the stream change record.</p>"}, "data": {"shape": "PropertygraphData", "documentation": "<p>The serialized Gremlin or openCypher change record.</p>"}, "op": {"shape": "String", "documentation": "<p>The operation that created the change.</p>"}, "isLastOp": {"shape": "Boolean", "documentation": "<p>Only present if this operation is the last one in its transaction. If present, it is set to true. It is useful for ensuring that an entire transaction is consumed.</p>"}}, "documentation": "<p>Structure of a property graph record.</p>"}, "PropertygraphRecordsList": {"type": "list", "member": {"shape": "PropertygraphRecord"}}, "PropertygraphSummary": {"type": "structure", "members": {"numNodes": {"shape": "<PERSON>", "documentation": "<p>The number of nodes in the graph.</p>"}, "numEdges": {"shape": "<PERSON>", "documentation": "<p>The number of edges in the graph.</p>"}, "numNodeLabels": {"shape": "<PERSON>", "documentation": "<p>The number of distinct node labels in the graph.</p>"}, "numEdgeLabels": {"shape": "<PERSON>", "documentation": "<p>The number of distinct edge labels in the graph.</p>"}, "nodeLabels": {"shape": "NodeLabels", "documentation": "<p>A list of the distinct node labels in the graph.</p>"}, "edgeLabels": {"shape": "EdgeLabels", "documentation": "<p>A list of the distinct edge labels in the graph.</p>"}, "numNodeProperties": {"shape": "<PERSON>", "documentation": "<p>A list of the distinct node properties in the graph, along with the count of nodes where each property is used.</p>"}, "numEdgeProperties": {"shape": "<PERSON>", "documentation": "<p>The number of distinct edge properties in the graph.</p>"}, "nodeProperties": {"shape": "LongValuedMapList", "documentation": "<p>The number of distinct node properties in the graph.</p>"}, "edgeProperties": {"shape": "LongValuedMapList", "documentation": "<p>A list of the distinct edge properties in the graph, along with the count of edges where each property is used.</p>"}, "totalNodePropertyValues": {"shape": "<PERSON>", "documentation": "<p>The total number of usages of all node properties.</p>"}, "totalEdgePropertyValues": {"shape": "<PERSON>", "documentation": "<p>The total number of usages of all edge properties.</p>"}, "nodeStructures": {"shape": "NodeStructures", "documentation": "<p>This field is only present when the requested mode is <code>DETAILED</code>. It contains a list of node structures.</p>"}, "edgeStructures": {"shape": "EdgeStructures", "documentation": "<p>This field is only present when the requested mode is <code>DETAILED</code>. It contains a list of edge structures.</p>"}}, "documentation": "<p>The graph summary API returns a read-only list of node and edge labels and property keys, along with counts of nodes, edges, and properties. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/neptune-graph-summary.html#neptune-graph-summary-pg-response\">Graph summary response for a property graph (PG)</a>.</p>"}, "PropertygraphSummaryValueMap": {"type": "structure", "members": {"version": {"shape": "String", "documentation": "<p>The version of this graph summary response.</p>"}, "lastStatisticsComputationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The timestamp, in ISO 8601 format, of the time at which Neptune last computed statistics.</p>"}, "graphSummary": {"shape": "PropertygraphSummary", "documentation": "<p>The graph summary.</p>"}}, "documentation": "<p>Payload for the property graph summary response.</p>"}, "QueryEvalStats": {"type": "structure", "members": {"waited": {"shape": "Integer", "documentation": "<p>Indicates how long the query waited, in milliseconds.</p>"}, "elapsed": {"shape": "Integer", "documentation": "<p>The number of milliseconds the query has been running so far.</p>"}, "cancelled": {"shape": "Boolean", "documentation": "<p>Set to <code>TRUE</code> if the query was cancelled, or FALSE otherwise.</p>"}, "subqueries": {"shape": "Document", "documentation": "<p>The number of subqueries in this query.</p>"}}, "documentation": "<p>Structure to capture query statistics such as how many queries are running, accepted or waiting and their details.</p>"}, "QueryLanguageVersion": {"type": "structure", "required": ["version"], "members": {"version": {"shape": "String", "documentation": "<p>The version of the query language.</p>"}}, "documentation": "<p>Structure for expressing the query language version.</p>"}, "QueryLimitExceededException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request which exceeded the limit.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when the number of active queries exceeds what the server can process. The query in question can be retried when the system is less busy.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "QueryLimitException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request that exceeded the limit.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when the size of a query exceeds the system limit.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "QueryTooLargeException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request that is too large.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when the body of a query is too large.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "RDFGraphSummary": {"type": "structure", "members": {"numDistinctSubjects": {"shape": "<PERSON>", "documentation": "<p>The number of distinct subjects in the graph.</p>"}, "numDistinctPredicates": {"shape": "<PERSON>", "documentation": "<p>The number of distinct predicates in the graph.</p>"}, "numQuads": {"shape": "<PERSON>", "documentation": "<p>The number of quads in the graph.</p>"}, "numClasses": {"shape": "<PERSON>", "documentation": "<p>The number of classes in the graph.</p>"}, "classes": {"shape": "Classes", "documentation": "<p>A list of the classes in the graph.</p>"}, "predicates": {"shape": "LongValuedMapList", "documentation": "<p>\"A list of predicates in the graph, along with the predicate counts.</p>"}, "subjectStructures": {"shape": "SubjectStructures", "documentation": "<p>This field is only present when the request mode is <code>DETAILED</code>. It contains a list of subject structures.</p>"}}, "documentation": "<p>The RDF graph summary API returns a read-only list of classes and predicate keys, along with counts of quads, subjects, and predicates.</p>"}, "RDFGraphSummaryValueMap": {"type": "structure", "members": {"version": {"shape": "String", "documentation": "<p>The version of this graph summary response.</p>"}, "lastStatisticsComputationTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The timestamp, in ISO 8601 format, of the time at which Neptune last computed statistics.</p>"}, "graphSummary": {"shape": "RDFGraphSummary", "documentation": "<p>The graph summary of an RDF graph. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/neptune-graph-summary.html#neptune-graph-summary-rdf-response\">Graph summary response for an RDF graph</a>.</p>"}}, "documentation": "<p>Payload for an RDF graph summary response.</p>"}, "ReadOnlyViolationException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request in which the parameter is missing.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when a request attempts to write to a read-only resource.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "RefreshStatisticsIdMap": {"type": "structure", "members": {"statisticsId": {"shape": "String", "documentation": "<p>The ID of the statistics generation run that is currently occurring.</p>"}}, "documentation": "<p>Statistics for <code>REFRESH</code> mode.</p>"}, "ReportAsText": {"type": "blob"}, "S3BucketRegion": {"type": "string", "enum": ["us-east-1", "us-east-2", "us-west-1", "us-west-2", "ca-central-1", "sa-east-1", "eu-north-1", "eu-west-1", "eu-west-2", "eu-west-3", "eu-central-1", "me-south-1", "af-south-1", "ap-east-1", "ap-northeast-1", "ap-northeast-2", "ap-southeast-1", "ap-southeast-2", "ap-south-1", "cn-north-1", "cn-northwest-1", "us-gov-west-1", "us-gov-east-1"]}, "S3Exception": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request in question.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when there is a problem accessing Amazon S3.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true, "retryable": {"throttling": false}}, "ServerShutdownException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request in question.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when the server shuts down while processing a request.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "SparqlData": {"type": "structure", "required": ["stmt"], "members": {"stmt": {"shape": "String", "documentation": "<p>Holds an <a href=\"https://www.w3.org/TR/n-quads/\">N-QUADS</a> statement expressing the changed quad.</p>"}}, "documentation": "<p>Neptune logs are converted to SPARQL quads in the graph using the Resource Description Framework (RDF) <a href=\"https://www.w3.org/TR/n-quads/\">N-QUADS</a> language defined in the W3C RDF 1.1 N-Quads specification</p>"}, "SparqlRecord": {"type": "structure", "required": ["commitTimestampInMillis", "eventId", "data", "op"], "members": {"commitTimestampInMillis": {"shape": "<PERSON>", "documentation": "<p>The time at which the commit for the transaction was requested, in milliseconds from the Unix epoch.</p>", "locationName": "commitTimestamp"}, "eventId": {"shape": "StringValuedMap", "documentation": "<p>The sequence identifier of the stream change record.</p>"}, "data": {"shape": "SparqlData", "documentation": "<p>The serialized SPARQL change record. The serialization formats of each record are described in more detail in <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/streams-change-formats.html\">Serialization Formats in Neptune Streams</a>.</p>"}, "op": {"shape": "String", "documentation": "<p>The operation that created the change.</p>"}, "isLastOp": {"shape": "Boolean", "documentation": "<p>Only present if this operation is the last one in its transaction. If present, it is set to true. It is useful for ensuring that an entire transaction is consumed.</p>"}}, "documentation": "<p>A serialized SPARQL stream record capturing a change-log entry for the RDF graph.</p>"}, "SparqlRecordsList": {"type": "list", "member": {"shape": "SparqlRecord"}}, "StartLoaderJobInput": {"type": "structure", "required": ["source", "format", "s3BucketRegion", "iamRoleArn"], "members": {"source": {"shape": "String", "documentation": "<p>The <code>source</code> parameter accepts an S3 URI that identifies a single file, multiple files, a folder, or multiple folders. <PERSON> loads every data file in any folder that is specified.</p> <p>The URI can be in any of the following formats.</p> <ul> <li> <p> <code>s3://(bucket_name)/(object-key-name)</code> </p> </li> <li> <p> <code>https://s3.amazonaws.com/(bucket_name)/(object-key-name)</code> </p> </li> <li> <p> <code>https://s3.us-east-1.amazonaws.com/(bucket_name)/(object-key-name)</code> </p> </li> </ul> <p>The <code>object-key-name</code> element of the URI is equivalent to the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_ListObjects.html#API_ListObjects_RequestParameters\">prefix</a> parameter in an S3 <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_ListObjects.html\">ListObjects</a> API call. It identifies all the objects in the specified S3 bucket whose names begin with that prefix. That can be a single file or folder, or multiple files and/or folders.</p> <p>The specified folder or folders can contain multiple vertex files and multiple edge files.</p>"}, "format": {"shape": "Format", "documentation": "<p>The format of the data. For more information about data formats for the Neptune <code>Loader</code> command, see <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/bulk-load-tutorial-format.html\">Load Data Formats</a>.</p> <p class=\"title\"> <b>Allowed values</b> </p> <ul> <li> <p> <b> <code>csv</code> </b> for the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/bulk-load-tutorial-format-gremlin.html\">Gremlin CSV data format</a>.</p> </li> <li> <p> <b> <code>opencypher</code> </b> for the <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/bulk-load-tutorial-format-opencypher.html\">openCypher CSV data format</a>.</p> </li> <li> <p> <b> <code>ntriples</code> </b> for the <a href=\"https://www.w3.org/TR/n-triples/\">N-Triples RDF data format</a>.</p> </li> <li> <p> <b> <code>nquads</code> </b> for the <a href=\"https://www.w3.org/TR/n-quads/\">N-Quads RDF data format</a>.</p> </li> <li> <p> <b> <code>rdfxml</code> </b> for the <a href=\"https://www.w3.org/TR/rdf-syntax-grammar/\">RDF\\XML RDF data format</a>.</p> </li> <li> <p> <b> <code>turtle</code> </b> for the <a href=\"https://www.w3.org/TR/turtle/\">Turtle RDF data format</a>.</p> </li> </ul>"}, "s3BucketRegion": {"shape": "S3BucketRegion", "documentation": "<p>The Amazon region of the S3 bucket. This must match the Amazon Region of the DB cluster.</p>", "locationName": "region"}, "iamRoleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) for an IAM role to be assumed by the Neptune DB instance for access to the S3 bucket. The IAM role ARN provided here should be attached to the DB cluster (see <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/bulk-load-tutorial-IAM-add-role-cluster.html\">Adding the IAM Role to an Amazon Neptune Cluster</a>.</p>"}, "mode": {"shape": "Mode", "documentation": "<p>The load job mode.</p> <p> <i>Allowed values</i>: <code>RESUME</code>, <code>NEW</code>, <code>AUTO</code>.</p> <p> <i>Default value</i>: <code>AUTO</code>.</p> <p class=\"title\"> <b/> </p> <ul> <li> <p> <code>RESUME</code>   –   In RESUME mode, the loader looks for a previous load from this source, and if it finds one, resumes that load job. If no previous load job is found, the loader stops.</p> <p>The loader avoids reloading files that were successfully loaded in a previous job. It only tries to process failed files. If you dropped previously loaded data from your Neptune cluster, that data is not reloaded in this mode. If a previous load job loaded all files from the same source successfully, nothing is reloaded, and the loader returns success.</p> </li> <li> <p> <code>NEW</code>   –   In NEW mode, the creates a new load request regardless of any previous loads. You can use this mode to reload all the data from a source after dropping previously loaded data from your Neptune cluster, or to load new data available at the same source.</p> </li> <li> <p> <code>AUTO</code>   –   In AUTO mode, the loader looks for a previous load job from the same source, and if it finds one, resumes that job, just as in <code>RESUME</code> mode.</p> <p>If the loader doesn't find a previous load job from the same source, it loads all data from the source, just as in <code>NEW</code> mode.</p> </li> </ul>"}, "failOnError": {"shape": "Boolean", "documentation": "<p> <b> <code>failOnError</code> </b>   –   A flag to toggle a complete stop on an error.</p> <p> <i>Allowed values</i>: <code>\"TRUE\"</code>, <code>\"FALSE\"</code>.</p> <p> <i>Default value</i>: <code>\"TRUE\"</code>.</p> <p>When this parameter is set to <code>\"FALSE\"</code>, the loader tries to load all the data in the location specified, skipping any entries with errors.</p> <p>When this parameter is set to <code>\"TRUE\"</code>, the loader stops as soon as it encounters an error. Data loaded up to that point persists.</p>"}, "parallelism": {"shape": "Parallelism", "documentation": "<p>The optional <code>parallelism</code> parameter can be set to reduce the number of threads used by the bulk load process.</p> <p> <i>Allowed values</i>:</p> <ul> <li> <p> <code>LOW</code> –   The number of threads used is the number of available vCPUs divided by 8.</p> </li> <li> <p> <code>MEDIUM</code> –   The number of threads used is the number of available vCPUs divided by 2.</p> </li> <li> <p> <code>HIGH</code> –   The number of threads used is the same as the number of available vCPUs.</p> </li> <li> <p> <code>OVERSUBSCRIBE</code> –   The number of threads used is the number of available vCPUs multiplied by 2. If this value is used, the bulk loader takes up all available resources.</p> <p>This does not mean, however, that the <code>OVERSUBSCRIBE</code> setting results in 100% CPU utilization. Because the load operation is I/O bound, the highest CPU utilization to expect is in the 60% to 70% range.</p> </li> </ul> <p> <i>Default value</i>: <code>HIGH</code> </p> <p>The <code>parallelism</code> setting can sometimes result in a deadlock between threads when loading openCypher data. When this happens, <PERSON> returns the <code>LOAD_DATA_DEADLOCK</code> error. You can generally fix the issue by setting <code>parallelism</code> to a lower setting and retrying the load command.</p>"}, "parserConfiguration": {"shape": "StringValuedMap", "documentation": "<p> <b> <code>parserConfiguration</code> </b>   –   An optional object with additional parser configuration values. Each of the child parameters is also optional:</p> <p class=\"title\"> <b/> </p> <ul> <li> <p> <b> <code>namedGraphUri</code> </b>   –   The default graph for all RDF formats when no graph is specified (for non-quads formats and NQUAD entries with no graph).</p> <p>The default is <code>https://aws.amazon.com/neptune/vocab/v01/DefaultNamedGraph</code>.</p> </li> <li> <p> <b> <code>baseUri</code> </b>   –   The base URI for RDF/XML and Turtle formats.</p> <p>The default is <code>https://aws.amazon.com/neptune/default</code>.</p> </li> <li> <p> <b> <code>allowEmptyStrings</code> </b>   –   Gremlin users need to be able to pass empty string values(\"\") as node and edge properties when loading CSV data. If <code>allowEmptyStrings</code> is set to <code>false</code> (the default), such empty strings are treated as nulls and are not loaded.</p> <p>If <code>allowEmptyStrings</code> is set to <code>true</code>, the loader treats empty strings as valid property values and loads them accordingly.</p> </li> </ul>"}, "updateSingleCardinalityProperties": {"shape": "Boolean", "documentation": "<p> <code>updateSingleCardinalityProperties</code> is an optional parameter that controls how the bulk loader treats a new value for single-cardinality vertex or edge properties. This is not supported for loading openCypher data.</p> <p> <i>Allowed values</i>: <code>\"TRUE\"</code>, <code>\"FALSE\"</code>.</p> <p> <i>Default value</i>: <code>\"FALSE\"</code>.</p> <p>By default, or when <code>updateSingleCardinalityProperties</code> is explicitly set to <code>\"FALSE\"</code>, the loader treats a new value as an error, because it violates single cardinality.</p> <p>When <code>updateSingleCardinalityProperties</code> is set to <code>\"TRUE\"</code>, on the other hand, the bulk loader replaces the existing value with the new one. If multiple edge or single-cardinality vertex property values are provided in the source file(s) being loaded, the final value at the end of the bulk load could be any one of those new values. The loader only guarantees that the existing value has been replaced by one of the new ones.</p>"}, "queueRequest": {"shape": "Boolean", "documentation": "<p>This is an optional flag parameter that indicates whether the load request can be queued up or not. </p> <p>You don't have to wait for one load job to complete before issuing the next one, because Neptune can queue up as many as 64 jobs at a time, provided that their <code>queueRequest</code> parameters are all set to <code>\"TRUE\"</code>. The queue order of the jobs will be first-in-first-out (FIFO).</p> <p>If the <code>queueRequest</code> parameter is omitted or set to <code>\"FALSE\"</code>, the load request will fail if another load job is already running.</p> <p> <i>Allowed values</i>: <code>\"TRUE\"</code>, <code>\"FALSE\"</code>.</p> <p> <i>Default value</i>: <code>\"FALSE\"</code>.</p>"}, "dependencies": {"shape": "StringList", "documentation": "<p>This is an optional parameter that can make a queued load request contingent on the successful completion of one or more previous jobs in the queue.</p> <p>Neptune can queue up as many as 64 load requests at a time, if their <code>queueRequest</code> parameters are set to <code>\"TRUE\"</code>. The <code>dependencies</code> parameter lets you make execution of such a queued request dependent on the successful completion of one or more specified previous requests in the queue.</p> <p>For example, if load <code>Job-A</code> and <code>Job-B</code> are independent of each other, but load <code>Job-C</code> needs <code>Job-A</code> and <code>Job-B</code> to be finished before it begins, proceed as follows:</p> <ol> <li> <p>Submit <code>load-job-A</code> and <code>load-job-B</code> one after another in any order, and save their load-ids.</p> </li> <li> <p>Submit <code>load-job-C</code> with the load-ids of the two jobs in its <code>dependencies</code> field:</p> </li> </ol> <p>Because of the <code>dependencies</code> parameter, the bulk loader will not start <code>Job-C</code> until <code>Job-A</code> and <code>Job-B</code> have completed successfully. If either one of them fails, Job-C will not be executed, and its status will be set to <code>LOAD_FAILED_BECAUSE_DEPENDENCY_NOT_SATISFIED</code>.</p> <p>You can set up multiple levels of dependency in this way, so that the failure of one job will cause all requests that are directly or indirectly dependent on it to be cancelled.</p>"}, "userProvidedEdgeIds": {"shape": "Boolean", "documentation": "<p>This parameter is required only when loading openCypher data that contains relationship IDs. It must be included and set to <code>True</code> when openCypher relationship IDs are explicitly provided in the load data (recommended).</p> <p>When <code>userProvidedEdgeIds</code> is absent or set to <code>True</code>, an <code>:ID</code> column must be present in every relationship file in the load.</p> <p>When <code>userProvidedEdgeIds</code> is present and set to <code>False</code>, relationship files in the load <b>must not</b> contain an <code>:ID</code> column. Instead, the Neptune loader automatically generates an ID for each relationship.</p> <p>It's useful to provide relationship IDs explicitly so that the loader can resume loading after error in the CSV data have been fixed, without having to reload any relationships that have already been loaded. If relationship IDs have not been explicitly assigned, the loader cannot resume a failed load if any relationship file has had to be corrected, and must instead reload all the relationships.</p>"}}}, "StartLoaderJobOutput": {"type": "structure", "required": ["status", "payload"], "members": {"status": {"shape": "String", "documentation": "<p>The HTTP return code indicating the status of the load job.</p>"}, "payload": {"shape": "StringValuedMap", "documentation": "<p>Contains a <code>loadId</code> name-value pair that provides an identifier for the load operation.</p>"}}}, "StartMLDataProcessingJobInput": {"type": "structure", "required": ["inputDataS3Location", "processedDataS3Location"], "members": {"id": {"shape": "String", "documentation": "<p>A unique identifier for the new job. The default is an autogenerated UUID.</p>"}, "previousDataProcessingJobId": {"shape": "String", "documentation": "<p>The job ID of a completed data processing job run on an earlier version of the data.</p>"}, "inputDataS3Location": {"shape": "String", "documentation": "<p>The URI of the Amazon S3 location where you want SageMaker to download the data needed to run the data processing job.</p>"}, "processedDataS3Location": {"shape": "String", "documentation": "<p>The URI of the Amazon S3 location where you want SageMaker to save the results of a data processing job.</p>"}, "sagemakerIamRoleArn": {"shape": "String", "documentation": "<p>The ARN of an IAM role for SageMaker execution. This must be listed in your DB cluster parameter group or an error will occur.</p>"}, "neptuneIamRoleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of an IAM role that SageMaker can assume to perform tasks on your behalf. This must be listed in your DB cluster parameter group or an error will occur.</p>"}, "processingInstanceType": {"shape": "String", "documentation": "<p>The type of ML instance used during data processing. Its memory should be large enough to hold the processed dataset. The default is the smallest ml.r5 type whose memory is ten times larger than the size of the exported graph data on disk.</p>"}, "processingInstanceVolumeSizeInGB": {"shape": "Integer", "documentation": "<p>The disk volume size of the processing instance. Both input data and processed data are stored on disk, so the volume size must be large enough to hold both data sets. The default is 0. If not specified or 0, Neptune ML chooses the volume size automatically based on the data size.</p>"}, "processingTimeOutInSeconds": {"shape": "Integer", "documentation": "<p>Timeout in seconds for the data processing job. The default is 86,400 (1 day).</p>"}, "modelType": {"shape": "String", "documentation": "<p>One of the two model types that Neptune ML currently supports: heterogeneous graph models (<code>heterogeneous</code>), and knowledge graph (<code>kge</code>). The default is none. If not specified, Neptune ML chooses the model type automatically based on the data.</p>"}, "configFileName": {"shape": "String", "documentation": "<p>A data specification file that describes how to load the exported graph data for training. The file is automatically generated by the Neptune export toolkit. The default is <code>training-data-configuration.json</code>.</p>"}, "subnets": {"shape": "StringList", "documentation": "<p>The IDs of the subnets in the Neptune VPC. The default is None.</p>"}, "securityGroupIds": {"shape": "StringList", "documentation": "<p>The VPC security group IDs. The default is None.</p>"}, "volumeEncryptionKMSKey": {"shape": "String", "documentation": "<p>The Amazon Key Management Service (Amazon KMS) key that SageMaker uses to encrypt data on the storage volume attached to the ML compute instances that run the training job. The default is None.</p>"}, "s3OutputEncryptionKMSKey": {"shape": "String", "documentation": "<p>The Amazon Key Management Service (Amazon KMS) key that SageMaker uses to encrypt the output of the processing job. The default is none.</p>"}}}, "StartMLDataProcessingJobOutput": {"type": "structure", "members": {"id": {"shape": "String", "documentation": "<p>The unique ID of the new data processing job.</p>"}, "arn": {"shape": "String", "documentation": "<p>The ARN of the data processing job.</p>"}, "creationTimeInMillis": {"shape": "<PERSON>", "documentation": "<p>The time it took to create the new processing job, in milliseconds.</p>"}}}, "StartMLModelTrainingJobInput": {"type": "structure", "required": ["dataProcessingJobId", "trainModelS3Location"], "members": {"id": {"shape": "String", "documentation": "<p>A unique identifier for the new job. The default is An autogenerated UUID.</p>"}, "previousModelTrainingJobId": {"shape": "String", "documentation": "<p>The job ID of a completed model-training job that you want to update incrementally based on updated data.</p>"}, "dataProcessingJobId": {"shape": "String", "documentation": "<p>The job ID of the completed data-processing job that has created the data that the training will work with.</p>"}, "trainModelS3Location": {"shape": "String", "documentation": "<p>The location in Amazon S3 where the model artifacts are to be stored.</p>"}, "sagemakerIamRoleArn": {"shape": "String", "documentation": "<p>The ARN of an IAM role for SageMaker execution.This must be listed in your DB cluster parameter group or an error will occur.</p>"}, "neptuneIamRoleArn": {"shape": "String", "documentation": "<p>The ARN of an IAM role that provides Neptune access to SageMaker and Amazon S3 resources. This must be listed in your DB cluster parameter group or an error will occur.</p>"}, "baseProcessingInstanceType": {"shape": "String", "documentation": "<p>The type of ML instance used in preparing and managing training of ML models. This is a CPU instance chosen based on memory requirements for processing the training data and model.</p>"}, "trainingInstanceType": {"shape": "String", "documentation": "<p>The type of ML instance used for model training. All Neptune ML models support CPU, GPU, and multiGPU training. The default is <code>ml.p3.2xlarge</code>. Choosing the right instance type for training depends on the task type, graph size, and your budget.</p>"}, "trainingInstanceVolumeSizeInGB": {"shape": "Integer", "documentation": "<p>The disk volume size of the training instance. Both input data and the output model are stored on disk, so the volume size must be large enough to hold both data sets. The default is 0. If not specified or 0, Neptune ML selects a disk volume size based on the recommendation generated in the data processing step.</p>"}, "trainingTimeOutInSeconds": {"shape": "Integer", "documentation": "<p>Timeout in seconds for the training job. The default is 86,400 (1 day).</p>"}, "maxHPONumberOfTrainingJobs": {"shape": "Integer", "documentation": "<p>Maximum total number of training jobs to start for the hyperparameter tuning job. The default is 2. Neptune ML automatically tunes the hyperparameters of the machine learning model. To obtain a model that performs well, use at least 10 jobs (in other words, set <code>maxHPONumberOfTrainingJobs</code> to 10). In general, the more tuning runs, the better the results.</p>"}, "maxHPOParallelTrainingJobs": {"shape": "Integer", "documentation": "<p>Maximum number of parallel training jobs to start for the hyperparameter tuning job. The default is 2. The number of parallel jobs you can run is limited by the available resources on your training instance.</p>"}, "subnets": {"shape": "StringList", "documentation": "<p>The IDs of the subnets in the Neptune VPC. The default is None.</p>"}, "securityGroupIds": {"shape": "StringList", "documentation": "<p>The VPC security group IDs. The default is None.</p>"}, "volumeEncryptionKMSKey": {"shape": "String", "documentation": "<p>The Amazon Key Management Service (KMS) key that SageMaker uses to encrypt data on the storage volume attached to the ML compute instances that run the training job. The default is None.</p>"}, "s3OutputEncryptionKMSKey": {"shape": "String", "documentation": "<p>The Amazon Key Management Service (KMS) key that SageMaker uses to encrypt the output of the processing job. The default is none.</p>"}, "enableManagedSpotTraining": {"shape": "Boolean", "documentation": "<p>Optimizes the cost of training machine-learning models by using Amazon Elastic Compute Cloud spot instances. The default is <code>False</code>.</p>"}, "customModelTrainingParameters": {"shape": "CustomModelTrainingParameters", "documentation": "<p>The configuration for custom model training. This is a JSON object.</p>"}}}, "StartMLModelTrainingJobOutput": {"type": "structure", "members": {"id": {"shape": "String", "documentation": "<p>The unique ID of the new model training job.</p>"}, "arn": {"shape": "String", "documentation": "<p>The ARN of the new model training job.</p>"}, "creationTimeInMillis": {"shape": "<PERSON>", "documentation": "<p>The model training job creation time, in milliseconds.</p>"}}}, "StartMLModelTransformJobInput": {"type": "structure", "required": ["modelTransformOutputS3Location"], "members": {"id": {"shape": "String", "documentation": "<p>A unique identifier for the new job. The default is an autogenerated UUID.</p>"}, "dataProcessingJobId": {"shape": "String", "documentation": "<p>The job ID of a completed data-processing job. You must include either <code>dataProcessingJobId</code> and a <code>mlModelTrainingJobId</code>, or a <code>trainingJobName</code>.</p>"}, "mlModelTrainingJobId": {"shape": "String", "documentation": "<p>The job ID of a completed model-training job. You must include either <code>dataProcessingJobId</code> and a <code>mlModelTrainingJobId</code>, or a <code>trainingJobName</code>.</p>"}, "trainingJobName": {"shape": "String", "documentation": "<p>The name of a completed SageMaker training job. You must include either <code>dataProcessingJobId</code> and a <code>mlModelTrainingJobId</code>, or a <code>trainingJobName</code>.</p>"}, "modelTransformOutputS3Location": {"shape": "String", "documentation": "<p>The location in Amazon S3 where the model artifacts are to be stored.</p>"}, "sagemakerIamRoleArn": {"shape": "String", "documentation": "<p>The ARN of an IAM role for SageMaker execution. This must be listed in your DB cluster parameter group or an error will occur.</p>"}, "neptuneIamRoleArn": {"shape": "String", "documentation": "<p>The ARN of an IAM role that provides Neptune access to SageMaker and Amazon S3 resources. This must be listed in your DB cluster parameter group or an error will occur.</p>"}, "customModelTransformParameters": {"shape": "CustomModelTransformParameters", "documentation": "<p>Configuration information for a model transform using a custom model. The <code>customModelTransformParameters</code> object contains the following fields, which must have values compatible with the saved model parameters from the training job:</p>"}, "baseProcessingInstanceType": {"shape": "String", "documentation": "<p>The type of ML instance used in preparing and managing training of ML models. This is an ML compute instance chosen based on memory requirements for processing the training data and model.</p>"}, "baseProcessingInstanceVolumeSizeInGB": {"shape": "Integer", "documentation": "<p>The disk volume size of the training instance in gigabytes. The default is 0. Both input data and the output model are stored on disk, so the volume size must be large enough to hold both data sets. If not specified or 0, Neptune ML selects a disk volume size based on the recommendation generated in the data processing step.</p>"}, "subnets": {"shape": "StringList", "documentation": "<p>The IDs of the subnets in the Neptune VPC. The default is None.</p>"}, "securityGroupIds": {"shape": "StringList", "documentation": "<p>The VPC security group IDs. The default is None.</p>"}, "volumeEncryptionKMSKey": {"shape": "String", "documentation": "<p>The Amazon Key Management Service (KMS) key that SageMaker uses to encrypt data on the storage volume attached to the ML compute instances that run the training job. The default is None.</p>"}, "s3OutputEncryptionKMSKey": {"shape": "String", "documentation": "<p>The Amazon Key Management Service (KMS) key that SageMaker uses to encrypt the output of the processing job. The default is none.</p>"}}}, "StartMLModelTransformJobOutput": {"type": "structure", "members": {"id": {"shape": "String", "documentation": "<p>The unique ID of the new model transform job.</p>"}, "arn": {"shape": "String", "documentation": "<p>The ARN of the model transform job.</p>"}, "creationTimeInMillis": {"shape": "<PERSON>", "documentation": "<p>The creation time of the model transform job, in milliseconds.</p>"}}}, "Statistics": {"type": "structure", "members": {"autoCompute": {"shape": "Boolean", "documentation": "<p>Indicates whether or not automatic statistics generation is enabled.</p>"}, "active": {"shape": "Boolean", "documentation": "<p>Indicates whether or not DFE statistics generation is enabled at all.</p>"}, "statisticsId": {"shape": "String", "documentation": "<p>Reports the ID of the current statistics generation run. A value of -1 indicates that no statistics have been generated.</p>"}, "date": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The UTC time at which DFE statistics have most recently been generated.</p>"}, "note": {"shape": "String", "documentation": "<p>A note about problems in the case where statistics are invalid.</p>"}, "signatureInfo": {"shape": "StatisticsSummary", "documentation": "<p>A StatisticsSummary structure that contains:</p> <ul> <li> <p> <code>signatureCount</code> - The total number of signatures across all characteristic sets.</p> </li> <li> <p> <code>instanceCount</code> - The total number of characteristic-set instances.</p> </li> <li> <p> <code>predicateCount</code> - The total number of unique predicates.</p> </li> </ul>"}}, "documentation": "<p>Contains statistics information. The DFE engine uses information about the data in your Neptune graph to make effective trade-offs when planning query execution. This information takes the form of statistics that include so-called characteristic sets and predicate statistics that can guide query planning. See <a href=\"https://docs.aws.amazon.com/neptune/latest/userguide/neptune-dfe-statistics.html\">Managing statistics for the Neptune DFE to use</a>.</p>"}, "StatisticsAutoGenerationMode": {"type": "string", "enum": ["disableAutoCompute", "enableAutoCompute", "refresh"]}, "StatisticsNotAvailableException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request in question.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when statistics needed to satisfy a request are not available.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "StatisticsSummary": {"type": "structure", "members": {"signatureCount": {"shape": "Integer", "documentation": "<p>The total number of signatures across all characteristic sets.</p>"}, "instanceCount": {"shape": "Integer", "documentation": "<p>The total number of characteristic-set instances.</p>"}, "predicateCount": {"shape": "Integer", "documentation": "<p>The total number of unique predicates.</p>"}}, "documentation": "<p>Information about the characteristic sets generated in the statistics.</p>"}, "StreamRecordsNotFoundException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request in question.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when stream records requested by a query cannot be found.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "String": {"type": "string"}, "StringList": {"type": "list", "member": {"shape": "String"}}, "StringValuedMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "SubjectStructure": {"type": "structure", "members": {"count": {"shape": "<PERSON>", "documentation": "<p>Number of occurrences of this specific structure.</p>"}, "predicates": {"shape": "Predicates", "documentation": "<p>A list of predicates present in this specific structure.</p>"}}, "documentation": "<p>A subject structure.</p>"}, "SubjectStructures": {"type": "list", "member": {"shape": "SubjectStructure"}}, "SyntheticTimestamp_date_time": {"type": "timestamp", "timestampFormat": "iso8601"}, "ThrottlingException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request that could not be processed for this reason.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when the rate of requests exceeds the maximum throughput. Requests can be retried after encountering this exception.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "TimeLimitExceededException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request that could not be processed for this reason.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when the an operation exceeds the time limit allowed for it.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "TooManyRequestsException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request that could not be processed for this reason.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when the number of requests being processed exceeds the limit.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true, "retryable": {"throttling": false}}, "UnsupportedOperationException": {"type": "structure", "required": ["detailedMessage", "requestId", "code"], "members": {"detailedMessage": {"shape": "String", "documentation": "<p>A detailed message describing the problem.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request in question.</p>"}, "code": {"shape": "String", "documentation": "<p>The HTTP status code returned with the exception.</p>"}}, "documentation": "<p>Raised when a request attempts to initiate an operation that is not supported.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}}, "documentation": "<p><fullname>Neptune Data API</fullname> <p>The Amazon Neptune data API provides SDK support for more than 40 of Neptune's data operations, including data loading, query execution, data inquiry, and machine learning. It supports the Gremlin and openCypher query languages, and is available in all SDK languages. It automatically signs API requests and greatly simplifies integrating Neptune into your applications.</p></p>"}