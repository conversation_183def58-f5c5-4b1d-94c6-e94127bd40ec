{"version": "2.0", "metadata": {"apiVersion": "2020-02-19", "endpointPrefix": "migrationhub-strategy", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "Migration Hub Strategy Recommendations", "serviceId": "MigrationHubStrategy", "signatureVersion": "v4", "signingName": "migrationhub-strategy", "uid": "migrationhubstrategy-2020-02-19"}, "operations": {"GetApplicationComponentDetails": {"name": "GetApplicationComponentDetails", "http": {"method": "GET", "requestUri": "/get-applicationcomponent-details/{applicationComponentId}", "responseCode": 200}, "input": {"shape": "GetApplicationComponentDetailsRequest"}, "output": {"shape": "GetApplicationComponentDetailsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p> Retrieves details about an application component. </p>"}, "GetApplicationComponentStrategies": {"name": "GetApplicationComponentStrategies", "http": {"method": "GET", "requestUri": "/get-applicationcomponent-strategies/{applicationComponentId}", "responseCode": 200}, "input": {"shape": "GetApplicationComponentStrategiesRequest"}, "output": {"shape": "GetApplicationComponentStrategiesResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p> Retrieves a list of all the recommended strategies and tools for an application component running on a server. </p>"}, "GetAssessment": {"name": "GetAssessment", "http": {"method": "GET", "requestUri": "/get-assessment/{id}", "responseCode": 200}, "input": {"shape": "GetAssessmentRequest"}, "output": {"shape": "GetAssessmentResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p> Retrieves the status of an on-going assessment. </p>"}, "GetImportFileTask": {"name": "GetImportFileTask", "http": {"method": "GET", "requestUri": "/get-import-file-task/{id}", "responseCode": 200}, "input": {"shape": "GetImportFileTaskRequest"}, "output": {"shape": "GetImportFileTaskResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Retrieves the details about a specific import task. </p>"}, "GetLatestAssessmentId": {"name": "GetLatestAssessmentId", "http": {"method": "GET", "requestUri": "/get-latest-assessment-id", "responseCode": 200}, "input": {"shape": "GetLatestAssessmentIdRequest"}, "output": {"shape": "GetLatestAssessmentIdResponse"}, "errors": [{"shape": "DependencyException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieve the latest ID of a specific assessment task.</p>"}, "GetPortfolioPreferences": {"name": "GetPortfolioPreferences", "http": {"method": "GET", "requestUri": "/get-portfolio-preferences", "responseCode": 200}, "input": {"shape": "GetPortfolioPreferencesRequest"}, "output": {"shape": "GetPortfolioPreferencesResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p> Retrieves your migration and modernization preferences. </p>"}, "GetPortfolioSummary": {"name": "GetPortfolioSummary", "http": {"method": "GET", "requestUri": "/get-portfolio-summary", "responseCode": 200}, "input": {"shape": "GetPortfolioSummaryRequest"}, "output": {"shape": "GetPortfolioSummaryResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p> Retrieves overall summary including the number of servers to rehost and the overall number of anti-patterns. </p>"}, "GetRecommendationReportDetails": {"name": "GetRecommendationReportDetails", "http": {"method": "GET", "requestUri": "/get-recommendation-report-details/{id}", "responseCode": 200}, "input": {"shape": "GetRecommendationReportDetailsRequest"}, "output": {"shape": "GetRecommendationReportDetailsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Retrieves detailed information about the specified recommendation report. </p>"}, "GetServerDetails": {"name": "GetServerDetails", "http": {"method": "GET", "requestUri": "/get-server-details/{serverId}", "responseCode": 200}, "input": {"shape": "GetServerDetailsRequest"}, "output": {"shape": "GetServerDetailsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Retrieves detailed information about a specified server. </p>"}, "GetServerStrategies": {"name": "GetServerStrategies", "http": {"method": "GET", "requestUri": "/get-server-strategies/{serverId}", "responseCode": 200}, "input": {"shape": "GetServerStrategiesRequest"}, "output": {"shape": "GetServerStrategiesResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Retrieves recommended strategies and tools for the specified server. </p>"}, "ListAnalyzableServers": {"name": "ListAnalyzableServers", "http": {"method": "POST", "requestUri": "/list-analyzable-servers", "responseCode": 200}, "input": {"shape": "ListAnalyzableServersRequest"}, "output": {"shape": "ListAnalyzableServersResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves a list of all the servers fetched from customer vCenter using Strategy Recommendation Collector.</p>"}, "ListApplicationComponents": {"name": "ListApplicationComponents", "http": {"method": "POST", "requestUri": "/list-applicationcomponents", "responseCode": 200}, "input": {"shape": "ListApplicationComponentsRequest"}, "output": {"shape": "ListApplicationComponentsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ServiceLinkedRoleLockClientException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Retrieves a list of all the application components (processes). </p>"}, "ListCollectors": {"name": "ListCollectors", "http": {"method": "GET", "requestUri": "/list-collectors", "responseCode": 200}, "input": {"shape": "ListCollectorsRequest"}, "output": {"shape": "ListCollectorsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Retrieves a list of all the installed collectors. </p>"}, "ListImportFileTask": {"name": "ListImportFileTask", "http": {"method": "GET", "requestUri": "/list-import-file-task", "responseCode": 200}, "input": {"shape": "ListImportFileTaskRequest"}, "output": {"shape": "ListImportFileTaskResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Retrieves a list of all the imports performed. </p>"}, "ListServers": {"name": "ListServers", "http": {"method": "POST", "requestUri": "/list-servers", "responseCode": 200}, "input": {"shape": "ListServersRequest"}, "output": {"shape": "ListServersResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Returns a list of all the servers. </p>"}, "PutPortfolioPreferences": {"name": "PutPortfolioPreferences", "http": {"method": "POST", "requestUri": "/put-portfolio-preferences", "responseCode": 200}, "input": {"shape": "PutPortfolioPreferencesRequest"}, "output": {"shape": "PutPortfolioPreferencesResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p> Saves the specified migration and modernization preferences. </p>"}, "StartAssessment": {"name": "StartAssessment", "http": {"method": "POST", "requestUri": "/start-assessment", "responseCode": 200}, "input": {"shape": "StartAssessmentRequest"}, "output": {"shape": "StartAssessmentResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "InternalServerException"}], "documentation": "<p> Starts the assessment of an on-premises environment. </p>"}, "StartImportFileTask": {"name": "StartImportFileTask", "http": {"method": "POST", "requestUri": "/start-import-file-task", "responseCode": 200}, "input": {"shape": "StartImportFileTaskRequest"}, "output": {"shape": "StartImportFileTaskResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Starts a file import. </p>"}, "StartRecommendationReportGeneration": {"name": "StartRecommendationReportGeneration", "http": {"method": "POST", "requestUri": "/start-recommendation-report-generation", "responseCode": 200}, "input": {"shape": "StartRecommendationReportGenerationRequest"}, "output": {"shape": "StartRecommendationReportGenerationResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p> Starts generating a recommendation report. </p>"}, "StopAssessment": {"name": "StopAssessment", "http": {"method": "POST", "requestUri": "/stop-assessment", "responseCode": 200}, "input": {"shape": "StopAssessmentRequest"}, "output": {"shape": "StopAssessmentResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Stops the assessment of an on-premises environment. </p>"}, "UpdateApplicationComponentConfig": {"name": "UpdateApplicationComponentConfig", "http": {"method": "POST", "requestUri": "/update-applicationcomponent-config/", "responseCode": 200}, "input": {"shape": "UpdateApplicationComponentConfigRequest"}, "output": {"shape": "UpdateApplicationComponentConfigResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Updates the configuration of an application component. </p>"}, "UpdateServerConfig": {"name": "UpdateServerConfig", "http": {"method": "POST", "requestUri": "/update-server-config/", "responseCode": 200}, "input": {"shape": "UpdateServerConfigRequest"}, "output": {"shape": "UpdateServerConfigResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Updates the configuration of the specified server. </p>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p> The user does not have permission to perform the action. Check the AWS Identity and Access Management (IAM) policy associated with this user.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "AnalysisStatusUnion": {"type": "structure", "members": {"runtimeAnalysisStatus": {"shape": "RuntimeAnalysisStatus", "documentation": "<p>The status of the analysis.</p>"}, "srcCodeOrDbAnalysisStatus": {"shape": "SrcCodeOrDbAnalysisStatus", "documentation": "<p>The status of the source code or database analysis.</p>"}}, "documentation": "<p>A combination of existing analysis statuses.</p>", "union": true}, "AnalysisType": {"type": "string", "enum": ["SOURCE_CODE_ANALYSIS", "DATABASE_ANALYSIS", "RUNTIME_ANALYSIS", "BINARY_ANALYSIS"]}, "AnalyzableServerSummary": {"type": "structure", "members": {"hostname": {"shape": "String", "documentation": "<p>The host name of the analyzable server.</p>"}, "ipAddress": {"shape": "String", "documentation": "<p>The ip address of the analyzable server.</p>"}, "source": {"shape": "String", "documentation": "<p>The data source of the analyzable server.</p>"}, "vmId": {"shape": "String", "documentation": "<p>The virtual machine id of the analyzable server.</p>"}}, "documentation": "<p>Summary information about an analyzable server.</p>"}, "AnalyzableServerSummaryList": {"type": "list", "member": {"shape": "AnalyzableServerSummary"}}, "AnalyzerNameUnion": {"type": "structure", "members": {"binaryAnalyzerName": {"shape": "BinaryAnalyzerName", "documentation": "<p>The binary analyzer names.</p>"}, "runTimeAnalyzerName": {"shape": "RunTimeAnalyzerName", "documentation": "<p>The assessment analyzer names.</p>"}, "sourceCodeAnalyzerName": {"shape": "SourceCodeAnalyzerName", "documentation": "<p>The source code analyzer names.</p>"}}, "documentation": "<p>The combination of the existing analyzers.</p>", "union": true}, "AntipatternReportResult": {"type": "structure", "members": {"analyzerName": {"shape": "AnalyzerNameUnion", "documentation": "<p>The analyzer name.</p>"}, "antiPatternReportS3Object": {"shape": "S3Object"}, "antipatternReportStatus": {"shape": "AntipatternReportStatus", "documentation": "<p>The status of the anti-pattern report generation.</p>"}, "antipatternReportStatusMessage": {"shape": "StatusMessage", "documentation": "<p>The status message for the anti-pattern.</p>"}}, "documentation": "<p>The anti-pattern report result.</p>"}, "AntipatternReportResultList": {"type": "list", "member": {"shape": "AntipatternReportResult"}}, "AntipatternReportStatus": {"type": "string", "enum": ["FAILED", "IN_PROGRESS", "SUCCESS"]}, "AntipatternSeveritySummary": {"type": "structure", "members": {"count": {"shape": "Integer", "documentation": "<p> Contains the count of anti-patterns. </p>"}, "severity": {"shape": "Severity", "documentation": "<p> Contains the severity of anti-patterns. </p>"}}, "documentation": "<p> Contains the summary of anti-patterns and their severity. </p>"}, "AppType": {"type": "string", "enum": ["DotNetFramework", "Java", "SQLServer", "IIS", "Oracle", "Other", "Tomcat", "<PERSON><PERSON><PERSON>", "Spring", "Mongo DB", "DB2", "Maria DB", "MySQL", "Sybase", "PostgreSQLServer", "<PERSON>", "IBM WebSphere", "Oracle WebLogic", "Visual Basic", "Unknown", "DotnetCore", "Dotnet"]}, "AppUnitError": {"type": "structure", "members": {"appUnitErrorCategory": {"shape": "AppUnitErrorCategory", "documentation": "<p>The category of the error.</p>"}}, "documentation": "<p>Error in the analysis of the application unit.</p>"}, "AppUnitErrorCategory": {"type": "string", "enum": ["CREDENTIAL_ERROR", "CONNECTIVITY_ERROR", "PERMISSION_ERROR", "UNSUPPORTED_ERROR", "OTHER_ERROR"]}, "ApplicationComponentCriteria": {"type": "string", "enum": ["NOT_DEFINED", "APP_NAME", "SERVER_ID", "APP_TYPE", "STRATEGY", "DESTINATION", "ANALYSIS_STATUS", "ERROR_CATEGORY"]}, "ApplicationComponentDetail": {"type": "structure", "members": {"analysisStatus": {"shape": "SrcCodeOrDbAnalysisStatus", "documentation": "<p> The status of analysis, if the application component has source code or an associated database. </p>"}, "antipatternReportS3Object": {"shape": "S3Object", "documentation": "<p> The S3 bucket name and the Amazon S3 key name for the anti-pattern report. </p>"}, "antipatternReportStatus": {"shape": "AntipatternReportStatus", "documentation": "<p> The status of the anti-pattern report generation.</p>"}, "antipatternReportStatusMessage": {"shape": "StatusMessage", "documentation": "<p> The status message for the anti-pattern. </p>"}, "appType": {"shape": "AppType", "documentation": "<p> The type of application component. </p>"}, "appUnitError": {"shape": "AppUnitError", "documentation": "<p>The error in the analysis of the source code or database.</p>"}, "associatedServerId": {"shape": "ServerId", "documentation": "<p> The ID of the server that the application component is running on. </p>"}, "databaseConfigDetail": {"shape": "DatabaseConfigDetail", "documentation": "<p> Configuration details for the database associated with the application component. </p>"}, "id": {"shape": "ResourceId", "documentation": "<p> The ID of the application component. </p>"}, "inclusionStatus": {"shape": "InclusionStatus", "documentation": "<p> Indicates whether the application component has been included for server recommendation or not. </p>"}, "lastAnalyzedTimestamp": {"shape": "TimeStamp", "documentation": "<p> The timestamp of when the application component was assessed. </p>"}, "listAntipatternSeveritySummary": {"shape": "ListAntipatternSeveritySummary", "documentation": "<p> A list of anti-pattern severity summaries. </p>"}, "moreServerAssociationExists": {"shape": "Boolean", "documentation": "<p> Set to true if the application component is running on multiple servers.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p> The name of application component. </p>"}, "osDriver": {"shape": "String", "documentation": "<p> OS driver. </p>"}, "osVersion": {"shape": "String", "documentation": "<p> OS version. </p>"}, "recommendationSet": {"shape": "RecommendationSet", "documentation": "<p> The top recommendation set for the application component. </p>"}, "resourceSubType": {"shape": "ResourceSubType", "documentation": "<p> The application component subtype.</p>"}, "resultList": {"shape": "ResultList", "documentation": "<p>A list of the analysis results.</p>"}, "runtimeStatus": {"shape": "RuntimeAnalysisStatus", "documentation": "<p>The status of the application unit.</p>"}, "runtimeStatusMessage": {"shape": "StatusMessage", "documentation": "<p>The status message for the application unit.</p>"}, "sourceCodeRepositories": {"shape": "SourceCodeRepositories", "documentation": "<p> Details about the source code repository associated with the application component. </p>"}, "statusMessage": {"shape": "StatusMessage", "documentation": "<p> A detailed description of the analysis status and any failure message. </p>"}}, "documentation": "<p> Contains detailed information about an application component. </p>"}, "ApplicationComponentDetails": {"type": "list", "member": {"shape": "ApplicationComponentDetail"}}, "ApplicationComponentId": {"type": "string", "max": 44, "min": 0, "pattern": "[0-9a-zA-Z-]+"}, "ApplicationComponentStatusSummary": {"type": "structure", "members": {"count": {"shape": "Integer", "documentation": "<p>The number of application components successfully analyzed, partially successful or failed analysis.</p>"}, "srcCodeOrDbAnalysisStatus": {"shape": "SrcCodeOrDbAnalysisStatus", "documentation": "<p>The status of database analysis.</p>"}}, "documentation": "<p>Summary of the analysis status of the application component.</p>"}, "ApplicationComponentStrategies": {"type": "list", "member": {"shape": "ApplicationComponentStrategy"}}, "ApplicationComponentStrategy": {"type": "structure", "members": {"isPreferred": {"shape": "Boolean", "documentation": "<p> Set to true if the recommendation is set as preferred. </p>"}, "recommendation": {"shape": "RecommendationSet", "documentation": "<p> Strategy recommendation for the application component. </p>"}, "status": {"shape": "StrategyRecommendation", "documentation": "<p> The recommendation status of a strategy for an application component. </p>"}}, "documentation": "<p> Contains information about a strategy recommendation for an application component. </p>"}, "ApplicationComponentSummary": {"type": "structure", "members": {"appType": {"shape": "AppType", "documentation": "<p> Contains the name of application types. </p>"}, "count": {"shape": "Integer", "documentation": "<p> Contains the count of application type. </p>"}}, "documentation": "<p> Contains the summary of application components. </p>"}, "ApplicationMode": {"type": "string", "enum": ["ALL", "KNOWN", "UNKNOWN"]}, "ApplicationPreferences": {"type": "structure", "members": {"managementPreference": {"shape": "ManagementPreference", "documentation": "<p> Application preferences that you specify to prefer managed environment. </p>"}}, "documentation": "<p> Application preferences that you specify. </p>"}, "AssessmentDataSourceType": {"type": "string", "enum": ["StrategyRecommendationsApplicationDataCollector", "ManualImport", "ApplicationDiscoveryService"]}, "AssessmentStatus": {"type": "string", "enum": ["IN_PROGRESS", "COMPLETE", "FAILED", "STOPPED"]}, "AssessmentStatusMessage": {"type": "string", "max": 512, "min": 0, "pattern": ".*\\S.*"}, "AssessmentSummary": {"type": "structure", "members": {"antipatternReportS3Object": {"shape": "S3Object", "documentation": "<p> The Amazon S3 object containing the anti-pattern report. </p>"}, "antipatternReportStatus": {"shape": "AntipatternReportStatus", "documentation": "<p> The status of the anti-pattern report. </p>"}, "antipatternReportStatusMessage": {"shape": "StatusMessage", "documentation": "<p> The status message of the anti-pattern report. </p>"}, "lastAnalyzedTimestamp": {"shape": "TimeStamp", "documentation": "<p> The time the assessment was performed. </p>"}, "listAntipatternSeveritySummary": {"shape": "ListAntipatternSeveritySummary", "documentation": "<p> List of AntipatternSeveritySummary. </p>"}, "listApplicationComponentStatusSummary": {"shape": "ListApplicationComponentStatusSummary", "documentation": "<p>List of status summaries of the analyzed application components.</p>"}, "listApplicationComponentStrategySummary": {"shape": "ListStrategySummary", "documentation": "<p> List of ApplicationComponentStrategySummary. </p>"}, "listApplicationComponentSummary": {"shape": "ListApplicationComponentSummary", "documentation": "<p> List of ApplicationComponentSummary. </p>"}, "listServerStatusSummary": {"shape": "ListServerStatusSummary", "documentation": "<p>List of status summaries of the analyzed servers.</p>"}, "listServerStrategySummary": {"shape": "ListStrategySummary", "documentation": "<p> List of ServerStrategySummary. </p>"}, "listServerSummary": {"shape": "ListServerSummary", "documentation": "<p> List of ServerSummary. </p>"}}, "documentation": "<p> Contains the summary of the assessment results. </p>"}, "AssessmentTarget": {"type": "structure", "required": ["condition", "name", "values"], "members": {"condition": {"shape": "Condition", "documentation": "<p>Condition of an assessment.</p>"}, "name": {"shape": "String", "documentation": "<p>Name of an assessment.</p>"}, "values": {"shape": "AssessmentTargetValues", "documentation": "<p>Values of an assessment.</p>"}}, "documentation": "<p>Defines the criteria of assessment.</p>"}, "AssessmentTargetValues": {"type": "list", "member": {"shape": "String"}}, "AssessmentTargets": {"type": "list", "member": {"shape": "Assessment<PERSON>arget"}, "max": 10, "min": 0}, "AssociatedApplication": {"type": "structure", "members": {"id": {"shape": "String", "documentation": "<p> ID of the application as defined in Application Discovery Service. </p>"}, "name": {"shape": "String", "documentation": "<p> Name of the application as defined in Application Discovery Service. </p>"}}, "documentation": "<p> Object containing details about applications as defined in Application Discovery Service. </p>"}, "AssociatedApplications": {"type": "list", "member": {"shape": "AssociatedApplication"}}, "AssociatedServerIDs": {"type": "list", "member": {"shape": "String"}}, "AsyncTaskId": {"type": "string", "max": 52, "min": 0, "pattern": "[0-9a-z-:]+"}, "AuthType": {"type": "string", "enum": ["NTLM", "SSH", "CERT"]}, "AwsManagedResources": {"type": "structure", "required": ["targetDestination"], "members": {"targetDestination": {"shape": "AwsManagedTargetDestinations", "documentation": "<p> The choice of application destination that you specify. </p>"}}, "documentation": "<p> Object containing the choice of application destination that you specify. </p>"}, "AwsManagedTargetDestination": {"type": "string", "enum": ["None specified", "AWS Elastic BeanStalk", "AWS Fargate"]}, "AwsManagedTargetDestinations": {"type": "list", "member": {"shape": "AwsManagedTargetDestination"}, "max": 1, "min": 1}, "BinaryAnalyzerName": {"type": "string", "enum": ["DLL_ANALYZER", "BYTECODE_ANALYZER"]}, "Boolean": {"type": "boolean", "box": true}, "BusinessGoals": {"type": "structure", "members": {"licenseCostReduction": {"shape": "BusinessGoalsInteger", "documentation": "<p> Business goal to reduce license costs. </p>"}, "modernizeInfrastructureWithCloudNativeTechnologies": {"shape": "BusinessGoalsInteger", "documentation": "<p> Business goal to modernize infrastructure by moving to cloud native technologies. </p>"}, "reduceOperationalOverheadWithManagedServices": {"shape": "BusinessGoalsInteger", "documentation": "<p> Business goal to reduce the operational overhead on the team by moving into managed services. </p>"}, "speedOfMigration": {"shape": "BusinessGoalsInteger", "documentation": "<p> Business goal to achieve migration at a fast pace. </p>"}}, "documentation": "<p> Business goals that you specify. </p>"}, "BusinessGoalsInteger": {"type": "integer", "box": true, "max": 5, "min": 1}, "Collector": {"type": "structure", "members": {"collectorHealth": {"shape": "CollectorHealth", "documentation": "<p> Indicates the health of a collector. </p>"}, "collectorId": {"shape": "String", "documentation": "<p> The ID of the collector. </p>"}, "collectorVersion": {"shape": "String", "documentation": "<p> Current version of the collector that is running in the environment that you specify. </p>"}, "configurationSummary": {"shape": "ConfigurationSummary", "documentation": "<p>Summary of the collector configuration.</p>"}, "hostName": {"shape": "String", "documentation": "<p> Hostname of the server that is hosting the collector. </p>"}, "ipAddress": {"shape": "String", "documentation": "<p> IP address of the server that is hosting the collector. </p>"}, "lastActivityTimeStamp": {"shape": "String", "documentation": "<p> Time when the collector last pinged the service. </p>"}, "registeredTimeStamp": {"shape": "String", "documentation": "<p> Time when the collector registered with the service. </p>"}}, "documentation": "<p> Process data collector that runs in the environment that you specify. </p>"}, "CollectorHealth": {"type": "string", "enum": ["COLLECTOR_HEALTHY", "COLLECTOR_UNHEALTHY"]}, "Collectors": {"type": "list", "member": {"shape": "Collector"}}, "Condition": {"type": "string", "enum": ["EQUALS", "NOT_EQUALS", "CONTAINS", "NOT_CONTAINS"]}, "ConfigurationSummary": {"type": "structure", "members": {"ipAddressBasedRemoteInfoList": {"shape": "IPAddressBasedRemoteInfoList", "documentation": "<p>IP address based configurations.</p>"}, "pipelineInfoList": {"shape": "PipelineInfoList", "documentation": "<p>The list of pipeline info configurations.</p>"}, "remoteSourceCodeAnalysisServerInfo": {"shape": "RemoteSourceCodeAnalysisServerInfo", "documentation": "<p>Info about the remote server source code configuration.</p>"}, "vcenterBasedRemoteInfoList": {"shape": "VcenterBasedRemoteInfoList", "documentation": "<p>The list of vCenter configurations.</p>"}, "versionControlInfoList": {"shape": "VersionControlInfoList", "documentation": "<p>The list of the version control configurations.</p>"}}, "documentation": "<p>Summary of the collector configuration.</p>"}, "ConflictException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p> Exception to indicate that there is an ongoing task when a new task is created. Return when once the existing tasks are complete. </p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "DataCollectionDetails": {"type": "structure", "members": {"completionTime": {"shape": "TimeStamp", "documentation": "<p> The time the assessment completes. </p>"}, "failed": {"shape": "Integer", "documentation": "<p> The number of failed servers in the assessment. </p>"}, "inProgress": {"shape": "Integer", "documentation": "<p> The number of servers with the assessment status <code>IN_PROGESS</code>. </p>"}, "servers": {"shape": "Integer", "documentation": "<p> The total number of servers in the assessment. </p>"}, "startTime": {"shape": "TimeStamp", "documentation": "<p> The start time of assessment. </p>"}, "status": {"shape": "AssessmentStatus", "documentation": "<p> The status of the assessment. </p>"}, "statusMessage": {"shape": "AssessmentStatusMessage", "documentation": "<p>The status message of the assessment.</p>"}, "success": {"shape": "Integer", "documentation": "<p> The number of successful servers in the assessment. </p>"}}, "documentation": "<p> Detailed information about an assessment. </p>"}, "DataSourceType": {"type": "string", "enum": ["ApplicationDiscoveryService", "MPA", "Import", "StrategyRecommendationsApplicationDataCollector"]}, "DatabaseConfigDetail": {"type": "structure", "members": {"secretName": {"shape": "String", "documentation": "<p> AWS Secrets Manager key that holds the credentials that you use to connect to a database. </p>"}}, "documentation": "<p> Configuration information used for assessing databases. </p>"}, "DatabaseManagementPreference": {"type": "string", "enum": ["AWS-managed", "Self-manage", "No preference"]}, "DatabaseMigrationPreference": {"type": "structure", "members": {"heterogeneous": {"shape": "Heterogeneous", "documentation": "<p> Indicates whether you are interested in moving from one type of database to another. For example, from SQL Server to Amazon Aurora MySQL-Compatible Edition. </p>"}, "homogeneous": {"shape": "Homogeneous", "documentation": "<p> Indicates whether you are interested in moving to the same type of database into AWS. For example, from SQL Server in your environment to SQL Server on AWS. </p>"}, "noPreference": {"shape": "NoDatabaseMigrationPreference", "documentation": "<p> Indicated that you do not prefer heterogeneous or homogeneous. </p>"}}, "documentation": "<p> Preferences for migrating a database to AWS. </p>", "union": true}, "DatabasePreferences": {"type": "structure", "members": {"databaseManagementPreference": {"shape": "DatabaseManagementPreference", "documentation": "<p> Specifies whether you're interested in self-managed databases or databases managed by AWS. </p>"}, "databaseMigrationPreference": {"shape": "DatabaseMigrationPreference", "documentation": "<p> Specifies your preferred migration path. </p>"}}, "documentation": "<p> Preferences on managing your databases on AWS. </p>"}, "DependencyException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>Dependency encountered an error.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "GetApplicationComponentDetailsRequest": {"type": "structure", "required": ["applicationComponentId"], "members": {"applicationComponentId": {"shape": "ApplicationComponentId", "documentation": "<p> The ID of the application component. The ID is unique within an AWS account.</p>", "location": "uri", "locationName": "applicationComponentId"}}}, "GetApplicationComponentDetailsResponse": {"type": "structure", "members": {"applicationComponentDetail": {"shape": "ApplicationComponentDetail", "documentation": "<p> Detailed information about an application component. </p>"}, "associatedApplications": {"shape": "AssociatedApplications", "documentation": "<p> The associated application group as defined in AWS Application Discovery Service. </p>"}, "associatedServerIds": {"shape": "AssociatedServerIDs", "documentation": "<p> A list of the IDs of the servers on which the application component is running. </p>"}, "moreApplicationResource": {"shape": "Boolean", "documentation": "<p> Set to true if the application component belongs to more than one application group. </p>"}}}, "GetApplicationComponentStrategiesRequest": {"type": "structure", "required": ["applicationComponentId"], "members": {"applicationComponentId": {"shape": "ApplicationComponentId", "documentation": "<p> The ID of the application component. The ID is unique within an AWS account.</p>", "location": "uri", "locationName": "applicationComponentId"}}}, "GetApplicationComponentStrategiesResponse": {"type": "structure", "members": {"applicationComponentStrategies": {"shape": "ApplicationComponentStrategies", "documentation": "<p> A list of application component strategy recommendations. </p>"}}}, "GetAssessmentRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "AsyncTaskId", "documentation": "<p> The <code>assessmentid</code> returned by <a>StartAssessment</a>.</p>", "location": "uri", "locationName": "id"}}}, "GetAssessmentResponse": {"type": "structure", "members": {"assessmentTargets": {"shape": "AssessmentTargets", "documentation": "<p>List of criteria for assessment.</p>"}, "dataCollectionDetails": {"shape": "DataCollectionDetails", "documentation": "<p> Detailed information about the assessment. </p>"}, "id": {"shape": "AsyncTaskId", "documentation": "<p> The ID for the specific assessment task. </p>"}}}, "GetImportFileTaskRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "String", "documentation": "<p> The ID of the import file task. This ID is returned in the response of <a>StartImportFileTask</a>. </p>", "location": "uri", "locationName": "id"}}}, "GetImportFileTaskResponse": {"type": "structure", "members": {"completionTime": {"shape": "TimeStamp", "documentation": "<p> The time that the import task completed. </p>"}, "id": {"shape": "String", "documentation": "<p> The import file task <code>id</code> returned in the response of <a>StartImportFileTask</a>. </p>"}, "importName": {"shape": "String", "documentation": "<p> The name of the import task given in <a>StartImportFileTask</a>. </p>"}, "inputS3Bucket": {"shape": "importS3Bucket", "documentation": "<p> The S3 bucket where import file is located. </p>"}, "inputS3Key": {"shape": "importS3Key", "documentation": "<p> The Amazon S3 key name of the import file. </p>"}, "numberOfRecordsFailed": {"shape": "Integer", "documentation": "<p> The number of records that failed to be imported. </p>"}, "numberOfRecordsSuccess": {"shape": "Integer", "documentation": "<p> The number of records successfully imported. </p>"}, "startTime": {"shape": "TimeStamp", "documentation": "<p> Start time of the import task. </p>"}, "status": {"shape": "ImportFileTaskStatus", "documentation": "<p> Status of import file task. </p>"}, "statusReportS3Bucket": {"shape": "importS3Bucket", "documentation": "<p> The S3 bucket name for status report of import task. </p>"}, "statusReportS3Key": {"shape": "importS3Key", "documentation": "<p> The Amazon S3 key name for status report of import task. The report contains details about whether each record imported successfully or why it did not.</p>"}}}, "GetLatestAssessmentIdRequest": {"type": "structure", "members": {}}, "GetLatestAssessmentIdResponse": {"type": "structure", "members": {"id": {"shape": "AsyncTaskId", "documentation": "<p>The latest ID for the specific assessment task.</p>"}}}, "GetPortfolioPreferencesRequest": {"type": "structure", "members": {}}, "GetPortfolioPreferencesResponse": {"type": "structure", "members": {"applicationMode": {"shape": "ApplicationMode", "documentation": "<p>The classification for application component types.</p>"}, "applicationPreferences": {"shape": "ApplicationPreferences", "documentation": "<p> The transformation preferences for non-database applications. </p>"}, "databasePreferences": {"shape": "DatabasePreferences", "documentation": "<p> The transformation preferences for database applications. </p>"}, "prioritizeBusinessGoals": {"shape": "PrioritizeBusinessGoals", "documentation": "<p> The rank of business goals based on priority. </p>"}}}, "GetPortfolioSummaryRequest": {"type": "structure", "members": {}}, "GetPortfolioSummaryResponse": {"type": "structure", "members": {"assessmentSummary": {"shape": "AssessmentSummary", "documentation": "<p> An assessment summary for the portfolio including the number of servers to rehost and the overall number of anti-patterns. </p>"}}}, "GetRecommendationReportDetailsRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "RecommendationTaskId", "documentation": "<p> The recommendation report generation task <code>id</code> returned by <a>StartRecommendationReportGeneration</a>. </p>", "location": "uri", "locationName": "id"}}}, "GetRecommendationReportDetailsResponse": {"type": "structure", "members": {"id": {"shape": "RecommendationTaskId", "documentation": "<p> The ID of the recommendation report generation task. See the response of <a>StartRecommendationReportGeneration</a>. </p>"}, "recommendationReportDetails": {"shape": "RecommendationReportDetails", "documentation": "<p> Detailed information about the recommendation report. </p>"}}}, "GetServerDetailsRequest": {"type": "structure", "required": ["serverId"], "members": {"maxResults": {"shape": "MaxResult", "documentation": "<p> The maximum number of items to include in the response. The maximum value is 100. </p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p> The token from a previous call that you use to retrieve the next set of results. For example, if a previous call to this action returned 100 items, but you set <code>maxResults</code> to 10. You'll receive a set of 10 results along with a token. You then use the returned token to retrieve the next set of 10. </p>", "location": "querystring", "locationName": "nextToken"}, "serverId": {"shape": "ServerId", "documentation": "<p> The ID of the server. </p>", "location": "uri", "locationName": "serverId"}}}, "GetServerDetailsResponse": {"type": "structure", "members": {"associatedApplications": {"shape": "AssociatedApplications", "documentation": "<p> The associated application group the server belongs to, as defined in AWS Application Discovery Service. </p>"}, "nextToken": {"shape": "String", "documentation": "<p> The token you use to retrieve the next set of results, or null if there are no more results. </p>"}, "serverDetail": {"shape": "ServerDetail", "documentation": "<p> Detailed information about the server. </p>"}}}, "GetServerStrategiesRequest": {"type": "structure", "required": ["serverId"], "members": {"serverId": {"shape": "ServerId", "documentation": "<p> The ID of the server. </p>", "location": "uri", "locationName": "serverId"}}}, "GetServerStrategiesResponse": {"type": "structure", "members": {"serverStrategies": {"shape": "ServerStrategies", "documentation": "<p> A list of strategy recommendations for the server. </p>"}}}, "Group": {"type": "structure", "members": {"name": {"shape": "GroupName", "documentation": "<p> The key of the specific import group. </p>"}, "value": {"shape": "String", "documentation": "<p> The value of the specific import group. </p>"}}, "documentation": "<p> The object containing information about distinct imports or groups for Strategy Recommendations. </p>"}, "GroupIds": {"type": "list", "member": {"shape": "Group"}}, "GroupName": {"type": "string", "enum": ["ExternalId", "ExternalSourceType"]}, "Heterogeneous": {"type": "structure", "required": ["targetDatabaseEngine"], "members": {"targetDatabaseEngine": {"shape": "HeterogeneousTargetDatabaseEngines", "documentation": "<p> The target database engine for heterogeneous database migration preference. </p>"}}, "documentation": "<p> The object containing details about heterogeneous database preferences. </p>"}, "HeterogeneousTargetDatabaseEngine": {"type": "string", "enum": ["None specified", "Amazon Aurora", "AWS PostgreSQL", "MySQL", "Microsoft SQL Server", "Oracle Database", "MariaDB", "SAP", "Db2 LUW", "MongoDB"]}, "HeterogeneousTargetDatabaseEngines": {"type": "list", "member": {"shape": "HeterogeneousTargetDatabaseEngine"}, "max": 1, "min": 1}, "Homogeneous": {"type": "structure", "members": {"targetDatabaseEngine": {"shape": "HomogeneousTargetDatabaseEngines", "documentation": "<p> The target database engine for homogeneous database migration preferences. </p>"}}, "documentation": "<p> The object containing details about homogeneous database preferences. </p>"}, "HomogeneousTargetDatabaseEngine": {"type": "string", "enum": ["None specified"]}, "HomogeneousTargetDatabaseEngines": {"type": "list", "member": {"shape": "HomogeneousTargetDatabaseEngine"}, "max": 1, "min": 0}, "IPAddress": {"type": "string", "max": 15, "min": 0, "pattern": "^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$"}, "IPAddressBasedRemoteInfo": {"type": "structure", "members": {"authType": {"shape": "AuthType", "documentation": "<p>The type of authorization.</p>"}, "ipAddressConfigurationTimeStamp": {"shape": "String", "documentation": "<p>The time stamp of the configuration.</p>"}, "osType": {"shape": "OSType", "documentation": "<p>The type of the operating system.</p>"}}, "documentation": "<p>IP address based configurations.</p>"}, "IPAddressBasedRemoteInfoList": {"type": "list", "member": {"shape": "IPAddressBasedRemoteInfo"}}, "ImportFileTaskInformation": {"type": "structure", "members": {"completionTime": {"shape": "TimeStamp", "documentation": "<p> The time that the import task completes. </p>"}, "id": {"shape": "String", "documentation": "<p> The ID of the import file task. </p>"}, "importName": {"shape": "String", "documentation": "<p> The name of the import task given in <code>StartImportFileTask</code>. </p>"}, "inputS3Bucket": {"shape": "importS3Bucket", "documentation": "<p> The S3 bucket where the import file is located. </p>"}, "inputS3Key": {"shape": "importS3Key", "documentation": "<p> The Amazon S3 key name of the import file. </p>"}, "numberOfRecordsFailed": {"shape": "Integer", "documentation": "<p> The number of records that failed to be imported. </p>"}, "numberOfRecordsSuccess": {"shape": "Integer", "documentation": "<p> The number of records successfully imported. </p>"}, "startTime": {"shape": "TimeStamp", "documentation": "<p> Start time of the import task. </p>"}, "status": {"shape": "ImportFileTaskStatus", "documentation": "<p> Status of import file task. </p>"}, "statusReportS3Bucket": {"shape": "importS3Bucket", "documentation": "<p> The S3 bucket name for status report of import task. </p>"}, "statusReportS3Key": {"shape": "importS3Key", "documentation": "<p> The Amazon S3 key name for status report of import task. The report contains details about whether each record imported successfully or why it did not. </p>"}}, "documentation": "<p> Information about the import file tasks you request. </p>"}, "ImportFileTaskStatus": {"type": "string", "enum": ["ImportInProgress", "ImportFailed", "ImportPartialSuccess", "ImportSuccess", "DeleteInProgress", "DeleteFailed", "DeletePartialSuccess", "DeleteSuccess"]}, "InclusionStatus": {"type": "string", "enum": ["excludeFromAssessment", "includeInAssessment"]}, "Integer": {"type": "integer", "box": true}, "InterfaceName": {"type": "string", "max": 1024, "min": 0, "pattern": ".*"}, "InternalServerException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p> The server experienced an internal error. Try again. </p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "ListAnalyzableServersRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResult", "documentation": "<p>The maximum number of items to include in the response. The maximum value is 100.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token from a previous call that you use to retrieve the next set of results. For example, if a previous call to this action returned 100 items, but you set maxResults to 10. You'll receive a set of 10 results along with a token. You then use the returned token to retrieve the next set of 10.</p>"}, "sort": {"shape": "SortOrder", "documentation": "<p>Specifies whether to sort by ascending (ASC) or descending (DESC) order.</p>"}}, "documentation": "<p>Represents input for ListAnalyzableServers operation.</p>"}, "ListAnalyzableServersResponse": {"type": "structure", "members": {"analyzableServers": {"shape": "AnalyzableServerSummaryList", "documentation": "<p>The list of analyzable servers with summary information about each server.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token you use to retrieve the next set of results, or null if there are no more results.</p>"}}, "documentation": "<p>Represents output for ListAnalyzableServers operation.</p>"}, "ListAntipatternSeveritySummary": {"type": "list", "member": {"shape": "AntipatternSeveritySummary"}}, "ListApplicationComponentStatusSummary": {"type": "list", "member": {"shape": "ApplicationComponentStatusSummary"}}, "ListApplicationComponentSummary": {"type": "list", "member": {"shape": "ApplicationComponentSummary"}}, "ListApplicationComponentsRequest": {"type": "structure", "members": {"applicationComponentCriteria": {"shape": "ApplicationComponentCriteria", "documentation": "<p> Criteria for filtering the list of application components. </p>"}, "filterValue": {"shape": "ListApplicationComponentsRequestFilterValueString", "documentation": "<p> Specify the value based on the application component criteria type. For example, if <code>applicationComponentCriteria</code> is set to <code>SERVER_ID</code> and <code>filterValue</code> is set to <code>server1</code>, then <a>ListApplicationComponents</a> returns all the application components running on server1. </p>"}, "groupIdFilter": {"shape": "GroupIds", "documentation": "<p> The group ID specified in to filter on. </p>"}, "maxResults": {"shape": "MaxResult", "documentation": "<p> The maximum number of items to include in the response. The maximum value is 100. </p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p> The token from a previous call that you use to retrieve the next set of results. For example, if a previous call to this action returned 100 items, but you set <code>maxResults</code> to 10. You'll receive a set of 10 results along with a token. You then use the returned token to retrieve the next set of 10. </p>"}, "sort": {"shape": "SortOrder", "documentation": "<p> Specifies whether to sort by ascending (<code>ASC</code>) or descending (<code>DESC</code>) order. </p>"}}}, "ListApplicationComponentsRequestFilterValueString": {"type": "string", "max": 256, "min": 0, "pattern": ".*\\S.*"}, "ListApplicationComponentsResponse": {"type": "structure", "members": {"applicationComponentInfos": {"shape": "ApplicationComponentDetails", "documentation": "<p> The list of application components with detailed information about each component. </p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p> The token you use to retrieve the next set of results, or null if there are no more results. </p>"}}}, "ListCollectorsRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResult", "documentation": "<p> The maximum number of items to include in the response. The maximum value is 100. </p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p> The token from a previous call that you use to retrieve the next set of results. For example, if a previous call to this action returned 100 items, but you set <code>maxResults</code> to 10. You'll receive a set of 10 results along with a token. You then use the returned token to retrieve the next set of 10. </p>", "location": "querystring", "locationName": "nextToken"}}}, "ListCollectorsResponse": {"type": "structure", "members": {"Collectors": {"shape": "Collectors", "documentation": "<p> The list of all the installed collectors. </p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p> The token you use to retrieve the next set of results, or null if there are no more results. </p>"}}}, "ListImportFileTaskInformation": {"type": "list", "member": {"shape": "ImportFileTaskInformation"}}, "ListImportFileTaskRequest": {"type": "structure", "members": {"maxResults": {"shape": "Integer", "documentation": "<p> The total number of items to return. The maximum value is 100. </p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "String", "documentation": "<p> The token from a previous call that you use to retrieve the next set of results. For example, if a previous call to this action returned 100 items, but you set <code>maxResults</code> to 10. You'll receive a set of 10 results along with a token. You then use the returned token to retrieve the next set of 10. </p>", "location": "querystring", "locationName": "nextToken"}}}, "ListImportFileTaskResponse": {"type": "structure", "members": {"nextToken": {"shape": "String", "documentation": "<p> The token you use to retrieve the next set of results, or null if there are no more results. </p>"}, "taskInfos": {"shape": "ListImportFileTaskInformation", "documentation": "<p> Lists information about the files you import.</p>"}}}, "ListServerStatusSummary": {"type": "list", "member": {"shape": "ServerStatusSummary"}}, "ListServerSummary": {"type": "list", "member": {"shape": "ServerSummary"}}, "ListServersRequest": {"type": "structure", "members": {"filterValue": {"shape": "String", "documentation": "<p> Specifies the filter value, which is based on the type of server criteria. For example, if <code>serverCriteria</code> is <code>OS_NAME</code>, and the <code>filterValue</code> is equal to <code>WindowsServer</code>, then <code>ListServers</code> returns all of the servers matching the OS name <code>WindowsServer</code>. </p>"}, "groupIdFilter": {"shape": "GroupIds", "documentation": "<p> Specifies the group ID to filter on. </p>"}, "maxResults": {"shape": "MaxResult", "documentation": "<p> The maximum number of items to include in the response. The maximum value is 100. </p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p> The token from a previous call that you use to retrieve the next set of results. For example, if a previous call to this action returned 100 items, but you set <code>maxResults</code> to 10. You'll receive a set of 10 results along with a token. You then use the returned token to retrieve the next set of 10. </p>"}, "serverCriteria": {"shape": "ServerCriteria", "documentation": "<p> Criteria for filtering servers. </p>"}, "sort": {"shape": "SortOrder", "documentation": "<p> Specifies whether to sort by ascending (<code>ASC</code>) or descending (<code>DESC</code>) order. </p>"}}}, "ListServersResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p> The token you use to retrieve the next set of results, or null if there are no more results. </p>"}, "serverInfos": {"shape": "ServerDetails", "documentation": "<p> The list of servers with detailed information about each server. </p>"}}}, "ListStrategySummary": {"type": "list", "member": {"shape": "StrategySummary"}}, "Location": {"type": "string", "max": 128, "min": 1, "pattern": ".*\\S.*"}, "MacAddress": {"type": "string", "max": 17, "min": 0, "pattern": "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})|([0-9a-fA-F]{4}\\\\.[0-9a-fA-F]{4}\\\\.[0-9a-fA-F]{4})$”$"}, "ManagementPreference": {"type": "structure", "members": {"awsManagedResources": {"shape": "AwsManagedResources", "documentation": "<p> Indicates interest in solutions that are managed by AWS. </p>"}, "noPreference": {"shape": "NoManagementPreference", "documentation": "<p> No specific preference. </p>"}, "selfManageResources": {"shape": "SelfManageResources", "documentation": "<p> Indicates interest in managing your own resources on AWS. </p>"}}, "documentation": "<p> Preferences for migrating an application to AWS. </p>", "union": true}, "MaxResult": {"type": "integer", "box": true}, "NetMask": {"type": "string", "max": 1024, "min": 0, "pattern": ".*"}, "NetworkInfo": {"type": "structure", "required": ["interfaceName", "ip<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "netMask"], "members": {"interfaceName": {"shape": "InterfaceName", "documentation": "<p> Information about the name of the interface of the server for which the assessment was run. </p>"}, "ipAddress": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p> Information about the IP address of the server for which the assessment was run. </p>"}, "macAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p> Information about the MAC address of the server for which the assessment was run. </p>"}, "netMask": {"shape": "NetMask", "documentation": "<p> Information about the subnet mask of the server for which the assessment was run. </p>"}}, "documentation": "<p> Information about the server's network for which the assessment was run. </p>"}, "NetworkInfoList": {"type": "list", "member": {"shape": "NetworkInfo"}}, "NextToken": {"type": "string", "max": 2048, "min": 0, "pattern": ".*\\S.*"}, "NoDatabaseMigrationPreference": {"type": "structure", "required": ["targetDatabaseEngine"], "members": {"targetDatabaseEngine": {"shape": "TargetDatabaseEngines", "documentation": "<p> The target database engine for database migration preference that you specify. </p>"}}, "documentation": "<p> The object containing details about database migration preferences, when you have no particular preference. </p>"}, "NoManagementPreference": {"type": "structure", "required": ["targetDestination"], "members": {"targetDestination": {"shape": "NoPreferenceTargetDestinations", "documentation": "<p> The choice of application destination that you specify. </p>"}}, "documentation": "<p> Object containing the choice of application destination that you specify. </p>"}, "NoPreferenceTargetDestination": {"type": "string", "enum": ["None specified", "AWS Elastic BeanStalk", "AWS Fargate", "Amazon Elastic Cloud Compute (EC2)", "Amazon Elastic Container Service (ECS)", "Amazon Elastic Kubernetes Service (EKS)"]}, "NoPreferenceTargetDestinations": {"type": "list", "member": {"shape": "NoPreferenceTargetDestination"}, "max": 1, "min": 1}, "OSInfo": {"type": "structure", "members": {"type": {"shape": "OSType", "documentation": "<p> Information about the type of operating system. </p>"}, "version": {"shape": "OSVersion", "documentation": "<p> Information about the version of operating system. </p>"}}, "documentation": "<p> Information about the operating system. </p>"}, "OSType": {"type": "string", "enum": ["LINUX", "WINDOWS"]}, "OSVersion": {"type": "string", "max": 64, "min": 1, "pattern": ".*\\S.*"}, "OutputFormat": {"type": "string", "enum": ["Excel", "Json"]}, "PipelineInfo": {"type": "structure", "members": {"pipelineConfigurationTimeStamp": {"shape": "String", "documentation": "<p>The time when the pipeline info was configured.</p>"}, "pipelineType": {"shape": "PipelineType", "documentation": "<p>The type of pipeline.</p>"}}, "documentation": "<p>Detailed information of the pipeline.</p>"}, "PipelineInfoList": {"type": "list", "member": {"shape": "PipelineInfo"}}, "PipelineType": {"type": "string", "enum": ["AZURE_DEVOPS"]}, "PrioritizeBusinessGoals": {"type": "structure", "members": {"businessGoals": {"shape": "BusinessGoals", "documentation": "<p> Rank of business goals based on priority. </p>"}}, "documentation": "<p> Rank of business goals based on priority. </p>"}, "ProjectName": {"type": "string", "max": 128, "min": 1, "pattern": ".*\\S.*"}, "PutPortfolioPreferencesRequest": {"type": "structure", "members": {"applicationMode": {"shape": "ApplicationMode", "documentation": "<p>The classification for application component types.</p>"}, "applicationPreferences": {"shape": "ApplicationPreferences", "documentation": "<p> The transformation preferences for non-database applications. </p>"}, "databasePreferences": {"shape": "DatabasePreferences", "documentation": "<p> The transformation preferences for database applications. </p>"}, "prioritizeBusinessGoals": {"shape": "PrioritizeBusinessGoals", "documentation": "<p> The rank of the business goals based on priority. </p>"}}}, "PutPortfolioPreferencesResponse": {"type": "structure", "members": {}}, "RecommendationReportDetails": {"type": "structure", "members": {"completionTime": {"shape": "RecommendationReportTimeStamp", "documentation": "<p> The time that the recommendation report generation task completes. </p>"}, "s3Bucket": {"shape": "String", "documentation": "<p> The S3 bucket where the report file is located. </p>"}, "s3Keys": {"shape": "S3Keys", "documentation": "<p> The Amazon S3 key name of the report file. </p>"}, "startTime": {"shape": "RecommendationReportTimeStamp", "documentation": "<p> The time that the recommendation report generation task starts. </p>"}, "status": {"shape": "RecommendationReportStatus", "documentation": "<p> The status of the recommendation report generation task. </p>"}, "statusMessage": {"shape": "RecommendationReportStatusMessage", "documentation": "<p> The status message for recommendation report generation. </p>"}}, "documentation": "<p> Contains detailed information about a recommendation report. </p>"}, "RecommendationReportStatus": {"type": "string", "enum": ["FAILED", "IN_PROGRESS", "SUCCESS"]}, "RecommendationReportStatusMessage": {"type": "string", "max": 512, "min": 0, "pattern": ".*\\S.*"}, "RecommendationReportTimeStamp": {"type": "timestamp"}, "RecommendationSet": {"type": "structure", "members": {"strategy": {"shape": "Strategy", "documentation": "<p> The recommended strategy. </p>"}, "targetDestination": {"shape": "TargetDestination", "documentation": "<p> The recommended target destination. </p>"}, "transformationTool": {"shape": "TransformationTool", "documentation": "<p> The target destination for the recommendation set. </p>"}}, "documentation": "<p> Contains a recommendation set. </p>"}, "RecommendationTaskId": {"type": "string", "max": 52, "min": 0, "pattern": "[0-9a-z-:]+"}, "RemoteSourceCodeAnalysisServerInfo": {"type": "structure", "members": {"remoteSourceCodeAnalysisServerConfigurationTimestamp": {"shape": "String", "documentation": "<p>The time when the remote source code server was configured.</p>"}}, "documentation": "<p>Information about the server configured for source code analysis.</p>"}, "ResourceId": {"type": "string", "max": 44, "min": 0, "pattern": "^[0-9a-b]+"}, "ResourceName": {"type": "string"}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p> The specified ID in the request is not found. </p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "ResourceSubType": {"type": "string", "enum": ["Database", "Process", "DatabaseProcess"]}, "Result": {"type": "structure", "members": {"analysisStatus": {"shape": "AnalysisStatusUnion", "documentation": "<p>The error in server analysis.</p>"}, "analysisType": {"shape": "AnalysisType", "documentation": "<p>The error in server analysis.</p>"}, "antipatternReportResultList": {"shape": "AntipatternReportResultList", "documentation": "<p>The error in server analysis.</p>"}, "statusMessage": {"shape": "StatusMessage", "documentation": "<p>The error in server analysis.</p>"}}, "documentation": "<p>The error in server analysis.</p>"}, "ResultList": {"type": "list", "member": {"shape": "Result"}}, "RunTimeAnalyzerName": {"type": "string", "enum": ["A2C_ANALYZER", "REHOST_ANALYZER", "EMP_PA_ANALYZER", "DATABASE_ANALYZER", "SCT_ANALYZER"]}, "RunTimeAssessmentStatus": {"type": "string", "enum": ["dataCollectionTaskToBeScheduled", "dataCollectionTaskScheduled", "dataCollectionTaskStarted", "dataCollectionTaskStopped", "dataCollectionTaskSuccess", "dataCollectionTaskFailed", "dataCollectionTaskPartialSuccess"]}, "RuntimeAnalysisStatus": {"type": "string", "enum": ["ANALYSIS_TO_BE_SCHEDULED", "ANALYSIS_STARTED", "ANALYSIS_SUCCESS", "ANALYSIS_FAILED"]}, "S3Bucket": {"type": "string", "max": 63, "min": 0, "pattern": "[0-9a-z]+[0-9a-z\\.\\-]*[0-9a-z]+"}, "S3Key": {"type": "string", "max": 1024, "min": 0, "pattern": ".*\\S.*"}, "S3Keys": {"type": "list", "member": {"shape": "String"}}, "S3Object": {"type": "structure", "members": {"s3Bucket": {"shape": "S3Bucket", "documentation": "<p> The S3 bucket name. </p>"}, "s3key": {"shape": "S3Key", "documentation": "<p> The Amazon S3 key name. </p>"}}, "documentation": "<p> Contains the S3 bucket name and the Amazon S3 key name. </p>"}, "SecretsManagerKey": {"type": "string", "max": 512, "min": 1, "pattern": ".*", "sensitive": true}, "SelfManageResources": {"type": "structure", "required": ["targetDestination"], "members": {"targetDestination": {"shape": "SelfManageTargetDestinations", "documentation": "<p> Self-managed resources target destination. </p>"}}, "documentation": "<p> Self-managed resources. </p>"}, "SelfManageTargetDestination": {"type": "string", "enum": ["None specified", "Amazon Elastic Cloud Compute (EC2)", "Amazon Elastic Container Service (ECS)", "Amazon Elastic Kubernetes Service (EKS)"]}, "SelfManageTargetDestinations": {"type": "list", "member": {"shape": "SelfManageTargetDestination"}, "max": 1, "min": 1}, "ServerCriteria": {"type": "string", "enum": ["NOT_DEFINED", "OS_NAME", "STRATEGY", "DESTINATION", "SERVER_ID", "ANALYSIS_STATUS", "ERROR_CATEGORY"]}, "ServerDetail": {"type": "structure", "members": {"antipatternReportS3Object": {"shape": "S3Object", "documentation": "<p> The S3 bucket name and Amazon S3 key name for anti-pattern report. </p>"}, "antipatternReportStatus": {"shape": "AntipatternReportStatus", "documentation": "<p> The status of the anti-pattern report generation. </p>"}, "antipatternReportStatusMessage": {"shape": "StatusMessage", "documentation": "<p> A message about the status of the anti-pattern report generation. </p>"}, "applicationComponentStrategySummary": {"shape": "ListStrategySummary", "documentation": "<p> A list of strategy summaries. </p>"}, "dataCollectionStatus": {"shape": "RunTimeAssessmentStatus", "documentation": "<p> The status of assessment for the server. </p>"}, "id": {"shape": "ResourceId", "documentation": "<p> The server ID. </p>"}, "lastAnalyzedTimestamp": {"shape": "TimeStamp", "documentation": "<p> The timestamp of when the server was assessed. </p>"}, "listAntipatternSeveritySummary": {"shape": "ListAntipatternSeveritySummary", "documentation": "<p> A list of anti-pattern severity summaries. </p>"}, "name": {"shape": "ResourceName", "documentation": "<p> The name of the server. </p>"}, "recommendationSet": {"shape": "RecommendationSet", "documentation": "<p> A set of recommendations. </p>"}, "serverError": {"shape": "ServerError", "documentation": "<p>The error in server analysis.</p>"}, "serverType": {"shape": "String", "documentation": "<p> The type of server. </p>"}, "statusMessage": {"shape": "StatusMessage", "documentation": "<p> A message about the status of data collection, which contains detailed descriptions of any error messages. </p>"}, "systemInfo": {"shape": "SystemInfo", "documentation": "<p> System information about the server. </p>"}}, "documentation": "<p> Detailed information about a server. </p>"}, "ServerDetails": {"type": "list", "member": {"shape": "ServerDetail"}}, "ServerError": {"type": "structure", "members": {"serverErrorCategory": {"shape": "ServerErrorCategory", "documentation": "<p>The error category of server analysis.</p>"}}, "documentation": "<p>The error in server analysis.</p>"}, "ServerErrorCategory": {"type": "string", "enum": ["CONNECTIVITY_ERROR", "CREDENTIAL_ERROR", "PERMISSION_ERROR", "ARCHITECTURE_ERROR", "OTHER_ERROR"]}, "ServerId": {"type": "string", "max": 27, "min": 1, "pattern": ".*\\S.*"}, "ServerOsType": {"type": "string", "enum": ["WindowsServer", "AmazonLinux", "EndOfSupportWindowsServer", "Redhat", "Other"]}, "ServerStatusSummary": {"type": "structure", "members": {"count": {"shape": "Integer", "documentation": "<p>The number of servers successfully analyzed, partially successful or failed analysis.</p>"}, "runTimeAssessmentStatus": {"shape": "RunTimeAssessmentStatus", "documentation": "<p>The status of the run time.</p>"}}, "documentation": "<p>The status summary of the server analysis.</p>"}, "ServerStrategies": {"type": "list", "member": {"shape": "ServerStrategy"}}, "ServerStrategy": {"type": "structure", "members": {"isPreferred": {"shape": "Boolean", "documentation": "<p> Set to true if the recommendation is set as preferred. </p>"}, "numberOfApplicationComponents": {"shape": "Integer", "documentation": "<p> The number of application components with this strategy recommendation running on the server. </p>"}, "recommendation": {"shape": "RecommendationSet", "documentation": "<p> Strategy recommendation for the server. </p>"}, "status": {"shape": "StrategyRecommendation", "documentation": "<p> The recommendation status of the strategy for the server. </p>"}}, "documentation": "<p> Contains information about a strategy recommendation for a server. </p>"}, "ServerSummary": {"type": "structure", "members": {"ServerOsType": {"shape": "ServerOsType", "documentation": "<p> Type of operating system for the servers. </p>"}, "count": {"shape": "Integer", "documentation": "<p> Number of servers. </p>"}}, "documentation": "<p> Object containing details about the servers imported by Application Discovery Service </p>"}, "ServiceLinkedRoleLockClientException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p> Exception to indicate that the service-linked role (SLR) is locked. </p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "ServiceQuotaExceededException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p> The AWS account has reached its quota of imports. Contact AWS Support to increase the quota for this account. </p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "Severity": {"type": "string", "enum": ["HIGH", "MEDIUM", "LOW"]}, "SortOrder": {"type": "string", "enum": ["ASC", "DESC"]}, "SourceCode": {"type": "structure", "members": {"location": {"shape": "Location", "documentation": "<p> The repository name for the source code. </p>"}, "projectName": {"shape": "ProjectName", "documentation": "<p>The name of the project.</p>"}, "sourceVersion": {"shape": "SourceVersion", "documentation": "<p> The branch of the source code. </p>"}, "versionControl": {"shape": "VersionControl", "documentation": "<p> The type of repository to use for the source code. </p>"}}, "documentation": "<p> Object containing source code information that is linked to an application component. </p>"}, "SourceCodeAnalyzerName": {"type": "string", "enum": ["CSHARP_ANALYZER", "JAVA_ANALYZER", "BYTECODE_ANALYZER", "PORTING_ASSISTANT"]}, "SourceCodeList": {"type": "list", "member": {"shape": "SourceCode"}}, "SourceCodeRepositories": {"type": "list", "member": {"shape": "SourceCodeRepository"}}, "SourceCodeRepository": {"type": "structure", "members": {"branch": {"shape": "String", "documentation": "<p> The branch of the source code. </p>"}, "projectName": {"shape": "String", "documentation": "<p>The name of the project.</p>"}, "repository": {"shape": "String", "documentation": "<p> The repository name for the source code. </p>"}, "versionControlType": {"shape": "String", "documentation": "<p> The type of repository to use for the source code. </p>"}}, "documentation": "<p> Object containing source code information that is linked to an application component. </p>"}, "SourceVersion": {"type": "string", "max": 40, "min": 1, "pattern": ".*\\S.*"}, "SrcCodeOrDbAnalysisStatus": {"type": "string", "enum": ["ANALYSIS_TO_BE_SCHEDULED", "ANALYSIS_STARTED", "ANALYSIS_SUCCESS", "ANALYSIS_FAILED", "ANALYSIS_PARTIAL_SUCCESS", "UNCONFIGURED", "CONFIGURED"]}, "StartAssessmentRequest": {"type": "structure", "members": {"assessmentDataSourceType": {"shape": "AssessmentDataSourceType", "documentation": "<p>The data source type of an assessment to be started.</p>"}, "assessmentTargets": {"shape": "AssessmentTargets", "documentation": "<p>List of criteria for assessment.</p>"}, "s3bucketForAnalysisData": {"shape": "StartAssessmentRequestS3bucketForAnalysisDataString", "documentation": "<p> The S3 bucket used by the collectors to send analysis data to the service. The bucket name must begin with <code>migrationhub-strategy-</code>. </p>"}, "s3bucketForReportData": {"shape": "StartAssessmentRequestS3bucketForReportDataString", "documentation": "<p> The S3 bucket where all the reports generated by the service are stored. The bucket name must begin with <code>migrationhub-strategy-</code>. </p>"}}}, "StartAssessmentRequestS3bucketForAnalysisDataString": {"type": "string", "max": 63, "min": 0, "pattern": "[0-9a-z]+[0-9a-z\\.\\-]*[0-9a-z]+"}, "StartAssessmentRequestS3bucketForReportDataString": {"type": "string", "max": 63, "min": 0, "pattern": "[0-9a-z]+[0-9a-z\\.\\-]*[0-9a-z]+"}, "StartAssessmentResponse": {"type": "structure", "members": {"assessmentId": {"shape": "AsyncTaskId", "documentation": "<p> The ID of the assessment. </p>"}}}, "StartImportFileTaskRequest": {"type": "structure", "required": ["S3Bucket", "name", "s3key"], "members": {"S3Bucket": {"shape": "importS3Bucket", "documentation": "<p> The S3 bucket where the import file is located. The bucket name is required to begin with <code>migrationhub-strategy-</code>.</p>"}, "dataSourceType": {"shape": "DataSourceType", "documentation": "<p>Specifies the source that the servers are coming from. By default, Strategy Recommendations assumes that the servers specified in the import file are available in AWS Application Discovery Service. </p>"}, "groupId": {"shape": "GroupIds", "documentation": "<p>Groups the resources in the import file together with a unique name. This ID can be as filter in <code>ListApplicationComponents</code> and <code>ListServers</code>. </p>"}, "name": {"shape": "StartImportFileTaskRequestNameString", "documentation": "<p> A descriptive name for the request. </p>"}, "s3bucketForReportData": {"shape": "StartImportFileTaskRequestS3bucketForReportDataString", "documentation": "<p> The S3 bucket where Strategy Recommendations uploads import results. The bucket name is required to begin with migrationhub-strategy-. </p>"}, "s3key": {"shape": "String", "documentation": "<p> The Amazon S3 key name of the import file. </p>"}}}, "StartImportFileTaskRequestNameString": {"type": "string", "max": 50, "min": 1, "pattern": "[a-zA-Z0-9_-]+"}, "StartImportFileTaskRequestS3bucketForReportDataString": {"type": "string", "max": 63, "min": 0, "pattern": "[0-9a-z]+[0-9a-z\\.\\-]*[0-9a-z]+"}, "StartImportFileTaskResponse": {"type": "structure", "members": {"id": {"shape": "String", "documentation": "<p> The ID for a specific import task. The ID is unique within an AWS account. </p>"}}}, "StartRecommendationReportGenerationRequest": {"type": "structure", "members": {"groupIdFilter": {"shape": "GroupIds", "documentation": "<p> Groups the resources in the recommendation report with a unique name. </p>"}, "outputFormat": {"shape": "OutputFormat", "documentation": "<p> The output format for the recommendation report file. The default format is Microsoft Excel. </p>"}}}, "StartRecommendationReportGenerationResponse": {"type": "structure", "members": {"id": {"shape": "RecommendationTaskId", "documentation": "<p> The ID of the recommendation report generation task. </p>"}}}, "StatusMessage": {"type": "string", "max": 1024, "min": 0, "pattern": ".*\\S.*"}, "StopAssessmentRequest": {"type": "structure", "required": ["assessmentId"], "members": {"assessmentId": {"shape": "AsyncTaskId", "documentation": "<p> The <code>assessmentId</code> returned by <a>StartAssessment</a>. </p>"}}}, "StopAssessmentResponse": {"type": "structure", "members": {}}, "Strategy": {"type": "string", "enum": ["Rehost", "Retirement", "Refa<PERSON>", "Replatform", "<PERSON><PERSON>", "Relocate", "Repurchase"]}, "StrategyOption": {"type": "structure", "members": {"isPreferred": {"shape": "Boolean", "documentation": "<p> Indicates if a specific strategy is preferred for the application component. </p>"}, "strategy": {"shape": "Strategy", "documentation": "<p> Type of transformation. For example, Rehost, Replatform, and so on. </p>"}, "targetDestination": {"shape": "TargetDestination", "documentation": "<p> Destination information about where the application component can migrate to. For example, <code>EC2</code>, <code>ECS</code>, and so on. </p>"}, "toolName": {"shape": "TransformationToolName", "documentation": "<p> The name of the tool that can be used to transform an application component using this strategy. </p>"}}, "documentation": "<p> Information about all the available strategy options for migrating and modernizing an application component. </p>"}, "StrategyRecommendation": {"type": "string", "enum": ["recommended", "viableOption", "notRecommended", "potential"]}, "StrategySummary": {"type": "structure", "members": {"count": {"shape": "Integer", "documentation": "<p> The count of recommendations per strategy. </p>"}, "strategy": {"shape": "Strategy", "documentation": "<p> The name of recommended strategy. </p>"}}, "documentation": "<p> Object containing the summary of the strategy recommendations. </p>"}, "String": {"type": "string", "max": 1024, "min": 0, "pattern": ".*\\S.*"}, "SystemInfo": {"type": "structure", "members": {"cpuArchitecture": {"shape": "String", "documentation": "<p> CPU architecture type for the server. </p>"}, "fileSystemType": {"shape": "String", "documentation": "<p> File system type for the server. </p>"}, "networkInfoList": {"shape": "NetworkInfoList", "documentation": "<p> Networking information related to a server. </p>"}, "osInfo": {"shape": "OSInfo", "documentation": "<p> Operating system corresponding to a server. </p>"}}, "documentation": "<p> Information about the server that hosts application components. </p>"}, "TargetDatabaseEngine": {"type": "string", "enum": ["None specified", "Amazon Aurora", "AWS PostgreSQL", "MySQL", "Microsoft SQL Server", "Oracle Database", "MariaDB", "SAP", "Db2 LUW", "MongoDB"]}, "TargetDatabaseEngines": {"type": "list", "member": {"shape": "TargetDatabaseEngine"}, "max": 1, "min": 1}, "TargetDestination": {"type": "string", "enum": ["None specified", "AWS Elastic BeanStalk", "AWS Fargate", "Amazon Elastic Cloud Compute (EC2)", "Amazon Elastic Container Service (ECS)", "Amazon Elastic Kubernetes Service (EKS)", "Aurora MySQL", "Aurora PostgreSQL", "Amazon Relational Database Service on MySQL", "Amazon Relational Database Service on PostgreSQL", "Amazon DocumentDB", "Amazon DynamoDB", "Amazon Relational Database Service", "Babelfish for Aurora PostgreSQL"]}, "ThrottlingException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p> The request was denied due to request throttling. </p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true}, "TimeStamp": {"type": "timestamp"}, "TranformationToolDescription": {"type": "string", "max": 1024, "min": 1, "pattern": ".*\\S.*"}, "TranformationToolInstallationLink": {"type": "string", "max": 1024, "min": 1, "pattern": ".*\\S.*"}, "TransformationTool": {"type": "structure", "members": {"description": {"shape": "TranformationToolDescription", "documentation": "<p> Description of the tool. </p>"}, "name": {"shape": "TransformationToolName", "documentation": "<p> Name of the tool. </p>"}, "tranformationToolInstallationLink": {"shape": "TranformationToolInstallationLink", "documentation": "<p> URL for installing the tool. </p>"}}, "documentation": "<p> Information of the transformation tool that can be used to migrate and modernize the application. </p>"}, "TransformationToolName": {"type": "string", "enum": ["App2Container", "Porting Assistant For .NET", "End of Support Migration", "Windows Web Application Migration Assistant", "Application Migration Service", "Strategy Recommendation Support", "In Place Operating System Upgrade", "Schema Conversion Tool", "Database Migration Service", "Native SQL Server Backup/Restore"]}, "UpdateApplicationComponentConfigRequest": {"type": "structure", "required": ["applicationComponentId"], "members": {"appType": {"shape": "AppType", "documentation": "<p>The type of known component.</p>"}, "applicationComponentId": {"shape": "ApplicationComponentId", "documentation": "<p> The ID of the application component. The ID is unique within an AWS account. </p>"}, "configureOnly": {"shape": "Boolean", "documentation": "<p>Update the configuration request of an application component. If it is set to true, the source code and/or database credentials are updated. If it is set to false, the source code and/or database credentials are updated and an analysis is initiated.</p>"}, "inclusionStatus": {"shape": "InclusionStatus", "documentation": "<p> Indicates whether the application component has been included for server recommendation or not. </p>"}, "secretsManagerKey": {"shape": "SecretsManager<PERSON>ey", "documentation": "<p> Database credentials. </p>"}, "sourceCodeList": {"shape": "SourceCodeList", "documentation": "<p> The list of source code configurations to update for the application component. </p>"}, "strategyOption": {"shape": "StrategyOption", "documentation": "<p> The preferred strategy options for the application component. Use values from the <a>GetApplicationComponentStrategies</a> response. </p>"}}}, "UpdateApplicationComponentConfigResponse": {"type": "structure", "members": {}}, "UpdateServerConfigRequest": {"type": "structure", "required": ["serverId"], "members": {"serverId": {"shape": "ServerId", "documentation": "<p> The ID of the server. </p>"}, "strategyOption": {"shape": "StrategyOption", "documentation": "<p> The preferred strategy options for the application component. See the response from <a>GetServerStrategies</a>.</p>"}}}, "UpdateServerConfigResponse": {"type": "structure", "members": {}}, "ValidationException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p> The request body isn't valid. </p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "VcenterBasedRemoteInfo": {"type": "structure", "members": {"osType": {"shape": "OSType", "documentation": "<p>The type of the operating system.</p>"}, "vcenterConfigurationTimeStamp": {"shape": "String", "documentation": "<p>The time when the remote server based on vCenter was last configured.</p>"}}, "documentation": "<p>Details about the server in vCenter.</p>"}, "VcenterBasedRemoteInfoList": {"type": "list", "member": {"shape": "VcenterBasedRemoteInfo"}}, "VersionControl": {"type": "string", "enum": ["GITHUB", "GITHUB_ENTERPRISE", "AZURE_DEVOPS_GIT"]}, "VersionControlInfo": {"type": "structure", "members": {"versionControlConfigurationTimeStamp": {"shape": "String", "documentation": "<p>The time when the version control system was last configured.</p>"}, "versionControlType": {"shape": "VersionControlType", "documentation": "<p>The type of version control.</p>"}}, "documentation": "<p>Details about the version control configuration.</p>"}, "VersionControlInfoList": {"type": "list", "member": {"shape": "VersionControlInfo"}}, "VersionControlType": {"type": "string", "enum": ["GITHUB", "GITHUB_ENTERPRISE", "AZURE_DEVOPS_GIT"]}, "errorMessage": {"type": "string"}, "importS3Bucket": {"type": "string", "max": 63, "min": 0, "pattern": "[0-9a-z]+[0-9a-z\\.\\-]*[0-9a-z]+"}, "importS3Key": {"type": "string", "max": 1024, "min": 0, "pattern": ".*\\S.*"}}, "documentation": "<p><fullname>Migration Hub Strategy Recommendations</fullname> <p>This API reference provides descriptions, syntax, and other details about each of the actions and data types for Migration Hub Strategy Recommendations (Strategy Recommendations). The topic for each action shows the API request parameters and the response. Alternatively, you can use one of the AWS SDKs to access an API that is tailored to the programming language or platform that you're using. For more information, see <a href=\"http://aws.amazon.com/tools/#SDKs\">AWS SDKs</a>.</p></p>"}