{"version": "2.0", "metadata": {"apiVersion": "2019-12-02", "endpointPrefix": "route53-recovery-cluster", "jsonVersion": "1.0", "protocol": "json", "serviceFullName": "Route53 Recovery Cluster", "serviceId": "Route53 Recovery Cluster", "signatureVersion": "v4", "signingName": "route53-recovery-cluster", "targetPrefix": "ToggleCustomerAPI", "uid": "route53-recovery-cluster-2019-12-02"}, "operations": {"GetRoutingControlState": {"name": "GetRoutingControlState", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetRoutingControlStateRequest"}, "output": {"shape": "GetRoutingControlStateResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "EndpointTemporarilyUnavailableException"}], "documentation": "<p>Get the state for a routing control. A routing control is a simple on/off switch that you can use to route traffic to cells. When a routing control state is set to ON, traffic flows to a cell. When the state is set to OFF, traffic does not flow. </p> <p>Before you can create a routing control, you must first create a cluster, and then host the control in a control panel on the cluster. For more information, see <a href=\"https://docs.aws.amazon.com/r53recovery/latest/dg/routing-control.create.html\"> Create routing control structures</a> in the Amazon Route 53 Application Recovery Controller Developer Guide. You access one of the endpoints for the cluster to get or update the routing control state to redirect traffic for your application. </p> <p> <i>You must specify Regional endpoints when you work with API cluster operations to get or update routing control states in Route 53 ARC.</i> </p> <p>To see a code example for getting a routing control state, including accessing Regional cluster endpoints in sequence, see <a href=\"https://docs.aws.amazon.com/r53recovery/latest/dg/service_code_examples_actions.html\">API examples</a> in the Amazon Route 53 Application Recovery Controller Developer Guide.</p> <p>Learn more about working with routing controls in the following topics in the Amazon Route 53 Application Recovery Controller Developer Guide:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/r53recovery/latest/dg/routing-control.update.html\"> Viewing and updating routing control states</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/r53recovery/latest/dg/routing-control.html\">Working with routing controls in Route 53 ARC</a> </p> </li> </ul>"}, "ListRoutingControls": {"name": "ListRoutingControls", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListRoutingControlsRequest"}, "output": {"shape": "ListRoutingControlsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "EndpointTemporarilyUnavailableException"}], "documentation": "<p>List routing control names and Amazon Resource Names (ARNs), as well as the routing control state for each routing control, along with the control panel name and control panel ARN for the routing controls. If you specify a control panel ARN, this call lists the routing controls in the control panel. Otherwise, it lists all the routing controls in the cluster.</p> <p>A routing control is a simple on/off switch in Route 53 ARC that you can use to route traffic to cells. When a routing control state is set to ON, traffic flows to a cell. When the state is set to OFF, traffic does not flow.</p> <p>Before you can create a routing control, you must first create a cluster, and then host the control in a control panel on the cluster. For more information, see <a href=\"https://docs.aws.amazon.com/r53recovery/latest/dg/routing-control.create.html\"> Create routing control structures</a> in the Amazon Route 53 Application Recovery Controller Developer Guide. You access one of the endpoints for the cluster to get or update the routing control state to redirect traffic for your application. </p> <p> <i>You must specify Regional endpoints when you work with API cluster operations to use this API operation to list routing controls in Route 53 ARC.</i> </p> <p>Learn more about working with routing controls in the following topics in the Amazon Route 53 Application Recovery Controller Developer Guide:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/r53recovery/latest/dg/routing-control.update.html\"> Viewing and updating routing control states</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/r53recovery/latest/dg/routing-control.html\">Working with routing controls in Route 53 ARC</a> </p> </li> </ul>"}, "UpdateRoutingControlState": {"name": "UpdateRoutingControlState", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateRoutingControlStateRequest"}, "output": {"shape": "UpdateRoutingControlStateResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "EndpointTemporarilyUnavailableException"}, {"shape": "ConflictException"}], "documentation": "<p>Set the state of the routing control to reroute traffic. You can set the value to ON or OFF. When the state is ON, traffic flows to a cell. When the state is OFF, traffic does not flow.</p> <p>With Route 53 ARC, you can add safety rules for routing controls, which are safeguards for routing control state updates that help prevent unexpected outcomes, like fail open traffic routing. However, there are scenarios when you might want to bypass the routing control safeguards that are enforced with safety rules that you've configured. For example, you might want to fail over quickly for disaster recovery, and one or more safety rules might be unexpectedly preventing you from updating a routing control state to reroute traffic. In a \"break glass\" scenario like this, you can override one or more safety rules to change a routing control state and fail over your application.</p> <p>The <code>SafetyRulesToOverride</code> property enables you override one or more safety rules and update routing control states. For more information, see <a href=\"https://docs.aws.amazon.com/r53recovery/latest/dg/routing-control.override-safety-rule.html\"> Override safety rules to reroute traffic</a> in the Amazon Route 53 Application Recovery Controller Developer Guide.</p> <p> <i>You must specify Regional endpoints when you work with API cluster operations to get or update routing control states in Route 53 ARC.</i> </p> <p>To see a code example for getting a routing control state, including accessing Regional cluster endpoints in sequence, see <a href=\"https://docs.aws.amazon.com/r53recovery/latest/dg/service_code_examples_actions.html\">API examples</a> in the Amazon Route 53 Application Recovery Controller Developer Guide.</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/r53recovery/latest/dg/routing-control.update.html\"> Viewing and updating routing control states</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/r53recovery/latest/dg/routing-control.html\">Working with routing controls overall</a> </p> </li> </ul>"}, "UpdateRoutingControlStates": {"name": "UpdateRoutingControlStates", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateRoutingControlStatesRequest"}, "output": {"shape": "UpdateRoutingControlStatesResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "EndpointTemporarilyUnavailableException"}, {"shape": "ConflictException"}, {"shape": "ServiceLimitExceededException"}], "documentation": "<p>Set multiple routing control states. You can set the value for each state to be ON or OFF. When the state is ON, traffic flows to a cell. When it's OFF, traffic does not flow.</p> <p>With Route 53 ARC, you can add safety rules for routing controls, which are safeguards for routing control state updates that help prevent unexpected outcomes, like fail open traffic routing. However, there are scenarios when you might want to bypass the routing control safeguards that are enforced with safety rules that you've configured. For example, you might want to fail over quickly for disaster recovery, and one or more safety rules might be unexpectedly preventing you from updating a routing control state to reroute traffic. In a \"break glass\" scenario like this, you can override one or more safety rules to change a routing control state and fail over your application.</p> <p>The <code>SafetyRulesToOverride</code> property enables you override one or more safety rules and update routing control states. For more information, see <a href=\"https://docs.aws.amazon.com/r53recovery/latest/dg/routing-control.override-safety-rule.html\"> Override safety rules to reroute traffic</a> in the Amazon Route 53 Application Recovery Controller Developer Guide.</p> <p> <i>You must specify Regional endpoints when you work with API cluster operations to get or update routing control states in Route 53 ARC.</i> </p> <p>To see a code example for getting a routing control state, including accessing Regional cluster endpoints in sequence, see <a href=\"https://docs.aws.amazon.com/r53recovery/latest/dg/service_code_examples_actions.html\">API examples</a> in the Amazon Route 53 Application Recovery Controller Developer Guide.</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/r53recovery/latest/dg/routing-control.update.html\"> Viewing and updating routing control states</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/r53recovery/latest/dg/routing-control.html\">Working with routing controls overall</a> </p> </li> </ul>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>You don't have sufficient permissions to perform this action.</p>", "exception": true}, "Arn": {"type": "string", "max": 255, "min": 1, "pattern": "^[A-Za-z0-9:.\\/_-]*$"}, "Arns": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}}, "ConflictException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "String", "documentation": "Description of the ConflictException error"}, "resourceId": {"shape": "String", "documentation": "Identifier of the resource in use"}, "resourceType": {"shape": "String", "documentation": "Type of the resource in use"}}, "documentation": "<p>There was a conflict with this request. Try again.</p>", "exception": true}, "ControlPanelName": {"type": "string", "max": 64, "min": 1, "pattern": "^\\S+$"}, "EndpointTemporarilyUnavailableException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The cluster endpoint isn't available. Try another cluster endpoint.</p>", "exception": true}, "GetRoutingControlStateRequest": {"type": "structure", "required": ["RoutingControlArn"], "members": {"RoutingControlArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) for the routing control that you want to get the state for.</p>"}}}, "GetRoutingControlStateResponse": {"type": "structure", "required": ["RoutingControlArn", "RoutingControlState"], "members": {"RoutingControlArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the response.</p>"}, "RoutingControlState": {"shape": "RoutingControlState", "documentation": "<p>The state of the routing control.</p>"}, "RoutingControlName": {"shape": "RoutingControlName", "documentation": "<p>The routing control name.</p>"}}}, "InternalServerException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "retryAfterSeconds": {"shape": "RetryAfterSeconds"}}, "documentation": "<p>There was an unexpected error during processing of the request.</p>", "exception": true, "fault": true}, "ListRoutingControlsRequest": {"type": "structure", "members": {"ControlPanelArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the control panel of the routing controls to list.</p>"}, "NextToken": {"shape": "PageToken", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of routing controls objects that you want to return with this call. The default value is 500.</p>", "box": true}}}, "ListRoutingControlsResponse": {"type": "structure", "required": ["RoutingControls"], "members": {"RoutingControls": {"shape": "RoutingControls", "documentation": "<p>The list of routing controls.</p>"}, "NextToken": {"shape": "PageToken", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "MaxResults": {"type": "integer", "min": 1}, "Owner": {"type": "string", "max": 1024, "min": 12, "pattern": "^\\S+$"}, "PageToken": {"type": "string", "max": 8096, "min": 1, "pattern": "[\\S]*"}, "ResourceNotFoundException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "String"}, "resourceId": {"shape": "String", "documentation": "Hypothetical resource identifier that was not found"}, "resourceType": {"shape": "String", "documentation": "Hypothetical resource type that was not found"}}, "documentation": "<p>The request references a routing control or control panel that was not found.</p>", "exception": true}, "RetryAfterSeconds": {"type": "integer", "documentation": "Advice to clients on when the call can be safely retried"}, "RoutingControl": {"type": "structure", "members": {"ControlPanelArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the control panel where the routing control is located.</p>"}, "ControlPanelName": {"shape": "ControlPanelName", "documentation": "<p>The name of the control panel where the routing control is located. Only ASCII characters are supported for control panel names.</p>"}, "RoutingControlArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the routing control.</p>"}, "RoutingControlName": {"shape": "RoutingControlName", "documentation": "<p>The name of the routing control.</p>"}, "RoutingControlState": {"shape": "RoutingControlState", "documentation": "<p>The current state of the routing control. When a routing control state is set to ON, traffic flows to a cell. When the state is set to OFF, traffic does not flow. </p>"}, "Owner": {"shape": "Owner", "documentation": "<p>The Amazon Web Services account ID of the routing control owner.</p>"}}, "documentation": "<p>A routing control, which is a simple on/off switch that you can use to route traffic to cells. When a routing control state is set to ON, traffic flows to a cell. When the state is set to OFF, traffic does not flow. </p>"}, "RoutingControlName": {"type": "string", "max": 64, "min": 1, "pattern": "^\\S+$"}, "RoutingControlState": {"type": "string", "enum": ["On", "Off"]}, "RoutingControls": {"type": "list", "member": {"shape": "RoutingControl"}}, "ServiceLimitExceededException": {"type": "structure", "required": ["message", "limitCode", "serviceCode"], "members": {"message": {"shape": "String"}, "resourceId": {"shape": "String", "documentation": "<p>The resource identifier of the limit that was exceeded.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>The resource type of the limit that was exceeded.</p>"}, "limitCode": {"shape": "String", "documentation": "<p>The code of the limit that was exceeded.</p>"}, "serviceCode": {"shape": "String", "documentation": "<p>The service code of the limit that was exceeded.</p>"}}, "documentation": "<p>The request can't update that many routing control states at the same time. Try again with fewer routing control states.</p>", "exception": true}, "String": {"type": "string"}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "retryAfterSeconds": {"shape": "RetryAfterSeconds"}}, "documentation": "<p>The request was denied because of request throttling.</p>", "exception": true}, "UpdateRoutingControlStateEntries": {"type": "list", "member": {"shape": "UpdateRoutingControlStateEntry"}}, "UpdateRoutingControlStateEntry": {"type": "structure", "required": ["RoutingControlArn", "RoutingControlState"], "members": {"RoutingControlArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) for a routing control state entry.</p>"}, "RoutingControlState": {"shape": "RoutingControlState", "documentation": "<p>The routing control state in a set of routing control state entries.</p>"}}, "documentation": "<p>A routing control state entry.</p>"}, "UpdateRoutingControlStateRequest": {"type": "structure", "required": ["RoutingControlArn", "RoutingControlState"], "members": {"RoutingControlArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) for the routing control that you want to update the state for.</p>"}, "RoutingControlState": {"shape": "RoutingControlState", "documentation": "<p>The state of the routing control. You can set the value to ON or OFF.</p>"}, "SafetyRulesToOverride": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Names (ARNs) for the safety rules that you want to override when you're updating the state of a routing control. You can override one safety rule or multiple safety rules by including one or more ARNs, separated by commas.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/r53recovery/latest/dg/routing-control.override-safety-rule.html\"> Override safety rules to reroute traffic</a> in the Amazon Route 53 Application Recovery Controller Developer Guide.</p>"}}}, "UpdateRoutingControlStateResponse": {"type": "structure", "members": {}}, "UpdateRoutingControlStatesRequest": {"type": "structure", "required": ["UpdateRoutingControlStateEntries"], "members": {"UpdateRoutingControlStateEntries": {"shape": "UpdateRoutingControlStateEntries", "documentation": "<p>A set of routing control entries that you want to update.</p>"}, "SafetyRulesToOverride": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Names (ARNs) for the safety rules that you want to override when you're updating routing control states. You can override one safety rule or multiple safety rules by including one or more ARNs, separated by commas.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/r53recovery/latest/dg/routing-control.override-safety-rule.html\"> Override safety rules to reroute traffic</a> in the Amazon Route 53 Application Recovery Controller Developer Guide.</p>"}}}, "UpdateRoutingControlStatesResponse": {"type": "structure", "members": {}}, "ValidationException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "reason": {"shape": "ValidationExceptionReason"}, "fields": {"shape": "ValidationExceptionFieldList"}}, "documentation": "<p>There was a validation error on the request.</p>", "exception": true}, "ValidationExceptionField": {"type": "structure", "required": ["name", "message"], "members": {"name": {"shape": "String", "documentation": "<p>The field that had the validation exception.</p>"}, "message": {"shape": "String", "documentation": "<p>Information about the validation exception.</p>"}}, "documentation": "<p>There was a validation error on the request.</p>"}, "ValidationExceptionFieldList": {"type": "list", "member": {"shape": "ValidationExceptionField"}, "documentation": "The fields that caused the error, if applicable"}, "ValidationExceptionReason": {"type": "string", "documentation": "Reason the request failed validation", "enum": ["unknownOperation", "<PERSON><PERSON><PERSON><PERSON>", "fieldValidationFailed", "other"]}}, "documentation": "<p>Welcome to the Routing Control (Recovery Cluster) API Reference Guide for Amazon Route 53 Application Recovery Controller.</p> <p>With Route 53 ARC, you can use routing control with extreme reliability to recover applications by rerouting traffic across Availability Zones or Amazon Web Services Regions. Routing controls are simple on/off switches hosted on a highly available cluster in Route 53 ARC. A cluster provides a set of five redundant Regional endpoints against which you can run API calls to get or update the state of routing controls. To implement failover, you set one routing control to ON and another one to OFF, to reroute traffic from one Availability Zone or Amazon Web Services Region to another. </p> <p> <i>Be aware that you must specify a Regional endpoint for a cluster when you work with API cluster operations to get or update routing control states in Route 53 ARC.</i> In addition, you must specify the US West (Oregon) Region for Route 53 ARC API calls. For example, use the parameter <code>--region us-west-2</code> with AWS CLI commands. For more information, see <a href=\"https://docs.aws.amazon.com/r53recovery/latest/dg/routing-control.update.api.html\"> Get and update routing control states using the API</a> in the Amazon Route 53 Application Recovery Controller Developer Guide.</p> <p>This API guide includes information about the API operations for how to get and update routing control states in Route 53 ARC. To work with routing control in Route 53 ARC, you must first create the required components (clusters, control panels, and routing controls) using the recovery cluster configuration API.</p> <p>For more information about working with routing control in Route 53 ARC, see the following:</p> <ul> <li> <p>Create clusters, control panels, and routing controls by using API operations. For more information, see the <a href=\"https://docs.aws.amazon.com/recovery-cluster/latest/api/\">Recovery Control Configuration API Reference Guide for Amazon Route 53 Application Recovery Controller</a>.</p> </li> <li> <p>Learn about the components in recovery control, including clusters, routing controls, and control panels, and how to work with Route 53 ARC in the Amazon Web Services console. For more information, see <a href=\"https://docs.aws.amazon.com/r53recovery/latest/dg/introduction-components.html#introduction-components-routing\"> Recovery control components</a> in the Amazon Route 53 Application Recovery Controller Developer Guide.</p> </li> <li> <p>Route 53 ARC also provides readiness checks that continually audit resources to help make sure that your applications are scaled and ready to handle failover traffic. For more information about the related API operations, see the <a href=\"https://docs.aws.amazon.com/recovery-readiness/latest/api/\">Recovery Readiness API Reference Guide for Amazon Route 53 Application Recovery Controller</a>.</p> </li> <li> <p>For more information about creating resilient applications and preparing for recovery readiness with Route 53 ARC, see the <a href=\"https://docs.aws.amazon.com/r53recovery/latest/dg/\">Amazon Route 53 Application Recovery Controller Developer Guide</a>.</p> </li> </ul>"}