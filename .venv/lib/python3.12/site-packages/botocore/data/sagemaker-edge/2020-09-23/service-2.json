{"version": "2.0", "metadata": {"apiVersion": "2020-09-23", "endpointPrefix": "edge.sagemaker", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "Amazon Sagemaker Edge Manager", "serviceId": "Sagemaker Edge", "signatureVersion": "v4", "signingName": "sagemaker", "uid": "sagemaker-edge-2020-09-23"}, "operations": {"GetDeployments": {"name": "GetDeployments", "http": {"method": "POST", "requestUri": "/GetDeployments"}, "input": {"shape": "GetDeploymentsRequest"}, "output": {"shape": "GetDeploymentsResult"}, "errors": [{"shape": "InternalServiceException"}], "documentation": "<p>Use to get the active deployments from a device.</p>"}, "GetDeviceRegistration": {"name": "GetDeviceRegistration", "http": {"method": "POST", "requestUri": "/GetDeviceRegistration"}, "input": {"shape": "GetDeviceRegistrationRequest"}, "output": {"shape": "GetDeviceRegistrationResult"}, "errors": [{"shape": "InternalServiceException"}], "documentation": "<p>Use to check if a device is registered with SageMaker Edge Manager.</p>"}, "SendHeartbeat": {"name": "SendHeartbeat", "http": {"method": "POST", "requestUri": "/SendHeartbeat"}, "input": {"shape": "SendHeartbeatRequest"}, "errors": [{"shape": "InternalServiceException"}], "documentation": "<p>Use to get the current status of devices registered on SageMaker Edge Manager.</p>"}}, "shapes": {"CacheTTLSeconds": {"type": "string", "max": 1000, "min": 1}, "Checksum": {"type": "structure", "members": {"Type": {"shape": "ChecksumType", "documentation": "<p>The type of the checksum.</p>"}, "Sum": {"shape": "ChecksumString", "documentation": "<p>The checksum of the model.</p>"}}, "documentation": "<p>Information about the checksum of a model deployed on a device.</p>"}, "ChecksumString": {"type": "string", "max": 63, "min": 1, "pattern": "^[a-z0-9](-*[a-z0-9])*$"}, "ChecksumType": {"type": "string", "enum": ["SHA1"]}, "Definition": {"type": "structure", "members": {"ModelHandle": {"shape": "EntityName", "documentation": "<p>The unique model handle.</p>"}, "S3Url": {"shape": "S3Uri", "documentation": "<p>The absolute S3 location of the model.</p>"}, "Checksum": {"shape": "Checksum", "documentation": "<p>The checksum information of the model.</p>"}, "State": {"shape": "ModelState", "documentation": "<p>The desired state of the model.</p>"}}, "documentation": "<p/>"}, "Definitions": {"type": "list", "member": {"shape": "Definition"}}, "DeploymentModel": {"type": "structure", "members": {"ModelHandle": {"shape": "EntityName", "documentation": "<p>The unique handle of the model.</p>"}, "ModelName": {"shape": "ModelName", "documentation": "<p>The name of the model.</p>"}, "ModelVersion": {"shape": "Version", "documentation": "<p>The version of the model.</p>"}, "DesiredState": {"shape": "ModelState", "documentation": "<p>The desired state of the model.</p>"}, "State": {"shape": "ModelState", "documentation": "<p>Returns the current state of the model.</p>"}, "Status": {"shape": "DeploymentStatus", "documentation": "<p>Returns the deployment status of the model.</p>"}, "StatusReason": {"shape": "String", "documentation": "<p>Returns the error message for the deployment status result.</p>"}, "RollbackFailureReason": {"shape": "String", "documentation": "<p>Returns the error message if there is a rollback.</p>"}}, "documentation": "<p/>"}, "DeploymentModels": {"type": "list", "member": {"shape": "DeploymentModel"}}, "DeploymentResult": {"type": "structure", "members": {"DeploymentName": {"shape": "EntityName", "documentation": "<p>The name and unique ID of the deployment.</p>"}, "DeploymentStatus": {"shape": "EntityName", "documentation": "<p>Returns the bucket error code.</p>"}, "DeploymentStatusMessage": {"shape": "String", "documentation": "<p>Returns the detailed error message.</p>"}, "DeploymentStartTime": {"shape": "Timestamp", "documentation": "<p>The timestamp of when the deployment was started on the agent.</p>"}, "DeploymentEndTime": {"shape": "Timestamp", "documentation": "<p>The timestamp of when the deployment was ended, and the agent got the deployment results.</p>"}, "DeploymentModels": {"shape": "DeploymentModels", "documentation": "<p>Returns a list of models deployed on the agent.</p>"}}, "documentation": "<p>Information about the result of a deployment on an edge device that is registered with SageMaker Edge Manager.</p>"}, "DeploymentStatus": {"type": "string", "enum": ["SUCCESS", "FAIL"]}, "DeploymentType": {"type": "string", "enum": ["Model"]}, "DeviceFleetName": {"type": "string", "max": 63, "min": 1, "pattern": "^[a-zA-Z0-9](-*_*[a-zA-Z0-9])*$"}, "DeviceName": {"type": "string", "max": 63, "min": 1, "pattern": "^[a-zA-Z0-9](-*_*[a-zA-Z0-9])*$"}, "DeviceRegistration": {"type": "string", "max": 1000, "min": 1}, "Dimension": {"type": "string", "max": 1000, "min": 1, "pattern": "^[a-zA-Z0-9](-*[a-zA-Z0-9\\/])*$"}, "EdgeDeployment": {"type": "structure", "members": {"DeploymentName": {"shape": "EntityName", "documentation": "<p>The name and unique ID of the deployment.</p>"}, "Type": {"shape": "DeploymentType", "documentation": "<p>The type of the deployment.</p>"}, "FailureHandlingPolicy": {"shape": "FailureHandlingPolicy", "documentation": "<p>Determines whether to rollback to previous configuration if deployment fails.</p>"}, "Definitions": {"shape": "Definitions", "documentation": "<p>Returns a list of Definition objects.</p>"}}, "documentation": "<p>Information about a deployment on an edge device that is registered with SageMaker Edge Manager.</p>"}, "EdgeDeployments": {"type": "list", "member": {"shape": "EdgeDeployment"}}, "EdgeMetric": {"type": "structure", "members": {"Dimension": {"shape": "Dimension", "documentation": "<p>The dimension of metrics published.</p>"}, "MetricName": {"shape": "Metric", "documentation": "<p>Returns the name of the metric.</p>"}, "Value": {"shape": "Value", "documentation": "<p>Returns the value of the metric.</p>"}, "Timestamp": {"shape": "Timestamp", "documentation": "<p>Timestamp of when the metric was requested.</p>"}}, "documentation": "<p>Information required for edge device metrics.</p>"}, "EdgeMetrics": {"type": "list", "member": {"shape": "EdgeMetric"}}, "EntityName": {"type": "string", "max": 63, "min": 1, "pattern": "^[a-zA-Z0-9](-*[a-zA-Z0-9])*$"}, "ErrorMessage": {"type": "string"}, "FailureHandlingPolicy": {"type": "string", "enum": ["ROLLBACK_ON_FAILURE", "DO_NOTHING"]}, "GetDeploymentsRequest": {"type": "structure", "required": ["DeviceName", "DeviceFleetName"], "members": {"DeviceName": {"shape": "DeviceName", "documentation": "<p>The unique name of the device you want to get the configuration of active deployments from.</p>"}, "DeviceFleetName": {"shape": "DeviceFleetName", "documentation": "<p>The name of the fleet that the device belongs to.</p>"}}}, "GetDeploymentsResult": {"type": "structure", "members": {"Deployments": {"shape": "EdgeDeployments", "documentation": "<p>Returns a list of the configurations of the active deployments on the device.</p>"}}}, "GetDeviceRegistrationRequest": {"type": "structure", "required": ["DeviceName", "DeviceFleetName"], "members": {"DeviceName": {"shape": "DeviceName", "documentation": "<p>The unique name of the device you want to get the registration status from.</p>"}, "DeviceFleetName": {"shape": "DeviceFleetName", "documentation": "<p>The name of the fleet that the device belongs to.</p>"}}}, "GetDeviceRegistrationResult": {"type": "structure", "members": {"DeviceRegistration": {"shape": "DeviceRegistration", "documentation": "<p>Describes if the device is currently registered with SageMaker Edge Manager.</p>"}, "CacheTTL": {"shape": "CacheTTLSeconds", "documentation": "<p>The amount of time, in seconds, that the registration status is stored on the device’s cache before it is refreshed.</p>"}}}, "InternalServiceException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>An internal failure occurred. Try your request again. If the problem persists, contact Amazon Web Services customer support.</p>", "exception": true}, "Metric": {"type": "string", "max": 100, "min": 4, "pattern": "^[a-zA-Z0-9](-*[a-zA-Z0-9])*$"}, "Model": {"type": "structure", "members": {"ModelName": {"shape": "ModelName", "documentation": "<p>The name of the model.</p>"}, "ModelVersion": {"shape": "Version", "documentation": "<p>The version of the model.</p>"}, "LatestSampleTime": {"shape": "Timestamp", "documentation": "<p>The timestamp of the last data sample taken.</p>"}, "LatestInference": {"shape": "Timestamp", "documentation": "<p>The timestamp of the last inference that was made.</p>"}, "ModelMetrics": {"shape": "EdgeMetrics", "documentation": "<p>Information required for model metrics.</p>"}}, "documentation": "<p>Information about a model deployed on an edge device that is registered with SageMaker Edge Manager.</p>"}, "ModelName": {"type": "string", "max": 255, "min": 4, "pattern": "^[a-zA-Z0-9](-*[a-zA-Z0-9])*$"}, "ModelState": {"type": "string", "enum": ["DEPLOY", "UNDEPLOY"]}, "Models": {"type": "list", "member": {"shape": "Model"}}, "S3Uri": {"type": "string", "max": 1024, "pattern": "^s3://([^/]+)/?(.*)$"}, "SendHeartbeatRequest": {"type": "structure", "required": ["AgentVersion", "DeviceName", "DeviceFleetName"], "members": {"AgentMetrics": {"shape": "EdgeMetrics", "documentation": "<p>For internal use. Returns a list of SageMaker Edge Manager agent operating metrics.</p>"}, "Models": {"shape": "Models", "documentation": "<p>Returns a list of models deployed on the the device.</p>"}, "AgentVersion": {"shape": "Version", "documentation": "<p>Returns the version of the agent.</p>"}, "DeviceName": {"shape": "DeviceName", "documentation": "<p>The unique name of the device.</p>"}, "DeviceFleetName": {"shape": "DeviceFleetName", "documentation": "<p>The name of the fleet that the device belongs to.</p>"}, "DeploymentResult": {"shape": "DeploymentResult", "documentation": "<p>Returns the result of a deployment on the device.</p>"}}}, "String": {"type": "string"}, "Timestamp": {"type": "timestamp"}, "Value": {"type": "double"}, "Version": {"type": "string", "max": 64, "min": 1, "pattern": "[a-zA-Z0-9\\ \\_\\.]+"}}, "documentation": "<p>SageMaker Edge Manager dataplane service for communicating with active agents.</p>"}