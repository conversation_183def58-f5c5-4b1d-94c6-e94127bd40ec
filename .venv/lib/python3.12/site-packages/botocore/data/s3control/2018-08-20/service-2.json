{"version": "2.0", "metadata": {"apiVersion": "2018-08-20", "endpointPrefix": "s3-control", "protocol": "rest-xml", "serviceFullName": "AWS S3 Control", "serviceId": "S3 Control", "signatureVersion": "s3v4", "signingName": "s3", "uid": "s3control-2018-08-20"}, "operations": {"CreateAccessPoint": {"name": "CreateAccessPoint", "http": {"method": "PUT", "requestUri": "/v20180820/accesspoint/{name}"}, "input": {"shape": "CreateAccessPointRequest", "locationName": "CreateAccessPointRequest", "xmlNamespace": {"uri": "http://awss3control.amazonaws.com/doc/2018-08-20/"}}, "output": {"shape": "CreateAccessPointResult"}, "documentation": "<p>Creates an access point and associates it with the specified bucket. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/access-points.html\">Managing Data Access with Amazon S3 Access Points</a> in the <i>Amazon S3 User Guide</i>.</p> <p/> <note> <p>S3 on Outposts only supports VPC-style access points. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3onOutposts.html\"> Accessing Amazon S3 on Outposts using virtual private cloud (VPC) only access points</a> in the <i>Amazon S3 User Guide</i>.</p> </note> <p>All Amazon S3 on Outposts REST API requests for this action require an additional parameter of <code>x-amz-outpost-id</code> to be passed with the request. In addition, you must use an S3 on Outposts endpoint hostname prefix instead of <code>s3-control</code>. For an example of the request syntax for Amazon S3 on Outposts that uses the S3 on Outposts endpoint hostname prefix and the <code>x-amz-outpost-id</code> derived by using the access point ARN, see the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_CreateAccessPoint.html#API_control_CreateAccessPoint_Examples\">Examples</a> section.</p> <p/> <p>The following actions are related to <code>CreateAccessPoint</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetAccessPoint.html\">GetAccessPoint</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteAccessPoint.html\">DeleteAccessPoint</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_ListAccessPoints.html\">ListAccessPoints</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "CreateAccessPointForObjectLambda": {"name": "CreateAccessPointForObjectLambda", "http": {"method": "PUT", "requestUri": "/v20180820/accesspointforobjectlambda/{name}"}, "input": {"shape": "CreateAccessPointForObjectLambdaRequest", "locationName": "CreateAccessPointForObjectLambdaRequest", "xmlNamespace": {"uri": "http://awss3control.amazonaws.com/doc/2018-08-20/"}}, "output": {"shape": "CreateAccessPointForObjectLambdaResult"}, "documentation": "<p>Creates an Object Lambda Access Point. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/transforming-objects.html\">Transforming objects with Object Lambda Access Points</a> in the <i>Amazon S3 User Guide</i>.</p> <p>The following actions are related to <code>CreateAccessPointForObjectLambda</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteAccessPointForObjectLambda.html\">DeleteAccessPointForObjectLambda</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetAccessPointForObjectLambda.html\">GetAccessPointForObjectLambda</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_ListAccessPointsForObjectLambda.html\">ListAccessPointsForObjectLambda</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "CreateBucket": {"name": "CreateBucket", "http": {"method": "PUT", "requestUri": "/v20180820/bucket/{name}"}, "input": {"shape": "CreateBucketRequest"}, "output": {"shape": "CreateBucketResult"}, "errors": [{"shape": "BucketAlreadyExists"}, {"shape": "BucketAlreadyOwnedByYou"}], "documentation": "<note> <p>This action creates an Amazon S3 on Outposts bucket. To create an S3 bucket, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_CreateBucket.html\">Create Bucket</a> in the <i>Amazon S3 API Reference</i>. </p> </note> <p>Creates a new Outposts bucket. By creating the bucket, you become the bucket owner. To create an Outposts bucket, you must have S3 on Outposts. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3onOutposts.html\">Using Amazon S3 on Outposts</a> in <i>Amazon S3 User Guide</i>.</p> <p>Not every string is an acceptable bucket name. For information on bucket naming restrictions, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/BucketRestrictions.html#bucketnamingrules\">Working with Amazon S3 Buckets</a>.</p> <p>S3 on Outposts buckets support:</p> <ul> <li> <p>Tags</p> </li> <li> <p>LifecycleConfigurations for deleting expired objects</p> </li> </ul> <p>For a complete list of restrictions and Amazon S3 feature limitations on S3 on Outposts, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3OnOutpostsRestrictionsLimitations.html\"> Amazon S3 on Outposts Restrictions and Limitations</a>.</p> <p>For an example of the request syntax for Amazon S3 on Outposts that uses the S3 on Outposts endpoint hostname prefix and <code>x-amz-outpost-id</code> in your API request, see the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_CreateBucket.html#API_control_CreateBucket_Examples\">Examples</a> section.</p> <p>The following actions are related to <code>CreateBucket</code> for Amazon S3 on Outposts:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_PutObject.html\">PutObject</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetBucket.html\">GetBucket</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteBucket.html\">DeleteBucket</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_CreateAccessPoint.html\">CreateAccessPoint</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutAccessPointPolicy.html\">PutAccessPointPolicy</a> </p> </li> </ul>", "httpChecksumRequired": true}, "CreateJob": {"name": "<PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/v20180820/jobs"}, "input": {"shape": "CreateJobRequest", "locationName": "CreateJobRequest", "xmlNamespace": {"uri": "http://awss3control.amazonaws.com/doc/2018-08-20/"}}, "output": {"shape": "CreateJobResult"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}, {"shape": "IdempotencyException"}, {"shape": "InternalServiceException"}], "documentation": "<p>You can use S3 Batch Operations to perform large-scale batch actions on Amazon S3 objects. Batch Operations can run a single action on lists of Amazon S3 objects that you specify. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/batch-ops.html\">S3 Batch Operations</a> in the <i>Amazon S3 User Guide</i>.</p> <p>This action creates a S3 Batch Operations job.</p> <p/> <p>Related actions include:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DescribeJob.html\">DescribeJob</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_ListJobs.html\">ListJobs</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_UpdateJobPriority.html\">UpdateJobPriority</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_UpdateJobStatus.html\">UpdateJobStatus</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_JobOperation.html\">JobOperation</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "CreateMultiRegionAccessPoint": {"name": "CreateMultiRegionAccessPoint", "http": {"method": "POST", "requestUri": "/v20180820/async-requests/mrap/create"}, "input": {"shape": "CreateMultiRegionAccessPointRequest", "locationName": "CreateMultiRegionAccessPointRequest", "xmlNamespace": {"uri": "http://awss3control.amazonaws.com/doc/2018-08-20/"}}, "output": {"shape": "CreateMultiRegionAccessPointResult"}, "documentation": "<p>Creates a Multi-Region Access Point and associates it with the specified buckets. For more information about creating Multi-Region Access Points, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/CreatingMultiRegionAccessPoints.html\">Creating Multi-Region Access Points</a> in the <i>Amazon S3 User Guide</i>.</p> <p>This action will always be routed to the US West (Oregon) Region. For more information about the restrictions around managing Multi-Region Access Points, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/ManagingMultiRegionAccessPoints.html\">Managing Multi-Region Access Points</a> in the <i>Amazon S3 User Guide</i>.</p> <p>This request is asynchronous, meaning that you might receive a response before the command has completed. When this request provides a response, it provides a token that you can use to monitor the status of the request with <code>DescribeMultiRegionAccessPointOperation</code>.</p> <p>The following actions are related to <code>CreateMultiRegionAccessPoint</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteMultiRegionAccessPoint.html\">DeleteMultiRegionAccessPoint</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DescribeMultiRegionAccessPointOperation.html\">DescribeMultiRegionAccessPointOperation</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetMultiRegionAccessPoint.html\">GetMultiRegionAccessPoint</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_ListMultiRegionAccessPoints.html\">ListMultiRegionAccessPoints</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "httpChecksumRequired": true, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "DeleteAccessPoint": {"name": "DeleteAccessPoint", "http": {"method": "DELETE", "requestUri": "/v20180820/accesspoint/{name}"}, "input": {"shape": "DeleteAccessPointRequest"}, "documentation": "<p>Deletes the specified access point.</p> <p>All Amazon S3 on Outposts REST API requests for this action require an additional parameter of <code>x-amz-outpost-id</code> to be passed with the request. In addition, you must use an S3 on Outposts endpoint hostname prefix instead of <code>s3-control</code>. For an example of the request syntax for Amazon S3 on Outposts that uses the S3 on Outposts endpoint hostname prefix and the <code>x-amz-outpost-id</code> derived by using the access point ARN, see the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteAccessPoint.html#API_control_DeleteAccessPoint_Examples\">Examples</a> section.</p> <p>The following actions are related to <code>DeleteAccessPoint</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_CreateAccessPoint.html\">CreateAccessPoint</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetAccessPoint.html\">GetAccessPoint</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_ListAccessPoints.html\">ListAccessPoints</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "DeleteAccessPointForObjectLambda": {"name": "DeleteAccessPointForObjectLambda", "http": {"method": "DELETE", "requestUri": "/v20180820/accesspointforobjectlambda/{name}"}, "input": {"shape": "DeleteAccessPointForObjectLambdaRequest"}, "documentation": "<p>Deletes the specified Object Lambda Access Point.</p> <p>The following actions are related to <code>DeleteAccessPointForObjectLambda</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_CreateAccessPointForObjectLambda.html\">CreateAccessPointForObjectLambda</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetAccessPointForObjectLambda.html\">GetAccessPointForObjectLambda</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_ListAccessPointsForObjectLambda.html\">ListAccessPointsForObjectLambda</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "DeleteAccessPointPolicy": {"name": "DeleteAccessPointPolicy", "http": {"method": "DELETE", "requestUri": "/v20180820/accesspoint/{name}/policy"}, "input": {"shape": "DeleteAccessPointPolicyRequest"}, "documentation": "<p>Deletes the access point policy for the specified access point.</p> <p/> <p>All Amazon S3 on Outposts REST API requests for this action require an additional parameter of <code>x-amz-outpost-id</code> to be passed with the request. In addition, you must use an S3 on Outposts endpoint hostname prefix instead of <code>s3-control</code>. For an example of the request syntax for Amazon S3 on Outposts that uses the S3 on Outposts endpoint hostname prefix and the <code>x-amz-outpost-id</code> derived by using the access point ARN, see the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteAccessPointPolicy.html#API_control_DeleteAccessPointPolicy_Examples\">Examples</a> section.</p> <p>The following actions are related to <code>DeleteAccessPointPolicy</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutAccessPointPolicy.html\">PutAccessPointPolicy</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetAccessPointPolicy.html\">GetAccessPointPolicy</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "DeleteAccessPointPolicyForObjectLambda": {"name": "DeleteAccessPointPolicyForObjectLambda", "http": {"method": "DELETE", "requestUri": "/v20180820/accesspointforobjectlambda/{name}/policy"}, "input": {"shape": "DeleteAccessPointPolicyForObjectLambdaRequest"}, "documentation": "<p>Removes the resource policy for an Object Lambda Access Point.</p> <p>The following actions are related to <code>DeleteAccessPointPolicyForObjectLambda</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetAccessPointPolicyForObjectLambda.html\">GetAccessPointPolicyForObjectLambda</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutAccessPointPolicyForObjectLambda.html\">PutAccessPointPolicyForObjectLambda</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "DeleteBucket": {"name": "DeleteBucket", "http": {"method": "DELETE", "requestUri": "/v20180820/bucket/{name}"}, "input": {"shape": "DeleteBucketRequest"}, "documentation": "<note> <p>This action deletes an Amazon S3 on Outposts bucket. To delete an S3 bucket, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_DeleteBucket.html\">DeleteBucket</a> in the <i>Amazon S3 API Reference</i>. </p> </note> <p>Deletes the Amazon S3 on Outposts bucket. All objects (including all object versions and delete markers) in the bucket must be deleted before the bucket itself can be deleted. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3onOutposts.html\">Using Amazon S3 on Outposts</a> in <i>Amazon S3 User Guide</i>.</p> <p>All Amazon S3 on Outposts REST API requests for this action require an additional parameter of <code>x-amz-outpost-id</code> to be passed with the request. In addition, you must use an S3 on Outposts endpoint hostname prefix instead of <code>s3-control</code>. For an example of the request syntax for Amazon S3 on Outposts that uses the S3 on Outposts endpoint hostname prefix and the <code>x-amz-outpost-id</code> derived by using the access point ARN, see the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteBucket.html#API_control_DeleteBucket_Examples\">Examples</a> section.</p> <p class=\"title\"> <b>Related Resources</b> </p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_CreateBucket.html\">CreateBucket</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetBucket.html\">GetBucket</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_DeleteObject.html\">DeleteObject</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "DeleteBucketLifecycleConfiguration": {"name": "DeleteBucketLifecycleConfiguration", "http": {"method": "DELETE", "requestUri": "/v20180820/bucket/{name}/lifecycleconfiguration"}, "input": {"shape": "DeleteBucketLifecycleConfigurationRequest"}, "documentation": "<note> <p>This action deletes an Amazon S3 on Outposts bucket's lifecycle configuration. To delete an S3 bucket's lifecycle configuration, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_DeleteBucketLifecycle.html\">DeleteBucketLifecycle</a> in the <i>Amazon S3 API Reference</i>. </p> </note> <p>Deletes the lifecycle configuration from the specified Outposts bucket. Amazon S3 on Outposts removes all the lifecycle configuration rules in the lifecycle subresource associated with the bucket. Your objects never expire, and Amazon S3 on Outposts no longer automatically deletes any objects on the basis of rules contained in the deleted lifecycle configuration. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3onOutposts.html\">Using Amazon S3 on Outposts</a> in <i>Amazon S3 User Guide</i>.</p> <p>To use this action, you must have permission to perform the <code>s3-outposts:DeleteLifecycleConfiguration</code> action. By default, the bucket owner has this permission and the Outposts bucket owner can grant this permission to others.</p> <p>All Amazon S3 on Outposts REST API requests for this action require an additional parameter of <code>x-amz-outpost-id</code> to be passed with the request. In addition, you must use an S3 on Outposts endpoint hostname prefix instead of <code>s3-control</code>. For an example of the request syntax for Amazon S3 on Outposts that uses the S3 on Outposts endpoint hostname prefix and the <code>x-amz-outpost-id</code> derived by using the access point ARN, see the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteBucketLifecycleConfiguration.html#API_control_DeleteBucketLifecycleConfiguration_Examples\">Examples</a> section.</p> <p>For more information about object expiration, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/intro-lifecycle-rules.html#intro-lifecycle-rules-actions\">Elements to Describe Lifecycle Actions</a>.</p> <p>Related actions include:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutBucketLifecycleConfiguration.html\">PutBucketLifecycleConfiguration</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetBucketLifecycleConfiguration.html\">GetBucketLifecycleConfiguration</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "DeleteBucketPolicy": {"name": "DeleteBucketPolicy", "http": {"method": "DELETE", "requestUri": "/v20180820/bucket/{name}/policy"}, "input": {"shape": "DeleteBucketPolicyRequest"}, "documentation": "<note> <p>This action deletes an Amazon S3 on Outposts bucket policy. To delete an S3 bucket policy, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_DeleteBucketPolicy.html\">DeleteBucketPolicy</a> in the <i>Amazon S3 API Reference</i>. </p> </note> <p>This implementation of the DELETE action uses the policy subresource to delete the policy of a specified Amazon S3 on Outposts bucket. If you are using an identity other than the root user of the Amazon Web Services account that owns the bucket, the calling identity must have the <code>s3-outposts:DeleteBucketPolicy</code> permissions on the specified Outposts bucket and belong to the bucket owner's account to use this action. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3onOutposts.html\">Using Amazon S3 on Outposts</a> in <i>Amazon S3 User Guide</i>.</p> <p>If you don't have <code>DeleteBucketPolicy</code> permissions, Amazon S3 returns a <code>403 Access Denied</code> error. If you have the correct permissions, but you're not using an identity that belongs to the bucket owner's account, Amazon S3 returns a <code>405 Method Not Allowed</code> error. </p> <important> <p>As a security precaution, the root user of the Amazon Web Services account that owns a bucket can always use this action, even if the policy explicitly denies the root user the ability to perform this action.</p> </important> <p>For more information about bucket policies, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/using-iam-policies.html\">Using Bucket Policies and User Policies</a>. </p> <p>All Amazon S3 on Outposts REST API requests for this action require an additional parameter of <code>x-amz-outpost-id</code> to be passed with the request. In addition, you must use an S3 on Outposts endpoint hostname prefix instead of <code>s3-control</code>. For an example of the request syntax for Amazon S3 on Outposts that uses the S3 on Outposts endpoint hostname prefix and the <code>x-amz-outpost-id</code> derived by using the access point ARN, see the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteBucketPolicy.html#API_control_DeleteBucketPolicy_Examples\">Examples</a> section.</p> <p>The following actions are related to <code>DeleteBucketPolicy</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetBucketPolicy.html\">GetBucketPolicy</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutBucketPolicy.html\">PutBucketPolicy</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "DeleteBucketReplication": {"name": "DeleteBucketReplication", "http": {"method": "DELETE", "requestUri": "/v20180820/bucket/{name}/replication"}, "input": {"shape": "DeleteBucketReplicationRequest"}, "documentation": "<note> <p>This operation deletes an Amazon S3 on Outposts bucket's replication configuration. To delete an S3 bucket's replication configuration, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_DeleteBucketReplication.html\">DeleteBucketReplication</a> in the <i>Amazon S3 API Reference</i>. </p> </note> <p>Deletes the replication configuration from the specified S3 on Outposts bucket.</p> <p>To use this operation, you must have permissions to perform the <code>s3-outposts:PutReplicationConfiguration</code> action. The Outposts bucket owner has this permission by default and can grant it to others. For more information about permissions, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3OutpostsIAM.html\">Setting up IAM with S3 on Outposts</a> and <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3OutpostsBucketPolicy.html\">Managing access to S3 on Outposts buckets</a> in the <i>Amazon S3 User Guide</i>.</p> <note> <p>It can take a while to propagate <code>PUT</code> or <code>DELETE</code> requests for a replication configuration to all S3 on Outposts systems. Therefore, the replication configuration that's returned by a <code>GET</code> request soon after a <code>PUT</code> or <code>DELETE</code> request might return a more recent result than what's on the Outpost. If an Outpost is offline, the delay in updating the replication configuration on that Outpost can be significant.</p> </note> <p>All Amazon S3 on Outposts REST API requests for this action require an additional parameter of <code>x-amz-outpost-id</code> to be passed with the request. In addition, you must use an S3 on Outposts endpoint hostname prefix instead of <code>s3-control</code>. For an example of the request syntax for Amazon S3 on Outposts that uses the S3 on Outposts endpoint hostname prefix and the <code>x-amz-outpost-id</code> derived by using the access point ARN, see the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteBucketReplication.html#API_control_DeleteBucketReplication_Examples\">Examples</a> section.</p> <p>For information about S3 replication on Outposts configuration, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3OutpostsReplication.html\">Replicating objects for S3 on Outposts</a> in the <i>Amazon S3 User Guide</i>.</p> <p>The following operations are related to <code>DeleteBucketReplication</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutBucketReplication.html\">PutBucketReplication</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetBucketReplication.html\">GetBucketReplication</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "DeleteBucketTagging": {"name": "DeleteBucketTagging", "http": {"method": "DELETE", "requestUri": "/v20180820/bucket/{name}/tagging", "responseCode": 204}, "input": {"shape": "DeleteBucketTaggingRequest"}, "documentation": "<note> <p>This action deletes an Amazon S3 on Outposts bucket's tags. To delete an S3 bucket tags, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_DeleteBucketTagging.html\">DeleteBucketTagging</a> in the <i>Amazon S3 API Reference</i>. </p> </note> <p>Deletes the tags from the Outposts bucket. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3onOutposts.html\">Using Amazon S3 on Outposts</a> in <i>Amazon S3 User Guide</i>.</p> <p>To use this action, you must have permission to perform the <code>PutBucketTagging</code> action. By default, the bucket owner has this permission and can grant this permission to others. </p> <p>All Amazon S3 on Outposts REST API requests for this action require an additional parameter of <code>x-amz-outpost-id</code> to be passed with the request. In addition, you must use an S3 on Outposts endpoint hostname prefix instead of <code>s3-control</code>. For an example of the request syntax for Amazon S3 on Outposts that uses the S3 on Outposts endpoint hostname prefix and the <code>x-amz-outpost-id</code> derived by using the access point ARN, see the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteBucketTagging.html#API_control_DeleteBucketTagging_Examples\">Examples</a> section.</p> <p>The following actions are related to <code>DeleteBucketTagging</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetBucketTagging.html\">GetBucketTagging</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutBucketTagging.html\">PutBucketTagging</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "DeleteJobTagging": {"name": "DeleteJobTagging", "http": {"method": "DELETE", "requestUri": "/v20180820/jobs/{id}/tagging"}, "input": {"shape": "DeleteJobTaggingRequest"}, "output": {"shape": "DeleteJobTaggingResult"}, "errors": [{"shape": "InternalServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "NotFoundException"}], "documentation": "<p>Removes the entire tag set from the specified S3 Batch Operations job. To use the <code>DeleteJobTagging</code> operation, you must have permission to perform the <code>s3:DeleteJobTagging</code> action. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/batch-ops-managing-jobs.html#batch-ops-job-tags\">Controlling access and labeling jobs using tags</a> in the <i>Amazon S3 User Guide</i>.</p> <p/> <p>Related actions include:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_CreateJob.html\">CreateJob</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetJobTagging.html\">GetJobTagging</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutJobTagging.html\">PutJobTagging</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "DeleteMultiRegionAccessPoint": {"name": "DeleteMultiRegionAccessPoint", "http": {"method": "POST", "requestUri": "/v20180820/async-requests/mrap/delete"}, "input": {"shape": "DeleteMultiRegionAccessPointRequest", "locationName": "DeleteMultiRegionAccessPointRequest", "xmlNamespace": {"uri": "http://awss3control.amazonaws.com/doc/2018-08-20/"}}, "output": {"shape": "DeleteMultiRegionAccessPointResult"}, "documentation": "<p>Deletes a Multi-Region Access Point. This action does not delete the buckets associated with the Multi-Region Access Point, only the Multi-Region Access Point itself.</p> <p>This action will always be routed to the US West (Oregon) Region. For more information about the restrictions around managing Multi-Region Access Points, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/ManagingMultiRegionAccessPoints.html\">Managing Multi-Region Access Points</a> in the <i>Amazon S3 User Guide</i>.</p> <p>This request is asynchronous, meaning that you might receive a response before the command has completed. When this request provides a response, it provides a token that you can use to monitor the status of the request with <code>DescribeMultiRegionAccessPointOperation</code>.</p> <p>The following actions are related to <code>DeleteMultiRegionAccessPoint</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_CreateMultiRegionAccessPoint.html\">CreateMultiRegionAccessPoint</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DescribeMultiRegionAccessPointOperation.html\">DescribeMultiRegionAccessPointOperation</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetMultiRegionAccessPoint.html\">GetMultiRegionAccessPoint</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_ListMultiRegionAccessPoints.html\">ListMultiRegionAccessPoints</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "httpChecksumRequired": true, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "DeletePublicAccessBlock": {"name": "DeletePublicAccessBlock", "http": {"method": "DELETE", "requestUri": "/v20180820/configuration/publicAccessBlock"}, "input": {"shape": "DeletePublicAccessBlockRequest"}, "documentation": "<p>Removes the <code>PublicAccessBlock</code> configuration for an Amazon Web Services account. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/access-control-block-public-access.html\"> Using Amazon S3 block public access</a>.</p> <p>Related actions include:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetPublicAccessBlock.html\">GetPublicAccessBlock</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutPublicAccessBlock.html\">PutPublicAccessBlock</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "DeleteStorageLensConfiguration": {"name": "DeleteStorageLensConfiguration", "http": {"method": "DELETE", "requestUri": "/v20180820/storagelens/{storagelensid}"}, "input": {"shape": "DeleteStorageLensConfigurationRequest"}, "documentation": "<p>Deletes the Amazon S3 Storage Lens configuration. For more information about S3 Storage Lens, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/storage_lens.html\">Assessing your storage activity and usage with Amazon S3 Storage Lens </a> in the <i>Amazon S3 User Guide</i>.</p> <note> <p>To use this action, you must have permission to perform the <code>s3:DeleteStorageLensConfiguration</code> action. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/storage_lens_iam_permissions.html\">Setting permissions to use Amazon S3 Storage Lens</a> in the <i>Amazon S3 User Guide</i>.</p> </note>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "DeleteStorageLensConfigurationTagging": {"name": "DeleteStorageLensConfigurationTagging", "http": {"method": "DELETE", "requestUri": "/v20180820/storagelens/{storagelensid}/tagging"}, "input": {"shape": "DeleteStorageLensConfigurationTaggingRequest"}, "output": {"shape": "DeleteStorageLensConfigurationTaggingResult"}, "documentation": "<p>Deletes the Amazon S3 Storage Lens configuration tags. For more information about S3 Storage Lens, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/storage_lens.html\">Assessing your storage activity and usage with Amazon S3 Storage Lens </a> in the <i>Amazon S3 User Guide</i>.</p> <note> <p>To use this action, you must have permission to perform the <code>s3:DeleteStorageLensConfigurationTagging</code> action. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/storage_lens_iam_permissions.html\">Setting permissions to use Amazon S3 Storage Lens</a> in the <i>Amazon S3 User Guide</i>.</p> </note>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "DescribeJob": {"name": "Describe<PERSON><PERSON>", "http": {"method": "GET", "requestUri": "/v20180820/jobs/{id}"}, "input": {"shape": "DescribeJobRequest"}, "output": {"shape": "DescribeJobResult"}, "errors": [{"shape": "BadRequestException"}, {"shape": "TooManyRequestsException"}, {"shape": "NotFoundException"}, {"shape": "InternalServiceException"}], "documentation": "<p>Retrieves the configuration parameters and status for a Batch Operations job. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/batch-ops.html\">S3 Batch Operations</a> in the <i>Amazon S3 User Guide</i>.</p> <p/> <p>Related actions include:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_CreateJob.html\">CreateJob</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_ListJobs.html\">ListJobs</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_UpdateJobPriority.html\">UpdateJobPriority</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_UpdateJobStatus.html\">UpdateJobStatus</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "DescribeMultiRegionAccessPointOperation": {"name": "DescribeMultiRegionAccessPointOperation", "http": {"method": "GET", "requestUri": "/v20180820/async-requests/mrap/{request_token+}"}, "input": {"shape": "DescribeMultiRegionAccessPointOperationRequest"}, "output": {"shape": "DescribeMultiRegionAccessPointOperationResult"}, "documentation": "<p>Retrieves the status of an asynchronous request to manage a Multi-Region Access Point. For more information about managing Multi-Region Access Points and how asynchronous requests work, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/ManagingMultiRegionAccessPoints.html\">Managing Multi-Region Access Points</a> in the <i>Amazon S3 User Guide</i>.</p> <p>The following actions are related to <code>GetMultiRegionAccessPoint</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_CreateMultiRegionAccessPoint.html\">CreateMultiRegionAccessPoint</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteMultiRegionAccessPoint.html\">DeleteMultiRegionAccessPoint</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetMultiRegionAccessPoint.html\">GetMultiRegionAccessPoint</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_ListMultiRegionAccessPoints.html\">ListMultiRegionAccessPoints</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "httpChecksumRequired": true, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "GetAccessPoint": {"name": "GetAccessPoint", "http": {"method": "GET", "requestUri": "/v20180820/accesspoint/{name}"}, "input": {"shape": "GetAccessPointRequest"}, "output": {"shape": "GetAccessPointResult"}, "documentation": "<p>Returns configuration information about the specified access point.</p> <p/> <p>All Amazon S3 on Outposts REST API requests for this action require an additional parameter of <code>x-amz-outpost-id</code> to be passed with the request. In addition, you must use an S3 on Outposts endpoint hostname prefix instead of <code>s3-control</code>. For an example of the request syntax for Amazon S3 on Outposts that uses the S3 on Outposts endpoint hostname prefix and the <code>x-amz-outpost-id</code> derived by using the access point ARN, see the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetAccessPoint.html#API_control_GetAccessPoint_Examples\">Examples</a> section.</p> <p>The following actions are related to <code>GetAccessPoint</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_CreateAccessPoint.html\">CreateAccessPoint</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteAccessPoint.html\">DeleteAccessPoint</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_ListAccessPoints.html\">ListAccessPoints</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "GetAccessPointConfigurationForObjectLambda": {"name": "GetAccessPointConfigurationForObjectLambda", "http": {"method": "GET", "requestUri": "/v20180820/accesspointforobjectlambda/{name}/configuration"}, "input": {"shape": "GetAccessPointConfigurationForObjectLambdaRequest"}, "output": {"shape": "GetAccessPointConfigurationForObjectLambdaResult"}, "documentation": "<p>Returns configuration for an Object Lambda Access Point.</p> <p>The following actions are related to <code>GetAccessPointConfigurationForObjectLambda</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutAccessPointConfigurationForObjectLambda.html\">PutAccessPointConfigurationForObjectLambda</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "GetAccessPointForObjectLambda": {"name": "GetAccessPointForObjectLambda", "http": {"method": "GET", "requestUri": "/v20180820/accesspointforobjectlambda/{name}"}, "input": {"shape": "GetAccessPointForObjectLambdaRequest"}, "output": {"shape": "GetAccessPointForObjectLambdaResult"}, "documentation": "<p>Returns configuration information about the specified Object Lambda Access Point</p> <p>The following actions are related to <code>GetAccessPointForObjectLambda</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_CreateAccessPointForObjectLambda.html\">CreateAccessPointForObjectLambda</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteAccessPointForObjectLambda.html\">DeleteAccessPointForObjectLambda</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_ListAccessPointsForObjectLambda.html\">ListAccessPointsForObjectLambda</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "GetAccessPointPolicy": {"name": "GetAccessPointPolicy", "http": {"method": "GET", "requestUri": "/v20180820/accesspoint/{name}/policy"}, "input": {"shape": "GetAccessPointPolicyRequest"}, "output": {"shape": "GetAccessPointPolicyResult"}, "documentation": "<p>Returns the access point policy associated with the specified access point.</p> <p>The following actions are related to <code>GetAccessPointPolicy</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutAccessPointPolicy.html\">PutAccessPointPolicy</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteAccessPointPolicy.html\">DeleteAccessPointPolicy</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "GetAccessPointPolicyForObjectLambda": {"name": "GetAccessPointPolicyForObjectLambda", "http": {"method": "GET", "requestUri": "/v20180820/accesspointforobjectlambda/{name}/policy"}, "input": {"shape": "GetAccessPointPolicyForObjectLambdaRequest"}, "output": {"shape": "GetAccessPointPolicyForObjectLambdaResult"}, "documentation": "<p>Returns the resource policy for an Object Lambda Access Point.</p> <p>The following actions are related to <code>GetAccessPointPolicyForObjectLambda</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteAccessPointPolicyForObjectLambda.html\">DeleteAccessPointPolicyForObjectLambda</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutAccessPointPolicyForObjectLambda.html\">PutAccessPointPolicyForObjectLambda</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "GetAccessPointPolicyStatus": {"name": "GetAccessPointPolicyStatus", "http": {"method": "GET", "requestUri": "/v20180820/accesspoint/{name}/policyStatus"}, "input": {"shape": "GetAccessPointPolicyStatusRequest"}, "output": {"shape": "GetAccessPointPolicyStatusResult"}, "documentation": "<p>Indicates whether the specified access point currently has a policy that allows public access. For more information about public access through access points, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/access-points.html\">Managing Data Access with Amazon S3 access points</a> in the <i>Amazon S3 User Guide</i>.</p>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "GetAccessPointPolicyStatusForObjectLambda": {"name": "GetAccessPointPolicyStatusForObjectLambda", "http": {"method": "GET", "requestUri": "/v20180820/accesspointforobjectlambda/{name}/policyStatus"}, "input": {"shape": "GetAccessPointPolicyStatusForObjectLambdaRequest"}, "output": {"shape": "GetAccessPointPolicyStatusForObjectLambdaResult"}, "documentation": "<p>Returns the status of the resource policy associated with an Object Lambda Access Point.</p>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "GetBucket": {"name": "GetBucket", "http": {"method": "GET", "requestUri": "/v20180820/bucket/{name}"}, "input": {"shape": "GetBucketRequest"}, "output": {"shape": "GetBucketResult"}, "documentation": "<p>Gets an Amazon S3 on Outposts bucket. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3onOutposts.html\"> Using Amazon S3 on Outposts</a> in the <i>Amazon S3 User Guide</i>.</p> <p>If you are using an identity other than the root user of the Amazon Web Services account that owns the Outposts bucket, the calling identity must have the <code>s3-outposts:GetBucket</code> permissions on the specified Outposts bucket and belong to the Outposts bucket owner's account in order to use this action. Only users from Outposts bucket owner account with the right permissions can perform actions on an Outposts bucket. </p> <p> If you don't have <code>s3-outposts:GetBucket</code> permissions or you're not using an identity that belongs to the bucket owner's account, Amazon S3 returns a <code>403 Access Denied</code> error.</p> <p>The following actions are related to <code>GetBucket</code> for Amazon S3 on Outposts:</p> <p>All Amazon S3 on Outposts REST API requests for this action require an additional parameter of <code>x-amz-outpost-id</code> to be passed with the request. In addition, you must use an S3 on Outposts endpoint hostname prefix instead of <code>s3-control</code>. For an example of the request syntax for Amazon S3 on Outposts that uses the S3 on Outposts endpoint hostname prefix and the <code>x-amz-outpost-id</code> derived by using the access point ARN, see the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetBucket.html#API_control_GetBucket_Examples\">Examples</a> section.</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_PutObject.html\">PutObject</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_CreateBucket.html\">CreateBucket</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteBucket.html\">DeleteBucket</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "GetBucketLifecycleConfiguration": {"name": "GetBucketLifecycleConfiguration", "http": {"method": "GET", "requestUri": "/v20180820/bucket/{name}/lifecycleconfiguration"}, "input": {"shape": "GetBucketLifecycleConfigurationRequest"}, "output": {"shape": "GetBucketLifecycleConfigurationResult"}, "documentation": "<note> <p>This action gets an Amazon S3 on Outposts bucket's lifecycle configuration. To get an S3 bucket's lifecycle configuration, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_GetBucketLifecycleConfiguration.html\">GetBucketLifecycleConfiguration</a> in the <i>Amazon S3 API Reference</i>. </p> </note> <p>Returns the lifecycle configuration information set on the Outposts bucket. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3onOutposts.html\">Using Amazon S3 on Outposts</a> and for information about lifecycle configuration, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/object-lifecycle-mgmt.html\"> Object Lifecycle Management</a> in <i>Amazon S3 User Guide</i>.</p> <p>To use this action, you must have permission to perform the <code>s3-outposts:GetLifecycleConfiguration</code> action. The Outposts bucket owner has this permission, by default. The bucket owner can grant this permission to others. For more information about permissions, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/using-with-s3-actions.html#using-with-s3-actions-related-to-bucket-subresources\">Permissions Related to Bucket Subresource Operations</a> and <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/s3-access-control.html\">Managing Access Permissions to Your Amazon S3 Resources</a>.</p> <p>All Amazon S3 on Outposts REST API requests for this action require an additional parameter of <code>x-amz-outpost-id</code> to be passed with the request. In addition, you must use an S3 on Outposts endpoint hostname prefix instead of <code>s3-control</code>. For an example of the request syntax for Amazon S3 on Outposts that uses the S3 on Outposts endpoint hostname prefix and the <code>x-amz-outpost-id</code> derived by using the access point ARN, see the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetBucketLifecycleConfiguration.html#API_control_GetBucketLifecycleConfiguration_Examples\">Examples</a> section.</p> <p> <code>GetBucketLifecycleConfiguration</code> has the following special error:</p> <ul> <li> <p>Error code: <code>NoSuchLifecycleConfiguration</code> </p> <ul> <li> <p>Description: The lifecycle configuration does not exist.</p> </li> <li> <p>HTTP Status Code: 404 Not Found</p> </li> <li> <p>SOAP Fault Code Prefix: Client</p> </li> </ul> </li> </ul> <p>The following actions are related to <code>GetBucketLifecycleConfiguration</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutBucketLifecycleConfiguration.html\">PutBucketLifecycleConfiguration</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteBucketLifecycleConfiguration.html\">DeleteBucketLifecycleConfiguration</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "GetBucketPolicy": {"name": "GetBucketPolicy", "http": {"method": "GET", "requestUri": "/v20180820/bucket/{name}/policy"}, "input": {"shape": "GetBucketPolicyRequest"}, "output": {"shape": "GetBucketPolicyResult"}, "documentation": "<note> <p>This action gets a bucket policy for an Amazon S3 on Outposts bucket. To get a policy for an S3 bucket, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_GetBucketPolicy.html\">GetBucketPolicy</a> in the <i>Amazon S3 API Reference</i>. </p> </note> <p>Returns the policy of a specified Outposts bucket. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3onOutposts.html\">Using Amazon S3 on Outposts</a> in the <i>Amazon S3 User Guide</i>.</p> <p>If you are using an identity other than the root user of the Amazon Web Services account that owns the bucket, the calling identity must have the <code>GetBucketPolicy</code> permissions on the specified bucket and belong to the bucket owner's account in order to use this action.</p> <p>Only users from Outposts bucket owner account with the right permissions can perform actions on an Outposts bucket. If you don't have <code>s3-outposts:GetBucketPolicy</code> permissions or you're not using an identity that belongs to the bucket owner's account, Amazon S3 returns a <code>403 Access Denied</code> error.</p> <important> <p>As a security precaution, the root user of the Amazon Web Services account that owns a bucket can always use this action, even if the policy explicitly denies the root user the ability to perform this action.</p> </important> <p>For more information about bucket policies, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/using-iam-policies.html\">Using Bucket Policies and User Policies</a>.</p> <p>All Amazon S3 on Outposts REST API requests for this action require an additional parameter of <code>x-amz-outpost-id</code> to be passed with the request. In addition, you must use an S3 on Outposts endpoint hostname prefix instead of <code>s3-control</code>. For an example of the request syntax for Amazon S3 on Outposts that uses the S3 on Outposts endpoint hostname prefix and the <code>x-amz-outpost-id</code> derived by using the access point ARN, see the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetBucketPolicy.html#API_control_GetBucketPolicy_Examples\">Examples</a> section.</p> <p>The following actions are related to <code>GetBucketPolicy</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_GetObject.html\">GetObject</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutBucketPolicy.html\">PutBucketPolicy</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteBucketPolicy.html\">DeleteBucketPolicy</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "GetBucketReplication": {"name": "GetBucketReplication", "http": {"method": "GET", "requestUri": "/v20180820/bucket/{name}/replication"}, "input": {"shape": "GetBucketReplicationRequest"}, "output": {"shape": "GetBucketReplicationResult"}, "documentation": "<note> <p>This operation gets an Amazon S3 on Outposts bucket's replication configuration. To get an S3 bucket's replication configuration, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_GetBucketReplication.html\">GetBucketReplication</a> in the <i>Amazon S3 API Reference</i>. </p> </note> <p>Returns the replication configuration of an S3 on Outposts bucket. For more information about S3 on Outposts, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3onOutposts.html\">Using Amazon S3 on Outposts</a> in the <i>Amazon S3 User Guide</i>. For information about S3 replication on Outposts configuration, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3OutpostsReplication.html\">Replicating objects for S3 on Outposts</a> in the <i>Amazon S3 User Guide</i>.</p> <note> <p>It can take a while to propagate <code>PUT</code> or <code>DELETE</code> requests for a replication configuration to all S3 on Outposts systems. Therefore, the replication configuration that's returned by a <code>GET</code> request soon after a <code>PUT</code> or <code>DELETE</code> request might return a more recent result than what's on the Outpost. If an Outpost is offline, the delay in updating the replication configuration on that Outpost can be significant.</p> </note> <p>This action requires permissions for the <code>s3-outposts:GetReplicationConfiguration</code> action. The Outposts bucket owner has this permission by default and can grant it to others. For more information about permissions, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3OutpostsIAM.html\">Setting up IAM with S3 on Outposts</a> and <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3OutpostsBucketPolicy.html\">Managing access to S3 on Outposts bucket</a> in the <i>Amazon S3 User Guide</i>.</p> <p>All Amazon S3 on Outposts REST API requests for this action require an additional parameter of <code>x-amz-outpost-id</code> to be passed with the request. In addition, you must use an S3 on Outposts endpoint hostname prefix instead of <code>s3-control</code>. For an example of the request syntax for Amazon S3 on Outposts that uses the S3 on Outposts endpoint hostname prefix and the <code>x-amz-outpost-id</code> derived by using the access point ARN, see the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetBucketReplication.html#API_control_GetBucketReplication_Examples\">Examples</a> section.</p> <p>If you include the <code>Filter</code> element in a replication configuration, you must also include the <code>DeleteMarkerReplication</code>, <code>Status</code>, and <code>Priority</code> elements. The response also returns those elements.</p> <p>For information about S3 on Outposts replication failure reasons, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/outposts-replication-eventbridge.html#outposts-replication-failure-codes\">Replication failure reasons</a> in the <i>Amazon S3 User Guide</i>.</p> <p>The following operations are related to <code>GetBucketReplication</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutBucketReplication.html\">PutBucketReplication</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteBucketReplication.html\">DeleteBucketReplication</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "GetBucketTagging": {"name": "GetBucketTagging", "http": {"method": "GET", "requestUri": "/v20180820/bucket/{name}/tagging"}, "input": {"shape": "GetBucketTaggingRequest"}, "output": {"shape": "GetBucketTaggingResult"}, "documentation": "<note> <p>This action gets an Amazon S3 on Outposts bucket's tags. To get an S3 bucket tags, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_GetBucketTagging.html\">GetBucketTagging</a> in the <i>Amazon S3 API Reference</i>. </p> </note> <p>Returns the tag set associated with the Outposts bucket. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3onOutposts.html\">Using Amazon S3 on Outposts</a> in the <i>Amazon S3 User Guide</i>.</p> <p>To use this action, you must have permission to perform the <code>GetBucketTagging</code> action. By default, the bucket owner has this permission and can grant this permission to others.</p> <p> <code>GetBucketTagging</code> has the following special error:</p> <ul> <li> <p>Error code: <code>NoSuchTagSetError</code> </p> <ul> <li> <p>Description: There is no tag set associated with the bucket.</p> </li> </ul> </li> </ul> <p>All Amazon S3 on Outposts REST API requests for this action require an additional parameter of <code>x-amz-outpost-id</code> to be passed with the request. In addition, you must use an S3 on Outposts endpoint hostname prefix instead of <code>s3-control</code>. For an example of the request syntax for Amazon S3 on Outposts that uses the S3 on Outposts endpoint hostname prefix and the <code>x-amz-outpost-id</code> derived by using the access point ARN, see the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetBucketTagging.html#API_control_GetBucketTagging_Examples\">Examples</a> section.</p> <p>The following actions are related to <code>GetBucketTagging</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutBucketTagging.html\">PutBucketTagging</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteBucketTagging.html\">DeleteBucketTagging</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "GetBucketVersioning": {"name": "GetBucketVersioning", "http": {"method": "GET", "requestUri": "/v20180820/bucket/{name}/versioning"}, "input": {"shape": "GetBucketVersioningRequest"}, "output": {"shape": "GetBucketVersioningResult"}, "documentation": "<note> <p>This operation returns the versioning state for S3 on Outposts buckets only. To return the versioning state for an S3 bucket, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_GetBucketVersioning.html\">GetBucketVersioning</a> in the <i>Amazon S3 API Reference</i>. </p> </note> <p>Returns the versioning state for an S3 on Outposts bucket. With S3 Versioning, you can save multiple distinct copies of your objects and recover from unintended user actions and application failures.</p> <p>If you've never set versioning on your bucket, it has no versioning state. In that case, the <code>GetBucketVersioning</code> request does not return a versioning state value.</p> <p>For more information about versioning, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/Versioning.html\">Versioning</a> in the <i>Amazon S3 User Guide</i>.</p> <p>All Amazon S3 on Outposts REST API requests for this action require an additional parameter of <code>x-amz-outpost-id</code> to be passed with the request. In addition, you must use an S3 on Outposts endpoint hostname prefix instead of <code>s3-control</code>. For an example of the request syntax for Amazon S3 on Outposts that uses the S3 on Outposts endpoint hostname prefix and the <code>x-amz-outpost-id</code> derived by using the access point ARN, see the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetBucketVersioning.html#API_control_GetBucketVersioning_Examples\">Examples</a> section.</p> <p>The following operations are related to <code>GetBucketVersioning</code> for S3 on Outposts.</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutBucketVersioning.html\">PutBucketVersioning</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutBucketLifecycleConfiguration.html\">PutBucketLifecycleConfiguration</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetBucketLifecycleConfiguration.html\">GetBucketLifecycleConfiguration</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "GetJobTagging": {"name": "GetJobTagging", "http": {"method": "GET", "requestUri": "/v20180820/jobs/{id}/tagging"}, "input": {"shape": "GetJobTaggingRequest"}, "output": {"shape": "GetJobTaggingResult"}, "errors": [{"shape": "InternalServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "NotFoundException"}], "documentation": "<p>Returns the tags on an S3 Batch Operations job. To use the <code>GetJobTagging</code> operation, you must have permission to perform the <code>s3:GetJobTagging</code> action. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/batch-ops-managing-jobs.html#batch-ops-job-tags\">Controlling access and labeling jobs using tags</a> in the <i>Amazon S3 User Guide</i>.</p> <p/> <p>Related actions include:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_CreateJob.html\">CreateJob</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutJobTagging.html\">PutJobTagging</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteJobTagging.html\">DeleteJobTagging</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "GetMultiRegionAccessPoint": {"name": "GetMultiRegionAccessPoint", "http": {"method": "GET", "requestUri": "/v20180820/mrap/instances/{name+}"}, "input": {"shape": "GetMultiRegionAccessPointRequest"}, "output": {"shape": "GetMultiRegionAccessPointResult"}, "documentation": "<p>Returns configuration information about the specified Multi-Region Access Point.</p> <p>This action will always be routed to the US West (Oregon) Region. For more information about the restrictions around managing Multi-Region Access Points, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/ManagingMultiRegionAccessPoints.html\">Managing Multi-Region Access Points</a> in the <i>Amazon S3 User Guide</i>.</p> <p>The following actions are related to <code>GetMultiRegionAccessPoint</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_CreateMultiRegionAccessPoint.html\">CreateMultiRegionAccessPoint</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteMultiRegionAccessPoint.html\">DeleteMultiRegionAccessPoint</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DescribeMultiRegionAccessPointOperation.html\">DescribeMultiRegionAccessPointOperation</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_ListMultiRegionAccessPoints.html\">ListMultiRegionAccessPoints</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "httpChecksumRequired": true, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "GetMultiRegionAccessPointPolicy": {"name": "GetMultiRegionAccessPointPolicy", "http": {"method": "GET", "requestUri": "/v20180820/mrap/instances/{name+}/policy"}, "input": {"shape": "GetMultiRegionAccessPointPolicyRequest"}, "output": {"shape": "GetMultiRegionAccessPointPolicyResult"}, "documentation": "<p>Returns the access control policy of the specified Multi-Region Access Point.</p> <p>This action will always be routed to the US West (Oregon) Region. For more information about the restrictions around managing Multi-Region Access Points, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/ManagingMultiRegionAccessPoints.html\">Managing Multi-Region Access Points</a> in the <i>Amazon S3 User Guide</i>.</p> <p>The following actions are related to <code>GetMultiRegionAccessPointPolicy</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetMultiRegionAccessPointPolicyStatus.html\">GetMultiRegionAccessPointPolicyStatus</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutMultiRegionAccessPointPolicy.html\">PutMultiRegionAccessPointPolicy</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "httpChecksumRequired": true, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "GetMultiRegionAccessPointPolicyStatus": {"name": "GetMultiRegionAccessPointPolicyStatus", "http": {"method": "GET", "requestUri": "/v20180820/mrap/instances/{name+}/policystatus"}, "input": {"shape": "GetMultiRegionAccessPointPolicyStatusRequest"}, "output": {"shape": "GetMultiRegionAccessPointPolicyStatusResult"}, "documentation": "<p>Indicates whether the specified Multi-Region Access Point has an access control policy that allows public access.</p> <p>This action will always be routed to the US West (Oregon) Region. For more information about the restrictions around managing Multi-Region Access Points, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/ManagingMultiRegionAccessPoints.html\">Managing Multi-Region Access Points</a> in the <i>Amazon S3 User Guide</i>.</p> <p>The following actions are related to <code>GetMultiRegionAccessPointPolicyStatus</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetMultiRegionAccessPointPolicy.html\">GetMultiRegionAccessPointPolicy</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutMultiRegionAccessPointPolicy.html\">PutMultiRegionAccessPointPolicy</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "httpChecksumRequired": true, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "GetMultiRegionAccessPointRoutes": {"name": "GetMultiRegionAccessPointRoutes", "http": {"method": "GET", "requestUri": "/v20180820/mrap/instances/{mrap+}/routes"}, "input": {"shape": "GetMultiRegionAccessPointRoutesRequest"}, "output": {"shape": "GetMultiRegionAccessPointRoutesResult"}, "documentation": "<p>Returns the routing configuration for a Multi-Region Access Point, indicating which Regions are active or passive.</p> <p>To obtain routing control changes and failover requests, use the Amazon S3 failover control infrastructure endpoints in these five Amazon Web Services Regions:</p> <ul> <li> <p> <code>us-east-1</code> </p> </li> <li> <p> <code>us-west-2</code> </p> </li> <li> <p> <code>ap-southeast-2</code> </p> </li> <li> <p> <code>ap-northeast-1</code> </p> </li> <li> <p> <code>eu-west-1</code> </p> </li> </ul> <note> <p>Your Amazon S3 bucket does not need to be in these five Regions.</p> </note>", "endpoint": {"hostPrefix": "{AccountId}."}, "httpChecksumRequired": true, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "GetPublicAccessBlock": {"name": "GetPublicAccessBlock", "http": {"method": "GET", "requestUri": "/v20180820/configuration/publicAccessBlock"}, "input": {"shape": "GetPublicAccessBlockRequest"}, "output": {"shape": "GetPublicAccessBlockOutput"}, "errors": [{"shape": "NoSuchPublicAccessBlockConfiguration"}], "documentation": "<p>Retrieves the <code>PublicAccessBlock</code> configuration for an Amazon Web Services account. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/access-control-block-public-access.html\"> Using Amazon S3 block public access</a>.</p> <p>Related actions include:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeletePublicAccessBlock.html\">DeletePublicAccessBlock</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutPublicAccessBlock.html\">PutPublicAccessBlock</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "GetStorageLensConfiguration": {"name": "GetStorageLensConfiguration", "http": {"method": "GET", "requestUri": "/v20180820/storagelens/{storagelensid}"}, "input": {"shape": "GetStorageLensConfigurationRequest"}, "output": {"shape": "GetStorageLensConfigurationResult"}, "documentation": "<p>Gets the Amazon S3 Storage Lens configuration. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/storage_lens.html\">Assessing your storage activity and usage with Amazon S3 Storage Lens </a> in the <i>Amazon S3 User Guide</i>. For a complete list of S3 Storage Lens metrics, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/storage_lens_metrics_glossary.html\">S3 Storage Lens metrics glossary</a> in the <i>Amazon S3 User Guide</i>.</p> <note> <p>To use this action, you must have permission to perform the <code>s3:GetStorageLensConfiguration</code> action. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/storage_lens_iam_permissions.html\">Setting permissions to use Amazon S3 Storage Lens</a> in the <i>Amazon S3 User Guide</i>.</p> </note>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "GetStorageLensConfigurationTagging": {"name": "GetStorageLensConfigurationTagging", "http": {"method": "GET", "requestUri": "/v20180820/storagelens/{storagelensid}/tagging"}, "input": {"shape": "GetStorageLensConfigurationTaggingRequest"}, "output": {"shape": "GetStorageLensConfigurationTaggingResult"}, "documentation": "<p>Gets the tags of Amazon S3 Storage Lens configuration. For more information about S3 Storage Lens, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/storage_lens.html\">Assessing your storage activity and usage with Amazon S3 Storage Lens </a> in the <i>Amazon S3 User Guide</i>.</p> <note> <p>To use this action, you must have permission to perform the <code>s3:GetStorageLensConfigurationTagging</code> action. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/storage_lens_iam_permissions.html\">Setting permissions to use Amazon S3 Storage Lens</a> in the <i>Amazon S3 User Guide</i>.</p> </note>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "ListAccessPoints": {"name": "ListAccessPoints", "http": {"method": "GET", "requestUri": "/v20180820/accesspoint"}, "input": {"shape": "ListAccessPointsRequest"}, "output": {"shape": "ListAccessPointsResult"}, "documentation": "<p>Returns a list of the access points that are owned by the current account that's associated with the specified bucket. You can retrieve up to 1000 access points per call. If the specified bucket has more than 1,000 access points (or the number specified in <code>maxResults</code>, whichever is less), the response will include a continuation token that you can use to list the additional access points.</p> <p/> <p>All Amazon S3 on Outposts REST API requests for this action require an additional parameter of <code>x-amz-outpost-id</code> to be passed with the request. In addition, you must use an S3 on Outposts endpoint hostname prefix instead of <code>s3-control</code>. For an example of the request syntax for Amazon S3 on Outposts that uses the S3 on Outposts endpoint hostname prefix and the <code>x-amz-outpost-id</code> derived by using the access point ARN, see the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetAccessPoint.html#API_control_GetAccessPoint_Examples\">Examples</a> section.</p> <p>The following actions are related to <code>ListAccessPoints</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_CreateAccessPoint.html\">CreateAccessPoint</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteAccessPoint.html\">DeleteAccessPoint</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetAccessPoint.html\">GetAccessPoint</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "ListAccessPointsForObjectLambda": {"name": "ListAccessPointsForObjectLambda", "http": {"method": "GET", "requestUri": "/v20180820/accesspointforobjectlambda"}, "input": {"shape": "ListAccessPointsForObjectLambdaRequest"}, "output": {"shape": "ListAccessPointsForObjectLambdaResult"}, "documentation": "<p>Returns some or all (up to 1,000) access points associated with the Object Lambda Access Point per call. If there are more access points than what can be returned in one call, the response will include a continuation token that you can use to list the additional access points.</p> <p>The following actions are related to <code>ListAccessPointsForObjectLambda</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_CreateAccessPointForObjectLambda.html\">CreateAccessPointForObjectLambda</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteAccessPointForObjectLambda.html\">DeleteAccessPointForObjectLambda</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetAccessPointForObjectLambda.html\">GetAccessPointForObjectLambda</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "ListJobs": {"name": "ListJobs", "http": {"method": "GET", "requestUri": "/v20180820/jobs"}, "input": {"shape": "ListJobsRequest"}, "output": {"shape": "ListJobsResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServiceException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Lists current S3 Batch Operations jobs and jobs that have ended within the last 30 days for the Amazon Web Services account making the request. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/batch-ops.html\">S3 Batch Operations</a> in the <i>Amazon S3 User Guide</i>.</p> <p>Related actions include:</p> <p/> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_CreateJob.html\">CreateJob</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DescribeJob.html\">DescribeJob</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_UpdateJobPriority.html\">UpdateJobPriority</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_UpdateJobStatus.html\">UpdateJobStatus</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "ListMultiRegionAccessPoints": {"name": "ListMultiRegionAccessPoints", "http": {"method": "GET", "requestUri": "/v20180820/mrap/instances"}, "input": {"shape": "ListMultiRegionAccessPointsRequest"}, "output": {"shape": "ListMultiRegionAccessPointsResult"}, "documentation": "<p>Returns a list of the Multi-Region Access Points currently associated with the specified Amazon Web Services account. Each call can return up to 100 Multi-Region Access Points, the maximum number of Multi-Region Access Points that can be associated with a single account.</p> <p>This action will always be routed to the US West (Oregon) Region. For more information about the restrictions around managing Multi-Region Access Points, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/ManagingMultiRegionAccessPoints.html\">Managing Multi-Region Access Points</a> in the <i>Amazon S3 User Guide</i>.</p> <p>The following actions are related to <code>ListMultiRegionAccessPoint</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_CreateMultiRegionAccessPoint.html\">CreateMultiRegionAccessPoint</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteMultiRegionAccessPoint.html\">DeleteMultiRegionAccessPoint</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DescribeMultiRegionAccessPointOperation.html\">DescribeMultiRegionAccessPointOperation</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetMultiRegionAccessPoint.html\">GetMultiRegionAccessPoint</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "httpChecksumRequired": true, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "ListRegionalBuckets": {"name": "ListRegionalBuckets", "http": {"method": "GET", "requestUri": "/v20180820/bucket"}, "input": {"shape": "ListRegionalBucketsRequest"}, "output": {"shape": "ListRegionalBucketsResult"}, "documentation": "<p>Returns a list of all Outposts buckets in an Outpost that are owned by the authenticated sender of the request. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3onOutposts.html\">Using Amazon S3 on Outposts</a> in the <i>Amazon S3 User Guide</i>.</p> <p>For an example of the request syntax for Amazon S3 on Outposts that uses the S3 on Outposts endpoint hostname prefix and <code>x-amz-outpost-id</code> in your request, see the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_ListRegionalBuckets.html#API_control_ListRegionalBuckets_Examples\">Examples</a> section.</p>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "ListStorageLensConfigurations": {"name": "ListStorageLensConfigurations", "http": {"method": "GET", "requestUri": "/v20180820/storagelens"}, "input": {"shape": "ListStorageLensConfigurationsRequest"}, "output": {"shape": "ListStorageLensConfigurationsResult"}, "documentation": "<p>Gets a list of Amazon S3 Storage Lens configurations. For more information about S3 Storage Lens, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/storage_lens.html\">Assessing your storage activity and usage with Amazon S3 Storage Lens </a> in the <i>Amazon S3 User Guide</i>.</p> <note> <p>To use this action, you must have permission to perform the <code>s3:ListStorageLensConfigurations</code> action. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/storage_lens_iam_permissions.html\">Setting permissions to use Amazon S3 Storage Lens</a> in the <i>Amazon S3 User Guide</i>.</p> </note>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "PutAccessPointConfigurationForObjectLambda": {"name": "PutAccessPointConfigurationForObjectLambda", "http": {"method": "PUT", "requestUri": "/v20180820/accesspointforobjectlambda/{name}/configuration"}, "input": {"shape": "PutAccessPointConfigurationForObjectLambdaRequest", "locationName": "PutAccessPointConfigurationForObjectLambdaRequest", "xmlNamespace": {"uri": "http://awss3control.amazonaws.com/doc/2018-08-20/"}}, "documentation": "<p>Replaces configuration for an Object Lambda Access Point.</p> <p>The following actions are related to <code>PutAccessPointConfigurationForObjectLambda</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetAccessPointConfigurationForObjectLambda.html\">GetAccessPointConfigurationForObjectLambda</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "PutAccessPointPolicy": {"name": "PutAccessPointPolicy", "http": {"method": "PUT", "requestUri": "/v20180820/accesspoint/{name}/policy"}, "input": {"shape": "PutAccessPointPolicyRequest", "locationName": "PutAccessPointPolicyRequest", "xmlNamespace": {"uri": "http://awss3control.amazonaws.com/doc/2018-08-20/"}}, "documentation": "<p>Associates an access policy with the specified access point. Each access point can have only one policy, so a request made to this API replaces any existing policy associated with the specified access point.</p> <p/> <p>All Amazon S3 on Outposts REST API requests for this action require an additional parameter of <code>x-amz-outpost-id</code> to be passed with the request. In addition, you must use an S3 on Outposts endpoint hostname prefix instead of <code>s3-control</code>. For an example of the request syntax for Amazon S3 on Outposts that uses the S3 on Outposts endpoint hostname prefix and the <code>x-amz-outpost-id</code> derived by using the access point ARN, see the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutAccessPointPolicy.html#API_control_PutAccessPointPolicy_Examples\">Examples</a> section.</p> <p>The following actions are related to <code>PutAccessPointPolicy</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetAccessPointPolicy.html\">GetAccessPointPolicy</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteAccessPointPolicy.html\">DeleteAccessPointPolicy</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "PutAccessPointPolicyForObjectLambda": {"name": "PutAccessPointPolicyForObjectLambda", "http": {"method": "PUT", "requestUri": "/v20180820/accesspointforobjectlambda/{name}/policy"}, "input": {"shape": "PutAccessPointPolicyForObjectLambdaRequest", "locationName": "PutAccessPointPolicyForObjectLambdaRequest", "xmlNamespace": {"uri": "http://awss3control.amazonaws.com/doc/2018-08-20/"}}, "documentation": "<p>Creates or replaces resource policy for an Object Lambda Access Point. For an example policy, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/olap-create.html#olap-create-cli\">Creating Object Lambda Access Points</a> in the <i>Amazon S3 User Guide</i>.</p> <p>The following actions are related to <code>PutAccessPointPolicyForObjectLambda</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteAccessPointPolicyForObjectLambda.html\">DeleteAccessPointPolicyForObjectLambda</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetAccessPointPolicyForObjectLambda.html\">GetAccessPointPolicyForObjectLambda</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "PutBucketLifecycleConfiguration": {"name": "PutBucketLifecycleConfiguration", "http": {"method": "PUT", "requestUri": "/v20180820/bucket/{name}/lifecycleconfiguration"}, "input": {"shape": "PutBucketLifecycleConfigurationRequest"}, "documentation": "<note> <p>This action puts a lifecycle configuration to an Amazon S3 on Outposts bucket. To put a lifecycle configuration to an S3 bucket, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_PutBucketLifecycleConfiguration.html\">PutBucketLifecycleConfiguration</a> in the <i>Amazon S3 API Reference</i>. </p> </note> <p>Creates a new lifecycle configuration for the S3 on Outposts bucket or replaces an existing lifecycle configuration. Outposts buckets only support lifecycle configurations that delete/expire objects after a certain period of time and abort incomplete multipart uploads.</p> <p/> <p>All Amazon S3 on Outposts REST API requests for this action require an additional parameter of <code>x-amz-outpost-id</code> to be passed with the request. In addition, you must use an S3 on Outposts endpoint hostname prefix instead of <code>s3-control</code>. For an example of the request syntax for Amazon S3 on Outposts that uses the S3 on Outposts endpoint hostname prefix and the <code>x-amz-outpost-id</code> derived by using the access point ARN, see the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutBucketLifecycleConfiguration.html#API_control_PutBucketLifecycleConfiguration_Examples\">Examples</a> section.</p> <p>The following actions are related to <code>PutBucketLifecycleConfiguration</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetBucketLifecycleConfiguration.html\">GetBucketLifecycleConfiguration</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteBucketLifecycleConfiguration.html\">DeleteBucketLifecycleConfiguration</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "httpChecksumRequired": true, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "PutBucketPolicy": {"name": "PutBucketPolicy", "http": {"method": "PUT", "requestUri": "/v20180820/bucket/{name}/policy"}, "input": {"shape": "PutBucketPolicyRequest", "locationName": "PutBucketPolicyRequest", "xmlNamespace": {"uri": "http://awss3control.amazonaws.com/doc/2018-08-20/"}}, "documentation": "<note> <p>This action puts a bucket policy to an Amazon S3 on Outposts bucket. To put a policy on an S3 bucket, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_PutBucketPolicy.html\">PutBucketPolicy</a> in the <i>Amazon S3 API Reference</i>. </p> </note> <p>Applies an Amazon S3 bucket policy to an Outposts bucket. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3onOutposts.html\">Using Amazon S3 on Outposts</a> in the <i>Amazon S3 User Guide</i>.</p> <p>If you are using an identity other than the root user of the Amazon Web Services account that owns the Outposts bucket, the calling identity must have the <code>PutBucketPolicy</code> permissions on the specified Outposts bucket and belong to the bucket owner's account in order to use this action.</p> <p>If you don't have <code>PutBucketPolicy</code> permissions, Amazon S3 returns a <code>403 Access Denied</code> error. If you have the correct permissions, but you're not using an identity that belongs to the bucket owner's account, Amazon S3 returns a <code>405 Method Not Allowed</code> error.</p> <important> <p> As a security precaution, the root user of the Amazon Web Services account that owns a bucket can always use this action, even if the policy explicitly denies the root user the ability to perform this action. </p> </important> <p>For more information about bucket policies, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/using-iam-policies.html\">Using Bucket Policies and User Policies</a>.</p> <p>All Amazon S3 on Outposts REST API requests for this action require an additional parameter of <code>x-amz-outpost-id</code> to be passed with the request. In addition, you must use an S3 on Outposts endpoint hostname prefix instead of <code>s3-control</code>. For an example of the request syntax for Amazon S3 on Outposts that uses the S3 on Outposts endpoint hostname prefix and the <code>x-amz-outpost-id</code> derived by using the access point ARN, see the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutBucketPolicy.html#API_control_PutBucketPolicy_Examples\">Examples</a> section.</p> <p>The following actions are related to <code>PutBucketPolicy</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetBucketPolicy.html\">GetBucketPolicy</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteBucketPolicy.html\">DeleteBucketPolicy</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "httpChecksumRequired": true, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "PutBucketReplication": {"name": "PutBucketReplication", "http": {"method": "PUT", "requestUri": "/v20180820/bucket/{name}/replication"}, "input": {"shape": "PutBucketReplicationRequest"}, "documentation": "<note> <p>This action creates an Amazon S3 on Outposts bucket's replication configuration. To create an S3 bucket's replication configuration, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_PutBucketReplication.html\">PutBucketReplication</a> in the <i>Amazon S3 API Reference</i>. </p> </note> <p>Creates a replication configuration or replaces an existing one. For information about S3 replication on Outposts configuration, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3OutpostsReplication.html\">Replicating objects for S3 on Outposts</a> in the <i>Amazon S3 User Guide</i>.</p> <note> <p>It can take a while to propagate <code>PUT</code> or <code>DELETE</code> requests for a replication configuration to all S3 on Outposts systems. Therefore, the replication configuration that's returned by a <code>GET</code> request soon after a <code>PUT</code> or <code>DELETE</code> request might return a more recent result than what's on the Outpost. If an Outpost is offline, the delay in updating the replication configuration on that Outpost can be significant.</p> </note> <p>Specify the replication configuration in the request body. In the replication configuration, you provide the following information:</p> <ul> <li> <p>The name of the destination bucket or buckets where you want S3 on Outposts to replicate objects</p> </li> <li> <p>The Identity and Access Management (IAM) role that S3 on Outposts can assume to replicate objects on your behalf</p> </li> <li> <p>Other relevant information, such as replication rules</p> </li> </ul> <p>A replication configuration must include at least one rule and can contain a maximum of 100. Each rule identifies a subset of objects to replicate by filtering the objects in the source Outposts bucket. To choose additional subsets of objects to replicate, add a rule for each subset.</p> <p>To specify a subset of the objects in the source Outposts bucket to apply a replication rule to, add the <code>Filter</code> element as a child of the <code>Rule</code> element. You can filter objects based on an object key prefix, one or more object tags, or both. When you add the <code>Filter</code> element in the configuration, you must also add the following elements: <code>DeleteMarkerReplication</code>, <code>Status</code>, and <code>Priority</code>.</p> <p>Using <code>PutBucketReplication</code> on Outposts requires that both the source and destination buckets must have versioning enabled. For information about enabling versioning on a bucket, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3OutpostsManagingVersioning.html\">Managing S3 Versioning for your S3 on Outposts bucket</a>.</p> <p>For information about S3 on Outposts replication failure reasons, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/outposts-replication-eventbridge.html#outposts-replication-failure-codes\">Replication failure reasons</a> in the <i>Amazon S3 User Guide</i>.</p> <p> <b>Handling Replication of Encrypted Objects</b> </p> <p>Outposts buckets are encrypted at all times. All the objects in the source Outposts bucket are encrypted and can be replicated. Also, all the replicas in the destination Outposts bucket are encrypted with the same encryption key as the objects in the source Outposts bucket.</p> <p> <b>Permissions</b> </p> <p>To create a <code>PutBucketReplication</code> request, you must have <code>s3-outposts:PutReplicationConfiguration</code> permissions for the bucket. The Outposts bucket owner has this permission by default and can grant it to others. For more information about permissions, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3OutpostsIAM.html\">Setting up IAM with S3 on Outposts</a> and <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3OutpostsBucketPolicy.html\">Managing access to S3 on Outposts buckets</a>. </p> <note> <p>To perform this operation, the user or role must also have the <code>iam:CreateRole</code> and <code>iam:PassRole</code> permissions. For more information, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/id_roles_use_passrole.html\">Granting a user permissions to pass a role to an Amazon Web Services service</a>.</p> </note> <p>All Amazon S3 on Outposts REST API requests for this action require an additional parameter of <code>x-amz-outpost-id</code> to be passed with the request. In addition, you must use an S3 on Outposts endpoint hostname prefix instead of <code>s3-control</code>. For an example of the request syntax for Amazon S3 on Outposts that uses the S3 on Outposts endpoint hostname prefix and the <code>x-amz-outpost-id</code> derived by using the access point ARN, see the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutBucketReplication.html#API_control_PutBucketReplication_Examples\">Examples</a> section.</p> <p>The following operations are related to <code>PutBucketReplication</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetBucketReplication.html\">GetBucketReplication</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteBucketReplication.html\">DeleteBucketReplication</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "httpChecksumRequired": true, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "PutBucketTagging": {"name": "PutBucketTagging", "http": {"method": "PUT", "requestUri": "/v20180820/bucket/{name}/tagging"}, "input": {"shape": "PutBucketTaggingRequest"}, "documentation": "<note> <p>This action puts tags on an Amazon S3 on Outposts bucket. To put tags on an S3 bucket, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_PutBucketTagging.html\">PutBucketTagging</a> in the <i>Amazon S3 API Reference</i>. </p> </note> <p>Sets the tags for an S3 on Outposts bucket. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3onOutposts.html\">Using Amazon S3 on Outposts</a> in the <i>Amazon S3 User Guide</i>.</p> <p>Use tags to organize your Amazon Web Services bill to reflect your own cost structure. To do this, sign up to get your Amazon Web Services account bill with tag key values included. Then, to see the cost of combined resources, organize your billing information according to resources with the same tag key values. For example, you can tag several resources with a specific application name, and then organize your billing information to see the total cost of that application across several services. For more information, see <a href=\"https://docs.aws.amazon.com/awsaccountbilling/latest/aboutv2/cost-alloc-tags.html\">Cost allocation and tagging</a>.</p> <note> <p>Within a bucket, if you add a tag that has the same key as an existing tag, the new value overwrites the old value. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/CostAllocTagging.html\"> Using cost allocation in Amazon S3 bucket tags</a>.</p> </note> <p>To use this action, you must have permissions to perform the <code>s3-outposts:PutBucketTagging</code> action. The Outposts bucket owner has this permission by default and can grant this permission to others. For more information about permissions, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/using-with-s3-actions.html#using-with-s3-actions-related-to-bucket-subresources\"> Permissions Related to Bucket Subresource Operations</a> and <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/s3-access-control.html\">Managing access permissions to your Amazon S3 resources</a>.</p> <p> <code>PutBucketTagging</code> has the following special errors:</p> <ul> <li> <p>Error code: <code>InvalidTagError</code> </p> <ul> <li> <p>Description: The tag provided was not a valid tag. This error can occur if the tag did not pass input validation. For information about tag restrictions, see <a href=\"https://docs.aws.amazon.com/awsaccountbilling/latest/aboutv2/allocation-tag-restrictions.html\"> User-Defined Tag Restrictions</a> and <a href=\"https://docs.aws.amazon.com/awsaccountbilling/latest/aboutv2/aws-tag-restrictions.html\"> Amazon Web Services-Generated Cost Allocation Tag Restrictions</a>.</p> </li> </ul> </li> <li> <p>Error code: <code>MalformedXMLError</code> </p> <ul> <li> <p>Description: The XML provided does not match the schema.</p> </li> </ul> </li> <li> <p>Error code: <code>OperationAbortedError </code> </p> <ul> <li> <p>Description: A conflicting conditional action is currently in progress against this resource. Try again.</p> </li> </ul> </li> <li> <p>Error code: <code>InternalError</code> </p> <ul> <li> <p>Description: The service was unable to apply the provided tag to the bucket.</p> </li> </ul> </li> </ul> <p>All Amazon S3 on Outposts REST API requests for this action require an additional parameter of <code>x-amz-outpost-id</code> to be passed with the request. In addition, you must use an S3 on Outposts endpoint hostname prefix instead of <code>s3-control</code>. For an example of the request syntax for Amazon S3 on Outposts that uses the S3 on Outposts endpoint hostname prefix and the <code>x-amz-outpost-id</code> derived by using the access point ARN, see the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutBucketTagging.html#API_control_PutBucketTagging_Examples\">Examples</a> section.</p> <p>The following actions are related to <code>PutBucketTagging</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetBucketTagging.html\">GetBucketTagging</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteBucketTagging.html\">DeleteBucketTagging</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "httpChecksumRequired": true, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "PutBucketVersioning": {"name": "PutBucketVersioning", "http": {"method": "PUT", "requestUri": "/v20180820/bucket/{name}/versioning"}, "input": {"shape": "PutBucketVersioningRequest"}, "documentation": "<note> <p>This operation sets the versioning state for S3 on Outposts buckets only. To set the versioning state for an S3 bucket, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_PutBucketVersioning.html\">PutBucketVersioning</a> in the <i>Amazon S3 API Reference</i>. </p> </note> <p>Sets the versioning state for an S3 on Outposts bucket. With S3 Versioning, you can save multiple distinct copies of your objects and recover from unintended user actions and application failures.</p> <p>You can set the versioning state to one of the following:</p> <ul> <li> <p> <b>Enabled</b> - Enables versioning for the objects in the bucket. All objects added to the bucket receive a unique version ID.</p> </li> <li> <p> <b>Suspended</b> - Suspends versioning for the objects in the bucket. All objects added to the bucket receive the version ID <code>null</code>.</p> </li> </ul> <p>If you've never set versioning on your bucket, it has no versioning state. In that case, a <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetBucketVersioning.html\"> GetBucketVersioning</a> request does not return a versioning state value.</p> <p>When you enable S3 Versioning, for each object in your bucket, you have a current version and zero or more noncurrent versions. You can configure your bucket S3 Lifecycle rules to expire noncurrent versions after a specified time period. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3OutpostsLifecycleManaging.html\"> Creating and managing a lifecycle configuration for your S3 on Outposts bucket</a> in the <i>Amazon S3 User Guide</i>.</p> <p>If you have an object expiration lifecycle configuration in your non-versioned bucket and you want to maintain the same permanent delete behavior when you enable versioning, you must add a noncurrent expiration policy. The noncurrent expiration lifecycle configuration will manage the deletes of the noncurrent object versions in the version-enabled bucket. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/Versioning.html\">Versioning</a> in the <i>Amazon S3 User Guide</i>.</p> <p>All Amazon S3 on Outposts REST API requests for this action require an additional parameter of <code>x-amz-outpost-id</code> to be passed with the request. In addition, you must use an S3 on Outposts endpoint hostname prefix instead of <code>s3-control</code>. For an example of the request syntax for Amazon S3 on Outposts that uses the S3 on Outposts endpoint hostname prefix and the <code>x-amz-outpost-id</code> derived by using the access point ARN, see the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutBucketVersioning.html#API_control_PutBucketVersioning_Examples\">Examples</a> section.</p> <p>The following operations are related to <code>PutBucketVersioning</code> for S3 on Outposts.</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetBucketVersioning.html\">GetBucketVersioning</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutBucketLifecycleConfiguration.html\">PutBucketLifecycleConfiguration</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetBucketLifecycleConfiguration.html\">GetBucketLifecycleConfiguration</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "httpChecksumRequired": true, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "PutJobTagging": {"name": "PutJobTagging", "http": {"method": "PUT", "requestUri": "/v20180820/jobs/{id}/tagging"}, "input": {"shape": "PutJobTaggingRequest", "locationName": "PutJobTaggingRequest", "xmlNamespace": {"uri": "http://awss3control.amazonaws.com/doc/2018-08-20/"}}, "output": {"shape": "PutJobTaggingResult"}, "errors": [{"shape": "InternalServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "NotFoundException"}, {"shape": "TooManyTagsException"}], "documentation": "<p>Sets the supplied tag-set on an S3 Batch Operations job.</p> <p>A tag is a key-value pair. You can associate S3 Batch Operations tags with any job by sending a PUT request against the tagging subresource that is associated with the job. To modify the existing tag set, you can either replace the existing tag set entirely, or make changes within the existing tag set by retrieving the existing tag set using <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetJobTagging.html\">GetJobTagging</a>, modify that tag set, and use this action to replace the tag set with the one you modified. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/batch-ops-managing-jobs.html#batch-ops-job-tags\">Controlling access and labeling jobs using tags</a> in the <i>Amazon S3 User Guide</i>. </p> <p/> <note> <ul> <li> <p>If you send this request with an empty tag set, Amazon S3 deletes the existing tag set on the Batch Operations job. If you use this method, you are charged for a Tier 1 Request (PUT). For more information, see <a href=\"http://aws.amazon.com/s3/pricing/\">Amazon S3 pricing</a>.</p> </li> <li> <p>For deleting existing tags for your Batch Operations job, a <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteJobTagging.html\">DeleteJobTagging</a> request is preferred because it achieves the same result without incurring charges.</p> </li> <li> <p>A few things to consider about using tags:</p> <ul> <li> <p>Amazon S3 limits the maximum number of tags to 50 tags per job.</p> </li> <li> <p>You can associate up to 50 tags with a job as long as they have unique tag keys.</p> </li> <li> <p>A tag key can be up to 128 Unicode characters in length, and tag values can be up to 256 Unicode characters in length.</p> </li> <li> <p>The key and values are case sensitive.</p> </li> <li> <p>For tagging-related restrictions related to characters and encodings, see <a href=\"https://docs.aws.amazon.com/awsaccountbilling/latest/aboutv2/allocation-tag-restrictions.html\">User-Defined Tag Restrictions</a> in the <i>Billing and Cost Management User Guide</i>.</p> </li> </ul> </li> </ul> </note> <p/> <p>To use the <code>PutJobTagging</code> operation, you must have permission to perform the <code>s3:PutJobTagging</code> action.</p> <p>Related actions include:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_CreateJob.html\">CreateJob</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetJobTagging.html\">GetJobTagging</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteJobTagging.html\">DeleteJobTagging</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "PutMultiRegionAccessPointPolicy": {"name": "PutMultiRegionAccessPointPolicy", "http": {"method": "POST", "requestUri": "/v20180820/async-requests/mrap/put-policy"}, "input": {"shape": "PutMultiRegionAccessPointPolicyRequest", "locationName": "PutMultiRegionAccessPointPolicyRequest", "xmlNamespace": {"uri": "http://awss3control.amazonaws.com/doc/2018-08-20/"}}, "output": {"shape": "PutMultiRegionAccessPointPolicyResult"}, "documentation": "<p>Associates an access control policy with the specified Multi-Region Access Point. Each Multi-Region Access Point can have only one policy, so a request made to this action replaces any existing policy that is associated with the specified Multi-Region Access Point.</p> <p>This action will always be routed to the US West (Oregon) Region. For more information about the restrictions around managing Multi-Region Access Points, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/ManagingMultiRegionAccessPoints.html\">Managing Multi-Region Access Points</a> in the <i>Amazon S3 User Guide</i>.</p> <p>The following actions are related to <code>PutMultiRegionAccessPointPolicy</code>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetMultiRegionAccessPointPolicy.html\">GetMultiRegionAccessPointPolicy</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetMultiRegionAccessPointPolicyStatus.html\">GetMultiRegionAccessPointPolicyStatus</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "httpChecksumRequired": true, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "PutPublicAccessBlock": {"name": "PutPublicAccessBlock", "http": {"method": "PUT", "requestUri": "/v20180820/configuration/publicAccessBlock"}, "input": {"shape": "PutPublicAccessBlockRequest"}, "documentation": "<p>Creates or modifies the <code>PublicAccessBlock</code> configuration for an Amazon Web Services account. For this operation, users must have the <code>s3:PutAccountPublicAccessBlock</code> permission. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/access-control-block-public-access.html\"> Using Amazon S3 block public access</a>.</p> <p>Related actions include:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_GetPublicAccessBlock.html\">GetPublicAccessBlock</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeletePublicAccessBlock.html\">DeletePublicAccessBlock</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "PutStorageLensConfiguration": {"name": "PutStorageLensConfiguration", "http": {"method": "PUT", "requestUri": "/v20180820/storagelens/{storagelensid}"}, "input": {"shape": "PutStorageLensConfigurationRequest", "locationName": "PutStorageLensConfigurationRequest", "xmlNamespace": {"uri": "http://awss3control.amazonaws.com/doc/2018-08-20/"}}, "documentation": "<p>Puts an Amazon S3 Storage Lens configuration. For more information about S3 Storage Lens, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/storage_lens.html\">Working with Amazon S3 Storage Lens</a> in the <i>Amazon S3 User Guide</i>. For a complete list of S3 Storage Lens metrics, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/storage_lens_metrics_glossary.html\">S3 Storage Lens metrics glossary</a> in the <i>Amazon S3 User Guide</i>.</p> <note> <p>To use this action, you must have permission to perform the <code>s3:PutStorageLensConfiguration</code> action. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/storage_lens_iam_permissions.html\">Setting permissions to use Amazon S3 Storage Lens</a> in the <i>Amazon S3 User Guide</i>.</p> </note>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "PutStorageLensConfigurationTagging": {"name": "PutStorageLensConfigurationTagging", "http": {"method": "PUT", "requestUri": "/v20180820/storagelens/{storagelensid}/tagging"}, "input": {"shape": "PutStorageLensConfigurationTaggingRequest", "locationName": "PutStorageLensConfigurationTaggingRequest", "xmlNamespace": {"uri": "http://awss3control.amazonaws.com/doc/2018-08-20/"}}, "output": {"shape": "PutStorageLensConfigurationTaggingResult"}, "documentation": "<p>Put or replace tags on an existing Amazon S3 Storage Lens configuration. For more information about S3 Storage Lens, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/storage_lens.html\">Assessing your storage activity and usage with Amazon S3 Storage Lens </a> in the <i>Amazon S3 User Guide</i>.</p> <note> <p>To use this action, you must have permission to perform the <code>s3:PutStorageLensConfigurationTagging</code> action. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/storage_lens_iam_permissions.html\">Setting permissions to use Amazon S3 Storage Lens</a> in the <i>Amazon S3 User Guide</i>.</p> </note>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "SubmitMultiRegionAccessPointRoutes": {"name": "SubmitMultiRegionAccessPointRoutes", "http": {"method": "PATCH", "requestUri": "/v20180820/mrap/instances/{mrap+}/routes"}, "input": {"shape": "SubmitMultiRegionAccessPointRoutesRequest", "locationName": "SubmitMultiRegionAccessPointRoutesRequest", "xmlNamespace": {"uri": "http://awss3control.amazonaws.com/doc/2018-08-20/"}}, "output": {"shape": "SubmitMultiRegionAccessPointRoutesResult"}, "documentation": "<p>Submits an updated route configuration for a Multi-Region Access Point. This API operation updates the routing status for the specified Regions from active to passive, or from passive to active. A value of <code>0</code> indicates a passive status, which means that traffic won't be routed to the specified Region. A value of <code>100</code> indicates an active status, which means that traffic will be routed to the specified Region. At least one Region must be active at all times.</p> <p>When the routing configuration is changed, any in-progress operations (uploads, copies, deletes, and so on) to formerly active Regions will continue to run to their final completion state (success or failure). The routing configurations of any Regions that aren’t specified remain unchanged.</p> <note> <p>Updated routing configurations might not be immediately applied. It can take up to 2 minutes for your changes to take effect.</p> </note> <p>To submit routing control changes and failover requests, use the Amazon S3 failover control infrastructure endpoints in these five Amazon Web Services Regions:</p> <ul> <li> <p> <code>us-east-1</code> </p> </li> <li> <p> <code>us-west-2</code> </p> </li> <li> <p> <code>ap-southeast-2</code> </p> </li> <li> <p> <code>ap-northeast-1</code> </p> </li> <li> <p> <code>eu-west-1</code> </p> </li> </ul> <note> <p>Your Amazon S3 bucket does not need to be in these five Regions.</p> </note>", "endpoint": {"hostPrefix": "{AccountId}."}, "httpChecksumRequired": true, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "UpdateJobPriority": {"name": "UpdateJobPriority", "http": {"method": "POST", "requestUri": "/v20180820/jobs/{id}/priority"}, "input": {"shape": "UpdateJobPriorityRequest"}, "output": {"shape": "UpdateJobPriorityResult"}, "errors": [{"shape": "BadRequestException"}, {"shape": "TooManyRequestsException"}, {"shape": "NotFoundException"}, {"shape": "InternalServiceException"}], "documentation": "<p>Updates an existing S3 Batch Operations job's priority. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/batch-ops.html\">S3 Batch Operations</a> in the <i>Amazon S3 User Guide</i>.</p> <p/> <p>Related actions include:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_CreateJob.html\">CreateJob</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_ListJobs.html\">ListJobs</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DescribeJob.html\">DescribeJob</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_UpdateJobStatus.html\">UpdateJobStatus</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}, "UpdateJobStatus": {"name": "UpdateJobStatus", "http": {"method": "POST", "requestUri": "/v20180820/jobs/{id}/status"}, "input": {"shape": "UpdateJobStatusRequest"}, "output": {"shape": "UpdateJobStatusResult"}, "errors": [{"shape": "BadRequestException"}, {"shape": "TooManyRequestsException"}, {"shape": "NotFoundException"}, {"shape": "JobStatusException"}, {"shape": "InternalServiceException"}], "documentation": "<p>Updates the status for the specified job. Use this action to confirm that you want to run a job or to cancel an existing job. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/batch-ops.html\">S3 Batch Operations</a> in the <i>Amazon S3 User Guide</i>.</p> <p/> <p>Related actions include:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_CreateJob.html\">CreateJob</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_ListJobs.html\">ListJobs</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DescribeJob.html\">DescribeJob</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_UpdateJobStatus.html\">UpdateJobStatus</a> </p> </li> </ul>", "endpoint": {"hostPrefix": "{AccountId}."}, "staticContextParams": {"RequiresAccountId": {"value": true}}}}, "shapes": {"AbortIncompleteMultipartUpload": {"type": "structure", "members": {"DaysAfterInitiation": {"shape": "DaysAfterInitiation", "documentation": "<p>Specifies the number of days after which Amazon S3 aborts an incomplete multipart upload to the Outposts bucket.</p>"}}, "documentation": "<p>The container for abort incomplete multipart upload</p>"}, "AccessControlTranslation": {"type": "structure", "required": ["Owner"], "members": {"Owner": {"shape": "OwnerOverride", "documentation": "<p>Specifies the replica ownership.</p>"}}, "documentation": "<p>A container for information about access control for replicas.</p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>"}, "AccessPoint": {"type": "structure", "required": ["Name", "NetworkOrigin", "Bucket"], "members": {"Name": {"shape": "AccessPointName", "documentation": "<p>The name of this access point.</p>"}, "NetworkOrigin": {"shape": "NetworkOrigin", "documentation": "<p>Indicates whether this access point allows access from the public internet. If <code>VpcConfiguration</code> is specified for this access point, then <code>NetworkOrigin</code> is <code>VPC</code>, and the access point doesn't allow access from the public internet. Otherwise, <code>NetworkOrigin</code> is <code>Internet</code>, and the access point allows access from the public internet, subject to the access point and bucket access policies.</p>"}, "VpcConfiguration": {"shape": "VpcConfiguration", "documentation": "<p>The virtual private cloud (VPC) configuration for this access point, if one exists.</p> <note> <p>This element is empty if this access point is an Amazon S3 on Outposts access point that is used by other Amazon Web Services.</p> </note>"}, "Bucket": {"shape": "BucketName", "documentation": "<p>The name of the bucket associated with this access point.</p>"}, "AccessPointArn": {"shape": "S3AccessPointArn", "documentation": "<p>The ARN for the access point.</p>"}, "Alias": {"shape": "<PERSON><PERSON>", "documentation": "<p>The name or alias of the access point.</p>"}, "BucketAccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID associated with the S3 bucket associated with this access point.</p>"}}, "documentation": "<p>An access point used to access a bucket.</p>"}, "AccessPointList": {"type": "list", "member": {"shape": "AccessPoint", "locationName": "AccessPoint"}}, "AccessPointName": {"type": "string", "max": 255, "min": 3}, "AccountId": {"type": "string", "max": 64, "pattern": "^\\d{12}$"}, "AccountLevel": {"type": "structure", "required": ["BucketLevel"], "members": {"ActivityMetrics": {"shape": "ActivityMetrics", "documentation": "<p>A container for S3 Storage Lens activity metrics.</p>"}, "BucketLevel": {"shape": "BucketLevel", "documentation": "<p>A container for the S3 Storage Lens bucket-level configuration.</p>"}, "AdvancedCostOptimizationMetrics": {"shape": "AdvancedCostOptimizationMetrics", "documentation": "<p>A container for S3 Storage Lens advanced cost-optimization metrics.</p>"}, "AdvancedDataProtectionMetrics": {"shape": "AdvancedDataProtectionMetrics", "documentation": "<p>A container for S3 Storage Lens advanced data-protection metrics.</p>"}, "DetailedStatusCodesMetrics": {"shape": "DetailedStatusCodesMetrics", "documentation": "<p>A container for detailed status code metrics. </p>"}}, "documentation": "<p>A container for the account-level Amazon S3 Storage Lens configuration.</p> <p>For more information about S3 Storage Lens, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/storage_lens.html\">Assessing your storage activity and usage with S3 Storage Lens</a> in the <i>Amazon S3 User Guide</i>. For a complete list of S3 Storage Lens metrics, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/storage_lens_metrics_glossary.html\">S3 Storage Lens metrics glossary</a> in the <i>Amazon S3 User Guide</i>.</p>"}, "ActivityMetrics": {"type": "structure", "members": {"IsEnabled": {"shape": "IsEnabled", "documentation": "<p>A container that indicates whether activity metrics are enabled.</p>"}}, "documentation": "<p>The container element for Amazon S3 Storage Lens activity metrics. Activity metrics show details about how your storage is requested, such as requests (for example, All requests, Get requests, Put requests), bytes uploaded or downloaded, and errors.</p> <p>For more information about S3 Storage Lens, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/storage_lens.html\">Assessing your storage activity and usage with S3 Storage Lens</a> in the <i>Amazon S3 User Guide</i>. For a complete list of S3 Storage Lens metrics, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/storage_lens_metrics_glossary.html\">S3 Storage Lens metrics glossary</a> in the <i>Amazon S3 User Guide</i>.</p>"}, "AdvancedCostOptimizationMetrics": {"type": "structure", "members": {"IsEnabled": {"shape": "IsEnabled", "documentation": "<p>A container that indicates whether advanced cost-optimization metrics are enabled.</p>"}}, "documentation": "<p>The container element for Amazon S3 Storage Lens advanced cost-optimization metrics. Advanced cost-optimization metrics provide insights that you can use to manage and optimize your storage costs, for example, lifecycle rule counts for transitions, expirations, and incomplete multipart uploads.</p> <p>For more information about S3 Storage Lens, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/storage_lens.html\">Assessing your storage activity and usage with S3 Storage Lens</a> in the <i>Amazon S3 User Guide</i>. For a complete list of S3 Storage Lens metrics, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/storage_lens_metrics_glossary.html\">S3 Storage Lens metrics glossary</a> in the <i>Amazon S3 User Guide</i>.</p>"}, "AdvancedDataProtectionMetrics": {"type": "structure", "members": {"IsEnabled": {"shape": "IsEnabled", "documentation": "<p>A container that indicates whether advanced data-protection metrics are enabled.</p>"}}, "documentation": "<p>The container element for Amazon S3 Storage Lens advanced data-protection metrics. Advanced data-protection metrics provide insights that you can use to perform audits and protect your data, for example replication rule counts within and across Regions.</p> <p>For more information about S3 Storage Lens, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/storage_lens.html\">Assessing your storage activity and usage with S3 Storage Lens</a> in the <i>Amazon S3 User Guide</i>. For a complete list of S3 Storage Lens metrics, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/storage_lens_metrics_glossary.html\">S3 Storage Lens metrics glossary</a> in the <i>Amazon S3 User Guide</i>.</p>"}, "Alias": {"type": "string", "max": 63, "pattern": "^[0-9a-z\\\\-]{63}"}, "AsyncCreationTimestamp": {"type": "timestamp"}, "AsyncErrorDetails": {"type": "structure", "members": {"Code": {"shape": "MaxLength1024String", "documentation": "<p>A string that uniquely identifies the error condition.</p>"}, "Message": {"shape": "MaxLength1024String", "documentation": "<p>A generic description of the error condition in English.</p>"}, "Resource": {"shape": "MaxLength1024String", "documentation": "<p>The identifier of the resource associated with the error.</p>"}, "RequestId": {"shape": "MaxLength1024String", "documentation": "<p>The ID of the request associated with the error.</p>"}}, "documentation": "<p>Error details for the failed asynchronous operation.</p>"}, "AsyncOperation": {"type": "structure", "members": {"CreationTime": {"shape": "AsyncCreationTimestamp", "documentation": "<p>The time that the request was sent to the service.</p>"}, "Operation": {"shape": "AsyncOperationName", "documentation": "<p>The specific operation for the asynchronous request.</p>"}, "RequestTokenARN": {"shape": "AsyncRequestTokenARN", "documentation": "<p>The request token associated with the request.</p>"}, "RequestParameters": {"shape": "AsyncRequestParameters", "documentation": "<p>The parameters associated with the request.</p>"}, "RequestStatus": {"shape": "AsyncRequestStatus", "documentation": "<p>The current status of the request.</p>"}, "ResponseDetails": {"shape": "AsyncResponseDetails", "documentation": "<p>The details of the response.</p>"}}, "documentation": "<p>A container for the information about an asynchronous operation.</p>"}, "AsyncOperationName": {"type": "string", "enum": ["CreateMultiRegionAccessPoint", "DeleteMultiRegionAccessPoint", "PutMultiRegionAccessPointPolicy"]}, "AsyncRequestParameters": {"type": "structure", "members": {"CreateMultiRegionAccessPointRequest": {"shape": "CreateMultiRegionAccessPointInput", "documentation": "<p>A container of the parameters for a <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_CreateMultiRegionAccessPoint.html\">CreateMultiRegionAccessPoint</a> request.</p>"}, "DeleteMultiRegionAccessPointRequest": {"shape": "DeleteMultiRegionAccessPointInput", "documentation": "<p>A container of the parameters for a <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteMultiRegionAccessPoint.html\">DeleteMultiRegionAccessPoint</a> request.</p>"}, "PutMultiRegionAccessPointPolicyRequest": {"shape": "PutMultiRegionAccessPointPolicyInput", "documentation": "<p>A container of the parameters for a <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutMultiRegionAccessPoint.html\">PutMultiRegionAccessPoint</a> request.</p>"}}, "documentation": "<p>A container for the request parameters associated with an asynchronous request.</p>"}, "AsyncRequestStatus": {"type": "string"}, "AsyncRequestTokenARN": {"type": "string", "max": 1024, "min": 1, "pattern": "arn:.+"}, "AsyncResponseDetails": {"type": "structure", "members": {"MultiRegionAccessPointDetails": {"shape": "MultiRegionAccessPointsAsyncResponse", "documentation": "<p>The details for the Multi-Region Access Point.</p>"}, "ErrorDetails": {"shape": "AsyncErrorDetails", "documentation": "<p>Error details for an asynchronous request.</p>"}}, "documentation": "<p>A container for the response details that are returned when querying about an asynchronous request.</p>"}, "AwsLambdaTransformation": {"type": "structure", "required": ["FunctionArn"], "members": {"FunctionArn": {"shape": "FunctionArnString", "documentation": "<p>The Amazon Resource Name (ARN) of the Lambda function.</p>"}, "FunctionPayload": {"shape": "AwsLambdaTransformationPayload", "documentation": "<p>Additional JSON that provides supplemental data to the Lambda function used to transform objects.</p>"}}, "documentation": "<p>Lambda function used to transform objects through an Object Lambda Access Point.</p>"}, "AwsLambdaTransformationPayload": {"type": "string"}, "AwsOrgArn": {"type": "string", "max": 1024, "min": 1, "pattern": "arn:[a-z\\-]+:organizations::\\d{12}:organization\\/o-[a-z0-9]{10,32}"}, "BadRequestException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p/>", "exception": true}, "Boolean": {"type": "boolean"}, "BucketAlreadyExists": {"type": "structure", "members": {}, "documentation": "<p>The requested Outposts bucket name is not available. The bucket namespace is shared by all users of the Outposts in this Region. Select a different name and try again.</p>", "exception": true}, "BucketAlreadyOwnedByYou": {"type": "structure", "members": {}, "documentation": "<p>The Outposts bucket you tried to create already exists, and you own it. </p>", "exception": true}, "BucketCannedACL": {"type": "string", "enum": ["private", "public-read", "public-read-write", "authenticated-read"]}, "BucketIdentifierString": {"type": "string"}, "BucketLevel": {"type": "structure", "members": {"ActivityMetrics": {"shape": "ActivityMetrics", "documentation": "<p>A container for the bucket-level activity metrics for S3 Storage Lens.</p>"}, "PrefixLevel": {"shape": "PrefixLevel", "documentation": "<p>A container for the prefix-level metrics for S3 Storage Lens. </p>"}, "AdvancedCostOptimizationMetrics": {"shape": "AdvancedCostOptimizationMetrics", "documentation": "<p>A container for bucket-level advanced cost-optimization metrics for S3 Storage Lens.</p>"}, "AdvancedDataProtectionMetrics": {"shape": "AdvancedDataProtectionMetrics", "documentation": "<p>A container for bucket-level advanced data-protection metrics for S3 Storage Lens.</p>"}, "DetailedStatusCodesMetrics": {"shape": "DetailedStatusCodesMetrics", "documentation": "<p>A container for bucket-level detailed status code metrics for S3 Storage Lens.</p>"}}, "documentation": "<p>A container for the bucket-level configuration for Amazon S3 Storage Lens.</p> <p>For more information about S3 Storage Lens, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/storage_lens.html\">Assessing your storage activity and usage with S3 Storage Lens</a> in the <i>Amazon S3 User Guide</i>.</p>"}, "BucketLocationConstraint": {"type": "string", "enum": ["EU", "eu-west-1", "us-west-1", "us-west-2", "ap-south-1", "ap-southeast-1", "ap-southeast-2", "ap-northeast-1", "sa-east-1", "cn-north-1", "eu-central-1"]}, "BucketName": {"type": "string", "max": 255, "min": 3}, "BucketVersioningStatus": {"type": "string", "enum": ["Enabled", "Suspended"]}, "Buckets": {"type": "list", "member": {"shape": "S3BucketArnString", "locationName": "<PERSON><PERSON>"}}, "CloudWatchMetrics": {"type": "structure", "required": ["IsEnabled"], "members": {"IsEnabled": {"shape": "IsEnabled", "documentation": "<p>A container that indicates whether CloudWatch publishing for S3 Storage Lens metrics is enabled. A value of <code>true</code> indicates that CloudWatch publishing for S3 Storage Lens metrics is enabled.</p>"}}, "documentation": "<p>A container for enabling Amazon CloudWatch publishing for S3 Storage Lens metrics.</p> <p>For more information about publishing S3 Storage Lens metrics to CloudWatch, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/storage_lens_view_metrics_cloudwatch.html\">Monitor S3 Storage Lens metrics in CloudWatch</a> in the <i>Amazon S3 User Guide</i>.</p>"}, "ConfigId": {"type": "string", "max": 64, "min": 1, "pattern": "[a-zA-Z0-9\\-\\_\\.]+"}, "ConfirmRemoveSelfBucketAccess": {"type": "boolean"}, "ConfirmationRequired": {"type": "boolean"}, "ContinuationToken": {"type": "string"}, "CreateAccessPointForObjectLambdaRequest": {"type": "structure", "required": ["AccountId", "Name", "Configuration"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID for owner of the specified Object Lambda Access Point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Name": {"shape": "ObjectLambdaAccessPointName", "documentation": "<p>The name you want to assign to this Object Lambda Access Point.</p>", "location": "uri", "locationName": "name"}, "Configuration": {"shape": "ObjectLambdaConfiguration", "documentation": "<p>Object Lambda Access Point configuration as a JSON document.</p>"}}}, "CreateAccessPointForObjectLambdaResult": {"type": "structure", "members": {"ObjectLambdaAccessPointArn": {"shape": "ObjectLambdaAccessPointArn", "documentation": "<p>Specifies the ARN for the Object Lambda Access Point.</p>"}, "Alias": {"shape": "ObjectLambdaAccessPointAlias", "documentation": "<p>The alias of the Object Lambda Access Point.</p>"}}}, "CreateAccessPointRequest": {"type": "structure", "required": ["AccountId", "Name", "Bucket"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID for the account that owns the specified access point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Name": {"shape": "AccessPointName", "documentation": "<p>The name you want to assign to this access point.</p>", "location": "uri", "locationName": "name"}, "Bucket": {"shape": "BucketName", "documentation": "<p>The name of the bucket that you want to associate this access point with.</p> <p>For using this parameter with Amazon S3 on Outposts with the REST API, you must specify the name and the x-amz-outpost-id as well.</p> <p>For using this parameter with S3 on Outposts with the Amazon Web Services SDK and CLI, you must specify the ARN of the bucket accessed in the format <code>arn:aws:s3-outposts:&lt;Region&gt;:&lt;account-id&gt;:outpost/&lt;outpost-id&gt;/bucket/&lt;my-bucket-name&gt;</code>. For example, to access the bucket <code>reports</code> through Outpost <code>my-outpost</code> owned by account <code>************</code> in Region <code>us-west-2</code>, use the URL encoding of <code>arn:aws:s3-outposts:us-west-2:************:outpost/my-outpost/bucket/reports</code>. The value must be URL encoded. </p>", "contextParam": {"name": "Bucket"}}, "VpcConfiguration": {"shape": "VpcConfiguration", "documentation": "<p>If you include this field, Amazon S3 restricts access to this access point to requests from the specified virtual private cloud (VPC).</p> <note> <p>This is required for creating an access point for Amazon S3 on Outposts buckets.</p> </note>"}, "PublicAccessBlockConfiguration": {"shape": "PublicAccessBlockConfiguration", "documentation": "<p> The <code>PublicAccessBlock</code> configuration that you want to apply to the access point. </p>"}, "BucketAccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID associated with the S3 bucket associated with this access point.</p>"}}}, "CreateAccessPointResult": {"type": "structure", "members": {"AccessPointArn": {"shape": "S3AccessPointArn", "documentation": "<p>The ARN of the access point.</p> <note> <p>This is only supported by Amazon S3 on Outposts.</p> </note>"}, "Alias": {"shape": "<PERSON><PERSON>", "documentation": "<p>The name or alias of the access point.</p>"}}}, "CreateBucketConfiguration": {"type": "structure", "members": {"LocationConstraint": {"shape": "BucketLocationConstraint", "documentation": "<p>Specifies the Region where the bucket will be created. If you are creating a bucket on the US East (N. Virginia) Region (us-east-1), you do not need to specify the location. </p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>"}}, "documentation": "<p>The container for the bucket configuration.</p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>"}, "CreateBucketRequest": {"type": "structure", "required": ["Bucket"], "members": {"ACL": {"shape": "BucketCannedACL", "documentation": "<p>The canned ACL to apply to the bucket.</p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>", "location": "header", "locationName": "x-amz-acl"}, "Bucket": {"shape": "BucketName", "documentation": "<p>The name of the bucket.</p>", "contextParam": {"name": "Bucket"}, "location": "uri", "locationName": "name"}, "CreateBucketConfiguration": {"shape": "CreateBucketConfiguration", "documentation": "<p>The configuration information for the bucket.</p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>", "locationName": "CreateBucketConfiguration", "xmlNamespace": {"uri": "http://awss3control.amazonaws.com/doc/2018-08-20/"}}, "GrantFullControl": {"shape": "GrantFullControl", "documentation": "<p>Allows grantee the read, write, read ACP, and write ACP permissions on the bucket.</p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>", "location": "header", "locationName": "x-amz-grant-full-control"}, "GrantRead": {"shape": "Grant<PERSON>ead", "documentation": "<p>Allows grantee to list the objects in the bucket.</p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>", "location": "header", "locationName": "x-amz-grant-read"}, "GrantReadACP": {"shape": "GrantReadACP", "documentation": "<p>Allows grantee to read the bucket ACL.</p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>", "location": "header", "locationName": "x-amz-grant-read-acp"}, "GrantWrite": {"shape": "GrantWrite", "documentation": "<p>Allows grantee to create, overwrite, and delete any object in the bucket.</p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>", "location": "header", "locationName": "x-amz-grant-write"}, "GrantWriteACP": {"shape": "GrantWriteACP", "documentation": "<p>Allows grantee to write the ACL for the applicable bucket.</p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>", "location": "header", "locationName": "x-amz-grant-write-acp"}, "ObjectLockEnabledForBucket": {"shape": "ObjectLockEnabledForBucket", "documentation": "<p>Specifies whether you want S3 Object Lock to be enabled for the new bucket.</p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>", "location": "header", "locationName": "x-amz-bucket-object-lock-enabled"}, "OutpostId": {"shape": "NonEmptyMaxLength64String", "documentation": "<p>The ID of the Outposts where the bucket is being created.</p> <note> <p>This ID is required by Amazon S3 on Outposts buckets.</p> </note>", "contextParam": {"name": "OutpostId"}, "location": "header", "locationName": "x-amz-outpost-id"}}, "payload": "CreateBucketConfiguration"}, "CreateBucketResult": {"type": "structure", "members": {"Location": {"shape": "Location", "documentation": "<p>The location of the bucket.</p>", "location": "header", "locationName": "Location"}, "BucketArn": {"shape": "S3RegionalBucketArn", "documentation": "<p>The Amazon Resource Name (ARN) of the bucket.</p> <p>For using this parameter with Amazon S3 on Outposts with the REST API, you must specify the name and the x-amz-outpost-id as well.</p> <p>For using this parameter with S3 on Outposts with the Amazon Web Services SDK and CLI, you must specify the ARN of the bucket accessed in the format <code>arn:aws:s3-outposts:&lt;Region&gt;:&lt;account-id&gt;:outpost/&lt;outpost-id&gt;/bucket/&lt;my-bucket-name&gt;</code>. For example, to access the bucket <code>reports</code> through Outpost <code>my-outpost</code> owned by account <code>************</code> in Region <code>us-west-2</code>, use the URL encoding of <code>arn:aws:s3-outposts:us-west-2:************:outpost/my-outpost/bucket/reports</code>. The value must be URL encoded. </p>"}}}, "CreateJobRequest": {"type": "structure", "required": ["AccountId", "Operation", "Report", "ClientRequestToken", "Priority", "RoleArn"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID that creates the job.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "ConfirmationRequired": {"shape": "ConfirmationRequired", "documentation": "<p>Indicates whether confirmation is required before Amazon S3 runs the job. Confirmation is only required for jobs created through the Amazon S3 console.</p>", "box": true}, "Operation": {"shape": "JobOperation", "documentation": "<p>The action that you want this job to perform on every object listed in the manifest. For more information about the available actions, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/batch-ops-actions.html\">Operations</a> in the <i>Amazon S3 User Guide</i>.</p>"}, "Report": {"shape": "JobReport", "documentation": "<p>Configuration parameters for the optional job-completion report.</p>"}, "ClientRequestToken": {"shape": "NonEmptyMaxLength64String", "documentation": "<p>An idempotency token to ensure that you don't accidentally submit the same request twice. You can use any string up to the maximum length.</p>", "idempotencyToken": true}, "Manifest": {"shape": "JobManifest", "documentation": "<p>Configuration parameters for the manifest.</p>"}, "Description": {"shape": "NonEmptyMaxLength256String", "documentation": "<p>A description for this job. You can use any string within the permitted length. Descriptions don't need to be unique and can be used for multiple jobs.</p>"}, "Priority": {"shape": "JobPriority", "documentation": "<p>The numerical priority for this job. Higher numbers indicate higher priority.</p>", "box": true}, "RoleArn": {"shape": "IAMRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) for the Identity and Access Management (IAM) role that Batch Operations will use to run this job's action on every object in the manifest.</p>"}, "Tags": {"shape": "S3TagSet", "documentation": "<p>A set of tags to associate with the S3 Batch Operations job. This is an optional parameter. </p>"}, "ManifestGenerator": {"shape": "JobManifestGenerator", "documentation": "<p>The attribute container for the ManifestGenerator details. Jobs must be created with either a manifest file or a ManifestGenerator, but not both.</p>"}}}, "CreateJobResult": {"type": "structure", "members": {"JobId": {"shape": "JobId", "documentation": "<p>The ID for this job. Amazon S3 generates this ID automatically and returns it after a successful <code>Create Job</code> request.</p>"}}}, "CreateMultiRegionAccessPointInput": {"type": "structure", "required": ["Name", "Regions"], "members": {"Name": {"shape": "MultiRegionAccessPointName", "documentation": "<p>The name of the Multi-Region Access Point associated with this request.</p>"}, "PublicAccessBlock": {"shape": "PublicAccessBlockConfiguration"}, "Regions": {"shape": "RegionCreationList", "documentation": "<p>The buckets in different Regions that are associated with the Multi-Region Access Point.</p>"}}, "documentation": "<p>A container for the information associated with a <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_CreateMultiRegionAccessPoint.html\">CreateMultiRegionAccessPoint</a> request. </p>"}, "CreateMultiRegionAccessPointRequest": {"type": "structure", "required": ["AccountId", "ClientToken", "Details"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID for the owner of the Multi-Region Access Point. The owner of the Multi-Region Access Point also must own the underlying buckets.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "ClientToken": {"shape": "MultiRegionAccessPointClientToken", "documentation": "<p>An idempotency token used to identify the request and guarantee that requests are unique.</p>", "idempotencyToken": true}, "Details": {"shape": "CreateMultiRegionAccessPointInput", "documentation": "<p>A container element containing details about the Multi-Region Access Point.</p>"}}}, "CreateMultiRegionAccessPointResult": {"type": "structure", "members": {"RequestTokenARN": {"shape": "AsyncRequestTokenARN", "documentation": "<p>The request token associated with the request. You can use this token with <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DescribeMultiRegionAccessPointOperation.html\">DescribeMultiRegionAccessPointOperation</a> to determine the status of asynchronous requests.</p>"}}}, "CreationDate": {"type": "timestamp"}, "CreationTimestamp": {"type": "timestamp"}, "Date": {"type": "timestamp"}, "Days": {"type": "integer"}, "DaysAfterInitiation": {"type": "integer"}, "DeleteAccessPointForObjectLambdaRequest": {"type": "structure", "required": ["AccountId", "Name"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID for the account that owns the specified Object Lambda Access Point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Name": {"shape": "ObjectLambdaAccessPointName", "documentation": "<p>The name of the access point you want to delete.</p>", "location": "uri", "locationName": "name"}}}, "DeleteAccessPointPolicyForObjectLambdaRequest": {"type": "structure", "required": ["AccountId", "Name"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID for the account that owns the specified Object Lambda Access Point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Name": {"shape": "ObjectLambdaAccessPointName", "documentation": "<p>The name of the Object Lambda Access Point you want to delete the policy for.</p>", "location": "uri", "locationName": "name"}}}, "DeleteAccessPointPolicyRequest": {"type": "structure", "required": ["AccountId", "Name"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID for the account that owns the specified access point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Name": {"shape": "AccessPointName", "documentation": "<p>The name of the access point whose policy you want to delete.</p> <p>For using this parameter with Amazon S3 on Outposts with the REST API, you must specify the name and the x-amz-outpost-id as well.</p> <p>For using this parameter with S3 on Outposts with the Amazon Web Services SDK and CLI, you must specify the ARN of the access point accessed in the format <code>arn:aws:s3-outposts:&lt;Region&gt;:&lt;account-id&gt;:outpost/&lt;outpost-id&gt;/accesspoint/&lt;my-accesspoint-name&gt;</code>. For example, to access the access point <code>reports-ap</code> through Outpost <code>my-outpost</code> owned by account <code>************</code> in Region <code>us-west-2</code>, use the URL encoding of <code>arn:aws:s3-outposts:us-west-2:************:outpost/my-outpost/accesspoint/reports-ap</code>. The value must be URL encoded. </p>", "contextParam": {"name": "AccessPointName"}, "location": "uri", "locationName": "name"}}}, "DeleteAccessPointRequest": {"type": "structure", "required": ["AccountId", "Name"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID for the account that owns the specified access point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Name": {"shape": "AccessPointName", "documentation": "<p>The name of the access point you want to delete.</p> <p>For using this parameter with Amazon S3 on Outposts with the REST API, you must specify the name and the x-amz-outpost-id as well.</p> <p>For using this parameter with S3 on Outposts with the Amazon Web Services SDK and CLI, you must specify the ARN of the access point accessed in the format <code>arn:aws:s3-outposts:&lt;Region&gt;:&lt;account-id&gt;:outpost/&lt;outpost-id&gt;/accesspoint/&lt;my-accesspoint-name&gt;</code>. For example, to access the access point <code>reports-ap</code> through Outpost <code>my-outpost</code> owned by account <code>************</code> in Region <code>us-west-2</code>, use the URL encoding of <code>arn:aws:s3-outposts:us-west-2:************:outpost/my-outpost/accesspoint/reports-ap</code>. The value must be URL encoded. </p>", "contextParam": {"name": "AccessPointName"}, "location": "uri", "locationName": "name"}}}, "DeleteBucketLifecycleConfigurationRequest": {"type": "structure", "required": ["AccountId", "Bucket"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID of the lifecycle configuration to delete.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Bucket": {"shape": "BucketName", "documentation": "<p>Specifies the bucket.</p> <p>For using this parameter with Amazon S3 on Outposts with the REST API, you must specify the name and the x-amz-outpost-id as well.</p> <p>For using this parameter with S3 on Outposts with the Amazon Web Services SDK and CLI, you must specify the ARN of the bucket accessed in the format <code>arn:aws:s3-outposts:&lt;Region&gt;:&lt;account-id&gt;:outpost/&lt;outpost-id&gt;/bucket/&lt;my-bucket-name&gt;</code>. For example, to access the bucket <code>reports</code> through Outpost <code>my-outpost</code> owned by account <code>************</code> in Region <code>us-west-2</code>, use the URL encoding of <code>arn:aws:s3-outposts:us-west-2:************:outpost/my-outpost/bucket/reports</code>. The value must be URL encoded. </p>", "contextParam": {"name": "Bucket"}, "location": "uri", "locationName": "name"}}}, "DeleteBucketPolicyRequest": {"type": "structure", "required": ["AccountId", "Bucket"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID of the Outposts bucket.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Bucket": {"shape": "BucketName", "documentation": "<p>Specifies the bucket.</p> <p>For using this parameter with Amazon S3 on Outposts with the REST API, you must specify the name and the x-amz-outpost-id as well.</p> <p>For using this parameter with S3 on Outposts with the Amazon Web Services SDK and CLI, you must specify the ARN of the bucket accessed in the format <code>arn:aws:s3-outposts:&lt;Region&gt;:&lt;account-id&gt;:outpost/&lt;outpost-id&gt;/bucket/&lt;my-bucket-name&gt;</code>. For example, to access the bucket <code>reports</code> through Outpost <code>my-outpost</code> owned by account <code>************</code> in Region <code>us-west-2</code>, use the URL encoding of <code>arn:aws:s3-outposts:us-west-2:************:outpost/my-outpost/bucket/reports</code>. The value must be URL encoded. </p>", "contextParam": {"name": "Bucket"}, "location": "uri", "locationName": "name"}}}, "DeleteBucketReplicationRequest": {"type": "structure", "required": ["AccountId", "Bucket"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the Outposts bucket to delete the replication configuration for.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Bucket": {"shape": "BucketName", "documentation": "<p>Specifies the S3 on Outposts bucket to delete the replication configuration for.</p> <p>For using this parameter with Amazon S3 on Outposts with the REST API, you must specify the name and the x-amz-outpost-id as well.</p> <p>For using this parameter with S3 on Outposts with the Amazon Web Services SDK and CLI, you must specify the ARN of the bucket accessed in the format <code>arn:aws:s3-outposts:&lt;Region&gt;:&lt;account-id&gt;:outpost/&lt;outpost-id&gt;/bucket/&lt;my-bucket-name&gt;</code>. For example, to access the bucket <code>reports</code> through Outpost <code>my-outpost</code> owned by account <code>************</code> in Region <code>us-west-2</code>, use the URL encoding of <code>arn:aws:s3-outposts:us-west-2:************:outpost/my-outpost/bucket/reports</code>. The value must be URL encoded. </p>", "contextParam": {"name": "Bucket"}, "location": "uri", "locationName": "name"}}}, "DeleteBucketRequest": {"type": "structure", "required": ["AccountId", "Bucket"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID that owns the Outposts bucket.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Bucket": {"shape": "BucketName", "documentation": "<p>Specifies the bucket being deleted.</p> <p>For using this parameter with Amazon S3 on Outposts with the REST API, you must specify the name and the x-amz-outpost-id as well.</p> <p>For using this parameter with S3 on Outposts with the Amazon Web Services SDK and CLI, you must specify the ARN of the bucket accessed in the format <code>arn:aws:s3-outposts:&lt;Region&gt;:&lt;account-id&gt;:outpost/&lt;outpost-id&gt;/bucket/&lt;my-bucket-name&gt;</code>. For example, to access the bucket <code>reports</code> through Outpost <code>my-outpost</code> owned by account <code>************</code> in Region <code>us-west-2</code>, use the URL encoding of <code>arn:aws:s3-outposts:us-west-2:************:outpost/my-outpost/bucket/reports</code>. The value must be URL encoded. </p>", "contextParam": {"name": "Bucket"}, "location": "uri", "locationName": "name"}}}, "DeleteBucketTaggingRequest": {"type": "structure", "required": ["AccountId", "Bucket"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the Outposts bucket tag set to be removed.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Bucket": {"shape": "BucketName", "documentation": "<p>The bucket ARN that has the tag set to be removed.</p> <p>For using this parameter with Amazon S3 on Outposts with the REST API, you must specify the name and the x-amz-outpost-id as well.</p> <p>For using this parameter with S3 on Outposts with the Amazon Web Services SDK and CLI, you must specify the ARN of the bucket accessed in the format <code>arn:aws:s3-outposts:&lt;Region&gt;:&lt;account-id&gt;:outpost/&lt;outpost-id&gt;/bucket/&lt;my-bucket-name&gt;</code>. For example, to access the bucket <code>reports</code> through Outpost <code>my-outpost</code> owned by account <code>************</code> in Region <code>us-west-2</code>, use the URL encoding of <code>arn:aws:s3-outposts:us-west-2:************:outpost/my-outpost/bucket/reports</code>. The value must be URL encoded. </p>", "contextParam": {"name": "Bucket"}, "location": "uri", "locationName": "name"}}}, "DeleteJobTaggingRequest": {"type": "structure", "required": ["AccountId", "JobId"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID associated with the S3 Batch Operations job.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "JobId": {"shape": "JobId", "documentation": "<p>The ID for the S3 Batch Operations job whose tags you want to delete.</p>", "location": "uri", "locationName": "id"}}}, "DeleteJobTaggingResult": {"type": "structure", "members": {}}, "DeleteMarkerReplication": {"type": "structure", "required": ["Status"], "members": {"Status": {"shape": "DeleteMarkerReplicationStatus", "documentation": "<p>Indicates whether to replicate delete markers.</p>"}}, "documentation": "<p>Specifies whether S3 on Outposts replicates delete markers. If you specify a <code>Filter</code> element in your replication configuration, you must also include a <code>DeleteMarkerReplication</code> element. If your <code>Filter</code> includes a <code>Tag</code> element, the <code>DeleteMarkerReplication</code> element's <code>Status</code> child element must be set to <code>Disabled</code>, because S3 on Outposts does not support replicating delete markers for tag-based rules.</p> <p>For more information about delete marker replication, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3OutpostsReplication.html#outposts-replication-what-is-replicated\">How delete operations affect replication</a> in the <i>Amazon S3 User Guide</i>. </p>"}, "DeleteMarkerReplicationStatus": {"type": "string", "enum": ["Enabled", "Disabled"]}, "DeleteMultiRegionAccessPointInput": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "MultiRegionAccessPointName", "documentation": "<p>The name of the Multi-Region Access Point associated with this request.</p>"}}, "documentation": "<p>A container for the information associated with a <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DeleteMultiRegionAccessPoint.html\">DeleteMultiRegionAccessPoint</a> request.</p>"}, "DeleteMultiRegionAccessPointRequest": {"type": "structure", "required": ["AccountId", "ClientToken", "Details"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID for the owner of the Multi-Region Access Point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "ClientToken": {"shape": "MultiRegionAccessPointClientToken", "documentation": "<p>An idempotency token used to identify the request and guarantee that requests are unique.</p>", "idempotencyToken": true}, "Details": {"shape": "DeleteMultiRegionAccessPointInput", "documentation": "<p>A container element containing details about the Multi-Region Access Point.</p>"}}}, "DeleteMultiRegionAccessPointResult": {"type": "structure", "members": {"RequestTokenARN": {"shape": "AsyncRequestTokenARN", "documentation": "<p>The request token associated with the request. You can use this token with <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DescribeMultiRegionAccessPointOperation.html\">DescribeMultiRegionAccessPointOperation</a> to determine the status of asynchronous requests.</p>"}}}, "DeletePublicAccessBlockRequest": {"type": "structure", "required": ["AccountId"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID for the Amazon Web Services account whose <code>PublicAccessBlock</code> configuration you want to remove.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}}}, "DeleteStorageLensConfigurationRequest": {"type": "structure", "required": ["ConfigId", "AccountId"], "members": {"ConfigId": {"shape": "ConfigId", "documentation": "<p>The ID of the S3 Storage Lens configuration.</p>", "location": "uri", "locationName": "storagelensid"}, "AccountId": {"shape": "AccountId", "documentation": "<p>The account ID of the requester.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}}}, "DeleteStorageLensConfigurationTaggingRequest": {"type": "structure", "required": ["ConfigId", "AccountId"], "members": {"ConfigId": {"shape": "ConfigId", "documentation": "<p>The ID of the S3 Storage Lens configuration.</p>", "location": "uri", "locationName": "storagelensid"}, "AccountId": {"shape": "AccountId", "documentation": "<p>The account ID of the requester.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}}}, "DeleteStorageLensConfigurationTaggingResult": {"type": "structure", "members": {}}, "DescribeJobRequest": {"type": "structure", "required": ["AccountId", "JobId"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID associated with the S3 Batch Operations job.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "JobId": {"shape": "JobId", "documentation": "<p>The ID for the job whose information you want to retrieve.</p>", "location": "uri", "locationName": "id"}}}, "DescribeJobResult": {"type": "structure", "members": {"Job": {"shape": "JobDescriptor", "documentation": "<p>Contains the configuration parameters and status for the job specified in the <code>Describe Job</code> request.</p>"}}}, "DescribeMultiRegionAccessPointOperationRequest": {"type": "structure", "required": ["AccountId", "RequestTokenARN"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID for the owner of the Multi-Region Access Point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "RequestTokenARN": {"shape": "AsyncRequestTokenARN", "documentation": "<p>The request token associated with the request you want to know about. This request token is returned as part of the response when you make an asynchronous request. You provide this token to query about the status of the asynchronous action.</p>", "location": "uri", "locationName": "request_token"}}}, "DescribeMultiRegionAccessPointOperationResult": {"type": "structure", "members": {"AsyncOperation": {"shape": "AsyncOperation", "documentation": "<p>A container element containing the details of the asynchronous operation.</p>"}}}, "Destination": {"type": "structure", "required": ["Bucket"], "members": {"Account": {"shape": "AccountId", "documentation": "<p>The destination bucket owner's account ID. </p>"}, "Bucket": {"shape": "BucketIdentifierString", "documentation": "<p>The Amazon Resource Name (ARN) of the access point for the destination bucket where you want S3 on Outposts to store the replication results.</p>"}, "ReplicationTime": {"shape": "ReplicationTime", "documentation": "<p>A container that specifies S3 Replication Time Control (S3 RTC) settings, including whether S3 RTC is enabled and the time when all objects and operations on objects must be replicated. Must be specified together with a <code>Metrics</code> block. </p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>"}, "AccessControlTranslation": {"shape": "AccessControlTranslation", "documentation": "<p>Specify this property only in a cross-account scenario (where the source and destination bucket owners are not the same), and you want to change replica ownership to the Amazon Web Services account that owns the destination bucket. If this property is not specified in the replication configuration, the replicas are owned by same Amazon Web Services account that owns the source object.</p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>"}, "EncryptionConfiguration": {"shape": "EncryptionConfiguration", "documentation": "<p>A container that provides information about encryption. If <code>SourceSelectionCriteria</code> is specified, you must specify this element.</p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>"}, "Metrics": {"shape": "Metrics", "documentation": "<p> A container that specifies replication metrics-related settings. </p>"}, "StorageClass": {"shape": "ReplicationStorageClass", "documentation": "<p> The storage class to use when replicating objects. All objects stored on S3 on Outposts are stored in the <code>OUTPOSTS</code> storage class. S3 on Outposts uses the <code>OUTPOSTS</code> storage class to create the object replicas. </p> <note> <p>Values other than <code>OUTPOSTS</code> are not supported by Amazon S3 on Outposts. </p> </note>"}}, "documentation": "<p>Specifies information about the replication destination bucket and its settings for an S3 on Outposts replication configuration.</p>"}, "DetailedStatusCodesMetrics": {"type": "structure", "members": {"IsEnabled": {"shape": "IsEnabled", "documentation": "<p>A container that indicates whether detailed status code metrics are enabled.</p>"}}, "documentation": "<p>The container element for Amazon S3 Storage Lens detailed status code metrics. Detailed status code metrics generate metrics for HTTP status codes, such as <code>200 OK</code>, <code>403 Forbidden</code>, <code>503 Service Unavailable</code> and others. </p> <p>For more information about S3 Storage Lens, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/storage_lens.html\">Assessing your storage activity and usage with S3 Storage Lens</a> in the <i>Amazon S3 User Guide</i>. For a complete list of S3 Storage Lens metrics, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/storage_lens_metrics_glossary.html\">S3 Storage Lens metrics glossary</a> in the <i>Amazon S3 User Guide</i>.</p>"}, "EncryptionConfiguration": {"type": "structure", "members": {"ReplicaKmsKeyID": {"shape": "ReplicaKmsKeyID", "documentation": "<p>Specifies the ID of the customer managed KMS key that's stored in Key Management Service (KMS) for the destination bucket. This ID is either the Amazon Resource Name (ARN) for the KMS key or the alias <PERSON>N for the KMS key. Amazon S3 uses this KMS key to encrypt replica objects. Amazon S3 supports only symmetric encryption KMS keys. For more information, see <a href=\"https://docs.aws.amazon.com/kms/latest/developerguide/concepts.html#symmetric-cmks\">Symmetric encryption KMS keys</a> in the <i>Amazon Web Services Key Management Service Developer Guide</i>.</p>", "box": true}}, "documentation": "<p>Specifies encryption-related information for an Amazon S3 bucket that is a destination for replicated objects.</p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>"}, "Endpoints": {"type": "map", "key": {"shape": "NonEmptyMaxLength64String"}, "value": {"shape": "NonEmptyMaxLength1024String"}}, "EstablishedMultiRegionAccessPointPolicy": {"type": "structure", "members": {"Policy": {"shape": "Policy", "documentation": "<p>The details of the last established policy.</p>"}}, "documentation": "<p>The last established access control policy for a Multi-Region Access Point.</p> <p>When you update the policy, the update is first listed as the proposed policy. After the update is finished and all Regions have been updated, the proposed policy is listed as the established policy. If both policies have the same version number, the proposed policy is the established policy.</p>"}, "ExceptionMessage": {"type": "string", "max": 1024, "min": 1}, "Exclude": {"type": "structure", "members": {"Buckets": {"shape": "Buckets", "documentation": "<p>A container for the S3 Storage Lens bucket excludes.</p>"}, "Regions": {"shape": "Regions", "documentation": "<p>A container for the S3 Storage Lens Region excludes.</p>"}}, "documentation": "<p>A container for what Amazon S3 Storage Lens will exclude.</p>"}, "ExistingObjectReplication": {"type": "structure", "required": ["Status"], "members": {"Status": {"shape": "ExistingObjectReplicationStatus", "documentation": "<p>Specifies whether Amazon S3 replicates existing source bucket objects. </p>"}}, "documentation": "<p>An optional configuration to replicate existing source bucket objects. </p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>"}, "ExistingObjectReplicationStatus": {"type": "string", "enum": ["Enabled", "Disabled"]}, "ExpirationStatus": {"type": "string", "enum": ["Enabled", "Disabled"]}, "ExpiredObjectDeleteMarker": {"type": "boolean"}, "Format": {"type": "string", "enum": ["CSV", "Pa<PERSON><PERSON>"]}, "FunctionArnString": {"type": "string", "max": 1024, "min": 1, "pattern": "(arn:(aws[a-zA-Z-]*)?:lambda:)?([a-z]{2}((-gov)|(-iso(b?)))?-[a-z]+-\\d{1}:)?(\\d{12}:)?(function:)?([a-zA-Z0-9-_]+)(:(\\$LATEST|[a-zA-Z0-9-_]+))?"}, "GeneratedManifestEncryption": {"type": "structure", "members": {"SSES3": {"shape": "SSES3Encryption", "documentation": "<p>Specifies the use of SSE-S3 to encrypt generated manifest objects.</p>", "locationName": "SSE-S3"}, "SSEKMS": {"shape": "SSEKMSEncryption", "documentation": "<p>Configuration details on how SSE-KMS is used to encrypt generated manifest objects.</p>", "locationName": "SSE-KMS"}}, "documentation": "<p>The encryption configuration to use when storing the generated manifest.</p>"}, "GeneratedManifestFormat": {"type": "string", "enum": ["S3InventoryReport_CSV_20211130"]}, "GetAccessPointConfigurationForObjectLambdaRequest": {"type": "structure", "required": ["AccountId", "Name"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID for the account that owns the specified Object Lambda Access Point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Name": {"shape": "ObjectLambdaAccessPointName", "documentation": "<p>The name of the Object Lambda Access Point you want to return the configuration for.</p>", "location": "uri", "locationName": "name"}}}, "GetAccessPointConfigurationForObjectLambdaResult": {"type": "structure", "members": {"Configuration": {"shape": "ObjectLambdaConfiguration", "documentation": "<p>Object Lambda Access Point configuration document.</p>"}}}, "GetAccessPointForObjectLambdaRequest": {"type": "structure", "required": ["AccountId", "Name"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID for the account that owns the specified Object Lambda Access Point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Name": {"shape": "ObjectLambdaAccessPointName", "documentation": "<p>The name of the Object Lambda Access Point.</p>", "location": "uri", "locationName": "name"}}}, "GetAccessPointForObjectLambdaResult": {"type": "structure", "members": {"Name": {"shape": "ObjectLambdaAccessPointName", "documentation": "<p>The name of the Object Lambda Access Point.</p>"}, "PublicAccessBlockConfiguration": {"shape": "PublicAccessBlockConfiguration", "documentation": "<p>Configuration to block all public access. This setting is turned on and can not be edited. </p>"}, "CreationDate": {"shape": "CreationDate", "documentation": "<p>The date and time when the specified Object Lambda Access Point was created.</p>"}, "Alias": {"shape": "ObjectLambdaAccessPointAlias", "documentation": "<p>The alias of the Object Lambda Access Point.</p>"}}}, "GetAccessPointPolicyForObjectLambdaRequest": {"type": "structure", "required": ["AccountId", "Name"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID for the account that owns the specified Object Lambda Access Point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Name": {"shape": "ObjectLambdaAccessPointName", "documentation": "<p>The name of the Object Lambda Access Point.</p>", "location": "uri", "locationName": "name"}}}, "GetAccessPointPolicyForObjectLambdaResult": {"type": "structure", "members": {"Policy": {"shape": "ObjectLambdaPolicy", "documentation": "<p>Object Lambda Access Point resource policy document.</p>"}}}, "GetAccessPointPolicyRequest": {"type": "structure", "required": ["AccountId", "Name"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID for the account that owns the specified access point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Name": {"shape": "AccessPointName", "documentation": "<p>The name of the access point whose policy you want to retrieve.</p> <p>For using this parameter with Amazon S3 on Outposts with the REST API, you must specify the name and the x-amz-outpost-id as well.</p> <p>For using this parameter with S3 on Outposts with the Amazon Web Services SDK and CLI, you must specify the ARN of the access point accessed in the format <code>arn:aws:s3-outposts:&lt;Region&gt;:&lt;account-id&gt;:outpost/&lt;outpost-id&gt;/accesspoint/&lt;my-accesspoint-name&gt;</code>. For example, to access the access point <code>reports-ap</code> through Outpost <code>my-outpost</code> owned by account <code>************</code> in Region <code>us-west-2</code>, use the URL encoding of <code>arn:aws:s3-outposts:us-west-2:************:outpost/my-outpost/accesspoint/reports-ap</code>. The value must be URL encoded. </p>", "contextParam": {"name": "AccessPointName"}, "location": "uri", "locationName": "name"}}}, "GetAccessPointPolicyResult": {"type": "structure", "members": {"Policy": {"shape": "Policy", "documentation": "<p>The access point policy associated with the specified access point.</p>"}}}, "GetAccessPointPolicyStatusForObjectLambdaRequest": {"type": "structure", "required": ["AccountId", "Name"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID for the account that owns the specified Object Lambda Access Point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Name": {"shape": "ObjectLambdaAccessPointName", "documentation": "<p>The name of the Object Lambda Access Point.</p>", "location": "uri", "locationName": "name"}}}, "GetAccessPointPolicyStatusForObjectLambdaResult": {"type": "structure", "members": {"PolicyStatus": {"shape": "PolicyStatus"}}}, "GetAccessPointPolicyStatusRequest": {"type": "structure", "required": ["AccountId", "Name"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID for the account that owns the specified access point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Name": {"shape": "AccessPointName", "documentation": "<p>The name of the access point whose policy status you want to retrieve.</p>", "contextParam": {"name": "AccessPointName"}, "location": "uri", "locationName": "name"}}}, "GetAccessPointPolicyStatusResult": {"type": "structure", "members": {"PolicyStatus": {"shape": "PolicyStatus", "documentation": "<p>Indicates the current policy status of the specified access point.</p>"}}}, "GetAccessPointRequest": {"type": "structure", "required": ["AccountId", "Name"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID for the account that owns the specified access point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Name": {"shape": "AccessPointName", "documentation": "<p>The name of the access point whose configuration information you want to retrieve.</p> <p>For using this parameter with Amazon S3 on Outposts with the REST API, you must specify the name and the x-amz-outpost-id as well.</p> <p>For using this parameter with S3 on Outposts with the Amazon Web Services SDK and CLI, you must specify the ARN of the access point accessed in the format <code>arn:aws:s3-outposts:&lt;Region&gt;:&lt;account-id&gt;:outpost/&lt;outpost-id&gt;/accesspoint/&lt;my-accesspoint-name&gt;</code>. For example, to access the access point <code>reports-ap</code> through Outpost <code>my-outpost</code> owned by account <code>************</code> in Region <code>us-west-2</code>, use the URL encoding of <code>arn:aws:s3-outposts:us-west-2:************:outpost/my-outpost/accesspoint/reports-ap</code>. The value must be URL encoded. </p>", "contextParam": {"name": "AccessPointName"}, "location": "uri", "locationName": "name"}}}, "GetAccessPointResult": {"type": "structure", "members": {"Name": {"shape": "AccessPointName", "documentation": "<p>The name of the specified access point.</p>"}, "Bucket": {"shape": "BucketName", "documentation": "<p>The name of the bucket associated with the specified access point.</p>"}, "NetworkOrigin": {"shape": "NetworkOrigin", "documentation": "<p>Indicates whether this access point allows access from the public internet. If <code>VpcConfiguration</code> is specified for this access point, then <code>NetworkOrigin</code> is <code>VPC</code>, and the access point doesn't allow access from the public internet. Otherwise, <code>NetworkOrigin</code> is <code>Internet</code>, and the access point allows access from the public internet, subject to the access point and bucket access policies.</p> <p>This will always be true for an Amazon S3 on Outposts access point</p>"}, "VpcConfiguration": {"shape": "VpcConfiguration", "documentation": "<p>Contains the virtual private cloud (VPC) configuration for the specified access point.</p> <note> <p>This element is empty if this access point is an Amazon S3 on Outposts access point that is used by other Amazon Web Services.</p> </note>"}, "PublicAccessBlockConfiguration": {"shape": "PublicAccessBlockConfiguration"}, "CreationDate": {"shape": "CreationDate", "documentation": "<p>The date and time when the specified access point was created.</p>"}, "Alias": {"shape": "<PERSON><PERSON>", "documentation": "<p>The name or alias of the access point.</p>"}, "AccessPointArn": {"shape": "S3AccessPointArn", "documentation": "<p>The ARN of the access point.</p>"}, "Endpoints": {"shape": "Endpoints", "documentation": "<p>The VPC endpoint for the access point.</p>"}, "BucketAccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID associated with the S3 bucket associated with this access point.</p>"}}}, "GetBucketLifecycleConfigurationRequest": {"type": "structure", "required": ["AccountId", "Bucket"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the Outposts bucket.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Bucket": {"shape": "BucketName", "documentation": "<p>The Amazon Resource Name (ARN) of the bucket.</p> <p>For using this parameter with Amazon S3 on Outposts with the REST API, you must specify the name and the x-amz-outpost-id as well.</p> <p>For using this parameter with S3 on Outposts with the Amazon Web Services SDK and CLI, you must specify the ARN of the bucket accessed in the format <code>arn:aws:s3-outposts:&lt;Region&gt;:&lt;account-id&gt;:outpost/&lt;outpost-id&gt;/bucket/&lt;my-bucket-name&gt;</code>. For example, to access the bucket <code>reports</code> through Outpost <code>my-outpost</code> owned by account <code>************</code> in Region <code>us-west-2</code>, use the URL encoding of <code>arn:aws:s3-outposts:us-west-2:************:outpost/my-outpost/bucket/reports</code>. The value must be URL encoded. </p>", "contextParam": {"name": "Bucket"}, "location": "uri", "locationName": "name"}}}, "GetBucketLifecycleConfigurationResult": {"type": "structure", "members": {"Rules": {"shape": "LifecycleRules", "documentation": "<p>Container for the lifecycle rule of the Outposts bucket.</p>"}}}, "GetBucketPolicyRequest": {"type": "structure", "required": ["AccountId", "Bucket"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the Outposts bucket.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Bucket": {"shape": "BucketName", "documentation": "<p>Specifies the bucket.</p> <p>For using this parameter with Amazon S3 on Outposts with the REST API, you must specify the name and the x-amz-outpost-id as well.</p> <p>For using this parameter with S3 on Outposts with the Amazon Web Services SDK and CLI, you must specify the ARN of the bucket accessed in the format <code>arn:aws:s3-outposts:&lt;Region&gt;:&lt;account-id&gt;:outpost/&lt;outpost-id&gt;/bucket/&lt;my-bucket-name&gt;</code>. For example, to access the bucket <code>reports</code> through Outpost <code>my-outpost</code> owned by account <code>************</code> in Region <code>us-west-2</code>, use the URL encoding of <code>arn:aws:s3-outposts:us-west-2:************:outpost/my-outpost/bucket/reports</code>. The value must be URL encoded. </p>", "contextParam": {"name": "Bucket"}, "location": "uri", "locationName": "name"}}}, "GetBucketPolicyResult": {"type": "structure", "members": {"Policy": {"shape": "Policy", "documentation": "<p>The policy of the Outposts bucket.</p>"}}}, "GetBucketReplicationRequest": {"type": "structure", "required": ["AccountId", "Bucket"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the Outposts bucket.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Bucket": {"shape": "BucketName", "documentation": "<p>Specifies the bucket to get the replication information for.</p> <p>For using this parameter with Amazon S3 on Outposts with the REST API, you must specify the name and the x-amz-outpost-id as well.</p> <p>For using this parameter with S3 on Outposts with the Amazon Web Services SDK and CLI, you must specify the ARN of the bucket accessed in the format <code>arn:aws:s3-outposts:&lt;Region&gt;:&lt;account-id&gt;:outpost/&lt;outpost-id&gt;/bucket/&lt;my-bucket-name&gt;</code>. For example, to access the bucket <code>reports</code> through Outpost <code>my-outpost</code> owned by account <code>************</code> in Region <code>us-west-2</code>, use the URL encoding of <code>arn:aws:s3-outposts:us-west-2:************:outpost/my-outpost/bucket/reports</code>. The value must be URL encoded. </p>", "contextParam": {"name": "Bucket"}, "location": "uri", "locationName": "name"}}}, "GetBucketReplicationResult": {"type": "structure", "members": {"ReplicationConfiguration": {"shape": "ReplicationConfiguration", "documentation": "<p>A container for one or more replication rules. A replication configuration must have at least one rule and you can add up to 100 rules. The maximum size of a replication configuration is 128 KB.</p>"}}}, "GetBucketRequest": {"type": "structure", "required": ["AccountId", "Bucket"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the Outposts bucket.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Bucket": {"shape": "BucketName", "documentation": "<p>Specifies the bucket.</p> <p>For using this parameter with Amazon S3 on Outposts with the REST API, you must specify the name and the x-amz-outpost-id as well.</p> <p>For using this parameter with S3 on Outposts with the Amazon Web Services SDK and CLI, you must specify the ARN of the bucket accessed in the format <code>arn:aws:s3-outposts:&lt;Region&gt;:&lt;account-id&gt;:outpost/&lt;outpost-id&gt;/bucket/&lt;my-bucket-name&gt;</code>. For example, to access the bucket <code>reports</code> through Outpost <code>my-outpost</code> owned by account <code>************</code> in Region <code>us-west-2</code>, use the URL encoding of <code>arn:aws:s3-outposts:us-west-2:************:outpost/my-outpost/bucket/reports</code>. The value must be URL encoded. </p>", "contextParam": {"name": "Bucket"}, "location": "uri", "locationName": "name"}}}, "GetBucketResult": {"type": "structure", "members": {"Bucket": {"shape": "BucketName", "documentation": "<p>The Outposts bucket requested.</p>"}, "PublicAccessBlockEnabled": {"shape": "PublicAccessBlockEnabled", "documentation": "<p/>"}, "CreationDate": {"shape": "CreationDate", "documentation": "<p>The creation date of the Outposts bucket.</p>"}}}, "GetBucketTaggingRequest": {"type": "structure", "required": ["AccountId", "Bucket"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the Outposts bucket.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Bucket": {"shape": "BucketName", "documentation": "<p>Specifies the bucket.</p> <p>For using this parameter with Amazon S3 on Outposts with the REST API, you must specify the name and the x-amz-outpost-id as well.</p> <p>For using this parameter with S3 on Outposts with the Amazon Web Services SDK and CLI, you must specify the ARN of the bucket accessed in the format <code>arn:aws:s3-outposts:&lt;Region&gt;:&lt;account-id&gt;:outpost/&lt;outpost-id&gt;/bucket/&lt;my-bucket-name&gt;</code>. For example, to access the bucket <code>reports</code> through Outpost <code>my-outpost</code> owned by account <code>************</code> in Region <code>us-west-2</code>, use the URL encoding of <code>arn:aws:s3-outposts:us-west-2:************:outpost/my-outpost/bucket/reports</code>. The value must be URL encoded. </p>", "contextParam": {"name": "Bucket"}, "location": "uri", "locationName": "name"}}}, "GetBucketTaggingResult": {"type": "structure", "required": ["TagSet"], "members": {"TagSet": {"shape": "S3TagSet", "documentation": "<p>The tags set of the Outposts bucket.</p>"}}}, "GetBucketVersioningRequest": {"type": "structure", "required": ["AccountId", "Bucket"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the S3 on Outposts bucket.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Bucket": {"shape": "BucketName", "documentation": "<p>The S3 on Outposts bucket to return the versioning state for.</p>", "contextParam": {"name": "Bucket"}, "location": "uri", "locationName": "name"}}}, "GetBucketVersioningResult": {"type": "structure", "members": {"Status": {"shape": "BucketVersioningStatus", "documentation": "<p>The versioning state of the S3 on Outposts bucket.</p>"}, "MFADelete": {"shape": "MFADeleteStatus", "documentation": "<p>Specifies whether MFA delete is enabled in the bucket versioning configuration. This element is returned only if the bucket has been configured with MFA delete. If MFA delete has never been configured for the bucket, this element is not returned.</p>", "locationName": "MfaDelete"}}}, "GetJobTaggingRequest": {"type": "structure", "required": ["AccountId", "JobId"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID associated with the S3 Batch Operations job.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "JobId": {"shape": "JobId", "documentation": "<p>The ID for the S3 Batch Operations job whose tags you want to retrieve.</p>", "location": "uri", "locationName": "id"}}}, "GetJobTaggingResult": {"type": "structure", "members": {"Tags": {"shape": "S3TagSet", "documentation": "<p>The set of tags associated with the S3 Batch Operations job.</p>"}}}, "GetMultiRegionAccessPointPolicyRequest": {"type": "structure", "required": ["AccountId", "Name"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID for the owner of the Multi-Region Access Point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Name": {"shape": "MultiRegionAccessPointName", "documentation": "<p>Specifies the Multi-Region Access Point. The name of the Multi-Region Access Point is different from the alias. For more information about the distinction between the name and the alias of an Multi-Region Access Point, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/CreatingMultiRegionAccessPoints.html#multi-region-access-point-naming\">Managing Multi-Region Access Points</a> in the <i>Amazon S3 User Guide</i>.</p>", "location": "uri", "locationName": "name"}}}, "GetMultiRegionAccessPointPolicyResult": {"type": "structure", "members": {"Policy": {"shape": "MultiRegionAccessPointPolicyDocument", "documentation": "<p>The policy associated with the specified Multi-Region Access Point.</p>"}}}, "GetMultiRegionAccessPointPolicyStatusRequest": {"type": "structure", "required": ["AccountId", "Name"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID for the owner of the Multi-Region Access Point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Name": {"shape": "MultiRegionAccessPointName", "documentation": "<p>Specifies the Multi-Region Access Point. The name of the Multi-Region Access Point is different from the alias. For more information about the distinction between the name and the alias of an Multi-Region Access Point, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/CreatingMultiRegionAccessPoints.html#multi-region-access-point-naming\">Managing Multi-Region Access Points</a> in the <i>Amazon S3 User Guide</i>.</p>", "location": "uri", "locationName": "name"}}}, "GetMultiRegionAccessPointPolicyStatusResult": {"type": "structure", "members": {"Established": {"shape": "PolicyStatus"}}}, "GetMultiRegionAccessPointRequest": {"type": "structure", "required": ["AccountId", "Name"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID for the owner of the Multi-Region Access Point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Name": {"shape": "MultiRegionAccessPointName", "documentation": "<p>The name of the Multi-Region Access Point whose configuration information you want to receive. The name of the Multi-Region Access Point is different from the alias. For more information about the distinction between the name and the alias of an Multi-Region Access Point, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/CreatingMultiRegionAccessPoints.html#multi-region-access-point-naming\">Managing Multi-Region Access Points</a> in the <i>Amazon S3 User Guide</i>.</p>", "location": "uri", "locationName": "name"}}}, "GetMultiRegionAccessPointResult": {"type": "structure", "members": {"AccessPoint": {"shape": "MultiRegionAccessPointReport", "documentation": "<p>A container element containing the details of the requested Multi-Region Access Point.</p>"}}}, "GetMultiRegionAccessPointRoutesRequest": {"type": "structure", "required": ["AccountId", "Mrap"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID for the owner of the Multi-Region Access Point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Mrap": {"shape": "MultiRegionAccessPointId", "documentation": "<p>The Multi-Region Access Point ARN.</p>", "location": "uri", "locationName": "mrap"}}}, "GetMultiRegionAccessPointRoutesResult": {"type": "structure", "members": {"Mrap": {"shape": "MultiRegionAccessPointId", "documentation": "<p>The Multi-Region Access Point ARN.</p>"}, "Routes": {"shape": "RouteList", "documentation": "<p>The different routes that make up the route configuration. Active routes return a value of <code>100</code>, and passive routes return a value of <code>0</code>.</p>"}}}, "GetPublicAccessBlockOutput": {"type": "structure", "members": {"PublicAccessBlockConfiguration": {"shape": "PublicAccessBlockConfiguration", "documentation": "<p>The <code>PublicAccessBlock</code> configuration currently in effect for this Amazon Web Services account.</p>"}}, "payload": "PublicAccessBlockConfiguration"}, "GetPublicAccessBlockRequest": {"type": "structure", "required": ["AccountId"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID for the Amazon Web Services account whose <code>PublicAccessBlock</code> configuration you want to retrieve.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}}}, "GetStorageLensConfigurationRequest": {"type": "structure", "required": ["ConfigId", "AccountId"], "members": {"ConfigId": {"shape": "ConfigId", "documentation": "<p>The ID of the Amazon S3 Storage Lens configuration.</p>", "location": "uri", "locationName": "storagelensid"}, "AccountId": {"shape": "AccountId", "documentation": "<p>The account ID of the requester.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}}}, "GetStorageLensConfigurationResult": {"type": "structure", "members": {"StorageLensConfiguration": {"shape": "StorageLensConfiguration", "documentation": "<p>The S3 Storage Lens configuration requested.</p>"}}, "payload": "StorageLensConfiguration"}, "GetStorageLensConfigurationTaggingRequest": {"type": "structure", "required": ["ConfigId", "AccountId"], "members": {"ConfigId": {"shape": "ConfigId", "documentation": "<p>The ID of the Amazon S3 Storage Lens configuration.</p>", "location": "uri", "locationName": "storagelensid"}, "AccountId": {"shape": "AccountId", "documentation": "<p>The account ID of the requester.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}}}, "GetStorageLensConfigurationTaggingResult": {"type": "structure", "members": {"Tags": {"shape": "StorageLensTags", "documentation": "<p>The tags of S3 Storage Lens configuration requested.</p>"}}}, "GrantFullControl": {"type": "string"}, "GrantRead": {"type": "string"}, "GrantReadACP": {"type": "string"}, "GrantWrite": {"type": "string"}, "GrantWriteACP": {"type": "string"}, "IAMRoleArn": {"type": "string", "max": 2048, "min": 1, "pattern": "arn:[^:]+:iam::\\d{12}:role/.*"}, "ID": {"type": "string"}, "IdempotencyException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p/>", "exception": true}, "Include": {"type": "structure", "members": {"Buckets": {"shape": "Buckets", "documentation": "<p>A container for the S3 Storage Lens bucket includes.</p>"}, "Regions": {"shape": "Regions", "documentation": "<p>A container for the S3 Storage Lens Region includes.</p>"}}, "documentation": "<p>A container for what Amazon S3 Storage Lens configuration includes.</p>"}, "InternalServiceException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p/>", "exception": true, "fault": true}, "InvalidNextTokenException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p/>", "exception": true}, "InvalidRequestException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p/>", "exception": true}, "IsEnabled": {"type": "boolean"}, "IsPublic": {"type": "boolean"}, "JobArn": {"type": "string", "max": 1024, "min": 1, "pattern": "arn:[^:]+:s3:[a-zA-Z0-9\\-]+:\\d{12}:job\\/.*"}, "JobCreationTime": {"type": "timestamp"}, "JobDescriptor": {"type": "structure", "members": {"JobId": {"shape": "JobId", "documentation": "<p>The ID for the specified job.</p>"}, "ConfirmationRequired": {"shape": "ConfirmationRequired", "documentation": "<p>Indicates whether confirmation is required before Amazon S3 begins running the specified job. Confirmation is required only for jobs created through the Amazon S3 console.</p>", "box": true}, "Description": {"shape": "NonEmptyMaxLength256String", "documentation": "<p>The description for this job, if one was provided in this job's <code>Create Job</code> request.</p>", "box": true}, "JobArn": {"shape": "JobArn", "documentation": "<p>The Amazon Resource Name (ARN) for this job.</p>", "box": true}, "Status": {"shape": "JobStatus", "documentation": "<p>The current status of the specified job.</p>"}, "Manifest": {"shape": "JobManifest", "documentation": "<p>The configuration information for the specified job's manifest object.</p>", "box": true}, "Operation": {"shape": "JobOperation", "documentation": "<p>The operation that the specified job is configured to run on the objects listed in the manifest.</p>", "box": true}, "Priority": {"shape": "JobPriority", "documentation": "<p>The priority of the specified job.</p>"}, "ProgressSummary": {"shape": "JobProgressSummary", "documentation": "<p>Describes the total number of tasks that the specified job has run, the number of tasks that succeeded, and the number of tasks that failed.</p>", "box": true}, "StatusUpdateReason": {"shape": "JobStatusUpdateReason", "documentation": "<p>The reason for updating the job.</p>", "box": true}, "FailureReasons": {"shape": "JobFailureList", "documentation": "<p>If the specified job failed, this field contains information describing the failure.</p>", "box": true}, "Report": {"shape": "JobReport", "documentation": "<p>Contains the configuration information for the job-completion report if you requested one in the <code>Create Job</code> request.</p>", "box": true}, "CreationTime": {"shape": "JobCreationTime", "documentation": "<p>A timestamp indicating when this job was created.</p>"}, "TerminationDate": {"shape": "JobTerminationDate", "documentation": "<p>A timestamp indicating when this job terminated. A job's termination date is the date and time when it succeeded, failed, or was canceled.</p>", "box": true}, "RoleArn": {"shape": "IAMRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) for the Identity and Access Management (IAM) role assigned to run the tasks for this job.</p>", "box": true}, "SuspendedDate": {"shape": "SuspendedDate", "documentation": "<p>The timestamp when this job was suspended, if it has been suspended.</p>", "box": true}, "SuspendedCause": {"shape": "SuspendedCause", "documentation": "<p>The reason why the specified job was suspended. A job is only suspended if you create it through the Amazon S3 console. When you create the job, it enters the <code>Suspended</code> state to await confirmation before running. After you confirm the job, it automatically exits the <code>Suspended</code> state.</p>", "box": true}, "ManifestGenerator": {"shape": "JobManifestGenerator", "documentation": "<p>The manifest generator that was used to generate a job manifest for this job.</p>"}, "GeneratedManifestDescriptor": {"shape": "S3GeneratedManifestDescriptor", "documentation": "<p>The attribute of the JobDescriptor containing details about the job's generated manifest.</p>"}}, "documentation": "<p>A container element for the job configuration and status information returned by a <code>Describe Job</code> request.</p>"}, "JobFailure": {"type": "structure", "members": {"FailureCode": {"shape": "JobFailureCode", "documentation": "<p>The failure code, if any, for the specified job.</p>"}, "FailureReason": {"shape": "JobFailureReason", "documentation": "<p>The failure reason, if any, for the specified job.</p>"}}, "documentation": "<p>If this job failed, this element indicates why the job failed.</p>"}, "JobFailureCode": {"type": "string", "max": 64, "min": 1}, "JobFailureList": {"type": "list", "member": {"shape": "JobFailure"}}, "JobFailureReason": {"type": "string", "max": 256, "min": 1}, "JobId": {"type": "string", "max": 36, "min": 5, "pattern": "[a-zA-Z0-9\\-\\_]+"}, "JobListDescriptor": {"type": "structure", "members": {"JobId": {"shape": "JobId", "documentation": "<p>The ID for the specified job.</p>"}, "Description": {"shape": "NonEmptyMaxLength256String", "documentation": "<p>The user-specified description that was included in the specified job's <code>Create Job</code> request.</p>"}, "Operation": {"shape": "OperationName", "documentation": "<p>The operation that the specified job is configured to run on every object listed in the manifest.</p>"}, "Priority": {"shape": "JobPriority", "documentation": "<p>The current priority for the specified job.</p>"}, "Status": {"shape": "JobStatus", "documentation": "<p>The specified job's current status.</p>"}, "CreationTime": {"shape": "JobCreationTime", "documentation": "<p>A timestamp indicating when the specified job was created.</p>"}, "TerminationDate": {"shape": "JobTerminationDate", "documentation": "<p>A timestamp indicating when the specified job terminated. A job's termination date is the date and time when it succeeded, failed, or was canceled.</p>"}, "ProgressSummary": {"shape": "JobProgressSummary", "documentation": "<p>Describes the total number of tasks that the specified job has run, the number of tasks that succeeded, and the number of tasks that failed.</p>"}}, "documentation": "<p>Contains the configuration and status information for a single job retrieved as part of a job list.</p>"}, "JobListDescriptorList": {"type": "list", "member": {"shape": "JobListDescriptor"}}, "JobManifest": {"type": "structure", "required": ["Spec", "Location"], "members": {"Spec": {"shape": "JobManifestSpec", "documentation": "<p>Describes the format of the specified job's manifest. If the manifest is in CSV format, also describes the columns contained within the manifest.</p>"}, "Location": {"shape": "JobManifestLocation", "documentation": "<p>Contains the information required to locate the specified job's manifest.</p>"}}, "documentation": "<p>Contains the configuration information for a job's manifest.</p>"}, "JobManifestFieldList": {"type": "list", "member": {"shape": "JobManifestFieldName"}}, "JobManifestFieldName": {"type": "string", "enum": ["Ignore", "Bucket", "Key", "VersionId"]}, "JobManifestFormat": {"type": "string", "enum": ["S3BatchOperations_CSV_20180820", "S3InventoryReport_CSV_20161130"]}, "JobManifestGenerator": {"type": "structure", "members": {"S3JobManifestGenerator": {"shape": "S3JobManifestGenerator", "documentation": "<p>The S3 job ManifestGenerator's configuration details.</p>"}}, "documentation": "<p>Configures the type of the job's ManifestGenerator.</p>", "union": true}, "JobManifestGeneratorFilter": {"type": "structure", "members": {"EligibleForReplication": {"shape": "Boolean", "documentation": "<p>Include objects in the generated manifest only if they are eligible for replication according to the Replication configuration on the source bucket.</p>", "box": true}, "CreatedAfter": {"shape": "ObjectCreationTime", "documentation": "<p>If provided, the generated manifest should include only source bucket objects that were created after this time.</p>"}, "CreatedBefore": {"shape": "ObjectCreationTime", "documentation": "<p>If provided, the generated manifest should include only source bucket objects that were created before this time.</p>"}, "ObjectReplicationStatuses": {"shape": "ReplicationStatusFilterList", "documentation": "<p>If provided, the generated manifest should include only source bucket objects that have one of the specified Replication statuses.</p>"}}, "documentation": "<p>The filter used to describe a set of objects for the job's manifest.</p>"}, "JobManifestLocation": {"type": "structure", "required": ["ObjectArn", "ETag"], "members": {"ObjectArn": {"shape": "S3KeyArnString", "documentation": "<p>The Amazon Resource Name (ARN) for a manifest object.</p> <important> <p>When you're using XML requests, you must replace special characters (such as carriage returns) in object keys with their equivalent XML entity codes. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/object-keys.html#object-key-xml-related-constraints\"> XML-related object key constraints</a> in the <i>Amazon S3 User Guide</i>.</p> </important>"}, "ObjectVersionId": {"shape": "S3ObjectVersionId", "documentation": "<p>The optional version ID to identify a specific version of the manifest object.</p>", "box": true}, "ETag": {"shape": "NonEmptyMaxLength1024String", "documentation": "<p>The ETag for the specified manifest object.</p>"}}, "documentation": "<p>Contains the information required to locate a manifest object.</p>"}, "JobManifestSpec": {"type": "structure", "required": ["Format"], "members": {"Format": {"shape": "JobManifestFormat", "documentation": "<p>Indicates which of the available formats the specified manifest uses.</p>"}, "Fields": {"shape": "JobManifestFieldList", "documentation": "<p>If the specified manifest object is in the <code>S3BatchOperations_CSV_20180820</code> format, this element describes which columns contain the required data.</p>", "box": true}}, "documentation": "<p>Describes the format of a manifest. If the manifest is in CSV format, also describes the columns contained within the manifest.</p>"}, "JobNumberOfTasksFailed": {"type": "long", "min": 0}, "JobNumberOfTasksSucceeded": {"type": "long", "min": 0}, "JobOperation": {"type": "structure", "members": {"LambdaInvoke": {"shape": "LambdaInvokeOperation", "documentation": "<p>Directs the specified job to invoke an Lambda function on every object in the manifest.</p>", "box": true}, "S3PutObjectCopy": {"shape": "S3CopyObjectOperation", "documentation": "<p>Directs the specified job to run a PUT Copy object call on every object in the manifest.</p>", "box": true}, "S3PutObjectAcl": {"shape": "S3SetObjectAclOperation", "documentation": "<p>Directs the specified job to run a <code>PutObjectAcl</code> call on every object in the manifest.</p>", "box": true}, "S3PutObjectTagging": {"shape": "S3SetObjectTaggingOperation", "documentation": "<p>Directs the specified job to run a PUT Object tagging call on every object in the manifest.</p>", "box": true}, "S3DeleteObjectTagging": {"shape": "S3DeleteObjectTaggingOperation", "documentation": "<p>Directs the specified job to execute a DELETE Object tagging call on every object in the manifest.</p>", "box": true}, "S3InitiateRestoreObject": {"shape": "S3InitiateRestoreObjectOperation", "documentation": "<p>Directs the specified job to initiate restore requests for every archived object in the manifest.</p>", "box": true}, "S3PutObjectLegalHold": {"shape": "S3SetObjectLegalHoldOperation", "box": true}, "S3PutObjectRetention": {"shape": "S3SetObjectRetentionOperation", "box": true}, "S3ReplicateObject": {"shape": "S3ReplicateObjectOperation", "documentation": "<p>Directs the specified job to invoke <code>ReplicateObject</code> on every object in the job's manifest.</p>", "box": true}}, "documentation": "<p>The operation that you want this job to perform on every object listed in the manifest. For more information about the available operations, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/batch-ops-operations.html\">Operations</a> in the <i>Amazon S3 User Guide</i>.</p>"}, "JobPriority": {"type": "integer", "max": 2147483647, "min": 0}, "JobProgressSummary": {"type": "structure", "members": {"TotalNumberOfTasks": {"shape": "JobTotalNumberOfTasks", "documentation": "<p/>", "box": true}, "NumberOfTasksSucceeded": {"shape": "JobNumberOfTasksSucceeded", "documentation": "<p/>", "box": true}, "NumberOfTasksFailed": {"shape": "JobNumberOfTasksFailed", "documentation": "<p/>", "box": true}, "Timers": {"shape": "JobTimers", "documentation": "<p>The JobTimers attribute of a job's progress summary.</p>"}}, "documentation": "<p>Describes the total number of tasks that the specified job has started, the number of tasks that succeeded, and the number of tasks that failed.</p>"}, "JobReport": {"type": "structure", "required": ["Enabled"], "members": {"Bucket": {"shape": "S3BucketArnString", "documentation": "<p>The Amazon Resource Name (ARN) for the bucket where specified job-completion report will be stored.</p>", "box": true}, "Format": {"shape": "JobReportFormat", "documentation": "<p>The format of the specified job-completion report.</p>", "box": true}, "Enabled": {"shape": "Boolean", "documentation": "<p>Indicates whether the specified job will generate a job-completion report.</p>"}, "Prefix": {"shape": "ReportPrefixString", "documentation": "<p>An optional prefix to describe where in the specified bucket the job-completion report will be stored. Amazon S3 stores the job-completion report at <code>&lt;prefix&gt;/job-&lt;job-id&gt;/report.json</code>.</p>", "box": true}, "ReportScope": {"shape": "JobReportScope", "documentation": "<p>Indicates whether the job-completion report will include details of all tasks or only failed tasks.</p>", "box": true}}, "documentation": "<p>Contains the configuration parameters for a job-completion report.</p>"}, "JobReportFormat": {"type": "string", "enum": ["Report_CSV_20180820"]}, "JobReportScope": {"type": "string", "enum": ["AllTasks", "FailedTasksOnly"]}, "JobStatus": {"type": "string", "enum": ["Active", "Cancelled", "Cancelling", "Complete", "Completing", "Failed", "Failing", "New", "Paused", "Pausing", "Preparing", "Ready", "Suspended"]}, "JobStatusException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p/>", "exception": true}, "JobStatusList": {"type": "list", "member": {"shape": "JobStatus"}}, "JobStatusUpdateReason": {"type": "string", "max": 256, "min": 1}, "JobTerminationDate": {"type": "timestamp"}, "JobTimeInStateSeconds": {"type": "long", "min": 0}, "JobTimers": {"type": "structure", "members": {"ElapsedTimeInActiveSeconds": {"shape": "JobTimeInStateSeconds", "documentation": "<p>Indicates the elapsed time in seconds the job has been in the Active job state.</p>", "box": true}}, "documentation": "<p>Provides timing details for the job.</p>"}, "JobTotalNumberOfTasks": {"type": "long", "min": 0}, "KmsKeyArnString": {"type": "string", "max": 2000, "min": 1}, "LambdaInvokeOperation": {"type": "structure", "members": {"FunctionArn": {"shape": "FunctionArnString", "documentation": "<p>The Amazon Resource Name (ARN) for the Lambda function that the specified job will invoke on every object in the manifest.</p>"}}, "documentation": "<p>Contains the configuration parameters for a <code>Lambda Invoke</code> operation.</p>"}, "LifecycleConfiguration": {"type": "structure", "members": {"Rules": {"shape": "LifecycleRules", "documentation": "<p>A lifecycle rule for individual objects in an Outposts bucket. </p>"}}, "documentation": "<p>The container for the Outposts bucket lifecycle configuration.</p>"}, "LifecycleExpiration": {"type": "structure", "members": {"Date": {"shape": "Date", "documentation": "<p>Indicates at what date the object is to be deleted. Should be in GMT ISO 8601 format.</p>"}, "Days": {"shape": "Days", "documentation": "<p>Indicates the lifetime, in days, of the objects that are subject to the rule. The value must be a non-zero positive integer.</p>"}, "ExpiredObjectDeleteMarker": {"shape": "ExpiredObjectDeleteMarker", "documentation": "<p>Indicates whether Amazon S3 will remove a delete marker with no noncurrent versions. If set to true, the delete marker will be expired. If set to false, the policy takes no action. This cannot be specified with Days or Date in a Lifecycle Expiration Policy.</p>"}}, "documentation": "<p>The container of the Outposts bucket lifecycle expiration.</p>"}, "LifecycleRule": {"type": "structure", "required": ["Status"], "members": {"Expiration": {"shape": "LifecycleExpiration", "documentation": "<p>Specifies the expiration for the lifecycle of the object in the form of date, days and, whether the object has a delete marker.</p>"}, "ID": {"shape": "ID", "documentation": "<p>Unique identifier for the rule. The value cannot be longer than 255 characters.</p>"}, "Filter": {"shape": "LifecycleRuleFilter", "documentation": "<p>The container for the filter of lifecycle rule.</p>"}, "Status": {"shape": "ExpirationStatus", "documentation": "<p>If 'Enabled', the rule is currently being applied. If 'Disabled', the rule is not currently being applied.</p>"}, "Transitions": {"shape": "TransitionList", "documentation": "<p>Specifies when an Amazon S3 object transitions to a specified storage class.</p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>"}, "NoncurrentVersionTransitions": {"shape": "NoncurrentVersionTransitionList", "documentation": "<p> Specifies the transition rule for the lifecycle rule that describes when noncurrent objects transition to a specific storage class. If your bucket is versioning-enabled (or versioning is suspended), you can set this action to request that Amazon S3 transition noncurrent object versions to a specific storage class at a set period in the object's lifetime. </p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>"}, "NoncurrentVersionExpiration": {"shape": "NoncurrentVersionExpiration", "documentation": "<p>The noncurrent version expiration of the lifecycle rule.</p>"}, "AbortIncompleteMultipartUpload": {"shape": "AbortIncompleteMultipartUpload", "documentation": "<p>Specifies the days since the initiation of an incomplete multipart upload that Amazon S3 waits before permanently removing all parts of the upload. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/mpuoverview.html#mpu-abort-incomplete-mpu-lifecycle-config\"> Aborting Incomplete Multipart Uploads Using a Bucket Lifecycle Configuration</a> in the <i>Amazon S3 User Guide</i>.</p>"}}, "documentation": "<p>The container for the Outposts bucket lifecycle rule.</p>"}, "LifecycleRuleAndOperator": {"type": "structure", "members": {"Prefix": {"shape": "Prefix", "documentation": "<p>Prefix identifying one or more objects to which the rule applies.</p>"}, "Tags": {"shape": "S3TagSet", "documentation": "<p>All of these tags must exist in the object's tag set in order for the rule to apply.</p>"}, "ObjectSizeGreaterThan": {"shape": "ObjectSizeGreaterThanBytes", "documentation": "<p>Minimum object size to which the rule applies.</p>", "box": true}, "ObjectSizeLessThan": {"shape": "ObjectSizeLessThanBytes", "documentation": "<p>Maximum object size to which the rule applies.</p>", "box": true}}, "documentation": "<p>The container for the Outposts bucket lifecycle rule and operator.</p>"}, "LifecycleRuleFilter": {"type": "structure", "members": {"Prefix": {"shape": "Prefix", "documentation": "<p>Prefix identifying one or more objects to which the rule applies.</p> <important> <p>When you're using XML requests, you must replace special characters (such as carriage returns) in object keys with their equivalent XML entity codes. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/object-keys.html#object-key-xml-related-constraints\"> XML-related object key constraints</a> in the <i>Amazon S3 User Guide</i>.</p> </important>"}, "Tag": {"shape": "S3Tag"}, "And": {"shape": "LifecycleRuleAndOperator", "documentation": "<p>The container for the <code>AND</code> condition for the lifecycle rule.</p>"}, "ObjectSizeGreaterThan": {"shape": "ObjectSizeGreaterThanBytes", "documentation": "<p>Minimum object size to which the rule applies.</p>", "box": true}, "ObjectSizeLessThan": {"shape": "ObjectSizeLessThanBytes", "documentation": "<p>Maximum object size to which the rule applies.</p>", "box": true}}, "documentation": "<p>The container for the filter of the lifecycle rule.</p>"}, "LifecycleRules": {"type": "list", "member": {"shape": "LifecycleRule", "locationName": "Rule"}}, "ListAccessPointsForObjectLambdaRequest": {"type": "structure", "required": ["AccountId"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID for the account that owns the specified Object Lambda Access Point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "NextToken": {"shape": "NonEmptyMaxLength1024String", "documentation": "<p>If the list has more access points than can be returned in one call to this API, this field contains a continuation token that you can provide in subsequent calls to this API to retrieve additional access points.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of access points that you want to include in the list. The response may contain fewer access points but will never contain more. If there are more than this number of access points, then the response will include a continuation token in the <code>NextToken</code> field that you can use to retrieve the next page of access points.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListAccessPointsForObjectLambdaResult": {"type": "structure", "members": {"ObjectLambdaAccessPointList": {"shape": "ObjectLambdaAccessPointList", "documentation": "<p>Returns list of Object Lambda Access Points.</p>"}, "NextToken": {"shape": "NonEmptyMaxLength1024String", "documentation": "<p>If the list has more access points than can be returned in one call to this API, this field contains a continuation token that you can provide in subsequent calls to this API to retrieve additional access points.</p>"}}}, "ListAccessPointsRequest": {"type": "structure", "required": ["AccountId"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID for the account that owns the specified access points.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Bucket": {"shape": "BucketName", "documentation": "<p>The name of the bucket whose associated access points you want to list.</p> <p>For using this parameter with Amazon S3 on Outposts with the REST API, you must specify the name and the x-amz-outpost-id as well.</p> <p>For using this parameter with S3 on Outposts with the Amazon Web Services SDK and CLI, you must specify the ARN of the bucket accessed in the format <code>arn:aws:s3-outposts:&lt;Region&gt;:&lt;account-id&gt;:outpost/&lt;outpost-id&gt;/bucket/&lt;my-bucket-name&gt;</code>. For example, to access the bucket <code>reports</code> through Outpost <code>my-outpost</code> owned by account <code>************</code> in Region <code>us-west-2</code>, use the URL encoding of <code>arn:aws:s3-outposts:us-west-2:************:outpost/my-outpost/bucket/reports</code>. The value must be URL encoded. </p>", "contextParam": {"name": "Bucket"}, "location": "querystring", "locationName": "bucket"}, "NextToken": {"shape": "NonEmptyMaxLength1024String", "documentation": "<p>A continuation token. If a previous call to <code>ListAccessPoints</code> returned a continuation token in the <code>NextToken</code> field, then providing that value here causes Amazon S3 to retrieve the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of access points that you want to include in the list. If the specified bucket has more than this number of access points, then the response will include a continuation token in the <code>NextToken</code> field that you can use to retrieve the next page of access points.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListAccessPointsResult": {"type": "structure", "members": {"AccessPointList": {"shape": "AccessPointList", "documentation": "<p>Contains identification and configuration information for one or more access points associated with the specified bucket.</p>"}, "NextToken": {"shape": "NonEmptyMaxLength1024String", "documentation": "<p>If the specified bucket has more access points than can be returned in one call to this API, this field contains a continuation token that you can provide in subsequent calls to this API to retrieve additional access points.</p>"}}}, "ListJobsRequest": {"type": "structure", "required": ["AccountId"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID associated with the S3 Batch Operations job.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "JobStatuses": {"shape": "JobStatusList", "documentation": "<p>The <code>List Jobs</code> request returns jobs that match the statuses listed in this element.</p>", "location": "querystring", "locationName": "jobStatuses"}, "NextToken": {"shape": "StringForNextToken", "documentation": "<p>A pagination token to request the next page of results. Use the token that Amazon S3 returned in the <code>NextToken</code> element of the <code>ListJobsResult</code> from the previous <code>List Jobs</code> request.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of jobs that Amazon S3 will include in the <code>List Jobs</code> response. If there are more jobs than this number, the response will include a pagination token in the <code>NextToken</code> field to enable you to retrieve the next page of results.</p>", "box": true, "location": "querystring", "locationName": "maxResults"}}}, "ListJobsResult": {"type": "structure", "members": {"NextToken": {"shape": "StringForNextToken", "documentation": "<p>If the <code>List Jobs</code> request produced more than the maximum number of results, you can pass this value into a subsequent <code>List Jobs</code> request in order to retrieve the next page of results.</p>"}, "Jobs": {"shape": "JobListDescriptorList", "documentation": "<p>The list of current jobs and jobs that have ended within the last 30 days.</p>"}}}, "ListMultiRegionAccessPointsRequest": {"type": "structure", "required": ["AccountId"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID for the owner of the Multi-Region Access Point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "NextToken": {"shape": "NonEmptyMaxLength1024String", "documentation": "<p>Not currently used. Do not use this parameter.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>Not currently used. Do not use this parameter.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListMultiRegionAccessPointsResult": {"type": "structure", "members": {"AccessPoints": {"shape": "MultiRegionAccessPointReportList", "documentation": "<p>The list of Multi-Region Access Points associated with the user.</p>"}, "NextToken": {"shape": "NonEmptyMaxLength1024String", "documentation": "<p>If the specified bucket has more Multi-Region Access Points than can be returned in one call to this action, this field contains a continuation token. You can use this token tin subsequent calls to this action to retrieve additional Multi-Region Access Points.</p>"}}}, "ListRegionalBucketsRequest": {"type": "structure", "required": ["AccountId"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the Outposts bucket.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "NextToken": {"shape": "NonEmptyMaxLength1024String", "documentation": "<p/>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p/>", "location": "querystring", "locationName": "maxResults"}, "OutpostId": {"shape": "NonEmptyMaxLength64String", "documentation": "<p>The ID of the Outposts resource.</p> <note> <p>This ID is required by Amazon S3 on Outposts buckets.</p> </note>", "contextParam": {"name": "OutpostId"}, "location": "header", "locationName": "x-amz-outpost-id"}}}, "ListRegionalBucketsResult": {"type": "structure", "members": {"RegionalBucketList": {"shape": "RegionalBucketList", "documentation": "<p/>"}, "NextToken": {"shape": "NonEmptyMaxLength1024String", "documentation": "<p> <code>NextToken</code> is sent when <code>isTruncated</code> is true, which means there are more buckets that can be listed. The next list requests to Amazon S3 can be continued with this <code>NextToken</code>. <code>NextToken</code> is obfuscated and is not a real key.</p>"}}}, "ListStorageLensConfigurationEntry": {"type": "structure", "required": ["Id", "StorageLensArn", "HomeRegion"], "members": {"Id": {"shape": "ConfigId", "documentation": "<p>A container for the S3 Storage Lens configuration ID.</p>"}, "StorageLensArn": {"shape": "StorageLensArn", "documentation": "<p>The ARN of the S3 Storage Lens configuration. This property is read-only.</p>"}, "HomeRegion": {"shape": "S3AWSRegion", "documentation": "<p>A container for the S3 Storage Lens home Region. Your metrics data is stored and retained in your designated S3 Storage Lens home Region.</p>"}, "IsEnabled": {"shape": "IsEnabled", "documentation": "<p>A container for whether the S3 Storage Lens configuration is enabled. This property is required.</p>"}}, "documentation": "<p>Part of <code>ListStorageLensConfigurationResult</code>. Each entry includes the description of the S3 Storage Lens configuration, its home Region, whether it is enabled, its Amazon Resource Name (ARN), and config ID.</p>"}, "ListStorageLensConfigurationsRequest": {"type": "structure", "required": ["AccountId"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID of the requester.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "NextToken": {"shape": "ContinuationToken", "documentation": "<p>A pagination token to request the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListStorageLensConfigurationsResult": {"type": "structure", "members": {"NextToken": {"shape": "ContinuationToken", "documentation": "<p>If the request produced more than the maximum number of S3 Storage Lens configuration results, you can pass this value into a subsequent request to retrieve the next page of results.</p>"}, "StorageLensConfigurationList": {"shape": "StorageLensConfigurationList", "documentation": "<p>A list of S3 Storage Lens configurations.</p>"}}}, "Location": {"type": "string"}, "MFA": {"type": "string"}, "MFADelete": {"type": "string", "enum": ["Enabled", "Disabled"]}, "MFADeleteStatus": {"type": "string", "enum": ["Enabled", "Disabled"]}, "ManifestPrefixString": {"type": "string", "max": 512, "min": 1}, "MaxLength1024String": {"type": "string", "max": 1024}, "MaxResults": {"type": "integer", "max": 1000, "min": 0}, "Metrics": {"type": "structure", "required": ["Status"], "members": {"Status": {"shape": "MetricsStatus", "documentation": "<p>Specifies whether replication metrics are enabled. </p>"}, "EventThreshold": {"shape": "ReplicationTimeValue", "documentation": "<p>A container that specifies the time threshold for emitting the <code>s3:Replication:OperationMissedThreshold</code> event. </p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>"}}, "documentation": "<p>A container that specifies replication metrics-related settings.</p>"}, "MetricsStatus": {"type": "string", "enum": ["Enabled", "Disabled"]}, "MinStorageBytesPercentage": {"type": "double", "max": 100, "min": 0.1}, "Minutes": {"type": "integer"}, "MultiRegionAccessPointAlias": {"type": "string", "max": 63, "pattern": "^[a-z][a-z0-9]*[.]mrap$"}, "MultiRegionAccessPointClientToken": {"type": "string", "max": 64, "pattern": "\\S+"}, "MultiRegionAccessPointId": {"type": "string", "max": 200, "pattern": "^[a-zA-Z0-9\\:.-]{3,200}$"}, "MultiRegionAccessPointName": {"type": "string", "max": 50, "pattern": "^[a-z0-9][-a-z0-9]{1,48}[a-z0-9]$"}, "MultiRegionAccessPointPolicyDocument": {"type": "structure", "members": {"Established": {"shape": "EstablishedMultiRegionAccessPointPolicy", "documentation": "<p>The last established policy for the Multi-Region Access Point.</p>"}, "Proposed": {"shape": "ProposedMultiRegionAccessPointPolicy", "documentation": "<p>The proposed policy for the Multi-Region Access Point.</p>"}}, "documentation": "<p>The Multi-Region Access Point access control policy.</p> <p>When you update the policy, the update is first listed as the proposed policy. After the update is finished and all Regions have been updated, the proposed policy is listed as the established policy. If both policies have the same version number, the proposed policy is the established policy.</p>"}, "MultiRegionAccessPointRegionalResponse": {"type": "structure", "members": {"Name": {"shape": "RegionName", "documentation": "<p>The name of the Region in the Multi-Region Access Point.</p>"}, "RequestStatus": {"shape": "AsyncRequestStatus", "documentation": "<p>The current status of the Multi-Region Access Point in this Region.</p>"}}, "documentation": "<p>Status information for a single Multi-Region Access Point Region.</p>"}, "MultiRegionAccessPointRegionalResponseList": {"type": "list", "member": {"shape": "MultiRegionAccessPointRegionalResponse", "locationName": "Region"}}, "MultiRegionAccessPointReport": {"type": "structure", "members": {"Name": {"shape": "MultiRegionAccessPointName", "documentation": "<p>The name of the Multi-Region Access Point.</p>"}, "Alias": {"shape": "MultiRegionAccessPointAlias", "documentation": "<p>The alias for the Multi-Region Access Point. For more information about the distinction between the name and the alias of an Multi-Region Access Point, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/CreatingMultiRegionAccessPoints.html#multi-region-access-point-naming\">Managing Multi-Region Access Points</a>.</p>"}, "CreatedAt": {"shape": "CreationTimestamp", "documentation": "<p>When the Multi-Region Access Point create request was received.</p>"}, "PublicAccessBlock": {"shape": "PublicAccessBlockConfiguration"}, "Status": {"shape": "MultiRegionAccessPointStatus", "documentation": "<p>The current status of the Multi-Region Access Point.</p> <p> <code>CREATING</code> and <code>DELETING</code> are temporary states that exist while the request is propagating and being completed. If a Multi-Region Access Point has a status of <code>PARTIALLY_CREATED</code>, you can retry creation or send a request to delete the Multi-Region Access Point. If a Multi-Region Access Point has a status of <code>PARTIALLY_DELETED</code>, you can retry a delete request to finish the deletion of the Multi-Region Access Point.</p>"}, "Regions": {"shape": "RegionReportList", "documentation": "<p>A collection of the Regions and buckets associated with the Multi-Region Access Point.</p>"}}, "documentation": "<p>A collection of statuses for a Multi-Region Access Point in the various Regions it supports.</p>"}, "MultiRegionAccessPointReportList": {"type": "list", "member": {"shape": "MultiRegionAccessPointReport", "locationName": "AccessPoint"}}, "MultiRegionAccessPointRoute": {"type": "structure", "required": ["TrafficDialPercentage"], "members": {"Bucket": {"shape": "BucketName", "documentation": "<p>The name of the Amazon S3 bucket for which you'll submit a routing configuration change. Either the <code>Bucket</code> or the <code>Region</code> value must be provided. If both are provided, the bucket must be in the specified Region.</p>"}, "Region": {"shape": "RegionName", "documentation": "<p>The Amazon Web Services Region to which you'll be submitting a routing configuration change. Either the <code>Bucket</code> or the <code>Region</code> value must be provided. If both are provided, the bucket must be in the specified Region.</p>"}, "TrafficDialPercentage": {"shape": "TrafficDialPercentage", "documentation": "<p>The traffic state for the specified bucket or Amazon Web Services Region. </p> <p>A value of <code>0</code> indicates a passive state, which means that no new traffic will be routed to the Region. </p> <p>A value of <code>100</code> indicates an active state, which means that traffic will be routed to the specified Region. </p> <p>When the routing configuration for a Region is changed from active to passive, any in-progress operations (uploads, copies, deletes, and so on) to the formerly active Region will continue to run to until a final success or failure status is reached.</p> <p>If all Regions in the routing configuration are designated as passive, you'll receive an <code>InvalidRequest</code> error.</p>"}}, "documentation": "<p>A structure for a Multi-Region Access Point that indicates where Amazon S3 traffic can be routed. Routes can be either active or passive. Active routes can process Amazon S3 requests through the Multi-Region Access Point, but passive routes are not eligible to process Amazon S3 requests. </p> <p>Each route contains the Amazon S3 bucket name and the Amazon Web Services Region that the bucket is located in. The route also includes the <code>TrafficDialPercentage</code> value, which shows whether the bucket and Region are active (indicated by a value of <code>100</code>) or passive (indicated by a value of <code>0</code>).</p>"}, "MultiRegionAccessPointStatus": {"type": "string", "enum": ["READY", "INCONSISTENT_ACROSS_REGIONS", "CREATING", "PARTIALLY_CREATED", "PARTIALLY_DELETED", "DELETING"]}, "MultiRegionAccessPointsAsyncResponse": {"type": "structure", "members": {"Regions": {"shape": "MultiRegionAccessPointRegionalResponseList", "documentation": "<p>A collection of status information for the different Regions that a Multi-Region Access Point supports.</p>"}}, "documentation": "<p>The Multi-Region Access Point details that are returned when querying about an asynchronous request.</p>"}, "NetworkOrigin": {"type": "string", "enum": ["Internet", "VPC"]}, "NoSuchPublicAccessBlockConfiguration": {"type": "structure", "members": {"Message": {"shape": "NoSuchPublicAccessBlockConfigurationMessage"}}, "documentation": "<p>Amazon S3 throws this exception if you make a <code>GetPublicAccessBlock</code> request against an account that doesn't have a <code>PublicAccessBlockConfiguration</code> set.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "NoSuchPublicAccessBlockConfigurationMessage": {"type": "string"}, "NonEmptyMaxLength1024String": {"type": "string", "max": 1024, "min": 1}, "NonEmptyMaxLength2048String": {"type": "string", "max": 2048, "min": 1}, "NonEmptyMaxLength256String": {"type": "string", "max": 256, "min": 1}, "NonEmptyMaxLength64String": {"type": "string", "max": 64, "min": 1}, "NoncurrentVersionCount": {"type": "integer"}, "NoncurrentVersionExpiration": {"type": "structure", "members": {"NoncurrentDays": {"shape": "Days", "documentation": "<p>Specifies the number of days an object is noncurrent before Amazon S3 can perform the associated action. For information about the noncurrent days calculations, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/intro-lifecycle-rules.html#non-current-days-calculations\">How Amazon S3 Calculates When an Object Became Noncurrent</a> in the <i>Amazon S3 User Guide</i>.</p>"}, "NewerNoncurrentVersions": {"shape": "NoncurrentVersionCount", "documentation": "<p>Specifies how many noncurrent versions S3 on Outposts will retain. If there are this many more recent noncurrent versions, S3 on Outposts will take the associated action. For more information about noncurrent versions, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/intro-lifecycle-rules.html\">Lifecycle configuration elements</a> in the <i>Amazon S3 User Guide</i>.</p>", "box": true}}, "documentation": "<p>The container of the noncurrent version expiration.</p>"}, "NoncurrentVersionTransition": {"type": "structure", "members": {"NoncurrentDays": {"shape": "Days", "documentation": "<p>Specifies the number of days an object is noncurrent before Amazon S3 can perform the associated action. For information about the noncurrent days calculations, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/intro-lifecycle-rules.html#non-current-days-calculations\"> How Amazon S3 Calculates How Long an Object Has Been Noncurrent</a> in the <i>Amazon S3 User Guide</i>.</p>"}, "StorageClass": {"shape": "TransitionStorageClass", "documentation": "<p>The class of storage used to store the object.</p>"}}, "documentation": "<p>The container for the noncurrent version transition.</p>"}, "NoncurrentVersionTransitionList": {"type": "list", "member": {"shape": "NoncurrentVersionTransition", "locationName": "NoncurrentVersionTransition"}}, "NotFoundException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p/>", "exception": true}, "ObjectCreationTime": {"type": "timestamp"}, "ObjectLambdaAccessPoint": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "ObjectLambdaAccessPointName", "documentation": "<p>The name of the Object Lambda Access Point.</p>"}, "ObjectLambdaAccessPointArn": {"shape": "ObjectLambdaAccessPointArn", "documentation": "<p>Specifies the ARN for the Object Lambda Access Point.</p>"}, "Alias": {"shape": "ObjectLambdaAccessPointAlias", "documentation": "<p>The alias of the Object Lambda Access Point.</p>"}}, "documentation": "<p>An access point with an attached Lambda function used to access transformed data from an Amazon S3 bucket.</p>"}, "ObjectLambdaAccessPointAlias": {"type": "structure", "members": {"Value": {"shape": "ObjectLambdaAccessPointAliasValue", "documentation": "<p>The alias value of the Object Lambda Access Point.</p>"}, "Status": {"shape": "ObjectLambdaAccessPointAliasStatus", "documentation": "<p>The status of the Object Lambda Access Point alias. If the status is <code>PROVISIONING</code>, the Object Lambda Access Point is provisioning the alias and the alias is not ready for use yet. If the status is <code>READY</code>, the Object Lambda Access Point alias is successfully provisioned and ready for use.</p>"}}, "documentation": "<p>The alias of an Object Lambda Access Point. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/olap-use.html#ol-access-points-alias\">How to use a bucket-style alias for your S3 bucket Object Lambda Access Point</a>.</p>"}, "ObjectLambdaAccessPointAliasStatus": {"type": "string", "enum": ["PROVISIONING", "READY"], "max": 16, "min": 2}, "ObjectLambdaAccessPointAliasValue": {"type": "string", "max": 63, "min": 3, "pattern": "^[0-9a-z\\\\-]{3,63}"}, "ObjectLambdaAccessPointArn": {"type": "string", "max": 2048, "min": 1, "pattern": "arn:[^:]+:s3-object-lambda:[^:]*:\\d{12}:accesspoint/.*"}, "ObjectLambdaAccessPointList": {"type": "list", "member": {"shape": "ObjectLambdaAccessPoint", "locationName": "ObjectLambdaAccessPoint"}}, "ObjectLambdaAccessPointName": {"type": "string", "max": 45, "min": 3, "pattern": "^[a-z0-9]([a-z0-9\\-]*[a-z0-9])?$"}, "ObjectLambdaAllowedFeature": {"type": "string", "enum": ["GetObject-Range", "GetObject-PartNumber", "HeadObject-Range", "HeadObject-PartNumber"]}, "ObjectLambdaAllowedFeaturesList": {"type": "list", "member": {"shape": "ObjectLambdaAllowedFeature", "locationName": "AllowedFeature"}}, "ObjectLambdaConfiguration": {"type": "structure", "required": ["SupportingAccessPoint", "TransformationConfigurations"], "members": {"SupportingAccessPoint": {"shape": "ObjectLambdaSupportingAccessPointArn", "documentation": "<p>Standard access point associated with the Object Lambda Access Point.</p>"}, "CloudWatchMetricsEnabled": {"shape": "Boolean", "documentation": "<p>A container for whether the CloudWatch metrics configuration is enabled.</p>"}, "AllowedFeatures": {"shape": "ObjectLambdaAllowedFeaturesList", "documentation": "<p>A container for allowed features. Valid inputs are <code>GetObject-Range</code>, <code>GetObject-PartNumber</code>, <code>HeadObject-Range</code>, and <code>HeadObject-PartNumber</code>.</p>"}, "TransformationConfigurations": {"shape": "ObjectLambdaTransformationConfigurationsList", "documentation": "<p>A container for transformation configurations for an Object Lambda Access Point.</p>"}}, "documentation": "<p>A configuration used when creating an Object Lambda Access Point.</p>"}, "ObjectLambdaContentTransformation": {"type": "structure", "members": {"AwsLambda": {"shape": "AwsLambdaTransformation", "documentation": "<p>A container for an Lambda function.</p>"}}, "documentation": "<p>A container for AwsLambdaTransformation.</p>", "union": true}, "ObjectLambdaPolicy": {"type": "string"}, "ObjectLambdaSupportingAccessPointArn": {"type": "string", "max": 2048, "min": 1, "pattern": "arn:[^:]+:s3:[^:]*:\\d{12}:accesspoint/.*"}, "ObjectLambdaTransformationConfiguration": {"type": "structure", "required": ["Actions", "ContentTransformation"], "members": {"Actions": {"shape": "ObjectLambdaTransformationConfigurationActionsList", "documentation": "<p>A container for the action of an Object Lambda Access Point configuration. Valid inputs are <code>GetObject</code>, <code>ListObjects</code>, <code>HeadObject</code>, and <code>ListObjectsV2</code>.</p>"}, "ContentTransformation": {"shape": "ObjectLambdaContentTransformation", "documentation": "<p>A container for the content transformation of an Object Lambda Access Point configuration.</p>"}}, "documentation": "<p>A configuration used when creating an Object Lambda Access Point transformation.</p>"}, "ObjectLambdaTransformationConfigurationAction": {"type": "string", "enum": ["GetObject", "HeadObject", "ListObjects", "ListObjectsV2"]}, "ObjectLambdaTransformationConfigurationActionsList": {"type": "list", "member": {"shape": "ObjectLambdaTransformationConfigurationAction", "locationName": "Action"}}, "ObjectLambdaTransformationConfigurationsList": {"type": "list", "member": {"shape": "ObjectLambdaTransformationConfiguration", "locationName": "TransformationConfiguration"}}, "ObjectLockEnabledForBucket": {"type": "boolean"}, "ObjectSizeGreaterThanBytes": {"type": "long"}, "ObjectSizeLessThanBytes": {"type": "long"}, "OperationName": {"type": "string", "enum": ["LambdaInvoke", "S3PutObjectCopy", "S3PutObjectAcl", "S3PutObjectTagging", "S3DeleteObjectTagging", "S3InitiateRestoreObject", "S3PutObjectLegalHold", "S3PutObjectRetention", "S3ReplicateObject"]}, "OutputSchemaVersion": {"type": "string", "enum": ["V_1"]}, "OwnerOverride": {"type": "string", "enum": ["Destination"]}, "Policy": {"type": "string"}, "PolicyStatus": {"type": "structure", "members": {"IsPublic": {"shape": "IsPublic", "documentation": "<p/>", "locationName": "IsPublic"}}, "documentation": "<p>Indicates whether this access point policy is public. For more information about how Amazon S3 evaluates policies to determine whether they are public, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/access-control-block-public-access.html#access-control-block-public-access-policy-status\">The Meaning of \"Public\"</a> in the <i>Amazon S3 User Guide</i>. </p>"}, "Prefix": {"type": "string"}, "PrefixLevel": {"type": "structure", "required": ["StorageMetrics"], "members": {"StorageMetrics": {"shape": "PrefixLevelStorageMetrics", "documentation": "<p>A container for the prefix-level storage metrics for S3 Storage Lens.</p>"}}, "documentation": "<p>A container for the prefix-level configuration.</p>"}, "PrefixLevelStorageMetrics": {"type": "structure", "members": {"IsEnabled": {"shape": "IsEnabled", "documentation": "<p>A container for whether prefix-level storage metrics are enabled.</p>"}, "SelectionCriteria": {"shape": "SelectionCriteria"}}, "documentation": "<p>A container for the prefix-level storage metrics for S3 Storage Lens.</p>"}, "Priority": {"type": "integer"}, "ProposedMultiRegionAccessPointPolicy": {"type": "structure", "members": {"Policy": {"shape": "Policy", "documentation": "<p>The details of the proposed policy.</p>"}}, "documentation": "<p>The proposed access control policy for the Multi-Region Access Point.</p> <p>When you update the policy, the update is first listed as the proposed policy. After the update is finished and all Regions have been updated, the proposed policy is listed as the established policy. If both policies have the same version number, the proposed policy is the established policy.</p>"}, "PublicAccessBlockConfiguration": {"type": "structure", "members": {"BlockPublicAcls": {"shape": "Setting", "documentation": "<p>Specifies whether Amazon S3 should block public access control lists (ACLs) for buckets in this account. Setting this element to <code>TRUE</code> causes the following behavior:</p> <ul> <li> <p> <code>PutBucketAcl</code> and <code>PutObjectAcl</code> calls fail if the specified ACL is public.</p> </li> <li> <p>PUT Object calls fail if the request includes a public ACL.</p> </li> <li> <p>PUT Bucket calls fail if the request includes a public ACL.</p> </li> </ul> <p>Enabling this setting doesn't affect existing policies or ACLs.</p> <p>This property is not supported for Amazon S3 on Outposts.</p>", "locationName": "BlockPublicAcls"}, "IgnorePublicAcls": {"shape": "Setting", "documentation": "<p>Specifies whether Amazon S3 should ignore public ACLs for buckets in this account. Setting this element to <code>TRUE</code> causes Amazon S3 to ignore all public ACLs on buckets in this account and any objects that they contain. </p> <p>Enabling this setting doesn't affect the persistence of any existing ACLs and doesn't prevent new public ACLs from being set.</p> <p>This property is not supported for Amazon S3 on Outposts.</p>", "locationName": "IgnorePublicAcls"}, "BlockPublicPolicy": {"shape": "Setting", "documentation": "<p>Specifies whether Amazon S3 should block public bucket policies for buckets in this account. Setting this element to <code>TRUE</code> causes Amazon S3 to reject calls to PUT Bucket policy if the specified bucket policy allows public access. </p> <p>Enabling this setting doesn't affect existing bucket policies.</p> <p>This property is not supported for Amazon S3 on Outposts.</p>", "locationName": "BlockPublicPolicy"}, "RestrictPublicBuckets": {"shape": "Setting", "documentation": "<p>Specifies whether Amazon S3 should restrict public bucket policies for buckets in this account. Setting this element to <code>TRUE</code> restricts access to buckets with public policies to only Amazon Web Service principals and authorized users within this account.</p> <p>Enabling this setting doesn't affect previously stored bucket policies, except that public and cross-account access within any public bucket policy, including non-public delegation to specific accounts, is blocked.</p> <p>This property is not supported for Amazon S3 on Outposts.</p>", "locationName": "RestrictPublicBuckets"}}, "documentation": "<p>The <code>PublicAccessBlock</code> configuration that you want to apply to this Amazon S3 account. You can enable the configuration options in any combination. For more information about when Amazon S3 considers a bucket or object public, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/access-control-block-public-access.html#access-control-block-public-access-policy-status\">The Meaning of \"Public\"</a> in the <i>Amazon S3 User Guide</i>.</p> <p>This data type is not supported for Amazon S3 on Outposts.</p>"}, "PublicAccessBlockEnabled": {"type": "boolean"}, "PutAccessPointConfigurationForObjectLambdaRequest": {"type": "structure", "required": ["AccountId", "Name", "Configuration"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID for the account that owns the specified Object Lambda Access Point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Name": {"shape": "ObjectLambdaAccessPointName", "documentation": "<p>The name of the Object Lambda Access Point.</p>", "location": "uri", "locationName": "name"}, "Configuration": {"shape": "ObjectLambdaConfiguration", "documentation": "<p>Object Lambda Access Point configuration document.</p>"}}}, "PutAccessPointPolicyForObjectLambdaRequest": {"type": "structure", "required": ["AccountId", "Name", "Policy"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID for the account that owns the specified Object Lambda Access Point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Name": {"shape": "ObjectLambdaAccessPointName", "documentation": "<p>The name of the Object Lambda Access Point.</p>", "location": "uri", "locationName": "name"}, "Policy": {"shape": "ObjectLambdaPolicy", "documentation": "<p>Object Lambda Access Point resource policy document.</p>"}}}, "PutAccessPointPolicyRequest": {"type": "structure", "required": ["AccountId", "Name", "Policy"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID for owner of the bucket associated with the specified access point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Name": {"shape": "AccessPointName", "documentation": "<p>The name of the access point that you want to associate with the specified policy.</p> <p>For using this parameter with Amazon S3 on Outposts with the REST API, you must specify the name and the x-amz-outpost-id as well.</p> <p>For using this parameter with S3 on Outposts with the Amazon Web Services SDK and CLI, you must specify the ARN of the access point accessed in the format <code>arn:aws:s3-outposts:&lt;Region&gt;:&lt;account-id&gt;:outpost/&lt;outpost-id&gt;/accesspoint/&lt;my-accesspoint-name&gt;</code>. For example, to access the access point <code>reports-ap</code> through Outpost <code>my-outpost</code> owned by account <code>************</code> in Region <code>us-west-2</code>, use the URL encoding of <code>arn:aws:s3-outposts:us-west-2:************:outpost/my-outpost/accesspoint/reports-ap</code>. The value must be URL encoded. </p>", "contextParam": {"name": "AccessPointName"}, "location": "uri", "locationName": "name"}, "Policy": {"shape": "Policy", "documentation": "<p>The policy that you want to apply to the specified access point. For more information about access point policies, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/access-points.html\">Managing data access with Amazon S3 access points</a> in the <i>Amazon S3 User Guide</i>.</p>"}}}, "PutBucketLifecycleConfigurationRequest": {"type": "structure", "required": ["AccountId", "Bucket"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the Outposts bucket.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Bucket": {"shape": "BucketName", "documentation": "<p>The name of the bucket for which to set the configuration.</p>", "contextParam": {"name": "Bucket"}, "location": "uri", "locationName": "name"}, "LifecycleConfiguration": {"shape": "LifecycleConfiguration", "documentation": "<p>Container for lifecycle rules. You can add as many as 1,000 rules.</p>", "locationName": "LifecycleConfiguration", "xmlNamespace": {"uri": "http://awss3control.amazonaws.com/doc/2018-08-20/"}}}, "payload": "LifecycleConfiguration"}, "PutBucketPolicyRequest": {"type": "structure", "required": ["AccountId", "Bucket", "Policy"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the Outposts bucket.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Bucket": {"shape": "BucketName", "documentation": "<p>Specifies the bucket.</p> <p>For using this parameter with Amazon S3 on Outposts with the REST API, you must specify the name and the x-amz-outpost-id as well.</p> <p>For using this parameter with S3 on Outposts with the Amazon Web Services SDK and CLI, you must specify the ARN of the bucket accessed in the format <code>arn:aws:s3-outposts:&lt;Region&gt;:&lt;account-id&gt;:outpost/&lt;outpost-id&gt;/bucket/&lt;my-bucket-name&gt;</code>. For example, to access the bucket <code>reports</code> through Outpost <code>my-outpost</code> owned by account <code>************</code> in Region <code>us-west-2</code>, use the URL encoding of <code>arn:aws:s3-outposts:us-west-2:************:outpost/my-outpost/bucket/reports</code>. The value must be URL encoded. </p>", "contextParam": {"name": "Bucket"}, "location": "uri", "locationName": "name"}, "ConfirmRemoveSelfBucketAccess": {"shape": "ConfirmRemoveSelfBucketAccess", "documentation": "<p>Set this parameter to true to confirm that you want to remove your permissions to change this bucket policy in the future.</p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>", "location": "header", "locationName": "x-amz-confirm-remove-self-bucket-access"}, "Policy": {"shape": "Policy", "documentation": "<p>The bucket policy as a JSON document.</p>"}}}, "PutBucketReplicationRequest": {"type": "structure", "required": ["AccountId", "Bucket", "ReplicationConfiguration"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the Outposts bucket.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Bucket": {"shape": "BucketName", "documentation": "<p>Specifies the S3 on Outposts bucket to set the configuration for.</p> <p>For using this parameter with Amazon S3 on Outposts with the REST API, you must specify the name and the x-amz-outpost-id as well.</p> <p>For using this parameter with S3 on Outposts with the Amazon Web Services SDK and CLI, you must specify the ARN of the bucket accessed in the format <code>arn:aws:s3-outposts:&lt;Region&gt;:&lt;account-id&gt;:outpost/&lt;outpost-id&gt;/bucket/&lt;my-bucket-name&gt;</code>. For example, to access the bucket <code>reports</code> through Outpost <code>my-outpost</code> owned by account <code>************</code> in Region <code>us-west-2</code>, use the URL encoding of <code>arn:aws:s3-outposts:us-west-2:************:outpost/my-outpost/bucket/reports</code>. The value must be URL encoded. </p>", "contextParam": {"name": "Bucket"}, "location": "uri", "locationName": "name"}, "ReplicationConfiguration": {"shape": "ReplicationConfiguration", "documentation": "<p/>", "locationName": "ReplicationConfiguration", "xmlNamespace": {"uri": "http://awss3control.amazonaws.com/doc/2018-08-20/"}}}, "payload": "ReplicationConfiguration"}, "PutBucketTaggingRequest": {"type": "structure", "required": ["AccountId", "Bucket", "Tagging"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the Outposts bucket.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Bucket": {"shape": "BucketName", "documentation": "<p>The Amazon Resource Name (ARN) of the bucket.</p> <p>For using this parameter with Amazon S3 on Outposts with the REST API, you must specify the name and the x-amz-outpost-id as well.</p> <p>For using this parameter with S3 on Outposts with the Amazon Web Services SDK and CLI, you must specify the ARN of the bucket accessed in the format <code>arn:aws:s3-outposts:&lt;Region&gt;:&lt;account-id&gt;:outpost/&lt;outpost-id&gt;/bucket/&lt;my-bucket-name&gt;</code>. For example, to access the bucket <code>reports</code> through Outpost <code>my-outpost</code> owned by account <code>************</code> in Region <code>us-west-2</code>, use the URL encoding of <code>arn:aws:s3-outposts:us-west-2:************:outpost/my-outpost/bucket/reports</code>. The value must be URL encoded. </p>", "contextParam": {"name": "Bucket"}, "location": "uri", "locationName": "name"}, "Tagging": {"shape": "Tagging", "documentation": "<p/>", "locationName": "Tagging", "xmlNamespace": {"uri": "http://awss3control.amazonaws.com/doc/2018-08-20/"}}}, "payload": "Tagging"}, "PutBucketVersioningRequest": {"type": "structure", "required": ["AccountId", "Bucket", "VersioningConfiguration"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the S3 on Outposts bucket.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Bucket": {"shape": "BucketName", "documentation": "<p>The S3 on Outposts bucket to set the versioning state for.</p>", "contextParam": {"name": "Bucket"}, "location": "uri", "locationName": "name"}, "MFA": {"shape": "MFA", "documentation": "<p>The concatenation of the authentication device's serial number, a space, and the value that is displayed on your authentication device.</p>", "location": "header", "locationName": "x-amz-mfa"}, "VersioningConfiguration": {"shape": "VersioningConfiguration", "documentation": "<p>The root-level tag for the <code>VersioningConfiguration</code> parameters.</p>", "locationName": "VersioningConfiguration", "xmlNamespace": {"uri": "http://awss3control.amazonaws.com/doc/2018-08-20/"}}}, "payload": "VersioningConfiguration"}, "PutJobTaggingRequest": {"type": "structure", "required": ["AccountId", "JobId", "Tags"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID associated with the S3 Batch Operations job.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "JobId": {"shape": "JobId", "documentation": "<p>The ID for the S3 Batch Operations job whose tags you want to replace.</p>", "location": "uri", "locationName": "id"}, "Tags": {"shape": "S3TagSet", "documentation": "<p>The set of tags to associate with the S3 Batch Operations job.</p>"}}}, "PutJobTaggingResult": {"type": "structure", "members": {}}, "PutMultiRegionAccessPointPolicyInput": {"type": "structure", "required": ["Name", "Policy"], "members": {"Name": {"shape": "MultiRegionAccessPointName", "documentation": "<p>The name of the Multi-Region Access Point associated with the request.</p>"}, "Policy": {"shape": "Policy", "documentation": "<p>The policy details for the <code>PutMultiRegionAccessPoint</code> request.</p>"}}, "documentation": "<p>A container for the information associated with a <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutMultiRegionAccessPoint.html\">PutMultiRegionAccessPoint</a> request.</p>"}, "PutMultiRegionAccessPointPolicyRequest": {"type": "structure", "required": ["AccountId", "ClientToken", "Details"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID for the owner of the Multi-Region Access Point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "ClientToken": {"shape": "MultiRegionAccessPointClientToken", "documentation": "<p>An idempotency token used to identify the request and guarantee that requests are unique.</p>", "idempotencyToken": true}, "Details": {"shape": "PutMultiRegionAccessPointPolicyInput", "documentation": "<p>A container element containing the details of the policy for the Multi-Region Access Point.</p>"}}}, "PutMultiRegionAccessPointPolicyResult": {"type": "structure", "members": {"RequestTokenARN": {"shape": "AsyncRequestTokenARN", "documentation": "<p>The request token associated with the request. You can use this token with <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_DescribeMultiRegionAccessPointOperation.html\">DescribeMultiRegionAccessPointOperation</a> to determine the status of asynchronous requests.</p>"}}}, "PutPublicAccessBlockRequest": {"type": "structure", "required": ["PublicAccessBlockConfiguration", "AccountId"], "members": {"PublicAccessBlockConfiguration": {"shape": "PublicAccessBlockConfiguration", "documentation": "<p>The <code>PublicAccessBlock</code> configuration that you want to apply to the specified Amazon Web Services account.</p>", "locationName": "PublicAccessBlockConfiguration", "xmlNamespace": {"uri": "http://awss3control.amazonaws.com/doc/2018-08-20/"}}, "AccountId": {"shape": "AccountId", "documentation": "<p>The account ID for the Amazon Web Services account whose <code>PublicAccessBlock</code> configuration you want to set.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}}, "payload": "PublicAccessBlockConfiguration"}, "PutStorageLensConfigurationRequest": {"type": "structure", "required": ["ConfigId", "AccountId", "StorageLensConfiguration"], "members": {"ConfigId": {"shape": "ConfigId", "documentation": "<p>The ID of the S3 Storage Lens configuration.</p>", "location": "uri", "locationName": "storagelensid"}, "AccountId": {"shape": "AccountId", "documentation": "<p>The account ID of the requester.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "StorageLensConfiguration": {"shape": "StorageLensConfiguration", "documentation": "<p>The S3 Storage Lens configuration.</p>"}, "Tags": {"shape": "StorageLensTags", "documentation": "<p>The tag set of the S3 Storage Lens configuration.</p> <note> <p>You can set up to a maximum of 50 tags.</p> </note>"}}}, "PutStorageLensConfigurationTaggingRequest": {"type": "structure", "required": ["ConfigId", "AccountId", "Tags"], "members": {"ConfigId": {"shape": "ConfigId", "documentation": "<p>The ID of the S3 Storage Lens configuration.</p>", "location": "uri", "locationName": "storagelensid"}, "AccountId": {"shape": "AccountId", "documentation": "<p>The account ID of the requester.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Tags": {"shape": "StorageLensTags", "documentation": "<p>The tag set of the S3 Storage Lens configuration.</p> <note> <p>You can set up to a maximum of 50 tags.</p> </note>"}}}, "PutStorageLensConfigurationTaggingResult": {"type": "structure", "members": {}}, "Region": {"type": "structure", "required": ["Bucket"], "members": {"Bucket": {"shape": "BucketName", "documentation": "<p>The name of the associated bucket for the Region.</p>"}, "BucketAccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID that owns the Amazon S3 bucket that's associated with this Multi-Region Access Point.</p>"}}, "documentation": "<p>A Region that supports a Multi-Region Access Point as well as the associated bucket for the Region.</p>"}, "RegionCreationList": {"type": "list", "member": {"shape": "Region", "locationName": "Region"}}, "RegionName": {"type": "string", "max": 64, "min": 1}, "RegionReport": {"type": "structure", "members": {"Bucket": {"shape": "BucketName", "documentation": "<p>The name of the bucket.</p>"}, "Region": {"shape": "RegionName", "documentation": "<p>The name of the Region.</p>"}, "BucketAccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID that owns the Amazon S3 bucket that's associated with this Multi-Region Access Point.</p>"}}, "documentation": "<p>A combination of a bucket and Region that's part of a Multi-Region Access Point.</p>"}, "RegionReportList": {"type": "list", "member": {"shape": "RegionReport", "locationName": "Region"}}, "RegionalBucket": {"type": "structure", "required": ["Bucket", "PublicAccessBlockEnabled", "CreationDate"], "members": {"Bucket": {"shape": "BucketName", "documentation": "<p/>"}, "BucketArn": {"shape": "S3RegionalBucketArn", "documentation": "<p>The Amazon Resource Name (ARN) for the regional bucket.</p>"}, "PublicAccessBlockEnabled": {"shape": "PublicAccessBlockEnabled", "documentation": "<p/>"}, "CreationDate": {"shape": "CreationDate", "documentation": "<p>The creation date of the regional bucket</p>"}, "OutpostId": {"shape": "NonEmptyMaxLength64String", "documentation": "<p>The Outposts ID of the regional bucket.</p>"}}, "documentation": "<p>The container for the regional bucket.</p>"}, "RegionalBucketList": {"type": "list", "member": {"shape": "RegionalBucket", "locationName": "RegionalBucket"}}, "Regions": {"type": "list", "member": {"shape": "S3AWSRegion", "locationName": "Region"}}, "ReplicaKmsKeyID": {"type": "string"}, "ReplicaModifications": {"type": "structure", "required": ["Status"], "members": {"Status": {"shape": "ReplicaModificationsStatus", "documentation": "<p>Specifies whether S3 on Outposts replicates modifications to object metadata on replicas.</p>"}}, "documentation": "<p>A filter that you can use to specify whether replica modification sync is enabled. S3 on Outposts replica modification sync can help you keep object metadata synchronized between replicas and source objects. By default, S3 on Outposts replicates metadata from the source objects to the replicas only. When replica modification sync is enabled, S3 on Outposts replicates metadata changes made to the replica copies back to the source object, making the replication bidirectional.</p> <p>To replicate object metadata modifications on replicas, you can specify this element and set the <code>Status</code> of this element to <code>Enabled</code>.</p> <note> <p>You must enable replica modification sync on the source and destination buckets to replicate replica metadata changes between the source and the replicas.</p> </note>"}, "ReplicaModificationsStatus": {"type": "string", "enum": ["Enabled", "Disabled"]}, "ReplicationConfiguration": {"type": "structure", "required": ["Role", "Rules"], "members": {"Role": {"shape": "Role", "documentation": "<p>The Amazon Resource Name (ARN) of the Identity and Access Management (IAM) role that S3 on Outposts assumes when replicating objects. For information about S3 replication on Outposts configuration, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/outposts-replication-how-setup.html\">Setting up replication</a> in the <i>Amazon S3 User Guide</i>.</p>"}, "Rules": {"shape": "ReplicationRules", "documentation": "<p>A container for one or more replication rules. A replication configuration must have at least one rule and can contain an array of 100 rules at the most. </p>"}}, "documentation": "<p>A container for one or more replication rules. A replication configuration must have at least one rule and you can add up to 100 rules. The maximum size of a replication configuration is 128 KB.</p>"}, "ReplicationRule": {"type": "structure", "required": ["Status", "Destination", "Bucket"], "members": {"ID": {"shape": "ID", "documentation": "<p>A unique identifier for the rule. The maximum value is 255 characters.</p>"}, "Priority": {"shape": "Priority", "documentation": "<p>The priority indicates which rule has precedence whenever two or more replication rules conflict. S3 on Outposts attempts to replicate objects according to all replication rules. However, if there are two or more rules with the same destination Outposts bucket, then objects will be replicated according to the rule with the highest priority. The higher the number, the higher the priority. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/replication-between-outposts.html\">Creating replication rules on Outposts</a> in the <i>Amazon S3 User Guide</i>.</p>", "box": true}, "Prefix": {"shape": "Prefix", "documentation": "<p>An object key name prefix that identifies the object or objects to which the rule applies. The maximum prefix length is 1,024 characters. To include all objects in an Outposts bucket, specify an empty string.</p> <important> <p>When you're using XML requests, you must replace special characters (such as carriage returns) in object keys with their equivalent XML entity codes. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/object-keys.html#object-key-xml-related-constraints\"> XML-related object key constraints</a> in the <i>Amazon S3 User Guide</i>.</p> </important>", "deprecated": true, "deprecatedMessage": "Prefix has been deprecated"}, "Filter": {"shape": "ReplicationRuleFilter", "documentation": "<p>A filter that identifies the subset of objects to which the replication rule applies. A <code>Filter</code> element must specify exactly one <code>Prefix</code>, <code>Tag</code>, or <code>And</code> child element.</p>"}, "Status": {"shape": "ReplicationRuleStatus", "documentation": "<p>Specifies whether the rule is enabled.</p>"}, "SourceSelectionCriteria": {"shape": "SourceSelectionCriteria", "documentation": "<p>A container that describes additional filters for identifying the source Outposts objects that you want to replicate. You can choose to enable or disable the replication of these objects.</p>"}, "ExistingObjectReplication": {"shape": "ExistingObjectReplication", "documentation": "<p>An optional configuration to replicate existing source bucket objects. </p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>"}, "Destination": {"shape": "Destination", "documentation": "<p>A container for information about the replication destination and its configurations.</p>"}, "DeleteMarkerReplication": {"shape": "DeleteMarkerReplication", "documentation": "<p>Specifies whether S3 on Outposts replicates delete markers. If you specify a <code>Filter</code> element in your replication configuration, you must also include a <code>DeleteMarkerReplication</code> element. If your <code>Filter</code> includes a <code>Tag</code> element, the <code>DeleteMarkerReplication</code> element's <code>Status</code> child element must be set to <code>Disabled</code>, because S3 on Outposts doesn't support replicating delete markers for tag-based rules.</p> <p>For more information about delete marker replication, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/S3OutpostsReplication.html#outposts-replication-what-is-replicated\">How delete operations affect replication</a> in the <i>Amazon S3 User Guide</i>. </p>"}, "Bucket": {"shape": "BucketIdentifierString", "documentation": "<p>The Amazon Resource Name (ARN) of the access point for the source Outposts bucket that you want S3 on Outposts to replicate the objects from.</p>"}}, "documentation": "<p>Specifies which S3 on Outposts objects to replicate and where to store the replicas.</p>"}, "ReplicationRuleAndOperator": {"type": "structure", "members": {"Prefix": {"shape": "Prefix", "documentation": "<p>An object key name prefix that identifies the subset of objects that the rule applies to.</p>"}, "Tags": {"shape": "S3TagSet", "documentation": "<p>An array of tags that contain key and value pairs.</p>"}}, "documentation": "<p>A container for specifying rule filters. The filters determine the subset of objects to which the rule applies. This element is required only if you specify more than one filter. </p> <p>For example:</p> <ul> <li> <p>If you specify both a <code>Prefix</code> and a <code>Tag</code> filter, wrap these filters in an <code>And</code> element. </p> </li> <li> <p>If you specify a filter based on multiple tags, wrap the <code>Tag</code> elements in an <code>And</code> element.</p> </li> </ul>"}, "ReplicationRuleFilter": {"type": "structure", "members": {"Prefix": {"shape": "Prefix", "documentation": "<p>An object key name prefix that identifies the subset of objects that the rule applies to.</p> <important> <p>When you're using XML requests, you must replace special characters (such as carriage returns) in object keys with their equivalent XML entity codes. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/object-keys.html#object-key-xml-related-constraints\"> XML-related object key constraints</a> in the <i>Amazon S3 User Guide</i>.</p> </important>"}, "Tag": {"shape": "S3Tag"}, "And": {"shape": "ReplicationRuleAndOperator", "documentation": "<p>A container for specifying rule filters. The filters determine the subset of objects that the rule applies to. This element is required only if you specify more than one filter. For example: </p> <ul> <li> <p>If you specify both a <code>Prefix</code> and a <code>Tag</code> filter, wrap these filters in an <code>And</code> element.</p> </li> <li> <p>If you specify a filter based on multiple tags, wrap the <code>Tag</code> elements in an <code>And</code> element.</p> </li> </ul>"}}, "documentation": "<p>A filter that identifies the subset of objects to which the replication rule applies. A <code>Filter</code> element must specify exactly one <code>Prefix</code>, <code>Tag</code>, or <code>And</code> child element.</p>"}, "ReplicationRuleStatus": {"type": "string", "enum": ["Enabled", "Disabled"]}, "ReplicationRules": {"type": "list", "member": {"shape": "ReplicationRule", "locationName": "Rule"}}, "ReplicationStatus": {"type": "string", "enum": ["COMPLETED", "FAILED", "REPLICA", "NONE"]}, "ReplicationStatusFilterList": {"type": "list", "member": {"shape": "ReplicationStatus"}}, "ReplicationStorageClass": {"type": "string", "enum": ["STANDARD", "REDUCED_REDUNDANCY", "STANDARD_IA", "ONEZONE_IA", "INTELLIGENT_TIERING", "GLACIER", "DEEP_ARCHIVE", "OUTPOSTS", "GLACIER_IR"]}, "ReplicationTime": {"type": "structure", "required": ["Status", "Time"], "members": {"Status": {"shape": "ReplicationTimeStatus", "documentation": "<p>Specifies whether S3 Replication Time Control (S3 RTC) is enabled. </p>"}, "Time": {"shape": "ReplicationTimeValue", "documentation": "<p>A container that specifies the time by which replication should be complete for all objects and operations on objects. </p>"}}, "documentation": "<p>A container that specifies S3 Replication Time Control (S3 RTC) related information, including whether S3 RTC is enabled and the time when all objects and operations on objects must be replicated.</p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>"}, "ReplicationTimeStatus": {"type": "string", "enum": ["Enabled", "Disabled"]}, "ReplicationTimeValue": {"type": "structure", "members": {"Minutes": {"shape": "Minutes", "documentation": "<p>Contains an integer that specifies the time period in minutes. </p> <p>Valid value: 15</p>", "box": true}}, "documentation": "<p>A container that specifies the time value for S3 Replication Time Control (S3 RTC). This value is also used for the replication metrics <code>EventThreshold</code> element. </p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>"}, "ReportPrefixString": {"type": "string", "max": 512, "min": 1}, "RequestedJobStatus": {"type": "string", "enum": ["Cancelled", "Ready"]}, "Role": {"type": "string"}, "RouteList": {"type": "list", "member": {"shape": "MultiRegionAccessPointRoute", "locationName": "Route"}}, "S3AWSRegion": {"type": "string", "max": 30, "min": 5, "pattern": "[a-z0-9\\-]+"}, "S3AccessControlList": {"type": "structure", "required": ["Owner"], "members": {"Owner": {"shape": "S3ObjectOwner", "documentation": "<p/>"}, "Grants": {"shape": "S3GrantList", "documentation": "<p/>"}}, "documentation": "<p/>"}, "S3AccessControlPolicy": {"type": "structure", "members": {"AccessControlList": {"shape": "S3AccessControlList", "documentation": "<p/>", "box": true}, "CannedAccessControlList": {"shape": "S3CannedAccessControlList", "documentation": "<p/>", "box": true}}, "documentation": "<p/>"}, "S3AccessPointArn": {"type": "string", "max": 128, "min": 4}, "S3BucketArnString": {"type": "string", "max": 128, "min": 1, "pattern": "arn:[^:]+:s3:.*"}, "S3BucketDestination": {"type": "structure", "required": ["Format", "OutputSchemaVersion", "AccountId", "<PERSON><PERSON>"], "members": {"Format": {"shape": "Format", "documentation": "<p/>"}, "OutputSchemaVersion": {"shape": "OutputSchemaVersion", "documentation": "<p>The schema version of the export file.</p>"}, "AccountId": {"shape": "AccountId", "documentation": "<p>The account ID of the owner of the S3 Storage Lens metrics export bucket.</p>"}, "Arn": {"shape": "S3BucketArnString", "documentation": "<p>The Amazon Resource Name (ARN) of the bucket. This property is read-only and follows the following format: <code> arn:aws:s3:<i>us-east-1</i>:<i>example-account-id</i>:bucket/<i>your-destination-bucket-name</i> </code> </p>"}, "Prefix": {"shape": "Prefix", "documentation": "<p>The prefix of the destination bucket where the metrics export will be delivered.</p>"}, "Encryption": {"shape": "StorageLensDataExportEncryption", "documentation": "<p>The container for the type encryption of the metrics exports in this bucket.</p>"}}, "documentation": "<p>A container for the bucket where the Amazon S3 Storage Lens metrics export files are located.</p>"}, "S3CannedAccessControlList": {"type": "string", "enum": ["private", "public-read", "public-read-write", "aws-exec-read", "authenticated-read", "bucket-owner-read", "bucket-owner-full-control"]}, "S3ChecksumAlgorithm": {"type": "string", "enum": ["CRC32", "CRC32C", "SHA1", "SHA256"]}, "S3ContentLength": {"type": "long", "min": 0}, "S3CopyObjectOperation": {"type": "structure", "members": {"TargetResource": {"shape": "S3BucketArnString", "documentation": "<p>Specifies the destination bucket Amazon Resource Name (ARN) for the batch copy operation. For example, to copy objects to a bucket named <code>destinationBucket</code>, set the <code>TargetResource</code> property to <code>arn:aws:s3:::destinationBucket</code>.</p>"}, "CannedAccessControlList": {"shape": "S3CannedAccessControlList", "documentation": "<p/>", "box": true}, "AccessControlGrants": {"shape": "S3GrantList", "documentation": "<p/>", "box": true}, "MetadataDirective": {"shape": "S3MetadataDirective", "documentation": "<p/>"}, "ModifiedSinceConstraint": {"shape": "TimeStamp", "documentation": "<p/>"}, "NewObjectMetadata": {"shape": "S3ObjectMetadata", "documentation": "<p>If you don't provide this parameter, Amazon S3 copies all the metadata from the original objects. If you specify an empty set, the new objects will have no tags. Otherwise, Amazon S3 assigns the supplied tags to the new objects.</p>"}, "NewObjectTagging": {"shape": "S3TagSet", "documentation": "<p/>"}, "RedirectLocation": {"shape": "NonEmptyMaxLength2048String", "documentation": "<p>Specifies an optional metadata property for website redirects, <code>x-amz-website-redirect-location</code>. Allows webpage redirects if the object is accessed through a website endpoint.</p>"}, "RequesterPays": {"shape": "Boolean", "documentation": "<p/>"}, "StorageClass": {"shape": "S3StorageClass", "documentation": "<p/>"}, "UnModifiedSinceConstraint": {"shape": "TimeStamp", "documentation": "<p/>"}, "SSEAwsKmsKeyId": {"shape": "KmsKeyArnString", "documentation": "<p/>"}, "TargetKeyPrefix": {"shape": "NonEmptyMaxLength1024String", "documentation": "<p>Specifies the folder prefix that you want the objects to be copied into. For example, to copy objects into a folder named <code>Folder1</code> in the destination bucket, set the <code>TargetKeyPrefix</code> property to <code>Folder1</code>.</p>"}, "ObjectLockLegalHoldStatus": {"shape": "S3ObjectLockLegalHoldStatus", "documentation": "<p>The legal hold status to be applied to all objects in the Batch Operations job.</p>"}, "ObjectLockMode": {"shape": "S3ObjectLockMode", "documentation": "<p>The retention mode to be applied to all objects in the Batch Operations job.</p>"}, "ObjectLockRetainUntilDate": {"shape": "TimeStamp", "documentation": "<p>The date when the applied object retention configuration expires on all objects in the Batch Operations job.</p>"}, "BucketKeyEnabled": {"shape": "Boolean", "documentation": "<p>Specifies whether Amazon S3 should use an S3 Bucket Key for object encryption with server-side encryption using Amazon Web Services KMS (SSE-KMS). Setting this header to <code>true</code> causes Amazon S3 to use an S3 Bucket Key for object encryption with SSE-KMS.</p> <p>Specifying this header with an <i>object</i> action doesn’t affect <i>bucket-level</i> settings for S3 Bucket Key.</p>"}, "ChecksumAlgorithm": {"shape": "S3ChecksumAlgorithm", "documentation": "<p>Indicates the algorithm that you want Amazon S3 to use to create the checksum. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/CheckingObjectIntegrity.xml\"> Checking object integrity</a> in the <i>Amazon S3 User Guide</i>.</p>"}}, "documentation": "<p>Contains the configuration parameters for a PUT Copy object operation. S3 Batch Operations passes every object to the underlying <code>CopyObject</code> API operation. For more information about the parameters for this operation, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/RESTObjectCOPY.html\">CopyObject</a>.</p>"}, "S3DeleteObjectTaggingOperation": {"type": "structure", "members": {}, "documentation": "<p>Contains no configuration parameters because the DELETE Object tagging (<code>DeleteObjectTagging</code>) API operation accepts only the bucket name and key name as parameters, which are defined in the job's manifest.</p>"}, "S3ExpirationInDays": {"type": "integer", "min": 0}, "S3GeneratedManifestDescriptor": {"type": "structure", "members": {"Format": {"shape": "GeneratedManifestFormat", "documentation": "<p>The format of the generated manifest.</p>"}, "Location": {"shape": "JobManifestLocation"}}, "documentation": "<p>Describes the specified job's generated manifest. Batch Operations jobs created with a ManifestGenerator populate details of this descriptor after execution of the ManifestGenerator.</p>"}, "S3GlacierJobTier": {"type": "string", "enum": ["BULK", "STANDARD"]}, "S3Grant": {"type": "structure", "members": {"Grantee": {"shape": "S3Grantee", "documentation": "<p/>"}, "Permission": {"shape": "S3Permission", "documentation": "<p/>"}}, "documentation": "<p/>"}, "S3GrantList": {"type": "list", "member": {"shape": "S3Grant"}}, "S3Grantee": {"type": "structure", "members": {"TypeIdentifier": {"shape": "S3GranteeTypeIdentifier", "documentation": "<p/>"}, "Identifier": {"shape": "NonEmptyMaxLength1024String", "documentation": "<p/>", "box": true}, "DisplayName": {"shape": "NonEmptyMaxLength1024String", "documentation": "<p/>"}}, "documentation": "<p/>"}, "S3GranteeTypeIdentifier": {"type": "string", "enum": ["id", "emailAddress", "uri"]}, "S3InitiateRestoreObjectOperation": {"type": "structure", "members": {"ExpirationInDays": {"shape": "S3ExpirationInDays", "documentation": "<p>This argument specifies how long the S3 Glacier or S3 Glacier Deep Archive object remains available in Amazon S3. S3 Initiate Restore Object jobs that target S3 Glacier and S3 Glacier Deep Archive objects require <code>ExpirationInDays</code> set to 1 or greater.</p> <p>Conversely, do <i>not</i> set <code>ExpirationInDays</code> when creating S3 Initiate Restore Object jobs that target S3 Intelligent-Tiering Archive Access and Deep Archive Access tier objects. Objects in S3 Intelligent-Tiering archive access tiers are not subject to restore expiry, so specifying <code>ExpirationInDays</code> results in restore request failure.</p> <p>S3 Batch Operations jobs can operate either on S3 Glacier and S3 Glacier Deep Archive storage class objects or on S3 Intelligent-Tiering Archive Access and Deep Archive Access storage tier objects, but not both types in the same job. If you need to restore objects of both types you <i>must</i> create separate Batch Operations jobs. </p>", "box": true}, "GlacierJobTier": {"shape": "S3GlacierJobTier", "documentation": "<p>S3 Batch Operations supports <code>STANDARD</code> and <code>BULK</code> retrieval tiers, but not the <code>EXPEDITED</code> retrieval tier.</p>"}}, "documentation": "<p>Contains the configuration parameters for a POST Object restore job. S3 Batch Operations passes every object to the underlying <code>RestoreObject</code> API operation. For more information about the parameters for this operation, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/RESTObjectPOSTrestore.html#RESTObjectPOSTrestore-restore-request\">RestoreObject</a>.</p>"}, "S3JobManifestGenerator": {"type": "structure", "required": ["SourceBucket", "EnableManifestOutput"], "members": {"ExpectedBucketOwner": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID that owns the bucket the generated manifest is written to. If provided the generated manifest bucket's owner Amazon Web Services account ID must match this value, else the job fails.</p>"}, "SourceBucket": {"shape": "S3BucketArnString", "documentation": "<p>The source bucket used by the ManifestGenerator.</p>"}, "ManifestOutputLocation": {"shape": "S3ManifestOutputLocation", "documentation": "<p>Specifies the location the generated manifest will be written to.</p>"}, "Filter": {"shape": "JobManifestGeneratorFilter", "documentation": "<p>Specifies rules the S3JobManifestGenerator should use to use to decide whether an object in the source bucket should or should not be included in the generated job manifest.</p>"}, "EnableManifestOutput": {"shape": "Boolean", "documentation": "<p>Determines whether or not to write the job's generated manifest to a bucket.</p>"}}, "documentation": "<p>The container for the service that will create the S3 manifest.</p>"}, "S3KeyArnString": {"type": "string", "max": 2000, "min": 1, "pattern": "arn:[^:]+:s3:.*"}, "S3ManifestOutputLocation": {"type": "structure", "required": ["Bucket", "ManifestFormat"], "members": {"ExpectedManifestBucketOwner": {"shape": "AccountId", "documentation": "<p>The Account ID that owns the bucket the generated manifest is written to.</p>"}, "Bucket": {"shape": "S3BucketArnString", "documentation": "<p>The bucket ARN the generated manifest should be written to.</p>"}, "ManifestPrefix": {"shape": "ManifestPrefixString", "documentation": "<p>Prefix identifying one or more objects to which the manifest applies.</p>"}, "ManifestEncryption": {"shape": "GeneratedManifestEncryption", "documentation": "<p>Specifies what encryption should be used when the generated manifest objects are written.</p>"}, "ManifestFormat": {"shape": "GeneratedManifestFormat", "documentation": "<p>The format of the generated manifest.</p>"}}, "documentation": "<p>Location details for where the generated manifest should be written.</p>"}, "S3MetadataDirective": {"type": "string", "enum": ["COPY", "REPLACE"]}, "S3ObjectLockLegalHold": {"type": "structure", "required": ["Status"], "members": {"Status": {"shape": "S3ObjectLockLegalHoldStatus", "documentation": "<p>The Object Lock legal hold status to be applied to all objects in the Batch Operations job.</p>"}}, "documentation": "<p>Whether S3 Object Lock legal hold will be applied to objects in an S3 Batch Operations job.</p>"}, "S3ObjectLockLegalHoldStatus": {"type": "string", "enum": ["OFF", "ON"]}, "S3ObjectLockMode": {"type": "string", "enum": ["COMPLIANCE", "GOVERNANCE"]}, "S3ObjectLockRetentionMode": {"type": "string", "enum": ["COMPLIANCE", "GOVERNANCE"]}, "S3ObjectMetadata": {"type": "structure", "members": {"CacheControl": {"shape": "NonEmptyMaxLength1024String", "documentation": "<p/>"}, "ContentDisposition": {"shape": "NonEmptyMaxLength1024String", "documentation": "<p/>"}, "ContentEncoding": {"shape": "NonEmptyMaxLength1024String", "documentation": "<p/>"}, "ContentLanguage": {"shape": "NonEmptyMaxLength1024String", "documentation": "<p/>"}, "UserMetadata": {"shape": "S3UserMetadata", "documentation": "<p/>"}, "ContentLength": {"shape": "S3ContentLength", "documentation": "<p/>", "box": true}, "ContentMD5": {"shape": "NonEmptyMaxLength1024String", "documentation": "<p/>"}, "ContentType": {"shape": "NonEmptyMaxLength1024String", "documentation": "<p/>"}, "HttpExpiresDate": {"shape": "TimeStamp", "documentation": "<p/>"}, "RequesterCharged": {"shape": "Boolean", "documentation": "<p/>"}, "SSEAlgorithm": {"shape": "S3SSEAlgorithm", "documentation": "<p/>"}}, "documentation": "<p/>"}, "S3ObjectOwner": {"type": "structure", "members": {"ID": {"shape": "NonEmptyMaxLength1024String", "documentation": "<p/>"}, "DisplayName": {"shape": "NonEmptyMaxLength1024String", "documentation": "<p/>"}}, "documentation": "<p/>"}, "S3ObjectVersionId": {"type": "string", "max": 2000, "min": 1}, "S3Permission": {"type": "string", "enum": ["FULL_CONTROL", "READ", "WRITE", "READ_ACP", "WRITE_ACP"]}, "S3RegionalBucketArn": {"type": "string", "max": 128, "min": 4}, "S3ReplicateObjectOperation": {"type": "structure", "members": {}, "documentation": "<p>Directs the specified job to invoke <code>ReplicateObject</code> on every object in the job's manifest.</p>"}, "S3Retention": {"type": "structure", "members": {"RetainUntilDate": {"shape": "TimeStamp", "documentation": "<p>The date when the applied Object Lock retention will expire on all objects set by the Batch Operations job.</p>"}, "Mode": {"shape": "S3ObjectLockRetentionMode", "documentation": "<p>The Object Lock retention mode to be applied to all objects in the Batch Operations job.</p>"}}, "documentation": "<p>Contains the S3 Object Lock retention mode to be applied to all objects in the S3 Batch Operations job. If you don't provide <code>Mode</code> and <code>RetainUntilDate</code> data types in your operation, you will remove the retention from your objects. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/batch-ops-retention-date.html\">Using S3 Object Lock retention with S3 Batch Operations</a> in the <i>Amazon S3 User Guide</i>.</p>"}, "S3SSEAlgorithm": {"type": "string", "enum": ["AES256", "KMS"]}, "S3SetObjectAclOperation": {"type": "structure", "members": {"AccessControlPolicy": {"shape": "S3AccessControlPolicy", "documentation": "<p/>"}}, "documentation": "<p>Contains the configuration parameters for a PUT Object ACL operation. S3 Batch Operations passes every object to the underlying <code>PutObjectAcl</code> API operation. For more information about the parameters for this operation, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/RESTObjectPUTacl.html\">PutObjectAcl</a>.</p>"}, "S3SetObjectLegalHoldOperation": {"type": "structure", "required": ["LegalHold"], "members": {"LegalHold": {"shape": "S3ObjectLockLegalHold", "documentation": "<p>Contains the Object Lock legal hold status to be applied to all objects in the Batch Operations job.</p>"}}, "documentation": "<p>Contains the configuration for an S3 Object Lock legal hold operation that an S3 Batch Operations job passes to every object to the underlying <code>PutObjectLegalHold</code> API operation. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/batch-ops-legal-hold.html\">Using S3 Object Lock legal hold with S3 Batch Operations</a> in the <i>Amazon S3 User Guide</i>.</p>"}, "S3SetObjectRetentionOperation": {"type": "structure", "required": ["Retention"], "members": {"BypassGovernanceRetention": {"shape": "Boolean", "documentation": "<p>Indicates if the action should be applied to objects in the Batch Operations job even if they have Object Lock <code> GOVERNANCE</code> type in place.</p>", "box": true}, "Retention": {"shape": "S3Retention", "documentation": "<p>Contains the Object Lock retention mode to be applied to all objects in the Batch Operations job. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/batch-ops-retention-date.html\">Using S3 Object Lock retention with S3 Batch Operations</a> in the <i>Amazon S3 User Guide</i>.</p>"}}, "documentation": "<p>Contains the configuration parameters for the Object Lock retention action for an S3 Batch Operations job. Batch Operations passes every object to the underlying <code>PutObjectRetention</code> API operation. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/batch-ops-retention-date.html\">Using S3 Object Lock retention with S3 Batch Operations</a> in the <i>Amazon S3 User Guide</i>.</p>"}, "S3SetObjectTaggingOperation": {"type": "structure", "members": {"TagSet": {"shape": "S3TagSet", "documentation": "<p/>"}}, "documentation": "<p>Contains the configuration parameters for a PUT Object Tagging operation. S3 Batch Operations passes every object to the underlying <code>PutObjectTagging</code> API operation. For more information about the parameters for this operation, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/RESTObjectPUTtagging.html\">PutObjectTagging</a>.</p>"}, "S3StorageClass": {"type": "string", "enum": ["STANDARD", "STANDARD_IA", "ONEZONE_IA", "GLACIER", "INTELLIGENT_TIERING", "DEEP_ARCHIVE", "GLACIER_IR"]}, "S3Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKeyString", "documentation": "<p>Key of the tag</p>"}, "Value": {"shape": "TagValueString", "documentation": "<p>Value of the tag</p>"}}, "documentation": "<p>A container for a key-value name pair.</p>"}, "S3TagSet": {"type": "list", "member": {"shape": "S3Tag"}}, "S3UserMetadata": {"type": "map", "key": {"shape": "NonEmptyMaxLength1024String"}, "value": {"shape": "MaxLength1024String"}, "max": 8192}, "SSEKMS": {"type": "structure", "required": ["KeyId"], "members": {"KeyId": {"shape": "SSEKMSKeyId", "documentation": "<p>A container for the ARN of the SSE-KMS encryption. This property is read-only and follows the following format: <code> arn:aws:kms:<i>us-east-1</i>:<i>example-account-id</i>:key/<i>example-9a73-4afc-8d29-8f5900cef44e</i> </code> </p>"}}, "documentation": "<p/>", "locationName": "SSE-KMS"}, "SSEKMSEncryption": {"type": "structure", "required": ["KeyId"], "members": {"KeyId": {"shape": "KmsKeyArnString", "documentation": "<p>Specifies the ID of the Amazon Web Services Key Management Service (Amazon Web Services KMS) symmetric encryption customer managed key to use for encrypting generated manifest objects.</p>"}}, "documentation": "<p>Configuration for the use of SSE-KMS to encrypt generated manifest objects.</p>", "locationName": "SSE-KMS"}, "SSEKMSKeyId": {"type": "string"}, "SSES3": {"type": "structure", "members": {}, "documentation": "<p/>", "locationName": "SSE-S3"}, "SSES3Encryption": {"type": "structure", "members": {}, "documentation": "<p>Configuration for the use of SSE-S3 to encrypt generated manifest objects.</p>", "locationName": "SSE-S3"}, "SelectionCriteria": {"type": "structure", "members": {"Delimiter": {"shape": "StorageLensPrefixLevelDelimiter", "documentation": "<p>A container for the delimiter of the selection criteria being used.</p>"}, "MaxDepth": {"shape": "StorageLensPrefixLevelMaxDepth", "documentation": "<p>The max depth of the selection criteria</p>"}, "MinStorageBytesPercentage": {"shape": "MinStorageBytesPercentage", "documentation": "<p>The minimum number of storage bytes percentage whose metrics will be selected.</p> <note> <p>You must choose a value greater than or equal to <code>1.0</code>.</p> </note>"}}, "documentation": "<p/>"}, "Setting": {"type": "boolean"}, "SourceSelectionCriteria": {"type": "structure", "members": {"SseKmsEncryptedObjects": {"shape": "SseKmsEncryptedObjects", "documentation": "<p>A filter that you can use to select Amazon S3 objects that are encrypted with server-side encryption by using Key Management Service (KMS) keys. If you include <code>SourceSelectionCriteria</code> in the replication configuration, this element is required. </p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>"}, "ReplicaModifications": {"shape": "ReplicaModifications", "documentation": "<p>A filter that you can use to specify whether replica modification sync is enabled. S3 on Outposts replica modification sync can help you keep object metadata synchronized between replicas and source objects. By default, S3 on Outposts replicates metadata from the source objects to the replicas only. When replica modification sync is enabled, S3 on Outposts replicates metadata changes made to the replica copies back to the source object, making the replication bidirectional.</p> <p>To replicate object metadata modifications on replicas, you can specify this element and set the <code>Status</code> of this element to <code>Enabled</code>.</p> <note> <p>You must enable replica modification sync on the source and destination buckets to replicate replica metadata changes between the source and the replicas.</p> </note>"}}, "documentation": "<p>A container that describes additional filters for identifying the source objects that you want to replicate. You can choose to enable or disable the replication of these objects.</p>"}, "SseKmsEncryptedObjects": {"type": "structure", "required": ["Status"], "members": {"Status": {"shape": "SseKmsEncryptedObjectsStatus", "documentation": "<p>Specifies whether Amazon S3 replicates objects that are created with server-side encryption by using an KMS key stored in Key Management Service.</p>"}}, "documentation": "<p>A container for filter information that you can use to select S3 objects that are encrypted with Key Management Service (KMS).</p> <note> <p>This is not supported by Amazon S3 on Outposts buckets.</p> </note>"}, "SseKmsEncryptedObjectsStatus": {"type": "string", "enum": ["Enabled", "Disabled"]}, "StorageLensArn": {"type": "string", "max": 1024, "min": 1, "pattern": "arn:[a-z\\-]+:s3:[a-z0-9\\-]+:\\d{12}:storage\\-lens\\/.*"}, "StorageLensAwsOrg": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"Arn": {"shape": "AwsOrgArn", "documentation": "<p>A container for the Amazon Resource Name (ARN) of the Amazon Web Services organization. This property is read-only and follows the following format: <code> arn:aws:organizations:<i>us-east-1</i>:<i>example-account-id</i>:organization/<i>o-ex2l495dck</i> </code> </p>"}}, "documentation": "<p>The Amazon Web Services organization for your S3 Storage Lens.</p>"}, "StorageLensConfiguration": {"type": "structure", "required": ["Id", "AccountLevel", "IsEnabled"], "members": {"Id": {"shape": "ConfigId", "documentation": "<p>A container for the Amazon S3 Storage Lens configuration ID.</p>"}, "AccountLevel": {"shape": "AccountLevel", "documentation": "<p>A container for all the account-level configurations of your S3 Storage Lens configuration.</p>"}, "Include": {"shape": "Include", "documentation": "<p>A container for what is included in this configuration. This container can only be valid if there is no <code>Exclude</code> container submitted, and it's not empty. </p>"}, "Exclude": {"shape": "Exclude", "documentation": "<p>A container for what is excluded in this configuration. This container can only be valid if there is no <code>Include</code> container submitted, and it's not empty. </p>"}, "DataExport": {"shape": "StorageLensDataExport", "documentation": "<p>A container to specify the properties of your S3 Storage Lens metrics export including, the destination, schema and format.</p>"}, "IsEnabled": {"shape": "IsEnabled", "documentation": "<p>A container for whether the S3 Storage Lens configuration is enabled.</p>"}, "AwsOrg": {"shape": "StorageLensAwsOrg", "documentation": "<p>A container for the Amazon Web Services organization for this S3 Storage Lens configuration.</p>"}, "StorageLensArn": {"shape": "StorageLensArn", "documentation": "<p>The Amazon Resource Name (ARN) of the S3 Storage Lens configuration. This property is read-only and follows the following format: <code> arn:aws:s3:<i>us-east-1</i>:<i>example-account-id</i>:storage-lens/<i>your-dashboard-name</i> </code> </p>"}}, "documentation": "<p>A container for the Amazon S3 Storage Lens configuration.</p>"}, "StorageLensConfigurationList": {"type": "list", "member": {"shape": "ListStorageLensConfigurationEntry", "locationName": "StorageLensConfiguration"}, "flattened": true}, "StorageLensDataExport": {"type": "structure", "members": {"S3BucketDestination": {"shape": "S3BucketDestination", "documentation": "<p>A container for the bucket where the S3 Storage Lens metrics export will be located.</p> <note> <p>This bucket must be located in the same Region as the storage lens configuration. </p> </note>"}, "CloudWatchMetrics": {"shape": "CloudWatchMetrics", "documentation": "<p>A container for enabling Amazon CloudWatch publishing for S3 Storage Lens metrics.</p>"}}, "documentation": "<p>A container to specify the properties of your S3 Storage Lens metrics export, including the destination, schema, and format.</p>"}, "StorageLensDataExportEncryption": {"type": "structure", "members": {"SSES3": {"shape": "SSES3", "documentation": "<p/>", "locationName": "SSE-S3"}, "SSEKMS": {"shape": "SSEKMS", "documentation": "<p/>", "locationName": "SSE-KMS"}}, "documentation": "<p>A container for the encryption of the S3 Storage Lens metrics exports.</p>"}, "StorageLensPrefixLevelDelimiter": {"type": "string", "max": 1}, "StorageLensPrefixLevelMaxDepth": {"type": "integer", "max": 10, "min": 1}, "StorageLensTag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKeyString", "documentation": "<p/>"}, "Value": {"shape": "TagValueString", "documentation": "<p/>"}}, "documentation": "<p/>"}, "StorageLensTags": {"type": "list", "member": {"shape": "StorageLensTag", "locationName": "Tag"}}, "StringForNextToken": {"type": "string", "max": 1024, "min": 1, "pattern": "^[A-Za-z0-9\\+\\:\\/\\=\\?\\#-_]+$"}, "SubmitMultiRegionAccessPointRoutesRequest": {"type": "structure", "required": ["AccountId", "Mrap", "RouteUpdates"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID for the owner of the Multi-Region Access Point.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "Mrap": {"shape": "MultiRegionAccessPointId", "documentation": "<p>The Multi-Region Access Point ARN.</p>", "location": "uri", "locationName": "mrap"}, "RouteUpdates": {"shape": "RouteList", "documentation": "<p>The different routes that make up the new route configuration. Active routes return a value of <code>100</code>, and passive routes return a value of <code>0</code>.</p>"}}}, "SubmitMultiRegionAccessPointRoutesResult": {"type": "structure", "members": {}}, "SuspendedCause": {"type": "string", "max": 1024, "min": 1}, "SuspendedDate": {"type": "timestamp"}, "TagKeyString": {"type": "string", "max": 1024, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "TagValueString": {"type": "string", "max": 1024, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "Tagging": {"type": "structure", "required": ["TagSet"], "members": {"TagSet": {"shape": "S3TagSet", "documentation": "<p>A collection for a set of tags.</p>"}}, "documentation": "<p/>"}, "TimeStamp": {"type": "timestamp"}, "TooManyRequestsException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p/>", "exception": true}, "TooManyTagsException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>Amazon S3 throws this exception if you have too many tags in your tag set.</p>", "exception": true}, "TrafficDialPercentage": {"type": "integer", "max": 100, "min": 0}, "Transition": {"type": "structure", "members": {"Date": {"shape": "Date", "documentation": "<p>Indicates when objects are transitioned to the specified storage class. The date value must be in ISO 8601 format. The time is always midnight UTC.</p>"}, "Days": {"shape": "Days", "documentation": "<p>Indicates the number of days after creation when objects are transitioned to the specified storage class. The value must be a positive integer.</p>"}, "StorageClass": {"shape": "TransitionStorageClass", "documentation": "<p>The storage class to which you want the object to transition.</p>"}}, "documentation": "<p>Specifies when an object transitions to a specified storage class. For more information about Amazon S3 Lifecycle configuration rules, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/lifecycle-transition-general-considerations.html\"> Transitioning objects using Amazon S3 Lifecycle</a> in the <i>Amazon S3 User Guide</i>.</p>"}, "TransitionList": {"type": "list", "member": {"shape": "Transition", "locationName": "Transition"}}, "TransitionStorageClass": {"type": "string", "enum": ["GLACIER", "STANDARD_IA", "ONEZONE_IA", "INTELLIGENT_TIERING", "DEEP_ARCHIVE"]}, "UpdateJobPriorityRequest": {"type": "structure", "required": ["AccountId", "JobId", "Priority"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID associated with the S3 Batch Operations job.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "JobId": {"shape": "JobId", "documentation": "<p>The ID for the job whose priority you want to update.</p>", "location": "uri", "locationName": "id"}, "Priority": {"shape": "JobPriority", "documentation": "<p>The priority you want to assign to this job.</p>", "location": "querystring", "locationName": "priority"}}}, "UpdateJobPriorityResult": {"type": "structure", "required": ["JobId", "Priority"], "members": {"JobId": {"shape": "JobId", "documentation": "<p>The ID for the job whose priority Amazon S3 updated.</p>"}, "Priority": {"shape": "JobPriority", "documentation": "<p>The new priority assigned to the specified job.</p>"}}}, "UpdateJobStatusRequest": {"type": "structure", "required": ["AccountId", "JobId", "RequestedJobStatus"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID associated with the S3 Batch Operations job.</p>", "contextParam": {"name": "AccountId"}, "hostLabel": true, "location": "header", "locationName": "x-amz-account-id"}, "JobId": {"shape": "JobId", "documentation": "<p>The ID of the job whose status you want to update.</p>", "location": "uri", "locationName": "id"}, "RequestedJobStatus": {"shape": "RequestedJobStatus", "documentation": "<p>The status that you want to move the specified job to.</p>", "location": "querystring", "locationName": "requestedJobStatus"}, "StatusUpdateReason": {"shape": "JobStatusUpdateReason", "documentation": "<p>A description of the reason why you want to change the specified job's status. This field can be any string up to the maximum length.</p>", "location": "querystring", "locationName": "statusUpdateReason"}}}, "UpdateJobStatusResult": {"type": "structure", "members": {"JobId": {"shape": "JobId", "documentation": "<p>The ID for the job whose status was updated.</p>"}, "Status": {"shape": "JobStatus", "documentation": "<p>The current status for the specified job.</p>"}, "StatusUpdateReason": {"shape": "JobStatusUpdateReason", "documentation": "<p>The reason that the specified job's status was updated.</p>"}}}, "VersioningConfiguration": {"type": "structure", "members": {"MFADelete": {"shape": "MFADelete", "documentation": "<p>Specifies whether MFA delete is enabled or disabled in the bucket versioning configuration for the S3 on Outposts bucket.</p>", "locationName": "MfaDelete"}, "Status": {"shape": "BucketVersioningStatus", "documentation": "<p>Sets the versioning state of the S3 on Outposts bucket.</p>"}}, "documentation": "<p>Describes the versioning state of an Amazon S3 on Outposts bucket. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_PutBucketVersioning.html\">PutBucketVersioning</a>.</p>"}, "VpcConfiguration": {"type": "structure", "required": ["VpcId"], "members": {"VpcId": {"shape": "VpcId", "documentation": "<p>If this field is specified, this access point will only allow connections from the specified VPC ID.</p>"}}, "documentation": "<p>The virtual private cloud (VPC) configuration for an access point.</p>"}, "VpcId": {"type": "string", "max": 1024, "min": 1}}, "documentation": "<p> Amazon Web Services S3 Control provides access to Amazon S3 control plane actions. </p>", "clientContextParams": {"UseArnRegion": {"documentation": "Enables this client to use an ARN's region when constructing an endpoint instead of the client's configured region.", "type": "boolean"}}}