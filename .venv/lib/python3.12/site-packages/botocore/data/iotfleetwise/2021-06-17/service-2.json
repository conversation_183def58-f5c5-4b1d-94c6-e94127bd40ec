{"version": "2.0", "metadata": {"apiVersion": "2021-06-17", "endpointPrefix": "iotfleetwise", "jsonVersion": "1.0", "protocol": "json", "serviceFullName": "AWS IoT FleetWise", "serviceId": "IoTFleetWise", "signatureVersion": "v4", "signingName": "iotfleetwise", "targetPrefix": "IoTAutobahnControlPlane", "uid": "iotfleetwise-2021-06-17"}, "operations": {"AssociateVehicleFleet": {"name": "AssociateVehicle<PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AssociateVehicleFleetRequest"}, "output": {"shape": "AssociateVehicleFleetResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Adds, or associates, a vehicle with a fleet. </p>"}, "BatchCreateVehicle": {"name": "BatchCreateVehicle", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "BatchCreateVehicleRequest"}, "output": {"shape": "BatchCreateVehicleResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "LimitExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Creates a group, or batch, of vehicles. </p> <note> <p> You must specify a decoder manifest and a vehicle model (model manifest) for each vehicle. </p> </note> <p>For more information, see <a href=\"https://docs.aws.amazon.com/iot-fleetwise/latest/developerguide/create-vehicles-cli.html\">Create multiple vehicles (AWS CLI)</a> in the <i>Amazon Web Services IoT FleetWise Developer Guide</i>. </p>"}, "BatchUpdateVehicle": {"name": "BatchUpdateVehicle", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "BatchUpdateVehicleRequest"}, "output": {"shape": "BatchUpdateVehicleResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Updates a group, or batch, of vehicles.</p> <note> <p> You must specify a decoder manifest and a vehicle model (model manifest) for each vehicle. </p> </note> <p>For more information, see <a href=\"https://docs.aws.amazon.com/iot-fleetwise/latest/developerguide/update-vehicles-cli.html\">Update multiple vehicles (AWS CLI)</a> in the <i>Amazon Web Services IoT FleetWise Developer Guide</i>. </p>"}, "CreateCampaign": {"name": "CreateCampaign", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateCampaignRequest"}, "output": {"shape": "CreateCampaignResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "LimitExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates an orchestration of data collection rules. The Amazon Web Services IoT FleetWise Edge Agent software running in vehicles uses campaigns to decide how to collect and transfer data to the cloud. You create campaigns in the cloud. After you or your team approve campaigns, Amazon Web Services IoT FleetWise automatically deploys them to vehicles. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/iot-fleetwise/latest/developerguide/campaigns.html\">Collect and transfer data with campaigns</a> in the <i>Amazon Web Services IoT FleetWise Developer Guide</i>.</p>", "idempotent": true}, "CreateDecoderManifest": {"name": "CreateDecoderManifest", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateDecoderManifestRequest"}, "output": {"shape": "CreateDecoderManifestResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "LimitExceededException"}, {"shape": "DecoderManifestValidationException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates the decoder manifest associated with a model manifest. To create a decoder manifest, the following must be true:</p> <ul> <li> <p>Every signal decoder has a unique name.</p> </li> <li> <p>Each signal decoder is associated with a network interface.</p> </li> <li> <p>Each network interface has a unique ID.</p> </li> <li> <p>The signal decoders are specified in the model manifest.</p> </li> </ul>", "idempotent": true}, "CreateFleet": {"name": "CreateFleet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateFleetRequest"}, "output": {"shape": "CreateFleetResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "LimitExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Creates a fleet that represents a group of vehicles. </p> <note> <p>You must create both a signal catalog and vehicles before you can create a fleet. </p> </note> <p>For more information, see <a href=\"https://docs.aws.amazon.com/iot-fleetwise/latest/developerguide/fleets.html\">Fleets</a> in the <i>Amazon Web Services IoT FleetWise Developer Guide</i>.</p>", "idempotent": true}, "CreateModelManifest": {"name": "CreateModelManifest", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateModelManifestRequest"}, "output": {"shape": "CreateModelManifestResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "LimitExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "InvalidSignalsException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Creates a vehicle model (model manifest) that specifies signals (attributes, branches, sensors, and actuators). </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/iot-fleetwise/latest/developerguide/vehicle-models.html\">Vehicle models</a> in the <i>Amazon Web Services IoT FleetWise Developer Guide</i>.</p>", "idempotent": true}, "CreateSignalCatalog": {"name": "CreateSignalCatalog", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateSignalCatalogRequest"}, "output": {"shape": "CreateSignalCatalogResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "LimitExceededException"}, {"shape": "InvalidNodeException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "InvalidSignalsException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Creates a collection of standardized signals that can be reused to create vehicle models.</p>", "idempotent": true}, "CreateVehicle": {"name": "CreateVehicle", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateVehicleRequest"}, "output": {"shape": "CreateVehicleResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "LimitExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Creates a vehicle, which is an instance of a vehicle model (model manifest). Vehicles created from the same vehicle model consist of the same signals inherited from the vehicle model.</p> <note> <p> If you have an existing Amazon Web Services IoT thing, you can use Amazon Web Services IoT FleetWise to create a vehicle and collect data from your thing. </p> </note> <p>For more information, see <a href=\"https://docs.aws.amazon.com/iot-fleetwise/latest/developerguide/create-vehicle-cli.html\">Create a vehicle (AWS CLI)</a> in the <i>Amazon Web Services IoT FleetWise Developer Guide</i>.</p>", "idempotent": true}, "DeleteCampaign": {"name": "DeleteCampaign", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteCampaignRequest"}, "output": {"shape": "DeleteCampaignResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Deletes a data collection campaign. Deleting a campaign suspends all data collection and removes it from any vehicles. </p>", "idempotent": true}, "DeleteDecoderManifest": {"name": "DeleteDecoderManifest", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDecoderManifestRequest"}, "output": {"shape": "DeleteDecoderManifestResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Deletes a decoder manifest. You can't delete a decoder manifest if it has vehicles associated with it. </p> <note> <p>If the decoder manifest is successfully deleted, Amazon Web Services IoT FleetWise sends back an HTTP 200 response with an empty body.</p> </note>", "idempotent": true}, "DeleteFleet": {"name": "DeleteFleet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteFleetRequest"}, "output": {"shape": "DeleteFleetResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Deletes a fleet. Before you delete a fleet, all vehicles must be dissociated from the fleet. For more information, see <a href=\"https://docs.aws.amazon.com/iot-fleetwise/latest/developerguide/delete-fleet-cli.html\">Delete a fleet (AWS CLI)</a> in the <i>Amazon Web Services IoT FleetWise Developer Guide</i>.</p> <note> <p>If the fleet is successfully deleted, Amazon Web Services IoT FleetWise sends back an HTTP 200 response with an empty body.</p> </note>", "idempotent": true}, "DeleteModelManifest": {"name": "DeleteModelManifest", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteModelManifestRequest"}, "output": {"shape": "DeleteModelManifestResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Deletes a vehicle model (model manifest).</p> <note> <p>If the vehicle model is successfully deleted, Amazon Web Services IoT FleetWise sends back an HTTP 200 response with an empty body.</p> </note>", "idempotent": true}, "DeleteSignalCatalog": {"name": "DeleteSignalCatalog", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteSignalCatalogRequest"}, "output": {"shape": "DeleteSignalCatalogResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Deletes a signal catalog. </p> <note> <p>If the signal catalog is successfully deleted, Amazon Web Services IoT FleetWise sends back an HTTP 200 response with an empty body.</p> </note>", "idempotent": true}, "DeleteVehicle": {"name": "DeleteVehicle", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteVehicleRequest"}, "output": {"shape": "DeleteVehicleResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Deletes a vehicle and removes it from any campaigns.</p> <note> <p>If the vehicle is successfully deleted, Amazon Web Services IoT FleetWise sends back an HTTP 200 response with an empty body.</p> </note>", "idempotent": true}, "DisassociateVehicleFleet": {"name": "DisassociateVehicleFleet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DisassociateVehicleFleetRequest"}, "output": {"shape": "DisassociateVehicleFleetResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Removes, or disassociates, a vehicle from a fleet. Disassociating a vehicle from a fleet doesn't delete the vehicle.</p> <note> <p>If the vehicle is successfully dissociated from a fleet, Amazon Web Services IoT FleetWise sends back an HTTP 200 response with an empty body.</p> </note>"}, "GetCampaign": {"name": "GetCampaign", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetCampaignRequest"}, "output": {"shape": "GetCampaignResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Retrieves information about a campaign. </p>"}, "GetDecoderManifest": {"name": "GetDecoderManifest", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetDecoderManifestRequest"}, "output": {"shape": "GetDecoderManifestResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Retrieves information about a created decoder manifest. </p>"}, "GetEncryptionConfiguration": {"name": "GetEncryptionConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetEncryptionConfigurationRequest"}, "output": {"shape": "GetEncryptionConfigurationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves the encryption configuration for resources and data in Amazon Web Services IoT FleetWise.</p>"}, "GetFleet": {"name": "GetFleet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetFleetRequest"}, "output": {"shape": "GetFleetResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Retrieves information about a fleet. </p>"}, "GetLoggingOptions": {"name": "GetLoggingOptions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetLoggingOptionsRequest"}, "output": {"shape": "GetLoggingOptionsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves the logging options.</p>"}, "GetModelManifest": {"name": "GetModelManifest", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetModelManifestRequest"}, "output": {"shape": "GetModelManifestResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Retrieves information about a vehicle model (model manifest). </p>"}, "GetRegisterAccountStatus": {"name": "GetRegisterAccountStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetRegisterAccountStatusRequest"}, "output": {"shape": "GetRegisterAccountStatusResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Retrieves information about the status of registering your Amazon Web Services account, IAM, and Amazon Timestream resources so that Amazon Web Services IoT FleetWise can transfer your vehicle data to the Amazon Web Services Cloud. </p> <p>For more information, including step-by-step procedures, see <a href=\"https://docs.aws.amazon.com/iot-fleetwise/latest/developerguide/setting-up.html\">Setting up Amazon Web Services IoT FleetWise</a>. </p> <note> <p>This API operation doesn't require input parameters.</p> </note>"}, "GetSignalCatalog": {"name": "GetSignalCatalog", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetSignalCatalogRequest"}, "output": {"shape": "GetSignalCatalogResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Retrieves information about a signal catalog. </p>"}, "GetVehicle": {"name": "GetVehicle", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetVehicleRequest"}, "output": {"shape": "GetVehicleResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Retrieves information about a vehicle. </p>"}, "GetVehicleStatus": {"name": "GetVehicleStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetVehicleStatusRequest"}, "output": {"shape": "GetVehicleStatusResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Retrieves information about the status of a vehicle with any associated campaigns. </p>"}, "ImportDecoderManifest": {"name": "ImportDecoderManifest", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ImportDecoderManifestRequest"}, "output": {"shape": "ImportDecoderManifestResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "DecoderManifestValidationException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "InvalidSignalsException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Creates a decoder manifest using your existing CAN DBC file from your local device. </p>"}, "ImportSignalCatalog": {"name": "ImportSignalCatalog", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ImportSignalCatalogRequest"}, "output": {"shape": "ImportSignalCatalogResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "LimitExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "InvalidSignalsException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Creates a signal catalog using your existing VSS formatted content from your local device. </p>", "idempotent": true}, "ListCampaigns": {"name": "ListCampaigns", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListCampaignsRequest"}, "output": {"shape": "ListCampaignsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Lists information about created campaigns. </p> <note> <p>This API operation uses pagination. Specify the <code>nextToken</code> parameter in the request to return more results.</p> </note>"}, "ListDecoderManifestNetworkInterfaces": {"name": "ListDecoderManifestNetworkInterfaces", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListDecoderManifestNetworkInterfacesRequest"}, "output": {"shape": "ListDecoderManifestNetworkInterfacesResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Lists the network interfaces specified in a decoder manifest. </p> <note> <p>This API operation uses pagination. Specify the <code>nextToken</code> parameter in the request to return more results.</p> </note>"}, "ListDecoderManifestSignals": {"name": "ListDecoderManifestSignals", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListDecoderManifestSignalsRequest"}, "output": {"shape": "ListDecoderManifestSignalsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> A list of information about signal decoders specified in a decoder manifest. </p> <note> <p>This API operation uses pagination. Specify the <code>nextToken</code> parameter in the request to return more results.</p> </note>"}, "ListDecoderManifests": {"name": "ListDecoderManifests", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListDecoderManifestsRequest"}, "output": {"shape": "ListDecoderManifestsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Lists decoder manifests. </p> <note> <p>This API operation uses pagination. Specify the <code>nextToken</code> parameter in the request to return more results.</p> </note>"}, "ListFleets": {"name": "ListFleets", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListFleetsRequest"}, "output": {"shape": "ListFleetsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Retrieves information for each created fleet in an Amazon Web Services account. </p> <note> <p>This API operation uses pagination. Specify the <code>nextToken</code> parameter in the request to return more results.</p> </note>"}, "ListFleetsForVehicle": {"name": "ListFleetsForVehicle", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListFleetsForVehicleRequest"}, "output": {"shape": "ListFleetsForVehicleResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a list of IDs for all fleets that the vehicle is associated with.</p> <note> <p>This API operation uses pagination. Specify the <code>nextToken</code> parameter in the request to return more results.</p> </note>"}, "ListModelManifestNodes": {"name": "ListModelManifestNodes", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListModelManifestNodesRequest"}, "output": {"shape": "ListModelManifestNodesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Lists information about nodes specified in a vehicle model (model manifest). </p> <note> <p>This API operation uses pagination. Specify the <code>nextToken</code> parameter in the request to return more results.</p> </note>"}, "ListModelManifests": {"name": "ListModelManifests", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListModelManifestsRequest"}, "output": {"shape": "ListModelManifestsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Retrieves a list of vehicle models (model manifests). </p> <note> <p>This API operation uses pagination. Specify the <code>nextToken</code> parameter in the request to return more results.</p> </note>"}, "ListSignalCatalogNodes": {"name": "ListSignalCatalogNodes", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListSignalCatalogNodesRequest"}, "output": {"shape": "ListSignalCatalogNodesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Lists of information about the signals (nodes) specified in a signal catalog. </p> <note> <p>This API operation uses pagination. Specify the <code>nextToken</code> parameter in the request to return more results.</p> </note>"}, "ListSignalCatalogs": {"name": "ListSignalCatalogs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListSignalCatalogsRequest"}, "output": {"shape": "ListSignalCatalogsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Lists all the created signal catalogs in an Amazon Web Services account. </p> <p>You can use to list information about each signal (node) specified in a signal catalog.</p> <note> <p>This API operation uses pagination. Specify the <code>nextToken</code> parameter in the request to return more results.</p> </note>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the tags (metadata) you have assigned to the resource.</p>"}, "ListVehicles": {"name": "ListVehicles", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListVehiclesRequest"}, "output": {"shape": "ListVehiclesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Retrieves a list of summaries of created vehicles. </p> <note> <p>This API operation uses pagination. Specify the <code>nextToken</code> parameter in the request to return more results.</p> </note>"}, "ListVehiclesInFleet": {"name": "ListVehiclesInFleet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListVehiclesInFleetRequest"}, "output": {"shape": "ListVehiclesInFleetResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Retrieves a list of summaries of all vehicles associated with a fleet. </p> <note> <p>This API operation uses pagination. Specify the <code>nextToken</code> parameter in the request to return more results.</p> </note>"}, "PutEncryptionConfiguration": {"name": "PutEncryptionConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutEncryptionConfigurationRequest"}, "output": {"shape": "PutEncryptionConfigurationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates or updates the encryption configuration. Amazon Web Services IoT FleetWise can encrypt your data and resources using an Amazon Web Services managed key. Or, you can use a KMS key that you own and manage. For more information, see <a href=\"https://docs.aws.amazon.com/iot-fleetwise/latest/developerguide/data-encryption.html\">Data encryption</a> in the <i>Amazon Web Services IoT FleetWise Developer Guide</i>.</p>"}, "PutLoggingOptions": {"name": "PutLoggingOptions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutLoggingOptionsRequest"}, "output": {"shape": "PutLoggingOptionsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates or updates the logging option.</p>", "idempotent": true}, "RegisterAccount": {"name": "RegisterAccount", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RegisterAccountRequest"}, "output": {"shape": "RegisterAccountResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<important> <p>This API operation contains deprecated parameters. Register your account again without the Timestream resources parameter so that Amazon Web Services IoT FleetWise can remove the Timestream metadata stored. You should then pass the data destination into the <a href=\"https://docs.aws.amazon.com/iot-fleetwise/latest/APIReference/API_CreateCampaign.html\">CreateCampaign</a> API operation.</p> <p>You must delete any existing campaigns that include an empty data destination before you register your account again. For more information, see the <a href=\"https://docs.aws.amazon.com/iot-fleetwise/latest/APIReference/API_DeleteCampaign.html\">DeleteCampaign</a> API operation.</p> <p>If you want to delete the Timestream inline policy from the service-linked role, such as to mitigate an overly permissive policy, you must first delete any existing campaigns. Then delete the service-linked role and register your account again to enable CloudWatch metrics. For more information, see <a href=\"https://docs.aws.amazon.com/IAM/latest/APIReference/API_DeleteServiceLinkedRole.html\">DeleteServiceLinkedRole</a> in the <i>Identity and Access Management API Reference</i>.</p> </important> <pre><code> &lt;p&gt;Registers your Amazon Web Services account, IAM, and Amazon Timestream resources so Amazon Web Services IoT FleetWise can transfer your vehicle data to the Amazon Web Services Cloud. For more information, including step-by-step procedures, see &lt;a href=&quot;https://docs.aws.amazon.com/iot-fleetwise/latest/developerguide/setting-up.html&quot;&gt;Setting up Amazon Web Services IoT FleetWise&lt;/a&gt;. &lt;/p&gt; &lt;note&gt; &lt;p&gt;An Amazon Web Services account is &lt;b&gt;not&lt;/b&gt; the same thing as a &quot;user.&quot; An &lt;a href=&quot;https://docs.aws.amazon.com/IAM/latest/UserGuide/introduction_identity-management.html#intro-identity-users&quot;&gt;Amazon Web Services user&lt;/a&gt; is an identity that you create using Identity and Access Management (IAM) and takes the form of either an &lt;a href=&quot;https://docs.aws.amazon.com/IAM/latest/UserGuide/id_users.html&quot;&gt;IAM user&lt;/a&gt; or an &lt;a href=&quot;https://docs.aws.amazon.com/IAM/latest/UserGuide/id_roles.html&quot;&gt;IAM role, both with credentials&lt;/a&gt;. A single Amazon Web Services account can, and typically does, contain many users and roles.&lt;/p&gt; &lt;/note&gt; </code></pre>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Adds to or modifies the tags of the given resource. Tags are metadata which can be used to manage a resource.</p>", "idempotent": true}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Removes the given tags (metadata) from the resource.</p>", "idempotent": true}, "UpdateCampaign": {"name": "UpdateCampaign", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateCampaignRequest"}, "output": {"shape": "UpdateCampaignResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Updates a campaign. </p>"}, "UpdateDecoderManifest": {"name": "UpdateDecoderManifest", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateDecoderManifestRequest"}, "output": {"shape": "UpdateDecoderManifestResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "LimitExceededException"}, {"shape": "DecoderManifestValidationException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Updates a decoder manifest.</p> <p>A decoder manifest can only be updated when the status is <code>DRAFT</code>. Only <code>ACTIVE</code> decoder manifests can be associated with vehicles.</p>", "idempotent": true}, "UpdateFleet": {"name": "UpdateFleet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateFleetRequest"}, "output": {"shape": "UpdateFleetResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Updates the description of an existing fleet. </p> <note> <p>If the fleet is successfully updated, Amazon Web Services IoT FleetWise sends back an HTTP 200 response with an empty HTTP body.</p> </note>"}, "UpdateModelManifest": {"name": "UpdateModelManifest", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateModelManifestRequest"}, "output": {"shape": "UpdateModelManifestResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "InvalidSignalsException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Updates a vehicle model (model manifest). If created vehicles are associated with a vehicle model, it can't be updated.</p>", "idempotent": true}, "UpdateSignalCatalog": {"name": "UpdateSignalCatalog", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateSignalCatalogRequest"}, "output": {"shape": "UpdateSignalCatalogResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "LimitExceededException"}, {"shape": "InvalidNodeException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "InvalidSignalsException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Updates a signal catalog. </p>", "idempotent": true}, "UpdateVehicle": {"name": "UpdateVehicle", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateVehicleRequest"}, "output": {"shape": "UpdateVehicleResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Updates a vehicle. </p>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "string"}}, "documentation": "<p>You don't have sufficient permission to perform this action.</p>", "exception": true}, "Actuator": {"type": "structure", "required": ["fullyQualifiedName", "dataType"], "members": {"fullyQualifiedName": {"shape": "string", "documentation": "<p>The fully qualified name of the actuator. For example, the fully qualified name of an actuator might be <code>Vehicle.Front.Left.Door.Lock</code>.</p>"}, "dataType": {"shape": "NodeDataType", "documentation": "<p>The specified data type of the actuator. </p>"}, "description": {"shape": "description", "documentation": "<p>A brief description of the actuator.</p>"}, "unit": {"shape": "string", "documentation": "<p>The scientific unit for the actuator.</p>"}, "allowedValues": {"shape": "listOfStrings", "documentation": "<p>A list of possible values an actuator can take.</p>"}, "min": {"shape": "double", "documentation": "<p>The specified possible minimum value of an actuator.</p>"}, "max": {"shape": "double", "documentation": "<p>The specified possible maximum value of an actuator.</p>"}, "assignedValue": {"shape": "string", "documentation": "<p>A specified value for the actuator.</p>", "deprecated": true, "deprecatedMessage": "assignedValue is no longer in use"}, "deprecationMessage": {"shape": "message", "documentation": "<p>The deprecation message for the node or the branch that was moved or deleted.</p>"}, "comment": {"shape": "message", "documentation": "<p>A comment in addition to the description.</p>"}}, "documentation": "<p>A signal that represents a vehicle device such as the engine, heater, and door locks. Data from an actuator reports the state of a certain vehicle device.</p> <note> <p> Updating actuator data can change the state of a device. For example, you can turn on or off the heater by updating its actuator data.</p> </note>"}, "AmazonResourceName": {"type": "string", "max": 1011, "min": 1}, "AssociateVehicleFleetRequest": {"type": "structure", "required": ["vehicleName", "fleetId"], "members": {"vehicleName": {"shape": "vehicleName", "documentation": "<p> The unique ID of the vehicle to associate with the fleet. </p>"}, "fleetId": {"shape": "fleetId", "documentation": "<p> The ID of a fleet. </p>"}}}, "AssociateVehicleFleetResponse": {"type": "structure", "members": {}}, "Attribute": {"type": "structure", "required": ["fullyQualifiedName", "dataType"], "members": {"fullyQualifiedName": {"shape": "string", "documentation": "<p>The fully qualified name of the attribute. For example, the fully qualified name of an attribute might be <code>Vehicle.Body.Engine.Type</code>.</p>"}, "dataType": {"shape": "NodeDataType", "documentation": "<p>The specified data type of the attribute. </p>"}, "description": {"shape": "description", "documentation": "<p>A brief description of the attribute.</p>"}, "unit": {"shape": "string", "documentation": "<p>The scientific unit for the attribute.</p>"}, "allowedValues": {"shape": "listOfStrings", "documentation": "<p>A list of possible values an attribute can be assigned.</p>"}, "min": {"shape": "double", "documentation": "<p>The specified possible minimum value of the attribute.</p>"}, "max": {"shape": "double", "documentation": "<p>The specified possible maximum value of the attribute.</p>"}, "assignedValue": {"shape": "string", "documentation": "<p>A specified value for the attribute.</p>", "deprecated": true, "deprecatedMessage": "assignedValue is no longer in use"}, "defaultValue": {"shape": "string", "documentation": "<p>The default value of the attribute.</p>"}, "deprecationMessage": {"shape": "message", "documentation": "<p>The deprecation message for the node or the branch that was moved or deleted.</p>"}, "comment": {"shape": "message", "documentation": "<p>A comment in addition to the description.</p>"}}, "documentation": "<p>A signal that represents static information about the vehicle, such as engine type or manufacturing date.</p>"}, "BatchCreateVehicleRequest": {"type": "structure", "required": ["vehicles"], "members": {"vehicles": {"shape": "createVehicleRequestItems", "documentation": "<p> A list of information about each vehicle to create. For more information, see the API data type.</p>"}}}, "BatchCreateVehicleResponse": {"type": "structure", "members": {"vehicles": {"shape": "createVehicleResponses", "documentation": "<p> A list of information about a batch of created vehicles. For more information, see the API data type.</p>"}, "errors": {"shape": "createVehicleErrors", "documentation": "<p>A list of information about creation errors, or an empty list if there aren't any errors. </p>"}}}, "BatchUpdateVehicleRequest": {"type": "structure", "required": ["vehicles"], "members": {"vehicles": {"shape": "updateVehicleRequestItems", "documentation": "<p> A list of information about the vehicles to update. For more information, see the API data type.</p>"}}}, "BatchUpdateVehicleResponse": {"type": "structure", "members": {"vehicles": {"shape": "updateVehicleResponseItems", "documentation": "<p> A list of information about the batch of updated vehicles. </p> <note> <p>This list contains only unique IDs for the vehicles that were updated.</p> </note>"}, "errors": {"shape": "updateVehicleErrors", "documentation": "<p>A list of information about errors returned while updating a batch of vehicles, or, if there aren't any errors, an empty list.</p>"}}}, "Branch": {"type": "structure", "required": ["fullyQualifiedName"], "members": {"fullyQualifiedName": {"shape": "string", "documentation": "<p>The fully qualified name of the branch. For example, the fully qualified name of a branch might be <code>Vehicle.Body.Engine</code>.</p>"}, "description": {"shape": "description", "documentation": "<p>A brief description of the branch.</p>"}, "deprecationMessage": {"shape": "message", "documentation": "<p>The deprecation message for the node or the branch that was moved or deleted.</p>"}, "comment": {"shape": "message", "documentation": "<p>A comment in addition to the description.</p>"}}, "documentation": "<p>A group of signals that are defined in a hierarchical structure.</p>"}, "CampaignStatus": {"type": "string", "enum": ["CREATING", "WAITING_FOR_APPROVAL", "RUNNING", "SUSPENDED"]}, "CampaignSummary": {"type": "structure", "required": ["creationTime", "lastModificationTime"], "members": {"arn": {"shape": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of a campaign.</p>"}, "name": {"shape": "campaignName", "documentation": "<p>The name of a campaign.</p>"}, "description": {"shape": "description", "documentation": "<p>The description of the campaign.</p>"}, "signalCatalogArn": {"shape": "arn", "documentation": "<p>The ARN of the signal catalog associated with the campaign.</p>"}, "targetArn": {"shape": "arn", "documentation": "<p>The ARN of a vehicle or fleet to which the campaign is deployed.</p>"}, "status": {"shape": "CampaignStatus", "documentation": "<p>The state of a campaign. The status can be one of the following:</p> <ul> <li> <p> <code>CREATING</code> - Amazon Web Services IoT FleetWise is processing your request to create the campaign.</p> </li> <li> <p> <code>WAITING_FOR_APPROVAL</code> - After a campaign is created, it enters the <code>WAITING_FOR_APPROVAL</code> state. To allow Amazon Web Services IoT FleetWise to deploy the campaign to the target vehicle or fleet, use the API operation to approve the campaign. </p> </li> <li> <p> <code>RUNNING</code> - The campaign is active. </p> </li> <li> <p> <code>SUSPENDED</code> - The campaign is suspended. To resume the campaign, use the API operation. </p> </li> </ul>"}, "creationTime": {"shape": "timestamp", "documentation": "<p>The time the campaign was created.</p>"}, "lastModificationTime": {"shape": "timestamp", "documentation": "<p>The last time the campaign was modified.</p>"}}, "documentation": "<p>Information about a campaign. </p> <p>You can use the API operation to return this information about multiple created campaigns.</p>"}, "CanDbcDefinition": {"type": "structure", "required": ["networkInterface", "canDbcFiles"], "members": {"networkInterface": {"shape": "InterfaceId", "documentation": "<p>Contains information about a network interface.</p>"}, "canDbcFiles": {"shape": "NetworkFilesList", "documentation": "<p>A list of DBC files. You can upload only one DBC file for each network interface and specify up to five (inclusive) files in the list.</p>"}, "signalsMap": {"shape": "ModelSignalsMap", "documentation": "<p>Pairs every signal specified in your vehicle model with a signal decoder.</p>"}}, "documentation": "<p>Configurations used to create a decoder manifest.</p>"}, "CanInterface": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "CanInterfaceName", "documentation": "<p>The unique name of the interface.</p>"}, "protocolName": {"shape": "ProtocolName", "documentation": "<p>The name of the communication protocol for the interface.</p>"}, "protocolVersion": {"shape": "ProtocolVersion", "documentation": "<p>The version of the communication protocol for the interface.</p>"}}, "documentation": "<p>A single controller area network (CAN) device interface.</p>"}, "CanInterfaceName": {"type": "string", "max": 100, "min": 1}, "CanSignal": {"type": "structure", "required": ["messageId", "isBigEndian", "isSigned", "startBit", "offset", "factor", "length"], "members": {"messageId": {"shape": "nonNegativeInteger", "documentation": "<p>The ID of the message.</p>"}, "isBigEndian": {"shape": "boolean", "documentation": "<p>Whether the byte ordering of a CAN message is big-endian.</p>"}, "isSigned": {"shape": "boolean", "documentation": "<p>Whether the message data is specified as a signed value.</p>"}, "startBit": {"shape": "nonNegativeInteger", "documentation": "<p>Indicates the beginning of the CAN signal. This should always be the least significant bit (LSB).</p> <p>This value might be different from the value in a DBC file. For little endian signals, <code>startBit</code> is the same value as in the DBC file. For big endian signals in a DBC file, the start bit is the most significant bit (MSB). You will have to calculate the LSB instead and pass it as the <code>startBit</code>.</p>"}, "offset": {"shape": "double", "documentation": "<p>The offset used to calculate the signal value. Combined with factor, the calculation is <code>value = raw_value * factor + offset</code>.</p>"}, "factor": {"shape": "double", "documentation": "<p>A multiplier used to decode the CAN message.</p>"}, "length": {"shape": "nonNegativeInteger", "documentation": "<p>How many bytes of data are in the message.</p>"}, "name": {"shape": "CanSignalName", "documentation": "<p>The name of the signal.</p>"}}, "documentation": "<p>Information about a single controller area network (CAN) signal and the messages it receives and transmits.</p>"}, "CanSignalName": {"type": "string", "max": 100, "min": 1}, "CloudWatchLogDeliveryOptions": {"type": "structure", "required": ["logType"], "members": {"logType": {"shape": "LogType", "documentation": "<p>The type of log to send data to Amazon CloudWatch Logs.</p>"}, "logGroupName": {"shape": "CloudWatchLogGroupName", "documentation": "<p>The Amazon CloudWatch Logs group the operation sends data to.</p>"}}, "documentation": "<p>The log delivery option to send data to Amazon CloudWatch Logs.</p>"}, "CloudWatchLogGroupName": {"type": "string", "max": 512, "min": 1, "pattern": "[\\.\\-_\\/#A-Za-z0-9]+"}, "CollectionScheme": {"type": "structure", "members": {"timeBasedCollectionScheme": {"shape": "TimeBasedCollectionScheme", "documentation": "<p>Information about a collection scheme that uses a time period to decide how often to collect data.</p>"}, "conditionBasedCollectionScheme": {"shape": "ConditionBasedCollectionScheme", "documentation": "<p>Information about a collection scheme that uses a simple logical expression to recognize what data to collect.</p>"}}, "documentation": "<p>Specifies what data to collect and how often or when to collect it.</p>", "union": true}, "Compression": {"type": "string", "enum": ["OFF", "SNAPPY"]}, "ConditionBasedCollectionScheme": {"type": "structure", "required": ["expression"], "members": {"expression": {"shape": "eventExpression", "documentation": "<p>The logical expression used to recognize what data to collect. For example, <code>$variable.Vehicle.OutsideAirTemperature &gt;= 105.0</code>.</p>"}, "minimumTriggerIntervalMs": {"shape": "uint32", "documentation": "<p>The minimum duration of time between two triggering events to collect data, in milliseconds.</p> <note> <p>If a signal changes often, you might want to collect data at a slower rate.</p> </note>"}, "triggerMode": {"shape": "TriggerMode", "documentation": "<p>Whether to collect data for all triggering events (<code>ALWAYS</code>). Specify (<code>RISING_EDGE</code>), or specify only when the condition first evaluates to false. For example, triggering on \"AirbagDeployed\"; Users aren't interested on triggering when the airbag is already exploded; they only care about the change from not deployed =&gt; deployed.</p>"}, "conditionLanguageVersion": {"shape": "languageVersion", "documentation": "<p>Specifies the version of the conditional expression language.</p>"}}, "documentation": "<p>Information about a collection scheme that uses a simple logical expression to recognize what data to collect.</p>"}, "ConflictException": {"type": "structure", "required": ["message", "resource", "resourceType"], "members": {"message": {"shape": "string"}, "resource": {"shape": "string", "documentation": "<p>The resource on which there are conflicting operations.</p>"}, "resourceType": {"shape": "string", "documentation": "<p>The type of resource on which there are conflicting operations..</p>"}}, "documentation": "<p>The request has conflicting operations. This can occur if you're trying to perform more than one operation on the same resource at the same time.</p>", "exception": true}, "CreateCampaignRequest": {"type": "structure", "required": ["name", "signalCatalogArn", "targetArn", "collectionScheme"], "members": {"name": {"shape": "campaignName", "documentation": "<p> The name of the campaign to create. </p>"}, "description": {"shape": "description", "documentation": "<p>An optional description of the campaign to help identify its purpose.</p>"}, "signalCatalogArn": {"shape": "arn", "documentation": "<p>(Optional) The Amazon Resource Name (ARN) of the signal catalog to associate with the campaign. </p>"}, "targetArn": {"shape": "arn", "documentation": "<p> The ARN of the vehicle or fleet to deploy a campaign to. </p>"}, "startTime": {"shape": "timestamp", "documentation": "<p>(Optional) The time, in milliseconds, to deliver a campaign after it was approved. If it's not specified, <code>0</code> is used.</p> <p>Default: <code>0</code> </p>"}, "expiryTime": {"shape": "timestamp", "documentation": "<p> (Optional) The time the campaign expires, in seconds since epoch (January 1, 1970 at midnight UTC time). Vehicle data isn't collected after the campaign expires. </p> <p>Default: 253402214400 (December 31, 9999, 00:00:00 UTC)</p>"}, "postTriggerCollectionDuration": {"shape": "uint32", "documentation": "<p> (Optional) How long (in milliseconds) to collect raw data after a triggering event initiates the collection. If it's not specified, <code>0</code> is used.</p> <p>Default: <code>0</code> </p>"}, "diagnosticsMode": {"shape": "DiagnosticsMode", "documentation": "<p> (Optional) Option for a vehicle to send diagnostic trouble codes to Amazon Web Services IoT FleetWise. If you want to send diagnostic trouble codes, use <code>SEND_ACTIVE_DTCS</code>. If it's not specified, <code>OFF</code> is used.</p> <p>Default: <code>OFF</code> </p>"}, "spoolingMode": {"shape": "SpoolingMode", "documentation": "<p>(Optional) Whether to store collected data after a vehicle lost a connection with the cloud. After a connection is re-established, the data is automatically forwarded to Amazon Web Services IoT FleetWise. If you want to store collected data when a vehicle loses connection with the cloud, use <code>TO_DISK</code>. If it's not specified, <code>OFF</code> is used.</p> <p>Default: <code>OFF</code> </p>"}, "compression": {"shape": "Compression", "documentation": "<p> (Optional) Whether to compress signals before transmitting data to Amazon Web Services IoT FleetWise. If you don't want to compress the signals, use <code>OFF</code>. If it's not specified, <code>SNAPPY</code> is used. </p> <p>Default: <code>SNAPPY</code> </p>"}, "priority": {"shape": "priority", "documentation": "<p>(Optional) A number indicating the priority of one campaign over another campaign for a certain vehicle or fleet. A campaign with the lowest value is deployed to vehicles before any other campaigns. If it's not specified, <code>0</code> is used. </p> <p>Default: <code>0</code> </p>"}, "signalsToCollect": {"shape": "SignalInformationList", "documentation": "<p>(Optional) A list of information about signals to collect. </p>"}, "collectionScheme": {"shape": "CollectionScheme", "documentation": "<p> The data collection scheme associated with the campaign. You can specify a scheme that collects data based on time or an event.</p>"}, "dataExtraDimensions": {"shape": "DataExtraDimensionNodePathList", "documentation": "<p> (Optional) A list of vehicle attributes to associate with a campaign. </p> <p>Enrich the data with specified vehicle attributes. For example, add <code>make</code> and <code>model</code> to the campaign, and Amazon Web Services IoT FleetWise will associate the data with those attributes as dimensions in Amazon Timestream. You can then query the data against <code>make</code> and <code>model</code>.</p> <p>Default: An empty array</p>"}, "tags": {"shape": "TagList", "documentation": "<p>Metadata that can be used to manage the campaign.</p>"}, "dataDestinationConfigs": {"shape": "DataDestinationConfigs", "documentation": "<p>The destination where the campaign sends data. You can choose to send data to be stored in Amazon S3 or Amazon Timestream.</p> <p>Amazon S3 optimizes the cost of data storage and provides additional mechanisms to use vehicle data, such as data lakes, centralized data storage, data processing pipelines, and analytics. Amazon Web Services IoT FleetWise supports at-least-once file delivery to S3. Your vehicle data is stored on multiple Amazon Web Services IoT FleetWise servers for redundancy and high availability.</p> <p>You can use Amazon Timestream to access and analyze time series data, and Timestream to query vehicle data so that you can identify trends and patterns.</p>"}}}, "CreateCampaignResponse": {"type": "structure", "members": {"name": {"shape": "campaignName", "documentation": "<p>The name of the created campaign.</p>"}, "arn": {"shape": "arn", "documentation": "<p> The ARN of the created campaign. </p>"}}}, "CreateDecoderManifestRequest": {"type": "structure", "required": ["name", "modelManifestArn"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The unique name of the decoder manifest to create.</p>"}, "description": {"shape": "description", "documentation": "<p> A brief description of the decoder manifest. </p>"}, "modelManifestArn": {"shape": "arn", "documentation": "<p> The Amazon Resource Name (ARN) of the vehicle model (model manifest). </p>"}, "signalDecoders": {"shape": "SignalDecoders", "documentation": "<p> A list of information about signal decoders. </p>"}, "networkInterfaces": {"shape": "NetworkInterfaces", "documentation": "<p> A list of information about available network interfaces. </p>"}, "tags": {"shape": "TagList", "documentation": "<p>Metadata that can be used to manage the decoder manifest.</p>"}}}, "CreateDecoderManifestResponse": {"type": "structure", "required": ["name", "arn"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the created decoder manifest. </p>"}, "arn": {"shape": "arn", "documentation": "<p> The ARN of the created decoder manifest. </p>"}}}, "CreateFleetRequest": {"type": "structure", "required": ["fleetId", "signalCatalogArn"], "members": {"fleetId": {"shape": "fleetId", "documentation": "<p> The unique ID of the fleet to create. </p>"}, "description": {"shape": "description", "documentation": "<p> A brief description of the fleet to create. </p>"}, "signalCatalogArn": {"shape": "arn", "documentation": "<p> The Amazon Resource Name (ARN) of a signal catalog. </p>"}, "tags": {"shape": "TagList", "documentation": "<p>Metadata that can be used to manage the fleet.</p>"}}}, "CreateFleetResponse": {"type": "structure", "required": ["id", "arn"], "members": {"id": {"shape": "fleetId", "documentation": "<p> The ID of the created fleet. </p>"}, "arn": {"shape": "arn", "documentation": "<p> The ARN of the created fleet. </p>"}}}, "CreateModelManifestRequest": {"type": "structure", "required": ["name", "nodes", "signalCatalogArn"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the vehicle model to create.</p>"}, "description": {"shape": "description", "documentation": "<p> A brief description of the vehicle model. </p>"}, "nodes": {"shape": "listOfStrings", "documentation": "<p> A list of nodes, which are a general abstraction of signals. </p>"}, "signalCatalogArn": {"shape": "arn", "documentation": "<p> The Amazon Resource Name (ARN) of a signal catalog. </p>"}, "tags": {"shape": "TagList", "documentation": "<p>Metadata that can be used to manage the vehicle model.</p>"}}}, "CreateModelManifestResponse": {"type": "structure", "required": ["name", "arn"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the created vehicle model.</p>"}, "arn": {"shape": "arn", "documentation": "<p> The ARN of the created vehicle model.</p>"}}}, "CreateSignalCatalogRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the signal catalog to create. </p>"}, "description": {"shape": "description", "documentation": "<p>A brief description of the signal catalog.</p>"}, "nodes": {"shape": "Nodes", "documentation": "<p> A list of information about nodes, which are a general abstraction of signals. For more information, see the API data type.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>Metadata that can be used to manage the signal catalog.</p>"}}}, "CreateSignalCatalogResponse": {"type": "structure", "required": ["name", "arn"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the created signal catalog. </p>"}, "arn": {"shape": "arn", "documentation": "<p> The ARN of the created signal catalog. </p>"}}}, "CreateVehicleError": {"type": "structure", "members": {"vehicleName": {"shape": "vehicleName", "documentation": "<p>The ID of the vehicle with the error.</p>"}, "code": {"shape": "string", "documentation": "<p>An HTTP error code.</p>"}, "message": {"shape": "string", "documentation": "<p>A description of the HTTP error.</p>"}}, "documentation": "<p>An HTTP error resulting from creating a vehicle.</p>"}, "CreateVehicleRequest": {"type": "structure", "required": ["vehicleName", "modelManifestArn", "decoderManifestArn"], "members": {"vehicleName": {"shape": "vehicleName", "documentation": "<p> The unique ID of the vehicle to create. </p>"}, "modelManifestArn": {"shape": "arn", "documentation": "<p> The Amazon Resource Name ARN of a vehicle model. </p>"}, "decoderManifestArn": {"shape": "arn", "documentation": "<p> The ARN of a decoder manifest. </p>"}, "attributes": {"shape": "attributesMap", "documentation": "<p>Static information about a vehicle in a key-value pair. For example: <code>\"engineType\"</code> : <code>\"1.3 L R2\"</code> </p> <p>A campaign must include the keys (attribute names) in <code>dataExtraDimensions</code> for them to display in Amazon Timestream.</p>"}, "associationBehavior": {"shape": "VehicleAssociationBehavior", "documentation": "<p> An option to create a new Amazon Web Services IoT thing when creating a vehicle, or to validate an existing Amazon Web Services IoT thing as a vehicle. </p> <p>Default: <code/> </p>"}, "tags": {"shape": "TagList", "documentation": "<p>Metadata that can be used to manage the vehicle.</p>"}}}, "CreateVehicleRequestItem": {"type": "structure", "required": ["vehicleName", "modelManifestArn", "decoderManifestArn"], "members": {"vehicleName": {"shape": "vehicleName", "documentation": "<p>The unique ID of the vehicle to create.</p>"}, "modelManifestArn": {"shape": "arn", "documentation": "<p>The ARN of the vehicle model (model manifest) to create the vehicle from.</p>"}, "decoderManifestArn": {"shape": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of a decoder manifest associated with the vehicle to create. </p>"}, "attributes": {"shape": "attributesMap", "documentation": "<p>Static information about a vehicle in a key-value pair. For example: <code>\"engine Type\"</code> : <code>\"v6\"</code> </p>"}, "associationBehavior": {"shape": "VehicleAssociationBehavior", "documentation": "<p>An option to create a new Amazon Web Services IoT thing when creating a vehicle, or to validate an existing thing as a vehicle.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>Metadata which can be used to manage the vehicle.</p>"}}, "documentation": "<p>Information about the vehicle to create.</p>"}, "CreateVehicleResponse": {"type": "structure", "members": {"vehicleName": {"shape": "vehicleName", "documentation": "<p>The unique ID of the created vehicle.</p>"}, "arn": {"shape": "arn", "documentation": "<p> The ARN of the created vehicle. </p>"}, "thingArn": {"shape": "arn", "documentation": "<p> The ARN of a created or validated Amazon Web Services IoT thing. </p>"}}}, "CreateVehicleResponseItem": {"type": "structure", "members": {"vehicleName": {"shape": "vehicleName", "documentation": "<p>The unique ID of the vehicle to create.</p>"}, "arn": {"shape": "arn", "documentation": "<p>The ARN of the created vehicle.</p>"}, "thingArn": {"shape": "arn", "documentation": "<p>The ARN of a created or validated Amazon Web Services IoT thing.</p>"}}, "documentation": "<p>Information about a created vehicle.</p>"}, "DataDestinationConfig": {"type": "structure", "members": {"s3Config": {"shape": "S3Config", "documentation": "<p>The Amazon S3 bucket where the Amazon Web Services IoT FleetWise campaign sends data.</p>"}, "timestreamConfig": {"shape": "TimestreamConfig", "documentation": "<p>The Amazon Timestream table where the campaign sends data.</p>"}}, "documentation": "<p>The destination where the Amazon Web Services IoT FleetWise campaign sends data. You can send data to be stored in Amazon S3 or Amazon Timestream.</p>", "union": true}, "DataDestinationConfigs": {"type": "list", "member": {"shape": "DataDestinationConfig"}, "max": 1, "min": 1}, "DataExtraDimensionNodePathList": {"type": "list", "member": {"shape": "NodePath"}, "max": 5, "min": 0}, "DataFormat": {"type": "string", "enum": ["JSON", "PARQUET"]}, "DecoderManifestSummary": {"type": "structure", "required": ["creationTime", "lastModificationTime"], "members": {"name": {"shape": "string", "documentation": "<p>The name of the decoder manifest.</p>"}, "arn": {"shape": "arn", "documentation": "<p>The ARN of a vehicle model (model manifest) associated with the decoder manifest. </p>"}, "modelManifestArn": {"shape": "arn", "documentation": "<p>The ARN of a vehicle model (model manifest) associated with the decoder manifest.</p>"}, "description": {"shape": "description", "documentation": "<p>A brief description of the decoder manifest.</p>"}, "status": {"shape": "ManifestStatus", "documentation": "<p>The state of the decoder manifest. If the status is <code>ACTIVE</code>, the decoder manifest can't be edited. If the status is marked <code>DRAFT</code>, you can edit the decoder manifest.</p>"}, "creationTime": {"shape": "timestamp", "documentation": "<p>The time the decoder manifest was created in seconds since epoch (January 1, 1970 at midnight UTC time).</p>"}, "lastModificationTime": {"shape": "timestamp", "documentation": "<p>The time the decoder manifest was last updated in seconds since epoch (January 1, 1970 at midnight UTC time).</p>"}}, "documentation": "<p>Information about a created decoder manifest. You can use the API operation to return this information about multiple decoder manifests.</p>"}, "DecoderManifestValidationException": {"type": "structure", "members": {"invalidSignals": {"shape": "InvalidSignalDecoders", "documentation": "<p>The request couldn't be completed because of invalid signals in the request.</p>"}, "invalidNetworkInterfaces": {"shape": "InvalidNetworkInterfaces", "documentation": "<p>The request couldn't be completed because of invalid network interfaces in the request.</p>"}, "message": {"shape": "string"}}, "documentation": "<p>The request couldn't be completed because it contains signal decoders with one or more validation errors.</p>", "exception": true}, "DeleteCampaignRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "campaignName", "documentation": "<p> The name of the campaign to delete. </p>"}}}, "DeleteCampaignResponse": {"type": "structure", "members": {"name": {"shape": "campaignName", "documentation": "<p>The name of the deleted campaign.</p>"}, "arn": {"shape": "arn", "documentation": "<p> The Amazon Resource Name (ARN) of the deleted campaign.</p> <note> <p>The ARN isn’t returned if a campaign doesn’t exist.</p> </note>"}}}, "DeleteDecoderManifestRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the decoder manifest to delete. </p>"}}}, "DeleteDecoderManifestResponse": {"type": "structure", "required": ["name", "arn"], "members": {"name": {"shape": "resourceName", "documentation": "<p>The name of the deleted decoder manifest.</p>"}, "arn": {"shape": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the deleted decoder manifest.</p>"}}}, "DeleteFleetRequest": {"type": "structure", "required": ["fleetId"], "members": {"fleetId": {"shape": "fleetId", "documentation": "<p> The ID of the fleet to delete. </p>"}}}, "DeleteFleetResponse": {"type": "structure", "members": {"id": {"shape": "fleetId", "documentation": "<p>The ID of the deleted fleet.</p>"}, "arn": {"shape": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the deleted fleet.</p>"}}}, "DeleteModelManifestRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the model manifest to delete. </p>"}}}, "DeleteModelManifestResponse": {"type": "structure", "required": ["name", "arn"], "members": {"name": {"shape": "resourceName", "documentation": "<p>The name of the deleted model manifest.</p>"}, "arn": {"shape": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the deleted model manifest.</p>"}}}, "DeleteSignalCatalogRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the signal catalog to delete. </p>"}}}, "DeleteSignalCatalogResponse": {"type": "structure", "required": ["name", "arn"], "members": {"name": {"shape": "resourceName", "documentation": "<p>The name of the deleted signal catalog.</p>"}, "arn": {"shape": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the deleted signal catalog.</p>"}}}, "DeleteVehicleRequest": {"type": "structure", "required": ["vehicleName"], "members": {"vehicleName": {"shape": "vehicleName", "documentation": "<p>The ID of the vehicle to delete. </p>"}}}, "DeleteVehicleResponse": {"type": "structure", "required": ["vehicleName", "arn"], "members": {"vehicleName": {"shape": "vehicleName", "documentation": "<p>The ID of the deleted vehicle.</p>"}, "arn": {"shape": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the deleted vehicle.</p>"}}}, "DiagnosticsMode": {"type": "string", "enum": ["OFF", "SEND_ACTIVE_DTCS"]}, "DisassociateVehicleFleetRequest": {"type": "structure", "required": ["vehicleName", "fleetId"], "members": {"vehicleName": {"shape": "vehicleName", "documentation": "<p> The unique ID of the vehicle to disassociate from the fleet.</p>"}, "fleetId": {"shape": "fleetId", "documentation": "<p> The unique ID of a fleet. </p>"}}}, "DisassociateVehicleFleetResponse": {"type": "structure", "members": {}}, "EncryptionStatus": {"type": "string", "enum": ["PENDING", "SUCCESS", "FAILURE"]}, "EncryptionType": {"type": "string", "enum": ["KMS_BASED_ENCRYPTION", "FLEETWISE_DEFAULT_ENCRYPTION"]}, "FleetSummary": {"type": "structure", "required": ["id", "arn", "signalCatalogArn", "creationTime"], "members": {"id": {"shape": "fleetId", "documentation": "<p>The unique ID of the fleet.</p>"}, "arn": {"shape": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the fleet.</p>"}, "description": {"shape": "description", "documentation": "<p>A brief description of the fleet.</p>"}, "signalCatalogArn": {"shape": "arn", "documentation": "<p>The ARN of the signal catalog associated with the fleet.</p>"}, "creationTime": {"shape": "timestamp", "documentation": "<p>The time the fleet was created, in seconds since epoch (January 1, 1970 at midnight UTC time).</p>"}, "lastModificationTime": {"shape": "timestamp", "documentation": "<p>The time the fleet was last updated in seconds since epoch (January 1, 1970 at midnight UTC time).</p>"}}, "documentation": "<p>Information about a fleet.</p> <p>You can use the API operation to return this information about multiple fleets.</p>"}, "FormattedVss": {"type": "structure", "members": {"vssJson": {"shape": "String", "documentation": "<p>Provides the VSS in JSON format.</p>"}}, "documentation": "<p> <a href=\"https://www.w3.org/auto/wg/wiki/Vehicle_Signal_Specification_(VSS)/Vehicle_Data_Spec\">Vehicle Signal Specification (VSS)</a> is a precise language used to describe and model signals in vehicle networks. The JSON file collects signal specificiations in a VSS format.</p>", "union": true}, "Fqns": {"type": "list", "member": {"shape": "FullyQualifiedName"}, "max": 500, "min": 1}, "FullyQualifiedName": {"type": "string", "max": 150, "min": 1}, "GetCampaignRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "campaignName", "documentation": "<p> The name of the campaign to retrieve information about. </p>"}}}, "GetCampaignResponse": {"type": "structure", "members": {"name": {"shape": "campaignName", "documentation": "<p>The name of the campaign.</p>"}, "arn": {"shape": "arn", "documentation": "<p> The Amazon Resource Name (ARN) of the campaign. </p>"}, "description": {"shape": "description", "documentation": "<p>The description of the campaign.</p>"}, "signalCatalogArn": {"shape": "arn", "documentation": "<p> The ARN of a signal catalog. </p>"}, "targetArn": {"shape": "arn", "documentation": "<p> The ARN of the vehicle or the fleet targeted by the campaign. </p>"}, "status": {"shape": "CampaignStatus", "documentation": "<p>The state of the campaign. The status can be one of: <code>CREATING</code>, <code>WAITING_FOR_APPROVAL</code>, <code>RUNNING</code>, and <code>SUSPENDED</code>. </p>"}, "startTime": {"shape": "timestamp", "documentation": "<p> The time, in milliseconds, to deliver a campaign after it was approved.</p>"}, "expiryTime": {"shape": "timestamp", "documentation": "<p> The time the campaign expires, in seconds since epoch (January 1, 1970 at midnight UTC time). Vehicle data won't be collected after the campaign expires.</p>"}, "postTriggerCollectionDuration": {"shape": "uint32", "documentation": "<p> How long (in seconds) to collect raw data after a triggering event initiates the collection. </p>"}, "diagnosticsMode": {"shape": "DiagnosticsMode", "documentation": "<p> Option for a vehicle to send diagnostic trouble codes to Amazon Web Services IoT FleetWise. </p>"}, "spoolingMode": {"shape": "SpoolingMode", "documentation": "<p> Whether to store collected data after a vehicle lost a connection with the cloud. After a connection is re-established, the data is automatically forwarded to Amazon Web Services IoT FleetWise. </p>"}, "compression": {"shape": "Compression", "documentation": "<p> Whether to compress signals before transmitting data to Amazon Web Services IoT FleetWise. If <code>OFF</code> is specified, the signals aren't compressed. If it's not specified, <code>SNAPPY</code> is used. </p>"}, "priority": {"shape": "priority", "documentation": "<p> A number indicating the priority of one campaign over another campaign for a certain vehicle or fleet. A campaign with the lowest value is deployed to vehicles before any other campaigns.</p>"}, "signalsToCollect": {"shape": "SignalInformationList", "documentation": "<p> Information about a list of signals to collect data on. </p>"}, "collectionScheme": {"shape": "CollectionScheme", "documentation": "<p> Information about the data collection scheme associated with the campaign. </p>"}, "dataExtraDimensions": {"shape": "DataExtraDimensionNodePathList", "documentation": "<p> A list of vehicle attributes associated with the campaign. </p>"}, "creationTime": {"shape": "timestamp", "documentation": "<p> The time the campaign was created in seconds since epoch (January 1, 1970 at midnight UTC time). </p>"}, "lastModificationTime": {"shape": "timestamp", "documentation": "<p>The last time the campaign was modified.</p>"}, "dataDestinationConfigs": {"shape": "DataDestinationConfigs", "documentation": "<p>The destination where the campaign sends data. You can choose to send data to be stored in Amazon S3 or Amazon Timestream.</p> <p>Amazon S3 optimizes the cost of data storage and provides additional mechanisms to use vehicle data, such as data lakes, centralized data storage, data processing pipelines, and analytics. </p> <p>You can use Amazon Timestream to access and analyze time series data, and Timestream to query vehicle data so that you can identify trends and patterns.</p>"}}}, "GetDecoderManifestRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the decoder manifest to retrieve information about. </p>"}}}, "GetDecoderManifestResponse": {"type": "structure", "required": ["name", "arn", "creationTime", "lastModificationTime"], "members": {"name": {"shape": "string", "documentation": "<p> The name of the decoder manifest. </p>"}, "arn": {"shape": "arn", "documentation": "<p> The Amazon Resource Name (ARN) of the decoder manifest. </p>"}, "description": {"shape": "description", "documentation": "<p> A brief description of the decoder manifest.</p>"}, "modelManifestArn": {"shape": "arn", "documentation": "<p> The ARN of a vehicle model (model manifest) associated with the decoder manifest.</p>"}, "status": {"shape": "ManifestStatus", "documentation": "<p> The state of the decoder manifest. If the status is <code>ACTIVE</code>, the decoder manifest can't be edited. If the status is marked <code>DRAFT</code>, you can edit the decoder manifest.</p>"}, "creationTime": {"shape": "timestamp", "documentation": "<p> The time the decoder manifest was created in seconds since epoch (January 1, 1970 at midnight UTC time). </p>"}, "lastModificationTime": {"shape": "timestamp", "documentation": "<p> The time the decoder manifest was last updated in seconds since epoch (January 1, 1970 at midnight UTC time). </p>"}}}, "GetEncryptionConfigurationRequest": {"type": "structure", "members": {}}, "GetEncryptionConfigurationResponse": {"type": "structure", "required": ["encryptionStatus", "encryptionType"], "members": {"kmsKeyId": {"shape": "String", "documentation": "<p>The ID of the KMS key that is used for encryption.</p>"}, "encryptionStatus": {"shape": "EncryptionStatus", "documentation": "<p>The encryption status.</p>"}, "encryptionType": {"shape": "EncryptionType", "documentation": "<p>The type of encryption. Set to <code>KMS_BASED_ENCRYPTION</code> to use an KMS key that you own and manage. Set to <code>FLEETWISE_DEFAULT_ENCRYPTION</code> to use an Amazon Web Services managed key that is owned by the Amazon Web Services IoT FleetWise service account.</p>"}, "errorMessage": {"shape": "errorMessage", "documentation": "<p>The error message that describes why encryption settings couldn't be configured, if applicable.</p>"}, "creationTime": {"shape": "timestamp", "documentation": "<p>The time when encryption was configured in seconds since epoch (January 1, 1970 at midnight UTC time).</p>"}, "lastModificationTime": {"shape": "timestamp", "documentation": "<p>The time when encryption was last updated in seconds since epoch (January 1, 1970 at midnight UTC time).</p>"}}}, "GetFleetRequest": {"type": "structure", "required": ["fleetId"], "members": {"fleetId": {"shape": "fleetId", "documentation": "<p> The ID of the fleet to retrieve information about. </p>"}}}, "GetFleetResponse": {"type": "structure", "required": ["id", "arn", "signalCatalogArn", "creationTime", "lastModificationTime"], "members": {"id": {"shape": "fleetId", "documentation": "<p> The ID of the fleet.</p>"}, "arn": {"shape": "arn", "documentation": "<p> The Amazon Resource Name (ARN) of the fleet. </p>"}, "description": {"shape": "description", "documentation": "<p> A brief description of the fleet. </p>"}, "signalCatalogArn": {"shape": "arn", "documentation": "<p> The ARN of a signal catalog associated with the fleet. </p>"}, "creationTime": {"shape": "timestamp", "documentation": "<p> The time the fleet was created in seconds since epoch (January 1, 1970 at midnight UTC time). </p>"}, "lastModificationTime": {"shape": "timestamp", "documentation": "<p> The time the fleet was last updated, in seconds since epoch (January 1, 1970 at midnight UTC time). </p>"}}}, "GetLoggingOptionsRequest": {"type": "structure", "members": {}}, "GetLoggingOptionsResponse": {"type": "structure", "required": ["cloudWatchLogDelivery"], "members": {"cloudWatchLogDelivery": {"shape": "CloudWatchLogDeliveryOptions", "documentation": "<p>Returns information about log delivery to Amazon CloudWatch Logs.</p>"}}}, "GetModelManifestRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the vehicle model to retrieve information about. </p>"}}}, "GetModelManifestResponse": {"type": "structure", "required": ["name", "arn", "creationTime", "lastModificationTime"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the vehicle model. </p>"}, "arn": {"shape": "arn", "documentation": "<p> The Amazon Resource Name (ARN) of the vehicle model. </p>"}, "description": {"shape": "description", "documentation": "<p> A brief description of the vehicle model. </p>"}, "signalCatalogArn": {"shape": "arn", "documentation": "<p> The ARN of the signal catalog associated with the vehicle model. </p>"}, "status": {"shape": "ManifestStatus", "documentation": "<p> The state of the vehicle model. If the status is <code>ACTIVE</code>, the vehicle model can't be edited. You can edit the vehicle model if the status is marked <code>DRAFT</code>.</p>"}, "creationTime": {"shape": "timestamp", "documentation": "<p>The time the vehicle model was created, in seconds since epoch (January 1, 1970 at midnight UTC time).</p>"}, "lastModificationTime": {"shape": "timestamp", "documentation": "<p>The last time the vehicle model was modified.</p>"}}}, "GetRegisterAccountStatusRequest": {"type": "structure", "members": {}}, "GetRegisterAccountStatusResponse": {"type": "structure", "required": ["customerAccountId", "accountStatus", "iamRegistrationResponse", "creationTime", "lastModificationTime"], "members": {"customerAccountId": {"shape": "customerAccountId", "documentation": "<p> The unique ID of the Amazon Web Services account, provided at account creation. </p>"}, "accountStatus": {"shape": "RegistrationStatus", "documentation": "<p> The status of registering your account and resources. The status can be one of:</p> <ul> <li> <p> <code>REGISTRATION_SUCCESS</code> - The Amazon Web Services resource is successfully registered.</p> </li> <li> <p> <code>REGISTRATION_PENDING</code> - Amazon Web Services IoT FleetWise is processing the registration request. This process takes approximately five minutes to complete.</p> </li> <li> <p> <code>REGISTRATION_FAILURE</code> - Amazon Web Services IoT FleetWise can't register the AWS resource. Try again later.</p> </li> </ul>"}, "timestreamRegistrationResponse": {"shape": "TimestreamRegistrationResponse", "documentation": "<p> Information about the registered Amazon Timestream resources or errors, if any.</p>"}, "iamRegistrationResponse": {"shape": "IamRegistrationResponse", "documentation": "<p> Information about the registered IAM resources or errors, if any. </p>"}, "creationTime": {"shape": "timestamp", "documentation": "<p> The time the account was registered, in seconds since epoch (January 1, 1970 at midnight UTC time). </p>"}, "lastModificationTime": {"shape": "timestamp", "documentation": "<p> The time this registration was last updated, in seconds since epoch (January 1, 1970 at midnight UTC time). </p>"}}}, "GetSignalCatalogRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the signal catalog to retrieve information about. </p>"}}}, "GetSignalCatalogResponse": {"type": "structure", "required": ["name", "arn", "creationTime", "lastModificationTime"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the signal catalog. </p>"}, "arn": {"shape": "arn", "documentation": "<p> The Amazon Resource Name (ARN) of the signal catalog. </p>"}, "description": {"shape": "description", "documentation": "<p> A brief description of the signal catalog. </p>"}, "nodeCounts": {"shape": "NodeCounts", "documentation": "<p> The total number of network nodes specified in a signal catalog. </p>"}, "creationTime": {"shape": "timestamp", "documentation": "<p> The time the signal catalog was created in seconds since epoch (January 1, 1970 at midnight UTC time). </p>"}, "lastModificationTime": {"shape": "timestamp", "documentation": "<p>The last time the signal catalog was modified.</p>"}}}, "GetVehicleRequest": {"type": "structure", "required": ["vehicleName"], "members": {"vehicleName": {"shape": "vehicleName", "documentation": "<p> The ID of the vehicle to retrieve information about. </p>"}}}, "GetVehicleResponse": {"type": "structure", "members": {"vehicleName": {"shape": "vehicleName", "documentation": "<p>The ID of the vehicle.</p>"}, "arn": {"shape": "arn", "documentation": "<p> The Amazon Resource Name (ARN) of the vehicle to retrieve information about. </p>"}, "modelManifestArn": {"shape": "arn", "documentation": "<p> The ARN of a vehicle model (model manifest) associated with the vehicle. </p>"}, "decoderManifestArn": {"shape": "arn", "documentation": "<p> The ARN of a decoder manifest associated with the vehicle. </p>"}, "attributes": {"shape": "attributesMap", "documentation": "<p>Static information about a vehicle in a key-value pair. For example:</p> <p> <code>\"engineType\"</code> : <code>\"1.3 L R2\"</code> </p>"}, "creationTime": {"shape": "timestamp", "documentation": "<p> The time the vehicle was created in seconds since epoch (January 1, 1970 at midnight UTC time). </p>"}, "lastModificationTime": {"shape": "timestamp", "documentation": "<p> The time the vehicle was last updated in seconds since epoch (January 1, 1970 at midnight UTC time). </p>"}}}, "GetVehicleStatusRequest": {"type": "structure", "required": ["vehicleName"], "members": {"nextToken": {"shape": "nextToken", "documentation": "<p>A pagination token for the next set of results.</p> <p>If the results of a search are large, only a portion of the results are returned, and a <code>nextToken</code> pagination token is returned in the response. To retrieve the next set of results, reissue the search request and include the returned token. When all results have been returned, the response does not contain a pagination token value. </p>"}, "maxResults": {"shape": "maxResults", "documentation": "<p> The maximum number of items to return, between 1 and 100, inclusive. </p>"}, "vehicleName": {"shape": "vehicleName", "documentation": "<p> The ID of the vehicle to retrieve information about. </p>"}}}, "GetVehicleStatusResponse": {"type": "structure", "members": {"campaigns": {"shape": "VehicleStatusList", "documentation": "<p> Lists information about the state of the vehicle with deployed campaigns. </p>"}, "nextToken": {"shape": "nextToken", "documentation": "<p> The token to retrieve the next set of results, or <code>null</code> if there are no more results. </p>"}}}, "IAMRoleArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:(aws[a-zA-Z0-9-]*):iam::(\\d{12})?:(role((\\u002F)|(\\u002F[\\u0021-\\u007F]+\\u002F))[\\w+=,.@-]+)"}, "IamRegistrationResponse": {"type": "structure", "required": ["roleArn", "registrationStatus"], "members": {"roleArn": {"shape": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role to register.</p>"}, "registrationStatus": {"shape": "RegistrationStatus", "documentation": "<p>The status of registering your IAM resource. The status can be one of <code>REGISTRATION_SUCCESS</code>, <code>REGISTRATION_PENDING</code>, <code>REGISTRATION_FAILURE</code>.</p>"}, "errorMessage": {"shape": "errorMessage", "documentation": "<p>A message associated with a registration error.</p>"}}, "documentation": "<p>Information about registering an Identity and Access Management (IAM) resource so Amazon Web Services IoT FleetWise edge agent software can transfer your vehicle data to Amazon Timestream.</p>"}, "IamResources": {"type": "structure", "required": ["roleArn"], "members": {"roleArn": {"shape": "IAMRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM resource that allows Amazon Web Services IoT FleetWise to send data to Amazon Timestream. For example, <code>arn:aws:iam::123456789012:role/SERVICE-ROLE-ARN</code>. </p>"}}, "documentation": "<p>The IAM resource that enables Amazon Web Services IoT FleetWise edge agent software to send data to Amazon Timestream. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/id_roles.html\">IAM roles</a> in the <i>Identity and Access Management User Guide</i>.</p>"}, "ImportDecoderManifestRequest": {"type": "structure", "required": ["name", "networkFileDefinitions"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the decoder manifest to import. </p>"}, "networkFileDefinitions": {"shape": "NetworkFileDefinitions", "documentation": "<p> The file to load into an Amazon Web Services account. </p>"}}}, "ImportDecoderManifestResponse": {"type": "structure", "required": ["name", "arn"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the imported decoder manifest. </p>"}, "arn": {"shape": "arn", "documentation": "<p> The Amazon Resource Name (ARN) of the decoder manifest that was imported. </p>"}}}, "ImportSignalCatalogRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "resourceName", "documentation": "<p>The name of the signal catalog to import.</p>"}, "description": {"shape": "description", "documentation": "<p> A brief description of the signal catalog. </p>"}, "vss": {"shape": "FormattedVss", "documentation": "<p>The contents of the Vehicle Signal Specification (VSS) configuration. VSS is a precise language used to describe and model signals in vehicle networks.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>Metadata that can be used to manage the signal catalog.</p>"}}}, "ImportSignalCatalogResponse": {"type": "structure", "required": ["name", "arn"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the imported signal catalog. </p>"}, "arn": {"shape": "arn", "documentation": "<p> The Amazon Resource Name (ARN) of the imported signal catalog.</p>"}}}, "InterfaceId": {"type": "string", "max": 50, "min": 1}, "InterfaceIds": {"type": "list", "member": {"shape": "InterfaceId"}, "max": 500, "min": 1}, "InternalServerException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "string"}, "retryAfterSeconds": {"shape": "RetryAfterSeconds", "documentation": "<p>The number of seconds to wait before retrying the command.</p>"}}, "documentation": "<p>The request couldn't be completed because the server temporarily failed.</p>", "exception": true, "fault": true}, "InvalidNetworkInterface": {"type": "structure", "members": {"interfaceId": {"shape": "InterfaceId", "documentation": "<p>The ID of the interface that isn't valid.</p>"}, "reason": {"shape": "NetworkInterfaceFailureReason", "documentation": "<p>A message about why the interface isn't valid. </p>"}}, "documentation": "<p>A reason a vehicle network interface isn't valid.</p>"}, "InvalidNetworkInterfaces": {"type": "list", "member": {"shape": "InvalidNetworkInterface"}}, "InvalidNodeException": {"type": "structure", "members": {"invalidNodes": {"shape": "Nodes", "documentation": "<p>The specified node type isn't valid.</p>"}, "reason": {"shape": "string", "documentation": "<p>The reason the node validation failed.</p>"}, "message": {"shape": "string"}}, "documentation": "<p>The specified node type doesn't match the expected node type for a node. You can specify the node type as branch, sensor, actuator, or attribute.</p>", "exception": true}, "InvalidSignal": {"type": "structure", "members": {"name": {"shape": "FullyQualifiedName", "documentation": "<p>The name of the signal that isn't valid.</p>"}, "reason": {"shape": "string", "documentation": "<p>A message about why the signal isn't valid.</p>"}}, "documentation": "<p>A reason that a signal isn't valid.</p>"}, "InvalidSignalDecoder": {"type": "structure", "members": {"name": {"shape": "FullyQualifiedName", "documentation": "<p>The name of a signal decoder that isn't valid.</p>"}, "reason": {"shape": "SignalDecoderFailureReason", "documentation": "<p>A message about why the signal decoder isn't valid.</p>"}}, "documentation": "<p>A reason that a signal decoder isn't valid.</p>"}, "InvalidSignalDecoders": {"type": "list", "member": {"shape": "InvalidSignalDecoder"}}, "InvalidSignals": {"type": "list", "member": {"shape": "InvalidSignal"}}, "InvalidSignalsException": {"type": "structure", "members": {"message": {"shape": "string"}, "invalidSignals": {"shape": "InvalidSignals", "documentation": "<p>The signals which caused the exception.</p>"}}, "documentation": "<p>The request couldn't be completed because it contains signals that aren't valid.</p>", "exception": true}, "LimitExceededException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "string"}, "resourceId": {"shape": "string", "documentation": "<p>The identifier of the resource that was exceeded.</p>"}, "resourceType": {"shape": "string", "documentation": "<p>The type of resource that was exceeded.</p>"}}, "documentation": "<p>A service quota was exceeded. </p>", "exception": true}, "ListCampaignsRequest": {"type": "structure", "members": {"nextToken": {"shape": "nextToken", "documentation": "<p>A pagination token for the next set of results.</p> <p>If the results of a search are large, only a portion of the results are returned, and a <code>nextToken</code> pagination token is returned in the response. To retrieve the next set of results, reissue the search request and include the returned token. When all results have been returned, the response does not contain a pagination token value. </p>"}, "maxResults": {"shape": "maxResults", "documentation": "<p> The maximum number of items to return, between 1 and 100, inclusive. </p>"}, "status": {"shape": "status", "documentation": "<p>Optional parameter to filter the results by the status of each created campaign in your account. The status can be one of: <code>CREATING</code>, <code>WAITING_FOR_APPROVAL</code>, <code>RUNNING</code>, or <code>SUSPENDED</code>.</p>"}}}, "ListCampaignsResponse": {"type": "structure", "members": {"campaignSummaries": {"shape": "campaignSummaries", "documentation": "<p> A summary of information about each campaign. </p>"}, "nextToken": {"shape": "nextToken", "documentation": "<p> The token to retrieve the next set of results, or <code>null</code> if there are no more results. </p>"}}}, "ListDecoderManifestNetworkInterfacesRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the decoder manifest to list information about. </p>"}, "nextToken": {"shape": "nextToken", "documentation": "<p>A pagination token for the next set of results.</p> <p>If the results of a search are large, only a portion of the results are returned, and a <code>nextToken</code> pagination token is returned in the response. To retrieve the next set of results, reissue the search request and include the returned token. When all results have been returned, the response does not contain a pagination token value. </p>"}, "maxResults": {"shape": "maxResults", "documentation": "<p> The maximum number of items to return, between 1 and 100, inclusive. </p>"}}}, "ListDecoderManifestNetworkInterfacesResponse": {"type": "structure", "members": {"networkInterfaces": {"shape": "NetworkInterfaces", "documentation": "<p> A list of information about network interfaces. </p>"}, "nextToken": {"shape": "nextToken", "documentation": "<p> The token to retrieve the next set of results, or <code>null</code> if there are no more results. </p>"}}}, "ListDecoderManifestSignalsRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the decoder manifest to list information about. </p>"}, "nextToken": {"shape": "nextToken", "documentation": "<p>A pagination token for the next set of results.</p> <p>If the results of a search are large, only a portion of the results are returned, and a <code>nextToken</code> pagination token is returned in the response. To retrieve the next set of results, reissue the search request and include the returned token. When all results have been returned, the response does not contain a pagination token value. </p>"}, "maxResults": {"shape": "maxResults", "documentation": "<p> The maximum number of items to return, between 1 and 100, inclusive. </p>"}}}, "ListDecoderManifestSignalsResponse": {"type": "structure", "members": {"signalDecoders": {"shape": "SignalDecoders", "documentation": "<p> Information about a list of signals to decode. </p>"}, "nextToken": {"shape": "nextToken", "documentation": "<p> The token to retrieve the next set of results, or <code>null</code> if there are no more results. </p>"}}}, "ListDecoderManifestsRequest": {"type": "structure", "members": {"modelManifestArn": {"shape": "arn", "documentation": "<p> The Amazon Resource Name (ARN) of a vehicle model (model manifest) associated with the decoder manifest. </p>"}, "nextToken": {"shape": "nextToken", "documentation": "<p>A pagination token for the next set of results.</p> <p>If the results of a search are large, only a portion of the results are returned, and a <code>nextToken</code> pagination token is returned in the response. To retrieve the next set of results, reissue the search request and include the returned token. When all results have been returned, the response does not contain a pagination token value. </p>"}, "maxResults": {"shape": "maxResults", "documentation": "<p> The maximum number of items to return, between 1 and 100, inclusive. </p>"}}}, "ListDecoderManifestsResponse": {"type": "structure", "members": {"summaries": {"shape": "decoderManifestSummaries", "documentation": "<p> A list of information about each decoder manifest. </p>"}, "nextToken": {"shape": "nextToken", "documentation": "<p> The token to retrieve the next set of results, or <code>null</code> if there are no more results. </p>"}}}, "ListFleetsForVehicleRequest": {"type": "structure", "required": ["vehicleName"], "members": {"vehicleName": {"shape": "vehicleName", "documentation": "<p> The ID of the vehicle to retrieve information about. </p>"}, "nextToken": {"shape": "nextToken", "documentation": "<p>A pagination token for the next set of results.</p> <p>If the results of a search are large, only a portion of the results are returned, and a <code>nextToken</code> pagination token is returned in the response. To retrieve the next set of results, reissue the search request and include the returned token. When all results have been returned, the response does not contain a pagination token value. </p>"}, "maxResults": {"shape": "maxResults", "documentation": "<p> The maximum number of items to return, between 1 and 100, inclusive. </p>"}}}, "ListFleetsForVehicleResponse": {"type": "structure", "members": {"fleets": {"shape": "fleets", "documentation": "<p> A list of fleet IDs that the vehicle is associated with. </p>"}, "nextToken": {"shape": "nextToken", "documentation": "<p> The token to retrieve the next set of results, or <code>null</code> if there are no more results. </p>"}}}, "ListFleetsRequest": {"type": "structure", "members": {"nextToken": {"shape": "nextToken", "documentation": "<p>A pagination token for the next set of results.</p> <p>If the results of a search are large, only a portion of the results are returned, and a <code>nextToken</code> pagination token is returned in the response. To retrieve the next set of results, reissue the search request and include the returned token. When all results have been returned, the response does not contain a pagination token value. </p>"}, "maxResults": {"shape": "maxResults", "documentation": "<p> The maximum number of items to return, between 1 and 100, inclusive. </p>"}}}, "ListFleetsResponse": {"type": "structure", "members": {"fleetSummaries": {"shape": "fleetSummaries", "documentation": "<p> A list of information for each fleet. </p>"}, "nextToken": {"shape": "nextToken", "documentation": "<p> The token to retrieve the next set of results, or <code>null</code> if there are no more results. </p>"}}}, "ListModelManifestNodesRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the vehicle model to list information about. </p>"}, "nextToken": {"shape": "nextToken", "documentation": "<p>A pagination token for the next set of results.</p> <p>If the results of a search are large, only a portion of the results are returned, and a <code>nextToken</code> pagination token is returned in the response. To retrieve the next set of results, reissue the search request and include the returned token. When all results have been returned, the response does not contain a pagination token value. </p>"}, "maxResults": {"shape": "maxResults", "documentation": "<p> The maximum number of items to return, between 1 and 100, inclusive. </p>"}}}, "ListModelManifestNodesResponse": {"type": "structure", "members": {"nodes": {"shape": "Nodes", "documentation": "<p> A list of information about nodes. </p>"}, "nextToken": {"shape": "nextToken", "documentation": "<p> The token to retrieve the next set of results, or <code>null</code> if there are no more results. </p>"}}}, "ListModelManifestsRequest": {"type": "structure", "members": {"signalCatalogArn": {"shape": "arn", "documentation": "<p> The ARN of a signal catalog. If you specify a signal catalog, only the vehicle models associated with it are returned.</p>"}, "nextToken": {"shape": "nextToken", "documentation": "<p>A pagination token for the next set of results.</p> <p>If the results of a search are large, only a portion of the results are returned, and a <code>nextToken</code> pagination token is returned in the response. To retrieve the next set of results, reissue the search request and include the returned token. When all results have been returned, the response does not contain a pagination token value. </p>"}, "maxResults": {"shape": "maxResults", "documentation": "<p> The maximum number of items to return, between 1 and 100, inclusive. </p>"}}}, "ListModelManifestsResponse": {"type": "structure", "members": {"summaries": {"shape": "modelManifestSummaries", "documentation": "<p> A list of information about vehicle models.</p>"}, "nextToken": {"shape": "nextToken", "documentation": "<p> The token to retrieve the next set of results, or <code>null</code> if there are no more results. </p>"}}}, "ListSignalCatalogNodesRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the signal catalog to list information about. </p>"}, "nextToken": {"shape": "nextToken", "documentation": "<p>A pagination token for the next set of results.</p> <p>If the results of a search are large, only a portion of the results are returned, and a <code>nextToken</code> pagination token is returned in the response. To retrieve the next set of results, reissue the search request and include the returned token. When all results have been returned, the response does not contain a pagination token value. </p>"}, "maxResults": {"shape": "maxResults", "documentation": "<p> The maximum number of items to return, between 1 and 100, inclusive. </p>"}}}, "ListSignalCatalogNodesResponse": {"type": "structure", "members": {"nodes": {"shape": "Nodes", "documentation": "<p> A list of information about nodes. </p>"}, "nextToken": {"shape": "nextToken", "documentation": "<p> The token to retrieve the next set of results, or <code>null</code> if there are no more results. </p>"}}}, "ListSignalCatalogsRequest": {"type": "structure", "members": {"nextToken": {"shape": "nextToken", "documentation": "<p>A pagination token for the next set of results.</p> <p>If the results of a search are large, only a portion of the results are returned, and a <code>nextToken</code> pagination token is returned in the response. To retrieve the next set of results, reissue the search request and include the returned token. When all results have been returned, the response does not contain a pagination token value. </p>"}, "maxResults": {"shape": "maxResults", "documentation": "<p> The maximum number of items to return, between 1 and 100, inclusive. </p>"}}}, "ListSignalCatalogsResponse": {"type": "structure", "members": {"summaries": {"shape": "signalCatalogSummaries", "documentation": "<p> A list of information about each signal catalog. </p>"}, "nextToken": {"shape": "nextToken", "documentation": "<p> The token to retrieve the next set of results, or <code>null</code> if there are no more results. </p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>The ARN of the resource.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "TagList", "documentation": "<p>The list of tags assigned to the resource.</p>"}}}, "ListVehiclesInFleetRequest": {"type": "structure", "required": ["fleetId"], "members": {"fleetId": {"shape": "fleetId", "documentation": "<p> The ID of a fleet. </p>"}, "nextToken": {"shape": "nextToken", "documentation": "<p>A pagination token for the next set of results.</p> <p>If the results of a search are large, only a portion of the results are returned, and a <code>nextToken</code> pagination token is returned in the response. To retrieve the next set of results, reissue the search request and include the returned token. When all results have been returned, the response does not contain a pagination token value. </p>"}, "maxResults": {"shape": "maxResults", "documentation": "<p> The maximum number of items to return, between 1 and 100, inclusive. </p>"}}}, "ListVehiclesInFleetResponse": {"type": "structure", "members": {"vehicles": {"shape": "vehicles", "documentation": "<p> A list of vehicles associated with the fleet. </p>"}, "nextToken": {"shape": "nextToken", "documentation": "<p> The token to retrieve the next set of results, or <code>null</code> if there are no more results. </p>"}}}, "ListVehiclesRequest": {"type": "structure", "members": {"modelManifestArn": {"shape": "arn", "documentation": "<p> The Amazon Resource Name (ARN) of a vehicle model (model manifest). You can use this optional parameter to list only the vehicles created from a certain vehicle model. </p>"}, "nextToken": {"shape": "nextToken", "documentation": "<p>A pagination token for the next set of results.</p> <p>If the results of a search are large, only a portion of the results are returned, and a <code>nextToken</code> pagination token is returned in the response. To retrieve the next set of results, reissue the search request and include the returned token. When all results have been returned, the response does not contain a pagination token value. </p>"}, "maxResults": {"shape": "listVehiclesMaxResults", "documentation": "<p> The maximum number of items to return, between 1 and 100, inclusive. </p>"}}}, "ListVehiclesResponse": {"type": "structure", "members": {"vehicleSummaries": {"shape": "vehicleSummaries", "documentation": "<p> A list of vehicles and information about them. </p>"}, "nextToken": {"shape": "nextToken", "documentation": "<p> The token to retrieve the next set of results, or <code>null</code> if there are no more results. </p>"}}}, "LogType": {"type": "string", "enum": ["OFF", "ERROR"]}, "ManifestStatus": {"type": "string", "enum": ["ACTIVE", "DRAFT"]}, "ModelManifestSummary": {"type": "structure", "required": ["creationTime", "lastModificationTime"], "members": {"name": {"shape": "string", "documentation": "<p>The name of the vehicle model.</p>"}, "arn": {"shape": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the vehicle model.</p>"}, "signalCatalogArn": {"shape": "arn", "documentation": "<p>The ARN of the signal catalog associated with the vehicle model.</p>"}, "description": {"shape": "description", "documentation": "<p>A brief description of the vehicle model.</p>"}, "status": {"shape": "ManifestStatus", "documentation": "<p>The state of the vehicle model. If the status is <code>ACTIVE</code>, the vehicle model can't be edited. If the status is <code>DRAFT</code>, you can edit the vehicle model.</p>"}, "creationTime": {"shape": "timestamp", "documentation": "<p>The time the vehicle model was created, in seconds since epoch (January 1, 1970 at midnight UTC time).</p>"}, "lastModificationTime": {"shape": "timestamp", "documentation": "<p>The time the vehicle model was last updated, in seconds since epoch (January 1, 1970 at midnight UTC time).</p>"}}, "documentation": "<p>Information about a vehicle model (model manifest). You can use the API operation to return this information about multiple vehicle models.</p>"}, "ModelSignalsMap": {"type": "map", "key": {"shape": "string"}, "value": {"shape": "string"}}, "NetworkFileBlob": {"type": "blob"}, "NetworkFileDefinition": {"type": "structure", "members": {"canDbc": {"shape": "CanDbcDefinition", "documentation": "<p>Information, including CAN DBC files, about the configurations used to create a decoder manifest.</p>"}}, "documentation": "<p>Specifications for defining a vehicle network.</p>", "union": true}, "NetworkFileDefinitions": {"type": "list", "member": {"shape": "NetworkFileDefinition"}}, "NetworkFilesList": {"type": "list", "member": {"shape": "NetworkFileBlob"}, "max": 5, "min": 1}, "NetworkInterface": {"type": "structure", "required": ["interfaceId", "type"], "members": {"interfaceId": {"shape": "InterfaceId", "documentation": "<p>The ID of the network interface.</p>"}, "type": {"shape": "NetworkInterfaceType", "documentation": "<p>The network protocol for the vehicle. For example, <code>CAN_SIGNAL</code> specifies a protocol that defines how data is communicated between electronic control units (ECUs). <code>OBD_SIGNAL</code> specifies a protocol that defines how self-diagnostic data is communicated between ECUs.</p>"}, "canInterface": {"shape": "CanInterface", "documentation": "<p>Information about a network interface specified by the Controller Area Network (CAN) protocol.</p>"}, "obdInterface": {"shape": "ObdInterface", "documentation": "<p>Information about a network interface specified by the On-board diagnostic (OBD) II protocol.</p>"}}, "documentation": "<p>Represents a node and its specifications in an in-vehicle communication network. All signal decoders must be associated with a network node. </p> <p> To return this information about all the network interfaces specified in a decoder manifest, use the API operation.</p>"}, "NetworkInterfaceFailureReason": {"type": "string", "enum": ["DUPLICATE_NETWORK_INTERFACE", "CONFLICTING_NETWORK_INTERFACE", "NETWORK_INTERFACE_TO_ADD_ALREADY_EXISTS", "CAN_NETWORK_INTERFACE_INFO_IS_NULL", "OBD_NETWORK_INTERFACE_INFO_IS_NULL", "NETWORK_INTERFACE_TO_REMOVE_ASSOCIATED_WITH_SIGNALS"]}, "NetworkInterfaceType": {"type": "string", "enum": ["CAN_INTERFACE", "OBD_INTERFACE"]}, "NetworkInterfaces": {"type": "list", "member": {"shape": "NetworkInterface"}, "max": 500, "min": 1}, "Node": {"type": "structure", "members": {"branch": {"shape": "Branch", "documentation": "<p>Information about a node specified as a branch.</p> <note> <p>A group of signals that are defined in a hierarchical structure.</p> </note>"}, "sensor": {"shape": "Sensor"}, "actuator": {"shape": "Actuator", "documentation": "<p>Information about a node specified as an actuator.</p> <note> <p>An actuator is a digital representation of a vehicle device.</p> </note>"}, "attribute": {"shape": "Attribute", "documentation": "<p>Information about a node specified as an attribute.</p> <note> <p>An attribute represents static information about a vehicle.</p> </note>"}}, "documentation": "<p>A general abstraction of a signal. A node can be specified as an actuator, attribute, branch, or sensor.</p>", "union": true}, "NodeCounts": {"type": "structure", "members": {"totalNodes": {"shape": "number", "documentation": "<p>The total number of nodes in a vehicle network.</p>"}, "totalBranches": {"shape": "number", "documentation": "<p>The total number of nodes in a vehicle network that represent branches.</p>"}, "totalSensors": {"shape": "number", "documentation": "<p>The total number of nodes in a vehicle network that represent sensors.</p>"}, "totalAttributes": {"shape": "number", "documentation": "<p>The total number of nodes in a vehicle network that represent attributes.</p>"}, "totalActuators": {"shape": "number", "documentation": "<p>The total number of nodes in a vehicle network that represent actuators.</p>"}}, "documentation": "<p>Information about the number of nodes and node types in a vehicle network.</p>"}, "NodeDataType": {"type": "string", "enum": ["INT8", "UINT8", "INT16", "UINT16", "INT32", "UINT32", "INT64", "UINT64", "BOOLEAN", "FLOAT", "DOUBLE", "STRING", "UNIX_TIMESTAMP", "INT8_ARRAY", "UINT8_ARRAY", "INT16_ARRAY", "UINT16_ARRAY", "INT32_ARRAY", "UINT32_ARRAY", "INT64_ARRAY", "UINT64_ARRAY", "BOOLEAN_ARRAY", "FLOAT_ARRAY", "DOUBLE_ARRAY", "STRING_ARRAY", "UNIX_TIMESTAMP_ARRAY", "UNKNOWN"]}, "NodePath": {"type": "string", "max": 150, "min": 1, "pattern": "[a-zA-Z0-9_.]+"}, "NodePaths": {"type": "list", "member": {"shape": "NodePath"}, "max": 500, "min": 1}, "Nodes": {"type": "list", "member": {"shape": "Node"}, "max": 500, "min": 0}, "ObdBitmaskLength": {"type": "integer", "box": true, "max": 8, "min": 1}, "ObdByteLength": {"type": "integer", "box": true, "max": 8, "min": 1}, "ObdInterface": {"type": "structure", "required": ["name", "requestMessageId"], "members": {"name": {"shape": "ObdInterfaceName", "documentation": "<p>The name of the interface.</p>"}, "requestMessageId": {"shape": "nonNegativeInteger", "documentation": "<p>The ID of the message requesting vehicle data.</p>"}, "obdStandard": {"shape": "ObdStandard", "documentation": "<p>The standard OBD II PID.</p>"}, "pidRequestIntervalSeconds": {"shape": "nonNegativeInteger", "documentation": "<p>The maximum number message requests per second.</p>"}, "dtcRequestIntervalSeconds": {"shape": "nonNegativeInteger", "documentation": "<p>The maximum number message requests per diagnostic trouble code per second.</p>"}, "useExtendedIds": {"shape": "boolean", "documentation": "<p>Whether to use extended IDs in the message.</p>"}, "hasTransmissionEcu": {"shape": "boolean", "documentation": "<p>Whether the vehicle has a transmission control module (TCM).</p>"}}, "documentation": "<p>A network interface that specifies the On-board diagnostic (OBD) II network protocol.</p>"}, "ObdInterfaceName": {"type": "string", "max": 100, "min": 1}, "ObdSignal": {"type": "structure", "required": ["pidResponseLength", "serviceMode", "pid", "scaling", "offset", "startByte", "byteLength"], "members": {"pidResponseLength": {"shape": "positiveInteger", "documentation": "<p>The length of the requested data.</p>"}, "serviceMode": {"shape": "nonNegativeInteger", "documentation": "<p>The mode of operation (diagnostic service) in a message.</p>"}, "pid": {"shape": "nonNegativeInteger", "documentation": "<p>The diagnostic code used to request data from a vehicle for this signal.</p>"}, "scaling": {"shape": "double", "documentation": "<p>A multiplier used to decode the message.</p>"}, "offset": {"shape": "double", "documentation": "<p>The offset used to calculate the signal value. Combined with scaling, the calculation is <code>value = raw_value * scaling + offset</code>.</p>"}, "startByte": {"shape": "nonNegativeInteger", "documentation": "<p>Indicates the beginning of the message.</p>"}, "byteLength": {"shape": "ObdByteLength", "documentation": "<p>The length of a message.</p>"}, "bitRightShift": {"shape": "nonNegativeInteger", "documentation": "<p>The number of positions to shift bits in the message.</p>"}, "bitMaskLength": {"shape": "ObdBitmaskLength", "documentation": "<p>The number of bits to mask in a message.</p>"}}, "documentation": "<p>Information about signal messages using the on-board diagnostics (OBD) II protocol in a vehicle.</p>"}, "ObdStandard": {"type": "string", "max": 50, "min": 1}, "Prefix": {"type": "string", "max": 512, "min": 1, "pattern": "[a-zA-Z0-9-_:./!*'()]+"}, "ProtocolName": {"type": "string", "max": 50, "min": 1}, "ProtocolVersion": {"type": "string", "max": 50, "min": 1}, "PutEncryptionConfigurationRequest": {"type": "structure", "required": ["encryptionType"], "members": {"kmsKeyId": {"shape": "String", "documentation": "<p>The ID of the KMS key that is used for encryption.</p>"}, "encryptionType": {"shape": "EncryptionType", "documentation": "<p>The type of encryption. Choose <code>KMS_BASED_ENCRYPTION</code> to use a KMS key or <code>FLEETWISE_DEFAULT_ENCRYPTION</code> to use an Amazon Web Services managed key.</p>"}}}, "PutEncryptionConfigurationResponse": {"type": "structure", "required": ["encryptionStatus", "encryptionType"], "members": {"kmsKeyId": {"shape": "String", "documentation": "<p>The ID of the KMS key that is used for encryption.</p>"}, "encryptionStatus": {"shape": "EncryptionStatus", "documentation": "<p>The encryption status.</p>"}, "encryptionType": {"shape": "EncryptionType", "documentation": "<p>The type of encryption. Set to <code>KMS_BASED_ENCRYPTION</code> to use an KMS key that you own and manage. Set to <code>FLEETWISE_DEFAULT_ENCRYPTION</code> to use an Amazon Web Services managed key that is owned by the Amazon Web Services IoT FleetWise service account.</p>"}}}, "PutLoggingOptionsRequest": {"type": "structure", "required": ["cloudWatchLogDelivery"], "members": {"cloudWatchLogDelivery": {"shape": "CloudWatchLogDeliveryOptions", "documentation": "<p>Creates or updates the log delivery option to Amazon CloudWatch Logs.</p>"}}}, "PutLoggingOptionsResponse": {"type": "structure", "members": {}}, "RegisterAccountRequest": {"type": "structure", "members": {"timestreamResources": {"shape": "TimestreamResources", "deprecated": true, "deprecatedMessage": "Amazon Timestream metadata is now passed in the CreateCampaign API."}, "iamResources": {"shape": "IamResources", "documentation": "<p>The IAM resource that allows Amazon Web Services IoT FleetWise to send data to Amazon Timestream.</p>", "deprecated": true, "deprecatedMessage": "iamResources is no longer used or needed as input"}}}, "RegisterAccountResponse": {"type": "structure", "required": ["registerAccountStatus", "iamResources", "creationTime", "lastModificationTime"], "members": {"registerAccountStatus": {"shape": "RegistrationStatus", "documentation": "<p> The status of registering your Amazon Web Services account, IAM role, and Timestream resources. </p>"}, "timestreamResources": {"shape": "TimestreamResources"}, "iamResources": {"shape": "IamResources", "documentation": "<p> The registered IAM resource that allows Amazon Web Services IoT FleetWise to send data to Amazon Timestream. </p>"}, "creationTime": {"shape": "timestamp", "documentation": "<p> The time the account was registered, in seconds since epoch (January 1, 1970 at midnight UTC time). </p>"}, "lastModificationTime": {"shape": "timestamp", "documentation": "<p> The time this registration was last updated, in seconds since epoch (January 1, 1970 at midnight UTC time). </p>"}}}, "RegistrationStatus": {"type": "string", "enum": ["REGISTRATION_PENDING", "REGISTRATION_SUCCESS", "REGISTRATION_FAILURE"]}, "ResourceNotFoundException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "string"}, "resourceId": {"shape": "string", "documentation": "<p>The identifier of the resource that wasn't found.</p>"}, "resourceType": {"shape": "string", "documentation": "<p>The type of resource that wasn't found.</p>"}}, "documentation": "<p>The resource wasn't found.</p>", "exception": true}, "RetryAfterSeconds": {"type": "integer"}, "S3BucketArn": {"type": "string", "max": 100, "min": 16, "pattern": "arn:(aws[a-zA-Z0-9-]*):s3:::.+"}, "S3Config": {"type": "structure", "required": ["bucketArn"], "members": {"bucketArn": {"shape": "S3BucketArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon S3 bucket.</p>"}, "dataFormat": {"shape": "DataFormat", "documentation": "<p>Specify the format that files are saved in the Amazon S3 bucket. You can save files in an Apache Parquet or JSON format.</p> <ul> <li> <p>Parquet - Store data in a columnar storage file format. Parquet is optimal for fast data retrieval and can reduce costs. This option is selected by default.</p> </li> <li> <p>JSON - Store data in a standard text-based JSON file format.</p> </li> </ul>"}, "storageCompressionFormat": {"shape": "StorageCompressionFormat", "documentation": "<p>By default, stored data is compressed as a .gzip file. Compressed files have a reduced file size, which can optimize the cost of data storage.</p>"}, "prefix": {"shape": "Prefix", "documentation": "<p>(Optional) Enter an S3 bucket prefix. The prefix is the string of characters after the bucket name and before the object name. You can use the prefix to organize data stored in Amazon S3 buckets. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/using-prefixes.html\">Organizing objects using prefixes</a> in the <i>Amazon Simple Storage Service User Guide</i>.</p> <p>By default, Amazon Web Services IoT FleetWise sets the prefix <code>processed-data/year=YY/month=MM/date=DD/hour=HH/</code> (in UTC) to data it delivers to Amazon S3. You can enter a prefix to append it to this default prefix. For example, if you enter the prefix <code>vehicles</code>, the prefix will be <code>vehicles/processed-data/year=YY/month=MM/date=DD/hour=HH/</code>.</p>"}}, "documentation": "<p>The Amazon S3 bucket where the Amazon Web Services IoT FleetWise campaign sends data. Amazon S3 is an object storage service that stores data as objects within buckets. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/creating-buckets-s3.html\">Creating, configuring, and working with Amazon S3 buckets</a> in the <i>Amazon Simple Storage Service User Guide</i>.</p>"}, "Sensor": {"type": "structure", "required": ["fullyQualifiedName", "dataType"], "members": {"fullyQualifiedName": {"shape": "string", "documentation": "<p>The fully qualified name of the sensor. For example, the fully qualified name of a sensor might be <code>Vehicle.Body.Engine.Battery</code>.</p>"}, "dataType": {"shape": "NodeDataType", "documentation": "<p>The specified data type of the sensor. </p>"}, "description": {"shape": "description", "documentation": "<p>A brief description of a sensor.</p>"}, "unit": {"shape": "string", "documentation": "<p>The scientific unit of measurement for data collected by the sensor.</p>"}, "allowedValues": {"shape": "listOfStrings", "documentation": "<p>A list of possible values a sensor can take.</p>"}, "min": {"shape": "double", "documentation": "<p>The specified possible minimum value of the sensor.</p>"}, "max": {"shape": "double", "documentation": "<p>The specified possible maximum value of the sensor.</p>"}, "deprecationMessage": {"shape": "message", "documentation": "<p>The deprecation message for the node or the branch that was moved or deleted.</p>"}, "comment": {"shape": "message", "documentation": "<p>A comment in addition to the description.</p>"}}, "documentation": "<p>An input component that reports the environmental condition of a vehicle.</p> <note> <p>You can collect data about fluid levels, temperatures, vibrations, or battery voltage from sensors.</p> </note>"}, "SignalCatalogSummary": {"type": "structure", "members": {"name": {"shape": "string", "documentation": "<p>The name of the signal catalog.</p>"}, "arn": {"shape": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the signal catalog.</p>"}, "creationTime": {"shape": "timestamp", "documentation": "<p>The time the signal catalog was created in seconds since epoch (January 1, 1970 at midnight UTC time). </p>"}, "lastModificationTime": {"shape": "timestamp", "documentation": "<p>The time the signal catalog was last updated in seconds since epoch (January 1, 1970 at midnight UTC time). </p>"}}, "documentation": "<p>Information about a collection of standardized signals, which can be attributes, branches, sensors, or actuators.</p>"}, "SignalDecoder": {"type": "structure", "required": ["fullyQualifiedName", "type", "interfaceId"], "members": {"fullyQualifiedName": {"shape": "FullyQualifiedName", "documentation": "<p>The fully qualified name of a signal decoder as defined in a vehicle model.</p>"}, "type": {"shape": "SignalDecoderType", "documentation": "<p>The network protocol for the vehicle. For example, <code>CAN_SIGNAL</code> specifies a protocol that defines how data is communicated between electronic control units (ECUs). <code>OBD_SIGNAL</code> specifies a protocol that defines how self-diagnostic data is communicated between ECUs.</p>"}, "interfaceId": {"shape": "InterfaceId", "documentation": "<p>The ID of a network interface that specifies what network protocol a vehicle follows.</p>"}, "canSignal": {"shape": "CanSignal", "documentation": "<p>Information about signal decoder using the Controller Area Network (CAN) protocol.</p>"}, "obdSignal": {"shape": "ObdSignal", "documentation": "<p>Information about signal decoder using the On-board diagnostic (OBD) II protocol.</p>"}}, "documentation": "<p>Information about a signal decoder.</p>"}, "SignalDecoderFailureReason": {"type": "string", "enum": ["DUPLICATE_SIGNAL", "CONFLICTING_SIGNAL", "SIGNAL_TO_ADD_ALREADY_EXISTS", "SIGNAL_NOT_ASSOCIATED_WITH_NETWORK_INTERFACE", "NETWORK_INTERFACE_TYPE_INCOMPATIBLE_WITH_SIGNAL_DECODER_TYPE", "SIGNAL_NOT_IN_MODEL", "CAN_SIGNAL_INFO_IS_NULL", "OBD_SIGNAL_INFO_IS_NULL", "NO_DECODER_INFO_FOR_SIGNAL_IN_MODEL"]}, "SignalDecoderType": {"type": "string", "enum": ["CAN_SIGNAL", "OBD_SIGNAL"]}, "SignalDecoders": {"type": "list", "member": {"shape": "SignalDecoder"}, "max": 500, "min": 1}, "SignalInformation": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "wildcardSignalName", "documentation": "<p>The name of the signal.</p>"}, "maxSampleCount": {"shape": "maxSampleCount", "documentation": "<p>The maximum number of samples to collect.</p>"}, "minimumSamplingIntervalMs": {"shape": "uint32", "documentation": "<p>The minimum duration of time (in milliseconds) between two triggering events to collect data.</p> <note> <p>If a signal changes often, you might want to collect data at a slower rate.</p> </note>"}}, "documentation": "<p>Information about a signal.</p>"}, "SignalInformationList": {"type": "list", "member": {"shape": "SignalInformation"}, "max": 1000, "min": 0}, "SpoolingMode": {"type": "string", "enum": ["OFF", "TO_DISK"]}, "StorageCompressionFormat": {"type": "string", "enum": ["NONE", "GZIP"]}, "String": {"type": "string"}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>The tag's key.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The tag's value.</p>"}}, "documentation": "<p>A set of key/value pairs that are used to manage the resource.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 0}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "max": 50, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["ResourceARN", "Tags"], "members": {"ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>The ARN of the resource.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The new or modified tags for the resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "string"}, "quotaCode": {"shape": "string", "documentation": "<p>The quota identifier of the applied throttling rules for this request.</p>"}, "serviceCode": {"shape": "string", "documentation": "<p>The code for the service that couldn't be completed due to throttling.</p>"}, "retryAfterSeconds": {"shape": "RetryAfterSeconds", "documentation": "<p>The number of seconds to wait before retrying the command.</p>"}}, "documentation": "<p>The request couldn't be completed due to throttling.</p>", "exception": true}, "TimeBasedCollectionScheme": {"type": "structure", "required": ["periodMs"], "members": {"periodMs": {"shape": "collectionPeriodMs", "documentation": "<p>The time period (in milliseconds) to decide how often to collect data. For example, if the time period is <code>60000</code>, the Edge Agent software collects data once every minute.</p>"}}, "documentation": "<p>Information about a collection scheme that uses a time period to decide how often to collect data.</p>"}, "TimestreamConfig": {"type": "structure", "required": ["timestreamTableArn", "executionRoleArn"], "members": {"timestreamTableArn": {"shape": "TimestreamTableArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon Timestream table.</p>"}, "executionRoleArn": {"shape": "IAMRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the task execution role that grants Amazon Web Services IoT FleetWise permission to deliver data to the Amazon Timestream table.</p>"}}, "documentation": "<p>The Amazon Timestream table where the Amazon Web Services IoT FleetWise campaign sends data. Timestream stores and organizes data to optimize query processing time and to reduce storage costs. For more information, see <a href=\"https://docs.aws.amazon.com/timestream/latest/developerguide/data-modeling.html\">Data modeling</a> in the <i>Amazon Timestream Developer Guide</i>.</p>"}, "TimestreamDatabaseName": {"type": "string", "max": 255, "min": 3, "pattern": "[a-zA-Z0-9_.-]+"}, "TimestreamRegistrationResponse": {"type": "structure", "required": ["timestreamDatabaseName", "timestreamTableName", "registrationStatus"], "members": {"timestreamDatabaseName": {"shape": "TimestreamDatabaseName", "documentation": "<p>The name of the Timestream database.</p>"}, "timestreamTableName": {"shape": "TimestreamTableName", "documentation": "<p>The name of the Timestream database table.</p>"}, "timestreamDatabaseArn": {"shape": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the Timestream database.</p>"}, "timestreamTableArn": {"shape": "arn", "documentation": "<p>The ARN of the Timestream database table.</p>"}, "registrationStatus": {"shape": "RegistrationStatus", "documentation": "<p>The status of registering your Amazon Timestream resources. The status can be one of <code>REGISTRATION_SUCCESS</code>, <code>REGISTRATION_PENDING</code>, <code>REGISTRATION_FAILURE</code>.</p>"}, "errorMessage": {"shape": "errorMessage", "documentation": "<p>A message associated with a registration error.</p>"}}, "documentation": "<p>Information about the registered Amazon Timestream resources or errors, if any.</p>"}, "TimestreamResources": {"type": "structure", "required": ["timestreamDatabaseName", "timestreamTableName"], "members": {"timestreamDatabaseName": {"shape": "TimestreamDatabaseName", "documentation": "<p>The name of the registered Amazon Timestream database.</p>"}, "timestreamTableName": {"shape": "TimestreamTableName", "documentation": "<p>The name of the registered Amazon Timestream database table.</p>"}}, "documentation": "<p>The registered Amazon Timestream resources that Amazon Web Services IoT FleetWise edge agent software can transfer your vehicle data to.</p>"}, "TimestreamTableArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:(aws[a-zA-Z0-9-]*):timestream:[a-zA-Z0-9-]+:[0-9]{12}:database/[a-zA-Z0-9_.-]+/table/[a-zA-Z0-9_.-]+"}, "TimestreamTableName": {"type": "string", "max": 255, "min": 3, "pattern": "[a-zA-Z0-9_.-]+"}, "TriggerMode": {"type": "string", "enum": ["ALWAYS", "RISING_EDGE"]}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceARN", "TagKeys"], "members": {"ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>The ARN of the resource.</p>"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>A list of the keys of the tags to be removed from the resource.</p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateCampaignAction": {"type": "string", "enum": ["APPROVE", "SUSPEND", "RESUME", "UPDATE"]}, "UpdateCampaignRequest": {"type": "structure", "required": ["name", "action"], "members": {"name": {"shape": "campaignName", "documentation": "<p> The name of the campaign to update. </p>"}, "description": {"shape": "description", "documentation": "<p>The description of the campaign.</p>"}, "dataExtraDimensions": {"shape": "DataExtraDimensionNodePathList", "documentation": "<p> A list of vehicle attributes to associate with a signal. </p> <p>Default: An empty array</p>"}, "action": {"shape": "UpdateCampaignAction", "documentation": "<p> Specifies how to update a campaign. The action can be one of the following:</p> <ul> <li> <p> <code>APPROVE</code> - To approve delivering a data collection scheme to vehicles. </p> </li> <li> <p> <code>SUSPEND</code> - To suspend collecting signal data. The campaign is deleted from vehicles and all vehicles in the suspended campaign will stop sending data.</p> </li> <li> <p> <code>RESUME</code> - To reactivate the <code>SUSPEND</code> campaign. The campaign is redeployed to all vehicles and the vehicles will resume sending data.</p> </li> <li> <p> <code>UPDATE</code> - To update a campaign. </p> </li> </ul>"}}}, "UpdateCampaignResponse": {"type": "structure", "members": {"arn": {"shape": "arn", "documentation": "<p> The Amazon Resource Name (ARN) of the campaign. </p>"}, "name": {"shape": "campaignName", "documentation": "<p>The name of the updated campaign.</p>"}, "status": {"shape": "CampaignStatus", "documentation": "<p>The state of a campaign. The status can be one of:</p> <ul> <li> <p> <code>CREATING</code> - Amazon Web Services IoT FleetWise is processing your request to create the campaign. </p> </li> <li> <p> <code>WAITING_FOR_APPROVAL</code> - After a campaign is created, it enters the <code>WAITING_FOR_APPROVAL</code> state. To allow Amazon Web Services IoT FleetWise to deploy the campaign to the target vehicle or fleet, use the API operation to approve the campaign. </p> </li> <li> <p> <code>RUNNING</code> - The campaign is active. </p> </li> <li> <p> <code>SUSPENDED</code> - The campaign is suspended. To resume the campaign, use the API operation. </p> </li> </ul>"}}}, "UpdateDecoderManifestRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the decoder manifest to update.</p>"}, "description": {"shape": "description", "documentation": "<p> A brief description of the decoder manifest to update. </p>"}, "signalDecodersToAdd": {"shape": "SignalDecoders", "documentation": "<p> A list of information about decoding additional signals to add to the decoder manifest. </p>"}, "signalDecodersToUpdate": {"shape": "SignalDecoders", "documentation": "<p> A list of updated information about decoding signals to update in the decoder manifest. </p>"}, "signalDecodersToRemove": {"shape": "Fqns", "documentation": "<p> A list of signal decoders to remove from the decoder manifest. </p>"}, "networkInterfacesToAdd": {"shape": "NetworkInterfaces", "documentation": "<p> A list of information about the network interfaces to add to the decoder manifest. </p>"}, "networkInterfacesToUpdate": {"shape": "NetworkInterfaces", "documentation": "<p> A list of information about the network interfaces to update in the decoder manifest. </p>"}, "networkInterfacesToRemove": {"shape": "InterfaceIds", "documentation": "<p> A list of network interfaces to remove from the decoder manifest.</p>"}, "status": {"shape": "ManifestStatus", "documentation": "<p> The state of the decoder manifest. If the status is <code>ACTIVE</code>, the decoder manifest can't be edited. If the status is <code>DRAFT</code>, you can edit the decoder manifest. </p>"}}}, "UpdateDecoderManifestResponse": {"type": "structure", "required": ["name", "arn"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the updated decoder manifest. </p>"}, "arn": {"shape": "arn", "documentation": "<p> The Amazon Resource Name (ARN) of the updated decoder manifest. </p>"}}}, "UpdateFleetRequest": {"type": "structure", "required": ["fleetId"], "members": {"fleetId": {"shape": "fleetId", "documentation": "<p> The ID of the fleet to update. </p>"}, "description": {"shape": "description", "documentation": "<p> An updated description of the fleet. </p>"}}}, "UpdateFleetResponse": {"type": "structure", "members": {"id": {"shape": "fleetId", "documentation": "<p>The ID of the updated fleet.</p>"}, "arn": {"shape": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the updated fleet.</p>"}}}, "UpdateMode": {"type": "string", "enum": ["Overwrite", "<PERSON><PERSON>"]}, "UpdateModelManifestRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the vehicle model to update. </p>"}, "description": {"shape": "description", "documentation": "<p> A brief description of the vehicle model. </p>"}, "nodesToAdd": {"shape": "NodePaths", "documentation": "<p> A list of <code>fullyQualifiedName</code> of nodes, which are a general abstraction of signals, to add to the vehicle model. </p>"}, "nodesToRemove": {"shape": "NodePaths", "documentation": "<p> A list of <code>fullyQualifiedName</code> of nodes, which are a general abstraction of signals, to remove from the vehicle model. </p>"}, "status": {"shape": "ManifestStatus", "documentation": "<p> The state of the vehicle model. If the status is <code>ACTIVE</code>, the vehicle model can't be edited. If the status is <code>DRAFT</code>, you can edit the vehicle model. </p>"}}}, "UpdateModelManifestResponse": {"type": "structure", "required": ["name", "arn"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the updated vehicle model. </p>"}, "arn": {"shape": "arn", "documentation": "<p> The Amazon Resource Name (ARN) of the updated vehicle model. </p>"}}}, "UpdateSignalCatalogRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the signal catalog to update. </p>"}, "description": {"shape": "description", "documentation": "<p> A brief description of the signal catalog to update.</p>"}, "nodesToAdd": {"shape": "Nodes", "documentation": "<p> A list of information about nodes to add to the signal catalog. </p>"}, "nodesToUpdate": {"shape": "Nodes", "documentation": "<p> A list of information about nodes to update in the signal catalog. </p>"}, "nodesToRemove": {"shape": "NodePaths", "documentation": "<p> A list of <code>fullyQualifiedName</code> of nodes to remove from the signal catalog. </p>"}}}, "UpdateSignalCatalogResponse": {"type": "structure", "required": ["name", "arn"], "members": {"name": {"shape": "resourceName", "documentation": "<p> The name of the updated signal catalog. </p>"}, "arn": {"shape": "arn", "documentation": "<p> The ARN of the updated signal catalog. </p>"}}}, "UpdateVehicleError": {"type": "structure", "members": {"vehicleName": {"shape": "vehicleName", "documentation": "<p>The ID of the vehicle with the error.</p>"}, "code": {"shape": "number", "documentation": "<p>The relevant HTTP error code (400+).</p>"}, "message": {"shape": "string", "documentation": "<p>A message associated with the error.</p>"}}, "documentation": "<p>An HTTP error resulting from updating the description for a vehicle.</p>"}, "UpdateVehicleRequest": {"type": "structure", "required": ["vehicleName"], "members": {"vehicleName": {"shape": "vehicleName", "documentation": "<p>The unique ID of the vehicle to update.</p>"}, "modelManifestArn": {"shape": "arn", "documentation": "<p>The ARN of a vehicle model (model manifest) associated with the vehicle.</p>"}, "decoderManifestArn": {"shape": "arn", "documentation": "<p>The ARN of the decoder manifest associated with this vehicle.</p>"}, "attributes": {"shape": "attributesMap", "documentation": "<p>Static information about a vehicle in a key-value pair. For example:</p> <p> <code>\"engineType\"</code> : <code>\"1.3 L R2\"</code> </p>"}, "attributeUpdateMode": {"shape": "UpdateMode", "documentation": "<p>The method the specified attributes will update the existing attributes on the vehicle. Use<code>Overwite</code> to replace the vehicle attributes with the specified attributes. Or use <code>Merge</code> to combine all attributes.</p> <p>This is required if attributes are present in the input.</p>"}}}, "UpdateVehicleRequestItem": {"type": "structure", "required": ["vehicleName"], "members": {"vehicleName": {"shape": "vehicleName", "documentation": "<p>The unique ID of the vehicle to update.</p>"}, "modelManifestArn": {"shape": "arn", "documentation": "<p>The ARN of the vehicle model (model manifest) associated with the vehicle to update.</p>"}, "decoderManifestArn": {"shape": "arn", "documentation": "<p>The ARN of the signal decoder manifest associated with the vehicle to update.</p>"}, "attributes": {"shape": "attributesMap", "documentation": "<p>Static information about a vehicle in a key-value pair. For example:</p> <p> <code>\"engineType\"</code> : <code>\"1.3 L R2\"</code> </p>"}, "attributeUpdateMode": {"shape": "UpdateMode", "documentation": "<p>The method the specified attributes will update the existing attributes on the vehicle. Use<code>Overwite</code> to replace the vehicle attributes with the specified attributes. Or use <code>Merge</code> to combine all attributes.</p> <p>This is required if attributes are present in the input.</p>"}}, "documentation": "<p>Information about the vehicle to update.</p>"}, "UpdateVehicleResponse": {"type": "structure", "members": {"vehicleName": {"shape": "vehicleName", "documentation": "<p>The ID of the updated vehicle.</p>"}, "arn": {"shape": "arn", "documentation": "<p>The ARN of the updated vehicle.</p>"}}}, "UpdateVehicleResponseItem": {"type": "structure", "members": {"vehicleName": {"shape": "vehicleName", "documentation": "<p>The unique ID of the updated vehicle.</p>"}, "arn": {"shape": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the updated vehicle.</p>"}}, "documentation": "<p>Information about the updated vehicle.</p>"}, "ValidationException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "string"}, "reason": {"shape": "ValidationExceptionReason", "documentation": "<p>The reason the input failed to satisfy the constraints specified by an Amazon Web Services service.</p>"}, "fieldList": {"shape": "ValidationExceptionFieldList", "documentation": "<p>The list of fields that fail to satisfy the constraints specified by an Amazon Web Services service.</p>"}}, "documentation": "<p>The input fails to satisfy the constraints specified by an Amazon Web Services service.</p>", "exception": true}, "ValidationExceptionField": {"type": "structure", "required": ["name", "message"], "members": {"name": {"shape": "String", "documentation": "<p>The name of the parameter field with the validation error.</p>"}, "message": {"shape": "String", "documentation": "<p>A message about the validation error.</p>"}}, "documentation": "<p>A validation error due to mismatch between the expected data type, length, or pattern of the parameter and the input.</p>"}, "ValidationExceptionFieldList": {"type": "list", "member": {"shape": "ValidationExceptionField"}}, "ValidationExceptionReason": {"type": "string", "enum": ["unknownOperation", "<PERSON><PERSON><PERSON><PERSON>", "fieldValidationFailed", "other"]}, "VehicleAssociationBehavior": {"type": "string", "enum": ["CreateIotThing", "ValidateIotThingExists"]}, "VehicleState": {"type": "string", "enum": ["CREATED", "READY", "HEALTHY", "SUSPENDED", "DELETING"]}, "VehicleStatus": {"type": "structure", "members": {"campaignName": {"shape": "string", "documentation": "<p>The name of a campaign.</p>"}, "vehicleName": {"shape": "vehicleName", "documentation": "<p>The unique ID of the vehicle.</p>"}, "status": {"shape": "VehicleState", "documentation": "<p>The state of a vehicle, which can be one of the following:</p> <ul> <li> <p> <code>CREATED</code> - Amazon Web Services IoT FleetWise sucessfully created the vehicle. </p> </li> <li> <p> <code>READY</code> - The vehicle is ready to receive a campaign deployment. </p> </li> <li> <p> <code>HEALTHY</code> - A campaign deployment was delivered to the vehicle. </p> </li> <li> <p> <code>SUSPENDED</code> - A campaign associated with the vehicle was suspended and data collection was paused. </p> </li> <li> <p> <code>DELETING</code> - Amazon Web Services IoT FleetWise is removing a campaign from the vehicle. </p> </li> </ul>"}}, "documentation": "<p>Information about the state of a vehicle and how it relates to the status of a campaign.</p>"}, "VehicleStatusList": {"type": "list", "member": {"shape": "VehicleStatus"}}, "VehicleSummary": {"type": "structure", "required": ["vehicleName", "arn", "modelManifestArn", "decoderManifestArn", "creationTime", "lastModificationTime"], "members": {"vehicleName": {"shape": "vehicleName", "documentation": "<p>The unique ID of the vehicle.</p>"}, "arn": {"shape": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the vehicle.</p>"}, "modelManifestArn": {"shape": "arn", "documentation": "<p>The ARN of a vehicle model (model manifest) associated with the vehicle.</p>"}, "decoderManifestArn": {"shape": "arn", "documentation": "<p>The ARN of a decoder manifest associated with the vehicle.</p>"}, "creationTime": {"shape": "timestamp", "documentation": "<p>The time the vehicle was created in seconds since epoch (January 1, 1970 at midnight UTC time).</p>"}, "lastModificationTime": {"shape": "timestamp", "documentation": "<p>The time the vehicle was last updated in seconds since epoch (January 1, 1970 at midnight UTC time). </p>"}}, "documentation": "<p>Information about a vehicle.</p> <p>To return this information about vehicles in your account, you can use the API operation.</p>"}, "arn": {"type": "string"}, "attributeName": {"type": "string", "max": 150, "min": 1, "pattern": "[a-zA-Z0-9_.-]+"}, "attributeValue": {"type": "string"}, "attributesMap": {"type": "map", "key": {"shape": "attributeName"}, "value": {"shape": "attributeValue"}}, "boolean": {"type": "boolean"}, "campaignName": {"type": "string", "max": 100, "min": 1, "pattern": "[a-zA-Z\\d\\-_:]+"}, "campaignSummaries": {"type": "list", "member": {"shape": "CampaignSummary"}}, "collectionPeriodMs": {"type": "long", "box": true, "max": 60000, "min": 10000}, "createVehicleErrors": {"type": "list", "member": {"shape": "CreateVehicleError"}}, "createVehicleRequestItems": {"type": "list", "member": {"shape": "CreateVehicleRequestItem"}, "min": 1}, "createVehicleResponses": {"type": "list", "member": {"shape": "CreateVehicleResponseItem"}}, "customerAccountId": {"type": "string"}, "decoderManifestSummaries": {"type": "list", "member": {"shape": "DecoderManifestSummary"}}, "description": {"type": "string", "max": 2048, "min": 1, "pattern": "[^\\u0000-\\u001F\\u007F]+"}, "double": {"type": "double", "box": true}, "errorMessage": {"type": "string"}, "eventExpression": {"type": "string", "max": 2048, "min": 1}, "fleetId": {"type": "string", "max": 100, "min": 1, "pattern": "[a-zA-Z0-9:_-]+"}, "fleetSummaries": {"type": "list", "member": {"shape": "FleetSummary"}}, "fleets": {"type": "list", "member": {"shape": "fleetId"}}, "languageVersion": {"type": "integer", "box": true, "min": 1}, "listOfStrings": {"type": "list", "member": {"shape": "string"}}, "listVehiclesMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "maxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "maxSampleCount": {"type": "long", "box": true, "max": **********, "min": 1}, "message": {"type": "string", "max": 2048, "min": 1, "pattern": "[^\\u0000-\\u001F\\u007F]+"}, "modelManifestSummaries": {"type": "list", "member": {"shape": "ModelManifestSummary"}}, "nextToken": {"type": "string", "max": 4096, "min": 1}, "nonNegativeInteger": {"type": "integer", "min": 0}, "number": {"type": "integer"}, "positiveInteger": {"type": "integer", "min": 1}, "priority": {"type": "integer", "box": true, "min": 0}, "resourceName": {"type": "string", "max": 100, "min": 1, "pattern": "[a-zA-Z\\d\\-_:]+"}, "signalCatalogSummaries": {"type": "list", "member": {"shape": "SignalCatalogSummary"}}, "status": {"type": "string"}, "string": {"type": "string"}, "timestamp": {"type": "timestamp"}, "uint32": {"type": "long", "box": true, "max": **********, "min": 0}, "updateVehicleErrors": {"type": "list", "member": {"shape": "UpdateVehicleError"}}, "updateVehicleRequestItems": {"type": "list", "member": {"shape": "UpdateVehicleRequestItem"}, "min": 1}, "updateVehicleResponseItems": {"type": "list", "member": {"shape": "UpdateVehicleResponseItem"}}, "vehicleName": {"type": "string", "max": 100, "min": 1, "pattern": "[a-zA-Z\\d\\-_:]+"}, "vehicleSummaries": {"type": "list", "member": {"shape": "VehicleSummary"}}, "vehicles": {"type": "list", "member": {"shape": "vehicleName"}}, "wildcardSignalName": {"type": "string", "max": 150, "min": 1, "pattern": "[\\w|*|-]+(\\.[\\w|*|-]+)*"}}, "documentation": "<p>Amazon Web Services IoT FleetWise is a fully managed service that you can use to collect, model, and transfer vehicle data to the Amazon Web Services cloud at scale. With Amazon Web Services IoT FleetWise, you can standardize all of your vehicle data models, independent of the in-vehicle communication architecture, and define data collection rules to transfer only high-value data to the cloud. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/iot-fleetwise/latest/developerguide/\">What is Amazon Web Services IoT FleetWise?</a> in the <i>Amazon Web Services IoT FleetWise Developer Guide</i>.</p>"}