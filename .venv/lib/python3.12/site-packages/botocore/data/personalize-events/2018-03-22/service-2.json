{"version": "2.0", "metadata": {"apiVersion": "2018-03-22", "endpointPrefix": "personalize-events", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "Amazon Personalize Events", "serviceId": "Personalize Events", "signatureVersion": "v4", "signingName": "personalize", "uid": "personalize-events-2018-03-22"}, "operations": {"PutEvents": {"name": "PutEvents", "http": {"method": "POST", "requestUri": "/events"}, "input": {"shape": "PutEventsRequest"}, "errors": [{"shape": "InvalidInputException"}], "documentation": "<p>Records user interaction event data. For more information see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/recording-events.html\">Recording Events</a>.</p>"}, "PutItems": {"name": "PutItems", "http": {"method": "POST", "requestUri": "/items"}, "input": {"shape": "PutItemsRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Adds one or more items to an Items dataset. For more information see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/importing-items.html\">Importing Items Incrementally</a>. </p>"}, "PutUsers": {"name": "PutUsers", "http": {"method": "POST", "requestUri": "/users"}, "input": {"shape": "PutUsersRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Adds one or more users to a Users dataset. For more information see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/importing-users.html\">Importing Users Incrementally</a>.</p>"}}, "shapes": {"Arn": {"type": "string", "max": 256, "pattern": "arn:([a-z\\d-]+):personalize:.*:.*:.+"}, "Date": {"type": "timestamp"}, "ErrorMessage": {"type": "string"}, "Event": {"type": "structure", "required": ["eventType", "sentAt"], "members": {"eventId": {"shape": "StringType", "documentation": "<p>An ID associated with the event. If an event ID is not provided, Amazon Personalize generates a unique ID for the event. An event ID is not used as an input to the model. Amazon Personalize uses the event ID to distinquish unique events. Any subsequent events after the first with the same event ID are not used in model training.</p>"}, "eventType": {"shape": "StringType", "documentation": "<p>The type of event, such as click or download. This property corresponds to the <code>EVENT_TYPE</code> field of your Interactions schema and depends on the types of events you are tracking.</p>"}, "eventValue": {"shape": "FloatType", "documentation": "<p>The event value that corresponds to the <code>EVENT_VALUE</code> field of the Interactions schema.</p>"}, "itemId": {"shape": "ItemId", "documentation": "<p>The item ID key that corresponds to the <code>ITEM_ID</code> field of the Interactions schema.</p>"}, "properties": {"shape": "EventPropertiesJSON", "documentation": "<p>A string map of event-specific data that you might choose to record. For example, if a user rates a movie on your site, other than movie ID (<code>itemId</code>) and rating (<code>eventValue</code>) , you might also send the number of movie ratings made by the user.</p> <p>Each item in the map consists of a key-value pair. For example,</p> <p> <code>{\"numberOfRatings\": \"12\"}</code> </p> <p>The keys use camel case names that match the fields in the Interactions schema. In the above example, the <code>numberOfRatings</code> would match the 'NUMBER_OF_RATINGS' field defined in the Interactions schema.</p>", "jsonvalue": true}, "sentAt": {"shape": "Date", "documentation": "<p>The timestamp (in Unix time) on the client side when the event occurred.</p>"}, "recommendationId": {"shape": "RecommendationId", "documentation": "<p>The ID of the list of recommendations that contains the item the user interacted with. Provide a <code>recommendationId</code> to have Amazon Personalize implicitly record the recommendations you show your user as impressions data. Or provide a <code>recommendationId</code> if you use a metric attribution to measure the impact of recommendations. </p> <p> For more information on recording impressions data, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/recording-events.html#putevents-including-impressions-data\">Recording impressions data</a>. For more information on creating a metric attribution see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/measuring-recommendation-impact.html\">Measuring impact of recommendations</a>. </p>"}, "impression": {"shape": "Impression", "documentation": "<p>A list of item IDs that represents the sequence of items you have shown the user. For example, <code>[\"itemId1\", \"itemId2\", \"itemId3\"]</code>. Provide a list of items to manually record impressions data for an event. For more information on recording impressions data, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/recording-events.html#putevents-including-impressions-data\">Recording impressions data</a>. </p>"}, "metricAttribution": {"shape": "MetricAttribution", "documentation": "<p>Contains information about the metric attribution associated with an event. For more information about metric attributions, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/measuring-recommendation-impact.html\">Measuring impact of recommendations</a>.</p>"}}, "documentation": "<p>Represents user interaction event information sent using the <code>PutEvents</code> API.</p>", "sensitive": true}, "EventAttributionSource": {"type": "string", "max": 1024, "pattern": "^[\\x20-\\x7E]*[\\x21-\\x7E]+[\\x20-\\x7E]*$"}, "EventList": {"type": "list", "member": {"shape": "Event"}, "max": 10, "min": 1}, "EventPropertiesJSON": {"type": "string", "max": 1024, "min": 1, "sensitive": true}, "FloatType": {"type": "float"}, "Impression": {"type": "list", "member": {"shape": "ItemId"}, "max": 25, "min": 1}, "InvalidInputException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>Provide a valid value for the field or parameter.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "Item": {"type": "structure", "required": ["itemId"], "members": {"itemId": {"shape": "StringType", "documentation": "<p>The ID associated with the item.</p>"}, "properties": {"shape": "ItemProperties", "documentation": "<p>A string map of item-specific metadata. Each element in the map consists of a key-value pair. For example, <code>{\"numberOfRatings\": \"12\"}</code>.</p> <p>The keys use camel case names that match the fields in the schema for the Items dataset. In the previous example, the <code>numberOfRatings</code> matches the 'NUMBER_OF_RATINGS' field defined in the Items schema. For categorical string data, to include multiple categories for a single item, separate each category with a pipe separator (<code>|</code>). For example, <code>\\\"Horror|Action\\\"</code>.</p>", "jsonvalue": true}}, "documentation": "<p>Represents item metadata added to an Items dataset using the <code>PutItems</code> API. For more information see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/importing-items.html\">Importing Items Incrementally</a>. </p>"}, "ItemId": {"type": "string", "max": 256, "min": 1, "sensitive": true}, "ItemList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}, "max": 10, "min": 1}, "ItemProperties": {"type": "string", "max": 24262, "min": 1, "sensitive": true}, "MetricAttribution": {"type": "structure", "required": ["eventAttributionSource"], "members": {"eventAttributionSource": {"shape": "EventAttributionSource", "documentation": "<p>The source of the event, such as a third party.</p>"}}, "documentation": "<p>Contains information about a metric attribution associated with an event. For more information about metric attributions, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/measuring-recommendation-impact.html\">Measuring impact of recommendations</a>.</p>"}, "PutEventsRequest": {"type": "structure", "required": ["trackingId", "sessionId", "eventList"], "members": {"trackingId": {"shape": "StringType", "documentation": "<p>The tracking ID for the event. The ID is generated by a call to the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateEventTracker.html\">CreateEventTracker</a> API.</p>"}, "userId": {"shape": "UserId", "documentation": "<p>The user associated with the event.</p>"}, "sessionId": {"shape": "StringType", "documentation": "<p>The session ID associated with the user's visit. Your application generates the sessionId when a user first visits your website or uses your application. Amazon Personalize uses the sessionId to associate events with the user before they log in. For more information, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/recording-events.html\">Recording Events</a>.</p>"}, "eventList": {"shape": "EventList", "documentation": "<p>A list of event data from the session.</p>"}}}, "PutItemsRequest": {"type": "structure", "required": ["datasetArn", "items"], "members": {"datasetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Items dataset you are adding the item or items to.</p>"}, "items": {"shape": "ItemList", "documentation": "<p>A list of item data.</p>"}}}, "PutUsersRequest": {"type": "structure", "required": ["datasetArn", "users"], "members": {"datasetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Users dataset you are adding the user or users to.</p>"}, "users": {"shape": "UserList", "documentation": "<p>A list of user data.</p>"}}}, "RecommendationId": {"type": "string", "max": 40, "min": 1}, "ResourceInUseException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The specified resource is in use.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>Could not find the specified resource.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "StringType": {"type": "string", "max": 256, "min": 1}, "User": {"type": "structure", "required": ["userId"], "members": {"userId": {"shape": "StringType", "documentation": "<p>The ID associated with the user.</p>"}, "properties": {"shape": "UserProperties", "documentation": "<p>A string map of user-specific metadata. Each element in the map consists of a key-value pair. For example, <code>{\"numberOfVideosWatched\": \"45\"}</code>.</p> <p>The keys use camel case names that match the fields in the schema for the Users dataset. In the previous example, the <code>numberOfVideosWatched</code> matches the 'NUMBER_OF_VIDEOS_WATCHED' field defined in the Users schema. For categorical string data, to include multiple categories for a single user, separate each category with a pipe separator (<code>|</code>). For example, <code>\\\"Member|Frequent shopper\\\"</code>.</p>", "jsonvalue": true}}, "documentation": "<p>Represents user metadata added to a Users dataset using the <code>PutUsers</code> API. For more information see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/importing-users.html\">Importing Users Incrementally</a>.</p>"}, "UserId": {"type": "string", "max": 256, "min": 1, "sensitive": true}, "UserList": {"type": "list", "member": {"shape": "User"}, "max": 10, "min": 1}, "UserProperties": {"type": "string", "max": 4096, "min": 1, "sensitive": true}}, "documentation": "<p>Amazon Personalize can consume real-time user event data, such as <i>stream</i> or <i>click</i> data, and use it for model training either alone or combined with historical data. For more information see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/recording-events.html\">Recording Events</a>.</p>"}