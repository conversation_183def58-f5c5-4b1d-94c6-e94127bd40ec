{"version": "2.0", "metadata": {"apiVersion": "2019-06-24", "endpointPrefix": "servicequotas", "jsonVersion": "1.1", "protocol": "json", "serviceFullName": "Service Quotas", "serviceId": "Service Quotas", "signatureVersion": "v4", "targetPrefix": "ServiceQuotasV20190624", "uid": "service-quotas-2019-06-24"}, "operations": {"AssociateServiceQuotaTemplate": {"name": "AssociateServiceQuotaTemplate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AssociateServiceQuotaTemplateRequest"}, "output": {"shape": "AssociateServiceQuotaTemplateResponse"}, "errors": [{"shape": "DependencyAccessDeniedException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "AWSServiceAccessNotEnabledException"}, {"shape": "OrganizationNotInAllFeaturesModeException"}, {"shape": "TemplatesNotAvailableInRegionException"}, {"shape": "NoAvailableOrganizationException"}], "documentation": "<p>Associates your quota request template with your organization. When a new Amazon Web Services account is created in your organization, the quota increase requests in the template are automatically applied to the account. You can add a quota increase request for any adjustable quota to your template.</p>"}, "DeleteServiceQuotaIncreaseRequestFromTemplate": {"name": "DeleteServiceQuotaIncreaseRequestFromTemplate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteServiceQuotaIncreaseRequestFromTemplateRequest"}, "output": {"shape": "DeleteServiceQuotaIncreaseRequestFromTemplateResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ServiceException"}, {"shape": "DependencyAccessDeniedException"}, {"shape": "TooManyRequestsException"}, {"shape": "NoSuchResourceException"}, {"shape": "IllegalArgumentException"}, {"shape": "AWSServiceAccessNotEnabledException"}, {"shape": "TemplatesNotAvailableInRegionException"}, {"shape": "NoAvailableOrganizationException"}], "documentation": "<p>Deletes the quota increase request for the specified quota from your quota request template.</p>"}, "DisassociateServiceQuotaTemplate": {"name": "DisassociateServiceQuotaTemplate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DisassociateServiceQuotaTemplateRequest"}, "output": {"shape": "DisassociateServiceQuotaTemplateResponse"}, "errors": [{"shape": "DependencyAccessDeniedException"}, {"shape": "ServiceQuotaTemplateNotInUseException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "AWSServiceAccessNotEnabledException"}, {"shape": "TemplatesNotAvailableInRegionException"}, {"shape": "NoAvailableOrganizationException"}], "documentation": "<p>Disables your quota request template. After a template is disabled, the quota increase requests in the template are not applied to new Amazon Web Services accounts in your organization. Disabling a quota request template does not apply its quota increase requests.</p>"}, "GetAWSDefaultServiceQuota": {"name": "GetAWSDefaultServiceQuota", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetAWSDefaultServiceQuotaRequest"}, "output": {"shape": "GetAWSDefaultServiceQuotaResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "NoSuchResourceException"}, {"shape": "IllegalArgumentException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Retrieves the default value for the specified quota. The default value does not reflect any quota increases.</p>"}, "GetAssociationForServiceQuotaTemplate": {"name": "GetAssociationForServiceQuotaTemplate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetAssociationForServiceQuotaTemplateRequest"}, "output": {"shape": "GetAssociationForServiceQuotaTemplateResponse"}, "errors": [{"shape": "DependencyAccessDeniedException"}, {"shape": "ServiceQuotaTemplateNotInUseException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "AWSServiceAccessNotEnabledException"}, {"shape": "TemplatesNotAvailableInRegionException"}, {"shape": "NoAvailableOrganizationException"}], "documentation": "<p>Retrieves the status of the association for the quota request template.</p>"}, "GetRequestedServiceQuotaChange": {"name": "GetRequestedServiceQuotaChange", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetRequestedServiceQuotaChangeRequest"}, "output": {"shape": "GetRequestedServiceQuotaChangeResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "NoSuchResourceException"}, {"shape": "IllegalArgumentException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Retrieves information about the specified quota increase request.</p>"}, "GetServiceQuota": {"name": "GetServiceQuota", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetServiceQuotaRequest"}, "output": {"shape": "GetServiceQuotaResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "NoSuchResourceException"}, {"shape": "IllegalArgumentException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Retrieves the applied quota value for the specified quota. For some quotas, only the default values are available. If the applied quota value is not available for a quota, the quota is not retrieved.</p>"}, "GetServiceQuotaIncreaseRequestFromTemplate": {"name": "GetServiceQuotaIncreaseRequestFromTemplate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetServiceQuotaIncreaseRequestFromTemplateRequest"}, "output": {"shape": "GetServiceQuotaIncreaseRequestFromTemplateResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "DependencyAccessDeniedException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "NoSuchResourceException"}, {"shape": "IllegalArgumentException"}, {"shape": "AWSServiceAccessNotEnabledException"}, {"shape": "TemplatesNotAvailableInRegionException"}, {"shape": "NoAvailableOrganizationException"}], "documentation": "<p>Retrieves information about the specified quota increase request in your quota request template.</p>"}, "ListAWSDefaultServiceQuotas": {"name": "ListAWSDefaultServiceQuotas", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAWSDefaultServiceQuotasRequest"}, "output": {"shape": "ListAWSDefaultServiceQuotasResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "NoSuchResourceException"}, {"shape": "IllegalArgumentException"}, {"shape": "InvalidPaginationTokenException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Lists the default values for the quotas for the specified Amazon Web Service. A default value does not reflect any quota increases.</p>"}, "ListRequestedServiceQuotaChangeHistory": {"name": "ListRequestedServiceQuotaChangeHistory", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListRequestedServiceQuotaChangeHistoryRequest"}, "output": {"shape": "ListRequestedServiceQuotaChangeHistoryResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "NoSuchResourceException"}, {"shape": "IllegalArgumentException"}, {"shape": "InvalidPaginationTokenException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Retrieves the quota increase requests for the specified Amazon Web Service.</p>"}, "ListRequestedServiceQuotaChangeHistoryByQuota": {"name": "ListRequestedServiceQuotaChangeHistoryByQuota", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListRequestedServiceQuotaChangeHistoryByQuotaRequest"}, "output": {"shape": "ListRequestedServiceQuotaChangeHistoryByQuotaResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "NoSuchResourceException"}, {"shape": "IllegalArgumentException"}, {"shape": "InvalidPaginationTokenException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Retrieves the quota increase requests for the specified quota.</p>"}, "ListServiceQuotaIncreaseRequestsInTemplate": {"name": "ListServiceQuotaIncreaseRequestsInTemplate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListServiceQuotaIncreaseRequestsInTemplateRequest"}, "output": {"shape": "ListServiceQuotaIncreaseRequestsInTemplateResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "DependencyAccessDeniedException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "IllegalArgumentException"}, {"shape": "AWSServiceAccessNotEnabledException"}, {"shape": "TemplatesNotAvailableInRegionException"}, {"shape": "NoAvailableOrganizationException"}], "documentation": "<p>Lists the quota increase requests in the specified quota request template.</p>"}, "ListServiceQuotas": {"name": "ListServiceQuotas", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListServiceQuotasRequest"}, "output": {"shape": "ListServiceQuotasResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "NoSuchResourceException"}, {"shape": "IllegalArgumentException"}, {"shape": "InvalidPaginationTokenException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Lists the applied quota values for the specified Amazon Web Service. For some quotas, only the default values are available. If the applied quota value is not available for a quota, the quota is not retrieved.</p>"}, "ListServices": {"name": "ListServices", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListServicesRequest"}, "output": {"shape": "ListServicesResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "IllegalArgumentException"}, {"shape": "InvalidPaginationTokenException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Lists the names and codes for the Amazon Web Services integrated with Service Quotas.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "NoSuchResourceException"}, {"shape": "IllegalArgumentException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceException"}], "documentation": "<p>Returns a list of the tags assigned to the specified applied quota.</p>"}, "PutServiceQuotaIncreaseRequestIntoTemplate": {"name": "PutServiceQuotaIncreaseRequestIntoTemplate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutServiceQuotaIncreaseRequestIntoTemplateRequest"}, "output": {"shape": "PutServiceQuotaIncreaseRequestIntoTemplateResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "DependencyAccessDeniedException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "IllegalArgumentException"}, {"shape": "QuotaExceededException"}, {"shape": "NoSuchResourceException"}, {"shape": "AWSServiceAccessNotEnabledException"}, {"shape": "TemplatesNotAvailableInRegionException"}, {"shape": "NoAvailableOrganizationException"}], "documentation": "<p>Adds a quota increase request to your quota request template.</p>"}, "RequestServiceQuotaIncrease": {"name": "RequestServiceQuotaIncrease", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RequestServiceQuotaIncreaseRequest"}, "output": {"shape": "RequestServiceQuotaIncreaseResponse"}, "errors": [{"shape": "DependencyAccessDeniedException"}, {"shape": "QuotaExceededException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "AccessDeniedException"}, {"shape": "NoSuchResourceException"}, {"shape": "IllegalArgumentException"}, {"shape": "InvalidResourceStateException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Submits a quota increase request for the specified quota.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "NoSuchResourceException"}, {"shape": "TooManyTagsException"}, {"shape": "TagPolicyViolationException"}, {"shape": "IllegalArgumentException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceException"}], "documentation": "<p>Adds tags to the specified applied quota. You can include one or more tags to add to the quota.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "NoSuchResourceException"}, {"shape": "IllegalArgumentException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceException"}], "documentation": "<p>Removes tags from the specified applied quota. You can specify one or more tags to remove.</p>"}}, "shapes": {"AWSServiceAccessNotEnabledException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>The action you attempted is not allowed unless Service Access with Service Quotas is enabled in your organization.</p>", "exception": true}, "AccessDeniedException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>You do not have sufficient permission to perform this action.</p>", "exception": true}, "AmazonResourceName": {"type": "string", "max": 1011, "min": 1, "pattern": "arn:aws(-[\\w]+)*:*:.+:[0-9]{12}:.+"}, "AppliedLevelEnum": {"type": "string", "enum": ["ACCOUNT", "RESOURCE", "ALL"]}, "AssociateServiceQuotaTemplateRequest": {"type": "structure", "members": {}}, "AssociateServiceQuotaTemplateResponse": {"type": "structure", "members": {}}, "AwsRegion": {"type": "string", "max": 64, "min": 1, "pattern": "[a-zA-Z][a-zA-Z0-9-]{1,128}"}, "CustomerServiceEngagementId": {"type": "string"}, "DateTime": {"type": "timestamp"}, "DeleteServiceQuotaIncreaseRequestFromTemplateRequest": {"type": "structure", "required": ["ServiceCode", "QuotaCode", "AwsRegion"], "members": {"ServiceCode": {"shape": "ServiceCode", "documentation": "<p>Specifies the service identifier. To find the service code value for an Amazon Web Services service, use the <a>ListServices</a> operation.</p>"}, "QuotaCode": {"shape": "QuotaCode", "documentation": "<p>Specifies the quota identifier. To find the quota code for a specific quota, use the <a>ListServiceQuotas</a> operation, and look for the <code>QuotaCode</code> response in the output for the quota you want.</p>"}, "AwsRegion": {"shape": "AwsRegion", "documentation": "<p>Specifies the Amazon Web Services Region for which the request was made.</p>"}}}, "DeleteServiceQuotaIncreaseRequestFromTemplateResponse": {"type": "structure", "members": {}}, "DependencyAccessDeniedException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>You can't perform this action because a dependency does not have access.</p>", "exception": true}, "DisassociateServiceQuotaTemplateRequest": {"type": "structure", "members": {}}, "DisassociateServiceQuotaTemplateResponse": {"type": "structure", "members": {}}, "ErrorCode": {"type": "string", "enum": ["DEPENDENCY_ACCESS_DENIED_ERROR", "DEPENDENCY_THROTTLING_ERROR", "DEPENDENCY_SERVICE_ERROR", "SERVICE_QUOTA_NOT_AVAILABLE_ERROR"]}, "ErrorMessage": {"type": "string"}, "ErrorReason": {"type": "structure", "members": {"ErrorCode": {"shape": "ErrorCode", "documentation": "<p>Service Quotas returns the following error values:</p> <ul> <li> <p> <code>DEPENDENCY_ACCESS_DENIED_ERROR</code> - The caller does not have the required permissions to complete the action. To resolve the error, you must have permission to access the Amazon Web Service or quota.</p> </li> <li> <p> <code>DEPENDENCY_THROTTLING_ERROR</code> - The Amazon Web Service is throttling Service Quotas. </p> </li> <li> <p> <code>DEPENDENCY_SERVICE_ERROR</code> - The Amazon Web Service is not available.</p> </li> <li> <p> <code>SERVICE_QUOTA_NOT_AVAILABLE_ERROR</code> - There was an error in Service Quotas.</p> </li> </ul>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>The error message.</p>"}}, "documentation": "<p>An error that explains why an action did not succeed.</p>"}, "ExceptionMessage": {"type": "string"}, "GetAWSDefaultServiceQuotaRequest": {"type": "structure", "required": ["ServiceCode", "QuotaCode"], "members": {"ServiceCode": {"shape": "ServiceCode", "documentation": "<p>Specifies the service identifier. To find the service code value for an Amazon Web Services service, use the <a>ListServices</a> operation.</p>"}, "QuotaCode": {"shape": "QuotaCode", "documentation": "<p>Specifies the quota identifier. To find the quota code for a specific quota, use the <a>ListServiceQuotas</a> operation, and look for the <code>QuotaCode</code> response in the output for the quota you want.</p>"}}}, "GetAWSDefaultServiceQuotaResponse": {"type": "structure", "members": {"Quota": {"shape": "ServiceQuota", "documentation": "<p>Information about the quota.</p>"}}}, "GetAssociationForServiceQuotaTemplateRequest": {"type": "structure", "members": {}}, "GetAssociationForServiceQuotaTemplateResponse": {"type": "structure", "members": {"ServiceQuotaTemplateAssociationStatus": {"shape": "ServiceQuotaTemplateAssociationStatus", "documentation": "<p>The association status. If the status is <code>ASSOCIATED</code>, the quota increase requests in the template are automatically applied to new Amazon Web Services accounts in your organization.</p>"}}}, "GetRequestedServiceQuotaChangeRequest": {"type": "structure", "required": ["RequestId"], "members": {"RequestId": {"shape": "RequestId", "documentation": "<p>Specifies the ID of the quota increase request.</p>"}}}, "GetRequestedServiceQuotaChangeResponse": {"type": "structure", "members": {"RequestedQuota": {"shape": "RequestedServiceQuotaChange", "documentation": "<p>Information about the quota increase request.</p>"}}}, "GetServiceQuotaIncreaseRequestFromTemplateRequest": {"type": "structure", "required": ["ServiceCode", "QuotaCode", "AwsRegion"], "members": {"ServiceCode": {"shape": "ServiceCode", "documentation": "<p>Specifies the service identifier. To find the service code value for an Amazon Web Services service, use the <a>ListServices</a> operation.</p>"}, "QuotaCode": {"shape": "QuotaCode", "documentation": "<p>Specifies the quota identifier. To find the quota code for a specific quota, use the <a>ListServiceQuotas</a> operation, and look for the <code>QuotaCode</code> response in the output for the quota you want.</p>"}, "AwsRegion": {"shape": "AwsRegion", "documentation": "<p>Specifies the Amazon Web Services Region for which you made the request.</p>"}}}, "GetServiceQuotaIncreaseRequestFromTemplateResponse": {"type": "structure", "members": {"ServiceQuotaIncreaseRequestInTemplate": {"shape": "ServiceQuotaIncreaseRequestInTemplate", "documentation": "<p>Information about the quota increase request.</p>"}}}, "GetServiceQuotaRequest": {"type": "structure", "required": ["ServiceCode", "QuotaCode"], "members": {"ServiceCode": {"shape": "ServiceCode", "documentation": "<p>Specifies the service identifier. To find the service code value for an Amazon Web Services service, use the <a>ListServices</a> operation.</p>"}, "QuotaCode": {"shape": "QuotaCode", "documentation": "<p>Specifies the quota identifier. To find the quota code for a specific quota, use the <a>ListServiceQuotas</a> operation, and look for the <code>QuotaCode</code> response in the output for the quota you want.</p>"}, "ContextId": {"shape": "QuotaContextId", "documentation": "<p>Specifies the Amazon Web Services account or resource to which the quota applies. The value in this field depends on the context scope associated with the specified service quota.</p>"}}}, "GetServiceQuotaResponse": {"type": "structure", "members": {"Quota": {"shape": "ServiceQuota", "documentation": "<p>Information about the quota.</p>"}}}, "GlobalQuota": {"type": "boolean"}, "IllegalArgumentException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>Invalid input was provided.</p>", "exception": true}, "InputTagKeys": {"type": "list", "member": {"shape": "TagKey"}}, "InputTags": {"type": "list", "member": {"shape": "Tag"}, "min": 1}, "InvalidPaginationTokenException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>Invalid input was provided.</p>", "exception": true}, "InvalidResourceStateException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>The resource is in an invalid state.</p>", "exception": true}, "ListAWSDefaultServiceQuotasRequest": {"type": "structure", "required": ["ServiceCode"], "members": {"ServiceCode": {"shape": "ServiceCode", "documentation": "<p>Specifies the service identifier. To find the service code value for an Amazon Web Services service, use the <a>ListServices</a> operation.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Specifies a value for receiving additional results after you receive a <code>NextToken</code> response in a previous request. A <code>NextToken</code> response indicates that more output is available. Set this parameter to the value of the previous call's <code>NextToken</code> response to indicate where the output should continue from.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the maximum number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value appropriate to the operation. If additional items exist beyond those included in the current response, the <code>NextToken</code> response element is present and has a value (is not null). Include that value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results.</p> <note> <p>An API operation can return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p> </note>"}}}, "ListAWSDefaultServiceQuotasResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>If present, indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>.</p>"}, "Quotas": {"shape": "ServiceQuotaListDefinition", "documentation": "<p>Information about the quotas.</p>"}}}, "ListRequestedServiceQuotaChangeHistoryByQuotaRequest": {"type": "structure", "required": ["ServiceCode", "QuotaCode"], "members": {"ServiceCode": {"shape": "ServiceCode", "documentation": "<p>Specifies the service identifier. To find the service code value for an Amazon Web Services service, use the <a>ListServices</a> operation.</p>"}, "QuotaCode": {"shape": "QuotaCode", "documentation": "<p>Specifies the quota identifier. To find the quota code for a specific quota, use the <a>ListServiceQuotas</a> operation, and look for the <code>QuotaCode</code> response in the output for the quota you want.</p>"}, "Status": {"shape": "RequestStatus", "documentation": "<p>Specifies that you want to filter the results to only the requests with the matching status.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Specifies a value for receiving additional results after you receive a <code>NextToken</code> response in a previous request. A <code>NextToken</code> response indicates that more output is available. Set this parameter to the value of the previous call's <code>NextToken</code> response to indicate where the output should continue from.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the maximum number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value appropriate to the operation. If additional items exist beyond those included in the current response, the <code>NextToken</code> response element is present and has a value (is not null). Include that value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results.</p> <note> <p>An API operation can return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p> </note>"}, "QuotaRequestedAtLevel": {"shape": "AppliedLevelEnum", "documentation": "<p>Specifies at which level within the Amazon Web Services account the quota request applies to.</p>"}}}, "ListRequestedServiceQuotaChangeHistoryByQuotaResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>If present, indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>.</p>"}, "RequestedQuotas": {"shape": "RequestedServiceQuotaChangeHistoryListDefinition", "documentation": "<p>Information about the quota increase requests.</p>"}}}, "ListRequestedServiceQuotaChangeHistoryRequest": {"type": "structure", "members": {"ServiceCode": {"shape": "ServiceCode", "documentation": "<p>Specifies the service identifier. To find the service code value for an Amazon Web Services service, use the <a>ListServices</a> operation.</p>"}, "Status": {"shape": "RequestStatus", "documentation": "<p>Specifies that you want to filter the results to only the requests with the matching status.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Specifies a value for receiving additional results after you receive a <code>NextToken</code> response in a previous request. A <code>NextToken</code> response indicates that more output is available. Set this parameter to the value of the previous call's <code>NextToken</code> response to indicate where the output should continue from.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the maximum number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value appropriate to the operation. If additional items exist beyond those included in the current response, the <code>NextToken</code> response element is present and has a value (is not null). Include that value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results.</p> <note> <p>An API operation can return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p> </note>"}, "QuotaRequestedAtLevel": {"shape": "AppliedLevelEnum", "documentation": "<p>Specifies at which level within the Amazon Web Services account the quota request applies to.</p>"}}}, "ListRequestedServiceQuotaChangeHistoryResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>If present, indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>.</p>"}, "RequestedQuotas": {"shape": "RequestedServiceQuotaChangeHistoryListDefinition", "documentation": "<p>Information about the quota increase requests.</p>"}}}, "ListServiceQuotaIncreaseRequestsInTemplateRequest": {"type": "structure", "members": {"ServiceCode": {"shape": "ServiceCode", "documentation": "<p>Specifies the service identifier. To find the service code value for an Amazon Web Services service, use the <a>ListServices</a> operation.</p>"}, "AwsRegion": {"shape": "AwsRegion", "documentation": "<p>Specifies the Amazon Web Services Region for which you made the request.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Specifies a value for receiving additional results after you receive a <code>NextToken</code> response in a previous request. A <code>NextToken</code> response indicates that more output is available. Set this parameter to the value of the previous call's <code>NextToken</code> response to indicate where the output should continue from.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the maximum number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value appropriate to the operation. If additional items exist beyond those included in the current response, the <code>NextToken</code> response element is present and has a value (is not null). Include that value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results.</p> <note> <p>An API operation can return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p> </note>"}}}, "ListServiceQuotaIncreaseRequestsInTemplateResponse": {"type": "structure", "members": {"ServiceQuotaIncreaseRequestInTemplateList": {"shape": "ServiceQuotaIncreaseRequestInTemplateList", "documentation": "<p>Information about the quota increase requests.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If present, indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>.</p>"}}}, "ListServiceQuotasRequest": {"type": "structure", "required": ["ServiceCode"], "members": {"ServiceCode": {"shape": "ServiceCode", "documentation": "<p>Specifies the service identifier. To find the service code value for an Amazon Web Services service, use the <a>ListServices</a> operation.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Specifies a value for receiving additional results after you receive a <code>NextToken</code> response in a previous request. A <code>NextToken</code> response indicates that more output is available. Set this parameter to the value of the previous call's <code>NextToken</code> response to indicate where the output should continue from.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the maximum number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value appropriate to the operation. If additional items exist beyond those included in the current response, the <code>NextToken</code> response element is present and has a value (is not null). Include that value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results.</p> <note> <p>An API operation can return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p> </note>"}, "QuotaCode": {"shape": "QuotaCode", "documentation": "<p>Specifies the quota identifier. To find the quota code for a specific quota, use the <a>ListServiceQuotas</a> operation, and look for the <code>QuotaCode</code> response in the output for the quota you want.</p>"}, "QuotaAppliedAtLevel": {"shape": "AppliedLevelEnum", "documentation": "<p>Specifies at which level of granularity that the quota value is applied.</p>"}}}, "ListServiceQuotasResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>If present, indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>.</p>"}, "Quotas": {"shape": "ServiceQuotaListDefinition", "documentation": "<p>Information about the quotas.</p>"}}}, "ListServicesRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>Specifies a value for receiving additional results after you receive a <code>NextToken</code> response in a previous request. A <code>NextToken</code> response indicates that more output is available. Set this parameter to the value of the previous call's <code>NextToken</code> response to indicate where the output should continue from.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the maximum number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value appropriate to the operation. If additional items exist beyond those included in the current response, the <code>NextToken</code> response element is present and has a value (is not null). Include that value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results.</p> <note> <p>An API operation can return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p> </note>"}}}, "ListServicesResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>If present, indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>.</p>"}, "Services": {"shape": "ServiceInfoListDefinition", "documentation": "<p>The list of the Amazon Web Service names and service codes.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) for the applied quota for which you want to list tags. You can get this information by using the Service Quotas console, or by listing the quotas using the <a href=\"https://docs.aws.amazon.com/cli/latest/reference/service-quotas/list-service-quotas.html\">list-service-quotas</a> CLI command or the <a href=\"https://docs.aws.amazon.com/servicequotas/2019-06-24/apireference/API_ListServiceQuotas.html\">ListServiceQuotas</a> Amazon Web Services API operation.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "OutputTags", "documentation": "<p>A complex data type that contains zero or more tag elements.</p>"}}}, "MaxResults": {"type": "integer", "max": 100, "min": 1}, "MetricDimensionName": {"type": "string"}, "MetricDimensionValue": {"type": "string"}, "MetricDimensionsMapDefinition": {"type": "map", "key": {"shape": "MetricDimensionName"}, "value": {"shape": "MetricDimensionValue"}, "max": 10}, "MetricInfo": {"type": "structure", "members": {"MetricNamespace": {"shape": "QuotaMetricNamespace", "documentation": "<p>The namespace of the metric.</p>"}, "MetricName": {"shape": "QuotaMetricName", "documentation": "<p>The name of the metric.</p>"}, "MetricDimensions": {"shape": "MetricDimensionsMapDefinition", "documentation": "<p>The metric dimension. This is a name/value pair that is part of the identity of a metric.</p>"}, "MetricStatisticRecommendation": {"shape": "Statistic", "documentation": "<p>The metric statistic that we recommend you use when determining quota usage.</p>"}}, "documentation": "<p>Information about the CloudWatch metric that reflects quota usage.</p>"}, "NextToken": {"type": "string", "max": 2048, "pattern": "^[a-zA-Z0-9/+]*={0,2}$"}, "NoAvailableOrganizationException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>The Amazon Web Services account making this call is not a member of an organization.</p>", "exception": true}, "NoSuchResourceException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>The specified resource does not exist.</p>", "exception": true}, "OrganizationNotInAllFeaturesModeException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>The organization that your Amazon Web Services account belongs to is not in All Features mode.</p>", "exception": true}, "OutputTags": {"type": "list", "member": {"shape": "Tag"}, "max": 200}, "PeriodUnit": {"type": "string", "enum": ["MICROSECOND", "MILLISECOND", "SECOND", "MINUTE", "HOUR", "DAY", "WEEK"]}, "PeriodValue": {"type": "integer"}, "PutServiceQuotaIncreaseRequestIntoTemplateRequest": {"type": "structure", "required": ["QuotaCode", "ServiceCode", "AwsRegion", "DesiredValue"], "members": {"QuotaCode": {"shape": "QuotaCode", "documentation": "<p>Specifies the quota identifier. To find the quota code for a specific quota, use the <a>ListServiceQuotas</a> operation, and look for the <code>QuotaCode</code> response in the output for the quota you want.</p>"}, "ServiceCode": {"shape": "ServiceCode", "documentation": "<p>Specifies the service identifier. To find the service code value for an Amazon Web Services service, use the <a>ListServices</a> operation.</p>"}, "AwsRegion": {"shape": "AwsRegion", "documentation": "<p>Specifies the Amazon Web Services Region to which the template applies.</p>"}, "DesiredValue": {"shape": "Quo<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Specifies the new, increased value for the quota.</p>"}}}, "PutServiceQuotaIncreaseRequestIntoTemplateResponse": {"type": "structure", "members": {"ServiceQuotaIncreaseRequestInTemplate": {"shape": "ServiceQuotaIncreaseRequestInTemplate", "documentation": "<p>Information about the quota increase request.</p>"}}}, "QuotaAdjustable": {"type": "boolean"}, "QuotaArn": {"type": "string"}, "QuotaCode": {"type": "string", "max": 128, "min": 1, "pattern": "[a-zA-Z][a-zA-Z0-9-]{1,128}"}, "QuotaContextId": {"type": "string"}, "QuotaContextInfo": {"type": "structure", "members": {"ContextScope": {"shape": "QuotaContextScope", "documentation": "<p>Specifies whether the quota applies to an Amazon Web Services account, or to a resource.</p>"}, "ContextScopeType": {"shape": "QuotaContextScopeType", "documentation": "<p>When the <code>ContextScope</code> is <code>RESOURCE</code>, then this specifies the resource type of the specified resource.</p>"}, "ContextId": {"shape": "QuotaContextId", "documentation": "<p>Specifies the Amazon Web Services account or resource to which the quota applies. The value in this field depends on the context scope associated with the specified service quota.</p>"}}, "documentation": "<p>A structure that describes the context for a service quota. The context identifies what the quota applies to.</p>"}, "QuotaContextScope": {"type": "string", "enum": ["RESOURCE", "ACCOUNT"]}, "QuotaContextScopeType": {"type": "string"}, "QuotaExceededException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>You have exceeded your service quota. To perform the requested action, remove some of the relevant resources, or use Service Quotas to request a service quota increase.</p>", "exception": true}, "QuotaMetricName": {"type": "string"}, "QuotaMetricNamespace": {"type": "string"}, "QuotaName": {"type": "string"}, "QuotaPeriod": {"type": "structure", "members": {"PeriodValue": {"shape": "PeriodValue", "documentation": "<p>The value associated with the reported <code>PeriodUnit</code>.</p>"}, "PeriodUnit": {"shape": "PeriodUnit", "documentation": "<p>The time unit.</p>"}}, "documentation": "<p>Information about the quota period.</p>"}, "QuotaUnit": {"type": "string"}, "QuotaValue": {"type": "double", "max": ***********, "min": 0}, "RequestId": {"type": "string", "max": 128, "min": 1, "pattern": "[0-9a-zA-Z][a-zA-Z0-9-]{1,128}"}, "RequestServiceQuotaIncreaseRequest": {"type": "structure", "required": ["ServiceCode", "QuotaCode", "DesiredValue"], "members": {"ServiceCode": {"shape": "ServiceCode", "documentation": "<p>Specifies the service identifier. To find the service code value for an Amazon Web Services service, use the <a>ListServices</a> operation.</p>"}, "QuotaCode": {"shape": "QuotaCode", "documentation": "<p>Specifies the quota identifier. To find the quota code for a specific quota, use the <a>ListServiceQuotas</a> operation, and look for the <code>QuotaCode</code> response in the output for the quota you want.</p>"}, "DesiredValue": {"shape": "Quo<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Specifies the new, increased value for the quota.</p>"}, "ContextId": {"shape": "QuotaContextId", "documentation": "<p>Specifies the Amazon Web Services account or resource to which the quota applies. The value in this field depends on the context scope associated with the specified service quota.</p>"}}}, "RequestServiceQuotaIncreaseResponse": {"type": "structure", "members": {"RequestedQuota": {"shape": "RequestedServiceQuotaChange", "documentation": "<p>Information about the quota increase request.</p>"}}}, "RequestStatus": {"type": "string", "enum": ["PENDING", "CASE_OPENED", "APPROVED", "DENIED", "CASE_CLOSED", "NOT_APPROVED", "INVALID_REQUEST"]}, "RequestedServiceQuotaChange": {"type": "structure", "members": {"Id": {"shape": "RequestId", "documentation": "<p>The unique identifier.</p>"}, "CaseId": {"shape": "CustomerServiceEngagementId", "documentation": "<p>The case ID.</p>"}, "ServiceCode": {"shape": "ServiceCode", "documentation": "<p>Specifies the service identifier. To find the service code value for an Amazon Web Services service, use the <a>ListServices</a> operation.</p>"}, "ServiceName": {"shape": "ServiceName", "documentation": "<p>Specifies the service name.</p>"}, "QuotaCode": {"shape": "QuotaCode", "documentation": "<p>Specifies the quota identifier. To find the quota code for a specific quota, use the <a>ListServiceQuotas</a> operation, and look for the <code>QuotaCode</code> response in the output for the quota you want.</p>"}, "QuotaName": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Specifies the quota name.</p>"}, "DesiredValue": {"shape": "Quo<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The new, increased value for the quota.</p>"}, "Status": {"shape": "RequestStatus", "documentation": "<p>The state of the quota increase request.</p>"}, "Created": {"shape": "DateTime", "documentation": "<p>The date and time when the quota increase request was received and the case ID was created.</p>"}, "LastUpdated": {"shape": "DateTime", "documentation": "<p>The date and time of the most recent change.</p>"}, "Requester": {"shape": "Requester", "documentation": "<p>The IAM identity of the requester.</p>"}, "QuotaArn": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the quota.</p>"}, "GlobalQuota": {"shape": "GlobalQuota", "documentation": "<p>Indicates whether the quota is global.</p>"}, "Unit": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The unit of measurement.</p>"}, "QuotaRequestedAtLevel": {"shape": "AppliedLevelEnum", "documentation": "<p>Specifies at which level within the Amazon Web Services account the quota request applies to.</p>"}, "QuotaContext": {"shape": "QuotaContextInfo", "documentation": "<p>The context for this service quota.</p>"}}, "documentation": "<p>Information about a quota increase request.</p>"}, "RequestedServiceQuotaChangeHistoryListDefinition": {"type": "list", "member": {"shape": "RequestedServiceQuotaChange"}}, "Requester": {"type": "string"}, "ResourceAlreadyExistsException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>The specified resource already exists.</p>", "exception": true}, "ServiceCode": {"type": "string", "max": 63, "min": 1, "pattern": "[a-zA-Z][a-zA-Z0-9-]{1,63}"}, "ServiceException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>Something went wrong.</p>", "exception": true, "fault": true}, "ServiceInfo": {"type": "structure", "members": {"ServiceCode": {"shape": "ServiceCode", "documentation": "<p>Specifies the service identifier. To find the service code value for an Amazon Web Services service, use the <a>ListServices</a> operation.</p>"}, "ServiceName": {"shape": "ServiceName", "documentation": "<p>Specifies the service name.</p>"}}, "documentation": "<p>Information about an Amazon Web Service.</p>"}, "ServiceInfoListDefinition": {"type": "list", "member": {"shape": "ServiceInfo"}}, "ServiceName": {"type": "string"}, "ServiceQuota": {"type": "structure", "members": {"ServiceCode": {"shape": "ServiceCode", "documentation": "<p>Specifies the service identifier. To find the service code value for an Amazon Web Services service, use the <a>ListServices</a> operation.</p>"}, "ServiceName": {"shape": "ServiceName", "documentation": "<p>Specifies the service name.</p>"}, "QuotaArn": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the quota.</p>"}, "QuotaCode": {"shape": "QuotaCode", "documentation": "<p>Specifies the quota identifier. To find the quota code for a specific quota, use the <a>ListServiceQuotas</a> operation, and look for the <code>QuotaCode</code> response in the output for the quota you want.</p>"}, "QuotaName": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Specifies the quota name.</p>"}, "Value": {"shape": "Quo<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The quota value.</p>"}, "Unit": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The unit of measurement.</p>"}, "Adjustable": {"shape": "QuotaAdjustable", "documentation": "<p>Indicates whether the quota value can be increased.</p>"}, "GlobalQuota": {"shape": "GlobalQuota", "documentation": "<p>Indicates whether the quota is global.</p>"}, "UsageMetric": {"shape": "MetricInfo", "documentation": "<p>Information about the measurement.</p>"}, "Period": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The period of time.</p>"}, "ErrorReason": {"shape": "ErrorReason", "documentation": "<p>The error code and error reason.</p>"}, "QuotaAppliedAtLevel": {"shape": "AppliedLevelEnum", "documentation": "<p>Specifies at which level of granularity that the quota value is applied.</p>"}, "QuotaContext": {"shape": "QuotaContextInfo", "documentation": "<p>The context for this service quota.</p>"}}, "documentation": "<p>Information about a quota.</p>"}, "ServiceQuotaIncreaseRequestInTemplate": {"type": "structure", "members": {"ServiceCode": {"shape": "ServiceCode", "documentation": "<p>Specifies the service identifier. To find the service code value for an Amazon Web Services service, use the <a>ListServices</a> operation.</p>"}, "ServiceName": {"shape": "ServiceName", "documentation": "<p>Specifies the service name.</p>"}, "QuotaCode": {"shape": "QuotaCode", "documentation": "<p>Specifies the quota identifier. To find the quota code for a specific quota, use the <a>ListServiceQuotas</a> operation, and look for the <code>QuotaCode</code> response in the output for the quota you want.</p>"}, "QuotaName": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Specifies the quota name.</p>"}, "DesiredValue": {"shape": "Quo<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The new, increased value of the quota.</p>"}, "AwsRegion": {"shape": "AwsRegion", "documentation": "<p>The Amazon Web Services Region.</p>"}, "Unit": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The unit of measurement.</p>"}, "GlobalQuota": {"shape": "GlobalQuota", "documentation": "<p>Indicates whether the quota is global.</p>"}}, "documentation": "<p>Information about a quota increase request.</p>"}, "ServiceQuotaIncreaseRequestInTemplateList": {"type": "list", "member": {"shape": "ServiceQuotaIncreaseRequestInTemplate"}}, "ServiceQuotaListDefinition": {"type": "list", "member": {"shape": "ServiceQuota"}}, "ServiceQuotaTemplateAssociationStatus": {"type": "string", "enum": ["ASSOCIATED", "DISASSOCIATED"]}, "ServiceQuotaTemplateNotInUseException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>The quota request template is not associated with your organization.</p>", "exception": true}, "Statistic": {"type": "string", "max": 256, "min": 1, "pattern": "(Sum|Maximum)"}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>A string that contains a tag key. The string length should be between 1 and 128 characters. Valid characters include a-z, A-Z, 0-9, space, and the special characters _ - . : / = + @.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>A string that contains an optional tag value. The string length should be between 0 and 256 characters. Valid characters include a-z, A-Z, 0-9, space, and the special characters _ - . : / = + @.</p>"}}, "documentation": "<p>A complex data type that contains a tag key and tag value.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "TagPolicyViolationException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>The specified tag is a reserved word and cannot be used.</p>", "exception": true}, "TagResourceRequest": {"type": "structure", "required": ["ResourceARN", "Tags"], "members": {"ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) for the applied quota. You can get this information by using the Service Quotas console, or by listing the quotas using the <a href=\"https://docs.aws.amazon.com/cli/latest/reference/service-quotas/list-service-quotas.html\">list-service-quotas</a> CLI command or the <a href=\"https://docs.aws.amazon.com/servicequotas/2019-06-24/apireference/API_ListServiceQuotas.html\">ListServiceQuotas</a> Amazon Web Services API operation.</p>"}, "Tags": {"shape": "InputTags", "documentation": "<p>The tags that you want to add to the resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "TemplatesNotAvailableInRegionException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>The Service Quotas template is not available in this Amazon Web Services Region.</p>", "exception": true}, "TooManyRequestsException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>Due to throttling, the request was denied. Slow down the rate of request calls, or request an increase for this quota.</p>", "exception": true}, "TooManyTagsException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>You've exceeded the number of tags allowed for a resource. For more information, see <a href=\"https://docs.aws.amazon.com/servicequotas/latest/userguide/sq-tagging.html#sq-tagging-restrictions\">Tag restrictions</a> in the <i>Service Quotas User Guide</i>.</p>", "exception": true}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceARN", "TagKeys"], "members": {"ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) for the applied quota that you want to untag. You can get this information by using the Service Quotas console, or by listing the quotas using the <a href=\"https://docs.aws.amazon.com/cli/latest/reference/service-quotas/list-service-quotas.html\">list-service-quotas</a> CLI command or the <a href=\"https://docs.aws.amazon.com/servicequotas/2019-06-24/apireference/API_ListServiceQuotas.html\">ListServiceQuotas</a> Amazon Web Services API operation.</p>"}, "TagKeys": {"shape": "InputTagKeys", "documentation": "<p>The keys of the tags that you want to remove from the resource.</p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}}, "documentation": "<p>With Service Quotas, you can view and manage your quotas easily as your Amazon Web Services workloads grow. Quotas, also referred to as limits, are the maximum number of resources that you can create in your Amazon Web Services account. For more information, see the <a href=\"https://docs.aws.amazon.com/servicequotas/latest/userguide/\">Service Quotas User Guide</a>.</p>"}