{"version": "2.0", "metadata": {"apiVersion": "2021-01-01", "endpointPrefix": "es", "protocol": "rest-json", "serviceFullName": "Amazon OpenSearch Service", "serviceId": "OpenSearch", "signatureVersion": "v4", "uid": "opensearch-2021-01-01"}, "operations": {"AcceptInboundConnection": {"name": "AcceptInboundConnection", "http": {"method": "PUT", "requestUri": "/2021-01-01/opensearch/cc/inboundConnection/{ConnectionId}/accept"}, "input": {"shape": "AcceptInboundConnectionRequest"}, "output": {"shape": "AcceptInboundConnectionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "DisabledOperationException"}], "documentation": "<p>Allows the destination Amazon OpenSearch Service domain owner to accept an inbound cross-cluster search connection request. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/cross-cluster-search.html\">Cross-cluster search for Amazon OpenSearch Service</a>.</p>"}, "AddTags": {"name": "AddTags", "http": {"method": "POST", "requestUri": "/2021-01-01/tags"}, "input": {"shape": "AddTagsRequest"}, "errors": [{"shape": "BaseException"}, {"shape": "LimitExceededException"}, {"shape": "ValidationException"}, {"shape": "InternalException"}], "documentation": "<p>Attaches tags to an existing Amazon OpenSearch Service domain. Tags are a set of case-sensitive key-value pairs. A domain can have up to 10 tags. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/managedomains-awsresourcetagging.html\">Tagging Amazon OpenSearch Service domains</a>.</p>"}, "AssociatePackage": {"name": "AssociatePackage", "http": {"method": "POST", "requestUri": "/2021-01-01/packages/associate/{PackageID}/{DomainName}"}, "input": {"shape": "AssociatePackageRequest"}, "output": {"shape": "AssociatePackageResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Associates a package with an Amazon OpenSearch Service domain. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/custom-packages.html\">Custom packages for Amazon OpenSearch Service</a>.</p>"}, "AuthorizeVpcEndpointAccess": {"name": "AuthorizeVpcEndpointAccess", "http": {"method": "POST", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}/authorizeVpcEndpointAccess"}, "input": {"shape": "AuthorizeVpcEndpointAccessRequest"}, "output": {"shape": "AuthorizeVpcEndpointAccessResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "DisabledOperationException"}, {"shape": "LimitExceededException"}, {"shape": "ValidationException"}, {"shape": "InternalException"}, {"shape": "BaseException"}], "documentation": "<p>Provides access to an Amazon OpenSearch Service domain through the use of an interface VPC endpoint.</p>"}, "CancelServiceSoftwareUpdate": {"name": "CancelServiceSoftwareUpdate", "http": {"method": "POST", "requestUri": "/2021-01-01/opensearch/serviceSoftwareUpdate/cancel"}, "input": {"shape": "CancelServiceSoftwareUpdateRequest"}, "output": {"shape": "CancelServiceSoftwareUpdateResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Cancels a scheduled service software update for an Amazon OpenSearch Service domain. You can only perform this operation before the <code>AutomatedUpdateDate</code> and when the domain's <code>UpdateStatus</code> is <code>PENDING_UPDATE</code>. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/service-software.html\">Service software updates in Amazon OpenSearch Service</a>.</p>"}, "CreateDomain": {"name": "CreateDomain", "http": {"method": "POST", "requestUri": "/2021-01-01/opensearch/domain"}, "input": {"shape": "CreateDomainRequest"}, "output": {"shape": "CreateDomainResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "DisabledOperationException"}, {"shape": "InternalException"}, {"shape": "InvalidTypeException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ValidationException"}], "documentation": "<p>Creates an Amazon OpenSearch Service domain. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/createupdatedomains.html\">Creating and managing Amazon OpenSearch Service domains</a>.</p>"}, "CreateOutboundConnection": {"name": "CreateOutboundConnection", "http": {"method": "POST", "requestUri": "/2021-01-01/opensearch/cc/outboundConnection"}, "input": {"shape": "CreateOutboundConnectionRequest"}, "output": {"shape": "CreateOutboundConnectionResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "InternalException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "DisabledOperationException"}], "documentation": "<p>Creates a new cross-cluster search connection from a source Amazon OpenSearch Service domain to a destination domain. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/cross-cluster-search.html\">Cross-cluster search for Amazon OpenSearch Service</a>.</p>"}, "CreatePackage": {"name": "CreatePackage", "http": {"method": "POST", "requestUri": "/2021-01-01/packages"}, "input": {"shape": "CreatePackageRequest"}, "output": {"shape": "CreatePackageResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "LimitExceededException"}, {"shape": "InvalidTypeException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Creates a package for use with Amazon OpenSearch Service domains. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/custom-packages.html\">Custom packages for Amazon OpenSearch Service</a>.</p>"}, "CreateVpcEndpoint": {"name": "CreateVpcEndpoint", "http": {"method": "POST", "requestUri": "/2021-01-01/opensearch/vpcEndpoints"}, "input": {"shape": "CreateVpcEndpointRequest"}, "output": {"shape": "CreateVpcEndpointResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "LimitExceededException"}, {"shape": "InternalException"}, {"shape": "DisabledOperationException"}, {"shape": "BaseException"}], "documentation": "<p>Creates an Amazon OpenSearch Service-managed VPC endpoint.</p>"}, "DeleteDomain": {"name": "DeleteDomain", "http": {"method": "DELETE", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}"}, "input": {"shape": "DeleteDomainRequest"}, "output": {"shape": "DeleteDomainResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Deletes an Amazon OpenSearch Service domain and all of its data. You can't recover a domain after you delete it.</p>"}, "DeleteInboundConnection": {"name": "DeleteInboundConnection", "http": {"method": "DELETE", "requestUri": "/2021-01-01/opensearch/cc/inboundConnection/{ConnectionId}"}, "input": {"shape": "DeleteInboundConnectionRequest"}, "output": {"shape": "DeleteInboundConnectionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "DisabledOperationException"}], "documentation": "<p>Allows the destination Amazon OpenSearch Service domain owner to delete an existing inbound cross-cluster search connection. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/cross-cluster-search.html\">Cross-cluster search for Amazon OpenSearch Service</a>.</p>"}, "DeleteOutboundConnection": {"name": "DeleteOutboundConnection", "http": {"method": "DELETE", "requestUri": "/2021-01-01/opensearch/cc/outboundConnection/{ConnectionId}"}, "input": {"shape": "DeleteOutboundConnectionRequest"}, "output": {"shape": "DeleteOutboundConnectionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "DisabledOperationException"}], "documentation": "<p>Allows the source Amazon OpenSearch Service domain owner to delete an existing outbound cross-cluster search connection. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/cross-cluster-search.html\">Cross-cluster search for Amazon OpenSearch Service</a>.</p>"}, "DeletePackage": {"name": "DeletePackage", "http": {"method": "DELETE", "requestUri": "/2021-01-01/packages/{PackageID}"}, "input": {"shape": "DeletePackageRequest"}, "output": {"shape": "DeletePackageResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes an Amazon OpenSearch Service package. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/custom-packages.html\">Custom packages for Amazon OpenSearch Service</a>.</p>"}, "DeleteVpcEndpoint": {"name": "DeleteVpcEndpoint", "http": {"method": "DELETE", "requestUri": "/2021-01-01/opensearch/vpcEndpoints/{VpcEndpointId}"}, "input": {"shape": "DeleteVpcEndpointRequest"}, "output": {"shape": "DeleteVpcEndpointResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "DisabledOperationException"}, {"shape": "InternalException"}, {"shape": "BaseException"}], "documentation": "<p>Deletes an Amazon OpenSearch Service-managed interface VPC endpoint.</p>"}, "DescribeDomain": {"name": "DescribeDomain", "http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}"}, "input": {"shape": "DescribeDomainRequest"}, "output": {"shape": "DescribeDomainResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Describes the domain configuration for the specified Amazon OpenSearch Service domain, including the domain ID, domain service endpoint, and domain ARN.</p>"}, "DescribeDomainAutoTunes": {"name": "DescribeDomainAutoTunes", "http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}/autoTunes"}, "input": {"shape": "DescribeDomainAutoTunesRequest"}, "output": {"shape": "DescribeDomainAutoTunesResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns the list of optimizations that Auto-Tune has made to an Amazon OpenSearch Service domain. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/auto-tune.html\">Auto-Tune for Amazon OpenSearch Service</a>.</p>"}, "DescribeDomainChangeProgress": {"name": "DescribeDomainChangeProgress", "http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}/progress"}, "input": {"shape": "DescribeDomainChangeProgressRequest"}, "output": {"shape": "DescribeDomainChangeProgressResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns information about the current blue/green deployment happening on an Amazon OpenSearch Service domain. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/managedomains-configuration-changes.html\">Making configuration changes in Amazon OpenSearch Service</a>.</p>"}, "DescribeDomainConfig": {"name": "DescribeDomainConfig", "http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}/config"}, "input": {"shape": "DescribeDomainConfigRequest"}, "output": {"shape": "DescribeDomainConfigResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns the configuration of an Amazon OpenSearch Service domain.</p>"}, "DescribeDomainHealth": {"name": "DescribeDomainHealth", "http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}/health"}, "input": {"shape": "DescribeDomainHealthRequest"}, "output": {"shape": "DescribeDomainHealthResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "DisabledOperationException"}], "documentation": "<p>Returns information about domain and node health, the standby Availability Zone, number of nodes per Availability Zone, and shard count per node.</p>"}, "DescribeDomainNodes": {"name": "DescribeDomainNodes", "http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}/nodes"}, "input": {"shape": "DescribeDomainNodesRequest"}, "output": {"shape": "DescribeDomainNodesResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "DisabledOperationException"}, {"shape": "DependencyFailureException"}], "documentation": "<p>Returns information about domain and nodes, including data nodes, master nodes, ultrawarm nodes, Availability Zone(s), standby nodes, node configurations, and node states.</p>"}, "DescribeDomains": {"name": "DescribeDomains", "http": {"method": "POST", "requestUri": "/2021-01-01/opensearch/domain-info"}, "input": {"shape": "DescribeDomainsRequest"}, "output": {"shape": "DescribeDomainsResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns domain configuration information about the specified Amazon OpenSearch Service domains.</p>"}, "DescribeDryRunProgress": {"name": "DescribeDryRunProgress", "http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}/dryRun"}, "input": {"shape": "DescribeDryRunProgressRequest"}, "output": {"shape": "DescribeDryRunProgressResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "DisabledOperationException"}], "documentation": "<p>Describes the progress of a pre-update dry run analysis on an Amazon OpenSearch Service domain. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/managedomains-configuration-changes#dryrun\">Determining whether a change will cause a blue/green deployment</a>.</p>"}, "DescribeInboundConnections": {"name": "DescribeInboundConnections", "http": {"method": "POST", "requestUri": "/2021-01-01/opensearch/cc/inboundConnection/search"}, "input": {"shape": "DescribeInboundConnectionsRequest"}, "output": {"shape": "DescribeInboundConnectionsResponse"}, "errors": [{"shape": "InvalidPaginationTokenException"}, {"shape": "DisabledOperationException"}], "documentation": "<p>Lists all the inbound cross-cluster search connections for a destination (remote) Amazon OpenSearch Service domain. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/cross-cluster-search.html\">Cross-cluster search for Amazon OpenSearch Service</a>.</p>"}, "DescribeInstanceTypeLimits": {"name": "DescribeInstanceTypeLimits", "http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/instanceTypeLimits/{EngineVersion}/{InstanceType}"}, "input": {"shape": "DescribeInstanceTypeLimitsRequest"}, "output": {"shape": "DescribeInstanceTypeLimitsResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "InvalidTypeException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Describes the instance count, storage, and master node limits for a given OpenSearch or Elasticsearch version and instance type.</p>"}, "DescribeOutboundConnections": {"name": "DescribeOutboundConnections", "http": {"method": "POST", "requestUri": "/2021-01-01/opensearch/cc/outboundConnection/search"}, "input": {"shape": "DescribeOutboundConnectionsRequest"}, "output": {"shape": "DescribeOutboundConnectionsResponse"}, "errors": [{"shape": "InvalidPaginationTokenException"}, {"shape": "DisabledOperationException"}], "documentation": "<p>Lists all the outbound cross-cluster connections for a local (source) Amazon OpenSearch Service domain. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/cross-cluster-search.html\">Cross-cluster search for Amazon OpenSearch Service</a>.</p>"}, "DescribePackages": {"name": "DescribePackages", "http": {"method": "POST", "requestUri": "/2021-01-01/packages/describe"}, "input": {"shape": "DescribePackagesRequest"}, "output": {"shape": "DescribePackagesResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Describes all packages available to OpenSearch Service. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/custom-packages.html\">Custom packages for Amazon OpenSearch Service</a>.</p>"}, "DescribeReservedInstanceOfferings": {"name": "DescribeReservedInstanceOfferings", "http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/reservedInstanceOfferings"}, "input": {"shape": "DescribeReservedInstanceOfferingsRequest"}, "output": {"shape": "DescribeReservedInstanceOfferingsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "DisabledOperationException"}, {"shape": "InternalException"}], "documentation": "<p>Describes the available Amazon OpenSearch Service Reserved Instance offerings for a given Region. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/ri.html\">Reserved Instances in Amazon OpenSearch Service</a>.</p>"}, "DescribeReservedInstances": {"name": "DescribeReservedInstances", "http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/reservedInstances"}, "input": {"shape": "DescribeReservedInstancesRequest"}, "output": {"shape": "DescribeReservedInstancesResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}, {"shape": "ValidationException"}, {"shape": "DisabledOperationException"}], "documentation": "<p>Describes the Amazon OpenSearch Service instances that you have reserved in a given Region. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/ri.html\">Reserved Instances in Amazon OpenSearch Service</a>.</p>"}, "DescribeVpcEndpoints": {"name": "DescribeVpcEndpoints", "http": {"method": "POST", "requestUri": "/2021-01-01/opensearch/vpcEndpoints/describe"}, "input": {"shape": "DescribeVpcEndpointsRequest"}, "output": {"shape": "DescribeVpcEndpointsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalException"}, {"shape": "DisabledOperationException"}, {"shape": "BaseException"}], "documentation": "<p>Describes one or more Amazon OpenSearch Service-managed VPC endpoints.</p>"}, "DissociatePackage": {"name": "DissociatePackage", "http": {"method": "POST", "requestUri": "/2021-01-01/packages/dissociate/{PackageID}/{DomainName}"}, "input": {"shape": "DissociatePackageRequest"}, "output": {"shape": "DissociatePackageResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Removes a package from the specified Amazon OpenSearch Service domain. The package can't be in use with any OpenSearch index for the dissociation to succeed. The package is still available in OpenSearch Service for association later. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/custom-packages.html\">Custom packages for Amazon OpenSearch Service</a>.</p>"}, "GetCompatibleVersions": {"name": "GetCompatibleVersions", "http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/compatibleVersions"}, "input": {"shape": "GetCompatibleVersionsRequest"}, "output": {"shape": "GetCompatibleVersionsResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "DisabledOperationException"}, {"shape": "ValidationException"}, {"shape": "InternalException"}], "documentation": "<p>Returns a map of OpenSearch or Elasticsearch versions and the versions you can upgrade them to.</p>"}, "GetDomainMaintenanceStatus": {"name": "GetDomainMaintenanceStatus", "http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}/domainMaintenance"}, "input": {"shape": "GetDomainMaintenanceStatusRequest"}, "output": {"shape": "GetDomainMaintenanceStatusResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "DisabledOperationException"}], "documentation": "<p>The status of the maintenance action.</p>"}, "GetPackageVersionHistory": {"name": "GetPackageVersionHistory", "http": {"method": "GET", "requestUri": "/2021-01-01/packages/{PackageID}/history"}, "input": {"shape": "GetPackageVersionHistoryRequest"}, "output": {"shape": "GetPackageVersionHistoryResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns a list of Amazon OpenSearch Service package versions, along with their creation time, commit message, and plugin properties (if the package is a zip plugin package). For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/custom-packages.html\">Custom packages for Amazon OpenSearch Service</a>.</p>"}, "GetUpgradeHistory": {"name": "GetUpgradeHistory", "http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/upgradeDomain/{DomainName}/history"}, "input": {"shape": "GetUpgradeHistoryRequest"}, "output": {"shape": "GetUpgradeHistoryResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "DisabledOperationException"}, {"shape": "ValidationException"}, {"shape": "InternalException"}], "documentation": "<p>Retrieves the complete history of the last 10 upgrades performed on an Amazon OpenSearch Service domain.</p>"}, "GetUpgradeStatus": {"name": "GetUpgradeStatus", "http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/upgradeDomain/{DomainName}/status"}, "input": {"shape": "GetUpgradeStatusRequest"}, "output": {"shape": "GetUpgradeStatusResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "DisabledOperationException"}, {"shape": "ValidationException"}, {"shape": "InternalException"}], "documentation": "<p>Returns the most recent status of the last upgrade or upgrade eligibility check performed on an Amazon OpenSearch Service domain.</p>"}, "ListDomainMaintenances": {"name": "ListDomainMaintenances", "http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}/domainMaintenances"}, "input": {"shape": "ListDomainMaintenancesRequest"}, "output": {"shape": "ListDomainMaintenancesResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "DisabledOperationException"}], "documentation": "<p>A list of maintenance actions for the domain.</p>"}, "ListDomainNames": {"name": "ListDomainNames", "http": {"method": "GET", "requestUri": "/2021-01-01/domain"}, "input": {"shape": "ListDomainNamesRequest"}, "output": {"shape": "ListDomainNamesResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns the names of all Amazon OpenSearch Service domains owned by the current user in the active Region.</p>"}, "ListDomainsForPackage": {"name": "ListDomainsForPackage", "http": {"method": "GET", "requestUri": "/2021-01-01/packages/{PackageID}/domains"}, "input": {"shape": "ListDomainsForPackageRequest"}, "output": {"shape": "ListDomainsForPackageResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists all Amazon OpenSearch Service domains associated with a given package. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/custom-packages.html\">Custom packages for Amazon OpenSearch Service</a>.</p>"}, "ListInstanceTypeDetails": {"name": "ListInstanceTypeDetails", "http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/instanceTypeDetails/{EngineVersion}"}, "input": {"shape": "ListInstanceTypeDetailsRequest"}, "output": {"shape": "ListInstanceTypeDetailsResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists all instance types and available features for a given OpenSearch or Elasticsearch version.</p>"}, "ListPackagesForDomain": {"name": "ListPackagesForDomain", "http": {"method": "GET", "requestUri": "/2021-01-01/domain/{DomainName}/packages"}, "input": {"shape": "ListPackagesForDomainRequest"}, "output": {"shape": "ListPackagesForDomainResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists all packages associated with an Amazon OpenSearch Service domain. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/custom-packages.html\">Custom packages for Amazon OpenSearch Service</a>.</p>"}, "ListScheduledActions": {"name": "ListScheduledActions", "http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}/scheduledActions"}, "input": {"shape": "ListScheduledActionsRequest"}, "output": {"shape": "ListScheduledActionsResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidPaginationTokenException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves a list of configuration changes that are scheduled for a domain. These changes can be <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/service-software.html\">service software updates</a> or <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/auto-tune.html#auto-tune-types\">blue/green Auto-Tune enhancements</a>.</p>"}, "ListTags": {"name": "ListTags", "http": {"method": "GET", "requestUri": "/2021-01-01/tags/"}, "input": {"shape": "ListTagsRequest"}, "output": {"shape": "ListTagsResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalException"}], "documentation": "<p>Returns all resource tags for an Amazon OpenSearch Service domain. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/managedomains-awsresourcetagging.html\">Tagging Amazon OpenSearch Service domains</a>.</p>"}, "ListVersions": {"name": "ListVersions", "http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/versions"}, "input": {"shape": "ListVersionsRequest"}, "output": {"shape": "ListVersionsResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists all versions of OpenSearch and Elasticsearch that Amazon OpenSearch Service supports.</p>"}, "ListVpcEndpointAccess": {"name": "ListVpcEndpointAccess", "http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}/listVpcEndpointAccess"}, "input": {"shape": "ListVpcEndpointAccessRequest"}, "output": {"shape": "ListVpcEndpointAccessResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "DisabledOperationException"}, {"shape": "InternalException"}, {"shape": "BaseException"}], "documentation": "<p>Retrieves information about each Amazon Web Services principal that is allowed to access a given Amazon OpenSearch Service domain through the use of an interface VPC endpoint.</p>"}, "ListVpcEndpoints": {"name": "ListVpcEndpoints", "http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/vpcEndpoints"}, "input": {"shape": "ListVpcEndpointsRequest"}, "output": {"shape": "ListVpcEndpointsResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "DisabledOperationException"}, {"shape": "BaseException"}], "documentation": "<p>Retrieves all Amazon OpenSearch Service-managed VPC endpoints in the current Amazon Web Services account and Region.</p>"}, "ListVpcEndpointsForDomain": {"name": "ListVpcEndpointsForDomain", "http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}/vpcEndpoints"}, "input": {"shape": "ListVpcEndpointsForDomainRequest"}, "output": {"shape": "ListVpcEndpointsForDomainResponse"}, "errors": [{"shape": "InternalException"}, {"shape": "DisabledOperationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "BaseException"}], "documentation": "<p>Retrieves all Amazon OpenSearch Service-managed VPC endpoints associated with a particular domain.</p>"}, "PurchaseReservedInstanceOffering": {"name": "PurchaseReservedInstanceOffering", "http": {"method": "POST", "requestUri": "/2021-01-01/opensearch/purchaseReservedInstanceOffering"}, "input": {"shape": "PurchaseReservedInstanceOfferingRequest"}, "output": {"shape": "PurchaseReservedInstanceOfferingResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "LimitExceededException"}, {"shape": "DisabledOperationException"}, {"shape": "ValidationException"}, {"shape": "InternalException"}], "documentation": "<p>Allows you to purchase Amazon OpenSearch Service Reserved Instances.</p>"}, "RejectInboundConnection": {"name": "RejectInboundConnection", "http": {"method": "PUT", "requestUri": "/2021-01-01/opensearch/cc/inboundConnection/{ConnectionId}/reject"}, "input": {"shape": "RejectInboundConnectionRequest"}, "output": {"shape": "RejectInboundConnectionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "DisabledOperationException"}], "documentation": "<p>Allows the remote Amazon OpenSearch Service domain owner to reject an inbound cross-cluster connection request.</p>"}, "RemoveTags": {"name": "RemoveTags", "http": {"method": "POST", "requestUri": "/2021-01-01/tags-removal"}, "input": {"shape": "RemoveTagsRequest"}, "errors": [{"shape": "BaseException"}, {"shape": "ValidationException"}, {"shape": "InternalException"}], "documentation": "<p>Removes the specified set of tags from an Amazon OpenSearch Service domain. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/managedomains.html#managedomains-awsresorcetagging\"> Tagging Amazon OpenSearch Service domains</a>.</p>"}, "RevokeVpcEndpointAccess": {"name": "RevokeVpcEndpointAccess", "http": {"method": "POST", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}/revokeVpcEndpointAccess"}, "input": {"shape": "RevokeVpcEndpointAccessRequest"}, "output": {"shape": "RevokeVpcEndpointAccessResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "DisabledOperationException"}, {"shape": "InternalException"}, {"shape": "BaseException"}], "documentation": "<p>Revokes access to an Amazon OpenSearch Service domain that was provided through an interface VPC endpoint.</p>"}, "StartDomainMaintenance": {"name": "StartDomainMaintenance", "http": {"method": "POST", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}/domainMaintenance"}, "input": {"shape": "StartDomainMaintenanceRequest"}, "output": {"shape": "StartDomainMaintenanceResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "DisabledOperationException"}], "documentation": "<p>Starts the node maintenance process on the data node. These processes can include a node reboot, an Opensearch or Elasticsearch process restart, or a Dashboard or Kibana restart.</p>"}, "StartServiceSoftwareUpdate": {"name": "StartServiceSoftwareUpdate", "http": {"method": "POST", "requestUri": "/2021-01-01/opensearch/serviceSoftwareUpdate/start"}, "input": {"shape": "StartServiceSoftwareUpdateRequest"}, "output": {"shape": "StartServiceSoftwareUpdateResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Schedules a service software update for an Amazon OpenSearch Service domain. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/service-software.html\">Service software updates in Amazon OpenSearch Service</a>.</p>"}, "UpdateDomainConfig": {"name": "UpdateDomainConfig", "http": {"method": "POST", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}/config"}, "input": {"shape": "UpdateDomainConfigRequest"}, "output": {"shape": "UpdateDomainConfigResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "InvalidTypeException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Modifies the cluster configuration of the specified Amazon OpenSearch Service domain.</p>"}, "UpdatePackage": {"name": "UpdatePackage", "http": {"method": "POST", "requestUri": "/2021-01-01/packages/update"}, "input": {"shape": "UpdatePackageRequest"}, "output": {"shape": "UpdatePackageResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Updates a package for use with Amazon OpenSearch Service domains. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/custom-packages.html\">Custom packages for Amazon OpenSearch Service</a>.</p>"}, "UpdateScheduledAction": {"name": "UpdateScheduledAction", "http": {"method": "PUT", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}/scheduledAction/update"}, "input": {"shape": "UpdateScheduledActionRequest"}, "output": {"shape": "UpdateScheduledActionResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "SlotNotAvailableException"}, {"shape": "ConflictException"}, {"shape": "LimitExceededException"}, {"shape": "ValidationException"}], "documentation": "<p>Reschedules a planned domain configuration change for a later time. This change can be a scheduled <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/service-software.html\">service software update</a> or a <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/auto-tune.html#auto-tune-types\">blue/green Auto-Tune enhancement</a>.</p>"}, "UpdateVpcEndpoint": {"name": "UpdateVpcEndpoint", "http": {"method": "POST", "requestUri": "/2021-01-01/opensearch/vpcEndpoints/update"}, "input": {"shape": "UpdateVpcEndpointRequest"}, "output": {"shape": "UpdateVpcEndpointResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "DisabledOperationException"}, {"shape": "InternalException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "BaseException"}], "documentation": "<p>Modifies an Amazon OpenSearch Service-managed interface VPC endpoint.</p>"}, "UpgradeDomain": {"name": "UpgradeDomain", "http": {"method": "POST", "requestUri": "/2021-01-01/opensearch/upgradeDomain"}, "input": {"shape": "UpgradeDomainRequest"}, "output": {"shape": "UpgradeDomainResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "DisabledOperationException"}, {"shape": "ValidationException"}, {"shape": "InternalException"}], "documentation": "<p>Allows you to either upgrade your Amazon OpenSearch Service domain or perform an upgrade eligibility check to a compatible version of OpenSearch or Elasticsearch.</p>"}}, "shapes": {"ARN": {"type": "string", "documentation": "<p>The Amazon Resource Name (ARN) of the domain. See <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/index.html\">Identifiers for IAM Entities </a> in <i>Using AWS Identity and Access Management</i> for more information. </p>", "max": 2048, "min": 20, "pattern": ".*"}, "AWSAccount": {"type": "string", "pattern": "^[0-9]+$"}, "AWSDomainInformation": {"type": "structure", "required": ["DomainName"], "members": {"OwnerId": {"shape": "OwnerId", "documentation": "<p>The Amazon Web Services account ID of the domain owner.</p>"}, "DomainName": {"shape": "DomainName", "documentation": "<p>Name of the domain.</p>"}, "Region": {"shape": "Region", "documentation": "<p>The Amazon Web Services Region in which the domain is located.</p>"}}, "documentation": "<p>Information about an Amazon OpenSearch Service domain.</p>"}, "AcceptInboundConnectionRequest": {"type": "structure", "required": ["ConnectionId"], "members": {"ConnectionId": {"shape": "ConnectionId", "documentation": "<p>The ID of the inbound connection to accept.</p>", "location": "uri", "locationName": "ConnectionId"}}, "documentation": "<p>Container for the parameters to the <code>AcceptInboundConnection</code> operation.</p>"}, "AcceptInboundConnectionResponse": {"type": "structure", "members": {"Connection": {"shape": "InboundConnection", "documentation": "<p>Information about the accepted inbound connection.</p>"}}, "documentation": "<p>Contains details about the accepted inbound connection.</p>"}, "AccessDeniedException": {"type": "structure", "members": {}, "documentation": "<p>An error occurred because you don't have permissions to access the resource.</p>", "error": {"httpStatusCode": 403}, "exception": true}, "AccessPoliciesStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "PolicyDocument", "documentation": "<p>The access policy configured for the domain. Access policies can be resource-based, IP-based, or IAM-based. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/createupdatedomains.html#createdomain-configure-access-policies\">Configuring access policies</a>.</p>"}, "Status": {"shape": "OptionStatus", "documentation": "<p>The status of the access policy for the domain.</p>"}}, "documentation": "<p>The configured access rules for the domain's search endpoint, and the current status of those rules.</p>"}, "ActionSeverity": {"type": "string", "enum": ["HIGH", "MEDIUM", "LOW"]}, "ActionStatus": {"type": "string", "enum": ["PENDING_UPDATE", "IN_PROGRESS", "FAILED", "COMPLETED", "NOT_ELIGIBLE", "ELIGIBLE"]}, "ActionType": {"type": "string", "enum": ["SERVICE_SOFTWARE_UPDATE", "JVM_HEAP_SIZE_TUNING", "JVM_YOUNG_GEN_TUNING"]}, "AddTagsRequest": {"type": "structure", "required": ["ARN", "TagList"], "members": {"ARN": {"shape": "ARN", "documentation": "<p>Amazon Resource Name (ARN) for the OpenSearch Service domain to which you want to attach resource tags.</p>"}, "TagList": {"shape": "TagList", "documentation": "<p>List of resource tags.</p>"}}, "documentation": "<p>Container for the parameters to the <code>AddTags</code> operation. Specifies the tags to attach to the domain.</p>"}, "AdditionalLimit": {"type": "structure", "members": {"LimitName": {"shape": "LimitName", "documentation": "<ul> <li> <p> <code>MaximumNumberOfDataNodesSupported</code> - This attribute only applies to master nodes and specifies the maximum number of data nodes of a given instance type a master node can support.</p> </li> <li> <p> <code>MaximumNumberOfDataNodesWithoutMasterNode</code> - This attribute only applies to data nodes and specifies the maximum number of data nodes of a given instance type can exist without a master node governing them.</p> </li> </ul>"}, "LimitValues": {"shape": "LimitValueList", "documentation": "<p> The values of the additional instance type limits.</p>"}}, "documentation": "<p> List of limits that are specific to a given instance type.</p>"}, "AdditionalLimitList": {"type": "list", "member": {"shape": "AdditionalLimit"}}, "AdvancedOptions": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}, "documentation": "<p>Exposes native OpenSearch configuration values from <code>opensearch.yml</code>. The following advanced options are available: </p> <ul> <li> <p>Allows references to indexes in an HTTP request body. Must be <code>false</code> when configuring access to individual sub-resources. Default is <code>true</code>.</p> </li> <li> <p>Specifies the percentage of heap space allocated to field data. Default is unbounded.</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/createupdatedomains.html#createdomain-configure-advanced-options\">Advanced cluster parameters</a>.</p>"}, "AdvancedOptionsStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "AdvancedOptions", "documentation": "<p>The status of advanced options for the specified domain.</p>"}, "Status": {"shape": "OptionStatus", "documentation": "<p>The status of advanced options for the specified domain.</p>"}}, "documentation": "<p>Status of the advanced options for the specified domain. The following options are available: </p> <ul> <li> <p> <code>\"rest.action.multi.allow_explicit_index\": \"true\" | \"false\"</code> - Note the use of a string rather than a boolean. Specifies whether explicit references to indexes are allowed inside the body of HTTP requests. If you want to configure access policies for domain sub-resources, such as specific indexes and domain APIs, you must disable this property. Default is true.</p> </li> <li> <p> <code>\"indices.fielddata.cache.size\": \"80\" </code> - Note the use of a string rather than a boolean. Specifies the percentage of heap space allocated to field data. Default is unbounded.</p> </li> <li> <p> <code>\"indices.query.bool.max_clause_count\": \"1024\"</code> - Note the use of a string rather than a boolean. Specifies the maximum number of clauses allowed in a Lucene boolean query. Default is 1,024. Queries with more than the permitted number of clauses result in a <code>TooManyClauses</code> error.</p> </li> <li> <p> <code>\"override_main_response_version\": \"true\" | \"false\"</code> - Note the use of a string rather than a boolean. Specifies whether the domain reports its version as 7.10 to allow Elasticsearch OSS clients and plugins to continue working with it. Default is false when creating a domain and true when upgrading a domain.</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/createupdatedomains.html#createdomain-configure-advanced-options\">Advanced cluster parameters</a>.</p>"}, "AdvancedSecurityOptions": {"type": "structure", "members": {"Enabled": {"shape": "Boolean", "documentation": "<p>True if fine-grained access control is enabled.</p>"}, "InternalUserDatabaseEnabled": {"shape": "Boolean", "documentation": "<p>True if the internal user database is enabled.</p>"}, "SAMLOptions": {"shape": "SAMLOptionsOutput", "documentation": "<p>Container for information about the SAML configuration for OpenSearch Dashboards.</p>"}, "AnonymousAuthDisableDate": {"shape": "DisableTimestamp", "documentation": "<p>Date and time when the migration period will be disabled. Only necessary when <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/fgac.html#fgac-enabling-existing\">enabling fine-grained access control on an existing domain</a>.</p>"}, "AnonymousAuthEnabled": {"shape": "Boolean", "documentation": "<p>True if a 30-day migration period is enabled, during which administrators can create role mappings. Only necessary when <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/fgac.html#fgac-enabling-existing\">enabling fine-grained access control on an existing domain</a>.</p>"}}, "documentation": "<p>Container for fine-grained access control settings.</p>"}, "AdvancedSecurityOptionsInput": {"type": "structure", "members": {"Enabled": {"shape": "Boolean", "documentation": "<p>True to enable fine-grained access control.</p>"}, "InternalUserDatabaseEnabled": {"shape": "Boolean", "documentation": "<p>True to enable the internal user database.</p>"}, "MasterUserOptions": {"shape": "MasterUserOptions", "documentation": "<p>Container for information about the master user.</p>"}, "SAMLOptions": {"shape": "SAMLOptionsInput", "documentation": "<p>Container for information about the SAML configuration for OpenSearch Dashboards.</p>"}, "AnonymousAuthEnabled": {"shape": "Boolean", "documentation": "<p>True to enable a 30-day migration period during which administrators can create role mappings. Only necessary when <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/fgac.html#fgac-enabling-existing\">enabling fine-grained access control on an existing domain</a>.</p>"}}, "documentation": "<p>Options for enabling and configuring fine-grained access control. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/fgac.html\">Fine-grained access control in Amazon OpenSearch Service</a>.</p>"}, "AdvancedSecurityOptionsStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "AdvancedSecurityOptions", "documentation": "<p>Container for fine-grained access control settings.</p>"}, "Status": {"shape": "OptionStatus", "documentation": "<p>Status of the fine-grained access control settings for a domain.</p>"}}, "documentation": "<p>The status of fine-grained access control settings for a domain.</p>"}, "AssociatePackageRequest": {"type": "structure", "required": ["PackageID", "DomainName"], "members": {"PackageID": {"shape": "PackageID", "documentation": "<p>Internal ID of the package to associate with a domain. Use <code>DescribePackages</code> to find this value. </p>", "location": "uri", "locationName": "PackageID"}, "DomainName": {"shape": "DomainName", "documentation": "<p>Name of the domain to associate the package with.</p>", "location": "uri", "locationName": "DomainName"}}, "documentation": "<p>Container for the request parameters to the <code>AssociatePackage</code> operation.</p>"}, "AssociatePackageResponse": {"type": "structure", "members": {"DomainPackageDetails": {"shape": "DomainPackageDetails", "documentation": "<p>Information about a package that is associated with a domain.</p>"}}, "documentation": "<p>Container for the response returned by the <code>AssociatePackage</code> operation.</p>"}, "AuthorizeVpcEndpointAccessRequest": {"type": "structure", "required": ["DomainName", "Account"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>The name of the OpenSearch Service domain to provide access to.</p>", "location": "uri", "locationName": "DomainName"}, "Account": {"shape": "AWSAccount", "documentation": "<p>The Amazon Web Services account ID to grant access to.</p>"}}}, "AuthorizeVpcEndpointAccessResponse": {"type": "structure", "required": ["AuthorizedPrincipal"], "members": {"AuthorizedPrincipal": {"shape": "AuthorizedPrincipal", "documentation": "<p>Information about the Amazon Web Services account or service that was provided access to the domain.</p>"}}}, "AuthorizedPrincipal": {"type": "structure", "members": {"PrincipalType": {"shape": "PrincipalType", "documentation": "<p>The type of principal.</p>"}, "Principal": {"shape": "String", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_policies_elements_principal.html\">IAM principal</a> that is allowed access to the domain.</p>"}}, "documentation": "<p>Information about an Amazon Web Services account or service that has access to an Amazon OpenSearch Service domain through the use of an interface VPC endpoint.</p>"}, "AuthorizedPrincipalList": {"type": "list", "member": {"shape": "AuthorizedPrincipal"}}, "AutoTune": {"type": "structure", "members": {"AutoTuneType": {"shape": "AutoTuneType", "documentation": "<p>The type of Auto-Tune action.</p>"}, "AutoTuneDetails": {"shape": "AutoTuneDetails", "documentation": "<p>Details about an Auto-Tune action.</p>"}}, "documentation": "<p>Information about an Auto-Tune action. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/auto-tune.html\">Auto-Tune for Amazon OpenSearch Service</a>.</p>"}, "AutoTuneDate": {"type": "timestamp", "documentation": "<p>The timestamp of the Auto-Tune action scheduled for the domain.</p>"}, "AutoTuneDesiredState": {"type": "string", "documentation": "<p>The Auto-Tune desired state. Valid values are ENABLED and DISABLED.</p>", "enum": ["ENABLED", "DISABLED"]}, "AutoTuneDetails": {"type": "structure", "members": {"ScheduledAutoTuneDetails": {"shape": "ScheduledAutoTuneDetails", "documentation": "<p>Container for details about a scheduled Auto-Tune action.</p>"}}, "documentation": "<p>Specifies details about a scheduled Auto-Tune action. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/auto-tune.html\">Auto-Tune for Amazon OpenSearch Service</a>.</p>"}, "AutoTuneList": {"type": "list", "member": {"shape": "AutoTune"}}, "AutoTuneMaintenanceSchedule": {"type": "structure", "members": {"StartAt": {"shape": "StartAt", "documentation": "<p>The Epoch timestamp at which the Auto-Tune maintenance schedule starts.</p>"}, "Duration": {"shape": "Duration", "documentation": "<p>The duration of the maintenance schedule. For example, <code>\"Duration\": {\"Value\": 2, \"Unit\": \"HOURS\"}</code>.</p>"}, "CronExpressionForRecurrence": {"shape": "String", "documentation": "<p>A cron expression for a recurring maintenance schedule during which Auto-Tune can deploy changes.</p>"}}, "documentation": "<note> <p>This object is deprecated. Use the domain's <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/off-peak.html\">off-peak window</a> to schedule Auto-Tune optimizations. For migration instructions, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/off-peak.html#off-peak-migrate\">Migrating from Auto-Tune maintenance windows</a>.</p> </note> <p>The Auto-Tune maintenance schedule. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/auto-tune.html\">Auto-Tune for Amazon OpenSearch Service</a>.</p>"}, "AutoTuneMaintenanceScheduleList": {"type": "list", "member": {"shape": "AutoTuneMaintenanceSchedule"}, "max": 100}, "AutoTuneOptions": {"type": "structure", "members": {"DesiredState": {"shape": "AutoTuneDesiredState", "documentation": "<p>Whether Auto-Tune is enabled or disabled.</p>"}, "RollbackOnDisable": {"shape": "RollbackOnDisable", "documentation": "<p>When disabling Auto-Tune, specify <code>NO_ROLLBACK</code> to retain all prior Auto-Tune settings or <code>DEFAULT_ROLLBACK</code> to revert to the OpenSearch Service defaults. If you specify <code>DEFAULT_ROLLBACK</code>, you must include a <code>MaintenanceSchedule</code> in the request. Otherwise, OpenSearch Service is unable to perform the rollback.</p>"}, "MaintenanceSchedules": {"shape": "AutoTuneMaintenanceScheduleList", "documentation": "<p>DEPRECATED. Use <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/off-peak.html\">off-peak window</a> instead.</p> <p>A list of maintenance schedules during which Auto-Tune can deploy changes.</p>"}, "UseOffPeakWindow": {"shape": "Boolean", "documentation": "<p>Whether to use the domain's <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/APIReference/API_OffPeakWindow.html\">off-peak window</a> to deploy configuration changes on the domain rather than a maintenance schedule.</p>"}}, "documentation": "<p>Auto-Tune settings when updating a domain. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/auto-tune.html\">Auto-Tune for Amazon OpenSearch Service</a>.</p>"}, "AutoTuneOptionsInput": {"type": "structure", "members": {"DesiredState": {"shape": "AutoTuneDesiredState", "documentation": "<p>Whether Auto-Tune is enabled or disabled.</p>"}, "MaintenanceSchedules": {"shape": "AutoTuneMaintenanceScheduleList", "documentation": "<p>A list of maintenance schedules during which Auto-Tune can deploy changes. Maintenance windows are deprecated and have been replaced with <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/off-peak.html\">off-peak windows</a>.</p>"}, "UseOffPeakWindow": {"shape": "Boolean", "documentation": "<p>Whether to schedule Auto-Tune optimizations that require blue/green deployments during the domain's configured daily off-peak window.</p>"}}, "documentation": "<p>Options for configuring Auto-Tune. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/auto-tune.html\">Auto-Tune for Amazon OpenSearch Service</a> </p>"}, "AutoTuneOptionsOutput": {"type": "structure", "members": {"State": {"shape": "AutoTuneState", "documentation": "<p>The current state of Auto-Tune on the domain.</p>"}, "ErrorMessage": {"shape": "String", "documentation": "<p>Any errors that occurred while enabling or disabling Auto-Tune.</p>"}, "UseOffPeakWindow": {"shape": "Boolean", "documentation": "<p>Whether the domain's off-peak window will be used to deploy Auto-Tune changes rather than a maintenance schedule.</p>"}}, "documentation": "<p>The Auto-Tune settings for a domain, displayed when enabling or disabling Auto-Tune.</p>"}, "AutoTuneOptionsStatus": {"type": "structure", "members": {"Options": {"shape": "AutoTuneOptions", "documentation": "<p>Auto-Tune settings for updating a domain.</p>"}, "Status": {"shape": "AutoTuneStatus", "documentation": "<p>The current status of Auto-Tune for a domain.</p>"}}, "documentation": "<p>The Auto-Tune status for the domain.</p>"}, "AutoTuneState": {"type": "string", "documentation": "<p>The Auto-Tune state for the domain. For valid states see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/auto-tune.html\">Auto-Tune for Amazon OpenSearch Service</a>. </p>", "enum": ["ENABLED", "DISABLED", "ENABLE_IN_PROGRESS", "DISABLE_IN_PROGRESS", "DISABLED_AND_ROLLBACK_SCHEDULED", "DISABLED_AND_ROLLBACK_IN_PROGRESS", "DISABLED_AND_ROLLBACK_COMPLETE", "DISABLED_AND_ROLLBACK_ERROR", "ERROR"]}, "AutoTuneStatus": {"type": "structure", "required": ["CreationDate", "UpdateDate", "State"], "members": {"CreationDate": {"shape": "UpdateTimestamp", "documentation": "<p>Date and time when Auto-Tune was enabled for the domain.</p>"}, "UpdateDate": {"shape": "UpdateTimestamp", "documentation": "<p>Date and time when the Auto-Tune options were last updated for the domain.</p>"}, "UpdateVersion": {"shape": "UIntValue", "documentation": "<p>The latest version of the Auto-Tune options.</p>"}, "State": {"shape": "AutoTuneState", "documentation": "<p>The current state of Auto-Tune on the domain.</p>"}, "ErrorMessage": {"shape": "String", "documentation": "<p>Any errors that occurred while enabling or disabling Auto-Tune.</p>"}, "PendingDeletion": {"shape": "Boolean", "documentation": "<p>Indicates whether the domain is being deleted.</p>"}}, "documentation": "<p>The current status of Auto-Tune for the domain. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/auto-tune.html\">Auto-Tune for Amazon OpenSearch Service</a>.</p>"}, "AutoTuneType": {"type": "string", "documentation": "<p>Specifies the Auto-Tune type. Valid value is SCHEDULED_ACTION.</p>", "enum": ["SCHEDULED_ACTION"]}, "AvailabilityZone": {"type": "string", "max": 15, "min": 1}, "AvailabilityZoneInfo": {"type": "structure", "members": {"AvailabilityZoneName": {"shape": "AvailabilityZone", "documentation": "<p>The name of the Availability Zone.</p>"}, "ZoneStatus": {"shape": "ZoneStatus", "documentation": "<p>The current state of the Availability Zone. Current options are <code>Active</code> and <code>StandBy</code>.</p> <ul> <li> <p> <code>Active</code> - Data nodes in the Availability Zone are in use.</p> </li> <li> <p> <code>StandBy</code> - Data nodes in the Availability Zone are in a standby state.</p> </li> <li> <p> <code>NotAvailable</code> - Unable to retrieve information.</p> </li> </ul>"}, "ConfiguredDataNodeCount": {"shape": "NumberOfNodes", "documentation": "<p>The total number of data nodes configured in the Availability Zone.</p>"}, "AvailableDataNodeCount": {"shape": "NumberOfNodes", "documentation": "<p>The number of data nodes active in the Availability Zone.</p>"}, "TotalShards": {"shape": "NumberOfShards", "documentation": "<p>The total number of primary and replica shards in the Availability Zone.</p>"}, "TotalUnAssignedShards": {"shape": "NumberOfShards", "documentation": "<p>The total number of primary and replica shards that aren't allocated to any of the nodes in the Availability Zone.</p>"}}, "documentation": "<p>Information about an Availability Zone on a domain.</p>"}, "AvailabilityZoneInfoList": {"type": "list", "member": {"shape": "AvailabilityZoneInfo"}}, "AvailabilityZoneList": {"type": "list", "member": {"shape": "AvailabilityZone"}}, "BackendRole": {"type": "string", "max": 256, "min": 1}, "BaseException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage", "documentation": "<p>A description of the error.</p>"}}, "documentation": "<p>An error occurred while processing the request.</p>", "exception": true}, "Boolean": {"type": "boolean"}, "CancelServiceSoftwareUpdateRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>Name of the OpenSearch Service domain that you want to cancel the service software update on.</p>"}}, "documentation": "<p>Container for the request parameters to cancel a service software update.</p>"}, "CancelServiceSoftwareUpdateResponse": {"type": "structure", "members": {"ServiceSoftwareOptions": {"shape": "ServiceSoftwareOptions", "documentation": "<p>Container for the state of your domain relative to the latest service software.</p>"}}, "documentation": "<p>Container for the response to a <code>CancelServiceSoftwareUpdate</code> operation. Contains the status of the update.</p>"}, "ChangeProgressDetails": {"type": "structure", "members": {"ChangeId": {"shape": "GUID", "documentation": "<p>The ID of the configuration change.</p>"}, "Message": {"shape": "Message", "documentation": "<p>A message corresponding to the status of the configuration change.</p>"}}, "documentation": "<p>Container for information about a configuration change happening on a domain.</p>"}, "ChangeProgressStage": {"type": "structure", "members": {"Name": {"shape": "ChangeProgressStageName", "documentation": "<p>The name of the stage.</p>"}, "Status": {"shape": "ChangeProgressStageStatus", "documentation": "<p>The status of the stage.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the stage.</p>"}, "LastUpdated": {"shape": "LastUpdated", "documentation": "<p>The most recent updated timestamp of the stage.</p>"}}, "documentation": "<p>Progress details for each stage of a domain update.</p>"}, "ChangeProgressStageList": {"type": "list", "member": {"shape": "ChangeProgressStage"}, "documentation": "<p>The list of progress stages of a specific domain configuration change.</p>"}, "ChangeProgressStageName": {"type": "string", "max": 64, "min": 1}, "ChangeProgressStageStatus": {"type": "string", "max": 256, "min": 1}, "ChangeProgressStatusDetails": {"type": "structure", "members": {"ChangeId": {"shape": "GUID", "documentation": "<p>The unique change identifier associated with a specific domain configuration change.</p>"}, "StartTime": {"shape": "UpdateTimestamp", "documentation": "<p>The time at which the configuration change is made on the domain.</p>"}, "Status": {"shape": "OverallChangeStatus", "documentation": "<p>The overall status of the domain configuration change.</p>"}, "PendingProperties": {"shape": "StringList", "documentation": "<p>The list of properties in the domain configuration change that are still pending.</p>"}, "CompletedProperties": {"shape": "StringList", "documentation": "<p>The list of properties in the domain configuration change that have completed.</p>"}, "TotalNumberOfStages": {"shape": "TotalNumberOfStages", "documentation": "<p>The total number of stages required for the configuration change.</p>"}, "ChangeProgressStages": {"shape": "ChangeProgressStageList", "documentation": "<p>The specific stages that the domain is going through to perform the configuration change.</p>"}}, "documentation": "<p>The progress details of a specific domain configuration change.</p>"}, "ClientToken": {"type": "string", "max": 64, "min": 1}, "CloudWatchLogsLogGroupArn": {"type": "string", "documentation": "<p>ARN of the Cloudwatch log group to publish logs to.</p>", "max": 2048, "min": 20, "pattern": ".*"}, "ClusterConfig": {"type": "structure", "members": {"InstanceType": {"shape": "OpenSearchPartitionInstanceType", "documentation": "<p>Instance type of data nodes in the cluster.</p>"}, "InstanceCount": {"shape": "IntegerClass", "documentation": "<p>Number of data nodes in the cluster. This number must be greater than 1, otherwise you receive a validation exception.</p>"}, "DedicatedMasterEnabled": {"shape": "Boolean", "documentation": "<p>Indicates whether dedicated master nodes are enabled for the cluster.<code>True</code> if the cluster will use a dedicated master node.<code>False</code> if the cluster will not.</p>"}, "ZoneAwarenessEnabled": {"shape": "Boolean", "documentation": "<p>Indicates whether multiple Availability Zones are enabled. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/managedomains-multiaz.html\">Configuring a multi-AZ domain in Amazon OpenSearch Service</a>.</p>"}, "ZoneAwarenessConfig": {"shape": "ZoneAwarenessConfig", "documentation": "<p>Container for zone awareness configuration options. Only required if <code>ZoneAwarenessEnabled</code> is <code>true</code>.</p>"}, "DedicatedMasterType": {"shape": "OpenSearchPartitionInstanceType", "documentation": "<p>OpenSearch Service instance type of the dedicated master nodes in the cluster.</p>"}, "DedicatedMasterCount": {"shape": "IntegerClass", "documentation": "<p>Number of dedicated master nodes in the cluster. This number must be greater than 2 and not 4, otherwise you receive a validation exception.</p>"}, "WarmEnabled": {"shape": "Boolean", "documentation": "<p>Whether to enable warm storage for the cluster.</p>"}, "WarmType": {"shape": "OpenSearchWarmPartitionInstanceType", "documentation": "<p>The instance type for the cluster's warm nodes.</p>"}, "WarmCount": {"shape": "IntegerClass", "documentation": "<p>The number of warm nodes in the cluster.</p>"}, "ColdStorageOptions": {"shape": "ColdStorageOptions", "documentation": "<p>Container for cold storage configuration options.</p>"}, "MultiAZWithStandbyEnabled": {"shape": "Boolean", "documentation": "<p>A boolean that indicates whether a multi-AZ domain is turned on with a standby AZ. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/managedomains-multiaz.html\">Configuring a multi-AZ domain in Amazon OpenSearch Service</a>. </p>"}}, "documentation": "<p>Container for the cluster configuration of an OpenSearch Service domain. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/createupdatedomains.html\">Creating and managing Amazon OpenSearch Service domains</a>.</p>"}, "ClusterConfigStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "ClusterConfig", "documentation": "<p>Cluster configuration options for the specified domain.</p>"}, "Status": {"shape": "OptionStatus", "documentation": "<p>The status of cluster configuration options for the specified domain.</p>"}}, "documentation": "<p>The cluster configuration status for a domain.</p>"}, "CognitoOptions": {"type": "structure", "members": {"Enabled": {"shape": "Boolean", "documentation": "<p>Whether to enable or disable Amazon Cognito authentication for OpenSearch Dashboards.</p>"}, "UserPoolId": {"shape": "UserPoolId", "documentation": "<p>The Amazon Cognito user pool ID that you want OpenSearch Service to use for OpenSearch Dashboards authentication.</p>"}, "IdentityPoolId": {"shape": "IdentityPoolId", "documentation": "<p>The Amazon Cognito identity pool ID that you want OpenSearch Service to use for OpenSearch Dashboards authentication.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The <code>AmazonOpenSearchServiceCognitoAccess</code> role that allows OpenSearch Service to configure your user pool and identity pool.</p>"}}, "documentation": "<p>Container for the parameters required to enable Cognito authentication for an OpenSearch Service domain. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/cognito-auth.html\">Configuring Amazon Cognito authentication for OpenSearch Dashboards</a>.</p>"}, "CognitoOptionsStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "CognitoOptions", "documentation": "<p>Cognito options for the specified domain.</p>"}, "Status": {"shape": "OptionStatus", "documentation": "<p>The status of the Cognito options for the specified domain.</p>"}}, "documentation": "<p>The status of the Cognito options for the specified domain.</p>"}, "ColdStorageOptions": {"type": "structure", "required": ["Enabled"], "members": {"Enabled": {"shape": "Boolean", "documentation": "<p>Whether to enable or disable cold storage on the domain.</p>"}}, "documentation": "<p>Container for the parameters required to enable cold storage for an OpenSearch Service domain. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/cold-storage.html\">Cold storage for Amazon OpenSearch Service</a>.</p>"}, "CommitMessage": {"type": "string", "max": 160}, "CompatibleVersionsList": {"type": "list", "member": {"shape": "CompatibleVersionsMap"}}, "CompatibleVersionsMap": {"type": "structure", "members": {"SourceVersion": {"shape": "VersionString", "documentation": "<p>The current version that the OpenSearch Service domain is running.</p>"}, "TargetVersions": {"shape": "VersionList", "documentation": "<p>The possible versions that you can upgrade the domain to.</p>"}}, "documentation": "<p>A map of OpenSearch or Elasticsearch versions and the versions you can upgrade them to.</p>"}, "ConflictException": {"type": "structure", "members": {}, "documentation": "<p>An error occurred because the client attempts to remove a resource that is currently in use.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "ConnectionAlias": {"type": "string", "max": 100, "min": 2, "pattern": "[a-zA-Z][a-zA-Z0-9\\-\\_]+"}, "ConnectionId": {"type": "string", "max": 256, "min": 10, "pattern": "[a-z][a-z0-9\\-]+"}, "ConnectionMode": {"type": "string", "documentation": "<p>The connection mode for the cross-cluster connection.</p> <ul> <li> <p> <b>DIRECT</b> - Used for cross-cluster search or cross-cluster replication.</p> </li> <li> <p> <b>VPC_ENDPOINT</b> - Used for remote reindex between Amazon OpenSearch Service VPC domains.</p> </li> </ul>", "enum": ["DIRECT", "VPC_ENDPOINT"]}, "ConnectionProperties": {"type": "structure", "members": {"Endpoint": {"shape": "Endpoint", "documentation": "<important> <p>The Endpoint attribute cannot be modified. </p> </important> <p>The endpoint of the remote domain. Applicable for VPC_ENDPOINT connection mode.</p>"}, "CrossClusterSearch": {"shape": "CrossClusterSearchConnectionProperties", "documentation": "<p>The connection properties for cross cluster search.</p>"}}, "documentation": "<p>The connection properties of an outbound connection.</p>"}, "ConnectionStatusMessage": {"type": "string"}, "CreateDomainRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>Name of the OpenSearch Service domain to create. Domain names are unique across the domains owned by an account within an Amazon Web Services Region.</p>"}, "EngineVersion": {"shape": "VersionString", "documentation": "<p>String of format Elasticsearch_X.Y or OpenSearch_X.Y to specify the engine version for the OpenSearch Service domain. For example, <code>OpenSearch_1.0</code> or <code>Elasticsearch_7.9</code>. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/createupdatedomains.html#createdomains\">Creating and managing Amazon OpenSearch Service domains</a>.</p>"}, "ClusterConfig": {"shape": "ClusterConfig", "documentation": "<p>Container for the cluster configuration of a domain.</p>"}, "EBSOptions": {"shape": "EBSOptions", "documentation": "<p>Container for the parameters required to enable EBS-based storage for an OpenSearch Service domain.</p>"}, "AccessPolicies": {"shape": "PolicyDocument", "documentation": "<p>Identity and Access Management (IAM) policy document specifying the access policies for the new domain.</p>"}, "IPAddressType": {"shape": "IPAddressType", "documentation": "<p>The type of IP addresses supported by the endpoint for the domain.</p>"}, "SnapshotOptions": {"shape": "SnapshotOptions", "documentation": "<p>DEPRECATED. Container for the parameters required to configure automated snapshots of domain indexes.</p>"}, "VPCOptions": {"shape": "VPCOptions", "documentation": "<p>Container for the values required to configure VPC access domains. If you don't specify these values, OpenSearch Service creates the domain with a public endpoint. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/vpc.html\">Launching your Amazon OpenSearch Service domains using a VPC</a>.</p>"}, "CognitoOptions": {"shape": "CognitoOptions", "documentation": "<p>Key-value pairs to configure Amazon Cognito authentication. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/cognito-auth.html\">Configuring Amazon Cognito authentication for OpenSearch Dashboards</a>.</p>"}, "EncryptionAtRestOptions": {"shape": "EncryptionAtRestOptions", "documentation": "<p>Key-value pairs to enable encryption at rest.</p>"}, "NodeToNodeEncryptionOptions": {"shape": "NodeToNodeEncryptionOptions", "documentation": "<p>Enables node-to-node encryption.</p>"}, "AdvancedOptions": {"shape": "AdvancedOptions", "documentation": "<p>Key-value pairs to specify advanced configuration options. The following key-value pairs are supported:</p> <ul> <li> <p> <code>\"rest.action.multi.allow_explicit_index\": \"true\" | \"false\"</code> - Note the use of a string rather than a boolean. Specifies whether explicit references to indexes are allowed inside the body of HTTP requests. If you want to configure access policies for domain sub-resources, such as specific indexes and domain APIs, you must disable this property. Default is true.</p> </li> <li> <p> <code>\"indices.fielddata.cache.size\": \"80\" </code> - Note the use of a string rather than a boolean. Specifies the percentage of heap space allocated to field data. Default is unbounded.</p> </li> <li> <p> <code>\"indices.query.bool.max_clause_count\": \"1024\"</code> - Note the use of a string rather than a boolean. Specifies the maximum number of clauses allowed in a Lucene boolean query. Default is 1,024. Queries with more than the permitted number of clauses result in a <code>TooManyClauses</code> error.</p> </li> <li> <p> <code>\"override_main_response_version\": \"true\" | \"false\"</code> - Note the use of a string rather than a boolean. Specifies whether the domain reports its version as 7.10 to allow Elasticsearch OSS clients and plugins to continue working with it. Default is false when creating a domain and true when upgrading a domain.</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/createupdatedomains.html#createdomain-configure-advanced-options\">Advanced cluster parameters</a>.</p>"}, "LogPublishingOptions": {"shape": "LogPublishingOptions", "documentation": "<p>Key-value pairs to configure log publishing.</p>"}, "DomainEndpointOptions": {"shape": "DomainEndpointOptions", "documentation": "<p>Additional options for the domain endpoint, such as whether to require HTTPS for all traffic.</p>"}, "AdvancedSecurityOptions": {"shape": "AdvancedSecurityOptionsInput", "documentation": "<p>Options for fine-grained access control.</p>"}, "TagList": {"shape": "TagList", "documentation": "<p>List of tags to add to the domain upon creation.</p>"}, "AutoTuneOptions": {"shape": "AutoTuneOptionsInput", "documentation": "<p>Options for Auto-Tune.</p>"}, "OffPeakWindowOptions": {"shape": "OffPeakWindowOptions", "documentation": "<p>Specifies a daily 10-hour time block during which OpenSearch Service can perform configuration changes on the domain, including service software updates and Auto-Tune enhancements that require a blue/green deployment. If no options are specified, the default start time of 10:00 P.M. local time (for the Region that the domain is created in) is used.</p>"}, "SoftwareUpdateOptions": {"shape": "SoftwareUpdateOptions", "documentation": "<p>Software update options for the domain.</p>"}}}, "CreateDomainResponse": {"type": "structure", "members": {"DomainStatus": {"shape": "DomainStatus", "documentation": "<p>The status of the newly created domain.</p>"}}, "documentation": "<p>The result of a <code>CreateDomain</code> operation. Contains the status of the newly created domain.</p>"}, "CreateOutboundConnectionRequest": {"type": "structure", "required": ["LocalDomainInfo", "RemoteDomainInfo", "ConnectionAlias"], "members": {"LocalDomainInfo": {"shape": "DomainInformationContainer", "documentation": "<p>Name and Region of the source (local) domain.</p>"}, "RemoteDomainInfo": {"shape": "DomainInformationContainer", "documentation": "<p>Name and Region of the destination (remote) domain.</p>"}, "ConnectionAlias": {"shape": "ConnectionAlias", "documentation": "<p>Name of the connection.</p>"}, "ConnectionMode": {"shape": "ConnectionMode", "documentation": "<p>The connection mode.</p>"}, "ConnectionProperties": {"shape": "ConnectionProperties", "documentation": "<p>The <code>ConnectionProperties</code> for the outbound connection.</p>"}}, "documentation": "<p>Container for the parameters to the <code>CreateOutboundConnection</code> operation.</p>"}, "CreateOutboundConnectionResponse": {"type": "structure", "members": {"LocalDomainInfo": {"shape": "DomainInformationContainer", "documentation": "<p>Information about the source (local) domain.</p>"}, "RemoteDomainInfo": {"shape": "DomainInformationContainer", "documentation": "<p>Information about the destination (remote) domain.</p>"}, "ConnectionAlias": {"shape": "ConnectionAlias", "documentation": "<p>Name of the connection.</p>"}, "ConnectionStatus": {"shape": "OutboundConnectionStatus", "documentation": "<p>The status of the connection.</p>"}, "ConnectionId": {"shape": "ConnectionId", "documentation": "<p>The unique identifier for the created outbound connection, which is used for subsequent operations on the connection.</p>"}, "ConnectionMode": {"shape": "ConnectionMode", "documentation": "<p>The connection mode.</p>"}, "ConnectionProperties": {"shape": "ConnectionProperties", "documentation": "<p>The <code>ConnectionProperties</code> for the newly created connection.</p>"}}, "documentation": "<p>The result of a <code>CreateOutboundConnection</code> request. Contains details about the newly created cross-cluster connection.</p>"}, "CreatePackageRequest": {"type": "structure", "required": ["PackageName", "PackageType", "PackageSource"], "members": {"PackageName": {"shape": "PackageName", "documentation": "<p>Unique name for the package.</p>"}, "PackageType": {"shape": "PackageType", "documentation": "<p>The type of package.</p>"}, "PackageDescription": {"shape": "PackageDescription", "documentation": "<p>Description of the package.</p>"}, "PackageSource": {"shape": "PackageSource", "documentation": "<p>The Amazon S3 location from which to import the package.</p>"}}, "documentation": "<p>Container for request parameters to the <code>CreatePackage</code> operation.</p>"}, "CreatePackageResponse": {"type": "structure", "members": {"PackageDetails": {"shape": "PackageDetails", "documentation": "<p>Basic information about an OpenSearch Service package.</p>"}}, "documentation": "<p>Container for the response returned by the <code>CreatePackage</code> operation.</p>"}, "CreateVpcEndpointRequest": {"type": "structure", "required": ["DomainArn", "VpcOptions"], "members": {"DomainArn": {"shape": "DomainArn", "documentation": "<p>The Amazon Resource Name (ARN) of the domain to create the endpoint for.</p>"}, "VpcOptions": {"shape": "VPCOptions", "documentation": "<p>Options to specify the subnets and security groups for the endpoint.</p>"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier to ensure idempotency of the request.</p>"}}}, "CreateVpcEndpointResponse": {"type": "structure", "required": ["VpcEndpoint"], "members": {"VpcEndpoint": {"shape": "VpcEndpoint", "documentation": "<p>Information about the newly created VPC endpoint.</p>"}}}, "CreatedAt": {"type": "timestamp"}, "CrossClusterSearchConnectionProperties": {"type": "structure", "members": {"SkipUnavailable": {"shape": "SkipUnavailableStatus", "documentation": "<p>The status of the <code>SkipUnavailable</code> setting for the outbound connection. This feature allows you to specify some clusters as optional and ensure that your cross-cluster queries return partial results despite failures on one or more remote clusters.</p>"}}, "documentation": "<p>Cross-cluster search specific connection properties.</p>"}, "DeleteDomainRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>The name of the domain you want to permanently delete.</p>", "location": "uri", "locationName": "DomainName"}}, "documentation": "<p>Container for the parameters to the <code>DeleteDomain</code> operation.</p>"}, "DeleteDomainResponse": {"type": "structure", "members": {"DomainStatus": {"shape": "DomainStatus", "documentation": "<p>The status of the domain being deleted.</p>"}}, "documentation": "<p>The results of a <code>DeleteDomain</code> request. Contains the status of the pending deletion, or a \"domain not found\" error if the domain and all of its resources have been deleted.</p>"}, "DeleteInboundConnectionRequest": {"type": "structure", "required": ["ConnectionId"], "members": {"ConnectionId": {"shape": "ConnectionId", "documentation": "<p>The ID of the inbound connection to permanently delete.</p>", "location": "uri", "locationName": "ConnectionId"}}, "documentation": "<p>Container for the parameters to the <code>DeleteInboundConnection</code> operation.</p>"}, "DeleteInboundConnectionResponse": {"type": "structure", "members": {"Connection": {"shape": "InboundConnection", "documentation": "<p>The deleted inbound connection.</p>"}}, "documentation": "<p>The results of a <code>DeleteInboundConnection</code> operation. Contains details about the deleted inbound connection.</p>"}, "DeleteOutboundConnectionRequest": {"type": "structure", "required": ["ConnectionId"], "members": {"ConnectionId": {"shape": "ConnectionId", "documentation": "<p>The ID of the outbound connection you want to permanently delete.</p>", "location": "uri", "locationName": "ConnectionId"}}, "documentation": "<p>Container for the parameters to the <code>DeleteOutboundConnection</code> operation.</p>"}, "DeleteOutboundConnectionResponse": {"type": "structure", "members": {"Connection": {"shape": "OutboundConnection", "documentation": "<p>The deleted inbound connection.</p>"}}, "documentation": "<p>Details about the deleted outbound connection.</p>"}, "DeletePackageRequest": {"type": "structure", "required": ["PackageID"], "members": {"PackageID": {"shape": "PackageID", "documentation": "<p>The internal ID of the package you want to delete. Use <code>DescribePackages</code> to find this value.</p>", "location": "uri", "locationName": "PackageID"}}, "documentation": "<p>Deletes a package from OpenSearch Service. The package can't be associated with any OpenSearch Service domain.</p>"}, "DeletePackageResponse": {"type": "structure", "members": {"PackageDetails": {"shape": "PackageDetails", "documentation": "<p> Information about the deleted package.</p>"}}, "documentation": "<p>Container for the response parameters to the <code>DeletePackage</code> operation.</p>"}, "DeleteVpcEndpointRequest": {"type": "structure", "required": ["VpcEndpointId"], "members": {"VpcEndpointId": {"shape": "VpcEndpointId", "documentation": "<p>The unique identifier of the endpoint.</p>", "location": "uri", "locationName": "VpcEndpointId"}}}, "DeleteVpcEndpointResponse": {"type": "structure", "required": ["VpcEndpointSummary"], "members": {"VpcEndpointSummary": {"shape": "VpcEndpointSummary", "documentation": "<p>Information about the deleted endpoint, including its current status (<code>DELETING</code> or <code>DELETE_FAILED</code>).</p>"}}}, "DependencyFailureException": {"type": "structure", "members": {}, "documentation": "<p>An exception for when a failure in one of the dependencies results in the service being unable to fetch details about the resource.</p>", "error": {"httpStatusCode": 424}, "exception": true}, "DeploymentCloseDateTimeStamp": {"type": "timestamp"}, "DeploymentStatus": {"type": "string", "enum": ["PENDING_UPDATE", "IN_PROGRESS", "COMPLETED", "NOT_ELIGIBLE", "ELIGIBLE"]}, "DeploymentType": {"type": "string", "max": 128, "min": 2}, "DescribeDomainAutoTunesRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>Name of the domain that you want Auto-Tune details about.</p>", "location": "uri", "locationName": "DomainName"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>An optional parameter that specifies the maximum number of results to return. You can use <code>nextToken</code> to get the next page of results.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If your initial <code>DescribeDomainAutoTunes</code> operation returns a <code>nextToken</code>, you can include the returned <code>nextToken</code> in subsequent <code>DescribeDomainAutoTunes</code> operations, which returns results in the next page.</p>"}}, "documentation": "<p>Container for the parameters to the <code>DescribeDomainAutoTunes</code> operation.</p>"}, "DescribeDomainAutoTunesResponse": {"type": "structure", "members": {"AutoTunes": {"shape": "AutoTuneList", "documentation": "<p>The list of setting adjustments that Auto-Tune has made to the domain.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>When <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}}, "documentation": "<p>The result of a <code>DescribeDomainAutoTunes</code> request.</p>"}, "DescribeDomainChangeProgressRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>The name of the domain to get progress information for.</p>", "location": "uri", "locationName": "DomainName"}, "ChangeId": {"shape": "GUID", "documentation": "<p>The specific change ID for which you want to get progress information. If omitted, the request returns information about the most recent configuration change.</p>", "location": "querystring", "locationName": "changeid"}}, "documentation": "<p>Container for the parameters to the <code>DescribeDomainChangeProgress</code> operation.</p>"}, "DescribeDomainChangeProgressResponse": {"type": "structure", "members": {"ChangeProgressStatus": {"shape": "ChangeProgressStatusDetails", "documentation": "<p>Container for information about the stages of a configuration change happening on a domain.</p>"}}, "documentation": "<p>The result of a <code>DescribeDomainChangeProgress</code> request. Contains progress information for the requested domain change.</p>"}, "DescribeDomainConfigRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>Name of the OpenSearch Service domain configuration that you want to describe.</p>", "location": "uri", "locationName": "DomainName"}}, "documentation": "<p>Container for the parameters to the <code>DescribeDomainConfig</code> operation.</p>"}, "DescribeDomainConfigResponse": {"type": "structure", "required": ["DomainConfig"], "members": {"DomainConfig": {"shape": "DomainConfig", "documentation": "<p>Container for the configuration of the OpenSearch Service domain.</p>"}}, "documentation": "<p>Contains the configuration information of the requested domain.</p>"}, "DescribeDomainHealthRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>The name of the domain.</p>", "location": "uri", "locationName": "DomainName"}}, "documentation": "<p>Container for the parameters to the <code>DescribeDomainHealth</code> operation.</p>"}, "DescribeDomainHealthResponse": {"type": "structure", "members": {"DomainState": {"shape": "DomainState", "documentation": "<p>The current state of the domain.</p> <ul> <li> <p> <code>Processing</code> - The domain has updates in progress.</p> </li> <li> <p> <code>Active</code> - Requested changes have been processed and deployed to the domain.</p> </li> </ul>"}, "AvailabilityZoneCount": {"shape": "NumberOfAZs", "documentation": "<p>The number of Availability Zones configured for the domain. If the service is unable to fetch this information, it will return <code>NotAvailable</code>.</p>"}, "ActiveAvailabilityZoneCount": {"shape": "NumberOfAZs", "documentation": "<p>The number of active Availability Zones configured for the domain. If the service is unable to fetch this information, it will return <code>NotAvailable</code>.</p>"}, "StandByAvailabilityZoneCount": {"shape": "NumberOfAZs", "documentation": "<p>The number of standby Availability Zones configured for the domain. If the service is unable to fetch this information, it will return <code>NotAvailable</code>.</p>"}, "DataNodeCount": {"shape": "NumberOfNodes", "documentation": "<p>The number of data nodes configured for the domain. If the service is unable to fetch this information, it will return <code>NotAvailable</code>.</p>"}, "DedicatedMaster": {"shape": "Boolean", "documentation": "<p>A boolean that indicates if dedicated master nodes are activated for the domain.</p>"}, "MasterEligibleNodeCount": {"shape": "NumberOfNodes", "documentation": "<p>The number of nodes that can be elected as a master node. If dedicated master nodes is turned on, this value is the number of dedicated master nodes configured for the domain. If the service is unable to fetch this information, it will return <code>NotAvailable</code>.</p>"}, "WarmNodeCount": {"shape": "NumberOfNodes", "documentation": "<p>The number of warm nodes configured for the domain.</p>"}, "MasterNode": {"shape": "MasterNodeStatus", "documentation": "<p>Indicates whether the domain has an elected master node.</p> <ul> <li> <p> <b>Available</b> - The domain has an elected master node.</p> </li> <li> <p> <b>UnAvailable</b> - The master node hasn't yet been elected, and a quorum to elect a new master node hasn't been reached.</p> </li> </ul>"}, "ClusterHealth": {"shape": "DomainHealth", "documentation": "<p>The current health status of your cluster.</p> <ul> <li> <p> <code>Red</code> - At least one primary shard is not allocated to any node.</p> </li> <li> <p> <code>Yellow</code> - All primary shards are allocated to nodes, but some replicas aren’t.</p> </li> <li> <p> <code>Green</code> - All primary shards and their replicas are allocated to nodes.</p> </li> <li> <p> <code>NotAvailable</code> - Unable to retrieve cluster health.</p> </li> </ul>"}, "TotalShards": {"shape": "NumberOfShards", "documentation": "<p>The total number of primary and replica shards for the domain.</p>"}, "TotalUnAssignedShards": {"shape": "NumberOfShards", "documentation": "<p>The total number of primary and replica shards not allocated to any of the nodes for the cluster.</p>"}, "EnvironmentInformation": {"shape": "EnvironmentInfoList", "documentation": "<p>A list of <code>EnvironmentInfo</code> for the domain. </p>"}}, "documentation": "<p>The result of a <code>DescribeDomainHealth</code> request. Contains health information for the requested domain.</p>"}, "DescribeDomainNodesRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>The name of the domain.</p>", "location": "uri", "locationName": "DomainName"}}, "documentation": "<p>Container for the parameters to the <code>DescribeDomainNodes</code> operation.</p>"}, "DescribeDomainNodesResponse": {"type": "structure", "members": {"DomainNodesStatusList": {"shape": "DomainNodesStatusList", "documentation": "<p>Contains nodes information list <code>DomainNodesStatusList</code> with details about the all nodes on the requested domain.</p>"}}, "documentation": "<p>The result of a <code>DescribeDomainNodes</code> request. Contains information about the nodes on the requested domain. </p>"}, "DescribeDomainRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>The name of the domain that you want information about.</p>", "location": "uri", "locationName": "DomainName"}}, "documentation": "<p>Container for the parameters to the <code>DescribeDomain</code> operation.</p>"}, "DescribeDomainResponse": {"type": "structure", "required": ["DomainStatus"], "members": {"DomainStatus": {"shape": "DomainStatus", "documentation": "<p>List that contains the status of each specified OpenSearch Service domain.</p>"}}, "documentation": "<p>Contains the status of the domain specified in the request.</p>"}, "DescribeDomainsRequest": {"type": "structure", "required": ["DomainNames"], "members": {"DomainNames": {"shape": "DomainNameList", "documentation": "<p>Array of OpenSearch Service domain names that you want information about. You must specify at least one domain name.</p>"}}, "documentation": "<p>Container for the parameters to the <code>DescribeDomains</code> operation.</p>"}, "DescribeDomainsResponse": {"type": "structure", "required": ["DomainStatusList"], "members": {"DomainStatusList": {"shape": "DomainStatusList", "documentation": "<p>The status of the requested domains.</p>"}}, "documentation": "<p>Contains the status of the specified domains or all domains owned by the account.</p>"}, "DescribeDryRunProgressRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>The name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "DryRunId": {"shape": "GUID", "documentation": "<p>The unique identifier of the dry run.</p>", "location": "querystring", "locationName": "dryRunId"}, "LoadDryRunConfig": {"shape": "Boolean", "documentation": "<p>Whether to include the configuration of the dry run in the response. The configuration specifies the updates that you're planning to make on the domain.</p>", "location": "querystring", "locationName": "loadDryRunConfig"}}}, "DescribeDryRunProgressResponse": {"type": "structure", "members": {"DryRunProgressStatus": {"shape": "DryRunProgressStatus", "documentation": "<p>The current status of the dry run, including any validation errors.</p>"}, "DryRunConfig": {"shape": "DomainStatus", "documentation": "<p>Details about the changes you're planning to make on the domain.</p>"}, "DryRunResults": {"shape": "DryRunResults", "documentation": "<p>The results of the dry run. </p>"}}}, "DescribeInboundConnectionsRequest": {"type": "structure", "members": {"Filters": {"shape": "FilterList", "documentation": "<p> A list of filters used to match properties for inbound cross-cluster connections.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>An optional parameter that specifies the maximum number of results to return. You can use <code>nextToken</code> to get the next page of results.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If your initial <code>DescribeInboundConnections</code> operation returns a <code>nextToken</code>, you can include the returned <code>nextToken</code> in subsequent <code>DescribeInboundConnections</code> operations, which returns results in the next page.</p>"}}, "documentation": "<p>Container for the parameters to the <code>DescribeInboundConnections</code> operation.</p>"}, "DescribeInboundConnectionsResponse": {"type": "structure", "members": {"Connections": {"shape": "InboundConnections", "documentation": "<p>List of inbound connections.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>When <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}}, "documentation": "<p>Contains a list of connections matching the filter criteria.</p>"}, "DescribeInstanceTypeLimitsRequest": {"type": "structure", "required": ["InstanceType", "EngineVersion"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>The name of the domain. Only specify if you need the limits for an existing domain.</p>", "location": "querystring", "locationName": "domainName"}, "InstanceType": {"shape": "OpenSearchPartitionInstanceType", "documentation": "<p>The OpenSearch Service instance type for which you need limit information.</p>", "location": "uri", "locationName": "InstanceType"}, "EngineVersion": {"shape": "VersionString", "documentation": "<p>Version of OpenSearch or Elasticsearch, in the format Elasticsearch_X.Y or OpenSearch_X.Y. Defaults to the latest version of OpenSearch.</p>", "location": "uri", "locationName": "EngineVersion"}}, "documentation": "<p>Container for the parameters to the <code>DescribeInstanceTypeLimits</code> operation.</p>"}, "DescribeInstanceTypeLimitsResponse": {"type": "structure", "members": {"LimitsByRole": {"shape": "LimitsByRole", "documentation": "<p>Map that contains all applicable instance type limits.<code>data</code> refers to data nodes.<code>master</code> refers to dedicated master nodes.</p>"}}, "documentation": "<p>Container for the parameters received from the <code>DescribeInstanceTypeLimits</code> operation.</p>"}, "DescribeOutboundConnectionsRequest": {"type": "structure", "members": {"Filters": {"shape": "FilterList", "documentation": "<p>List of filter names and values that you can use for requests.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>An optional parameter that specifies the maximum number of results to return. You can use <code>nextToken</code> to get the next page of results.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If your initial <code>DescribeOutboundConnections</code> operation returns a <code>nextToken</code>, you can include the returned <code>nextToken</code> in subsequent <code>DescribeOutboundConnections</code> operations, which returns results in the next page.</p>"}}, "documentation": "<p>Container for the parameters to the <code>DescribeOutboundConnections</code> operation.</p>"}, "DescribeOutboundConnectionsResponse": {"type": "structure", "members": {"Connections": {"shape": "OutboundConnections", "documentation": "<p>List of outbound connections that match the filter criteria.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>When <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}}, "documentation": "<p>Contains a list of connections matching the filter criteria.</p>"}, "DescribePackagesFilter": {"type": "structure", "members": {"Name": {"shape": "DescribePackagesFilterName", "documentation": "<p>Any field from <code>PackageDetails</code>.</p>"}, "Value": {"shape": "DescribePackagesFilterValues", "documentation": "<p>A non-empty list of values for the specified filter field.</p>"}}, "documentation": "<p>A filter to apply to the <code>DescribePackage</code> response.</p>"}, "DescribePackagesFilterList": {"type": "list", "member": {"shape": "DescribePackagesFilter"}, "documentation": "<p>A list of <code>DescribePackagesFilter</code> to filter the packages included in a <code>DescribePackages</code> response.</p>"}, "DescribePackagesFilterName": {"type": "string", "enum": ["PackageID", "PackageName", "PackageStatus", "PackageType", "EngineVersion"]}, "DescribePackagesFilterValue": {"type": "string", "pattern": "^[0-9a-zA-Z\\*\\.\\_\\\\\\/\\?-]+$"}, "DescribePackagesFilterValues": {"type": "list", "member": {"shape": "DescribePackagesFilterValue"}, "min": 1}, "DescribePackagesRequest": {"type": "structure", "members": {"Filters": {"shape": "DescribePackagesFilterList", "documentation": "<p>Only returns packages that match the <code>DescribePackagesFilterList</code> values.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>An optional parameter that specifies the maximum number of results to return. You can use <code>nextToken</code> to get the next page of results.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If your initial <code>DescribePackageFilters</code> operation returns a <code>nextToken</code>, you can include the returned <code>nextToken</code> in subsequent <code>DescribePackageFilters</code> operations, which returns results in the next page.</p>"}}, "documentation": "<p>Container for the request parameters to the <code>DescribePackage</code> operation.</p>"}, "DescribePackagesResponse": {"type": "structure", "members": {"PackageDetailsList": {"shape": "PackageDetailsList", "documentation": "<p>Basic information about a package.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>When <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}}, "documentation": "<p>Container for the response returned by the <code>DescribePackages</code> operation.</p>"}, "DescribeReservedInstanceOfferingsRequest": {"type": "structure", "members": {"ReservedInstanceOfferingId": {"shape": "GUID", "documentation": "<p>The Reserved Instance identifier filter value. Use this parameter to show only the available instance types that match the specified reservation identifier.</p>", "location": "querystring", "locationName": "offeringId"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>An optional parameter that specifies the maximum number of results to return. You can use <code>nextToken</code> to get the next page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If your initial <code>DescribeReservedInstanceOfferings</code> operation returns a <code>nextToken</code>, you can include the returned <code>nextToken</code> in subsequent <code>DescribeReservedInstanceOfferings</code> operations, which returns results in the next page.</p>", "location": "querystring", "locationName": "nextToken"}}, "documentation": "<p>Container for the request parameters to a <code>DescribeReservedInstanceOfferings</code> operation.</p>"}, "DescribeReservedInstanceOfferingsResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>When <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}, "ReservedInstanceOfferings": {"shape": "ReservedInstanceOfferingList", "documentation": "<p>List of Reserved Instance offerings.</p>"}}, "documentation": "<p>Container for results of a <code>DescribeReservedInstanceOfferings</code> request.</p>"}, "DescribeReservedInstancesRequest": {"type": "structure", "members": {"ReservedInstanceId": {"shape": "GUID", "documentation": "<p>The reserved instance identifier filter value. Use this parameter to show only the reservation that matches the specified reserved OpenSearch instance ID.</p>", "location": "querystring", "locationName": "reservationId"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>An optional parameter that specifies the maximum number of results to return. You can use <code>nextToken</code> to get the next page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If your initial <code>DescribeReservedInstances</code> operation returns a <code>nextToken</code>, you can include the returned <code>nextToken</code> in subsequent <code>DescribeReservedInstances</code> operations, which returns results in the next page.</p>", "location": "querystring", "locationName": "nextToken"}}, "documentation": "<p>Container for the request parameters to the <code>DescribeReservedInstances</code> operation.</p>"}, "DescribeReservedInstancesResponse": {"type": "structure", "members": {"NextToken": {"shape": "String", "documentation": "<p>When <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}, "ReservedInstances": {"shape": "ReservedInstanceList", "documentation": "<p>List of Reserved Instances in the current Region.</p>"}}, "documentation": "<p>Container for results from <code>DescribeReservedInstances</code> </p>"}, "DescribeVpcEndpointsRequest": {"type": "structure", "required": ["VpcEndpointIds"], "members": {"VpcEndpointIds": {"shape": "VpcEndpointIdList", "documentation": "<p>The unique identifiers of the endpoints to get information about.</p>"}}}, "DescribeVpcEndpointsResponse": {"type": "structure", "required": ["VpcEndpoints", "VpcEndpointErrors"], "members": {"VpcEndpoints": {"shape": "VpcEndpoints", "documentation": "<p>Information about each requested VPC endpoint.</p>"}, "VpcEndpointErrors": {"shape": "VpcEndpointErrorList", "documentation": "<p>Any errors associated with the request.</p>"}}}, "Description": {"type": "string"}, "DisableTimestamp": {"type": "timestamp"}, "DisabledOperationException": {"type": "structure", "members": {}, "documentation": "<p>An error occured because the client wanted to access an unsupported operation.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "DissociatePackageRequest": {"type": "structure", "required": ["PackageID", "DomainName"], "members": {"PackageID": {"shape": "PackageID", "documentation": "<p>Internal ID of the package to dissociate from the domain. Use <code>ListPackagesForDomain</code> to find this value.</p>", "location": "uri", "locationName": "PackageID"}, "DomainName": {"shape": "DomainName", "documentation": "<p>Name of the domain to dissociate the package from.</p>", "location": "uri", "locationName": "DomainName"}}, "documentation": "<p>Container for the request parameters to the <code>DissociatePackage</code> operation.</p>"}, "DissociatePackageResponse": {"type": "structure", "members": {"DomainPackageDetails": {"shape": "DomainPackageDetails", "documentation": "<p> Information about a package that has been dissociated from the domain.</p>"}}, "documentation": "<p>Container for the response returned by an <code>DissociatePackage</code> operation.</p>"}, "DomainArn": {"type": "string", "max": 512, "min": 1, "pattern": "arn:aws[a-z\\-]*:[a-z]+:[a-z0-9\\-]+:[0-9]+:domain\\/[a-z0-9\\-]+"}, "DomainConfig": {"type": "structure", "members": {"EngineVersion": {"shape": "VersionStatus", "documentation": "<p>The OpenSearch or Elasticsearch version that the domain is running.</p>"}, "ClusterConfig": {"shape": "ClusterConfigStatus", "documentation": "<p>Container for the cluster configuration of a the domain.</p>"}, "EBSOptions": {"shape": "EBSOptionsStatus", "documentation": "<p>Container for EBS options configured for the domain.</p>"}, "AccessPolicies": {"shape": "AccessPoliciesStatus", "documentation": "<p>Specifies the access policies for the domain.</p>"}, "IPAddressType": {"shape": "IPAddressTypeStatus", "documentation": "<p>The type of IP addresses supported by the endpoint for the domain.</p>"}, "SnapshotOptions": {"shape": "SnapshotOptionsStatus", "documentation": "<p>DEPRECATED. Container for parameters required to configure automated snapshots of domain indexes.</p>"}, "VPCOptions": {"shape": "VPCDerivedInfoStatus", "documentation": "<p>The current VPC options for the domain and the status of any updates to their configuration.</p>"}, "CognitoOptions": {"shape": "CognitoOptionsStatus", "documentation": "<p>Container for Amazon Cognito options for the domain.</p>"}, "EncryptionAtRestOptions": {"shape": "EncryptionAtRestOptionsStatus", "documentation": "<p>Key-value pairs to enable encryption at rest.</p>"}, "NodeToNodeEncryptionOptions": {"shape": "NodeToNodeEncryptionOptionsStatus", "documentation": "<p>Whether node-to-node encryption is enabled or disabled.</p>"}, "AdvancedOptions": {"shape": "AdvancedOptionsStatus", "documentation": "<p>Key-value pairs to specify advanced configuration options. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/createupdatedomains.html#createdomain-configure-advanced-options\">Advanced options</a>.</p>"}, "LogPublishingOptions": {"shape": "LogPublishingOptionsStatus", "documentation": "<p>Key-value pairs to configure log publishing.</p>"}, "DomainEndpointOptions": {"shape": "DomainEndpointOptionsStatus", "documentation": "<p>Additional options for the domain endpoint, such as whether to require HTTPS for all traffic.</p>"}, "AdvancedSecurityOptions": {"shape": "AdvancedSecurityOptionsStatus", "documentation": "<p>Container for fine-grained access control settings for the domain.</p>"}, "AutoTuneOptions": {"shape": "AutoTuneOptionsStatus", "documentation": "<p>Container for Auto-Tune settings for the domain.</p>"}, "ChangeProgressDetails": {"shape": "ChangeProgressDetails", "documentation": "<p>Container for information about the progress of an existing configuration change.</p>"}, "OffPeakWindowOptions": {"shape": "OffPeakWindowOptionsStatus", "documentation": "<p>Container for off-peak window options for the domain.</p>"}, "SoftwareUpdateOptions": {"shape": "SoftwareUpdateOptionsStatus", "documentation": "<p>Software update options for the domain.</p>"}}, "documentation": "<p>Container for the configuration of an OpenSearch Service domain.</p>"}, "DomainEndpointOptions": {"type": "structure", "members": {"EnforceHTTPS": {"shape": "Boolean", "documentation": "<p>True to require that all traffic to the domain arrive over HTTPS.</p>"}, "TLSSecurityPolicy": {"shape": "TLSSecurityPolicy", "documentation": "<p>Specify the TLS security policy to apply to the HTTPS endpoint of the domain.</p> <p> Can be one of the following values:</p> <ul> <li> <p> <b>Policy-Min-TLS-1-0-2019-07:</b> TLS security policy which supports TLS version 1.0 and higher.</p> </li> <li> <p> <b>Policy-Min-TLS-1-2-2019-07:</b> TLS security policy which supports only TLS version 1.2 </p> </li> </ul>"}, "CustomEndpointEnabled": {"shape": "Boolean", "documentation": "<p>Whether to enable a custom endpoint for the domain.</p>"}, "CustomEndpoint": {"shape": "DomainNameFqdn", "documentation": "<p>The fully qualified URL for the custom endpoint.</p>"}, "CustomEndpointCertificateArn": {"shape": "ARN", "documentation": "<p>The ARN for your security certificate, managed in Amazon Web Services Certificate Manager (ACM).</p>"}}, "documentation": "<p>Options to configure a custom endpoint for an OpenSearch Service domain.</p>"}, "DomainEndpointOptionsStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "DomainEndpointOptions", "documentation": "<p>Options to configure the endpoint for a domain.</p>"}, "Status": {"shape": "OptionStatus", "documentation": "<p>The status of the endpoint options for a domain.</p>"}}, "documentation": "<p>The configured endpoint options for a domain and their current status.</p>"}, "DomainHealth": {"type": "string", "enum": ["Red", "Yellow", "Green", "NotAvailable"]}, "DomainId": {"type": "string", "documentation": "<p>Unique identifier for an OpenSearch Service domain.</p>", "max": 64, "min": 1}, "DomainInfo": {"type": "structure", "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>Name of the domain.</p>"}, "EngineType": {"shape": "EngineType", "documentation": "<p>The type of search engine that the domain is running.<code>OpenSearch</code> for an OpenSearch engine, or <code>Elasticsearch</code> for a legacy Elasticsearch OSS engine.</p>"}}, "documentation": "<p>Information about an OpenSearch Service domain.</p>"}, "DomainInfoList": {"type": "list", "member": {"shape": "DomainInfo"}, "documentation": "<p>Contains a list of information about a domain.</p>"}, "DomainInformationContainer": {"type": "structure", "members": {"AWSDomainInformation": {"shape": "AWSDomainInformation", "documentation": "<p>Information about an Amazon OpenSearch Service domain.</p>"}}, "documentation": "<p>Container for information about an OpenSearch Service domain.</p>"}, "DomainMaintenanceDetails": {"type": "structure", "members": {"MaintenanceId": {"shape": "RequestId", "documentation": "<p>The ID of the requested action.</p>"}, "DomainName": {"shape": "DomainName", "documentation": "<p>The name of the domain.</p>"}, "Action": {"shape": "MaintenanceType", "documentation": "<p>The name of the action.</p>"}, "NodeId": {"shape": "NodeId", "documentation": "<p>The ID of the data node.</p>"}, "Status": {"shape": "MaintenanceStatus", "documentation": "<p>The status of the action.</p>"}, "StatusMessage": {"shape": "MaintenanceStatusMessage", "documentation": "<p>The status message for the action.</p>"}, "CreatedAt": {"shape": "UpdateTimestamp", "documentation": "<p>The time at which the action was created.</p>"}, "UpdatedAt": {"shape": "UpdateTimestamp", "documentation": "<p>The time at which the action was updated.</p>"}}, "documentation": "<p>Container for the domain maintenance details.</p>"}, "DomainMaintenanceList": {"type": "list", "member": {"shape": "DomainMaintenanceDetails"}}, "DomainName": {"type": "string", "documentation": "<p>The name of an OpenSearch Service domain. Domain names are unique across the domains owned by an account within an Amazon Web Services Region.</p>", "max": 28, "min": 3, "pattern": "[a-z][a-z0-9\\-]+"}, "DomainNameFqdn": {"type": "string", "max": 255, "min": 1, "pattern": "^(((?!-)[A-Za-z0-9-]{0,62}[A-Za-z0-9])\\.)+((?!-)[A-Za-z0-9-]{1,62}[A-Za-z0-9])$"}, "DomainNameList": {"type": "list", "member": {"shape": "DomainName"}, "documentation": "<p>A list of OpenSearch Service domain names.</p>"}, "DomainNodesStatus": {"type": "structure", "members": {"NodeId": {"shape": "NodeId", "documentation": "<p>The ID of the node.</p>"}, "NodeType": {"shape": "NodeType", "documentation": "<p>Indicates whether the nodes is a data, master, or ultrawarm node.</p>"}, "AvailabilityZone": {"shape": "AvailabilityZone", "documentation": "<p>The Availability Zone of the node.</p>"}, "InstanceType": {"shape": "OpenSearchPartitionInstanceType", "documentation": "<p>The instance type information of the node.</p>"}, "NodeStatus": {"shape": "NodeStatus", "documentation": "<p>Indicates if the node is active or in standby.</p>"}, "StorageType": {"shape": "StorageTypeName", "documentation": "<p>Indicates if the node has EBS or instance storage. </p>"}, "StorageVolumeType": {"shape": "VolumeType", "documentation": "<p>If the nodes has EBS storage, indicates if the volume type is GP2 or GP3. Only applicable for data nodes. </p>"}, "StorageSize": {"shape": "VolumeSize", "documentation": "<p>The storage size of the node, in GiB.</p>"}}, "documentation": "<p>Container for information about nodes on the domain.</p>"}, "DomainNodesStatusList": {"type": "list", "member": {"shape": "DomainNodesStatus"}, "documentation": "<p>List of <code>DomainNodesStatus</code> with details about nodes on the requested domain.</p>"}, "DomainPackageDetails": {"type": "structure", "members": {"PackageID": {"shape": "PackageID", "documentation": "<p>Internal ID of the package.</p>"}, "PackageName": {"shape": "PackageName", "documentation": "<p>User-specified name of the package.</p>"}, "PackageType": {"shape": "PackageType", "documentation": "<p>The type of package.</p>"}, "LastUpdated": {"shape": "LastUpdated", "documentation": "<p>Timestamp of the most recent update to the package association status.</p>"}, "DomainName": {"shape": "DomainName", "documentation": "<p>Name of the domain that the package is associated with.</p>"}, "DomainPackageStatus": {"shape": "DomainPackageStatus", "documentation": "<p>State of the association.</p>"}, "PackageVersion": {"shape": "PackageVersion", "documentation": "<p>The current version of the package.</p>"}, "ReferencePath": {"shape": "ReferencePath", "documentation": "<p>The relative path of the package on the OpenSearch Service cluster nodes. This is <code>synonym_path</code> when the package is for synonym files.</p>"}, "ErrorDetails": {"shape": "ErrorDetails", "documentation": "<p>Additional information if the package is in an error state. Null otherwise.</p>"}}, "documentation": "<p>Information about a package that is associated with a domain. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/custom-packages.html\">Custom packages for Amazon OpenSearch Service</a>.</p>"}, "DomainPackageDetailsList": {"type": "list", "member": {"shape": "DomainPackageDetails"}}, "DomainPackageStatus": {"type": "string", "enum": ["ASSOCIATING", "ASSOCIATION_FAILED", "ACTIVE", "DISSOCIATING", "DISSOCIATION_FAILED"]}, "DomainState": {"type": "string", "enum": ["Active", "Processing", "NotAvailable"]}, "DomainStatus": {"type": "structure", "required": ["DomainId", "DomainName", "ARN", "ClusterConfig"], "members": {"DomainId": {"shape": "DomainId", "documentation": "<p>Unique identifier for the domain.</p>"}, "DomainName": {"shape": "DomainName", "documentation": "<p>Name of the domain. Domain names are unique across all domains owned by the same account within an Amazon Web Services Region.</p>"}, "ARN": {"shape": "ARN", "documentation": "<p>The Amazon Resource Name (ARN) of the domain. For more information, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_identifiers.html\">IAM identifiers </a> in the <i>AWS Identity and Access Management User Guide</i>.</p>"}, "Created": {"shape": "Boolean", "documentation": "<p>Creation status of an OpenSearch Service domain. True if domain creation is complete. False if domain creation is still in progress.</p>"}, "Deleted": {"shape": "Boolean", "documentation": "<p>Deletion status of an OpenSearch Service domain. True if domain deletion is complete. False if domain deletion is still in progress. Once deletion is complete, the status of the domain is no longer returned.</p>"}, "Endpoint": {"shape": "ServiceUrl", "documentation": "<p>Domain-specific endpoint used to submit index, search, and data upload requests to the domain.</p>"}, "EndpointV2": {"shape": "ServiceUrl"}, "Endpoints": {"shape": "EndpointsMap", "documentation": "<p>The key-value pair that exists if the OpenSearch Service domain uses VPC endpoints.. Example <code>key, value</code>: <code>'vpc','vpc-endpoint-h2dsd34efgyghrtguk5gt6j2foh4.us-east-1.es.amazonaws.com'</code>.</p>"}, "Processing": {"shape": "Boolean", "documentation": "<p>The status of the domain configuration. True if OpenSearch Service is processing configuration changes. False if the configuration is active.</p>"}, "UpgradeProcessing": {"shape": "Boolean", "documentation": "<p>The status of a domain version upgrade to a new version of OpenSearch or Elasticsearch. True if OpenSearch Service is in the process of a version upgrade. False if the configuration is active.</p>"}, "EngineVersion": {"shape": "VersionString", "documentation": "<p>Version of OpenSearch or Elasticsearch that the domain is running, in the format <code>Elasticsearch_X.Y</code> or <code>OpenSearch_X.Y</code>.</p>"}, "ClusterConfig": {"shape": "ClusterConfig", "documentation": "<p>Container for the cluster configuration of the domain.</p>"}, "EBSOptions": {"shape": "EBSOptions", "documentation": "<p>Container for EBS-based storage settings for the domain.</p>"}, "AccessPolicies": {"shape": "PolicyDocument", "documentation": "<p>Identity and Access Management (IAM) policy document specifying the access policies for the domain.</p>"}, "IPAddressType": {"shape": "IPAddressType", "documentation": "<p>The type of IP addresses supported by the endpoint for the domain.</p>"}, "SnapshotOptions": {"shape": "SnapshotOptions", "documentation": "<p>DEPRECATED. Container for parameters required to configure automated snapshots of domain indexes.</p>"}, "VPCOptions": {"shape": "VPCDerivedInfo", "documentation": "<p>The VPC configuration for the domain.</p>"}, "CognitoOptions": {"shape": "CognitoOptions", "documentation": "<p>Key-value pairs to configure Amazon Cognito authentication for OpenSearch Dashboards.</p>"}, "EncryptionAtRestOptions": {"shape": "EncryptionAtRestOptions", "documentation": "<p>Encryption at rest settings for the domain.</p>"}, "NodeToNodeEncryptionOptions": {"shape": "NodeToNodeEncryptionOptions", "documentation": "<p>Whether node-to-node encryption is enabled or disabled.</p>"}, "AdvancedOptions": {"shape": "AdvancedOptions", "documentation": "<p>Key-value pairs that specify advanced configuration options.</p>"}, "LogPublishingOptions": {"shape": "LogPublishingOptions", "documentation": "<p>Log publishing options for the domain.</p>"}, "ServiceSoftwareOptions": {"shape": "ServiceSoftwareOptions", "documentation": "<p>The current status of the domain's service software.</p>"}, "DomainEndpointOptions": {"shape": "DomainEndpointOptions", "documentation": "<p>Additional options for the domain endpoint, such as whether to require HTTPS for all traffic.</p>"}, "AdvancedSecurityOptions": {"shape": "AdvancedSecurityOptions", "documentation": "<p>Settings for fine-grained access control.</p>"}, "AutoTuneOptions": {"shape": "AutoTuneOptionsOutput", "documentation": "<p>Auto-Tune settings for the domain.</p>"}, "ChangeProgressDetails": {"shape": "ChangeProgressDetails", "documentation": "<p>Information about a configuration change happening on the domain.</p>"}, "OffPeakWindowOptions": {"shape": "OffPeakWindowOptions", "documentation": "<p>Options that specify a custom 10-hour window during which OpenSearch Service can perform configuration changes on the domain.</p>"}, "SoftwareUpdateOptions": {"shape": "SoftwareUpdateOptions", "documentation": "<p>Service software update options for the domain.</p>"}}, "documentation": "<p>The current status of an OpenSearch Service domain.</p>"}, "DomainStatusList": {"type": "list", "member": {"shape": "DomainStatus"}, "documentation": "<p>List that contains the status of each specified OpenSearch Service domain.</p>"}, "Double": {"type": "double"}, "DryRun": {"type": "boolean"}, "DryRunMode": {"type": "string", "enum": ["Basic", "Verbose"]}, "DryRunProgressStatus": {"type": "structure", "required": ["DryRunId", "DryRunStatus", "CreationDate", "UpdateDate"], "members": {"DryRunId": {"shape": "GUID", "documentation": "<p>The unique identifier of the dry run.</p>"}, "DryRunStatus": {"shape": "String", "documentation": "<p>The current status of the dry run.</p>"}, "CreationDate": {"shape": "String", "documentation": "<p>The timestamp when the dry run was initiated.</p>"}, "UpdateDate": {"shape": "String", "documentation": "<p>The timestamp when the dry run was last updated.</p>"}, "ValidationFailures": {"shape": "ValidationFailures", "documentation": "<p>Any validation failures that occurred as a result of the dry run.</p>"}}, "documentation": "<p>Information about the progress of a pre-upgrade dry run analysis.</p>"}, "DryRunResults": {"type": "structure", "members": {"DeploymentType": {"shape": "DeploymentType", "documentation": "<p> Specifies the way in which OpenSearch Service will apply an update. Possible values are:</p> <ul> <li> <p> <b>Blue/Green</b> - The update requires a blue/green deployment.</p> </li> <li> <p> <b>DynamicUpdate</b> - No blue/green deployment required</p> </li> <li> <p> <b>Undetermined</b> - The domain is in the middle of an update and can't predict the deployment type. Try again after the update is complete.</p> </li> <li> <p> <b>None</b> - The request doesn't include any configuration changes.</p> </li> </ul>"}, "Message": {"shape": "Message", "documentation": "<p>A message corresponding to the deployment type.</p>"}}, "documentation": "<p>Results of a dry run performed in an update domain request.</p>"}, "Duration": {"type": "structure", "members": {"Value": {"shape": "DurationValue", "documentation": "<p>Integer to specify the value of a maintenance schedule duration.</p>"}, "Unit": {"shape": "TimeUnit", "documentation": "<p>The unit of measurement for the duration of a maintenance schedule.</p>"}}, "documentation": "<p>The duration of a maintenance schedule. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/auto-tune.html\">Auto-Tune for Amazon OpenSearch Service</a>.</p>"}, "DurationValue": {"type": "long", "documentation": "<p>Integer that specifies the value of a maintenance schedule duration.</p>", "max": 24, "min": 1}, "EBSOptions": {"type": "structure", "members": {"EBSEnabled": {"shape": "Boolean", "documentation": "<p>Indicates whether EBS volumes are attached to data nodes in an OpenSearch Service domain.</p>"}, "VolumeType": {"shape": "VolumeType", "documentation": "<p>Specifies the type of EBS volumes attached to data nodes.</p>"}, "VolumeSize": {"shape": "IntegerClass", "documentation": "<p>Specifies the size (in GiB) of EBS volumes attached to data nodes.</p>"}, "Iops": {"shape": "IntegerClass", "documentation": "<p>Specifies the baseline input/output (I/O) performance of EBS volumes attached to data nodes. Applicable only for the <code>gp3</code> and provisioned IOPS EBS volume types.</p>"}, "Throughput": {"shape": "IntegerClass", "documentation": "<p>Specifies the throughput (in MiB/s) of the EBS volumes attached to data nodes. Applicable only for the <code>gp3</code> volume type.</p>"}}, "documentation": "<p>Container for the parameters required to enable EBS-based storage for an OpenSearch Service domain.</p>"}, "EBSOptionsStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "EBSOptions", "documentation": "<p>The configured EBS options for the specified domain.</p>"}, "Status": {"shape": "OptionStatus", "documentation": "<p>The status of the EBS options for the specified domain.</p>"}}, "documentation": "<p>The status of the EBS options for the specified OpenSearch Service domain.</p>"}, "EncryptionAtRestOptions": {"type": "structure", "members": {"Enabled": {"shape": "Boolean", "documentation": "<p>True to enable encryption at rest.</p>"}, "KmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>The KMS key ID. Takes the form <code>1a2a3a4-1a2a-3a4a-5a6a-1a2a3a4a5a6a</code>.</p>"}}, "documentation": "<p>Specifies whether the domain should encrypt data at rest, and if so, the Key Management Service (KMS) key to use. Can be used only to create a new domain, not update an existing one.</p>"}, "EncryptionAtRestOptionsStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "EncryptionAtRestOptions", "documentation": "<p>Encryption at rest options for the specified domain.</p>"}, "Status": {"shape": "OptionStatus", "documentation": "<p>The status of the encryption at rest options for the specified domain.</p>"}}, "documentation": "<p>Status of the encryption at rest options for the specified OpenSearch Service domain.</p>"}, "Endpoint": {"type": "string", "pattern": "^[A-Za-z0-9\\-\\.]+$"}, "EndpointsMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "ServiceUrl"}}, "EngineType": {"type": "string", "enum": ["OpenSearch", "Elasticsearch"]}, "EngineVersion": {"type": "string", "pattern": "^Elasticsearch_[0-9]{1}\\.[0-9]{1,2}$|^OpenSearch_[0-9]{1,2}\\.[0-9]{1,2}$"}, "EnvironmentInfo": {"type": "structure", "members": {"AvailabilityZoneInformation": {"shape": "AvailabilityZoneInfoList", "documentation": "<p> A list of <code>AvailabilityZoneInfo</code> for the domain.</p>"}}, "documentation": "<p>Information about the active domain environment.</p>"}, "EnvironmentInfoList": {"type": "list", "member": {"shape": "EnvironmentInfo"}}, "ErrorDetails": {"type": "structure", "members": {"ErrorType": {"shape": "ErrorType", "documentation": "<p>The type of error that occurred.</p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>A message describing the error.</p>"}}, "documentation": "<p>Additional information if the package is in an error state. Null otherwise.</p>"}, "ErrorMessage": {"type": "string"}, "ErrorType": {"type": "string"}, "Filter": {"type": "structure", "members": {"Name": {"shape": "NonEmptyString", "documentation": "<p>The name of the filter.</p>"}, "Values": {"shape": "ValueStringList", "documentation": "<p>One or more values for the filter.</p>"}}, "documentation": "<p>A filter used to limit results when describing inbound or outbound cross-cluster connections. You can specify multiple values per filter. A cross-cluster connection must match at least one of the specified values for it to be returned from an operation.</p>"}, "FilterList": {"type": "list", "member": {"shape": "Filter"}}, "GUID": {"type": "string", "max": 36, "min": 36, "pattern": "\\p{XDigit}{8}-\\p{XDigit}{4}-\\p{XDigit}{4}-\\p{XDigit}{4}-\\p{XDigit}{12}"}, "GetCompatibleVersionsRequest": {"type": "structure", "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>The name of an existing domain. Provide this parameter to limit the results to a single domain.</p>", "location": "querystring", "locationName": "domainName"}}, "documentation": "<p>Container for the request parameters to <code>GetCompatibleVersions</code> operation.</p>"}, "GetCompatibleVersionsResponse": {"type": "structure", "members": {"CompatibleVersions": {"shape": "CompatibleVersionsList", "documentation": "<p>A map of OpenSearch or Elasticsearch versions and the versions you can upgrade them to.</p>"}}, "documentation": "<p>Container for the response returned by the <code>GetCompatibleVersions</code> operation.</p>"}, "GetDomainMaintenanceStatusRequest": {"type": "structure", "required": ["DomainName", "MaintenanceId"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>The name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "MaintenanceId": {"shape": "RequestId", "documentation": "<p>The request ID of the maintenance action.</p>", "location": "querystring", "locationName": "maintenanceId"}}, "documentation": "<p>Container for the parameters to the <code>GetDomainMaintenanceStatus</code> operation.</p>"}, "GetDomainMaintenanceStatusResponse": {"type": "structure", "members": {"Status": {"shape": "MaintenanceStatus", "documentation": "<p>The status of the maintenance action.</p>"}, "StatusMessage": {"shape": "MaintenanceStatusMessage", "documentation": "<p>The status message of the maintenance action.</p>"}, "NodeId": {"shape": "NodeId", "documentation": "<p>The node ID of the maintenance action.</p>"}, "Action": {"shape": "MaintenanceType", "documentation": "<p>The action name.</p>"}, "CreatedAt": {"shape": "UpdateTimestamp", "documentation": "<p>The time at which the action was created.</p>"}, "UpdatedAt": {"shape": "UpdateTimestamp", "documentation": "<p>The time at which the action was updated.</p>"}}, "documentation": "<p>The result of a <code>GetDomainMaintenanceStatus</code> request that information about the requested action.</p>"}, "GetPackageVersionHistoryRequest": {"type": "structure", "required": ["PackageID"], "members": {"PackageID": {"shape": "PackageID", "documentation": "<p>The unique identifier of the package.</p>", "location": "uri", "locationName": "PackageID"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>An optional parameter that specifies the maximum number of results to return. You can use <code>nextToken</code> to get the next page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If your initial <code>GetPackageVersionHistory</code> operation returns a <code>nextToken</code>, you can include the returned <code>nextToken</code> in subsequent <code>GetPackageVersionHistory</code> operations, which returns results in the next page. </p>", "location": "querystring", "locationName": "nextToken"}}, "documentation": "<p>Container for the request parameters to the <code>GetPackageVersionHistory</code> operation.</p>"}, "GetPackageVersionHistoryResponse": {"type": "structure", "members": {"PackageID": {"shape": "PackageID", "documentation": "<p>The unique identifier of the package.</p>"}, "PackageVersionHistoryList": {"shape": "PackageVersionHistoryList", "documentation": "<p>A list of package versions, along with their creation time and commit message.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>When <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}}, "documentation": "<p>Container for response returned by <code>GetPackageVersionHistory</code> operation.</p>"}, "GetUpgradeHistoryRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>The name of an existing domain.</p>", "location": "uri", "locationName": "DomainName"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>An optional parameter that specifies the maximum number of results to return. You can use <code>nextToken</code> to get the next page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If your initial <code>GetUpgradeHistory</code> operation returns a <code>nextToken</code>, you can include the returned <code>nextToken</code> in subsequent <code>GetUpgradeHistory</code> operations, which returns results in the next page.</p>", "location": "querystring", "locationName": "nextToken"}}, "documentation": "<p>Container for the request parameters to the <code>GetUpgradeHistory</code> operation.</p>"}, "GetUpgradeHistoryResponse": {"type": "structure", "members": {"UpgradeHistories": {"shape": "UpgradeHistoryList", "documentation": "<p>A list of objects corresponding to each upgrade or upgrade eligibility check performed on a domain.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>When <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}}, "documentation": "<p>Container for the response returned by the <code>GetUpgradeHistory</code> operation.</p>"}, "GetUpgradeStatusRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>The domain of the domain to get upgrade status information for.</p>", "location": "uri", "locationName": "DomainName"}}, "documentation": "<p>Container for the request parameters to the <code>GetUpgradeStatus</code> operation.</p>"}, "GetUpgradeStatusResponse": {"type": "structure", "members": {"UpgradeStep": {"shape": "UpgradeStep", "documentation": "<p>One of three steps that an upgrade or upgrade eligibility check goes through.</p>"}, "StepStatus": {"shape": "UpgradeStatus", "documentation": "<p>The status of the current step that an upgrade is on.</p>"}, "UpgradeName": {"shape": "UpgradeName", "documentation": "<p>A string that describes the update.</p>"}}, "documentation": "<p>Container for the response returned by the <code>GetUpgradeStatus</code> operation.</p>"}, "IPAddressType": {"type": "string", "enum": ["ipv4", "dualstack"]}, "IPAddressTypeStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "IPAddressType", "documentation": "<p>The IP address options for the domain.</p>"}, "Status": {"shape": "OptionStatus"}}, "documentation": "<p>The IP address type status for the domain.</p>"}, "IdentityPoolId": {"type": "string", "max": 55, "min": 1, "pattern": "[\\w-]+:[0-9a-f-]+"}, "InboundConnection": {"type": "structure", "members": {"LocalDomainInfo": {"shape": "DomainInformationContainer", "documentation": "<p>Information about the source (local) domain.</p>"}, "RemoteDomainInfo": {"shape": "DomainInformationContainer", "documentation": "<p>Information about the destination (remote) domain.</p>"}, "ConnectionId": {"shape": "ConnectionId", "documentation": "<p>The unique identifier of the connection.</p>"}, "ConnectionStatus": {"shape": "InboundConnectionStatus", "documentation": "<p>The current status of the connection.</p>"}, "ConnectionMode": {"shape": "ConnectionMode", "documentation": "<p>The connection mode.</p>"}}, "documentation": "<p>Describes an inbound cross-cluster connection for Amazon OpenSearch Service. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/cross-cluster-search.html\">Cross-cluster search for Amazon OpenSearch Service</a>.</p>"}, "InboundConnectionStatus": {"type": "structure", "members": {"StatusCode": {"shape": "InboundConnectionStatusCode", "documentation": "<p>The status code for the connection. Can be one of the following:</p> <ul> <li> <p> <b>PENDING_ACCEPTANCE</b> - Inbound connection is not yet accepted by the remote domain owner.</p> </li> <li> <p> <b>APPROVED</b>: Inbound connection is pending acceptance by the remote domain owner.</p> </li> <li> <p> <b>PROVISIONING</b>: Inbound connection is being provisioned.</p> </li> <li> <p> <b>ACTIVE</b>: Inbound connection is active and ready to use.</p> </li> <li> <p> <b>REJECTING</b>: Inbound connection rejection is in process.</p> </li> <li> <p> <b>REJECTED</b>: Inbound connection is rejected.</p> </li> <li> <p> <b>DELETING</b>: Inbound connection deletion is in progress.</p> </li> <li> <p> <b>DELETED</b>: Inbound connection is deleted and can no longer be used.</p> </li> </ul>"}, "Message": {"shape": "ConnectionStatusMessage", "documentation": "<p>Information about the connection.</p>"}}, "documentation": "<p>The status of an inbound cross-cluster connection for OpenSearch Service.</p>"}, "InboundConnectionStatusCode": {"type": "string", "enum": ["PENDING_ACCEPTANCE", "APPROVED", "PROVISIONING", "ACTIVE", "REJECTING", "REJECTED", "DELETING", "DELETED"]}, "InboundConnections": {"type": "list", "member": {"shape": "InboundConnection"}}, "InstanceCount": {"type": "integer", "documentation": "<p>Number of instances in an OpenSearch Service cluster.</p>", "min": 1}, "InstanceCountLimits": {"type": "structure", "members": {"MinimumInstanceCount": {"shape": "MinimumInstanceCount", "documentation": "<p>The maximum allowed number of instances.</p>"}, "MaximumInstanceCount": {"shape": "MaximumInstanceCount", "documentation": "<p>The minimum allowed number of instances.</p>"}}, "documentation": "<p>Limits on the number of instances that can be created in OpenSearch Service for a given instance type.</p>"}, "InstanceLimits": {"type": "structure", "members": {"InstanceCountLimits": {"shape": "InstanceCountLimits", "documentation": "<p>Limits on the number of instances that can be created for a given instance type.</p>"}}, "documentation": "<p>Instance-related attributes that are available for a given instance type.</p>"}, "InstanceRole": {"type": "string"}, "InstanceRoleList": {"type": "list", "member": {"shape": "InstanceRole"}}, "InstanceTypeDetails": {"type": "structure", "members": {"InstanceType": {"shape": "OpenSearchPartitionInstanceType", "documentation": "<p>The instance type.</p>"}, "EncryptionEnabled": {"shape": "Boolean", "documentation": "<p>Whether encryption at rest and node-to-node encryption are supported for the instance type.</p>"}, "CognitoEnabled": {"shape": "Boolean", "documentation": "<p>Whether Amazon Cognito access is supported for the instance type.</p>"}, "AppLogsEnabled": {"shape": "Boolean", "documentation": "<p>Whether logging is supported for the instance type.</p>"}, "AdvancedSecurityEnabled": {"shape": "Boolean", "documentation": "<p>Whether fine-grained access control is supported for the instance type.</p>"}, "WarmEnabled": {"shape": "Boolean", "documentation": "<p>Whether UltraWarm is supported for the instance type.</p>"}, "InstanceRole": {"shape": "InstanceRoleList", "documentation": "<p>Whether the instance acts as a data node, a dedicated master node, or an UltraWarm node.</p>"}, "AvailabilityZones": {"shape": "AvailabilityZoneList", "documentation": "<p>The supported Availability Zones for the instance type.</p>"}}, "documentation": "<p>Lists all instance types and available features for a given OpenSearch or Elasticsearch version.</p>"}, "InstanceTypeDetailsList": {"type": "list", "member": {"shape": "InstanceTypeDetails"}}, "InstanceTypeString": {"type": "string", "max": 40, "min": 10, "pattern": "^.*\\..*\\.search$"}, "Integer": {"type": "integer"}, "IntegerClass": {"type": "integer"}, "InternalException": {"type": "structure", "members": {}, "documentation": "<p>Request processing failed because of an unknown error, exception, or internal failure.</p>", "error": {"httpStatusCode": 500}, "exception": true}, "InvalidPaginationTokenException": {"type": "structure", "members": {}, "documentation": "<p>Request processing failed because you provided an invalid pagination token.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "InvalidTypeException": {"type": "structure", "members": {}, "documentation": "<p>An exception for trying to create or access a sub-resource that's either invalid or not supported.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "Issue": {"type": "string"}, "Issues": {"type": "list", "member": {"shape": "Issue"}}, "KmsKeyId": {"type": "string", "max": 500, "min": 1, "pattern": ".*"}, "LastUpdated": {"type": "timestamp"}, "LimitExceededException": {"type": "structure", "members": {}, "documentation": "<p>An exception for trying to create more than the allowed number of resources or sub-resources.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "LimitName": {"type": "string"}, "LimitValue": {"type": "string"}, "LimitValueList": {"type": "list", "member": {"shape": "LimitValue"}}, "Limits": {"type": "structure", "members": {"StorageTypes": {"shape": "StorageTypeList", "documentation": "<p>Storage-related attributes that are available for a given instance type.</p>"}, "InstanceLimits": {"shape": "InstanceLimits", "documentation": "<p>The limits for a given instance type.</p>"}, "AdditionalLimits": {"shape": "AdditionalLimitList", "documentation": "<p>List of additional limits that are specific to a given instance type for each of its instance roles.</p>"}}, "documentation": "<p>Limits for a given instance type and for each of its roles.</p>"}, "LimitsByRole": {"type": "map", "key": {"shape": "InstanceRole"}, "value": {"shape": "Limits"}, "documentation": "<p> The role of a given instance and all applicable limits. The role performed by a given OpenSearch instance can be one of the following: </p> <ul> <li> <p> <b>data</b> - A data node.</p> </li> <li> <p> <b>master</b> - A dedicated master node.</p> </li> <li> <p> <b>ultra_warm</b> - An UltraWarm node.</p> </li> </ul>"}, "ListDomainMaintenancesRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>The name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "Action": {"shape": "MaintenanceType", "documentation": "<p>The name of the action.</p>", "location": "querystring", "locationName": "action"}, "Status": {"shape": "MaintenanceStatus", "documentation": "<p>The status of the action.</p>", "location": "querystring", "locationName": "status"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>An optional parameter that specifies the maximum number of results to return. You can use <code>nextToken</code> to get the next page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If your initial <code>ListDomainMaintenances</code> operation returns a <code>nextToken</code>, include the returned <code>nextToken</code> in subsequent <code>ListDomainMaintenances</code> operations, which returns results in the next page.</p>", "location": "querystring", "locationName": "nextToken"}}, "documentation": "<p>Container for the parameters to the <code>ListDomainMaintenances</code> operation.</p>"}, "ListDomainMaintenancesResponse": {"type": "structure", "members": {"DomainMaintenances": {"shape": "DomainMaintenanceList", "documentation": "<p>A list of the submitted maintenance actions.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>When <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}}, "documentation": "<p>The result of a <code>ListDomainMaintenances</code> request that contains information about the requested actions. </p>"}, "ListDomainNamesRequest": {"type": "structure", "members": {"EngineType": {"shape": "EngineType", "documentation": "<p>Filters the output by domain engine type.</p>", "location": "querystring", "locationName": "engineType"}}, "documentation": "<p>Container for the parameters to the <code>ListDomainNames</code> operation.</p>"}, "ListDomainNamesResponse": {"type": "structure", "members": {"DomainNames": {"shape": "DomainInfoList", "documentation": "<p>The names of all OpenSearch Service domains owned by the current user and their respective engine types.</p>"}}, "documentation": "<p>The results of a <code>ListDomainNames</code> operation. Contains the names of all domains owned by this account and their respective engine types.</p>"}, "ListDomainsForPackageRequest": {"type": "structure", "required": ["PackageID"], "members": {"PackageID": {"shape": "PackageID", "documentation": "<p>The unique identifier of the package for which to list associated domains.</p>", "location": "uri", "locationName": "PackageID"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>An optional parameter that specifies the maximum number of results to return. You can use <code>nextToken</code> to get the next page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If your initial <code>ListDomainsForPackage</code> operation returns a <code>nextToken</code>, you can include the returned <code>nextToken</code> in subsequent <code>ListDomainsForPackage</code> operations, which returns results in the next page.</p>", "location": "querystring", "locationName": "nextToken"}}, "documentation": "<p>Container for the request parameters to the <code>ListDomainsForPackage</code> operation.</p>"}, "ListDomainsForPackageResponse": {"type": "structure", "members": {"DomainPackageDetailsList": {"shape": "DomainPackageDetailsList", "documentation": "<p>Information about all domains associated with a package.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>When <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}}, "documentation": "<p>Container for the response parameters to the <code>ListDomainsForPackage</code> operation.</p>"}, "ListInstanceTypeDetailsRequest": {"type": "structure", "required": ["EngineVersion"], "members": {"EngineVersion": {"shape": "VersionString", "documentation": "<p>The version of OpenSearch or Elasticsearch, in the format Elasticsearch_X.Y or OpenSearch_X.Y. Defaults to the latest version of OpenSearch.</p>", "location": "uri", "locationName": "EngineVersion"}, "DomainName": {"shape": "DomainName", "documentation": "<p>The name of the domain.</p>", "location": "querystring", "locationName": "domainName"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>An optional parameter that specifies the maximum number of results to return. You can use <code>nextToken</code> to get the next page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If your initial <code>ListInstanceTypeDetails</code> operation returns a <code>nextToken</code>, you can include the returned <code>nextToken</code> in subsequent <code>ListInstanceTypeDetails</code> operations, which returns results in the next page.</p>", "location": "querystring", "locationName": "nextToken"}, "RetrieveAZs": {"shape": "Boolean", "documentation": "<p>An optional parameter that specifies the Availability Zones for the domain.</p>", "location": "querystring", "locationName": "retrieveAZs"}, "InstanceType": {"shape": "InstanceTypeString", "documentation": "<p>An optional parameter that lists information for a given instance type.</p>", "location": "querystring", "locationName": "instanceType"}}}, "ListInstanceTypeDetailsResponse": {"type": "structure", "members": {"InstanceTypeDetails": {"shape": "InstanceTypeDetailsList", "documentation": "<p>Lists all supported instance types and features for the given OpenSearch or Elasticsearch version.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>When <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}}}, "ListPackagesForDomainRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>The name of the domain for which you want to list associated packages.</p>", "location": "uri", "locationName": "DomainName"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>An optional parameter that specifies the maximum number of results to return. You can use <code>nextToken</code> to get the next page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If your initial <code>ListPackagesForDomain</code> operation returns a <code>nextToken</code>, you can include the returned <code>nextToken</code> in subsequent <code>ListPackagesForDomain</code> operations, which returns results in the next page.</p>", "location": "querystring", "locationName": "nextToken"}}, "documentation": "<p>Container for the request parameters to the <code>ListPackagesForDomain</code> operation.</p>"}, "ListPackagesForDomainResponse": {"type": "structure", "members": {"DomainPackageDetailsList": {"shape": "DomainPackageDetailsList", "documentation": "<p>List of all packages associated with a domain.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>When <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}}, "documentation": "<p>Container for the response parameters to the <code>ListPackagesForDomain</code> operation.</p>"}, "ListScheduledActionsRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>The name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>An optional parameter that specifies the maximum number of results to return. You can use <code>nextToken</code> to get the next page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If your initial <code>ListScheduledActions</code> operation returns a <code>nextToken</code>, you can include the returned <code>nextToken</code> in subsequent <code>ListScheduledActions</code> operations, which returns results in the next page.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListScheduledActionsResponse": {"type": "structure", "members": {"ScheduledActions": {"shape": "ScheduledActionsList", "documentation": "<p>A list of actions that are scheduled for the domain.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>When <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}}}, "ListTagsRequest": {"type": "structure", "required": ["ARN"], "members": {"ARN": {"shape": "ARN", "documentation": "<p>Amazon Resource Name (ARN) for the domain to view tags for.</p>", "location": "querystring", "locationName": "arn"}}, "documentation": "<p>Container for the parameters to the <code>ListTags</code> operation.</p>"}, "ListTagsResponse": {"type": "structure", "members": {"TagList": {"shape": "TagList", "documentation": "<p>List of resource tags associated with the specified domain.</p>"}}, "documentation": "<p>The results of a <code>ListTags</code> operation.</p>"}, "ListVersionsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>An optional parameter that specifies the maximum number of results to return. You can use <code>nextToken</code> to get the next page of results.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If your initial <code>ListVersions</code> operation returns a <code>nextToken</code>, you can include the returned <code>nextToken</code> in subsequent <code>ListVersions</code> operations, which returns results in the next page.</p>", "location": "querystring", "locationName": "nextToken"}}, "documentation": "<p>Container for the request parameters to the <code>ListVersions</code> operation.</p>"}, "ListVersionsResponse": {"type": "structure", "members": {"Versions": {"shape": "VersionList", "documentation": "<p>A list of all versions of OpenSearch and Elasticsearch that Amazon OpenSearch Service supports.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>When <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}}, "documentation": "<p>Container for the parameters for response received from the <code>ListVersions</code> operation.</p>"}, "ListVpcEndpointAccessRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>The name of the OpenSearch Service domain to retrieve access information for.</p>", "location": "uri", "locationName": "DomainName"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If your initial <code>ListVpcEndpointAccess</code> operation returns a <code>nextToken</code>, you can include the returned <code>nextToken</code> in subsequent <code>ListVpcEndpointAccess</code> operations, which returns results in the next page.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListVpcEndpointAccessResponse": {"type": "structure", "required": ["AuthorizedPrincipalList", "NextToken"], "members": {"AuthorizedPrincipalList": {"shape": "AuthorizedPrincipalList", "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_policies_elements_principal.html\">IAM principals</a> that can currently access the domain.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>When <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}}}, "ListVpcEndpointsForDomainRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>The name of the domain to list associated VPC endpoints for.</p>", "location": "uri", "locationName": "DomainName"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If your initial <code>ListEndpointsForDomain</code> operation returns a <code>nextToken</code>, you can include the returned <code>nextToken</code> in subsequent <code>ListEndpointsForDomain</code> operations, which returns results in the next page.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListVpcEndpointsForDomainResponse": {"type": "structure", "required": ["VpcEndpointSummaryList", "NextToken"], "members": {"VpcEndpointSummaryList": {"shape": "VpcEndpointSummaryList", "documentation": "<p>Information about each endpoint associated with the domain.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>When <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}}}, "ListVpcEndpointsRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>If your initial <code>ListVpcEndpoints</code> operation returns a <code>nextToken</code>, you can include the returned <code>nextToken</code> in subsequent <code>ListVpcEndpoints</code> operations, which returns results in the next page.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListVpcEndpointsResponse": {"type": "structure", "required": ["VpcEndpointSummaryList", "NextToken"], "members": {"VpcEndpointSummaryList": {"shape": "VpcEndpointSummaryList", "documentation": "<p>Information about each endpoint.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>When <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}}}, "LogPublishingOption": {"type": "structure", "members": {"CloudWatchLogsLogGroupArn": {"shape": "CloudWatchLogsLogGroupArn", "documentation": "<p>The Amazon Resource Name (ARN) of the CloudWatch Logs group to publish logs to.</p>"}, "Enabled": {"shape": "Boolean", "documentation": "<p>Whether the log should be published.</p>"}}, "documentation": "<p>Specifies whether the Amazon OpenSearch Service domain publishes the OpenSearch application and slow logs to Amazon CloudWatch. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/createdomain-configure-slow-logs.html\">Monitoring OpenSearch logs with Amazon CloudWatch Logs</a>.</p> <note> <p>After you enable log publishing, you still have to enable the collection of slow logs using the OpenSearch REST API.</p> </note>"}, "LogPublishingOptions": {"type": "map", "key": {"shape": "LogType"}, "value": {"shape": "LogPublishingOption"}}, "LogPublishingOptionsStatus": {"type": "structure", "members": {"Options": {"shape": "LogPublishingOptions", "documentation": "<p>The log publishing options configured for the domain.</p>"}, "Status": {"shape": "OptionStatus", "documentation": "<p>The status of the log publishing options for the domain.</p>"}}, "documentation": "<p>The configured log publishing options for the domain and their current status.</p>"}, "LogType": {"type": "string", "documentation": "<p>The type of log file. Can be one of the following:</p> <ul> <li> <p> <b>INDEX_SLOW_LOGS</b> - Index slow logs contain insert requests that took more time than the configured index query log threshold to execute.</p> </li> <li> <p> <b>SEARCH_SLOW_LOGS</b> - Search slow logs contain search queries that took more time than the configured search query log threshold to execute.</p> </li> <li> <p> <b>ES_APPLICATION_LOGS</b> - OpenSearch application logs contain information about errors and warnings raised during the operation of the service and can be useful for troubleshooting.</p> </li> <li> <p> <b>AUDIT_LOGS</b> - Audit logs contain records of user requests for access to the domain.</p> </li> </ul>", "enum": ["INDEX_SLOW_LOGS", "SEARCH_SLOW_LOGS", "ES_APPLICATION_LOGS", "AUDIT_LOGS"]}, "Long": {"type": "long"}, "MaintenanceStatus": {"type": "string", "enum": ["PENDING", "IN_PROGRESS", "COMPLETED", "FAILED", "TIMED_OUT"]}, "MaintenanceStatusMessage": {"type": "string", "max": 1000, "min": 0, "pattern": "^([\\s\\S]*)$"}, "MaintenanceType": {"type": "string", "enum": ["REBOOT_NODE", "RESTART_SEARCH_PROCESS", "RESTART_DASHBOARD"]}, "MasterNodeStatus": {"type": "string", "enum": ["Available", "UnAvailable"]}, "MasterUserOptions": {"type": "structure", "members": {"MasterUserARN": {"shape": "ARN", "documentation": "<p>Amazon Resource Name (ARN) for the master user. Only specify if <code>InternalUserDatabaseEnabled</code> is <code>false</code>.</p>"}, "MasterUserName": {"shape": "Username", "documentation": "<p>User name for the master user. Only specify if <code>InternalUserDatabaseEnabled</code> is <code>true</code>.</p>"}, "MasterUserPassword": {"shape": "Password", "documentation": "<p>Password for the master user. Only specify if <code>InternalUserDatabaseEnabled</code> is <code>true</code>.</p>"}}, "documentation": "<p>Credentials for the master user for a domain.</p>"}, "MaxResults": {"type": "integer", "documentation": "<p>An optional parameter that specifies the maximum number of results to return for a given request.</p>", "max": 100}, "MaximumInstanceCount": {"type": "integer", "documentation": "<p>Maximum number of instances that can be instantiated for a given instance type.</p>"}, "Message": {"type": "string", "max": 1024, "min": 0}, "MinimumInstanceCount": {"type": "integer", "documentation": "<p> Minimum number of instances that can be instantiated for a given instance type.</p>"}, "NextToken": {"type": "string", "documentation": "<p>When <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}, "NodeId": {"type": "string", "max": 40, "min": 10}, "NodeStatus": {"type": "string", "enum": ["Active", "StandBy", "NotAvailable"]}, "NodeToNodeEncryptionOptions": {"type": "structure", "members": {"Enabled": {"shape": "Boolean", "documentation": "<p>True to enable node-to-node encryption.</p>"}}, "documentation": "<p>Enables or disables node-to-node encryption. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/ntn.html\">Node-to-node encryption for Amazon OpenSearch Service</a>.</p>"}, "NodeToNodeEncryptionOptionsStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "NodeToNodeEncryptionOptions", "documentation": "<p>The node-to-node encryption options for the specified domain.</p>"}, "Status": {"shape": "OptionStatus", "documentation": "<p>The status of the node-to-node encryption options for the specified domain.</p>"}}, "documentation": "<p>Status of the node-to-node encryption options for the specified domain.</p>"}, "NodeType": {"type": "string", "enum": ["Data", "Ultrawarm", "Master"]}, "NonEmptyString": {"type": "string", "max": 100, "min": 1, "pattern": "[a-zA-Z0-9\\-\\_\\.]+"}, "NumberOfAZs": {"type": "string", "pattern": "^((\\d+)|(NotAvailable))$"}, "NumberOfNodes": {"type": "string", "pattern": "^((\\d+)|(NotAvailable))$"}, "NumberOfShards": {"type": "string", "pattern": "^((\\d+)|(NotAvailable))$"}, "OffPeakWindow": {"type": "structure", "members": {"WindowStartTime": {"shape": "WindowStartTime", "documentation": "<p>A custom start time for the off-peak window, in Coordinated Universal Time (UTC). The window length will always be 10 hours, so you can't specify an end time. For example, if you specify 11:00 P.M. UTC as a start time, the end time will automatically be set to 9:00 A.M.</p>"}}, "documentation": "<p>A custom 10-hour, low-traffic window during which OpenSearch Service can perform mandatory configuration changes on the domain. These actions can include scheduled service software updates and blue/green Auto-Tune enhancements. OpenSearch Service will schedule these actions during the window that you specify.</p> <p>If you don't specify a window start time, it defaults to 10:00 P.M. local time.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/off-peak.html\">Defining off-peak maintenance windows for Amazon OpenSearch Service</a>.</p>"}, "OffPeakWindowOptions": {"type": "structure", "members": {"Enabled": {"shape": "Boolean", "documentation": "<p>Whether to enable an off-peak window.</p> <p>This option is only available when modifying a domain created prior to February 16, 2023, not when creating a new domain. All domains created after this date have the off-peak window enabled by default. You can't disable the off-peak window after it's enabled for a domain.</p>"}, "OffPeakWindow": {"shape": "OffPeakWindow", "documentation": "<p>Off-peak window settings for the domain.</p>"}}, "documentation": "<p>Options for a domain's <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/APIReference/API_OffPeakWindow.html\">off-peak window</a>, during which OpenSearch Service can perform mandatory configuration changes on the domain.</p>"}, "OffPeakWindowOptionsStatus": {"type": "structure", "members": {"Options": {"shape": "OffPeakWindowOptions", "documentation": "<p>The domain's off-peak window configuration.</p>"}, "Status": {"shape": "OptionStatus", "documentation": "<p>The current status of off-peak window options.</p>"}}, "documentation": "<p>The status of <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/APIReference/API_OffPeakWindow.html\">off-peak window</a> options for a domain.</p>"}, "OpenSearchPartitionInstanceType": {"type": "string", "enum": ["m3.medium.search", "m3.large.search", "m3.xlarge.search", "m3.2xlarge.search", "m4.large.search", "m4.xlarge.search", "m4.2xlarge.search", "m4.4xlarge.search", "m4.10xlarge.search", "m5.large.search", "m5.xlarge.search", "m5.2xlarge.search", "m5.4xlarge.search", "m5.12xlarge.search", "m5.24xlarge.search", "r5.large.search", "r5.xlarge.search", "r5.2xlarge.search", "r5.4xlarge.search", "r5.12xlarge.search", "r5.24xlarge.search", "c5.large.search", "c5.xlarge.search", "c5.2xlarge.search", "c5.4xlarge.search", "c5.9xlarge.search", "c5.18xlarge.search", "t3.nano.search", "t3.micro.search", "t3.small.search", "t3.medium.search", "t3.large.search", "t3.xlarge.search", "t3.2xlarge.search", "ultrawarm1.medium.search", "ultrawarm1.large.search", "ultrawarm1.xlarge.search", "t2.micro.search", "t2.small.search", "t2.medium.search", "r3.large.search", "r3.xlarge.search", "r3.2xlarge.search", "r3.4xlarge.search", "r3.8xlarge.search", "i2.xlarge.search", "i2.2xlarge.search", "d2.xlarge.search", "d2.2xlarge.search", "d2.4xlarge.search", "d2.8xlarge.search", "c4.large.search", "c4.xlarge.search", "c4.2xlarge.search", "c4.4xlarge.search", "c4.8xlarge.search", "r4.large.search", "r4.xlarge.search", "r4.2xlarge.search", "r4.4xlarge.search", "r4.8xlarge.search", "r4.16xlarge.search", "i3.large.search", "i3.xlarge.search", "i3.2xlarge.search", "i3.4xlarge.search", "i3.8xlarge.search", "i3.16xlarge.search", "r6g.large.search", "r6g.xlarge.search", "r6g.2xlarge.search", "r6g.4xlarge.search", "r6g.8xlarge.search", "r6g.12xlarge.search", "m6g.large.search", "m6g.xlarge.search", "m6g.2xlarge.search", "m6g.4xlarge.search", "m6g.8xlarge.search", "m6g.12xlarge.search", "c6g.large.search", "c6g.xlarge.search", "c6g.2xlarge.search", "c6g.4xlarge.search", "c6g.8xlarge.search", "c6g.12xlarge.search", "r6gd.large.search", "r6gd.xlarge.search", "r6gd.2xlarge.search", "r6gd.4xlarge.search", "r6gd.8xlarge.search", "r6gd.12xlarge.search", "r6gd.16xlarge.search", "t4g.small.search", "t4g.medium.search"]}, "OpenSearchWarmPartitionInstanceType": {"type": "string", "enum": ["ultrawarm1.medium.search", "ultrawarm1.large.search", "ultrawarm1.xlarge.search"]}, "OptionState": {"type": "string", "documentation": "<p>The state of a requested domain configuration change. Can be one of the following:</p> <ul> <li> <p> <b>Processing</b> - The requested change is still in progress.</p> </li> <li> <p> <b>Active</b> - The requested change is processed and deployed to the domain.</p> </li> </ul>", "enum": ["RequiresIndexDocuments", "Processing", "Active"]}, "OptionStatus": {"type": "structure", "required": ["CreationDate", "UpdateDate", "State"], "members": {"CreationDate": {"shape": "UpdateTimestamp", "documentation": "<p>The timestamp when the entity was created.</p>"}, "UpdateDate": {"shape": "UpdateTimestamp", "documentation": "<p>The timestamp of the last time the entity was updated.</p>"}, "UpdateVersion": {"shape": "UIntValue", "documentation": "<p>The latest version of the entity.</p>"}, "State": {"shape": "OptionState", "documentation": "<p>The state of the entity.</p>"}, "PendingDeletion": {"shape": "Boolean", "documentation": "<p>Indicates whether the entity is being deleted.</p>"}}, "documentation": "<p>Provides the current status of an entity.</p>"}, "OutboundConnection": {"type": "structure", "members": {"LocalDomainInfo": {"shape": "DomainInformationContainer", "documentation": "<p>Information about the source (local) domain.</p>"}, "RemoteDomainInfo": {"shape": "DomainInformationContainer", "documentation": "<p>Information about the destination (remote) domain.</p>"}, "ConnectionId": {"shape": "ConnectionId", "documentation": "<p>Unique identifier of the connection.</p>"}, "ConnectionAlias": {"shape": "ConnectionAlias", "documentation": "<p>Name of the connection.</p>"}, "ConnectionStatus": {"shape": "OutboundConnectionStatus", "documentation": "<p>Status of the connection.</p>"}, "ConnectionMode": {"shape": "ConnectionMode", "documentation": "<p>The connection mode.</p>"}, "ConnectionProperties": {"shape": "ConnectionProperties", "documentation": "<p>Properties for the outbound connection.</p>"}}, "documentation": "<p>Specifies details about an outbound cross-cluster connection.</p>"}, "OutboundConnectionStatus": {"type": "structure", "members": {"StatusCode": {"shape": "OutboundConnectionStatusCode", "documentation": "<p>The status code for the outbound connection. Can be one of the following:</p> <ul> <li> <p> <b>VALIDATING</b> - The outbound connection request is being validated.</p> </li> <li> <p> <b>VALIDATION_FAILED</b> - Validation failed for the connection request.</p> </li> <li> <p> <b>PENDING_ACCEPTANCE</b>: Outbound connection request is validated and is not yet accepted by the remote domain owner.</p> </li> <li> <p> <b>APPROVED</b> - Outbound connection has been approved by the remote domain owner for getting provisioned.</p> </li> <li> <p> <b>PROVISIONING</b> - Outbound connection request is in process.</p> </li> <li> <p> <b>ACTIVE</b> - Outbound connection is active and ready to use.</p> </li> <li> <p> <b>REJECTING</b> - Outbound connection rejection by remote domain owner is in progress.</p> </li> <li> <p> <b>REJECTED</b> - Outbound connection request is rejected by remote domain owner.</p> </li> <li> <p> <b>DELETING</b> - Outbound connection deletion is in progress.</p> </li> <li> <p> <b>DELETED</b> - Outbound connection is deleted and can no longer be used.</p> </li> </ul>"}, "Message": {"shape": "ConnectionStatusMessage", "documentation": "<p>Verbose information for the outbound connection.</p>"}}, "documentation": "<p>The status of an outbound cross-cluster connection.</p>"}, "OutboundConnectionStatusCode": {"type": "string", "enum": ["VALIDATING", "VALIDATION_FAILED", "PENDING_ACCEPTANCE", "APPROVED", "PROVISIONING", "ACTIVE", "REJECTING", "REJECTED", "DELETING", "DELETED"]}, "OutboundConnections": {"type": "list", "member": {"shape": "OutboundConnection"}}, "OverallChangeStatus": {"type": "string", "documentation": "<p>The overall status value of the domain configuration change.</p>", "enum": ["PENDING", "PROCESSING", "COMPLETED", "FAILED"]}, "OwnerId": {"type": "string", "max": 12, "min": 12, "pattern": "[0-9]+"}, "PackageDescription": {"type": "string", "max": 1024}, "PackageDetails": {"type": "structure", "members": {"PackageID": {"shape": "PackageID", "documentation": "<p>The unique identifier of the package.</p>"}, "PackageName": {"shape": "PackageName", "documentation": "<p>The user-specified name of the package.</p>"}, "PackageType": {"shape": "PackageType", "documentation": "<p>The type of package.</p>"}, "PackageDescription": {"shape": "PackageDescription", "documentation": "<p>User-specified description of the package.</p>"}, "PackageStatus": {"shape": "PackageStatus", "documentation": "<p>The current status of the package. The available options are <code>AVAILABLE</code>, <code>COPYING</code>, <code>COPY_FAILED</code>, <code>VALIDATNG</code>, <code>VAL<PERSON>ATION_FAILED</code>, <code>DELETING</code>, and <code>DELETE_FAILED</code>.</p>"}, "CreatedAt": {"shape": "CreatedAt", "documentation": "<p>The timestamp when the package was created.</p>"}, "LastUpdatedAt": {"shape": "LastUpdated", "documentation": "<p>Date and time when the package was last updated.</p>"}, "AvailablePackageVersion": {"shape": "PackageVersion", "documentation": "<p>The package version.</p>"}, "ErrorDetails": {"shape": "ErrorDetails", "documentation": "<p>Additional information if the package is in an error state. Null otherwise.</p>"}, "EngineVersion": {"shape": "EngineVersion", "documentation": "<p>Version of OpenSearch or Elasticsearch, in the format Elasticsearch_X.Y or OpenSearch_X.Y. Defaults to the latest version of OpenSearch.</p>"}, "AvailablePluginProperties": {"shape": "PluginProperties", "documentation": "<p>If the package is a <code>ZIP-PLUGIN</code> package, additional information about plugin properties.</p>"}}, "documentation": "<p>Basic information about a package.</p>"}, "PackageDetailsList": {"type": "list", "member": {"shape": "PackageDetails"}}, "PackageID": {"type": "string", "pattern": "^([FG][0-9]+)$"}, "PackageName": {"type": "string", "max": 256, "min": 3, "pattern": "[a-z][a-z0-9\\-]+"}, "PackageSource": {"type": "structure", "members": {"S3BucketName": {"shape": "S3BucketName", "documentation": "<p>The name of the Amazon S3 bucket containing the package.</p>"}, "S3Key": {"shape": "S3Key", "documentation": "<p>Key (file name) of the package.</p>"}}, "documentation": "<p>The Amazon S3 location to import the package from.</p>"}, "PackageStatus": {"type": "string", "enum": ["COPYING", "COPY_FAILED", "VALIDATING", "VALIDATION_FAILED", "AVAILABLE", "DELETING", "DELETED", "DELETE_FAILED"]}, "PackageType": {"type": "string", "enum": ["TXT-DICTIONARY", "ZIP-PLUGIN"]}, "PackageVersion": {"type": "string"}, "PackageVersionHistory": {"type": "structure", "members": {"PackageVersion": {"shape": "PackageVersion", "documentation": "<p>The package version.</p>"}, "CommitMessage": {"shape": "CommitMessage", "documentation": "<p>A message associated with the package version when it was uploaded.</p>"}, "CreatedAt": {"shape": "CreatedAt", "documentation": "<p>The date and time when the package was created.</p>"}, "PluginProperties": {"shape": "PluginProperties", "documentation": "<p>Additional information about plugin properties if the package is a <code>ZIP-PLUGIN</code> package.</p>"}}, "documentation": "<p>Details about a package version.</p>"}, "PackageVersionHistoryList": {"type": "list", "member": {"shape": "PackageVersionHistory"}}, "Password": {"type": "string", "max": 128, "min": 8, "pattern": ".*", "sensitive": true}, "PluginClassName": {"type": "string", "max": 1024}, "PluginDescription": {"type": "string", "max": 1024}, "PluginName": {"type": "string", "max": 1024}, "PluginProperties": {"type": "structure", "members": {"Name": {"shape": "PluginName", "documentation": "<p>The name of the plugin.</p>"}, "Description": {"shape": "PluginDescription", "documentation": "<p>The description of the plugin.</p>"}, "Version": {"shape": "PluginVersion", "documentation": "<p>The version of the plugin.</p>"}, "ClassName": {"shape": "PluginClassName", "documentation": "<p>The name of the class to load.</p>"}, "UncompressedSizeInBytes": {"shape": "UncompressedPluginSizeInBytes", "documentation": "<p>The uncompressed size of the plugin.</p>"}}, "documentation": "<p>Basic information about the plugin.</p>"}, "PluginVersion": {"type": "string", "max": 1024}, "PolicyDocument": {"type": "string", "documentation": "<p>Access policy rules for an Amazon OpenSearch Service domain endpoint. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/createupdatedomains.html#createdomain-configure-access-policies\">Configuring access policies</a>. The maximum size of a policy document is 100 KB.</p>", "max": 102400, "min": 0, "pattern": ".*"}, "PrincipalType": {"type": "string", "enum": ["AWS_ACCOUNT", "AWS_SERVICE"]}, "PurchaseReservedInstanceOfferingRequest": {"type": "structure", "required": ["ReservedInstanceOfferingId", "ReservationName"], "members": {"ReservedInstanceOfferingId": {"shape": "GUID", "documentation": "<p>The ID of the Reserved Instance offering to purchase.</p>"}, "ReservationName": {"shape": "ReservationToken", "documentation": "<p>A customer-specified identifier to track this reservation.</p>"}, "InstanceCount": {"shape": "InstanceCount", "documentation": "<p>The number of OpenSearch instances to reserve.</p>"}}, "documentation": "<p>Container for request parameters to the <code>PurchaseReservedInstanceOffering</code> operation.</p>"}, "PurchaseReservedInstanceOfferingResponse": {"type": "structure", "members": {"ReservedInstanceId": {"shape": "GUID", "documentation": "<p>The ID of the Reserved Instance offering that was purchased.</p>"}, "ReservationName": {"shape": "ReservationToken", "documentation": "<p>The customer-specified identifier used to track this reservation.</p>"}}, "documentation": "<p>Represents the output of a <code>PurchaseReservedInstanceOffering</code> operation.</p>"}, "RecurringCharge": {"type": "structure", "members": {"RecurringChargeAmount": {"shape": "Double", "documentation": "<p>The monetary amount of the recurring charge.</p>"}, "RecurringChargeFrequency": {"shape": "String", "documentation": "<p>The frequency of the recurring charge.</p>"}}, "documentation": "<p>Contains the specific price and frequency of a recurring charges for an OpenSearch Reserved Instance, or for a Reserved Instance offering.</p>"}, "RecurringChargeList": {"type": "list", "member": {"shape": "RecurringCharge"}}, "ReferencePath": {"type": "string"}, "Region": {"type": "string", "max": 30, "min": 5, "pattern": "[a-z][a-z0-9\\-]+"}, "RejectInboundConnectionRequest": {"type": "structure", "required": ["ConnectionId"], "members": {"ConnectionId": {"shape": "ConnectionId", "documentation": "<p>The unique identifier of the inbound connection to reject.</p>", "location": "uri", "locationName": "ConnectionId"}}, "documentation": "<p>Container for the request parameters to the <code>RejectInboundConnection</code> operation.</p>"}, "RejectInboundConnectionResponse": {"type": "structure", "members": {"Connection": {"shape": "InboundConnection", "documentation": "<p>Contains details about the rejected inbound connection.</p>"}}, "documentation": "<p>Represents the output of a <code>RejectInboundConnection</code> operation.</p>"}, "RemoveTagsRequest": {"type": "structure", "required": ["ARN", "TagKeys"], "members": {"ARN": {"shape": "ARN", "documentation": "<p>The Amazon Resource Name (ARN) of the domain from which you want to delete the specified tags.</p>"}, "TagKeys": {"shape": "StringList", "documentation": "<p>The list of tag keys to remove from the domain.</p>"}}, "documentation": "<p>Container for the request parameters to the <code>RemoveTags</code> operation.</p>"}, "RequestId": {"type": "string", "max": 100, "min": 1, "pattern": "^([\\s\\S]*)$"}, "ReservationToken": {"type": "string", "max": 64, "min": 5, "pattern": ".*"}, "ReservedInstance": {"type": "structure", "members": {"ReservationName": {"shape": "ReservationToken", "documentation": "<p>The customer-specified identifier to track this reservation.</p>"}, "ReservedInstanceId": {"shape": "GUID", "documentation": "<p>The unique identifier for the reservation.</p>"}, "BillingSubscriptionId": {"shape": "<PERSON>", "documentation": "<p>The unique identifier of the billing subscription.</p>"}, "ReservedInstanceOfferingId": {"shape": "String", "documentation": "<p>The unique identifier of the Reserved Instance offering.</p>"}, "InstanceType": {"shape": "OpenSearchPartitionInstanceType", "documentation": "<p>The OpenSearch instance type offered by theReserved Instance offering.</p>"}, "StartTime": {"shape": "UpdateTimestamp", "documentation": "<p>The date and time when the reservation was purchased.</p>"}, "Duration": {"shape": "Integer", "documentation": "<p>The duration, in seconds, for which the OpenSearch instance is reserved.</p>"}, "FixedPrice": {"shape": "Double", "documentation": "<p>The upfront fixed charge you will paid to purchase the specific Reserved Instance offering.</p>"}, "UsagePrice": {"shape": "Double", "documentation": "<p>The hourly rate at which you're charged for the domain using this Reserved Instance.</p>"}, "CurrencyCode": {"shape": "String", "documentation": "<p>The currency code for the offering.</p>"}, "InstanceCount": {"shape": "Integer", "documentation": "<p>The number of OpenSearch instances that have been reserved.</p>"}, "State": {"shape": "String", "documentation": "<p>The state of the Reserved Instance.</p>"}, "PaymentOption": {"shape": "ReservedInstancePaymentOption", "documentation": "<p>The payment option as defined in the Reserved Instance offering.</p>"}, "RecurringCharges": {"shape": "RecurringChargeList", "documentation": "<p>The recurring charge to your account, regardless of whether you create any domains using the Reserved Instance offering.</p>"}}, "documentation": "<p>Details of an OpenSearch Reserved Instance.</p>"}, "ReservedInstanceList": {"type": "list", "member": {"shape": "ReservedInstance"}}, "ReservedInstanceOffering": {"type": "structure", "members": {"ReservedInstanceOfferingId": {"shape": "GUID", "documentation": "<p>The unique identifier of the Reserved Instance offering.</p>"}, "InstanceType": {"shape": "OpenSearchPartitionInstanceType", "documentation": "<p>The OpenSearch instance type offered by the Reserved Instance offering.</p>"}, "Duration": {"shape": "Integer", "documentation": "<p>The duration, in seconds, for which the offering will reserve the OpenSearch instance.</p>"}, "FixedPrice": {"shape": "Double", "documentation": "<p>The upfront fixed charge you will pay to purchase the specific Reserved Instance offering.</p>"}, "UsagePrice": {"shape": "Double", "documentation": "<p>The hourly rate at which you're charged for the domain using this Reserved Instance.</p>"}, "CurrencyCode": {"shape": "String", "documentation": "<p>The currency code for the Reserved Instance offering.</p>"}, "PaymentOption": {"shape": "ReservedInstancePaymentOption", "documentation": "<p>Payment option for the Reserved Instance offering</p>"}, "RecurringCharges": {"shape": "RecurringChargeList", "documentation": "<p>The recurring charge to your account, regardless of whether you creates any domains using the offering.</p>"}}, "documentation": "<p>Details of an OpenSearch Reserved Instance offering.</p>"}, "ReservedInstanceOfferingList": {"type": "list", "member": {"shape": "ReservedInstanceOffering"}}, "ReservedInstancePaymentOption": {"type": "string", "enum": ["ALL_UPFRONT", "PARTIAL_UPFRONT", "NO_UPFRONT"]}, "ResourceAlreadyExistsException": {"type": "structure", "members": {}, "documentation": "<p>An exception for creating a resource that already exists.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "ResourceNotFoundException": {"type": "structure", "members": {}, "documentation": "<p>An exception for accessing or deleting a resource that doesn't exist.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "RevokeVpcEndpointAccessRequest": {"type": "structure", "required": ["DomainName", "Account"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>The name of the OpenSearch Service domain.</p>", "location": "uri", "locationName": "DomainName"}, "Account": {"shape": "AWSAccount", "documentation": "<p>The account ID to revoke access from.</p>"}}}, "RevokeVpcEndpointAccessResponse": {"type": "structure", "members": {}}, "RoleArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:(aws|aws\\-cn|aws\\-us\\-gov|aws\\-iso|aws\\-iso\\-b):iam::[0-9]+:role\\/.*"}, "RollbackOnDisable": {"type": "string", "documentation": "<p>The rollback state while disabling Auto-Tune for the domain.</p>", "enum": ["NO_ROLLBACK", "DEFAULT_ROLLBACK"]}, "S3BucketName": {"type": "string", "max": 63, "min": 3}, "S3Key": {"type": "string", "max": 1024, "min": 1}, "SAMLEntityId": {"type": "string", "max": 512, "min": 8}, "SAMLIdp": {"type": "structure", "required": ["Metada<PERSON><PERSON><PERSON><PERSON>", "EntityId"], "members": {"MetadataContent": {"shape": "SAMLMetadata", "documentation": "<p>The metadata of the SAML application, in XML format.</p>"}, "EntityId": {"shape": "SAMLEntityId", "documentation": "<p>The unique entity ID of the application in the SAML identity provider.</p>"}}, "documentation": "<p>The SAML identity povider information.</p>"}, "SAMLMetadata": {"type": "string", "max": 1048576, "min": 1}, "SAMLOptionsInput": {"type": "structure", "members": {"Enabled": {"shape": "Boolean", "documentation": "<p>True to enable SAML authentication for a domain.</p>"}, "Idp": {"shape": "SAMLIdp", "documentation": "<p>The SAML Identity Provider's information.</p>"}, "MasterUserName": {"shape": "Username", "documentation": "<p>The SAML master user name, which is stored in the domain's internal user database.</p>"}, "MasterBackendRole": {"shape": "BackendRole", "documentation": "<p>The backend role that the SAML master user is mapped to.</p>"}, "SubjectKey": {"shape": "String", "documentation": "<p>Element of the SAML assertion to use for the user name. Default is <code>NameID</code>.</p>"}, "RolesKey": {"shape": "String", "documentation": "<p>Element of the SAML assertion to use for backend roles. Default is <code>roles</code>.</p>"}, "SessionTimeoutMinutes": {"shape": "IntegerClass", "documentation": "<p>The duration, in minutes, after which a user session becomes inactive. Acceptable values are between 1 and 1440, and the default value is 60.</p>"}}, "documentation": "<p>The SAML authentication configuration for an Amazon OpenSearch Service domain.</p>"}, "SAMLOptionsOutput": {"type": "structure", "members": {"Enabled": {"shape": "Boolean", "documentation": "<p>True if SAML is enabled.</p>"}, "Idp": {"shape": "SAMLIdp", "documentation": "<p>Describes the SAML identity provider's information.</p>"}, "SubjectKey": {"shape": "String", "documentation": "<p>The key used for matching the SAML subject attribute.</p>"}, "RolesKey": {"shape": "String", "documentation": "<p>The key used for matching the SAML roles attribute.</p>"}, "SessionTimeoutMinutes": {"shape": "IntegerClass", "documentation": "<p>The duration, in minutes, after which a user session becomes inactive.</p>"}}, "documentation": "<p>Describes the SAML application configured for the domain.</p>"}, "ScheduleAt": {"type": "string", "enum": ["NOW", "TIMESTAMP", "OFF_PEAK_WINDOW"]}, "ScheduledAction": {"type": "structure", "required": ["Id", "Type", "Severity", "ScheduledTime"], "members": {"Id": {"shape": "String", "documentation": "<p>The unique identifier of the scheduled action.</p>"}, "Type": {"shape": "ActionType", "documentation": "<p>The type of action that will be taken on the domain.</p>"}, "Severity": {"shape": "ActionSeverity", "documentation": "<p>The severity of the action.</p>"}, "ScheduledTime": {"shape": "<PERSON>", "documentation": "<p>The time when the change is scheduled to happen.</p>"}, "Description": {"shape": "String", "documentation": "<p>A description of the action to be taken.</p>"}, "ScheduledBy": {"shape": "ScheduledBy", "documentation": "<p>Whether the action was scheduled manually (<code>CUSTOMER</code>, or by OpenSearch Service automatically (<code>SYSTEM</code>).</p>"}, "Status": {"shape": "ActionStatus", "documentation": "<p>The current status of the scheduled action.</p>"}, "Mandatory": {"shape": "Boolean", "documentation": "<p>Whether the action is required or optional.</p>"}, "Cancellable": {"shape": "Boolean", "documentation": "<p>Whether or not the scheduled action is cancellable.</p>"}}, "documentation": "<p>Information about a scheduled configuration change for an OpenSearch Service domain. This actions can be a <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/service-software.html\">service software update</a> or a <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/auto-tune.html#auto-tune-types\">blue/green Auto-Tune enhancement</a>.</p>"}, "ScheduledActionsList": {"type": "list", "member": {"shape": "ScheduledAction"}}, "ScheduledAutoTuneActionType": {"type": "string", "documentation": "<p>The Auto-Tune action type.</p>", "enum": ["JVM_HEAP_SIZE_TUNING", "JVM_YOUNG_GEN_TUNING"]}, "ScheduledAutoTuneDescription": {"type": "string", "documentation": "<p>The description of an Auto-Tune maintenance action that occurs on a domain.</p>"}, "ScheduledAutoTuneDetails": {"type": "structure", "members": {"Date": {"shape": "AutoTuneDate", "documentation": "<p>The date and time when the Auto-Tune action is scheduled for the domain.</p>"}, "ActionType": {"shape": "ScheduledAutoTuneActionType", "documentation": "<p>The type of Auto-Tune action.</p>"}, "Action": {"shape": "ScheduledAutoTuneDescription", "documentation": "<p>A description of the Auto-Tune action.</p>"}, "Severity": {"shape": "ScheduledAutoTuneSeverityType", "documentation": "<p>The severity of the Auto-Tune action. Valid values are <code>LOW</code>, <code>MEDIUM</code>, and <code>HIGH</code>.</p>"}}, "documentation": "<p>Specifies details about a scheduled Auto-Tune action. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/auto-tune.html\">Auto-Tune for Amazon OpenSearch Service</a>.</p>"}, "ScheduledAutoTuneSeverityType": {"type": "string", "documentation": "<p>The Auto-Tune action severity.</p>", "enum": ["LOW", "MEDIUM", "HIGH"]}, "ScheduledBy": {"type": "string", "enum": ["CUSTOMER", "SYSTEM"]}, "ServiceSoftwareOptions": {"type": "structure", "members": {"CurrentVersion": {"shape": "String", "documentation": "<p>The current service software version present on the domain.</p>"}, "NewVersion": {"shape": "String", "documentation": "<p>The new service software version, if one is available.</p>"}, "UpdateAvailable": {"shape": "Boolean", "documentation": "<p>True if you're able to update your service software version. False if you can't update your service software version.</p>"}, "Cancellable": {"shape": "Boolean", "documentation": "<p> True if you're able to cancel your service software version update. False if you can't cancel your service software update.</p>"}, "UpdateStatus": {"shape": "DeploymentStatus", "documentation": "<p>The status of your service software update.</p>"}, "Description": {"shape": "String", "documentation": "<p>A description of the service software update status.</p>"}, "AutomatedUpdateDate": {"shape": "DeploymentCloseDateTimeStamp", "documentation": "<p>The timestamp, in Epoch time, until which you can manually request a service software update. After this date, we automatically update your service software.</p>"}, "OptionalDeployment": {"shape": "Boolean", "documentation": "<p>True if a service software is never automatically updated. False if a service software is automatically updated after the automated update date.</p>"}}, "documentation": "<p>The current status of the service software for an Amazon OpenSearch Service domain. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/service-software.html\">Service software updates in Amazon OpenSearch Service</a>.</p>"}, "ServiceUrl": {"type": "string", "documentation": "<p>The domain endpoint to which index and search requests are submitted. For example, <code>search-imdb-movies-oopcnjfn6ugo.eu-west-1.es.amazonaws.com</code> or <code>doc-imdb-movies-oopcnjfn6u.eu-west-1.es.amazonaws.com</code>.</p>"}, "SkipUnavailableStatus": {"type": "string", "documentation": "<p>The status of <code>SkipUnavailable</code> setting for the outbound connection.</p> <ul> <li> <p> <b>ENABLED</b> - The <code>SkipUnavailable</code> setting is enabled for the connection.</p> </li> <li> <p> <b>DISABLED</b> - The <code>SkipUnavailable</code> setting is disabled for the connection.</p> </li> </ul>", "enum": ["ENABLED", "DISABLED"]}, "SlotList": {"type": "list", "member": {"shape": "<PERSON>"}}, "SlotNotAvailableException": {"type": "structure", "members": {"SlotSuggestions": {"shape": "SlotList", "documentation": "<p>Alternate time slots during which OpenSearch Service has available capacity to schedule a domain action.</p>"}}, "documentation": "<p>An exception for attempting to schedule a domain action during an unavailable time slot.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "SnapshotOptions": {"type": "structure", "members": {"AutomatedSnapshotStartHour": {"shape": "IntegerClass", "documentation": "<p>The time, in UTC format, when OpenSearch Service takes a daily automated snapshot of the specified domain. Default is <code>0</code> hours.</p>"}}, "documentation": "<p>The time, in UTC format, when OpenSearch Service takes a daily automated snapshot of the specified domain. Default is <code>0</code> hours.</p>"}, "SnapshotOptionsStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "SnapshotOptions", "documentation": "<p>The daily snapshot options specified for the domain.</p>"}, "Status": {"shape": "OptionStatus", "documentation": "<p>The status of a daily automated snapshot.</p>"}}, "documentation": "<p>Container for information about a daily automated snapshot for an OpenSearch Service domain.</p>"}, "SoftwareUpdateOptions": {"type": "structure", "members": {"AutoSoftwareUpdateEnabled": {"shape": "Boolean", "documentation": "<p>Whether automatic service software updates are enabled for the domain.</p>"}}, "documentation": "<p>Options for configuring service software updates for a domain.</p>"}, "SoftwareUpdateOptionsStatus": {"type": "structure", "members": {"Options": {"shape": "SoftwareUpdateOptions", "documentation": "<p>The service software update options for a domain.</p>"}, "Status": {"shape": "OptionStatus", "documentation": "<p>The status of service software update options, including creation date and last updated date.</p>"}}, "documentation": "<p>The status of the service software options for a domain.</p>"}, "StartAt": {"type": "timestamp"}, "StartDomainMaintenanceRequest": {"type": "structure", "required": ["DomainName", "Action"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>The name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "Action": {"shape": "MaintenanceType", "documentation": "<p>The name of the action.</p>"}, "NodeId": {"shape": "NodeId", "documentation": "<p>The ID of the data node.</p>"}}, "documentation": "<p>Container for the parameters to the <code>StartDomainMaintenance</code> operation.</p>"}, "StartDomainMaintenanceResponse": {"type": "structure", "members": {"MaintenanceId": {"shape": "RequestId", "documentation": "<p>The request ID of requested action.</p>"}}, "documentation": "<p>The result of a <code>StartDomainMaintenance</code> request that information about the requested action. </p>"}, "StartServiceSoftwareUpdateRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>The name of the domain that you want to update to the latest service software.</p>"}, "ScheduleAt": {"shape": "ScheduleAt", "documentation": "<p>When to start the service software update.</p> <ul> <li> <p> <code>NOW</code> - Immediately schedules the update to happen in the current hour if there's capacity available.</p> </li> <li> <p> <code>TIMESTAMP</code> - Lets you specify a custom date and time to apply the update. If you specify this value, you must also provide a value for <code>DesiredStartTime</code>.</p> </li> <li> <p> <code>OFF_PEAK_WINDOW</code> - Marks the update to be picked up during an upcoming off-peak window. There's no guarantee that the update will happen during the next immediate window. Depending on capacity, it might happen in subsequent days.</p> </li> </ul> <p>Default: <code>NOW</code> if you don't specify a value for <code>DesiredStartTime</code>, and <code>TIMESTAMP</code> if you do.</p>"}, "DesiredStartTime": {"shape": "<PERSON>", "documentation": "<p>The Epoch timestamp when you want the service software update to start. You only need to specify this parameter if you set <code>ScheduleAt</code> to <code>TIMESTAMP</code>.</p>"}}, "documentation": "<p>Container for the request parameters to the <code>StartServiceSoftwareUpdate</code> operation.</p>"}, "StartServiceSoftwareUpdateResponse": {"type": "structure", "members": {"ServiceSoftwareOptions": {"shape": "ServiceSoftwareOptions", "documentation": "<p>The current status of the OpenSearch Service software update.</p>"}}, "documentation": "<p>Represents the output of a <code>StartServiceSoftwareUpdate</code> operation. Contains the status of the update.</p>"}, "StartTimeHours": {"type": "long", "max": 23, "min": 0}, "StartTimeMinutes": {"type": "long", "max": 59, "min": 0}, "StartTimestamp": {"type": "timestamp"}, "StorageSubTypeName": {"type": "string", "documentation": "<p> Sub-type of the given EBS storage type. List of available sub-storage options. The <code>instance</code> storage type has no storage sub-type. The <code>ebs</code> storage type has the following valid sub-types: </p> <ul> <li> <p> <code>standard</code> </p> </li> <li> <p> <code>gp2</code> </p> </li> <li> <p> <code>gp3</code> </p> </li> <li> <p> <code>io1</code> </p> </li> </ul>"}, "StorageType": {"type": "structure", "members": {"StorageTypeName": {"shape": "StorageTypeName", "documentation": "<p>The name of the storage type.</p>"}, "StorageSubTypeName": {"shape": "StorageSubTypeName", "documentation": "<p>The storage sub-type, such as <code>gp3</code> or <code>io1</code>.</p>"}, "StorageTypeLimits": {"shape": "StorageTypeLimitList", "documentation": "<p>Limits that are applicable for the given storage type.</p>"}}, "documentation": "<p>A list of storage types for an Amazon OpenSearch Service domain that are available for a given intance type.</p>"}, "StorageTypeLimit": {"type": "structure", "members": {"LimitName": {"shape": "LimitName", "documentation": "<p> Name of storage limits that are applicable for the given storage type. If <code>StorageType</code> is <code>ebs</code>, the following options are available:</p> <ul> <li> <p> <b>MinimumVolumeSize</b> - Minimum volume size that is available for the given storage type. Can be empty if not applicable.</p> </li> <li> <p> <b>MaximumVolumeSize</b> - Maximum volume size that is available for the given storage type. Can be empty if not applicable.</p> </li> <li> <p> <b>MaximumIops</b> - Maximum amount of IOPS that is available for the given the storage type. Can be empty if not applicable.</p> </li> <li> <p> <b>MinimumIops</b> - Minimum amount of IOPS that is available for the given the storage type. Can be empty if not applicable.</p> </li> <li> <p> <b>MaximumThroughput</b> - Maximum amount of throughput that is available for the given the storage type. Can be empty if not applicable.</p> </li> <li> <p> <b>MinimumThroughput</b> - Minimum amount of throughput that is available for the given the storage type. Can be empty if not applicable.</p> </li> </ul>"}, "LimitValues": {"shape": "LimitValueList", "documentation": "<p>The limit values.</p>"}}, "documentation": "<p>Limits that are applicable for the given Amazon OpenSearch Service storage type.</p>"}, "StorageTypeLimitList": {"type": "list", "member": {"shape": "StorageTypeLimit"}}, "StorageTypeList": {"type": "list", "member": {"shape": "StorageType"}}, "StorageTypeName": {"type": "string", "documentation": "<p>The type of storage that the domain uses. Can be one of the following:</p> <dl> <dt>instance</dt> <dd> <p>Built-in storage available for the instance.</p> </dd> <dt>ebs</dt> <dd> <p>Elastic Block Storage (EBS) attached to the instance.</p> </dd> </dl>"}, "String": {"type": "string"}, "StringList": {"type": "list", "member": {"shape": "String"}}, "TLSSecurityPolicy": {"type": "string", "enum": ["Policy-Min-TLS-1-0-2019-07", "Policy-Min-TLS-1-2-2019-07"]}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>The tag key. Tag keys must be unique for the domain to which they are attached.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The value assigned to the corresponding tag key. Tag values can be null and don't have to be unique in a tag set. For example, you can have a key value pair in a tag set of <code>project : Trinity</code> and <code>cost-center : Trinity</code> </p>"}}, "documentation": "<p>A tag (key-value pair) for an Amazon OpenSearch Service resource.</p>"}, "TagKey": {"type": "string", "documentation": "<p>A string between 1 to 128 characters that specifies the key for a tag. Tag keys must be unique for the domain to which they're attached.</p>", "max": 128, "min": 1, "pattern": ".*"}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "documentation": "<p>A list of tags attached to a domain.</p>"}, "TagValue": {"type": "string", "documentation": "<p>A string between 0 to 256 characters that specifies the value for a tag. Tag values can be null and don't have to be unique in a tag set.</p>", "max": 256, "min": 0, "pattern": ".*"}, "TimeUnit": {"type": "string", "documentation": "<p>The unit of a maintenance schedule duration. Valid value is <code>HOUR</code>.</p>", "enum": ["HOURS"]}, "TotalNumberOfStages": {"type": "integer"}, "UIntValue": {"type": "integer", "min": 0}, "UncompressedPluginSizeInBytes": {"type": "long"}, "UpdateDomainConfigRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>The name of the domain that you're updating.</p>", "location": "uri", "locationName": "DomainName"}, "ClusterConfig": {"shape": "ClusterConfig", "documentation": "<p>Changes that you want to make to the cluster configuration, such as the instance type and number of EC2 instances.</p>"}, "EBSOptions": {"shape": "EBSOptions", "documentation": "<p>The type and size of the EBS volume to attach to instances in the domain.</p>"}, "SnapshotOptions": {"shape": "SnapshotOptions", "documentation": "<p>Option to set the time, in UTC format, for the daily automated snapshot. Default value is <code>0</code> hours. </p>"}, "VPCOptions": {"shape": "VPCOptions", "documentation": "<p>Options to specify the subnets and security groups for a VPC endpoint. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/vpc.html\">Launching your Amazon OpenSearch Service domains using a VPC</a>.</p>"}, "CognitoOptions": {"shape": "CognitoOptions", "documentation": "<p>Key-value pairs to configure Amazon Cognito authentication for OpenSearch Dashboards.</p>"}, "AdvancedOptions": {"shape": "AdvancedOptions", "documentation": "<p>Key-value pairs to specify advanced configuration options. The following key-value pairs are supported:</p> <ul> <li> <p> <code>\"rest.action.multi.allow_explicit_index\": \"true\" | \"false\"</code> - Note the use of a string rather than a boolean. Specifies whether explicit references to indexes are allowed inside the body of HTTP requests. If you want to configure access policies for domain sub-resources, such as specific indexes and domain APIs, you must disable this property. Default is true.</p> </li> <li> <p> <code>\"indices.fielddata.cache.size\": \"80\" </code> - Note the use of a string rather than a boolean. Specifies the percentage of heap space allocated to field data. Default is unbounded.</p> </li> <li> <p> <code>\"indices.query.bool.max_clause_count\": \"1024\"</code> - Note the use of a string rather than a boolean. Specifies the maximum number of clauses allowed in a Lucene boolean query. Default is 1,024. Queries with more than the permitted number of clauses result in a <code>TooManyClauses</code> error.</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/createupdatedomains.html#createdomain-configure-advanced-options\">Advanced cluster parameters</a>.</p>"}, "AccessPolicies": {"shape": "PolicyDocument", "documentation": "<p>Identity and Access Management (IAM) access policy as a JSON-formatted string.</p>"}, "IPAddressType": {"shape": "IPAddressType", "documentation": "<p>The type of IP addresses supported by the endpoint for the domain.</p>"}, "LogPublishingOptions": {"shape": "LogPublishingOptions", "documentation": "<p>Options to publish OpenSearch logs to Amazon CloudWatch Logs.</p>"}, "EncryptionAtRestOptions": {"shape": "EncryptionAtRestOptions", "documentation": "<p>Encryption at rest options for the domain.</p>"}, "DomainEndpointOptions": {"shape": "DomainEndpointOptions", "documentation": "<p>Additional options for the domain endpoint, such as whether to require HTTPS for all traffic.</p>"}, "NodeToNodeEncryptionOptions": {"shape": "NodeToNodeEncryptionOptions", "documentation": "<p>Node-to-node encryption options for the domain.</p>"}, "AdvancedSecurityOptions": {"shape": "AdvancedSecurityOptionsInput", "documentation": "<p>Options for fine-grained access control.</p>"}, "AutoTuneOptions": {"shape": "AutoTuneOptions", "documentation": "<p>Options for Auto-Tune.</p>"}, "DryRun": {"shape": "DryRun", "documentation": "<p>This flag, when set to True, specifies whether the <code>UpdateDomain</code> request should return the results of a dry run analysis without actually applying the change. A dry run determines what type of deployment the update will cause.</p>"}, "DryRunMode": {"shape": "DryRunMode", "documentation": "<p>The type of dry run to perform.</p> <ul> <li> <p> <code>Basic</code> only returns the type of deployment (blue/green or dynamic) that the update will cause.</p> </li> <li> <p> <code>Ver<PERSON>e</code> runs an additional check to validate the changes you're making. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/managedomains-configuration-changes#validation-check\">Validating a domain update</a>.</p> </li> </ul>"}, "OffPeakWindowOptions": {"shape": "OffPeakWindowOptions", "documentation": "<p>Off-peak window options for the domain.</p>"}, "SoftwareUpdateOptions": {"shape": "SoftwareUpdateOptions", "documentation": "<p>Service software update options for the domain.</p>"}}, "documentation": "<p>Container for the request parameters to the <code>UpdateDomain</code> operation.</p>"}, "UpdateDomainConfigResponse": {"type": "structure", "required": ["DomainConfig"], "members": {"DomainConfig": {"shape": "DomainConfig", "documentation": "<p>The status of the updated domain.</p>"}, "DryRunResults": {"shape": "DryRunResults", "documentation": "<p>Results of the dry run performed in the update domain request.</p>"}, "DryRunProgressStatus": {"shape": "DryRunProgressStatus", "documentation": "<p>The status of the dry run being performed on the domain, if any.</p>"}}, "documentation": "<p>The results of an <code>UpdateDomain</code> request. Contains the status of the domain being updated.</p>"}, "UpdatePackageRequest": {"type": "structure", "required": ["PackageID", "PackageSource"], "members": {"PackageID": {"shape": "PackageID", "documentation": "<p>The unique identifier for the package.</p>"}, "PackageSource": {"shape": "PackageSource", "documentation": "<p>Amazon S3 bucket and key for the package.</p>"}, "PackageDescription": {"shape": "PackageDescription", "documentation": "<p>A new description of the package.</p>"}, "CommitMessage": {"shape": "CommitMessage", "documentation": "<p>Commit message for the updated file, which is shown as part of <code>GetPackageVersionHistoryResponse</code>.</p>"}}, "documentation": "<p>Container for request parameters to the <code>UpdatePackage</code> operation.</p>"}, "UpdatePackageResponse": {"type": "structure", "members": {"PackageDetails": {"shape": "PackageDetails", "documentation": "<p>Information about a package.</p>"}}, "documentation": "<p>Container for the response returned by the <code>UpdatePackage</code> operation.</p>"}, "UpdateScheduledActionRequest": {"type": "structure", "required": ["DomainName", "ActionID", "ActionType", "ScheduleAt"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>The name of the domain to reschedule an action for.</p>", "location": "uri", "locationName": "DomainName"}, "ActionID": {"shape": "String", "documentation": "<p>The unique identifier of the action to reschedule. To retrieve this ID, send a <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/APIReference/API_ListScheduledActions.html\">ListScheduledActions</a> request.</p>"}, "ActionType": {"shape": "ActionType", "documentation": "<p>The type of action to reschedule. Can be one of <code>SERVICE_SOFTWARE_UPDATE</code>, <code>JVM_HEAP_SIZE_TUNING</code>, or <code>JVM_YOUNG_GEN_TUNING</code>. To retrieve this value, send a <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/APIReference/API_ListScheduledActions.html\">ListScheduledActions</a> request.</p>"}, "ScheduleAt": {"shape": "ScheduleAt", "documentation": "<p>When to schedule the action.</p> <ul> <li> <p> <code>NOW</code> - Immediately schedules the update to happen in the current hour if there's capacity available.</p> </li> <li> <p> <code>TIMESTAMP</code> - Lets you specify a custom date and time to apply the update. If you specify this value, you must also provide a value for <code>DesiredStartTime</code>.</p> </li> <li> <p> <code>OFF_PEAK_WINDOW</code> - Marks the action to be picked up during an upcoming off-peak window. There's no guarantee that the change will be implemented during the next immediate window. Depending on capacity, it might happen in subsequent days.</p> </li> </ul>"}, "DesiredStartTime": {"shape": "<PERSON>", "documentation": "<p>The time to implement the change, in Coordinated Universal Time (UTC). Only specify this parameter if you set <code>ScheduleAt</code> to <code>TIMESTAMP</code>.</p>"}}}, "UpdateScheduledActionResponse": {"type": "structure", "members": {"ScheduledAction": {"shape": "ScheduledAction", "documentation": "<p>Information about the rescheduled action.</p>"}}}, "UpdateTimestamp": {"type": "timestamp"}, "UpdateVpcEndpointRequest": {"type": "structure", "required": ["VpcEndpointId", "VpcOptions"], "members": {"VpcEndpointId": {"shape": "VpcEndpointId", "documentation": "<p>The unique identifier of the endpoint.</p>"}, "VpcOptions": {"shape": "VPCOptions", "documentation": "<p>The security groups and/or subnets to add, remove, or modify.</p>"}}}, "UpdateVpcEndpointResponse": {"type": "structure", "required": ["VpcEndpoint"], "members": {"VpcEndpoint": {"shape": "VpcEndpoint", "documentation": "<p>The endpoint to be updated.</p>"}}}, "UpgradeDomainRequest": {"type": "structure", "required": ["DomainName", "TargetVersion"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>Name of the OpenSearch Service domain that you want to upgrade.</p>"}, "TargetVersion": {"shape": "VersionString", "documentation": "<p>OpenSearch or Elasticsearch version to which you want to upgrade, in the format Opensearch_X.Y or Elasticsearch_X.Y.</p>"}, "PerformCheckOnly": {"shape": "Boolean", "documentation": "<p>When true, indicates that an upgrade eligibility check needs to be performed. Does not actually perform the upgrade.</p>"}, "AdvancedOptions": {"shape": "AdvancedOptions", "documentation": "<p>Only supports the <code>override_main_response_version</code> parameter and not other advanced options. You can only include this option when upgrading to an OpenSearch version. Specifies whether the domain reports its version as 7.10 so that it continues to work with Elasticsearch OSS clients and plugins.</p>"}}, "documentation": "<p>Container for the request parameters to the <code>UpgradeDomain</code> operation.</p>"}, "UpgradeDomainResponse": {"type": "structure", "members": {"UpgradeId": {"shape": "String", "documentation": "<p>The unique identifier of the domain upgrade.</p>"}, "DomainName": {"shape": "DomainName", "documentation": "<p>The name of the domain that was upgraded.</p>"}, "TargetVersion": {"shape": "VersionString", "documentation": "<p>OpenSearch or Elasticsearch version that the domain was upgraded to.</p>"}, "PerformCheckOnly": {"shape": "Boolean", "documentation": "<p>When true, indicates that an upgrade eligibility check was performed.</p>"}, "AdvancedOptions": {"shape": "AdvancedOptions", "documentation": "<p>The advanced options configuration for the domain.</p>"}, "ChangeProgressDetails": {"shape": "ChangeProgressDetails", "documentation": "<p>Container for information about a configuration change happening on a domain.</p>"}}, "documentation": "<p>Container for the response returned by <code>UpgradeDomain</code> operation.</p>"}, "UpgradeHistory": {"type": "structure", "members": {"UpgradeName": {"shape": "UpgradeName", "documentation": "<p>A string that describes the upgrade.</p>"}, "StartTimestamp": {"shape": "StartTimestamp", "documentation": "<p>UTC timestamp at which the upgrade API call was made, in the format <code>yyyy-MM-ddTHH:mm:ssZ</code>.</p>"}, "UpgradeStatus": {"shape": "UpgradeStatus", "documentation": "<p> The current status of the upgrade. The status can take one of the following values: </p> <ul> <li> <p>In Progress</p> </li> <li> <p>Succeeded</p> </li> <li> <p>Succeeded with Issues</p> </li> <li> <p>Failed</p> </li> </ul>"}, "StepsList": {"shape": "UpgradeStepsList", "documentation": "<p>A list of each step performed as part of a specific upgrade or upgrade eligibility check.</p>"}}, "documentation": "<p>History of the last 10 upgrades and upgrade eligibility checks for an Amazon OpenSearch Service domain.</p>"}, "UpgradeHistoryList": {"type": "list", "member": {"shape": "UpgradeHistory"}}, "UpgradeName": {"type": "string"}, "UpgradeStatus": {"type": "string", "enum": ["IN_PROGRESS", "SUCCEEDED", "SUCCEEDED_WITH_ISSUES", "FAILED"]}, "UpgradeStep": {"type": "string", "enum": ["PRE_UPGRADE_CHECK", "SNAPSHOT", "UPGRADE"]}, "UpgradeStepItem": {"type": "structure", "members": {"UpgradeStep": {"shape": "UpgradeStep", "documentation": "<p> One of three steps that an upgrade or upgrade eligibility check goes through: </p> <ul> <li> <p>PreUpgradeCheck</p> </li> <li> <p>Snapshot</p> </li> <li> <p>Upgrade</p> </li> </ul>"}, "UpgradeStepStatus": {"shape": "UpgradeStatus", "documentation": "<p> The current status of the upgrade. The status can take one of the following values: </p> <ul> <li> <p>In Progress</p> </li> <li> <p>Succeeded</p> </li> <li> <p>Succeeded with Issues</p> </li> <li> <p>Failed</p> </li> </ul>"}, "Issues": {"shape": "Issues", "documentation": "<p>A list of strings containing detailed information about the errors encountered in a particular step.</p>"}, "ProgressPercent": {"shape": "Double", "documentation": "<p>The floating point value representing the progress percentage of a particular step.</p>"}}, "documentation": "<p>Represents a single step of an upgrade or upgrade eligibility check workflow.</p>"}, "UpgradeStepsList": {"type": "list", "member": {"shape": "UpgradeStepItem"}}, "UserPoolId": {"type": "string", "max": 55, "min": 1, "pattern": "[\\w-]+_[0-9a-zA-Z]+"}, "Username": {"type": "string", "max": 64, "min": 1, "pattern": ".*", "sensitive": true}, "VPCDerivedInfo": {"type": "structure", "members": {"VPCId": {"shape": "String", "documentation": "<p>The ID for your VPC. Amazon VPC generates this value when you create a VPC.</p>"}, "SubnetIds": {"shape": "StringList", "documentation": "<p>A list of subnet IDs associated with the VPC endpoints for the domain.</p>"}, "AvailabilityZones": {"shape": "StringList", "documentation": "<p>The list of Availability Zones associated with the VPC subnets.</p>"}, "SecurityGroupIds": {"shape": "StringList", "documentation": "<p>The list of security group IDs associated with the VPC endpoints for the domain.</p>"}}, "documentation": "<p>Information about the subnets and security groups for an Amazon OpenSearch Service domain provisioned within a virtual private cloud (VPC). For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/vpc.html\">Launching your Amazon OpenSearch Service domains using a VPC</a>. This information only exists if the domain was created with <code>VPCOptions</code>.</p>"}, "VPCDerivedInfoStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "VPCDerivedInfo", "documentation": "<p>The VPC options for the specified domain.</p>"}, "Status": {"shape": "OptionStatus", "documentation": "<p>The status of the VPC options for the specified domain.</p>"}}, "documentation": "<p>Status of the VPC options for a specified domain.</p>"}, "VPCOptions": {"type": "structure", "members": {"SubnetIds": {"shape": "StringList", "documentation": "<p>A list of subnet IDs associated with the VPC endpoints for the domain. If your domain uses multiple Availability Zones, you need to provide two subnet IDs, one per zone. Otherwise, provide only one.</p>"}, "SecurityGroupIds": {"shape": "StringList", "documentation": "<p>The list of security group IDs associated with the VPC endpoints for the domain. If you do not provide a security group ID, OpenSearch Service uses the default security group for the VPC.</p>"}}, "documentation": "<p>Options to specify the subnets and security groups for an Amazon OpenSearch Service VPC endpoint. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/vpc.html\">Launching your Amazon OpenSearch Service domains using a VPC</a>.</p>"}, "ValidationException": {"type": "structure", "members": {}, "documentation": "<p>An exception for accessing or deleting a resource that doesn't exist.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ValidationFailure": {"type": "structure", "members": {"Code": {"shape": "String", "documentation": "<p>The error code of the failure.</p>"}, "Message": {"shape": "String", "documentation": "<p>A message corresponding to the failure.</p>"}}, "documentation": "<p>A validation failure that occurred as the result of a pre-update validation check (verbose dry run) on a domain.</p>"}, "ValidationFailures": {"type": "list", "member": {"shape": "ValidationFailure"}}, "ValueStringList": {"type": "list", "member": {"shape": "NonEmptyString"}, "min": 1}, "VersionList": {"type": "list", "member": {"shape": "VersionString"}, "documentation": "<p>List of supported OpenSearch versions.</p>"}, "VersionStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "VersionString", "documentation": "<p>The OpenSearch or Elasticsearch version for the specified domain.</p>"}, "Status": {"shape": "OptionStatus", "documentation": "<p>The status of the version options for the specified domain.</p>"}}, "documentation": "<p>The status of the the OpenSearch or Elasticsearch version options for the specified Amazon OpenSearch Service domain.</p>"}, "VersionString": {"type": "string", "max": 18, "min": 14, "pattern": "^Elasticsearch_[0-9]{1}\\.[0-9]{1,2}$|^OpenSearch_[0-9]{1,2}\\.[0-9]{1,2}$"}, "VolumeSize": {"type": "string"}, "VolumeType": {"type": "string", "documentation": "<p>The type of EBS volume that a domain uses. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/opensearch-createupdatedomains.html#opensearch-createdomain-configure-ebs\">Configuring EBS-based storage</a>.</p>", "enum": ["standard", "gp2", "io1", "gp3"]}, "VpcEndpoint": {"type": "structure", "members": {"VpcEndpointId": {"shape": "VpcEndpointId", "documentation": "<p>The unique identifier of the endpoint.</p>"}, "VpcEndpointOwner": {"shape": "AWSAccount", "documentation": "<p>The creator of the endpoint.</p>"}, "DomainArn": {"shape": "DomainArn", "documentation": "<p>The Amazon Resource Name (ARN) of the domain associated with the endpoint.</p>"}, "VpcOptions": {"shape": "VPCDerivedInfo", "documentation": "<p>Options to specify the subnets and security groups for an Amazon OpenSearch Service VPC endpoint.</p>"}, "Status": {"shape": "VpcEndpointStatus", "documentation": "<p>The current status of the endpoint.</p>"}, "Endpoint": {"shape": "Endpoint", "documentation": "<p>The connection endpoint ID for connecting to the domain.</p>"}}, "documentation": "<p>The connection endpoint for connecting to an Amazon OpenSearch Service domain through a proxy.</p>"}, "VpcEndpointError": {"type": "structure", "members": {"VpcEndpointId": {"shape": "VpcEndpointId", "documentation": "<p>The unique identifier of the endpoint.</p>"}, "ErrorCode": {"shape": "VpcEndpointErrorCode", "documentation": "<p>The code associated with the error.</p>"}, "ErrorMessage": {"shape": "String", "documentation": "<p>A message describing the error.</p>"}}, "documentation": "<p>Error information when attempting to describe an Amazon OpenSearch Service-managed VPC endpoint.</p>"}, "VpcEndpointErrorCode": {"type": "string", "enum": ["ENDPOINT_NOT_FOUND", "SERVER_ERROR"]}, "VpcEndpointErrorList": {"type": "list", "member": {"shape": "VpcEndpointError"}}, "VpcEndpointId": {"type": "string", "max": 256, "min": 5, "pattern": "^aos-[a-zA-Z0-9]*$"}, "VpcEndpointIdList": {"type": "list", "member": {"shape": "VpcEndpointId"}}, "VpcEndpointStatus": {"type": "string", "enum": ["CREATING", "CREATE_FAILED", "ACTIVE", "UPDATING", "UPDATE_FAILED", "DELETING", "DELETE_FAILED"]}, "VpcEndpointSummary": {"type": "structure", "members": {"VpcEndpointId": {"shape": "VpcEndpointId", "documentation": "<p>The unique identifier of the endpoint.</p>"}, "VpcEndpointOwner": {"shape": "String", "documentation": "<p>The creator of the endpoint.</p>"}, "DomainArn": {"shape": "DomainArn", "documentation": "<p>The Amazon Resource Name (ARN) of the domain associated with the endpoint.</p>"}, "Status": {"shape": "VpcEndpointStatus", "documentation": "<p>The current status of the endpoint.</p>"}}, "documentation": "<p>Summary information for an Amazon OpenSearch Service-managed VPC endpoint.</p>"}, "VpcEndpointSummaryList": {"type": "list", "member": {"shape": "VpcEndpointSummary"}}, "VpcEndpoints": {"type": "list", "member": {"shape": "VpcEndpoint"}}, "WindowStartTime": {"type": "structure", "required": ["Hours", "Minutes"], "members": {"Hours": {"shape": "StartTimeHours", "documentation": "<p>The start hour of the window in Coordinated Universal Time (UTC), using 24-hour time. For example, <code>17</code> refers to 5:00 P.M. UTC.</p>"}, "Minutes": {"shape": "StartTimeMinutes", "documentation": "<p>The start minute of the window, in UTC.</p>"}}, "documentation": "<p>The desired start time for an <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/APIReference/API_OffPeakWindow.html\">off-peak maintenance window</a>.</p>"}, "ZoneAwarenessConfig": {"type": "structure", "members": {"AvailabilityZoneCount": {"shape": "IntegerClass", "documentation": "<p>If you enabled multiple Availability Zones, this value is the number of zones that you want the domain to use. Valid values are <code>2</code> and <code>3</code>. If your domain is provisioned within a VPC, this value be equal to number of subnets.</p>"}}, "documentation": "<p>The zone awareness configuration for an Amazon OpenSearch Service domain.</p>"}, "ZoneStatus": {"type": "string", "enum": ["Active", "StandBy", "NotAvailable"]}}, "documentation": "<p>Use the Amazon OpenSearch Service configuration API to create, configure, and manage OpenSearch Service domains.</p> <p>For sample code that uses the configuration API, see the <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/opensearch-configuration-samples.html\"> <i>Amazon OpenSearch Service Developer Guide</i> </a>. The guide also contains <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/request-signing.html\">sample code</a> for sending signed HTTP requests to the OpenSearch APIs. The endpoint for configuration service requests is Region specific: es.<i>region</i>.amazonaws.com. For example, es.us-east-1.amazonaws.com. For a current list of supported Regions and endpoints, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/rande.html#service-regions\">Amazon Web Services service endpoints</a>.</p>"}