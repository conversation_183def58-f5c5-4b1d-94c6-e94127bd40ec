{"version": "2.0", "metadata": {"apiVersion": "2020-08-01", "endpointPrefix": "nimble", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "AmazonNimbleStudio", "serviceId": "nimble", "signatureVersion": "v4", "signingName": "nimble", "uid": "nimble-2020-08-01"}, "operations": {"AcceptEulas": {"name": "AcceptEulas", "http": {"method": "POST", "requestUri": "/2020-08-01/studios/{studioId}/eula-acceptances", "responseCode": 200}, "input": {"shape": "AcceptEulasRequest"}, "output": {"shape": "AcceptEulasResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Accept EULAs.</p>"}, "CreateLaunchProfile": {"name": "CreateLaunchProfile", "http": {"method": "POST", "requestUri": "/2020-08-01/studios/{studioId}/launch-profiles", "responseCode": 200}, "input": {"shape": "CreateLaunchProfileRequest"}, "output": {"shape": "CreateLaunchProfileResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Create a launch profile.</p>"}, "CreateStreamingImage": {"name": "CreateStreamingImage", "http": {"method": "POST", "requestUri": "/2020-08-01/studios/{studioId}/streaming-images", "responseCode": 200}, "input": {"shape": "CreateStreamingImageRequest"}, "output": {"shape": "CreateStreamingImageResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a streaming image resource in a studio.</p>"}, "CreateStreamingSession": {"name": "CreateStreamingSession", "http": {"method": "POST", "requestUri": "/2020-08-01/studios/{studioId}/streaming-sessions", "responseCode": 200}, "input": {"shape": "CreateStreamingSessionRequest"}, "output": {"shape": "CreateStreamingSessionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a streaming session in a studio.</p> <p>After invoking this operation, you must poll GetStreamingSession until the streaming session is in the <code>READY</code> state.</p>"}, "CreateStreamingSessionStream": {"name": "CreateStreamingSessionStream", "http": {"method": "POST", "requestUri": "/2020-08-01/studios/{studioId}/streaming-sessions/{sessionId}/streams", "responseCode": 200}, "input": {"shape": "CreateStreamingSessionStreamRequest"}, "output": {"shape": "CreateStreamingSessionStreamResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a streaming session stream for a streaming session.</p> <p>After invoking this API, invoke GetStreamingSessionStream with the returned streamId to poll the resource until it is in the <code>READY</code> state.</p>", "idempotent": true}, "CreateStudio": {"name": "CreateStudio", "http": {"method": "POST", "requestUri": "/2020-08-01/studios", "responseCode": 200}, "input": {"shape": "CreateStudioRequest"}, "output": {"shape": "CreateStudioResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Create a new studio.</p> <p>When creating a studio, two IAM roles must be provided: the admin role and the user role. These roles are assumed by your users when they log in to the Nimble Studio portal.</p> <p>The user role must have the <code>AmazonNimbleStudio-StudioUser</code> managed policy attached for the portal to function properly.</p> <p>The admin role must have the <code>AmazonNimbleStudio-StudioAdmin</code> managed policy attached for the portal to function properly.</p> <p>You may optionally specify a KMS key in the <code>StudioEncryptionConfiguration</code>.</p> <p>In Nimble Studio, resource names, descriptions, initialization scripts, and other data you provide are always encrypted at rest using an KMS key. By default, this key is owned by Amazon Web Services and managed on your behalf. You may provide your own KMS key when calling <code>CreateStudio</code> to encrypt this data using a key you own and manage.</p> <p>When providing an KMS key during studio creation, Nimble Studio creates KMS grants in your account to provide your studio user and admin roles access to these KMS keys.</p> <p>If you delete this grant, the studio will no longer be accessible to your portal users.</p> <p>If you delete the studio KMS key, your studio will no longer be accessible.</p>"}, "CreateStudioComponent": {"name": "CreateStudioComponent", "http": {"method": "POST", "requestUri": "/2020-08-01/studios/{studioId}/studio-components", "responseCode": 200}, "input": {"shape": "CreateStudioComponentRequest"}, "output": {"shape": "CreateStudioComponentResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a studio component resource.</p>"}, "DeleteLaunchProfile": {"name": "DeleteLaunchProfile", "http": {"method": "DELETE", "requestUri": "/2020-08-01/studios/{studioId}/launch-profiles/{launchProfileId}", "responseCode": 200}, "input": {"shape": "DeleteLaunchProfileRequest"}, "output": {"shape": "DeleteLaunchProfileResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Permanently delete a launch profile.</p>", "idempotent": true}, "DeleteLaunchProfileMember": {"name": "DeleteLaunchProfileMember", "http": {"method": "DELETE", "requestUri": "/2020-08-01/studios/{studioId}/launch-profiles/{launchProfileId}/membership/{principalId}", "responseCode": 200}, "input": {"shape": "DeleteLaunchProfileMemberRequest"}, "output": {"shape": "DeleteLaunchProfileMemberResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Delete a user from launch profile membership.</p>", "idempotent": true}, "DeleteStreamingImage": {"name": "DeleteStreamingImage", "http": {"method": "DELETE", "requestUri": "/2020-08-01/studios/{studioId}/streaming-images/{streamingImageId}", "responseCode": 200}, "input": {"shape": "DeleteStreamingImageRequest"}, "output": {"shape": "DeleteStreamingImageResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Delete streaming image.</p>", "idempotent": true}, "DeleteStreamingSession": {"name": "DeleteStreamingSession", "http": {"method": "DELETE", "requestUri": "/2020-08-01/studios/{studioId}/streaming-sessions/{sessionId}", "responseCode": 200}, "input": {"shape": "DeleteStreamingSessionRequest"}, "output": {"shape": "DeleteStreamingSessionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Deletes streaming session resource.</p> <p>After invoking this operation, use GetStreamingSession to poll the resource until it transitions to a <code>DELETED</code> state.</p> <p>A streaming session will count against your streaming session quota until it is marked <code>DELETED</code>.</p>", "idempotent": true}, "DeleteStudio": {"name": "DeleteStudio", "http": {"method": "DELETE", "requestUri": "/2020-08-01/studios/{studioId}", "responseCode": 200}, "input": {"shape": "DeleteStudioRequest"}, "output": {"shape": "DeleteStudioResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Delete a studio resource.</p>", "idempotent": true}, "DeleteStudioComponent": {"name": "DeleteStudioComponent", "http": {"method": "DELETE", "requestUri": "/2020-08-01/studios/{studioId}/studio-components/{studioComponentId}", "responseCode": 200}, "input": {"shape": "DeleteStudioComponentRequest"}, "output": {"shape": "DeleteStudioComponentResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Deletes a studio component resource.</p>", "idempotent": true}, "DeleteStudioMember": {"name": "DeleteStudioMember", "http": {"method": "DELETE", "requestUri": "/2020-08-01/studios/{studioId}/membership/{principalId}", "responseCode": 200}, "input": {"shape": "DeleteStudioMemberRequest"}, "output": {"shape": "DeleteStudioMemberResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Delete a user from studio membership.</p>", "idempotent": true}, "GetEula": {"name": "GetEula", "http": {"method": "GET", "requestUri": "/2020-08-01/eulas/{eulaId}", "responseCode": 200}, "input": {"shape": "GetEulaRequest"}, "output": {"shape": "GetEulaResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Get EULA.</p>"}, "GetLaunchProfile": {"name": "GetLaunchProfile", "http": {"method": "GET", "requestUri": "/2020-08-01/studios/{studioId}/launch-profiles/{launchProfileId}", "responseCode": 200}, "input": {"shape": "GetLaunchProfileRequest"}, "output": {"shape": "GetLaunchProfileResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Get a launch profile.</p>"}, "GetLaunchProfileDetails": {"name": "GetLaunchProfileDetails", "http": {"method": "GET", "requestUri": "/2020-08-01/studios/{studioId}/launch-profiles/{launchProfileId}/details", "responseCode": 200}, "input": {"shape": "GetLaunchProfileDetailsRequest"}, "output": {"shape": "GetLaunchProfileDetailsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Launch profile details include the launch profile resource and summary information of resources that are used by, or available to, the launch profile. This includes the name and description of all studio components used by the launch profiles, and the name and description of streaming images that can be used with this launch profile.</p>"}, "GetLaunchProfileInitialization": {"name": "GetLaunchProfileInitialization", "http": {"method": "GET", "requestUri": "/2020-08-01/studios/{studioId}/launch-profiles/{launchProfileId}/init", "responseCode": 200}, "input": {"shape": "GetLaunchProfileInitializationRequest"}, "output": {"shape": "GetLaunchProfileInitializationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Get a launch profile initialization.</p>"}, "GetLaunchProfileMember": {"name": "GetLaunchProfileMember", "http": {"method": "GET", "requestUri": "/2020-08-01/studios/{studioId}/launch-profiles/{launchProfileId}/membership/{principalId}", "responseCode": 200}, "input": {"shape": "GetLaunchProfileMemberRequest"}, "output": {"shape": "GetLaunchProfileMemberResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Get a user persona in launch profile membership.</p>"}, "GetStreamingImage": {"name": "GetStreamingImage", "http": {"method": "GET", "requestUri": "/2020-08-01/studios/{studioId}/streaming-images/{streamingImageId}", "responseCode": 200}, "input": {"shape": "GetStreamingImageRequest"}, "output": {"shape": "GetStreamingImageResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Get streaming image.</p>"}, "GetStreamingSession": {"name": "GetStreamingSession", "http": {"method": "GET", "requestUri": "/2020-08-01/studios/{studioId}/streaming-sessions/{sessionId}", "responseCode": 200}, "input": {"shape": "GetStreamingSessionRequest"}, "output": {"shape": "GetStreamingSessionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Gets StreamingSession resource.</p> <p>Invoke this operation to poll for a streaming session state while creating or deleting a session.</p>"}, "GetStreamingSessionBackup": {"name": "GetStreamingSessionBackup", "http": {"method": "GET", "requestUri": "/2020-08-01/studios/{studioId}/streaming-session-backups/{backupId}", "responseCode": 200}, "input": {"shape": "GetStreamingSessionBackupRequest"}, "output": {"shape": "GetStreamingSessionBackupResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Gets <code>StreamingSessionBackup</code> resource.</p> <p>Invoke this operation to poll for a streaming session backup while stopping a streaming session.</p>"}, "GetStreamingSessionStream": {"name": "GetStreamingSessionStream", "http": {"method": "GET", "requestUri": "/2020-08-01/studios/{studioId}/streaming-sessions/{sessionId}/streams/{streamId}", "responseCode": 200}, "input": {"shape": "GetStreamingSessionStreamRequest"}, "output": {"shape": "GetStreamingSessionStreamResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Gets a StreamingSessionStream for a streaming session.</p> <p>Invoke this operation to poll the resource after invoking <code>CreateStreamingSessionStream</code>.</p> <p>After the <code>StreamingSessionStream</code> changes to the <code>READY</code> state, the url property will contain a stream to be used with the DCV streaming client.</p>"}, "GetStudio": {"name": "GetStudio", "http": {"method": "GET", "requestUri": "/2020-08-01/studios/{studioId}", "responseCode": 200}, "input": {"shape": "GetStudioRequest"}, "output": {"shape": "GetStudioResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Get a studio resource.</p>"}, "GetStudioComponent": {"name": "GetStudioComponent", "http": {"method": "GET", "requestUri": "/2020-08-01/studios/{studioId}/studio-components/{studioComponentId}", "responseCode": 200}, "input": {"shape": "GetStudioComponentRequest"}, "output": {"shape": "GetStudioComponentResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Gets a studio component resource.</p>"}, "GetStudioMember": {"name": "GetStudioMember", "http": {"method": "GET", "requestUri": "/2020-08-01/studios/{studioId}/membership/{principalId}", "responseCode": 200}, "input": {"shape": "GetStudioMemberRequest"}, "output": {"shape": "GetStudioMemberResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Get a user's membership in a studio.</p>"}, "ListEulaAcceptances": {"name": "ListEulaAcceptances", "http": {"method": "GET", "requestUri": "/2020-08-01/studios/{studioId}/eula-acceptances", "responseCode": 200}, "input": {"shape": "ListEulaAcceptancesRequest"}, "output": {"shape": "ListEulaAcceptancesResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>List EULA acceptances.</p>"}, "ListEulas": {"name": "ListEulas", "http": {"method": "GET", "requestUri": "/2020-08-01/eulas", "responseCode": 200}, "input": {"shape": "ListEulasRequest"}, "output": {"shape": "ListEulasResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>List EULAs.</p>"}, "ListLaunchProfileMembers": {"name": "ListLaunchProfileMembers", "http": {"method": "GET", "requestUri": "/2020-08-01/studios/{studioId}/launch-profiles/{launchProfileId}/membership", "responseCode": 200}, "input": {"shape": "ListLaunchProfileMembersRequest"}, "output": {"shape": "ListLaunchProfileMembersResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Get all users in a given launch profile membership.</p>"}, "ListLaunchProfiles": {"name": "ListLaunchProfiles", "http": {"method": "GET", "requestUri": "/2020-08-01/studios/{studioId}/launch-profiles", "responseCode": 200}, "input": {"shape": "ListLaunchProfilesRequest"}, "output": {"shape": "ListLaunchProfilesResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>List all the launch profiles a studio.</p>"}, "ListStreamingImages": {"name": "ListStreamingImages", "http": {"method": "GET", "requestUri": "/2020-08-01/studios/{studioId}/streaming-images", "responseCode": 200}, "input": {"shape": "ListStreamingImagesRequest"}, "output": {"shape": "ListStreamingImagesResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>List the streaming image resources available to this studio.</p> <p>This list will contain both images provided by Amazon Web Services, as well as streaming images that you have created in your studio.</p>"}, "ListStreamingSessionBackups": {"name": "ListStreamingSessionBackups", "http": {"method": "GET", "requestUri": "/2020-08-01/studios/{studioId}/streaming-session-backups", "responseCode": 200}, "input": {"shape": "ListStreamingSessionBackupsRequest"}, "output": {"shape": "ListStreamingSessionBackupsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Lists the backups of a streaming session in a studio.</p>"}, "ListStreamingSessions": {"name": "ListStreamingSessions", "http": {"method": "GET", "requestUri": "/2020-08-01/studios/{studioId}/streaming-sessions", "responseCode": 200}, "input": {"shape": "ListStreamingSessionsRequest"}, "output": {"shape": "ListStreamingSessionsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Lists the streaming sessions in a studio.</p>"}, "ListStudioComponents": {"name": "ListStudioComponents", "http": {"method": "GET", "requestUri": "/2020-08-01/studios/{studioId}/studio-components", "responseCode": 200}, "input": {"shape": "ListStudioComponentsRequest"}, "output": {"shape": "ListStudioComponentsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Lists the <code>StudioComponents</code> in a studio.</p>"}, "ListStudioMembers": {"name": "ListStudioMembers", "http": {"method": "GET", "requestUri": "/2020-08-01/studios/{studioId}/membership", "responseCode": 200}, "input": {"shape": "ListStudioMembersRequest"}, "output": {"shape": "ListStudioMembersResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Get all users in a given studio membership.</p> <note> <p> <code>ListStudioMembers</code> only returns admin members.</p> </note>"}, "ListStudios": {"name": "ListStudios", "http": {"method": "GET", "requestUri": "/2020-08-01/studios", "responseCode": 200}, "input": {"shape": "ListStudiosRequest"}, "output": {"shape": "ListStudiosResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>List studios in your Amazon Web Services accounts in the requested Amazon Web Services Region.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/2020-08-01/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Gets the tags for a resource, given its Amazon Resource Names (ARN).</p> <p>This operation supports ARNs for all resource types in Nimble Studio that support tags, including studio, studio component, launch profile, streaming image, and streaming session. All resources that can be tagged will contain an ARN property, so you do not have to create this ARN yourself.</p>"}, "PutLaunchProfileMembers": {"name": "PutLaunchProfileMembers", "http": {"method": "POST", "requestUri": "/2020-08-01/studios/{studioId}/launch-profiles/{launchProfileId}/membership", "responseCode": 200}, "input": {"shape": "PutLaunchProfileMembersRequest"}, "output": {"shape": "PutLaunchProfileMembersResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Add/update users with given persona to launch profile membership.</p>"}, "PutStudioMembers": {"name": "PutStudioMembers", "http": {"method": "POST", "requestUri": "/2020-08-01/studios/{studioId}/membership", "responseCode": 200}, "input": {"shape": "PutStudioMembersRequest"}, "output": {"shape": "PutStudioMembersResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Add/update users with given persona to studio membership.</p>"}, "StartStreamingSession": {"name": "StartStreamingSession", "http": {"method": "POST", "requestUri": "/2020-08-01/studios/{studioId}/streaming-sessions/{sessionId}/start", "responseCode": 200}, "input": {"shape": "StartStreamingSessionRequest"}, "output": {"shape": "StartStreamingSessionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Transitions sessions from the <code>STOPPED</code> state into the <code>READY</code> state. The <code>START_IN_PROGRESS</code> state is the intermediate state between the <code>STOPPED</code> and <code>READY</code> states.</p>", "idempotent": true}, "StartStudioSSOConfigurationRepair": {"name": "StartStudioSSOConfigurationRepair", "http": {"method": "PUT", "requestUri": "/2020-08-01/studios/{studioId}/sso-configuration", "responseCode": 200}, "input": {"shape": "StartStudioSSOConfigurationRepairRequest"}, "output": {"shape": "StartStudioSSOConfigurationRepairResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Repairs the IAM Identity Center configuration for a given studio.</p> <p>If the studio has a valid IAM Identity Center configuration currently associated with it, this operation will fail with a validation error.</p> <p>If the studio does not have a valid IAM Identity Center configuration currently associated with it, then a new IAM Identity Center application is created for the studio and the studio is changed to the <code>READY</code> state.</p> <p>After the IAM Identity Center application is repaired, you must use the Amazon Nimble Studio console to add administrators and users to your studio.</p>", "idempotent": true}, "StopStreamingSession": {"name": "StopStreamingSession", "http": {"method": "POST", "requestUri": "/2020-08-01/studios/{studioId}/streaming-sessions/{sessionId}/stop", "responseCode": 200}, "input": {"shape": "StopStreamingSessionRequest"}, "output": {"shape": "StopStreamingSessionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Transitions sessions from the <code>READY</code> state into the <code>STOPPED</code> state. The <code>STOP_IN_PROGRESS</code> state is the intermediate state between the <code>READY</code> and <code>STOPPED</code> states.</p>", "idempotent": true}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/2020-08-01/tags/{resourceArn}", "responseCode": 204}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates tags for a resource, given its ARN.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/2020-08-01/tags/{resourceArn}", "responseCode": 204}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Deletes the tags for a resource.</p>", "idempotent": true}, "UpdateLaunchProfile": {"name": "UpdateLaunchProfile", "http": {"method": "PATCH", "requestUri": "/2020-08-01/studios/{studioId}/launch-profiles/{launchProfileId}", "responseCode": 200}, "input": {"shape": "UpdateLaunchProfileRequest"}, "output": {"shape": "UpdateLaunchProfileResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Update a launch profile.</p>"}, "UpdateLaunchProfileMember": {"name": "UpdateLaunchProfileMember", "http": {"method": "PATCH", "requestUri": "/2020-08-01/studios/{studioId}/launch-profiles/{launchProfileId}/membership/{principalId}", "responseCode": 200}, "input": {"shape": "UpdateLaunchProfileMemberRequest"}, "output": {"shape": "UpdateLaunchProfileMemberResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Update a user persona in launch profile membership.</p>"}, "UpdateStreamingImage": {"name": "UpdateStreamingImage", "http": {"method": "PATCH", "requestUri": "/2020-08-01/studios/{studioId}/streaming-images/{streamingImageId}", "responseCode": 200}, "input": {"shape": "UpdateStreamingImageRequest"}, "output": {"shape": "UpdateStreamingImageResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Update streaming image.</p>"}, "UpdateStudio": {"name": "UpdateStudio", "http": {"method": "PATCH", "requestUri": "/2020-08-01/studios/{studioId}", "responseCode": 200}, "input": {"shape": "UpdateStudioRequest"}, "output": {"shape": "UpdateStudioResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Update a Studio resource.</p> <p>Currently, this operation only supports updating the displayName of your studio.</p>"}, "UpdateStudioComponent": {"name": "UpdateStudioComponent", "http": {"method": "PATCH", "requestUri": "/2020-08-01/studios/{studioId}/studio-components/{studioComponentId}", "responseCode": 200}, "input": {"shape": "UpdateStudioComponentRequest"}, "output": {"shape": "UpdateStudioComponentResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Updates a studio component resource.</p>"}}, "shapes": {"AcceptEulasRequest": {"type": "structure", "required": ["studioId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don’t specify a client token, the Amazon Web Services SDK automatically generates a client token and uses it for the request to ensure idempotency.</p>", "idempotencyToken": true, "location": "header", "locationName": "X-Amz-Client-Token"}, "eulaIds": {"shape": "EulaIdList", "documentation": "<p>The EULA ID.</p>"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID.</p>", "location": "uri", "locationName": "studioId"}}}, "AcceptEulasResponse": {"type": "structure", "members": {"eulaAcceptances": {"shape": "EulaAcceptanceList", "documentation": "<p>A collection of EULA acceptances.</p>"}}}, "AccessDeniedException": {"type": "structure", "members": {"code": {"shape": "String", "documentation": "<p>A more specific error code.</p>"}, "context": {"shape": "ExceptionContext", "documentation": "<p>The exception context.</p>"}, "message": {"shape": "String", "documentation": "<p>A human-readable description of the error.</p>"}}, "documentation": "<p>You are not authorized to perform this operation. Check your IAM policies, and ensure that you are using the correct access keys.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "ActiveDirectoryComputerAttribute": {"type": "structure", "members": {"name": {"shape": "ActiveDirectoryComputerAttributeName", "documentation": "<p>The name for the LDAP attribute.</p>"}, "value": {"shape": "ActiveDirectoryComputerAttributeValue", "documentation": "<p>The value for the LDAP attribute.</p>"}}, "documentation": "<p>An LDAP attribute of an Active Directory computer account, in the form of a name:value pair.</p>"}, "ActiveDirectoryComputerAttributeList": {"type": "list", "member": {"shape": "ActiveDirectoryComputerAttribute"}, "documentation": "<p>A collection of LDAP attributes to apply to Active Directory computer accounts that are created for streaming sessions.</p>", "max": 50, "min": 0, "sensitive": true}, "ActiveDirectoryComputerAttributeName": {"type": "string", "documentation": "<p>The name for the LDAP attribute.</p>", "max": 40, "min": 1}, "ActiveDirectoryComputerAttributeValue": {"type": "string", "documentation": "<p>The value for the LDAP attribute.</p>", "max": 64, "min": 1}, "ActiveDirectoryConfiguration": {"type": "structure", "members": {"computerAttributes": {"shape": "ActiveDirectoryComputerAttributeList", "documentation": "<p>A collection of custom attributes for an Active Directory computer.</p>"}, "directoryId": {"shape": "DirectoryId", "documentation": "<p>The directory ID of the Directory Service for Microsoft Active Directory to access using this studio component.</p>"}, "organizationalUnitDistinguishedName": {"shape": "ActiveDirectoryOrganizationalUnitDistinguishedName", "documentation": "<p>The distinguished name (DN) and organizational unit (OU) of an Active Directory computer.</p>"}}, "documentation": "<p>The configuration for a Directory Service for Microsoft Active Directory studio resource.</p>"}, "ActiveDirectoryDnsIpAddress": {"type": "string"}, "ActiveDirectoryDnsIpAddressList": {"type": "list", "member": {"shape": "ActiveDirectoryDnsIpAddress"}, "max": 10, "min": 0}, "ActiveDirectoryOrganizationalUnitDistinguishedName": {"type": "string", "documentation": "<p>The fully-qualified distinguished name of the organizational unit to place the computer account in.</p>", "max": 2000, "min": 1}, "AutomaticTerminationMode": {"type": "string", "enum": ["DEACTIVATED", "ACTIVATED"]}, "ClientToken": {"type": "string", "max": 64, "min": 1}, "ComputeFarmConfiguration": {"type": "structure", "members": {"activeDirectoryUser": {"shape": "String", "documentation": "<p>The name of an Active Directory user that is used on ComputeFarm worker instances.</p>"}, "endpoint": {"shape": "SensitiveString", "documentation": "<p>The endpoint of the ComputeFarm that is accessed by the studio component resource.</p>"}}, "documentation": "<p>The configuration for a render farm that is associated with a studio resource.</p>"}, "ConflictException": {"type": "structure", "members": {"code": {"shape": "String", "documentation": "<p>A more specific error code.</p>"}, "context": {"shape": "ExceptionContext", "documentation": "<p>The exception context.</p>"}, "message": {"shape": "String", "documentation": "<p>A human-readable description of the error.</p>"}}, "documentation": "<p>Another operation is in progress. </p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "CreateLaunchProfileRequest": {"type": "structure", "required": ["ec2SubnetIds", "launchProfileProtocolVersions", "name", "streamConfiguration", "studioComponentIds", "studioId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don’t specify a client token, the Amazon Web Services SDK automatically generates a client token and uses it for the request to ensure idempotency.</p>", "idempotencyToken": true, "location": "header", "locationName": "X-Amz-Client-Token"}, "description": {"shape": "LaunchProfileDescription", "documentation": "<p>The description.</p>"}, "ec2SubnetIds": {"shape": "EC2SubnetIdList", "documentation": "<p>Specifies the IDs of the EC2 subnets where streaming sessions will be accessible from. These subnets must support the specified instance types. </p>"}, "launchProfileProtocolVersions": {"shape": "LaunchProfileProtocolVersionList", "documentation": "<p>The version number of the protocol that is used by the launch profile. The only valid version is \"2021-03-31\".</p>"}, "name": {"shape": "LaunchProfileName", "documentation": "<p>The name for the launch profile.</p>"}, "streamConfiguration": {"shape": "StreamConfigurationCreate", "documentation": "<p>A configuration for a streaming session.</p>"}, "studioComponentIds": {"shape": "LaunchProfileStudioComponentIdList", "documentation": "<p>Unique identifiers for a collection of studio components that can be used with this launch profile.</p>"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}, "tags": {"shape": "Tags", "documentation": "<p>A collection of labels, in the form of key-value pairs, that apply to this resource.</p>"}}}, "CreateLaunchProfileResponse": {"type": "structure", "members": {"launchProfile": {"shape": "LaunchProfile", "documentation": "<p>The launch profile.</p>"}}}, "CreateStreamingImageRequest": {"type": "structure", "required": ["ec2ImageId", "name", "studioId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don’t specify a client token, the Amazon Web Services SDK automatically generates a client token and uses it for the request to ensure idempotency.</p>", "idempotencyToken": true, "location": "header", "locationName": "X-Amz-Client-Token"}, "description": {"shape": "StreamingImageDescription", "documentation": "<p>A human-readable description of the streaming image.</p>"}, "ec2ImageId": {"shape": "EC2ImageId", "documentation": "<p>The ID of an EC2 machine image with which to create this streaming image.</p>"}, "name": {"shape": "StreamingImageName", "documentation": "<p>A friendly name for a streaming image resource.</p>"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}, "tags": {"shape": "Tags", "documentation": "<p>A collection of labels, in the form of key-value pairs, that apply to this resource.</p>"}}}, "CreateStreamingImageResponse": {"type": "structure", "members": {"streamingImage": {"shape": "StreamingImage", "documentation": "<p>The streaming image.</p>"}}}, "CreateStreamingSessionRequest": {"type": "structure", "required": ["launchProfileId", "studioId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don’t specify a client token, the Amazon Web Services SDK automatically generates a client token and uses it for the request to ensure idempotency.</p>", "idempotencyToken": true, "location": "header", "locationName": "X-Amz-Client-Token"}, "ec2InstanceType": {"shape": "StreamingInstanceType", "documentation": "<p>The EC2 Instance type used for the streaming session.</p>"}, "launchProfileId": {"shape": "String", "documentation": "<p>The ID of the launch profile used to control access from the streaming session.</p>"}, "ownedBy": {"shape": "String", "documentation": "<p>The user ID of the user that owns the streaming session. The user that owns the session will be logging into the session and interacting with the virtual workstation.</p>"}, "streamingImageId": {"shape": "StreamingImageId", "documentation": "<p>The ID of the streaming image.</p>"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}, "tags": {"shape": "Tags", "documentation": "<p>A collection of labels, in the form of key-value pairs, that apply to this resource.</p>"}}}, "CreateStreamingSessionResponse": {"type": "structure", "members": {"session": {"shape": "StreamingSession", "documentation": "<p>The session.</p>"}}}, "CreateStreamingSessionStreamRequest": {"type": "structure", "required": ["sessionId", "studioId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don’t specify a client token, the Amazon Web Services SDK automatically generates a client token and uses it for the request to ensure idempotency.</p>", "idempotencyToken": true, "location": "header", "locationName": "X-Amz-Client-Token"}, "expirationInSeconds": {"shape": "StreamingSessionStreamExpirationInSeconds", "documentation": "<p>The expiration time in seconds.</p>"}, "sessionId": {"shape": "String", "documentation": "<p>The streaming session ID.</p>", "location": "uri", "locationName": "sessionId"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "CreateStreamingSessionStreamResponse": {"type": "structure", "members": {"stream": {"shape": "StreamingSessionStream", "documentation": "<p>The stream.</p>"}}}, "CreateStudioComponentRequest": {"type": "structure", "required": ["name", "studioId", "type"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don’t specify a client token, the Amazon Web Services SDK automatically generates a client token and uses it for the request to ensure idempotency.</p>", "idempotencyToken": true, "location": "header", "locationName": "X-Amz-Client-Token"}, "configuration": {"shape": "StudioComponentConfiguration", "documentation": "<p>The configuration of the studio component, based on component type.</p>"}, "description": {"shape": "StudioComponentDescription", "documentation": "<p>The description.</p>"}, "ec2SecurityGroupIds": {"shape": "StudioComponentSecurityGroupIdList", "documentation": "<p>The EC2 security groups that control access to the studio component.</p>"}, "initializationScripts": {"shape": "StudioComponentInitializationScriptList", "documentation": "<p>Initialization scripts for studio components.</p>"}, "name": {"shape": "StudioComponentName", "documentation": "<p>The name for the studio component.</p>"}, "runtimeRoleArn": {"shape": "RoleArn", "documentation": "<p>An IAM role attached to a Studio Component that gives the studio component access to Amazon Web Services resources at anytime while the instance is running. </p>"}, "scriptParameters": {"shape": "StudioComponentScriptParameterKeyValueList", "documentation": "<p>Parameters for the studio component scripts.</p>"}, "secureInitializationRoleArn": {"shape": "RoleArn", "documentation": "<p>An IAM role attached to Studio Component when the system initialization script runs which give the studio component access to Amazon Web Services resources when the system initialization script runs.</p>"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}, "subtype": {"shape": "StudioComponentSubtype", "documentation": "<p>The specific subtype of a studio component.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A collection of labels, in the form of key-value pairs, that apply to this resource.</p>"}, "type": {"shape": "StudioComponentType", "documentation": "<p>The type of the studio component.</p>"}}}, "CreateStudioComponentResponse": {"type": "structure", "members": {"studioComponent": {"shape": "StudioComponent", "documentation": "<p>Information about the studio component.</p>"}}}, "CreateStudioRequest": {"type": "structure", "required": ["adminRoleArn", "displayName", "studioName", "userRoleArn"], "members": {"adminRoleArn": {"shape": "RoleArn", "documentation": "<p>The IAM role that studio admins will assume when logging in to the Nimble Studio portal.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don’t specify a client token, the Amazon Web Services SDK automatically generates a client token and uses it for the request to ensure idempotency.</p>", "idempotencyToken": true, "location": "header", "locationName": "X-Amz-Client-Token"}, "displayName": {"shape": "StudioDisplayName", "documentation": "<p>A friendly name for the studio.</p>"}, "studioEncryptionConfiguration": {"shape": "StudioEncryptionConfiguration", "documentation": "<p>The studio encryption configuration.</p>"}, "studioName": {"shape": "StudioName", "documentation": "<p>The studio name that is used in the URL of the Nimble Studio portal when accessed by Nimble Studio users.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A collection of labels, in the form of key-value pairs, that apply to this resource.</p>"}, "userRoleArn": {"shape": "RoleArn", "documentation": "<p>The IAM role that studio users will assume when logging in to the Nimble Studio portal.</p>"}}}, "CreateStudioResponse": {"type": "structure", "members": {"studio": {"shape": "Studio", "documentation": "<p>Information about a studio.</p>"}}}, "DeleteLaunchProfileMemberRequest": {"type": "structure", "required": ["launchProfileId", "principalId", "studioId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don’t specify a client token, the Amazon Web Services SDK automatically generates a client token and uses it for the request to ensure idempotency.</p>", "idempotencyToken": true, "location": "header", "locationName": "X-Amz-Client-Token"}, "launchProfileId": {"shape": "String", "documentation": "<p>The ID of the launch profile used to control access from the streaming session.</p>", "location": "uri", "locationName": "launchProfileId"}, "principalId": {"shape": "String", "documentation": "<p>The principal ID. This currently supports a IAM Identity Center UserId. </p>", "location": "uri", "locationName": "principalId"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "DeleteLaunchProfileMemberResponse": {"type": "structure", "members": {}}, "DeleteLaunchProfileRequest": {"type": "structure", "required": ["launchProfileId", "studioId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don’t specify a client token, the Amazon Web Services SDK automatically generates a client token and uses it for the request to ensure idempotency.</p>", "idempotencyToken": true, "location": "header", "locationName": "X-Amz-Client-Token"}, "launchProfileId": {"shape": "String", "documentation": "<p>The ID of the launch profile used to control access from the streaming session.</p>", "location": "uri", "locationName": "launchProfileId"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "DeleteLaunchProfileResponse": {"type": "structure", "members": {"launchProfile": {"shape": "LaunchProfile", "documentation": "<p>The launch profile.</p>"}}}, "DeleteStreamingImageRequest": {"type": "structure", "required": ["streamingImageId", "studioId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don’t specify a client token, the Amazon Web Services SDK automatically generates a client token and uses it for the request to ensure idempotency.</p>", "idempotencyToken": true, "location": "header", "locationName": "X-Amz-Client-Token"}, "streamingImageId": {"shape": "String", "documentation": "<p>The streaming image ID.</p>", "location": "uri", "locationName": "streamingImageId"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "DeleteStreamingImageResponse": {"type": "structure", "members": {"streamingImage": {"shape": "StreamingImage", "documentation": "<p>The streaming image.</p>"}}}, "DeleteStreamingSessionRequest": {"type": "structure", "required": ["sessionId", "studioId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don’t specify a client token, the Amazon Web Services SDK automatically generates a client token and uses it for the request to ensure idempotency.</p>", "idempotencyToken": true, "location": "header", "locationName": "X-Amz-Client-Token"}, "sessionId": {"shape": "String", "documentation": "<p>The streaming session ID.</p>", "location": "uri", "locationName": "sessionId"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "DeleteStreamingSessionResponse": {"type": "structure", "members": {"session": {"shape": "StreamingSession", "documentation": "<p>The session.</p>"}}}, "DeleteStudioComponentRequest": {"type": "structure", "required": ["studioComponentId", "studioId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don’t specify a client token, the Amazon Web Services SDK automatically generates a client token and uses it for the request to ensure idempotency.</p>", "idempotencyToken": true, "location": "header", "locationName": "X-Amz-Client-Token"}, "studioComponentId": {"shape": "String", "documentation": "<p>The studio component ID.</p>", "location": "uri", "locationName": "studioComponentId"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "DeleteStudioComponentResponse": {"type": "structure", "members": {"studioComponent": {"shape": "StudioComponent", "documentation": "<p>Information about the studio component.</p>"}}}, "DeleteStudioMemberRequest": {"type": "structure", "required": ["principalId", "studioId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don’t specify a client token, the Amazon Web Services SDK automatically generates a client token and uses it for the request to ensure idempotency.</p>", "idempotencyToken": true, "location": "header", "locationName": "X-Amz-Client-Token"}, "principalId": {"shape": "String", "documentation": "<p>The principal ID. This currently supports a IAM Identity Center UserId. </p>", "location": "uri", "locationName": "principalId"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "DeleteStudioMemberResponse": {"type": "structure", "members": {}}, "DeleteStudioRequest": {"type": "structure", "required": ["studioId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don’t specify a client token, the Amazon Web Services SDK automatically generates a client token and uses it for the request to ensure idempotency.</p>", "idempotencyToken": true, "location": "header", "locationName": "X-Amz-Client-Token"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "DeleteStudioResponse": {"type": "structure", "required": ["studio"], "members": {"studio": {"shape": "Studio", "documentation": "<p>Information about a studio.</p>"}}}, "DirectoryId": {"type": "string"}, "EC2ImageId": {"type": "string", "pattern": "^ami-[0-9A-z]+$"}, "EC2SubnetId": {"type": "string"}, "EC2SubnetIdList": {"type": "list", "member": {"shape": "EC2SubnetId"}, "max": 6, "min": 0}, "Eula": {"type": "structure", "members": {"content": {"shape": "String", "documentation": "<p>The EULA content.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The ISO timestamp in seconds for when the resource was created.</p>"}, "eulaId": {"shape": "EulaId", "documentation": "<p>The EULA ID.</p>"}, "name": {"shape": "<PERSON>ulaName", "documentation": "<p>The name for the EULA.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The ISO timestamp in seconds for when the resource was updated.</p>"}}, "documentation": "<p>Represents a EULA resource.</p>"}, "EulaAcceptance": {"type": "structure", "members": {"acceptedAt": {"shape": "Timestamp", "documentation": "<p>The ISO timestamp in seconds for when the EULA was accepted.</p>"}, "acceptedBy": {"shape": "String", "documentation": "<p>The ID of the person who accepted the EULA.</p>"}, "accepteeId": {"shape": "String", "documentation": "<p>The ID of the acceptee.</p>"}, "eulaAcceptanceId": {"shape": "EulaAcceptanceId", "documentation": "<p>The EULA acceptance ID.</p>"}, "eulaId": {"shape": "EulaId", "documentation": "<p>The EULA ID.</p>"}}, "documentation": "<p>The acceptance of a EULA, required to use Amazon-provided streaming images.</p>"}, "EulaAcceptanceId": {"type": "string", "documentation": "<p>The EULA acceptance ID.</p>", "max": 22, "min": 0, "pattern": "^[a-zA-Z0-9-_]*$"}, "EulaAcceptanceList": {"type": "list", "member": {"shape": "EulaAcceptance"}}, "EulaId": {"type": "string", "documentation": "<p>Represents a EULA resource.</p>", "max": 22, "min": 0, "pattern": "^[a-zA-Z0-9-_]*$"}, "EulaIdList": {"type": "list", "member": {"shape": "String"}}, "EulaList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}}, "EulaName": {"type": "string", "max": 64, "min": 0}, "ExceptionContext": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}, "documentation": "<p>ExceptionContext is a set of key-value pairs that provide you with more information about the error that occurred. For example, when the service returns a 404 ResourceNotFound error, ExceptionContext will contain the key `resourceId` with the value of resource that was not found.</p> <p> <code>ExceptionContext</code> allows scripts and other programmatic clients to provide better error handling.</p>"}, "GetEulaRequest": {"type": "structure", "required": ["eulaId"], "members": {"eulaId": {"shape": "String", "documentation": "<p>The EULA ID.</p>", "location": "uri", "locationName": "eulaId"}}}, "GetEulaResponse": {"type": "structure", "members": {"eula": {"shape": "<PERSON><PERSON>", "documentation": "<p>The EULA.</p>"}}}, "GetLaunchProfileDetailsRequest": {"type": "structure", "required": ["launchProfileId", "studioId"], "members": {"launchProfileId": {"shape": "String", "documentation": "<p>The ID of the launch profile used to control access from the streaming session.</p>", "location": "uri", "locationName": "launchProfileId"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "GetLaunchProfileDetailsResponse": {"type": "structure", "members": {"launchProfile": {"shape": "LaunchProfile", "documentation": "<p>The launch profile.</p>"}, "streamingImages": {"shape": "StreamingImageList", "documentation": "<p>A collection of streaming images.</p>"}, "studioComponentSummaries": {"shape": "StudioComponentSummaryList", "documentation": "<p>A collection of studio component summaries.</p>"}}}, "GetLaunchProfileInitializationRequest": {"type": "structure", "required": ["launchProfileId", "launchProfileProtocolVersions", "launchPurpose", "platform", "studioId"], "members": {"launchProfileId": {"shape": "String", "documentation": "<p>The ID of the launch profile used to control access from the streaming session.</p>", "location": "uri", "locationName": "launchProfileId"}, "launchProfileProtocolVersions": {"shape": "StringList", "documentation": "<p>The launch profile protocol versions supported by the client.</p>", "location": "querystring", "locationName": "launchProfileProtocolVersions"}, "launchPurpose": {"shape": "String", "documentation": "<p>The launch purpose.</p>", "location": "querystring", "locationName": "launchPurpose"}, "platform": {"shape": "String", "documentation": "<p>The platform where this Launch Profile will be used, either Windows or Linux.</p>", "location": "querystring", "locationName": "platform"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "GetLaunchProfileInitializationResponse": {"type": "structure", "members": {"launchProfileInitialization": {"shape": "LaunchProfileInitialization", "documentation": "<p>The launch profile initialization.</p>"}}}, "GetLaunchProfileMemberRequest": {"type": "structure", "required": ["launchProfileId", "principalId", "studioId"], "members": {"launchProfileId": {"shape": "String", "documentation": "<p>The ID of the launch profile used to control access from the streaming session.</p>", "location": "uri", "locationName": "launchProfileId"}, "principalId": {"shape": "String", "documentation": "<p>The principal ID. This currently supports a IAM Identity Center UserId. </p>", "location": "uri", "locationName": "principalId"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "GetLaunchProfileMemberResponse": {"type": "structure", "members": {"member": {"shape": "LaunchProfileMembership", "documentation": "<p>The member.</p>"}}}, "GetLaunchProfileRequest": {"type": "structure", "required": ["launchProfileId", "studioId"], "members": {"launchProfileId": {"shape": "String", "documentation": "<p>The ID of the launch profile used to control access from the streaming session.</p>", "location": "uri", "locationName": "launchProfileId"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "GetLaunchProfileResponse": {"type": "structure", "members": {"launchProfile": {"shape": "LaunchProfile", "documentation": "<p>The launch profile.</p>"}}}, "GetStreamingImageRequest": {"type": "structure", "required": ["streamingImageId", "studioId"], "members": {"streamingImageId": {"shape": "String", "documentation": "<p>The streaming image ID.</p>", "location": "uri", "locationName": "streamingImageId"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "GetStreamingImageResponse": {"type": "structure", "members": {"streamingImage": {"shape": "StreamingImage", "documentation": "<p>The streaming image.</p>"}}}, "GetStreamingSessionBackupRequest": {"type": "structure", "required": ["backupId", "studioId"], "members": {"backupId": {"shape": "String", "documentation": "<p>The ID of the backup.</p>", "location": "uri", "locationName": "backupId"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "GetStreamingSessionBackupResponse": {"type": "structure", "members": {"streamingSessionBackup": {"shape": "StreamingSessionBackup", "documentation": "<p>Information about the streaming session backup.</p>"}}}, "GetStreamingSessionRequest": {"type": "structure", "required": ["sessionId", "studioId"], "members": {"sessionId": {"shape": "String", "documentation": "<p>The streaming session ID.</p>", "location": "uri", "locationName": "sessionId"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "GetStreamingSessionResponse": {"type": "structure", "members": {"session": {"shape": "StreamingSession", "documentation": "<p>The session.</p>"}}}, "GetStreamingSessionStreamRequest": {"type": "structure", "required": ["sessionId", "streamId", "studioId"], "members": {"sessionId": {"shape": "String", "documentation": "<p>The streaming session ID.</p>", "location": "uri", "locationName": "sessionId"}, "streamId": {"shape": "String", "documentation": "<p>The streaming session stream ID.</p>", "location": "uri", "locationName": "streamId"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "GetStreamingSessionStreamResponse": {"type": "structure", "members": {"stream": {"shape": "StreamingSessionStream", "documentation": "<p>The stream.</p>"}}}, "GetStudioComponentRequest": {"type": "structure", "required": ["studioComponentId", "studioId"], "members": {"studioComponentId": {"shape": "String", "documentation": "<p>The studio component ID.</p>", "location": "uri", "locationName": "studioComponentId"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "GetStudioComponentResponse": {"type": "structure", "members": {"studioComponent": {"shape": "StudioComponent", "documentation": "<p>Information about the studio component.</p>"}}}, "GetStudioMemberRequest": {"type": "structure", "required": ["principalId", "studioId"], "members": {"principalId": {"shape": "String", "documentation": "<p>The principal ID. This currently supports a IAM Identity Center UserId. </p>", "location": "uri", "locationName": "principalId"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "GetStudioMemberResponse": {"type": "structure", "members": {"member": {"shape": "StudioMembership", "documentation": "<p>The member.</p>"}}}, "GetStudioRequest": {"type": "structure", "required": ["studioId"], "members": {"studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "GetStudioResponse": {"type": "structure", "required": ["studio"], "members": {"studio": {"shape": "Studio", "documentation": "<p>Information about a studio.</p>"}}}, "InternalServerErrorException": {"type": "structure", "members": {"code": {"shape": "String", "documentation": "<p>A more specific error code.</p>"}, "context": {"shape": "ExceptionContext", "documentation": "<p>The exception context.</p>"}, "message": {"shape": "String", "documentation": "<p>A human-readable description of the error.</p>"}}, "documentation": "<p>An internal error has occurred. Please retry your request.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "LaunchProfile": {"type": "structure", "members": {"arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) that is assigned to a studio resource and uniquely identifies it. ARNs are unique across all Regions.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The ISO timestamp in seconds for when the resource was created.</p>"}, "createdBy": {"shape": "String", "documentation": "<p>The user ID of the user that created the launch profile.</p>"}, "description": {"shape": "LaunchProfileDescription", "documentation": "<p>A human-readable description of the launch profile.</p>"}, "ec2SubnetIds": {"shape": "EC2SubnetIdList", "documentation": "<p>Unique identifiers for a collection of EC2 subnets.</p>"}, "launchProfileId": {"shape": "LaunchProfileId", "documentation": "<p>The ID of the launch profile used to control access from the streaming session.</p>"}, "launchProfileProtocolVersions": {"shape": "LaunchProfileProtocolVersionList", "documentation": "<p>The version number of the protocol that is used by the launch profile. The only valid version is \"2021-03-31\".</p>"}, "name": {"shape": "LaunchProfileName", "documentation": "<p>A friendly name for the launch profile.</p>"}, "state": {"shape": "LaunchProfileState", "documentation": "<p>The current state.</p>"}, "statusCode": {"shape": "LaunchProfileStatusCode", "documentation": "<p>The status code.</p>"}, "statusMessage": {"shape": "String", "documentation": "<p>The status message for the launch profile.</p>"}, "streamConfiguration": {"shape": "StreamConfiguration", "documentation": "<p>A configuration for a streaming session.</p>"}, "studioComponentIds": {"shape": "LaunchProfileStudioComponentIdList", "documentation": "<p>Unique identifiers for a collection of studio components that can be used with this launch profile.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A collection of labels, in the form of key-value pairs, that apply to this resource.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The ISO timestamp in seconds for when the resource was updated.</p>"}, "updatedBy": {"shape": "String", "documentation": "<p>The user ID of the user that most recently updated the resource.</p>"}, "validationResults": {"shape": "ValidationResults", "documentation": "<p>The list of the latest validation results.</p>"}}, "documentation": "<p>A launch profile controls your artist workforce’s access to studio components, like compute farms, shared file systems, managed file systems, and license server configurations, as well as instance types and Amazon Machine Images (AMIs). </p> <p>Studio administrators create launch profiles in the Nimble Studio console. Artists can use their launch profiles to launch an instance from the Nimble Studio portal. Each user’s launch profile defines how they can launch a streaming session. By default, studio admins can use all launch profiles.</p>"}, "LaunchProfileDescription": {"type": "string", "documentation": "<p>A human-readable description of the launch profile.</p>", "max": 256, "min": 0, "sensitive": true}, "LaunchProfileId": {"type": "string", "max": 22, "min": 0, "pattern": "^[a-zA-Z0-9-_]*$"}, "LaunchProfileInitialization": {"type": "structure", "members": {"activeDirectory": {"shape": "LaunchProfileInitializationActiveDirectory", "documentation": "<p>A <code>LaunchProfileInitializationActiveDirectory</code> resource.</p>"}, "ec2SecurityGroupIds": {"shape": "LaunchProfileSecurityGroupIdList", "documentation": "<p>The EC2 security groups that control access to the studio component.</p>"}, "launchProfileId": {"shape": "LaunchProfileId", "documentation": "<p>The ID of the launch profile used to control access from the streaming session.</p>"}, "launchProfileProtocolVersion": {"shape": "LaunchProfileProtocolVersion", "documentation": "<p>The version number of the protocol that is used by the launch profile. The only valid version is \"2021-03-31\".</p>"}, "launchPurpose": {"shape": "LaunchPurpose", "documentation": "<p>The launch purpose.</p>"}, "name": {"shape": "LaunchProfileName", "documentation": "<p>The name for the launch profile.</p>"}, "platform": {"shape": "LaunchProfilePlatform", "documentation": "<p>The platform of the launch platform, either Windows or Linux.</p>"}, "systemInitializationScripts": {"shape": "LaunchProfileInitializationScriptList", "documentation": "<p>The system initializtion scripts.</p>"}, "userInitializationScripts": {"shape": "LaunchProfileInitializationScriptList", "documentation": "<p>The user initializtion scripts.</p>"}}, "documentation": "<p>A launch profile initialization contains information required for a workstation or server to connect to a launch profile.</p> <p>This includes scripts, endpoints, security groups, subnets, and other configuration.</p>"}, "LaunchProfileInitializationActiveDirectory": {"type": "structure", "members": {"computerAttributes": {"shape": "ActiveDirectoryComputerAttributeList", "documentation": "<p>A collection of custom attributes for an Active Directory computer.</p>"}, "directoryId": {"shape": "DirectoryId", "documentation": "<p>The directory ID of the Directory Service for Microsoft Active Directory to access using this launch profile.</p>"}, "directoryName": {"shape": "String", "documentation": "<p>The directory name.</p>"}, "dnsIpAddresses": {"shape": "ActiveDirectoryDnsIpAddressList", "documentation": "<p>The DNS IP address.</p>"}, "organizationalUnitDistinguishedName": {"shape": "ActiveDirectoryOrganizationalUnitDistinguishedName", "documentation": "<p>The name for the organizational unit distinguished name.</p>"}, "studioComponentId": {"shape": "StudioComponentId", "documentation": "<p>The unique identifier for a studio component resource.</p>"}, "studioComponentName": {"shape": "StudioComponentName", "documentation": "<p>The name for the studio component.</p>"}}, "documentation": "<p>The launch profile initialization Active Directory contains information required for the launch profile to connect to the Active Directory.</p>"}, "LaunchProfileInitializationScript": {"type": "structure", "members": {"runtimeRoleArn": {"shape": "RoleArn", "documentation": "<p>An IAM role attached to a Studio Component that gives the studio component access to Amazon Web Services resources at anytime while the instance is running. </p>"}, "script": {"shape": "StudioComponentInitializationScriptContent", "documentation": "<p>The initialization script.</p>"}, "secureInitializationRoleArn": {"shape": "RoleArn", "documentation": "<p>An IAM role attached to Studio Component when the system initialization script runs which give the studio component access to Amazon Web Services resources when the system initialization script runs.</p>"}, "studioComponentId": {"shape": "StudioComponentId", "documentation": "<p>The unique identifier for a studio component resource.</p>"}, "studioComponentName": {"shape": "StudioComponentName", "documentation": "<p>The name for the studio component.</p>"}}, "documentation": "<p>The launch profile initialization script is used when start streaming session runs.</p>"}, "LaunchProfileInitializationScriptList": {"type": "list", "member": {"shape": "LaunchProfileInitializationScript"}}, "LaunchProfileList": {"type": "list", "member": {"shape": "LaunchProfile"}}, "LaunchProfileMembership": {"type": "structure", "members": {"identityStoreId": {"shape": "String", "documentation": "<p>The ID of the identity store.</p>"}, "persona": {"shape": "LaunchProfilePersona", "documentation": "<p>The persona.</p>"}, "principalId": {"shape": "String", "documentation": "<p>The principal ID.</p>"}, "sid": {"shape": "String", "documentation": "<p>The Active Directory Security Identifier for this user, if available.</p>"}}, "documentation": "<p>Studio admins can use launch profile membership to delegate launch profile access to studio users in the Nimble Studio portal without writing or maintaining complex IAM policies. A launch profile member is a user association from your studio identity source who is granted permissions to a launch profile.</p> <p>A launch profile member (type USER) provides the following permissions to that launch profile:</p> <ul> <li> <p>GetLaunchProfile</p> </li> <li> <p>GetLaunchProfileInitialization</p> </li> <li> <p>GetLaunchProfileMembers</p> </li> <li> <p>GetLaunchProfileMember</p> </li> <li> <p>CreateStreamingSession</p> </li> <li> <p>GetLaunchProfileDetails</p> </li> </ul>"}, "LaunchProfileMembershipList": {"type": "list", "member": {"shape": "LaunchProfileMembership"}, "max": 20, "min": 0}, "LaunchProfileName": {"type": "string", "max": 64, "min": 1, "sensitive": true}, "LaunchProfilePersona": {"type": "string", "enum": ["USER"]}, "LaunchProfilePlatform": {"type": "string", "enum": ["LINUX", "WINDOWS"]}, "LaunchProfileProtocolVersion": {"type": "string", "documentation": "<p>The version number of the protocol that is used by the launch profile. The only valid version is \"2021-03-31\".</p>", "max": 10, "min": 0, "pattern": "^2021\\-03\\-31$"}, "LaunchProfileProtocolVersionList": {"type": "list", "member": {"shape": "LaunchProfileProtocolVersion"}}, "LaunchProfileSecurityGroupIdList": {"type": "list", "member": {"shape": "SecurityGroupId"}, "min": 1}, "LaunchProfileState": {"type": "string", "enum": ["CREATE_IN_PROGRESS", "READY", "UPDATE_IN_PROGRESS", "DELETE_IN_PROGRESS", "DELETED", "DELETE_FAILED", "CREATE_FAILED", "UPDATE_FAILED"]}, "LaunchProfileStateList": {"type": "list", "member": {"shape": "LaunchProfileState"}}, "LaunchProfileStatusCode": {"type": "string", "enum": ["LAUNCH_PROFILE_CREATED", "LAUNCH_PROFILE_UPDATED", "LAUNCH_PROFILE_DELETED", "LAUNCH_PROFILE_CREATE_IN_PROGRESS", "LAUNCH_PROFILE_UPDATE_IN_PROGRESS", "LAUNCH_PROFILE_DELETE_IN_PROGRESS", "INTERNAL_ERROR", "STREAMING_IMAGE_NOT_FOUND", "STREAMING_IMAGE_NOT_READY", "LAUNCH_PROFILE_WITH_STREAM_SESSIONS_NOT_DELETED", "ENCRYPTION_KEY_ACCESS_DENIED", "ENCRYPTION_KEY_NOT_FOUND", "INVALID_SUBNETS_PROVIDED", "INVALID_INSTANCE_TYPES_PROVIDED", "INVALID_SUBNETS_COMBINATION"]}, "LaunchProfileStudioComponentIdList": {"type": "list", "member": {"shape": "String"}, "max": 100, "min": 1}, "LaunchProfileValidationState": {"type": "string", "enum": ["VALIDATION_NOT_STARTED", "VALIDATION_IN_PROGRESS", "VALIDATION_SUCCESS", "VALIDATION_FAILED", "VALIDATION_FAILED_INTERNAL_SERVER_ERROR"]}, "LaunchProfileValidationStatusCode": {"type": "string", "enum": ["VALIDATION_NOT_STARTED", "VALIDATION_IN_PROGRESS", "VALIDATION_SUCCESS", "VALIDATION_FAILED_INVALID_SUBNET_ROUTE_TABLE_ASSOCIATION", "VALIDATION_FAILED_SUBNET_NOT_FOUND", "VALIDATION_FAILED_INVALID_SECURITY_GROUP_ASSOCIATION", "VALIDATION_FAILED_INVALID_ACTIVE_DIRECTORY", "VALIDATION_FAILED_UNAUTHORIZED", "VALIDATION_FAILED_INTERNAL_SERVER_ERROR"]}, "LaunchProfileValidationStatusMessage": {"type": "string"}, "LaunchProfileValidationType": {"type": "string", "enum": ["VALIDATE_ACTIVE_DIRECTORY_STUDIO_COMPONENT", "VALIDATE_SUBNET_ASSOCIATION", "VALIDATE_NETWORK_ACL_ASSOCIATION", "VALIDATE_SECURITY_GROUP_ASSOCIATION"]}, "LaunchPurpose": {"type": "string", "documentation": "<p>The launch purpose.</p>", "max": 64, "min": 0, "pattern": "^[A-Z0-9_]+$"}, "LicenseServiceConfiguration": {"type": "structure", "members": {"endpoint": {"shape": "SensitiveString", "documentation": "<p>The endpoint of the license service that is accessed by the studio component resource.</p>"}}, "documentation": "<p>The configuration for a license service that is associated with a studio resource.</p>"}, "LinuxMountPoint": {"type": "string", "max": 128, "min": 0, "pattern": "^(/?|(\\$HOME)?(/[^/\\n\\s\\\\]+)*)$", "sensitive": true}, "ListEulaAcceptancesRequest": {"type": "structure", "required": ["studioId"], "members": {"eulaIds": {"shape": "StringList", "documentation": "<p>The list of EULA IDs that have been previously accepted.</p>", "location": "querystring", "locationName": "eulaIds"}, "nextToken": {"shape": "String", "documentation": "<p>The token for the next set of results, or null if there are no more results.</p>", "location": "querystring", "locationName": "nextToken"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "ListEulaAcceptancesResponse": {"type": "structure", "members": {"eulaAcceptances": {"shape": "EulaAcceptanceList", "documentation": "<p>A collection of EULA acceptances.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>The token for the next set of results, or null if there are no more results.</p>"}}}, "ListEulasRequest": {"type": "structure", "members": {"eulaIds": {"shape": "StringList", "documentation": "<p>The list of EULA IDs that should be returned</p>", "location": "querystring", "locationName": "eulaIds"}, "nextToken": {"shape": "String", "documentation": "<p>The token for the next set of results, or null if there are no more results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListEulasResponse": {"type": "structure", "members": {"eulas": {"shape": "EulaList", "documentation": "<p>A collection of EULA resources.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>The token for the next set of results, or null if there are no more results.</p>"}}}, "ListLaunchProfileMembersRequest": {"type": "structure", "required": ["launchProfileId", "studioId"], "members": {"launchProfileId": {"shape": "String", "documentation": "<p>The ID of the launch profile used to control access from the streaming session.</p>", "location": "uri", "locationName": "launchProfileId"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The max number of results to return in the response.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "String", "documentation": "<p>The token for the next set of results, or null if there are no more results.</p>", "location": "querystring", "locationName": "nextToken"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "ListLaunchProfileMembersResponse": {"type": "structure", "members": {"members": {"shape": "LaunchProfileMembershipList", "documentation": "<p>A list of members.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>The token for the next set of results, or null if there are no more results.</p>"}}}, "ListLaunchProfilesRequest": {"type": "structure", "required": ["studioId"], "members": {"maxResults": {"shape": "MaxResults", "documentation": "<p>The max number of results to return in the response.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "String", "documentation": "<p>The token for the next set of results, or null if there are no more results.</p>", "location": "querystring", "locationName": "nextToken"}, "principalId": {"shape": "String", "documentation": "<p>The principal ID. This currently supports a IAM Identity Center UserId. </p>", "location": "querystring", "locationName": "principalId"}, "states": {"shape": "LaunchProfileStateList", "documentation": "<p>Filter this request to launch profiles in any of the given states.</p>", "location": "querystring", "locationName": "states"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "ListLaunchProfilesResponse": {"type": "structure", "members": {"launchProfiles": {"shape": "LaunchProfileList", "documentation": "<p>A collection of launch profiles.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>The token for the next set of results, or null if there are no more results.</p>"}}}, "ListStreamingImagesRequest": {"type": "structure", "required": ["studioId"], "members": {"nextToken": {"shape": "String", "documentation": "<p>The token for the next set of results, or null if there are no more results.</p>", "location": "querystring", "locationName": "nextToken"}, "owner": {"shape": "String", "documentation": "<p>Filter this request to streaming images with the given owner</p>", "location": "querystring", "locationName": "owner"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "ListStreamingImagesResponse": {"type": "structure", "members": {"nextToken": {"shape": "String", "documentation": "<p>The token for the next set of results, or null if there are no more results.</p>"}, "streamingImages": {"shape": "StreamingImageList", "documentation": "<p>A collection of streaming images.</p>"}}}, "ListStreamingSessionBackupsRequest": {"type": "structure", "required": ["studioId"], "members": {"nextToken": {"shape": "String", "documentation": "<p>The token for the next set of results, or null if there are no more results.</p>", "location": "querystring", "locationName": "nextToken"}, "ownedBy": {"shape": "String", "documentation": "<p>The user ID of the user that owns the streaming session.</p>", "location": "querystring", "locationName": "ownedBy"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "ListStreamingSessionBackupsResponse": {"type": "structure", "members": {"nextToken": {"shape": "String", "documentation": "<p>The token for the next set of results, or null if there are no more results.</p>"}, "streamingSessionBackups": {"shape": "StreamingSessionBackupList", "documentation": "<p>Information about the streaming session backups.</p>"}}}, "ListStreamingSessionsRequest": {"type": "structure", "required": ["studioId"], "members": {"createdBy": {"shape": "String", "documentation": "<p>Filters the request to streaming sessions created by the given user.</p>", "location": "querystring", "locationName": "created<PERSON>y"}, "nextToken": {"shape": "String", "documentation": "<p>The token for the next set of results, or null if there are no more results.</p>", "location": "querystring", "locationName": "nextToken"}, "ownedBy": {"shape": "String", "documentation": "<p>Filters the request to streaming session owned by the given user</p>", "location": "querystring", "locationName": "ownedBy"}, "sessionIds": {"shape": "String", "documentation": "<p>Filters the request to only the provided session IDs.</p>", "location": "querystring", "locationName": "sessionIds"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "ListStreamingSessionsResponse": {"type": "structure", "members": {"nextToken": {"shape": "String", "documentation": "<p>The token for the next set of results, or null if there are no more results.</p>"}, "sessions": {"shape": "StreamingSessionList", "documentation": "<p>A collection of streaming sessions.</p>"}}}, "ListStudioComponentsRequest": {"type": "structure", "required": ["studioId"], "members": {"maxResults": {"shape": "MaxResults", "documentation": "<p>The max number of results to return in the response.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "String", "documentation": "<p>The token for the next set of results, or null if there are no more results.</p>", "location": "querystring", "locationName": "nextToken"}, "states": {"shape": "StudioComponentStateList", "documentation": "<p>Filters the request to studio components that are in one of the given states. </p>", "location": "querystring", "locationName": "states"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}, "types": {"shape": "StudioComponentTypeList", "documentation": "<p>Filters the request to studio components that are of one of the given types.</p>", "location": "querystring", "locationName": "types"}}}, "ListStudioComponentsResponse": {"type": "structure", "members": {"nextToken": {"shape": "String", "documentation": "<p>The token for the next set of results, or null if there are no more results.</p>"}, "studioComponents": {"shape": "StudioComponentList", "documentation": "<p>A collection of studio components.</p>"}}}, "ListStudioMembersRequest": {"type": "structure", "required": ["studioId"], "members": {"maxResults": {"shape": "MaxResults", "documentation": "<p>The max number of results to return in the response.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "String", "documentation": "<p>The token for the next set of results, or null if there are no more results.</p>", "location": "querystring", "locationName": "nextToken"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "ListStudioMembersResponse": {"type": "structure", "members": {"members": {"shape": "StudioMembershipList", "documentation": "<p>A list of admin members.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>The token for the next set of results, or null if there are no more results.</p>"}}}, "ListStudiosRequest": {"type": "structure", "members": {"nextToken": {"shape": "String", "documentation": "<p>The token for the next set of results, or null if there are no more results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListStudiosResponse": {"type": "structure", "required": ["studios"], "members": {"nextToken": {"shape": "String", "documentation": "<p>The token for the next set of results, or null if there are no more results.</p>"}, "studios": {"shape": "StudioList", "documentation": "<p>A collection of studios.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the resource for which you want to list tags.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "Tags", "documentation": "<p>A collection of labels, in the form of key-value pairs, that apply to this resource.</p>"}}}, "MaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "NewLaunchProfileMember": {"type": "structure", "required": ["persona", "principalId"], "members": {"persona": {"shape": "LaunchProfilePersona", "documentation": "<p>The persona.</p>"}, "principalId": {"shape": "String", "documentation": "<p>The principal ID.</p>"}}, "documentation": "<p>A new member that is added to a launch profile.</p>"}, "NewLaunchProfileMemberList": {"type": "list", "member": {"shape": "NewLaunchProfileMember"}, "max": 20, "min": 1}, "NewStudioMember": {"type": "structure", "required": ["persona", "principalId"], "members": {"persona": {"shape": "StudioPersona", "documentation": "<p>The persona.</p>"}, "principalId": {"shape": "String", "documentation": "<p>The principal ID.</p>"}}, "documentation": "<p>A new studio user's membership.</p>"}, "NewStudioMemberList": {"type": "list", "member": {"shape": "NewStudioMember"}, "max": 20, "min": 1}, "PutLaunchProfileMembersRequest": {"type": "structure", "required": ["identityStoreId", "launchProfileId", "members", "studioId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don’t specify a client token, the Amazon Web Services SDK automatically generates a client token and uses it for the request to ensure idempotency.</p>", "idempotencyToken": true, "location": "header", "locationName": "X-Amz-Client-Token"}, "identityStoreId": {"shape": "String", "documentation": "<p>The ID of the identity store.</p>"}, "launchProfileId": {"shape": "String", "documentation": "<p>The ID of the launch profile used to control access from the streaming session.</p>", "location": "uri", "locationName": "launchProfileId"}, "members": {"shape": "NewLaunchProfileMemberList", "documentation": "<p>A list of members.</p>"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "PutLaunchProfileMembersResponse": {"type": "structure", "members": {}}, "PutStudioMembersRequest": {"type": "structure", "required": ["identityStoreId", "members", "studioId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don’t specify a client token, the Amazon Web Services SDK automatically generates a client token and uses it for the request to ensure idempotency.</p>", "idempotencyToken": true, "location": "header", "locationName": "X-Amz-Client-Token"}, "identityStoreId": {"shape": "String", "documentation": "<p>The ID of the identity store.</p>"}, "members": {"shape": "NewStudioMemberList", "documentation": "<p>A list of members.</p>"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "PutStudioMembersResponse": {"type": "structure", "members": {}}, "Region": {"type": "string", "max": 50, "min": 0, "pattern": "[a-z]{2}-?(iso|gov)?-{1}[a-z]*-{1}[0-9]"}, "ResourceNotFoundException": {"type": "structure", "members": {"code": {"shape": "String", "documentation": "<p>A more specific error code.</p>"}, "context": {"shape": "ExceptionContext", "documentation": "<p>The exception context.</p>"}, "message": {"shape": "String", "documentation": "<p>A human-readable description of the error.</p>"}}, "documentation": "<p>The specified resource could not be found.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "RoleArn": {"type": "string", "max": 2048, "min": 0}, "ScriptParameterKey": {"type": "string", "documentation": "<p>A script parameter key.</p>", "max": 64, "min": 1, "pattern": "^[a-zA-Z_][a-zA-Z0-9_]+$"}, "ScriptParameterKeyValue": {"type": "structure", "members": {"key": {"shape": "ScriptParameterKey", "documentation": "<p>A script parameter key.</p>"}, "value": {"shape": "ScriptParameterValue", "documentation": "<p>A script parameter value.</p>"}}, "documentation": "<p>A parameter for a studio component script, in the form of a key-value pair.</p>"}, "ScriptParameterValue": {"type": "string", "documentation": "<p>A script parameter value.</p>", "max": 256, "min": 1}, "SecurityGroupId": {"type": "string"}, "SensitiveString": {"type": "string", "sensitive": true}, "ServiceQuotaExceededException": {"type": "structure", "members": {"code": {"shape": "String", "documentation": "<p>A more specific error code.</p>"}, "context": {"shape": "ExceptionContext", "documentation": "<p>The exception context.</p>"}, "message": {"shape": "String", "documentation": "<p>A human-readable description of the error.</p>"}}, "documentation": "<p>Your current quota does not allow you to perform the request action. You can request increases for some quotas, and other quotas cannot be increased.</p> <p>Please use Amazon Web Services Service Quotas to request an increase. </p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "SessionBackupMode": {"type": "string", "enum": ["AUTOMATIC", "DEACTIVATED"]}, "SessionPersistenceMode": {"type": "string", "enum": ["DEACTIVATED", "ACTIVATED"]}, "SharedFileSystemConfiguration": {"type": "structure", "members": {"endpoint": {"shape": "SensitiveString", "documentation": "<p>The endpoint of the shared file system that is accessed by the studio component resource.</p>"}, "fileSystemId": {"shape": "String", "documentation": "<p>The unique identifier for a file system.</p>"}, "linuxMountPoint": {"shape": "LinuxMountPoint", "documentation": "<p>The mount location for a shared file system on a Linux virtual workstation.</p>"}, "shareName": {"shape": "SensitiveString", "documentation": "<p>The name of the file share.</p>"}, "windowsMountDrive": {"shape": "WindowsMountDrive", "documentation": "<p>The mount location for a shared file system on a Windows virtual workstation.</p>"}}, "documentation": "<p>The configuration for a shared file storage system that is associated with a studio resource.</p>"}, "StartStreamingSessionRequest": {"type": "structure", "required": ["sessionId", "studioId"], "members": {"backupId": {"shape": "String", "documentation": "<p>The ID of the backup.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don’t specify a client token, the Amazon Web Services SDK automatically generates a client token and uses it for the request to ensure idempotency.</p>", "idempotencyToken": true, "location": "header", "locationName": "X-Amz-Client-Token"}, "sessionId": {"shape": "String", "documentation": "<p>The streaming session ID for the <code>StartStreamingSessionRequest</code>.</p>", "location": "uri", "locationName": "sessionId"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID for the StartStreamingSessionRequest.</p>", "location": "uri", "locationName": "studioId"}}}, "StartStreamingSessionResponse": {"type": "structure", "members": {"session": {"shape": "StreamingSession"}}}, "StartStudioSSOConfigurationRepairRequest": {"type": "structure", "required": ["studioId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don’t specify a client token, the Amazon Web Services SDK automatically generates a client token and uses it for the request to ensure idempotency.</p>", "idempotencyToken": true, "location": "header", "locationName": "X-Amz-Client-Token"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "StartStudioSSOConfigurationRepairResponse": {"type": "structure", "required": ["studio"], "members": {"studio": {"shape": "Studio", "documentation": "<p>Information about a studio.</p>"}}}, "StopStreamingSessionRequest": {"type": "structure", "required": ["sessionId", "studioId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don’t specify a client token, the Amazon Web Services SDK automatically generates a client token and uses it for the request to ensure idempotency.</p>", "idempotencyToken": true, "location": "header", "locationName": "X-Amz-Client-Token"}, "sessionId": {"shape": "String", "documentation": "<p>The streaming session ID for the <code>StopStreamingSessionRequest</code>.</p>", "location": "uri", "locationName": "sessionId"}, "studioId": {"shape": "String", "documentation": "<p>The studioId for the StopStreamingSessionRequest.</p>", "location": "uri", "locationName": "studioId"}, "volumeRetentionMode": {"shape": "VolumeRetentionMode", "documentation": "<p>Adds additional instructions to a streaming session stop action to either retain the EBS volumes or delete the EBS volumes.</p>"}}}, "StopStreamingSessionResponse": {"type": "structure", "members": {"session": {"shape": "StreamingSession"}}}, "StreamConfiguration": {"type": "structure", "required": ["clipboardMode", "ec2InstanceTypes", "streamingImageIds"], "members": {"automaticTerminationMode": {"shape": "AutomaticTerminationMode", "documentation": "<p>Indicates if a streaming session created from this launch profile should be terminated automatically or retained without termination after being in a <code>STOPPED</code> state.</p> <ul> <li> <p>When <code>ACTIVATED</code>, the streaming session is scheduled for termination after being in the <code>STOPPED</code> state for the time specified in <code>maxStoppedSessionLengthInMinutes</code>.</p> </li> <li> <p>When <code>DEACTIVATED</code>, the streaming session can remain in the <code>STOPPED</code> state indefinitely.</p> </li> </ul> <p>This parameter is only allowed when <code>sessionPersistenceMode</code> is <code>ACTIVATED</code>. When allowed, the default value for this parameter is <code>DEACTIVATED</code>.</p>"}, "clipboardMode": {"shape": "StreamingClipboardMode", "documentation": "<p>Allows or deactivates the use of the system clipboard to copy and paste between the streaming session and streaming client.</p>"}, "ec2InstanceTypes": {"shape": "StreamingInstanceTypeList", "documentation": "<p>The EC2 instance types that users can select from when launching a streaming session with this launch profile.</p>"}, "maxSessionLengthInMinutes": {"shape": "StreamConfigurationMaxSessionLengthInMinutes", "documentation": "<p>The length of time, in minutes, that a streaming session can be active before it is stopped or terminated. After this point, Nimble Studio automatically terminates or stops the session. The default length of time is 690 minutes, and the maximum length of time is 30 days.</p>"}, "maxStoppedSessionLengthInMinutes": {"shape": "StreamConfigurationMaxStoppedSessionLengthInMinutes", "documentation": "<p>Integer that determines if you can start and stop your sessions and how long a session can stay in the <code>STOPPED</code> state. The default value is 0. The maximum value is 5760.</p> <p>This field is allowed only when <code>sessionPersistenceMode</code> is <code>ACTIVATED</code> and <code>automaticTerminationMode</code> is <code>ACTIVATED</code>.</p> <p>If the value is set to 0, your sessions can’t be <code>STOPPED</code>. If you then call <code>StopStreamingSession</code>, the session fails. If the time that a session stays in the <code>READY</code> state exceeds the <code>maxSessionLengthInMinutes</code> value, the session will automatically be terminated (instead of <code>STOPPED</code>).</p> <p>If the value is set to a positive number, the session can be stopped. You can call <code>StopStreamingSession</code> to stop sessions in the <code>READY</code> state. If the time that a session stays in the <code>READY</code> state exceeds the <code>maxSessionLengthInMinutes</code> value, the session will automatically be stopped (instead of terminated).</p>"}, "sessionBackup": {"shape": "StreamConfigurationSessionBackup", "documentation": "<p>Information about the streaming session backup.</p>"}, "sessionPersistenceMode": {"shape": "SessionPersistenceMode", "documentation": "<p>Determine if a streaming session created from this launch profile can configure persistent storage. This means that <code>volumeConfiguration</code> and <code>automaticTerminationMode</code> are configured.</p>"}, "sessionStorage": {"shape": "StreamConfigurationSessionStorage", "documentation": "<p>The upload storage for a streaming session.</p>"}, "streamingImageIds": {"shape": "StreamingImageIdList", "documentation": "<p>The streaming images that users can select from when launching a streaming session with this launch profile.</p>"}, "volumeConfiguration": {"shape": "VolumeConfiguration", "documentation": "<p>Custom volume configuration for the root volumes that are attached to streaming sessions.</p> <p>This parameter is only allowed when <code>sessionPersistenceMode</code> is <code>ACTIVATED</code>.</p>"}}, "documentation": "<p>A configuration for a streaming session.</p>"}, "StreamConfigurationCreate": {"type": "structure", "required": ["clipboardMode", "ec2InstanceTypes", "streamingImageIds"], "members": {"automaticTerminationMode": {"shape": "AutomaticTerminationMode", "documentation": "<p>Indicates if a streaming session created from this launch profile should be terminated automatically or retained without termination after being in a <code>STOPPED</code> state.</p> <ul> <li> <p>When <code>ACTIVATED</code>, the streaming session is scheduled for termination after being in the <code>STOPPED</code> state for the time specified in <code>maxStoppedSessionLengthInMinutes</code>.</p> </li> <li> <p>When <code>DEACTIVATED</code>, the streaming session can remain in the <code>STOPPED</code> state indefinitely.</p> </li> </ul> <p>This parameter is only allowed when <code>sessionPersistenceMode</code> is <code>ACTIVATED</code>. When allowed, the default value for this parameter is <code>DEACTIVATED</code>.</p>"}, "clipboardMode": {"shape": "StreamingClipboardMode", "documentation": "<p>Allows or deactivates the use of the system clipboard to copy and paste between the streaming session and streaming client.</p>"}, "ec2InstanceTypes": {"shape": "StreamingInstanceTypeList", "documentation": "<p>The EC2 instance types that users can select from when launching a streaming session with this launch profile.</p>"}, "maxSessionLengthInMinutes": {"shape": "StreamConfigurationMaxSessionLengthInMinutes", "documentation": "<p>The length of time, in minutes, that a streaming session can be active before it is stopped or terminated. After this point, Nimble Studio automatically terminates or stops the session. The default length of time is 690 minutes, and the maximum length of time is 30 days.</p>"}, "maxStoppedSessionLengthInMinutes": {"shape": "StreamConfigurationMaxStoppedSessionLengthInMinutes", "documentation": "<p>Integer that determines if you can start and stop your sessions and how long a session can stay in the <code>STOPPED</code> state. The default value is 0. The maximum value is 5760.</p> <p>This field is allowed only when <code>sessionPersistenceMode</code> is <code>ACTIVATED</code> and <code>automaticTerminationMode</code> is <code>ACTIVATED</code>.</p> <p>If the value is set to 0, your sessions can’t be <code>STOPPED</code>. If you then call <code>StopStreamingSession</code>, the session fails. If the time that a session stays in the <code>READY</code> state exceeds the <code>maxSessionLengthInMinutes</code> value, the session will automatically be terminated (instead of <code>STOPPED</code>).</p> <p>If the value is set to a positive number, the session can be stopped. You can call <code>StopStreamingSession</code> to stop sessions in the <code>READY</code> state. If the time that a session stays in the <code>READY</code> state exceeds the <code>maxSessionLengthInMinutes</code> value, the session will automatically be stopped (instead of terminated).</p>"}, "sessionBackup": {"shape": "StreamConfigurationSessionBackup", "documentation": "<p>Configures how streaming sessions are backed up when launched from this launch profile.</p>"}, "sessionPersistenceMode": {"shape": "SessionPersistenceMode", "documentation": "<p>Determine if a streaming session created from this launch profile can configure persistent storage. This means that <code>volumeConfiguration</code> and <code>automaticTerminationMode</code> are configured.</p>"}, "sessionStorage": {"shape": "StreamConfigurationSessionStorage", "documentation": "<p>The upload storage for a streaming workstation that is created using this launch profile.</p>"}, "streamingImageIds": {"shape": "StreamingImageIdList", "documentation": "<p>The streaming images that users can select from when launching a streaming session with this launch profile.</p>"}, "volumeConfiguration": {"shape": "VolumeConfiguration", "documentation": "<p>Custom volume configuration for the root volumes that are attached to streaming sessions.</p> <p>This parameter is only allowed when <code>sessionPersistenceMode</code> is <code>ACTIVATED</code>.</p>"}}, "documentation": "<p>Configuration for streaming workstations created using this launch profile.</p>"}, "StreamConfigurationMaxBackupsToRetain": {"type": "integer", "max": 10, "min": 0}, "StreamConfigurationMaxSessionLengthInMinutes": {"type": "integer", "box": true, "max": 43200, "min": 1}, "StreamConfigurationMaxStoppedSessionLengthInMinutes": {"type": "integer", "max": 5760, "min": 0}, "StreamConfigurationSessionBackup": {"type": "structure", "members": {"maxBackupsToRetain": {"shape": "StreamConfigurationMaxBackupsToRetain", "documentation": "<p>The maximum number of backups that each streaming session created from this launch profile can have.</p>"}, "mode": {"shape": "SessionBackupMode", "documentation": "<p>Specifies how artists sessions are backed up.</p> <p>Configures backups for streaming sessions launched with this launch profile. The default value is <code>DEACTIVATED</code>, which means that backups are deactivated. To allow backups, set this value to <code>AUTOMATIC</code>.</p>"}}, "documentation": "<p>Configures how streaming sessions are backed up when launched from this launch profile.</p>"}, "StreamConfigurationSessionStorage": {"type": "structure", "required": ["mode"], "members": {"mode": {"shape": "StreamingSessionStorageModeList", "documentation": "<p>Allows artists to upload files to their workstations. The only valid option is <code>UPLOAD</code>.</p>"}, "root": {"shape": "StreamingSessionStorageRoot", "documentation": "<p>The configuration for the upload storage root of the streaming session.</p>"}}, "documentation": "<p>The configuration for a streaming session’s upload storage.</p>"}, "StreamingClipboardMode": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "StreamingImage": {"type": "structure", "members": {"arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) that is assigned to a studio resource and uniquely identifies it. ARNs are unique across all Regions.</p>"}, "description": {"shape": "StreamingImageDescription", "documentation": "<p>A human-readable description of the streaming image.</p>"}, "ec2ImageId": {"shape": "EC2ImageId", "documentation": "<p>The ID of an EC2 machine image with which to create the streaming image.</p>"}, "encryptionConfiguration": {"shape": "StreamingImageEncryptionConfiguration", "documentation": "<p>The encryption configuration.</p>"}, "eulaIds": {"shape": "EulaIdList", "documentation": "<p>The list of EULAs that must be accepted before a Streaming Session can be started using this streaming image.</p>"}, "name": {"shape": "StreamingImageName", "documentation": "<p>A friendly name for a streaming image resource.</p>"}, "owner": {"shape": "StreamingImageOwner", "documentation": "<p>The owner of the streaming image, either the <code>studioId</code> that contains the streaming image, or <code>amazon</code> for images that are provided by Amazon Nimble Studio.</p>"}, "platform": {"shape": "StreamingImagePlatform", "documentation": "<p>The platform of the streaming image, either Windows or Linux.</p>"}, "state": {"shape": "StreamingImageState", "documentation": "<p>The current state.</p>"}, "statusCode": {"shape": "StreamingImageStatusCode", "documentation": "<p>The status code.</p>"}, "statusMessage": {"shape": "String", "documentation": "<p>The status message for the streaming image.</p>"}, "streamingImageId": {"shape": "StreamingImageId", "documentation": "<p>The ID of the streaming image.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A collection of labels, in the form of key-value pairs, that apply to this resource.</p>"}}, "documentation": "<p>Represents a streaming image resource.</p> <p>Streaming images are used by studio users to select which operating system and software they want to use in a Nimble Studio streaming session.</p> <p>Amazon provides a number of streaming images that include popular 3rd-party software.</p> <p>You can create your own streaming images using an Amazon EC2 machine image that you create for this purpose. You can also include software that your users require.</p>"}, "StreamingImageDescription": {"type": "string", "documentation": "<p>The description.</p>", "max": 256, "min": 0, "sensitive": true}, "StreamingImageEncryptionConfiguration": {"type": "structure", "required": ["keyType"], "members": {"keyArn": {"shape": "StreamingImageEncryptionConfigurationKeyArn", "documentation": "<p>The ARN for a KMS key that is used to encrypt studio data.</p>"}, "keyType": {"shape": "StreamingImageEncryptionConfigurationKeyType", "documentation": "<p>The type of KMS key that is used to encrypt studio data.</p>"}}, "documentation": "<p>Specifies how a streaming image is encrypted.</p>"}, "StreamingImageEncryptionConfigurationKeyArn": {"type": "string", "min": 4, "pattern": "^arn:.*"}, "StreamingImageEncryptionConfigurationKeyType": {"type": "string", "enum": ["CUSTOMER_MANAGED_KEY"]}, "StreamingImageId": {"type": "string", "max": 22, "min": 0, "pattern": "^[a-zA-Z0-9-_]*$"}, "StreamingImageIdList": {"type": "list", "member": {"shape": "StreamingImageId"}, "documentation": "<p>A list of streaming image IDs that users can select from when launching a streaming session with this launch profile.</p>", "max": 20, "min": 1}, "StreamingImageList": {"type": "list", "member": {"shape": "StreamingImage"}}, "StreamingImageName": {"type": "string", "documentation": "<p>A friendly name for a streaming image resource.</p>", "max": 64, "min": 0, "sensitive": true}, "StreamingImageOwner": {"type": "string", "documentation": "<p>StreamingImageOwner is the owner of a particular streaming image.</p> <p>This string is either the studioId that contains the streaming image, or the word <code>AMAZON</code> for images provided by Nimble Studio.</p>"}, "StreamingImagePlatform": {"type": "string", "documentation": "<p>The platform of this streaming image, either Windows or Linux.</p>", "pattern": "^[a-zA-Z]*$"}, "StreamingImageState": {"type": "string", "enum": ["CREATE_IN_PROGRESS", "READY", "DELETE_IN_PROGRESS", "DELETED", "UPDATE_IN_PROGRESS", "UPDATE_FAILED", "CREATE_FAILED", "DELETE_FAILED"]}, "StreamingImageStatusCode": {"type": "string", "documentation": "<p>The status code.</p>", "enum": ["STREAMING_IMAGE_CREATE_IN_PROGRESS", "STREAMING_IMAGE_READY", "STREAMING_IMAGE_DELETE_IN_PROGRESS", "STREAMING_IMAGE_DELETED", "STREAMING_IMAGE_UPDATE_IN_PROGRESS", "INTERNAL_ERROR", "ACCESS_DENIED"]}, "StreamingInstanceType": {"type": "string", "enum": ["g4dn.xlarge", "g4dn.2xlarge", "g4dn.4xlarge", "g4dn.8xlarge", "g4dn.12xlarge", "g4dn.16xlarge", "g3.4xlarge", "g3s.xlarge", "g5.xlarge", "g5.2xlarge", "g5.4xlarge", "g5.8xlarge", "g5.16xlarge"]}, "StreamingInstanceTypeList": {"type": "list", "member": {"shape": "StreamingInstanceType"}, "documentation": "<p>The EC2 instance types that users can select from when launching a streaming session with this launch profile.</p>", "max": 30, "min": 1}, "StreamingSession": {"type": "structure", "members": {"arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) that is assigned to a studio resource and uniquely identifies it. ARNs are unique across all Regions.</p>"}, "automaticTerminationMode": {"shape": "AutomaticTerminationMode", "documentation": "<p>Indicates if a streaming session created from this launch profile should be terminated automatically or retained without termination after being in a <code>STOPPED</code> state.</p> <ul> <li> <p>When <code>ACTIVATED</code>, the streaming session is scheduled for termination after being in the <code>STOPPED</code> state for the time specified in <code>maxStoppedSessionLengthInMinutes</code>.</p> </li> <li> <p>When <code>DEACTIVATED</code>, the streaming session can remain in the <code>STOPPED</code> state indefinitely.</p> </li> </ul> <p>This parameter is only allowed when <code>sessionPersistenceMode</code> is <code>ACTIVATED</code>. When allowed, the default value for this parameter is <code>DEACTIVATED</code>.</p>"}, "backupMode": {"shape": "SessionBackupMode", "documentation": "<p>Shows the current backup setting of the session.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The ISO timestamp in seconds for when the resource was created.</p>"}, "createdBy": {"shape": "String", "documentation": "<p>The user ID of the user that created the streaming session.</p>"}, "ec2InstanceType": {"shape": "String", "documentation": "<p>The EC2 Instance type used for the streaming session.</p>"}, "launchProfileId": {"shape": "String", "documentation": "<p>The ID of the launch profile used to control access from the streaming session.</p>"}, "maxBackupsToRetain": {"shape": "StreamConfigurationMaxBackupsToRetain", "documentation": "<p>The maximum number of backups of a streaming session that you can have. When the maximum number of backups is reached, the oldest backup is deleted.</p>"}, "ownedBy": {"shape": "String", "documentation": "<p>The user ID of the user that owns the streaming session. The user that owns the session will be logging into the session and interacting with the virtual workstation.</p>"}, "sessionId": {"shape": "StreamingSessionId", "documentation": "<p>The session ID.</p>"}, "sessionPersistenceMode": {"shape": "SessionPersistenceMode", "documentation": "<p>Determine if a streaming session created from this launch profile can configure persistent storage. This means that <code>volumeConfiguration</code> and <code>automaticTerminationMode</code> are configured.</p>"}, "startedAt": {"shape": "Timestamp", "documentation": "<p>The time the session entered <code>START_IN_PROGRESS</code> state.</p>"}, "startedBy": {"shape": "String", "documentation": "<p>The user ID of the user that started the streaming session.</p>"}, "startedFromBackupId": {"shape": "String", "documentation": "<p>The backup ID used to restore a streaming session.</p>"}, "state": {"shape": "StreamingSessionState", "documentation": "<p>The current state.</p>"}, "statusCode": {"shape": "StreamingSessionStatusCode", "documentation": "<p>The status code.</p>"}, "statusMessage": {"shape": "String", "documentation": "<p>The status message for the streaming session.</p>"}, "stopAt": {"shape": "Timestamp", "documentation": "<p>The time the streaming session will automatically be stopped if the user doesn’t stop the session themselves. </p>"}, "stoppedAt": {"shape": "Timestamp", "documentation": "<p>The time the session entered <code>STOP_IN_PROGRESS</code> state.</p>"}, "stoppedBy": {"shape": "String", "documentation": "<p>The user ID of the user that stopped the streaming session.</p>"}, "streamingImageId": {"shape": "StreamingImageId", "documentation": "<p>The ID of the streaming image.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A collection of labels, in the form of key-value pairs, that apply to this resource.</p>"}, "terminateAt": {"shape": "Timestamp", "documentation": "<p>The time the streaming session will automatically terminate if not terminated by the user.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The ISO timestamp in seconds for when the resource was updated.</p>"}, "updatedBy": {"shape": "String", "documentation": "<p>The user ID of the user that most recently updated the resource.</p>"}, "volumeConfiguration": {"shape": "VolumeConfiguration", "documentation": "<p>Custom volume configuration for the root volumes that are attached to streaming sessions.</p> <p>This parameter is only allowed when <code>sessionPersistenceMode</code> is <code>ACTIVATED</code>.</p>"}, "volumeRetentionMode": {"shape": "VolumeRetentionMode", "documentation": "<p>Determine if an EBS volume created from this streaming session will be backed up.</p>"}}, "documentation": "<p>A streaming session is a virtual workstation created using a particular launch profile.</p>"}, "StreamingSessionBackup": {"type": "structure", "members": {"arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) that is assigned to a studio resource and uniquely identifies it. ARNs are unique across all Regions.</p>"}, "backupId": {"shape": "String", "documentation": "<p>The ID of the backup.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The ISO timestamp in for when the resource was created.</p>"}, "launchProfileId": {"shape": "String", "documentation": "<p>The ID of the launch profile which allowed the backups for the streaming session.</p>"}, "ownedBy": {"shape": "String", "documentation": "<p>The user ID of the user that owns the streaming session.</p>"}, "sessionId": {"shape": "StreamingSessionId", "documentation": "<p>The streaming session ID for the <code>StreamingSessionBackup</code>.</p>"}, "state": {"shape": "StreamingSessionState"}, "statusCode": {"shape": "StreamingSessionStatusCode", "documentation": "<p>The status code.</p>"}, "statusMessage": {"shape": "String", "documentation": "<p>The status message for the streaming session backup.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A collection of labels, in the form of key-value pairs, that apply to this resource.</p>"}}, "documentation": "<p>Information about the streaming session backup.</p>"}, "StreamingSessionBackupList": {"type": "list", "member": {"shape": "StreamingSessionBackup"}}, "StreamingSessionId": {"type": "string"}, "StreamingSessionList": {"type": "list", "member": {"shape": "StreamingSession"}}, "StreamingSessionState": {"type": "string", "documentation": "<p>The streaming session state.</p>", "enum": ["CREATE_IN_PROGRESS", "DELETE_IN_PROGRESS", "READY", "DELETED", "CREATE_FAILED", "DELETE_FAILED", "STOP_IN_PROGRESS", "START_IN_PROGRESS", "STOPPED", "STOP_FAILED", "START_FAILED"]}, "StreamingSessionStatusCode": {"type": "string", "enum": ["STREAMING_SESSION_READY", "STREAMING_SESSION_DELETED", "STREAMING_SESSION_CREATE_IN_PROGRESS", "STREAMING_SESSION_DELETE_IN_PROGRESS", "INTERNAL_ERROR", "INSUFFICIENT_CAPACITY", "ACTIVE_DIRECTORY_DOMAIN_JOIN_ERROR", "NETWORK_CONNECTION_ERROR", "INITIALIZATION_SCRIPT_ERROR", "DECRYPT_STREAMING_IMAGE_ERROR", "NETWORK_INTERFACE_ERROR", "STREAMING_SESSION_STOPPED", "STREAMING_SESSION_STARTED", "STREAMING_SESSION_STOP_IN_PROGRESS", "STREAMING_SESSION_START_IN_PROGRESS", "AMI_VALIDATION_ERROR"]}, "StreamingSessionStorageMode": {"type": "string", "enum": ["UPLOAD"]}, "StreamingSessionStorageModeList": {"type": "list", "member": {"shape": "StreamingSessionStorageMode"}, "min": 1}, "StreamingSessionStorageRoot": {"type": "structure", "members": {"linux": {"shape": "StreamingSessionStorageRootPathLinux", "documentation": "<p>The folder path in Linux workstations where files are uploaded.</p>"}, "windows": {"shape": "StreamingSessionStorageRootPathWindows", "documentation": "<p>The folder path in Windows workstations where files are uploaded.</p>"}}, "documentation": "<p>The upload storage root location (folder) on streaming workstations where files are uploaded.</p>"}, "StreamingSessionStorageRootPathLinux": {"type": "string", "max": 128, "min": 1, "pattern": "^(\\$HOME|/)[/]?([A-Za-z0-9-_]+/)*([A-Za-z0-9_-]+)$", "sensitive": true}, "StreamingSessionStorageRootPathWindows": {"type": "string", "max": 128, "min": 1, "pattern": "^((\\%HOMEPATH\\%)|[a-zA-Z]:)[\\\\/](?:[a-zA-Z0-9_-]+[\\\\/])*[a-zA-Z0-9_-]+$", "sensitive": true}, "StreamingSessionStream": {"type": "structure", "members": {"createdAt": {"shape": "Timestamp", "documentation": "<p>The ISO timestamp in seconds for when the resource was created.</p>"}, "createdBy": {"shape": "String", "documentation": "<p>The user ID of the user that created the streaming session stream.</p>"}, "expiresAt": {"shape": "Timestamp", "documentation": "<p>The ISO timestamp in seconds for when the resource expires.</p>"}, "ownedBy": {"shape": "String", "documentation": "<p>The user ID of the user that owns the streaming session. The user that owns the session will be logging into the session and interacting with the virtual workstation.</p>"}, "state": {"shape": "StreamingSessionStreamState", "documentation": "<p>The current state.</p>"}, "statusCode": {"shape": "StreamingSessionStreamStatusCode", "documentation": "<p>The streaming session stream status code.</p>"}, "streamId": {"shape": "String", "documentation": "<p>The stream ID.</p>"}, "url": {"shape": "SensitiveString", "documentation": "<p>The URL to connect to this stream using the DCV client.</p>"}}, "documentation": "<p>A stream is an active connection to a streaming session, enabling a studio user to control the streaming session using a compatible client. Streaming session streams are compatible with the NICE DCV web client, included in the Nimble Studio portal, or the NICE DCV desktop client.</p>"}, "StreamingSessionStreamExpirationInSeconds": {"type": "integer", "box": true, "max": 3600, "min": 60}, "StreamingSessionStreamState": {"type": "string", "enum": ["READY", "CREATE_IN_PROGRESS", "DELETE_IN_PROGRESS", "DELETED", "CREATE_FAILED", "DELETE_FAILED"]}, "StreamingSessionStreamStatusCode": {"type": "string", "enum": ["STREAM_CREATE_IN_PROGRESS", "STREAM_READY", "STREAM_DELETE_IN_PROGRESS", "STREAM_DELETED", "INTERNAL_ERROR", "NETWORK_CONNECTION_ERROR"]}, "String": {"type": "string"}, "StringList": {"type": "list", "member": {"shape": "String"}}, "Studio": {"type": "structure", "members": {"adminRoleArn": {"shape": "RoleArn", "documentation": "<p>The IAM role that studio admins assume when logging in to the Nimble Studio portal.</p>"}, "arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) that is assigned to a studio resource and uniquely identifies it. ARNs are unique across all Regions.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The ISO timestamp in seconds for when the resource was created.</p>"}, "displayName": {"shape": "StudioDisplayName", "documentation": "<p>A friendly name for the studio.</p>"}, "homeRegion": {"shape": "Region", "documentation": "<p>The Amazon Web Services Region where the studio resource is located.</p>"}, "ssoClientId": {"shape": "String", "documentation": "<p>The IAM Identity Center application client ID used to integrate with IAM Identity Center. This ID allows IAM Identity Center users to log in to Nimble Studio portal.</p>"}, "state": {"shape": "StudioState", "documentation": "<p>The current state of the studio resource.</p>"}, "statusCode": {"shape": "StudioStatusCode", "documentation": "<p>Status codes that provide additional detail on the studio state.</p>"}, "statusMessage": {"shape": "String", "documentation": "<p>Additional detail on the studio state.</p>"}, "studioEncryptionConfiguration": {"shape": "StudioEncryptionConfiguration", "documentation": "<p>Configuration of the encryption method that is used for the studio.</p>"}, "studioId": {"shape": "String", "documentation": "<p>The unique identifier for a studio resource. In Nimble Studio, all other resources are contained in a studio resource.</p>"}, "studioName": {"shape": "StudioName", "documentation": "<p>The name of the studio, as included in the URL when accessing it in the Nimble Studio portal.</p>"}, "studioUrl": {"shape": "String", "documentation": "<p>The address of the web page for the studio.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A collection of labels, in the form of key-value pairs, that apply to this resource.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The ISO timestamp in seconds for when the resource was updated.</p>"}, "userRoleArn": {"shape": "RoleArn", "documentation": "<p>The IAM role that studio users assume when logging in to the Nimble Studio portal.</p>"}}, "documentation": "<p>Represents a studio resource.</p> <p>A studio is the core resource used with Nimble Studio. You must create a studio first, before any other resource type can be created. All other resources you create and manage in Nimble Studio are contained within a studio.</p> <p>When creating a studio, you must provides two IAM roles for use with the Nimble Studio portal. These roles are assumed by your users when they log in to the Nimble Studio portal via IAM Identity Center and your identity source.</p> <p>The user role must have the <code>AmazonNimbleStudio-StudioUser</code> managed policy attached for the portal to function properly.</p> <p>The admin role must have the <code>AmazonNimbleStudio-StudioAdmin</code> managed policy attached for the portal to function properly.</p> <p>Your studio roles must trust the <code>identity.nimble.amazonaws.com</code> service principal to function properly.</p>"}, "StudioComponent": {"type": "structure", "members": {"arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) that is assigned to a studio resource and uniquely identifies it. ARNs are unique across all Regions.</p>"}, "configuration": {"shape": "StudioComponentConfiguration", "documentation": "<p>The configuration of the studio component, based on component type.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The ISO timestamp in seconds for when the resource was created.</p>"}, "createdBy": {"shape": "String", "documentation": "<p>The user ID of the user that created the studio component.</p>"}, "description": {"shape": "StudioComponentDescription", "documentation": "<p>A human-readable description for the studio component resource.</p>"}, "ec2SecurityGroupIds": {"shape": "StudioComponentSecurityGroupIdList", "documentation": "<p>The EC2 security groups that control access to the studio component.</p>"}, "initializationScripts": {"shape": "StudioComponentInitializationScriptList", "documentation": "<p>Initialization scripts for studio components.</p>"}, "name": {"shape": "StudioComponentName", "documentation": "<p>A friendly name for the studio component resource.</p>"}, "runtimeRoleArn": {"shape": "RoleArn", "documentation": "<p>An IAM role attached to a Studio Component that gives the studio component access to Amazon Web Services resources at anytime while the instance is running. </p>"}, "scriptParameters": {"shape": "StudioComponentScriptParameterKeyValueList", "documentation": "<p>Parameters for the studio component scripts.</p>"}, "secureInitializationRoleArn": {"shape": "RoleArn", "documentation": "<p>An IAM role attached to Studio Component when the system initialization script runs which give the studio component access to Amazon Web Services resources when the system initialization script runs.</p>"}, "state": {"shape": "StudioComponentState", "documentation": "<p>The current state.</p>"}, "statusCode": {"shape": "StudioComponentStatusCode", "documentation": "<p>The status code.</p>"}, "statusMessage": {"shape": "String", "documentation": "<p>The status message for the studio component.</p>"}, "studioComponentId": {"shape": "StudioComponentId", "documentation": "<p>The unique identifier for a studio component resource.</p>"}, "subtype": {"shape": "StudioComponentSubtype", "documentation": "<p>The specific subtype of a studio component.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A collection of labels, in the form of key-value pairs, that apply to this resource.</p>"}, "type": {"shape": "StudioComponentType", "documentation": "<p>The type of the studio component.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The ISO timestamp in seconds for when the resource was updated.</p>"}, "updatedBy": {"shape": "String", "documentation": "<p>The user ID of the user that most recently updated the resource.</p>"}}, "documentation": "<p>A studio component represents a network resource to be used by a studio's users and workflows. A typical studio contains studio components for each of the following: render farm, Active Directory, licensing, and file system.</p> <p>Access to a studio component is managed by specifying security groups for the resource, as well as its endpoint.</p> <p>A studio component also has a set of initialization scripts that are returned by <code>GetLaunchProfileInitialization</code>. These initialization scripts run on streaming sessions when they start. They provide users with flexibility in controlling how the studio resources are configured on a streaming session.</p>"}, "StudioComponentConfiguration": {"type": "structure", "members": {"activeDirectoryConfiguration": {"shape": "ActiveDirectoryConfiguration", "documentation": "<p>The configuration for a Directory Service for Microsoft Active Directory studio resource.</p>"}, "computeFarmConfiguration": {"shape": "ComputeFarmConfiguration", "documentation": "<p>The configuration for a render farm that is associated with a studio resource.</p>"}, "licenseServiceConfiguration": {"shape": "LicenseServiceConfiguration", "documentation": "<p>The configuration for a license service that is associated with a studio resource.</p>"}, "sharedFileSystemConfiguration": {"shape": "SharedFileSystemConfiguration", "documentation": "<p>The configuration for a shared file storage system that is associated with a studio resource.</p>"}}, "documentation": "<p>The configuration of the studio component, based on component type.</p>", "union": true}, "StudioComponentDescription": {"type": "string", "max": 256, "min": 0, "sensitive": true}, "StudioComponentId": {"type": "string", "max": 22, "min": 0, "pattern": "^[a-zA-Z0-9-_]*$"}, "StudioComponentInitializationScript": {"type": "structure", "members": {"launchProfileProtocolVersion": {"shape": "LaunchProfileProtocolVersion", "documentation": "<p>The version number of the protocol that is used by the launch profile. The only valid version is \"2021-03-31\".</p>"}, "platform": {"shape": "LaunchProfilePlatform", "documentation": "<p>The platform of the initialization script, either Windows or Linux.</p>"}, "runContext": {"shape": "StudioComponentInitializationScriptRunContext", "documentation": "<p>The method to use when running the initialization script.</p>"}, "script": {"shape": "StudioComponentInitializationScriptContent", "documentation": "<p>The initialization script.</p>"}}, "documentation": "<p>Initialization scripts for studio components.</p>"}, "StudioComponentInitializationScriptContent": {"type": "string", "max": 5120, "min": 1, "sensitive": true}, "StudioComponentInitializationScriptList": {"type": "list", "member": {"shape": "StudioComponentInitializationScript"}}, "StudioComponentInitializationScriptRunContext": {"type": "string", "enum": ["SYSTEM_INITIALIZATION", "USER_INITIALIZATION"]}, "StudioComponentList": {"type": "list", "member": {"shape": "StudioComponent"}, "max": 50, "min": 0}, "StudioComponentName": {"type": "string", "max": 64, "min": 0, "sensitive": true}, "StudioComponentScriptParameterKeyValueList": {"type": "list", "member": {"shape": "ScriptParameterKeyValue"}, "max": 30, "min": 0, "sensitive": true}, "StudioComponentSecurityGroupIdList": {"type": "list", "member": {"shape": "SecurityGroupId"}, "max": 30, "min": 0}, "StudioComponentState": {"type": "string", "documentation": "<p>The current state of the studio component resource.</p> <p>While a studio component is being created, modified, or deleted, its state will be <code>CREATE_IN_PROGRESS</code>, <code>UPDATE_IN_PROGRESS</code>, or <code>DELETE_IN_PROGRESS</code>.</p> <p>These are called <i>transition states</i>.</p> <p>No modifications may be made to the studio component while it is in a transition state.</p> <p>If creation of the resource fails, the state will change to <code>CREATE_FAILED</code>. The resource <code>StatusCode</code> and <code>StatusMessage</code> will provide more information of why creation failed. The resource in this state will automatically be deleted from your account after a period of time.</p> <p>If updating the resource fails, the state will change to <code>UPDATE_FAILED</code>. The resource <code>StatusCode</code> and <code>StatusMessage</code> will provide more information of why the update failed. The resource will be returned to the state it was in when the update request was invoked.</p> <p>If deleting the resource fails, the state will change to <code>DELETE_FAILED</code>. The resource <code>StatusCode</code> and <code>StatusMessage</code> will provide more information of why the update failed. The resource will be returned to the state it was in when the update request was invoked. After the resource is deleted successfully, it will change to the <code>DELETED</code> state. The resource will no longer count against service quotas and cannot be used or acted upon any futher. It will be removed from your account after a period of time.</p>", "enum": ["CREATE_IN_PROGRESS", "READY", "UPDATE_IN_PROGRESS", "DELETE_IN_PROGRESS", "DELETED", "DELETE_FAILED", "CREATE_FAILED", "UPDATE_FAILED"]}, "StudioComponentStateList": {"type": "list", "member": {"shape": "StudioComponentState"}}, "StudioComponentStatusCode": {"type": "string", "documentation": "<p>The current status of the studio component resource.</p> <p>When the resource is in the <code>READY</code> state, the status code signals what the last mutation made to the resource was.</p> <p>When the resource is in a <code>CREATE_FAILED</code>, <code>UPDATE_FAILED</code>, or <code>DELETE_FAILED</code> state, the status code signals what went wrong and why the mutation failed.</p>", "enum": ["ACTIVE_DIRECTORY_ALREADY_EXISTS", "STUDIO_COMPONENT_CREATED", "STUDIO_COMPONENT_UPDATED", "STUDIO_COMPONENT_DELETED", "ENCRYPTION_KEY_ACCESS_DENIED", "ENCRYPTION_KEY_NOT_FOUND", "STUDIO_COMPONENT_CREATE_IN_PROGRESS", "STUDIO_COMPONENT_UPDATE_IN_PROGRESS", "STUDIO_COMPONENT_DELETE_IN_PROGRESS", "INTERNAL_ERROR"]}, "StudioComponentSubtype": {"type": "string", "enum": ["AWS_MANAGED_MICROSOFT_AD", "AMAZON_FSX_FOR_WINDOWS", "AMAZON_FSX_FOR_LUSTRE", "CUSTOM"]}, "StudioComponentSummary": {"type": "structure", "members": {"createdAt": {"shape": "Timestamp", "documentation": "<p>The ISO timestamp in seconds for when the resource was created.</p>"}, "createdBy": {"shape": "String", "documentation": "<p>The user ID of the user that created the studio component.</p>"}, "description": {"shape": "StudioComponentDescription", "documentation": "<p>The description.</p>"}, "name": {"shape": "StudioComponentName", "documentation": "<p>The name for the studio component.</p>"}, "studioComponentId": {"shape": "StudioComponentId", "documentation": "<p>The unique identifier for a studio component resource.</p>"}, "subtype": {"shape": "StudioComponentSubtype", "documentation": "<p>The specific subtype of a studio component.</p>"}, "type": {"shape": "StudioComponentType", "documentation": "<p>The type of the studio component.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The ISO timestamp in seconds for when the resource was updated.</p>"}, "updatedBy": {"shape": "String", "documentation": "<p>The user ID of the user that most recently updated the resource.</p>"}}, "documentation": "<p>The studio component's summary.</p>"}, "StudioComponentSummaryList": {"type": "list", "member": {"shape": "StudioComponentSummary"}}, "StudioComponentType": {"type": "string", "enum": ["ACTIVE_DIRECTORY", "SHARED_FILE_SYSTEM", "COMPUTE_FARM", "LICENSE_SERVICE", "CUSTOM"]}, "StudioComponentTypeList": {"type": "list", "member": {"shape": "StudioComponentType"}}, "StudioDisplayName": {"type": "string", "max": 64, "min": 0, "sensitive": true}, "StudioEncryptionConfiguration": {"type": "structure", "required": ["keyType"], "members": {"keyArn": {"shape": "StudioEncryptionConfigurationKeyArn", "documentation": "<p>The ARN for a KMS key that is used to encrypt studio data.</p>"}, "keyType": {"shape": "StudioEncryptionConfigurationKeyType", "documentation": "<p>The type of KMS key that is used to encrypt studio data.</p>"}}, "documentation": "<p>Configuration of the encryption method that is used for the studio.</p>"}, "StudioEncryptionConfigurationKeyArn": {"type": "string", "documentation": "<p>The Amazon Resource Name (ARN) for a KMS key that is used to encrypt studio data.</p>", "min": 4, "pattern": "^arn:.*"}, "StudioEncryptionConfigurationKeyType": {"type": "string", "documentation": "<p>The type of KMS key that is used to encrypt studio data.</p>", "enum": ["AWS_OWNED_KEY", "CUSTOMER_MANAGED_KEY"]}, "StudioList": {"type": "list", "member": {"shape": "Studio"}}, "StudioMembership": {"type": "structure", "members": {"identityStoreId": {"shape": "String", "documentation": "<p>The ID of the identity store.</p>"}, "persona": {"shape": "StudioPersona", "documentation": "<p>The persona.</p>"}, "principalId": {"shape": "String", "documentation": "<p>The principal ID.</p>"}, "sid": {"shape": "String", "documentation": "<p>The Active Directory Security Identifier for this user, if available.</p>"}}, "documentation": "<p>A studio member is an association of a user from your studio identity source to elevated permissions that they are granted in the studio.</p> <p>When you add a user to your studio using the Nimble Studio console, they are given access to the studio's IAM Identity Center application and are given access to log in to the Nimble Studio portal. These users have the permissions provided by the studio's user IAM role and do not appear in the studio membership collection. Only studio admins appear in studio membership.</p> <p>When you add a user to studio membership with the ADMIN persona, upon logging in to the Nimble Studio portal, they are granted permissions specified by the Studio's Admin IAM role.</p>"}, "StudioMembershipList": {"type": "list", "member": {"shape": "StudioMembership"}, "max": 20, "min": 0}, "StudioName": {"type": "string", "max": 64, "min": 3, "pattern": "^[a-z0-9]*$"}, "StudioPersona": {"type": "string", "enum": ["ADMINISTRATOR"]}, "StudioState": {"type": "string", "enum": ["CREATE_IN_PROGRESS", "READY", "UPDATE_IN_PROGRESS", "DELETE_IN_PROGRESS", "DELETED", "DELETE_FAILED", "CREATE_FAILED", "UPDATE_FAILED"]}, "StudioStatusCode": {"type": "string", "documentation": "<p>The status code.</p>", "enum": ["STUDIO_CREATED", "STUDIO_DELETED", "STUDIO_UPDATED", "STUDIO_CREATE_IN_PROGRESS", "STUDIO_UPDATE_IN_PROGRESS", "STUDIO_DELETE_IN_PROGRESS", "STUDIO_WITH_LAUNCH_PROFILES_NOT_DELETED", "STUDIO_WITH_STUDIO_COMPONENTS_NOT_DELETED", "STUDIO_WITH_STREAMING_IMAGES_NOT_DELETED", "AWS_SSO_NOT_ENABLED", "AWS_SSO_ACCESS_DENIED", "ROLE_NOT_OWNED_BY_STUDIO_OWNER", "ROLE_COULD_NOT_BE_ASSUMED", "INTERNAL_ERROR", "ENCRYPTION_KEY_NOT_FOUND", "ENCRYPTION_KEY_ACCESS_DENIED", "AWS_SSO_CONFIGURATION_REPAIRED", "AWS_SSO_CONFIGURATION_REPAIR_IN_PROGRESS", "AWS_STS_REGION_DISABLED"]}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the resource you want to add tags to. </p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "Tags", "documentation": "<p>A collection of labels, in the form of key-value pairs, that apply to this resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "Tags": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "ThrottlingException": {"type": "structure", "members": {"code": {"shape": "String", "documentation": "<p>A more specific error code.</p>"}, "context": {"shape": "ExceptionContext", "documentation": "<p>The exception context.</p>"}, "message": {"shape": "String", "documentation": "<p>A human-readable description of the error.</p>"}}, "documentation": "<p>The request throughput limit was exceeded.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true, "retryable": {"throttling": false}}, "Timestamp": {"type": "timestamp", "timestampFormat": "iso8601"}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "String", "documentation": "<p>Identifies the Amazon Resource Name(ARN) key from which you are removing tags. </p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "StringList", "documentation": "<p>One or more tag keys. Specify only the tag keys, not the tag values.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateLaunchProfileMemberRequest": {"type": "structure", "required": ["launchProfileId", "persona", "principalId", "studioId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don’t specify a client token, the Amazon Web Services SDK automatically generates a client token and uses it for the request to ensure idempotency.</p>", "idempotencyToken": true, "location": "header", "locationName": "X-Amz-Client-Token"}, "launchProfileId": {"shape": "String", "documentation": "<p>The ID of the launch profile used to control access from the streaming session.</p>", "location": "uri", "locationName": "launchProfileId"}, "persona": {"shape": "LaunchProfilePersona", "documentation": "<p>The persona.</p>"}, "principalId": {"shape": "String", "documentation": "<p>The principal ID. This currently supports a IAM Identity Center UserId. </p>", "location": "uri", "locationName": "principalId"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "UpdateLaunchProfileMemberResponse": {"type": "structure", "members": {"member": {"shape": "LaunchProfileMembership", "documentation": "<p>The updated member. </p>"}}}, "UpdateLaunchProfileRequest": {"type": "structure", "required": ["launchProfileId", "studioId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don’t specify a client token, the Amazon Web Services SDK automatically generates a client token and uses it for the request to ensure idempotency.</p>", "idempotencyToken": true, "location": "header", "locationName": "X-Amz-Client-Token"}, "description": {"shape": "LaunchProfileDescription", "documentation": "<p>The description.</p>"}, "launchProfileId": {"shape": "String", "documentation": "<p>The ID of the launch profile used to control access from the streaming session.</p>", "location": "uri", "locationName": "launchProfileId"}, "launchProfileProtocolVersions": {"shape": "LaunchProfileProtocolVersionList", "documentation": "<p>The version number of the protocol that is used by the launch profile. The only valid version is \"2021-03-31\".</p>"}, "name": {"shape": "LaunchProfileName", "documentation": "<p>The name for the launch profile.</p>"}, "streamConfiguration": {"shape": "StreamConfigurationCreate", "documentation": "<p>A configuration for a streaming session.</p>"}, "studioComponentIds": {"shape": "LaunchProfileStudioComponentIdList", "documentation": "<p>Unique identifiers for a collection of studio components that can be used with this launch profile.</p>"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "UpdateLaunchProfileResponse": {"type": "structure", "members": {"launchProfile": {"shape": "LaunchProfile", "documentation": "<p>The launch profile.</p>"}}}, "UpdateStreamingImageRequest": {"type": "structure", "required": ["streamingImageId", "studioId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don’t specify a client token, the Amazon Web Services SDK automatically generates a client token and uses it for the request to ensure idempotency.</p>", "idempotencyToken": true, "location": "header", "locationName": "X-Amz-Client-Token"}, "description": {"shape": "StreamingImageDescription", "documentation": "<p>The description.</p>"}, "name": {"shape": "StreamingImageName", "documentation": "<p>The name for the streaming image.</p>"}, "streamingImageId": {"shape": "String", "documentation": "<p>The streaming image ID.</p>", "location": "uri", "locationName": "streamingImageId"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}}}, "UpdateStreamingImageResponse": {"type": "structure", "members": {"streamingImage": {"shape": "StreamingImage"}}}, "UpdateStudioComponentRequest": {"type": "structure", "required": ["studioComponentId", "studioId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don’t specify a client token, the Amazon Web Services SDK automatically generates a client token and uses it for the request to ensure idempotency.</p>", "idempotencyToken": true, "location": "header", "locationName": "X-Amz-Client-Token"}, "configuration": {"shape": "StudioComponentConfiguration", "documentation": "<p>The configuration of the studio component, based on component type.</p>"}, "description": {"shape": "StudioComponentDescription", "documentation": "<p>The description.</p>"}, "ec2SecurityGroupIds": {"shape": "StudioComponentSecurityGroupIdList", "documentation": "<p>The EC2 security groups that control access to the studio component.</p>"}, "initializationScripts": {"shape": "StudioComponentInitializationScriptList", "documentation": "<p>Initialization scripts for studio components.</p>"}, "name": {"shape": "StudioComponentName", "documentation": "<p>The name for the studio component.</p>"}, "runtimeRoleArn": {"shape": "RoleArn", "documentation": "<p>An IAM role attached to a Studio Component that gives the studio component access to Amazon Web Services resources at anytime while the instance is running. </p>"}, "scriptParameters": {"shape": "StudioComponentScriptParameterKeyValueList", "documentation": "<p>Parameters for the studio component scripts.</p>"}, "secureInitializationRoleArn": {"shape": "RoleArn", "documentation": "<p>An IAM role attached to Studio Component when the system initialization script runs which give the studio component access to Amazon Web Services resources when the system initialization script runs.</p>"}, "studioComponentId": {"shape": "String", "documentation": "<p>The studio component ID.</p>", "location": "uri", "locationName": "studioComponentId"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}, "subtype": {"shape": "StudioComponentSubtype", "documentation": "<p>The specific subtype of a studio component.</p>"}, "type": {"shape": "StudioComponentType", "documentation": "<p>The type of the studio component.</p>"}}}, "UpdateStudioComponentResponse": {"type": "structure", "members": {"studioComponent": {"shape": "StudioComponent", "documentation": "<p>Information about the studio component.</p>"}}}, "UpdateStudioRequest": {"type": "structure", "required": ["studioId"], "members": {"adminRoleArn": {"shape": "RoleArn", "documentation": "<p>The IAM role that Studio Admins will assume when logging in to the Nimble Studio portal.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don’t specify a client token, the Amazon Web Services SDK automatically generates a client token and uses it for the request to ensure idempotency.</p>", "idempotencyToken": true, "location": "header", "locationName": "X-Amz-Client-Token"}, "displayName": {"shape": "StudioDisplayName", "documentation": "<p>A friendly name for the studio.</p>"}, "studioId": {"shape": "String", "documentation": "<p>The studio ID. </p>", "location": "uri", "locationName": "studioId"}, "userRoleArn": {"shape": "RoleArn", "documentation": "<p>The IAM role that Studio Users will assume when logging in to the Nimble Studio portal.</p>"}}}, "UpdateStudioResponse": {"type": "structure", "required": ["studio"], "members": {"studio": {"shape": "Studio", "documentation": "<p>Information about a studio.</p>"}}}, "ValidationException": {"type": "structure", "members": {"code": {"shape": "String", "documentation": "<p>A more specific error code.</p>"}, "context": {"shape": "ExceptionContext", "documentation": "<p>The exception context.</p>"}, "message": {"shape": "String", "documentation": "<p>A human-readable description of the error.</p>"}}, "documentation": "<p>One of the parameters in the request is invalid.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "ValidationResult": {"type": "structure", "required": ["state", "statusCode", "statusMessage", "type"], "members": {"state": {"shape": "LaunchProfileValidationState", "documentation": "<p>The current state.</p>"}, "statusCode": {"shape": "LaunchProfileValidationStatusCode", "documentation": "<p>The status code. This will contain the failure reason if the state is <code>VALIDATION_FAILED</code>.</p>"}, "statusMessage": {"shape": "LaunchProfileValidationStatusMessage", "documentation": "<p>The status message for the validation result.</p>"}, "type": {"shape": "LaunchProfileValidationType", "documentation": "<p>The type of the validation result.</p>"}}, "documentation": "<p>The launch profile validation result.</p>"}, "ValidationResults": {"type": "list", "member": {"shape": "ValidationResult"}}, "VolumeConfiguration": {"type": "structure", "members": {"iops": {"shape": "VolumeIops", "documentation": "<p>The number of I/O operations per second for the root volume that is attached to streaming session.</p>"}, "size": {"shape": "VolumeSizeInGiB", "documentation": "<p>The size of the root volume that is attached to the streaming session. The root volume size is measured in GiBs.</p>"}, "throughput": {"shape": "VolumeThroughputInMiBs", "documentation": "<p>The throughput to provision for the root volume that is attached to the streaming session. The throughput is measured in MiB/s.</p>"}}, "documentation": "<p>Custom volume configuration for the root volumes that are attached to streaming sessions.</p> <p>This parameter is only allowed when <code>sessionPersistenceMode</code> is <code>ACTIVATED</code>.</p>"}, "VolumeIops": {"type": "integer", "box": true, "max": 16000, "min": 3000}, "VolumeRetentionMode": {"type": "string", "enum": ["RETAIN", "DELETE"]}, "VolumeSizeInGiB": {"type": "integer", "box": true, "max": 16000, "min": 100}, "VolumeThroughputInMiBs": {"type": "integer", "box": true, "max": 1000, "min": 125}, "WindowsMountDrive": {"type": "string", "pattern": "^[A-Z]$"}}, "documentation": "<p>Welcome to the Amazon Nimble Studio API reference. This API reference provides methods, schema, resources, parameters, and more to help you get the most out of Nimble Studio.</p> <p>Nimble Studio is a virtual studio that empowers visual effects, animation, and interactive content teams to create content securely within a scalable, private cloud service.</p>"}