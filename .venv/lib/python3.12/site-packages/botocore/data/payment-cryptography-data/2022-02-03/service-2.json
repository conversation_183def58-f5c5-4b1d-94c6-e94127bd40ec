{"version": "2.0", "metadata": {"apiVersion": "2022-02-03", "endpointPrefix": "dataplane.payment-cryptography", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "Payment Cryptography Data Plane", "serviceId": "Payment Cryptography Data", "signatureVersion": "v4", "signingName": "payment-cryptography", "uid": "payment-cryptography-data-2022-02-03"}, "operations": {"DecryptData": {"name": "DecryptData", "http": {"method": "POST", "requestUri": "/keys/{KeyIdentifier}/decrypt", "responseCode": 200}, "input": {"shape": "DecryptDataInput"}, "output": {"shape": "DecryptDataOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Decrypts ciphertext data to plaintext using symmetric, asymmetric, or DUKPT data encryption key. For more information, see <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/decrypt-data.html\">Decrypt data</a> in the <i>Amazon Web Services Payment Cryptography User Guide</i>.</p> <p>You can use an encryption key generated within Amazon Web Services Payment Cryptography, or you can import your own encryption key by calling <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/APIReference/API_ImportKey.html\">ImportKey</a>. For this operation, the key must have <code>KeyModesOfUse</code> set to <code>Decrypt</code>. In asymmetric decryption, Amazon Web Services Payment Cryptography decrypts the ciphertext using the private component of the asymmetric encryption key pair. For data encryption outside of Amazon Web Services Payment Cryptography, you can export the public component of the asymmetric key pair by calling <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/APIReference/API_GetPublicKeyCertificate.html\">GetPublicCertificate</a>.</p> <p>For symmetric and DUKPT decryption, Amazon Web Services Payment Cryptography supports <code>TDES</code> and <code>AES</code> algorithms. For asymmetric decryption, Amazon Web Services Payment Cryptography supports <code>RSA</code>. When you use DUKPT, for <code>TDES</code> algorithm, the ciphertext data length must be a multiple of 16 bytes. For <code>AES</code> algorithm, the ciphertext data length must be a multiple of 32 bytes.</p> <p>For information about valid keys for this operation, see <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/keys-validattributes.html\">Understanding key attributes</a> and <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/crypto-ops-validkeys-ops.html\">Key types for specific data operations</a> in the <i>Amazon Web Services Payment Cryptography User Guide</i>. </p> <p> <b>Cross-account use</b>: This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>EncryptData</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/APIReference/API_GetPublicKeyCertificate.html\">GetPublicCertificate</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/APIReference/API_ImportKey.html\">ImportKey</a> </p> </li> </ul>"}, "EncryptData": {"name": "EncryptData", "http": {"method": "POST", "requestUri": "/keys/{KeyIdentifier}/encrypt", "responseCode": 200}, "input": {"shape": "EncryptDataInput"}, "output": {"shape": "EncryptDataOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Encrypts plaintext data to ciphertext using symmetric, asymmetric, or DUKPT data encryption key. For more information, see <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/encrypt-data.html\">Encrypt data</a> in the <i>Amazon Web Services Payment Cryptography User Guide</i>.</p> <p>You can generate an encryption key within Amazon Web Services Payment Cryptography by calling <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/APIReference/API_CreateKey.html\">CreateKey</a>. You can import your own encryption key by calling <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/APIReference/API_ImportKey.html\">ImportKey</a>. For this operation, the key must have <code>KeyModesOfUse</code> set to <code>Encrypt</code>. In asymmetric encryption, plaintext is encrypted using public component. You can import the public component of an asymmetric key pair created outside Amazon Web Services Payment Cryptography by calling <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/APIReference/API_ImportKey.html\">ImportKey</a>). </p> <p>for symmetric and DUKPT encryption, Amazon Web Services Payment Cryptography supports <code>TDES</code> and <code>AES</code> algorithms. For asymmetric encryption, Amazon Web Services Payment Cryptography supports <code>RSA</code>. To encrypt using DUKPT, you must already have a DUKPT key in your account with <code>KeyModesOfUse</code> set to <code>DeriveKey</code>, or you can generate a new DUKPT key by calling <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/APIReference/API_CreateKey.html\">CreateKey</a>.</p> <p>For information about valid keys for this operation, see <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/keys-validattributes.html\">Understanding key attributes</a> and <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/crypto-ops-validkeys-ops.html\">Key types for specific data operations</a> in the <i>Amazon Web Services Payment Cryptography User Guide</i>.</p> <p> <b>Cross-account use</b>: This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>DecryptData</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/APIReference/API_GetPublicKeyCertificate.html\">GetPublicCertificate</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/APIReference/API_ImportKey.html\">ImportKey</a> </p> </li> <li> <p> <a>ReEncryptData</a> </p> </li> </ul>"}, "GenerateCardValidationData": {"name": "GenerateCardValidationData", "http": {"method": "POST", "requestUri": "/cardvalidationdata/generate", "responseCode": 200}, "input": {"shape": "GenerateCardValidationDataInput"}, "output": {"shape": "GenerateCardValidationDataOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Generates card-related validation data using algorithms such as Card Verification Values (CVV/CVV2), Dynamic Card Verification Values (dCVV/dCVV2), or Card Security Codes (CSC). For more information, see <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/generate-card-data.html\">Generate card data</a> in the <i>Amazon Web Services Payment Cryptography User Guide</i>.</p> <p>This operation generates a CVV or CSC value that is printed on a payment credit or debit card during card production. The CVV or CSC, PAN (Primary Account Number) and expiration date of the card are required to check its validity during transaction processing. To begin this operation, a CVK (Card Verification Key) encryption key is required. You can use <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/APIReference/API_CreateKey.html\">CreateKey</a> or <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/APIReference/API_ImportKey.html\">ImportKey</a> to establish a CVK within Amazon Web Services Payment Cryptography. The <code>KeyModesOfUse</code> should be set to <code>Generate</code> and <code>Verify</code> for a CVK encryption key. </p> <p>For information about valid keys for this operation, see <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/keys-validattributes.html\">Understanding key attributes</a> and <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/crypto-ops-validkeys-ops.html\">Key types for specific data operations</a> in the <i>Amazon Web Services Payment Cryptography User Guide</i>. </p> <p> <b>Cross-account use</b>: This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/APIReference/API_ImportKey.html\">ImportKey</a> </p> </li> <li> <p> <a>VerifyCardValidationData</a> </p> </li> </ul>"}, "GenerateMac": {"name": "GenerateMac", "http": {"method": "POST", "requestUri": "/mac/generate", "responseCode": 200}, "input": {"shape": "GenerateMacInput"}, "output": {"shape": "GenerateMacOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Generates a Message Authentication Code (MAC) cryptogram within Amazon Web Services Payment Cryptography. </p> <p>You can use this operation when keys won't be shared but mutual data is present on both ends for validation. In this case, known data values are used to generate a MAC on both ends for comparision without sending or receiving data in ciphertext or plaintext. You can use this operation to generate a DUPKT, HMAC or EMV MAC by setting generation attributes and algorithm to the associated values. The MAC generation encryption key must have valid values for <code>KeyUsage</code> such as <code>TR31_M7_HMAC_KEY</code> for HMAC generation, and they key must have <code>KeyModesOfUse</code> set to <code>Generate</code> and <code>Verify</code>.</p> <p>For information about valid keys for this operation, see <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/keys-validattributes.html\">Understanding key attributes</a> and <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/crypto-ops-validkeys-ops.html\">Key types for specific data operations</a> in the <i>Amazon Web Services Payment Cryptography User Guide</i>. </p> <p> <b>Cross-account use</b>: This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>VerifyMac</a> </p> </li> </ul>"}, "GeneratePinData": {"name": "GeneratePinData", "http": {"method": "POST", "requestUri": "/pindata/generate", "responseCode": 200}, "input": {"shape": "GeneratePinDataInput"}, "output": {"shape": "GeneratePinDataOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Generates pin-related data such as <PERSON>IN, PIN Verification Value (PVV), PIN Block, and PIN Offset during new card issuance or reissuance. For more information, see <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/generate-pin-data.html\">Generate PIN data</a> in the <i>Amazon Web Services Payment Cryptography User Guide</i>.</p> <p>PIN data is never transmitted in clear to or from Amazon Web Services Payment Cryptography. This operation generates PIN, PVV, or PIN Offset and then encrypts it using Pin Encryption Key (PEK) to create an <code>EncryptedPinBlock</code> for transmission from Amazon Web Services Payment Cryptography. This operation uses a separate Pin Verification Key (PVK) for VISA PVV generation. </p> <p>For information about valid keys for this operation, see <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/keys-validattributes.html\">Understanding key attributes</a> and <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/crypto-ops-validkeys-ops.html\">Key types for specific data operations</a> in the <i>Amazon Web Services Payment Cryptography User Guide</i>.</p> <p> <b>Cross-account use</b>: This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>GenerateCardValidationData</a> </p> </li> <li> <p> <a>TranslatePinData</a> </p> </li> <li> <p> <a>VerifyPinData</a> </p> </li> </ul>"}, "ReEncryptData": {"name": "ReEncryptData", "http": {"method": "POST", "requestUri": "/keys/{IncomingKeyIdentifier}/reencrypt", "responseCode": 200}, "input": {"shape": "ReEncryptDataInput"}, "output": {"shape": "ReEncryptDataOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Re-encrypt ciphertext using DUKPT, Symmetric and Asymmetric Data Encryption Keys. </p> <p>You can either generate an encryption key within Amazon Web Services Payment Cryptography by calling <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/APIReference/API_CreateKey.html\"><PERSON><PERSON><PERSON><PERSON></a> or import your own encryption key by calling <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/APIReference/API_ImportKey.html\">ImportKey</a>. The <code>KeyArn</code> for use with this operation must be in a compatible key state with <code>KeyModesOfUse</code> set to <code>Encrypt</code>. In asymmetric encryption, ciphertext is encrypted using public component (imported by calling <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/APIReference/API_ImportKey.html\">ImportKey</a>) of the asymmetric key pair created outside of Amazon Web Services Payment Cryptography. </p> <p>For symmetric and DUKPT encryption, Amazon Web Services Payment Cryptography supports <code>TDES</code> and <code>AES</code> algorithms. For asymmetric encryption, Amazon Web Services Payment Cryptography supports <code>RSA</code>. To encrypt using DUKPT, a DUKPT key must already exist within your account with <code>KeyModesOfUse</code> set to <code>DeriveKey</code> or a new DUKPT can be generated by calling <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/APIReference/API_CreateKey.html\">CreateKey</a>.</p> <p>For information about valid keys for this operation, see <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/keys-validattributes.html\">Understanding key attributes</a> and <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/crypto-ops-validkeys-ops.html\">Key types for specific data operations</a> in the <i>Amazon Web Services Payment Cryptography User Guide</i>. </p> <p> <b>Cross-account use</b>: This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>DecryptData</a> </p> </li> <li> <p> <a>EncryptData</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/APIReference/API_GetPublicKeyCertificate.html\">GetPublicCertificate</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/APIReference/API_ImportKey.html\">ImportKey</a> </p> </li> </ul>"}, "TranslatePinData": {"name": "TranslatePinData", "http": {"method": "POST", "requestUri": "/pindata/translate", "responseCode": 200}, "input": {"shape": "TranslatePinDataInput"}, "output": {"shape": "TranslatePinDataOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Translates encrypted PIN block from and to ISO 9564 formats 0,1,3,4. For more information, see <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/translate-pin-data.html\">Translate PIN data</a> in the <i>Amazon Web Services Payment Cryptography User Guide</i>.</p> <p>PIN block translation involves changing the encrytion of PIN block from one encryption key to another encryption key and changing PIN block format from one to another without PIN block data leaving Amazon Web Services Payment Cryptography. The encryption key transformation can be from PEK (Pin Encryption Key) to BDK (Base Derivation Key) for DUKPT or from BDK for DUKPT to PEK. Amazon Web Services Payment Cryptography supports <code>TDES</code> and <code>AES</code> key derivation type for DUKPT tranlations. You can use this operation for P2PE (Point to Point Encryption) use cases where the encryption keys should change but the processing system either does not need to, or is not permitted to, decrypt the data.</p> <p>The allowed combinations of PIN block format translations are guided by PCI. It is important to note that not all encrypted PIN block formats (example, format 1) require PAN (Primary Account Number) as input. And as such, PIN block format that requires PAN (example, formats 0,3,4) cannot be translated to a format (format 1) that does not require a PAN for generation. </p> <p>For information about valid keys for this operation, see <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/keys-validattributes.html\">Understanding key attributes</a> and <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/crypto-ops-validkeys-ops.html\">Key types for specific data operations</a> in the <i>Amazon Web Services Payment Cryptography User Guide</i>.</p> <note> <p>At this time, Amazon Web Services Payment Cryptography does not support translations to PIN format 4.</p> </note> <p> <b>Cross-account use</b>: This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>GeneratePinData</a> </p> </li> <li> <p> <a>VerifyPinData</a> </p> </li> </ul>"}, "VerifyAuthRequestCryptogram": {"name": "VerifyAuthRequestCryptogram", "http": {"method": "POST", "requestUri": "/cryptogram/verify", "responseCode": 200}, "input": {"shape": "VerifyAuthRequestCryptogramInput"}, "output": {"shape": "VerifyAuthRequestCryptogramOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "VerificationFailedException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Verifies Authorization Request Cryptogram (ARQC) for a EMV chip payment card authorization. For more information, see <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/data-operations.verifyauthrequestcryptogram.html\">Verify auth request cryptogram</a> in the <i>Amazon Web Services Payment Cryptography User Guide</i>.</p> <p>ARQC generation is done outside of Amazon Web Services Payment Cryptography and is typically generated on a point of sale terminal for an EMV chip card to obtain payment authorization during transaction time. For ARQC verification, you must first import the ARQC generated outside of Amazon Web Services Payment Cryptography by calling <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/APIReference/API_ImportKey.html\">ImportKey</a>. This operation uses the imported ARQC and an major encryption key (DUKPT) created by calling <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/APIReference/API_CreateKey.html\">C<PERSON><PERSON>ey</a> to either provide a boolean ARQC verification result or provide an APRC (Authorization Response Cryptogram) response using Method 1 or Method 2. The <code>ARPC_METHOD_1</code> uses <code>AuthResponseCode</code> to generate ARPC and <code>ARPC_METHOD_2</code> uses <code>CardStatusUpdate</code> to generate ARPC. </p> <p>For information about valid keys for this operation, see <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/keys-validattributes.html\">Understanding key attributes</a> and <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/crypto-ops-validkeys-ops.html\">Key types for specific data operations</a> in the <i>Amazon Web Services Payment Cryptography User Guide</i>.</p> <p> <b>Cross-account use</b>: This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>VerifyCardValidationData</a> </p> </li> <li> <p> <a>VerifyPinData</a> </p> </li> </ul>"}, "VerifyCardValidationData": {"name": "VerifyCardValidationData", "http": {"method": "POST", "requestUri": "/cardvalidationdata/verify", "responseCode": 200}, "input": {"shape": "VerifyCardValidationDataInput"}, "output": {"shape": "VerifyCardValidationDataOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "VerificationFailedException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Verifies card-related validation data using algorithms such as Card Verification Values (CVV/CVV2), Dynamic Card Verification Values (dCVV/dCVV2) and Card Security Codes (CSC). For more information, see <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/verify-card-data.html\">Verify card data</a> in the <i>Amazon Web Services Payment Cryptography User Guide</i>.</p> <p>This operation validates the CVV or CSC codes that is printed on a payment credit or debit card during card payment transaction. The input values are typically provided as part of an inbound transaction to an issuer or supporting platform partner. Amazon Web Services Payment Cryptography uses CVV or CSC, PAN (Primary Account Number) and expiration date of the card to check its validity during transaction processing. In this operation, the CVK (Card Verification Key) encryption key for use with card data verification is same as the one in used for <a>GenerateCardValidationData</a>. </p> <p>For information about valid keys for this operation, see <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/keys-validattributes.html\">Understanding key attributes</a> and <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/crypto-ops-validkeys-ops.html\">Key types for specific data operations</a> in the <i>Amazon Web Services Payment Cryptography User Guide</i>. </p> <p> <b>Cross-account use</b>: This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>GenerateCardValidationData</a> </p> </li> <li> <p> <a>VerifyAuthRequestCryptogram</a> </p> </li> <li> <p> <a>VerifyPinData</a> </p> </li> </ul>"}, "VerifyMac": {"name": "VerifyMac", "http": {"method": "POST", "requestUri": "/mac/verify", "responseCode": 200}, "input": {"shape": "VerifyMacInput"}, "output": {"shape": "VerifyMacOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "VerificationFailedException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Verifies a Message Authentication Code (MAC). </p> <p>You can use this operation when keys won't be shared but mutual data is present on both ends for validation. In this case, known data values are used to generate a MAC on both ends for verification without sending or receiving data in ciphertext or plaintext. You can use this operation to verify a DUPKT, HMAC or EMV MAC by setting generation attributes and algorithm to the associated values. Use the same encryption key for MAC verification as you use for <a>GenerateMac</a>. </p> <p>For information about valid keys for this operation, see <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/keys-validattributes.html\">Understanding key attributes</a> and <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/crypto-ops-validkeys-ops.html\">Key types for specific data operations</a> in the <i>Amazon Web Services Payment Cryptography User Guide</i>. </p> <p> <b>Cross-account use</b>: This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>GenerateMac</a> </p> </li> </ul>"}, "VerifyPinData": {"name": "VerifyPinData", "http": {"method": "POST", "requestUri": "/pindata/verify", "responseCode": 200}, "input": {"shape": "VerifyPinDataInput"}, "output": {"shape": "VerifyPinDataOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "VerificationFailedException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Verifies pin-related data such as PIN and PIN Offset using algorithms including VISA PVV and IBM3624. For more information, see <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/verify-pin-data.html\">Verify PIN data</a> in the <i>Amazon Web Services Payment Cryptography User Guide</i>.</p> <p>This operation verifies PIN data for user payment card. A card holder PIN data is never transmitted in clear to or from Amazon Web Services Payment Cryptography. This operation uses PIN Verification Key (PVK) for PIN or PIN Offset generation and then encrypts it using PIN Encryption Key (PEK) to create an <code>EncryptedPinBlock</code> for transmission from Amazon Web Services Payment Cryptography. </p> <p>For information about valid keys for this operation, see <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/keys-validattributes.html\">Understanding key attributes</a> and <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/crypto-ops-validkeys-ops.html\">Key types for specific data operations</a> in the <i>Amazon Web Services Payment Cryptography User Guide</i>. </p> <p> <b>Cross-account use</b>: This operation can't be used across different Amazon Web Services accounts.</p> <p> <b>Related operations:</b> </p> <ul> <li> <p> <a>GeneratePinData</a> </p> </li> <li> <p> <a>TranslatePinData</a> </p> </li> </ul>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>You do not have sufficient access to perform this action.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "AmexCardSecurityCodeVersion1": {"type": "structure", "required": ["CardExpiryDate"], "members": {"CardExpiryDate": {"shape": "NumberLengthEquals4", "documentation": "<p>The expiry date of a payment card.</p>"}}, "documentation": "<p>Card data parameters that are required to generate a Card Security Code (CSC2) for an AMEX payment card.</p>"}, "AmexCardSecurityCodeVersion2": {"type": "structure", "required": ["CardExpiryDate", "ServiceCode"], "members": {"CardExpiryDate": {"shape": "NumberLengthEquals4", "documentation": "<p>The expiry date of a payment card.</p>"}, "ServiceCode": {"shape": "NumberLengthEquals3", "documentation": "<p>The service code of the AMEX payment card. This is different from the Card Security Code (CSC).</p>"}}, "documentation": "<p>Card data parameters that are required to generate a Card Security Code (CSC2) for an AMEX payment card.</p>"}, "AsymmetricEncryptionAttributes": {"type": "structure", "members": {"PaddingType": {"shape": "PaddingType", "documentation": "<p>The padding to be included with the data.</p>"}}, "documentation": "<p>Parameters for plaintext encryption using asymmetric keys.</p>"}, "CardGenerationAttributes": {"type": "structure", "members": {"AmexCardSecurityCodeVersion1": {"shape": "AmexCardSecurityCodeVersion1"}, "AmexCardSecurityCodeVersion2": {"shape": "AmexCardSecurityCodeVersion2", "documentation": "<p>Card data parameters that are required to generate a Card Security Code (CSC2) for an AMEX payment card.</p>"}, "CardHolderVerificationValue": {"shape": "CardHolderVerificationValue", "documentation": "<p>Card data parameters that are required to generate a cardholder verification value for the payment card.</p>"}, "CardVerificationValue1": {"shape": "CardVerificationValue1", "documentation": "<p>Card data parameters that are required to generate Card Verification Value (CVV) for the payment card.</p>"}, "CardVerificationValue2": {"shape": "CardVerificationValue2", "documentation": "<p>Card data parameters that are required to generate Card Verification Value (CVV2) for the payment card.</p>"}, "DynamicCardVerificationCode": {"shape": "DynamicCardVerificationCode", "documentation": "<p>Card data parameters that are required to generate CDynamic Card Verification Code (dCVC) for the payment card.</p>"}, "DynamicCardVerificationValue": {"shape": "DynamicCardVerificationValue", "documentation": "<p>Card data parameters that are required to generate CDynamic Card Verification Value (dCVV) for the payment card.</p>"}}, "documentation": "<p>Card data parameters that are required to generate Card Verification Values (CVV/CVV2), Dynamic Card Verification Values (dCVV/dCVV2), or Card Security Codes (CSC).</p>", "union": true}, "CardHolderVerificationValue": {"type": "structure", "required": ["ApplicationTransactionCounter", "PanSequenceNumber", "UnpredictableNumber"], "members": {"ApplicationTransactionCounter": {"shape": "HexLengthBetween2And4", "documentation": "<p>The transaction counter value that comes from a point of sale terminal.</p>"}, "PanSequenceNumber": {"shape": "HexLengthEquals2", "documentation": "<p>A number that identifies and differentiates payment cards with the same Primary Account Number (PAN).</p>"}, "UnpredictableNumber": {"shape": "HexLengthBetween2And8", "documentation": "<p>A random number generated by the issuer.</p>"}}, "documentation": "<p>Card data parameters that are required to generate a cardholder verification value for the payment card.</p>"}, "CardVerificationAttributes": {"type": "structure", "members": {"AmexCardSecurityCodeVersion1": {"shape": "AmexCardSecurityCodeVersion1"}, "AmexCardSecurityCodeVersion2": {"shape": "AmexCardSecurityCodeVersion2", "documentation": "<p>Card data parameters that are required to verify a Card Security Code (CSC2) for an AMEX payment card.</p>"}, "CardHolderVerificationValue": {"shape": "CardHolderVerificationValue", "documentation": "<p>Card data parameters that are required to verify a cardholder verification value for the payment card.</p>"}, "CardVerificationValue1": {"shape": "CardVerificationValue1", "documentation": "<p>Card data parameters that are required to verify Card Verification Value (CVV) for the payment card.</p>"}, "CardVerificationValue2": {"shape": "CardVerificationValue2", "documentation": "<p>Card data parameters that are required to verify Card Verification Value (CVV2) for the payment card.</p>"}, "DiscoverDynamicCardVerificationCode": {"shape": "DiscoverDynamicCardVerificationCode", "documentation": "<p>Card data parameters that are required to verify CDynamic Card Verification Code (dCVC) for the payment card.</p>"}, "DynamicCardVerificationCode": {"shape": "DynamicCardVerificationCode", "documentation": "<p>Card data parameters that are required to verify CDynamic Card Verification Code (dCVC) for the payment card.</p>"}, "DynamicCardVerificationValue": {"shape": "DynamicCardVerificationValue", "documentation": "<p>Card data parameters that are required to verify CDynamic Card Verification Value (dCVV) for the payment card.</p>"}}, "documentation": "<p>Card data parameters that are requried to verify Card Verification Values (CVV/CVV2), Dynamic Card Verification Values (dCVV/dCVV2), or Card Security Codes (CSC).</p>", "union": true}, "CardVerificationValue1": {"type": "structure", "required": ["CardExpiryDate", "ServiceCode"], "members": {"CardExpiryDate": {"shape": "NumberLengthEquals4", "documentation": "<p>The expiry date of a payment card.</p>"}, "ServiceCode": {"shape": "NumberLengthEquals3", "documentation": "<p>The service code of the payment card. This is different from Card Security Code (CSC).</p>"}}, "documentation": "<p>Card data parameters that are required to verify CVV (Card Verification Value) for the payment card.</p>"}, "CardVerificationValue2": {"type": "structure", "required": ["CardExpiryDate"], "members": {"CardExpiryDate": {"shape": "NumberLengthEquals4", "documentation": "<p>The expiry date of a payment card.</p>"}}, "documentation": "<p>Card data parameters that are required to verify Card Verification Value (CVV2) for the payment card.</p>"}, "CryptogramAuthResponse": {"type": "structure", "members": {"ArpcMethod1": {"shape": "CryptogramVerificationArpcMethod1", "documentation": "<p>Parameters that are required for ARPC response generation using method1 after ARQC verification is successful.</p>"}, "ArpcMethod2": {"shape": "CryptogramVerificationArpcMethod2", "documentation": "<p>Parameters that are required for ARPC response generation using method2 after ARQC verification is successful.</p>"}}, "documentation": "<p>Parameters that are required for Authorization Response Cryptogram (ARPC) generation after Authorization Request Cryptogram (ARQC) verification is successful.</p>", "union": true}, "CryptogramVerificationArpcMethod1": {"type": "structure", "required": ["AuthResponseCode"], "members": {"AuthResponseCode": {"shape": "HexLengthEquals4", "documentation": "<p>The auth code used to calculate APRC after ARQC verification is successful. This is the same auth code used for ARQC generation outside of Amazon Web Services Payment Cryptography.</p>"}}, "documentation": "<p>Parameters that are required for ARPC response generation using method1 after ARQC verification is successful.</p>"}, "CryptogramVerificationArpcMethod2": {"type": "structure", "required": ["CardStatusUpdate"], "members": {"CardStatusUpdate": {"shape": "HexLengthEquals8", "documentation": "<p>The data indicating whether the issuer approves or declines an online transaction using an EMV chip card.</p>"}, "ProprietaryAuthenticationData": {"shape": "HexLengthBetween1And16", "documentation": "<p>The proprietary authentication data used by issuer for communication during online transaction using an EMV chip card.</p>"}}, "documentation": "<p>Parameters that are required for ARPC response generation using method2 after ARQC verification is successful.</p>"}, "DecryptDataInput": {"type": "structure", "required": ["CipherText", "DecryptionAttributes", "KeyIdentifier"], "members": {"CipherText": {"shape": "HexEvenLengthBetween16And4096", "documentation": "<p>The ciphertext to decrypt.</p>"}, "DecryptionAttributes": {"shape": "EncryptionDecryptionAttributes", "documentation": "<p>The encryption key type and attributes for ciphertext decryption.</p>"}, "KeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>keyARN</code> of the encryption key that Amazon Web Services Payment Cryptography uses for ciphertext decryption.</p>", "location": "uri", "locationName": "KeyIdentifier"}}}, "DecryptDataOutput": {"type": "structure", "required": ["KeyArn", "KeyCheckValue", "PlainText"], "members": {"KeyArn": {"shape": "KeyArn", "documentation": "<p>The <code>keyARN</code> of the encryption key that Amazon Web Services Payment Cryptography uses for ciphertext decryption.</p>"}, "KeyCheckValue": {"shape": "KeyCheckValue", "documentation": "<p>The key check value (KCV) of the encryption key. The KCV is used to check if all parties holding a given key have the same key or to detect that a key has changed. Amazon Web Services Payment Cryptography calculates the KCV by using standard algorithms, typically by encrypting 8 or 16 bytes or \"00\" or \"01\" and then truncating the result to the first 3 bytes, or 6 hex digits, of the resulting cryptogram.</p>"}, "PlainText": {"shape": "HexEvenLengthBetween16And4096", "documentation": "<p>The decrypted plaintext data.</p>"}}}, "DiscoverDynamicCardVerificationCode": {"type": "structure", "required": ["ApplicationTransactionCounter", "CardExpiryDate", "UnpredictableNumber"], "members": {"ApplicationTransactionCounter": {"shape": "HexLengthBetween2And4", "documentation": "<p>The transaction counter value that comes from the terminal.</p>"}, "CardExpiryDate": {"shape": "NumberLengthEquals4", "documentation": "<p>The expiry date of a payment card.</p>"}, "UnpredictableNumber": {"shape": "HexLengthBetween2And8", "documentation": "<p>A random number that is generated by the issuer.</p>"}}, "documentation": "<p>Parameters that are required to generate or verify dCVC (Dynamic Card Verification Code).</p>"}, "DukptAttributes": {"type": "structure", "required": ["DukptDerivationType", "KeySerialNumber"], "members": {"DukptDerivationType": {"shape": "DukptDerivationType", "documentation": "<p>The key type derived using DUKPT from a Base Derivation Key (BDK) and Key Serial Number (KSN). This must be less than or equal to the strength of the BDK. For example, you can't use <code>AES_128</code> as a derivation type for a BDK of <code>AES_128</code> or <code>TDES_2KEY</code>.</p>"}, "KeySerialNumber": {"shape": "HexLengthBetween10And24", "documentation": "<p>The unique identifier known as Key Serial Number (KSN) that comes from an encrypting device using DUKPT encryption method. The KSN is derived from the encrypting device unique identifier and an internal transaction counter.</p>"}}, "documentation": "<p>Parameters that are used for Derived Unique Key Per Transaction (DUKPT) derivation algorithm.</p>"}, "DukptDerivationAttributes": {"type": "structure", "required": ["KeySerialNumber"], "members": {"DukptKeyDerivationType": {"shape": "DukptDerivationType", "documentation": "<p>The key type derived using DUKPT from a Base Derivation Key (BDK) and Key Serial Number (KSN). This must be less than or equal to the strength of the BDK. For example, you can't use <code>AES_128</code> as a derivation type for a BDK of <code>AES_128</code> or <code>TDES_2KEY</code> </p>"}, "DukptKeyVariant": {"shape": "DukptKeyVariant", "documentation": "<p>The type of use of DUKPT, which can be for incoming data decryption, outgoing data encryption, or both.</p>"}, "KeySerialNumber": {"shape": "HexLengthBetween10And24", "documentation": "<p>The unique identifier known as Key Serial Number (KSN) that comes from an encrypting device using DUKPT encryption method. The KSN is derived from the encrypting device unique identifier and an internal transaction counter.</p>"}}, "documentation": "<p>Parameters required for encryption or decryption of data using DUKPT.</p>"}, "DukptDerivationType": {"type": "string", "enum": ["TDES_2KEY", "TDES_3KEY", "AES_128", "AES_192", "AES_256"]}, "DukptEncryptionAttributes": {"type": "structure", "required": ["KeySerialNumber"], "members": {"DukptKeyDerivationType": {"shape": "DukptDerivationType", "documentation": "<p>The key type encrypted using DUKPT from a Base Derivation Key (BDK) and Key Serial Number (KSN). This must be less than or equal to the strength of the BDK. For example, you can't use <code>AES_128</code> as a derivation type for a BDK of <code>AES_128</code> or <code>TDES_2KEY</code> </p>"}, "DukptKeyVariant": {"shape": "DukptKeyVariant", "documentation": "<p>The type of use of DUKPT, which can be incoming data decryption, outgoing data encryption, or both.</p>"}, "InitializationVector": {"shape": "HexLength16Or32", "documentation": "<p>An input to cryptographic primitive used to provide the intial state. Typically the <code>InitializationVector</code> must have a random or psuedo-random value, but sometimes it only needs to be unpredictable or unique. If you don't provide a value, Amazon Web Services Payment Cryptography generates a random value.</p>"}, "KeySerialNumber": {"shape": "HexLengthBetween10And24", "documentation": "<p>The unique identifier known as Key Serial Number (KSN) that comes from an encrypting device using DUKPT encryption method. The KSN is derived from the encrypting device unique identifier and an internal transaction counter.</p>"}, "Mode": {"shape": "DukptEncryptionMode", "documentation": "<p>The block cipher mode of operation. Block ciphers are designed to encrypt a block of data of fixed size, for example, 128 bits. The size of the input block is usually same as the size of the encrypted output block, while the key length can be different. A mode of operation describes how to repeatedly apply a cipher's single-block operation to securely transform amounts of data larger than a block.</p> <p>The default is CBC.</p>"}}, "documentation": "<p>Parameters that are required to encrypt plaintext data using DUKPT.</p>"}, "DukptEncryptionMode": {"type": "string", "enum": ["ECB", "CBC"]}, "DukptKeyVariant": {"type": "string", "enum": ["BIDIRECTIONAL", "REQUEST", "RESPONSE"]}, "DynamicCardVerificationCode": {"type": "structure", "required": ["ApplicationTransactionCounter", "PanSequenceNumber", "TrackData", "UnpredictableNumber"], "members": {"ApplicationTransactionCounter": {"shape": "HexLengthBetween2And4", "documentation": "<p>The transaction counter value that comes from the terminal.</p>"}, "PanSequenceNumber": {"shape": "HexLengthEquals2", "documentation": "<p>A number that identifies and differentiates payment cards with the same Primary Account Number (PAN).</p>"}, "TrackData": {"shape": "HexLengthBetween2And160", "documentation": "<p>The data on the two tracks of magnetic cards used for financial transactions. This includes the cardholder name, PAN, expiration date, bank ID (BIN) and several other numbers the issuing bank uses to validate the data received.</p>"}, "UnpredictableNumber": {"shape": "HexLengthBetween2And8", "documentation": "<p>A random number generated by the issuer.</p>"}}, "documentation": "<p>Parameters that are required to generate or verify Dynamic Card Verification Value (dCVV).</p>"}, "DynamicCardVerificationValue": {"type": "structure", "required": ["ApplicationTransactionCounter", "CardExpiryDate", "PanSequenceNumber", "ServiceCode"], "members": {"ApplicationTransactionCounter": {"shape": "HexLengthBetween2And4", "documentation": "<p>The transaction counter value that comes from the terminal.</p>"}, "CardExpiryDate": {"shape": "NumberLengthEquals4", "documentation": "<p>The expiry date of a payment card.</p>"}, "PanSequenceNumber": {"shape": "HexLengthEquals2", "documentation": "<p>A number that identifies and differentiates payment cards with the same Primary Account Number (PAN).</p>"}, "ServiceCode": {"shape": "NumberLengthEquals3", "documentation": "<p>The service code of the payment card. This is different from Card Security Code (CSC).</p>"}}, "documentation": "<p>Parameters that are required to generate or verify Dynamic Card Verification Value (dCVV).</p>"}, "EncryptDataInput": {"type": "structure", "required": ["EncryptionAttributes", "KeyIdentifier", "PlainText"], "members": {"EncryptionAttributes": {"shape": "EncryptionDecryptionAttributes", "documentation": "<p>The encryption key type and attributes for plaintext encryption.</p>"}, "KeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>keyARN</code> of the encryption key that Amazon Web Services Payment Cryptography uses for plaintext encryption.</p>", "location": "uri", "locationName": "KeyIdentifier"}, "PlainText": {"shape": "HexEvenLengthBetween16And4064", "documentation": "<p>The plaintext to be encrypted.</p>"}}}, "EncryptDataOutput": {"type": "structure", "required": ["CipherText", "KeyArn"], "members": {"CipherText": {"shape": "HexEvenLengthBetween16And4096", "documentation": "<p>The encrypted ciphertext.</p>"}, "KeyArn": {"shape": "KeyArn", "documentation": "<p>The <code>keyARN</code> of the encryption key that Amazon Web Services Payment Cryptography uses for plaintext encryption.</p>"}, "KeyCheckValue": {"shape": "KeyCheckValue", "documentation": "<p>The key check value (KCV) of the encryption key. The KCV is used to check if all parties holding a given key have the same key or to detect that a key has changed. Amazon Web Services Payment Cryptography calculates the KCV by using standard algorithms, typically by encrypting 8 or 16 bytes or \"00\" or \"01\" and then truncating the result to the first 3 bytes, or 6 hex digits, of the resulting cryptogram.</p>"}}}, "EncryptionDecryptionAttributes": {"type": "structure", "members": {"Asymmetric": {"shape": "AsymmetricEncryptionAttributes"}, "Dukpt": {"shape": "DukptEncryptionAttributes"}, "Symmetric": {"shape": "SymmetricEncryptionAttributes", "documentation": "<p>Parameters that are required to perform encryption and decryption using symmetric keys.</p>"}}, "documentation": "<p>Parameters that are required to perform encryption and decryption operations.</p>", "union": true}, "EncryptionMode": {"type": "string", "enum": ["ECB", "CBC", "CFB", "CFB1", "CFB8", "CFB64", "CFB128", "OFB"]}, "GenerateCardValidationDataInput": {"type": "structure", "required": ["GenerationAttributes", "KeyIdentifier", "PrimaryAccountNumber"], "members": {"GenerationAttributes": {"shape": "CardGenerationAttributes", "documentation": "<p>The algorithm for generating CVV or CSC values for the card within Amazon Web Services Payment Cryptography.</p>"}, "KeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>keyARN</code> of the CVK encryption key that Amazon Web Services Payment Cryptography uses to generate card data.</p>"}, "PrimaryAccountNumber": {"shape": "NumberLengthBetween12And19", "documentation": "<p>The Primary Account Number (PAN), a unique identifier for a payment credit or debit card that associates the card with a specific account holder.</p>"}, "ValidationDataLength": {"shape": "IntegerRangeBetween3And5Type", "documentation": "<p>The length of the CVV or CSC to be generated. The default value is 3.</p>"}}}, "GenerateCardValidationDataOutput": {"type": "structure", "required": ["KeyArn", "KeyCheckValue", "ValidationData"], "members": {"KeyArn": {"shape": "KeyArn", "documentation": "<p>The <code>keyARN</code> of the CVK encryption key that Amazon Web Services Payment Cryptography uses to generate CVV or CSC.</p>"}, "KeyCheckValue": {"shape": "KeyCheckValue", "documentation": "<p>The key check value (KCV) of the encryption key. The KCV is used to check if all parties holding a given key have the same key or to detect that a key has changed. Amazon Web Services Payment Cryptography calculates the KCV by using standard algorithms, typically by encrypting 8 or 16 bytes or \"00\" or \"01\" and then truncating the result to the first 3 bytes, or 6 hex digits, of the resulting cryptogram.</p>"}, "ValidationData": {"shape": "NumberLengthBetween3And5", "documentation": "<p>The CVV or CSC value that Amazon Web Services Payment Cryptography generates for the card.</p>"}}}, "GenerateMacInput": {"type": "structure", "required": ["GenerationAttributes", "KeyIdentifier", "MessageData"], "members": {"GenerationAttributes": {"shape": "MacAttributes", "documentation": "<p>The attributes and data values to use for MAC generation within Amazon Web Services Payment Cryptography.</p>"}, "KeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>keyARN</code> of the MAC generation encryption key.</p>"}, "MacLength": {"shape": "IntegerRangeBetween4And16", "documentation": "<p>The length of a MAC under generation.</p>"}, "MessageData": {"shape": "HexEvenLengthBetween2And4096", "documentation": "<p>The data for which a MAC is under generation.</p>"}}}, "GenerateMacOutput": {"type": "structure", "required": ["KeyArn", "KeyCheckValue", "<PERSON>"], "members": {"KeyArn": {"shape": "KeyArn", "documentation": "<p>The <code>keyARN</code> of the encryption key that Amazon Web Services Payment Cryptography uses for MAC generation.</p>"}, "KeyCheckValue": {"shape": "KeyCheckValue", "documentation": "<p>The key check value (KCV) of the encryption key. The KCV is used to check if all parties holding a given key have the same key or to detect that a key has changed. Amazon Web Services Payment Cryptography calculates the KCV by using standard algorithms, typically by encrypting 8 or 16 bytes or \"00\" or \"01\" and then truncating the result to the first 3 bytes, or 6 hex digits, of the resulting cryptogram.</p>"}, "Mac": {"shape": "HexLengthBetween4And128", "documentation": "<p>The MAC cryptogram generated within Amazon Web Services Payment Cryptography.</p>"}}}, "GeneratePinDataInput": {"type": "structure", "required": ["EncryptionKeyIdentifier", "GenerationAttributes", "GenerationKeyIdentifier", "PinBlockFormat", "PrimaryAccountNumber"], "members": {"EncryptionKeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>keyARN</code> of the PEK that Amazon Web Services Payment Cryptography uses to encrypt the PIN Block.</p>"}, "GenerationAttributes": {"shape": "PinGenerationAttributes", "documentation": "<p>The attributes and values to use for PIN, PVV, or PIN Offset generation.</p>"}, "GenerationKeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>keyARN</code> of the PEK that Amazon Web Services Payment Cryptography uses for pin data generation.</p>"}, "PinBlockFormat": {"shape": "PinBlockFormatForPinData", "documentation": "<p>The PIN encoding format for pin data generation as specified in ISO 9564. Amazon Web Services Payment Cryptography supports <code>ISO_Format_0</code> and <code>ISO_Format_3</code>.</p> <p>The <code>ISO_Format_0</code> PIN block format is equivalent to the ANSI X9.8, VISA-1, and ECI-1 PIN block formats. It is similar to a VISA-4 PIN block format. It supports a PIN from 4 to 12 digits in length.</p> <p>The <code>ISO_Format_3</code> PIN block format is the same as <code>ISO_Format_0</code> except that the fill digits are random values from 10 to 15.</p>"}, "PinDataLength": {"shape": "IntegerRangeBetween4And12", "documentation": "<p>The length of PIN under generation.</p>", "box": true}, "PrimaryAccountNumber": {"shape": "NumberLengthBetween12And19", "documentation": "<p>The Primary Account Number (PAN), a unique identifier for a payment credit or debit card that associates the card with a specific account holder.</p>"}}}, "GeneratePinDataOutput": {"type": "structure", "required": ["EncryptedPinBlock", "EncryptionKeyArn", "EncryptionKeyCheckValue", "GenerationKeyArn", "GenerationKeyCheckValue", "PinData"], "members": {"EncryptedPinBlock": {"shape": "HexLengthBetween16And32", "documentation": "<p>The PIN block encrypted under PEK from Amazon Web Services Payment Cryptography. The encrypted PIN block is a composite of PAN (Primary Account Number) and PIN (Personal Identification Number), generated in accordance with ISO 9564 standard.</p>"}, "EncryptionKeyArn": {"shape": "KeyArn", "documentation": "<p>The <code>keyARN</code> of the PEK that Amazon Web Services Payment Cryptography uses for encrypted pin block generation.</p>"}, "EncryptionKeyCheckValue": {"shape": "KeyCheckValue", "documentation": "<p>The key check value (KCV) of the encryption key. The KCV is used to check if all parties holding a given key have the same key or to detect that a key has changed. Amazon Web Services Payment Cryptography calculates the KCV by using standard algorithms, typically by encrypting 8 or 16 bytes or \"00\" or \"01\" and then truncating the result to the first 3 bytes, or 6 hex digits, of the resulting cryptogram.</p>"}, "GenerationKeyArn": {"shape": "KeyArn", "documentation": "<p>The <code>keyARN</code> of the pin data generation key that Amazon Web Services Payment Cryptography uses for PIN, PVV or PIN Offset generation.</p>"}, "GenerationKeyCheckValue": {"shape": "KeyCheckValue", "documentation": "<p>The key check value (KCV) of the encryption key. The KCV is used to check if all parties holding a given key have the same key or to detect that a key has changed. Amazon Web Services Payment Cryptography calculates the KCV by using standard algorithms, typically by encrypting 8 or 16 bytes or \"00\" or \"01\" and then truncating the result to the first 3 bytes, or 6 hex digits, of the resulting cryptogram.</p>"}, "PinData": {"shape": "PinData", "documentation": "<p>The attributes and values Amazon Web Services Payment Cryptography uses for pin data generation.</p>"}}}, "HexEvenLengthBetween16And32": {"type": "string", "max": 32, "min": 16, "pattern": "^(?:[0-9a-fA-F][0-9a-fA-F])+$", "sensitive": true}, "HexEvenLengthBetween16And4064": {"type": "string", "max": 4064, "min": 16, "pattern": "^(?:[0-9a-fA-F][0-9a-fA-F])+$", "sensitive": true}, "HexEvenLengthBetween16And4096": {"type": "string", "max": 4096, "min": 16, "pattern": "^(?:[0-9a-fA-F][0-9a-fA-F])+$", "sensitive": true}, "HexEvenLengthBetween2And4096": {"type": "string", "max": 4096, "min": 2, "pattern": "^(?:[0-9a-fA-F][0-9a-fA-F])+$", "sensitive": true}, "HexEvenLengthBetween4And128": {"type": "string", "max": 128, "min": 4, "pattern": "^(?:[0-9a-fA-F][0-9a-fA-F])+$", "sensitive": true}, "HexLength16Or32": {"type": "string", "max": 32, "min": 16, "pattern": "^(?:[0-9a-fA-F]{16}|[0-9a-fA-F]{32})$", "sensitive": true}, "HexLengthBetween10And24": {"type": "string", "max": 24, "min": 10, "pattern": "^[0-9a-fA-F]+$"}, "HexLengthBetween16And32": {"type": "string", "max": 32, "min": 16, "pattern": "^[0-9a-fA-F]+$"}, "HexLengthBetween1And16": {"type": "string", "max": 16, "min": 1, "pattern": "^[0-9a-fA-F]+$"}, "HexLengthBetween2And1024": {"type": "string", "max": 1024, "min": 2, "pattern": "^[0-9a-fA-F]+$"}, "HexLengthBetween2And160": {"type": "string", "max": 160, "min": 2, "pattern": "^[0-9a-fA-F]+$"}, "HexLengthBetween2And4": {"type": "string", "max": 4, "min": 2, "pattern": "^[0-9a-fA-F]+$"}, "HexLengthBetween2And8": {"type": "string", "max": 8, "min": 2, "pattern": "^[0-9a-fA-F]+$"}, "HexLengthBetween4And128": {"type": "string", "max": 128, "min": 4, "pattern": "^[0-9a-fA-F]+$"}, "HexLengthEquals1": {"type": "string", "max": 1, "min": 1, "pattern": "^[0-9A-F]+$"}, "HexLengthEquals16": {"type": "string", "max": 16, "min": 16, "pattern": "^[0-9a-fA-F]+$"}, "HexLengthEquals2": {"type": "string", "max": 2, "min": 2, "pattern": "^[0-9a-fA-F]+$"}, "HexLengthEquals4": {"type": "string", "max": 4, "min": 4, "pattern": "^[0-9a-fA-F]+$"}, "HexLengthEquals8": {"type": "string", "max": 8, "min": 8, "pattern": "^[0-9a-fA-F]+$"}, "Ibm3624NaturalPin": {"type": "structure", "required": ["DecimalizationTable", "PinValidationData", "PinValidationDataPadCharacter"], "members": {"DecimalizationTable": {"shape": "NumberLengthEquals16", "documentation": "<p>The decimalization table to use for IBM 3624 PIN algorithm. The table is used to convert the algorithm intermediate result from hexadecimal characters to decimal.</p>"}, "PinValidationData": {"shape": "NumberLengthBetween4And16", "documentation": "<p>The unique data for cardholder identification.</p>"}, "PinValidationDataPadCharacter": {"shape": "HexLengthEquals1", "documentation": "<p>The padding character for validation data.</p>"}}, "documentation": "<p>Parameters that are required to generate or verify Ibm3624 natural PIN.</p>"}, "Ibm3624PinFromOffset": {"type": "structure", "required": ["DecimalizationTable", "PinOffset", "PinValidationData", "PinValidationDataPadCharacter"], "members": {"DecimalizationTable": {"shape": "NumberLengthEquals16", "documentation": "<p>The decimalization table to use for IBM 3624 PIN algorithm. The table is used to convert the algorithm intermediate result from hexadecimal characters to decimal.</p>"}, "PinOffset": {"shape": "NumberLengthBetween4And12", "documentation": "<p>The PIN offset value.</p>"}, "PinValidationData": {"shape": "NumberLengthBetween4And16", "documentation": "<p>The unique data for cardholder identification.</p>"}, "PinValidationDataPadCharacter": {"shape": "HexLengthEquals1", "documentation": "<p>The padding character for validation data.</p>"}}, "documentation": "<p>Parameters that are required to generate or verify Ibm3624 PIN from offset PIN.</p>"}, "Ibm3624PinOffset": {"type": "structure", "required": ["DecimalizationTable", "EncryptedPinBlock", "PinValidationData", "PinValidationDataPadCharacter"], "members": {"DecimalizationTable": {"shape": "NumberLengthEquals16", "documentation": "<p>The decimalization table to use for IBM 3624 PIN algorithm. The table is used to convert the algorithm intermediate result from hexadecimal characters to decimal.</p>"}, "EncryptedPinBlock": {"shape": "HexLengthBetween16And32", "documentation": "<p>The encrypted PIN block data. According to ISO 9564 standard, a PIN Block is an encoded representation of a payment card Personal Account Number (PAN) and the cardholder Personal Identification Number (PIN).</p>"}, "PinValidationData": {"shape": "NumberLengthBetween4And16", "documentation": "<p>The unique data for cardholder identification.</p>"}, "PinValidationDataPadCharacter": {"shape": "HexLengthEquals1", "documentation": "<p>The padding character for validation data.</p>"}}, "documentation": "<p>Pparameters that are required to generate or verify Ibm3624 PIN offset PIN.</p>"}, "Ibm3624PinVerification": {"type": "structure", "required": ["DecimalizationTable", "PinOffset", "PinValidationData", "PinValidationDataPadCharacter"], "members": {"DecimalizationTable": {"shape": "NumberLengthEquals16", "documentation": "<p>The decimalization table to use for IBM 3624 PIN algorithm. The table is used to convert the algorithm intermediate result from hexadecimal characters to decimal.</p>"}, "PinOffset": {"shape": "NumberLengthBetween4And12", "documentation": "<p>The PIN offset value.</p>"}, "PinValidationData": {"shape": "NumberLengthBetween4And16", "documentation": "<p>The unique data for cardholder identification.</p>"}, "PinValidationDataPadCharacter": {"shape": "HexLengthEquals1", "documentation": "<p>The padding character for validation data.</p>"}}, "documentation": "<p>Parameters that are required to generate or verify Ibm3624 PIN verification PIN.</p>"}, "Ibm3624RandomPin": {"type": "structure", "required": ["DecimalizationTable", "PinValidationData", "PinValidationDataPadCharacter"], "members": {"DecimalizationTable": {"shape": "NumberLengthEquals16", "documentation": "<p>The decimalization table to use for IBM 3624 PIN algorithm. The table is used to convert the algorithm intermediate result from hexadecimal characters to decimal.</p>"}, "PinValidationData": {"shape": "NumberLengthBetween4And16", "documentation": "<p>The unique data for cardholder identification.</p>"}, "PinValidationDataPadCharacter": {"shape": "HexLengthEquals1", "documentation": "<p>The padding character for validation data.</p>"}}, "documentation": "<p>Parameters that are required to generate or verify Ibm3624 random PIN.</p>"}, "IntegerRangeBetween0And9": {"type": "integer", "box": true, "max": 9, "min": 0}, "IntegerRangeBetween3And5Type": {"type": "integer", "box": true, "max": 5, "min": 3}, "IntegerRangeBetween4And12": {"type": "integer", "max": 12, "min": 4}, "IntegerRangeBetween4And16": {"type": "integer", "box": true, "max": 16, "min": 4}, "InternalServerException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The request processing has failed because of an unknown error, exception, or failure.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "KeyArn": {"type": "string", "max": 150, "min": 70, "pattern": "^arn:aws:payment-cryptography:[a-z]{2}-[a-z]{1,16}-[0-9]+:[0-9]{12}:key/[0-9a-zA-Z]{16,64}$"}, "KeyArnOrKeyAliasType": {"type": "string", "max": 322, "min": 7, "pattern": "^arn:aws:payment-cryptography:[a-z]{2}-[a-z]{1,16}-[0-9]+:[0-9]{12}:(key/[0-9a-zA-Z]{16,64}|alias/[a-zA-Z0-9/_-]+)$|^alias/[a-zA-Z0-9/_-]+$"}, "KeyCheckValue": {"type": "string", "max": 16, "min": 4, "pattern": "^[0-9a-fA-F]+$"}, "MacAlgorithm": {"type": "string", "enum": ["ISO9797_ALGORITHM1", "ISO9797_ALGORITHM3", "CMAC", "HMAC_SHA224", "HMAC_SHA256", "HMAC_SHA384", "HMAC_SHA512"]}, "MacAlgorithmDukpt": {"type": "structure", "required": ["DukptKeyVariant", "KeySerialNumber"], "members": {"DukptDerivationType": {"shape": "DukptDerivationType", "documentation": "<p>The key type derived using DUKPT from a Base Derivation Key (BDK) and Key Serial Number (KSN). This must be less than or equal to the strength of the BDK. For example, you can't use <code>AES_128</code> as a derivation type for a BDK of <code>AES_128</code> or <code>TDES_2KEY</code>.</p>"}, "DukptKeyVariant": {"shape": "DukptKeyVariant", "documentation": "<p>The type of use of DUKPT, which can be MAC generation, MAC verification, or both.</p>"}, "KeySerialNumber": {"shape": "HexLengthBetween10And24", "documentation": "<p>The unique identifier known as Key Serial Number (KSN) that comes from an encrypting device using DUKPT encryption method. The KSN is derived from the encrypting device unique identifier and an internal transaction counter.</p>"}}, "documentation": "<p>Parameters required for DUKPT MAC generation and verification.</p>"}, "MacAlgorithmEmv": {"type": "structure", "required": ["MajorKeyDerivationMode", "PanSequenceNumber", "PrimaryAccountNumber", "SessionKeyDerivationMode", "SessionKeyDerivationValue"], "members": {"MajorKeyDerivationMode": {"shape": "MajorKeyDerivationMode", "documentation": "<p>The method to use when deriving the master key for EMV MAC generation or verification.</p>"}, "PanSequenceNumber": {"shape": "HexLengthEquals2", "documentation": "<p>A number that identifies and differentiates payment cards with the same Primary Account Number (PAN).</p>"}, "PrimaryAccountNumber": {"shape": "NumberLengthBetween12And19", "documentation": "<p>The Primary Account Number (PAN), a unique identifier for a payment credit or debit card and associates the card to a specific account holder.</p>"}, "SessionKeyDerivationMode": {"shape": "SessionKeyDerivationMode", "documentation": "<p>The method of deriving a session key for EMV MAC generation or verification.</p>"}, "SessionKeyDerivationValue": {"shape": "SessionKeyDerivationValue", "documentation": "<p>Parameters that are required to generate session key for EMV generation and verification.</p>"}}, "documentation": "<p>Parameters that are required for EMV MAC generation and verification.</p>"}, "MacAttributes": {"type": "structure", "members": {"Algorithm": {"shape": "MacAlgorithm", "documentation": "<p>The encryption algorithm for MAC generation or verification.</p>"}, "DukptCmac": {"shape": "MacAlgorithmDukpt", "documentation": "<p>Parameters that are required for MAC generation or verification using DUKPT CMAC algorithm.</p>"}, "DukptIso9797Algorithm1": {"shape": "MacAlgorithmDukpt", "documentation": "<p>Parameters that are required for MAC generation or verification using DUKPT ISO 9797 algorithm1.</p>"}, "DukptIso9797Algorithm3": {"shape": "MacAlgorithmDukpt", "documentation": "<p>Parameters that are required for MAC generation or verification using DUKPT ISO 9797 algorithm2.</p>"}, "EmvMac": {"shape": "MacAlgorithmEmv", "documentation": "<p>Parameters that are required for MAC generation or verification using EMV MAC algorithm.</p>"}}, "documentation": "<p>Parameters that are required for DUKPT, HMAC, or EMV MAC generation or verification.</p>", "union": true}, "MajorKeyDerivationMode": {"type": "string", "enum": ["EMV_OPTION_A", "EMV_OPTION_B"]}, "NumberLengthBetween12And19": {"type": "string", "max": 19, "min": 12, "pattern": "^[0-9]+$", "sensitive": true}, "NumberLengthBetween3And5": {"type": "string", "max": 5, "min": 3, "pattern": "^[0-9]+$"}, "NumberLengthBetween4And12": {"type": "string", "max": 12, "min": 4, "pattern": "^[0-9]+$"}, "NumberLengthBetween4And16": {"type": "string", "max": 16, "min": 4, "pattern": "^[0-9]+$"}, "NumberLengthEquals16": {"type": "string", "max": 16, "min": 16, "pattern": "^[0-9]+$"}, "NumberLengthEquals3": {"type": "string", "max": 3, "min": 3, "pattern": "^[0-9]+$"}, "NumberLengthEquals4": {"type": "string", "max": 4, "min": 4, "pattern": "^[0-9]+$"}, "PaddingType": {"type": "string", "enum": ["PKCS1", "OAEP_SHA1", "OAEP_SHA256", "OAEP_SHA512"]}, "PinBlockFormatForPinData": {"type": "string", "enum": ["ISO_FORMAT_0", "ISO_FORMAT_3"]}, "PinData": {"type": "structure", "members": {"PinOffset": {"shape": "NumberLengthBetween4And12", "documentation": "<p>The PIN offset value.</p>"}, "VerificationValue": {"shape": "NumberLengthBetween4And12", "documentation": "<p>The unique data to identify a cardholder. In most cases, this is the same as cardholder's Primary Account Number (PAN). If a value is not provided, it defaults to PAN.</p>"}}, "documentation": "<p>Parameters that are required to generate, translate, or verify PIN data.</p>", "union": true}, "PinGenerationAttributes": {"type": "structure", "members": {"Ibm3624NaturalPin": {"shape": "Ibm3624NaturalPin", "documentation": "<p>Parameters that are required to generate or verify Ibm3624 natural PIN.</p>"}, "Ibm3624PinFromOffset": {"shape": "Ibm3624PinFromOffset", "documentation": "<p>Parameters that are required to generate or verify Ibm3624 PIN from offset PIN.</p>"}, "Ibm3624PinOffset": {"shape": "Ibm3624PinOffset", "documentation": "<p>Parameters that are required to generate or verify Ibm3624 PIN offset PIN.</p>"}, "Ibm3624RandomPin": {"shape": "Ibm3624RandomPin", "documentation": "<p>Parameters that are required to generate or verify Ibm3624 random PIN.</p>"}, "VisaPin": {"shape": "VisaPin", "documentation": "<p>Parameters that are required to generate or verify Visa PIN.</p>"}, "VisaPinVerificationValue": {"shape": "VisaPinVerificationValue", "documentation": "<p>Parameters that are required to generate or verify Visa PIN Verification Value (PVV).</p>"}}, "documentation": "<p>Parameters that are required for PIN data generation.</p>", "union": true}, "PinVerificationAttributes": {"type": "structure", "members": {"Ibm3624Pin": {"shape": "Ibm3624PinVerification", "documentation": "<p>Parameters that are required to generate or verify Ibm3624 PIN.</p>"}, "VisaPin": {"shape": "VisaPinVerification", "documentation": "<p>Parameters that are required to generate or verify Visa PIN.</p>"}}, "documentation": "<p>Parameters that are required for PIN data verification.</p>", "union": true}, "ReEncryptDataInput": {"type": "structure", "required": ["CipherText", "IncomingEncryptionAttributes", "IncomingKeyIdentifier", "OutgoingEncryptionAttributes", "OutgoingKeyIdentifier"], "members": {"CipherText": {"shape": "HexEvenLengthBetween16And4096", "documentation": "<p>Ciphertext to be encrypted. The minimum allowed length is 16 bytes and maximum allowed length is 4096 bytes.</p>"}, "IncomingEncryptionAttributes": {"shape": "ReEncryptionAttributes", "documentation": "<p>The attributes and values for incoming ciphertext.</p>"}, "IncomingKeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>keyARN</code> of the encryption key of incoming ciphertext data.</p>", "location": "uri", "locationName": "IncomingKeyIdentifier"}, "OutgoingEncryptionAttributes": {"shape": "ReEncryptionAttributes", "documentation": "<p>The attributes and values for outgoing ciphertext data after encryption by Amazon Web Services Payment Cryptography.</p>"}, "OutgoingKeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>keyARN</code> of the encryption key of outgoing ciphertext data after encryption by Amazon Web Services Payment Cryptography.</p>"}}}, "ReEncryptDataOutput": {"type": "structure", "required": ["CipherText", "KeyArn", "KeyCheckValue"], "members": {"CipherText": {"shape": "HexEvenLengthBetween16And4096", "documentation": "<p>The encrypted ciphertext.</p>"}, "KeyArn": {"shape": "KeyArn", "documentation": "<p>The keyARN (Amazon Resource Name) of the encryption key that Amazon Web Services Payment Cryptography uses for plaintext encryption.</p>"}, "KeyCheckValue": {"shape": "KeyCheckValue", "documentation": "<p>The key check value (KCV) of the encryption key. The KCV is used to check if all parties holding a given key have the same key or to detect that a key has changed. Amazon Web Services Payment Cryptography calculates the KCV by using standard algorithms, typically by encrypting 8 or 16 bytes or \"00\" or \"01\" and then truncating the result to the first 3 bytes, or 6 hex digits, of the resulting cryptogram.</p>"}}}, "ReEncryptionAttributes": {"type": "structure", "members": {"Dukpt": {"shape": "DukptEncryptionAttributes"}, "Symmetric": {"shape": "SymmetricEncryptionAttributes", "documentation": "<p>Parameters that are required to encrypt data using symmetric keys.</p>"}}, "documentation": "<p>Parameters that are required to perform reencryption operation.</p>", "union": true}, "ResourceNotFoundException": {"type": "structure", "members": {"ResourceId": {"shape": "String", "documentation": "<p>The resource that is missing.</p>"}}, "documentation": "<p>The request was denied due to an invalid resource error.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "SessionKeyAmex": {"type": "structure", "required": ["PanSequenceNumber", "PrimaryAccountNumber"], "members": {"PanSequenceNumber": {"shape": "HexLengthEquals2", "documentation": "<p>A number that identifies and differentiates payment cards with the same Primary Account Number (PAN).</p>"}, "PrimaryAccountNumber": {"shape": "NumberLengthBetween12And19", "documentation": "<p>The Primary Account Number (PAN) of the cardholder. A PAN is a unique identifier for a payment credit or debit card and associates the card to a specific account holder.</p>"}}, "documentation": "<p>Parameters to derive session key for an Amex payment card.</p>"}, "SessionKeyDerivation": {"type": "structure", "members": {"Amex": {"shape": "SessionKeyAmex", "documentation": "<p>Parameters to derive session key for an Amex payment card for ARQC verification.</p>"}, "Emv2000": {"shape": "SessionKeyEmv2000", "documentation": "<p>Parameters to derive session key for an Emv2000 payment card for ARQC verification.</p>"}, "EmvCommon": {"shape": "SessionKeyEmvCommon", "documentation": "<p>Parameters to derive session key for an Emv common payment card for ARQC verification.</p>"}, "Mastercard": {"shape": "SessionKeyMastercard", "documentation": "<p>Parameters to derive session key for a Mastercard payment card for ARQC verification.</p>"}, "Visa": {"shape": "SessionKeyVisa", "documentation": "<p>Parameters to derive session key for a Visa payment cardfor ARQC verification.</p>"}}, "documentation": "<p>Parameters to derive a session key for Authorization Response Cryptogram (ARQC) verification.</p>", "union": true}, "SessionKeyDerivationMode": {"type": "string", "enum": ["EMV_COMMON_SESSION_KEY", "EMV2000", "AMEX", "MASTERCARD_SESSION_KEY", "VISA"]}, "SessionKeyDerivationValue": {"type": "structure", "members": {"ApplicationCryptogram": {"shape": "HexLengthEquals16", "documentation": "<p>The cryptogram provided by the terminal during transaction processing.</p>"}, "ApplicationTransactionCounter": {"shape": "HexLengthBetween2And4", "documentation": "<p>The transaction counter that is provided by the terminal during transaction processing.</p>"}}, "documentation": "<p>Parameters to derive session key value using a MAC EMV algorithm.</p>", "union": true}, "SessionKeyEmv2000": {"type": "structure", "required": ["ApplicationTransactionCounter", "PanSequenceNumber", "PrimaryAccountNumber"], "members": {"ApplicationTransactionCounter": {"shape": "HexLengthBetween2And4", "documentation": "<p>The transaction counter that is provided by the terminal during transaction processing.</p>"}, "PanSequenceNumber": {"shape": "HexLengthEquals2", "documentation": "<p>A number that identifies and differentiates payment cards with the same Primary Account Number (PAN).</p>"}, "PrimaryAccountNumber": {"shape": "NumberLengthBetween12And19", "documentation": "<p>The Primary Account Number (PAN) of the cardholder. A PAN is a unique identifier for a payment credit or debit card and associates the card to a specific account holder.</p>"}}, "documentation": "<p>Parameters to derive session key for an Emv2000 payment card for ARQC verification.</p>"}, "SessionKeyEmvCommon": {"type": "structure", "required": ["ApplicationTransactionCounter", "PanSequenceNumber", "PrimaryAccountNumber"], "members": {"ApplicationTransactionCounter": {"shape": "HexLengthBetween2And4", "documentation": "<p>The transaction counter that is provided by the terminal during transaction processing.</p>"}, "PanSequenceNumber": {"shape": "HexLengthEquals2", "documentation": "<p>A number that identifies and differentiates payment cards with the same Primary Account Number (PAN).</p>"}, "PrimaryAccountNumber": {"shape": "NumberLengthBetween12And19", "documentation": "<p>The Primary Account Number (PAN) of the cardholder. A PAN is a unique identifier for a payment credit or debit card and associates the card to a specific account holder.</p>"}}, "documentation": "<p>Parameters to derive session key for an Emv common payment card for ARQC verification.</p>"}, "SessionKeyMastercard": {"type": "structure", "required": ["ApplicationTransactionCounter", "PanSequenceNumber", "PrimaryAccountNumber", "UnpredictableNumber"], "members": {"ApplicationTransactionCounter": {"shape": "HexLengthBetween2And4", "documentation": "<p>The transaction counter that is provided by the terminal during transaction processing.</p>"}, "PanSequenceNumber": {"shape": "HexLengthEquals2", "documentation": "<p>A number that identifies and differentiates payment cards with the same Primary Account Number (PAN).</p>"}, "PrimaryAccountNumber": {"shape": "NumberLengthBetween12And19", "documentation": "<p>The Primary Account Number (PAN) of the cardholder. A PAN is a unique identifier for a payment credit or debit card and associates the card to a specific account holder.</p>"}, "UnpredictableNumber": {"shape": "HexLengthBetween2And8", "documentation": "<p>A random number generated by the issuer.</p>"}}, "documentation": "<p>Parameters to derive session key for Mastercard payment card for ARQC verification.</p>"}, "SessionKeyVisa": {"type": "structure", "required": ["PanSequenceNumber", "PrimaryAccountNumber"], "members": {"PanSequenceNumber": {"shape": "HexLengthEquals2", "documentation": "<p>A number that identifies and differentiates payment cards with the same Primary Account Number (PAN).</p>"}, "PrimaryAccountNumber": {"shape": "NumberLengthBetween12And19", "documentation": "<p>The Primary Account Number (PAN) of the cardholder. A PAN is a unique identifier for a payment credit or debit card and associates the card to a specific account holder.</p>"}}, "documentation": "<p>Parameters to derive session key for Visa payment card for ARQC verification.</p>"}, "String": {"type": "string"}, "SymmetricEncryptionAttributes": {"type": "structure", "required": ["Mode"], "members": {"InitializationVector": {"shape": "HexLength16Or32", "documentation": "<p>An input to cryptographic primitive used to provide the intial state. The <code>InitializationVector</code> is typically required have a random or psuedo-random value, but sometimes it only needs to be unpredictable or unique. If a value is not provided, Amazon Web Services Payment Cryptography generates a random value.</p>"}, "Mode": {"shape": "EncryptionMode", "documentation": "<p>The block cipher mode of operation. Block ciphers are designed to encrypt a block of data of fixed size (for example, 128 bits). The size of the input block is usually same as the size of the encrypted output block, while the key length can be different. A mode of operation describes how to repeatedly apply a cipher's single-block operation to securely transform amounts of data larger than a block.</p>"}, "PaddingType": {"shape": "PaddingType", "documentation": "<p>The padding to be included with the data.</p>"}}, "documentation": "<p>Parameters requried to encrypt plaintext data using symmetric keys.</p>"}, "ThrottlingException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The request was denied due to request throttling.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true}, "TranslatePinDataInput": {"type": "structure", "required": ["EncryptedPinBlock", "IncomingKeyIdentifier", "IncomingTranslationAttributes", "OutgoingKeyIdentifier", "OutgoingTranslationAttributes"], "members": {"EncryptedPinBlock": {"shape": "HexEvenLengthBetween16And32", "documentation": "<p>The encrypted PIN block data that Amazon Web Services Payment Cryptography translates.</p>"}, "IncomingDukptAttributes": {"shape": "DukptDerivationAttributes", "documentation": "<p>The attributes and values to use for incoming DUKPT encryption key for PIN block tranlation.</p>"}, "IncomingKeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>keyARN</code> of the encryption key under which incoming PIN block data is encrypted. This key type can be PEK or BDK.</p>"}, "IncomingTranslationAttributes": {"shape": "TranslationIsoFormats", "documentation": "<p>The format of the incoming PIN block data for tranlation within Amazon Web Services Payment Cryptography.</p>"}, "OutgoingDukptAttributes": {"shape": "DukptDerivationAttributes", "documentation": "<p>The attributes and values to use for outgoing DUKPT encryption key after PIN block translation.</p>"}, "OutgoingKeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>keyARN</code> of the encryption key for encrypting outgoing PIN block data. This key type can be PEK or BDK.</p>"}, "OutgoingTranslationAttributes": {"shape": "TranslationIsoFormats", "documentation": "<p>The format of the outgoing PIN block data after tranlation by Amazon Web Services Payment Cryptography.</p>"}}}, "TranslatePinDataOutput": {"type": "structure", "required": ["KeyArn", "KeyCheckValue", "Pin<PERSON>lock"], "members": {"KeyArn": {"shape": "KeyArn", "documentation": "<p>The <code>keyARN</code> of the encryption key that Amazon Web Services Payment Cryptography uses to encrypt outgoing PIN block data after translation.</p>"}, "KeyCheckValue": {"shape": "KeyCheckValue", "documentation": "<p>The key check value (KCV) of the encryption key. The KCV is used to check if all parties holding a given key have the same key or to detect that a key has changed. Amazon Web Services Payment Cryptography calculates the KCV by using standard algorithms, typically by encrypting 8 or 16 bytes or \"00\" or \"01\" and then truncating the result to the first 3 bytes, or 6 hex digits, of the resulting cryptogram.</p>"}, "PinBlock": {"shape": "HexLengthBetween16And32", "documentation": "<p>The ougoing encrypted PIN block data after tranlation.</p>"}}}, "TranslationIsoFormats": {"type": "structure", "members": {"IsoFormat0": {"shape": "TranslationPinDataIsoFormat034", "documentation": "<p>Parameters that are required for ISO9564 PIN format 0 tranlation.</p>"}, "IsoFormat1": {"shape": "TranslationPinDataIsoFormat1", "documentation": "<p>Parameters that are required for ISO9564 PIN format 1 tranlation.</p>"}, "IsoFormat3": {"shape": "TranslationPinDataIsoFormat034", "documentation": "<p>Parameters that are required for ISO9564 PIN format 3 tranlation.</p>"}, "IsoFormat4": {"shape": "TranslationPinDataIsoFormat034", "documentation": "<p>Parameters that are required for ISO9564 PIN format 4 tranlation.</p>"}}, "documentation": "<p>Parameters that are required for translation between ISO9564 PIN block formats 0,1,3,4.</p>", "union": true}, "TranslationPinDataIsoFormat034": {"type": "structure", "required": ["PrimaryAccountNumber"], "members": {"PrimaryAccountNumber": {"shape": "NumberLengthBetween12And19", "documentation": "<p>The Primary Account Number (PAN) of the cardholder. A PAN is a unique identifier for a payment credit or debit card and associates the card to a specific account holder.</p>"}}, "documentation": "<p>Parameters that are required for tranlation between ISO9564 PIN format 0,3,4 tranlation.</p>"}, "TranslationPinDataIsoFormat1": {"type": "structure", "members": {}, "documentation": "<p>Parameters that are required for ISO9564 PIN format 1 tranlation.</p>"}, "ValidationException": {"type": "structure", "required": ["message"], "members": {"fieldList": {"shape": "ValidationExceptionFieldList", "documentation": "<p>The request was denied due to an invalid request error.</p>"}, "message": {"shape": "String"}}, "documentation": "<p>The request was denied due to an invalid request error.</p>", "exception": true}, "ValidationExceptionField": {"type": "structure", "required": ["message", "path"], "members": {"message": {"shape": "String", "documentation": "<p>The request was denied due to an invalid request error.</p>"}, "path": {"shape": "String", "documentation": "<p>The request was denied due to an invalid request error.</p>"}}, "documentation": "<p>The request was denied due to an invalid request error.</p>"}, "ValidationExceptionFieldList": {"type": "list", "member": {"shape": "ValidationExceptionField"}}, "VerificationFailedException": {"type": "structure", "required": ["Message", "Reason"], "members": {"Message": {"shape": "String"}, "Reason": {"shape": "VerificationFailedReason", "documentation": "<p>The reason for the exception.</p>"}}, "documentation": "<p>This request failed verification.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "VerificationFailedReason": {"type": "string", "enum": ["INVALID_MAC", "INVALID_PIN", "INVALID_VALIDATION_DATA", "INVALID_AUTH_REQUEST_CRYPTOGRAM"]}, "VerifyAuthRequestCryptogramInput": {"type": "structure", "required": ["AuthRequestCryptogram", "KeyIdentifier", "MajorKeyDerivationMode", "SessionKeyDerivationAttributes", "TransactionData"], "members": {"AuthRequestCryptogram": {"shape": "HexLengthEquals16", "documentation": "<p>The auth request cryptogram imported into Amazon Web Services Payment Cryptography for ARQC verification using a major encryption key and transaction data.</p>"}, "AuthResponseAttributes": {"shape": "CryptogramAuthResponse", "documentation": "<p>The attributes and values for auth request cryptogram verification. These parameters are required in case using ARPC Method 1 or Method 2 for ARQC verification.</p>"}, "KeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>keyARN</code> of the major encryption key that Amazon Web Services Payment Cryptography uses for ARQC verification.</p>"}, "MajorKeyDerivationMode": {"shape": "MajorKeyDerivationMode", "documentation": "<p>The method to use when deriving the major encryption key for ARQC verification within Amazon Web Services Payment Cryptography. The same key derivation mode was used for ARQC generation outside of Amazon Web Services Payment Cryptography.</p>"}, "SessionKeyDerivationAttributes": {"shape": "SessionKeyDerivation", "documentation": "<p>The attributes and values to use for deriving a session key for ARQC verification within Amazon Web Services Payment Cryptography. The same attributes were used for ARQC generation outside of Amazon Web Services Payment Cryptography.</p>"}, "TransactionData": {"shape": "HexLengthBetween2And1024", "documentation": "<p>The transaction data that Amazon Web Services Payment Cryptography uses for ARQC verification. The same transaction is used for ARQC generation outside of Amazon Web Services Payment Cryptography.</p>"}}}, "VerifyAuthRequestCryptogramOutput": {"type": "structure", "required": ["KeyArn", "KeyCheckValue"], "members": {"AuthResponseValue": {"shape": "HexLengthBetween1And16", "documentation": "<p>The result for ARQC verification or ARPC generation within Amazon Web Services Payment Cryptography.</p>"}, "KeyArn": {"shape": "KeyArn", "documentation": "<p>The <code>keyARN</code> of the major encryption key that Amazon Web Services Payment Cryptography uses for ARQC verification.</p>"}, "KeyCheckValue": {"shape": "KeyCheckValue", "documentation": "<p>The key check value (KCV) of the encryption key. The KCV is used to check if all parties holding a given key have the same key or to detect that a key has changed. Amazon Web Services Payment Cryptography calculates the KCV by using standard algorithms, typically by encrypting 8 or 16 bytes or \"00\" or \"01\" and then truncating the result to the first 3 bytes, or 6 hex digits, of the resulting cryptogram.</p>"}}}, "VerifyCardValidationDataInput": {"type": "structure", "required": ["KeyIdentifier", "PrimaryAccountNumber", "ValidationData", "VerificationAttributes"], "members": {"KeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>keyARN</code> of the CVK encryption key that Amazon Web Services Payment Cryptography uses to verify card data.</p>"}, "PrimaryAccountNumber": {"shape": "NumberLengthBetween12And19", "documentation": "<p>The Primary Account Number (PAN), a unique identifier for a payment credit or debit card that associates the card with a specific account holder.</p>"}, "ValidationData": {"shape": "NumberLengthBetween3And5", "documentation": "<p>The CVV or CSC value for use for card data verification within Amazon Web Services Payment Cryptography.</p>"}, "VerificationAttributes": {"shape": "CardVerificationAttributes", "documentation": "<p>The algorithm to use for verification of card data within Amazon Web Services Payment Cryptography.</p>"}}}, "VerifyCardValidationDataOutput": {"type": "structure", "required": ["KeyArn", "KeyCheckValue"], "members": {"KeyArn": {"shape": "KeyArn", "documentation": "<p>The <code>keyARN</code> of the CVK encryption key that Amazon Web Services Payment Cryptography uses to verify CVV or CSC.</p>"}, "KeyCheckValue": {"shape": "KeyCheckValue", "documentation": "<p>The key check value (KCV) of the encryption key. The KCV is used to check if all parties holding a given key have the same key or to detect that a key has changed. Amazon Web Services Payment Cryptography calculates the KCV by using standard algorithms, typically by encrypting 8 or 16 bytes or \"00\" or \"01\" and then truncating the result to the first 3 bytes, or 6 hex digits, of the resulting cryptogram.</p>"}}}, "VerifyMacInput": {"type": "structure", "required": ["KeyIdentifier", "<PERSON>", "MessageData", "VerificationAttributes"], "members": {"KeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>keyARN</code> of the encryption key that Amazon Web Services Payment Cryptography uses to verify MAC data.</p>"}, "Mac": {"shape": "HexEvenLengthBetween4And128", "documentation": "<p>The MAC being verified.</p>"}, "MacLength": {"shape": "IntegerRangeBetween4And16", "documentation": "<p>The length of the MAC.</p>"}, "MessageData": {"shape": "HexEvenLengthBetween2And4096", "documentation": "<p>The data on for which MAC is under verification.</p>"}, "VerificationAttributes": {"shape": "MacAttributes", "documentation": "<p>The attributes and data values to use for MAC verification within Amazon Web Services Payment Cryptography.</p>"}}}, "VerifyMacOutput": {"type": "structure", "required": ["KeyArn", "KeyCheckValue"], "members": {"KeyArn": {"shape": "KeyArn", "documentation": "<p>The <code>keyARN</code> of the encryption key that Amazon Web Services Payment Cryptography uses for MAC verification.</p>"}, "KeyCheckValue": {"shape": "KeyCheckValue", "documentation": "<p>The key check value (KCV) of the encryption key. The KCV is used to check if all parties holding a given key have the same key or to detect that a key has changed. Amazon Web Services Payment Cryptography calculates the KCV by using standard algorithms, typically by encrypting 8 or 16 bytes or \"00\" or \"01\" and then truncating the result to the first 3 bytes, or 6 hex digits, of the resulting cryptogram.</p>"}}}, "VerifyPinDataInput": {"type": "structure", "required": ["EncryptedPinBlock", "EncryptionKeyIdentifier", "PinBlockFormat", "PrimaryAccountNumber", "VerificationAttributes", "VerificationKeyIdentifier"], "members": {"DukptAttributes": {"shape": "DukptAttributes", "documentation": "<p>The attributes and values for the DUKPT encrypted PIN block data.</p>"}, "EncryptedPinBlock": {"shape": "HexLengthBetween16And32", "documentation": "<p>The encrypted PIN block data that Amazon Web Services Payment Cryptography verifies.</p>"}, "EncryptionKeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>keyARN</code> of the encryption key under which the PIN block data is encrypted. This key type can be PEK or BDK.</p>"}, "PinBlockFormat": {"shape": "PinBlockFormatForPinData", "documentation": "<p>The PIN encoding format for pin data generation as specified in ISO 9564. Amazon Web Services Payment Cryptography supports <code>ISO_Format_0</code> and <code>ISO_Format_3</code>.</p> <p>The <code>ISO_Format_0</code> PIN block format is equivalent to the ANSI X9.8, VISA-1, and ECI-1 PIN block formats. It is similar to a VISA-4 PIN block format. It supports a PIN from 4 to 12 digits in length.</p> <p>The <code>ISO_Format_3</code> PIN block format is the same as <code>ISO_Format_0</code> except that the fill digits are random values from 10 to 15.</p>"}, "PinDataLength": {"shape": "IntegerRangeBetween4And12", "documentation": "<p>The length of PIN being verified.</p>", "box": true}, "PrimaryAccountNumber": {"shape": "NumberLengthBetween12And19", "documentation": "<p>The Primary Account Number (PAN), a unique identifier for a payment credit or debit card that associates the card with a specific account holder.</p>"}, "VerificationAttributes": {"shape": "PinVerificationAttributes", "documentation": "<p>The attributes and values for PIN data verification.</p>"}, "VerificationKeyIdentifier": {"shape": "KeyArnOrKeyAliasType", "documentation": "<p>The <code>keyARN</code> of the PIN verification key.</p>"}}}, "VerifyPinDataOutput": {"type": "structure", "required": ["EncryptionKeyArn", "EncryptionKeyCheckValue", "VerificationKeyArn", "VerificationKeyCheckValue"], "members": {"EncryptionKeyArn": {"shape": "KeyArn", "documentation": "<p>The <code>keyARN</code> of the PEK that Amazon Web Services Payment Cryptography uses for encrypted pin block generation.</p>"}, "EncryptionKeyCheckValue": {"shape": "KeyCheckValue", "documentation": "<p>The key check value (KCV) of the encryption key. The KCV is used to check if all parties holding a given key have the same key or to detect that a key has changed. Amazon Web Services Payment Cryptography calculates the KCV by using standard algorithms, typically by encrypting 8 or 16 bytes or \"00\" or \"01\" and then truncating the result to the first 3 bytes, or 6 hex digits, of the resulting cryptogram.</p>"}, "VerificationKeyArn": {"shape": "KeyArn", "documentation": "<p>The <code>keyARN</code> of the PIN encryption key that Amazon Web Services Payment Cryptography uses for PIN or PIN Offset verification.</p>"}, "VerificationKeyCheckValue": {"shape": "KeyCheckValue", "documentation": "<p>The key check value (KCV) of the encryption key. The KCV is used to check if all parties holding a given key have the same key or to detect that a key has changed. Amazon Web Services Payment Cryptography calculates the KCV by using standard algorithms, typically by encrypting 8 or 16 bytes or \"00\" or \"01\" and then truncating the result to the first 3 bytes, or 6 hex digits, of the resulting cryptogram.</p>"}}}, "VisaPin": {"type": "structure", "required": ["PinVerificationKeyIndex"], "members": {"PinVerificationKeyIndex": {"shape": "IntegerRangeBetween0And9", "documentation": "<p>The value for PIN verification index. It is used in the Visa PIN algorithm to calculate the PVV (PIN Verification Value).</p>"}}, "documentation": "<p>Parameters that are required to generate or verify Visa PIN.</p>"}, "VisaPinVerification": {"type": "structure", "required": ["PinVerificationKeyIndex", "VerificationValue"], "members": {"PinVerificationKeyIndex": {"shape": "IntegerRangeBetween0And9", "documentation": "<p>The value for PIN verification index. It is used in the Visa PIN algorithm to calculate the PVV (PIN Verification Value).</p>"}, "VerificationValue": {"shape": "NumberLengthBetween4And12", "documentation": "<p>Parameters that are required to generate or verify Visa PVV (PIN Verification Value).</p>"}}, "documentation": "<p>Parameters that are required to generate or verify Visa PIN.</p>"}, "VisaPinVerificationValue": {"type": "structure", "required": ["EncryptedPinBlock", "PinVerificationKeyIndex"], "members": {"EncryptedPinBlock": {"shape": "HexLengthBetween16And32", "documentation": "<p>The encrypted PIN block data to verify.</p>"}, "PinVerificationKeyIndex": {"shape": "IntegerRangeBetween0And9", "documentation": "<p>The value for PIN verification index. It is used in the Visa PIN algorithm to calculate the PVV (PIN Verification Value).</p>"}}, "documentation": "<p>Parameters that are required to generate or verify Visa PVV (PIN Verification Value).</p>"}}, "documentation": "<p>You use the Amazon Web Services Payment Cryptography Data Plane to manage how encryption keys are used for payment-related transaction processing and associated cryptographic operations. You can encrypt, decrypt, generate, verify, and translate payment-related cryptographic operations in Amazon Web Services Payment Cryptography. For more information, see <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/userguide/data-operations.html\">Data operations</a> in the <i>Amazon Web Services Payment Cryptography User Guide</i>.</p> <p>To manage your encryption keys, you use the <a href=\"https://docs.aws.amazon.com/payment-cryptography/latest/APIReference/Welcome.html\">Amazon Web Services Payment Cryptography Control Plane</a>. You can create, import, export, share, manage, and delete keys. You can also manage Identity and Access Management (IAM) policies for keys. </p>"}