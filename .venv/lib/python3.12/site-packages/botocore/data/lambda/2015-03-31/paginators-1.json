{"pagination": {"ListEventSourceMappings": {"input_token": "<PERSON><PERSON>", "output_token": "NextMarker", "limit_key": "MaxItems", "result_key": "EventSourceMappings"}, "ListFunctions": {"input_token": "<PERSON><PERSON>", "output_token": "NextMarker", "limit_key": "MaxItems", "result_key": "Functions"}, "ListAliases": {"input_token": "<PERSON><PERSON>", "output_token": "NextMarker", "limit_key": "MaxItems", "result_key": "Aliases"}, "ListLayerVersions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "output_token": "NextMarker", "result_key": "LayerVersions"}, "ListLayers": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "output_token": "NextMarker", "result_key": "Layers"}, "ListVersionsByFunction": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "output_token": "NextMarker", "result_key": "Versions"}, "ListFunctionEventInvokeConfigs": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "output_token": "NextMarker", "result_key": "FunctionEventInvokeConfigs"}, "ListProvisionedConcurrencyConfigs": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "output_token": "NextMarker", "result_key": "ProvisionedConcurrencyConfigs"}, "ListCodeSigningConfigs": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "output_token": "NextMarker", "result_key": "CodeSigningConfigs"}, "ListFunctionsByCodeSigningConfig": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "output_token": "NextMarker", "result_key": "FunctionArns"}, "ListFunctionUrlConfigs": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "output_token": "NextMarker", "result_key": "FunctionUrlConfigs"}}}