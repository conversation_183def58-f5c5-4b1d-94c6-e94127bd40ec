{"version": "2.0", "metadata": {"apiVersion": "2017-07-25", "endpointPrefix": "lookoutmetrics", "jsonVersion": "1.1", "protocol": "rest-json", "serviceAbbreviation": "LookoutMetrics", "serviceFullName": "Amazon Lookout for Metrics", "serviceId": "LookoutMetrics", "signatureVersion": "v4", "signingName": "lookoutmetrics", "uid": "lookoutmetrics-2017-07-25"}, "operations": {"ActivateAnomalyDetector": {"name": "ActivateAnomalyDetector", "http": {"method": "POST", "requestUri": "/ActivateAnomalyDetector"}, "input": {"shape": "ActivateAnomalyDetectorRequest"}, "output": {"shape": "ActivateAnomalyDetectorResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "TooManyRequestsException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}], "documentation": "<p>Activates an anomaly detector.</p>"}, "BackTestAnomalyDetector": {"name": "BackTestAnomalyDetector", "http": {"method": "POST", "requestUri": "/BackTestAnomalyDetector"}, "input": {"shape": "BackTestAnomalyDetectorRequest"}, "output": {"shape": "BackTestAnomalyDetectorResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "TooManyRequestsException"}, {"shape": "InternalServerException"}], "documentation": "<p>Runs a backtest for anomaly detection for the specified resource.</p>"}, "CreateAlert": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/C<PERSON><PERSON><PERSON><PERSON>"}, "input": {"shape": "CreateAlertRequest"}, "output": {"shape": "CreateAlertResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "TooManyRequestsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates an alert for an anomaly detector.</p>"}, "CreateAnomalyDetector": {"name": "CreateAnomalyDetector", "http": {"method": "POST", "requestUri": "/CreateAnomalyDetector"}, "input": {"shape": "CreateAnomalyDetectorRequest"}, "output": {"shape": "CreateAnomalyDetectorResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "TooManyRequestsException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates an anomaly detector.</p>"}, "CreateMetricSet": {"name": "CreateMetricSet", "http": {"method": "POST", "requestUri": "/CreateMetricSet"}, "input": {"shape": "CreateMetricSetRequest"}, "output": {"shape": "CreateMetricSetResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "TooManyRequestsException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a dataset.</p>"}, "DeactivateAnomalyDetector": {"name": "DeactivateAnomalyDetector", "http": {"method": "POST", "requestUri": "/DeactivateAnomalyDetector"}, "input": {"shape": "DeactivateAnomalyDetectorRequest"}, "output": {"shape": "DeactivateAnomalyDetectorResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "TooManyRequestsException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deactivates an anomaly detector.</p>"}, "DeleteAlert": {"name": "Delete<PERSON>lert", "http": {"method": "POST", "requestUri": "/DeleteAlert"}, "input": {"shape": "DeleteAlertRequest"}, "output": {"shape": "DeleteAlertResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "TooManyRequestsException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an alert.</p>"}, "DeleteAnomalyDetector": {"name": "DeleteAnomalyDetector", "http": {"method": "POST", "requestUri": "/DeleteAnomalyDetector"}, "input": {"shape": "DeleteAnomalyDetectorRequest"}, "output": {"shape": "DeleteAnomalyDetectorResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "TooManyRequestsException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes a detector. Deleting an anomaly detector will delete all of its corresponding resources including any configured datasets and alerts.</p>"}, "DescribeAlert": {"name": "Describe<PERSON>lert", "http": {"method": "POST", "requestUri": "/DescribeAlert"}, "input": {"shape": "DescribeAlertRequest"}, "output": {"shape": "DescribeAlertResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Describes an alert.</p> <p>Amazon Lookout for Metrics API actions are eventually consistent. If you do a read operation on a resource immediately after creating or modifying it, use retries to allow time for the write operation to complete.</p>"}, "DescribeAnomalyDetectionExecutions": {"name": "DescribeAnomalyDetectionExecutions", "http": {"method": "POST", "requestUri": "/DescribeAnomalyDetectionExecutions"}, "input": {"shape": "DescribeAnomalyDetectionExecutionsRequest"}, "output": {"shape": "DescribeAnomalyDetectionExecutionsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "TooManyRequestsException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns information about the status of the specified anomaly detection jobs.</p>"}, "DescribeAnomalyDetector": {"name": "DescribeAnomalyDetector", "http": {"method": "POST", "requestUri": "/DescribeAnomalyDetector"}, "input": {"shape": "DescribeAnomalyDetectorRequest"}, "output": {"shape": "DescribeAnomalyDetectorResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Describes a detector.</p> <p>Amazon Lookout for Metrics API actions are eventually consistent. If you do a read operation on a resource immediately after creating or modifying it, use retries to allow time for the write operation to complete.</p>"}, "DescribeMetricSet": {"name": "DescribeMetricSet", "http": {"method": "POST", "requestUri": "/DescribeMetricSet"}, "input": {"shape": "DescribeMetricSetRequest"}, "output": {"shape": "DescribeMetricSetResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "TooManyRequestsException"}, {"shape": "InternalServerException"}], "documentation": "<p>Describes a dataset.</p> <p>Amazon Lookout for Metrics API actions are eventually consistent. If you do a read operation on a resource immediately after creating or modifying it, use retries to allow time for the write operation to complete.</p>"}, "DetectMetricSetConfig": {"name": "DetectMetricSetConfig", "http": {"method": "POST", "requestUri": "/DetectMetricSetConfig"}, "input": {"shape": "DetectMetricSetConfigRequest"}, "output": {"shape": "DetectMetricSetConfigResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Detects an Amazon S3 dataset's file format, interval, and offset.</p>"}, "GetAnomalyGroup": {"name": "GetAnomalyGroup", "http": {"method": "POST", "requestUri": "/GetAnomalyGroup"}, "input": {"shape": "GetAnomalyGroupRequest"}, "output": {"shape": "GetAnomalyGroupResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Returns details about a group of anomalous metrics.</p>"}, "GetDataQualityMetrics": {"name": "GetDataQualityMetrics", "http": {"method": "POST", "requestUri": "/GetDataQualityMetrics"}, "input": {"shape": "GetDataQualityMetricsRequest"}, "output": {"shape": "GetDataQualityMetricsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "TooManyRequestsException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns details about the requested data quality metrics.</p>"}, "GetFeedback": {"name": "GetFeedback", "http": {"method": "POST", "requestUri": "/GetFeedback"}, "input": {"shape": "GetFeedbackRequest"}, "output": {"shape": "GetFeedbackResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Get feedback for an anomaly group.</p>"}, "GetSampleData": {"name": "GetSampleData", "http": {"method": "POST", "requestUri": "/GetSampleData"}, "input": {"shape": "GetSampleDataRequest"}, "output": {"shape": "GetSampleDataResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "TooManyRequestsException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns a selection of sample records from an Amazon S3 datasource.</p>"}, "ListAlerts": {"name": "ListAlerts", "http": {"method": "POST", "requestUri": "/ListAlerts"}, "input": {"shape": "ListAlertsRequest"}, "output": {"shape": "ListAlertsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "TooManyRequestsException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the alerts attached to a detector.</p> <p>Amazon Lookout for Metrics API actions are eventually consistent. If you do a read operation on a resource immediately after creating or modifying it, use retries to allow time for the write operation to complete.</p>"}, "ListAnomalyDetectors": {"name": "ListAnomalyDetectors", "http": {"method": "POST", "requestUri": "/ListAnomalyDetectors"}, "input": {"shape": "ListAnomalyDetectorsRequest"}, "output": {"shape": "ListAnomalyDetectorsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "TooManyRequestsException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the detectors in the current AWS Region.</p> <p>Amazon Lookout for Metrics API actions are eventually consistent. If you do a read operation on a resource immediately after creating or modifying it, use retries to allow time for the write operation to complete.</p>"}, "ListAnomalyGroupRelatedMetrics": {"name": "ListAnomalyGroupRelatedMetrics", "http": {"method": "POST", "requestUri": "/ListAnomalyGroupRelatedMetrics"}, "input": {"shape": "ListAnomalyGroupRelatedMetricsRequest"}, "output": {"shape": "ListAnomalyGroupRelatedMetricsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Returns a list of measures that are potential causes or effects of an anomaly group.</p>"}, "ListAnomalyGroupSummaries": {"name": "ListAnomalyGroupSummaries", "http": {"method": "POST", "requestUri": "/ListAnomalyGroupSummaries"}, "input": {"shape": "ListAnomalyGroupSummariesRequest"}, "output": {"shape": "ListAnomalyGroupSummariesResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Returns a list of anomaly groups.</p>"}, "ListAnomalyGroupTimeSeries": {"name": "ListAnomalyGroupTimeSeries", "http": {"method": "POST", "requestUri": "/ListAnomalyGroupTimeSeries"}, "input": {"shape": "ListAnomalyGroupTimeSeriesRequest"}, "output": {"shape": "ListAnomalyGroupTimeSeriesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Gets a list of anomalous metrics for a measure in an anomaly group.</p>"}, "ListMetricSets": {"name": "ListMetricSets", "http": {"method": "POST", "requestUri": "/ListMetricSets"}, "input": {"shape": "ListMetricSetsRequest"}, "output": {"shape": "ListMetricSetsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "TooManyRequestsException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the datasets in the current AWS Region.</p> <p>Amazon Lookout for Metrics API actions are eventually consistent. If you do a read operation on a resource immediately after creating or modifying it, use retries to allow time for the write operation to complete.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Gets a list of <a href=\"https://docs.aws.amazon.com/lookoutmetrics/latest/dev/detectors-tags.html\">tags</a> for a detector, dataset, or alert.</p>"}, "PutFeedback": {"name": "PutFeedback", "http": {"method": "POST", "requestUri": "/PutFeedback"}, "input": {"shape": "PutFeedbackRequest"}, "output": {"shape": "PutFeedbackResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Add feedback for an anomalous metric.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}", "responseCode": 204}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Adds <a href=\"https://docs.aws.amazon.com/lookoutmetrics/latest/dev/detectors-tags.html\">tags</a> to a detector, dataset, or alert.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 204}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Removes <a href=\"https://docs.aws.amazon.com/lookoutmetrics/latest/dev/detectors-tags.html\">tags</a> from a detector, dataset, or alert.</p>"}, "UpdateAlert": {"name": "Update<PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/UpdateAlert"}, "input": {"shape": "UpdateAlertRequest"}, "output": {"shape": "UpdateAlertResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Make changes to an existing alert.</p>"}, "UpdateAnomalyDetector": {"name": "UpdateAnomalyDetector", "http": {"method": "POST", "requestUri": "/UpdateAnomalyDetector"}, "input": {"shape": "UpdateAnomalyDetectorRequest"}, "output": {"shape": "UpdateAnomalyDetectorResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Updates a detector. After activation, you can only change a detector's ingestion delay and description.</p>"}, "UpdateMetricSet": {"name": "UpdateMetricSet", "http": {"method": "POST", "requestUri": "/UpdateMetricSet"}, "input": {"shape": "UpdateMetricSetRequest"}, "output": {"shape": "UpdateMetricSetResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "TooManyRequestsException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Updates a dataset.</p>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "Message"}}, "documentation": "<p>You do not have sufficient permissions to perform this action.</p>", "error": {"httpStatusCode": 403}, "exception": true}, "Action": {"type": "structure", "members": {"SNSConfiguration": {"shape": "SNSConfiguration", "documentation": "<p>A configuration for an Amazon SNS channel.</p>"}, "LambdaConfiguration": {"shape": "LambdaConfiguration", "documentation": "<p>A configuration for an AWS Lambda channel.</p>"}}, "documentation": "<p>A configuration that specifies the action to perform when anomalies are detected.</p>"}, "ActivateAnomalyDetectorRequest": {"type": "structure", "required": ["AnomalyDetectorArn"], "members": {"AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the anomaly detector.</p>"}}}, "ActivateAnomalyDetectorResponse": {"type": "structure", "members": {}}, "AggregationFunction": {"type": "string", "enum": ["AVG", "SUM"]}, "Alert": {"type": "structure", "members": {"Action": {"shape": "Action", "documentation": "<p>Action that will be triggered when there is an alert.</p>"}, "AlertDescription": {"shape": "AlertDescription", "documentation": "<p>A description of the alert.</p>"}, "AlertArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the alert.</p>"}, "AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the detector to which the alert is attached.</p>"}, "AlertName": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name of the alert.</p>"}, "AlertSensitivityThreshold": {"shape": "SensitivityThreshold", "documentation": "<p>The minimum severity for an anomaly to trigger the alert.</p>"}, "AlertType": {"shape": "AlertType", "documentation": "<p>The type of the alert.</p>"}, "AlertStatus": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The status of the alert.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The time at which the alert was last modified.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>The time at which the alert was created.</p>"}, "AlertFilters": {"shape": "Alert<PERSON><PERSON><PERSON>", "documentation": "<p>The configuration of the alert filters, containing MetricList and DimensionFilter.</p>"}}, "documentation": "<p>A configuration for Amazon SNS-integrated notifications.</p>"}, "AlertDescription": {"type": "string", "max": 256, "pattern": ".*\\S.*"}, "AlertFilters": {"type": "structure", "members": {"MetricList": {"shape": "MetricNameList", "documentation": "<p>The list of measures that you want to get alerts for.</p>"}, "DimensionFilterList": {"shape": "DimensionFilterList", "documentation": "<p>The list of DimensionFilter objects that are used for dimension-based filtering.</p>"}}, "documentation": "<p>The configuration of the alert filters.</p>"}, "AlertName": {"type": "string", "max": 63, "min": 1, "pattern": "^[a-zA-Z0-9][a-zA-Z0-9\\-_]*"}, "AlertStatus": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}, "AlertSummary": {"type": "structure", "members": {"AlertArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the alert.</p>"}, "AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the detector to which the alert is attached.</p>"}, "AlertName": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name of the alert.</p>"}, "AlertSensitivityThreshold": {"shape": "SensitivityThreshold", "documentation": "<p>The minimum severity for an anomaly to trigger the alert.</p>"}, "AlertType": {"shape": "AlertType", "documentation": "<p>The type of the alert.</p>"}, "AlertStatus": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The status of the alert.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The time at which the alert was last modified.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>The time at which the alert was created.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The alert's <a href=\"https://docs.aws.amazon.com/lookoutmetrics/latest/dev/detectors-tags.html\">tags</a>.</p>"}}, "documentation": "<p>Provides a summary of an alert's configuration.</p>"}, "AlertSummaryList": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "AlertType": {"type": "string", "enum": ["SNS", "LAMBDA"]}, "AnomalyDetectionTaskStatus": {"type": "string", "enum": ["PENDING", "IN_PROGRESS", "COMPLETED", "FAILED", "FAILED_TO_SCHEDULE"]}, "AnomalyDetectionTaskStatusMessage": {"type": "string", "max": 256, "min": 1, "pattern": ".*\\S.*"}, "AnomalyDetectorConfig": {"type": "structure", "members": {"AnomalyDetectorFrequency": {"shape": "Frequency", "documentation": "<p>The frequency at which the detector analyzes its source data.</p>"}}, "documentation": "<p>Contains information about a detector's configuration.</p>"}, "AnomalyDetectorConfigSummary": {"type": "structure", "members": {"AnomalyDetectorFrequency": {"shape": "Frequency", "documentation": "<p>The interval at which the detector analyzes its source data.</p>"}}, "documentation": "<p>Contains information about a detector's configuration.</p>"}, "AnomalyDetectorDataQualityMetric": {"type": "structure", "members": {"StartTimestamp": {"shape": "Timestamp", "documentation": "<p>The start time for the data quality metrics collection.</p>"}, "MetricSetDataQualityMetricList": {"shape": "MetricSetDataQualityMetricList", "documentation": "<p>An array of <code>DataQualityMetricList</code> objects. Each object in the array contains information about a data quality metric.</p>"}}, "documentation": "<p>Aggregated details about the data quality metrics collected for the <code>AnomalyDetectorArn</code> provided in the <a>GetDataQualityMetrics</a> object.</p>"}, "AnomalyDetectorDataQualityMetricList": {"type": "list", "member": {"shape": "AnomalyDetectorDataQualityMetric"}}, "AnomalyDetectorDescription": {"type": "string", "max": 256, "min": 1, "pattern": ".*\\S.*"}, "AnomalyDetectorFailureType": {"type": "string", "enum": ["ACTIVATION_FAILURE", "BACK_TEST_ACTIVATION_FAILURE", "DELETION_FAILURE", "DEACTIVATION_FAILURE"]}, "AnomalyDetectorName": {"type": "string", "max": 63, "min": 1, "pattern": "^[a-zA-Z0-9][a-zA-Z0-9\\-_]*"}, "AnomalyDetectorStatus": {"type": "string", "enum": ["ACTIVE", "ACTIVATING", "DELETING", "FAILED", "INACTIVE", "LEARNING", "BACK_TEST_ACTIVATING", "BACK_TEST_ACTIVE", "BACK_TEST_COMPLETE", "DEACTIVATED", "DEACTIVATING"]}, "AnomalyDetectorSummary": {"type": "structure", "members": {"AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the detector.</p>"}, "AnomalyDetectorName": {"shape": "AnomalyDetectorName", "documentation": "<p>The name of the detector.</p>"}, "AnomalyDetectorDescription": {"shape": "AnomalyDetectorDescription", "documentation": "<p>A description of the detector.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>The time at which the detector was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The time at which the detector was last modified.</p>"}, "Status": {"shape": "AnomalyDetectorStatus", "documentation": "<p>The status of detector.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The detector's <a href=\"https://docs.aws.amazon.com/lookoutmetrics/latest/dev/detectors-tags.html\">tags</a>.</p>"}}, "documentation": "<p>Contains information about an an anomaly detector.</p>"}, "AnomalyDetectorSummaryList": {"type": "list", "member": {"shape": "AnomalyDetector<PERSON><PERSON><PERSON>y"}}, "AnomalyGroup": {"type": "structure", "members": {"StartTime": {"shape": "TimestampString", "documentation": "<p>The start time for the group.</p>"}, "EndTime": {"shape": "TimestampString", "documentation": "<p>The end time for the group.</p>"}, "AnomalyGroupId": {"shape": "UUID", "documentation": "<p>The ID of the anomaly group.</p>"}, "AnomalyGroupScore": {"shape": "Score", "documentation": "<p>The severity score of the group.</p>"}, "PrimaryMetricName": {"shape": "MetricName", "documentation": "<p>The name of the primary affected measure for the group.</p>"}, "MetricLevelImpactList": {"shape": "MetricLevelImpactList", "documentation": "<p>A list of measures affected by the anomaly.</p>"}}, "documentation": "<p>A group of anomalous metrics</p>"}, "AnomalyGroupStatistics": {"type": "structure", "members": {"EvaluationStartDate": {"shape": "TimestampString", "documentation": "<p>The start of the time range that was searched.</p>"}, "TotalCount": {"shape": "Integer", "documentation": "<p>The number of groups found.</p>"}, "ItemizedMetricStatsList": {"shape": "ItemizedMetricStatsList", "documentation": "<p>Statistics for individual metrics within the group.</p>"}}, "documentation": "<p>Aggregated statistics for a group of anomalous metrics.</p>"}, "AnomalyGroupSummary": {"type": "structure", "members": {"StartTime": {"shape": "TimestampString", "documentation": "<p>The start time for the group.</p>"}, "EndTime": {"shape": "TimestampString", "documentation": "<p>The end time for the group.</p>"}, "AnomalyGroupId": {"shape": "UUID", "documentation": "<p>The ID of the anomaly group.</p>"}, "AnomalyGroupScore": {"shape": "Score", "documentation": "<p>The severity score of the group.</p>"}, "PrimaryMetricName": {"shape": "MetricName", "documentation": "<p>The name of the primary affected measure for the group.</p>"}}, "documentation": "<p>Details about a group of anomalous metrics.</p>"}, "AnomalyGroupSummaryList": {"type": "list", "member": {"shape": "AnomalyGroupSummary"}}, "AnomalyGroupTimeSeries": {"type": "structure", "required": ["AnomalyGroupId"], "members": {"AnomalyGroupId": {"shape": "UUID", "documentation": "<p>The ID of the anomaly group.</p>"}, "TimeSeriesId": {"shape": "TimeSeriesId", "documentation": "<p>The ID of the metric.</p>"}}, "documentation": "<p>An anomalous metric in an anomaly group.</p>"}, "AnomalyGroupTimeSeriesFeedback": {"type": "structure", "required": ["AnomalyGroupId", "TimeSeriesId", "IsAnomaly"], "members": {"AnomalyGroupId": {"shape": "UUID", "documentation": "<p>The ID of the anomaly group.</p>"}, "TimeSeriesId": {"shape": "TimeSeriesId", "documentation": "<p>The ID of the metric.</p>"}, "IsAnomaly": {"shape": "Boolean", "documentation": "<p>Feedback on whether the metric is a legitimate anomaly.</p>"}}, "documentation": "<p>Feedback for an anomalous metric.</p>"}, "AppFlowConfig": {"type": "structure", "members": {"RoleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>An IAM role that gives Amazon Lookout for Metrics permission to access the flow.</p>"}, "FlowName": {"shape": "FlowName", "documentation": "<p> name of the flow.</p>"}}, "documentation": "<p>Details about an Amazon AppFlow flow datasource.</p>"}, "Arn": {"type": "string", "max": 256, "pattern": "arn:([a-z\\d-]+):.*:.*:.*:.+"}, "AthenaDataCatalog": {"type": "string", "max": 256, "min": 1, "pattern": "[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*"}, "AthenaDatabaseName": {"type": "string", "max": 255, "min": 1, "pattern": "[a-zA-Z0-9_]+"}, "AthenaS3ResultsPath": {"type": "string", "max": 1024, "pattern": "^s3://[a-z0-9].+$"}, "AthenaSourceConfig": {"type": "structure", "members": {"RoleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>An IAM role that gives Amazon Lookout for Metrics permission to access the data.</p>"}, "DatabaseName": {"shape": "AthenaDatabaseName", "documentation": "<p>The database's name.</p>"}, "DataCatalog": {"shape": "AthenaDataCatalog", "documentation": "<p>The database's data catalog.</p>"}, "TableName": {"shape": "AthenaTableName", "documentation": "<p>The database's table name.</p>"}, "WorkGroupName": {"shape": "AthenaWorkGroupName", "documentation": "<p>The database's work group name.</p>"}, "S3ResultsPath": {"shape": "AthenaS3ResultsPath", "documentation": "<p>The database's results path.</p>"}, "BackTestConfiguration": {"shape": "BackTestConfiguration", "documentation": "<p>Settings for backtest mode.</p>"}}, "documentation": "<p>Details about an Amazon Athena datasource.</p>"}, "AthenaTableName": {"type": "string", "max": 128, "min": 1, "pattern": "[a-zA-Z0-9_]+"}, "AthenaWorkGroupName": {"type": "string", "max": 128, "min": 1, "pattern": "[a-zA-Z0-9._-]{1,128}"}, "AttributeValue": {"type": "structure", "members": {"S": {"shape": "StringAttributeValue", "documentation": "<p>A string.</p>"}, "N": {"shape": "NumberAttributeValue", "documentation": "<p>A number.</p>"}, "B": {"shape": "BinaryAttributeValue", "documentation": "<p>A binary value.</p>"}, "SS": {"shape": "StringListAttributeValue", "documentation": "<p>A list of strings.</p>"}, "NS": {"shape": "NumberListAttributeValue", "documentation": "<p>A list of numbers.</p>"}, "BS": {"shape": "BinaryListAttributeValue", "documentation": "<p>A list of binary values.</p>"}}, "documentation": "<p>An attribute value.</p>"}, "AutoDetectionMetricSource": {"type": "structure", "members": {"S3SourceConfig": {"shape": "AutoDetectionS3SourceConfig", "documentation": "<p>The source's source config.</p>"}}, "documentation": "<p>An auto detection metric source.</p>"}, "AutoDetectionS3SourceConfig": {"type": "structure", "members": {"TemplatedPathList": {"shape": "TemplatedPathList", "documentation": "<p>The config's templated path list.</p>"}, "HistoricalDataPathList": {"shape": "HistoricalDataPathList", "documentation": "<p>The config's historical data path list.</p>"}}, "documentation": "<p>An auto detection source config.</p>"}, "BackTestAnomalyDetectorRequest": {"type": "structure", "required": ["AnomalyDetectorArn"], "members": {"AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the anomaly detector.</p>"}}}, "BackTestAnomalyDetectorResponse": {"type": "structure", "members": {}}, "BackTestConfiguration": {"type": "structure", "required": ["RunBackTestMode"], "members": {"RunBackTestMode": {"shape": "Boolean", "documentation": "<p>Run a backtest instead of monitoring new data.</p>"}}, "documentation": "<p>Settings for backtest mode.</p>"}, "BinaryAttributeValue": {"type": "string"}, "BinaryListAttributeValue": {"type": "list", "member": {"shape": "BinaryAttributeValue"}}, "Boolean": {"type": "boolean"}, "CSVFileCompression": {"type": "string", "enum": ["NONE", "GZIP"]}, "Charset": {"type": "string", "max": 63, "pattern": "^[a-zA-Z0-9][a-zA-Z0-9\\-_]*"}, "CloudWatchConfig": {"type": "structure", "members": {"RoleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>An IAM role that gives Amazon Lookout for Metrics permission to access data in Amazon CloudWatch.</p>"}, "BackTestConfiguration": {"shape": "BackTestConfiguration", "documentation": "<p>Settings for backtest mode.</p>"}}, "documentation": "<p>Details about an Amazon CloudWatch datasource.</p>"}, "ColumnName": {"type": "string", "max": 63, "min": 1, "pattern": "^[a-zA-Z0-9][a-zA-Z0-9\\-_]*"}, "Confidence": {"type": "string", "enum": ["HIGH", "LOW", "NONE"]}, "ConflictException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "Message"}, "ResourceId": {"shape": "ResourceId", "documentation": "<p>The ID of the resource.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The type of the resource.</p>"}}, "documentation": "<p>There was a conflict processing the request. Try your request again.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "ContributionMatrix": {"type": "structure", "members": {"DimensionContributionList": {"shape": "DimensionContributionList", "documentation": "<p>A list of contributing dimensions.</p>"}}, "documentation": "<p>Details about dimensions that contributed to an anomaly.</p>"}, "CreateAlertRequest": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>", "AnomalyDetectorArn", "Action"], "members": {"AlertName": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name of the alert.</p>"}, "AlertSensitivityThreshold": {"shape": "SensitivityThreshold", "documentation": "<p>An integer from 0 to 100 specifying the alert sensitivity threshold.</p>"}, "AlertDescription": {"shape": "AlertDescription", "documentation": "<p>A description of the alert.</p>"}, "AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the detector to which the alert is attached.</p>"}, "Action": {"shape": "Action", "documentation": "<p>Action that will be triggered when there is an alert.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/lookoutmetrics/latest/dev/detectors-tags.html\">tags</a> to apply to the alert.</p>"}, "AlertFilters": {"shape": "Alert<PERSON><PERSON><PERSON>", "documentation": "<p>The configuration of the alert filters, containing MetricList and DimensionFilterList.</p>"}}}, "CreateAlertResponse": {"type": "structure", "members": {"AlertArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the alert.</p>"}}}, "CreateAnomalyDetectorRequest": {"type": "structure", "required": ["AnomalyDetectorName", "AnomalyDetectorConfig"], "members": {"AnomalyDetectorName": {"shape": "AnomalyDetectorName", "documentation": "<p>The name of the detector.</p>"}, "AnomalyDetectorDescription": {"shape": "AnomalyDetectorDescription", "documentation": "<p>A description of the detector.</p>"}, "AnomalyDetectorConfig": {"shape": "AnomalyDetectorConfig", "documentation": "<p>Contains information about the configuration of the anomaly detector.</p>"}, "KmsKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The ARN of the KMS key to use to encrypt your data.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/lookoutmetrics/latest/dev/detectors-tags.html\">tags</a> to apply to the anomaly detector.</p>"}}}, "CreateAnomalyDetectorResponse": {"type": "structure", "members": {"AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the detector.</p>"}}}, "CreateMetricSetRequest": {"type": "structure", "required": ["AnomalyDetectorArn", "MetricSetName", "MetricList", "MetricSource"], "members": {"AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the anomaly detector that will use the dataset.</p>"}, "MetricSetName": {"shape": "MetricSetName", "documentation": "<p>The name of the dataset.</p>"}, "MetricSetDescription": {"shape": "MetricSetDescription", "documentation": "<p>A description of the dataset you are creating.</p>"}, "MetricList": {"shape": "MetricList", "documentation": "<p>A list of metrics that the dataset will contain.</p>"}, "Offset": {"shape": "Offset", "documentation": "<p>After an interval ends, the amount of seconds that the detector waits before importing data. Offset is only supported for S3, Redshift, Athena and datasources.</p>", "box": true}, "TimestampColumn": {"shape": "TimestampColumn", "documentation": "<p>Contains information about the column used for tracking time in your source data.</p>"}, "DimensionList": {"shape": "DimensionList", "documentation": "<p>A list of the fields you want to treat as dimensions.</p>"}, "MetricSetFrequency": {"shape": "Frequency", "documentation": "<p>The frequency with which the source data will be analyzed for anomalies.</p>"}, "MetricSource": {"shape": "MetricSource", "documentation": "<p>Contains information about how the source data should be interpreted.</p>"}, "Timezone": {"shape": "Timezone", "documentation": "<p>The time zone in which your source data was recorded.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/lookoutmetrics/latest/dev/detectors-tags.html\">tags</a> to apply to the dataset.</p>"}, "DimensionFilterList": {"shape": "MetricSetDimensionFilterList", "documentation": "<p>A list of filters that specify which data is kept for anomaly detection.</p>"}}}, "CreateMetricSetResponse": {"type": "structure", "members": {"MetricSetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the dataset.</p>"}}}, "CsvFormatDescriptor": {"type": "structure", "members": {"FileCompression": {"shape": "CSVFileCompression", "documentation": "<p>The level of compression of the source CSV file.</p>"}, "Charset": {"shape": "Charset", "documentation": "<p>The character set in which the source CSV file is written.</p>"}, "ContainsHeader": {"shape": "Boolean", "documentation": "<p>Whether or not the source CSV file contains a header.</p>"}, "Delimiter": {"shape": "Delimiter", "documentation": "<p>The character used to delimit the source CSV file.</p>"}, "HeaderList": {"shape": "HeaderList", "documentation": "<p>A list of the source CSV file's headers, if any.</p>"}, "QuoteSymbol": {"shape": "QuoteSymbol", "documentation": "<p>The character used as a quote character.</p>"}}, "documentation": "<p>Contains information about how a source CSV data file should be analyzed.</p>"}, "DataItem": {"type": "string"}, "DataQualityMetric": {"type": "structure", "members": {"MetricType": {"shape": "DataQualityMetricType", "documentation": "<p>The name of the data quality metric.</p>"}, "MetricDescription": {"shape": "DataQualityMetricDescription", "documentation": "<p>A description of the data quality metric.</p>"}, "RelatedColumnName": {"shape": "RelatedColumnName", "documentation": "<p>The column that is being monitored.</p>"}, "MetricValue": {"shape": "Double", "documentation": "<p>The value of the data quality metric.</p>"}}, "documentation": "<p>An array that describes a data quality metric. Each <code>DataQualityMetric</code> object contains the data quality metric name, its value, a description of the metric, and the affected column.</p>"}, "DataQualityMetricDescription": {"type": "string", "max": 256, "min": 1, "pattern": ".*\\S.*"}, "DataQualityMetricList": {"type": "list", "member": {"shape": "DataQualityMetric"}}, "DataQualityMetricType": {"type": "string", "enum": ["COLUMN_COMPLETENESS", "DIMENSION_UNIQUENESS", "TIME_SERIES_COUNT", "ROWS_PROCESSED", "ROWS_PARTIAL_COMPLIANCE", "INVALID_ROWS_COMPLIANCE", "BACKTEST_TRAINING_DATA_START_TIME_STAMP", "BACKTEST_TRAINING_DATA_END_TIME_STAMP", "BACKTEST_INFERENCE_DATA_START_TIME_STAMP", "BACKTEST_INFERENCE_DATA_END_TIME_STAMP"]}, "DatabaseHost": {"type": "string", "max": 253, "min": 1, "pattern": ".*\\S.*"}, "DatabasePort": {"type": "integer", "max": 65535, "min": 1}, "DateTimeFormat": {"type": "string", "max": 63, "pattern": ".*\\S.*"}, "DeactivateAnomalyDetectorRequest": {"type": "structure", "required": ["AnomalyDetectorArn"], "members": {"AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the anomaly detector.</p>"}}}, "DeactivateAnomalyDetectorResponse": {"type": "structure", "members": {}}, "DeleteAlertRequest": {"type": "structure", "required": ["Alert<PERSON>rn"], "members": {"AlertArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the alert to delete.</p>"}}}, "DeleteAlertResponse": {"type": "structure", "members": {}}, "DeleteAnomalyDetectorRequest": {"type": "structure", "required": ["AnomalyDetectorArn"], "members": {"AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the detector to delete.</p>"}}}, "DeleteAnomalyDetectorResponse": {"type": "structure", "members": {}}, "Delimiter": {"type": "string", "max": 1, "pattern": "[^\\r\\n]"}, "DescribeAlertRequest": {"type": "structure", "required": ["Alert<PERSON>rn"], "members": {"AlertArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the alert to describe.</p>"}}}, "DescribeAlertResponse": {"type": "structure", "members": {"Alert": {"shape": "<PERSON><PERSON>", "documentation": "<p>Contains information about an alert.</p>"}}}, "DescribeAnomalyDetectionExecutionsRequest": {"type": "structure", "required": ["AnomalyDetectorArn"], "members": {"AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the anomaly detector.</p>"}, "Timestamp": {"shape": "TimestampString", "documentation": "<p>The timestamp of the anomaly detection job.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of items to return in the response.</p>", "box": true}, "NextToken": {"shape": "NextToken", "documentation": "<p>Specify the pagination token that's returned by a previous request to retrieve the next page of results.</p>"}}}, "DescribeAnomalyDetectionExecutionsResponse": {"type": "structure", "members": {"ExecutionList": {"shape": "ExecutionList", "documentation": "<p>A list of detection jobs.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The pagination token that's included if more results are available.</p>"}}}, "DescribeAnomalyDetectorRequest": {"type": "structure", "required": ["AnomalyDetectorArn"], "members": {"AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the detector to describe.</p>"}}}, "DescribeAnomalyDetectorResponse": {"type": "structure", "members": {"AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the detector.</p>"}, "AnomalyDetectorName": {"shape": "AnomalyDetectorName", "documentation": "<p>The name of the detector.</p>"}, "AnomalyDetectorDescription": {"shape": "AnomalyDetectorDescription", "documentation": "<p>A description of the detector.</p>"}, "AnomalyDetectorConfig": {"shape": "AnomalyDetectorConfigSummary", "documentation": "<p>Contains information about the detector's configuration.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>The time at which the detector was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The time at which the detector was last modified.</p>"}, "Status": {"shape": "AnomalyDetectorStatus", "documentation": "<p>The status of the detector.</p>"}, "FailureReason": {"shape": "ErrorMessage", "documentation": "<p>The reason that the detector failed.</p>"}, "KmsKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The ARN of the KMS key to use to encrypt your data.</p>"}, "FailureType": {"shape": "AnomalyDetectorFailureType", "documentation": "<p>The process that caused the detector to fail.</p>"}}}, "DescribeMetricSetRequest": {"type": "structure", "required": ["MetricSetArn"], "members": {"MetricSetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the dataset.</p>"}}}, "DescribeMetricSetResponse": {"type": "structure", "members": {"MetricSetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the dataset.</p>"}, "AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the detector that contains the dataset.</p>"}, "MetricSetName": {"shape": "MetricSetName", "documentation": "<p>The name of the dataset.</p>"}, "MetricSetDescription": {"shape": "MetricSetDescription", "documentation": "<p>The dataset's description.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>The time at which the dataset was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The time at which the dataset was last modified.</p>"}, "Offset": {"shape": "Offset", "documentation": "<p>After an interval ends, the amount of seconds that the detector waits before importing data. Offset is only supported for S3, Redshift, Athena and datasources.</p>", "box": true}, "MetricList": {"shape": "MetricList", "documentation": "<p>A list of the metrics defined by the dataset.</p>"}, "TimestampColumn": {"shape": "TimestampColumn", "documentation": "<p>Contains information about the column used for tracking time in your source data.</p>"}, "DimensionList": {"shape": "DimensionList", "documentation": "<p>A list of the dimensions chosen for analysis.</p>"}, "MetricSetFrequency": {"shape": "Frequency", "documentation": "<p>The interval at which the data will be analyzed for anomalies.</p>"}, "Timezone": {"shape": "Timezone", "documentation": "<p>The time zone in which the dataset's data was recorded.</p>"}, "MetricSource": {"shape": "MetricSource", "documentation": "<p>Contains information about the dataset's source data.</p>"}, "DimensionFilterList": {"shape": "MetricSetDimensionFilterList", "documentation": "<p>The dimensions and their values that were used to filter the dataset.</p>"}}}, "DetectMetricSetConfigRequest": {"type": "structure", "required": ["AnomalyDetectorArn", "AutoDetectionMetricSource"], "members": {"AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>An anomaly detector ARN.</p>"}, "AutoDetectionMetricSource": {"shape": "AutoDetectionMetricSource", "documentation": "<p>A data source.</p>"}}}, "DetectMetricSetConfigResponse": {"type": "structure", "members": {"DetectedMetricSetConfig": {"shape": "DetectedMetricSetConfig", "documentation": "<p>The inferred dataset configuration for the datasource.</p>"}}}, "DetectedCsvFormatDescriptor": {"type": "structure", "members": {"FileCompression": {"shape": "DetectedField", "documentation": "<p>The format's file compression.</p>"}, "Charset": {"shape": "DetectedField", "documentation": "<p>The format's charset.</p>"}, "ContainsHeader": {"shape": "DetectedField", "documentation": "<p>Whether the format includes a header.</p>"}, "Delimiter": {"shape": "DetectedField", "documentation": "<p>The format's delimiter.</p>"}, "HeaderList": {"shape": "DetectedField", "documentation": "<p>The format's header list.</p>"}, "QuoteSymbol": {"shape": "DetectedField", "documentation": "<p>The format's quote symbol.</p>"}}, "documentation": "<p>Properties of an inferred CSV format.</p>"}, "DetectedField": {"type": "structure", "members": {"Value": {"shape": "AttributeValue", "documentation": "<p>The field's value.</p>"}, "Confidence": {"shape": "Confidence", "documentation": "<p>The field's confidence.</p>"}, "Message": {"shape": "Message", "documentation": "<p>The field's message.</p>"}}, "documentation": "<p>An inferred field.</p>"}, "DetectedFileFormatDescriptor": {"type": "structure", "members": {"CsvFormatDescriptor": {"shape": "DetectedCsvFormatDescriptor", "documentation": "<p>Details about a CSV format.</p>"}, "JsonFormatDescriptor": {"shape": "DetectedJsonFormatDescriptor", "documentation": "<p>Details about a JSON format.</p>"}}, "documentation": "<p>Properties of an inferred data format.</p>"}, "DetectedJsonFormatDescriptor": {"type": "structure", "members": {"FileCompression": {"shape": "DetectedField", "documentation": "<p>The format's file compression.</p>"}, "Charset": {"shape": "DetectedField", "documentation": "<p>The format's character set.</p>"}}, "documentation": "<p>A detected JSON format descriptor.</p>"}, "DetectedMetricSetConfig": {"type": "structure", "members": {"Offset": {"shape": "DetectedField", "documentation": "<p>The dataset's offset.</p>"}, "MetricSetFrequency": {"shape": "DetectedField", "documentation": "<p>The dataset's interval.</p>"}, "MetricSource": {"shape": "DetectedMetricSource", "documentation": "<p>The dataset's data source.</p>"}}, "documentation": "<p>An inferred dataset configuration.</p>"}, "DetectedMetricSource": {"type": "structure", "members": {"S3SourceConfig": {"shape": "DetectedS3SourceConfig", "documentation": "<p>The data source's source configuration.</p>"}}, "documentation": "<p>An inferred data source.</p>"}, "DetectedS3SourceConfig": {"type": "structure", "members": {"FileFormatDescriptor": {"shape": "DetectedFileFormatDescriptor", "documentation": "<p>The source's file format descriptor.</p>"}}, "documentation": "<p>An inferred source configuration.</p>"}, "DimensionContribution": {"type": "structure", "members": {"DimensionName": {"shape": "ColumnName", "documentation": "<p>The name of the dimension.</p>"}, "DimensionValueContributionList": {"shape": "DimensionValueContributionList", "documentation": "<p>A list of dimension values that contributed to the anomaly.</p>"}}, "documentation": "<p>Details about a dimension that contributed to an anomaly.</p>"}, "DimensionContributionList": {"type": "list", "member": {"shape": "DimensionContribution"}}, "DimensionFilter": {"type": "structure", "members": {"DimensionName": {"shape": "ColumnName", "documentation": "<p>The name of the dimension to filter on.</p>"}, "DimensionValueList": {"shape": "DimensionValueList", "documentation": "<p>The list of values for the dimension specified in DimensionName that you want to filter on.</p>"}}, "documentation": "<p>The dimension filter, containing DimensionName and DimensionValueList.</p>"}, "DimensionFilterList": {"type": "list", "member": {"shape": "DimensionFilter"}, "max": 5, "min": 1}, "DimensionList": {"type": "list", "member": {"shape": "ColumnName"}, "min": 1}, "DimensionNameValue": {"type": "structure", "required": ["DimensionName", "DimensionValue"], "members": {"DimensionName": {"shape": "ColumnName", "documentation": "<p>The name of the dimension.</p>"}, "DimensionValue": {"shape": "DimensionValue", "documentation": "<p>The value of the dimension.</p>"}}, "documentation": "<p>A dimension name and value.</p>"}, "DimensionNameValueList": {"type": "list", "member": {"shape": "DimensionNameValue"}}, "DimensionValue": {"type": "string"}, "DimensionValueContribution": {"type": "structure", "members": {"DimensionValue": {"shape": "DimensionValue", "documentation": "<p>The value of the dimension.</p>"}, "ContributionScore": {"shape": "Score", "documentation": "<p>The severity score of the value.</p>"}}, "documentation": "<p>The severity of a value of a dimension that contributed to an anomaly.</p>"}, "DimensionValueContributionList": {"type": "list", "member": {"shape": "DimensionValueContribution"}}, "DimensionValueList": {"type": "list", "member": {"shape": "DimensionValue"}, "max": 10, "min": 1}, "Double": {"type": "double"}, "ErrorMessage": {"type": "string", "max": 256}, "ExecutionList": {"type": "list", "member": {"shape": "ExecutionStatus"}}, "ExecutionStatus": {"type": "structure", "members": {"Timestamp": {"shape": "TimestampString", "documentation": "<p>The run's timestamp.</p>"}, "Status": {"shape": "AnomalyDetectionTaskStatus", "documentation": "<p>The run's status.</p>"}, "FailureReason": {"shape": "AnomalyDetectionTaskStatusMessage", "documentation": "<p>The reason that the run failed, if applicable.</p>"}}, "documentation": "<p>The status of an anomaly detector run.</p>"}, "FieldName": {"type": "string"}, "FileFormatDescriptor": {"type": "structure", "members": {"CsvFormatDescriptor": {"shape": "CsvFormatDescriptor", "documentation": "<p>Contains information about how a source CSV data file should be analyzed.</p>"}, "JsonFormatDescriptor": {"shape": "JsonFormatDescriptor", "documentation": "<p>Contains information about how a source JSON data file should be analyzed.</p>"}}, "documentation": "<p>Contains information about a source file's formatting.</p>"}, "Filter": {"type": "structure", "members": {"DimensionValue": {"shape": "DimensionValue", "documentation": "<p>The value that you want to include in the filter.</p>"}, "FilterOperation": {"shape": "FilterOperation", "documentation": "<p>The condition to apply.</p>"}}, "documentation": "<p>Describes a filter for choosing a subset of dimension values. Each filter consists of the dimension that you want to include and the condition statement. The condition statement is specified in the <code>FilterOperation</code> object.</p>"}, "FilterList": {"type": "list", "member": {"shape": "Filter"}, "min": 1}, "FilterOperation": {"type": "string", "enum": ["EQUALS"]}, "FlowName": {"type": "string", "max": 256, "pattern": "[a-zA-Z0-9][\\w!@#.-]+"}, "Frequency": {"type": "string", "enum": ["P1D", "PT1H", "PT10M", "PT5M"]}, "GetAnomalyGroupRequest": {"type": "structure", "required": ["AnomalyGroupId", "AnomalyDetectorArn"], "members": {"AnomalyGroupId": {"shape": "UUID", "documentation": "<p>The ID of the anomaly group.</p>"}, "AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the anomaly detector.</p>"}}}, "GetAnomalyGroupResponse": {"type": "structure", "members": {"AnomalyGroup": {"shape": "AnomalyGroup", "documentation": "<p>Details about the anomaly group.</p>"}}}, "GetDataQualityMetricsRequest": {"type": "structure", "required": ["AnomalyDetectorArn"], "members": {"AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the anomaly detector that you want to investigate.</p>"}, "MetricSetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of a specific data quality metric set.</p>"}}}, "GetDataQualityMetricsResponse": {"type": "structure", "members": {"AnomalyDetectorDataQualityMetricList": {"shape": "AnomalyDetectorDataQualityMetricList", "documentation": "<p>A list of the data quality metrics for the <code>AnomalyDetectorArn</code> that you requested.</p>"}}}, "GetFeedbackRequest": {"type": "structure", "required": ["AnomalyDetectorArn", "AnomalyGroupTimeSeriesFeedback"], "members": {"AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the anomaly detector.</p>"}, "AnomalyGroupTimeSeriesFeedback": {"shape": "AnomalyGroupTimeSeries", "documentation": "<p>The anomalous metric and group ID.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "box": true}, "NextToken": {"shape": "NextToken", "documentation": "<p>Specify the pagination token that's returned by a previous request to retrieve the next page of results.</p>"}}}, "GetFeedbackResponse": {"type": "structure", "members": {"AnomalyGroupTimeSeriesFeedback": {"shape": "TimeSeriesFeedbackList", "documentation": "<p>Feedback for an anomalous metric.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The pagination token that's included if more results are available.</p>"}}}, "GetSampleDataRequest": {"type": "structure", "members": {"S3SourceConfig": {"shape": "SampleDataS3SourceConfig", "documentation": "<p>A datasource bucket in Amazon S3.</p>"}}}, "GetSampleDataResponse": {"type": "structure", "members": {"HeaderValues": {"shape": "HeaderValueList", "documentation": "<p>A list of header labels for the records.</p>"}, "SampleRows": {"shape": "SampleRows", "documentation": "<p>A list of records.</p>"}}}, "HeaderList": {"type": "list", "member": {"shape": "ColumnName"}}, "HeaderValue": {"type": "string"}, "HeaderValueList": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "HistoricalDataPath": {"type": "string", "max": 1024, "pattern": "^s3://[a-z0-9].+$"}, "HistoricalDataPathList": {"type": "list", "member": {"shape": "HistoricalDataPath"}, "max": 1, "min": 1}, "Integer": {"type": "integer"}, "InterMetricImpactDetails": {"type": "structure", "members": {"MetricName": {"shape": "MetricName", "documentation": "<p>The name of the measure.</p>"}, "AnomalyGroupId": {"shape": "UUID", "documentation": "<p>The ID of the anomaly group.</p>"}, "RelationshipType": {"shape": "RelationshipType", "documentation": "<p>Whether a measure is a potential cause of the anomaly group (<code>CAUSE_OF_INPUT_ANOMALY_GROUP</code>), or whether the measure is impacted by the anomaly group (<code>EFFECT_OF_INPUT_ANOMALY_GROUP</code>).</p>"}, "ContributionPercentage": {"shape": "MetricChangePercentage", "documentation": "<p>For potential causes (<code>CAUSE_OF_INPUT_ANOMALY_GROUP</code>), the percentage contribution the measure has in causing the anomalies.</p>"}}, "documentation": "<p>Aggregated details about the measures contributing to the anomaly group, and the measures potentially impacted by the anomaly group.</p> <p/>"}, "InterMetricImpactList": {"type": "list", "member": {"shape": "InterMetricImpactDetails"}}, "InternalServerException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "Message"}}, "documentation": "<p>The request processing has failed because of an unknown error, exception, or failure.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "ItemizedMetricStats": {"type": "structure", "members": {"MetricName": {"shape": "ColumnName", "documentation": "<p>The name of the measure.</p>"}, "OccurrenceCount": {"shape": "Integer", "documentation": "<p>The number of times that the measure appears.</p>"}}, "documentation": "<p>Aggregated statistics about a measure affected by an anomaly.</p>"}, "ItemizedMetricStatsList": {"type": "list", "member": {"shape": "ItemizedMetricStats"}}, "JsonFileCompression": {"type": "string", "enum": ["NONE", "GZIP"]}, "JsonFormatDescriptor": {"type": "structure", "members": {"FileCompression": {"shape": "JsonFileCompression", "documentation": "<p>The level of compression of the source CSV file.</p>"}, "Charset": {"shape": "Charset", "documentation": "<p>The character set in which the source JSON file is written.</p>"}}, "documentation": "<p>Contains information about how a source JSON data file should be analyzed.</p>"}, "KmsKeyArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:aws.*:kms:.*:[0-9]{12}:key/[a-z0-9]{8}-[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{12}"}, "LambdaConfiguration": {"type": "structure", "required": ["RoleArn", "LambdaArn"], "members": {"RoleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of an IAM role that has permission to invoke the Lambda function.</p>"}, "LambdaArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the Lambda function.</p>"}}, "documentation": "<p>Contains information about a Lambda configuration.</p>"}, "ListAlertsRequest": {"type": "structure", "members": {"AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the alert's detector.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the result of the previous request is truncated, the response includes a <code>NextToken</code>. To retrieve the next set of results, use the token in the next request. Tokens expire after 24 hours.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results that will be displayed by the request.</p>", "box": true}}}, "ListAlertsResponse": {"type": "structure", "members": {"AlertSummaryList": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<PERSON>ist", "documentation": "<p>Contains information about an alert.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, the service returns this token. To retrieve the next set of results, use this token in the next request.</p>"}}}, "ListAnomalyDetectorsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "box": true}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the result of the previous request was truncated, the response includes a <code>NextToken</code>. To retrieve the next set of results, use the token in the next request. Tokens expire after 24 hours.</p>"}}}, "ListAnomalyDetectorsResponse": {"type": "structure", "members": {"AnomalyDetectorSummaryList": {"shape": "AnomalyDetectorSummaryList", "documentation": "<p>A list of anomaly detectors in the account in the current region.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, the service returns this token. To retrieve the next set of results, use the token in the next request.</p>"}}}, "ListAnomalyGroupRelatedMetricsRequest": {"type": "structure", "required": ["AnomalyDetectorArn", "AnomalyGroupId"], "members": {"AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the anomaly detector.</p>"}, "AnomalyGroupId": {"shape": "UUID", "documentation": "<p>The ID of the anomaly group.</p>"}, "RelationshipTypeFilter": {"shape": "RelationshipType", "documentation": "<p>Filter for potential causes (<code>CAUSE_OF_INPUT_ANOMALY_GROUP</code>) or downstream effects (<code>EFFECT_OF_INPUT_ANOMALY_GROUP</code>) of the anomaly group.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "box": true}, "NextToken": {"shape": "NextToken", "documentation": "<p>Specify the pagination token that's returned by a previous request to retrieve the next page of results.</p>"}}}, "ListAnomalyGroupRelatedMetricsResponse": {"type": "structure", "members": {"InterMetricImpactList": {"shape": "InterMetricImpactList", "documentation": "<p>Aggregated details about the measures contributing to the anomaly group, and the measures potentially impacted by the anomaly group.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The pagination token that's included if more results are available.</p>"}}}, "ListAnomalyGroupSummariesRequest": {"type": "structure", "required": ["AnomalyDetectorArn", "SensitivityThreshold"], "members": {"AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the anomaly detector.</p>"}, "SensitivityThreshold": {"shape": "SensitivityThreshold", "documentation": "<p>The minimum severity score for inclusion in the output.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "box": true}, "NextToken": {"shape": "NextToken", "documentation": "<p>Specify the pagination token that's returned by a previous request to retrieve the next page of results.</p>"}}}, "ListAnomalyGroupSummariesResponse": {"type": "structure", "members": {"AnomalyGroupSummaryList": {"shape": "AnomalyGroupSummaryList", "documentation": "<p>A list of anomaly group summaries.</p>"}, "AnomalyGroupStatistics": {"shape": "AnomalyGroupStatistics", "documentation": "<p>Aggregated details about the anomaly groups.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The pagination token that's included if more results are available.</p>"}}}, "ListAnomalyGroupTimeSeriesRequest": {"type": "structure", "required": ["AnomalyDetectorArn", "AnomalyGroupId", "MetricName"], "members": {"AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the anomaly detector.</p>"}, "AnomalyGroupId": {"shape": "UUID", "documentation": "<p>The ID of the anomaly group.</p>"}, "MetricName": {"shape": "MetricName", "documentation": "<p>The name of the measure field.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "box": true}, "NextToken": {"shape": "NextToken", "documentation": "<p>Specify the pagination token that's returned by a previous request to retrieve the next page of results.</p>"}}}, "ListAnomalyGroupTimeSeriesResponse": {"type": "structure", "members": {"AnomalyGroupId": {"shape": "UUID", "documentation": "<p>The ID of the anomaly group.</p>"}, "MetricName": {"shape": "MetricName", "documentation": "<p>The name of the measure field.</p>"}, "TimestampList": {"shape": "TimestampList", "documentation": "<p>Timestamps for the anomalous metrics.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The pagination token that's included if more results are available.</p>"}, "TimeSeriesList": {"shape": "TimeSeriesList", "documentation": "<p>A list of anomalous metrics.</p>"}}}, "ListMetricSetsRequest": {"type": "structure", "members": {"AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the anomaly detector containing the metrics sets to list.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "box": true}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the result of the previous request was truncated, the response includes a <code>NextToken</code>. To retrieve the next set of results, use the token in the next request. Tokens expire after 24 hours.</p>"}}}, "ListMetricSetsResponse": {"type": "structure", "members": {"MetricSetSummaryList": {"shape": "MetricSetSummaryList", "documentation": "<p>A list of the datasets in the AWS Region, with configuration details for each.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, the list call returns this token. To retrieve the next set of results, use the token in the next list request. </p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The resource's Amazon Resource Name (ARN).</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "TagMap", "documentation": "<p>The resource's tags.</p>", "locationName": "Tags"}}}, "MaxResults": {"type": "integer", "max": 100, "min": 1}, "Message": {"type": "string"}, "Metric": {"type": "structure", "required": ["MetricName", "AggregationFunction"], "members": {"MetricName": {"shape": "ColumnName", "documentation": "<p>The name of the metric.</p>"}, "AggregationFunction": {"shape": "AggregationFunction", "documentation": "<p>The function with which the metric is calculated.</p>"}, "Namespace": {"shape": "Namespace", "documentation": "<p>The namespace for the metric.</p>"}}, "documentation": "<p>A calculation made by contrasting a measure and a dimension from your source data.</p>"}, "MetricChangePercentage": {"type": "double", "max": 100.0, "min": 0.0}, "MetricLevelImpact": {"type": "structure", "members": {"MetricName": {"shape": "MetricName", "documentation": "<p>The name of the measure.</p>"}, "NumTimeSeries": {"shape": "Integer", "documentation": "<p>The number of anomalous metrics for the measure.</p>"}, "ContributionMatrix": {"shape": "ContributionMatrix", "documentation": "<p>Details about the dimensions that contributed to the anomaly.</p>"}}, "documentation": "<p>Details about a measure affected by an anomaly.</p>"}, "MetricLevelImpactList": {"type": "list", "member": {"shape": "MetricLevelImpact"}}, "MetricList": {"type": "list", "member": {"shape": "Metric"}, "min": 1}, "MetricName": {"type": "string", "max": 256, "pattern": "^[a-zA-Z0-9][a-zA-Z0-9\\-_]*"}, "MetricNameList": {"type": "list", "member": {"shape": "MetricName"}, "max": 5, "min": 1}, "MetricSetDataQualityMetric": {"type": "structure", "members": {"MetricSetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the data quality metric array.</p>"}, "DataQualityMetricList": {"shape": "DataQualityMetricList", "documentation": "<p>The array of data quality metrics contained in the data quality metric set.</p>"}}, "documentation": "<p>An array of <code>DataQualityMetric</code> objects that describes one or more data quality metrics.</p>"}, "MetricSetDataQualityMetricList": {"type": "list", "member": {"shape": "MetricSetDataQualityMetric"}}, "MetricSetDescription": {"type": "string", "max": 256, "min": 1, "pattern": ".*\\S.*"}, "MetricSetDimensionFilter": {"type": "structure", "members": {"Name": {"shape": "ColumnName", "documentation": "<p>The dimension that you want to filter on.</p>"}, "FilterList": {"shape": "FilterList", "documentation": "<p>The list of filters that you are applying.</p>"}}, "documentation": "<p>Describes a list of filters for choosing a subset of dimension values. Each filter consists of the dimension and one of its values that you want to include. When multiple dimensions or values are specified, the dimensions are joined with an AND operation and the values are joined with an OR operation. </p>"}, "MetricSetDimensionFilterList": {"type": "list", "member": {"shape": "MetricSetDimensionFilter"}}, "MetricSetName": {"type": "string", "max": 63, "min": 1, "pattern": "^[a-zA-Z0-9][a-zA-Z0-9\\-_]*"}, "MetricSetSummary": {"type": "structure", "members": {"MetricSetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the dataset.</p>"}, "AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the detector to which the dataset belongs.</p>"}, "MetricSetDescription": {"shape": "MetricSetDescription", "documentation": "<p>The description of the dataset.</p>"}, "MetricSetName": {"shape": "MetricSetName", "documentation": "<p>The name of the dataset.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>The time at which the dataset was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The time at which the dataset was last modified.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The dataset's <a href=\"https://docs.aws.amazon.com/lookoutmetrics/latest/dev/detectors-tags.html\">tags</a>.</p>"}}, "documentation": "<p>Contains information about a dataset.</p>"}, "MetricSetSummaryList": {"type": "list", "member": {"shape": "MetricSetSummary"}}, "MetricSource": {"type": "structure", "members": {"S3SourceConfig": {"shape": "S3SourceConfig"}, "AppFlowConfig": {"shape": "AppFlowConfig", "documentation": "<p>Details about an AppFlow datasource.</p>"}, "CloudWatchConfig": {"shape": "CloudWatchConfig", "documentation": "<p>Details about an Amazon CloudWatch monitoring datasource.</p>"}, "RDSSourceConfig": {"shape": "RDSSourceConfig", "documentation": "<p>Details about an Amazon Relational Database Service (RDS) datasource.</p>"}, "RedshiftSourceConfig": {"shape": "RedshiftSourceConfig", "documentation": "<p>Details about an Amazon Redshift database datasource.</p>"}, "AthenaSourceConfig": {"shape": "AthenaSourceConfig", "documentation": "<p>Details about an Amazon Athena datasource.</p>"}}, "documentation": "<p>Contains information about source data used to generate metrics.</p>"}, "MetricValue": {"type": "double"}, "MetricValueList": {"type": "list", "member": {"shape": "MetricValue"}}, "Namespace": {"type": "string", "max": 255, "min": 1, "pattern": "[^:].*"}, "NextToken": {"type": "string", "max": 3000, "min": 1, "pattern": ".*\\S.*"}, "NumberAttributeValue": {"type": "string"}, "NumberListAttributeValue": {"type": "list", "member": {"shape": "NumberAttributeValue"}}, "Offset": {"type": "integer", "max": 432000, "min": 0}, "PoirotSecretManagerArn": {"type": "string", "max": 256, "pattern": "arn:([a-z\\d-]+):.*:.*:secret:AmazonLookoutMetrics-.+"}, "PutFeedbackRequest": {"type": "structure", "required": ["AnomalyDetectorArn", "AnomalyGroupTimeSeriesFeedback"], "members": {"AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the anomaly detector.</p>"}, "AnomalyGroupTimeSeriesFeedback": {"shape": "AnomalyGroupTimeSeriesFeedback", "documentation": "<p>Feedback for an anomalous metric.</p>"}}}, "PutFeedbackResponse": {"type": "structure", "members": {}}, "QuotaCode": {"type": "string"}, "QuoteSymbol": {"type": "string", "max": 1, "pattern": "[^\\r\\n]|^$"}, "RDSDatabaseIdentifier": {"type": "string", "max": 63, "min": 1, "pattern": "^[a-zA-Z](?!.*--)(?!.*-$)[0-9a-zA-Z\\-]*$"}, "RDSDatabaseName": {"type": "string", "max": 64, "min": 1, "pattern": "[a-zA-Z0-9_.]+"}, "RDSSourceConfig": {"type": "structure", "members": {"DBInstanceIdentifier": {"shape": "RDSDatabaseIdentifier", "documentation": "<p>A string identifying the database instance.</p>"}, "DatabaseHost": {"shape": "DatabaseHost", "documentation": "<p>The host name of the database.</p>"}, "DatabasePort": {"shape": "DatabasePort", "documentation": "<p>The port number where the database can be accessed.</p>", "box": true}, "SecretManagerArn": {"shape": "PoirotSecretManagerArn", "documentation": "<p>The Amazon Resource Name (ARN) of the AWS Secrets Manager role.</p>"}, "DatabaseName": {"shape": "RDSDatabaseName", "documentation": "<p>The name of the RDS database.</p>"}, "TableName": {"shape": "TableName", "documentation": "<p>The name of the table in the database.</p>"}, "RoleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the role.</p>"}, "VpcConfiguration": {"shape": "VpcConfiguration", "documentation": "<p>An object containing information about the Amazon Virtual Private Cloud (VPC) configuration.</p>"}}, "documentation": "<p>Contains information about the Amazon Relational Database Service (RDS) configuration.</p>"}, "RedshiftClusterIdentifier": {"type": "string", "max": 63, "min": 1, "pattern": "^[a-z](?!.*--)(?!.*-$)[0-9a-z\\-]*$"}, "RedshiftDatabaseName": {"type": "string", "max": 100, "min": 1, "pattern": "[a-zA-Z0-9_.]+"}, "RedshiftSourceConfig": {"type": "structure", "members": {"ClusterIdentifier": {"shape": "RedshiftClusterIdentifier", "documentation": "<p>A string identifying the Redshift cluster.</p>"}, "DatabaseHost": {"shape": "DatabaseHost", "documentation": "<p>The name of the database host.</p>"}, "DatabasePort": {"shape": "DatabasePort", "documentation": "<p>The port number where the database can be accessed.</p>", "box": true}, "SecretManagerArn": {"shape": "PoirotSecretManagerArn", "documentation": "<p>The Amazon Resource Name (ARN) of the AWS Secrets Manager role.</p>"}, "DatabaseName": {"shape": "RedshiftDatabaseName", "documentation": "<p>The Redshift database name.</p>"}, "TableName": {"shape": "TableName", "documentation": "<p>The table name of the Redshift database.</p>"}, "RoleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the role providing access to the database.</p>"}, "VpcConfiguration": {"shape": "VpcConfiguration", "documentation": "<p>Contains information about the Amazon Virtual Private Cloud (VPC) configuration.</p>"}}, "documentation": "<p>Provides information about the Amazon Redshift database configuration.</p>"}, "RelatedColumnName": {"type": "string", "max": 256, "min": 1, "pattern": ".*\\S.*"}, "RelationshipType": {"type": "string", "enum": ["CAUSE_OF_INPUT_ANOMALY_GROUP", "EFFECT_OF_INPUT_ANOMALY_GROUP"]}, "ResourceId": {"type": "string"}, "ResourceNotFoundException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "Message"}, "ResourceId": {"shape": "ResourceId", "documentation": "<p>The ID of the resource.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The type of the resource.</p>"}}, "documentation": "<p>The specified resource cannot be found. Check the ARN of the resource and try again.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ResourceType": {"type": "string"}, "S3SourceConfig": {"type": "structure", "members": {"RoleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of an IAM role that has read and write access permissions to the source S3 bucket.</p>"}, "TemplatedPathList": {"shape": "TemplatedPathList", "documentation": "<p>A list of templated paths to the source files.</p>"}, "HistoricalDataPathList": {"shape": "HistoricalDataPathList", "documentation": "<p>A list of paths to the historical data files.</p>"}, "FileFormatDescriptor": {"shape": "FileFormatDescriptor", "documentation": "<p>Contains information about a source file's formatting.</p>"}}, "documentation": "<p>Contains information about the configuration of the S3 bucket that contains source files.</p>"}, "SNSConfiguration": {"type": "structure", "required": ["RoleArn", "SnsTopicArn"], "members": {"RoleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the IAM role that has access to the target SNS topic.</p>"}, "SnsTopicArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the target SNS topic.</p>"}, "SnsFormat": {"shape": "SnsFormat", "documentation": "<p>The format of the SNS topic.</p> <ul> <li> <p> <code>JSON</code> – Send JSON alerts with an anomaly ID and a link to the anomaly detail page. This is the default.</p> </li> <li> <p> <code>LONG_TEXT</code> – Send human-readable alerts with information about the impacted timeseries and a link to the anomaly detail page. We recommend this for email.</p> </li> <li> <p> <code>SHORT_TEXT</code> – Send human-readable alerts with a link to the anomaly detail page. We recommend this for SMS.</p> </li> </ul>"}}, "documentation": "<p>Contains information about the SNS topic to which you want to send your alerts and the IAM role that has access to that topic.</p>"}, "SampleDataS3SourceConfig": {"type": "structure", "required": ["RoleArn", "FileFormatDescriptor"], "members": {"RoleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the role.</p>"}, "TemplatedPathList": {"shape": "TemplatedPathList", "documentation": "<p>An array of strings containing the list of templated paths.</p>"}, "HistoricalDataPathList": {"shape": "HistoricalDataPathList", "documentation": "<p>An array of strings containing the historical set of data paths.</p>"}, "FileFormatDescriptor": {"shape": "FileFormatDescriptor"}}, "documentation": "<p>Contains information about the source configuration in Amazon S3.</p>"}, "SampleRow": {"type": "list", "member": {"shape": "DataItem"}}, "SampleRows": {"type": "list", "member": {"shape": "SampleRow"}}, "Score": {"type": "double", "max": 100.0, "min": 0.0}, "SecurityGroupId": {"type": "string", "max": 255, "min": 1, "pattern": "[-0-9a-zA-Z]+"}, "SecurityGroupIdList": {"type": "list", "member": {"shape": "SecurityGroupId"}}, "SensitivityThreshold": {"type": "integer", "max": 100, "min": 0}, "ServiceCode": {"type": "string"}, "ServiceQuotaExceededException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "Message"}, "ResourceId": {"shape": "ResourceId", "documentation": "<p>The ID of the resource.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The type of the resource.</p>"}, "QuotaCode": {"shape": "QuotaCode", "documentation": "<p>The quota code.</p>"}, "ServiceCode": {"shape": "ServiceCode", "documentation": "<p>The service code.</p>"}}, "documentation": "<p>The request exceeded the service's quotas. Check the service quotas and try again.</p>", "error": {"httpStatusCode": 402}, "exception": true}, "SnsFormat": {"type": "string", "enum": ["LONG_TEXT", "SHORT_TEXT", "JSON"]}, "StringAttributeValue": {"type": "string"}, "StringListAttributeValue": {"type": "list", "member": {"shape": "StringAttributeValue"}}, "SubnetId": {"type": "string", "max": 255, "pattern": "[\\-0-9a-zA-Z]+"}, "SubnetIdList": {"type": "list", "member": {"shape": "SubnetId"}}, "TableName": {"type": "string", "max": 100, "min": 1, "pattern": "^[a-zA-Z][a-zA-Z0-9_.]*$"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 1}, "TagMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "max": 50, "min": 1}, "TagResourceRequest": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The resource's Amazon Resource Name (ARN).</p>", "location": "uri", "locationName": "resourceArn"}, "Tags": {"shape": "TagMap", "documentation": "<p>Tags to apply to the resource. Tag keys and values can contain letters, numbers, spaces, and the following symbols: <code>_.:/=+@-</code> </p>", "locationName": "tags"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256}, "TemplatedPath": {"type": "string", "max": 1024, "pattern": "^s3://[a-zA-Z0-9_\\-\\/ {}=]+$"}, "TemplatedPathList": {"type": "list", "member": {"shape": "<PERSON>mp<PERSON><PERSON><PERSON>"}, "max": 1, "min": 1}, "TimeSeries": {"type": "structure", "required": ["TimeSeriesId", "DimensionList", "MetricValueList"], "members": {"TimeSeriesId": {"shape": "TimeSeriesId", "documentation": "<p>The ID of the metric.</p>"}, "DimensionList": {"shape": "DimensionNameValueList", "documentation": "<p>The dimensions of the metric.</p>"}, "MetricValueList": {"shape": "MetricValueList", "documentation": "<p>The values for the metric.</p>"}}, "documentation": "<p>Details about a metric. A metric is an aggregation of the values of a measure for a dimension value, such as <i>availability</i> in the <i>us-east-1</i> Region.</p>"}, "TimeSeriesFeedback": {"type": "structure", "members": {"TimeSeriesId": {"shape": "TimeSeriesId", "documentation": "<p>The ID of the metric.</p>"}, "IsAnomaly": {"shape": "Boolean", "documentation": "<p>Feedback on whether the metric is a legitimate anomaly.</p>"}}, "documentation": "<p>Details about feedback submitted for an anomalous metric.</p>"}, "TimeSeriesFeedbackList": {"type": "list", "member": {"shape": "TimeSeriesFeedback"}}, "TimeSeriesId": {"type": "string", "max": 520, "pattern": ".*\\S.*"}, "TimeSeriesList": {"type": "list", "member": {"shape": "TimeSeries"}}, "Timestamp": {"type": "timestamp"}, "TimestampColumn": {"type": "structure", "members": {"ColumnName": {"shape": "ColumnName", "documentation": "<p>The name of the timestamp column.</p>"}, "ColumnFormat": {"shape": "DateTimeFormat", "documentation": "<p>The format of the timestamp column.</p>"}}, "documentation": "<p>Contains information about the column used to track time in a source data file.</p>"}, "TimestampList": {"type": "list", "member": {"shape": "TimestampString"}}, "TimestampString": {"type": "string", "max": 60, "pattern": "^([12]\\d{3})-(1[0-2]|0[1-9])-(0[1-9]|[12]\\d|3[01])T([01]\\d|2[0-3]):([0-5]\\d):([0-5]\\d)(Z|(\\+|\\-)(0\\d|1[0-2]):([0-5]\\d)(\\[[[:alnum:]\\/\\_]+\\])?)$"}, "Timezone": {"type": "string", "max": 60, "pattern": ".*\\S.*"}, "TooManyRequestsException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "Message"}}, "documentation": "<p>The request was denied due to too many requests being submitted at the same time.</p>", "error": {"httpStatusCode": 429}, "exception": true}, "UUID": {"type": "string", "max": 63, "pattern": "[a-z0-9]{8}-[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{12}"}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The resource's Amazon Resource Name (ARN).</p>", "location": "uri", "locationName": "resourceArn"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>Keys to remove from the resource's tags.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateAlertRequest": {"type": "structure", "required": ["Alert<PERSON>rn"], "members": {"AlertArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the alert to update.</p>"}, "AlertDescription": {"shape": "AlertDescription", "documentation": "<p>A description of the alert.</p>"}, "AlertSensitivityThreshold": {"shape": "SensitivityThreshold", "documentation": "<p>An integer from 0 to 100 specifying the alert sensitivity threshold.</p>"}, "Action": {"shape": "Action", "documentation": "<p>Action that will be triggered when there is an alert.</p>"}, "AlertFilters": {"shape": "Alert<PERSON><PERSON><PERSON>", "documentation": "<p>The configuration of the alert filters, containing MetricList and DimensionFilterList.</p>"}}}, "UpdateAlertResponse": {"type": "structure", "members": {"AlertArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the updated alert.</p>"}}}, "UpdateAnomalyDetectorRequest": {"type": "structure", "required": ["AnomalyDetectorArn"], "members": {"AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the detector to update.</p>"}, "KmsKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of an AWS KMS encryption key.</p>"}, "AnomalyDetectorDescription": {"shape": "AnomalyDetectorDescription", "documentation": "<p>The updated detector description.</p>"}, "AnomalyDetectorConfig": {"shape": "AnomalyDetectorConfig", "documentation": "<p>Contains information about the configuration to which the detector will be updated.</p>"}}}, "UpdateAnomalyDetectorResponse": {"type": "structure", "members": {"AnomalyDetectorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the updated detector.</p>"}}}, "UpdateMetricSetRequest": {"type": "structure", "required": ["MetricSetArn"], "members": {"MetricSetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the dataset to update.</p>"}, "MetricSetDescription": {"shape": "MetricSetDescription", "documentation": "<p>The dataset's description.</p>"}, "MetricList": {"shape": "MetricList", "documentation": "<p>The metric list.</p>"}, "Offset": {"shape": "Offset", "documentation": "<p>After an interval ends, the amount of seconds that the detector waits before importing data. Offset is only supported for S3, Redshift, Athena and datasources.</p>", "box": true}, "TimestampColumn": {"shape": "TimestampColumn", "documentation": "<p>The timestamp column.</p>"}, "DimensionList": {"shape": "DimensionList", "documentation": "<p>The dimension list.</p>"}, "MetricSetFrequency": {"shape": "Frequency", "documentation": "<p>The dataset's interval.</p>"}, "MetricSource": {"shape": "MetricSource"}, "DimensionFilterList": {"shape": "MetricSetDimensionFilterList", "documentation": "<p>Describes a list of filters for choosing specific dimensions and specific values. Each filter consists of the dimension and one of its values that you want to include. When multiple dimensions or values are specified, the dimensions are joined with an AND operation and the values are joined with an OR operation.</p>"}}}, "UpdateMetricSetResponse": {"type": "structure", "members": {"MetricSetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the dataset.</p>"}}}, "ValidationException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "Message"}, "Reason": {"shape": "ValidationExceptionReason", "documentation": "<p>The reason that validation failed.</p>"}, "Fields": {"shape": "ValidationExceptionFieldList", "documentation": "<p>Fields that failed validation.</p>"}}, "documentation": "<p>The input fails to satisfy the constraints specified by the AWS service. Check your input values and try again.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ValidationExceptionField": {"type": "structure", "required": ["Name", "Message"], "members": {"Name": {"shape": "FieldName", "documentation": "<p>The name of the field.</p>"}, "Message": {"shape": "Message", "documentation": "<p>The message with more information about the validation exception.</p>"}}, "documentation": "<p>Contains information about a a field in a validation exception.</p>"}, "ValidationExceptionFieldList": {"type": "list", "member": {"shape": "ValidationExceptionField"}}, "ValidationExceptionReason": {"type": "string", "enum": ["UNKNOWN_OPERATION", "CANNOT_PARSE", "FIELD_VALIDATION_FAILED", "OTHER"]}, "VpcConfiguration": {"type": "structure", "required": ["SubnetIdList", "SecurityGroupIdList"], "members": {"SubnetIdList": {"shape": "SubnetIdList", "documentation": "<p>An array of strings containing the Amazon VPC subnet IDs (e.g., <code>subnet-0bb1c79de3EXAMPLE</code>.</p>"}, "SecurityGroupIdList": {"shape": "SecurityGroupIdList", "documentation": "<p>An array of strings containing the list of security groups.</p>"}}, "documentation": "<p>Contains configuration information about the Amazon Virtual Private Cloud (VPC).</p>"}}, "documentation": "<p>This is the <i>Amazon Lookout for Metrics API Reference</i>. For an introduction to the service with tutorials for getting started, visit <a href=\"https://docs.aws.amazon.com/lookoutmetrics/latest/dev\">Amazon Lookout for Metrics Developer Guide</a>.</p>"}