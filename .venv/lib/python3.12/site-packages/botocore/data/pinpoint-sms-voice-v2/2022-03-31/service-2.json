{"version": "2.0", "metadata": {"apiVersion": "2022-03-31", "endpointPrefix": "sms-voice", "jsonVersion": "1.0", "protocol": "json", "serviceFullName": "Amazon Pinpoint SMS Voice V2", "serviceId": "Pinpoint SMS Voice V2", "signatureVersion": "v4", "signingName": "sms-voice", "targetPrefix": "PinpointSMSVoiceV2", "uid": "pinpoint-sms-voice-v2-2022-03-31"}, "operations": {"AssociateOriginationIdentity": {"name": "AssociateOriginationIdentity", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AssociateOriginationIdentityRequest"}, "output": {"shape": "AssociateOriginationIdentityResult"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Associates the specified origination identity with a pool.</p> <p>If the origination identity is a phone number and is already associated with another pool, an Error is returned. A sender ID can be associated with multiple pools.</p> <p>If the origination identity configuration doesn't match the pool's configuration, an Error is returned.</p>"}, "CreateConfigurationSet": {"name": "CreateConfigurationSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateConfigurationSetRequest"}, "output": {"shape": "CreateConfigurationSetResult"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a new configuration set. After you create the configuration set, you can add one or more event destinations to it.</p> <p>A configuration set is a set of rules that you apply to the SMS and voice messages that you send.</p> <p>When you send a message, you can optionally specify a single configuration set.</p>"}, "CreateEventDestination": {"name": "CreateEventDestination", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateEventDestinationRequest"}, "output": {"shape": "CreateEventDestinationResult"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a new event destination in a configuration set.</p> <p>An event destination is a location where you send message events. The event options are Amazon CloudWatch, Amazon Kinesis Data Firehose, or Amazon SNS. For example, when a message is delivered successfully, you can send information about that event to an event destination, or send notifications to endpoints that are subscribed to an Amazon SNS topic.</p> <p>Each configuration set can contain between 0 and 5 event destinations. Each event destination can contain a reference to a single destination, such as a CloudWatch or Kinesis Data Firehose destination.</p>"}, "CreateOptOutList": {"name": "CreateOptOutList", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateOptOutListRequest"}, "output": {"shape": "CreateOptOutListResult"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a new opt-out list.</p> <p>If the opt-out list name already exists, an Error is returned.</p> <p>An opt-out list is a list of phone numbers that are opted out, meaning you can't send SMS or voice messages to them. If end user replies with the keyword \"STOP,\" an entry for the phone number is added to the opt-out list. In addition to STOP, your recipients can use any supported opt-out keyword, such as CANCEL or OPTOUT. For a list of supported opt-out keywords, see <a href=\"https://docs.aws.amazon.com/pinpoint/latest/userguide/channels-sms-manage.html#channels-sms-manage-optout\"> SMS opt out </a> in the <i>Amazon Pinpoint User Guide</i>.</p>"}, "CreatePool": {"name": "CreatePool", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreatePoolRequest"}, "output": {"shape": "CreatePoolResult"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a new pool and associates the specified origination identity to the pool. A pool can include one or more phone numbers and SenderIds that are associated with your Amazon Web Services account.</p> <p>The new pool inherits its configuration from the specified origination identity. This includes keywords, message type, opt-out list, two-way configuration, and self-managed opt-out configuration. Deletion protection isn't inherited from the origination identity and defaults to false.</p> <p>If the origination identity is a phone number and is already associated with another pool, an Error is returned. A sender ID can be associated with multiple pools.</p>"}, "DeleteConfigurationSet": {"name": "DeleteConfigurationSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteConfigurationSetRequest"}, "output": {"shape": "DeleteConfigurationSetResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an existing configuration set.</p> <p>A configuration set is a set of rules that you apply to voice and SMS messages that you send. In a configuration set, you can specify a destination for specific types of events related to voice and SMS messages. </p>"}, "DeleteDefaultMessageType": {"name": "DeleteDefaultMessageType", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDefaultMessageTypeRequest"}, "output": {"shape": "DeleteDefaultMessageTypeResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an existing default message type on a configuration set.</p> <p> A message type is a type of messages that you plan to send. If you send account-related messages or time-sensitive messages such as one-time passcodes, choose <b>Transactional</b>. If you plan to send messages that contain marketing material or other promotional content, choose <b>Promotional</b>. This setting applies to your entire Amazon Web Services account. </p>"}, "DeleteDefaultSenderId": {"name": "DeleteDefaultSenderId", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDefaultSenderIdRequest"}, "output": {"shape": "DeleteDefaultSenderIdResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an existing default sender ID on a configuration set.</p> <p>A default sender ID is the identity that appears on recipients' devices when they receive SMS messages. Support for sender ID capabilities varies by country or region.</p>"}, "DeleteEventDestination": {"name": "DeleteEventDestination", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteEventDestinationRequest"}, "output": {"shape": "DeleteEventDestinationResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an existing event destination.</p> <p>An event destination is a location where you send response information about the messages that you send. For example, when a message is delivered successfully, you can send information about that event to an Amazon CloudWatch destination, or send notifications to endpoints that are subscribed to an Amazon SNS topic.</p>"}, "DeleteKeyword": {"name": "DeleteKeyword", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteKeywordRequest"}, "output": {"shape": "DeleteKeywordResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an existing keyword from an origination phone number or pool.</p> <p>A keyword is a word that you can search for on a particular phone number or pool. It is also a specific word or phrase that an end user can send to your number to elicit a response, such as an informational message or a special offer. When your number receives a message that begins with a keyword, Amazon Pinpoint responds with a customizable message.</p> <p>Keywords \"HELP\" and \"STOP\" can't be deleted or modified.</p>"}, "DeleteOptOutList": {"name": "DeleteOptOutList", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteOptOutListRequest"}, "output": {"shape": "DeleteOptOutListResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an existing opt-out list. All opted out phone numbers in the opt-out list are deleted.</p> <p>If the specified opt-out list name doesn't exist or is in-use by an origination phone number or pool, an Error is returned.</p>"}, "DeleteOptedOutNumber": {"name": "DeleteOptedOutNumber", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteOptedOutNumberRequest"}, "output": {"shape": "DeleteOptedOutNumberResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an existing opted out destination phone number from the specified opt-out list.</p> <p>Each destination phone number can only be deleted once every 30 days.</p> <p>If the specified destination phone number doesn't exist or if the opt-out list doesn't exist, an Error is returned.</p>"}, "DeletePool": {"name": "DeletePool", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeletePoolRequest"}, "output": {"shape": "DeletePoolResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an existing pool. Deleting a pool disassociates all origination identities from that pool.</p> <p>If the pool status isn't active or if deletion protection is enabled, an Error is returned.</p> <p>A pool is a collection of phone numbers and SenderIds. A pool can include one or more phone numbers and SenderIds that are associated with your Amazon Web Services account.</p>"}, "DeleteTextMessageSpendLimitOverride": {"name": "DeleteTextMessageSpendLimitOverride", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteTextMessageSpendLimitOverrideRequest"}, "output": {"shape": "DeleteTextMessageSpendLimitOverrideResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an account-level monthly spending limit override for sending text messages. Deleting a spend limit override will set the <code>EnforcedLimit</code> to equal the <code>MaxLimit</code>, which is controlled by Amazon Web Services. For more information on spend limits (quotas) see <a href=\"https://docs.aws.amazon.com/pinpoint/latest/developerguide/quotas.html\">Amazon Pinpoint quotas </a> in the <i>Amazon Pinpoint Developer Guide</i>.</p>"}, "DeleteVoiceMessageSpendLimitOverride": {"name": "DeleteVoiceMessageSpendLimitOverride", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteVoiceMessageSpendLimitOverrideRequest"}, "output": {"shape": "DeleteVoiceMessageSpendLimitOverrideResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an account level monthly spend limit override for sending voice messages. Deleting a spend limit override sets the <code>EnforcedLimit</code> equal to the <code>MaxLimit</code>, which is controlled by Amazon Web Services. For more information on spending limits (quotas) see <a href=\"https://docs.aws.amazon.com/pinpoint/latest/developerguide/quotas.html\">Amazon Pinpoint quotas</a> in the <i>Amazon Pinpoint Developer Guide</i>.</p>"}, "DescribeAccountAttributes": {"name": "DescribeAccountAttributes", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeAccountAttributesRequest"}, "output": {"shape": "DescribeAccountAttributesResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Describes attributes of your Amazon Web Services account. The supported account attributes include account tier, which indicates whether your account is in the sandbox or production environment. When you're ready to move your account out of the sandbox, create an Amazon Web Services Support case for a service limit increase request.</p> <p>New Amazon Pinpoint accounts are placed into an SMS or voice sandbox. The sandbox protects both Amazon Web Services end recipients and SMS or voice recipients from fraud and abuse. </p>"}, "DescribeAccountLimits": {"name": "DescribeAccountLimits", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeAccountLimitsRequest"}, "output": {"shape": "DescribeAccountLimitsResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Describes the current Amazon Pinpoint SMS Voice V2 resource quotas for your account. The description for a quota includes the quota name, current usage toward that quota, and the quota's maximum value.</p> <p>When you establish an Amazon Web Services account, the account has initial quotas on the maximum number of configuration sets, opt-out lists, phone numbers, and pools that you can create in a given Region. For more information see <a href=\"https://docs.aws.amazon.com/pinpoint/latest/developerguide/quotas.html\"> Amazon Pinpoint quotas </a> in the <i>Amazon Pinpoint Developer Guide</i>.</p>"}, "DescribeConfigurationSets": {"name": "DescribeConfigurationSets", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeConfigurationSetsRequest"}, "output": {"shape": "DescribeConfigurationSetsResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Describes the specified configuration sets or all in your account.</p> <p>If you specify configuration set names, the output includes information for only the specified configuration sets. If you specify filters, the output includes information for only those configuration sets that meet the filter criteria. If you don't specify configuration set names or filters, the output includes information for all configuration sets.</p> <p>If you specify a configuration set name that isn't valid, an error is returned.</p>"}, "DescribeKeywords": {"name": "DescribeKeywords", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeKeywordsRequest"}, "output": {"shape": "DescribeKeywordsResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Describes the specified keywords or all keywords on your origination phone number or pool.</p> <p>A keyword is a word that you can search for on a particular phone number or pool. It is also a specific word or phrase that an end user can send to your number to elicit a response, such as an informational message or a special offer. When your number receives a message that begins with a keyword, Amazon Pinpoint responds with a customizable message.</p> <p>If you specify a keyword that isn't valid, an Error is returned.</p>"}, "DescribeOptOutLists": {"name": "DescribeOptOutLists", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeOptOutListsRequest"}, "output": {"shape": "DescribeOptOutListsResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Describes the specified opt-out list or all opt-out lists in your account.</p> <p>If you specify opt-out list names, the output includes information for only the specified opt-out lists. Opt-out lists include only those that meet the filter criteria. If you don't specify opt-out list names or filters, the output includes information for all opt-out lists.</p> <p>If you specify an opt-out list name that isn't valid, an Error is returned.</p>"}, "DescribeOptedOutNumbers": {"name": "DescribeOptedOutNumbers", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeOptedOutNumbersRequest"}, "output": {"shape": "DescribeOptedOutNumbersResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Describes the specified opted out destination numbers or all opted out destination numbers in an opt-out list.</p> <p>If you specify opted out numbers, the output includes information for only the specified opted out numbers. If you specify filters, the output includes information for only those opted out numbers that meet the filter criteria. If you don't specify opted out numbers or filters, the output includes information for all opted out destination numbers in your opt-out list.</p> <p>If you specify an opted out number that isn't valid, an Error is returned.</p>"}, "DescribePhoneNumbers": {"name": "DescribePhoneNumbers", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribePhoneNumbersRequest"}, "output": {"shape": "DescribePhoneNumbersResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Describes the specified origination phone number, or all the phone numbers in your account.</p> <p>If you specify phone number IDs, the output includes information for only the specified phone numbers. If you specify filters, the output includes information for only those phone numbers that meet the filter criteria. If you don't specify phone number IDs or filters, the output includes information for all phone numbers.</p> <p>If you specify a phone number ID that isn't valid, an Error is returned.</p>"}, "DescribePools": {"name": "DescribePools", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribePoolsRequest"}, "output": {"shape": "DescribePoolsResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves the specified pools or all pools associated with your Amazon Web Services account.</p> <p>If you specify pool IDs, the output includes information for only the specified pools. If you specify filters, the output includes information for only those pools that meet the filter criteria. If you don't specify pool IDs or filters, the output includes information for all pools.</p> <p>If you specify a pool ID that isn't valid, an Error is returned.</p> <p>A pool is a collection of phone numbers and SenderIds. A pool can include one or more phone numbers and SenderIds that are associated with your Amazon Web Services account.</p>"}, "DescribeSenderIds": {"name": "DescribeSenderIds", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeSenderIdsRequest"}, "output": {"shape": "DescribeSenderIdsResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Describes the specified SenderIds or all SenderIds associated with your Amazon Web Services account.</p> <p>If you specify SenderIds, the output includes information for only the specified SenderIds. If you specify filters, the output includes information for only those SenderIds that meet the filter criteria. If you don't specify SenderIds or filters, the output includes information for all SenderIds.</p> <p>f you specify a sender ID that isn't valid, an Error is returned.</p>"}, "DescribeSpendLimits": {"name": "DescribeSpendLimits", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeSpendLimitsRequest"}, "output": {"shape": "DescribeSpendLimitsResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Describes the current Amazon Pinpoint monthly spend limits for sending voice and text messages.</p> <p>When you establish an Amazon Web Services account, the account has initial monthly spend limit in a given Region. For more information on increasing your monthly spend limit, see <a href=\"https://docs.aws.amazon.com/pinpoint/latest/userguide/channels-sms-awssupport-spend-threshold.html\"> Requesting increases to your monthly SMS spending quota for Amazon Pinpoint </a> in the <i>Amazon Pinpoint User Guide</i>.</p>"}, "DisassociateOriginationIdentity": {"name": "DisassociateOriginationIdentity", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DisassociateOriginationIdentityRequest"}, "output": {"shape": "DisassociateOriginationIdentityResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Removes the specified origination identity from an existing pool.</p> <p>If the origination identity isn't associated with the specified pool, an Error is returned.</p>"}, "ListPoolOriginationIdentities": {"name": "ListPoolOriginationIdentities", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListPoolOriginationIdentitiesRequest"}, "output": {"shape": "ListPoolOriginationIdentitiesResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists all associated origination identities in your pool.</p> <p>If you specify filters, the output includes information for only those origination identities that meet the filter criteria.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>List all tags associated with a resource.</p>"}, "PutKeyword": {"name": "PutKeyword", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutKeywordRequest"}, "output": {"shape": "PutKeywordResult"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates or updates a keyword configuration on an origination phone number or pool.</p> <p> A keyword is a word that you can search for on a particular phone number or pool. It is also a specific word or phrase that an end user can send to your number to elicit a response, such as an informational message or a special offer. When your number receives a message that begins with a keyword, Amazon Pinpoint responds with a customizable message.</p> <p>If you specify a keyword that isn't valid, an Error is returned.</p>"}, "PutOptedOutNumber": {"name": "PutOptedOutNumber", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutOptedOutNumberRequest"}, "output": {"shape": "PutOptedOutNumberResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates an opted out destination phone number in the opt-out list.</p> <p>If the destination phone number isn't valid or if the specified opt-out list doesn't exist, an Error is returned.</p>"}, "ReleasePhoneNumber": {"name": "ReleasePhoneNumber", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ReleasePhoneNumberRequest"}, "output": {"shape": "ReleasePhoneNumberResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Releases an existing origination phone number in your account. Once released, a phone number is no longer available for sending messages.</p> <p>If the origination phone number has deletion protection enabled or is associated with a pool, an Error is returned.</p>"}, "RequestPhoneNumber": {"name": "RequestPhoneNumber", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RequestPhoneNumberRequest"}, "output": {"shape": "RequestPhoneNumberResult"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Request an origination phone number for use in your account. For more information on phone number request see <a href=\"https://docs.aws.amazon.com/pinpoint/latest/userguide/settings-sms-request-number.html\"> Requesting a number </a> in the <i>Amazon Pinpoint User Guide</i>.</p>"}, "SendTextMessage": {"name": "SendTextMessage", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "SendTextMessageRequest"}, "output": {"shape": "SendTextMessageResult"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a new text message and sends it to a recipient's phone number.</p> <p>SMS throughput limits are measured in Message Parts per Second (MPS). Your MPS limit depends on the destination country of your messages, as well as the type of phone number (origination number) that you use to send the message. For more information, see <a href=\"https://docs.aws.amazon.com/pinpoint/latest/userguide/channels-sms-limitations-mps.html\">Message Parts per Second (MPS) limits</a> in the <i>Amazon Pinpoint User Guide</i>.</p>"}, "SendVoiceMessage": {"name": "SendVoiceMessage", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "SendVoiceMessageRequest"}, "output": {"shape": "SendVoiceMessageResult"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Allows you to send a request that sends a text message through Amazon Pinpoint. This operation uses <a href=\"http://aws.amazon.com/polly/\">Amazon Polly</a> to convert a text script into a voice message.</p>"}, "SetDefaultMessageType": {"name": "SetDefaultMessageType", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "SetDefaultMessageTypeRequest"}, "output": {"shape": "SetDefaultMessageTypeResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Sets the default message type on a configuration set.</p> <p>Choose the category of SMS messages that you plan to send from this account. If you send account-related messages or time-sensitive messages such as one-time passcodes, choose <b>Transactional</b>. If you plan to send messages that contain marketing material or other promotional content, choose <b>Promotional</b>. This setting applies to your entire Amazon Web Services account.</p>"}, "SetDefaultSenderId": {"name": "SetDefaultSenderId", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "SetDefaultSenderIdRequest"}, "output": {"shape": "SetDefaultSenderIdResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Sets default sender ID on a configuration set.</p> <p>When sending a text message to a destination country that supports sender IDs, the default sender ID on the configuration set specified will be used if no dedicated origination phone numbers or registered sender IDs are available in your account.</p>"}, "SetTextMessageSpendLimitOverride": {"name": "SetTextMessageSpendLimitOverride", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "SetTextMessageSpendLimitOverrideRequest"}, "output": {"shape": "SetTextMessageSpendLimitOverrideResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Sets an account level monthly spend limit override for sending text messages. The requested spend limit must be less than or equal to the <code>MaxLimit</code>, which is set by Amazon Web Services. </p>"}, "SetVoiceMessageSpendLimitOverride": {"name": "SetVoiceMessageSpendLimitOverride", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "SetVoiceMessageSpendLimitOverrideRequest"}, "output": {"shape": "SetVoiceMessageSpendLimitOverrideResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Sets an account level monthly spend limit override for sending voice messages. The requested spend limit must be less than or equal to the <code>MaxLimit</code>, which is set by Amazon Web Services. </p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResult"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Adds or overwrites only the specified tags for the specified Amazon Pinpoint SMS Voice, version 2 resource. When you specify an existing tag key, the value is overwritten with the new value. Each resource can have a maximum of 50 tags. Each tag consists of a key and an optional value. Tag keys must be unique per resource. For more information about tags, see <a href=\"https://docs.aws.amazon.com/pinpoint/latest/developerguide/tagging-resources.html\"> Tagging Amazon Pinpoint resources</a> in the <i>Amazon Pinpoint Developer Guide</i>.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Removes the association of the specified tags from an Amazon Pinpoint SMS Voice V2 resource. For more information on tags see <a href=\"https://docs.aws.amazon.com/pinpoint/latest/developerguide/tagging-resources.html\"> Tagging Amazon Pinpoint resources</a> in the <i>Amazon Pinpoint Developer Guide</i>. </p>"}, "UpdateEventDestination": {"name": "UpdateEventDestination", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateEventDestinationRequest"}, "output": {"shape": "UpdateEventDestinationResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates an existing event destination in a configuration set. You can update the IAM role ARN for CloudWatch Logs and Kinesis Data Firehose. You can also enable or disable the event destination.</p> <p>You may want to update an event destination to change its matching event types or updating the destination resource ARN. You can't change an event destination's type between CloudWatch Logs, Kinesis Data Firehose, and Amazon SNS.</p>"}, "UpdatePhoneNumber": {"name": "UpdatePhoneNumber", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdatePhoneNumberRequest"}, "output": {"shape": "UpdatePhoneNumberResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates the configuration of an existing origination phone number. You can update the opt-out list, enable or disable two-way messaging, change the TwoWayChannelArn, enable or disable self-managed opt-outs, and enable or disable deletion protection.</p> <p>If the origination phone number is associated with a pool, an Error is returned.</p>"}, "UpdatePool": {"name": "UpdatePool", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdatePoolRequest"}, "output": {"shape": "UpdatePoolResult"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates the configuration of an existing pool. You can update the opt-out list, enable or disable two-way messaging, change the <code>TwoWayChannelArn</code>, enable or disable self-managed opt-outs, enable or disable deletion protection, and enable or disable shared routes.</p>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"Message": {"shape": "String"}, "Reason": {"shape": "AccessDeniedExceptionReason", "documentation": "<p>The reason for the exception.</p>"}}, "documentation": "<p>The request was denied because you don't have sufficient permissions to access the resource.</p>", "exception": true}, "AccessDeniedExceptionReason": {"type": "string", "enum": ["INSUFFICIENT_ACCOUNT_REPUTATION", "ACCOUNT_DISABLED"]}, "AccountAttribute": {"type": "structure", "required": ["Name", "Value"], "members": {"Name": {"shape": "AccountAttributeName", "documentation": "<p>The name of the account attribute.</p>"}, "Value": {"shape": "String", "documentation": "<p>The value associated with the account attribute name.</p>"}}, "documentation": "<p>Displays the attributes associated with a single Amazon Web Services account.</p>"}, "AccountAttributeList": {"type": "list", "member": {"shape": "AccountAttribute"}}, "AccountAttributeName": {"type": "string", "enum": ["ACCOUNT_TIER"]}, "AccountLimit": {"type": "structure", "required": ["Name", "Used", "Max"], "members": {"Name": {"shape": "AccountLimitName", "documentation": "<p>The name of the attribute to apply the account limit to.</p>"}, "Used": {"shape": "PrimitiveLong", "documentation": "<p>The current amount that has been spent, in US dollars.</p>"}, "Max": {"shape": "PrimitiveLong", "documentation": "<p>The Amazon Web Services set limit for that resource type, in US dollars.</p>"}}, "documentation": "<p>The current resource quotas associated with an Amazon Web Services account.</p>"}, "AccountLimitList": {"type": "list", "member": {"shape": "AccountLimit"}}, "AccountLimitName": {"type": "string", "enum": ["PHONE_NUMBERS", "POOLS", "CONFIGURATION_SETS", "OPT_OUT_LISTS"]}, "AmazonResourceName": {"type": "string", "max": 256, "min": 20, "pattern": "arn:[A-Za-z0-9_:/-]+"}, "AssociateOriginationIdentityRequest": {"type": "structure", "required": ["PoolId", "OriginationIdentity", "IsoCountryCode"], "members": {"PoolId": {"shape": "PoolIdOrArn", "documentation": "<p>The pool to update with the new Identity. This value can be either the PoolId or PoolArn, and you can find these values using <a>DescribePools</a>.</p>"}, "OriginationIdentity": {"shape": "PhoneOrSenderIdOrArn", "documentation": "<p>The origination identity to use, such as PhoneNumberId, PhoneNumberArn, SenderId, or SenderIdArn. You can use <a>DescribePhoneNumbers</a> to find the values for PhoneNumberId and PhoneNumberArn, while <a>DescribeSenderIds</a> can be used to get the values for SenderId and SenderIdArn.</p>"}, "IsoCountryCode": {"shape": "IsoCountryCode", "documentation": "<p>The new two-character code, in ISO 3166-1 alpha-2 format, for the country or region of the origination identity.</p>"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don't specify a client token, a randomly generated token is used for the request to ensure idempotency.</p>", "idempotencyToken": true}}}, "AssociateOriginationIdentityResult": {"type": "structure", "members": {"PoolArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the pool that is now associated with the origination identity.</p>"}, "PoolId": {"shape": "String", "documentation": "<p>The PoolId of the pool that is now associated with the origination identity.</p>"}, "OriginationIdentityArn": {"shape": "String", "documentation": "<p>The PhoneNumberArn or SenderIdArn of the origination identity.</p>"}, "OriginationIdentity": {"shape": "String", "documentation": "<p>The PhoneNumberId or SenderId of the origination identity.</p>"}, "IsoCountryCode": {"shape": "IsoCountryCode", "documentation": "<p>The two-character code, in ISO 3166-1 alpha-2 format, for the country or region. </p>"}}}, "Boolean": {"type": "boolean", "box": true}, "ClientToken": {"type": "string", "max": 64, "min": 1, "pattern": "[!-~]+"}, "CloudWatchLogsDestination": {"type": "structure", "required": ["IamRoleArn", "LogGroupArn"], "members": {"IamRoleArn": {"shape": "IamRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of an Amazon Identity and Access Management (IAM) role that is able to write event data to an Amazon CloudWatch destination.</p>"}, "LogGroupArn": {"shape": "LogGroupArn", "documentation": "<p>The name of the Amazon CloudWatch log group that you want to record events in. </p>"}}, "documentation": "<p>Contains the destination configuration to use when publishing message sending events. </p>"}, "ConfigurationSetFilter": {"type": "structure", "required": ["Name", "Values"], "members": {"Name": {"shape": "ConfigurationSetFilterName", "documentation": "<p>The name of the attribute to filter on.</p>"}, "Values": {"shape": "FilterValueList", "documentation": "<p>An array values to filter for.</p>"}}, "documentation": "<p>The information for configuration sets that meet a specified criteria.</p>"}, "ConfigurationSetFilterList": {"type": "list", "member": {"shape": "ConfigurationSetFilter"}, "max": 20, "min": 0}, "ConfigurationSetFilterName": {"type": "string", "enum": ["event-destination-name", "matching-event-types", "default-message-type", "default-sender-id"]}, "ConfigurationSetInformation": {"type": "structure", "required": ["ConfigurationSetArn", "ConfigurationSetName", "EventDestinations", "CreatedTimestamp"], "members": {"ConfigurationSetArn": {"shape": "String", "documentation": "<p>The Resource Name (ARN) of the ConfigurationSet.</p>"}, "ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the ConfigurationSet.</p>"}, "EventDestinations": {"shape": "EventDestinationList", "documentation": "<p>An array of EventDestination objects that describe any events to log and where to log them.</p>"}, "DefaultMessageType": {"shape": "MessageType", "documentation": "<p>The type of message. Valid values are TRANSACTIONAL for messages that are critical or time-sensitive and PROMOTIONAL for messages that aren't critical or time-sensitive.</p>"}, "DefaultSenderId": {"shape": "SenderId", "documentation": "<p>The default sender ID used by the ConfigurationSet.</p>"}, "CreatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The time when the ConfigurationSet was created, in <a href=\"https://www.epochconverter.com/\">UNIX epoch time</a> format.</p>"}}, "documentation": "<p>Information related to a given configuration set in your Amazon Web Services account.</p>"}, "ConfigurationSetInformationList": {"type": "list", "member": {"shape": "ConfigurationSetInformation"}}, "ConfigurationSetName": {"type": "string", "max": 64, "min": 1, "pattern": "[A-Za-z0-9_-]+"}, "ConfigurationSetNameList": {"type": "list", "member": {"shape": "ConfigurationSetNameOrArn"}, "max": 5, "min": 0}, "ConfigurationSetNameOrArn": {"type": "string", "max": 256, "min": 1, "pattern": "[A-Za-z0-9_:/-]+"}, "ConflictException": {"type": "structure", "members": {"Message": {"shape": "String"}, "Reason": {"shape": "ConflictExceptionReason", "documentation": "<p>The reason for the exception.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The type of resource that caused the exception.</p>"}, "ResourceId": {"shape": "String", "documentation": "<p>The unique identifier of the request.</p>"}}, "documentation": "<p>Your request has conflicting operations. This can occur if you're trying to perform more than one operation on the same resource at the same time or it could be that the requested action isn't valid for the current state or configuration of the resource.</p>", "exception": true}, "ConflictExceptionReason": {"type": "string", "enum": ["DELETION_PROTECTION_ENABLED", "DESTINATION_PHONE_NUMBER_NOT_VERIFIED", "DESTINATION_PHONE_NUMBER_OPTED_OUT", "EVENT_DESTINATION_MISMATCH", "KEYWORD_MISMATCH", "LAST_PHONE_NUMBER", "SELF_MANAGED_OPT_OUTS_MISMATCH", "MESSAGE_TYPE_MISMATCH", "NO_ORIGINATION_IDENTITIES_FOUND", "OPT_OUT_LIST_MISMATCH", "PHONE_NUMBER_ASSOCIATED_TO_POOL", "PHONE_NUMBER_NOT_ASSOCIATED_TO_POOL", "PHONE_NUMBER_NOT_IN_REGISTRATION_REGION", "RESOURCE_ALREADY_EXISTS", "RESOURCE_DELETION_NOT_ALLOWED", "RESOURCE_MODIFICATION_NOT_ALLOWED", "RESOURCE_NOT_ACTIVE", "RESOURCE_NOT_EMPTY", "TWO_WAY_CONFIG_MISMATCH"]}, "ContextKey": {"type": "string", "max": 100, "min": 1, "pattern": "\\S+"}, "ContextMap": {"type": "map", "key": {"shape": "Context<PERSON>ey"}, "value": {"shape": "ContextValue"}, "max": 5, "min": 0}, "ContextValue": {"type": "string", "max": 800, "min": 1, "pattern": "\\S+"}, "CreateConfigurationSetRequest": {"type": "structure", "required": ["ConfigurationSetName"], "members": {"ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name to use for the new configuration set.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>An array of key and value pair tags that's associated with the new configuration set. </p>"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don't specify a client token, a randomly generated token is used for the request to ensure idempotency.</p>", "idempotencyToken": true}}}, "CreateConfigurationSetResult": {"type": "structure", "members": {"ConfigurationSetArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the newly created configuration set.</p>"}, "ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the new configuration set.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>An array of key and value pair tags that's associated with the configuration set.</p>"}, "CreatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The time when the configuration set was created, in <a href=\"https://www.epochconverter.com/\">UNIX epoch time</a> format.</p>"}}}, "CreateEventDestinationRequest": {"type": "structure", "required": ["ConfigurationSetName", "EventDestinationName", "MatchingEventTypes"], "members": {"ConfigurationSetName": {"shape": "ConfigurationSetNameOrArn", "documentation": "<p>Either the name of the configuration set or the configuration set ARN to apply event logging to. The ConfigurateSetName and ConfigurationSetArn can be found using the <a>DescribeConfigurationSets</a> action.</p>"}, "EventDestinationName": {"shape": "EventDestinationName", "documentation": "<p>The name that identifies the event destination.</p>"}, "MatchingEventTypes": {"shape": "EventTypeList", "documentation": "<p>An array of event types that determine which events to log. If \"ALL\" is used, then Amazon Pinpoint logs every event type.</p>"}, "CloudWatchLogsDestination": {"shape": "CloudWatchLogsDestination", "documentation": "<p>An object that contains information about an event destination for logging to Amazon CloudWatch logs.</p>"}, "KinesisFirehoseDestination": {"shape": "KinesisFirehoseDestination", "documentation": "<p>An object that contains information about an event destination for logging to Amazon Kinesis Data Firehose.</p>"}, "SnsDestination": {"shape": "SnsDestination", "documentation": "<p>An object that contains information about an event destination for logging to Amazon SNS.</p>"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don't specify a client token, a randomly generated token is used for the request to ensure idempotency.</p>", "idempotencyToken": true}}}, "CreateEventDestinationResult": {"type": "structure", "members": {"ConfigurationSetArn": {"shape": "String", "documentation": "<p>The ARN of the configuration set.</p>"}, "ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the configuration set.</p>"}, "EventDestination": {"shape": "EventDestination", "documentation": "<p>The details of the destination where events are logged.</p>"}}}, "CreateOptOutListRequest": {"type": "structure", "required": ["OptOutListName"], "members": {"OptOutListName": {"shape": "OptOutListName", "documentation": "<p>The name of the new OptOutList.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>An array of tags (key and value pairs) to associate with the new OptOutList.</p>"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don't specify a client token, a randomly generated token is used for the request to ensure idempotency.</p>", "idempotencyToken": true}}}, "CreateOptOutListResult": {"type": "structure", "members": {"OptOutListArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) for the OptOutList.</p>"}, "OptOutListName": {"shape": "OptOutListName", "documentation": "<p>The name of the new OptOutList.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>An array of tags (key and value pairs) associated with the new OptOutList.</p>"}, "CreatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The time when the pool was created, in <a href=\"https://www.epochconverter.com/\">UNIX epoch time</a> format.</p>"}}}, "CreatePoolRequest": {"type": "structure", "required": ["OriginationIdentity", "IsoCountryCode", "MessageType"], "members": {"OriginationIdentity": {"shape": "PhoneOrSenderIdOrArn", "documentation": "<p>The origination identity to use such as a PhoneNumberId, PhoneNumberArn, SenderId or SenderIdArn. You can use <a>DescribePhoneNumbers</a> to find the values for PhoneNumberId and PhoneNumberArn while <a>DescribeSenderIds</a> can be used to get the values for SenderId and SenderIdArn.</p>"}, "IsoCountryCode": {"shape": "IsoCountryCode", "documentation": "<p>The new two-character code, in ISO 3166-1 alpha-2 format, for the country or region of the new pool.</p>"}, "MessageType": {"shape": "MessageType", "documentation": "<p>The type of message. Valid values are TRANSACTIONAL for messages that are critical or time-sensitive and PROMOTIONAL for messages that aren't critical or time-sensitive.</p>"}, "DeletionProtectionEnabled": {"shape": "Boolean", "documentation": "<p>By default this is set to false. When set to true the pool can't be deleted. You can change this value using the <a>UpdatePool</a> action.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>An array of tags (key and value pairs) associated with the pool.</p>"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don't specify a client token, a randomly generated token is used for the request to ensure idempotency.</p>", "idempotencyToken": true}}}, "CreatePoolResult": {"type": "structure", "members": {"PoolArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) for the pool.</p>"}, "PoolId": {"shape": "String", "documentation": "<p>The unique identifier for the pool.</p>"}, "Status": {"shape": "PoolStatus", "documentation": "<p>The current status of the pool.</p> <ul> <li> <p>CREATING: The pool is currently being created and isn't yet available for use.</p> </li> <li> <p>ACTIVE: The pool is active and available for use.</p> </li> <li> <p>DELETING: The pool is being deleted.</p> </li> </ul>"}, "MessageType": {"shape": "MessageType", "documentation": "<p>The type of message for the pool to use.</p>"}, "TwoWayEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>By default this is set to false. When set to true you can receive incoming text messages from your end recipients.</p>"}, "TwoWayChannelArn": {"shape": "TwoWayChannelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the two way channel.</p>"}, "SelfManagedOptOutsEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>By default this is set to false. When an end recipient sends a message that begins with HELP or STOP to one of your dedicated numbers, Amazon Pinpoint automatically replies with a customizable message and adds the end recipient to the OptOutList. When set to true you're responsible for responding to HELP and STOP requests. You're also responsible for tracking and honoring opt-out requests.</p>"}, "OptOutListName": {"shape": "OptOutListName", "documentation": "<p>The name of the OptOutList associated with the pool.</p>"}, "SharedRoutesEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>Indicates whether shared routes are enabled for the pool.</p>"}, "DeletionProtectionEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>When set to true deletion protection is enabled. By default this is set to false. </p>"}, "Tags": {"shape": "TagList", "documentation": "<p>An array of tags (key and value pairs) associated with the pool.</p>"}, "CreatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The time when the pool was created, in <a href=\"https://www.epochconverter.com/\">UNIX epoch time</a> format.</p>"}}}, "DeleteConfigurationSetRequest": {"type": "structure", "required": ["ConfigurationSetName"], "members": {"ConfigurationSetName": {"shape": "ConfigurationSetNameOrArn", "documentation": "<p>The name of the configuration set or the configuration set ARN that you want to delete. The ConfigurationSetName and ConfigurationSetArn can be found using the <a>DescribeConfigurationSets</a> action.</p>"}}}, "DeleteConfigurationSetResult": {"type": "structure", "members": {"ConfigurationSetArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the deleted configuration set.</p>"}, "ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the deleted configuration set.</p>"}, "EventDestinations": {"shape": "EventDestinationList", "documentation": "<p>An array of any EventDestination objects that were associated with the deleted configuration set.</p>"}, "DefaultMessageType": {"shape": "MessageType", "documentation": "<p>The default message type of the configuration set that was deleted.</p>"}, "DefaultSenderId": {"shape": "SenderId", "documentation": "<p>The default Sender ID of the configuration set that was deleted.</p>"}, "CreatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The time that the deleted configuration set was created in <a href=\"https://www.epochconverter.com/\">UNIX epoch time</a> format.</p>"}}}, "DeleteDefaultMessageTypeRequest": {"type": "structure", "required": ["ConfigurationSetName"], "members": {"ConfigurationSetName": {"shape": "ConfigurationSetNameOrArn", "documentation": "<p>The name of the configuration set or the configuration set Amazon Resource Name (ARN) to delete the default message type from. The ConfigurationSetName and ConfigurationSetArn can be found using the <a>DescribeConfigurationSets</a> action.</p>"}}}, "DeleteDefaultMessageTypeResult": {"type": "structure", "members": {"ConfigurationSetArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the configuration set.</p>"}, "ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the configuration set.</p>"}, "MessageType": {"shape": "MessageType", "documentation": "<p>The current message type for the configuration set.</p>"}}}, "DeleteDefaultSenderIdRequest": {"type": "structure", "required": ["ConfigurationSetName"], "members": {"ConfigurationSetName": {"shape": "ConfigurationSetNameOrArn", "documentation": "<p>The name of the configuration set or the configuration set Amazon Resource Name (ARN) to delete the default sender ID from. The ConfigurationSetName and ConfigurationSetArn can be found using the <a>DescribeConfigurationSets</a> action.</p>"}}}, "DeleteDefaultSenderIdResult": {"type": "structure", "members": {"ConfigurationSetArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the configuration set.</p>"}, "ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the configuration set.</p>"}, "SenderId": {"shape": "SenderId", "documentation": "<p>The current sender ID for the configuration set.</p>"}}}, "DeleteEventDestinationRequest": {"type": "structure", "required": ["ConfigurationSetName", "EventDestinationName"], "members": {"ConfigurationSetName": {"shape": "ConfigurationSetNameOrArn", "documentation": "<p>The name of the configuration set or the configuration set's Amazon Resource Name (ARN) to remove the event destination from. The ConfigurateSetName and ConfigurationSetArn can be found using the <a>DescribeConfigurationSets</a> action.</p>"}, "EventDestinationName": {"shape": "EventDestinationName", "documentation": "<p>The name of the event destination to delete.</p>"}}}, "DeleteEventDestinationResult": {"type": "structure", "members": {"ConfigurationSetArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the configuration set.</p>"}, "ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the configuration set the event destination was deleted from.</p>"}, "EventDestination": {"shape": "EventDestination", "documentation": "<p>The event destination object that was deleted.</p>"}}}, "DeleteKeywordRequest": {"type": "structure", "required": ["OriginationIdentity", "Keyword"], "members": {"OriginationIdentity": {"shape": "PhoneOrPoolIdOrArn", "documentation": "<p>The origination identity to use such as a PhoneNumberId, PhoneNumberArn, PoolId or PoolArn. You can use <a>DescribePhoneNumbers</a> to find the values for PhoneNumberId and PhoneNumberArn and <a>DescribePools</a> to find the values of PoolId and PoolArn.</p>"}, "Keyword": {"shape": "Keyword", "documentation": "<p>The keyword to delete.</p>"}}}, "DeleteKeywordResult": {"type": "structure", "members": {"OriginationIdentityArn": {"shape": "String", "documentation": "<p>The PhoneNumberArn or PoolArn that the keyword was associated with.</p>"}, "OriginationIdentity": {"shape": "String", "documentation": "<p>The PhoneNumberId or PoolId that the keyword was associated with.</p>"}, "Keyword": {"shape": "Keyword", "documentation": "<p>The keyword that was deleted.</p>"}, "KeywordMessage": {"shape": "KeywordMessage", "documentation": "<p>The message that was associated with the deleted keyword.</p>"}, "KeywordAction": {"shape": "KeywordAction", "documentation": "<p>The action that was associated with the deleted keyword.</p>"}}}, "DeleteOptOutListRequest": {"type": "structure", "required": ["OptOutListName"], "members": {"OptOutListName": {"shape": "OptOutListNameOrArn", "documentation": "<p>The OptOutListName or OptOutListArn of the OptOutList to delete. You can use <a>DescribeOptOutLists</a> to find the values for OptOutListName and OptOutListArn.</p>"}}}, "DeleteOptOutListResult": {"type": "structure", "members": {"OptOutListArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the OptOutList that was removed.</p>"}, "OptOutListName": {"shape": "OptOutListName", "documentation": "<p>The name of the OptOutList that was removed.</p>"}, "CreatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The time when the OptOutList was created, in <a href=\"https://www.epochconverter.com/\">UNIX epoch time</a> format.</p>"}}}, "DeleteOptedOutNumberRequest": {"type": "structure", "required": ["OptOutListName", "OptedOutNumber"], "members": {"OptOutListName": {"shape": "OptOutListNameOrArn", "documentation": "<p>The OptOutListName or OptOutListArn to remove the phone number from.</p>"}, "OptedOutNumber": {"shape": "PhoneNumber", "documentation": "<p>The phone number, in E.164 format, to remove from the OptOutList.</p>"}}}, "DeleteOptedOutNumberResult": {"type": "structure", "members": {"OptOutListArn": {"shape": "String", "documentation": "<p>The OptOutListArn that the phone number was removed from.</p>"}, "OptOutListName": {"shape": "OptOutListName", "documentation": "<p>The OptOutListName that the phone number was removed from.</p>"}, "OptedOutNumber": {"shape": "PhoneNumber", "documentation": "<p>The phone number that was removed from the OptOutList.</p>"}, "OptedOutTimestamp": {"shape": "Timestamp", "documentation": "<p>The time that the number was removed at, in <a href=\"https://www.epochconverter.com/\">UNIX epoch time</a> format.</p>"}, "EndUserOptedOut": {"shape": "PrimitiveBoolean", "documentation": "<p>This is true if it was the end user who requested their phone number be removed. </p>"}}}, "DeletePoolRequest": {"type": "structure", "required": ["PoolId"], "members": {"PoolId": {"shape": "PoolIdOrArn", "documentation": "<p>The PoolId or PoolArn of the pool to delete. You can use <a>DescribePools</a> to find the values for PoolId and PoolArn .</p>"}}}, "DeletePoolResult": {"type": "structure", "members": {"PoolArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the pool that was deleted.</p>"}, "PoolId": {"shape": "String", "documentation": "<p>The PoolId of the pool that was deleted.</p>"}, "Status": {"shape": "PoolStatus", "documentation": "<p>The current status of the pool.</p> <ul> <li> <p>CREATING: The pool is currently being created and isn't yet available for use.</p> </li> <li> <p>ACTIVE: The pool is active and available for use.</p> </li> <li> <p>DELETING: The pool is being deleted.</p> </li> </ul>"}, "MessageType": {"shape": "MessageType", "documentation": "<p>The message type that was associated with the deleted pool.</p>"}, "TwoWayEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>By default this is set to false. When set to true you can receive incoming text messages from your end recipients.</p>"}, "TwoWayChannelArn": {"shape": "TwoWayChannelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the TwoWayChannel.</p>"}, "SelfManagedOptOutsEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>By default this is set to false. When an end recipient sends a message that begins with HELP or STOP to one of your dedicated numbers, Amazon Pinpoint automatically replies with a customizable message and adds the end recipient to the OptOutList. When set to true you're responsible for responding to HELP and STOP requests. You're also responsible for tracking and honoring opt-out requests.</p>"}, "OptOutListName": {"shape": "OptOutListName", "documentation": "<p>The name of the OptOutList that was associated with the deleted pool.</p>"}, "SharedRoutesEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>Indicates whether shared routes are enabled for the pool.</p>"}, "CreatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The time when the pool was created, in <a href=\"https://www.epochconverter.com/\">UNIX epoch time</a> format.</p>"}}}, "DeleteTextMessageSpendLimitOverrideRequest": {"type": "structure", "members": {}}, "DeleteTextMessageSpendLimitOverrideResult": {"type": "structure", "members": {"MonthlyLimit": {"shape": "MonthlyLimit", "documentation": "<p>The current monthly limit, in US dollars.</p>"}}}, "DeleteVoiceMessageSpendLimitOverrideRequest": {"type": "structure", "members": {}}, "DeleteVoiceMessageSpendLimitOverrideResult": {"type": "structure", "members": {"MonthlyLimit": {"shape": "MonthlyLimit", "documentation": "<p>The current monthly limit, in US dollars.</p>"}}}, "DeliveryStreamArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:\\S+"}, "DescribeAccountAttributesRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results. You don't need to supply a value for this field in the initial request.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return per each request.</p>"}}}, "DescribeAccountAttributesResult": {"type": "structure", "members": {"AccountAttributes": {"shape": "AccountAttributeList", "documentation": "<p>An array of AccountAttributes objects.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results. If this field is empty then there are no more results.</p>"}}}, "DescribeAccountLimitsRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results. You don't need to supply a value for this field in the initial request.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return per each request.</p>"}}}, "DescribeAccountLimitsResult": {"type": "structure", "members": {"AccountLimits": {"shape": "AccountLimitList", "documentation": "<p>An array of AccountLimit objects that show the current spend limits.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results. If this field is empty then there are no more results.</p>"}}}, "DescribeConfigurationSetsRequest": {"type": "structure", "members": {"ConfigurationSetNames": {"shape": "ConfigurationSetNameList", "documentation": "<p>An array of strings. Each element can be either a ConfigurationSetName or ConfigurationSetArn.</p>"}, "Filters": {"shape": "ConfigurationSetFilterList", "documentation": "<p>An array of filters to apply to the results that are returned.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results. You don't need to supply a value for this field in the initial request.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return per each request.</p>"}}}, "DescribeConfigurationSetsResult": {"type": "structure", "members": {"ConfigurationSets": {"shape": "ConfigurationSetInformationList", "documentation": "<p>An array of ConfigurationSets objects.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results. If this field is empty then there are no more results.</p>"}}}, "DescribeKeywordsRequest": {"type": "structure", "required": ["OriginationIdentity"], "members": {"OriginationIdentity": {"shape": "PhoneOrPoolIdOrArn", "documentation": "<p>The origination identity to use such as a PhoneNumberId, PhoneNumberArn, SenderId or SenderIdArn. You can use <a>DescribePhoneNumbers</a> to find the values for PhoneNumberId and PhoneNumberArn while <a>DescribeSenderIds</a> can be used to get the values for SenderId and SenderIdArn.</p>"}, "Keywords": {"shape": "KeywordList", "documentation": "<p>An array of keywords to search for.</p>"}, "Filters": {"shape": "KeywordFilterList", "documentation": "<p>An array of keyword filters to filter the results.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results. You don't need to supply a value for this field in the initial request.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return per each request.</p>"}}}, "DescribeKeywordsResult": {"type": "structure", "members": {"OriginationIdentityArn": {"shape": "String", "documentation": "<p>The PhoneNumberArn or PoolArn that is associated with the OriginationIdentity. </p>"}, "OriginationIdentity": {"shape": "String", "documentation": "<p>The PhoneNumberId or PoolId that is associated with the OriginationIdentity.</p>"}, "Keywords": {"shape": "KeywordInformationList", "documentation": "<p>An array of KeywordInformation objects that contain the results.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results. If this field is empty then there are no more results.</p>"}}}, "DescribeOptOutListsRequest": {"type": "structure", "members": {"OptOutListNames": {"shape": "OptOutListNameList", "documentation": "<p>The OptOutLists to show the details of. This is an array of strings that can be either the OptOutListName or OptOutListArn.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results. You don't need to supply a value for this field in the initial request.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return per each request.</p>"}}}, "DescribeOptOutListsResult": {"type": "structure", "members": {"OptOutLists": {"shape": "OptOutListInformationList", "documentation": "<p>An array of OptOutListInformation objects that contain the details for the requested OptOutLists.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results. If this field is empty then there are no more results.</p>"}}}, "DescribeOptedOutNumbersRequest": {"type": "structure", "required": ["OptOutListName"], "members": {"OptOutListName": {"shape": "OptOutListNameOrArn", "documentation": "<p>The OptOutListName or OptOutListArn of the OptOutList. You can use <a>DescribeOptOutLists</a> to find the values for OptOutListName and OptOutListArn.</p>"}, "OptedOutNumbers": {"shape": "OptedOutNumberList", "documentation": "<p>An array of phone numbers to search for in the OptOutList.</p>"}, "Filters": {"shape": "OptedOutFilterList", "documentation": "<p>An array of OptedOutFilter objects to filter the results on.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results. You don't need to supply a value for this field in the initial request.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return per each request.</p>"}}}, "DescribeOptedOutNumbersResult": {"type": "structure", "members": {"OptOutListArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the OptOutList.</p>"}, "OptOutListName": {"shape": "OptOutListName", "documentation": "<p>The name of the OptOutList.</p>"}, "OptedOutNumbers": {"shape": "OptedOutNumberInformationList", "documentation": "<p>An array of OptedOutNumbersInformation objects that provide information about the requested OptedOutNumbers.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results. If this field is empty then there are no more results.</p>"}}}, "DescribePhoneNumbersRequest": {"type": "structure", "members": {"PhoneNumberIds": {"shape": "PhoneNumberIdList", "documentation": "<p>The unique identifier of phone numbers to find information about. This is an array of strings that can be either the PhoneNumberId or PhoneNumberArn.</p>"}, "Filters": {"shape": "PhoneNumberFilterList", "documentation": "<p>An array of PhoneNumberFilter objects to filter the results.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results. You don't need to supply a value for this field in the initial request.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return per each request.</p>"}}}, "DescribePhoneNumbersResult": {"type": "structure", "members": {"PhoneNumbers": {"shape": "PhoneNumberInformationList", "documentation": "<p>An array of PhoneNumberInformation objects that contain the details for the requested phone numbers.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results. If this field is empty then there are no more results.</p>"}}}, "DescribePoolsRequest": {"type": "structure", "members": {"PoolIds": {"shape": "PoolIdList", "documentation": "<p>The unique identifier of pools to find. This is an array of strings that can be either the PoolId or PoolArn.</p>"}, "Filters": {"shape": "PoolFilterList", "documentation": "<p>An array of PoolFilter objects to filter the results.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results. You don't need to supply a value for this field in the initial request.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return per each request.</p>"}}}, "DescribePoolsResult": {"type": "structure", "members": {"Pools": {"shape": "PoolInformationList", "documentation": "<p>An array of PoolInformation objects that contain the details for the requested pools. </p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results. If this field is empty then there are no more results.</p>"}}}, "DescribeSenderIdsRequest": {"type": "structure", "members": {"SenderIds": {"shape": "SenderIdList", "documentation": "<p>An array of SenderIdAndCountry objects to search for.</p>"}, "Filters": {"shape": "SenderIdFilterList", "documentation": "<p>An array of SenderIdFilter objects to filter the results.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results. You don't need to supply a value for this field in the initial request.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return per each request.</p>"}}}, "DescribeSenderIdsResult": {"type": "structure", "members": {"SenderIds": {"shape": "SenderIdInformationList", "documentation": "<p>An array of SernderIdInformation objects that contain the details for the requested SenderIds.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results. If this field is empty then there are no more results.</p>"}}}, "DescribeSpendLimitsRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results. You don't need to supply a value for this field in the initial request.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return per each request.</p>"}}}, "DescribeSpendLimitsResult": {"type": "structure", "members": {"SpendLimits": {"shape": "SpendLimitList", "documentation": "<p>An array of SpendLimit objects that contain the details for the requested spend limits.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results. If this field is empty then there are no more results.</p>"}}}, "DestinationCountryParameterKey": {"type": "string", "enum": ["IN_TEMPLATE_ID", "IN_ENTITY_ID"]}, "DestinationCountryParameterValue": {"type": "string", "max": 64, "min": 1, "pattern": "\\S+"}, "DestinationCountryParameters": {"type": "map", "key": {"shape": "DestinationCountryParameterKey"}, "value": {"shape": "DestinationCountryParameterValue"}, "max": 10, "min": 0}, "DisassociateOriginationIdentityRequest": {"type": "structure", "required": ["PoolId", "OriginationIdentity", "IsoCountryCode"], "members": {"PoolId": {"shape": "PoolIdOrArn", "documentation": "<p>The unique identifier for the pool to disassociate with the origination identity. This value can be either the PoolId or PoolArn.</p>"}, "OriginationIdentity": {"shape": "PhoneOrSenderIdOrArn", "documentation": "<p>The origination identity to use such as a PhoneNumberId, PhoneNumberArn, SenderId or SenderIdArn. You can use <a>DescribePhoneNumbers</a> find the values for PhoneNumberId and PhoneNumberArn, or use <a>DescribeSenderIds</a> to get the values for SenderId and SenderIdArn.</p>"}, "IsoCountryCode": {"shape": "IsoCountryCode", "documentation": "<p>The two-character code, in ISO 3166-1 alpha-2 format, for the country or region. </p>"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier you provide to ensure the idempotency of the request. If you don't specify a client token, a randomly generated token is used for the request to ensure idempotency.</p>", "idempotencyToken": true}}}, "DisassociateOriginationIdentityResult": {"type": "structure", "members": {"PoolArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the pool.</p>"}, "PoolId": {"shape": "String", "documentation": "<p>The PoolId of the pool no longer associated with the origination identity.</p>"}, "OriginationIdentityArn": {"shape": "String", "documentation": "<p>The PhoneNumberArn or SenderIdArn of the origination identity.</p>"}, "OriginationIdentity": {"shape": "String", "documentation": "<p>The PhoneNumberId or SenderId of the origination identity.</p>"}, "IsoCountryCode": {"shape": "IsoCountryCode", "documentation": "<p>The two-character code, in ISO 3166-1 alpha-2 format, for the country or region.</p>"}}}, "EventDestination": {"type": "structure", "required": ["EventDestinationName", "Enabled", "MatchingEventTypes"], "members": {"EventDestinationName": {"shape": "EventDestinationName", "documentation": "<p>The name of the EventDestination.</p>"}, "Enabled": {"shape": "Boolean", "documentation": "<p>When set to true events will be logged.</p>"}, "MatchingEventTypes": {"shape": "EventTypeList", "documentation": "<p>An array of event types that determine which events to log.</p>"}, "CloudWatchLogsDestination": {"shape": "CloudWatchLogsDestination", "documentation": "<p>An object that contains information about an event destination that sends logging events to Amazon CloudWatch logs.</p>"}, "KinesisFirehoseDestination": {"shape": "KinesisFirehoseDestination", "documentation": "<p>An object that contains information about an event destination for logging to Amazon Kinesis Data Firehose.</p>"}, "SnsDestination": {"shape": "SnsDestination", "documentation": "<p>An object that contains information about an event destination that sends logging events to Amazon SNS.</p>"}}, "documentation": "<p>Contains information about an event destination.</p> <p>Event destinations are associated with configuration sets, which enable you to publish message sending events to Amazon CloudWatch, Amazon Kinesis Data Firehose, or Amazon SNS.</p>"}, "EventDestinationList": {"type": "list", "member": {"shape": "EventDestination"}}, "EventDestinationName": {"type": "string", "max": 64, "min": 1, "pattern": "[A-Za-z0-9_-]+"}, "EventType": {"type": "string", "enum": ["ALL", "TEXT_ALL", "TEXT_SENT", "TEXT_PENDING", "TEXT_QUEUED", "TEXT_SUCCESSFUL", "TEXT_DELIVERED", "TEXT_INVALID", "TEXT_INVALID_MESSAGE", "TEXT_UNREACHABLE", "TEXT_CARRIER_UNREACHABLE", "TEXT_BLOCKED", "TEXT_CARRIER_BLOCKED", "TEXT_SPAM", "TEXT_UNKNOWN", "TEXT_TTL_EXPIRED", "VOICE_ALL", "VOICE_INITIATED", "VOICE_RINGING", "VOICE_ANSWERED", "VOICE_COMPLETED", "VOICE_BUSY", "VOICE_NO_ANSWER", "VOICE_FAILED", "VOICE_TTL_EXPIRED"]}, "EventTypeList": {"type": "list", "member": {"shape": "EventType"}, "max": 25, "min": 1}, "FilterValue": {"type": "string", "max": 100, "min": 1, "pattern": "[A-Za-z0-9_-]+"}, "FilterValueList": {"type": "list", "member": {"shape": "FilterValue"}, "max": 20, "min": 1}, "IamRoleArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:\\S+"}, "InternalServerException": {"type": "structure", "members": {"Message": {"shape": "String"}, "RequestId": {"shape": "String", "documentation": "<p>The unique identifier of the request.</p>"}}, "documentation": "<p>The API encountered an unexpected error and couldn't complete the request. You might be able to successfully issue the request again in the future.</p>", "exception": true, "fault": true, "retryable": {"throttling": false}}, "IsoCountryCode": {"type": "string", "max": 2, "min": 2, "pattern": "[A-Z]{2}"}, "Keyword": {"type": "string", "max": 30, "min": 1, "pattern": "[ \\S]+"}, "KeywordAction": {"type": "string", "enum": ["AUTOMATIC_RESPONSE", "OPT_OUT", "OPT_IN"]}, "KeywordFilter": {"type": "structure", "required": ["Name", "Values"], "members": {"Name": {"shape": "KeywordFilterName", "documentation": "<p>The name of the attribute to filter on.</p>"}, "Values": {"shape": "FilterValueList", "documentation": "<p>An array values to filter for.</p>"}}, "documentation": "<p>The information for keywords that meet a specified criteria.</p>"}, "KeywordFilterList": {"type": "list", "member": {"shape": "KeywordFilter"}, "max": 20, "min": 0}, "KeywordFilterName": {"type": "string", "enum": ["keyword-action"]}, "KeywordInformation": {"type": "structure", "required": ["Keyword", "KeywordMessage", "KeywordAction"], "members": {"Keyword": {"shape": "Keyword", "documentation": "<p>The keyword as a string.</p>"}, "KeywordMessage": {"shape": "KeywordMessage", "documentation": "<p>A custom message that can be used with the keyword.</p>"}, "KeywordAction": {"shape": "KeywordAction", "documentation": "<p>The action to perform for the keyword.</p>"}}, "documentation": "<p>The information for all keywords in a pool.</p>"}, "KeywordInformationList": {"type": "list", "member": {"shape": "KeywordInformation"}}, "KeywordList": {"type": "list", "member": {"shape": "Keyword"}, "max": 5, "min": 0}, "KeywordMessage": {"type": "string", "max": 1600, "min": 1, "pattern": "(?!\\s*$)[\\s\\S]+"}, "KinesisFirehoseDestination": {"type": "structure", "required": ["IamRoleArn", "DeliveryStreamArn"], "members": {"IamRoleArn": {"shape": "IamRoleArn", "documentation": "<p>The ARN of an Amazon Identity and Access Management (IAM) role that is able to write event data to an Amazon Firehose destination.</p>"}, "DeliveryStreamArn": {"shape": "DeliveryStreamArn", "documentation": "<p>The Amazon Resource Name (ARN) of the delivery stream.</p>"}}, "documentation": "<p>Contains the delivery stream Amazon Resource Name (ARN), and the ARN of the Identity and Access Management (IAM) role associated with an Kinesis Data Firehose event destination.</p> <p>Event destinations, such as Kinesis Data Firehose, are associated with configuration sets, which enable you to publish message sending events.</p>"}, "ListPoolOriginationIdentitiesRequest": {"type": "structure", "required": ["PoolId"], "members": {"PoolId": {"shape": "PoolIdOrArn", "documentation": "<p>The unique identifier for the pool. This value can be either the PoolId or PoolArn.</p>"}, "Filters": {"shape": "PoolOriginationIdentitiesFilterList", "documentation": "<p>An array of PoolOriginationIdentitiesFilter objects to filter the results..</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results. You don't need to supply a value for this field in the initial request.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return per each request.</p>"}}}, "ListPoolOriginationIdentitiesResult": {"type": "structure", "members": {"PoolArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) for the pool.</p>"}, "PoolId": {"shape": "String", "documentation": "<p>The unique PoolId of the pool.</p>"}, "OriginationIdentities": {"shape": "OriginationIdentityMetadataList", "documentation": "<p>An array of any OriginationIdentityMetadata objects.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to be used for the next set of paginated results. If this field is empty then there are no more results.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) of the resource to query for.</p>"}}}, "ListTagsForResourceResult": {"type": "structure", "members": {"ResourceArn": {"shape": "AmazonResourceName", "documentation": "<p>The ARN of the resource.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>An array of key and value pair tags that are associated with the resource.</p>"}}}, "LogGroupArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:\\S+"}, "MaxPrice": {"type": "string", "max": 8, "min": 2, "pattern": "[0-9]{0,2}\\.[0-9]{1,5}"}, "MaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "MessageType": {"type": "string", "enum": ["TRANSACTIONAL", "PROMOTIONAL"]}, "MessageTypeList": {"type": "list", "member": {"shape": "MessageType"}}, "MonthlyLimit": {"type": "long", "box": true, "max": 1000000000, "min": 0}, "NextToken": {"type": "string", "max": 1024, "min": 1, "pattern": ".+"}, "NonEmptyTagList": {"type": "list", "member": {"shape": "Tag"}, "max": 200, "min": 1}, "NumberCapability": {"type": "string", "enum": ["SMS", "VOICE"]}, "NumberCapabilityList": {"type": "list", "member": {"shape": "NumberCapability"}, "max": 2, "min": 1}, "NumberStatus": {"type": "string", "enum": ["PENDING", "ACTIVE", "ASSOCIATING", "DISASSOCIATING", "DELETED"]}, "NumberType": {"type": "string", "enum": ["SHORT_CODE", "LONG_CODE", "TOLL_FREE", "TEN_DLC"]}, "OptOutListInformation": {"type": "structure", "required": ["OptOutListArn", "OptOutListName", "CreatedTimestamp"], "members": {"OptOutListArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the OptOutList.</p>"}, "OptOutListName": {"shape": "OptOutListName", "documentation": "<p>The name of the OptOutList.</p>"}, "CreatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The time when the OutOutList was created, in <a href=\"https://www.epochconverter.com/\">UNIX epoch time</a> format.</p>"}}, "documentation": "<p>The information for all OptOutList in an Amazon Web Services account.</p>"}, "OptOutListInformationList": {"type": "list", "member": {"shape": "OptOutListInformation"}}, "OptOutListName": {"type": "string", "max": 64, "min": 1, "pattern": "[A-Za-z0-9_-]+"}, "OptOutListNameList": {"type": "list", "member": {"shape": "OptOutListNameOrArn"}, "max": 5, "min": 0}, "OptOutListNameOrArn": {"type": "string", "max": 256, "min": 1, "pattern": "[A-Za-z0-9_:/-]+"}, "OptedOutFilter": {"type": "structure", "required": ["Name", "Values"], "members": {"Name": {"shape": "OptedOutFilterName", "documentation": "<p>The name of the attribute to filter on.</p>"}, "Values": {"shape": "FilterValueList", "documentation": "<p>An array of values to filter for.</p>"}}, "documentation": "<p>The information for opted out numbers that meet a specified criteria.</p>"}, "OptedOutFilterList": {"type": "list", "member": {"shape": "OptedOutFilter"}, "max": 20, "min": 0}, "OptedOutFilterName": {"type": "string", "enum": ["end-user-opted-out"]}, "OptedOutNumberInformation": {"type": "structure", "required": ["OptedOutNumber", "OptedOutTimestamp", "EndUserOptedOut"], "members": {"OptedOutNumber": {"shape": "PhoneNumber", "documentation": "<p>The phone number that is opted out.</p>"}, "OptedOutTimestamp": {"shape": "Timestamp", "documentation": "<p>The time that the op tout occurred, in <a href=\"https://www.epochconverter.com/\">UNIX epoch time</a> format.</p>"}, "EndUserOptedOut": {"shape": "PrimitiveBoolean", "documentation": "<p>This is set to true if it was the end recipient that opted out.</p>"}}, "documentation": "<p>The information for an opted out number in an Amazon Web Services account.</p>"}, "OptedOutNumberInformationList": {"type": "list", "member": {"shape": "OptedOutNumberInformation"}}, "OptedOutNumberList": {"type": "list", "member": {"shape": "PhoneNumber"}, "max": 5, "min": 0}, "OriginationIdentityMetadata": {"type": "structure", "required": ["OriginationIdentityArn", "OriginationIdentity", "IsoCountryCode", "NumberCapabilities"], "members": {"OriginationIdentityArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) associated with the origination identity.</p>"}, "OriginationIdentity": {"shape": "String", "documentation": "<p>The unique identifier of the origination identity.</p>"}, "IsoCountryCode": {"shape": "IsoCountryCode", "documentation": "<p>The two-character code, in ISO 3166-1 alpha-2 format, for the country or region. </p>"}, "NumberCapabilities": {"shape": "NumberCapabilityList", "documentation": "<p>Describes if the origination identity can be used for text messages, voice calls or both.</p>"}}, "documentation": "<p>The metadata for an origination identity associated with a pool.</p>"}, "OriginationIdentityMetadataList": {"type": "list", "member": {"shape": "OriginationIdentityMetadata"}}, "PhoneNumber": {"type": "string", "max": 20, "min": 1, "pattern": "\\+?[1-9][0-9]{1,18}"}, "PhoneNumberFilter": {"type": "structure", "required": ["Name", "Values"], "members": {"Name": {"shape": "PhoneNumberFilterName", "documentation": "<p>The name of the attribute to filter on.</p>"}, "Values": {"shape": "FilterValueList", "documentation": "<p>An array values to filter for.</p>"}}, "documentation": "<p>The information for a phone number that meets a specified criteria.</p>"}, "PhoneNumberFilterList": {"type": "list", "member": {"shape": "PhoneNumberFilter"}, "max": 20, "min": 0}, "PhoneNumberFilterName": {"type": "string", "enum": ["status", "iso-country-code", "message-type", "number-capability", "number-type", "two-way-enabled", "self-managed-opt-outs-enabled", "opt-out-list-name", "deletion-protection-enabled"]}, "PhoneNumberIdList": {"type": "list", "member": {"shape": "PhoneNumberIdOrArn"}, "max": 5, "min": 0}, "PhoneNumberIdOrArn": {"type": "string", "max": 256, "min": 1, "pattern": "[A-Za-z0-9_:/-]+"}, "PhoneNumberInformation": {"type": "structure", "required": ["PhoneNumberArn", "PhoneNumber", "Status", "IsoCountryCode", "MessageType", "NumberCapabilities", "NumberType", "MonthlyLeasingPrice", "TwoWayEnabled", "SelfManagedOptOutsEnabled", "OptOutListName", "DeletionProtectionEnabled", "CreatedTimestamp"], "members": {"PhoneNumberArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) associated with the phone number.</p>"}, "PhoneNumberId": {"shape": "String", "documentation": "<p>The unique identifier for the phone number.</p>"}, "PhoneNumber": {"shape": "PhoneNumber", "documentation": "<p>The phone number in E.164 format.</p>"}, "Status": {"shape": "NumberStatus", "documentation": "<p>The current status of the phone number.</p>"}, "IsoCountryCode": {"shape": "IsoCountryCode", "documentation": "<p>The two-character code, in ISO 3166-1 alpha-2 format, for the country or region. </p>"}, "MessageType": {"shape": "MessageType", "documentation": "<p>The type of message. Valid values are TRANSACTIONAL for messages that are critical or time-sensitive and PROMOTIONAL for messages that aren't critical or time-sensitive.</p>"}, "NumberCapabilities": {"shape": "NumberCapabilityList", "documentation": "<p>Describes if the origination identity can be used for text messages, voice calls or both.</p>"}, "NumberType": {"shape": "NumberType", "documentation": "<p>The type of phone number.</p>"}, "MonthlyLeasingPrice": {"shape": "String", "documentation": "<p>The price, in US dollars, to lease the phone number.</p>"}, "TwoWayEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>By default this is set to false. When set to true you can receive incoming text messages from your end recipients using the TwoWayChannelArn.</p>"}, "TwoWayChannelArn": {"shape": "TwoWayChannelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the two way channel.</p>"}, "SelfManagedOptOutsEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>When set to false an end recipient sends a message that begins with HELP or STOP to one of your dedicated numbers, Amazon Pinpoint automatically replies with a customizable message and adds the end recipient to the OptOutList. When set to true you're responsible for responding to HELP and STOP requests. You're also responsible for tracking and honoring opt-out request. For more information see <a href=\"https://docs.aws.amazon.com/pinpoint/latest/userguide/settings-sms-managing.html#settings-account-sms-self-managed-opt-out\">Self-managed opt-outs</a> </p>"}, "OptOutListName": {"shape": "OptOutListName", "documentation": "<p>The name of the OptOutList associated with the phone number.</p>"}, "DeletionProtectionEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>When set to true the phone number can't be deleted.</p>"}, "PoolId": {"shape": "String", "documentation": "<p>The unique identifier of the pool associated with the phone number.</p>"}, "CreatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The time when the phone number was created, in <a href=\"https://www.epochconverter.com/\">UNIX epoch time</a> format.</p>"}}, "documentation": "<p>The information for a phone number in an Amazon Web Services account.</p>"}, "PhoneNumberInformationList": {"type": "list", "member": {"shape": "PhoneNumberInformation"}}, "PhoneOrPoolIdOrArn": {"type": "string", "max": 256, "min": 1, "pattern": "[A-Za-z0-9_:/-]+"}, "PhoneOrSenderIdOrArn": {"type": "string", "max": 256, "min": 1, "pattern": "[A-Za-z0-9_:/-]+"}, "PoolFilter": {"type": "structure", "required": ["Name", "Values"], "members": {"Name": {"shape": "PoolFilterName", "documentation": "<p>The name of the attribute to filter on.</p>"}, "Values": {"shape": "FilterValueList", "documentation": "<p>An array values to filter for.</p>"}}, "documentation": "<p>The information for a pool that meets a specified criteria.</p>"}, "PoolFilterList": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON>"}, "max": 20, "min": 0}, "PoolFilterName": {"type": "string", "enum": ["status", "message-type", "two-way-enabled", "self-managed-opt-outs-enabled", "opt-out-list-name", "shared-routes-enabled", "deletion-protection-enabled"]}, "PoolIdList": {"type": "list", "member": {"shape": "PoolIdOrArn"}, "max": 5, "min": 0}, "PoolIdOrArn": {"type": "string", "max": 256, "min": 1, "pattern": "[A-Za-z0-9_:/-]+"}, "PoolInformation": {"type": "structure", "required": ["PoolArn", "PoolId", "Status", "MessageType", "TwoWayEnabled", "SelfManagedOptOutsEnabled", "OptOutListName", "SharedRoutesEnabled", "DeletionProtectionEnabled", "CreatedTimestamp"], "members": {"PoolArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) for the pool.</p>"}, "PoolId": {"shape": "String", "documentation": "<p>The unique identifier for the pool.</p>"}, "Status": {"shape": "PoolStatus", "documentation": "<p>The current status of the pool.</p>"}, "MessageType": {"shape": "MessageType", "documentation": "<p>The type of message. Valid values are TRANSACTIONAL for messages that are critical or time-sensitive and PROMOTIONAL for messages that aren't critical or time-sensitive.</p>"}, "TwoWayEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>When set to true you can receive incoming text messages from your end recipients using the TwoWayChannelArn.</p>"}, "TwoWayChannelArn": {"shape": "TwoWayChannelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the two way channel.</p>"}, "SelfManagedOptOutsEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>When set to false, an end recipient sends a message that begins with HELP or STOP to one of your dedicated numbers, Amazon Pinpoint automatically replies with a customizable message and adds the end recipient to the OptOutList. When set to true you're responsible for responding to HELP and STOP requests. You're also responsible for tracking and honoring opt-out requests. For more information see <a href=\"https://docs.aws.amazon.com/pinpoint/latest/userguide/settings-sms-managing.html#settings-account-sms-self-managed-opt-out\">Self-managed opt-outs</a> </p>"}, "OptOutListName": {"shape": "OptOutListName", "documentation": "<p>The name of the OptOutList associated with the pool.</p>"}, "SharedRoutesEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>Allows you to enable shared routes on your pool.</p> <p>By default, this is set to <code>False</code>. If you set this value to <code>True</code>, your messages are sent using phone numbers or sender IDs (depending on the country) that are shared with other Amazon Pinpoint users. In some countries, such as the United States, senders aren't allowed to use shared routes and must use a dedicated phone number or short code.</p>"}, "DeletionProtectionEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>When set to true the pool can't be deleted.</p>"}, "CreatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The time when the pool was created, in <a href=\"https://www.epochconverter.com/\">UNIX epoch time</a> format.</p>"}}, "documentation": "<p>The information for a pool in an Amazon Web Services account.</p>"}, "PoolInformationList": {"type": "list", "member": {"shape": "PoolInformation"}}, "PoolOriginationIdentitiesFilter": {"type": "structure", "required": ["Name", "Values"], "members": {"Name": {"shape": "PoolOriginationIdentitiesFilterName", "documentation": "<p>The name of the attribute to filter on.</p>"}, "Values": {"shape": "FilterValueList", "documentation": "<p>An array values to filter for.</p>"}}, "documentation": "<p>Information about origination identities associated with a pool that meets a specified criteria.</p>"}, "PoolOriginationIdentitiesFilterList": {"type": "list", "member": {"shape": "PoolOriginationIdentitiesFilter"}, "max": 20, "min": 0}, "PoolOriginationIdentitiesFilterName": {"type": "string", "enum": ["iso-country-code", "number-capability"]}, "PoolStatus": {"type": "string", "enum": ["CREATING", "ACTIVE", "DELETING"]}, "PrimitiveBoolean": {"type": "boolean"}, "PrimitiveLong": {"type": "long"}, "PutKeywordRequest": {"type": "structure", "required": ["OriginationIdentity", "Keyword", "KeywordMessage"], "members": {"OriginationIdentity": {"shape": "PhoneOrPoolIdOrArn", "documentation": "<p>The origination identity to use such as a PhoneNumberId, PhoneNumberArn, SenderId or SenderIdArn. You can use <a>DescribePhoneNumbers</a> get the values for PhoneNumberId and PhoneNumberArn while <a>DescribeSenderIds</a> can be used to get the values for SenderId and SenderIdArn.</p>"}, "Keyword": {"shape": "Keyword", "documentation": "<p>The new keyword to add.</p>"}, "KeywordMessage": {"shape": "KeywordMessage", "documentation": "<p>The message associated with the keyword.</p> <ul> <li> <p>AUTOMATIC_RESPONSE: A message is sent to the recipient.</p> </li> <li> <p>OPT_OUT: Keeps the recipient from receiving future messages.</p> </li> <li> <p>OPT_IN: The recipient wants to receive future messages.</p> </li> </ul>"}, "KeywordAction": {"shape": "KeywordAction", "documentation": "<p>The action to perform for the new keyword when it is received.</p>"}}}, "PutKeywordResult": {"type": "structure", "members": {"OriginationIdentityArn": {"shape": "String", "documentation": "<p>The PhoneNumberArn or PoolArn that the keyword was associated with.</p>"}, "OriginationIdentity": {"shape": "String", "documentation": "<p>The PhoneNumberId or PoolId that the keyword was associated with.</p>"}, "Keyword": {"shape": "Keyword", "documentation": "<p>The keyword that was added.</p>"}, "KeywordMessage": {"shape": "KeywordMessage", "documentation": "<p>The message associated with the keyword.</p>"}, "KeywordAction": {"shape": "KeywordAction", "documentation": "<p>The action to perform when the keyword is used.</p>"}}}, "PutOptedOutNumberRequest": {"type": "structure", "required": ["OptOutListName", "OptedOutNumber"], "members": {"OptOutListName": {"shape": "OptOutListNameOrArn", "documentation": "<p>The OptOutListName or OptOutListArn to add the phone number to.</p>"}, "OptedOutNumber": {"shape": "PhoneNumber", "documentation": "<p>The phone number to add to the OptOutList in E.164 format.</p>"}}}, "PutOptedOutNumberResult": {"type": "structure", "members": {"OptOutListArn": {"shape": "String", "documentation": "<p>The OptOutListArn that the phone number was removed from.</p>"}, "OptOutListName": {"shape": "OptOutListName", "documentation": "<p>The OptOutListName that the phone number was removed from.</p>"}, "OptedOutNumber": {"shape": "PhoneNumber", "documentation": "<p>The phone number that was added to the OptOutList.</p>"}, "OptedOutTimestamp": {"shape": "Timestamp", "documentation": "<p>The time that the phone number was added to the OptOutList, in <a href=\"https://www.epochconverter.com/\">UNIX epoch time</a> format.</p>"}, "EndUserOptedOut": {"shape": "PrimitiveBoolean", "documentation": "<p>This is true if it was the end user who requested their phone number be removed. </p>"}}}, "RegistrationId": {"type": "string", "max": 64, "min": 1, "pattern": "\\S+"}, "ReleasePhoneNumberRequest": {"type": "structure", "required": ["PhoneNumberId"], "members": {"PhoneNumberId": {"shape": "PhoneNumberIdOrArn", "documentation": "<p>The PhoneNumberId or PhoneNumberArn of the phone number to release. You can use <a>DescribePhoneNumbers</a> to get the values for PhoneNumberId and PhoneNumberArn.</p>"}}}, "ReleasePhoneNumberResult": {"type": "structure", "members": {"PhoneNumberArn": {"shape": "String", "documentation": "<p>The PhoneNumberArn of the phone number that was released.</p>"}, "PhoneNumberId": {"shape": "String", "documentation": "<p>The PhoneNumberId of the phone number that was released.</p>"}, "PhoneNumber": {"shape": "PhoneNumber", "documentation": "<p>The phone number that was released.</p>"}, "Status": {"shape": "NumberStatus", "documentation": "<p>The current status of the request.</p>"}, "IsoCountryCode": {"shape": "IsoCountryCode", "documentation": "<p>The two-character code, in ISO 3166-1 alpha-2 format, for the country or region.</p>"}, "MessageType": {"shape": "MessageType", "documentation": "<p>The message type that was associated with the phone number.</p>"}, "NumberCapabilities": {"shape": "NumberCapabilityList", "documentation": "<p>Specifies if the number could be used for text messages, voice, or both.</p>"}, "NumberType": {"shape": "NumberType", "documentation": "<p>The type of number that was released.</p>"}, "MonthlyLeasingPrice": {"shape": "String", "documentation": "<p>The monthly price of the phone number, in US dollars.</p>"}, "TwoWayEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>By default this is set to false. When set to true you can receive incoming text messages from your end recipients.</p>"}, "TwoWayChannelArn": {"shape": "TwoWayChannelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the TwoWayChannel.</p>"}, "SelfManagedOptOutsEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>By default this is set to false. When an end recipient sends a message that begins with HELP or STOP to one of your dedicated numbers, Amazon Pinpoint automatically replies with a customizable message and adds the end recipient to the OptOutList. When set to true you're responsible for responding to HELP and STOP requests. You're also responsible for tracking and honoring opt-out requests.</p>"}, "OptOutListName": {"shape": "OptOutListName", "documentation": "<p>The name of the OptOutList that was associated with the phone number.</p>"}, "CreatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The time when the phone number was created, in <a href=\"https://www.epochconverter.com/\">UNIX epoch time</a> format.</p>"}}}, "RequestPhoneNumberRequest": {"type": "structure", "required": ["IsoCountryCode", "MessageType", "NumberCapabilities", "NumberType"], "members": {"IsoCountryCode": {"shape": "IsoCountryCode", "documentation": "<p>The two-character code, in ISO 3166-1 alpha-2 format, for the country or region. </p>"}, "MessageType": {"shape": "MessageType", "documentation": "<p>The type of message. Valid values are TRANSACTIONAL for messages that are critical or time-sensitive and PROMOTIONAL for messages that aren't critical or time-sensitive.</p>"}, "NumberCapabilities": {"shape": "NumberCapabilityList", "documentation": "<p>Indicates if the phone number will be used for text messages, voice messages, or both. </p>"}, "NumberType": {"shape": "RequestableNumberType", "documentation": "<p>The type of phone number to request.</p>"}, "OptOutListName": {"shape": "OptOutListNameOrArn", "documentation": "<p>The name of the OptOutList to associate with the phone number. You can use the OutOutListName or OptPutListArn.</p>"}, "PoolId": {"shape": "PoolIdOrArn", "documentation": "<p>The pool to associated with the phone number. You can use the PoolId or PoolArn. </p>"}, "RegistrationId": {"shape": "RegistrationId", "documentation": "<p>Use this field to attach your phone number for an external registration process.</p>"}, "DeletionProtectionEnabled": {"shape": "Boolean", "documentation": "<p>By default this is set to false. When set to true the phone number can't be deleted.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>An array of tags (key and value pairs) associate with the requested phone number. </p>"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If you don't specify a client token, a randomly generated token is used for the request to ensure idempotency.</p>", "idempotencyToken": true}}}, "RequestPhoneNumberResult": {"type": "structure", "members": {"PhoneNumberArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the requested phone number.</p>"}, "PhoneNumberId": {"shape": "String", "documentation": "<p>The unique identifier of the new phone number.</p>"}, "PhoneNumber": {"shape": "PhoneNumber", "documentation": "<p>The new phone number that was requested.</p>"}, "Status": {"shape": "NumberStatus", "documentation": "<p>The current status of the request.</p>"}, "IsoCountryCode": {"shape": "IsoCountryCode", "documentation": "<p>The two-character code, in ISO 3166-1 alpha-2 format, for the country or region. </p>"}, "MessageType": {"shape": "MessageType", "documentation": "<p>The type of message. Valid values are TRANSACTIONAL for messages that are critical or time-sensitive and PROMOTIONAL for messages that aren't critical or time-sensitive.</p>"}, "NumberCapabilities": {"shape": "NumberCapabilityList", "documentation": "<p>Indicates if the phone number will be used for text messages, voice messages or both. </p>"}, "NumberType": {"shape": "RequestableNumberType", "documentation": "<p>The type of number that was released.</p>"}, "MonthlyLeasingPrice": {"shape": "String", "documentation": "<p>The monthly price, in US dollars, to lease the phone number.</p>"}, "TwoWayEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>By default this is set to false. When set to true you can receive incoming text messages from your end recipients.</p>"}, "TwoWayChannelArn": {"shape": "TwoWayChannelArn", "documentation": "<p>The ARN used to identify the two way channel.</p>"}, "SelfManagedOptOutsEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>By default this is set to false. When an end recipient sends a message that begins with HELP or STOP to one of your dedicated numbers, Amazon Pinpoint automatically replies with a customizable message and adds the end recipient to the OptOutList. When set to true you're responsible for responding to HELP and STOP requests. You're also responsible for tracking and honoring opt-out requests.</p>"}, "OptOutListName": {"shape": "OptOutListName", "documentation": "<p>The name of the OptOutList that is associated with the requested phone number.</p>"}, "DeletionProtectionEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>By default this is set to false. When set to true the phone number can't be deleted. </p>"}, "PoolId": {"shape": "String", "documentation": "<p>The unique identifier of the pool associated with the phone number </p>"}, "Tags": {"shape": "TagList", "documentation": "<p>An array of key and value pair tags that are associated with the phone number.</p>"}, "CreatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The time when the phone number was created, in <a href=\"https://www.epochconverter.com/\">UNIX epoch time</a> format.</p>"}}}, "RequestableNumberType": {"type": "string", "enum": ["LONG_CODE", "TOLL_FREE", "TEN_DLC"]}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "String"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The type of resource that caused the exception.</p>"}, "ResourceId": {"shape": "String", "documentation": "<p>The unique identifier of the resource.</p>"}}, "documentation": "<p>A requested resource couldn't be found.</p>", "exception": true}, "ResourceType": {"type": "string", "enum": ["account", "phone-number", "sender-id", "pool", "configuration-set", "opt-out-list", "event-destination", "keyword", "opted-out-number", "registration"]}, "SendTextMessageRequest": {"type": "structure", "required": ["DestinationPhoneNumber"], "members": {"DestinationPhoneNumber": {"shape": "PhoneNumber", "documentation": "<p>The destination phone number in E.164 format.</p>"}, "OriginationIdentity": {"shape": "TextMessageOriginationIdentity", "documentation": "<p>The origination identity of the message. This can be either the PhoneNumber, PhoneNumberId, PhoneNumberArn, SenderId, SenderIdArn, PoolId, or PoolArn.</p>"}, "MessageBody": {"shape": "TextMessageBody", "documentation": "<p>The body of the text message.</p>"}, "MessageType": {"shape": "MessageType", "documentation": "<p>The type of message. Valid values are TRANSACTIONAL for messages that are critical or time-sensitive and PROMOTIONAL for messages that aren't critical or time-sensitive.</p>"}, "Keyword": {"shape": "Keyword", "documentation": "<p>When you register a short code in the US, you must specify a program name. If you don’t have a US short code, omit this attribute.</p>"}, "ConfigurationSetName": {"shape": "ConfigurationSetNameOrArn", "documentation": "<p>The name of the configuration set to use. This can be either the ConfigurationSetName or ConfigurationSetArn.</p>"}, "MaxPrice": {"shape": "MaxPrice", "documentation": "<p>The maximum amount that you want to spend, in US dollars, per each text message part. A text message can contain multiple parts.</p>"}, "TimeToLive": {"shape": "TimeToLive", "documentation": "<p>How long the text message is valid for. By default this is 72 hours.</p>"}, "Context": {"shape": "ContextMap", "documentation": "<p>You can specify custom data in this field. If you do, that data is logged to the event destination.</p>"}, "DestinationCountryParameters": {"shape": "DestinationCountryParameters", "documentation": "<p>This field is used for any country-specific registration requirements. Currently, this setting is only used when you send messages to recipients in India using a sender ID. For more information see <a href=\"https://docs.aws.amazon.com/pinpoint/latest/userguide/channels-sms-senderid-india.html\">Special requirements for sending SMS messages to recipients in India</a>. </p>"}, "DryRun": {"shape": "PrimitiveBoolean", "documentation": "<p>When set to true, the message is checked and validated, but isn't sent to the end recipient.</p>"}}}, "SendTextMessageResult": {"type": "structure", "members": {"MessageId": {"shape": "String", "documentation": "<p>The unique identifier for the message.</p>"}}}, "SendVoiceMessageRequest": {"type": "structure", "required": ["DestinationPhoneNumber", "OriginationIdentity"], "members": {"DestinationPhoneNumber": {"shape": "PhoneNumber", "documentation": "<p>The destination phone number in E.164 format.</p>"}, "OriginationIdentity": {"shape": "VoiceMessageOriginationIdentity", "documentation": "<p>The origination identity to use for the voice call. This can be the PhoneNumber, PhoneNumberId, PhoneNumberArn, PoolId, or PoolArn.</p>"}, "MessageBody": {"shape": "VoiceMessageBody", "documentation": "<p>The text to convert to a voice message.</p>"}, "MessageBodyTextType": {"shape": "VoiceMessageBodyTextType", "documentation": "<p>Specifies if the MessageBody field contains text or <a href=\"https://docs.aws.amazon.com/polly/latest/dg/what-is.html\">speech synthesis markup language (SSML)</a>.</p> <ul> <li> <p>TEXT: This is the default value. When used the maximum character limit is 3000.</p> </li> <li> <p>SSML: When used the maximum character limit is 6000 including SSML tagging.</p> </li> </ul>"}, "VoiceId": {"shape": "VoiceId", "documentation": "<p>The voice for the <a href=\"https://docs.aws.amazon.com/polly/latest/dg/what-is.html\">Amazon Polly</a> service to use. By default this is set to \"MATTHEW\".</p>"}, "ConfigurationSetName": {"shape": "ConfigurationSetNameOrArn", "documentation": "<p>The name of the configuration set to use. This can be either the ConfigurationSetName or ConfigurationSetArn.</p>"}, "MaxPricePerMinute": {"shape": "MaxPrice", "documentation": "<p>The maximum amount to spend per voice message, in US dollars.</p>"}, "TimeToLive": {"shape": "TimeToLive", "documentation": "<p>How long the voice message is valid for. By default this is 72 hours.</p>"}, "Context": {"shape": "ContextMap", "documentation": "<p>You can specify custom data in this field. If you do, that data is logged to the event destination.</p>"}, "DryRun": {"shape": "PrimitiveBoolean", "documentation": "<p>When set to true, the message is checked and validated, but isn't sent to the end recipient.</p>"}}}, "SendVoiceMessageResult": {"type": "structure", "members": {"MessageId": {"shape": "String", "documentation": "<p>The unique identifier for the message.</p>"}}}, "SenderId": {"type": "string", "max": 11, "min": 1, "pattern": "[A-Za-z0-9_-]+"}, "SenderIdAndCountry": {"type": "structure", "required": ["SenderId", "IsoCountryCode"], "members": {"SenderId": {"shape": "SenderIdOrArn", "documentation": "<p>The unique identifier of the sender.</p>"}, "IsoCountryCode": {"shape": "IsoCountryCode", "documentation": "<p>The two-character code, in ISO 3166-1 alpha-2 format, for the country or region. </p>"}}, "documentation": "<p> The alphanumeric sender ID in a specific country that you want to describe. For more information on sender IDs see <a href=\"https://docs.aws.amazon.com/pinpoint/latest/userguide/channels-sms-awssupport-sender-id.html\">Requesting sender IDs for SMS messaging with Amazon Pinpoint </a> in the <i>Amazon Pinpoint User Guide</i>.</p>"}, "SenderIdFilter": {"type": "structure", "required": ["Name", "Values"], "members": {"Name": {"shape": "SenderIdFilterName", "documentation": "<p>The name of the attribute to filter on.</p>"}, "Values": {"shape": "FilterValueList", "documentation": "<p>An array of values to filter for.</p>"}}, "documentation": "<p>The information for a sender ID that meets a specified criteria.</p>"}, "SenderIdFilterList": {"type": "list", "member": {"shape": "SenderIdFilter"}, "max": 20, "min": 0}, "SenderIdFilterName": {"type": "string", "enum": ["sender-id", "iso-country-code", "message-type"]}, "SenderIdInformation": {"type": "structure", "required": ["SenderIdArn", "SenderId", "IsoCountryCode", "MessageTypes", "MonthlyLeasingPrice"], "members": {"SenderIdArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) associated with the SenderId.</p>"}, "SenderId": {"shape": "SenderId", "documentation": "<p>The alphanumeric sender ID in a specific country that you'd like to describe.</p>"}, "IsoCountryCode": {"shape": "IsoCountryCode", "documentation": "<p>The two-character code, in ISO 3166-1 alpha-2 format, for the country or region. </p>"}, "MessageTypes": {"shape": "MessageTypeList", "documentation": "<p>The type of message. Valid values are TRANSACTIONAL for messages that are critical or time-sensitive and PROMOTIONAL for messages that aren't critical or time-sensitive.</p>"}, "MonthlyLeasingPrice": {"shape": "String", "documentation": "<p>The monthly leasing price, in US dollars.</p>"}}, "documentation": "<p>The information for all SenderIds in an Amazon Web Services account.</p>"}, "SenderIdInformationList": {"type": "list", "member": {"shape": "SenderIdInformation"}}, "SenderIdList": {"type": "list", "member": {"shape": "SenderIdAndCountry"}, "max": 5, "min": 0}, "SenderIdOrArn": {"type": "string", "max": 256, "min": 1, "pattern": "[A-Za-z0-9_:/-]+"}, "ServiceQuotaExceededException": {"type": "structure", "members": {"Message": {"shape": "String"}, "Reason": {"shape": "ServiceQuotaExceededExceptionReason", "documentation": "<p>The reason for the exception.</p>"}}, "documentation": "<p>The request would cause a service quota to be exceeded.</p>", "exception": true}, "ServiceQuotaExceededExceptionReason": {"type": "string", "enum": ["CONFIGURATION_SETS_PER_ACCOUNT", "DAILY_DESTINATION_CALL_LIMIT", "EVENT_DESTINATIONS_PER_CONFIGURATION_SET", "KEYWORDS_PER_PHONE_NUMBER", "KEYWORDS_PER_POOL", "MONTHLY_SPEND_LIMIT_REACHED_FOR_TEXT", "MONTHLY_SPEND_LIMIT_REACHED_FOR_VOICE", "OPT_OUT_LISTS_PER_ACCOUNT", "ORIGINATION_IDENTITIES_PER_POOL", "PHONE_NUMBERS_PER_ACCOUNT", "PHONE_NUMBERS_PER_REGISTRATION", "POOLS_PER_ACCOUNT", "TAGS_PER_RESOURCE"]}, "SetDefaultMessageTypeRequest": {"type": "structure", "required": ["ConfigurationSetName", "MessageType"], "members": {"ConfigurationSetName": {"shape": "ConfigurationSetNameOrArn", "documentation": "<p>The configuration set to update with a new default message type. This field can be the ConsigurationSetName or ConfigurationSetArn.</p>"}, "MessageType": {"shape": "MessageType", "documentation": "<p>The type of message. Valid values are TRANSACTIONAL for messages that are critical or time-sensitive and PROMOTIONAL for messages that aren't critical or time-sensitive.</p>"}}}, "SetDefaultMessageTypeResult": {"type": "structure", "members": {"ConfigurationSetArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the updated configuration set.</p>"}, "ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the configuration set that was updated.</p>"}, "MessageType": {"shape": "MessageType", "documentation": "<p>The new default message type of the configuration set.</p>"}}}, "SetDefaultSenderIdRequest": {"type": "structure", "required": ["ConfigurationSetName", "SenderId"], "members": {"ConfigurationSetName": {"shape": "ConfigurationSetNameOrArn", "documentation": "<p>The configuration set to updated with a new default SenderId. This field can be the ConsigurationSetName or ConfigurationSetArn.</p>"}, "SenderId": {"shape": "SenderId", "documentation": "<p>The current sender ID for the configuration set. When sending a text message to a destination country which supports SenderIds, the default sender ID on the configuration set specified on <a>SendTextMessage</a> will be used if no dedicated origination phone numbers or registered SenderIds are available in your account, instead of a generic sender ID, such as 'NOTICE'.</p>"}}}, "SetDefaultSenderIdResult": {"type": "structure", "members": {"ConfigurationSetArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the updated configuration set.</p>"}, "ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the configuration set that was updated.</p>"}, "SenderId": {"shape": "SenderId", "documentation": "<p>The default sender ID to set for the ConfigurationSet.</p>"}}}, "SetTextMessageSpendLimitOverrideRequest": {"type": "structure", "required": ["MonthlyLimit"], "members": {"MonthlyLimit": {"shape": "MonthlyLimit", "documentation": "<p>The new monthly limit to enforce on text messages.</p>"}}}, "SetTextMessageSpendLimitOverrideResult": {"type": "structure", "members": {"MonthlyLimit": {"shape": "MonthlyLimit", "documentation": "<p>The current monthly limit to enforce on sending text messages.</p>"}}}, "SetVoiceMessageSpendLimitOverrideRequest": {"type": "structure", "required": ["MonthlyLimit"], "members": {"MonthlyLimit": {"shape": "MonthlyLimit", "documentation": "<p>The new monthly limit to enforce on voice messages.</p>"}}}, "SetVoiceMessageSpendLimitOverrideResult": {"type": "structure", "members": {"MonthlyLimit": {"shape": "MonthlyLimit", "documentation": "<p>The current monthly limit to enforce on sending voice messages.</p>"}}}, "SnsDestination": {"type": "structure", "required": ["TopicArn"], "members": {"TopicArn": {"shape": "SnsTopicArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon SNS topic that you want to publish events to.</p>"}}, "documentation": "<p>An object that defines an Amazon SNS destination for events. You can use Amazon SNS to send notification when certain events occur.</p>"}, "SnsTopicArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:\\S+"}, "SpendLimit": {"type": "structure", "required": ["Name", "EnforcedLimit", "MaxLimit", "Overridden"], "members": {"Name": {"shape": "SpendLimitName", "documentation": "<p>The name for the SpendLimit.</p>"}, "EnforcedLimit": {"shape": "PrimitiveLong", "documentation": "<p>The maximum amount of money, in US dollars, that you want to be able to spend sending messages each month. This value has to be less than or equal to the amount in <code>MaxLimit</code>. To use this custom limit, <code>Overridden</code> must be set to true.</p>"}, "MaxLimit": {"shape": "PrimitiveLong", "documentation": "<p> The maximum amount of money that you are able to spend to send messages each month, in US dollars.</p>"}, "Overridden": {"shape": "PrimitiveBoolean", "documentation": "<p>When set to <code>True</code>, the value that has been specified in the <code>EnforcedLimit</code> is used to determine the maximum amount in US dollars that can be spent to send messages each month, in US dollars.</p>"}}, "documentation": "<p>Describes the current Amazon Pinpoint monthly spend limits for sending voice and text messages. For more information on increasing your monthly spend limit, see <a href=\"https://docs.aws.amazon.com/pinpoint/latest/userguide/channels-sms-awssupport-spend-threshold.html\"> Requesting increases to your monthly SMS spending quota for Amazon Pinpoint </a> in the <i>Amazon Pinpoint User Guide</i>. </p>"}, "SpendLimitList": {"type": "list", "member": {"shape": "SpendLimit"}}, "SpendLimitName": {"type": "string", "enum": ["TEXT_MESSAGE_MONTHLY_SPEND_LIMIT", "VOICE_MESSAGE_MONTHLY_SPEND_LIMIT"]}, "String": {"type": "string"}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>The key identifier, or name, of the tag.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The string value associated with the key of the tag.</p>"}}, "documentation": "<p>The list of tags to be added to the specified topic.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": ".+"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 1}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "max": 200, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>"}, "Tags": {"shape": "NonEmptyTagList", "documentation": "<p>An array of key and value pair tags that are associated with the resource.</p>"}}}, "TagResourceResult": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": ".*"}, "TextMessageBody": {"type": "string", "max": 1600, "min": 1, "pattern": "(?!\\s*$)[\\s\\S]+"}, "TextMessageOriginationIdentity": {"type": "string", "max": 256, "min": 1, "pattern": "[A-Za-z0-9_:/\\+-]+"}, "ThrottlingException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>An error that occurred because too many requests were sent during a certain amount of time.</p>", "exception": true, "retryable": {"throttling": true}}, "TimeToLive": {"type": "integer", "box": true, "max": 259200, "min": 5}, "Timestamp": {"type": "timestamp"}, "TwoWayChannelArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:\\S+"}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>An array of tag key values to unassociate with the resource.</p>"}}}, "UntagResourceResult": {"type": "structure", "members": {}}, "UpdateEventDestinationRequest": {"type": "structure", "required": ["ConfigurationSetName", "EventDestinationName"], "members": {"ConfigurationSetName": {"shape": "ConfigurationSetNameOrArn", "documentation": "<p>The configuration set to update with the new event destination. Valid values for this can be the ConfigurationSetName or ConfigurationSetArn.</p>"}, "EventDestinationName": {"shape": "EventDestinationName", "documentation": "<p>The name to use for the event destination.</p>"}, "Enabled": {"shape": "Boolean", "documentation": "<p>When set to true logging is enabled.</p>"}, "MatchingEventTypes": {"shape": "EventTypeList", "documentation": "<p>An array of event types that determine which events to log.</p>"}, "CloudWatchLogsDestination": {"shape": "CloudWatchLogsDestination", "documentation": "<p>An object that contains information about an event destination that sends data to CloudWatch Logs.</p>"}, "KinesisFirehoseDestination": {"shape": "KinesisFirehoseDestination", "documentation": "<p>An object that contains information about an event destination for logging to Kinesis Data Firehose.</p>"}, "SnsDestination": {"shape": "SnsDestination", "documentation": "<p>An object that contains information about an event destination that sends data to Amazon SNS.</p>"}}}, "UpdateEventDestinationResult": {"type": "structure", "members": {"ConfigurationSetArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) for the ConfigurationSet that was updated.</p>"}, "ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the configuration set.</p>"}, "EventDestination": {"shape": "EventDestination", "documentation": "<p>An EventDestination object containing the details of where events will be logged. </p>"}}}, "UpdatePhoneNumberRequest": {"type": "structure", "required": ["PhoneNumberId"], "members": {"PhoneNumberId": {"shape": "PhoneNumberIdOrArn", "documentation": "<p>The unique identifier of the phone number. Valid values for this field can be either the PhoneNumberId or PhoneNumberArn.</p>"}, "TwoWayEnabled": {"shape": "Boolean", "documentation": "<p>By default this is set to false. When set to true you can receive incoming text messages from your end recipients.</p>"}, "TwoWayChannelArn": {"shape": "TwoWayChannelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the two way channel.</p>"}, "SelfManagedOptOutsEnabled": {"shape": "Boolean", "documentation": "<p>By default this is set to false. When an end recipient sends a message that begins with HELP or STOP to one of your dedicated numbers, Amazon Pinpoint automatically replies with a customizable message and adds the end recipient to the OptOutList. When set to true you're responsible for responding to HELP and STOP requests. You're also responsible for tracking and honoring opt-out requests.</p>"}, "OptOutListName": {"shape": "OptOutListNameOrArn", "documentation": "<p>The OptOutList to add the phone number to. Valid values for this field can be either the OutOutListName or OutOutListArn.</p>"}, "DeletionProtectionEnabled": {"shape": "Boolean", "documentation": "<p>By default this is set to false. When set to true the phone number can't be deleted. </p>"}}}, "UpdatePhoneNumberResult": {"type": "structure", "members": {"PhoneNumberArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the updated phone number.</p>"}, "PhoneNumberId": {"shape": "String", "documentation": "<p>The unique identifier of the phone number.</p>"}, "PhoneNumber": {"shape": "PhoneNumber", "documentation": "<p>The phone number that was updated.</p>"}, "Status": {"shape": "NumberStatus", "documentation": "<p>The current status of the request.</p>"}, "IsoCountryCode": {"shape": "IsoCountryCode", "documentation": "<p>The two-character code, in ISO 3166-1 alpha-2 format, for the country or region. </p>"}, "MessageType": {"shape": "MessageType", "documentation": "<p>The type of message. Valid values are TRANSACTIONAL for messages that are critical or time-sensitive and PROMOTIONAL for messages that aren't critical or time-sensitive.</p>"}, "NumberCapabilities": {"shape": "NumberCapabilityList", "documentation": "<p>Specifies if the number could be used for text messages, voice or both.</p>"}, "NumberType": {"shape": "NumberType", "documentation": "<p>The type of number that was requested.</p>"}, "MonthlyLeasingPrice": {"shape": "String", "documentation": "<p>The monthly leasing price of the phone number, in US dollars.</p>"}, "TwoWayEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>By default this is set to false. When set to true you can receive incoming text messages from your end recipients.</p>"}, "TwoWayChannelArn": {"shape": "TwoWayChannelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the two way channel.</p>"}, "SelfManagedOptOutsEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>This is true if self managed opt-out are enabled.</p>"}, "OptOutListName": {"shape": "OptOutListName", "documentation": "<p>The name of the OptOutList associated with the phone number.</p>"}, "DeletionProtectionEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>When set to true the phone number can't be deleted.</p>"}, "CreatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The time when the phone number was created, in <a href=\"https://www.epochconverter.com/\">UNIX epoch time</a> format.</p>"}}}, "UpdatePoolRequest": {"type": "structure", "required": ["PoolId"], "members": {"PoolId": {"shape": "PoolIdOrArn", "documentation": "<p>The unique identifier of the pool to update. Valid values are either the PoolId or PoolArn.</p>"}, "TwoWayEnabled": {"shape": "Boolean", "documentation": "<p>By default this is set to false. When set to true you can receive incoming text messages from your end recipients.</p>"}, "TwoWayChannelArn": {"shape": "TwoWayChannelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the two way channel.</p>"}, "SelfManagedOptOutsEnabled": {"shape": "Boolean", "documentation": "<p>By default this is set to false. When an end recipient sends a message that begins with HELP or STOP to one of your dedicated numbers, Amazon Pinpoint automatically replies with a customizable message and adds the end recipient to the OptOutList. When set to true you're responsible for responding to HELP and STOP requests. You're also responsible for tracking and honoring opt-out requests.</p>"}, "OptOutListName": {"shape": "OptOutListNameOrArn", "documentation": "<p>The OptOutList to associate with the pool. Valid values are either OptOutListName or OptOutListArn.</p>"}, "SharedRoutesEnabled": {"shape": "Boolean", "documentation": "<p>Indicates whether shared routes are enabled for the pool.</p>"}, "DeletionProtectionEnabled": {"shape": "Boolean", "documentation": "<p>When set to true the pool can't be deleted.</p>"}}}, "UpdatePoolResult": {"type": "structure", "members": {"PoolArn": {"shape": "String", "documentation": "<p>The ARN of the pool.</p>"}, "PoolId": {"shape": "String", "documentation": "<p>The unique identifier of the pool.</p>"}, "Status": {"shape": "PoolStatus", "documentation": "<p>The current status of the pool update request.</p>"}, "MessageType": {"shape": "MessageType", "documentation": "<p>The type of message for the pool to use.</p>"}, "TwoWayEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>By default this is set to false. When set to true you can receive incoming text messages from your end recipients.</p>"}, "TwoWayChannelArn": {"shape": "TwoWayChannelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the two way channel.</p>"}, "SelfManagedOptOutsEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>When an end recipient sends a message that begins with HELP or STOP to one of your dedicated numbers, Amazon Pinpoint automatically replies with a customizable message and adds the end recipient to the OptOutList. When set to true you're responsible for responding to HELP and STOP requests. You're also responsible for tracking and honoring opt-out requests.</p>"}, "OptOutListName": {"shape": "OptOutListName", "documentation": "<p>The name of the OptOutList associated with the pool.</p>"}, "SharedRoutesEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>Indicates whether shared routes are enabled for the pool.</p>"}, "DeletionProtectionEnabled": {"shape": "PrimitiveBoolean", "documentation": "<p>When set to true the pool can't be deleted.</p>"}, "CreatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The time when the pool was created, in <a href=\"https://www.epochconverter.com/\">UNIX epoch time</a> format.</p>"}}}, "ValidationException": {"type": "structure", "members": {"Message": {"shape": "String"}, "Reason": {"shape": "ValidationExceptionReason", "documentation": "<p>The reason for the exception.</p>"}, "Fields": {"shape": "ValidationExceptionFieldList", "documentation": "<p>The field that failed validation.</p>"}}, "documentation": "<p>A validation exception for a field.</p>", "exception": true}, "ValidationExceptionField": {"type": "structure", "required": ["Name", "Message"], "members": {"Name": {"shape": "String", "documentation": "<p>The name of the field.</p>"}, "Message": {"shape": "String", "documentation": "<p>The message associated with the validation exception with information to help determine its cause.</p>"}}, "documentation": "<p>The field associated with the validation exception.</p>"}, "ValidationExceptionFieldList": {"type": "list", "member": {"shape": "ValidationExceptionField"}}, "ValidationExceptionReason": {"type": "string", "enum": ["UNKNOWN_OPERATION", "CANNOT_PARSE", "FIELD_VALIDATION_FAILED", "OTHER", "INVALID_PARAMETER", "INVALID_ARN", "INVALID_IDENTITY_FOR_DESTINATION_COUNTRY", "DESTINATION_COUNTRY_BLOCKED", "CANNOT_ADD_OPTED_OUT_NUMBER", "COUNTRY_CODE_MISMATCH", "INVALID_FILTER_VALUES", "INVALID_NEXT_TOKEN", "MISSING_PARAMETER", "PARAMETERS_CANNOT_BE_USED_TOGETHER", "PHONE_NUMBER_CANNOT_BE_OPTED_IN", "PHONE_NUMBER_CANNOT_BE_RELEASED", "PRICE_OVER_THRESHOLD", "REQUESTED_SPEND_LIMIT_HIGHER_THAN_SERVICE_LIMIT", "SENDER_ID_NOT_REGISTERED", "SENDER_ID_NOT_SUPPORTED", "TWO_WAY_NOT_ENABLED", "TWO_WAY_NOT_SUPPORTED_IN_COUNTRY", "TWO_WAY_NOT_SUPPORTED_IN_REGION", "TWO_WAY_TOPIC_NOT_PRESENT"]}, "VoiceId": {"type": "string", "enum": ["AMY", "ASTRID", "BIANCA", "BRIAN", "CAMILA", "CARLA", "CARMEN", "CELINE", "CHANTAL", "CONCHITA", "CRISTIANO", "DORA", "EMMA", "ENRIQUE", "EWA", "FILIZ", "GERAINT", "GIORGIO", "GWYNETH", "HANS", "INES", "IVY", "JACEK", "JAN", "JOANNA", "JOEY", "JUSTIN", "KARL", "KENDRA", "KIMBERLY", "LEA", "LIV", "LOTTE", "LUCIA", "LUPE", "MADS", "MAJA", "MARLENE", "MATHIEU", "MATTHEW", "MAXIM", "MIA", "MIGUEL", "MIZUKI", "NAJA", "NICOLE", "PENELOPE", "RAVEENA", "RICARDO", "RUBEN", "RUSSELL", "SALLI", "SEOYEON", "TAKUMI", "TATYANA", "VICKI", "VITORIA", "ZEINA", "ZHIYU"]}, "VoiceMessageBody": {"type": "string", "max": 6000, "min": 1, "pattern": "(?!\\s*$)[\\s\\S]+"}, "VoiceMessageBodyTextType": {"type": "string", "enum": ["TEXT", "SSML"]}, "VoiceMessageOriginationIdentity": {"type": "string", "max": 256, "min": 1, "pattern": "[A-Za-z0-9_:/\\+-]+"}}, "documentation": "<p>Welcome to the <i>Amazon Pinpoint SMS and Voice, version 2 API Reference</i>. This guide provides information about Amazon Pinpoint SMS and Voice, version 2 API resources, including supported HTTP methods, parameters, and schemas.</p> <p>Amazon Pinpoint is an Amazon Web Services service that you can use to engage with your recipients across multiple messaging channels. The Amazon Pinpoint SMS and Voice, version 2 API provides programmatic access to options that are unique to the SMS and voice channels and supplements the resources provided by the Amazon Pinpoint API.</p> <p>If you're new to Amazon Pinpoint, it's also helpful to review the <a href=\"https://docs.aws.amazon.com/pinpoint/latest/developerguide/welcome.html\"> Amazon Pinpoint Developer Guide</a>. The <i>Amazon Pinpoint Developer Guide</i> provides tutorials, code samples, and procedures that demonstrate how to use Amazon Pinpoint features programmatically and how to integrate Amazon Pinpoint functionality into mobile apps and other types of applications. The guide also provides key information, such as Amazon Pinpoint integration with other Amazon Web Services services, and the quotas that apply to use of the service.</p>"}