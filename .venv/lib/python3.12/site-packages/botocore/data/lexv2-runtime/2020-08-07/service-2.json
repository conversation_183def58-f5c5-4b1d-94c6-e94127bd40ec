{"version": "2.0", "metadata": {"apiVersion": "2020-08-07", "endpointPrefix": "runtime-v2-lex", "jsonVersion": "1.1", "protocol": "rest-json", "protocolSettings": {"h2": "eventstream"}, "serviceAbbreviation": "Lex Runtime V2", "serviceFullName": "Amazon Lex Runtime V2", "serviceId": "Lex Runtime V2", "signatureVersion": "v4", "signingName": "lex", "uid": "runtime.lex.v2-2020-08-07"}, "operations": {"DeleteSession": {"name": "DeleteSession", "http": {"method": "DELETE", "requestUri": "/bots/{botId}/botAliases/{botAliasId}/botLocales/{localeId}/sessions/{sessionId}"}, "input": {"shape": "DeleteSessionRequest"}, "output": {"shape": "DeleteSessionResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}], "documentation": "<p>Removes session information for a specified bot, alias, and user ID. </p> <p>You can use this operation to restart a conversation with a bot. When you remove a session, the entire history of the session is removed so that you can start again.</p> <p>You don't need to delete a session. Sessions have a time limit and will expire. Set the session time limit when you create the bot. The default is 5 minutes, but you can specify anything between 1 minute and 24 hours.</p> <p>If you specify a bot or alias ID that doesn't exist, you receive a <code>BadRequestException.</code> </p> <p>If the locale doesn't exist in the bot, or if the locale hasn't been enables for the alias, you receive a <code>BadRequestException</code>.</p>"}, "GetSession": {"name": "GetSession", "http": {"method": "GET", "requestUri": "/bots/{botId}/botAliases/{botAliasId}/botLocales/{localeId}/sessions/{sessionId}"}, "input": {"shape": "GetSessionRequest"}, "output": {"shape": "GetSessionResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns session information for a specified bot, alias, and user.</p> <p>For example, you can use this operation to retrieve session information for a user that has left a long-running session in use.</p> <p>If the bot, alias, or session identifier doesn't exist, Amazon Lex V2 returns a <code>BadRequestException</code>. If the locale doesn't exist or is not enabled for the alias, you receive a <code>BadRequestException</code>.</p>"}, "PutSession": {"name": "PutSession", "http": {"method": "POST", "requestUri": "/bots/{botId}/botAliases/{botAliasId}/botLocales/{localeId}/sessions/{sessionId}"}, "input": {"shape": "PutSessionRequest"}, "output": {"shape": "PutSessionResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "DependencyFailedException"}, {"shape": "BadGatewayException"}], "documentation": "<p>Creates a new session or modifies an existing session with an Amazon Lex V2 bot. Use this operation to enable your application to set the state of the bot.</p>"}, "RecognizeText": {"name": "RecognizeText", "http": {"method": "POST", "requestUri": "/bots/{botId}/botAliases/{botAliasId}/botLocales/{localeId}/sessions/{sessionId}/text"}, "input": {"shape": "RecognizeTextRequest"}, "output": {"shape": "RecognizeTextResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "DependencyFailedException"}, {"shape": "BadGatewayException"}], "documentation": "<p>Sends user input to Amazon Lex V2. Client applications use this API to send requests to Amazon Lex V2 at runtime. Amazon Lex V2 then interprets the user input using the machine learning model that it build for the bot.</p> <p>In response, Amazon Lex V2 returns the next message to convey to the user and an optional response card to display.</p> <p>If the optional post-fulfillment response is specified, the messages are returned as follows. For more information, see <a href=\"https://docs.aws.amazon.com/lexv2/latest/dg/API_PostFulfillmentStatusSpecification.html\">PostFulfillmentStatusSpecification</a>.</p> <ul> <li> <p> <b>Success message</b> - Returned if the Lambda function completes successfully and the intent state is fulfilled or ready fulfillment if the message is present.</p> </li> <li> <p> <b>Failed message</b> - The failed message is returned if the Lambda function throws an exception or if the Lambda function returns a failed intent state without a message.</p> </li> <li> <p> <b>Timeout message</b> - If you don't configure a timeout message and a timeout, and the Lambda function doesn't return within 30 seconds, the timeout message is returned. If you configure a timeout, the timeout message is returned when the period times out. </p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/lexv2/latest/dg/streaming-progress.html#progress-complete.html\">Completion message</a>.</p>"}, "RecognizeUtterance": {"name": "RecognizeUtterance", "http": {"method": "POST", "requestUri": "/bots/{botId}/botAliases/{botAliasId}/botLocales/{localeId}/sessions/{sessionId}/utterance"}, "input": {"shape": "RecognizeUtteranceRequest"}, "output": {"shape": "RecognizeUtteranceResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "DependencyFailedException"}, {"shape": "BadGatewayException"}], "documentation": "<p>Sends user input to Amazon Lex V2. You can send text or speech. Clients use this API to send text and audio requests to Amazon Lex V2 at runtime. Amazon Lex V2 interprets the user input using the machine learning model built for the bot.</p> <p>The following request fields must be compressed with gzip and then base64 encoded before you send them to Amazon Lex V2. </p> <ul> <li> <p>requestAttributes</p> </li> <li> <p>sessionState</p> </li> </ul> <p>The following response fields are compressed using gzip and then base64 encoded by Amazon Lex V2. Before you can use these fields, you must decode and decompress them. </p> <ul> <li> <p>inputTranscript</p> </li> <li> <p>interpretations</p> </li> <li> <p>messages</p> </li> <li> <p>requestAttributes</p> </li> <li> <p>sessionState</p> </li> </ul> <p>The example contains a Java application that compresses and encodes a Java object to send to Amazon Lex V2, and a second that decodes and decompresses a response from Amazon Lex V2.</p> <p>If the optional post-fulfillment response is specified, the messages are returned as follows. For more information, see <a href=\"https://docs.aws.amazon.com/lexv2/latest/dg/API_PostFulfillmentStatusSpecification.html\">PostFulfillmentStatusSpecification</a>.</p> <ul> <li> <p> <b>Success message</b> - Returned if the Lambda function completes successfully and the intent state is fulfilled or ready fulfillment if the message is present.</p> </li> <li> <p> <b>Failed message</b> - The failed message is returned if the Lambda function throws an exception or if the Lambda function returns a failed intent state without a message.</p> </li> <li> <p> <b>Timeout message</b> - If you don't configure a timeout message and a timeout, and the Lambda function doesn't return within 30 seconds, the timeout message is returned. If you configure a timeout, the timeout message is returned when the period times out. </p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/lexv2/latest/dg/streaming-progress.html#progress-complete.html\">Completion message</a>.</p>", "authtype": "v4-unsigned-body"}, "StartConversation": {"name": "StartConversation", "http": {"method": "POST", "requestUri": "/bots/{botId}/botAliases/{botAliasId}/botLocales/{localeId}/sessions/{sessionId}/conversation"}, "input": {"shape": "StartConversationRequest"}, "output": {"shape": "StartConversationResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Starts an HTTP/2 bidirectional event stream that enables you to send audio, text, or DTMF input in real time. After your application starts a conversation, users send input to Amazon Lex V2 as a stream of events. Amazon Lex V2 processes the incoming events and responds with streaming text or audio events. </p> <p>Audio input must be in the following format: <code>audio/lpcm sample-rate=8000 sample-size-bits=16 channel-count=1; is-big-endian=false</code>.</p> <p>If the optional post-fulfillment response is specified, the messages are returned as follows. For more information, see <a href=\"https://docs.aws.amazon.com/lexv2/latest/dg/API_PostFulfillmentStatusSpecification.html\">PostFulfillmentStatusSpecification</a>.</p> <ul> <li> <p> <b>Success message</b> - Returned if the Lambda function completes successfully and the intent state is fulfilled or ready fulfillment if the message is present.</p> </li> <li> <p> <b>Failed message</b> - The failed message is returned if the Lambda function throws an exception or if the Lambda function returns a failed intent state without a message.</p> </li> <li> <p> <b>Timeout message</b> - If you don't configure a timeout message and a timeout, and the Lambda function doesn't return within 30 seconds, the timeout message is returned. If you configure a timeout, the timeout message is returned when the period times out. </p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/lexv2/latest/dg/streaming-progress.html#progress-complete.html\">Completion message</a>.</p> <p>If the optional update message is configured, it is played at the specified frequency while the Lambda function is running and the update message state is active. If the fulfillment update message is not active, the Lambda function runs with a 30 second timeout. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/lexv2/latest/dg/streaming-progress.html#progress-update.html\">Update message </a> </p> <p>The <code>StartConversation</code> operation is supported only in the following SDKs: </p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/goto/SdkForCpp/runtime.lex.v2-2020-08-07/StartConversation\">AWS SDK for C++</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/goto/SdkForJavaV2/runtime.lex.v2-2020-08-07/StartConversation\">AWS SDK for Java V2</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/goto/SdkForRubyV3/runtime.lex.v2-2020-08-07/StartConversation\">AWS SDK for Ruby V3</a> </p> </li> </ul>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p/>", "error": {"httpStatusCode": 403}, "exception": true}, "ActiveContext": {"type": "structure", "required": ["name", "timeToLive", "contextAttributes"], "members": {"name": {"shape": "ActiveContextName", "documentation": "<p>The name of the context.</p>"}, "timeToLive": {"shape": "ActiveContextTimeToLive", "documentation": "<p>Indicates the number of turns or seconds that the context is active. Once the time to live expires, the context is no longer returned in a response.</p>"}, "contextAttributes": {"shape": "ActiveContextParametersMap", "documentation": "<p>A list of contexts active for the request. A context can be activated when a previous intent is fulfilled, or by including the context in the request.</p> <p>If you don't specify a list of contexts, Amazon Lex V2 will use the current list of contexts for the session. If you specify an empty list, all contexts for the session are cleared. </p>"}}, "documentation": "<p>Contains information about the contexts that a user is using in a session. You can configure Amazon Lex V2 to set a context when an intent is fulfilled, or you can set a context using the , , or operations.</p> <p>Use a context to indicate to Amazon Lex V2 intents that should be used as follow-up intents. For example, if the active context is <code>order-fulfilled</code>, only intents that have <code>order-fulfilled</code> configured as a trigger are considered for follow up.</p>"}, "ActiveContextName": {"type": "string", "max": 100, "min": 1, "pattern": "^([A-Za-z]_?)+$"}, "ActiveContextParametersMap": {"type": "map", "key": {"shape": "ParameterName"}, "value": {"shape": "Text"}, "max": 10, "min": 0}, "ActiveContextTimeToLive": {"type": "structure", "required": ["timeToLiveInSeconds", "turnsToLive"], "members": {"timeToLiveInSeconds": {"shape": "ActiveContextTimeToLiveInSeconds", "documentation": "<p>The number of seconds that the context is active. You can specify between 5 and 86400 seconds (24 hours).</p>"}, "turnsToLive": {"shape": "ActiveContextTurnsToLive", "documentation": "<p>The number of turns that the context is active. You can specify up to 20 turns. Each request and response from the bot is a turn.</p>"}}, "documentation": "<p>The time that a context is active. You can specify the time to live in seconds or in conversation turns.</p>"}, "ActiveContextTimeToLiveInSeconds": {"type": "integer", "max": 86400, "min": 5}, "ActiveContextTurnsToLive": {"type": "integer", "max": 20, "min": 1}, "ActiveContextsList": {"type": "list", "member": {"shape": "ActiveContext"}, "max": 20, "min": 0}, "AttachmentTitle": {"type": "string", "max": 250, "min": 1}, "AttachmentUrl": {"type": "string", "max": 250, "min": 1}, "AudioChunk": {"type": "blob"}, "AudioInputEvent": {"type": "structure", "required": ["contentType"], "members": {"audioChunk": {"shape": "AudioChunk", "documentation": "<p>An encoded stream of audio.</p>"}, "contentType": {"shape": "NonEmptyString", "documentation": "<p>The encoding used for the audio chunk. You must use 8 KHz PCM 16-bit mono-channel little-endian format. The value of the field should be:</p> <p> <code>audio/lpcm; sample-rate=8000; sample-size-bits=16; channel-count=1; is-big-endian=false</code> </p>"}, "eventId": {"shape": "EventId", "documentation": "<p>A unique identifier that your application assigns to the event. You can use this to identify events in logs.</p>"}, "clientTimestampMillis": {"shape": "EpochMill<PERSON>", "documentation": "<p>A timestamp set by the client of the date and time that the event was sent to Amazon Lex V2.</p>"}}, "documentation": "<p>Represents a chunk of audio sent from the client application to Amazon Lex V2. The audio is all or part of an utterance from the user.</p> <p>Amazon Lex V2 accumulates audio chunks until it recognizes a natural pause in speech before processing the input.</p>", "event": true}, "AudioResponseEvent": {"type": "structure", "members": {"audioChunk": {"shape": "AudioChunk", "documentation": "<p>A chunk of the audio to play. </p>"}, "contentType": {"shape": "NonEmptyString", "documentation": "<p>The encoding of the audio chunk. This is the same as the encoding configure in the <code>contentType</code> field of the <code>ConfigurationEvent</code>.</p>"}, "eventId": {"shape": "EventId", "documentation": "<p>A unique identifier of the event sent by Amazon Lex V2. The identifier is in the form <code>RESPONSE-N</code>, where N is a number starting with one and incremented for each event sent by Amazon Lex V2 in the current session.</p>"}}, "documentation": "<p>An event sent from Amazon Lex V2 to your client application containing audio to play to the user. </p>", "event": true}, "BadGatewayException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p/>", "error": {"httpStatusCode": 502}, "exception": true}, "BlobStream": {"type": "blob", "streaming": true}, "Boolean": {"type": "boolean"}, "BotAliasIdentifier": {"type": "string"}, "BotIdentifier": {"type": "string", "max": 10, "min": 10, "pattern": "^[0-9a-zA-Z]+$"}, "Button": {"type": "structure", "required": ["text", "value"], "members": {"text": {"shape": "ButtonText", "documentation": "<p>The text that is displayed on the button.</p>"}, "value": {"shape": "ButtonValue", "documentation": "<p>The value returned to Amazon Lex V2 when a user chooses the button.</p>"}}, "documentation": "<p>A button that appears on a response card show to the user.</p>"}, "ButtonText": {"type": "string", "max": 50, "min": 1}, "ButtonValue": {"type": "string", "max": 50, "min": 1}, "ButtonsList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}, "max": 5, "min": 0}, "ConfidenceScore": {"type": "structure", "members": {"score": {"shape": "Double", "documentation": "<p>A score that indicates how confident Amazon Lex V2 is that an intent satisfies the user's intent. Ranges between 0.00 and 1.00. Higher scores indicate higher confidence.</p>"}}, "documentation": "<p>Provides a score that indicates the confidence that Amazon Lex V2 has that an intent is the one that satisfies the user's intent.</p>"}, "ConfigurationEvent": {"type": "structure", "required": ["responseContentType"], "members": {"requestAttributes": {"shape": "StringMap", "documentation": "<p>Request-specific information passed between the client application and Amazon Lex V2.</p> <p>The namespace <code>x-amz-lex:</code> is reserved for special attributes. Don't create any request attributes for prefix <code>x-amz-lex:</code>.</p>"}, "responseContentType": {"shape": "NonEmptyString", "documentation": "<p>The message that Amazon Lex V2 returns in the response can be either text or speech based on the <code>responseContentType</code> value.</p> <ul> <li> <p>If the value is <code>text/plain;charset=utf-8</code>, Amazon Lex V2 returns text in the response.</p> </li> <li> <p>If the value begins with <code>audio/</code>, Amazon Lex V2 returns speech in the response. Amazon Lex V2 uses Amazon Polly to generate the speech using the configuration that you specified in the <code>requestContentType</code> parameter. For example, if you specify <code>audio/mpeg</code> as the value, Amazon Lex V2 returns speech in the MPEG format.</p> </li> <li> <p>If the value is <code>audio/pcm</code>, the speech returned is audio/pcm in 16-bit, little-endian format.</p> </li> <li> <p>The following are the accepted values:</p> <ul> <li> <p>audio/mpeg</p> </li> <li> <p>audio/ogg</p> </li> <li> <p>audio/pcm</p> </li> <li> <p>audio/* (defaults to mpeg)</p> </li> <li> <p>text/plain; charset=utf-8</p> </li> </ul> </li> </ul>"}, "sessionState": {"shape": "SessionState"}, "welcomeMessages": {"shape": "Messages", "documentation": "<p>A list of messages to send to the user.</p> <p>If you set the <code>welcomeMessage</code> field, you must also set the <a href=\"https://docs.aws.amazon.com/lexv2/latest/dg/API_runtime_DialogAction.html\"> <code>DialogAction</code> </a> structure's <a href=\"https://docs.aws.amazon.com/lexv2/latest/dg/API_runtime_DialogAction.html#lexv2-Type-runtime_DialogAction-type\"> <code>type</code> </a> field.</p>"}, "disablePlayback": {"shape": "Boolean", "documentation": "<p>Determines whether Amazon Lex V2 should send audio responses to the client application. </p> <p>Set this field to false when the client is operating in a playback mode where audio responses are played to the user. If the client isn't operating in playback mode, such as a text chat application, set this to true so that Amazon Lex V2 doesn't wait for the prompt to finish playing on the client.</p>"}, "eventId": {"shape": "EventId", "documentation": "<p>A unique identifier that your application assigns to the event. You can use this to identify events in logs.</p>"}, "clientTimestampMillis": {"shape": "EpochMill<PERSON>", "documentation": "<p>A timestamp set by the client of the date and time that the event was sent to Amazon Lex V2.</p>"}}, "documentation": "<p>The initial event sent from the application to Amazon Lex V2 to configure the conversation, including session and request attributes and the response content type.</p>", "event": true}, "ConfirmationState": {"type": "string", "enum": ["Confirmed", "Denied", "None"]}, "ConflictException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p/>", "error": {"httpStatusCode": 409}, "exception": true}, "ConversationMode": {"type": "string", "enum": ["AUDIO", "TEXT"]}, "DTMFInputEvent": {"type": "structure", "required": ["inputCharacter"], "members": {"inputCharacter": {"shape": "DTMFRegex", "documentation": "<p>The DTMF character that the user pressed. The allowed characters are A - D, 0 - 9, # and *.</p>"}, "eventId": {"shape": "EventId", "documentation": "<p>A unique identifier that your application assigns to the event. You can use this to identify events in logs.</p>"}, "clientTimestampMillis": {"shape": "EpochMill<PERSON>", "documentation": "<p>A timestamp set by the client of the date and time that the event was sent to Amazon Lex V2.</p>"}}, "documentation": "<p>A DTMF character sent from the client application. DTMF characters are typically sent from a phone keypad to represent numbers. For example, you can have Amazon Lex V2 process a credit card number input from a phone.</p>", "event": true}, "DTMFRegex": {"type": "string", "max": 1, "min": 1, "pattern": "^[A-D0-9#*]{1}$", "sensitive": true}, "DeleteSessionRequest": {"type": "structure", "required": ["botId", "botAliasId", "sessionId", "localeId"], "members": {"botId": {"shape": "BotIdentifier", "documentation": "<p>The identifier of the bot that contains the session data.</p>", "location": "uri", "locationName": "botId"}, "botAliasId": {"shape": "BotAliasIdentifier", "documentation": "<p>The alias identifier in use for the bot that contains the session data.</p>", "location": "uri", "locationName": "botAliasId"}, "localeId": {"shape": "LocaleId", "documentation": "<p>The locale where the session is in use.</p>", "location": "uri", "locationName": "localeId"}, "sessionId": {"shape": "SessionId", "documentation": "<p>The identifier of the session to delete.</p>", "location": "uri", "locationName": "sessionId"}}}, "DeleteSessionResponse": {"type": "structure", "members": {"botId": {"shape": "BotIdentifier", "documentation": "<p>The identifier of the bot that contained the session data.</p>"}, "botAliasId": {"shape": "BotAliasIdentifier", "documentation": "<p>The alias identifier in use for the bot that contained the session data.</p>"}, "localeId": {"shape": "LocaleId", "documentation": "<p>The locale where the session was used.</p>"}, "sessionId": {"shape": "SessionId", "documentation": "<p>The identifier of the deleted session.</p>"}}}, "DependencyFailedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p/>", "error": {"httpStatusCode": 424}, "exception": true}, "DialogAction": {"type": "structure", "required": ["type"], "members": {"type": {"shape": "DialogActionType", "documentation": "<p>The next action that the bot should take in its interaction with the user. The possible values are:</p> <ul> <li> <p> <code>Close</code> - Indicates that there will not be a response from the user. For example, the statement \"Your order has been placed\" does not require a response.</p> </li> <li> <p> <code>ConfirmIntent</code> - The next action is asking the user if the intent is complete and ready to be fulfilled. This is a yes/no question such as \"Place the order?\"</p> </li> <li> <p> <code>Delegate</code> - The next action is determined by Amazon Lex V2.</p> </li> <li> <p> <code>ElicitIntent</code> - The next action is to elicit an intent from the user.</p> </li> <li> <p> <code>ElicitSlot</code> - The next action is to elicit a slot value from the user.</p> </li> </ul>"}, "slotToElicit": {"shape": "NonEmptyString", "documentation": "<p>The name of the slot that should be elicited from the user.</p>"}, "slotElicitationStyle": {"shape": "StyleType", "documentation": "<p>Configures the slot to use spell-by-letter or spell-by-word style. When you use a style on a slot, users can spell out their input to make it clear to your bot.</p> <ul> <li> <p>Spell by letter - \"b\" \"o\" \"b\"</p> </li> <li> <p>Spell by word - \"b as in boy\" \"o as in oscar\" \"b as in boy\"</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/lexv2/latest/dg/using-spelling.html\"> Using spelling to enter slot values </a>.</p>"}, "subSlotToElicit": {"shape": "ElicitSubSlot", "documentation": "<p>The name of the constituent sub slot of the composite slot specified in slotToElicit that should be elicited from the user.</p>"}}, "documentation": "<p>The next action that Amazon Lex V2 should take.</p>"}, "DialogActionType": {"type": "string", "enum": ["Close", "ConfirmIntent", "Delegate", "ElicitIntent", "ElicitSlot", "None"]}, "DisconnectionEvent": {"type": "structure", "members": {"eventId": {"shape": "EventId", "documentation": "<p>A unique identifier that your application assigns to the event. You can use this to identify events in logs.</p>"}, "clientTimestampMillis": {"shape": "EpochMill<PERSON>", "documentation": "<p>A timestamp set by the client of the date and time that the event was sent to Amazon Lex V2.</p>"}}, "documentation": "<p>A notification from the client that it is disconnecting from Amazon Lex V2. Sending a <code>DisconnectionEvent</code> event is optional, but can help identify a conversation in logs.</p>", "event": true}, "Double": {"type": "double"}, "ElicitSubSlot": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "NonEmptyString", "documentation": "<p>The name of the slot that should be elicited from the user.</p>"}, "subSlotToElicit": {"shape": "ElicitSubSlot", "documentation": "<p>The field is not supported.</p>"}}, "documentation": "<p>The specific constituent sub slot of the composite slot to elicit in dialog action.</p>"}, "EpochMillis": {"type": "long"}, "EventId": {"type": "string", "max": 100, "min": 2, "pattern": "[0-9a-zA-Z._:-]+"}, "GetSessionRequest": {"type": "structure", "required": ["botId", "botAliasId", "localeId", "sessionId"], "members": {"botId": {"shape": "BotIdentifier", "documentation": "<p>The identifier of the bot that contains the session data.</p>", "location": "uri", "locationName": "botId"}, "botAliasId": {"shape": "BotAliasIdentifier", "documentation": "<p>The alias identifier in use for the bot that contains the session data.</p>", "location": "uri", "locationName": "botAliasId"}, "localeId": {"shape": "LocaleId", "documentation": "<p>The locale where the session is in use.</p>", "location": "uri", "locationName": "localeId"}, "sessionId": {"shape": "SessionId", "documentation": "<p>The identifier of the session to return.</p>", "location": "uri", "locationName": "sessionId"}}}, "GetSessionResponse": {"type": "structure", "members": {"sessionId": {"shape": "NonEmptyString", "documentation": "<p>The identifier of the returned session.</p>"}, "messages": {"shape": "Messages", "documentation": "<p>A list of messages that were last sent to the user. The messages are ordered based on the order that your returned the messages from your Lambda function or the order that messages are defined in the bot. </p>"}, "interpretations": {"shape": "Interpretations", "documentation": "<p>A list of intents that Amazon Lex V2 determined might satisfy the user's utterance. </p> <p>Each interpretation includes the intent, a score that indicates how confident Amazon Lex V2 is that the interpretation is the correct one, and an optional sentiment response that indicates the sentiment expressed in the utterance.</p>"}, "sessionState": {"shape": "SessionState", "documentation": "<p>Represents the current state of the dialog between the user and the bot.</p> <p>You can use this to determine the progress of the conversation and what the next action might be.</p>"}}}, "HeartbeatEvent": {"type": "structure", "members": {"eventId": {"shape": "EventId", "documentation": "<p>A unique identifier of the event sent by Amazon Lex V2. The identifier is in the form <code>RESPONSE-N</code>, where N is a number starting with one and incremented for each event sent by Amazon Lex V2 in the current session.</p>"}}, "documentation": "<p>Event that Amazon Lex V2 sends to indicate that the stream is still open between the client application and Amazon Lex V2 </p>", "event": true}, "ImageResponseCard": {"type": "structure", "required": ["title"], "members": {"title": {"shape": "AttachmentTitle", "documentation": "<p>The title to display on the response card. The format of the title is determined by the platform displaying the response card.</p>"}, "subtitle": {"shape": "AttachmentTitle", "documentation": "<p>The subtitle to display on the response card. The format of the subtitle is determined by the platform displaying the response card.</p>"}, "imageUrl": {"shape": "AttachmentUrl", "documentation": "<p>The URL of an image to display on the response card. The image URL must be publicly available so that the platform displaying the response card has access to the image.</p>"}, "buttons": {"shape": "ButtonsList", "documentation": "<p>A list of buttons that should be displayed on the response card. The arrangement of the buttons is determined by the platform that displays the button.</p>"}}, "documentation": "<p>A card that is shown to the user by a messaging platform. You define the contents of the card, the card is displayed by the platform. </p> <p>When you use a response card, the response from the user is constrained to the text associated with a button on the card.</p>"}, "InputMode": {"type": "string", "enum": ["Text", "Speech", "DTMF"]}, "Intent": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "NonEmptyString", "documentation": "<p>The name of the intent.</p>"}, "slots": {"shape": "Slots", "documentation": "<p>A map of all of the slots for the intent. The name of the slot maps to the value of the slot. If a slot has not been filled, the value is null.</p>"}, "state": {"shape": "IntentState", "documentation": "<p>Contains fulfillment information for the intent. </p>"}, "confirmationState": {"shape": "ConfirmationState", "documentation": "<p>Contains information about whether fulfillment of the intent has been confirmed.</p>"}}, "documentation": "<p>The current intent that Amazon Lex V2 is attempting to fulfill.</p>"}, "IntentResultEvent": {"type": "structure", "members": {"inputMode": {"shape": "InputMode", "documentation": "<p>Indicates whether the input to the operation was text or speech.</p>"}, "interpretations": {"shape": "Interpretations", "documentation": "<p>A list of intents that Amazon Lex V2 determined might satisfy the user's utterance.</p> <p>Each interpretation includes the intent, a score that indicates how confident Amazon Lex V2 is that the interpretation is the correct one, and an optional sentiment response that indicates the sentiment expressed in the utterance.</p>"}, "sessionState": {"shape": "SessionState"}, "requestAttributes": {"shape": "StringMap", "documentation": "<p>The attributes sent in the request.</p>"}, "sessionId": {"shape": "SessionId", "documentation": "<p>The identifier of the session in use.</p>"}, "eventId": {"shape": "EventId", "documentation": "<p>A unique identifier of the event sent by Amazon Lex V2. The identifier is in the form <code>RESPONSE-N</code>, where N is a number starting with one and incremented for each event sent by Amazon Lex V2 in the current session.</p>"}, "recognizedBotMember": {"shape": "RecognizedBotMember", "documentation": "<p>The bot member that is processing the intent.</p>"}}, "documentation": "<p>Contains the current state of the conversation between the client application and Amazon Lex V2.</p>", "event": true}, "IntentState": {"type": "string", "enum": ["Failed", "Fulfilled", "InProgress", "ReadyForFulfillment", "Waiting", "FulfillmentInProgress"]}, "InternalServerException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p/>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "Interpretation": {"type": "structure", "members": {"nluConfidence": {"shape": "ConfidenceScore", "documentation": "<p>Determines the threshold where Amazon Lex V2 will insert the <code>AMAZON.FallbackIntent</code>, <code>AMAZON.KendraSearchIntent</code>, or both when returning alternative intents in a response. <code>AMAZON.FallbackIntent</code> and <code>AMAZON.KendraSearchIntent</code> are only inserted if they are configured for the bot.</p>"}, "sentimentResponse": {"shape": "SentimentResponse", "documentation": "<p>The sentiment expressed in an utterance. </p> <p>When the bot is configured to send utterances to Amazon Comprehend for sentiment analysis, this field contains the result of the analysis.</p>"}, "intent": {"shape": "Intent", "documentation": "<p>A list of intents that might satisfy the user's utterance. The intents are ordered by the confidence score.</p>"}}, "documentation": "<p>An intent that Amazon Lex V2 determined might satisfy the user's utterance. The intents are ordered by the confidence score. </p>"}, "Interpretations": {"type": "list", "member": {"shape": "Interpretation"}, "max": 5}, "LocaleId": {"type": "string", "min": 1}, "Message": {"type": "structure", "required": ["contentType"], "members": {"content": {"shape": "Text", "documentation": "<p>The text of the message.</p>"}, "contentType": {"shape": "MessageContentType", "documentation": "<p>Indicates the type of response.</p>"}, "imageResponseCard": {"shape": "ImageResponseCard"}}, "documentation": "<p>Container for text that is returned to the customer..</p>"}, "MessageContentType": {"type": "string", "enum": ["CustomPayload", "ImageResponseCard", "PlainText", "SSML"]}, "Messages": {"type": "list", "member": {"shape": "Message"}, "max": 10}, "Name": {"type": "string", "max": 100, "min": 1, "pattern": "^([0-9a-zA-Z][_-]?)+$"}, "NonEmptyString": {"type": "string", "min": 1}, "ParameterName": {"type": "string", "max": 100, "min": 1}, "PlaybackCompletionEvent": {"type": "structure", "members": {"eventId": {"shape": "EventId", "documentation": "<p>A unique identifier that your application assigns to the event. You can use this to identify events in logs.</p>"}, "clientTimestampMillis": {"shape": "EpochMill<PERSON>", "documentation": "<p>A timestamp set by the client of the date and time that the event was sent to Amazon Lex V2.</p>"}}, "documentation": "<p>Event sent from the client application to Amazon Lex V2 to indicate that playback of audio is complete and that Amazon Lex V2 should start processing the user's input.</p>", "event": true}, "PlaybackInterruptionEvent": {"type": "structure", "members": {"eventReason": {"shape": "PlaybackInterruptionReason", "documentation": "<p>Indicates the type of user input that Amazon Lex V2 detected.</p>"}, "causedByEventId": {"shape": "EventId", "documentation": "<p>The identifier of the event that contained the audio, DTMF, or text that caused the interruption.</p>"}, "eventId": {"shape": "EventId", "documentation": "<p>A unique identifier of the event sent by Amazon Lex V2. The identifier is in the form <code>RESPONSE-N</code>, where N is a number starting with one and incremented for each event sent by Amazon Lex V2 in the current session.</p>"}}, "documentation": "<p>Event sent from Amazon Lex V2 to indicate to the client application should stop playback of audio. For example, if the client is playing a prompt that asks for the user's telephone number, the user might start to say the phone number before the prompt is complete. Amazon Lex V2 sends this event to the client application to indicate that the user is responding and that Amazon Lex V2 is processing their input.</p>", "event": true}, "PlaybackInterruptionReason": {"type": "string", "enum": ["DTMF_START_DETECTED", "TEXT_DETECTED", "VOICE_START_DETECTED"]}, "PutSessionRequest": {"type": "structure", "required": ["botId", "botAliasId", "localeId", "sessionState", "sessionId"], "members": {"botId": {"shape": "BotIdentifier", "documentation": "<p>The identifier of the bot that receives the session data.</p>", "location": "uri", "locationName": "botId"}, "botAliasId": {"shape": "BotAliasIdentifier", "documentation": "<p>The alias identifier of the bot that receives the session data.</p>", "location": "uri", "locationName": "botAliasId"}, "localeId": {"shape": "LocaleId", "documentation": "<p>The locale where the session is in use.</p>", "location": "uri", "locationName": "localeId"}, "sessionId": {"shape": "SessionId", "documentation": "<p>The identifier of the session that receives the session data.</p>", "location": "uri", "locationName": "sessionId"}, "messages": {"shape": "Messages", "documentation": "<p>A list of messages to send to the user. Messages are sent in the order that they are defined in the list.</p>"}, "sessionState": {"shape": "SessionState", "documentation": "<p>Sets the state of the session with the user. You can use this to set the current intent, attributes, context, and dialog action. Use the dialog action to determine the next step that Amazon Lex V2 should use in the conversation with the user.</p>"}, "requestAttributes": {"shape": "StringMap", "documentation": "<p>Request-specific information passed between Amazon Lex V2 and the client application.</p> <p>The namespace <code>x-amz-lex:</code> is reserved for special attributes. Don't create any request attributes with the prefix <code>x-amz-lex:</code>.</p>"}, "responseContentType": {"shape": "NonEmptyString", "documentation": "<p>The message that Amazon Lex V2 returns in the response can be either text or speech depending on the value of this parameter. </p> <ul> <li> <p>If the value is <code>text/plain; charset=utf-8</code>, Amazon Lex V2 returns text in the response.</p> </li> </ul>", "location": "header", "locationName": "ResponseContentType"}}}, "PutSessionResponse": {"type": "structure", "members": {"contentType": {"shape": "NonEmptyString", "documentation": "<p>The type of response. Same as the type specified in the <code>responseContentType</code> field in the request.</p>", "location": "header", "locationName": "Content-Type"}, "messages": {"shape": "NonEmptyString", "documentation": "<p>A list of messages that were last sent to the user. The messages are ordered based on how you return the messages from you Lambda function or the order that the messages are defined in the bot.</p>", "location": "header", "locationName": "x-amz-lex-messages"}, "sessionState": {"shape": "NonEmptyString", "documentation": "<p>Represents the current state of the dialog between the user and the bot.</p> <p>Use this to determine the progress of the conversation and what the next action may be.</p>", "location": "header", "locationName": "x-amz-lex-session-state"}, "requestAttributes": {"shape": "NonEmptyString", "documentation": "<p>Request-specific information passed between the client application and Amazon Lex V2. These are the same as the <code>requestAttribute</code> parameter in the call to the <code>PutSession</code> operation.</p>", "location": "header", "locationName": "x-amz-lex-request-attributes"}, "sessionId": {"shape": "SessionId", "documentation": "<p>The identifier of the session that received the data.</p>", "location": "header", "locationName": "x-amz-lex-session-id"}, "audioStream": {"shape": "BlobStream", "documentation": "<p>If the requested content type was audio, the audio version of the message to convey to the user.</p>"}}, "payload": "audioStream"}, "RecognizeTextRequest": {"type": "structure", "required": ["botId", "botAliasId", "localeId", "text", "sessionId"], "members": {"botId": {"shape": "BotIdentifier", "documentation": "<p>The identifier of the bot that processes the request.</p>", "location": "uri", "locationName": "botId"}, "botAliasId": {"shape": "BotAliasIdentifier", "documentation": "<p>The alias identifier in use for the bot that processes the request.</p>", "location": "uri", "locationName": "botAliasId"}, "localeId": {"shape": "LocaleId", "documentation": "<p>The locale where the session is in use.</p>", "location": "uri", "locationName": "localeId"}, "sessionId": {"shape": "SessionId", "documentation": "<p>The identifier of the user session that is having the conversation.</p>", "location": "uri", "locationName": "sessionId"}, "text": {"shape": "Text", "documentation": "<p>The text that the user entered. Amazon Lex V2 interprets this text.</p>"}, "sessionState": {"shape": "SessionState", "documentation": "<p>The current state of the dialog between the user and the bot.</p>"}, "requestAttributes": {"shape": "StringMap", "documentation": "<p>Request-specific information passed between the client application and Amazon Lex V2 </p> <p>The namespace <code>x-amz-lex:</code> is reserved for special attributes. Don't create any request attributes with the prefix <code>x-amz-lex:</code>.</p>"}}}, "RecognizeTextResponse": {"type": "structure", "members": {"messages": {"shape": "Messages", "documentation": "<p>A list of messages last sent to the user. The messages are ordered based on the order that you returned the messages from your Lambda function or the order that the messages are defined in the bot.</p>"}, "sessionState": {"shape": "SessionState", "documentation": "<p>Represents the current state of the dialog between the user and the bot. </p> <p>Use this to determine the progress of the conversation and what the next action may be.</p>"}, "interpretations": {"shape": "Interpretations", "documentation": "<p>A list of intents that Amazon Lex V2 determined might satisfy the user's utterance. </p> <p>Each interpretation includes the intent, a score that indicates now confident Amazon Lex V2 is that the interpretation is the correct one, and an optional sentiment response that indicates the sentiment expressed in the utterance.</p>"}, "requestAttributes": {"shape": "StringMap", "documentation": "<p>The attributes sent in the request.</p>"}, "sessionId": {"shape": "SessionId", "documentation": "<p>The identifier of the session in use.</p>"}, "recognizedBotMember": {"shape": "RecognizedBotMember", "documentation": "<p>The bot member that recognized the text.</p>"}}}, "RecognizeUtteranceRequest": {"type": "structure", "required": ["botId", "botAliasId", "localeId", "requestContentType", "sessionId"], "members": {"botId": {"shape": "BotIdentifier", "documentation": "<p>The identifier of the bot that should receive the request.</p>", "location": "uri", "locationName": "botId"}, "botAliasId": {"shape": "BotAliasIdentifier", "documentation": "<p>The alias identifier in use for the bot that should receive the request.</p>", "location": "uri", "locationName": "botAliasId"}, "localeId": {"shape": "LocaleId", "documentation": "<p>The locale where the session is in use.</p>", "location": "uri", "locationName": "localeId"}, "sessionId": {"shape": "SessionId", "documentation": "<p>The identifier of the session in use.</p>", "location": "uri", "locationName": "sessionId"}, "sessionState": {"shape": "SensitiveNonEmptyString", "documentation": "<p>Sets the state of the session with the user. You can use this to set the current intent, attributes, context, and dialog action. Use the dialog action to determine the next step that Amazon Lex V2 should use in the conversation with the user.</p> <p>The <code>sessionState</code> field must be compressed using gzip and then base64 encoded before sending to Amazon Lex V2.</p>", "location": "header", "locationName": "x-amz-lex-session-state"}, "requestAttributes": {"shape": "SensitiveNonEmptyString", "documentation": "<p>Request-specific information passed between the client application and Amazon Lex V2 </p> <p>The namespace <code>x-amz-lex:</code> is reserved for special attributes. Don't create any request attributes for prefix <code>x-amz-lex:</code>.</p> <p>The <code>requestAttributes</code> field must be compressed using gzip and then base64 encoded before sending to Amazon Lex V2.</p>", "location": "header", "locationName": "x-amz-lex-request-attributes"}, "requestContentType": {"shape": "NonEmptyString", "documentation": "<p>Indicates the format for audio input or that the content is text. The header must start with one of the following prefixes:</p> <ul> <li> <p>PCM format, audio data must be in little-endian byte order.</p> <ul> <li> <p>audio/l16; rate=16000; channels=1</p> </li> <li> <p>audio/x-l16; sample-rate=16000; channel-count=1</p> </li> <li> <p>audio/lpcm; sample-rate=8000; sample-size-bits=16; channel-count=1; is-big-endian=false</p> </li> </ul> </li> <li> <p>Opus format</p> <ul> <li> <p>audio/x-cbr-opus-with-preamble;preamble-size=0;bit-rate=256000;frame-size-milliseconds=4</p> </li> </ul> </li> <li> <p>Text format</p> <ul> <li> <p>text/plain; charset=utf-8</p> </li> </ul> </li> </ul>", "location": "header", "locationName": "Content-Type"}, "responseContentType": {"shape": "NonEmptyString", "documentation": "<p>The message that Amazon Lex V2 returns in the response can be either text or speech based on the <code>responseContentType</code> value.</p> <ul> <li> <p>If the value is <code>text/plain;charset=utf-8</code>, Amazon Lex V2 returns text in the response.</p> </li> <li> <p>If the value begins with <code>audio/</code>, Amazon Lex V2 returns speech in the response. Amazon Lex V2 uses Amazon Polly to generate the speech using the configuration that you specified in the <code>responseContentType</code> parameter. For example, if you specify <code>audio/mpeg</code> as the value, Amazon Lex V2 returns speech in the MPEG format.</p> </li> <li> <p>If the value is <code>audio/pcm</code>, the speech returned is <code>audio/pcm</code> at 16 KHz in 16-bit, little-endian format.</p> </li> <li> <p>The following are the accepted values:</p> <ul> <li> <p>audio/mpeg</p> </li> <li> <p>audio/ogg</p> </li> <li> <p>audio/pcm (16 KHz)</p> </li> <li> <p>audio/* (defaults to mpeg)</p> </li> <li> <p>text/plain; charset=utf-8</p> </li> </ul> </li> </ul>", "location": "header", "locationName": "Response-Content-Type"}, "inputStream": {"shape": "BlobStream", "documentation": "<p>User input in PCM or Opus audio format or text format as described in the <code>requestContentType</code> parameter.</p>"}}, "payload": "inputStream"}, "RecognizeUtteranceResponse": {"type": "structure", "members": {"inputMode": {"shape": "NonEmptyString", "documentation": "<p>Indicates whether the input mode to the operation was text or speech. </p>", "location": "header", "locationName": "x-amz-lex-input-mode"}, "contentType": {"shape": "NonEmptyString", "documentation": "<p>Content type as specified in the <code>responseContentType</code> in the request.</p>", "location": "header", "locationName": "Content-Type"}, "messages": {"shape": "NonEmptyString", "documentation": "<p>A list of messages that were last sent to the user. The messages are ordered based on the order that you returned the messages from your Lambda function or the order that the messages are defined in the bot.</p> <p>The <code>messages</code> field is compressed with gzip and then base64 encoded. Before you can use the contents of the field, you must decode and decompress the contents. See the example for a simple function to decode and decompress the contents.</p>", "location": "header", "locationName": "x-amz-lex-messages"}, "interpretations": {"shape": "NonEmptyString", "documentation": "<p>A list of intents that Amazon Lex V2 determined might satisfy the user's utterance.</p> <p>Each interpretation includes the intent, a score that indicates how confident Amazon Lex V2 is that the interpretation is the correct one, and an optional sentiment response that indicates the sentiment expressed in the utterance.</p> <p>The <code>interpretations</code> field is compressed with gzip and then base64 encoded. Before you can use the contents of the field, you must decode and decompress the contents. See the example for a simple function to decode and decompress the contents.</p>", "location": "header", "locationName": "x-amz-lex-interpretations"}, "sessionState": {"shape": "NonEmptyString", "documentation": "<p>Represents the current state of the dialog between the user and the bot.</p> <p>Use this to determine the progress of the conversation and what the next action might be.</p> <p>The <code>sessionState</code> field is compressed with gzip and then base64 encoded. Before you can use the contents of the field, you must decode and decompress the contents. See the example for a simple function to decode and decompress the contents.</p>", "location": "header", "locationName": "x-amz-lex-session-state"}, "requestAttributes": {"shape": "NonEmptyString", "documentation": "<p>The attributes sent in the request.</p> <p>The <code>requestAttributes</code> field is compressed with gzip and then base64 encoded. Before you can use the contents of the field, you must decode and decompress the contents.</p>", "location": "header", "locationName": "x-amz-lex-request-attributes"}, "sessionId": {"shape": "SessionId", "documentation": "<p>The identifier of the session in use.</p>", "location": "header", "locationName": "x-amz-lex-session-id"}, "inputTranscript": {"shape": "NonEmptyString", "documentation": "<p>The text used to process the request.</p> <p>If the input was an audio stream, the <code>inputTranscript</code> field contains the text extracted from the audio stream. This is the text that is actually processed to recognize intents and slot values. You can use this information to determine if Amazon Lex V2 is correctly processing the audio that you send.</p> <p>The <code>inputTranscript</code> field is compressed with gzip and then base64 encoded. Before you can use the contents of the field, you must decode and decompress the contents. See the example for a simple function to decode and decompress the contents.</p>", "location": "header", "locationName": "x-amz-lex-input-transcript"}, "audioStream": {"shape": "BlobStream", "documentation": "<p>The prompt or statement to send to the user. This is based on the bot configuration and context. For example, if Amazon Lex V2 did not understand the user intent, it sends the <code>clarificationPrompt</code> configured for the bot. If the intent requires confirmation before taking the fulfillment action, it sends the <code>confirmationPrompt</code>. Another example: Suppose that the Lambda function successfully fulfilled the intent, and sent a message to convey to the user. Then Amazon Lex V2 sends that message in the response.</p>"}, "recognizedBotMember": {"shape": "NonEmptyString", "documentation": "<p>The bot member that recognized the utterance.</p>", "location": "header", "locationName": "x-amz-lex-recognized-bot-member"}}, "payload": "audioStream"}, "RecognizedBotMember": {"type": "structure", "required": ["botId"], "members": {"botId": {"shape": "BotIdentifier", "documentation": "<p>The identifier of the bot member that processes the request.</p>"}, "botName": {"shape": "Name", "documentation": "<p>The name of the bot member that processes the request.</p>"}}, "documentation": "<p>The bot member that processes the request.</p>"}, "ResourceNotFoundException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p/>", "error": {"httpStatusCode": 404}, "exception": true}, "RuntimeHintDetails": {"type": "structure", "members": {"runtimeHintValues": {"shape": "RuntimeHintValuesList", "documentation": "<p>One or more strings that Amazon Lex V2 should look for in the input to the bot. Each phrase is given preference when deciding on slot values.</p>"}, "subSlotHints": {"shape": "SlotHintsSlotMap", "documentation": "<p>A map of constituent sub slot names inside a composite slot in the intent and the phrases that should be added for each sub slot. Inside each composite slot hints, this structure provides a mechanism to add granular sub slot phrases. Only sub slot hints are supported for composite slots. The intent name, composite slot name and the constituent sub slot names must exist.</p>"}}, "documentation": "<p>Provides an array of phrases that should be given preference when resolving values for a slot.</p>"}, "RuntimeHintPhrase": {"type": "string", "max": 140, "min": 1}, "RuntimeHintValue": {"type": "structure", "required": ["phrase"], "members": {"phrase": {"shape": "RuntimeHintPhrase", "documentation": "<p>The phrase that Amazon Lex V2 should look for in the user's input to the bot.</p>"}}, "documentation": "<p>Provides the phrase that Amazon Lex V2 should look for in the user's input to the bot.</p>"}, "RuntimeHintValuesList": {"type": "list", "member": {"shape": "RuntimeHintValue"}, "max": 100, "min": 1}, "RuntimeHints": {"type": "structure", "members": {"slotHints": {"shape": "SlotHintsIntentMap", "documentation": "<p>A list of the slots in the intent that should have runtime hints added, and the phrases that should be added for each slot.</p> <p>The first level of the <code>slotHints</code> map is the name of the intent. The second level is the name of the slot within the intent. For more information, see <a href=\"https://docs.aws.amazon.com/lexv2/latest/dg/using-hints.html\">Using hints to improve accuracy</a>.</p> <p>The intent name and slot name must exist.</p>"}}, "documentation": "<p>You can provide Amazon Lex V2 with hints to the phrases that a customer is likely to use for a slot. When a slot with hints is resolved, the phrases in the runtime hints are preferred in the resolution. You can provide hints for a maximum of 100 intents. You can provide a maximum of 100 slots.</p> <p>Before you can use runtime hints with an existing bot, you must first rebuild the bot.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/lexv2/latest/dg/using-hints.html\">Using runtime hints to improve recognition of slot values</a>.</p>"}, "SensitiveNonEmptyString": {"type": "string", "sensitive": true}, "SentimentResponse": {"type": "structure", "members": {"sentiment": {"shape": "SentimentType", "documentation": "<p>The overall sentiment expressed in the user's response. This is the sentiment most likely expressed by the user based on the analysis by Amazon Comprehend.</p>"}, "sentimentScore": {"shape": "SentimentScore"}}, "documentation": "<p>Provides information about the sentiment expressed in a user's response in a conversation. Sentiments are determined using Amazon Comprehend. Sentiments are only returned if they are enabled for the bot.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/comprehend/latest/dg/how-sentiment.html\"> Determine Sentiment </a> in the <i>Amazon Comprehend developer guide</i>.</p>"}, "SentimentScore": {"type": "structure", "members": {"positive": {"shape": "Double", "documentation": "<p>The level of confidence that Amazon Comprehend has in the accuracy of its detection of the <code>POSITIVE</code> sentiment.</p>"}, "negative": {"shape": "Double", "documentation": "<p>The level of confidence that Amazon Comprehend has in the accuracy of its detection of the <code>NEGATIVE</code> sentiment.</p>"}, "neutral": {"shape": "Double", "documentation": "<p>The level of confidence that Amazon Comprehend has in the accuracy of its detection of the <code>NEUTRAL</code> sentiment.</p>"}, "mixed": {"shape": "Double", "documentation": "<p>The level of confidence that Amazon Comprehend has in the accuracy of its detection of the <code>MIXED</code> sentiment.</p>"}}, "documentation": "<p>The individual sentiment responses for the utterance.</p>"}, "SentimentType": {"type": "string", "enum": ["MIXED", "NEGATIVE", "NEUTRAL", "POSITIVE"]}, "SessionId": {"type": "string", "max": 100, "min": 2, "pattern": "[0-9a-zA-Z._:-]+"}, "SessionState": {"type": "structure", "members": {"dialogAction": {"shape": "DialogAction", "documentation": "<p>The next step that Amazon Lex V2 should take in the conversation with a user.</p>"}, "intent": {"shape": "Intent", "documentation": "<p>The active intent that Amazon Lex V2 is processing.</p>"}, "activeContexts": {"shape": "ActiveContextsList", "documentation": "<p>One or more contexts that indicate to Amazon Lex V2 the context of a request. When a context is active, Amazon Lex V2 considers intents with the matching context as a trigger as the next intent in a session.</p>"}, "sessionAttributes": {"shape": "StringMap", "documentation": "<p>Map of key/value pairs representing session-specific context information. It contains application information passed between Amazon Lex V2 and a client application.</p>"}, "originatingRequestId": {"shape": "NonEmptyString", "documentation": "<p>A unique identifier for a specific request.</p>"}, "runtimeHints": {"shape": "RuntimeHints", "documentation": "<p>Hints for phrases that a customer is likely to use for a slot. Amazon Lex V2 uses the hints to help determine the correct value of a slot.</p>"}}, "documentation": "<p>The state of the user's session with Amazon Lex V2.</p>"}, "Shape": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "List", "Composite"]}, "Slot": {"type": "structure", "members": {"value": {"shape": "Value", "documentation": "<p>The current value of the slot.</p>"}, "shape": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>When the <code>shape</code> value is <code>List</code>, it indicates that the <code>values</code> field contains a list of slot values. When the value is <code>Scalar</code>, it indicates that the <code>value</code> field contains a single value.</p>"}, "values": {"shape": "Values", "documentation": "<p>A list of one or more values that the user provided for the slot. For example, if a for a slot that elicits pizza toppings, the values might be \"pepperoni\" and \"pineapple.\" </p>"}, "subSlots": {"shape": "Slots", "documentation": "<p>The constituent sub slots of a composite slot.</p>"}}, "documentation": "<p>A value that Amazon Lex V2 uses to fulfill an intent. </p>"}, "SlotHintsIntentMap": {"type": "map", "key": {"shape": "Name"}, "value": {"shape": "SlotHintsSlotMap"}}, "SlotHintsSlotMap": {"type": "map", "key": {"shape": "Name"}, "value": {"shape": "RuntimeHintDetails"}}, "Slots": {"type": "map", "key": {"shape": "NonEmptyString"}, "value": {"shape": "Slot"}}, "StartConversationRequest": {"type": "structure", "required": ["botId", "botAliasId", "localeId", "requestEventStream", "sessionId"], "members": {"botId": {"shape": "BotIdentifier", "documentation": "<p>The identifier of the bot to process the request.</p>", "location": "uri", "locationName": "botId"}, "botAliasId": {"shape": "BotAliasIdentifier", "documentation": "<p>The alias identifier in use for the bot that processes the request.</p>", "location": "uri", "locationName": "botAliasId"}, "localeId": {"shape": "LocaleId", "documentation": "<p>The locale where the session is in use.</p>", "location": "uri", "locationName": "localeId"}, "sessionId": {"shape": "SessionId", "documentation": "<p>The identifier of the user session that is having the conversation.</p>", "location": "uri", "locationName": "sessionId"}, "conversationMode": {"shape": "ConversationMode", "documentation": "<p>The conversation type that you are using the Amazon Lex V2. If the conversation mode is <code>AUDIO</code> you can send both audio and DTMF information. If the mode is <code>TEXT</code> you can only send text.</p>", "location": "header", "locationName": "x-amz-lex-conversation-mode"}, "requestEventStream": {"shape": "StartConversationRequestEventStream", "documentation": "<p>Represents the stream of events to Amazon Lex V2 from your application. The events are encoded as HTTP/2 data frames.</p>"}}, "payload": "requestEventStream"}, "StartConversationRequestEventStream": {"type": "structure", "members": {"ConfigurationEvent": {"shape": "ConfigurationEvent", "documentation": "<p>Configuration information sent from your client application to Amazon Lex V2</p>"}, "AudioInputEvent": {"shape": "AudioInputEvent", "documentation": "<p>Speech audio sent from your client application to Amazon Lex V2. Audio starts accumulating when Amazon Lex V2 identifies a voice and continues until a natural pause in the speech is found before processing.</p>"}, "DTMFInputEvent": {"shape": "DTMFInputEvent", "documentation": "<p>DTMF information sent to Amazon Lex V2 by your application. Amazon Lex V2 accumulates the DMTF information from when the user sends the first character and ends</p> <ul> <li> <p>when there's a pause longer that the value configured for the end timeout.</p> </li> <li> <p>when there's a digit that is the configured end character.</p> </li> <li> <p>when Amazon Lex V2 accumulates characters equal to the maximum DTMF character configuration.</p> </li> </ul>"}, "TextInputEvent": {"shape": "TextInputEvent", "documentation": "<p>Text sent from your client application to Amazon Lex V2. Each <code>TextInputEvent</code> is processed individually.</p>"}, "PlaybackCompletionEvent": {"shape": "PlaybackCompletionEvent", "documentation": "<p>Event sent from the client application to Amazon Lex V2 to indicate that it has finished playing audio and that Amazon Lex V2 should start listening for user input.</p>"}, "DisconnectionEvent": {"shape": "DisconnectionEvent", "documentation": "<p>Event sent from the client application to indicate to Amazon Lex V2 that the conversation is over.</p>"}}, "documentation": "<p>Represents a stream of events between your application and Amazon Lex V2.</p>", "eventstream": true}, "StartConversationResponse": {"type": "structure", "members": {"responseEventStream": {"shape": "StartConversationResponseEventStream", "documentation": "<p>Represents the stream of events from Amazon Lex V2 to your application. The events are encoded as HTTP/2 data frames.</p>"}}, "payload": "responseEventStream"}, "StartConversationResponseEventStream": {"type": "structure", "members": {"PlaybackInterruptionEvent": {"shape": "PlaybackInterruptionEvent"}, "TranscriptEvent": {"shape": "TranscriptEvent"}, "IntentResultEvent": {"shape": "IntentResultEvent", "documentation": "<p>Event sent from Amazon Lex V2 to the client application containing the current state of the conversation between the user and Amazon Lex V2.</p>"}, "TextResponseEvent": {"shape": "TextResponseEvent"}, "AudioResponseEvent": {"shape": "AudioResponseEvent"}, "HeartbeatEvent": {"shape": "HeartbeatEvent"}, "AccessDeniedException": {"shape": "AccessDeniedException", "documentation": "<p>Exception thrown when the credentials passed with the request are invalid or expired. Also thrown when the credentials in the request do not have permission to access the <code>StartConversation</code> operation.</p>"}, "ResourceNotFoundException": {"shape": "ResourceNotFoundException", "documentation": "<p>Exception thrown if one of the input parameters points to a resource that does not exist. For example, if the bot ID specified does not exist.</p>"}, "ValidationException": {"shape": "ValidationException", "documentation": "<p>Exception thrown when one or more parameters could not be validated. The <code>message</code> contains the name of the field that isn't valid.</p>"}, "ThrottlingException": {"shape": "ThrottlingException", "documentation": "<p>Exception thrown when your application exceeds the maximum number of concurrent requests. </p>"}, "InternalServerException": {"shape": "InternalServerException", "documentation": "<p>An error occurred with Amazon Lex V2.</p>"}, "ConflictException": {"shape": "ConflictException", "documentation": "<p>Exception thrown when two clients are using the same AWS account, Amazon Lex V2 bot, and session ID.</p>"}, "DependencyFailedException": {"shape": "DependencyFailedException"}, "BadGatewayException": {"shape": "BadGatewayException"}}, "documentation": "<p>Represents a stream of events between Amazon Lex V2 and your application.</p>", "eventstream": true}, "String": {"type": "string"}, "StringList": {"type": "list", "member": {"shape": "NonEmptyString"}}, "StringMap": {"type": "map", "key": {"shape": "NonEmptyString"}, "value": {"shape": "String"}}, "StyleType": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "SpellByLetter", "SpellByWord"]}, "Text": {"type": "string", "max": 1024, "min": 1, "sensitive": true}, "TextInputEvent": {"type": "structure", "required": ["text"], "members": {"text": {"shape": "Text", "documentation": "<p>The text from the user. Amazon Lex V2 processes this as a complete statement.</p>"}, "eventId": {"shape": "EventId", "documentation": "<p>A unique identifier that your application assigns to the event. You can use this to identify events in logs.</p>"}, "clientTimestampMillis": {"shape": "EpochMill<PERSON>", "documentation": "<p>A timestamp set by the client of the date and time that the event was sent to Amazon Lex V2.</p>"}}, "documentation": "<p>The event sent from your client application to Amazon Lex V2 with text input from the user.</p>", "event": true}, "TextResponseEvent": {"type": "structure", "members": {"messages": {"shape": "Messages", "documentation": "<p>A list of messages to send to the user. Messages are ordered based on the order that you returned the messages from your Lambda function or the order that the messages are defined in the bot.</p>"}, "eventId": {"shape": "EventId", "documentation": "<p>A unique identifier of the event sent by Amazon Lex V2. The identifier is in the form <code>RESPONSE-N</code>, where N is a number starting with one and incremented for each event sent by Amazon Lex V2 in the current session.</p>"}}, "documentation": "<p>The event sent from Amazon Lex V2 to your application with text to present to the user.</p>", "event": true}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p/>", "error": {"httpStatusCode": 429}, "exception": true}, "TranscriptEvent": {"type": "structure", "members": {"transcript": {"shape": "String", "documentation": "<p>The transcript of the voice audio from the user.</p>"}, "eventId": {"shape": "EventId", "documentation": "<p>A unique identifier of the event sent by Amazon Lex V2. The identifier is in the form <code>RESPONSE-N</code>, where N is a number starting with one and incremented for each event sent by Amazon Lex V2 in the current session.</p>"}}, "documentation": "<p>Event sent from Amazon Lex V2 to your client application that contains a transcript of voice audio. </p>", "event": true}, "ValidationException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p/>", "error": {"httpStatusCode": 400}, "exception": true}, "Value": {"type": "structure", "required": ["interpretedValue"], "members": {"originalValue": {"shape": "NonEmptyString", "documentation": "<p>The text of the utterance from the user that was entered for the slot.</p>"}, "interpretedValue": {"shape": "NonEmptyString", "documentation": "<p>The value that Amazon Lex V2 determines for the slot. The actual value depends on the setting of the value selection strategy for the bot. You can choose to use the value entered by the user, or you can have Amazon Lex V2 choose the first value in the <code>resolvedValues</code> list.</p>"}, "resolvedValues": {"shape": "StringList", "documentation": "<p>A list of additional values that have been recognized for the slot.</p>"}}, "documentation": "<p>The value of a slot.</p>"}, "Values": {"type": "list", "member": {"shape": "Slot"}}}, "documentation": "<p>This section contains documentation for the Amazon Lex V2 Runtime V2 API operations.</p>"}