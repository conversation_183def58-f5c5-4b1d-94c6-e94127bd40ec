{"version": "2.0", "metadata": {"apiVersion": "2020-11-22", "endpointPrefix": "api.iotwireless", "protocol": "rest-json", "serviceFullName": "AWS IoT Wireless", "serviceId": "IoT Wireless", "signatureVersion": "v4", "signingName": "iotwireless", "uid": "iotwireless-2020-11-22"}, "operations": {"AssociateAwsAccountWithPartnerAccount": {"name": "AssociateAwsAccountWithPartnerAccount", "http": {"method": "POST", "requestUri": "/partner-accounts"}, "input": {"shape": "AssociateAwsAccountWithPartnerAccountRequest"}, "output": {"shape": "AssociateAwsAccountWithPartnerAccountResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Associates a partner account with your AWS account.</p>"}, "AssociateMulticastGroupWithFuotaTask": {"name": "AssociateMulticastGroupWithFuotaTask", "http": {"method": "PUT", "requestUri": "/fuota-tasks/{Id}/multicast-group", "responseCode": 204}, "input": {"shape": "AssociateMulticastGroupWithFuotaTaskRequest"}, "output": {"shape": "AssociateMulticastGroupWithFuotaTaskResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Associate a multicast group with a FUOTA task.</p>"}, "AssociateWirelessDeviceWithFuotaTask": {"name": "AssociateWirelessDeviceWithFuotaTask", "http": {"method": "PUT", "requestUri": "/fuota-tasks/{Id}/wireless-device", "responseCode": 204}, "input": {"shape": "AssociateWirelessDeviceWithFuotaTaskRequest"}, "output": {"shape": "AssociateWirelessDeviceWithFuotaTaskResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Associate a wireless device with a FUOTA task.</p>"}, "AssociateWirelessDeviceWithMulticastGroup": {"name": "AssociateWirelessDeviceWithMulticastGroup", "http": {"method": "PUT", "requestUri": "/multicast-groups/{Id}/wireless-device", "responseCode": 204}, "input": {"shape": "AssociateWirelessDeviceWithMulticastGroupRequest"}, "output": {"shape": "AssociateWirelessDeviceWithMulticastGroupResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Associates a wireless device with a multicast group.</p>"}, "AssociateWirelessDeviceWithThing": {"name": "AssociateWirelessDeviceWithThing", "http": {"method": "PUT", "requestUri": "/wireless-devices/{Id}/thing", "responseCode": 204}, "input": {"shape": "AssociateWirelessDeviceWithThingRequest"}, "output": {"shape": "AssociateWirelessDeviceWithThingResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Associates a wireless device with a thing.</p>"}, "AssociateWirelessGatewayWithCertificate": {"name": "AssociateWirelessGatewayWithCertificate", "http": {"method": "PUT", "requestUri": "/wireless-gateways/{Id}/certificate"}, "input": {"shape": "AssociateWirelessGatewayWithCertificateRequest"}, "output": {"shape": "AssociateWirelessGatewayWithCertificateResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Associates a wireless gateway with a certificate.</p>"}, "AssociateWirelessGatewayWithThing": {"name": "AssociateWirelessGatewayWithThing", "http": {"method": "PUT", "requestUri": "/wireless-gateways/{Id}/thing", "responseCode": 204}, "input": {"shape": "AssociateWirelessGatewayWithThingRequest"}, "output": {"shape": "AssociateWirelessGatewayWithThingResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Associates a wireless gateway with a thing.</p>"}, "CancelMulticastGroupSession": {"name": "CancelMulticastGroupSession", "http": {"method": "DELETE", "requestUri": "/multicast-groups/{Id}/session", "responseCode": 204}, "input": {"shape": "CancelMulticastGroupSessionRequest"}, "output": {"shape": "CancelMulticastGroupSessionResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Cancels an existing multicast group session.</p>"}, "CreateDestination": {"name": "CreateDestination", "http": {"method": "POST", "requestUri": "/destinations", "responseCode": 201}, "input": {"shape": "CreateDestinationRequest"}, "output": {"shape": "CreateDestinationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Creates a new destination that maps a device message to an AWS IoT rule.</p>"}, "CreateDeviceProfile": {"name": "CreateDeviceProfile", "http": {"method": "POST", "requestUri": "/device-profiles", "responseCode": 201}, "input": {"shape": "CreateDeviceProfileRequest"}, "output": {"shape": "CreateDeviceProfileResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Creates a new device profile.</p>"}, "CreateFuotaTask": {"name": "CreateFuotaTask", "http": {"method": "POST", "requestUri": "/fuota-tasks", "responseCode": 201}, "input": {"shape": "CreateFuotaTaskRequest"}, "output": {"shape": "CreateFuotaTaskResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Creates a FUOTA task.</p>"}, "CreateMulticastGroup": {"name": "CreateMulticastGroup", "http": {"method": "POST", "requestUri": "/multicast-groups", "responseCode": 201}, "input": {"shape": "CreateMulticastGroupRequest"}, "output": {"shape": "CreateMulticastGroupResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Creates a multicast group.</p>"}, "CreateNetworkAnalyzerConfiguration": {"name": "CreateNetworkAnalyzerConfiguration", "http": {"method": "POST", "requestUri": "/network-analyzer-configurations", "responseCode": 201}, "input": {"shape": "CreateNetworkAnalyzerConfigurationRequest"}, "output": {"shape": "CreateNetworkAnalyzerConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Creates a new network analyzer configuration.</p>"}, "CreateServiceProfile": {"name": "CreateServiceProfile", "http": {"method": "POST", "requestUri": "/service-profiles", "responseCode": 201}, "input": {"shape": "CreateServiceProfileRequest"}, "output": {"shape": "CreateServiceProfileResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Creates a new service profile.</p>"}, "CreateWirelessDevice": {"name": "CreateWirelessDevice", "http": {"method": "POST", "requestUri": "/wireless-devices", "responseCode": 201}, "input": {"shape": "CreateWirelessDeviceRequest"}, "output": {"shape": "CreateWirelessDeviceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Provisions a wireless device.</p>"}, "CreateWirelessGateway": {"name": "CreateWirelessGateway", "http": {"method": "POST", "requestUri": "/wireless-gateways", "responseCode": 201}, "input": {"shape": "CreateWirelessGatewayRequest"}, "output": {"shape": "CreateWirelessGatewayResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Provisions a wireless gateway.</p>"}, "CreateWirelessGatewayTask": {"name": "CreateWirelessGatewayTask", "http": {"method": "POST", "requestUri": "/wireless-gateways/{Id}/tasks", "responseCode": 201}, "input": {"shape": "CreateWirelessGatewayTaskRequest"}, "output": {"shape": "CreateWirelessGatewayTaskResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Creates a task for a wireless gateway.</p>"}, "CreateWirelessGatewayTaskDefinition": {"name": "CreateWirelessGatewayTaskDefinition", "http": {"method": "POST", "requestUri": "/wireless-gateway-task-definitions", "responseCode": 201}, "input": {"shape": "CreateWirelessGatewayTaskDefinitionRequest"}, "output": {"shape": "CreateWirelessGatewayTaskDefinitionResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Creates a gateway task definition.</p>"}, "DeleteDestination": {"name": "DeleteDestination", "http": {"method": "DELETE", "requestUri": "/destinations/{Name}", "responseCode": 204}, "input": {"shape": "DeleteDestinationRequest"}, "output": {"shape": "DeleteDestinationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes a destination.</p>"}, "DeleteDeviceProfile": {"name": "DeleteDeviceProfile", "http": {"method": "DELETE", "requestUri": "/device-profiles/{Id}", "responseCode": 204}, "input": {"shape": "DeleteDeviceProfileRequest"}, "output": {"shape": "DeleteDeviceProfileResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes a device profile.</p>"}, "DeleteFuotaTask": {"name": "DeleteFuotaTask", "http": {"method": "DELETE", "requestUri": "/fuota-tasks/{Id}", "responseCode": 204}, "input": {"shape": "DeleteFuotaTaskRequest"}, "output": {"shape": "DeleteFuotaTaskResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes a FUOTA task.</p>"}, "DeleteMulticastGroup": {"name": "DeleteMulticastGroup", "http": {"method": "DELETE", "requestUri": "/multicast-groups/{Id}", "responseCode": 204}, "input": {"shape": "DeleteMulticastGroupRequest"}, "output": {"shape": "DeleteMulticastGroupResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes a multicast group if it is not in use by a fuota task.</p>"}, "DeleteNetworkAnalyzerConfiguration": {"name": "DeleteNetworkAnalyzerConfiguration", "http": {"method": "DELETE", "requestUri": "/network-analyzer-configurations/{ConfigurationName}", "responseCode": 204}, "input": {"shape": "DeleteNetworkAnalyzerConfigurationRequest"}, "output": {"shape": "DeleteNetworkAnalyzerConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes a network analyzer configuration.</p>"}, "DeleteQueuedMessages": {"name": "DeleteQueuedMessages", "http": {"method": "DELETE", "requestUri": "/wireless-devices/{Id}/data", "responseCode": 204}, "input": {"shape": "DeleteQueuedMessagesRequest"}, "output": {"shape": "DeleteQueuedMessagesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Remove queued messages from the downlink queue.</p>"}, "DeleteServiceProfile": {"name": "DeleteServiceProfile", "http": {"method": "DELETE", "requestUri": "/service-profiles/{Id}", "responseCode": 204}, "input": {"shape": "DeleteServiceProfileRequest"}, "output": {"shape": "DeleteServiceProfileResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes a service profile.</p>"}, "DeleteWirelessDevice": {"name": "DeleteWirelessDevice", "http": {"method": "DELETE", "requestUri": "/wireless-devices/{Id}", "responseCode": 204}, "input": {"shape": "DeleteWirelessDeviceRequest"}, "output": {"shape": "DeleteWirelessDeviceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes a wireless device.</p>"}, "DeleteWirelessDeviceImportTask": {"name": "DeleteWirelessDeviceImportTask", "http": {"method": "DELETE", "requestUri": "/wireless_device_import_task/{Id}", "responseCode": 204}, "input": {"shape": "DeleteWirelessDeviceImportTaskRequest"}, "output": {"shape": "DeleteWirelessDeviceImportTaskResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Delete an import task.</p>"}, "DeleteWirelessGateway": {"name": "DeleteWirelessGateway", "http": {"method": "DELETE", "requestUri": "/wireless-gateways/{Id}", "responseCode": 204}, "input": {"shape": "DeleteWirelessGatewayRequest"}, "output": {"shape": "DeleteWirelessGatewayResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes a wireless gateway.</p>"}, "DeleteWirelessGatewayTask": {"name": "DeleteWirelessGatewayTask", "http": {"method": "DELETE", "requestUri": "/wireless-gateways/{Id}/tasks", "responseCode": 204}, "input": {"shape": "DeleteWirelessGatewayTaskRequest"}, "output": {"shape": "DeleteWirelessGatewayTaskResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes a wireless gateway task.</p>"}, "DeleteWirelessGatewayTaskDefinition": {"name": "DeleteWirelessGatewayTaskDefinition", "http": {"method": "DELETE", "requestUri": "/wireless-gateway-task-definitions/{Id}", "responseCode": 204}, "input": {"shape": "DeleteWirelessGatewayTaskDefinitionRequest"}, "output": {"shape": "DeleteWirelessGatewayTaskDefinitionResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes a wireless gateway task definition. Deleting this task definition does not affect tasks that are currently in progress.</p>"}, "DeregisterWirelessDevice": {"name": "DeregisterWirelessDevice", "http": {"method": "PATCH", "requestUri": "/wireless-devices/{Identifier}/deregister"}, "input": {"shape": "DeregisterWirelessDeviceRequest"}, "output": {"shape": "DeregisterWirelessDeviceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deregister a wireless device from AWS IoT Wireless.</p>"}, "DisassociateAwsAccountFromPartnerAccount": {"name": "DisassociateAwsAccountFromPartnerAccount", "http": {"method": "DELETE", "requestUri": "/partner-accounts/{PartnerAccountId}", "responseCode": 204}, "input": {"shape": "DisassociateAwsAccountFromPartnerAccountRequest"}, "output": {"shape": "DisassociateAwsAccountFromPartnerAccountResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Disassociates your AWS account from a partner account. If <code>PartnerAccountId</code> and <code>PartnerType</code> are <code>null</code>, disassociates your AWS account from all partner accounts.</p>"}, "DisassociateMulticastGroupFromFuotaTask": {"name": "DisassociateMulticastGroupFromFuotaTask", "http": {"method": "DELETE", "requestUri": "/fuota-tasks/{Id}/multicast-groups/{MulticastGroupId}", "responseCode": 204}, "input": {"shape": "DisassociateMulticastGroupFromFuotaTaskRequest"}, "output": {"shape": "DisassociateMulticastGroupFromFuotaTaskResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Disassociates a multicast group from a fuota task.</p>"}, "DisassociateWirelessDeviceFromFuotaTask": {"name": "DisassociateWirelessDeviceFromFuotaTask", "http": {"method": "DELETE", "requestUri": "/fuota-tasks/{Id}/wireless-devices/{WirelessDeviceId}", "responseCode": 204}, "input": {"shape": "DisassociateWirelessDeviceFromFuotaTaskRequest"}, "output": {"shape": "DisassociateWirelessDeviceFromFuotaTaskResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Disassociates a wireless device from a FUOTA task.</p>"}, "DisassociateWirelessDeviceFromMulticastGroup": {"name": "DisassociateWirelessDeviceFromMulticastGroup", "http": {"method": "DELETE", "requestUri": "/multicast-groups/{Id}/wireless-devices/{WirelessDeviceId}", "responseCode": 204}, "input": {"shape": "DisassociateWirelessDeviceFromMulticastGroupRequest"}, "output": {"shape": "DisassociateWirelessDeviceFromMulticastGroupResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Disassociates a wireless device from a multicast group.</p>"}, "DisassociateWirelessDeviceFromThing": {"name": "DisassociateWirelessDeviceFromThing", "http": {"method": "DELETE", "requestUri": "/wireless-devices/{Id}/thing", "responseCode": 204}, "input": {"shape": "DisassociateWirelessDeviceFromThingRequest"}, "output": {"shape": "DisassociateWirelessDeviceFromThingResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Disassociates a wireless device from its currently associated thing.</p>"}, "DisassociateWirelessGatewayFromCertificate": {"name": "DisassociateWirelessGatewayFromCertificate", "http": {"method": "DELETE", "requestUri": "/wireless-gateways/{Id}/certificate", "responseCode": 204}, "input": {"shape": "DisassociateWirelessGatewayFromCertificateRequest"}, "output": {"shape": "DisassociateWirelessGatewayFromCertificateResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Disassociates a wireless gateway from its currently associated certificate.</p>"}, "DisassociateWirelessGatewayFromThing": {"name": "DisassociateWirelessGatewayFromThing", "http": {"method": "DELETE", "requestUri": "/wireless-gateways/{Id}/thing", "responseCode": 204}, "input": {"shape": "DisassociateWirelessGatewayFromThingRequest"}, "output": {"shape": "DisassociateWirelessGatewayFromThingResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Disassociates a wireless gateway from its currently associated thing.</p>"}, "GetDestination": {"name": "GetDestination", "http": {"method": "GET", "requestUri": "/destinations/{Name}"}, "input": {"shape": "GetDestinationRequest"}, "output": {"shape": "GetDestinationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Gets information about a destination.</p>"}, "GetDeviceProfile": {"name": "GetDeviceProfile", "http": {"method": "GET", "requestUri": "/device-profiles/{Id}"}, "input": {"shape": "GetDeviceProfileRequest"}, "output": {"shape": "GetDeviceProfileResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Gets information about a device profile.</p>"}, "GetEventConfigurationByResourceTypes": {"name": "GetEventConfigurationByResourceTypes", "http": {"method": "GET", "requestUri": "/event-configurations-resource-types"}, "input": {"shape": "GetEventConfigurationByResourceTypesRequest"}, "output": {"shape": "GetEventConfigurationByResourceTypesResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get the event configuration based on resource types.</p>"}, "GetFuotaTask": {"name": "GetFuotaTask", "http": {"method": "GET", "requestUri": "/fuota-tasks/{Id}"}, "input": {"shape": "GetFuotaTaskRequest"}, "output": {"shape": "GetFuotaTaskResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Gets information about a FUOTA task.</p>"}, "GetLogLevelsByResourceTypes": {"name": "GetLogLevelsByResourceTypes", "http": {"method": "GET", "requestUri": "/log-levels", "responseCode": 200}, "input": {"shape": "GetLogLevelsByResourceTypesRequest"}, "output": {"shape": "GetLogLevelsByResourceTypesResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns current default log levels or log levels by resource types. Based on resource types, log levels can be for wireless device log options or wireless gateway log options.</p>"}, "GetMulticastGroup": {"name": "GetMulticastGroup", "http": {"method": "GET", "requestUri": "/multicast-groups/{Id}"}, "input": {"shape": "GetMulticastGroupRequest"}, "output": {"shape": "GetMulticastGroupResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Gets information about a multicast group.</p>"}, "GetMulticastGroupSession": {"name": "GetMulticastGroupSession", "http": {"method": "GET", "requestUri": "/multicast-groups/{Id}/session"}, "input": {"shape": "GetMulticastGroupSessionRequest"}, "output": {"shape": "GetMulticastGroupSessionResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Gets information about a multicast group session.</p>"}, "GetNetworkAnalyzerConfiguration": {"name": "GetNetworkAnalyzerConfiguration", "http": {"method": "GET", "requestUri": "/network-analyzer-configurations/{ConfigurationName}", "responseCode": 200}, "input": {"shape": "GetNetworkAnalyzerConfigurationRequest"}, "output": {"shape": "GetNetworkAnalyzerConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Get network analyzer configuration.</p>"}, "GetPartnerAccount": {"name": "GetPartnerAccount", "http": {"method": "GET", "requestUri": "/partner-accounts/{PartnerAccountId}"}, "input": {"shape": "GetPartnerAccountRequest"}, "output": {"shape": "GetPartnerAccountResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Gets information about a partner account. If <code>PartnerAccountId</code> and <code>PartnerType</code> are <code>null</code>, returns all partner accounts.</p>"}, "GetPosition": {"name": "GetPosition", "http": {"method": "GET", "requestUri": "/positions/{ResourceIdentifier}"}, "input": {"shape": "GetPositionRequest"}, "output": {"shape": "GetPositionResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get the position information for a given resource.</p> <important> <p>This action is no longer supported. Calls to retrieve the position information should use the <a href=\"https://docs.aws.amazon.com/iot-wireless/2020-11-22/apireference/API_GetResourcePosition.html\">GetResourcePosition</a> API operation instead.</p> </important>", "deprecated": true, "deprecatedMessage": "This operation is no longer supported."}, "GetPositionConfiguration": {"name": "GetPositionConfiguration", "http": {"method": "GET", "requestUri": "/position-configurations/{ResourceIdentifier}", "responseCode": 200}, "input": {"shape": "GetPositionConfigurationRequest"}, "output": {"shape": "GetPositionConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get position configuration for a given resource.</p> <important> <p>This action is no longer supported. Calls to retrieve the position configuration should use the <a href=\"https://docs.aws.amazon.com/iot-wireless/2020-11-22/apireference/API_GetResourcePosition.html\">GetResourcePosition</a> API operation instead.</p> </important>", "deprecated": true, "deprecatedMessage": "This operation is no longer supported."}, "GetPositionEstimate": {"name": "GetPositionEstimate", "http": {"method": "POST", "requestUri": "/position-estimate"}, "input": {"shape": "GetPositionEstimateRequest"}, "output": {"shape": "GetPositionEstimateResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get estimated position information as a payload in GeoJSON format. The payload measurement data is resolved using solvers that are provided by third-party vendors.</p>"}, "GetResourceEventConfiguration": {"name": "GetResourceEventConfiguration", "http": {"method": "GET", "requestUri": "/event-configurations/{Identifier}"}, "input": {"shape": "GetResourceEventConfigurationRequest"}, "output": {"shape": "GetResourceEventConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get the event configuration for a particular resource identifier.</p>"}, "GetResourceLogLevel": {"name": "GetResourceLogLevel", "http": {"method": "GET", "requestUri": "/log-levels/{ResourceIdentifier}", "responseCode": 200}, "input": {"shape": "GetResourceLogLevelRequest"}, "output": {"shape": "GetResourceLogLevelResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Fetches the log-level override, if any, for a given resource-ID and resource-type. It can be used for a wireless device or a wireless gateway.</p>"}, "GetResourcePosition": {"name": "GetResourcePosition", "http": {"method": "GET", "requestUri": "/resource-positions/{ResourceIdentifier}"}, "input": {"shape": "GetResourcePositionRequest"}, "output": {"shape": "GetResourcePositionResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get the position information for a given wireless device or a wireless gateway resource. The position information uses the <a href=\"https://gisgeography.com/wgs84-world-geodetic-system/\"> World Geodetic System (WGS84)</a>.</p>"}, "GetServiceEndpoint": {"name": "GetServiceEndpoint", "http": {"method": "GET", "requestUri": "/service-endpoint"}, "input": {"shape": "GetServiceEndpointRequest"}, "output": {"shape": "GetServiceEndpointResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Gets the account-specific endpoint for Configuration and Update Server (CUPS) protocol or LoRaWAN Network Server (LNS) connections.</p>"}, "GetServiceProfile": {"name": "GetServiceProfile", "http": {"method": "GET", "requestUri": "/service-profiles/{Id}"}, "input": {"shape": "GetServiceProfileRequest"}, "output": {"shape": "GetServiceProfileResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Gets information about a service profile.</p>"}, "GetWirelessDevice": {"name": "GetWirelessDevice", "http": {"method": "GET", "requestUri": "/wireless-devices/{Identifier}"}, "input": {"shape": "GetWirelessDeviceRequest"}, "output": {"shape": "GetWirelessDeviceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Gets information about a wireless device.</p>"}, "GetWirelessDeviceImportTask": {"name": "GetWirelessDeviceImportTask", "http": {"method": "GET", "requestUri": "/wireless_device_import_task/{Id}"}, "input": {"shape": "GetWirelessDeviceImportTaskRequest"}, "output": {"shape": "GetWirelessDeviceImportTaskResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Get information about an import task and count of device onboarding summary information for the import task.</p>"}, "GetWirelessDeviceStatistics": {"name": "GetWirelessDeviceStatistics", "http": {"method": "GET", "requestUri": "/wireless-devices/{Id}/statistics", "responseCode": 200}, "input": {"shape": "GetWirelessDeviceStatisticsRequest"}, "output": {"shape": "GetWirelessDeviceStatisticsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Gets operating information about a wireless device.</p>"}, "GetWirelessGateway": {"name": "GetWirelessGateway", "http": {"method": "GET", "requestUri": "/wireless-gateways/{Identifier}"}, "input": {"shape": "GetWirelessGatewayRequest"}, "output": {"shape": "GetWirelessGatewayResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Gets information about a wireless gateway.</p>"}, "GetWirelessGatewayCertificate": {"name": "GetWirelessGatewayCertificate", "http": {"method": "GET", "requestUri": "/wireless-gateways/{Id}/certificate"}, "input": {"shape": "GetWirelessGatewayCertificateRequest"}, "output": {"shape": "GetWirelessGatewayCertificateResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Gets the ID of the certificate that is currently associated with a wireless gateway.</p>"}, "GetWirelessGatewayFirmwareInformation": {"name": "GetWirelessGatewayFirmwareInformation", "http": {"method": "GET", "requestUri": "/wireless-gateways/{Id}/firmware-information"}, "input": {"shape": "GetWirelessGatewayFirmwareInformationRequest"}, "output": {"shape": "GetWirelessGatewayFirmwareInformationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Gets the firmware version and other information about a wireless gateway.</p>"}, "GetWirelessGatewayStatistics": {"name": "GetWirelessGatewayStatistics", "http": {"method": "GET", "requestUri": "/wireless-gateways/{Id}/statistics", "responseCode": 200}, "input": {"shape": "GetWirelessGatewayStatisticsRequest"}, "output": {"shape": "GetWirelessGatewayStatisticsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Gets operating information about a wireless gateway.</p>"}, "GetWirelessGatewayTask": {"name": "GetWirelessGatewayTask", "http": {"method": "GET", "requestUri": "/wireless-gateways/{Id}/tasks"}, "input": {"shape": "GetWirelessGatewayTaskRequest"}, "output": {"shape": "GetWirelessGatewayTaskResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Gets information about a wireless gateway task.</p>"}, "GetWirelessGatewayTaskDefinition": {"name": "GetWirelessGatewayTaskDefinition", "http": {"method": "GET", "requestUri": "/wireless-gateway-task-definitions/{Id}"}, "input": {"shape": "GetWirelessGatewayTaskDefinitionRequest"}, "output": {"shape": "GetWirelessGatewayTaskDefinitionResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Gets information about a wireless gateway task definition.</p>"}, "ListDestinations": {"name": "ListDestinations", "http": {"method": "GET", "requestUri": "/destinations"}, "input": {"shape": "ListDestinationsRequest"}, "output": {"shape": "ListDestinationsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Lists the destinations registered to your AWS account.</p>"}, "ListDeviceProfiles": {"name": "ListDeviceProfiles", "http": {"method": "GET", "requestUri": "/device-profiles"}, "input": {"shape": "ListDeviceProfilesRequest"}, "output": {"shape": "ListDeviceProfilesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Lists the device profiles registered to your AWS account.</p>"}, "ListDevicesForWirelessDeviceImportTask": {"name": "ListDevicesForWirelessDeviceImportTask", "http": {"method": "GET", "requestUri": "/wireless_device_import_task"}, "input": {"shape": "ListDevicesForWirelessDeviceImportTaskRequest"}, "output": {"shape": "ListDevicesForWirelessDeviceImportTaskResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}], "documentation": "<p>List the Sidewalk devices in an import task and their onboarding status.</p>"}, "ListEventConfigurations": {"name": "ListEventConfigurations", "http": {"method": "GET", "requestUri": "/event-configurations"}, "input": {"shape": "ListEventConfigurationsRequest"}, "output": {"shape": "ListEventConfigurationsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>List event configurations where at least one event topic has been enabled.</p>"}, "ListFuotaTasks": {"name": "ListFuotaTasks", "http": {"method": "GET", "requestUri": "/fuota-tasks"}, "input": {"shape": "ListFuotaTasksRequest"}, "output": {"shape": "ListFuotaTasksResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Lists the FUOTA tasks registered to your AWS account.</p>"}, "ListMulticastGroups": {"name": "ListMulticastGroups", "http": {"method": "GET", "requestUri": "/multicast-groups"}, "input": {"shape": "ListMulticastGroupsRequest"}, "output": {"shape": "ListMulticastGroupsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Lists the multicast groups registered to your AWS account.</p>"}, "ListMulticastGroupsByFuotaTask": {"name": "ListMulticastGroupsByFuotaTask", "http": {"method": "GET", "requestUri": "/fuota-tasks/{Id}/multicast-groups"}, "input": {"shape": "ListMulticastGroupsByFuotaTaskRequest"}, "output": {"shape": "ListMulticastGroupsByFuotaTaskResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>List all multicast groups associated with a fuota task.</p>"}, "ListNetworkAnalyzerConfigurations": {"name": "ListNetworkAnalyzerConfigurations", "http": {"method": "GET", "requestUri": "/network-analyzer-configurations"}, "input": {"shape": "ListNetworkAnalyzerConfigurationsRequest"}, "output": {"shape": "ListNetworkAnalyzerConfigurationsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Lists the network analyzer configurations.</p>"}, "ListPartnerAccounts": {"name": "ListPartnerAccounts", "http": {"method": "GET", "requestUri": "/partner-accounts"}, "input": {"shape": "ListPartnerAccountsRequest"}, "output": {"shape": "ListPartnerAccountsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Lists the partner accounts associated with your AWS account.</p>"}, "ListPositionConfigurations": {"name": "ListPositionConfigurations", "http": {"method": "GET", "requestUri": "/position-configurations", "responseCode": 200}, "input": {"shape": "ListPositionConfigurationsRequest"}, "output": {"shape": "ListPositionConfigurationsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>List position configurations for a given resource, such as positioning solvers.</p> <important> <p>This action is no longer supported. Calls to retrieve position information should use the <a href=\"https://docs.aws.amazon.com/iot-wireless/2020-11-22/apireference/API_GetResourcePosition.html\">GetResourcePosition</a> API operation instead.</p> </important>", "deprecated": true, "deprecatedMessage": "This operation is no longer supported."}, "ListQueuedMessages": {"name": "ListQueuedMessages", "http": {"method": "GET", "requestUri": "/wireless-devices/{Id}/data"}, "input": {"shape": "ListQueuedMessagesRequest"}, "output": {"shape": "ListQueuedMessagesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>List queued messages in the downlink queue.</p>"}, "ListServiceProfiles": {"name": "ListServiceProfiles", "http": {"method": "GET", "requestUri": "/service-profiles"}, "input": {"shape": "ListServiceProfilesRequest"}, "output": {"shape": "ListServiceProfilesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Lists the service profiles registered to your AWS account.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Lists the tags (metadata) you have assigned to the resource.</p>"}, "ListWirelessDeviceImportTasks": {"name": "ListWirelessDeviceImportTasks", "http": {"method": "GET", "requestUri": "/wireless_device_import_tasks"}, "input": {"shape": "ListWirelessDeviceImportTasksRequest"}, "output": {"shape": "ListWirelessDeviceImportTasksResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}], "documentation": "<p>List wireless devices that have been added to an import task.</p>"}, "ListWirelessDevices": {"name": "ListWirelessDevices", "http": {"method": "GET", "requestUri": "/wireless-devices"}, "input": {"shape": "ListWirelessDevicesRequest"}, "output": {"shape": "ListWirelessDevicesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the wireless devices registered to your AWS account.</p>"}, "ListWirelessGatewayTaskDefinitions": {"name": "ListWirelessGatewayTaskDefinitions", "http": {"method": "GET", "requestUri": "/wireless-gateway-task-definitions"}, "input": {"shape": "ListWirelessGatewayTaskDefinitionsRequest"}, "output": {"shape": "ListWirelessGatewayTaskDefinitionsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>List the wireless gateway tasks definitions registered to your AWS account.</p>"}, "ListWirelessGateways": {"name": "ListWirelessGateways", "http": {"method": "GET", "requestUri": "/wireless-gateways"}, "input": {"shape": "ListWirelessGatewaysRequest"}, "output": {"shape": "ListWirelessGatewaysResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the wireless gateways registered to your AWS account.</p>"}, "PutPositionConfiguration": {"name": "PutPositionConfiguration", "http": {"method": "PUT", "requestUri": "/position-configurations/{ResourceIdentifier}", "responseCode": 200}, "input": {"shape": "PutPositionConfigurationRequest"}, "output": {"shape": "PutPositionConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Put position configuration for a given resource.</p> <important> <p>This action is no longer supported. Calls to update the position configuration should use the <a href=\"https://docs.aws.amazon.com/iot-wireless/2020-11-22/apireference/API_UpdateResourcePosition.html\">UpdateResourcePosition</a> API operation instead.</p> </important>", "deprecated": true, "deprecatedMessage": "This operation is no longer supported."}, "PutResourceLogLevel": {"name": "PutResourceLogLevel", "http": {"method": "PUT", "requestUri": "/log-levels/{ResourceIdentifier}", "responseCode": 200}, "input": {"shape": "PutResourceLogLevelRequest"}, "output": {"shape": "PutResourceLogLevelResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Sets the log-level override for a resource-ID and resource-type. This option can be specified for a wireless gateway or a wireless device. A limit of 200 log level override can be set per account.</p>"}, "ResetAllResourceLogLevels": {"name": "ResetAllResourceLogLevels", "http": {"method": "DELETE", "requestUri": "/log-levels", "responseCode": 204}, "input": {"shape": "ResetAllResourceLogLevelsRequest"}, "output": {"shape": "ResetAllResourceLogLevelsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Removes the log-level overrides for all resources; both wireless devices and wireless gateways.</p>"}, "ResetResourceLogLevel": {"name": "ResetResourceLogLevel", "http": {"method": "DELETE", "requestUri": "/log-levels/{ResourceIdentifier}", "responseCode": 204}, "input": {"shape": "ResetResourceLogLevelRequest"}, "output": {"shape": "ResetResourceLogLevelResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Removes the log-level override, if any, for a specific resource-ID and resource-type. It can be used for a wireless device or a wireless gateway.</p>"}, "SendDataToMulticastGroup": {"name": "SendDataToMulticastGroup", "http": {"method": "POST", "requestUri": "/multicast-groups/{Id}/data", "responseCode": 201}, "input": {"shape": "SendDataToMulticastGroupRequest"}, "output": {"shape": "SendDataToMulticastGroupResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Sends the specified data to a multicast group.</p>"}, "SendDataToWirelessDevice": {"name": "SendDataToWirelessDevice", "http": {"method": "POST", "requestUri": "/wireless-devices/{Id}/data", "responseCode": 202}, "input": {"shape": "SendDataToWirelessDeviceRequest"}, "output": {"shape": "SendDataToWirelessDeviceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Sends a decrypted application data frame to a device.</p>"}, "StartBulkAssociateWirelessDeviceWithMulticastGroup": {"name": "StartBulkAssociateWirelessDeviceWithMulticastGroup", "http": {"method": "PATCH", "requestUri": "/multicast-groups/{Id}/bulk", "responseCode": 204}, "input": {"shape": "StartBulkAssociateWirelessDeviceWithMulticastGroupRequest"}, "output": {"shape": "StartBulkAssociateWirelessDeviceWithMulticastGroupResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Starts a bulk association of all qualifying wireless devices with a multicast group.</p>"}, "StartBulkDisassociateWirelessDeviceFromMulticastGroup": {"name": "StartBulkDisassociateWirelessDeviceFromMulticastGroup", "http": {"method": "POST", "requestUri": "/multicast-groups/{Id}/bulk", "responseCode": 204}, "input": {"shape": "StartBulkDisassociateWirelessDeviceFromMulticastGroupRequest"}, "output": {"shape": "StartBulkDisassociateWirelessDeviceFromMulticastGroupResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Starts a bulk disassociatin of all qualifying wireless devices from a multicast group.</p>"}, "StartFuotaTask": {"name": "StartFuotaTask", "http": {"method": "PUT", "requestUri": "/fuota-tasks/{Id}", "responseCode": 204}, "input": {"shape": "StartFuotaTaskRequest"}, "output": {"shape": "StartFuotaTaskResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Starts a FUOTA task.</p>"}, "StartMulticastGroupSession": {"name": "StartMulticastGroupSession", "http": {"method": "PUT", "requestUri": "/multicast-groups/{Id}/session", "responseCode": 204}, "input": {"shape": "StartMulticastGroupSessionRequest"}, "output": {"shape": "StartMulticastGroupSessionResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Starts a multicast group session.</p>"}, "StartSingleWirelessDeviceImportTask": {"name": "StartSingleWirelessDeviceImportTask", "http": {"method": "POST", "requestUri": "/wireless_single_device_import_task", "responseCode": 201}, "input": {"shape": "StartSingleWirelessDeviceImportTaskRequest"}, "output": {"shape": "StartSingleWirelessDeviceImportTaskResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Start import task for a single wireless device.</p>"}, "StartWirelessDeviceImportTask": {"name": "StartWirelessDeviceImportTask", "http": {"method": "POST", "requestUri": "/wireless_device_import_task", "responseCode": 201}, "input": {"shape": "StartWirelessDeviceImportTaskRequest"}, "output": {"shape": "StartWirelessDeviceImportTaskResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Start import task for provisioning Sidewalk devices in bulk using an S3 CSV file.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags", "responseCode": 204}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "TooManyTagsException"}], "documentation": "<p>Adds a tag to a resource.</p>"}, "TestWirelessDevice": {"name": "TestWirelessDevice", "http": {"method": "POST", "requestUri": "/wireless-devices/{Id}/test", "responseCode": 200}, "input": {"shape": "TestWirelessDeviceRequest"}, "output": {"shape": "TestWirelessDeviceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Simulates a provisioned device by sending an uplink data payload of <code>Hello</code>.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags", "responseCode": 204}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Removes one or more tags from a resource.</p>"}, "UpdateDestination": {"name": "UpdateDestination", "http": {"method": "PATCH", "requestUri": "/destinations/{Name}", "responseCode": 204}, "input": {"shape": "UpdateDestinationRequest"}, "output": {"shape": "UpdateDestinationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Updates properties of a destination.</p>"}, "UpdateEventConfigurationByResourceTypes": {"name": "UpdateEventConfigurationByResourceTypes", "http": {"method": "PATCH", "requestUri": "/event-configurations-resource-types", "responseCode": 204}, "input": {"shape": "UpdateEventConfigurationByResourceTypesRequest"}, "output": {"shape": "UpdateEventConfigurationByResourceTypesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Update the event configuration based on resource types.</p>"}, "UpdateFuotaTask": {"name": "UpdateFuotaTask", "http": {"method": "PATCH", "requestUri": "/fuota-tasks/{Id}", "responseCode": 204}, "input": {"shape": "UpdateFuotaTaskRequest"}, "output": {"shape": "UpdateFuotaTaskResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Updates properties of a FUOTA task.</p>"}, "UpdateLogLevelsByResourceTypes": {"name": "UpdateLogLevelsByResourceTypes", "http": {"method": "POST", "requestUri": "/log-levels", "responseCode": 200}, "input": {"shape": "UpdateLogLevelsByResourceTypesRequest"}, "output": {"shape": "UpdateLogLevelsByResourceTypesResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Set default log level, or log levels by resource types. This can be for wireless device log options or wireless gateways log options and is used to control the log messages that'll be displayed in CloudWatch.</p>"}, "UpdateMulticastGroup": {"name": "UpdateMulticastGroup", "http": {"method": "PATCH", "requestUri": "/multicast-groups/{Id}", "responseCode": 204}, "input": {"shape": "UpdateMulticastGroupRequest"}, "output": {"shape": "UpdateMulticastGroupResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Updates properties of a multicast group session.</p>"}, "UpdateNetworkAnalyzerConfiguration": {"name": "UpdateNetworkAnalyzerConfiguration", "http": {"method": "PATCH", "requestUri": "/network-analyzer-configurations/{ConfigurationName}", "responseCode": 204}, "input": {"shape": "UpdateNetworkAnalyzerConfigurationRequest"}, "output": {"shape": "UpdateNetworkAnalyzerConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Update network analyzer configuration.</p>"}, "UpdatePartnerAccount": {"name": "UpdatePartnerAccount", "http": {"method": "PATCH", "requestUri": "/partner-accounts/{PartnerAccountId}", "responseCode": 204}, "input": {"shape": "UpdatePartnerAccountRequest"}, "output": {"shape": "UpdatePartnerAccountResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Updates properties of a partner account.</p>"}, "UpdatePosition": {"name": "UpdatePosition", "http": {"method": "PATCH", "requestUri": "/positions/{ResourceIdentifier}", "responseCode": 204}, "input": {"shape": "UpdatePositionRequest"}, "output": {"shape": "UpdatePositionResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Update the position information of a resource.</p> <important> <p>This action is no longer supported. Calls to update the position information should use the <a href=\"https://docs.aws.amazon.com/iot-wireless/2020-11-22/apireference/API_UpdateResourcePosition.html\">UpdateResourcePosition</a> API operation instead.</p> </important>", "deprecated": true, "deprecatedMessage": "This operation is no longer supported."}, "UpdateResourceEventConfiguration": {"name": "UpdateResourceEventConfiguration", "http": {"method": "PATCH", "requestUri": "/event-configurations/{Identifier}", "responseCode": 204}, "input": {"shape": "UpdateResourceEventConfigurationRequest"}, "output": {"shape": "UpdateResourceEventConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Update the event configuration for a particular resource identifier.</p>"}, "UpdateResourcePosition": {"name": "UpdateResourcePosition", "http": {"method": "PATCH", "requestUri": "/resource-positions/{ResourceIdentifier}", "responseCode": 204}, "input": {"shape": "UpdateResourcePositionRequest"}, "output": {"shape": "UpdateResourcePositionResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Update the position information of a given wireless device or a wireless gateway resource. The position coordinates are based on the <a href=\"https://gisgeography.com/wgs84-world-geodetic-system/\"> World Geodetic System (WGS84)</a>.</p>"}, "UpdateWirelessDevice": {"name": "UpdateWirelessDevice", "http": {"method": "PATCH", "requestUri": "/wireless-devices/{Id}", "responseCode": 204}, "input": {"shape": "UpdateWirelessDeviceRequest"}, "output": {"shape": "UpdateWirelessDeviceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Updates properties of a wireless device.</p>"}, "UpdateWirelessDeviceImportTask": {"name": "UpdateWirelessDeviceImportTask", "http": {"method": "PATCH", "requestUri": "/wireless_device_import_task/{Id}", "responseCode": 204}, "input": {"shape": "UpdateWirelessDeviceImportTaskRequest"}, "output": {"shape": "UpdateWirelessDeviceImportTaskResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Update an import task to add more devices to the task.</p>"}, "UpdateWirelessGateway": {"name": "UpdateWirelessGateway", "http": {"method": "PATCH", "requestUri": "/wireless-gateways/{Id}", "responseCode": 204}, "input": {"shape": "UpdateWirelessGatewayRequest"}, "output": {"shape": "UpdateWirelessGatewayResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Updates properties of a wireless gateway.</p>"}}, "shapes": {"AbpV1_0_x": {"type": "structure", "members": {"DevAddr": {"shape": "DevAddr", "documentation": "<p>The DevAddr value.</p>"}, "SessionKeys": {"shape": "SessionKeysAbpV1_0_x", "documentation": "<p>Session keys for ABP v1.0.x</p>"}, "FCntStart": {"shape": "FCntStart", "documentation": "<p>The FCnt init value.</p>"}}, "documentation": "<p>ABP device object for LoRaWAN specification v1.0.x</p>"}, "AbpV1_1": {"type": "structure", "members": {"DevAddr": {"shape": "DevAddr", "documentation": "<p>The DevAddr value.</p>"}, "SessionKeys": {"shape": "SessionKeysAbpV1_1", "documentation": "<p>Session keys for ABP v1.1</p>"}, "FCntStart": {"shape": "FCntStart", "documentation": "<p>The FCnt init value.</p>"}}, "documentation": "<p>ABP device object for LoRaWAN specification v1.1</p>"}, "AccessDeniedException": {"type": "structure", "members": {"Message": {"shape": "Message"}}, "documentation": "<p>User does not have permission to perform this action.</p>", "error": {"httpStatusCode": 403}, "exception": true}, "AccountLinked": {"type": "boolean"}, "Accuracy": {"type": "structure", "members": {"HorizontalAccuracy": {"shape": "HorizontalAccuracy", "documentation": "<p>The horizontal accuracy of the estimated position, which is the difference between the estimated location and the actual device location.</p>"}, "VerticalAccuracy": {"shape": "VerticalAccuracy", "documentation": "<p>The vertical accuracy of the estimated position, which is the difference between the estimated altitude and actual device latitude in meters.</p>"}}, "documentation": "<p>The accuracy of the estimated position in meters. An empty value indicates that no position data is available. A value of ‘0.0’ value indicates that position data is available. This data corresponds to the position information that you specified instead of the position computed by solver.</p>"}, "AckModeRetryDurationSecs": {"type": "integer", "max": 604800, "min": 0}, "AddGwMetadata": {"type": "boolean"}, "AmazonId": {"type": "string", "documentation": "<p>The Sidewalk Amazon ID.</p>", "max": 2048}, "AmazonResourceName": {"type": "string", "max": 1011, "min": 1}, "ApId": {"type": "string", "max": 256}, "AppEui": {"type": "string", "pattern": "[a-fA-F0-9]{16}"}, "AppKey": {"type": "string", "pattern": "[a-fA-F0-9]{32}"}, "AppSKey": {"type": "string", "pattern": "[a-fA-F0-9]{32}"}, "AppServerPrivateKey": {"type": "string", "max": 4096, "min": 1, "pattern": "[a-fA-F0-9]{64}", "sensitive": true}, "ApplicationConfig": {"type": "structure", "members": {"FPort": {"shape": "FPort"}, "Type": {"shape": "ApplicationConfigType", "documentation": "<p>Application type, which can be specified to obtain real-time position information of your LoRaWAN device.</p>"}, "DestinationName": {"shape": "DestinationName", "documentation": "<p>The name of the position data destination that describes the AWS IoT rule that processes the device's position data for use by AWS IoT Core for LoRaWAN.</p>"}}, "documentation": "<p>LoRaWAN application configuration, which can be used to perform geolocation.</p>"}, "ApplicationConfigType": {"type": "string", "enum": ["SemtechGeolocation"]}, "ApplicationServerPublicKey": {"type": "string", "max": 4096, "min": 1, "pattern": "[a-fA-F0-9]{64}", "sensitive": true}, "Applications": {"type": "list", "member": {"shape": "ApplicationConfig"}}, "AssistPosition": {"type": "list", "member": {"shape": "Coordinate"}, "max": 2, "min": 2}, "AssociateAwsAccountWithPartnerAccountRequest": {"type": "structure", "required": ["Sidewalk"], "members": {"Sidewalk": {"shape": "SidewalkAccountInfo", "documentation": "<p>The Sidewalk account credentials.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p>Each resource must have a unique client request token. If you try to create a new resource with the same token as a resource that already exists, an exception occurs. If you omit this value, AWS SDKs will automatically generate a unique client request. </p>", "idempotencyToken": true}, "Tags": {"shape": "TagList", "documentation": "<p>The tags to attach to the specified resource. Tags are metadata that you can use to manage a resource.</p>"}}}, "AssociateAwsAccountWithPartnerAccountResponse": {"type": "structure", "members": {"Sidewalk": {"shape": "SidewalkAccountInfo", "documentation": "<p>The Sidewalk account credentials.</p>"}, "Arn": {"shape": "PartnerAccountArn", "documentation": "<p>The Amazon Resource Name of the resource.</p>"}}}, "AssociateMulticastGroupWithFuotaTaskRequest": {"type": "structure", "required": ["Id", "MulticastGroupId"], "members": {"Id": {"shape": "FuotaTaskId", "location": "uri", "locationName": "Id"}, "MulticastGroupId": {"shape": "MulticastGroupId"}}}, "AssociateMulticastGroupWithFuotaTaskResponse": {"type": "structure", "members": {}}, "AssociateWirelessDeviceWithFuotaTaskRequest": {"type": "structure", "required": ["Id", "WirelessDeviceId"], "members": {"Id": {"shape": "FuotaTaskId", "location": "uri", "locationName": "Id"}, "WirelessDeviceId": {"shape": "WirelessDeviceId"}}}, "AssociateWirelessDeviceWithFuotaTaskResponse": {"type": "structure", "members": {}}, "AssociateWirelessDeviceWithMulticastGroupRequest": {"type": "structure", "required": ["Id", "WirelessDeviceId"], "members": {"Id": {"shape": "MulticastGroupId", "location": "uri", "locationName": "Id"}, "WirelessDeviceId": {"shape": "WirelessDeviceId"}}}, "AssociateWirelessDeviceWithMulticastGroupResponse": {"type": "structure", "members": {}}, "AssociateWirelessDeviceWithThingRequest": {"type": "structure", "required": ["Id", "ThingArn"], "members": {"Id": {"shape": "WirelessDeviceId", "documentation": "<p>The ID of the resource to update.</p>", "location": "uri", "locationName": "Id"}, "ThingArn": {"shape": "ThingArn", "documentation": "<p>The ARN of the thing to associate with the wireless device.</p>"}}}, "AssociateWirelessDeviceWithThingResponse": {"type": "structure", "members": {}}, "AssociateWirelessGatewayWithCertificateRequest": {"type": "structure", "required": ["Id", "IotCertificateId"], "members": {"Id": {"shape": "WirelessGatewayId", "documentation": "<p>The ID of the resource to update.</p>", "location": "uri", "locationName": "Id"}, "IotCertificateId": {"shape": "IotCertificateId", "documentation": "<p>The ID of the certificate to associate with the wireless gateway.</p>"}}}, "AssociateWirelessGatewayWithCertificateResponse": {"type": "structure", "members": {"IotCertificateId": {"shape": "IotCertificateId", "documentation": "<p>The ID of the certificate associated with the wireless gateway.</p>"}}}, "AssociateWirelessGatewayWithThingRequest": {"type": "structure", "required": ["Id", "ThingArn"], "members": {"Id": {"shape": "WirelessGatewayId", "documentation": "<p>The ID of the resource to update.</p>", "location": "uri", "locationName": "Id"}, "ThingArn": {"shape": "ThingArn", "documentation": "<p>The ARN of the thing to associate with the wireless gateway.</p>"}}}, "AssociateWirelessGatewayWithThingResponse": {"type": "structure", "members": {}}, "AutoCreateTasks": {"type": "boolean"}, "BCCH": {"type": "integer", "max": 1023, "min": 0}, "BSIC": {"type": "integer", "max": 63, "min": 0}, "BaseLat": {"type": "float", "max": 90, "min": -90}, "BaseLng": {"type": "float", "max": 180, "min": -180}, "BaseStationId": {"type": "integer", "max": 65535, "min": 0}, "BatteryLevel": {"type": "string", "documentation": "<p>Sidewalk device battery level.</p>", "enum": ["normal", "low", "critical"]}, "Beaconing": {"type": "structure", "members": {"DataRate": {"shape": "BeaconingDataRate", "documentation": "<p>The data rate for gateways that are sending the beacons.</p>"}, "Frequencies": {"shape": "BeaconingFrequencies", "documentation": "<p>The frequency list for the gateways to send the beacons.</p>"}}, "documentation": "<p>Beaconing parameters for configuring the wireless gateways.</p>"}, "BeaconingDataRate": {"type": "integer", "max": 15, "min": 0}, "BeaconingFrequencies": {"type": "list", "member": {"shape": "BeaconingFrequency"}, "max": 10, "min": 0}, "BeaconingFrequency": {"type": "integer", "max": 1000000000, "min": 100000000}, "CancelMulticastGroupSessionRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "MulticastGroupId", "location": "uri", "locationName": "Id"}}}, "CancelMulticastGroupSessionResponse": {"type": "structure", "members": {}}, "CaptureTimeAccuracy": {"type": "float"}, "CdmaChannel": {"type": "integer", "max": 4095, "min": 0}, "CdmaList": {"type": "list", "member": {"shape": "CdmaObj"}, "max": 16, "min": 1}, "CdmaLocalId": {"type": "structure", "required": ["PnOffset", "CdmaChannel"], "members": {"PnOffset": {"shape": "PnOffset", "documentation": "<p>Pseudo-noise offset, which is a characteristic of the signal from a cell on a radio tower.</p>"}, "CdmaChannel": {"shape": "CdmaChannel", "documentation": "<p>CDMA channel information.</p>"}}, "documentation": "<p>CDMA local ID information, which corresponds to the local identification parameters of a CDMA cell.</p>"}, "CdmaNmrList": {"type": "list", "member": {"shape": "CdmaNmrObj"}, "max": 32, "min": 1}, "CdmaNmrObj": {"type": "structure", "required": ["PnOffset", "CdmaChannel"], "members": {"PnOffset": {"shape": "PnOffset", "documentation": "<p>Pseudo-noise offset, which is a characteristic of the signal from a cell on a radio tower.</p>"}, "CdmaChannel": {"shape": "CdmaChannel", "documentation": "<p>CDMA channel information.</p>"}, "PilotPower": {"shape": "Pilot<PERSON>ower", "documentation": "<p>Transmit power level of the pilot signal, measured in dBm (decibel-milliwatts).</p>"}, "BaseStationId": {"shape": "BaseStationId", "documentation": "<p>CDMA base station ID (BSID).</p>"}}, "documentation": "<p>CDMA object for network measurement reports.</p>"}, "CdmaObj": {"type": "structure", "required": ["SystemId", "NetworkId", "BaseStationId"], "members": {"SystemId": {"shape": "SystemId", "documentation": "<p>CDMA system ID (SID).</p>"}, "NetworkId": {"shape": "NetworkId", "documentation": "<p>CDMA network ID (NID).</p>"}, "BaseStationId": {"shape": "BaseStationId", "documentation": "<p>CDMA base station ID (BSID).</p>"}, "RegistrationZone": {"shape": "RegistrationZone", "documentation": "<p>CDMA registration zone (RZ).</p>"}, "CdmaLocalId": {"shape": "CdmaLocalId", "documentation": "<p>CDMA local identification (local ID) parameters.</p>"}, "PilotPower": {"shape": "Pilot<PERSON>ower", "documentation": "<p>Transmit power level of the pilot signal, measured in dBm (decibel-milliwatts).</p>"}, "BaseLat": {"shape": "BaseLat", "documentation": "<p>CDMA base station latitude in degrees.</p>"}, "BaseLng": {"shape": "BaseLng", "documentation": "<p>CDMA base station longitude in degrees.</p>"}, "CdmaNmr": {"shape": "CdmaNmrList", "documentation": "<p>CDMA network measurement reports.</p>"}}, "documentation": "<p>CDMA (Code-division multiple access) object.</p>"}, "CellParams": {"type": "integer", "max": 127, "min": 0}, "CellTowers": {"type": "structure", "members": {"Gsm": {"shape": "GsmList", "documentation": "<p>GSM object information.</p>"}, "Wcdma": {"shape": "WcdmaList", "documentation": "<p>WCDMA object information.</p>"}, "Tdscdma": {"shape": "TdscdmaList", "documentation": "<p>TD-SCDMA object information.</p>"}, "Lte": {"shape": "LteList", "documentation": "<p>LTE object information.</p>"}, "Cdma": {"shape": "CdmaList", "documentation": "<p>CDMA object information.</p>"}}, "documentation": "<p>The cell towers that were used to perform the measurements.</p>"}, "CertificateList": {"type": "structure", "required": ["SigningAlg", "Value"], "members": {"SigningAlg": {"shape": "SigningAlg", "documentation": "<p>The certificate chain algorithm provided by sidewalk.</p>"}, "Value": {"shape": "CertificateValue", "documentation": "<p>The value of the chosen sidewalk certificate.</p>"}}, "documentation": "<p>List of sidewalk certificates.</p>"}, "CertificatePEM": {"type": "string", "max": 4096, "min": 1, "pattern": "[^-A-Za-z0-9+/=]|=[^=]|={3,}${1,4096}"}, "CertificateValue": {"type": "string", "documentation": "<p>Certificate value.</p>", "max": 2048, "min": 0}, "ChannelMask": {"type": "string", "max": 2048}, "ClassBTimeout": {"type": "integer", "max": 1000, "min": 0}, "ClassCTimeout": {"type": "integer", "max": 1000, "min": 0}, "ClientRequestToken": {"type": "string", "documentation": "<p>Each resource must have a unique client request token. If you try to create a new resource with the same token as a resource that already exists, an exception occurs. If you omit this value, AWS SDKs will automatically generate a unique client request.</p>", "max": 64, "min": 1, "pattern": "^[a-zA-Z0-9-_]+$"}, "ConflictException": {"type": "structure", "members": {"Message": {"shape": "Message"}, "ResourceId": {"shape": "ResourceId", "documentation": "<p>Id of the resource in the conflicting operation.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>Type of the resource in the conflicting operation.</p>"}}, "documentation": "<p>Adding, updating, or deleting the resource can cause an inconsistent state.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "ConnectionStatus": {"type": "string", "enum": ["Connected", "Disconnected"]}, "ConnectionStatusEventConfiguration": {"type": "structure", "members": {"LoRaWAN": {"shape": "LoRaWANConnectionStatusEventNotificationConfigurations", "documentation": "<p>Connection status event configuration object for enabling or disabling LoRaWAN related event topics.</p>"}, "WirelessGatewayIdEventTopic": {"shape": "EventNotificationTopicStatus", "documentation": "<p>Denotes whether the wireless gateway ID connection status event topic is enabled or disabled.</p>"}}, "documentation": "<p>Connection status event configuration object for enabling or disabling topic.</p>"}, "ConnectionStatusResourceTypeEventConfiguration": {"type": "structure", "members": {"LoRaWAN": {"shape": "LoRaWANConnectionStatusResourceTypeEventConfiguration", "documentation": "<p>Connection status resource type event configuration object for enabling or disabling LoRaWAN related event topics.</p>"}}, "documentation": "<p>Connection status resource type event configuration object for enabling or disabling topic.</p>"}, "Coordinate": {"type": "float"}, "Crc": {"type": "long", "max": 4294967295, "min": 1}, "CreateDestinationRequest": {"type": "structure", "required": ["Name", "ExpressionType", "Expression", "RoleArn"], "members": {"Name": {"shape": "DestinationName", "documentation": "<p>The name of the new resource.</p>"}, "ExpressionType": {"shape": "ExpressionType", "documentation": "<p>The type of value in <code>Expression</code>.</p>"}, "Expression": {"shape": "Expression", "documentation": "<p>The rule name or topic rule to send messages to.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the new resource.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of the IAM Role that authorizes the destination.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags to attach to the new destination. Tags are metadata that you can use to manage a resource.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p>Each resource must have a unique client request token. If you try to create a new resource with the same token as a resource that already exists, an exception occurs. If you omit this value, AWS SDKs will automatically generate a unique client request. </p>", "idempotencyToken": true}}}, "CreateDestinationResponse": {"type": "structure", "members": {"Arn": {"shape": "DestinationArn", "documentation": "<p>The Amazon Resource Name of the new resource.</p>"}, "Name": {"shape": "DestinationName", "documentation": "<p>The name of the new resource.</p>"}}}, "CreateDeviceProfileRequest": {"type": "structure", "members": {"Name": {"shape": "DeviceProfileName", "documentation": "<p>The name of the new resource.</p>"}, "LoRaWAN": {"shape": "LoRaWANDeviceProfile", "documentation": "<p>The device profile information to use to create the device profile.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags to attach to the new device profile. Tags are metadata that you can use to manage a resource.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p>Each resource must have a unique client request token. If you try to create a new resource with the same token as a resource that already exists, an exception occurs. If you omit this value, AWS SDKs will automatically generate a unique client request. </p>", "idempotencyToken": true}, "Sidewalk": {"shape": "SidewalkCreateDeviceProfile", "documentation": "<p>The Sidewalk-related information for creating the Sidewalk device profile.</p>"}}}, "CreateDeviceProfileResponse": {"type": "structure", "members": {"Arn": {"shape": "DeviceProfileArn", "documentation": "<p>The Amazon Resource Name of the new resource.</p>"}, "Id": {"shape": "DeviceProfileId", "documentation": "<p>The ID of the new device profile.</p>"}}}, "CreateFuotaTaskRequest": {"type": "structure", "required": ["FirmwareUpdateImage", "FirmwareUpdateRole"], "members": {"Name": {"shape": "FuotaTaskName"}, "Description": {"shape": "Description"}, "ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "LoRaWAN": {"shape": "LoRaWANFuotaTask"}, "FirmwareUpdateImage": {"shape": "FirmwareUpdateImage"}, "FirmwareUpdateRole": {"shape": "FirmwareUpdateRole"}, "Tags": {"shape": "TagList"}, "RedundancyPercent": {"shape": "RedundancyPercent"}, "FragmentSizeBytes": {"shape": "FragmentSizeBytes"}, "FragmentIntervalMS": {"shape": "FragmentIntervalMS"}}}, "CreateFuotaTaskResponse": {"type": "structure", "members": {"Arn": {"shape": "FuotaTaskArn"}, "Id": {"shape": "FuotaTaskId"}}}, "CreateMulticastGroupRequest": {"type": "structure", "required": ["LoRaWAN"], "members": {"Name": {"shape": "MulticastGroupName"}, "Description": {"shape": "Description", "documentation": "<p>The description of the multicast group.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p>Each resource must have a unique client request token. If you try to create a new resource with the same token as a resource that already exists, an exception occurs. If you omit this value, AWS SDKs will automatically generate a unique client request. </p>", "idempotencyToken": true}, "LoRaWAN": {"shape": "LoRaWANMulticast"}, "Tags": {"shape": "TagList"}}}, "CreateMulticastGroupResponse": {"type": "structure", "members": {"Arn": {"shape": "MulticastGroupArn"}, "Id": {"shape": "MulticastGroupId"}}}, "CreateNetworkAnalyzerConfigurationRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "NetworkAnalyzerConfigurationName"}, "TraceContent": {"shape": "Trace<PERSON><PERSON>nt"}, "WirelessDevices": {"shape": "WirelessDeviceList", "documentation": "<p>Wireless device resources to add to the network analyzer configuration. Provide the <code>WirelessDeviceId</code> of the resource to add in the input array.</p>"}, "WirelessGateways": {"shape": "WirelessGatewayList", "documentation": "<p>Wireless gateway resources to add to the network analyzer configuration. Provide the <code>WirelessGatewayId</code> of the resource to add in the input array.</p>"}, "Description": {"shape": "Description"}, "Tags": {"shape": "TagList"}, "ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "MulticastGroups": {"shape": "NetworkAnalyzerMulticastGroupList", "documentation": "<p>Multicast Group resources to add to the network analyzer configruation. Provide the <code>MulticastGroupId</code> of the resource to add in the input array.</p>"}}}, "CreateNetworkAnalyzerConfigurationResponse": {"type": "structure", "members": {"Arn": {"shape": "NetworkAnalyzerConfigurationArn", "documentation": "<p>The Amazon Resource Name of the new resource.</p>"}, "Name": {"shape": "NetworkAnalyzerConfigurationName"}}}, "CreateServiceProfileRequest": {"type": "structure", "members": {"Name": {"shape": "ServiceProfileName", "documentation": "<p>The name of the new resource.</p>"}, "LoRaWAN": {"shape": "LoRaWANServiceProfile", "documentation": "<p>The service profile information to use to create the service profile.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags to attach to the new service profile. Tags are metadata that you can use to manage a resource.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p>Each resource must have a unique client request token. If you try to create a new resource with the same token as a resource that already exists, an exception occurs. If you omit this value, AWS SDKs will automatically generate a unique client request. </p>", "idempotencyToken": true}}}, "CreateServiceProfileResponse": {"type": "structure", "members": {"Arn": {"shape": "ServiceProfileArn", "documentation": "<p>The Amazon Resource Name of the new resource.</p>"}, "Id": {"shape": "ServiceProfileId", "documentation": "<p>The ID of the new service profile.</p>"}}}, "CreateWirelessDeviceRequest": {"type": "structure", "required": ["Type", "DestinationName"], "members": {"Type": {"shape": "WirelessDeviceType", "documentation": "<p>The wireless device type.</p>"}, "Name": {"shape": "WirelessDeviceName", "documentation": "<p>The name of the new resource.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the new resource.</p>"}, "DestinationName": {"shape": "DestinationName", "documentation": "<p>The name of the destination to assign to the new wireless device.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p>Each resource must have a unique client request token. If you try to create a new resource with the same token as a resource that already exists, an exception occurs. If you omit this value, AWS SDKs will automatically generate a unique client request. </p>", "idempotencyToken": true}, "LoRaWAN": {"shape": "LoRaWANDevice", "documentation": "<p>The device configuration information to use to create the wireless device.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags to attach to the new wireless device. Tags are metadata that you can use to manage a resource.</p>"}, "Positioning": {"shape": "PositioningConfigStatus", "documentation": "<p>FPort values for the GNSS, stream, and ClockSync functions of the positioning information.</p>"}, "Sidewalk": {"shape": "SidewalkCreateWirelessDevice", "documentation": "<p>The device configuration information to use to create the Sidewalk device.</p>"}}}, "CreateWirelessDeviceResponse": {"type": "structure", "members": {"Arn": {"shape": "WirelessDeviceArn", "documentation": "<p>The Amazon Resource Name of the new resource.</p>"}, "Id": {"shape": "WirelessDeviceId", "documentation": "<p>The ID of the new wireless device.</p>"}}}, "CreateWirelessGatewayRequest": {"type": "structure", "required": ["LoRaWAN"], "members": {"Name": {"shape": "WirelessGatewayName", "documentation": "<p>The name of the new resource.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the new resource.</p>"}, "LoRaWAN": {"shape": "LoRaWANGateway", "documentation": "<p>The gateway configuration information to use to create the wireless gateway.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags to attach to the new wireless gateway. Tags are metadata that you can use to manage a resource.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p>Each resource must have a unique client request token. If you try to create a new resource with the same token as a resource that already exists, an exception occurs. If you omit this value, AWS SDKs will automatically generate a unique client request. </p>", "idempotencyToken": true}}}, "CreateWirelessGatewayResponse": {"type": "structure", "members": {"Arn": {"shape": "WirelessGatewayArn", "documentation": "<p>The Amazon Resource Name of the new resource.</p>"}, "Id": {"shape": "WirelessDeviceId", "documentation": "<p>The ID of the new wireless gateway.</p>"}}}, "CreateWirelessGatewayTaskDefinitionRequest": {"type": "structure", "required": ["AutoCreateTasks"], "members": {"AutoCreateTasks": {"shape": "AutoCreateTasks", "documentation": "<p>Whether to automatically create tasks using this task definition for all gateways with the specified current version. If <code>false</code>, the task must me created by calling <code>CreateWirelessGatewayTask</code>.</p>"}, "Name": {"shape": "WirelessGatewayTaskName", "documentation": "<p>The name of the new resource.</p>"}, "Update": {"shape": "UpdateWirelessGatewayTaskCreate", "documentation": "<p>Information about the gateways to update.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p>Each resource must have a unique client request token. If you try to create a new resource with the same token as a resource that already exists, an exception occurs. If you omit this value, AWS SDKs will automatically generate a unique client request. </p>", "idempotencyToken": true}, "Tags": {"shape": "TagList", "documentation": "<p>The tags to attach to the specified resource. Tags are metadata that you can use to manage a resource.</p>"}}}, "CreateWirelessGatewayTaskDefinitionResponse": {"type": "structure", "members": {"Id": {"shape": "WirelessGatewayTaskDefinitionId", "documentation": "<p>The ID of the new wireless gateway task definition.</p>"}, "Arn": {"shape": "WirelessGatewayTaskDefinitionArn", "documentation": "<p>The Amazon Resource Name of the resource.</p>"}}}, "CreateWirelessGatewayTaskRequest": {"type": "structure", "required": ["Id", "WirelessGatewayTaskDefinitionId"], "members": {"Id": {"shape": "WirelessGatewayId", "documentation": "<p>The ID of the resource to update.</p>", "location": "uri", "locationName": "Id"}, "WirelessGatewayTaskDefinitionId": {"shape": "WirelessGatewayTaskDefinitionId", "documentation": "<p>The ID of the WirelessGatewayTaskDefinition.</p>"}}}, "CreateWirelessGatewayTaskResponse": {"type": "structure", "members": {"WirelessGatewayTaskDefinitionId": {"shape": "WirelessGatewayTaskDefinitionId", "documentation": "<p>The ID of the WirelessGatewayTaskDefinition.</p>"}, "Status": {"shape": "WirelessGatewayTaskStatus", "documentation": "<p>The status of the request.</p>"}}}, "CreatedAt": {"type": "timestamp", "documentation": "<p>Created at timestamp for the resource.</p>"}, "CreationDate": {"type": "timestamp"}, "CreationTime": {"type": "timestamp", "timestampFormat": "iso8601"}, "DakCertificateId": {"type": "string", "max": 256}, "DakCertificateMetadata": {"type": "structure", "required": ["CertificateId"], "members": {"CertificateId": {"shape": "DakCertificateId", "documentation": "<p>The certificate ID for the DAK.</p>"}, "MaxAllowedSignature": {"shape": "MaxAllowedSignature", "documentation": "<p>The maximum number of signatures that the DAK can sign. A value of <code>-1</code> indicates that there's no device limit.</p>"}, "FactorySupport": {"shape": "FactorySupport", "documentation": "<p>Whether factory support has been enabled.</p>"}, "ApId": {"shape": "ApId", "documentation": "<p>The advertised product ID (APID) that's used for pre-production and production applications.</p>"}, "DeviceTypeId": {"shape": "DeviceTypeId", "documentation": "<p>The device type ID that's used for prototyping applications.</p>"}}, "documentation": "<p>The device attestation key (DAK) information.</p>"}, "DakCertificateMetadataList": {"type": "list", "member": {"shape": "DakCertificateMetadata"}}, "DeleteDestinationRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "DestinationName", "documentation": "<p>The name of the resource to delete.</p>", "location": "uri", "locationName": "Name"}}}, "DeleteDestinationResponse": {"type": "structure", "members": {}}, "DeleteDeviceProfileRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "DeviceProfileId", "documentation": "<p>The ID of the resource to delete.</p>", "location": "uri", "locationName": "Id"}}}, "DeleteDeviceProfileResponse": {"type": "structure", "members": {}}, "DeleteFuotaTaskRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "FuotaTaskId", "location": "uri", "locationName": "Id"}}}, "DeleteFuotaTaskResponse": {"type": "structure", "members": {}}, "DeleteMulticastGroupRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "MulticastGroupId", "location": "uri", "locationName": "Id"}}}, "DeleteMulticastGroupResponse": {"type": "structure", "members": {}}, "DeleteNetworkAnalyzerConfigurationRequest": {"type": "structure", "required": ["ConfigurationName"], "members": {"ConfigurationName": {"shape": "NetworkAnalyzerConfigurationName", "location": "uri", "locationName": "ConfigurationName"}}}, "DeleteNetworkAnalyzerConfigurationResponse": {"type": "structure", "members": {}}, "DeleteQueuedMessagesRequest": {"type": "structure", "required": ["Id", "MessageId"], "members": {"Id": {"shape": "WirelessDeviceId", "documentation": "<p>The ID of a given wireless device for which downlink messages will be deleted.</p>", "location": "uri", "locationName": "Id"}, "MessageId": {"shape": "MessageId", "documentation": "<p>If message ID is <code>\"*\"</code>, it cleares the entire downlink queue for a given device, specified by the wireless device ID. Otherwise, the downlink message with the specified message ID will be deleted.</p>", "location": "querystring", "locationName": "messageId"}, "WirelessDeviceType": {"shape": "WirelessDeviceType", "documentation": "<p>The wireless device type, which can be either Sidewalk or LoRaWAN.</p>", "location": "querystring", "locationName": "WirelessDeviceType"}}}, "DeleteQueuedMessagesResponse": {"type": "structure", "members": {}}, "DeleteServiceProfileRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "ServiceProfileId", "documentation": "<p>The ID of the resource to delete.</p>", "location": "uri", "locationName": "Id"}}}, "DeleteServiceProfileResponse": {"type": "structure", "members": {}}, "DeleteWirelessDeviceImportTaskRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "ImportTaskId", "documentation": "<p>The unique identifier of the import task to be deleted.</p>", "location": "uri", "locationName": "Id"}}}, "DeleteWirelessDeviceImportTaskResponse": {"type": "structure", "members": {}}, "DeleteWirelessDeviceRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "WirelessDeviceId", "documentation": "<p>The ID of the resource to delete.</p>", "location": "uri", "locationName": "Id"}}}, "DeleteWirelessDeviceResponse": {"type": "structure", "members": {}}, "DeleteWirelessGatewayRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "WirelessGatewayId", "documentation": "<p>The ID of the resource to delete.</p>", "location": "uri", "locationName": "Id"}}}, "DeleteWirelessGatewayResponse": {"type": "structure", "members": {}}, "DeleteWirelessGatewayTaskDefinitionRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "WirelessGatewayTaskDefinitionId", "documentation": "<p>The ID of the resource to delete.</p>", "location": "uri", "locationName": "Id"}}}, "DeleteWirelessGatewayTaskDefinitionResponse": {"type": "structure", "members": {}}, "DeleteWirelessGatewayTaskRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "WirelessGatewayId", "documentation": "<p>The ID of the resource to delete.</p>", "location": "uri", "locationName": "Id"}}}, "DeleteWirelessGatewayTaskResponse": {"type": "structure", "members": {}}, "DeregisterWirelessDeviceRequest": {"type": "structure", "required": ["Identifier"], "members": {"Identifier": {"shape": "Identifier", "documentation": "<p>The identifier of the wireless device to deregister from AWS IoT Wireless.</p>", "location": "uri", "locationName": "Identifier"}, "WirelessDeviceType": {"shape": "WirelessDeviceType", "documentation": "<p>The type of wireless device to deregister from AWS IoT Wireless, which can be <code>LoRaWAN</code> or <code>Sidewalk</code>.</p>", "location": "querystring", "locationName": "WirelessDeviceType"}}}, "DeregisterWirelessDeviceResponse": {"type": "structure", "members": {}}, "Description": {"type": "string", "documentation": "<p>The description of the new resource.</p>", "max": 2048}, "DestinationArn": {"type": "string"}, "DestinationList": {"type": "list", "member": {"shape": "Destinations"}}, "DestinationName": {"type": "string", "max": 128, "pattern": "[a-zA-Z0-9-_]+"}, "Destinations": {"type": "structure", "members": {"Arn": {"shape": "DestinationArn", "documentation": "<p>The Amazon Resource Name of the resource.</p>"}, "Name": {"shape": "DestinationName", "documentation": "<p>The name of the resource.</p>"}, "ExpressionType": {"shape": "ExpressionType", "documentation": "<p>The type of value in <code>Expression</code>.</p>"}, "Expression": {"shape": "Expression", "documentation": "<p>The rule name or topic rule to send messages to.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the resource.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of the IAM Role that authorizes the destination.</p>"}}, "documentation": "<p>Describes a destination.</p>"}, "DevAddr": {"type": "string", "pattern": "[a-fA-F0-9]{8}"}, "DevEui": {"type": "string", "pattern": "[a-fA-F0-9]{16}"}, "DevStatusReqFreq": {"type": "integer", "max": 2147483647, "min": 0}, "DeviceCertificateList": {"type": "list", "member": {"shape": "CertificateList"}, "documentation": "<p>List of device certificate chain.</p>"}, "DeviceCreationFile": {"type": "string", "max": 1024}, "DeviceCreationFileList": {"type": "list", "member": {"shape": "DeviceCreationFile"}}, "DeviceName": {"type": "string"}, "DeviceProfile": {"type": "structure", "members": {"Arn": {"shape": "DeviceProfileArn", "documentation": "<p>The Amazon Resource Name of the resource.</p>"}, "Name": {"shape": "DeviceProfileName", "documentation": "<p>The name of the resource.</p>"}, "Id": {"shape": "DeviceProfileId", "documentation": "<p>The ID of the device profile.</p>"}}, "documentation": "<p>Describes a device profile.</p>"}, "DeviceProfileArn": {"type": "string"}, "DeviceProfileId": {"type": "string", "max": 256}, "DeviceProfileList": {"type": "list", "member": {"shape": "DeviceProfile"}}, "DeviceProfileName": {"type": "string", "max": 256}, "DeviceProfileType": {"type": "string", "enum": ["Sidewalk", "LoRaWAN"]}, "DeviceRegistrationStateEventConfiguration": {"type": "structure", "members": {"Sidewalk": {"shape": "SidewalkEventNotificationConfigurations", "documentation": "<p>Device registration state event configuration object for enabling or disabling Sidewalk related event topics.</p>"}, "WirelessDeviceIdEventTopic": {"shape": "EventNotificationTopicStatus", "documentation": "<p>Denotes whether the wireless device ID device registration state event topic is enabled or disabled.</p>"}}, "documentation": "<p>Device registration state event configuration object for enabling and disabling relevant topics.</p>"}, "DeviceRegistrationStateResourceTypeEventConfiguration": {"type": "structure", "members": {"Sidewalk": {"shape": "SidewalkResourceTypeEventConfiguration", "documentation": "<p>Device registration resource type state event configuration object for enabling or disabling Sidewalk related event topics.</p>"}}, "documentation": "<p>Device registration state resource type event configuration object for enabling or disabling topic.</p>"}, "DeviceState": {"type": "string", "documentation": "<p>Device state defines the device status of sidewalk device.</p>", "enum": ["Provisioned", "RegisteredNotSeen", "RegisteredReachable", "RegisteredUnreachable"]}, "DeviceTypeId": {"type": "string", "max": 2048}, "DisassociateAwsAccountFromPartnerAccountRequest": {"type": "structure", "required": ["PartnerAccountId", "PartnerType"], "members": {"PartnerAccountId": {"shape": "PartnerAccountId", "documentation": "<p>The partner account ID to disassociate from the AWS account.</p>", "location": "uri", "locationName": "PartnerAccountId"}, "PartnerType": {"shape": "PartnerType", "documentation": "<p>The partner type.</p>", "location": "querystring", "locationName": "partnerType"}}}, "DisassociateAwsAccountFromPartnerAccountResponse": {"type": "structure", "members": {}}, "DisassociateMulticastGroupFromFuotaTaskRequest": {"type": "structure", "required": ["Id", "MulticastGroupId"], "members": {"Id": {"shape": "FuotaTaskId", "location": "uri", "locationName": "Id"}, "MulticastGroupId": {"shape": "MulticastGroupId", "location": "uri", "locationName": "MulticastGroupId"}}}, "DisassociateMulticastGroupFromFuotaTaskResponse": {"type": "structure", "members": {}}, "DisassociateWirelessDeviceFromFuotaTaskRequest": {"type": "structure", "required": ["Id", "WirelessDeviceId"], "members": {"Id": {"shape": "FuotaTaskId", "location": "uri", "locationName": "Id"}, "WirelessDeviceId": {"shape": "WirelessDeviceId", "location": "uri", "locationName": "WirelessDeviceId"}}}, "DisassociateWirelessDeviceFromFuotaTaskResponse": {"type": "structure", "members": {}}, "DisassociateWirelessDeviceFromMulticastGroupRequest": {"type": "structure", "required": ["Id", "WirelessDeviceId"], "members": {"Id": {"shape": "MulticastGroupId", "location": "uri", "locationName": "Id"}, "WirelessDeviceId": {"shape": "WirelessDeviceId", "location": "uri", "locationName": "WirelessDeviceId"}}}, "DisassociateWirelessDeviceFromMulticastGroupResponse": {"type": "structure", "members": {}}, "DisassociateWirelessDeviceFromThingRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "WirelessDeviceId", "documentation": "<p>The ID of the resource to update.</p>", "location": "uri", "locationName": "Id"}}}, "DisassociateWirelessDeviceFromThingResponse": {"type": "structure", "members": {}}, "DisassociateWirelessGatewayFromCertificateRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "WirelessGatewayId", "documentation": "<p>The ID of the resource to update.</p>", "location": "uri", "locationName": "Id"}}}, "DisassociateWirelessGatewayFromCertificateResponse": {"type": "structure", "members": {}}, "DisassociateWirelessGatewayFromThingRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "WirelessGatewayId", "documentation": "<p>The ID of the resource to update.</p>", "location": "uri", "locationName": "Id"}}}, "DisassociateWirelessGatewayFromThingResponse": {"type": "structure", "members": {}}, "DlBucketSize": {"type": "integer", "max": 2147483647, "min": 0}, "DlClass": {"type": "string", "documentation": "<p>DlClass for LoRaWAM, valid values are ClassB and ClassC.</p>", "enum": ["ClassB", "ClassC"], "max": 256}, "DlDr": {"type": "integer", "documentation": "<p>Downlink data rate.</p>", "max": 15, "min": 0}, "DlFreq": {"type": "integer", "documentation": "<p>Downlink frequency.</p>", "max": 1000000000, "min": 100000000}, "DlRate": {"type": "integer", "max": 2147483647, "min": 0}, "DlRatePolicy": {"type": "string", "max": 256}, "Double": {"type": "double"}, "DownlinkFrequency": {"type": "integer", "max": 1000000000, "min": 100000000}, "DownlinkMode": {"type": "string", "enum": ["SEQUENTIAL", "CONCURRENT", "USING_UPLINK_GATEWAY"]}, "DownlinkQueueMessage": {"type": "structure", "members": {"MessageId": {"shape": "MessageId", "documentation": "<p> The message ID assigned by IoT Wireless to each downlink message, which helps identify the message.</p>"}, "TransmitMode": {"shape": "TransmitMode", "documentation": "<p>The transmit mode to use for sending data to the wireless device. This can be <code>0</code> for UM (unacknowledge mode) or <code>1</code> for AM (acknowledge mode).</p>"}, "ReceivedAt": {"shape": "ISODateTimeString", "documentation": "<p>The time at which Iot Wireless received the downlink message.</p>"}, "LoRaWAN": {"shape": "LoRaWANSendDataToDevice"}}, "documentation": "<p>The message in the downlink queue.</p>"}, "DownlinkQueueMessagesList": {"type": "list", "member": {"shape": "DownlinkQueueMessage"}}, "DrMax": {"type": "integer", "max": 15, "min": 0}, "DrMaxBox": {"type": "integer", "max": 15, "min": 0}, "DrMin": {"type": "integer", "max": 15, "min": 0}, "DrMinBox": {"type": "integer", "max": 15, "min": 0}, "EARFCN": {"type": "integer", "max": 262143, "min": 0}, "EndPoint": {"type": "string", "max": 256, "min": 1}, "EutranCid": {"type": "integer", "max": 268435455, "min": 0}, "Event": {"type": "string", "documentation": "<p>Sidewalk device status notification.</p>", "enum": ["discovered", "lost", "ack", "nack", "passthrough"]}, "EventConfigurationItem": {"type": "structure", "members": {"Identifier": {"shape": "Identifier", "documentation": "<p>Resource identifier opted in for event messaging.</p>"}, "IdentifierType": {"shape": "IdentifierType", "documentation": "<p>Identifier type of the particular resource identifier for event configuration.</p>"}, "PartnerType": {"shape": "EventNotificationPartnerType", "documentation": "<p>Partner type of the resource if the identifier type is PartnerAccountId.</p>"}, "Events": {"shape": "EventNotificationItemConfigurations"}}, "documentation": "<p>Event configuration object for a single resource.</p>"}, "EventConfigurationsList": {"type": "list", "member": {"shape": "EventConfigurationItem"}}, "EventNotificationItemConfigurations": {"type": "structure", "members": {"DeviceRegistrationState": {"shape": "DeviceRegistrationStateEventConfiguration", "documentation": "<p>Device registration state event configuration for an event configuration item.</p>"}, "Proximity": {"shape": "ProximityEventConfiguration", "documentation": "<p>Proximity event configuration for an event configuration item.</p>"}, "Join": {"shape": "JoinEventConfiguration", "documentation": "<p>Join event configuration for an event configuration item.</p>"}, "ConnectionStatus": {"shape": "ConnectionStatusEventConfiguration", "documentation": "<p>Connection status event configuration for an event configuration item.</p>"}, "MessageDeliveryStatus": {"shape": "MessageDeliveryStatusEventConfiguration", "documentation": "<p>Message delivery status event configuration for an event configuration item.</p>"}}, "documentation": "<p>Object of all event configurations and the status of the event topics.</p>"}, "EventNotificationPartnerType": {"type": "string", "enum": ["Sidewalk"]}, "EventNotificationResourceType": {"type": "string", "enum": ["SidewalkAccount", "WirelessDevice", "WirelessGateway"]}, "EventNotificationTopicStatus": {"type": "string", "enum": ["Enabled", "Disabled"]}, "Expression": {"type": "string", "max": 2048}, "ExpressionType": {"type": "string", "enum": ["RuleName", "MqttTopic"]}, "FCntStart": {"type": "integer", "documentation": "<p>The FCnt init value.</p>", "max": 65535, "min": 0}, "FNwkSIntKey": {"type": "string", "pattern": "[a-fA-F0-9]{32}"}, "FPort": {"type": "integer", "documentation": "<p>The Fport value.</p>", "max": 223, "min": 1}, "FPorts": {"type": "structure", "members": {"Fuota": {"shape": "FPort"}, "Multicast": {"shape": "FPort"}, "ClockSync": {"shape": "FPort"}, "Positioning": {"shape": "Positioning", "documentation": "<p>FPort values for the GNSS, stream, and ClockSync functions of the positioning information.</p>"}, "Applications": {"shape": "Applications", "documentation": "<p>Optional LoRaWAN application information, which can be used for geolocation.</p>"}}, "documentation": "<p>List of FPort assigned for different LoRaWAN application packages to use</p>"}, "FactoryPresetFreqsList": {"type": "list", "member": {"shape": "PresetFreq"}, "max": 20, "min": 0}, "FactorySupport": {"type": "boolean"}, "Fingerprint": {"type": "string", "max": 64, "min": 64, "pattern": "[a-fA-F0-9]{64}", "sensitive": true}, "FirmwareUpdateImage": {"type": "string", "documentation": "<p>The S3 URI points to a firmware update image that is to be used with a FUOTA task.</p>", "max": 4096, "min": 1}, "FirmwareUpdateRole": {"type": "string", "documentation": "<p>The firmware update role that is to be used with a FUOTA task.</p>", "max": 2048, "min": 1}, "FragmentIntervalMS": {"type": "integer", "documentation": "<p>The interval for sending fragments in milliseconds, rounded to the nearest second.</p> <note> <p>This interval only determines the timing for when the Cloud sends down the fragments to yor device. There can be a delay for when your device will receive these fragments. This delay depends on the device's class and the communication delay with the cloud.</p> </note>", "min": 1}, "FragmentSizeBytes": {"type": "integer", "documentation": "<p>The size of each fragment in bytes. This parameter is supported only for FUOTA tasks with multicast groups.</p>", "min": 1}, "FuotaDeviceStatus": {"type": "string", "documentation": "<p>The status of a wireless device in a FUOTA task.</p>", "enum": ["Initial", "Package_Not_Supported", "FragAlgo_unsupported", "Not_enough_memory", "FragIndex_unsupported", "Wrong_descriptor", "SessionCnt_replay", "MissingFrag", "MemoryError", "MICError", "Successful"]}, "FuotaTask": {"type": "structure", "members": {"Id": {"shape": "FuotaTaskId"}, "Arn": {"shape": "FuotaTaskArn"}, "Name": {"shape": "FuotaTaskName"}}, "documentation": "<p>A FUOTA task.</p>"}, "FuotaTaskArn": {"type": "string", "documentation": "<p>The arn of a FUOTA task.</p>", "max": 128}, "FuotaTaskId": {"type": "string", "documentation": "<p>The ID of a FUOTA task.</p>", "max": 256}, "FuotaTaskList": {"type": "list", "member": {"shape": "FuotaTask"}, "documentation": "<p>Lists the FUOTA tasks registered to your AWS account.</p>"}, "FuotaTaskName": {"type": "string", "documentation": "<p>The name of a FUOTA task.</p>", "max": 256}, "FuotaTaskStatus": {"type": "string", "documentation": "<p>The status of a FUOTA task.</p>", "enum": ["Pending", "FuotaSession_Waiting", "In_FuotaSession", "FuotaDone", "Delete_Waiting"]}, "GPST": {"type": "float"}, "GatewayEui": {"type": "string", "pattern": "^(([0-9A-Fa-f]{2}-){7}|([0-9A-Fa-f]{2}:){7}|([0-9A-Fa-f]{2}\\s){7}|([0-9A-Fa-f]{2}){7})([0-9A-Fa-f]{2})$"}, "GatewayList": {"type": "list", "member": {"shape": "GatewayListItem"}}, "GatewayListItem": {"type": "structure", "required": ["GatewayId", "DownlinkFrequency"], "members": {"GatewayId": {"shape": "WirelessGatewayId", "documentation": "<p>The ID of the wireless gateways that you want to add to the list of gateways when sending downlink messages.</p>"}, "DownlinkFrequency": {"shape": "DownlinkFrequency", "documentation": "<p>The frequency to use for the gateways when sending a downlink message to the wireless device.</p>"}}, "documentation": "<p>Gateway list item object that specifies the frequency and list of gateways for which the downlink message should be sent.</p>"}, "GatewayMaxEirp": {"type": "float", "max": 30, "min": 0}, "GenAppKey": {"type": "string", "pattern": "[a-fA-F0-9]{32}"}, "GeoJsonPayload": {"type": "blob"}, "GeranCid": {"type": "integer", "max": 65535, "min": 0}, "GetDestinationRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "DestinationName", "documentation": "<p>The name of the resource to get.</p>", "location": "uri", "locationName": "Name"}}}, "GetDestinationResponse": {"type": "structure", "members": {"Arn": {"shape": "DestinationArn", "documentation": "<p>The Amazon Resource Name of the resource.</p>"}, "Name": {"shape": "DestinationName", "documentation": "<p>The name of the resource.</p>"}, "Expression": {"shape": "Expression", "documentation": "<p>The rule name or topic rule to send messages to.</p>"}, "ExpressionType": {"shape": "ExpressionType", "documentation": "<p>The type of value in <code>Expression</code>.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the resource.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of the IAM Role that authorizes the destination.</p>"}}}, "GetDeviceProfileRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "DeviceProfileId", "documentation": "<p>The ID of the resource to get.</p>", "location": "uri", "locationName": "Id"}}}, "GetDeviceProfileResponse": {"type": "structure", "members": {"Arn": {"shape": "DeviceProfileArn", "documentation": "<p>The Amazon Resource Name of the resource.</p>"}, "Name": {"shape": "DeviceProfileName", "documentation": "<p>The name of the resource.</p>"}, "Id": {"shape": "DeviceProfileId", "documentation": "<p>The ID of the device profile.</p>"}, "LoRaWAN": {"shape": "LoRaWANDeviceProfile", "documentation": "<p>Information about the device profile.</p>"}, "Sidewalk": {"shape": "SidewalkGetDeviceProfile", "documentation": "<p>Information about the Sidewalk parameters in the device profile.</p>"}}}, "GetEventConfigurationByResourceTypesRequest": {"type": "structure", "members": {}}, "GetEventConfigurationByResourceTypesResponse": {"type": "structure", "members": {"DeviceRegistrationState": {"shape": "DeviceRegistrationStateResourceTypeEventConfiguration", "documentation": "<p>Resource type event configuration for the device registration state event.</p>"}, "Proximity": {"shape": "ProximityResourceTypeEventConfiguration", "documentation": "<p>Resource type event configuration for the proximity event.</p>"}, "Join": {"shape": "JoinResourceTypeEventConfiguration", "documentation": "<p>Resource type event configuration for the join event.</p>"}, "ConnectionStatus": {"shape": "ConnectionStatusResourceTypeEventConfiguration", "documentation": "<p>Resource type event configuration for the connection status event.</p>"}, "MessageDeliveryStatus": {"shape": "MessageDeliveryStatusResourceTypeEventConfiguration", "documentation": "<p>Resource type event configuration object for the message delivery status event.</p>"}}}, "GetFuotaTaskRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "FuotaTaskId", "location": "uri", "locationName": "Id"}}}, "GetFuotaTaskResponse": {"type": "structure", "members": {"Arn": {"shape": "FuotaTaskArn"}, "Id": {"shape": "FuotaTaskId"}, "Status": {"shape": "FuotaTaskStatus"}, "Name": {"shape": "FuotaTaskName"}, "Description": {"shape": "Description"}, "LoRaWAN": {"shape": "LoRaWANFuotaTaskGetInfo"}, "FirmwareUpdateImage": {"shape": "FirmwareUpdateImage"}, "FirmwareUpdateRole": {"shape": "FirmwareUpdateRole"}, "CreatedAt": {"shape": "CreatedAt"}, "RedundancyPercent": {"shape": "RedundancyPercent"}, "FragmentSizeBytes": {"shape": "FragmentSizeBytes"}, "FragmentIntervalMS": {"shape": "FragmentIntervalMS"}}}, "GetLogLevelsByResourceTypesRequest": {"type": "structure", "members": {}}, "GetLogLevelsByResourceTypesResponse": {"type": "structure", "members": {"DefaultLogLevel": {"shape": "LogLevel"}, "WirelessGatewayLogOptions": {"shape": "WirelessGatewayLogOptionList"}, "WirelessDeviceLogOptions": {"shape": "WirelessDeviceLogOptionList"}}}, "GetMulticastGroupRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "MulticastGroupId", "location": "uri", "locationName": "Id"}}}, "GetMulticastGroupResponse": {"type": "structure", "members": {"Arn": {"shape": "MulticastGroupArn"}, "Id": {"shape": "MulticastGroupId"}, "Name": {"shape": "MulticastGroupName"}, "Description": {"shape": "Description"}, "Status": {"shape": "MulticastGroupStatus"}, "LoRaWAN": {"shape": "LoRaWANMulticastGet"}, "CreatedAt": {"shape": "CreatedAt"}}}, "GetMulticastGroupSessionRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "MulticastGroupId", "location": "uri", "locationName": "Id"}}}, "GetMulticastGroupSessionResponse": {"type": "structure", "members": {"LoRaWAN": {"shape": "LoRaWANMulticastSession"}}}, "GetNetworkAnalyzerConfigurationRequest": {"type": "structure", "required": ["ConfigurationName"], "members": {"ConfigurationName": {"shape": "NetworkAnalyzerConfigurationName", "location": "uri", "locationName": "ConfigurationName"}}}, "GetNetworkAnalyzerConfigurationResponse": {"type": "structure", "members": {"TraceContent": {"shape": "Trace<PERSON><PERSON>nt"}, "WirelessDevices": {"shape": "WirelessDeviceList", "documentation": "<p>List of wireless device resources that have been added to the network analyzer configuration.</p>"}, "WirelessGateways": {"shape": "WirelessGatewayList", "documentation": "<p>List of wireless gateway resources that have been added to the network analyzer configuration.</p>"}, "Description": {"shape": "Description"}, "Arn": {"shape": "NetworkAnalyzerConfigurationArn", "documentation": "<p>The Amazon Resource Name of the new resource.</p>"}, "Name": {"shape": "NetworkAnalyzerConfigurationName"}, "MulticastGroups": {"shape": "NetworkAnalyzerMulticastGroupList", "documentation": "<p>List of multicast group resources that have been added to the network analyzer configuration.</p>"}}}, "GetPartnerAccountRequest": {"type": "structure", "required": ["PartnerAccountId", "PartnerType"], "members": {"PartnerAccountId": {"shape": "PartnerAccountId", "documentation": "<p>The partner account ID to disassociate from the AWS account.</p>", "location": "uri", "locationName": "PartnerAccountId"}, "PartnerType": {"shape": "PartnerType", "documentation": "<p>The partner type.</p>", "location": "querystring", "locationName": "partnerType"}}}, "GetPartnerAccountResponse": {"type": "structure", "members": {"Sidewalk": {"shape": "SidewalkAccountInfoWithFingerprint", "documentation": "<p>The Sidewalk account credentials.</p>"}, "AccountLinked": {"shape": "AccountLinked", "documentation": "<p>Whether the partner account is linked to the AWS account.</p>"}}}, "GetPositionConfigurationRequest": {"type": "structure", "required": ["ResourceIdentifier", "ResourceType"], "members": {"ResourceIdentifier": {"shape": "PositionResourceIdentifier", "documentation": "<p>Resource identifier used in a position configuration.</p>", "location": "uri", "locationName": "ResourceIdentifier"}, "ResourceType": {"shape": "PositionResourceType", "documentation": "<p>Resource type of the resource for which position configuration is retrieved.</p>", "location": "querystring", "locationName": "resourceType"}}, "deprecated": true, "deprecatedMessage": "This operation is no longer supported."}, "GetPositionConfigurationResponse": {"type": "structure", "members": {"Solvers": {"shape": "PositionSolverDetails", "documentation": "<p>The wrapper for the solver configuration details object.</p>"}, "Destination": {"shape": "DestinationName", "documentation": "<p>The position data destination that describes the AWS IoT rule that processes the device's position data for use by AWS IoT Core for LoRaWAN.</p>"}}, "deprecated": true, "deprecatedMessage": "This operation is no longer supported."}, "GetPositionEstimateRequest": {"type": "structure", "members": {"WiFiAccessPoints": {"shape": "WiFiAccessPoints", "documentation": "<p>Retrieves an estimated device position by resolving WLAN measurement data. The position is resolved using HERE's Wi-Fi based solver.</p>"}, "CellTowers": {"shape": "CellTowers", "documentation": "<p>Retrieves an estimated device position by resolving measurement data from cellular radio towers. The position is resolved using HERE's cellular-based solver.</p>"}, "Ip": {"shape": "Ip", "documentation": "<p>Retrieves an estimated device position by resolving the IP address information from the device. The position is resolved using MaxMind's IP-based solver.</p>"}, "Gnss": {"shape": "Gnss", "documentation": "<p>Retrieves an estimated device position by resolving the global navigation satellite system (GNSS) scan data. The position is resolved using the GNSS solver powered by LoRa Cloud.</p>"}, "Timestamp": {"shape": "CreationDate", "documentation": "<p>Optional information that specifies the time when the position information will be resolved. It uses the Unix timestamp format. If not specified, the time at which the request was received will be used.</p>"}}}, "GetPositionEstimateResponse": {"type": "structure", "members": {"GeoJsonPayload": {"shape": "GeoJsonPayload", "documentation": "<p>The position information of the resource, displayed as a JSON payload. The payload is of type blob and uses the <a href=\"https://geojson.org/\">GeoJSON</a> format, which a format that's used to encode geographic data structures. A sample payload contains the timestamp information, the WGS84 coordinates of the location, and the accuracy and confidence level. For more information and examples, see <a href=\"https://docs.aws.amazon.com/iot/latest/developerguide/location-resolve-console.html\">Resolve device location (console)</a>.</p>"}}, "payload": "GeoJsonPayload"}, "GetPositionRequest": {"type": "structure", "required": ["ResourceIdentifier", "ResourceType"], "members": {"ResourceIdentifier": {"shape": "PositionResourceIdentifier", "documentation": "<p>Resource identifier used to retrieve the position information.</p>", "location": "uri", "locationName": "ResourceIdentifier"}, "ResourceType": {"shape": "PositionResourceType", "documentation": "<p>Resource type of the resource for which position information is retrieved.</p>", "location": "querystring", "locationName": "resourceType"}}, "deprecated": true, "deprecatedMessage": "This operation is no longer supported."}, "GetPositionResponse": {"type": "structure", "members": {"Position": {"shape": "PositionCoordinate", "documentation": "<p>The position information of the resource.</p>"}, "Accuracy": {"shape": "Accuracy", "documentation": "<p>The accuracy of the estimated position in meters. An empty value indicates that no position data is available. A value of ‘0.0’ value indicates that position data is available. This data corresponds to the position information that you specified instead of the position computed by solver.</p>"}, "SolverType": {"shape": "PositionSolverType", "documentation": "<p>The type of solver used to identify the position of the resource.</p>"}, "SolverProvider": {"shape": "PositionSolverProvider", "documentation": "<p>The vendor of the positioning solver.</p>"}, "SolverVersion": {"shape": "PositionSolverVersion", "documentation": "<p>The version of the positioning solver.</p>"}, "Timestamp": {"shape": "ISODateTimeString", "documentation": "<p>The timestamp at which the device's position was determined.</p>"}}, "deprecated": true, "deprecatedMessage": "This operation is no longer supported."}, "GetResourceEventConfigurationRequest": {"type": "structure", "required": ["Identifier", "IdentifierType"], "members": {"Identifier": {"shape": "Identifier", "documentation": "<p>Resource identifier to opt in for event messaging.</p>", "location": "uri", "locationName": "Identifier"}, "IdentifierType": {"shape": "IdentifierType", "documentation": "<p>Identifier type of the particular resource identifier for event configuration.</p>", "location": "querystring", "locationName": "identifierType"}, "PartnerType": {"shape": "EventNotificationPartnerType", "documentation": "<p>Partner type of the resource if the identifier type is <code>PartnerAccountId</code>.</p>", "location": "querystring", "locationName": "partnerType"}}}, "GetResourceEventConfigurationResponse": {"type": "structure", "members": {"DeviceRegistrationState": {"shape": "DeviceRegistrationStateEventConfiguration", "documentation": "<p>Event configuration for the device registration state event.</p>"}, "Proximity": {"shape": "ProximityEventConfiguration", "documentation": "<p>Event configuration for the proximity event.</p>"}, "Join": {"shape": "JoinEventConfiguration", "documentation": "<p>Event configuration for the join event.</p>"}, "ConnectionStatus": {"shape": "ConnectionStatusEventConfiguration", "documentation": "<p>Event configuration for the connection status event.</p>"}, "MessageDeliveryStatus": {"shape": "MessageDeliveryStatusEventConfiguration", "documentation": "<p>Event configuration for the message delivery status event.</p>"}}}, "GetResourceLogLevelRequest": {"type": "structure", "required": ["ResourceIdentifier", "ResourceType"], "members": {"ResourceIdentifier": {"shape": "ResourceIdentifier", "location": "uri", "locationName": "ResourceIdentifier"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The type of the resource, which can be <code>WirelessDevice</code> or <code>WirelessGateway</code>.</p>", "location": "querystring", "locationName": "resourceType"}}}, "GetResourceLogLevelResponse": {"type": "structure", "members": {"LogLevel": {"shape": "LogLevel"}}}, "GetResourcePositionRequest": {"type": "structure", "required": ["ResourceIdentifier", "ResourceType"], "members": {"ResourceIdentifier": {"shape": "PositionResourceIdentifier", "documentation": "<p>The identifier of the resource for which position information is retrieved. It can be the wireless device ID or the wireless gateway ID, depending on the resource type.</p>", "location": "uri", "locationName": "ResourceIdentifier"}, "ResourceType": {"shape": "PositionResourceType", "documentation": "<p>The type of resource for which position information is retrieved, which can be a wireless device or a wireless gateway.</p>", "location": "querystring", "locationName": "resourceType"}}}, "GetResourcePositionResponse": {"type": "structure", "members": {"GeoJsonPayload": {"shape": "GeoJsonPayload", "documentation": "<p>The position information of the resource, displayed as a JSON payload. The payload uses the GeoJSON format, which a format that's used to encode geographic data structures. For more information, see <a href=\"https://geojson.org/\">GeoJSON</a>.</p>"}}, "payload": "GeoJsonPayload"}, "GetServiceEndpointRequest": {"type": "structure", "members": {"ServiceType": {"shape": "WirelessGatewayServiceType", "documentation": "<p>The service type for which to get endpoint information about. Can be <code>CUPS</code> for the Configuration and Update Server endpoint, or <code>LNS</code> for the LoRaWAN Network Server endpoint or <code>CLAIM</code> for the global endpoint.</p>", "location": "querystring", "locationName": "serviceType"}}}, "GetServiceEndpointResponse": {"type": "structure", "members": {"ServiceType": {"shape": "WirelessGatewayServiceType", "documentation": "<p>The endpoint's service type.</p>"}, "ServiceEndpoint": {"shape": "EndPoint", "documentation": "<p>The service endpoint value.</p>"}, "ServerTrust": {"shape": "CertificatePEM", "documentation": "<p>The Root CA of the server trust certificate.</p>"}}}, "GetServiceProfileRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "ServiceProfileId", "documentation": "<p>The ID of the resource to get.</p>", "location": "uri", "locationName": "Id"}}}, "GetServiceProfileResponse": {"type": "structure", "members": {"Arn": {"shape": "ServiceProfileArn", "documentation": "<p>The Amazon Resource Name of the resource.</p>"}, "Name": {"shape": "ServiceProfileName", "documentation": "<p>The name of the resource.</p>"}, "Id": {"shape": "ServiceProfileId", "documentation": "<p>The ID of the service profile.</p>"}, "LoRaWAN": {"shape": "LoRaWANGetServiceProfileInfo", "documentation": "<p>Information about the service profile.</p>"}}}, "GetWirelessDeviceImportTaskRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "ImportTaskId", "documentation": "<p>The identifier of the import task for which information is requested.</p>", "location": "uri", "locationName": "Id"}}}, "GetWirelessDeviceImportTaskResponse": {"type": "structure", "members": {"Id": {"shape": "ImportTaskId", "documentation": "<p>The identifier of the import task for which information is retrieved.</p>"}, "Arn": {"shape": "ImportTaskArn", "documentation": "<p>The ARN (Amazon Resource Name) of the import task.</p>"}, "DestinationName": {"shape": "DestinationName", "documentation": "<p>The name of the destination that's assigned to the wireless devices in the import task.</p>"}, "Sidewalk": {"shape": "SidewalkGetStartImportInfo", "documentation": "<p>The Sidewalk-related information about an import task.</p>"}, "CreationTime": {"shape": "CreationTime", "documentation": "<p>The time at which the import task was created.</p>"}, "Status": {"shape": "ImportTaskStatus", "documentation": "<p>The import task status.</p>"}, "StatusReason": {"shape": "StatusReason", "documentation": "<p>The reason for the provided status information, such as a validation error that causes the import task to fail.</p>"}, "InitializedImportedDeviceCount": {"shape": "ImportedWirelessDeviceCount", "documentation": "<p>The number of devices in the import task that are waiting for the control log to start processing.</p>"}, "PendingImportedDeviceCount": {"shape": "ImportedWirelessDeviceCount", "documentation": "<p>The number of devices in the import task that are waiting in the import task queue to be onboarded.</p>"}, "OnboardedImportedDeviceCount": {"shape": "ImportedWirelessDeviceCount", "documentation": "<p>The number of devices in the import task that have been onboarded to the import task.</p>"}, "FailedImportedDeviceCount": {"shape": "ImportedWirelessDeviceCount", "documentation": "<p>The number of devices in the import task that failed to onboard to the import task.</p>"}}}, "GetWirelessDeviceRequest": {"type": "structure", "required": ["Identifier", "IdentifierType"], "members": {"Identifier": {"shape": "Identifier", "documentation": "<p>The identifier of the wireless device to get.</p>", "location": "uri", "locationName": "Identifier"}, "IdentifierType": {"shape": "WirelessDeviceIdType", "documentation": "<p>The type of identifier used in <code>identifier</code>.</p>", "location": "querystring", "locationName": "identifierType"}}}, "GetWirelessDeviceResponse": {"type": "structure", "members": {"Type": {"shape": "WirelessDeviceType", "documentation": "<p>The wireless device type.</p>"}, "Name": {"shape": "WirelessDeviceName", "documentation": "<p>The name of the resource.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the resource.</p>"}, "DestinationName": {"shape": "DestinationName", "documentation": "<p>The name of the destination to which the device is assigned.</p>"}, "Id": {"shape": "WirelessDeviceId", "documentation": "<p>The ID of the wireless device.</p>"}, "Arn": {"shape": "WirelessDeviceArn", "documentation": "<p>The Amazon Resource Name of the resource.</p>"}, "ThingName": {"shape": "ThingName", "documentation": "<p>The name of the thing associated with the wireless device. The value is empty if a thing isn't associated with the device.</p>"}, "ThingArn": {"shape": "ThingArn", "documentation": "<p>The ARN of the thing associated with the wireless device.</p>"}, "LoRaWAN": {"shape": "LoRaWANDevice", "documentation": "<p>Information about the wireless device.</p>"}, "Sidewalk": {"shape": "SidewalkDevice", "documentation": "<p>Sidewalk device object.</p>"}, "Positioning": {"shape": "PositioningConfigStatus", "documentation": "<p>FPort values for the GNSS, stream, and ClockSync functions of the positioning information.</p>"}}}, "GetWirelessDeviceStatisticsRequest": {"type": "structure", "required": ["WirelessDeviceId"], "members": {"WirelessDeviceId": {"shape": "WirelessDeviceId", "documentation": "<p>The ID of the wireless device for which to get the data.</p>", "location": "uri", "locationName": "Id"}}}, "GetWirelessDeviceStatisticsResponse": {"type": "structure", "members": {"WirelessDeviceId": {"shape": "WirelessDeviceId", "documentation": "<p>The ID of the wireless device.</p>"}, "LastUplinkReceivedAt": {"shape": "ISODateTimeString", "documentation": "<p>The date and time when the most recent uplink was received.</p> <note> <p>This value is only valid for 3 months.</p> </note>"}, "LoRaWAN": {"shape": "LoRaWANDeviceMetadata", "documentation": "<p>Information about the wireless device's operations.</p>"}, "Sidewalk": {"shape": "SidewalkDeviceMetadata", "documentation": "<p>MetaData for Sidewalk device.</p>"}}}, "GetWirelessGatewayCertificateRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "WirelessGatewayId", "documentation": "<p>The ID of the resource to get.</p>", "location": "uri", "locationName": "Id"}}}, "GetWirelessGatewayCertificateResponse": {"type": "structure", "members": {"IotCertificateId": {"shape": "IotCertificateId", "documentation": "<p>The ID of the certificate associated with the wireless gateway.</p>"}, "LoRaWANNetworkServerCertificateId": {"shape": "IotCertificateId", "documentation": "<p>The ID of the certificate that is associated with the wireless gateway and used for the LoRaWANNetworkServer endpoint.</p>"}}}, "GetWirelessGatewayFirmwareInformationRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "WirelessGatewayId", "documentation": "<p>The ID of the resource to get.</p>", "location": "uri", "locationName": "Id"}}}, "GetWirelessGatewayFirmwareInformationResponse": {"type": "structure", "members": {"LoRaWAN": {"shape": "LoRaWANGatewayCurrentVersion", "documentation": "<p>Information about the wireless gateway's firmware.</p>"}}}, "GetWirelessGatewayRequest": {"type": "structure", "required": ["Identifier", "IdentifierType"], "members": {"Identifier": {"shape": "Identifier", "documentation": "<p>The identifier of the wireless gateway to get.</p>", "location": "uri", "locationName": "Identifier"}, "IdentifierType": {"shape": "WirelessGatewayIdType", "documentation": "<p>The type of identifier used in <code>identifier</code>.</p>", "location": "querystring", "locationName": "identifierType"}}}, "GetWirelessGatewayResponse": {"type": "structure", "members": {"Name": {"shape": "WirelessGatewayName", "documentation": "<p>The name of the resource.</p>"}, "Id": {"shape": "WirelessGatewayId", "documentation": "<p>The ID of the wireless gateway.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the resource.</p>"}, "LoRaWAN": {"shape": "LoRaWANGateway", "documentation": "<p>Information about the wireless gateway.</p>"}, "Arn": {"shape": "WirelessGatewayArn", "documentation": "<p>The Amazon Resource Name of the resource.</p>"}, "ThingName": {"shape": "ThingName", "documentation": "<p>The name of the thing associated with the wireless gateway. The value is empty if a thing isn't associated with the gateway.</p>"}, "ThingArn": {"shape": "ThingArn", "documentation": "<p>The ARN of the thing associated with the wireless gateway.</p>"}}}, "GetWirelessGatewayStatisticsRequest": {"type": "structure", "required": ["WirelessGatewayId"], "members": {"WirelessGatewayId": {"shape": "WirelessGatewayId", "documentation": "<p>The ID of the wireless gateway for which to get the data.</p>", "location": "uri", "locationName": "Id"}}}, "GetWirelessGatewayStatisticsResponse": {"type": "structure", "members": {"WirelessGatewayId": {"shape": "WirelessGatewayId", "documentation": "<p>The ID of the wireless gateway.</p>"}, "LastUplinkReceivedAt": {"shape": "ISODateTimeString", "documentation": "<p>The date and time when the most recent uplink was received.</p> <note> <p>This value is only valid for 3 months.</p> </note>"}, "ConnectionStatus": {"shape": "ConnectionStatus", "documentation": "<p>The connection status of the wireless gateway.</p>"}}}, "GetWirelessGatewayTaskDefinitionRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "WirelessGatewayTaskDefinitionId", "documentation": "<p>The ID of the resource to get.</p>", "location": "uri", "locationName": "Id"}}}, "GetWirelessGatewayTaskDefinitionResponse": {"type": "structure", "members": {"AutoCreateTasks": {"shape": "AutoCreateTasks", "documentation": "<p>Whether to automatically create tasks using this task definition for all gateways with the specified current version. If <code>false</code>, the task must me created by calling <code>CreateWirelessGatewayTask</code>.</p>"}, "Name": {"shape": "WirelessGatewayTaskName", "documentation": "<p>The name of the resource.</p>"}, "Update": {"shape": "UpdateWirelessGatewayTaskCreate", "documentation": "<p>Information about the gateways to update.</p>"}, "Arn": {"shape": "WirelessGatewayTaskDefinitionArn", "documentation": "<p>The Amazon Resource Name of the resource.</p>"}}}, "GetWirelessGatewayTaskRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "WirelessGatewayId", "documentation": "<p>The ID of the resource to get.</p>", "location": "uri", "locationName": "Id"}}}, "GetWirelessGatewayTaskResponse": {"type": "structure", "members": {"WirelessGatewayId": {"shape": "WirelessGatewayId", "documentation": "<p>The ID of the wireless gateway.</p>"}, "WirelessGatewayTaskDefinitionId": {"shape": "WirelessGatewayTaskDefinitionId", "documentation": "<p>The ID of the WirelessGatewayTask.</p>"}, "LastUplinkReceivedAt": {"shape": "ISODateTimeString", "documentation": "<p>The date and time when the most recent uplink was received.</p> <note> <p>This value is only valid for 3 months.</p> </note>"}, "TaskCreatedAt": {"shape": "ISODateTimeString", "documentation": "<p>The date and time when the task was created.</p>"}, "Status": {"shape": "WirelessGatewayTaskStatus", "documentation": "<p>The status of the request.</p>"}}}, "GlobalIdentity": {"type": "structure", "required": ["Lac", "GeranCid"], "members": {"Lac": {"shape": "LAC", "documentation": "<p>Location area code of the global identity.</p>"}, "GeranCid": {"shape": "GeranCid", "documentation": "<p>GERAN (GSM EDGE Radio Access Network) cell global identifier.</p>"}}, "documentation": "<p>Global identity information.</p>"}, "Gnss": {"type": "structure", "required": ["Payload"], "members": {"Payload": {"shape": "GnssNav", "documentation": "<p>Payload that contains the GNSS scan result, or NAV message, in hexadecimal notation.</p>"}, "CaptureTime": {"shape": "GPST", "documentation": "<p>Optional parameter that gives an estimate of the time when the GNSS scan information is taken, in seconds GPS time (GPST). If capture time is not specified, the local server time is used.</p>"}, "CaptureTimeAccuracy": {"shape": "CaptureTimeAccuracy", "documentation": "<p>Optional value that gives the capture time estimate accuracy, in seconds. If capture time accuracy is not specified, default value of 300 is used.</p>"}, "AssistPosition": {"shape": "AssistPosition", "documentation": "<p>Optional assistance position information, specified using latitude and longitude values in degrees. The coordinates are inside the WGS84 reference frame.</p>"}, "AssistAltitude": {"shape": "Coordinate", "documentation": "<p>Optional assistance altitude, which is the altitude of the device at capture time, specified in meters above the WGS84 reference ellipsoid.</p>"}, "Use2DSolver": {"shape": "Use2DSolver", "documentation": "<p>Optional parameter that forces 2D solve, which modifies the positioning algorithm to a 2D solution problem. When this parameter is specified, the assistance altitude should have an accuracy of at least 10 meters.</p>"}}, "documentation": "<p>Global navigation satellite system (GNSS) object used for positioning.</p>"}, "GnssNav": {"type": "string", "max": 2048}, "GsmList": {"type": "list", "member": {"shape": "GsmObj"}, "max": 16, "min": 1}, "GsmLocalId": {"type": "structure", "required": ["Bsic", "Bcch"], "members": {"Bsic": {"shape": "BSIC", "documentation": "<p>GSM base station identity code (BSIC).</p>"}, "Bcch": {"shape": "BCCH", "documentation": "<p>GSM broadcast control channel.</p>"}}, "documentation": "<p>GSM local ID information, which corresponds to the local identification parameters of a GSM cell.</p>"}, "GsmNmrList": {"type": "list", "member": {"shape": "GsmNmrObj"}, "max": 32, "min": 1}, "GsmNmrObj": {"type": "structure", "required": ["Bsic", "Bcch"], "members": {"Bsic": {"shape": "BSIC", "documentation": "<p>GSM base station identity code (BSIC).</p>"}, "Bcch": {"shape": "BCCH", "documentation": "<p>GSM broadcast control channel.</p>"}, "RxLevel": {"shape": "RxLevel", "documentation": "<p>Rx level, which is the received signal power, measured in dBm (decibel-milliwatts).</p>"}, "GlobalIdentity": {"shape": "GlobalIdentity", "documentation": "<p>Global identity information of the GSM object.</p>"}}, "documentation": "<p>GSM object for network measurement reports.</p>"}, "GsmObj": {"type": "structure", "required": ["Mcc", "Mnc", "Lac", "GeranCid"], "members": {"Mcc": {"shape": "MCC", "documentation": "<p>Mobile Country Code.</p>"}, "Mnc": {"shape": "MNC", "documentation": "<p>Mobile Network Code.</p>"}, "Lac": {"shape": "LAC", "documentation": "<p>Location area code.</p>"}, "GeranCid": {"shape": "GeranCid", "documentation": "<p>GERAN (GSM EDGE Radio Access Network) Cell Global Identifier.</p>"}, "GsmLocalId": {"shape": "GsmLocalId", "documentation": "<p>GSM local identification (local ID) information.</p>"}, "GsmTimingAdvance": {"shape": "GsmTimingAdvance", "documentation": "<p>Timing advance value, which corresponds to the length of time a signal takes to reach the base station from a mobile phone.</p>"}, "RxLevel": {"shape": "RxLevel", "documentation": "<p>Rx level, which is the received signal power, measured in dBm (decibel-milliwatts).</p>"}, "GsmNmr": {"shape": "GsmNmrList", "documentation": "<p>GSM object for network measurement reports.</p>"}}, "documentation": "<p>GSM object.</p>"}, "GsmTimingAdvance": {"type": "integer", "max": 63, "min": 0}, "HorizontalAccuracy": {"type": "float", "min": 0}, "HrAllowed": {"type": "boolean"}, "IPAddress": {"type": "string"}, "ISODateTimeString": {"type": "string", "pattern": "^([\\+-]?\\d{4}(?!\\d{2}\\b))((-?)((0[1-9]|1[0-2])(\\3([12]\\d|0[1-9]|3[01]))?|W([0-4]\\d|5[0-2])(-?[1-7])?|(00[1-9]|0[1-9]\\d|[12]\\d{2}|3([0-5]\\d|6[1-6])))([T\\s]((([01]\\d|2[0-3])((:?)[0-5]\\d)?|24\\:?00)([\\.,]\\d+(?!:))?)?(\\17[0-5]\\d([\\.,]\\d+)?)?([zZ]|([\\+-])([01]\\d|2[0-3]):?([0-5]\\d)?)?)?)?$"}, "Identifier": {"type": "string", "max": 256}, "IdentifierType": {"type": "string", "enum": ["PartnerAccountId", "DevEui", "GatewayEui", "WirelessDeviceId", "WirelessGatewayId"]}, "ImportTaskArn": {"type": "string", "max": 128}, "ImportTaskId": {"type": "string", "max": 256}, "ImportTaskStatus": {"type": "string", "enum": ["INITIALIZING", "INITIALIZED", "PENDING", "COMPLETE", "FAILED", "DELETING"]}, "ImportedSidewalkDevice": {"type": "structure", "members": {"SidewalkManufacturingSn": {"shape": "SidewalkManufacturingSn", "documentation": "<p>The Sidewalk manufacturing serial number (SMSN) of the Sidewalk device.</p>"}, "OnboardingStatus": {"shape": "OnboardStatus", "documentation": "<p>The onboarding status of the Sidewalk device in the import task.</p>"}, "OnboardingStatusReason": {"shape": "OnboardStatusReason", "documentation": "<p>The reason for the onboarding status information for the Sidewalk device.</p>"}, "LastUpdateTime": {"shape": "LastUpdateTime", "documentation": "<p>The time at which the status information was last updated.</p>"}}, "documentation": "<p>Information about a Sidewalk device that has been added to an import task.</p>"}, "ImportedWirelessDevice": {"type": "structure", "members": {"Sidewalk": {"shape": "ImportedSidewalkDevice", "documentation": "<p>The Sidewalk-related information about a device that has been added to an import task.</p>"}}, "documentation": "<p>Information about a wireless device that has been added to an import task.</p>"}, "ImportedWirelessDeviceCount": {"type": "long"}, "ImportedWirelessDeviceList": {"type": "list", "member": {"shape": "ImportedWirelessDevice"}}, "Integer": {"type": "integer"}, "InternalServerException": {"type": "structure", "members": {"Message": {"shape": "Message"}}, "documentation": "<p>An unexpected error occurred while processing a request.</p>", "error": {"httpStatusCode": 500}, "exception": true}, "IotCertificateId": {"type": "string", "max": 4096, "min": 1}, "Ip": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "members": {"IpAddress": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>IP address information.</p>"}}, "documentation": "<p>IP address used for resolving device location.</p>"}, "JoinEui": {"type": "string", "pattern": "[a-fA-F0-9]{16}"}, "JoinEuiFilters": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "documentation": "<p>A list of JoinEuiRange used by LoRa gateways to filter LoRa frames.</p>", "max": 3, "min": 0}, "JoinEuiRange": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON>"}, "documentation": "<p>A pair of join EUI describing a range [BegEui, EndEui], both ends are inclusive.</p>", "max": 2, "min": 2}, "JoinEventConfiguration": {"type": "structure", "members": {"LoRaWAN": {"shape": "LoRaWANJoinEventNotificationConfigurations", "documentation": "<p>Join event configuration object for enabling or disabling LoRaWAN related event topics.</p>"}, "WirelessDeviceIdEventTopic": {"shape": "EventNotificationTopicStatus", "documentation": "<p>Denotes whether the wireless device ID join event topic is enabled or disabled.</p>"}}, "documentation": "<p>Join event configuration object for enabling or disabling topic.</p>"}, "JoinResourceTypeEventConfiguration": {"type": "structure", "members": {"LoRaWAN": {"shape": "LoRaWANJoinResourceTypeEventConfiguration", "documentation": "<p>Join resource type event configuration object for enabling or disabling LoRaWAN related event topics.</p>"}}, "documentation": "<p>Join resource type event configuration object for enabling or disabling topic.</p>"}, "LAC": {"type": "integer", "max": 65535, "min": 1}, "LastUpdateTime": {"type": "timestamp", "timestampFormat": "iso8601"}, "ListDestinationsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in this operation.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>To retrieve the next set of results, the <code>nextToken</code> value from a previous response; otherwise <b>null</b> to receive the first set of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListDestinationsResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to get the next set of results, or <b>null</b> if there are no additional results.</p>"}, "DestinationList": {"shape": "DestinationList", "documentation": "<p>The list of destinations.</p>"}}}, "ListDeviceProfilesRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>To retrieve the next set of results, the <code>nextToken</code> value from a previous response; otherwise <b>null</b> to receive the first set of results.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in this operation.</p>", "location": "querystring", "locationName": "maxResults"}, "DeviceProfileType": {"shape": "DeviceProfileType", "documentation": "<p>A filter to list only device profiles that use this type, which can be <code>LoRaWAN</code> or <code>Sidewalk</code>.</p>", "location": "querystring", "locationName": "deviceProfileType"}}}, "ListDeviceProfilesResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to get the next set of results, or <b>null</b> if there are no additional results.</p>"}, "DeviceProfileList": {"shape": "DeviceProfileList", "documentation": "<p>The list of device profiles.</p>"}}}, "ListDevicesForWirelessDeviceImportTaskRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "ImportTaskId", "documentation": "<p>The identifier of the import task for which wireless devices are listed.</p>", "location": "querystring", "locationName": "id"}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>To retrieve the next set of results, the <code>nextToken</code> value from a previous response; otherwise <code>null</code> to receive the first set of results.</p>", "location": "querystring", "locationName": "nextToken"}, "Status": {"shape": "OnboardStatus", "documentation": "<p>The status of the devices in the import task.</p>", "location": "querystring", "locationName": "status"}}}, "ListDevicesForWirelessDeviceImportTaskResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to get the next set of results, or <code>null</code> if there are no additional results.</p>"}, "DestinationName": {"shape": "DestinationName", "documentation": "<p>The name of the Sidewalk destination that describes the IoT rule to route messages received from devices in an import task that are onboarded to AWS IoT Wireless.</p>"}, "ImportedWirelessDeviceList": {"shape": "ImportedWirelessDeviceList", "documentation": "<p>List of wireless devices in an import task and their onboarding status.</p>"}}}, "ListEventConfigurationsRequest": {"type": "structure", "required": ["ResourceType"], "members": {"ResourceType": {"shape": "EventNotificationResourceType", "documentation": "<p>Resource type to filter event configurations.</p>", "location": "querystring", "locationName": "resourceType"}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>To retrieve the next set of results, the <code>nextToken</code> value from a previous response; otherwise <b>null</b> to receive the first set of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListEventConfigurationsResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>To retrieve the next set of results, the <code>nextToken</code> value from a previous response; otherwise <b>null</b> to receive the first set of results.</p>"}, "EventConfigurationsList": {"shape": "EventConfigurationsList", "documentation": "<p>Event configurations of all events for a single resource.</p>"}}}, "ListFuotaTasksRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>To retrieve the next set of results, the <code>nextToken</code> value from a previous response; otherwise <b>null</b> to receive the first set of results.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults"}}}, "ListFuotaTasksResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>To retrieve the next set of results, the <code>nextToken</code> value from a previous response; otherwise <b>null</b> to receive the first set of results.</p>"}, "FuotaTaskList": {"shape": "FuotaTaskList"}}}, "ListMulticastGroupsByFuotaTaskRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "FuotaTaskId", "location": "uri", "locationName": "Id"}, "NextToken": {"shape": "NextToken", "documentation": "<p>To retrieve the next set of results, the <code>nextToken</code> value from a previous response; otherwise <b>null</b> to receive the first set of results.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults"}}}, "ListMulticastGroupsByFuotaTaskResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>To retrieve the next set of results, the <code>nextToken</code> value from a previous response; otherwise <b>null</b> to receive the first set of results.</p>"}, "MulticastGroupList": {"shape": "MulticastGroupListByFuotaTask"}}}, "ListMulticastGroupsRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>To retrieve the next set of results, the <code>nextToken</code> value from a previous response; otherwise <b>null</b> to receive the first set of results.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults"}}}, "ListMulticastGroupsResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>To retrieve the next set of results, the <code>nextToken</code> value from a previous response; otherwise <b>null</b> to receive the first set of results.</p>"}, "MulticastGroupList": {"shape": "MulticastGroupList"}}}, "ListNetworkAnalyzerConfigurationsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>To retrieve the next set of results, the <code>nextToken</code> value from a previous response; otherwise <b>null</b> to receive the first set of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListNetworkAnalyzerConfigurationsResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to get the next set of results, or <b>null</b> if there are no additional results.</p>"}, "NetworkAnalyzerConfigurationList": {"shape": "NetworkAnalyzerConfigurationList", "documentation": "<p>The list of network analyzer configurations.</p>"}}}, "ListPartnerAccountsRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>To retrieve the next set of results, the <code>nextToken</code> value from a previous response; otherwise <b>null</b> to receive the first set of results.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in this operation.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListPartnerAccountsResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to get the next set of results, or <b>null</b> if there are no additional results.</p>"}, "Sidewalk": {"shape": "SidewalkAccountList", "documentation": "<p>The Sidewalk account credentials.</p>"}}}, "ListPositionConfigurationsRequest": {"type": "structure", "members": {"ResourceType": {"shape": "PositionResourceType", "documentation": "<p>Resource type for which position configurations are listed.</p>", "location": "querystring", "locationName": "resourceType"}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>To retrieve the next set of results, the <code>nextToken</code> value from a previous response; otherwise <b>null</b> to receive the first set of results.</p>", "location": "querystring", "locationName": "nextToken"}}, "deprecated": true, "deprecatedMessage": "This operation is no longer supported."}, "ListPositionConfigurationsResponse": {"type": "structure", "members": {"PositionConfigurationList": {"shape": "PositionConfigurationList", "documentation": "<p>A list of position configurations.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to get the next set of results, or <b>null</b> if there are no additional results.</p>"}}, "deprecated": true, "deprecatedMessage": "This operation is no longer supported."}, "ListQueuedMessagesRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "WirelessDeviceId", "documentation": "<p>The ID of a given wireless device which the downlink message packets are being sent.</p>", "location": "uri", "locationName": "Id"}, "NextToken": {"shape": "NextToken", "documentation": "<p>To retrieve the next set of results, the <code>nextToken</code> value from a previous response; otherwise <b>null</b> to receive the first set of results.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in this operation.</p>", "location": "querystring", "locationName": "maxResults"}, "WirelessDeviceType": {"shape": "WirelessDeviceType", "documentation": "<p>The wireless device type, whic can be either Sidewalk or LoRaWAN.</p>", "location": "querystring", "locationName": "WirelessDeviceType"}}}, "ListQueuedMessagesResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>To retrieve the next set of results, the <code>nextToken</code> value from a previous response; otherwise <b>null</b> to receive the first set of results.</p>"}, "DownlinkQueueMessagesList": {"shape": "DownlinkQueueMessagesList", "documentation": "<p>The messages in the downlink queue.</p>"}}}, "ListServiceProfilesRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>To retrieve the next set of results, the <code>nextToken</code> value from a previous response; otherwise <b>null</b> to receive the first set of results.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in this operation.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListServiceProfilesResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to get the next set of results, or <b>null</b> if there are no additional results.</p>"}, "ServiceProfileList": {"shape": "ServiceProfileList", "documentation": "<p>The list of service profiles.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "AmazonResourceName", "documentation": "<p>The ARN of the resource for which you want to list tags.</p>", "location": "querystring", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "TagList", "documentation": "<p>The tags to attach to the specified resource. Tags are metadata that you can use to manage a resource.</p>"}}}, "ListWirelessDeviceImportTasksRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>To retrieve the next set of results, the <code>nextToken</code> value from a previous response; otherwise <code>null</code> to receive the first set of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListWirelessDeviceImportTasksResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to get the next set of results, or <code>null</code> if there are no additional results.</p>"}, "WirelessDeviceImportTaskList": {"shape": "WirelessDeviceImportTaskList", "documentation": "<p>List of import tasks and summary information of onboarding status of devices in each import task.</p>"}}}, "ListWirelessDevicesRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in this operation.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>To retrieve the next set of results, the <code>nextToken</code> value from a previous response; otherwise <b>null</b> to receive the first set of results.</p>", "location": "querystring", "locationName": "nextToken"}, "DestinationName": {"shape": "DestinationName", "documentation": "<p>A filter to list only the wireless devices that use this destination.</p>", "location": "querystring", "locationName": "destinationName"}, "DeviceProfileId": {"shape": "DeviceProfileId", "documentation": "<p>A filter to list only the wireless devices that use this device profile.</p>", "location": "querystring", "locationName": "deviceProfileId"}, "ServiceProfileId": {"shape": "ServiceProfileId", "documentation": "<p>A filter to list only the wireless devices that use this service profile.</p>", "location": "querystring", "locationName": "serviceProfileId"}, "WirelessDeviceType": {"shape": "WirelessDeviceType", "documentation": "<p>A filter to list only the wireless devices that use this wireless device type.</p>", "location": "querystring", "locationName": "wirelessDeviceType"}, "FuotaTaskId": {"shape": "FuotaTaskId", "location": "querystring", "locationName": "fuotaTaskId"}, "MulticastGroupId": {"shape": "MulticastGroupId", "location": "querystring", "locationName": "multicastGroupId"}}}, "ListWirelessDevicesResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to get the next set of results, or <b>null</b> if there are no additional results.</p>"}, "WirelessDeviceList": {"shape": "WirelessDeviceStatisticsList", "documentation": "<p>The ID of the wireless device.</p>"}}}, "ListWirelessGatewayTaskDefinitionsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in this operation.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>To retrieve the next set of results, the <code>nextToken</code> value from a previous response; otherwise <b>null</b> to receive the first set of results.</p>", "location": "querystring", "locationName": "nextToken"}, "TaskDefinitionType": {"shape": "WirelessGatewayTaskDefinitionType", "documentation": "<p>A filter to list only the wireless gateway task definitions that use this task definition type.</p>", "location": "querystring", "locationName": "taskDefinitionType"}}}, "ListWirelessGatewayTaskDefinitionsResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to get the next set of results, or <b>null</b> if there are no additional results.</p>"}, "TaskDefinitions": {"shape": "WirelessGatewayTaskDefinitionList", "documentation": "<p>The list of task definitions.</p>"}}}, "ListWirelessGatewaysRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>To retrieve the next set of results, the <code>nextToken</code> value from a previous response; otherwise <b>null</b> to receive the first set of results.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in this operation.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListWirelessGatewaysResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to get the next set of results, or <b>null</b> if there are no additional results.</p>"}, "WirelessGatewayList": {"shape": "WirelessGatewayStatisticsList", "documentation": "<p>The ID of the wireless gateway.</p>"}}}, "LoRaWANConnectionStatusEventNotificationConfigurations": {"type": "structure", "members": {"GatewayEuiEventTopic": {"shape": "EventNotificationTopicStatus", "documentation": "<p>Denotes whether the gateway EUI connection status event topic is enabled or disabled.</p>"}}, "documentation": "<p>Object for LoRaWAN connection status resource type event configuration.</p>"}, "LoRaWANConnectionStatusResourceTypeEventConfiguration": {"type": "structure", "members": {"WirelessGatewayEventTopic": {"shape": "EventNotificationTopicStatus", "documentation": "<p>Denotes whether the wireless gateway connection status event topic is enabled or disabled.</p>"}}, "documentation": "<p>Object for LoRaWAN connection status resource type event configuration.</p>"}, "LoRaWANDevice": {"type": "structure", "members": {"DevEui": {"shape": "DevEui", "documentation": "<p>The DevEUI value.</p>"}, "DeviceProfileId": {"shape": "DeviceProfileId", "documentation": "<p>The ID of the device profile for the new wireless device.</p>"}, "ServiceProfileId": {"shape": "ServiceProfileId", "documentation": "<p>The ID of the service profile.</p>"}, "OtaaV1_1": {"shape": "OtaaV1_1", "documentation": "<p>OTAA device object for v1.1 for create APIs</p>"}, "OtaaV1_0_x": {"shape": "OtaaV1_0_x", "documentation": "<p>OTAA device object for create APIs for v1.0.x</p>"}, "AbpV1_1": {"shape": "AbpV1_1", "documentation": "<p>ABP device object for create APIs for v1.1</p>"}, "AbpV1_0_x": {"shape": "AbpV1_0_x", "documentation": "<p>LoRaWAN object for create APIs</p>"}, "FPorts": {"shape": "FPorts"}}, "documentation": "<p>LoRaWAN object for create functions.</p>"}, "LoRaWANDeviceMetadata": {"type": "structure", "members": {"DevEui": {"shape": "DevEui", "documentation": "<p>The DevEUI value.</p>"}, "FPort": {"shape": "Integer", "documentation": "<p>The FPort value.</p>"}, "DataRate": {"shape": "Integer", "documentation": "<p>The DataRate value.</p>"}, "Frequency": {"shape": "Integer", "documentation": "<p>The device's channel frequency in Hz.</p>"}, "Timestamp": {"shape": "ISODateTimeString", "documentation": "<p>The date and time of the metadata.</p>"}, "Gateways": {"shape": "LoRaWANGatewayMetadataList", "documentation": "<p>Information about the gateways accessed by the device.</p>"}}, "documentation": "<p>LoRaWAN device metatdata.</p>"}, "LoRaWANDeviceProfile": {"type": "structure", "members": {"SupportsClassB": {"shape": "SupportsClassB", "documentation": "<p>The SupportsClassB value.</p>"}, "ClassBTimeout": {"shape": "ClassBTimeout", "documentation": "<p>The ClassBTimeout value.</p>"}, "PingSlotPeriod": {"shape": "PingSlotPeriod", "documentation": "<p>The PingSlotPeriod value.</p>"}, "PingSlotDr": {"shape": "PingSlotDr", "documentation": "<p>The PingSlotDR value.</p>"}, "PingSlotFreq": {"shape": "PingSlotFreq", "documentation": "<p>The PingSlotFreq value.</p>"}, "SupportsClassC": {"shape": "SupportsClassC", "documentation": "<p>The SupportsClassC value.</p>"}, "ClassCTimeout": {"shape": "ClassCTimeout", "documentation": "<p>The ClassCTimeout value.</p>"}, "MacVersion": {"shape": "MacVersion", "documentation": "<p>The MAC version (such as OTAA 1.1 or OTAA 1.0.3) to use with this device profile.</p>"}, "RegParamsRevision": {"shape": "RegParamsRevision", "documentation": "<p>The version of regional parameters.</p>"}, "RxDelay1": {"shape": "RxDelay1", "documentation": "<p>The RXDelay1 value.</p>"}, "RxDrOffset1": {"shape": "RxDrOffset1", "documentation": "<p>The RXDROffset1 value.</p>"}, "RxDataRate2": {"shape": "RxDataRate2", "documentation": "<p>The RXDataRate2 value.</p>"}, "RxFreq2": {"shape": "RxFreq2", "documentation": "<p>The RXFreq2 value.</p>"}, "FactoryPresetFreqsList": {"shape": "FactoryPresetFreqsList", "documentation": "<p>The list of values that make up the FactoryPresetFreqs value.</p>"}, "MaxEirp": {"shape": "MaxEirp", "documentation": "<p>The MaxEIRP value.</p>"}, "MaxDutyCycle": {"shape": "MaxDutyCycle", "documentation": "<p>The MaxDutyCycle value. It ranges from 0 to 15.</p>"}, "RfRegion": {"shape": "RfRegion", "documentation": "<p>The frequency band (RFRegion) value.</p>"}, "SupportsJoin": {"shape": "SupportsJoin", "documentation": "<p>The SupportsJoin value.</p>"}, "Supports32BitFCnt": {"shape": "Supports32BitFCnt", "documentation": "<p>The Supports32BitFCnt value.</p>"}}, "documentation": "<p>LoRaWANDeviceProfile object.</p>"}, "LoRaWANFuotaTask": {"type": "structure", "members": {"RfRegion": {"shape": "SupportedRfRegion"}}, "documentation": "<p>The LoRaWAN information used with a FUOTA task.</p>"}, "LoRaWANFuotaTaskGetInfo": {"type": "structure", "members": {"RfRegion": {"shape": "RfRegion"}, "StartTime": {"shape": "StartTime"}}, "documentation": "<p>The LoRaWAN information returned from getting a FUOTA task.</p>"}, "LoRaWANGateway": {"type": "structure", "members": {"GatewayEui": {"shape": "GatewayEui", "documentation": "<p>The gateway's EUI value.</p>"}, "RfRegion": {"shape": "RfRegion", "documentation": "<p>The frequency band (RFRegion) value.</p>"}, "JoinEuiFilters": {"shape": "Join<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "NetIdFilters": {"shape": "NetIdFilters"}, "SubBands": {"shape": "SubBands"}, "Beaconing": {"shape": "Beaconing", "documentation": "<p>Beaconing object information, which consists of the data rate and frequency parameters.</p>"}, "MaxEirp": {"shape": "GatewayMaxEirp", "documentation": "<p>The MaxEIRP value.</p>"}}, "documentation": "<p>LoRaWANGateway object.</p>"}, "LoRaWANGatewayCurrentVersion": {"type": "structure", "members": {"CurrentVersion": {"shape": "LoRaWANGatewayVersion", "documentation": "<p>The version of the gateways that should receive the update.</p>"}}, "documentation": "<p>LoRaWANGatewayCurrentVersion object.</p>"}, "LoRaWANGatewayMetadata": {"type": "structure", "members": {"GatewayEui": {"shape": "GatewayEui", "documentation": "<p>The gateway's EUI value.</p>"}, "Snr": {"shape": "Double", "documentation": "<p>The SNR value.</p>"}, "Rssi": {"shape": "Double", "documentation": "<p>The RSSI value.</p>"}}, "documentation": "<p>LoRaWAN gateway metatdata.</p>"}, "LoRaWANGatewayMetadataList": {"type": "list", "member": {"shape": "LoRaWANGatewayMetadata"}}, "LoRaWANGatewayVersion": {"type": "structure", "members": {"PackageVersion": {"shape": "PackageVersion", "documentation": "<p>The version of the wireless gateway firmware.</p>"}, "Model": {"shape": "Model", "documentation": "<p>The model number of the wireless gateway.</p>"}, "Station": {"shape": "Station", "documentation": "<p>The basic station version of the wireless gateway.</p>"}}, "documentation": "<p>LoRaWANGatewayVersion object.</p>"}, "LoRaWANGetServiceProfileInfo": {"type": "structure", "members": {"UlRate": {"shape": "UlRate", "documentation": "<p>The ULRate value.</p>"}, "UlBucketSize": {"shape": "UlBucketSize", "documentation": "<p>The ULBucketSize value.</p>"}, "UlRatePolicy": {"shape": "UlRatePolicy", "documentation": "<p>The ULRatePolicy value.</p>"}, "DlRate": {"shape": "DlRate", "documentation": "<p>The DLRate value.</p>"}, "DlBucketSize": {"shape": "DlBucketSize", "documentation": "<p>The DLBucketSize value.</p>"}, "DlRatePolicy": {"shape": "DlRatePolicy", "documentation": "<p>The DLRatePolicy value.</p>"}, "AddGwMetadata": {"shape": "AddGwMetadata", "documentation": "<p>The AddGWMetaData value.</p>"}, "DevStatusReqFreq": {"shape": "DevStatusReqFreq", "documentation": "<p>The DevStatusReqFreq value.</p>"}, "ReportDevStatusBattery": {"shape": "ReportDevStatusBattery", "documentation": "<p>The ReportDevStatusBattery value.</p>"}, "ReportDevStatusMargin": {"shape": "ReportDevStatusMargin", "documentation": "<p>The ReportDevStatusMargin value.</p>"}, "DrMin": {"shape": "Dr<PERSON><PERSON>", "documentation": "<p>The DRMin value.</p>"}, "DrMax": {"shape": "DrMax", "documentation": "<p>The DRMax value.</p>"}, "ChannelMask": {"shape": "ChannelMask", "documentation": "<p>The ChannelMask value.</p>"}, "PrAllowed": {"shape": "PrAllowed", "documentation": "<p>The PRAllowed value that describes whether passive roaming is allowed.</p>"}, "HrAllowed": {"shape": "<PERSON>r<PERSON><PERSON>ed", "documentation": "<p>The HRAllowed value that describes whether handover roaming is allowed.</p>"}, "RaAllowed": {"shape": "<PERSON><PERSON><PERSON>ed", "documentation": "<p>The RAAllowed value that describes whether roaming activation is allowed.</p>"}, "NwkGeoLoc": {"shape": "NwkGeoLoc", "documentation": "<p>The NwkGeoLoc value.</p>"}, "TargetPer": {"shape": "TargetPer", "documentation": "<p>The TargetPER value.</p>"}, "MinGwDiversity": {"shape": "MinGwDiversity", "documentation": "<p>The MinGwDiversity value.</p>"}}, "documentation": "<p>LoRaWANGetServiceProfileInfo object.</p>"}, "LoRaWANJoinEventNotificationConfigurations": {"type": "structure", "members": {"DevEuiEventTopic": {"shape": "EventNotificationTopicStatus", "documentation": "<p>Denotes whether the Dev EUI join event topic is enabled or disabled.</p>"}}, "documentation": "<p>Object for LoRaWAN join resource type event configuration.</p>"}, "LoRaWANJoinResourceTypeEventConfiguration": {"type": "structure", "members": {"WirelessDeviceEventTopic": {"shape": "EventNotificationTopicStatus", "documentation": "<p>Denotes whether the wireless device join event topic is enabled or disabled.</p>"}}, "documentation": "<p>Object for LoRaWAN join resource type event configuration.</p>"}, "LoRaWANListDevice": {"type": "structure", "members": {"DevEui": {"shape": "DevEui", "documentation": "<p>The DevEUI value.</p>"}}, "documentation": "<p>LoRaWAN object for list functions.</p>"}, "LoRaWANMulticast": {"type": "structure", "members": {"RfRegion": {"shape": "SupportedRfRegion"}, "DlClass": {"shape": "DlClass"}}, "documentation": "<p>The LoRaWAN information that is to be used with the multicast group.</p>"}, "LoRaWANMulticastGet": {"type": "structure", "members": {"RfRegion": {"shape": "SupportedRfRegion"}, "DlClass": {"shape": "DlClass"}, "NumberOfDevicesRequested": {"shape": "NumberOfDevicesRequested"}, "NumberOfDevicesInGroup": {"shape": "NumberOfDevicesInGroup"}}, "documentation": "<p>The LoRaWAN information that is to be returned from getting multicast group information.</p>"}, "LoRaWANMulticastMetadata": {"type": "structure", "members": {"FPort": {"shape": "FPort"}}, "documentation": "<p>The metadata information of the LoRaWAN multicast group.</p>"}, "LoRaWANMulticastSession": {"type": "structure", "members": {"DlDr": {"shape": "DlDr"}, "DlFreq": {"shape": "DlFreq"}, "SessionStartTime": {"shape": "SessionStartTimeTimestamp"}, "SessionTimeout": {"shape": "SessionTimeout"}, "PingSlotPeriod": {"shape": "PingSlotPeriod", "documentation": "<p>The PingSlotPeriod value.</p>"}}, "documentation": "<p>The LoRaWAN information used with the multicast session.</p>"}, "LoRaWANSendDataToDevice": {"type": "structure", "members": {"FPort": {"shape": "FPort"}, "ParticipatingGateways": {"shape": "ParticipatingGateways", "documentation": "<p>Choose the gateways that you want to use for the downlink data traffic when the wireless device is running in class B or class C mode.</p>"}}, "documentation": "<p>LoRaWAN router info.</p>"}, "LoRaWANServiceProfile": {"type": "structure", "members": {"AddGwMetadata": {"shape": "AddGwMetadata", "documentation": "<p>The AddGWMetaData value.</p>"}, "DrMin": {"shape": "DrMinBox", "documentation": "<p>The DrMin value.</p>"}, "DrMax": {"shape": "DrMaxBox", "documentation": "<p>The DrMax value.</p>"}, "PrAllowed": {"shape": "PrAllowed", "documentation": "<p>The PRAllowed value that describes whether passive roaming is allowed.</p>"}, "RaAllowed": {"shape": "<PERSON><PERSON><PERSON>ed", "documentation": "<p>The RAAllowed value that describes whether roaming activation is allowed.</p>"}}, "documentation": "<p>LoRaWANServiceProfile object.</p>"}, "LoRaWANStartFuotaTask": {"type": "structure", "members": {"StartTime": {"shape": "StartTime"}}, "documentation": "<p>The LoRaWAN information used to start a FUOTA task.</p>"}, "LoRaWANUpdateDevice": {"type": "structure", "members": {"DeviceProfileId": {"shape": "DeviceProfileId", "documentation": "<p>The ID of the device profile for the wireless device.</p>"}, "ServiceProfileId": {"shape": "ServiceProfileId", "documentation": "<p>The ID of the service profile.</p>"}, "AbpV1_1": {"shape": "UpdateAbpV1_1", "documentation": "<p>ABP device object for update APIs for v1.1</p>"}, "AbpV1_0_x": {"shape": "UpdateAbpV1_0_x", "documentation": "<p>ABP device object for update APIs for v1.0.x</p>"}, "FPorts": {"shape": "UpdateFPorts", "documentation": "<p>FPorts object for the positioning information of the device.</p>"}}, "documentation": "<p>LoRaWAN object for update functions.</p>"}, "LoRaWANUpdateGatewayTaskCreate": {"type": "structure", "members": {"UpdateSignature": {"shape": "UpdateSignature", "documentation": "<p>The signature used to verify the update firmware.</p>"}, "SigKeyCrc": {"shape": "Crc", "documentation": "<p>The CRC of the signature private key to check.</p>"}, "CurrentVersion": {"shape": "LoRaWANGatewayVersion", "documentation": "<p>The version of the gateways that should receive the update.</p>"}, "UpdateVersion": {"shape": "LoRaWANGatewayVersion", "documentation": "<p>The firmware version to update the gateway to.</p>"}}, "documentation": "<p>LoRaWANUpdateGatewayTaskCreate object.</p>"}, "LoRaWANUpdateGatewayTaskEntry": {"type": "structure", "members": {"CurrentVersion": {"shape": "LoRaWANGatewayVersion", "documentation": "<p>The version of the gateways that should receive the update.</p>"}, "UpdateVersion": {"shape": "LoRaWANGatewayVersion", "documentation": "<p>The firmware version to update the gateway to.</p>"}}, "documentation": "<p>LoRaWANUpdateGatewayTaskEntry object.</p>"}, "LogLevel": {"type": "string", "documentation": "<p>The log level for a log message. The log levels can be disabled, or set to <code>ERROR</code> to display less verbose logs containing only error information, or to <code>INFO</code> for more detailed logs.</p>", "enum": ["INFO", "ERROR", "DISABLED"]}, "LteList": {"type": "list", "member": {"shape": "LteObj"}, "max": 16, "min": 1}, "LteLocalId": {"type": "structure", "required": ["Pci", "Earfcn"], "members": {"Pci": {"shape": "PCI", "documentation": "<p>Physical cell ID.</p>"}, "Earfcn": {"shape": "EARFCN", "documentation": "<p>Evolved universal terrestrial radio access (E-UTRA) absolute radio frequency channel number (FCN).</p>"}}, "documentation": "<p>LTE local identification (local ID) information.</p>"}, "LteNmrList": {"type": "list", "member": {"shape": "LteNmrObj"}, "max": 32, "min": 1}, "LteNmrObj": {"type": "structure", "required": ["Pci", "Earfcn", "EutranCid"], "members": {"Pci": {"shape": "PCI", "documentation": "<p>Physical cell ID.</p>"}, "Earfcn": {"shape": "EARFCN", "documentation": "<p>E-UTRA (Evolved universal terrestrial Radio Access) absolute radio frequency channel Number (EARFCN).</p>"}, "EutranCid": {"shape": "EutranCid", "documentation": "<p>E-UTRAN (Evolved Universal Terrestrial Radio Access Network) cell global identifier (EUTRANCID).</p>"}, "Rsrp": {"shape": "RSRP", "documentation": "<p>Signal power of the reference signal received, measured in dBm (decibel-milliwatts).</p>"}, "Rsrq": {"shape": "RSRQ", "documentation": "<p>Signal quality of the reference Signal received, measured in decibels (dB).</p>"}}, "documentation": "<p>LTE object for network measurement reports.</p>"}, "LteObj": {"type": "structure", "required": ["Mcc", "Mnc", "EutranCid"], "members": {"Mcc": {"shape": "MCC", "documentation": "<p>Mobile Country Code.</p>"}, "Mnc": {"shape": "MNC", "documentation": "<p>Mobile Network Code.</p>"}, "EutranCid": {"shape": "EutranCid", "documentation": "<p>E-UTRAN (Evolved Universal Terrestrial Radio Access Network) Cell Global Identifier.</p>"}, "Tac": {"shape": "TAC", "documentation": "<p>LTE tracking area code.</p>"}, "LteLocalId": {"shape": "LteLocalId", "documentation": "<p>LTE local identification (local ID) information.</p>"}, "LteTimingAdvance": {"shape": "LteTimingAdvance", "documentation": "<p>LTE timing advance.</p>"}, "Rsrp": {"shape": "RSRP", "documentation": "<p>Signal power of the reference signal received, measured in dBm (decibel-milliwatts).</p>"}, "Rsrq": {"shape": "RSRQ", "documentation": "<p>Signal quality of the reference Signal received, measured in decibels (dB).</p>"}, "NrCapable": {"shape": "NRCapable", "documentation": "<p>Parameter that determines whether the LTE object is capable of supporting NR (new radio).</p>"}, "LteNmr": {"shape": "LteNmrList", "documentation": "<p>LTE object for network measurement reports.</p>"}}, "documentation": "<p>LTE object.</p>"}, "LteTimingAdvance": {"type": "integer", "max": 1282, "min": 0}, "MCC": {"type": "integer", "max": 999, "min": 200}, "MNC": {"type": "integer", "max": 999, "min": 0}, "MacAddress": {"type": "string", "max": 17, "min": 12, "pattern": "^([0-9A-Fa-f]{2}[:-]?){5}([0-9A-Fa-f]{2})$"}, "MacVersion": {"type": "string", "max": 64}, "MaxAllowedSignature": {"type": "integer"}, "MaxDutyCycle": {"type": "integer", "max": 100, "min": 0}, "MaxEirp": {"type": "integer", "max": 15, "min": 0}, "MaxResults": {"type": "integer", "documentation": "<p>The maximum number of results to return in this operation.</p>", "max": 250, "min": 0}, "McGroupId": {"type": "integer", "documentation": "<p>Id of the multicast group.</p>", "max": 256, "min": 1}, "Message": {"type": "string", "max": 2048}, "MessageDeliveryStatusEventConfiguration": {"type": "structure", "members": {"Sidewalk": {"shape": "SidewalkEventNotificationConfigurations"}, "WirelessDeviceIdEventTopic": {"shape": "EventNotificationTopicStatus", "documentation": "<p>Denotes whether the wireless device ID message delivery status event topic is enabled or disabled.</p>"}}, "documentation": "<p>Message delivery status event configuration object for enabling and disabling relevant topics.</p>"}, "MessageDeliveryStatusResourceTypeEventConfiguration": {"type": "structure", "members": {"Sidewalk": {"shape": "SidewalkResourceTypeEventConfiguration"}}, "documentation": "<p>Message delivery status resource type event configuration object for enabling or disabling relevant topic.</p>"}, "MessageId": {"type": "string"}, "MessageType": {"type": "string", "documentation": "<p>Sidewalk device message type. Default value is <code>CUSTOM_COMMAND_ID_NOTIFY</code>.</p>", "enum": ["CUSTOM_COMMAND_ID_NOTIFY", "CUSTOM_COMMAND_ID_GET", "CUSTOM_COMMAND_ID_SET", "CUSTOM_COMMAND_ID_RESP"]}, "MinGwDiversity": {"type": "integer", "max": 100, "min": 1}, "Model": {"type": "string", "max": 4096, "min": 1}, "MulticastDeviceStatus": {"type": "string", "max": 256}, "MulticastFrameInfo": {"type": "string", "documentation": "<p> <code>FrameInfo</code> of your multicast group resources for the trace content. Use FrameInfo to debug the multicast communication between your multicast groups and the network server.</p>", "enum": ["ENABLED", "DISABLED"]}, "MulticastGroup": {"type": "structure", "members": {"Id": {"shape": "MulticastGroupId"}, "Arn": {"shape": "MulticastGroupArn"}, "Name": {"shape": "MulticastGroupName"}}, "documentation": "<p>A multicast group.</p>"}, "MulticastGroupArn": {"type": "string", "documentation": "<p>The arn of the multicast group.</p>", "max": 128}, "MulticastGroupByFuotaTask": {"type": "structure", "members": {"Id": {"shape": "MulticastGroupId"}}, "documentation": "<p>A multicast group that is associated with a FUOTA task.</p>"}, "MulticastGroupId": {"type": "string", "documentation": "<p>The ID of the multicast group.</p>", "max": 256}, "MulticastGroupList": {"type": "list", "member": {"shape": "MulticastGroup"}, "documentation": "<p>List of multicast groups.</p>"}, "MulticastGroupListByFuotaTask": {"type": "list", "member": {"shape": "MulticastGroupByFuotaTask"}, "documentation": "<p>List of multicast groups associated with a FUOTA task.</p>"}, "MulticastGroupMessageId": {"type": "string", "documentation": "<p>ID of a multicast group message.</p>", "max": 256}, "MulticastGroupName": {"type": "string", "documentation": "<p>The name of the multicast group.</p>", "max": 256}, "MulticastGroupStatus": {"type": "string", "documentation": "<p>The status of the multicast group.</p>", "max": 256}, "MulticastWirelessMetadata": {"type": "structure", "members": {"LoRaWAN": {"shape": "LoRaWANMulticastMetadata"}}, "documentation": "<p>Wireless metadata that is to be sent to multicast group.</p>"}, "NRCapable": {"type": "boolean"}, "NetId": {"type": "string", "documentation": "<p>LoRaWAN network ID.</p>", "pattern": "[a-fA-F0-9]{6}"}, "NetIdFilters": {"type": "list", "member": {"shape": "NetId"}, "documentation": "<p>A list of NetId values that are used by LoRa gateways to filter the uplink frames.</p>", "max": 10, "min": 0}, "NetworkAnalyzerConfigurationArn": {"type": "string", "max": 1124}, "NetworkAnalyzerConfigurationList": {"type": "list", "member": {"shape": "NetworkAnalyzerConfigurations"}}, "NetworkAnalyzerConfigurationName": {"type": "string", "documentation": "<p>Name of the network analyzer configuration.</p>", "max": 1024, "min": 1, "pattern": "[a-zA-Z0-9-_]+"}, "NetworkAnalyzerConfigurations": {"type": "structure", "members": {"Arn": {"shape": "NetworkAnalyzerConfigurationArn", "documentation": "<p>The Amazon Resource Name of the new resource.</p>"}, "Name": {"shape": "NetworkAnalyzerConfigurationName"}}, "documentation": "<p>Network analyzer configurations.</p>"}, "NetworkAnalyzerMulticastGroupList": {"type": "list", "member": {"shape": "MulticastGroupId"}, "max": 10, "min": 0}, "NetworkId": {"type": "integer", "max": 65535, "min": 0}, "NextToken": {"type": "string", "max": 4096}, "NumberOfDevicesInGroup": {"type": "integer", "documentation": "<p>Number of devices that are associated to the multicast group.</p>"}, "NumberOfDevicesRequested": {"type": "integer", "documentation": "<p>Number of devices that are requested to be associated with the multicast group.</p>"}, "NwkGeoLoc": {"type": "boolean"}, "NwkKey": {"type": "string", "pattern": "[a-fA-F0-9]{32}"}, "NwkSEncKey": {"type": "string", "pattern": "[a-fA-F0-9]{32}"}, "NwkSKey": {"type": "string", "pattern": "[a-fA-F0-9]{32}"}, "OnboardStatus": {"type": "string", "enum": ["INITIALIZED", "PENDING", "ONBOARDED", "FAILED"]}, "OnboardStatusReason": {"type": "string"}, "OtaaV1_0_x": {"type": "structure", "members": {"AppKey": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The AppKey value.</p>"}, "AppEui": {"shape": "AppEui", "documentation": "<p>The AppEUI value.</p>"}, "JoinEui": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The JoinEUI value.</p>"}, "GenAppKey": {"shape": "GenAppKey", "documentation": "<p>The GenAppKey value.</p>"}}, "documentation": "<p>OTAA device object for v1.0.x</p>"}, "OtaaV1_1": {"type": "structure", "members": {"AppKey": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The AppKey value.</p>"}, "NwkKey": {"shape": "NwkKey", "documentation": "<p>The NwkKey value.</p>"}, "JoinEui": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The JoinEUI value.</p>"}}, "documentation": "<p>OTAA device object for v1.1</p>"}, "PCI": {"type": "integer", "max": 503, "min": 0}, "PSC": {"type": "integer", "max": 511, "min": 0}, "PackageVersion": {"type": "string", "max": 32, "min": 1}, "ParticipatingGateways": {"type": "structure", "required": ["DownlinkMode", "GatewayList", "TransmissionInterval"], "members": {"DownlinkMode": {"shape": "DownlinkMode", "documentation": "<p>Indicates whether to send the downlink message in sequential mode or concurrent mode, or to use only the chosen gateways from the previous uplink message transmission.</p>"}, "GatewayList": {"shape": "GatewayList", "documentation": "<p>The list of gateways that you want to use for sending the downlink data traffic.</p>"}, "TransmissionInterval": {"shape": "TransmissionInterval", "documentation": "<p>The duration of time for which AWS IoT Core for LoRaWAN will wait before transmitting the payload to the next gateway.</p>"}}, "documentation": "<p>Specify the list of gateways to which you want to send downlink data traffic when the wireless device is running in class B or class C mode.</p>"}, "PartnerAccountArn": {"type": "string"}, "PartnerAccountId": {"type": "string", "max": 256}, "PartnerType": {"type": "string", "enum": ["Sidewalk"]}, "PathLoss": {"type": "integer", "max": 158, "min": 46}, "PayloadData": {"type": "string", "documentation": "<p>The binary to be sent to the end device, encoded in base64.</p>", "max": 2048, "pattern": "^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$"}, "PilotPower": {"type": "integer", "max": -49, "min": -142}, "PingSlotDr": {"type": "integer", "max": 15, "min": 0}, "PingSlotFreq": {"type": "integer", "max": ********, "min": 1000000}, "PingSlotPeriod": {"type": "integer", "max": 4096, "min": 32}, "PnOffset": {"type": "integer", "max": 511, "min": 0}, "PositionConfigurationFec": {"type": "string", "enum": ["ROSE", "NONE"]}, "PositionConfigurationItem": {"type": "structure", "members": {"ResourceIdentifier": {"shape": "PositionResourceIdentifier", "documentation": "<p>Resource identifier for the position configuration.</p>"}, "ResourceType": {"shape": "PositionResourceType", "documentation": "<p>Resource type of the resource for the position configuration.</p>"}, "Solvers": {"shape": "PositionSolverDetails", "documentation": "<p>The details of the positioning solver object used to compute the location.</p>"}, "Destination": {"shape": "DestinationName", "documentation": "<p>The position data destination that describes the AWS IoT rule that processes the device's position data for use by AWS IoT Core for LoRaWAN.</p>"}}, "documentation": "<p>The wrapper for a position configuration.</p>"}, "PositionConfigurationList": {"type": "list", "member": {"shape": "PositionConfigurationItem"}}, "PositionConfigurationStatus": {"type": "string", "enum": ["Enabled", "Disabled"]}, "PositionCoordinate": {"type": "list", "member": {"shape": "PositionCoordinateValue"}}, "PositionCoordinateValue": {"type": "float"}, "PositionResourceIdentifier": {"type": "string", "pattern": "[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}"}, "PositionResourceType": {"type": "string", "enum": ["WirelessDevice", "WirelessGateway"]}, "PositionSolverConfigurations": {"type": "structure", "members": {"SemtechGnss": {"shape": "SemtechGnssConfiguration", "documentation": "<p>The Semtech GNSS solver configuration object.</p>"}}, "documentation": "<p>The wrapper for position solver configurations.</p>"}, "PositionSolverDetails": {"type": "structure", "members": {"SemtechGnss": {"shape": "SemtechGnssDetail", "documentation": "<p>The Semtech GNSS solver object details.</p>"}}, "documentation": "<p>The wrapper for position solver details.</p>"}, "PositionSolverProvider": {"type": "string", "enum": ["Semtech"]}, "PositionSolverType": {"type": "string", "enum": ["GNSS"]}, "PositionSolverVersion": {"type": "string", "max": 50, "min": 0}, "Positioning": {"type": "structure", "members": {"ClockSync": {"shape": "FPort"}, "Stream": {"shape": "FPort"}, "Gnss": {"shape": "FPort"}}, "documentation": "<p>The FPorts for the position information.</p>"}, "PositioningConfigStatus": {"type": "string", "enum": ["Enabled", "Disabled"]}, "PrAllowed": {"type": "boolean"}, "PresetFreq": {"type": "integer", "max": ********, "min": 1000000}, "PrivateKeysList": {"type": "list", "member": {"shape": "CertificateList"}}, "ProximityEventConfiguration": {"type": "structure", "members": {"Sidewalk": {"shape": "SidewalkEventNotificationConfigurations", "documentation": "<p>Proximity event configuration object for enabling or disabling Sidewalk related event topics.</p>"}, "WirelessDeviceIdEventTopic": {"shape": "EventNotificationTopicStatus", "documentation": "<p>Denotes whether the wireless device ID proximity event topic is enabled or disabled.</p>"}}, "documentation": "<p>Proximity event configuration object for enabling and disabling relevant topics.</p>"}, "ProximityResourceTypeEventConfiguration": {"type": "structure", "members": {"Sidewalk": {"shape": "SidewalkResourceTypeEventConfiguration", "documentation": "<p>Proximity resource type event configuration object for enabling and disabling wireless device topic.</p>"}}, "documentation": "<p>Proximity resource type event configuration object for enabling or disabling topic.</p>"}, "PutPositionConfigurationRequest": {"type": "structure", "required": ["ResourceIdentifier", "ResourceType"], "members": {"ResourceIdentifier": {"shape": "PositionResourceIdentifier", "documentation": "<p>Resource identifier used to update the position configuration.</p>", "location": "uri", "locationName": "ResourceIdentifier"}, "ResourceType": {"shape": "PositionResourceType", "documentation": "<p>Resource type of the resource for which you want to update the position configuration.</p>", "location": "querystring", "locationName": "resourceType"}, "Solvers": {"shape": "PositionSolverConfigurations", "documentation": "<p>The positioning solvers used to update the position configuration of the resource.</p>"}, "Destination": {"shape": "DestinationName", "documentation": "<p>The position data destination that describes the AWS IoT rule that processes the device's position data for use by AWS IoT Core for LoRaWAN.</p>"}}, "deprecated": true, "deprecatedMessage": "This operation is no longer supported."}, "PutPositionConfigurationResponse": {"type": "structure", "members": {}, "deprecated": true, "deprecatedMessage": "This operation is no longer supported."}, "PutResourceLogLevelRequest": {"type": "structure", "required": ["ResourceIdentifier", "ResourceType", "LogLevel"], "members": {"ResourceIdentifier": {"shape": "ResourceIdentifier", "location": "uri", "locationName": "ResourceIdentifier"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The type of the resource, which can be <code>WirelessDevice</code> or <code>WirelessGateway</code>.</p>", "location": "querystring", "locationName": "resourceType"}, "LogLevel": {"shape": "LogLevel"}}}, "PutResourceLogLevelResponse": {"type": "structure", "members": {}}, "QualificationStatus": {"type": "boolean"}, "QueryString": {"type": "string", "documentation": "<p>Query string used to search for wireless devices as part of the bulk associate and disassociate process.</p>", "max": 4096}, "RSCP": {"type": "integer", "max": -25, "min": -120}, "RSRP": {"type": "integer", "max": -44, "min": -140}, "RSRQ": {"type": "float", "max": -3, "min": -19.5}, "RSS": {"type": "integer", "max": 0, "min": -128}, "RaAllowed": {"type": "boolean"}, "RedundancyPercent": {"type": "integer", "documentation": "<p>The percentage of the added fragments that are redundant. For example, if the size of the firmware image file is 100 bytes and the fragment size is 10 bytes, with <code>RedundancyPercent</code> set to 50(%), the final number of encoded fragments is (100 / 10) + (100 / 10 * 50%) = 15.</p>", "max": 100, "min": 0}, "RegParamsRevision": {"type": "string", "max": 64}, "RegistrationZone": {"type": "integer", "max": 4095, "min": 0}, "ReportDevStatusBattery": {"type": "boolean"}, "ReportDevStatusMargin": {"type": "boolean"}, "ResetAllResourceLogLevelsRequest": {"type": "structure", "members": {}}, "ResetAllResourceLogLevelsResponse": {"type": "structure", "members": {}}, "ResetResourceLogLevelRequest": {"type": "structure", "required": ["ResourceIdentifier", "ResourceType"], "members": {"ResourceIdentifier": {"shape": "ResourceIdentifier", "location": "uri", "locationName": "ResourceIdentifier"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The type of the resource, which can be <code>WirelessDevice</code> or <code>WirelessGateway</code>.</p>", "location": "querystring", "locationName": "resourceType"}}}, "ResetResourceLogLevelResponse": {"type": "structure", "members": {}}, "ResourceId": {"type": "string"}, "ResourceIdentifier": {"type": "string", "documentation": "<p>The identifier of the resource. For a Wireless Device, it is the wireless device ID. For a wireless gateway, it is the wireless gateway ID.</p>", "max": 256}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "Message"}, "ResourceId": {"shape": "ResourceId", "documentation": "<p>Id of the not found resource.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>Type of the font found resource.</p>"}}, "documentation": "<p>Resource does not exist.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "ResourceType": {"type": "string"}, "Result": {"type": "string", "max": 2048}, "RfRegion": {"type": "string", "documentation": "<p>The frequency band (RFRegion) value.</p>", "max": 64}, "Role": {"type": "string", "max": 2048}, "RoleArn": {"type": "string", "max": 2048, "min": 20}, "RxDataRate2": {"type": "integer", "max": 15, "min": 0}, "RxDelay1": {"type": "integer", "max": 15, "min": 0}, "RxDrOffset1": {"type": "integer", "max": 7, "min": 0}, "RxFreq2": {"type": "integer", "max": ********, "min": 1000000}, "RxLevel": {"type": "integer", "max": -25, "min": -110}, "SNwkSIntKey": {"type": "string", "pattern": "[a-fA-F0-9]{32}"}, "SemtechGnssConfiguration": {"type": "structure", "required": ["Status", "Fec"], "members": {"Status": {"shape": "PositionConfigurationStatus", "documentation": "<p>The status indicating whether the solver is enabled.</p>"}, "Fec": {"shape": "PositionConfigurationFec", "documentation": "<p>Whether forward error correction is enabled.</p>"}}, "documentation": "<p>Information about the Semtech GNSS solver configuration.</p>"}, "SemtechGnssDetail": {"type": "structure", "members": {"Provider": {"shape": "PositionSolverProvider", "documentation": "<p>The vendor of the solver object.</p>"}, "Type": {"shape": "PositionSolverType", "documentation": "<p>The type of positioning solver used.</p>"}, "Status": {"shape": "PositionConfigurationStatus", "documentation": "<p>The status indicating whether the solver is enabled.</p>"}, "Fec": {"shape": "PositionConfigurationFec", "documentation": "<p>Whether forward error correction is enabled.</p>"}}, "documentation": "<p>Details of the Semtech GNSS solver object.</p>"}, "SendDataToMulticastGroupRequest": {"type": "structure", "required": ["Id", "PayloadData", "WirelessMetadata"], "members": {"Id": {"shape": "MulticastGroupId", "location": "uri", "locationName": "Id"}, "PayloadData": {"shape": "PayloadData"}, "WirelessMetadata": {"shape": "MulticastWirelessMetadata"}}}, "SendDataToMulticastGroupResponse": {"type": "structure", "members": {"MessageId": {"shape": "MulticastGroupMessageId"}}}, "SendDataToWirelessDeviceRequest": {"type": "structure", "required": ["Id", "TransmitMode", "PayloadData"], "members": {"Id": {"shape": "WirelessDeviceId", "documentation": "<p>The ID of the wireless device to receive the data.</p>", "location": "uri", "locationName": "Id"}, "TransmitMode": {"shape": "TransmitMode", "documentation": "<p>The transmit mode to use to send data to the wireless device. Can be: <code>0</code> for UM (unacknowledge mode) or <code>1</code> for AM (acknowledge mode).</p>"}, "PayloadData": {"shape": "PayloadData"}, "WirelessMetadata": {"shape": "WirelessMetadata", "documentation": "<p><PERSON><PERSON><PERSON> about the message request.</p>"}}}, "SendDataToWirelessDeviceResponse": {"type": "structure", "members": {"MessageId": {"shape": "MessageId", "documentation": "<p>The ID of the message sent to the wireless device.</p>"}}}, "Seq": {"type": "integer", "max": 16383, "min": 0}, "ServiceProfile": {"type": "structure", "members": {"Arn": {"shape": "ServiceProfileArn", "documentation": "<p>The Amazon Resource Name of the resource.</p>"}, "Name": {"shape": "ServiceProfileName", "documentation": "<p>The name of the resource.</p>"}, "Id": {"shape": "ServiceProfileId", "documentation": "<p>The ID of the service profile.</p>"}}, "documentation": "<p>Information about a service profile.</p>"}, "ServiceProfileArn": {"type": "string"}, "ServiceProfileId": {"type": "string", "max": 256}, "ServiceProfileList": {"type": "list", "member": {"shape": "ServiceProfile"}}, "ServiceProfileName": {"type": "string", "max": 256}, "SessionKeysAbpV1_0_x": {"type": "structure", "members": {"NwkSKey": {"shape": "NwkSKey", "documentation": "<p>The NwkSKey value.</p>"}, "AppSKey": {"shape": "AppS<PERSON>ey", "documentation": "<p>The AppSKey value.</p>"}}, "documentation": "<p>Session keys for ABP v1.1</p>"}, "SessionKeysAbpV1_1": {"type": "structure", "members": {"FNwkSIntKey": {"shape": "FNwkSIntKey", "documentation": "<p>The FNwkSIntKey value.</p>"}, "SNwkSIntKey": {"shape": "SNwkSIntKey", "documentation": "<p>The SNwkSIntKey value.</p>"}, "NwkSEncKey": {"shape": "NwkSEncKey", "documentation": "<p>The NwkSEncKey value.</p>"}, "AppSKey": {"shape": "AppS<PERSON>ey", "documentation": "<p>The AppSKey value.</p>"}}, "documentation": "<p>Session keys for ABP v1.1</p>"}, "SessionStartTimeTimestamp": {"type": "timestamp", "documentation": "<p>Timestamp of when the multicast group session is to start.</p>", "timestampFormat": "iso8601"}, "SessionTimeout": {"type": "integer", "documentation": "<p>How long before a multicast group session is to timeout.</p>", "max": 172800, "min": 60}, "SidewalkAccountInfo": {"type": "structure", "members": {"AmazonId": {"shape": "AmazonId", "documentation": "<p>The Sidewalk Amazon ID.</p>"}, "AppServerPrivateKey": {"shape": "AppServerPrivateKey", "documentation": "<p>The Sidewalk application server private key.</p>"}}, "documentation": "<p>Information about a Sidewalk account.</p>"}, "SidewalkAccountInfoWithFingerprint": {"type": "structure", "members": {"AmazonId": {"shape": "AmazonId", "documentation": "<p>The Sidewalk Amazon ID.</p>"}, "Fingerprint": {"shape": "Fingerprint", "documentation": "<p>The fingerprint of the Sidewalk application server private key.</p>"}, "Arn": {"shape": "PartnerAccountArn", "documentation": "<p>The Amazon Resource Name of the resource.</p>"}}, "documentation": "<p>Information about a Sidewalk account.</p>"}, "SidewalkAccountList": {"type": "list", "member": {"shape": "SidewalkAccountInfoWithFingerprint"}}, "SidewalkCreateDeviceProfile": {"type": "structure", "members": {}, "documentation": "<p>Sidewalk object for creating a device profile.</p>"}, "SidewalkCreateWirelessDevice": {"type": "structure", "members": {"DeviceProfileId": {"shape": "DeviceProfileId", "documentation": "<p>The ID of the Sidewalk device profile.</p>"}}, "documentation": "<p>Sidewalk object for creating a wireless device.</p>"}, "SidewalkDevice": {"type": "structure", "members": {"AmazonId": {"shape": "AmazonId"}, "SidewalkId": {"shape": "SidewalkId", "documentation": "<p>The sidewalk device identification.</p>"}, "SidewalkManufacturingSn": {"shape": "SidewalkManufacturingSn", "documentation": "<p>The Sidewalk manufacturing series number.</p>"}, "DeviceCertificates": {"shape": "DeviceCertificateList", "documentation": "<p>The sidewalk device certificates for Ed25519 and P256r1.</p>"}, "PrivateKeys": {"shape": "PrivateKeysList", "documentation": "<p>The Sidewalk device private keys that will be used for onboarding the device.</p>"}, "DeviceProfileId": {"shape": "DeviceProfileId", "documentation": "<p>The ID of the Sidewalk device profile.</p>"}, "CertificateId": {"shape": "DakCertificateId", "documentation": "<p>The ID of the Sidewalk device profile.</p>"}, "Status": {"shape": "WirelessDeviceSidewalkStatus", "documentation": "<p>The Sidewalk device status, such as provisioned or registered.</p>"}}, "documentation": "<p>Sidewalk device object.</p>"}, "SidewalkDeviceMetadata": {"type": "structure", "members": {"Rssi": {"shape": "Integer", "documentation": "<p>The RSSI value.</p>"}, "BatteryLevel": {"shape": "BatteryLevel", "documentation": "<p>Sidewalk device battery level.</p>"}, "Event": {"shape": "Event", "documentation": "<p>Sidewalk device status notification.</p>"}, "DeviceState": {"shape": "DeviceState", "documentation": "<p>Device state defines the device status of sidewalk device.</p>"}}, "documentation": "<p>MetaData for Sidewalk device.</p>"}, "SidewalkEventNotificationConfigurations": {"type": "structure", "members": {"AmazonIdEventTopic": {"shape": "EventNotificationTopicStatus", "documentation": "<p>Denotes whether the Amazon ID event topic is enabled or disabled.</p>"}}, "documentation": "<p> <code>SidewalkEventNotificationConfigurations</code> object, which is the event configuration object for Sidewalk-related event topics.</p>"}, "SidewalkGetDeviceProfile": {"type": "structure", "members": {"ApplicationServerPublicKey": {"shape": "ApplicationServerPublicKey", "documentation": "<p>The Sidewalk application server public key.</p>"}, "QualificationStatus": {"shape": "QualificationStatus", "documentation": "<p>Gets information about the certification status of a Sidewalk device profile.</p>"}, "DakCertificateMetadata": {"shape": "DakCertificateMetadataList", "documentation": "<p>The DAK certificate information of the Sidewalk device profile.</p>"}}, "documentation": "<p>Gets information about a Sidewalk device profile.</p>"}, "SidewalkGetStartImportInfo": {"type": "structure", "members": {"DeviceCreationFileList": {"shape": "DeviceCreationFileList", "documentation": "<p>List of Sidewalk devices that are added to the import task.</p>"}, "Role": {"shape": "Role", "documentation": "<p>The IAM role that allows AWS IoT Wireless to access the CSV file in the S3 bucket.</p>"}}, "documentation": "<p>Sidewalk-related information for devices in an import task that are being onboarded.</p>"}, "SidewalkId": {"type": "string", "documentation": "<p>The sidewalk device identification.</p>", "max": 256}, "SidewalkListDevice": {"type": "structure", "members": {"AmazonId": {"shape": "AmazonId", "documentation": "<p>The Sidewalk Amazon ID.</p>"}, "SidewalkId": {"shape": "SidewalkId", "documentation": "<p>The sidewalk device identification.</p>"}, "SidewalkManufacturingSn": {"shape": "SidewalkManufacturingSn", "documentation": "<p>The Sidewalk manufacturing series number.</p>"}, "DeviceCertificates": {"shape": "DeviceCertificateList", "documentation": "<p>The sidewalk device certificates for Ed25519 and P256r1.</p>"}, "DeviceProfileId": {"shape": "DeviceProfileId", "documentation": "<p>Sidewalk object used by list functions.</p>"}, "Status": {"shape": "WirelessDeviceSidewalkStatus", "documentation": "<p>The status of the Sidewalk devices, such as provisioned or registered.</p>"}}, "documentation": "<p>Sidewalk object used by list functions.</p>"}, "SidewalkManufacturingSn": {"type": "string", "max": 64}, "SidewalkResourceTypeEventConfiguration": {"type": "structure", "members": {"WirelessDeviceEventTopic": {"shape": "EventNotificationTopicStatus", "documentation": "<p>Denotes whether the wireless device join event topic is enabled or disabled.</p>"}}, "documentation": "<p>Sidewalk resource type event configuration object for enabling or disabling topic.</p>"}, "SidewalkSendDataToDevice": {"type": "structure", "members": {"Seq": {"shape": "Seq", "documentation": "<p>The sequence number.</p>"}, "MessageType": {"shape": "MessageType"}, "AckModeRetryDurationSecs": {"shape": "AckModeRetryDurationSecs", "documentation": "<p>The duration of time in seconds to retry sending the ACK.</p>"}}, "documentation": "<p>Information about a Sidewalk router.</p>"}, "SidewalkSingleStartImportInfo": {"type": "structure", "members": {"SidewalkManufacturingSn": {"shape": "SidewalkManufacturingSn", "documentation": "<p>The Sidewalk manufacturing serial number (SMSN) of the device added to the import task.</p>"}}, "documentation": "<p>Information about an import task created for an individual Sidewalk device.</p>"}, "SidewalkStartImportInfo": {"type": "structure", "members": {"DeviceCreationFile": {"shape": "DeviceCreationFile", "documentation": "<p>The CSV file contained in an S3 bucket that's used for adding devices to an import task.</p>"}, "Role": {"shape": "Role", "documentation": "<p>The IAM role that allows AWS IoT Wireless to access the CSV file in the S3 bucket.</p>"}}, "documentation": "<p>Information about an import task created for bulk provisioning.</p>"}, "SidewalkUpdateAccount": {"type": "structure", "members": {"AppServerPrivateKey": {"shape": "AppServerPrivateKey", "documentation": "<p>The new Sidewalk application server private key.</p>"}}, "documentation": "<p>Sidewalk update.</p>"}, "SidewalkUpdateImportInfo": {"type": "structure", "members": {"DeviceCreationFile": {"shape": "DeviceCreationFile", "documentation": "<p>The CSV file contained in an S3 bucket that's used for appending devices to an existing import task.</p>"}}, "documentation": "<p>Sidewalk object information for updating an import task.</p>"}, "SigningAlg": {"type": "string", "documentation": "<p>The certificate chain algorithm provided by sidewalk.</p>", "enum": ["Ed25519", "P256r1"]}, "StartBulkAssociateWirelessDeviceWithMulticastGroupRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "MulticastGroupId", "location": "uri", "locationName": "Id"}, "QueryString": {"shape": "QueryString"}, "Tags": {"shape": "TagList"}}}, "StartBulkAssociateWirelessDeviceWithMulticastGroupResponse": {"type": "structure", "members": {}}, "StartBulkDisassociateWirelessDeviceFromMulticastGroupRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "MulticastGroupId", "location": "uri", "locationName": "Id"}, "QueryString": {"shape": "QueryString"}, "Tags": {"shape": "TagList"}}}, "StartBulkDisassociateWirelessDeviceFromMulticastGroupResponse": {"type": "structure", "members": {}}, "StartFuotaTaskRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "FuotaTaskId", "location": "uri", "locationName": "Id"}, "LoRaWAN": {"shape": "LoRaWANStartFuotaTask"}}}, "StartFuotaTaskResponse": {"type": "structure", "members": {}}, "StartMulticastGroupSessionRequest": {"type": "structure", "required": ["Id", "LoRaWAN"], "members": {"Id": {"shape": "MulticastGroupId", "location": "uri", "locationName": "Id"}, "LoRaWAN": {"shape": "LoRaWANMulticastSession"}}}, "StartMulticastGroupSessionResponse": {"type": "structure", "members": {}}, "StartSingleWirelessDeviceImportTaskRequest": {"type": "structure", "required": ["DestinationName", "Sidewalk"], "members": {"DestinationName": {"shape": "DestinationName", "documentation": "<p>The name of the Sidewalk destination that describes the IoT rule to route messages from the device in the import task that will be onboarded to AWS IoT Wireless.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "DeviceName": {"shape": "DeviceName", "documentation": "<p>The name of the wireless device for which an import task is being started.</p>"}, "Tags": {"shape": "TagList"}, "Sidewalk": {"shape": "SidewalkSingleStartImportInfo", "documentation": "<p>The Sidewalk-related parameters for importing a single wireless device.</p>"}}}, "StartSingleWirelessDeviceImportTaskResponse": {"type": "structure", "members": {"Id": {"shape": "ImportTaskId", "documentation": "<p>The import task ID.</p>"}, "Arn": {"shape": "ImportTaskArn", "documentation": "<p>The ARN (Amazon Resource Name) of the import task.</p>"}}}, "StartTime": {"type": "timestamp", "documentation": "<p>Start time of a FUOTA task.</p>", "timestampFormat": "iso8601"}, "StartWirelessDeviceImportTaskRequest": {"type": "structure", "required": ["DestinationName", "Sidewalk"], "members": {"DestinationName": {"shape": "DestinationName", "documentation": "<p>The name of the Sidewalk destination that describes the IoT rule to route messages from the devices in the import task that are onboarded to AWS IoT Wireless.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "Tags": {"shape": "TagList"}, "Sidewalk": {"shape": "SidewalkStartImportInfo", "documentation": "<p>The Sidewalk-related parameters for importing wireless devices that need to be provisioned in bulk.</p>"}}}, "StartWirelessDeviceImportTaskResponse": {"type": "structure", "members": {"Id": {"shape": "ImportTaskId", "documentation": "<p>The import task ID.</p>"}, "Arn": {"shape": "ImportTaskArn", "documentation": "<p>The ARN (Amazon Resource Name) of the import task.</p>"}}}, "Station": {"type": "string", "max": 4096, "min": 1}, "StatusReason": {"type": "string"}, "SubBand": {"type": "integer", "documentation": "<p>A subset of supported frequency channels in a certain RFRegion.</p>", "max": 8, "min": 1}, "SubBands": {"type": "list", "member": {"shape": "SubBand"}, "documentation": "<p>A list of integer indicating which sub bands are supported by LoRa gateway.</p>", "max": 8, "min": 0}, "SupportedRfRegion": {"type": "string", "documentation": "<p>Supported RfRegions</p>", "enum": ["EU868", "US915", "AU915", "AS923-1", "AS923-2", "AS923-3", "AS923-4", "EU433", "CN470", "CN779", "RU864", "KR920", "IN865"]}, "Supports32BitFCnt": {"type": "boolean"}, "SupportsClassB": {"type": "boolean"}, "SupportsClassC": {"type": "boolean"}, "SupportsJoin": {"type": "boolean"}, "SystemId": {"type": "integer", "max": 32767, "min": 1}, "TAC": {"type": "integer", "max": 65535, "min": 0}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>The tag's key value.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The tag's value.</p>"}}, "documentation": "<p>A simple label consisting of a customer-defined key-value pair</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 0}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "documentation": "<p>The tag to attach to the specified resource. Tags are metadata that you can use to manage a resource.</p>", "max": 200, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"shape": "AmazonResourceName", "documentation": "<p>The ARN of the resource to add tags to.</p>", "location": "querystring", "locationName": "resourceArn"}, "Tags": {"shape": "TagList", "documentation": "<p>Adds to or modifies the tags of the given resource. Tags are metadata that you can use to manage a resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "TargetPer": {"type": "integer", "max": 100, "min": 0}, "TdscdmaList": {"type": "list", "member": {"shape": "TdscdmaObj"}, "max": 16, "min": 1}, "TdscdmaLocalId": {"type": "structure", "required": ["Uarfcn", "CellParams"], "members": {"Uarfcn": {"shape": "UARFCN", "documentation": "<p>TD-SCDMA UTRA (Universal Terrestrial Radio Access Network) absolute RF channel number (UARFCN).</p>"}, "CellParams": {"shape": "CellParams", "documentation": "<p>Cell parameters for TD-SCDMA.</p>"}}, "documentation": "<p>TD-SCDMA local identification (local Id) information.</p>"}, "TdscdmaNmrList": {"type": "list", "member": {"shape": "TdscdmaNmrObj"}, "max": 32, "min": 1}, "TdscdmaNmrObj": {"type": "structure", "required": ["Uarfcn", "CellParams"], "members": {"Uarfcn": {"shape": "UARFCN", "documentation": "<p>TD-SCDMA UTRA (Universal Terrestrial Radio Access Network) absolute RF channel number.</p>"}, "CellParams": {"shape": "CellParams", "documentation": "<p>Cell parameters for TD-SCDMA network measurement reports object.</p>"}, "UtranCid": {"shape": "UtranCid", "documentation": "<p>UTRAN (UMTS Terrestrial Radio Access Network) cell global identifier.</p>"}, "Rscp": {"shape": "RSCP", "documentation": "<p>Code power of the received signal, measured in decibel-milliwatts (dBm).</p>"}, "PathLoss": {"shape": "PathLoss", "documentation": "<p>Path loss, or path attenuation, is the reduction in power density of an electromagnetic wave as it propagates through space.</p>"}}, "documentation": "<p>TD-SCDMA object for network measurement reports.</p>"}, "TdscdmaObj": {"type": "structure", "required": ["Mcc", "Mnc", "UtranCid"], "members": {"Mcc": {"shape": "MCC", "documentation": "<p>Mobile Country Code.</p>"}, "Mnc": {"shape": "MNC", "documentation": "<p>Mobile Network Code.</p>"}, "Lac": {"shape": "LAC", "documentation": "<p>Location Area Code.</p>"}, "UtranCid": {"shape": "UtranCid", "documentation": "<p>UTRAN (UMTS Terrestrial Radio Access Network) Cell Global Identifier.</p>"}, "TdscdmaLocalId": {"shape": "TdscdmaLocalId", "documentation": "<p>TD-SCDMA local identification (local ID) information.</p>"}, "TdscdmaTimingAdvance": {"shape": "TdscdmaTimingAdvance", "documentation": "<p>TD-SCDM<PERSON> Timing advance.</p>"}, "Rscp": {"shape": "RSCP", "documentation": "<p>Signal power of the received signal (Received Signal Code Power), measured in decibel-milliwatts (dBm).</p>"}, "PathLoss": {"shape": "PathLoss", "documentation": "<p>Path loss, or path attenuation, is the reduction in power density of an electromagnetic wave as it propagates through space.</p>"}, "TdscdmaNmr": {"shape": "TdscdmaNmrList", "documentation": "<p>TD-SCDMA object for network measurement reports.</p>"}}, "documentation": "<p>TD-SCDMA object.</p>"}, "TdscdmaTimingAdvance": {"type": "integer", "max": 1530, "min": 0}, "TestWirelessDeviceRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "WirelessDeviceId", "documentation": "<p>The ID of the wireless device to test.</p>", "location": "uri", "locationName": "Id"}}}, "TestWirelessDeviceResponse": {"type": "structure", "members": {"Result": {"shape": "Result", "documentation": "<p>The result returned by the test.</p>"}}}, "ThingArn": {"type": "string"}, "ThingName": {"type": "string"}, "ThrottlingException": {"type": "structure", "members": {"Message": {"shape": "Message"}}, "documentation": "<p>The request was denied because it exceeded the allowed API request rate.</p>", "error": {"httpStatusCode": 429}, "exception": true}, "TooManyTagsException": {"type": "structure", "members": {"Message": {"shape": "Message"}, "ResourceName": {"shape": "AmazonResourceName", "documentation": "<p>Name of the resource that exceeds maximum number of tags allowed.</p>"}}, "documentation": "<p>The request was denied because the resource can't have any more tags.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "TraceContent": {"type": "structure", "members": {"WirelessDeviceFrameInfo": {"shape": "WirelessDeviceFrameInfo"}, "LogLevel": {"shape": "LogLevel"}, "MulticastFrameInfo": {"shape": "MulticastFrameInfo"}}, "documentation": "<p>Trace content for your wireless devices, gateways, and multicast groups.</p>"}, "TransmissionInterval": {"type": "integer", "max": 604800, "min": 1}, "TransmitMode": {"type": "integer", "max": 1, "min": 0}, "UARFCN": {"type": "integer", "max": 16383, "min": 0}, "UARFCNDL": {"type": "integer", "max": 16383, "min": 0}, "UlBucketSize": {"type": "integer", "max": 2147483647, "min": 0}, "UlRate": {"type": "integer", "max": 2147483647, "min": 0}, "UlRatePolicy": {"type": "string", "max": 256}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {"shape": "AmazonResourceName", "documentation": "<p>The ARN of the resource to remove tags from.</p>", "location": "querystring", "locationName": "resourceArn"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>A list of the keys of the tags to remove from the resource.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateAbpV1_0_x": {"type": "structure", "members": {"FCntStart": {"shape": "FCntStart", "documentation": "<p>The FCnt init value.</p>"}}, "documentation": "<p>ABP device object for LoRaWAN specification v1.0.x</p>"}, "UpdateAbpV1_1": {"type": "structure", "members": {"FCntStart": {"shape": "FCntStart", "documentation": "<p>The FCnt init value.</p>"}}, "documentation": "<p>ABP device object for LoRaWAN specification v1.1</p>"}, "UpdateDataSource": {"type": "string", "max": 4096, "min": 1}, "UpdateDestinationRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "DestinationName", "documentation": "<p>The new name of the resource.</p>", "location": "uri", "locationName": "Name"}, "ExpressionType": {"shape": "ExpressionType", "documentation": "<p>The type of value in <code>Expression</code>.</p>"}, "Expression": {"shape": "Expression", "documentation": "<p>The new rule name or topic rule to send messages to.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A new description of the resource.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of the IAM Role that authorizes the destination.</p>"}}}, "UpdateDestinationResponse": {"type": "structure", "members": {}}, "UpdateEventConfigurationByResourceTypesRequest": {"type": "structure", "members": {"DeviceRegistrationState": {"shape": "DeviceRegistrationStateResourceTypeEventConfiguration", "documentation": "<p>Device registration state resource type event configuration object for enabling and disabling wireless gateway topic.</p>"}, "Proximity": {"shape": "ProximityResourceTypeEventConfiguration", "documentation": "<p>Proximity resource type event configuration object for enabling and disabling wireless gateway topic.</p>"}, "Join": {"shape": "JoinResourceTypeEventConfiguration", "documentation": "<p>Join resource type event configuration object for enabling and disabling wireless device topic.</p>"}, "ConnectionStatus": {"shape": "ConnectionStatusResourceTypeEventConfiguration", "documentation": "<p>Connection status resource type event configuration object for enabling and disabling wireless gateway topic.</p>"}, "MessageDeliveryStatus": {"shape": "MessageDeliveryStatusResourceTypeEventConfiguration", "documentation": "<p>Message delivery status resource type event configuration object for enabling and disabling wireless device topic.</p>"}}}, "UpdateEventConfigurationByResourceTypesResponse": {"type": "structure", "members": {}}, "UpdateFPorts": {"type": "structure", "members": {"Positioning": {"shape": "Positioning", "documentation": "<p>Positioning FPorts for the ClockSync, Stream, and GNSS functions.</p>"}, "Applications": {"shape": "Applications", "documentation": "<p>LoRaWAN application, which can be used for geolocation by activating positioning.</p>"}}, "documentation": "<p>Object for updating the FPorts information.</p>"}, "UpdateFuotaTaskRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "FuotaTaskId", "location": "uri", "locationName": "Id"}, "Name": {"shape": "FuotaTaskName"}, "Description": {"shape": "Description"}, "LoRaWAN": {"shape": "LoRaWANFuotaTask"}, "FirmwareUpdateImage": {"shape": "FirmwareUpdateImage"}, "FirmwareUpdateRole": {"shape": "FirmwareUpdateRole"}, "RedundancyPercent": {"shape": "RedundancyPercent"}, "FragmentSizeBytes": {"shape": "FragmentSizeBytes"}, "FragmentIntervalMS": {"shape": "FragmentIntervalMS"}}}, "UpdateFuotaTaskResponse": {"type": "structure", "members": {}}, "UpdateLogLevelsByResourceTypesRequest": {"type": "structure", "members": {"DefaultLogLevel": {"shape": "LogLevel"}, "WirelessDeviceLogOptions": {"shape": "WirelessDeviceLogOptionList"}, "WirelessGatewayLogOptions": {"shape": "WirelessGatewayLogOptionList"}}}, "UpdateLogLevelsByResourceTypesResponse": {"type": "structure", "members": {}}, "UpdateMulticastGroupRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "MulticastGroupId", "location": "uri", "locationName": "Id"}, "Name": {"shape": "MulticastGroupName"}, "Description": {"shape": "Description"}, "LoRaWAN": {"shape": "LoRaWANMulticast"}}}, "UpdateMulticastGroupResponse": {"type": "structure", "members": {}}, "UpdateNetworkAnalyzerConfigurationRequest": {"type": "structure", "required": ["ConfigurationName"], "members": {"ConfigurationName": {"shape": "NetworkAnalyzerConfigurationName", "location": "uri", "locationName": "ConfigurationName"}, "TraceContent": {"shape": "Trace<PERSON><PERSON>nt"}, "WirelessDevicesToAdd": {"shape": "WirelessDeviceList", "documentation": "<p>Wireless device resources to add to the network analyzer configuration. Provide the <code>WirelessDeviceId</code> of the resource to add in the input array.</p>"}, "WirelessDevicesToRemove": {"shape": "WirelessDeviceList", "documentation": "<p>Wireless device resources to remove from the network analyzer configuration. Provide the <code>WirelessDeviceId</code> of the resources to remove in the input array.</p>"}, "WirelessGatewaysToAdd": {"shape": "WirelessGatewayList", "documentation": "<p>Wireless gateway resources to add to the network analyzer configuration. Provide the <code>WirelessGatewayId</code> of the resource to add in the input array.</p>"}, "WirelessGatewaysToRemove": {"shape": "WirelessGatewayList", "documentation": "<p>Wireless gateway resources to remove from the network analyzer configuration. Provide the <code>WirelessGatewayId</code> of the resources to remove in the input array.</p>"}, "Description": {"shape": "Description"}, "MulticastGroupsToAdd": {"shape": "NetworkAnalyzerMulticastGroupList", "documentation": "<p>Multicast group resources to add to the network analyzer configuration. Provide the <code>MulticastGroupId</code> of the resource to add in the input array.</p>"}, "MulticastGroupsToRemove": {"shape": "NetworkAnalyzerMulticastGroupList", "documentation": "<p>Multicast group resources to remove from the network analyzer configuration. Provide the <code>MulticastGroupId</code> of the resources to remove in the input array.</p>"}}}, "UpdateNetworkAnalyzerConfigurationResponse": {"type": "structure", "members": {}}, "UpdatePartnerAccountRequest": {"type": "structure", "required": ["Sidewalk", "PartnerAccountId", "PartnerType"], "members": {"Sidewalk": {"shape": "SidewalkUpdateAccount", "documentation": "<p>The Sidewalk account credentials.</p>"}, "PartnerAccountId": {"shape": "PartnerAccountId", "documentation": "<p>The ID of the partner account to update.</p>", "location": "uri", "locationName": "PartnerAccountId"}, "PartnerType": {"shape": "PartnerType", "documentation": "<p>The partner type.</p>", "location": "querystring", "locationName": "partnerType"}}}, "UpdatePartnerAccountResponse": {"type": "structure", "members": {}}, "UpdatePositionRequest": {"type": "structure", "required": ["ResourceIdentifier", "ResourceType", "Position"], "members": {"ResourceIdentifier": {"shape": "PositionResourceIdentifier", "documentation": "<p>Resource identifier of the resource for which position is updated.</p>", "location": "uri", "locationName": "ResourceIdentifier"}, "ResourceType": {"shape": "PositionResourceType", "documentation": "<p>Resource type of the resource for which position is updated.</p>", "location": "querystring", "locationName": "resourceType"}, "Position": {"shape": "PositionCoordinate", "documentation": "<p>The position information of the resource.</p>"}}, "deprecated": true, "deprecatedMessage": "This operation is no longer supported."}, "UpdatePositionResponse": {"type": "structure", "members": {}, "deprecated": true, "deprecatedMessage": "This operation is no longer supported."}, "UpdateResourceEventConfigurationRequest": {"type": "structure", "required": ["Identifier", "IdentifierType"], "members": {"Identifier": {"shape": "Identifier", "documentation": "<p>Resource identifier to opt in for event messaging.</p>", "location": "uri", "locationName": "Identifier"}, "IdentifierType": {"shape": "IdentifierType", "documentation": "<p>Identifier type of the particular resource identifier for event configuration.</p>", "location": "querystring", "locationName": "identifierType"}, "PartnerType": {"shape": "EventNotificationPartnerType", "documentation": "<p>Partner type of the resource if the identifier type is <code>PartnerAccountId</code> </p>", "location": "querystring", "locationName": "partnerType"}, "DeviceRegistrationState": {"shape": "DeviceRegistrationStateEventConfiguration", "documentation": "<p>Event configuration for the device registration state event.</p>"}, "Proximity": {"shape": "ProximityEventConfiguration", "documentation": "<p>Event configuration for the proximity event.</p>"}, "Join": {"shape": "JoinEventConfiguration", "documentation": "<p>Event configuration for the join event.</p>"}, "ConnectionStatus": {"shape": "ConnectionStatusEventConfiguration", "documentation": "<p>Event configuration for the connection status event.</p>"}, "MessageDeliveryStatus": {"shape": "MessageDeliveryStatusEventConfiguration", "documentation": "<p>Event configuration for the message delivery status event.</p>"}}}, "UpdateResourceEventConfigurationResponse": {"type": "structure", "members": {}}, "UpdateResourcePositionRequest": {"type": "structure", "required": ["ResourceIdentifier", "ResourceType"], "members": {"ResourceIdentifier": {"shape": "PositionResourceIdentifier", "documentation": "<p>The identifier of the resource for which position information is updated. It can be the wireless device ID or the wireless gateway ID, depending on the resource type.</p>", "location": "uri", "locationName": "ResourceIdentifier"}, "ResourceType": {"shape": "PositionResourceType", "documentation": "<p>The type of resource for which position information is updated, which can be a wireless device or a wireless gateway.</p>", "location": "querystring", "locationName": "resourceType"}, "GeoJsonPayload": {"shape": "GeoJsonPayload", "documentation": "<p>The position information of the resource, displayed as a JSON payload. The payload uses the GeoJSON format, which a format that's used to encode geographic data structures. For more information, see <a href=\"https://geojson.org/\">GeoJSON</a>.</p>"}}, "payload": "GeoJsonPayload"}, "UpdateResourcePositionResponse": {"type": "structure", "members": {}}, "UpdateSignature": {"type": "string", "max": 4096, "min": 1}, "UpdateWirelessDeviceImportTaskRequest": {"type": "structure", "required": ["Id", "Sidewalk"], "members": {"Id": {"shape": "ImportTaskId", "documentation": "<p>The identifier of the import task to be updated.</p>", "location": "uri", "locationName": "Id"}, "Sidewalk": {"shape": "SidewalkUpdateImportInfo", "documentation": "<p>The Sidewalk-related parameters of the import task to be updated.</p>"}}}, "UpdateWirelessDeviceImportTaskResponse": {"type": "structure", "members": {}}, "UpdateWirelessDeviceRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "WirelessDeviceId", "documentation": "<p>The ID of the resource to update.</p>", "location": "uri", "locationName": "Id"}, "DestinationName": {"shape": "DestinationName", "documentation": "<p>The name of the new destination for the device.</p>"}, "Name": {"shape": "WirelessDeviceName", "documentation": "<p>The new name of the resource.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A new description of the resource.</p>"}, "LoRaWAN": {"shape": "LoRaWANUpdateDevice", "documentation": "<p>The updated wireless device's configuration.</p>"}, "Positioning": {"shape": "PositioningConfigStatus", "documentation": "<p>FPort values for the GNSS, stream, and ClockSync functions of the positioning information.</p>"}}}, "UpdateWirelessDeviceResponse": {"type": "structure", "members": {}}, "UpdateWirelessGatewayRequest": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "WirelessGatewayId", "documentation": "<p>The ID of the resource to update.</p>", "location": "uri", "locationName": "Id"}, "Name": {"shape": "WirelessGatewayName", "documentation": "<p>The new name of the resource.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A new description of the resource.</p>"}, "JoinEuiFilters": {"shape": "Join<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "NetIdFilters": {"shape": "NetIdFilters"}, "MaxEirp": {"shape": "GatewayMaxEirp", "documentation": "<p>The MaxEIRP value.</p>"}}}, "UpdateWirelessGatewayResponse": {"type": "structure", "members": {}}, "UpdateWirelessGatewayTaskCreate": {"type": "structure", "members": {"UpdateDataSource": {"shape": "UpdateDataSource", "documentation": "<p>The link to the S3 bucket.</p>"}, "UpdateDataRole": {"shape": "UpdateDataSource", "documentation": "<p>The IAM role used to read data from the S3 bucket.</p>"}, "LoRaWAN": {"shape": "LoRaWANUpdateGatewayTaskCreate", "documentation": "<p>The properties that relate to the LoRaWAN wireless gateway.</p>"}}, "documentation": "<p>UpdateWirelessGatewayTaskCreate object.</p>"}, "UpdateWirelessGatewayTaskEntry": {"type": "structure", "members": {"Id": {"shape": "WirelessGatewayTaskDefinitionId", "documentation": "<p>The ID of the new wireless gateway task entry.</p>"}, "LoRaWAN": {"shape": "LoRaWANUpdateGatewayTaskEntry", "documentation": "<p>The properties that relate to the LoRaWAN wireless gateway.</p>"}, "Arn": {"shape": "WirelessGatewayTaskDefinitionArn", "documentation": "<p>The Amazon Resource Name of the resource.</p>"}}, "documentation": "<p>UpdateWirelessGatewayTaskEntry object.</p>"}, "Use2DSolver": {"type": "boolean"}, "UtranCid": {"type": "integer", "max": 268435455, "min": 0}, "ValidationException": {"type": "structure", "members": {"Message": {"shape": "Message"}}, "documentation": "<p>The input did not meet the specified constraints.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "VerticalAccuracy": {"type": "float", "min": 0}, "WcdmaList": {"type": "list", "member": {"shape": "WcdmaObj"}, "max": 16, "min": 1}, "WcdmaLocalId": {"type": "structure", "required": ["Uarfcndl", "Psc"], "members": {"Uarfcndl": {"shape": "UARFCNDL", "documentation": "<p>WCDMA UTRA Absolute RF Channel Number downlink.</p>"}, "Psc": {"shape": "PSC", "documentation": "<p>Primary Scrambling Code.</p>"}}, "documentation": "<p>WCDMA local identification (local ID) information.</p>"}, "WcdmaNmrList": {"type": "list", "member": {"shape": "WcdmaNmrObj"}, "max": 32, "min": 1}, "WcdmaNmrObj": {"type": "structure", "required": ["Uarfcndl", "Psc", "UtranCid"], "members": {"Uarfcndl": {"shape": "UARFCNDL", "documentation": "<p>WCDMA UTRA Absolute RF Channel Number downlink.</p>"}, "Psc": {"shape": "PSC", "documentation": "<p>Primary Scrambling Code.</p>"}, "UtranCid": {"shape": "UtranCid", "documentation": "<p>UTRAN (UMTS Terrestrial Radio Access Network) Cell Global Identifier.</p>"}, "Rscp": {"shape": "RSCP", "documentation": "<p>Received Signal Code Power (signal power) (dBm)</p>"}, "PathLoss": {"shape": "PathLoss", "documentation": "<p>Path loss, or path attenuation, is the reduction in power density of an electromagnetic wave as it propagates through space.</p>"}}, "documentation": "<p>Network Measurement Reports.</p>"}, "WcdmaObj": {"type": "structure", "required": ["Mcc", "Mnc", "UtranCid"], "members": {"Mcc": {"shape": "MCC", "documentation": "<p>Mobile Country Code.</p>"}, "Mnc": {"shape": "MNC", "documentation": "<p>Mobile Network Code.</p>"}, "Lac": {"shape": "LAC", "documentation": "<p>Location Area Code.</p>"}, "UtranCid": {"shape": "UtranCid", "documentation": "<p>UTRAN (UMTS Terrestrial Radio Access Network) Cell Global Identifier.</p>"}, "WcdmaLocalId": {"shape": "WcdmaLocalId", "documentation": "<p>WCDMA local ID information.</p>"}, "Rscp": {"shape": "RSCP", "documentation": "<p>Received Signal Code Power (signal power) (dBm).</p>"}, "PathLoss": {"shape": "PathLoss", "documentation": "<p>Path loss, or path attenuation, is the reduction in power density of an electromagnetic wave as it propagates through space.</p>"}, "WcdmaNmr": {"shape": "WcdmaNmrList", "documentation": "<p>WCDMA object for network measurement reports.</p>"}}, "documentation": "<p>WCDMA.</p>"}, "WiFiAccessPoint": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>", "Rss"], "members": {"MacAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Wi-Fi MAC Address.</p>"}, "Rss": {"shape": "RSS", "documentation": "<p>Received signal strength (dBm) of the WLAN measurement data.</p>"}}, "documentation": "<p>Wi-Fi access point.</p>"}, "WiFiAccessPoints": {"type": "list", "member": {"shape": "WiFiAccessPoint"}}, "WirelessDeviceArn": {"type": "string"}, "WirelessDeviceEvent": {"type": "string", "documentation": "<p>The event for a log message, if the log message is tied to a wireless device.</p>", "enum": ["Join", "Rejoin", "Uplink_Data", "Downlink_Data", "Registration"]}, "WirelessDeviceEventLogOption": {"type": "structure", "required": ["Event", "LogLevel"], "members": {"Event": {"shape": "WirelessDeviceEvent"}, "LogLevel": {"shape": "LogLevel"}}, "documentation": "<p>The log options for a wireless device event and can be used to set log levels for a specific wireless device event.</p> <p>For a LoRaWAN device, possible events for a log messsage are: <code>Join</code>, <code>Rejoin</code>, <code>Downlink_Data</code>, and <code>Uplink_Data</code>. For a Sidewalk device, possible events for a log message are <code>Registration</code>, <code>Downlink_Data</code>, and <code>Uplink_Data</code>.</p>"}, "WirelessDeviceEventLogOptionList": {"type": "list", "member": {"shape": "WirelessDeviceEventLogOption"}, "documentation": "<p>The list of wireless device event log options.</p>"}, "WirelessDeviceFrameInfo": {"type": "string", "documentation": "<p> <code>FrameInfo</code> of your wireless device resources for the trace content. Use FrameInfo to debug the communication between your LoRaWAN end devices and the network server.</p>", "enum": ["ENABLED", "DISABLED"]}, "WirelessDeviceId": {"type": "string", "documentation": "<p>The ID of the wireless device.</p>", "max": 256}, "WirelessDeviceIdType": {"type": "string", "enum": ["WirelessDeviceId", "DevEui", "ThingName", "SidewalkManufacturingSn"]}, "WirelessDeviceImportTask": {"type": "structure", "members": {"Id": {"shape": "ImportTaskId", "documentation": "<p>The ID of the wireless device import task.</p>"}, "Arn": {"shape": "ImportTaskArn", "documentation": "<p>The ARN (Amazon Resource Name) of the wireless device import task.</p>"}, "DestinationName": {"shape": "DestinationName", "documentation": "<p>The name of the Sidewalk destination that that describes the IoT rule to route messages from the device in the import task that will be onboarded to AWS IoT Wireless</p>"}, "Sidewalk": {"shape": "SidewalkGetStartImportInfo", "documentation": "<p>The Sidewalk-related information of the wireless device import task.</p>"}, "CreationTime": {"shape": "CreationTime", "documentation": "<p>The time at which the import task was created.</p>"}, "Status": {"shape": "ImportTaskStatus", "documentation": "<p>The status information of the wireless device import task.</p>"}, "StatusReason": {"shape": "StatusReason", "documentation": "<p>The reason that provides additional information about the import task status.</p>"}, "InitializedImportedDeviceCount": {"shape": "ImportedWirelessDeviceCount", "documentation": "<p>The summary information of count of wireless devices that are waiting for the control log to be added to an import task.</p>"}, "PendingImportedDeviceCount": {"shape": "ImportedWirelessDeviceCount", "documentation": "<p>The summary information of count of wireless devices in an import task that are waiting in the queue to be onboarded.</p>"}, "OnboardedImportedDeviceCount": {"shape": "ImportedWirelessDeviceCount", "documentation": "<p>The summary information of count of wireless devices in an import task that have been onboarded to the import task.</p>"}, "FailedImportedDeviceCount": {"shape": "ImportedWirelessDeviceCount", "documentation": "<p>The summary information of count of wireless devices in an import task that failed to onboarded to the import task.</p>"}}, "documentation": "<p>Information about an import task for wireless devices.</p>"}, "WirelessDeviceImportTaskList": {"type": "list", "member": {"shape": "WirelessDeviceImportTask"}}, "WirelessDeviceList": {"type": "list", "member": {"shape": "WirelessDeviceId"}, "max": 250, "min": 0}, "WirelessDeviceLogOption": {"type": "structure", "required": ["Type", "LogLevel"], "members": {"Type": {"shape": "WirelessDeviceType", "documentation": "<p>The wireless device type.</p>"}, "LogLevel": {"shape": "LogLevel"}, "Events": {"shape": "WirelessDeviceEventLogOptionList"}}, "documentation": "<p>The log options for wireless devices and can be used to set log levels for a specific type of wireless device.</p>"}, "WirelessDeviceLogOptionList": {"type": "list", "member": {"shape": "WirelessDeviceLogOption"}, "documentation": "<p>The list of wireless device log options.</p>"}, "WirelessDeviceName": {"type": "string", "max": 256}, "WirelessDeviceSidewalkStatus": {"type": "string", "enum": ["PROVISIONED", "REGISTERED", "ACTIVATED", "UNKNOWN"]}, "WirelessDeviceStatistics": {"type": "structure", "members": {"Arn": {"shape": "WirelessDeviceArn", "documentation": "<p>The Amazon Resource Name of the resource.</p>"}, "Id": {"shape": "WirelessDeviceId", "documentation": "<p>The ID of the wireless device reporting the data.</p>"}, "Type": {"shape": "WirelessDeviceType", "documentation": "<p>The wireless device type.</p>"}, "Name": {"shape": "WirelessDeviceName", "documentation": "<p>The name of the resource.</p>"}, "DestinationName": {"shape": "DestinationName", "documentation": "<p>The name of the destination to which the device is assigned.</p>"}, "LastUplinkReceivedAt": {"shape": "ISODateTimeString", "documentation": "<p>The date and time when the most recent uplink was received.</p> <note> <p>Theis value is only valid for 3 months.</p> </note>"}, "LoRaWAN": {"shape": "LoRaWANListDevice", "documentation": "<p>LoRaWAN device info.</p>"}, "Sidewalk": {"shape": "SidewalkListDevice", "documentation": "<p>The Sidewalk account credentials.</p>"}, "FuotaDeviceStatus": {"shape": "FuotaDeviceStatus"}, "MulticastDeviceStatus": {"shape": "MulticastDeviceStatus", "documentation": "<p>The status of the wireless device in the multicast group.</p>"}, "McGroupId": {"shape": "McGroupId"}}, "documentation": "<p>Information about a wireless device's operation.</p>"}, "WirelessDeviceStatisticsList": {"type": "list", "member": {"shape": "WirelessDeviceStatistics"}}, "WirelessDeviceType": {"type": "string", "enum": ["Sidewalk", "LoRaWAN"]}, "WirelessGatewayArn": {"type": "string"}, "WirelessGatewayEvent": {"type": "string", "documentation": "<p>The event for a log message, if the log message is tied to a wireless gateway.</p>", "enum": ["CUPS_Request", "Certificate"]}, "WirelessGatewayEventLogOption": {"type": "structure", "required": ["Event", "LogLevel"], "members": {"Event": {"shape": "WirelessGatewayEvent"}, "LogLevel": {"shape": "LogLevel"}}, "documentation": "<p>The log options for a wireless gateway event and can be used to set log levels for a specific wireless gateway event.</p> <p>For a LoRaWAN gateway, possible events for a log message are <code>CUPS_Request</code> and <code>Certificate</code>.</p>"}, "WirelessGatewayEventLogOptionList": {"type": "list", "member": {"shape": "WirelessGatewayEventLogOption"}, "documentation": "<p>The list of wireless gateway event log options.</p>"}, "WirelessGatewayId": {"type": "string", "max": 256}, "WirelessGatewayIdType": {"type": "string", "enum": ["GatewayEui", "WirelessGatewayId", "ThingName"]}, "WirelessGatewayList": {"type": "list", "member": {"shape": "WirelessGatewayId"}}, "WirelessGatewayLogOption": {"type": "structure", "required": ["Type", "LogLevel"], "members": {"Type": {"shape": "WirelessGatewayType"}, "LogLevel": {"shape": "LogLevel"}, "Events": {"shape": "WirelessGatewayEventLogOptionList"}}, "documentation": "<p>The log options for wireless gateways and can be used to set log levels for a specific type of wireless gateway.</p>"}, "WirelessGatewayLogOptionList": {"type": "list", "member": {"shape": "WirelessGatewayLogOption"}, "documentation": "<p>The list of wireless gateway log options.</p>"}, "WirelessGatewayName": {"type": "string", "max": 256}, "WirelessGatewayServiceType": {"type": "string", "enum": ["CUPS", "LNS"]}, "WirelessGatewayStatistics": {"type": "structure", "members": {"Arn": {"shape": "WirelessGatewayArn", "documentation": "<p>The Amazon Resource Name of the resource.</p>"}, "Id": {"shape": "WirelessGatewayId", "documentation": "<p>The ID of the wireless gateway reporting the data.</p>"}, "Name": {"shape": "WirelessGatewayName", "documentation": "<p>The name of the resource.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the resource.</p>"}, "LoRaWAN": {"shape": "LoRaWANGateway", "documentation": "<p>LoRaWAN gateway info.</p>"}, "LastUplinkReceivedAt": {"shape": "ISODateTimeString", "documentation": "<p>The date and time when the most recent uplink was received.</p> <note> <p>This value is only valid for 3 months.</p> </note>"}}, "documentation": "<p>Information about a wireless gateway's operation.</p>"}, "WirelessGatewayStatisticsList": {"type": "list", "member": {"shape": "WirelessGatewayStatistics"}}, "WirelessGatewayTaskDefinitionArn": {"type": "string"}, "WirelessGatewayTaskDefinitionId": {"type": "string", "max": 36, "pattern": "[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}"}, "WirelessGatewayTaskDefinitionList": {"type": "list", "member": {"shape": "UpdateWirelessGatewayTaskEntry"}}, "WirelessGatewayTaskDefinitionType": {"type": "string", "enum": ["UPDATE"]}, "WirelessGatewayTaskName": {"type": "string", "max": 2048, "min": 1}, "WirelessGatewayTaskStatus": {"type": "string", "enum": ["PENDING", "IN_PROGRESS", "FIRST_RETRY", "SECOND_RETRY", "COMPLETED", "FAILED"]}, "WirelessGatewayType": {"type": "string", "documentation": "<p>The wireless gateway type.</p>", "enum": ["LoRaWAN"]}, "WirelessMetadata": {"type": "structure", "members": {"LoRaWAN": {"shape": "LoRaWANSendDataToDevice", "documentation": "<p>LoRaWAN device info.</p>"}, "Sidewalk": {"shape": "SidewalkSendDataToDevice", "documentation": "<p>The Sidewalk account credentials.</p>"}}, "documentation": "<p>WirelessMetadata object.</p>"}}, "documentation": "<p>AWS IoT Wireless provides bi-directional communication between internet-connected wireless devices and the AWS Cloud. To onboard both LoRaWAN and Sidewalk devices to AWS IoT, use the IoT Wireless API. These wireless devices use the Low Power Wide Area Networking (LPWAN) communication protocol to communicate with AWS IoT.</p> <p>Using the API, you can perform create, read, update, and delete operations for your wireless devices, gateways, destinations, and profiles. After onboarding your devices, you can use the API operations to set log levels and monitor your devices with CloudWatch.</p> <p>You can also use the API operations to create multicast groups and schedule a multicast session for sending a downlink message to devices in the group. By using Firmware Updates Over-The-Air (FUOTA) API operations, you can create a FUOTA task and schedule a session to update the firmware of individual devices or an entire group of devices in a multicast group.</p>"}