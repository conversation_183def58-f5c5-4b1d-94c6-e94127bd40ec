{"version": "2.0", "metadata": {"apiVersion": "2018-05-10", "endpointPrefix": "ssm-sap", "jsonVersion": "1.1", "protocol": "rest-json", "serviceAbbreviation": "SsmSap", "serviceFullName": "AWS Systems Manager for SAP", "serviceId": "Ssm Sap", "signatureVersion": "v4", "signingName": "ssm-sap", "uid": "ssm-sap-2018-05-10"}, "operations": {"DeleteResourcePermission": {"name": "DeleteResourcePermission", "http": {"method": "POST", "requestUri": "/delete-resource-permission", "responseCode": 200}, "input": {"shape": "DeleteResourcePermissionInput"}, "output": {"shape": "DeleteResourcePermissionOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Removes permissions associated with the target database.</p>"}, "DeregisterApplication": {"name": "DeregisterApplication", "http": {"method": "POST", "requestUri": "/deregister-application", "responseCode": 200}, "input": {"shape": "DeregisterApplicationInput"}, "output": {"shape": "DeregisterApplicationOutput"}, "errors": [{"shape": "UnauthorizedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deregister an SAP application with AWS Systems Manager for SAP. This action does not aﬀect the existing setup of your SAP workloads on Amazon EC2.</p>"}, "GetApplication": {"name": "GetApplication", "http": {"method": "POST", "requestUri": "/get-application", "responseCode": 200}, "input": {"shape": "GetApplicationInput"}, "output": {"shape": "GetApplicationOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets an application registered with AWS Systems Manager for SAP. It also returns the components of the application.</p>"}, "GetComponent": {"name": "GetComponent", "http": {"method": "POST", "requestUri": "/get-component", "responseCode": 200}, "input": {"shape": "GetComponentInput"}, "output": {"shape": "GetComponentOutput"}, "errors": [{"shape": "UnauthorizedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the component of an application registered with AWS Systems Manager for SAP.</p>"}, "GetDatabase": {"name": "GetDatabase", "http": {"method": "POST", "requestUri": "/get-database", "responseCode": 200}, "input": {"shape": "GetDatabaseInput"}, "output": {"shape": "GetDatabaseOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the SAP HANA database of an application registered with AWS Systems Manager for SAP.</p>"}, "GetOperation": {"name": "GetOperation", "http": {"method": "POST", "requestUri": "/get-operation", "responseCode": 200}, "input": {"shape": "GetOperationInput"}, "output": {"shape": "GetOperationOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the details of an operation by specifying the operation ID.</p>"}, "GetResourcePermission": {"name": "GetResourcePermission", "http": {"method": "POST", "requestUri": "/get-resource-permission", "responseCode": 200}, "input": {"shape": "GetResourcePermissionInput"}, "output": {"shape": "GetResourcePermissionOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets permissions associated with the target database.</p>"}, "ListApplications": {"name": "ListApplications", "http": {"method": "POST", "requestUri": "/list-applications", "responseCode": 200}, "input": {"shape": "ListApplicationsInput"}, "output": {"shape": "ListApplicationsOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists all the applications registered with AWS Systems Manager for SAP.</p>"}, "ListComponents": {"name": "ListComponents", "http": {"method": "POST", "requestUri": "/list-components", "responseCode": 200}, "input": {"shape": "ListComponentsInput"}, "output": {"shape": "ListComponentsOutput"}, "errors": [{"shape": "UnauthorizedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists all the components registered with AWS Systems Manager for SAP.</p>"}, "ListDatabases": {"name": "ListDatabases", "http": {"method": "POST", "requestUri": "/list-databases", "responseCode": 200}, "input": {"shape": "ListDatabasesInput"}, "output": {"shape": "ListDatabasesOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the SAP HANA databases of an application registered with AWS Systems Manager for SAP.</p>"}, "ListOperations": {"name": "ListOperations", "http": {"method": "POST", "requestUri": "/list-operations", "responseCode": 200}, "input": {"shape": "ListOperationsInput"}, "output": {"shape": "ListOperationsOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the operations performed by AWS Systems Manager for SAP.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Lists all tags on an SAP HANA application and/or database registered with AWS Systems Manager for SAP.</p>"}, "PutResourcePermission": {"name": "PutResourcePermission", "http": {"method": "POST", "requestUri": "/put-resource-permission", "responseCode": 200}, "input": {"shape": "PutResourcePermissionInput"}, "output": {"shape": "PutResourcePermissionOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Adds permissions to the target database.</p>"}, "RegisterApplication": {"name": "RegisterApplication", "http": {"method": "POST", "requestUri": "/register-application", "responseCode": 200}, "input": {"shape": "RegisterApplicationInput"}, "output": {"shape": "RegisterApplicationOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Register an SAP application with AWS Systems Manager for SAP. You must meet the following requirements before registering. </p> <p>The SAP application you want to register with AWS Systems Manager for SAP is running on Amazon EC2.</p> <p>AWS Systems Manager Agent must be setup on an Amazon EC2 instance along with the required IAM permissions.</p> <p>Amazon EC2 instance(s) must have access to the secrets created in AWS Secrets Manager to manage SAP applications and components.</p>"}, "StartApplicationRefresh": {"name": "StartApplicationRefresh", "http": {"method": "POST", "requestUri": "/start-application-refresh", "responseCode": 200}, "input": {"shape": "StartApplicationRefreshInput"}, "output": {"shape": "StartApplicationRefreshOutput"}, "errors": [{"shape": "UnauthorizedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Refreshes a registered application.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates tag for a resource by specifying the ARN.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Delete the tags for a resource.</p>", "idempotent": true}, "UpdateApplicationSettings": {"name": "UpdateApplicationSettings", "http": {"method": "POST", "requestUri": "/update-application-settings", "responseCode": 200}, "input": {"shape": "UpdateApplicationSettingsInput"}, "output": {"shape": "UpdateApplicationSettingsOutput"}, "errors": [{"shape": "UnauthorizedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates the settings of an application registered with AWS Systems Manager for SAP.</p>"}}, "shapes": {"AllocationType": {"type": "string", "enum": ["VPC_SUBNET", "ELASTIC_IP", "OVERLAY", "UNKNOWN"]}, "AppRegistryArn": {"type": "string", "pattern": "arn:aws:servicecatalog:[a-z0-9:\\/-]+"}, "Application": {"type": "structure", "members": {"Id": {"shape": "ApplicationId", "documentation": "<p>The ID of the application.</p>"}, "Type": {"shape": "ApplicationType", "documentation": "<p>The type of the application.</p>"}, "Arn": {"shape": "SsmSapArn", "documentation": "<p>The Amazon Resource Name (ARN) of the application.</p>"}, "AppRegistryArn": {"shape": "AppRegistryArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Application Registry.</p>"}, "Status": {"shape": "ApplicationStatus", "documentation": "<p>The status of the application.</p>"}, "DiscoveryStatus": {"shape": "ApplicationDiscoveryStatus", "documentation": "<p>The latest discovery result for the application.</p>"}, "Components": {"shape": "ComponentIdList", "documentation": "<p>The components of the application.</p>"}, "LastUpdated": {"shape": "Timestamp", "documentation": "<p>The time at which the application was last updated.</p>"}, "StatusMessage": {"shape": "String", "documentation": "<p>The status message.</p>"}}, "documentation": "<p>An SAP application registered with AWS Systems Manager for SAP.</p>"}, "ApplicationCredential": {"type": "structure", "required": ["DatabaseName", "CredentialType", "SecretId"], "members": {"DatabaseName": {"shape": "DatabaseName", "documentation": "<p>The name of the SAP HANA database.</p>"}, "CredentialType": {"shape": "CredentialType", "documentation": "<p>The type of the application credentials. </p>"}, "SecretId": {"shape": "SecretId", "documentation": "<p>The secret ID created in AWS Secrets Manager to store the credentials of the SAP application. </p>"}}, "documentation": "<p>The credentials of your SAP application.</p>"}, "ApplicationCredentialList": {"type": "list", "member": {"shape": "ApplicationCredential"}, "max": 20, "min": 0}, "ApplicationDiscoveryStatus": {"type": "string", "enum": ["SUCCESS", "REGISTRATION_FAILED", "REFRESH_FAILED", "REGISTERING", "DELETING"]}, "ApplicationId": {"type": "string", "pattern": "[\\w\\d]{1,50}"}, "ApplicationStatus": {"type": "string", "enum": ["ACTIVATED", "STARTING", "STOPPED", "STOPPING", "FAILED", "REGISTERING", "DELETING", "UNKNOWN"]}, "ApplicationSummary": {"type": "structure", "members": {"Id": {"shape": "ApplicationId", "documentation": "<p>The ID of the application.</p>"}, "DiscoveryStatus": {"shape": "ApplicationDiscoveryStatus", "documentation": "<p>The status of the latest discovery.</p>"}, "Type": {"shape": "ApplicationType", "documentation": "<p>The type of the application.</p>"}, "Arn": {"shape": "SsmSapArn", "documentation": "<p>The Amazon Resource Name (ARN) of the application.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags on the application.</p>"}}, "documentation": "<p>The summary of the SAP application registered with AWS Systems Manager for SAP. </p>"}, "ApplicationSummaryList": {"type": "list", "member": {"shape": "ApplicationSummary"}}, "ApplicationType": {"type": "string", "enum": ["HANA", "SAP_ABAP"]}, "Arn": {"type": "string", "pattern": "arn:(.+:){2,4}.+$|^arn:(.+:){1,3}.+\\/.+"}, "AssociatedHost": {"type": "structure", "members": {"Hostname": {"shape": "String", "documentation": "<p>The name of the host.</p>"}, "Ec2InstanceId": {"shape": "String", "documentation": "<p>The ID of the Amazon EC2 instance.</p>"}, "IpAddresses": {"shape": "IpAddressList", "documentation": "<p>The IP addresses of the associated host.</p>"}, "OsVersion": {"shape": "String", "documentation": "<p>The version of the operating system.</p>"}}, "documentation": "<p>Describes the properties of the associated host.</p>"}, "BackintConfig": {"type": "structure", "required": ["BackintMode", "EnsureNoBackupInProcess"], "members": {"BackintMode": {"shape": "BackintMode", "documentation": "<p>AWS service for your database backup.</p>"}, "EnsureNoBackupInProcess": {"shape": "Boolean", "documentation": "<p/>"}}, "documentation": "<p>Configuration parameters for AWS Backint Agent for SAP HANA. You can backup your SAP HANA database with AWS Backup or Amazon S3.</p>"}, "BackintMode": {"type": "string", "enum": ["AWSBackup"]}, "Boolean": {"type": "boolean", "box": true}, "ClusterStatus": {"type": "string", "enum": ["ONLINE", "STANDBY", "MAINTENANCE", "OFFLINE", "NONE"]}, "Component": {"type": "structure", "members": {"ComponentId": {"shape": "ComponentId", "documentation": "<p>The ID of the component.</p>"}, "Sid": {"shape": "SID", "documentation": "<p>The SAP System Identifier of the application component.</p>"}, "SystemNumber": {"shape": "SAPInstanceNumber", "documentation": "<p>The SAP system number of the application component.</p>"}, "ParentComponent": {"shape": "ComponentId", "documentation": "<p>The parent component of a highly available environment. For example, in a highly available SAP on AWS workload, the parent component consists of the entire setup, including the child components.</p>"}, "ChildComponents": {"shape": "ComponentIdList", "documentation": "<p>The child components of a highly available environment. For example, in a highly available SAP on AWS workload, the child component consists of the primary and secondar instances.</p>"}, "ApplicationId": {"shape": "ApplicationId", "documentation": "<p>The ID of the application.</p>"}, "ComponentType": {"shape": "ComponentType", "documentation": "<p>The type of the component.</p>"}, "Status": {"shape": "ComponentStatus", "documentation": "<p>The status of the component.</p> <ul> <li> <p>ACTIVATED - this status has been deprecated.</p> </li> <li> <p>STARTING - the component is in the process of being started.</p> </li> <li> <p>STOPPED - the component is not running.</p> </li> <li> <p>STOPPING - the component is in the process of being stopped.</p> </li> <li> <p>RUNNING - the component is running.</p> </li> <li> <p>RUNNING_WITH_ERROR - one or more child component(s) of the parent component is not running. Call <a href=\"https://docs.aws.amazon.com/ssmsap/latest/APIReference/API_GetComponent.html\"> <code>GetComponent</code> </a> to review the status of each child component.</p> </li> <li> <p>UNDEFINED - AWS Systems Manager for SAP cannot provide the component status based on the discovered information. Verify your SAP application.</p> </li> </ul>"}, "SapHostname": {"shape": "String", "documentation": "<p>The hostname of the component.</p>"}, "SapFeature": {"shape": "String", "documentation": "<p>The SAP feature of the component.</p>"}, "SapKernelVersion": {"shape": "String", "documentation": "<p>The kernel version of the component.</p>"}, "HdbVersion": {"shape": "String", "documentation": "<p>The SAP HANA version of the component.</p>"}, "Resilience": {"shape": "Resilience", "documentation": "<p>Details of the SAP HANA system replication for the component.</p>"}, "AssociatedHost": {"shape": "AssociatedHost", "documentation": "<p>The associated host of the component.</p>"}, "Databases": {"shape": "DatabaseIdList", "documentation": "<p>The SAP HANA databases of the component.</p>"}, "Hosts": {"shape": "HostList", "documentation": "<p>The hosts of the component.</p>", "deprecated": true, "deprecatedMessage": "This shape is no longer used. Please use AssociatedHost."}, "PrimaryHost": {"shape": "String", "documentation": "<p>The primary host of the component.</p>", "deprecated": true, "deprecatedMessage": "This shape is no longer used. Please use AssociatedHost."}, "DatabaseConnection": {"shape": "DatabaseConnection", "documentation": "<p>The connection specifications for the database of the component.</p>"}, "LastUpdated": {"shape": "Timestamp", "documentation": "<p>The time at which the component was last updated.</p>"}, "Arn": {"shape": "SsmSapArn", "documentation": "<p>The Amazon Resource Name (ARN) of the component.</p>"}}, "documentation": "<p>The SAP component of your application.</p>"}, "ComponentId": {"type": "string", "pattern": "[\\w\\d-]+"}, "ComponentIdList": {"type": "list", "member": {"shape": "ComponentId"}}, "ComponentStatus": {"type": "string", "enum": ["ACTIVATED", "STARTING", "STOPPED", "STOPPING", "RUNNING", "RUNNING_WITH_ERROR", "UNDEFINED"]}, "ComponentSummary": {"type": "structure", "members": {"ApplicationId": {"shape": "ApplicationId", "documentation": "<p>The ID of the application.</p>"}, "ComponentId": {"shape": "ComponentId", "documentation": "<p>The ID of the component.</p>"}, "ComponentType": {"shape": "ComponentType", "documentation": "<p>The type of the component.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags of the component.</p>"}, "Arn": {"shape": "SsmSapArn", "documentation": "<p>The Amazon Resource Name (ARN) of the component summary.</p>"}}, "documentation": "<p>The summary of the component.</p>"}, "ComponentSummaryList": {"type": "list", "member": {"shape": "ComponentSummary"}}, "ComponentType": {"type": "string", "enum": ["HANA", "HANA_NODE", "ABAP", "ASCS", "DIALOG", "WEBDISP", "WD", "ERS"]}, "ConflictException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>A conflict has occurred.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "CredentialType": {"type": "string", "enum": ["ADMIN"]}, "Database": {"type": "structure", "members": {"ApplicationId": {"shape": "ApplicationId", "documentation": "<p>The ID of the application.</p>"}, "ComponentId": {"shape": "ComponentId", "documentation": "<p>The ID of the component.</p>"}, "Credentials": {"shape": "ApplicationCredentialList", "documentation": "<p>The credentials of the database.</p>"}, "DatabaseId": {"shape": "DatabaseId", "documentation": "<p>The ID of the SAP HANA database.</p>"}, "DatabaseName": {"shape": "String", "documentation": "<p>The name of the database.</p>"}, "DatabaseType": {"shape": "DatabaseType", "documentation": "<p>The type of the database.</p>"}, "Arn": {"shape": "SsmSapArn", "documentation": "<p>The Amazon Resource Name (ARN) of the database.</p>"}, "Status": {"shape": "DatabaseStatus", "documentation": "<p>The status of the database.</p>"}, "PrimaryHost": {"shape": "String", "documentation": "<p>The primary host of the database.</p>"}, "SQLPort": {"shape": "Integer", "documentation": "<p>The SQL port of the database.</p>"}, "LastUpdated": {"shape": "Timestamp", "documentation": "<p>The time at which the database was last updated.</p>"}}, "documentation": "<p>The SAP HANA database of the application registered with AWS Systems Manager for SAP.</p>"}, "DatabaseConnection": {"type": "structure", "members": {"DatabaseConnectionMethod": {"shape": "DatabaseConnectionMethod", "documentation": "<p>The method of connection.</p>"}, "DatabaseArn": {"shape": "SsmSapArn", "documentation": "<p>The Amazon Resource Name of the connected SAP HANA database.</p>"}, "ConnectionIp": {"shape": "String", "documentation": "<p>The IP address for connection.</p>"}}, "documentation": "<p>The connection specifications for the database.</p>"}, "DatabaseConnectionMethod": {"type": "string", "enum": ["DIRECT", "OVERLAY"]}, "DatabaseId": {"type": "string", "pattern": ".*[\\w\\d]+"}, "DatabaseIdList": {"type": "list", "member": {"shape": "DatabaseId"}}, "DatabaseName": {"type": "string", "max": 100, "min": 1}, "DatabaseStatus": {"type": "string", "enum": ["RUNNING", "STARTING", "STOPPED", "WARNING", "UNKNOWN", "ERROR"]}, "DatabaseSummary": {"type": "structure", "members": {"ApplicationId": {"shape": "ApplicationId", "documentation": "<p>The ID of the application.</p>"}, "ComponentId": {"shape": "ComponentId", "documentation": "<p>The ID of the component.</p>"}, "DatabaseId": {"shape": "DatabaseId", "documentation": "<p>The ID of the database.</p>"}, "DatabaseType": {"shape": "DatabaseType", "documentation": "<p>The type of the database.</p>"}, "Arn": {"shape": "SsmSapArn", "documentation": "<p>The Amazon Resource Name (ARN) of the database.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags of the database.</p>"}}, "documentation": "<p>The summary of the database.</p>"}, "DatabaseSummaryList": {"type": "list", "member": {"shape": "DatabaseSummary"}}, "DatabaseType": {"type": "string", "enum": ["SYSTEM", "TENANT"]}, "DeleteResourcePermissionInput": {"type": "structure", "required": ["ResourceArn"], "members": {"ActionType": {"shape": "PermissionActionType", "documentation": "<p>Delete or restore the permissions on the target database.</p>"}, "SourceResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the source resource.</p>"}, "ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>"}}}, "DeleteResourcePermissionOutput": {"type": "structure", "members": {"Policy": {"shape": "String", "documentation": "<p>The policy that removes permissions on the target database.</p>"}}}, "DeregisterApplicationInput": {"type": "structure", "required": ["ApplicationId"], "members": {"ApplicationId": {"shape": "ApplicationId", "documentation": "<p>The ID of the application.</p>"}}}, "DeregisterApplicationOutput": {"type": "structure", "members": {}}, "Filter": {"type": "structure", "required": ["Name", "Value", "Operator"], "members": {"Name": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name of the filter. Filter names are case-sensitive. </p>"}, "Value": {"shape": "FilterValue", "documentation": "<p>The filter values. Filter values are case-sensitive. If you specify multiple values for a filter, the values are joined with an OR, and the request returns all results that match any of the specified values</p>"}, "Operator": {"shape": "FilterOperator", "documentation": "<p>The operator for the filter. </p>"}}, "documentation": "<p>A specific result obtained by specifying the name, value, and operator. </p>"}, "FilterList": {"type": "list", "member": {"shape": "Filter"}, "max": 10, "min": 1}, "FilterName": {"type": "string", "max": 32, "min": 1}, "FilterOperator": {"type": "string", "enum": ["Equals", "GreaterThanOrEquals", "LessThanOrEquals"]}, "FilterValue": {"type": "string", "max": 64, "min": 1}, "GetApplicationInput": {"type": "structure", "members": {"ApplicationId": {"shape": "ApplicationId", "documentation": "<p>The ID of the application.</p>"}, "ApplicationArn": {"shape": "SsmSapArn", "documentation": "<p>The Amazon Resource Name (ARN) of the application. </p>"}, "AppRegistryArn": {"shape": "AppRegistryArn", "documentation": "<p>The Amazon Resource Name (ARN) of the application registry.</p>"}}}, "GetApplicationOutput": {"type": "structure", "members": {"Application": {"shape": "Application", "documentation": "<p>Returns all of the metadata of an application registered with AWS Systems Manager for SAP.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags of a registered application.</p>"}}}, "GetComponentInput": {"type": "structure", "required": ["ApplicationId", "ComponentId"], "members": {"ApplicationId": {"shape": "ApplicationId", "documentation": "<p>The ID of the application.</p>"}, "ComponentId": {"shape": "ComponentId", "documentation": "<p>The ID of the component.</p>"}}}, "GetComponentOutput": {"type": "structure", "members": {"Component": {"shape": "Component", "documentation": "<p>The component of an application registered with AWS Systems Manager for SAP.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags of a component.</p>"}}}, "GetDatabaseInput": {"type": "structure", "members": {"ApplicationId": {"shape": "ApplicationId", "documentation": "<p>The ID of the application.</p>"}, "ComponentId": {"shape": "ComponentId", "documentation": "<p>The ID of the component.</p>"}, "DatabaseId": {"shape": "DatabaseId", "documentation": "<p>The ID of the database.</p>"}, "DatabaseArn": {"shape": "SsmSapArn", "documentation": "<p>The Amazon Resource Name (ARN) of the database.</p>"}}}, "GetDatabaseOutput": {"type": "structure", "members": {"Database": {"shape": "Database", "documentation": "<p>The SAP HANA database of an application registered with AWS Systems Manager for SAP.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags of a database.</p>"}}}, "GetOperationInput": {"type": "structure", "required": ["OperationId"], "members": {"OperationId": {"shape": "OperationId", "documentation": "<p>The ID of the operation.</p>"}}}, "GetOperationOutput": {"type": "structure", "members": {"Operation": {"shape": "Operation", "documentation": "<p>Returns the details of an operation.</p>"}}}, "GetResourcePermissionInput": {"type": "structure", "required": ["ResourceArn"], "members": {"ActionType": {"shape": "PermissionActionType", "documentation": "<p/>"}, "ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>"}}}, "GetResourcePermissionOutput": {"type": "structure", "members": {"Policy": {"shape": "String", "documentation": "<p/>"}}}, "Host": {"type": "structure", "members": {"HostName": {"shape": "String", "documentation": "<p>The name of the Dedicated Host.</p>"}, "HostIp": {"shape": "String", "documentation": "<p>The IP address of the Dedicated Host. </p>"}, "EC2InstanceId": {"shape": "String", "documentation": "<p>The ID of Amazon EC2 instance.</p>"}, "InstanceId": {"shape": "String", "documentation": "<p>The instance ID of the instance on the Dedicated Host.</p>"}, "HostRole": {"shape": "HostRole", "documentation": "<p>The role of the Dedicated Host.</p>"}, "OsVersion": {"shape": "String", "documentation": "<p>The version of the operating system.</p>"}}, "documentation": "<p>Describes the properties of the Dedicated Host. </p>"}, "HostList": {"type": "list", "member": {"shape": "Host"}}, "HostRole": {"type": "string", "enum": ["LEADER", "WORKER", "STANDBY", "UNKNOWN"]}, "InstanceId": {"type": "string", "pattern": "i-[\\w\\d]{8}$|^i-[\\w\\d]{17}"}, "InstanceList": {"type": "list", "member": {"shape": "InstanceId"}, "max": 1, "min": 1}, "Integer": {"type": "integer", "box": true}, "InternalServerException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>An internal error has occurred.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "IpAddressList": {"type": "list", "member": {"shape": "IpAddressMember"}}, "IpAddressMember": {"type": "structure", "members": {"IpAddress": {"shape": "String", "documentation": "<p>The IP address.</p>"}, "Primary": {"shape": "Boolean", "documentation": "<p>The primary IP address.</p>"}, "AllocationType": {"shape": "AllocationType", "documentation": "<p>The type of allocation for the IP address.</p>"}}, "documentation": "<p>Provides information of the IP address.</p>"}, "ListApplicationsInput": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return with a single call. To retrieve the remaining results, make another call with the returned nextToken value. </p>", "box": true}, "Filters": {"shape": "FilterList", "documentation": "<p>The filter of name, value, and operator.</p>"}}}, "ListApplicationsOutput": {"type": "structure", "members": {"Applications": {"shape": "ApplicationSummaryList", "documentation": "<p>The applications registered with AWS Systems Manager for SAP.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. This value is null when there are no more results to return.</p>"}}}, "ListComponentsInput": {"type": "structure", "members": {"ApplicationId": {"shape": "ApplicationId", "documentation": "<p>The ID of the application.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return with a single call. To retrieve the remaining results, make another call with the returned nextToken value.</p> <p>If you do not specify a value for MaxResults, the request returns 50 items per page by default.</p>", "box": true}}}, "ListComponentsOutput": {"type": "structure", "members": {"Components": {"shape": "ComponentSummaryList", "documentation": "<p>List of components registered with AWS System Manager for SAP.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. This value is null when there are no more results to return.</p>"}}}, "ListDatabasesInput": {"type": "structure", "members": {"ApplicationId": {"shape": "ApplicationId", "documentation": "<p>The ID of the application.</p>"}, "ComponentId": {"shape": "ComponentId", "documentation": "<p>The ID of the component.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results. </p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return with a single call. To retrieve the remaining results, make another call with the returned nextToken value. If you do not specify a value for MaxResults, the request returns 50 items per page by default.</p>", "box": true}}}, "ListDatabasesOutput": {"type": "structure", "members": {"Databases": {"shape": "DatabaseSummaryList", "documentation": "<p>The SAP HANA databases of an application.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. This value is null when there are no more results to return.</p>"}}}, "ListOperationsInput": {"type": "structure", "required": ["ApplicationId"], "members": {"ApplicationId": {"shape": "ApplicationId", "documentation": "<p>The ID of the application.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return with a single call. To retrieve the remaining results, make another call with the returned nextToken value. If you do not specify a value for MaxResults, the request returns 50 items per page by default.</p>", "box": true}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results. </p>"}, "Filters": {"shape": "FilterList", "documentation": "<p>The filters of an operation.</p>"}}}, "ListOperationsOutput": {"type": "structure", "members": {"Operations": {"shape": "OperationList", "documentation": "<p>List of operations performed by AWS Systems Manager for SAP.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. This value is null when there are no more results to return.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "SsmSapArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagMap", "documentation": "<p/>"}}}, "MaxResults": {"type": "integer", "max": 50, "min": 1}, "NextToken": {"type": "string", "pattern": ".{16,1024}"}, "Operation": {"type": "structure", "members": {"Id": {"shape": "OperationId", "documentation": "<p>The ID of the operation.</p>"}, "Type": {"shape": "OperationType", "documentation": "<p>The type of the operation.</p>"}, "Status": {"shape": "OperationStatus", "documentation": "<p>The status of the operation.</p>"}, "StatusMessage": {"shape": "String", "documentation": "<p>The status message of the operation.</p>"}, "Properties": {"shape": "OperationProperties", "documentation": "<p>The properties of the operation.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The resource type of the operation.</p>"}, "ResourceId": {"shape": "ResourceId", "documentation": "<p>The resource ID of the operation.</p>"}, "ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the operation.</p>"}, "StartTime": {"shape": "Timestamp", "documentation": "<p>The start time of the operation.</p>"}, "EndTime": {"shape": "Timestamp", "documentation": "<p>The end time of the operation.</p>"}, "LastUpdatedTime": {"shape": "Timestamp", "documentation": "<p>The time at which the operation was last updated.</p>"}}, "documentation": "<p>The operations performed by AWS Systems Manager for SAP.</p>"}, "OperationId": {"type": "string", "pattern": "[{]?[0-9a-fA-F]{8}-([0-9a-fA-F]{4}-){3}[0-9a-fA-F]{12}[}]?"}, "OperationIdList": {"type": "list", "member": {"shape": "OperationId"}}, "OperationList": {"type": "list", "member": {"shape": "Operation"}}, "OperationMode": {"type": "string", "enum": ["PRIMARY", "LOGREPLAY", "DELTA_DATASHIPPING", "LOGREPLAY_READACCESS", "NONE"]}, "OperationProperties": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "OperationStatus": {"type": "string", "enum": ["INPROGRESS", "SUCCESS", "ERROR"]}, "OperationType": {"type": "string"}, "PermissionActionType": {"type": "string", "enum": ["RESTORE"]}, "PutResourcePermissionInput": {"type": "structure", "required": ["ActionType", "SourceResourceArn", "ResourceArn"], "members": {"ActionType": {"shape": "PermissionActionType", "documentation": "<p/>"}, "SourceResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p/>"}, "ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p/>"}}}, "PutResourcePermissionOutput": {"type": "structure", "members": {"Policy": {"shape": "String", "documentation": "<p/>"}}}, "RegisterApplicationInput": {"type": "structure", "required": ["ApplicationId", "ApplicationType", "Instances"], "members": {"ApplicationId": {"shape": "ApplicationId", "documentation": "<p>The ID of the application.</p>"}, "ApplicationType": {"shape": "ApplicationType", "documentation": "<p>The type of the application.</p>"}, "Instances": {"shape": "InstanceList", "documentation": "<p>The Amazon EC2 instances on which your SAP application is running.</p>"}, "SapInstanceNumber": {"shape": "SAPInstanceNumber", "documentation": "<p>The SAP instance number of the application.</p>"}, "Sid": {"shape": "SID", "documentation": "<p>The System ID of the application.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags to be attached to the SAP application.</p>"}, "Credentials": {"shape": "ApplicationCredentialList", "documentation": "<p>The credentials of the SAP application.</p>"}, "DatabaseArn": {"shape": "SsmSapArn", "documentation": "<p>The Amazon Resource Name of the SAP HANA database.</p>"}}}, "RegisterApplicationOutput": {"type": "structure", "members": {"Application": {"shape": "Application", "documentation": "<p>The application registered with AWS Systems Manager for SAP.</p>"}, "OperationId": {"shape": "OperationId", "documentation": "<p>The ID of the operation.</p>"}}}, "ReplicationMode": {"type": "string", "enum": ["PRIMARY", "NONE", "SYNC", "SYNCMEM", "ASYNC"]}, "Resilience": {"type": "structure", "members": {"HsrTier": {"shape": "String", "documentation": "<p>The tier of the component.</p>"}, "HsrReplicationMode": {"shape": "ReplicationMode", "documentation": "<p>The replication mode of the component.</p>"}, "HsrOperationMode": {"shape": "OperationMode", "documentation": "<p>The operation mode of the component.</p>"}, "ClusterStatus": {"shape": "ClusterStatus", "documentation": "<p>The cluster status of the component.</p>"}, "EnqueueReplication": {"shape": "Boolean", "documentation": "<p>Indicates if or not enqueue replication is enabled for the ASCS component.</p>"}}, "documentation": "<p>Details of the SAP HANA system replication for the instance.</p>"}, "ResourceId": {"type": "string", "max": 64, "min": 1}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The resource is not available.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "ResourceType": {"type": "string", "max": 64, "min": 1}, "SAPInstanceNumber": {"type": "string", "pattern": "[0-9]{2}"}, "SID": {"type": "string", "pattern": "[A-Z][A-Z0-9]{2}"}, "SecretId": {"type": "string", "max": 100, "min": 1, "sensitive": true}, "SsmSapArn": {"type": "string", "pattern": "arn:(.+:){2,4}.+$|^arn:(.+:){1,3}.+\\/.+"}, "StartApplicationRefreshInput": {"type": "structure", "required": ["ApplicationId"], "members": {"ApplicationId": {"shape": "ApplicationId", "documentation": "<p>The ID of the application.</p>"}}}, "StartApplicationRefreshOutput": {"type": "structure", "members": {"OperationId": {"shape": "OperationId", "documentation": "<p>The ID of the operation.</p>"}}}, "String": {"type": "string"}, "TagKey": {"type": "string", "pattern": "(?!aws:)[a-zA-Z+-=._:/]+"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}}, "TagMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "SsmSapArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags on a resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 1}, "Timestamp": {"type": "timestamp"}, "UnauthorizedException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The request is not authorized.</p>", "error": {"httpStatusCode": 401, "senderFault": true}, "exception": true}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "SsmSapArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>Adds/updates or removes credentials for applications registered with AWS Systems Manager for SAP.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateApplicationSettingsInput": {"type": "structure", "required": ["ApplicationId"], "members": {"ApplicationId": {"shape": "ApplicationId", "documentation": "<p>The ID of the application.</p>"}, "CredentialsToAddOrUpdate": {"shape": "ApplicationCredentialList", "documentation": "<p>The credentials to be added or updated.</p>"}, "CredentialsToRemove": {"shape": "ApplicationCredentialList", "documentation": "<p>The credentials to be removed.</p>"}, "Backint": {"shape": "BackintConfig", "documentation": "<p>Installation of AWS Backint Agent for SAP HANA.</p>"}, "DatabaseArn": {"shape": "SsmSapArn", "documentation": "<p>The Amazon Resource Name of the SAP HANA database that replaces the current SAP HANA connection with the SAP_ABAP application.</p>"}}}, "UpdateApplicationSettingsOutput": {"type": "structure", "members": {"Message": {"shape": "String", "documentation": "<p>The update message.</p>"}, "OperationIds": {"shape": "OperationIdList", "documentation": "<p>The IDs of the operations.</p>"}}}, "ValidationException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The input fails to satisfy the constraints specified by an AWS service. </p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}}, "documentation": "<p>This API reference provides descriptions, syntax, and other details about each of the actions and data types for AWS Systems Manager for SAP. The topic for each action shows the API request parameters and responses. </p>"}