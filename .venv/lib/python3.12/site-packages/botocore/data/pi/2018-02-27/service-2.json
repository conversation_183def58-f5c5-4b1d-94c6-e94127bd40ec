{"version": "2.0", "metadata": {"apiVersion": "2018-02-27", "endpointPrefix": "pi", "jsonVersion": "1.1", "protocol": "json", "serviceAbbreviation": "AWS PI", "serviceFullName": "AWS Performance Insights", "serviceId": "PI", "signatureVersion": "v4", "signingName": "pi", "targetPrefix": "PerformanceInsightsv20180227", "uid": "pi-2018-02-27"}, "operations": {"CreatePerformanceAnalysisReport": {"name": "CreatePerformanceAnalysisReport", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreatePerformanceAnalysisReportRequest"}, "output": {"shape": "CreatePerformanceAnalysisReportResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "InternalServiceError"}, {"shape": "NotAuthorizedException"}], "documentation": "<p>Creates a new performance analysis report for a specific time period for the DB instance.</p>"}, "DeletePerformanceAnalysisReport": {"name": "DeletePerformanceAnalysisReport", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeletePerformanceAnalysisReportRequest"}, "output": {"shape": "DeletePerformanceAnalysisReportResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "InternalServiceError"}, {"shape": "NotAuthorizedException"}], "documentation": "<p>Deletes a performance analysis report.</p>"}, "DescribeDimensionKeys": {"name": "DescribeDimensionKeys", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeDimensionKeysRequest"}, "output": {"shape": "DescribeDimensionKeysResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "InternalServiceError"}, {"shape": "NotAuthorizedException"}], "documentation": "<p>For a specific time period, retrieve the top <code>N</code> dimension keys for a metric. </p> <note> <p>Each response element returns a maximum of 500 bytes. For larger elements, such as SQL statements, only the first 500 bytes are returned.</p> </note>"}, "GetDimensionKeyDetails": {"name": "GetDimensionKeyDetails", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetDimensionKeyDetailsRequest"}, "output": {"shape": "GetDimensionKeyDetailsResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "InternalServiceError"}, {"shape": "NotAuthorizedException"}], "documentation": "<p>Get the attributes of the specified dimension group for a DB instance or data source. For example, if you specify a SQL ID, <code>GetDimensionKeyDetails</code> retrieves the full text of the dimension <code>db.sql.statement</code> associated with this ID. This operation is useful because <code>GetResourceMetrics</code> and <code>DescribeDimensionKeys</code> don't support retrieval of large SQL statement text.</p>"}, "GetPerformanceAnalysisReport": {"name": "GetPerformanceAnalysisReport", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetPerformanceAnalysisReportRequest"}, "output": {"shape": "GetPerformanceAnalysisReportResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "InternalServiceError"}, {"shape": "NotAuthorizedException"}], "documentation": "<p>Retrieves the report including the report ID, status, time details, and the insights with recommendations. The report status can be <code>RUNNING</code>, <code>SUCCEEDED</code>, or <code>FAILED</code>. The insights include the <code>description</code> and <code>recommendation</code> fields. </p>"}, "GetResourceMetadata": {"name": "GetResourceMetadata", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetResourceMetadataRequest"}, "output": {"shape": "GetResourceMetadataResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "InternalServiceError"}, {"shape": "NotAuthorizedException"}], "documentation": "<p>Retrieve the metadata for different features. For example, the metadata might indicate that a feature is turned on or off on a specific DB instance. </p>"}, "GetResourceMetrics": {"name": "GetResourceMetrics", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetResourceMetricsRequest"}, "output": {"shape": "GetResourceMetricsResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "InternalServiceError"}, {"shape": "NotAuthorizedException"}], "documentation": "<p>Retrieve Performance Insights metrics for a set of data sources over a time period. You can provide specific dimension groups and dimensions, and provide aggregation and filtering criteria for each group.</p> <note> <p>Each response element returns a maximum of 500 bytes. For larger elements, such as SQL statements, only the first 500 bytes are returned.</p> </note>"}, "ListAvailableResourceDimensions": {"name": "ListAvailableResourceDimensions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAvailableResourceDimensionsRequest"}, "output": {"shape": "ListAvailableResourceDimensionsResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "InternalServiceError"}, {"shape": "NotAuthorizedException"}], "documentation": "<p>Retrieve the dimensions that can be queried for each specified metric type on a specified DB instance.</p>"}, "ListAvailableResourceMetrics": {"name": "ListAvailableResourceMetrics", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAvailableResourceMetricsRequest"}, "output": {"shape": "ListAvailableResourceMetricsResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "InternalServiceError"}, {"shape": "NotAuthorizedException"}], "documentation": "<p>Retrieve metrics of the specified types that can be queried for a specified DB instance. </p>"}, "ListPerformanceAnalysisReports": {"name": "ListPerformanceAnalysisReports", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListPerformanceAnalysisReportsRequest"}, "output": {"shape": "ListPerformanceAnalysisReportsResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "InternalServiceError"}, {"shape": "NotAuthorizedException"}], "documentation": "<p>Lists all the analysis reports created for the DB instance. The reports are sorted based on the start time of each report.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "InternalServiceError"}, {"shape": "NotAuthorizedException"}], "documentation": "<p>Retrieves all the metadata tags associated with Amazon RDS Performance Insights resource.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "InternalServiceError"}, {"shape": "NotAuthorizedException"}], "documentation": "<p>Adds metadata tags to the Amazon RDS Performance Insights resource.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "InternalServiceError"}, {"shape": "NotAuthorizedException"}], "documentation": "<p>Deletes the metadata tags from the Amazon RDS Performance Insights resource.</p>"}}, "shapes": {"AcceptLanguage": {"type": "string", "enum": ["EN_US"]}, "AdditionalMetricsList": {"type": "list", "member": {"shape": "RequestString"}, "max": 30, "min": 1}, "AdditionalMetricsMap": {"type": "map", "key": {"shape": "RequestString"}, "value": {"shape": "Double"}}, "AmazonResourceName": {"type": "string", "max": 1011, "min": 1, "pattern": "^arn:.*:pi:.*$"}, "AnalysisReport": {"type": "structure", "required": ["AnalysisReportId"], "members": {"AnalysisReportId": {"shape": "AnalysisReportId", "documentation": "<p>The name of the analysis report.</p>"}, "Identifier": {"shape": "IdentifierString", "documentation": "<p>The unique identifier of the analysis report.</p>"}, "ServiceType": {"shape": "ServiceType", "documentation": "<p>List the tags for the Amazon Web Services service for which Performance Insights returns metrics. Valid values are as follows:</p> <ul> <li> <p> <code>RDS</code> </p> </li> <li> <p> <code>DOCDB</code> </p> </li> </ul>"}, "CreateTime": {"shape": "ISOTimestamp", "documentation": "<p>The time you created the analysis report.</p>"}, "StartTime": {"shape": "ISOTimestamp", "documentation": "<p>The analysis start time in the report.</p>"}, "EndTime": {"shape": "ISOTimestamp", "documentation": "<p>The analysis end time in the report.</p>"}, "Status": {"shape": "AnalysisStatus", "documentation": "<p>The status of the created analysis report.</p>"}, "Insights": {"shape": "InsightList", "documentation": "<p>The list of identified insights in the analysis report.</p>"}}, "documentation": "<p>Retrieves the summary of the performance analysis report created for a time period.</p>"}, "AnalysisReportId": {"type": "string", "max": 100, "min": 1, "pattern": "report-[0-9a-f]{17}"}, "AnalysisReportSummary": {"type": "structure", "members": {"AnalysisReportId": {"shape": "String", "documentation": "<p>The name of the analysis report.</p>"}, "CreateTime": {"shape": "ISOTimestamp", "documentation": "<p>The time you created the analysis report.</p>"}, "StartTime": {"shape": "ISOTimestamp", "documentation": "<p>The start time of the analysis in the report.</p>"}, "EndTime": {"shape": "ISOTimestamp", "documentation": "<p>The end time of the analysis in the report.</p>"}, "Status": {"shape": "AnalysisStatus", "documentation": "<p>The status of the analysis report.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>List of all the tags added to the analysis report.</p>"}}, "documentation": "<p>Retrieves the details of the performance analysis report.</p>"}, "AnalysisReportSummaryList": {"type": "list", "member": {"shape": "AnalysisReportSummary"}}, "AnalysisStatus": {"type": "string", "enum": ["RUNNING", "SUCCEEDED", "FAILED"]}, "Boolean": {"type": "boolean"}, "ContextType": {"type": "string", "enum": ["CAUSAL", "CONTEXTUAL"]}, "CreatePerformanceAnalysisReportRequest": {"type": "structure", "required": ["ServiceType", "Identifier", "StartTime", "EndTime"], "members": {"ServiceType": {"shape": "ServiceType", "documentation": "<p>The Amazon Web Services service for which Performance Insights will return metrics. Valid value is <code>RDS</code>.</p>"}, "Identifier": {"shape": "IdentifierString", "documentation": "<p>An immutable, Amazon Web Services Region-unique identifier for a data source. Performance Insights gathers metrics from this data source.</p> <p>To use an Amazon RDS instance as a data source, you specify its <code>DbiResourceId</code> value. For example, specify <code>db-ADECBTYHKTSAUMUZQYPDS2GW4A</code>.</p>"}, "StartTime": {"shape": "ISOTimestamp", "documentation": "<p>The start time defined for the analysis report.</p>"}, "EndTime": {"shape": "ISOTimestamp", "documentation": "<p>The end time defined for the analysis report.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The metadata assigned to the analysis report consisting of a key-value pair.</p>"}}}, "CreatePerformanceAnalysisReportResponse": {"type": "structure", "members": {"AnalysisReportId": {"shape": "AnalysisReportId", "documentation": "<p>A unique identifier for the created analysis report.</p>"}}}, "Data": {"type": "structure", "members": {"PerformanceInsightsMetric": {"shape": "PerformanceInsightsMetric", "documentation": "<p>This field determines the Performance Insights metric to render for the insight. The <code>name</code> field refers to a Performance Insights metric. </p>"}}, "documentation": "<p>List of data objects which provide details about source metrics. This field can be used to determine the PI metric to render for the insight. This data type also includes static values for the metrics for the Insight that were calculated and included in text and annotations on the DB load chart.</p>"}, "DataList": {"type": "list", "member": {"shape": "Data"}}, "DataPoint": {"type": "structure", "required": ["Timestamp", "Value"], "members": {"Timestamp": {"shape": "ISOTimestamp", "documentation": "<p>The time, in epoch format, associated with a particular <code>Value</code>.</p>"}, "Value": {"shape": "Double", "documentation": "<p>The actual value associated with a particular <code>Timestamp</code>.</p>"}}, "documentation": "<p>A timestamp, and a single numerical value, which together represent a measurement at a particular point in time.</p>"}, "DataPointsList": {"type": "list", "member": {"shape": "DataPoint"}}, "DeletePerformanceAnalysisReportRequest": {"type": "structure", "required": ["ServiceType", "Identifier", "AnalysisReportId"], "members": {"ServiceType": {"shape": "ServiceType", "documentation": "<p>The Amazon Web Services service for which Performance Insights will return metrics. Valid value is <code>RDS</code>.</p>"}, "Identifier": {"shape": "IdentifierString", "documentation": "<p>An immutable identifier for a data source that is unique for an Amazon Web Services Region. Performance Insights gathers metrics from this data source. In the console, the identifier is shown as <i>ResourceID</i>. When you call <code>DescribeDBInstances</code>, the identifier is returned as <code>DbiResourceId</code>.</p> <p>To use a DB instance as a data source, specify its <code>DbiResourceId</code> value. For example, specify <code>db-ABCDEFGHIJKLMNOPQRSTU1VW2X</code>.</p>"}, "AnalysisReportId": {"shape": "AnalysisReportId", "documentation": "<p>The unique identifier of the analysis report for deletion.</p>"}}}, "DeletePerformanceAnalysisReportResponse": {"type": "structure", "members": {}}, "DescribeDimensionKeysRequest": {"type": "structure", "required": ["ServiceType", "Identifier", "StartTime", "EndTime", "Metric", "GroupBy"], "members": {"ServiceType": {"shape": "ServiceType", "documentation": "<p>The Amazon Web Services service for which Performance Insights will return metrics. Valid values are as follows:</p> <ul> <li> <p> <code>RDS</code> </p> </li> <li> <p> <code>DOCDB</code> </p> </li> </ul>"}, "Identifier": {"shape": "IdentifierString", "documentation": "<p>An immutable, Amazon Web Services Region-unique identifier for a data source. Performance Insights gathers metrics from this data source.</p> <p>To use an Amazon RDS instance as a data source, you specify its <code>DbiResourceId</code> value. For example, specify <code>db-FAIHNTYBKTGAUSUZQYPDS2GW4A</code>. </p>"}, "StartTime": {"shape": "ISOTimestamp", "documentation": "<p>The date and time specifying the beginning of the requested time series data. You must specify a <code>StartTime</code> within the past 7 days. The value specified is <i>inclusive</i>, which means that data points equal to or greater than <code>StartTime</code> are returned. </p> <p>The value for <code>StartTime</code> must be earlier than the value for <code>EndTime</code>. </p>"}, "EndTime": {"shape": "ISOTimestamp", "documentation": "<p>The date and time specifying the end of the requested time series data. The value specified is <i>exclusive</i>, which means that data points less than (but not equal to) <code>EndTime</code> are returned.</p> <p>The value for <code>EndTime</code> must be later than the value for <code>StartTime</code>.</p>"}, "Metric": {"shape": "RequestString", "documentation": "<p>The name of a Performance Insights metric to be measured.</p> <p>Valid values for <code>Metric</code> are:</p> <ul> <li> <p> <code>db.load.avg</code> - A scaled representation of the number of active sessions for the database engine. </p> </li> <li> <p> <code>db.sampledload.avg</code> - The raw number of active sessions for the database engine. </p> </li> </ul> <p>If the number of active sessions is less than an internal Performance Insights threshold, <code>db.load.avg</code> and <code>db.sampledload.avg</code> are the same value. If the number of active sessions is greater than the internal threshold, Performance Insights samples the active sessions, with <code>db.load.avg</code> showing the scaled values, <code>db.sampledload.avg</code> showing the raw values, and <code>db.sampledload.avg</code> less than <code>db.load.avg</code>. For most use cases, you can query <code>db.load.avg</code> only. </p>"}, "PeriodInSeconds": {"shape": "Integer", "documentation": "<p>The granularity, in seconds, of the data points returned from Performance Insights. A period can be as short as one second, or as long as one day (86400 seconds). Valid values are: </p> <ul> <li> <p> <code>1</code> (one second)</p> </li> <li> <p> <code>60</code> (one minute)</p> </li> <li> <p> <code>300</code> (five minutes)</p> </li> <li> <p> <code>3600</code> (one hour)</p> </li> <li> <p> <code>86400</code> (twenty-four hours)</p> </li> </ul> <p>If you don't specify <code>PeriodInSeconds</code>, then Performance Insights chooses a value for you, with a goal of returning roughly 100-200 data points in the response. </p>"}, "GroupBy": {"shape": "DimensionGroup", "documentation": "<p>A specification for how to aggregate the data points from a query result. You must specify a valid dimension group. Performance Insights returns all dimensions within this group, unless you provide the names of specific dimensions within this group. You can also request that Performance Insights return a limited number of values for a dimension. </p>"}, "AdditionalMetrics": {"shape": "AdditionalMetricsList", "documentation": "<p>Additional metrics for the top <code>N</code> dimension keys. If the specified dimension group in the <code>GroupBy</code> parameter is <code>db.sql_tokenized</code>, you can specify per-SQL metrics to get the values for the top <code>N</code> SQL digests. The response syntax is as follows: <code>\"AdditionalMetrics\" : { \"<i>string</i>\" : \"<i>string</i>\" }</code>. </p>"}, "PartitionBy": {"shape": "DimensionGroup", "documentation": "<p>For each dimension specified in <code>GroupBy</code>, specify a secondary dimension to further subdivide the partition keys in the response. </p>"}, "Filter": {"shape": "MetricQueryFilterMap", "documentation": "<p>One or more filters to apply in the request. Restrictions:</p> <ul> <li> <p>Any number of filters by the same dimension, as specified in the <code>GroupBy</code> or <code>Partition</code> parameters.</p> </li> <li> <p>A single filter for any other dimension in this dimension group.</p> </li> </ul>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of items to return in the response. If more items exist than the specified <code>MaxRecords</code> value, a pagination token is included in the response so that the remaining results can be retrieved. </p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the token, up to the value specified by <code>MaxRecords</code>.</p>"}}}, "DescribeDimensionKeysResponse": {"type": "structure", "members": {"AlignedStartTime": {"shape": "ISOTimestamp", "documentation": "<p>The start time for the returned dimension keys, after alignment to a granular boundary (as specified by <code>PeriodInSeconds</code>). <code>AlignedStartTime</code> will be less than or equal to the value of the user-specified <code>StartTime</code>. </p>"}, "AlignedEndTime": {"shape": "ISOTimestamp", "documentation": "<p>The end time for the returned dimension keys, after alignment to a granular boundary (as specified by <code>PeriodInSeconds</code>). <code>AlignedEndTime</code> will be greater than or equal to the value of the user-specified <code>Endtime</code>. </p>"}, "PartitionKeys": {"shape": "ResponsePartitionKeyList", "documentation": "<p>If <code>PartitionBy</code> was present in the request, <code>PartitionKeys</code> contains the breakdown of dimension keys by the specified partitions. </p>"}, "Keys": {"shape": "DimensionKeyDescriptionList", "documentation": "<p>The dimension keys that were requested.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A pagination token that indicates the response didn’t return all available records because <code>MaxRecords</code> was specified in the previous request. To get the remaining records, specify <code>NextToken</code> in a separate request with this value. </p>"}}}, "Description": {"type": "string", "max": 2048, "min": 1}, "DescriptiveMap": {"type": "map", "key": {"shape": "DescriptiveString"}, "value": {"shape": "DescriptiveString"}}, "DescriptiveString": {"type": "string", "max": 2000, "min": 1, "pattern": "^.*$"}, "DetailStatus": {"type": "string", "enum": ["AVAILABLE", "PROCESSING", "UNAVAILABLE"]}, "DimensionDetail": {"type": "structure", "members": {"Identifier": {"shape": "String", "documentation": "<p>The identifier of a dimension.</p>"}}, "documentation": "<p>The information about a dimension.</p>"}, "DimensionDetailList": {"type": "list", "member": {"shape": "DimensionDetail"}}, "DimensionGroup": {"type": "structure", "required": ["Group"], "members": {"Group": {"shape": "RequestString", "documentation": "<p>The name of the dimension group. Valid values are as follows:</p> <ul> <li> <p> <code>db</code> - The name of the database to which the client is connected. The following values are permitted:</p> <ul> <li> <p>Aurora PostgreSQL</p> </li> <li> <p>Amazon RDS PostgreSQL</p> </li> <li> <p>Aurora MySQL</p> </li> <li> <p>Amazon RDS MySQL</p> </li> <li> <p>Amazon RDS MariaDB</p> </li> <li> <p>Amazon DocumentDB</p> </li> </ul> </li> <li> <p> <code>db.application</code> - The name of the application that is connected to the database. The following values are permitted:</p> <ul> <li> <p>Aurora PostgreSQL</p> </li> <li> <p>Amazon RDS PostgreSQL</p> </li> <li> <p>Amazon DocumentDB</p> </li> </ul> </li> <li> <p> <code>db.host</code> - The host name of the connected client (all engines).</p> </li> <li> <p> <code>db.query</code> - The query that is currently running (only Amazon DocumentDB).</p> </li> <li> <p> <code>db.query_tokenized</code> - The digest query (only Amazon DocumentDB).</p> </li> <li> <p> <code>db.session_type</code> - The type of the current session (only Aurora PostgreSQL and RDS PostgreSQL).</p> </li> <li> <p> <code>db.sql</code> - The text of the SQL statement that is currently running (all engines except Amazon DocumentDB).</p> </li> <li> <p> <code>db.sql_tokenized</code> - The SQL digest (all engines except Amazon DocumentDB).</p> </li> <li> <p> <code>db.user</code> - The user logged in to the database (all engines except Amazon DocumentDB).</p> </li> <li> <p> <code>db.wait_event</code> - The event for which the database backend is waiting (all engines except Amazon DocumentDB).</p> </li> <li> <p> <code>db.wait_event_type</code> - The type of event for which the database backend is waiting (all engines except Amazon DocumentDB).</p> </li> <li> <p> <code>db.wait_state</code> - The event for which the database backend is waiting (only Amazon DocumentDB).</p> </li> </ul>"}, "Dimensions": {"shape": "RequestStringList", "documentation": "<p>A list of specific dimensions from a dimension group. If this parameter is not present, then it signifies that all of the dimensions in the group were requested, or are present in the response.</p> <p>Valid values for elements in the <code>Dimensions</code> array are:</p> <ul> <li> <p> <code>db.application.name</code> - The name of the application that is connected to the database. Valid values are as follows: </p> <ul> <li> <p>Aurora PostgreSQL</p> </li> <li> <p>Amazon RDS PostgreSQL</p> </li> <li> <p>Amazon DocumentDB</p> </li> </ul> </li> <li> <p> <code>db.host.id</code> - The host ID of the connected client (all engines).</p> </li> <li> <p> <code>db.host.name</code> - The host name of the connected client (all engines).</p> </li> <li> <p> <code>db.name</code> - The name of the database to which the client is connected. Valid values are as follows:</p> <ul> <li> <p>Aurora PostgreSQL</p> </li> <li> <p>Amazon RDS PostgreSQL</p> </li> <li> <p>Aurora MySQL</p> </li> <li> <p>Amazon RDS MySQL</p> </li> <li> <p>Amazon RDS MariaDB</p> </li> <li> <p>Amazon DocumentDB</p> </li> </ul> </li> <li> <p> <code>db.query.id</code> - The query ID generated by Performance Insights (only Amazon DocumentDB).</p> </li> <li> <p> <code>db.query.db_id</code> - The query ID generated by the database (only Amazon DocumentDB).</p> </li> <li> <p> <code>db.query.statement</code> - The text of the query that is being run (only Amazon DocumentDB).</p> </li> <li> <p> <code>db.query.tokenized_id</code> </p> </li> <li> <p> <code>db.query.tokenized.id</code> - The query digest ID generated by Performance Insights (only Amazon DocumentDB).</p> </li> <li> <p> <code>db.query.tokenized.db_id</code> - The query digest ID generated by Performance Insights (only Amazon DocumentDB).</p> </li> <li> <p> <code>db.query.tokenized.statement</code> - The text of the query digest (only Amazon DocumentDB).</p> </li> <li> <p> <code>db.session_type.name</code> - The type of the current session (only Amazon DocumentDB).</p> </li> <li> <p> <code>db.sql.id</code> - The hash of the full, non-tokenized SQL statement generated by Performance Insights (all engines except Amazon DocumentDB).</p> </li> <li> <p> <code>db.sql.db_id</code> - Either the SQL ID generated by the database engine, or a value generated by Performance Insights that begins with <code>pi-</code> (all engines except Amazon DocumentDB).</p> </li> <li> <p> <code>db.sql.statement</code> - The full text of the SQL statement that is running, as in <code>SELECT * FROM employees</code> (all engines except Amazon DocumentDB)</p> </li> <li> <p> <code>db.sql.tokenized_id</code> </p> </li> <li> <p> <code>db.sql_tokenized.id</code> - The hash of the SQL digest generated by Performance Insights (all engines except Amazon DocumentDB). In the console, <code>db.sql_tokenized.id</code> is called the Support ID because Amazon Web Services Support can look at this data to help you troubleshoot database issues.</p> </li> <li> <p> <code>db.sql_tokenized.db_id</code> - Either the native database ID used to refer to the SQL statement, or a synthetic ID such as <code>pi-2372568224</code> that Performance Insights generates if the native database ID isn't available (all engines except Amazon DocumentDB).</p> </li> <li> <p> <code>db.sql_tokenized.statement</code> - The text of the SQL digest, as in <code>SELECT * FROM employees WHERE employee_id = ?</code> (all engines except Amazon DocumentDB)</p> </li> <li> <p> <code>db.user.id</code> - The ID of the user logged in to the database (all engines except Amazon DocumentDB).</p> </li> <li> <p> <code>db.user.name</code> - The name of the user logged in to the database (all engines except Amazon DocumentDB).</p> </li> <li> <p> <code>db.wait_event.name</code> - The event for which the backend is waiting (all engines except Amazon DocumentDB).</p> </li> <li> <p> <code>db.wait_event.type</code> - The type of event for which the backend is waiting (all engines except Amazon DocumentDB).</p> </li> <li> <p> <code>db.wait_event_type.name</code> - The name of the event type for which the backend is waiting (all engines except Amazon DocumentDB).</p> </li> <li> <p> <code>db.wait_state.name</code> - The event for which the backend is waiting (only Amazon DocumentDB).</p> </li> </ul>"}, "Limit": {"shape": "Limit", "documentation": "<p>The maximum number of items to fetch for this dimension group.</p>"}}, "documentation": "<p>A logical grouping of Performance Insights metrics for a related subject area. For example, the <code>db.sql</code> dimension group consists of the following dimensions:</p> <ul> <li> <p> <code>db.sql.id</code> - The hash of a running SQL statement, generated by Performance Insights.</p> </li> <li> <p> <code>db.sql.db_id</code> - Either the SQL ID generated by the database engine, or a value generated by Performance Insights that begins with <code>pi-</code>.</p> </li> <li> <p> <code>db.sql.statement</code> - The full text of the SQL statement that is running, for example, <code>SELECT * FROM employees</code>.</p> </li> <li> <p> <code>db.sql_tokenized.id</code> - The hash of the SQL digest generated by Performance Insights.</p> </li> </ul> <note> <p>Each response element returns a maximum of 500 bytes. For larger elements, such as SQL statements, only the first 500 bytes are returned.</p> </note>"}, "DimensionGroupDetail": {"type": "structure", "members": {"Group": {"shape": "String", "documentation": "<p>The name of the dimension group.</p>"}, "Dimensions": {"shape": "DimensionDetailList", "documentation": "<p>The dimensions within a dimension group.</p>"}}, "documentation": "<p>Information about dimensions within a dimension group.</p>"}, "DimensionGroupDetailList": {"type": "list", "member": {"shape": "DimensionGroupDetail"}}, "DimensionKeyDescription": {"type": "structure", "members": {"Dimensions": {"shape": "DimensionMap", "documentation": "<p>A map of name-value pairs for the dimensions in the group.</p>"}, "Total": {"shape": "Double", "documentation": "<p>The aggregated metric value for the dimensions, over the requested time range.</p>"}, "AdditionalMetrics": {"shape": "AdditionalMetricsMap", "documentation": "<p>A map that contains the value for each additional metric.</p>"}, "Partitions": {"shape": "MetricValuesList", "documentation": "<p>If <code>PartitionBy</code> was specified, <code>PartitionKeys</code> contains the dimensions that were.</p>"}}, "documentation": "<p>An object that includes the requested dimension key values and aggregated metric values within a dimension group.</p>"}, "DimensionKeyDescriptionList": {"type": "list", "member": {"shape": "DimensionKeyDescription"}}, "DimensionKeyDetail": {"type": "structure", "members": {"Value": {"shape": "String", "documentation": "<p>The value of the dimension detail data. Depending on the return status, this value is either the full or truncated SQL query for the following dimensions:</p> <ul> <li> <p> <code>db.query.statement</code> (Amazon DocumentDB)</p> </li> <li> <p> <code>db.sql.statement</code> (Amazon RDS and Aurora)</p> </li> </ul>"}, "Dimension": {"shape": "String", "documentation": "<p>The full name of the dimension. The full name includes the group name and key name. The following values are valid:</p> <ul> <li> <p> <code>db.query.statement</code> (Amazon DocumentDB)</p> </li> <li> <p> <code>db.sql.statement</code> (Amazon RDS and Aurora)</p> </li> </ul>"}, "Status": {"shape": "DetailStatus", "documentation": "<p>The status of the dimension detail data. Possible values include the following:</p> <ul> <li> <p> <code>AVAILABLE</code> - The dimension detail data is ready to be retrieved.</p> </li> <li> <p> <code>PROCESSING</code> - The dimension detail data isn't ready to be retrieved because more processing time is required. If the requested detail data has the status <code>PROCESSING</code>, Performance Insights returns the truncated query.</p> </li> <li> <p> <code>UNAVAILABLE</code> - The dimension detail data could not be collected successfully.</p> </li> </ul>"}}, "documentation": "<p>An object that describes the details for a specified dimension.</p>"}, "DimensionKeyDetailList": {"type": "list", "member": {"shape": "DimensionKeyDetail"}}, "DimensionMap": {"type": "map", "key": {"shape": "RequestString"}, "value": {"shape": "RequestString"}}, "DimensionsMetricList": {"type": "list", "member": {"shape": "RequestString"}, "max": 5, "min": 1}, "Double": {"type": "double"}, "ErrorString": {"type": "string"}, "FeatureMetadata": {"type": "structure", "members": {"Status": {"shape": "FeatureStatus", "documentation": "<p>The status of the feature on the DB instance. Possible values include the following:</p> <ul> <li> <p> <code>ENABLED</code> - The feature is enabled on the instance.</p> </li> <li> <p> <code>DISABLED</code> - The feature is disabled on the instance.</p> </li> <li> <p> <code>UNSUPPORTED</code> - The feature isn't supported on the instance.</p> </li> <li> <p> <code>ENABLED_PENDING_REBOOT</code> - The feature is enabled on the instance but requires a reboot to take effect.</p> </li> <li> <p> <code>DISABLED_PENDING_REBOOT</code> - The feature is disabled on the instance but requires a reboot to take effect.</p> </li> <li> <p> <code>UNKNOWN</code> - The feature status couldn't be determined.</p> </li> </ul>"}}, "documentation": "<p>The metadata for a feature. For example, the metadata might indicate that a feature is turned on or off on a specific DB instance.</p>"}, "FeatureMetadataMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "FeatureMetadata"}}, "FeatureStatus": {"type": "string", "enum": ["ENABLED", "DISABLED", "UNSUPPORTED", "ENABLED_PENDING_REBOOT", "DISABLED_PENDING_REBOOT", "UNKNOWN"]}, "GetDimensionKeyDetailsRequest": {"type": "structure", "required": ["ServiceType", "Identifier", "Group", "GroupIdentifier"], "members": {"ServiceType": {"shape": "ServiceType", "documentation": "<p>The Amazon Web Services service for which Performance Insights returns data. The only valid value is <code>RDS</code>.</p>"}, "Identifier": {"shape": "IdentifierString", "documentation": "<p>The ID for a data source from which to gather dimension data. This ID must be immutable and unique within an Amazon Web Services Region. When a DB instance is the data source, specify its <code>DbiResourceId</code> value. For example, specify <code>db-ABCDEFGHIJKLMNOPQRSTU1VW2X</code>. </p>"}, "Group": {"shape": "RequestString", "documentation": "<p>The name of the dimension group. Performance Insights searches the specified group for the dimension group ID. The following group name values are valid:</p> <ul> <li> <p> <code>db.query</code> (Amazon DocumentDB only)</p> </li> <li> <p> <code>db.sql</code> (Amazon RDS and Aurora only)</p> </li> </ul>"}, "GroupIdentifier": {"shape": "RequestString", "documentation": "<p>The ID of the dimension group from which to retrieve dimension details. For dimension group <code>db.sql</code>, the group ID is <code>db.sql.id</code>. The following group ID values are valid:</p> <ul> <li> <p> <code>db.sql.id</code> for dimension group <code>db.sql</code> (Aurora and RDS only)</p> </li> <li> <p> <code>db.query.id</code> for dimension group <code>db.query</code> (DocumentDB only)</p> </li> </ul>"}, "RequestedDimensions": {"shape": "RequestedDimensionList", "documentation": "<p>A list of dimensions to retrieve the detail data for within the given dimension group. If you don't specify this parameter, Performance Insights returns all dimension data within the specified dimension group. Specify dimension names for the following dimension groups:</p> <ul> <li> <p> <code>db.sql</code> - Specify either the full dimension name <code>db.sql.statement</code> or the short dimension name <code>statement</code> (Aurora and RDS only).</p> </li> <li> <p> <code>db.query</code> - Specify either the full dimension name <code>db.query.statement</code> or the short dimension name <code>statement</code> (DocumentDB only).</p> </li> </ul>"}}}, "GetDimensionKeyDetailsResponse": {"type": "structure", "members": {"Dimensions": {"shape": "DimensionKeyDetailList", "documentation": "<p>The details for the requested dimensions.</p>"}}}, "GetPerformanceAnalysisReportRequest": {"type": "structure", "required": ["ServiceType", "Identifier", "AnalysisReportId"], "members": {"ServiceType": {"shape": "ServiceType", "documentation": "<p>The Amazon Web Services service for which Performance Insights will return metrics. Valid value is <code>RDS</code>.</p>"}, "Identifier": {"shape": "IdentifierString", "documentation": "<p>An immutable identifier for a data source that is unique for an Amazon Web Services Region. Performance Insights gathers metrics from this data source. In the console, the identifier is shown as <i>ResourceID</i>. When you call <code>DescribeDBInstances</code>, the identifier is returned as <code>DbiResourceId</code>.</p> <p>To use a DB instance as a data source, specify its <code>DbiResourceId</code> value. For example, specify <code>db-ABCDEFGHIJKLMNOPQRSTU1VW2X</code>.</p>"}, "AnalysisReportId": {"shape": "AnalysisReportId", "documentation": "<p>A unique identifier of the created analysis report. For example, <code>report-12345678901234567</code> </p>"}, "TextFormat": {"shape": "TextFormat", "documentation": "<p>Indicates the text format in the report. The options are <code>PLAIN_TEXT</code> or <code>MARKDOWN</code>. The default value is <code>plain text</code>.</p>"}, "AcceptLanguage": {"shape": "AcceptLanguage", "documentation": "<p>The text language in the report. The default language is <code>EN_US</code> (English). </p>"}}}, "GetPerformanceAnalysisReportResponse": {"type": "structure", "members": {"AnalysisReport": {"shape": "AnalysisReport", "documentation": "<p>The summary of the performance analysis report created for a time period.</p>"}}}, "GetResourceMetadataRequest": {"type": "structure", "required": ["ServiceType", "Identifier"], "members": {"ServiceType": {"shape": "ServiceType", "documentation": "<p>The Amazon Web Services service for which Performance Insights returns metrics.</p>"}, "Identifier": {"shape": "IdentifierString", "documentation": "<p>An immutable identifier for a data source that is unique for an Amazon Web Services Region. Performance Insights gathers metrics from this data source. To use a DB instance as a data source, specify its <code>DbiResourceId</code> value. For example, specify <code>db-ABCDEFGHIJKLMNOPQRSTU1VW2X</code>. </p>"}}}, "GetResourceMetadataResponse": {"type": "structure", "members": {"Identifier": {"shape": "String", "documentation": "<p>An immutable identifier for a data source that is unique for an Amazon Web Services Region. Performance Insights gathers metrics from this data source. To use a DB instance as a data source, specify its <code>DbiResourceId</code> value. For example, specify <code>db-ABCDEFGHIJKLMNOPQRSTU1VW2X</code>. </p>"}, "Features": {"shape": "FeatureMetadataMap", "documentation": "<p>The metadata for different features. For example, the metadata might indicate that a feature is turned on or off on a specific DB instance.</p>"}}}, "GetResourceMetricsRequest": {"type": "structure", "required": ["ServiceType", "Identifier", "MetricQueries", "StartTime", "EndTime"], "members": {"ServiceType": {"shape": "ServiceType", "documentation": "<p>The Amazon Web Services service for which Performance Insights returns metrics. Valid values are as follows:</p> <ul> <li> <p> <code>RDS</code> </p> </li> <li> <p> <code>DOCDB</code> </p> </li> </ul>"}, "Identifier": {"shape": "IdentifierString", "documentation": "<p>An immutable identifier for a data source that is unique for an Amazon Web Services Region. Performance Insights gathers metrics from this data source. In the console, the identifier is shown as <i>ResourceID</i>. When you call <code>DescribeDBInstances</code>, the identifier is returned as <code>DbiResourceId</code>.</p> <p>To use a DB instance as a data source, specify its <code>DbiResourceId</code> value. For example, specify <code>db-ABCDEFGHIJKLMNOPQRSTU1VW2X</code>.</p>"}, "MetricQueries": {"shape": "MetricQueryList", "documentation": "<p>An array of one or more queries to perform. Each query must specify a Performance Insights metric, and can optionally specify aggregation and filtering criteria.</p>"}, "StartTime": {"shape": "ISOTimestamp", "documentation": "<p>The date and time specifying the beginning of the requested time series query range. You can't specify a <code>StartTime</code> that is earlier than 7 days ago. By default, Performance Insights has 7 days of retention, but you can extend this range up to 2 years. The value specified is <i>inclusive</i>. Thus, the command returns data points equal to or greater than <code>StartTime</code>.</p> <p>The value for <code>StartTime</code> must be earlier than the value for <code>EndTime</code>.</p>"}, "EndTime": {"shape": "ISOTimestamp", "documentation": "<p>The date and time specifying the end of the requested time series query range. The value specified is <i>exclusive</i>. Thus, the command returns data points less than (but not equal to) <code>EndTime</code>.</p> <p>The value for <code>EndTime</code> must be later than the value for <code>StartTime</code>.</p>"}, "PeriodInSeconds": {"shape": "Integer", "documentation": "<p>The granularity, in seconds, of the data points returned from Performance Insights. A period can be as short as one second, or as long as one day (86400 seconds). Valid values are:</p> <ul> <li> <p> <code>1</code> (one second)</p> </li> <li> <p> <code>60</code> (one minute)</p> </li> <li> <p> <code>300</code> (five minutes)</p> </li> <li> <p> <code>3600</code> (one hour)</p> </li> <li> <p> <code>86400</code> (twenty-four hours)</p> </li> </ul> <p>If you don't specify <code>PeriodInSeconds</code>, then Performance Insights will choose a value for you, with a goal of returning roughly 100-200 data points in the response.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of items to return in the response. If more items exist than the specified <code>MaxRecords</code> value, a pagination token is included in the response so that the remaining results can be retrieved. </p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the token, up to the value specified by <code>MaxRecords</code>.</p>"}, "PeriodAlignment": {"shape": "PeriodAlignment", "documentation": "<p>The returned timestamp which is the start or end time of the time periods. The default value is <code>END_TIME</code>.</p>"}}}, "GetResourceMetricsResponse": {"type": "structure", "members": {"AlignedStartTime": {"shape": "ISOTimestamp", "documentation": "<p>The start time for the returned metrics, after alignment to a granular boundary (as specified by <code>PeriodInSeconds</code>). <code>AlignedStartTime</code> will be less than or equal to the value of the user-specified <code>StartTime</code>.</p>"}, "AlignedEndTime": {"shape": "ISOTimestamp", "documentation": "<p>The end time for the returned metrics, after alignment to a granular boundary (as specified by <code>PeriodInSeconds</code>). <code>AlignedEndTime</code> will be greater than or equal to the value of the user-specified <code>Endtime</code>.</p>"}, "Identifier": {"shape": "String", "documentation": "<p>An immutable identifier for a data source that is unique for an Amazon Web Services Region. Performance Insights gathers metrics from this data source. In the console, the identifier is shown as <i>ResourceID</i>. When you call <code>DescribeDBInstances</code>, the identifier is returned as <code>DbiResourceId</code>.</p>"}, "MetricList": {"shape": "MetricKeyDataPointsList", "documentation": "<p>An array of metric results, where each array element contains all of the data points for a particular dimension.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the token, up to the value specified by <code>MaxRecords</code>. </p>"}}}, "ISOTimestamp": {"type": "timestamp"}, "IdentifierString": {"type": "string", "max": 256, "min": 0, "pattern": "^[a-zA-Z0-9-]+$"}, "Insight": {"type": "structure", "required": ["InsightId"], "members": {"InsightId": {"shape": "String", "documentation": "<p>The unique identifier for the insight. For example, <code>insight-12345678901234567</code>.</p>"}, "InsightType": {"shape": "String", "documentation": "<p>The type of insight. For example, <code>HighDBLoad</code>, <code>HighCPU</code>, or <code>DominatingSQLs</code>.</p>"}, "Context": {"shape": "ContextType", "documentation": "<p>Indicates if the insight is causal or correlated insight.</p>"}, "StartTime": {"shape": "ISOTimestamp", "documentation": "<p>The start time of the insight. For example, <code>2018-10-30T00:00:00Z</code>.</p>"}, "EndTime": {"shape": "ISOTimestamp", "documentation": "<p>The end time of the insight. For example, <code>2018-10-30T00:00:00Z</code>.</p>"}, "Severity": {"shape": "Severity", "documentation": "<p>The severity of the insight. The values are: <code>Low</code>, <code>Medium</code>, or <code>High</code>.</p>"}, "SupportingInsights": {"shape": "InsightList", "documentation": "<p>List of supporting insights that provide additional factors for the insight.</p>"}, "Description": {"shape": "MarkdownString", "documentation": "<p>Description of the insight. For example: <code>A high severity Insight found between 02:00 to 02:30, where there was an unusually high DB load 600x above baseline. Likely performance impact</code>.</p>"}, "Recommendations": {"shape": "RecommendationList", "documentation": "<p>List of recommendations for the insight. For example, <code>Investigate the following SQLs that contributed to 100% of the total DBLoad during that time period: sql-id</code>.</p>"}, "InsightData": {"shape": "DataList", "documentation": "<p>List of data objects containing metrics and references from the time range while generating the insight.</p>"}, "BaselineData": {"shape": "DataList", "documentation": "<p> Metric names and values from the timeframe used as baseline to generate the insight.</p>"}}, "documentation": "<p>Retrieves the list of performance issues which are identified.</p>"}, "InsightList": {"type": "list", "member": {"shape": "Insight"}}, "Integer": {"type": "integer"}, "InternalServiceError": {"type": "structure", "members": {"Message": {"shape": "ErrorString"}}, "documentation": "<p>The request failed due to an unknown error.</p>", "exception": true, "fault": true}, "InvalidArgumentException": {"type": "structure", "members": {"Message": {"shape": "ErrorString"}}, "documentation": "<p>One of the arguments provided is invalid for this request.</p>", "exception": true}, "Limit": {"type": "integer", "max": 25, "min": 1}, "ListAvailableResourceDimensionsRequest": {"type": "structure", "required": ["ServiceType", "Identifier", "Metrics"], "members": {"ServiceType": {"shape": "ServiceType", "documentation": "<p>The Amazon Web Services service for which Performance Insights returns metrics.</p>"}, "Identifier": {"shape": "IdentifierString", "documentation": "<p>An immutable identifier for a data source that is unique within an Amazon Web Services Region. Performance Insights gathers metrics from this data source. To use an Amazon RDS DB instance as a data source, specify its <code>DbiResourceId</code> value. For example, specify <code>db-ABCDEFGHIJKLMNOPQRSTU1VWZ</code>. </p>"}, "Metrics": {"shape": "DimensionsMetricList", "documentation": "<p>The types of metrics for which to retrieve dimensions. Valid values include <code>db.load</code>.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of items to return in the response. If more items exist than the specified <code>MaxRecords</code> value, a pagination token is included in the response so that the remaining results can be retrieved.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the token, up to the value specified by <code>MaxRecords</code>. </p>"}}}, "ListAvailableResourceDimensionsResponse": {"type": "structure", "members": {"MetricDimensions": {"shape": "MetricDimensionsList", "documentation": "<p>The dimension information returned for requested metric types.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the token, up to the value specified by <code>MaxRecords</code>.</p>"}}}, "ListAvailableResourceMetricsRequest": {"type": "structure", "required": ["ServiceType", "Identifier", "MetricTypes"], "members": {"ServiceType": {"shape": "ServiceType", "documentation": "<p>The Amazon Web Services service for which Performance Insights returns metrics.</p>"}, "Identifier": {"shape": "IdentifierString", "documentation": "<p>An immutable identifier for a data source that is unique within an Amazon Web Services Region. Performance Insights gathers metrics from this data source. To use an Amazon RDS DB instance as a data source, specify its <code>DbiResourceId</code> value. For example, specify <code>db-ABCDEFGHIJKLMNOPQRSTU1VWZ</code>. </p>"}, "MetricTypes": {"shape": "MetricTypeList", "documentation": "<p>The types of metrics to return in the response. Valid values in the array include the following:</p> <ul> <li> <p> <code>os</code> (OS counter metrics) - All engines</p> </li> <li> <p> <code>db</code> (DB load metrics) - All engines except for Amazon DocumentDB</p> </li> <li> <p> <code>db.sql.stats</code> (per-SQL metrics) - All engines except for Amazon DocumentDB</p> </li> <li> <p> <code>db.sql_tokenized.stats</code> (per-SQL digest metrics) - All engines except for Amazon DocumentDB</p> </li> </ul>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the token, up to the value specified by <code>MaxRecords</code>. </p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of items to return. If the <code>MaxRecords</code> value is less than the number of existing items, the response includes a pagination token. </p>"}}}, "ListAvailableResourceMetricsResponse": {"type": "structure", "members": {"Metrics": {"shape": "ResponseResourceMetricList", "documentation": "<p>An array of metrics available to query. Each array element contains the full name, description, and unit of the metric. </p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A pagination token that indicates the response didn’t return all available records because <code>MaxRecords</code> was specified in the previous request. To get the remaining records, specify <code>NextToken</code> in a separate request with this value. </p>"}}}, "ListPerformanceAnalysisReportsRequest": {"type": "structure", "required": ["ServiceType", "Identifier"], "members": {"ServiceType": {"shape": "ServiceType", "documentation": "<p>The Amazon Web Services service for which Performance Insights returns metrics. Valid value is <code>RDS</code>.</p>"}, "Identifier": {"shape": "IdentifierString", "documentation": "<p>An immutable identifier for a data source that is unique for an Amazon Web Services Region. Performance Insights gathers metrics from this data source. In the console, the identifier is shown as <i>ResourceID</i>. When you call <code>DescribeDBInstances</code>, the identifier is returned as <code>DbiResourceId</code>.</p> <p>To use a DB instance as a data source, specify its <code>DbiResourceId</code> value. For example, specify <code>db-ABCDEFGHIJKLMNOPQRSTU1VW2X</code>.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the token, up to the value specified by <code>MaxResults</code>.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of items to return in the response. If more items exist than the specified <code>MaxResults</code> value, a pagination token is included in the response so that the remaining results can be retrieved. </p>"}, "ListTags": {"shape": "Boolean", "documentation": "<p>Specifies whether or not to include the list of tags in the response.</p>"}}}, "ListPerformanceAnalysisReportsResponse": {"type": "structure", "members": {"AnalysisReports": {"shape": "AnalysisReportSummaryList", "documentation": "<p>List of reports including the report identifier, start and end time, creation time, and status.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the token, up to the value specified by <code>MaxResults</code>.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ServiceType", "ResourceARN"], "members": {"ServiceType": {"shape": "ServiceType", "documentation": "<p>List the tags for the Amazon Web Services service for which Performance Insights returns metrics. Valid value is <code>RDS</code>.</p>"}, "ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>Lists all the tags for the Amazon RDS Performance Insights resource. This value is an Amazon Resource Name (ARN). For information about creating an ARN, see <a href=\"https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_Tagging.ARN.html#USER_Tagging.ARN.Constructing\"> Constructing an RDS Amazon Resource Name (ARN)</a>.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "TagList", "documentation": "<p>The metadata assigned to an Amazon RDS resource consisting of a key-value pair.</p>"}}}, "MarkdownString": {"type": "string", "max": 8000, "min": 0, "pattern": "(.|\\n)*", "sensitive": true}, "MaxResults": {"type": "integer", "max": 25, "min": 0}, "MetricDimensionGroups": {"type": "structure", "members": {"Metric": {"shape": "String", "documentation": "<p>The metric type to which the dimension information belongs.</p>"}, "Groups": {"shape": "DimensionGroupDetailList", "documentation": "<p>The available dimension groups for a metric type.</p>"}}, "documentation": "<p>The available dimension information for a metric type.</p>"}, "MetricDimensionsList": {"type": "list", "member": {"shape": "MetricDimensionGroups"}}, "MetricKeyDataPoints": {"type": "structure", "members": {"Key": {"shape": "ResponseResourceMetricKey", "documentation": "<p>The dimensions to which the data points apply.</p>"}, "DataPoints": {"shape": "DataPointsList", "documentation": "<p>An array of timestamp-value pairs, representing measurements over a period of time.</p>"}}, "documentation": "<p>A time-ordered series of data points, corresponding to a dimension of a Performance Insights metric.</p>"}, "MetricKeyDataPointsList": {"type": "list", "member": {"shape": "MetricKeyDataPoints"}}, "MetricQuery": {"type": "structure", "required": ["Metric"], "members": {"Metric": {"shape": "RequestString", "documentation": "<p>The name of a Performance Insights metric to be measured.</p> <p>Valid values for <code>Metric</code> are:</p> <ul> <li> <p> <code>db.load.avg</code> - A scaled representation of the number of active sessions for the database engine.</p> </li> <li> <p> <code>db.sampledload.avg</code> - The raw number of active sessions for the database engine.</p> </li> <li> <p>The counter metrics listed in <a href=\"https://docs.aws.amazon.com/AmazonRDS/latest/AuroraUserGuide/USER_PerfInsights_Counters.html#USER_PerfInsights_Counters.OS\">Performance Insights operating system counters</a> in the <i>Amazon Aurora User Guide</i>.</p> </li> </ul> <p>If the number of active sessions is less than an internal Performance Insights threshold, <code>db.load.avg</code> and <code>db.sampledload.avg</code> are the same value. If the number of active sessions is greater than the internal threshold, Performance Insights samples the active sessions, with <code>db.load.avg</code> showing the scaled values, <code>db.sampledload.avg</code> showing the raw values, and <code>db.sampledload.avg</code> less than <code>db.load.avg</code>. For most use cases, you can query <code>db.load.avg</code> only.</p>"}, "GroupBy": {"shape": "DimensionGroup", "documentation": "<p>A specification for how to aggregate the data points from a query result. You must specify a valid dimension group. Performance Insights will return all of the dimensions within that group, unless you provide the names of specific dimensions within that group. You can also request that Performance Insights return a limited number of values for a dimension.</p>"}, "Filter": {"shape": "MetricQueryFilterMap", "documentation": "<p>One or more filters to apply in the request. Restrictions:</p> <ul> <li> <p>Any number of filters by the same dimension, as specified in the <code>GroupBy</code> parameter.</p> </li> <li> <p>A single filter for any other dimension in this dimension group.</p> </li> </ul>"}}, "documentation": "<p>A single query to be processed. You must provide the metric to query. If no other parameters are specified, Performance Insights returns all data points for the specified metric. Optionally, you can request that the data points be aggregated by dimension group (<code>GroupBy</code>), and return only those data points that match your criteria (<code>Filter</code>).</p>"}, "MetricQueryFilterMap": {"type": "map", "key": {"shape": "RequestString"}, "value": {"shape": "RequestString"}}, "MetricQueryList": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "max": 15, "min": 1}, "MetricTypeList": {"type": "list", "member": {"shape": "RequestString"}}, "MetricValuesList": {"type": "list", "member": {"shape": "Double"}}, "NextToken": {"type": "string", "max": 8192, "min": 1, "pattern": "^[a-zA-Z0-9_=-]+$"}, "NotAuthorizedException": {"type": "structure", "members": {"Message": {"shape": "ErrorString"}}, "documentation": "<p>The user is not authorized to perform this request.</p>", "exception": true}, "PerformanceInsightsMetric": {"type": "structure", "members": {"Metric": {"shape": "DescriptiveString", "documentation": "<p>The Performance Insights metric.</p>"}, "DisplayName": {"shape": "DescriptiveString", "documentation": "<p>The Performance Insights metric name.</p>"}, "Dimensions": {"shape": "DescriptiveMap", "documentation": "<p>A dimension map that contains the dimensions for this partition.</p>"}, "Value": {"shape": "Double", "documentation": "<p>The value of the metric. For example, <code>9</code> for <code>db.load.avg</code>.</p>"}}, "documentation": "<p>This data type helps to determine Performance Insights metric to render for the insight.</p>"}, "PeriodAlignment": {"type": "string", "enum": ["END_TIME", "START_TIME"]}, "Recommendation": {"type": "structure", "members": {"RecommendationId": {"shape": "String", "documentation": "<p>The unique identifier for the recommendation.</p>"}, "RecommendationDescription": {"shape": "MarkdownString", "documentation": "<p>The recommendation details to help resolve the performance issue. For example, <code>Investigate the following SQLs that contributed to 100% of the total DBLoad during that time period: sql-id</code> </p>"}}, "documentation": "<p>The list of recommendations for the insight.</p>"}, "RecommendationList": {"type": "list", "member": {"shape": "Recommendation"}}, "RequestString": {"type": "string", "max": 256, "min": 0, "pattern": ".*\\S.*"}, "RequestStringList": {"type": "list", "member": {"shape": "RequestString"}, "max": 10, "min": 1}, "RequestedDimensionList": {"type": "list", "member": {"shape": "RequestString"}, "max": 10, "min": 1}, "ResponsePartitionKey": {"type": "structure", "required": ["Dimensions"], "members": {"Dimensions": {"shape": "DimensionMap", "documentation": "<p>A dimension map that contains the dimensions for this partition.</p>"}}, "documentation": "<p>If <code>PartitionBy</code> was specified in a <code>DescribeDimensionKeys</code> request, the dimensions are returned in an array. Each element in the array specifies one dimension. </p>"}, "ResponsePartitionKeyList": {"type": "list", "member": {"shape": "ResponsePartitionKey"}}, "ResponseResourceMetric": {"type": "structure", "members": {"Metric": {"shape": "String", "documentation": "<p>The full name of the metric.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the metric.</p>"}, "Unit": {"shape": "String", "documentation": "<p>The unit of the metric.</p>"}}, "documentation": "<p>An object that contains the full name, description, and unit of a metric. </p>"}, "ResponseResourceMetricKey": {"type": "structure", "required": ["Metric"], "members": {"Metric": {"shape": "String", "documentation": "<p>The name of a Performance Insights metric to be measured.</p> <p>Valid values for <code>Metric</code> are:</p> <ul> <li> <p> <code>db.load.avg</code> - A scaled representation of the number of active sessions for the database engine.</p> </li> <li> <p> <code>db.sampledload.avg</code> - The raw number of active sessions for the database engine.</p> </li> <li> <p>The counter metrics listed in <a href=\"https://docs.aws.amazon.com/AmazonRDS/latest/AuroraUserGuide/USER_PerfInsights_Counters.html#USER_PerfInsights_Counters.OS\">Performance Insights operating system counters</a> in the <i>Amazon Aurora User Guide</i>.</p> </li> </ul> <p>If the number of active sessions is less than an internal Performance Insights threshold, <code>db.load.avg</code> and <code>db.sampledload.avg</code> are the same value. If the number of active sessions is greater than the internal threshold, Performance Insights samples the active sessions, with <code>db.load.avg</code> showing the scaled values, <code>db.sampledload.avg</code> showing the raw values, and <code>db.sampledload.avg</code> less than <code>db.load.avg</code>. For most use cases, you can query <code>db.load.avg</code> only. </p>"}, "Dimensions": {"shape": "DimensionMap", "documentation": "<p>The valid dimensions for the metric.</p>"}}, "documentation": "<p>An object describing a Performance Insights metric and one or more dimensions for that metric.</p>"}, "ResponseResourceMetricList": {"type": "list", "member": {"shape": "ResponseResourceMetric"}}, "ServiceType": {"type": "string", "enum": ["RDS", "DOCDB"]}, "Severity": {"type": "string", "enum": ["LOW", "MEDIUM", "HIGH"]}, "String": {"type": "string", "max": 256, "min": 0, "pattern": ".*\\S.*"}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>A key is the required name of the tag. The string value can be from 1 to 128 Unicode characters in length and can't be prefixed with <code>aws:</code> or <code>rds:</code>. The string can only contain only the set of Unicode letters, digits, white-space, '_', '.', ':', '/', '=', '+', '-', '@' (Java regex: <code>\"^([\\\\p{L}\\\\p{Z}\\\\p{N}_.:/=+\\\\-@]*)$\"</code>).</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>A value is the optional value of the tag. The string value can be from 1 to 256 Unicode characters in length and can't be prefixed with <code>aws:</code> or <code>rds:</code>. The string can only contain only the set of Unicode letters, digits, white-space, '_', '.', ':', '/', '=', '+', '-', '@' (Java regex: \"^([\\\\p{L}\\\\p{Z}\\\\p{N}_.:/=+\\\\-@]*)$\").</p>"}}, "documentation": "<p>Metadata assigned to an Amazon RDS resource consisting of a key-value pair.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^.*$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 0}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "max": 200, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["ServiceType", "ResourceARN", "Tags"], "members": {"ServiceType": {"shape": "ServiceType", "documentation": "<p>The Amazon Web Services service for which Performance Insights returns metrics. Valid value is <code>RDS</code>.</p>"}, "ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon RDS Performance Insights resource that the tags are added to. This value is an Amazon Resource Name (ARN). For information about creating an ARN, see <a href=\"https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_Tagging.ARN.html#USER_Tagging.ARN.Constructing\"> Constructing an RDS Amazon Resource Name (ARN)</a>.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The metadata assigned to an Amazon RDS resource consisting of a key-value pair.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": "^.*$"}, "TextFormat": {"type": "string", "enum": ["PLAIN_TEXT", "MARKDOWN"]}, "UntagResourceRequest": {"type": "structure", "required": ["ServiceType", "ResourceARN", "TagKeys"], "members": {"ServiceType": {"shape": "ServiceType", "documentation": "<p>List the tags for the Amazon Web Services service for which Performance Insights returns metrics. Valid value is <code>RDS</code>.</p>"}, "ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon RDS Performance Insights resource that the tags are added to. This value is an Amazon Resource Name (ARN). For information about creating an ARN, see <a href=\"https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_Tagging.ARN.html#USER_Tagging.ARN.Constructing\"> Constructing an RDS Amazon Resource Name (ARN)</a>.</p>"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>The metadata assigned to an Amazon RDS Performance Insights resource consisting of a key-value pair.</p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}}, "documentation": "<fullname>Amazon RDS Performance Insights</fullname> <p>Amazon RDS Performance Insights enables you to monitor and explore different dimensions of database load based on data captured from a running DB instance. The guide provides detailed information about Performance Insights data types, parameters and errors.</p> <p>When Performance Insights is enabled, the Amazon RDS Performance Insights API provides visibility into the performance of your DB instance. Amazon CloudWatch provides the authoritative source for Amazon Web Services service-vended monitoring metrics. Performance Insights offers a domain-specific view of DB load.</p> <p>DB load is measured as average active sessions. Performance Insights provides the data to API consumers as a two-dimensional time-series dataset. The time dimension provides DB load data for each time point in the queried time range. Each time point decomposes overall load in relation to the requested dimensions, measured at that time point. Examples include SQL, Wait event, User, and Host.</p> <ul> <li> <p>To learn more about Performance Insights and Amazon Aurora DB instances, go to the <i> <a href=\"https://docs.aws.amazon.com/AmazonRDS/latest/AuroraUserGuide/USER_PerfInsights.html\"> Amazon Aurora User Guide</a> </i>. </p> </li> <li> <p>To learn more about Performance Insights and Amazon RDS DB instances, go to the <i> <a href=\"https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_PerfInsights.html\"> Amazon RDS User Guide</a> </i>. </p> </li> <li> <p>To learn more about Performance Insights and Amazon DocumentDB clusters, go to the <i> <a href=\"https://docs.aws.amazon.com/documentdb/latest/developerguide/performance-insights.html\"> Amazon DocumentDB Developer Guide</a> </i>.</p> </li> </ul>"}