{"metadata": {"apiVersion": "2014-09-30", "endpointPrefix": "elasticache", "serviceFullName": "Amazon ElastiCache", "serviceId": "ElastiCache", "signatureVersion": "v4", "xmlNamespace": "http://elasticache.amazonaws.com/doc/2014-09-30/", "protocol": "query"}, "documentation": "<fullname>Amazon ElastiCache</fullname> <p>Amazon ElastiCache is a web service that makes it easier to set up, operate, and scale a distributed cache in the cloud.</p> <p>With ElastiCache, customers gain all of the benefits of a high-performance, in-memory cache with far less of the administrative burden of launching and managing a distributed cache. The service makes setup, scaling, and cluster failure handling much simpler than in a self-managed cache deployment.</p> <p>In addition, through integration with Amazon CloudWatch, customers get enhanced visibility into the key performance statistics associated with their cache and can receive alarms if a part of their cache runs hot.</p>", "operations": {"AuthorizeCacheSecurityGroupIngress": {"name": "AuthorizeCacheSecurityGroupIngress", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AuthorizeCacheSecurityGroupIngressMessage", "documentation": "<p>Represents the input of an <i>AuthorizeCacheSecurityGroupIngress</i> operation.</p>"}, "output": {"shape": "AuthorizeCacheSecurityGroupIngressResult", "wrapper": true, "documentation": "<p>Represents the output of one of the following operations:</p> <ul> <li> <i>AuthorizeCacheSecurityGroupIngress</i> </li> <li> <i>CreateCacheSecurityGroup</i> </li> <li> <i>RevokeCacheSecurityGroupIngress</i> </li> </ul>", "resultWrapper": "AuthorizeCacheSecurityGroupIngressResult"}, "errors": [{"shape": "CacheSecurityGroupNotFoundFault", "error": {"code": "CacheSecurityGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The requested cache security group name does not refer to an existing cache security group.</p>"}, {"shape": "InvalidCacheSecurityGroupStateFault", "error": {"code": "InvalidCacheSecurityGroupState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The current state of the cache security group does not allow deletion.</p>"}, {"shape": "AuthorizationAlreadyExistsFault", "error": {"code": "AuthorizationAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The specified Amazon EC2 security group is already authorized for the specified cache security group.</p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>AuthorizeCacheSecurityGroupIngress</i> operation allows network ingress to a cache security group. Applications using ElastiCache must be running on Amazon EC2, and Amazon EC2 security groups are used as the authorization mechanism.</p>"}, "CopySnapshot": {"name": "CopySnapshot", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CopySnapshotMessage", "documentation": "<p>Represents the input of a <i>CopySnapshotMessage</i> operation.</p>"}, "output": {"shape": "CopySnapshotResult", "wrapper": true, "documentation": "<p>Represents a copy of an entire cache cluster as of the time when the snapshot was taken.</p>", "resultWrapper": "CopySnapshotResult"}, "errors": [{"shape": "SnapshotAlreadyExistsFault", "error": {"code": "SnapshotAlreadyExistsFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>You already have a snapshot with the given name.</p>"}, {"shape": "SnapshotNotFoundFault", "error": {"code": "SnapshotNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The requested snapshot name does not refer to an existing snapshot.</p>"}, {"shape": "SnapshotQuotaExceededFault", "error": {"code": "SnapshotQuotaExceededFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the maximum number of snapshots.</p>"}, {"shape": "InvalidSnapshotStateFault", "error": {"code": "InvalidSnapshotState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The current state of the snapshot does not allow the requested action to occur.</p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>CopySnapshot</i> operation makes a copy of an existing snapshot.</p>"}, "CreateCacheCluster": {"name": "CreateCacheCluster", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateCacheClusterMessage", "documentation": "<p>Represents the input of a <i>CreateCacheCluster</i> operation.</p>"}, "output": {"shape": "CreateCacheClusterResult", "wrapper": true, "documentation": "<p>Contains all of the attributes of a specific cache cluster.</p>", "resultWrapper": "CreateCacheClusterResult"}, "errors": [{"shape": "ReplicationGroupNotFoundFault", "error": {"code": "ReplicationGroupNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The specified replication group does not exist.</p>"}, {"shape": "InvalidReplicationGroupStateFault", "error": {"code": "InvalidReplicationGroupState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested replication group is not in the <i>available</i> state.</p>"}, {"shape": "CacheClusterAlreadyExistsFault", "error": {"code": "CacheClusterAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>You already have a cache cluster with the given identifier.</p>"}, {"shape": "InsufficientCacheClusterCapacityFault", "error": {"code": "InsufficientCacheClusterCapacity", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache node type is not available in the specified Availability Zone.</p>"}, {"shape": "CacheSecurityGroupNotFoundFault", "error": {"code": "CacheSecurityGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The requested cache security group name does not refer to an existing cache security group.</p>"}, {"shape": "CacheSubnetGroupNotFoundFault", "error": {"code": "CacheSubnetGroupNotFoundFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache subnet group name does not refer to an existing cache subnet group.</p>"}, {"shape": "ClusterQuotaForCustomerExceededFault", "error": {"code": "ClusterQuotaForCustomerExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the allowed number of cache clusters per customer.</p>"}, {"shape": "NodeQuotaForClusterExceededFault", "error": {"code": "NodeQuotaForClusterExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the allowed number of cache nodes in a single cache cluster.</p>"}, {"shape": "NodeQuotaForCustomerExceededFault", "error": {"code": "NodeQuotaForCustomerExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the allowed number of cache nodes per customer. </p>"}, {"shape": "CacheParameterGroupNotFoundFault", "error": {"code": "CacheParameterGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The requested cache parameter group name does not refer to an existing cache parameter group.</p>"}, {"shape": "InvalidVPCNetworkStateFault", "error": {"code": "InvalidVPCNetworkStateFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The VPC network is in an invalid state.</p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>CreateCacheCluster</i> operation creates a cache cluster. All nodes in the cache cluster run the same protocol-compliant cache engine software, either Memcached or Redis.</p>"}, "CreateCacheParameterGroup": {"name": "CreateCacheParameterGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateCacheParameterGroupMessage", "documentation": "<p>Represents the input of a <i>CreateCacheParameterGroup</i> operation.</p>"}, "output": {"shape": "CreateCacheParameterGroupResult", "wrapper": true, "documentation": "<p>Represents the output of a <i>CreateCacheParameterGroup</i> operation.</p>", "resultWrapper": "CreateCacheParameterGroupResult"}, "errors": [{"shape": "CacheParameterGroupQuotaExceededFault", "error": {"code": "CacheParameterGroupQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the maximum number of cache security groups.</p>"}, {"shape": "CacheParameterGroupAlreadyExistsFault", "error": {"code": "CacheParameterGroupAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>A cache parameter group with the requested name already exists.</p>"}, {"shape": "InvalidCacheParameterGroupStateFault", "error": {"code": "InvalidCacheParameterGroupState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The current state of the cache parameter group does not allow the requested action to occur. </p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>CreateCacheParameterGroup</i> operation creates a new cache parameter group. A cache parameter group is a collection of parameters that you apply to all of the nodes in a cache cluster.</p>"}, "CreateCacheSecurityGroup": {"name": "CreateCacheSecurityGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateCacheSecurityGroupMessage", "documentation": "<p>Represents the input of a <i>CreateCacheSecurityGroup</i> operation.</p>"}, "output": {"shape": "CreateCacheSecurityGroupResult", "wrapper": true, "documentation": "<p>Represents the output of one of the following operations:</p> <ul> <li> <i>AuthorizeCacheSecurityGroupIngress</i> </li> <li> <i>CreateCacheSecurityGroup</i> </li> <li> <i>RevokeCacheSecurityGroupIngress</i> </li> </ul>", "resultWrapper": "CreateCacheSecurityGroupResult"}, "errors": [{"shape": "CacheSecurityGroupAlreadyExistsFault", "error": {"code": "CacheSecurityGroupAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>A cache security group with the specified name already exists.</p>"}, {"shape": "CacheSecurityGroupQuotaExceededFault", "error": {"code": "QuotaExceeded.CacheSecurityGroup", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the allowed number of cache security groups.</p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>CreateCacheSecurityGroup</i> operation creates a new cache security group. Use a cache security group to control access to one or more cache clusters.</p> <p>Cache security groups are only used when you are creating a cache cluster outside of an Amazon Virtual Private Cloud (VPC). If you are creating a cache cluster inside of a VPC, use a cache subnet group instead. For more information, see <a href=\"http://docs.aws.amazon.com/AmazonElastiCache/latest/APIReference/API_CreateCacheSubnetGroup.html\">CreateCacheSubnetGroup</a>.</p>"}, "CreateCacheSubnetGroup": {"name": "CreateCacheSubnetGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateCacheSubnetGroupMessage", "documentation": "<p>Represents the input of a <i>CreateCacheSubnetGroup</i> operation.</p>"}, "output": {"shape": "CreateCacheSubnetGroupResult", "wrapper": true, "documentation": "<p>Represents the output of one of the following operations:</p> <ul> <li> <i>CreateCacheSubnetGroup</i> </li> <li> <i>ModifyCacheSubnetGroup</i> </li> </ul>", "resultWrapper": "CreateCacheSubnetGroupResult"}, "errors": [{"shape": "CacheSubnetGroupAlreadyExistsFault", "error": {"code": "CacheSubnetGroupAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The requested cache subnet group name is already in use by an existing cache subnet group.</p>"}, {"shape": "CacheSubnetGroupQuotaExceededFault", "error": {"code": "CacheSubnetGroupQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the allowed number of cache subnet groups.</p>"}, {"shape": "CacheSubnetQuotaExceededFault", "error": {"code": "CacheSubnetQuotaExceededFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the allowed number of subnets in a cache subnet group.</p>"}, {"shape": "InvalidSubnet", "error": {"code": "InvalidSubnet", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>An invalid subnet identifier was specified.</p>"}], "documentation": "<p>The <i>CreateCacheSubnetGroup</i> operation creates a new cache subnet group.</p> <p>Use this parameter only when you are creating a cluster in an Amazon Virtual Private Cloud (VPC).</p>"}, "CreateReplicationGroup": {"name": "CreateReplicationGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateReplicationGroupMessage", "documentation": "<p>Represents the input of a <i>CreateReplicationGroup</i> operation.</p>"}, "output": {"shape": "CreateReplicationGroupResult", "wrapper": true, "documentation": "<p>Contains all of the attributes of a specific replication group.</p>", "resultWrapper": "CreateReplicationGroupResult"}, "errors": [{"shape": "CacheClusterNotFoundFault", "error": {"code": "CacheClusterNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache cluster ID does not refer to an existing cache cluster.</p>"}, {"shape": "InvalidCacheClusterStateFault", "error": {"code": "InvalidCacheClusterState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache cluster is not in the <i>available</i> state.</p>"}, {"shape": "ReplicationGroupAlreadyExistsFault", "error": {"code": "ReplicationGroupAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The specified replication group already exists.</p>"}, {"shape": "InsufficientCacheClusterCapacityFault", "error": {"code": "InsufficientCacheClusterCapacity", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache node type is not available in the specified Availability Zone.</p>"}, {"shape": "CacheSecurityGroupNotFoundFault", "error": {"code": "CacheSecurityGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The requested cache security group name does not refer to an existing cache security group.</p>"}, {"shape": "CacheSubnetGroupNotFoundFault", "error": {"code": "CacheSubnetGroupNotFoundFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache subnet group name does not refer to an existing cache subnet group.</p>"}, {"shape": "ClusterQuotaForCustomerExceededFault", "error": {"code": "ClusterQuotaForCustomerExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the allowed number of cache clusters per customer.</p>"}, {"shape": "NodeQuotaForClusterExceededFault", "error": {"code": "NodeQuotaForClusterExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the allowed number of cache nodes in a single cache cluster.</p>"}, {"shape": "NodeQuotaForCustomerExceededFault", "error": {"code": "NodeQuotaForCustomerExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the allowed number of cache nodes per customer. </p>"}, {"shape": "CacheParameterGroupNotFoundFault", "error": {"code": "CacheParameterGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The requested cache parameter group name does not refer to an existing cache parameter group.</p>"}, {"shape": "InvalidVPCNetworkStateFault", "error": {"code": "InvalidVPCNetworkStateFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The VPC network is in an invalid state.</p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>CreateReplicationGroup</i> operation creates a replication group. A replication group is a collection of cache clusters, where one of the cache clusters is a read/write primary and the others are read-only replicas. Writes to the primary are automatically propagated to the replicas.</p> <p>When you create a replication group, you must specify an existing cache cluster that is in the primary role. When the replication group has been successfully created, you can add one or more read replica replicas to it, up to a total of five read replicas.</p> <p><b>Note:</b> This action is valid only for Redis.</p>"}, "CreateSnapshot": {"name": "CreateSnapshot", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateSnapshotMessage", "documentation": "<p>Represents the input of a <i>CreateSnapshot</i> operation.</p>"}, "output": {"shape": "CreateSnapshotResult", "wrapper": true, "documentation": "<p>Represents a copy of an entire cache cluster as of the time when the snapshot was taken.</p>", "resultWrapper": "CreateSnapshotResult"}, "errors": [{"shape": "SnapshotAlreadyExistsFault", "error": {"code": "SnapshotAlreadyExistsFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>You already have a snapshot with the given name.</p>"}, {"shape": "CacheClusterNotFoundFault", "error": {"code": "CacheClusterNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache cluster ID does not refer to an existing cache cluster.</p>"}, {"shape": "InvalidCacheClusterStateFault", "error": {"code": "InvalidCacheClusterState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache cluster is not in the <i>available</i> state.</p>"}, {"shape": "SnapshotQuotaExceededFault", "error": {"code": "SnapshotQuotaExceededFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the maximum number of snapshots.</p>"}, {"shape": "SnapshotFeatureNotSupportedFault", "error": {"code": "SnapshotFeatureNotSupportedFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>You attempted one of the following actions:</p> <ul> <li> <p>Creating a snapshot of a Redis cache cluster running on a <i>t1.micro</i> cache node.</p> </li> <li> <p>Creating a snapshot of a cache cluster that is running Memcached rather than Redis.</p> </li> </ul> <p>Neither of these are supported by ElastiCache.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}], "documentation": "<p>The <i>CreateSnapshot</i> operation creates a copy of an entire cache cluster at a specific moment in time.</p>"}, "DeleteCacheCluster": {"name": "DeleteCacheCluster", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteCacheClusterMessage", "documentation": "<p>Represents the input of a <i>DeleteCacheCluster</i> operation.</p>"}, "output": {"shape": "DeleteCacheClusterResult", "wrapper": true, "documentation": "<p>Contains all of the attributes of a specific cache cluster.</p>", "resultWrapper": "DeleteCacheClusterResult"}, "errors": [{"shape": "CacheClusterNotFoundFault", "error": {"code": "CacheClusterNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache cluster ID does not refer to an existing cache cluster.</p>"}, {"shape": "InvalidCacheClusterStateFault", "error": {"code": "InvalidCacheClusterState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache cluster is not in the <i>available</i> state.</p>"}, {"shape": "SnapshotAlreadyExistsFault", "error": {"code": "SnapshotAlreadyExistsFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>You already have a snapshot with the given name.</p>"}, {"shape": "SnapshotFeatureNotSupportedFault", "error": {"code": "SnapshotFeatureNotSupportedFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>You attempted one of the following actions:</p> <ul> <li> <p>Creating a snapshot of a Redis cache cluster running on a <i>t1.micro</i> cache node.</p> </li> <li> <p>Creating a snapshot of a cache cluster that is running Memcached rather than Redis.</p> </li> </ul> <p>Neither of these are supported by ElastiCache.</p>"}, {"shape": "SnapshotQuotaExceededFault", "error": {"code": "SnapshotQuotaExceededFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the maximum number of snapshots.</p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>DeleteCacheCluster</i> operation deletes a previously provisioned cache cluster. <i>DeleteCacheCluster</i> deletes all associated cache nodes, node endpoints and the cache cluster itself. When you receive a successful response from this operation, Amazon ElastiCache immediately begins deleting the cache cluster; you cannot cancel or revert this operation.</p> <p>This API cannot be used to delete a cache cluster that is the last read replica of a replication group that has automatic failover mode enabled.</p>"}, "DeleteCacheParameterGroup": {"name": "DeleteCacheParameterGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteCacheParameterGroupMessage", "documentation": "<p>Represents the input of a <i>DeleteCacheParameterGroup</i> operation.</p>"}, "errors": [{"shape": "InvalidCacheParameterGroupStateFault", "error": {"code": "InvalidCacheParameterGroupState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The current state of the cache parameter group does not allow the requested action to occur. </p>"}, {"shape": "CacheParameterGroupNotFoundFault", "error": {"code": "CacheParameterGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The requested cache parameter group name does not refer to an existing cache parameter group.</p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>DeleteCacheParameterGroup</i> operation deletes the specified cache parameter group. You cannot delete a cache parameter group if it is associated with any cache clusters.</p>"}, "DeleteCacheSecurityGroup": {"name": "DeleteCacheSecurityGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteCacheSecurityGroupMessage", "documentation": "<p>Represents the input of a <i>DeleteCacheSecurityGroup</i> operation.</p>"}, "errors": [{"shape": "InvalidCacheSecurityGroupStateFault", "error": {"code": "InvalidCacheSecurityGroupState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The current state of the cache security group does not allow deletion.</p>"}, {"shape": "CacheSecurityGroupNotFoundFault", "error": {"code": "CacheSecurityGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The requested cache security group name does not refer to an existing cache security group.</p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>DeleteCacheSecurityGroup</i> operation deletes a cache security group.</p>"}, "DeleteCacheSubnetGroup": {"name": "DeleteCacheSubnetGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteCacheSubnetGroupMessage", "documentation": "<p>Represents the input of a <i>DeleteCacheSubnetGroup</i> operation.</p>"}, "errors": [{"shape": "CacheSubnetGroupInUse", "error": {"code": "CacheSubnetGroupInUse", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache subnet group is currently in use.</p>"}, {"shape": "CacheSubnetGroupNotFoundFault", "error": {"code": "CacheSubnetGroupNotFoundFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache subnet group name does not refer to an existing cache subnet group.</p>"}], "documentation": "<p>The <i>DeleteCacheSubnetGroup</i> operation deletes a cache subnet group.</p>"}, "DeleteReplicationGroup": {"name": "DeleteReplicationGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteReplicationGroupMessage", "documentation": "<p>Represents the input of a <i>DeleteReplicationGroup</i> operation.</p>"}, "output": {"shape": "DeleteReplicationGroupResult", "wrapper": true, "documentation": "<p>Contains all of the attributes of a specific replication group.</p>", "resultWrapper": "DeleteReplicationGroupResult"}, "errors": [{"shape": "ReplicationGroupNotFoundFault", "error": {"code": "ReplicationGroupNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The specified replication group does not exist.</p>"}, {"shape": "InvalidReplicationGroupStateFault", "error": {"code": "InvalidReplicationGroupState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested replication group is not in the <i>available</i> state.</p>"}, {"shape": "SnapshotAlreadyExistsFault", "error": {"code": "SnapshotAlreadyExistsFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>You already have a snapshot with the given name.</p>"}, {"shape": "SnapshotFeatureNotSupportedFault", "error": {"code": "SnapshotFeatureNotSupportedFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>You attempted one of the following actions:</p> <ul> <li> <p>Creating a snapshot of a Redis cache cluster running on a <i>t1.micro</i> cache node.</p> </li> <li> <p>Creating a snapshot of a cache cluster that is running Memcached rather than Redis.</p> </li> </ul> <p>Neither of these are supported by ElastiCache.</p>"}, {"shape": "SnapshotQuotaExceededFault", "error": {"code": "SnapshotQuotaExceededFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the maximum number of snapshots.</p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>DeleteReplicationGroup</i> operation deletes an existing cluster. By default, this operation deletes the entire cluster, including the primary node group and all of the read replicas. You can optionally delete only the read replicas, while retaining the primary node group.</p> <p>When you receive a successful response from this operation, Amazon ElastiCache immediately begins deleting the selected resources; you cannot cancel or revert this operation.</p>"}, "DeleteSnapshot": {"name": "DeleteSnapshot", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteSnapshotMessage", "documentation": "<p>Represents the input of a <i>DeleteSnapshot</i> operation.</p>"}, "output": {"shape": "DeleteSnapshotResult", "wrapper": true, "documentation": "<p>Represents a copy of an entire cache cluster as of the time when the snapshot was taken.</p>", "resultWrapper": "DeleteSnapshotResult"}, "errors": [{"shape": "SnapshotNotFoundFault", "error": {"code": "SnapshotNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The requested snapshot name does not refer to an existing snapshot.</p>"}, {"shape": "InvalidSnapshotStateFault", "error": {"code": "InvalidSnapshotState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The current state of the snapshot does not allow the requested action to occur.</p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>DeleteSnapshot</i> operation deletes an existing snapshot. When you receive a successful response from this operation, ElastiCache immediately begins deleting the snapshot; you cannot cancel or revert this operation.</p>"}, "DescribeCacheClusters": {"name": "DescribeCacheClusters", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeCacheClustersMessage", "documentation": "<p>Represents the input of a <i>DescribeCacheClusters</i> operation.</p>"}, "output": {"shape": "CacheClusterMessage", "documentation": "<p>Represents the output of a <i>DescribeCacheClusters</i> operation.</p>", "resultWrapper": "DescribeCacheClustersResult"}, "errors": [{"shape": "CacheClusterNotFoundFault", "error": {"code": "CacheClusterNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache cluster ID does not refer to an existing cache cluster.</p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>DescribeCacheClusters</i> operation returns information about all provisioned cache clusters if no cache cluster identifier is specified, or about a specific cache cluster if a cache cluster identifier is supplied.</p> <p>By default, abbreviated information about the cache clusters(s) will be returned. You can use the optional <i>ShowDetails</i> flag to retrieve detailed information about the cache nodes associated with the cache clusters. These details include the DNS address and port for the cache node endpoint.</p> <p>If the cluster is in the CREATING state, only cluster level information will be displayed until all of the nodes are successfully provisioned.</p> <p>If the cluster is in the DELETING state, only cluster level information will be displayed.</p> <p>If cache nodes are currently being added to the cache cluster, node endpoint information and creation time for the additional nodes will not be displayed until they are completely provisioned. When the cache cluster state is <i>available</i>, the cluster is ready for use.</p> <p>If cache nodes are currently being removed from the cache cluster, no endpoint information for the removed nodes is displayed.</p>"}, "DescribeCacheEngineVersions": {"name": "DescribeCacheEngineVersions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeCacheEngineVersionsMessage", "documentation": "<p>Represents the input of a <i>DescribeCacheEngineVersions</i> operation.</p>"}, "output": {"shape": "CacheEngineVersionMessage", "documentation": "<p>Represents the output of a <a>DescribeCacheEngineVersions</a> operation.</p>", "resultWrapper": "DescribeCacheEngineVersionsResult"}, "documentation": "<p>The <i>DescribeCacheEngineVersions</i> operation returns a list of the available cache engines and their versions.</p>"}, "DescribeCacheParameterGroups": {"name": "DescribeCacheParameterGroups", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeCacheParameterGroupsMessage", "documentation": "<p>Represents the input of a <i>DescribeCacheParameterGroups</i> operation.</p>"}, "output": {"shape": "CacheParameterGroupsMessage", "documentation": "<p>Represents the output of a <i>DescribeCacheParameterGroups</i> operation.</p>", "resultWrapper": "DescribeCacheParameterGroupsResult"}, "errors": [{"shape": "CacheParameterGroupNotFoundFault", "error": {"code": "CacheParameterGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The requested cache parameter group name does not refer to an existing cache parameter group.</p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>DescribeCacheParameterGroups</i> operation returns a list of cache parameter group descriptions. If a cache parameter group name is specified, the list will contain only the descriptions for that group.</p>"}, "DescribeCacheParameters": {"name": "DescribeCacheParameters", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeCacheParametersMessage", "documentation": "<p>Represents the input of a <i>DescribeCacheParameters</i> operation.</p>"}, "output": {"shape": "CacheParameterGroupDetails", "documentation": "<p>Represents the output of a <i>DescribeCacheParameters</i> operation.</p>", "resultWrapper": "DescribeCacheParametersResult"}, "errors": [{"shape": "CacheParameterGroupNotFoundFault", "error": {"code": "CacheParameterGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The requested cache parameter group name does not refer to an existing cache parameter group.</p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>DescribeCacheParameters</i> operation returns the detailed parameter list for a particular cache parameter group.</p>"}, "DescribeCacheSecurityGroups": {"name": "DescribeCacheSecurityGroups", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeCacheSecurityGroupsMessage", "documentation": "<p>Represents the input of a <i>DescribeCacheSecurityGroups</i> operation.</p>"}, "output": {"shape": "CacheSecurityGroupMessage", "documentation": "<p>Represents the output of a <i>DescribeCacheSecurityGroups</i> operation.</p>", "resultWrapper": "DescribeCacheSecurityGroupsResult"}, "errors": [{"shape": "CacheSecurityGroupNotFoundFault", "error": {"code": "CacheSecurityGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The requested cache security group name does not refer to an existing cache security group.</p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>DescribeCacheSecurityGroups</i> operation returns a list of cache security group descriptions. If a cache security group name is specified, the list will contain only the description of that group.</p>"}, "DescribeCacheSubnetGroups": {"name": "DescribeCacheSubnetGroups", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeCacheSubnetGroupsMessage", "documentation": "<p>Represents the input of a <i>DescribeCacheSubnetGroups</i> operation.</p>"}, "output": {"shape": "CacheSubnetGroupMessage", "documentation": "<p>Represents the output of a <i>DescribeCacheSubnetGroups</i> operation.</p>", "resultWrapper": "DescribeCacheSubnetGroupsResult"}, "errors": [{"shape": "CacheSubnetGroupNotFoundFault", "error": {"code": "CacheSubnetGroupNotFoundFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache subnet group name does not refer to an existing cache subnet group.</p>"}], "documentation": "<p>The <i>DescribeCacheSubnetGroups</i> operation returns a list of cache subnet group descriptions. If a subnet group name is specified, the list will contain only the description of that group.</p>"}, "DescribeEngineDefaultParameters": {"name": "DescribeEngineDefaultParameters", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeEngineDefaultParametersMessage", "documentation": "<p>Represents the input of a <i>DescribeEngineDefaultParameters</i> operation.</p>"}, "output": {"shape": "DescribeEngineDefaultParametersResult", "wrapper": true, "documentation": "<p>Represents the output of a <i>DescribeEngineDefaultParameters</i> operation.</p>", "resultWrapper": "DescribeEngineDefaultParametersResult"}, "errors": [{"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>DescribeEngineDefaultParameters</i> operation returns the default engine and system parameter information for the specified cache engine.</p>"}, "DescribeEvents": {"name": "DescribeEvents", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeEventsMessage", "documentation": "<p>Represents the input of a <i>DescribeEvents</i> operation.</p>"}, "output": {"shape": "EventsMessage", "documentation": "<p>Represents the output of a <i>DescribeEvents</i> operation.</p>", "resultWrapper": "DescribeEventsResult"}, "errors": [{"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>DescribeEvents</i> operation returns events related to cache clusters, cache security groups, and cache parameter groups. You can obtain events specific to a particular cache cluster, cache security group, or cache parameter group by providing the name as a parameter.</p> <p>By default, only the events occurring within the last hour are returned; however, you can retrieve up to 14 days' worth of events if necessary.</p>"}, "DescribeReplicationGroups": {"name": "DescribeReplicationGroups", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeReplicationGroupsMessage", "documentation": "<p>Represents the input of a <i>DescribeReplicationGroups</i> operation.</p>"}, "output": {"shape": "ReplicationGroupMessage", "documentation": "<p>Represents the output of a <i>DescribeReplicationGroups</i> operation.</p>", "resultWrapper": "DescribeReplicationGroupsResult"}, "errors": [{"shape": "ReplicationGroupNotFoundFault", "error": {"code": "ReplicationGroupNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The specified replication group does not exist.</p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>DescribeReplicationGroups</i> operation returns information about a particular replication group. If no identifier is specified, <i>DescribeReplicationGroups</i> returns information about all replication groups.</p>"}, "DescribeReservedCacheNodes": {"name": "DescribeReservedCacheNodes", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeReservedCacheNodesMessage", "documentation": "<p>Represents the input of a <i>DescribeReservedCacheNodes</i> operation.</p>"}, "output": {"shape": "ReservedCacheNodeMessage", "documentation": "<p>Represents the output of a <i>DescribeReservedCacheNodes</i> operation.</p>", "resultWrapper": "DescribeReservedCacheNodesResult"}, "errors": [{"shape": "ReservedCacheNodeNotFoundFault", "error": {"code": "ReservedCacheNodeNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The requested reserved cache node was not found.</p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>DescribeReservedCacheNodes</i> operation returns information about reserved cache nodes for this account, or about a specified reserved cache node.</p>"}, "DescribeReservedCacheNodesOfferings": {"name": "DescribeReservedCacheNodesOfferings", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeReservedCacheNodesOfferingsMessage", "documentation": "<p>Represents the input of a <i>DescribeReservedCacheNodesOfferings</i> operation.</p>"}, "output": {"shape": "ReservedCacheNodesOfferingMessage", "documentation": "<p>Represents the output of a <i>DescribeReservedCacheNodesOfferings</i> operation.</p>", "resultWrapper": "DescribeReservedCacheNodesOfferingsResult"}, "errors": [{"shape": "ReservedCacheNodesOfferingNotFoundFault", "error": {"code": "ReservedCacheNodesOfferingNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache node offering does not exist.</p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>DescribeReservedCacheNodesOfferings</i> operation lists available reserved cache node offerings.</p>"}, "DescribeSnapshots": {"name": "DescribeSnapshots", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeSnapshotsMessage", "documentation": "<p>Represents the input of a <i>DescribeSnapshotsMessage</i> operation.</p>"}, "output": {"shape": "DescribeSnapshotsListMessage", "documentation": "<p>Represents the output of a <i>DescribeSnapshots</i> operation.</p>", "resultWrapper": "DescribeSnapshotsResult"}, "errors": [{"shape": "CacheClusterNotFoundFault", "error": {"code": "CacheClusterNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache cluster ID does not refer to an existing cache cluster.</p>"}, {"shape": "SnapshotNotFoundFault", "error": {"code": "SnapshotNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The requested snapshot name does not refer to an existing snapshot.</p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>DescribeSnapshots</i> operation returns information about cache cluster snapshots. By default, <i>DescribeSnapshots</i> lists all of your snapshots; it can optionally describe a single snapshot, or just the snapshots associated with a particular cache cluster.</p>"}, "ModifyCacheCluster": {"name": "ModifyCacheCluster", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ModifyCacheClusterMessage", "documentation": "<p>Represents the input of a <i>ModifyCacheCluster</i> operation.</p>"}, "output": {"shape": "ModifyCacheClusterResult", "wrapper": true, "documentation": "<p>Contains all of the attributes of a specific cache cluster.</p>", "resultWrapper": "ModifyCacheClusterResult"}, "errors": [{"shape": "InvalidCacheClusterStateFault", "error": {"code": "InvalidCacheClusterState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache cluster is not in the <i>available</i> state.</p>"}, {"shape": "InvalidCacheSecurityGroupStateFault", "error": {"code": "InvalidCacheSecurityGroupState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The current state of the cache security group does not allow deletion.</p>"}, {"shape": "InsufficientCacheClusterCapacityFault", "error": {"code": "InsufficientCacheClusterCapacity", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache node type is not available in the specified Availability Zone.</p>"}, {"shape": "CacheClusterNotFoundFault", "error": {"code": "CacheClusterNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache cluster ID does not refer to an existing cache cluster.</p>"}, {"shape": "NodeQuotaForClusterExceededFault", "error": {"code": "NodeQuotaForClusterExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the allowed number of cache nodes in a single cache cluster.</p>"}, {"shape": "NodeQuotaForCustomerExceededFault", "error": {"code": "NodeQuotaForCustomerExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the allowed number of cache nodes per customer. </p>"}, {"shape": "CacheSecurityGroupNotFoundFault", "error": {"code": "CacheSecurityGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The requested cache security group name does not refer to an existing cache security group.</p>"}, {"shape": "CacheParameterGroupNotFoundFault", "error": {"code": "CacheParameterGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The requested cache parameter group name does not refer to an existing cache parameter group.</p>"}, {"shape": "InvalidVPCNetworkStateFault", "error": {"code": "InvalidVPCNetworkStateFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The VPC network is in an invalid state.</p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>ModifyCacheCluster</i> operation modifies the settings for a cache cluster. You can use this operation to change one or more cluster configuration parameters by specifying the parameters and the new values.</p>"}, "ModifyCacheParameterGroup": {"name": "ModifyCacheParameterGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ModifyCacheParameterGroupMessage", "documentation": "<p>Represents the input of a <i>ModifyCacheParameterGroup</i> operation.</p>"}, "output": {"shape": "CacheParameterGroupNameMessage", "documentation": "<p>Represents the output of one of the following operations:</p> <ul> <li> <i>ModifyCacheParameterGroup</i> </li> <li> <i>ResetCacheParameterGroup</i> </li> </ul>", "resultWrapper": "ModifyCacheParameterGroupResult"}, "errors": [{"shape": "CacheParameterGroupNotFoundFault", "error": {"code": "CacheParameterGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The requested cache parameter group name does not refer to an existing cache parameter group.</p>"}, {"shape": "InvalidCacheParameterGroupStateFault", "error": {"code": "InvalidCacheParameterGroupState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The current state of the cache parameter group does not allow the requested action to occur. </p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>ModifyCacheParameterGroup</i> operation modifies the parameters of a cache parameter group. You can modify up to 20 parameters in a single request by submitting a list parameter name and value pairs.</p>"}, "ModifyCacheSubnetGroup": {"name": "ModifyCacheSubnetGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ModifyCacheSubnetGroupMessage", "documentation": "<p>Represents the input of a <i>ModifyCacheSubnetGroup</i> operation.</p>"}, "output": {"shape": "ModifyCacheSubnetGroupResult", "wrapper": true, "documentation": "<p>Represents the output of one of the following operations:</p> <ul> <li> <i>CreateCacheSubnetGroup</i> </li> <li> <i>ModifyCacheSubnetGroup</i> </li> </ul>", "resultWrapper": "ModifyCacheSubnetGroupResult"}, "errors": [{"shape": "CacheSubnetGroupNotFoundFault", "error": {"code": "CacheSubnetGroupNotFoundFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache subnet group name does not refer to an existing cache subnet group.</p>"}, {"shape": "CacheSubnetQuotaExceededFault", "error": {"code": "CacheSubnetQuotaExceededFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the allowed number of subnets in a cache subnet group.</p>"}, {"shape": "SubnetInUse", "error": {"code": "SubnetInUse", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested subnet is being used by another cache subnet group.</p>"}, {"shape": "InvalidSubnet", "error": {"code": "InvalidSubnet", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>An invalid subnet identifier was specified.</p>"}], "documentation": "<p>The <i>ModifyCacheSubnetGroup</i> operation modifies an existing cache subnet group.</p>"}, "ModifyReplicationGroup": {"name": "ModifyReplicationGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ModifyReplicationGroupMessage", "documentation": "<p>Represents the input of a <i>ModifyReplicationGroups</i> operation.</p>"}, "output": {"shape": "ModifyReplicationGroupResult", "wrapper": true, "documentation": "<p>Contains all of the attributes of a specific replication group.</p>", "resultWrapper": "ModifyReplicationGroupResult"}, "errors": [{"shape": "ReplicationGroupNotFoundFault", "error": {"code": "ReplicationGroupNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The specified replication group does not exist.</p>"}, {"shape": "InvalidReplicationGroupStateFault", "error": {"code": "InvalidReplicationGroupState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested replication group is not in the <i>available</i> state.</p>"}, {"shape": "InvalidCacheClusterStateFault", "error": {"code": "InvalidCacheClusterState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache cluster is not in the <i>available</i> state.</p>"}, {"shape": "InvalidCacheSecurityGroupStateFault", "error": {"code": "InvalidCacheSecurityGroupState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The current state of the cache security group does not allow deletion.</p>"}, {"shape": "InsufficientCacheClusterCapacityFault", "error": {"code": "InsufficientCacheClusterCapacity", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache node type is not available in the specified Availability Zone.</p>"}, {"shape": "CacheClusterNotFoundFault", "error": {"code": "CacheClusterNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache cluster ID does not refer to an existing cache cluster.</p>"}, {"shape": "NodeQuotaForClusterExceededFault", "error": {"code": "NodeQuotaForClusterExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the allowed number of cache nodes in a single cache cluster.</p>"}, {"shape": "NodeQuotaForCustomerExceededFault", "error": {"code": "NodeQuotaForCustomerExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the allowed number of cache nodes per customer. </p>"}, {"shape": "CacheSecurityGroupNotFoundFault", "error": {"code": "CacheSecurityGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The requested cache security group name does not refer to an existing cache security group.</p>"}, {"shape": "CacheParameterGroupNotFoundFault", "error": {"code": "CacheParameterGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The requested cache parameter group name does not refer to an existing cache parameter group.</p>"}, {"shape": "InvalidVPCNetworkStateFault", "error": {"code": "InvalidVPCNetworkStateFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The VPC network is in an invalid state.</p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>ModifyReplicationGroup</i> operation modifies the settings for a replication group.</p>"}, "PurchaseReservedCacheNodesOffering": {"name": "PurchaseReservedCacheNodesOffering", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PurchaseReservedCacheNodesOfferingMessage", "documentation": "<p>Represents the input of a <i>PurchaseReservedCacheNodesOffering</i> operation.</p>"}, "output": {"shape": "PurchaseReservedCacheNodesOfferingResult", "wrapper": true, "documentation": "<p>Represents the output of a <i>PurchaseReservedCacheNodesOffering</i> operation.</p>", "resultWrapper": "PurchaseReservedCacheNodesOfferingResult"}, "errors": [{"shape": "ReservedCacheNodesOfferingNotFoundFault", "error": {"code": "ReservedCacheNodesOfferingNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache node offering does not exist.</p>"}, {"shape": "ReservedCacheNodeAlreadyExistsFault", "error": {"code": "ReservedCacheNodeAlreadyExists", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>You already have a reservation with the given identifier.</p>"}, {"shape": "ReservedCacheNodeQuotaExceededFault", "error": {"code": "ReservedCacheNodeQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the user's cache node quota.</p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>PurchaseReservedCacheNodesOffering</i> operation allows you to purchase a reserved cache node offering.</p>"}, "RebootCacheCluster": {"name": "RebootCacheCluster", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RebootCacheClusterMessage", "documentation": "<p>Represents the input of a <i>RebootCacheCluster</i> operation.</p>"}, "output": {"shape": "RebootCacheClusterResult", "wrapper": true, "documentation": "<p>Contains all of the attributes of a specific cache cluster.</p>", "resultWrapper": "RebootCacheClusterResult"}, "errors": [{"shape": "InvalidCacheClusterStateFault", "error": {"code": "InvalidCacheClusterState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache cluster is not in the <i>available</i> state.</p>"}, {"shape": "CacheClusterNotFoundFault", "error": {"code": "CacheClusterNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache cluster ID does not refer to an existing cache cluster.</p>"}], "documentation": "<p>The <i>RebootCacheCluster</i> operation reboots some, or all, of the cache nodes within a provisioned cache cluster. This API will apply any modified cache parameter groups to the cache cluster. The reboot action takes place as soon as possible, and results in a momentary outage to the cache cluster. During the reboot, the cache cluster status is set to REBOOTING.</p> <p>The reboot causes the contents of the cache (for each cache node being rebooted) to be lost.</p> <p>When the reboot is complete, a cache cluster event is created.</p>"}, "ResetCacheParameterGroup": {"name": "ResetCacheParameterGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ResetCacheParameterGroupMessage", "documentation": "<p>Represents the input of a <i>ResetCacheParameterGroup</i> operation.</p>"}, "output": {"shape": "CacheParameterGroupNameMessage", "documentation": "<p>Represents the output of one of the following operations:</p> <ul> <li> <i>ModifyCacheParameterGroup</i> </li> <li> <i>ResetCacheParameterGroup</i> </li> </ul>", "resultWrapper": "ResetCacheParameterGroupResult"}, "errors": [{"shape": "InvalidCacheParameterGroupStateFault", "error": {"code": "InvalidCacheParameterGroupState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The current state of the cache parameter group does not allow the requested action to occur. </p>"}, {"shape": "CacheParameterGroupNotFoundFault", "error": {"code": "CacheParameterGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The requested cache parameter group name does not refer to an existing cache parameter group.</p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>ResetCacheParameterGroup</i> operation modifies the parameters of a cache parameter group to the engine or system default value. You can reset specific parameters by submitting a list of parameter names. To reset the entire cache parameter group, specify the <i>ResetAllParameters</i> and <i>CacheParameterGroupName</i> parameters.</p>"}, "RevokeCacheSecurityGroupIngress": {"name": "RevokeCacheSecurityGroupIngress", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RevokeCacheSecurityGroupIngressMessage", "documentation": "<p>Represents the input of a <i>RevokeCacheSecurityGroupIngress</i> operation.</p>"}, "output": {"shape": "RevokeCacheSecurityGroupIngressResult", "wrapper": true, "documentation": "<p>Represents the output of one of the following operations:</p> <ul> <li> <i>AuthorizeCacheSecurityGroupIngress</i> </li> <li> <i>CreateCacheSecurityGroup</i> </li> <li> <i>RevokeCacheSecurityGroupIngress</i> </li> </ul>", "resultWrapper": "RevokeCacheSecurityGroupIngressResult"}, "errors": [{"shape": "CacheSecurityGroupNotFoundFault", "error": {"code": "CacheSecurityGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The requested cache security group name does not refer to an existing cache security group.</p>"}, {"shape": "AuthorizationNotFoundFault", "error": {"code": "AuthorizationNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The specified Amazon EC2 security group is not authorized for the specified cache security group.</p>"}, {"shape": "InvalidCacheSecurityGroupStateFault", "error": {"code": "InvalidCacheSecurityGroupState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The current state of the cache security group does not allow deletion.</p>"}, {"shape": "InvalidParameterValueException", "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, {"shape": "InvalidParameterCombinationException", "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}], "documentation": "<p>The <i>RevokeCacheSecurityGroupIngress</i> operation revokes ingress from a cache security group. Use this operation to disallow access from an Amazon EC2 security group that had been previously authorized.</p>"}}, "shapes": {"AZMode": {"type": "string", "enum": ["single-az", "cross-az"]}, "AuthorizationAlreadyExistsFault": {"type": "structure", "members": {}, "error": {"code": "AuthorizationAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The specified Amazon EC2 security group is already authorized for the specified cache security group.</p>"}, "AuthorizationNotFoundFault": {"type": "structure", "members": {}, "error": {"code": "AuthorizationNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The specified Amazon EC2 security group is not authorized for the specified cache security group.</p>"}, "AuthorizeCacheSecurityGroupIngressMessage": {"type": "structure", "required": ["CacheSecurityGroupName", "EC2SecurityGroupName", "EC2SecurityGroupOwnerId"], "members": {"CacheSecurityGroupName": {"shape": "String", "documentation": "<p>The cache security group which will allow network ingress.</p>"}, "EC2SecurityGroupName": {"shape": "String", "documentation": "<p>The Amazon EC2 security group to be authorized for ingress to the cache security group.</p>"}, "EC2SecurityGroupOwnerId": {"shape": "String", "documentation": "<p>The AWS account number of the Amazon EC2 security group owner. Note that this is not the same thing as an AWS access key ID - you must provide a valid AWS account number for this parameter.</p>"}}, "documentation": "<p>Represents the input of an <i>AuthorizeCacheSecurityGroupIngress</i> operation.</p>"}, "AutomaticFailoverStatus": {"type": "string", "enum": ["enabled", "disabled", "enabling", "disabling"]}, "AvailabilityZone": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>The name of the Availability Zone.</p>"}}, "wrapper": true, "documentation": "<p>Describes an Availability Zone in which the cache cluster is launched.</p>"}, "AvailabilityZonesList": {"type": "list", "member": {"shape": "String", "locationName": "AvailabilityZone"}}, "AwsQueryErrorMessage": {"type": "string"}, "Boolean": {"type": "boolean"}, "BooleanOptional": {"type": "boolean"}, "CacheCluster": {"type": "structure", "members": {"CacheClusterId": {"shape": "String", "documentation": "<p>The user-supplied identifier of the cache cluster. This identifier is a unique key that identifies a cache cluster.</p>"}, "ConfigurationEndpoint": {"shape": "Endpoint"}, "ClientDownloadLandingPage": {"shape": "String", "documentation": "<p>The URL of the web page where you can download the latest ElastiCache client library.</p>"}, "CacheNodeType": {"shape": "String", "documentation": "<p>The name of the compute and memory capacity node type for the cache cluster.</p>"}, "Engine": {"shape": "String", "documentation": "<p>The name of the cache engine (<i>memcached</i> or <i>redis</i>) to be used for this cache cluster.</p>"}, "EngineVersion": {"shape": "String", "documentation": "<p>The version of the cache engine version that is used in this cache cluster.</p>"}, "CacheClusterStatus": {"shape": "String", "documentation": "<p>The current state of this cache cluster, one of the following values: <i>available</i>, <i>creating</i>, <i>deleted</i>, <i>deleting</i>, <i>incompatible-network</i>, <i>modifying</i>, <i>rebooting cache cluster nodes</i>, <i>restore-failed</i>, or <i>snapshotting</i>.</p>"}, "NumCacheNodes": {"shape": "IntegerOptional", "documentation": "<p>The number of cache nodes in the cache cluster.</p>"}, "PreferredAvailabilityZone": {"shape": "String", "documentation": "<p>The name of the Availability Zone in which the cache cluster is located or \"Multiple\" if the cache nodes are located in different Availability Zones.</p>"}, "CacheClusterCreateTime": {"shape": "TStamp", "documentation": "<p>The date and time when the cache cluster was created.</p>"}, "PreferredMaintenanceWindow": {"shape": "String", "documentation": "<p>The time range (in UTC) during which weekly system maintenance can occur.</p>"}, "PendingModifiedValues": {"shape": "PendingModifiedValues"}, "NotificationConfiguration": {"shape": "NotificationConfiguration"}, "CacheSecurityGroups": {"shape": "CacheSecurityGroupMembershipList", "documentation": "<p>A list of cache security group elements, composed of name and status sub-elements.</p>"}, "CacheParameterGroup": {"shape": "CacheParameterGroupStatus"}, "CacheSubnetGroupName": {"shape": "String", "documentation": "<p>The name of the cache subnet group associated with the cache cluster.</p>"}, "CacheNodes": {"shape": "CacheNodeList", "documentation": "<p>A list of cache nodes that are members of the cache cluster.</p>"}, "AutoMinorVersionUpgrade": {"shape": "Boolean", "documentation": "<p>If <code>true</code>, then minor version patches are applied automatically; if <code>false</code>, then automatic minor version patches are disabled.</p>"}, "SecurityGroups": {"shape": "SecurityGroupMembershipList", "documentation": "<p>A list of VPC Security Groups associated with the cache cluster.</p>"}, "ReplicationGroupId": {"shape": "String", "documentation": "<p>The replication group to which this cache cluster belongs. If this field is empty, the cache cluster is not associated with any replication group.</p>"}, "SnapshotRetentionLimit": {"shape": "IntegerOptional", "documentation": "<p>The number of days for which ElastiCache will retain automatic cache cluster snapshots before deleting them. For example, if you set <i>SnapshotRetentionLimit</i> to 5, then a snapshot that was taken today will be retained for 5 days before being deleted.</p> <p><b>Important</b><br>If the value of SnapshotRetentionLimit is set to zero (0), backups are turned off.</p>"}, "SnapshotWindow": {"shape": "String", "documentation": "<p>The daily time range (in UTC) during which ElastiCache will begin taking a daily snapshot of your cache cluster.</p> <p>Example: <code>05:00-09:00</code></p>"}}, "wrapper": true, "documentation": "<p>Contains all of the attributes of a specific cache cluster.</p>"}, "CacheClusterAlreadyExistsFault": {"type": "structure", "members": {}, "error": {"code": "CacheClusterAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>You already have a cache cluster with the given identifier.</p>"}, "CacheClusterList": {"type": "list", "member": {"shape": "CacheCluster", "locationName": "CacheCluster"}}, "CacheClusterMessage": {"type": "structure", "members": {"Marker": {"shape": "String", "documentation": "<p>Provides an identifier to allow retrieval of paginated results.</p>"}, "CacheClusters": {"shape": "CacheClusterList", "documentation": "<p>A list of cache clusters. Each item in the list contains detailed information about one cache cluster.</p>"}}, "documentation": "<p>Represents the output of a <i>DescribeCacheClusters</i> operation.</p>"}, "CacheClusterNotFoundFault": {"type": "structure", "members": {}, "error": {"code": "CacheClusterNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache cluster ID does not refer to an existing cache cluster.</p>"}, "CacheEngineVersion": {"type": "structure", "members": {"Engine": {"shape": "String", "documentation": "<p>The name of the cache engine.</p>"}, "EngineVersion": {"shape": "String", "documentation": "<p>The version number of the cache engine.</p>"}, "CacheParameterGroupFamily": {"shape": "String", "documentation": "<p>The name of the cache parameter group family associated with this cache engine.</p>"}, "CacheEngineDescription": {"shape": "String", "documentation": "<p>The description of the cache engine.</p>"}, "CacheEngineVersionDescription": {"shape": "String", "documentation": "<p>The description of the cache engine version.</p>"}}, "documentation": "<p>Provides all of the details about a particular cache engine version.</p>"}, "CacheEngineVersionList": {"type": "list", "member": {"shape": "CacheEngineVersion", "locationName": "CacheEngineVersion"}}, "CacheEngineVersionMessage": {"type": "structure", "members": {"Marker": {"shape": "String", "documentation": "<p>Provides an identifier to allow retrieval of paginated results.</p>"}, "CacheEngineVersions": {"shape": "CacheEngineVersionList", "documentation": "<p>A list of cache engine version details. Each element in the list contains detailed information about one cache engine version.</p>"}}, "documentation": "<p>Represents the output of a <a>DescribeCacheEngineVersions</a> operation.</p>"}, "CacheNode": {"type": "structure", "members": {"CacheNodeId": {"shape": "String", "documentation": "<p>The cache node identifier. A node ID is a numeric identifier (0001, 0002, etc.). The combination of cluster ID and node ID uniquely identifies every cache node used in a customer's AWS account.</p>"}, "CacheNodeStatus": {"shape": "String", "documentation": "<p>The current state of this cache node.</p>"}, "CacheNodeCreateTime": {"shape": "TStamp", "documentation": "<p>The date and time when the cache node was created.</p>"}, "Endpoint": {"shape": "Endpoint", "documentation": "<p>The hostname and IP address for connecting to this cache node.</p>"}, "ParameterGroupStatus": {"shape": "String", "documentation": "<p>The status of the parameter group applied to this cache node.</p>"}, "SourceCacheNodeId": {"shape": "String", "documentation": "<p>The ID of the primary node to which this read replica node is synchronized. If this field is empty, then this node is not associated with a primary cache cluster.</p>"}, "CustomerAvailabilityZone": {"shape": "String", "documentation": "<p>The Availability Zone where this node was created and now resides.</p>"}}, "documentation": "<p>Represents an individual cache node within a cache cluster. Each cache node runs its own instance of the cluster's protocol-compliant caching software - either Memcached or Redis.</p>"}, "CacheNodeIdsList": {"type": "list", "member": {"shape": "String", "locationName": "CacheNodeId"}}, "CacheNodeList": {"type": "list", "member": {"shape": "CacheNode", "locationName": "CacheNode"}}, "CacheNodeTypeSpecificParameter": {"type": "structure", "members": {"ParameterName": {"shape": "String", "documentation": "<p>The name of the parameter.</p>"}, "Description": {"shape": "String", "documentation": "<p>A description of the parameter.</p>"}, "Source": {"shape": "String", "documentation": "<p>The source of the parameter value.</p>"}, "DataType": {"shape": "String", "documentation": "<p>The valid data type for the parameter.</p>"}, "AllowedValues": {"shape": "String", "documentation": "<p>The valid range of values for the parameter.</p>"}, "IsModifiable": {"shape": "Boolean", "documentation": "<p>Indicates whether (<code>true</code>) or not (<code>false</code>) the parameter can be modified. Some parameters have security or operational implications that prevent them from being changed.</p>"}, "MinimumEngineVersion": {"shape": "String", "documentation": "<p>The earliest cache engine version to which the parameter can apply.</p>"}, "CacheNodeTypeSpecificValues": {"shape": "CacheNodeTypeSpecificValueList", "documentation": "<p>A list of cache node types and their corresponding values for this parameter.</p>"}}, "documentation": "<p>A parameter that has a different value for each cache node type it is applied to. For example, in a Redis cache cluster, a <i>cache.m1.large</i> cache node type would have a larger <i>maxmemory</i> value than a <i>cache.m1.small</i> type.</p>"}, "CacheNodeTypeSpecificParametersList": {"type": "list", "member": {"shape": "CacheNodeTypeSpecificParameter", "locationName": "CacheNodeTypeSpecificParameter"}}, "CacheNodeTypeSpecificValue": {"type": "structure", "members": {"CacheNodeType": {"shape": "String", "documentation": "<p>The cache node type for which this value applies.</p>"}, "Value": {"shape": "String", "documentation": "<p>The value for the cache node type.</p>"}}, "documentation": "<p>A value that applies only to a certain cache node type.</p>"}, "CacheNodeTypeSpecificValueList": {"type": "list", "member": {"shape": "CacheNodeTypeSpecificValue", "locationName": "CacheNodeTypeSpecificValue"}}, "CacheParameterGroup": {"type": "structure", "members": {"CacheParameterGroupName": {"shape": "String", "documentation": "<p>The name of the cache parameter group.</p>"}, "CacheParameterGroupFamily": {"shape": "String", "documentation": "<p>The name of the cache parameter group family that this cache parameter group is compatible with.</p>"}, "Description": {"shape": "String", "documentation": "<p>The description for this cache parameter group.</p>"}}, "wrapper": true, "documentation": "<p>Represents the output of a <i>CreateCacheParameterGroup</i> operation.</p>"}, "CacheParameterGroupAlreadyExistsFault": {"type": "structure", "members": {}, "error": {"code": "CacheParameterGroupAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>A cache parameter group with the requested name already exists.</p>"}, "CacheParameterGroupDetails": {"type": "structure", "members": {"Marker": {"shape": "String", "documentation": "<p>Provides an identifier to allow retrieval of paginated results.</p>"}, "Parameters": {"shape": "ParametersList", "documentation": "<p>A list of <a>Parameter</a> instances.</p>"}, "CacheNodeTypeSpecificParameters": {"shape": "CacheNodeTypeSpecificParametersList", "documentation": "<p>A list of parameters specific to a particular cache node type. Each element in the list contains detailed information about one parameter.</p>"}}, "documentation": "<p>Represents the output of a <i>DescribeCacheParameters</i> operation.</p>"}, "CacheParameterGroupList": {"type": "list", "member": {"shape": "CacheParameterGroup", "locationName": "CacheParameterGroup"}}, "CacheParameterGroupNameMessage": {"type": "structure", "members": {"CacheParameterGroupName": {"shape": "String", "documentation": "<p>The name of the cache parameter group.</p>"}}, "documentation": "<p>Represents the output of one of the following operations:</p> <ul> <li> <i>ModifyCacheParameterGroup</i> </li> <li> <i>ResetCacheParameterGroup</i> </li> </ul>"}, "CacheParameterGroupNotFoundFault": {"type": "structure", "members": {}, "error": {"code": "CacheParameterGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The requested cache parameter group name does not refer to an existing cache parameter group.</p>"}, "CacheParameterGroupQuotaExceededFault": {"type": "structure", "members": {}, "error": {"code": "CacheParameterGroupQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the maximum number of cache security groups.</p>"}, "CacheParameterGroupStatus": {"type": "structure", "members": {"CacheParameterGroupName": {"shape": "String", "documentation": "<p>The name of the cache parameter group.</p>"}, "ParameterApplyStatus": {"shape": "String", "documentation": "<p>The status of parameter updates.</p>"}, "CacheNodeIdsToReboot": {"shape": "CacheNodeIdsList", "documentation": "<p>A list of the cache node IDs which need to be rebooted for parameter changes to be applied. A node ID is a numeric identifier (0001, 0002, etc.).</p>"}}, "documentation": "<p>The status of the cache parameter group.</p>"}, "CacheParameterGroupsMessage": {"type": "structure", "members": {"Marker": {"shape": "String", "documentation": "<p>Provides an identifier to allow retrieval of paginated results.</p>"}, "CacheParameterGroups": {"shape": "CacheParameterGroupList", "documentation": "<p>A list of cache parameter groups. Each element in the list contains detailed information about one cache parameter group.</p>"}}, "documentation": "<p>Represents the output of a <i>DescribeCacheParameterGroups</i> operation.</p>"}, "CacheSecurityGroup": {"type": "structure", "members": {"OwnerId": {"shape": "String", "documentation": "<p>The AWS account ID of the cache security group owner.</p>"}, "CacheSecurityGroupName": {"shape": "String", "documentation": "<p>The name of the cache security group.</p>"}, "Description": {"shape": "String", "documentation": "<p>The description of the cache security group.</p>"}, "EC2SecurityGroups": {"shape": "EC2SecurityGroupList", "documentation": "<p>A list of Amazon EC2 security groups that are associated with this cache security group.</p>"}}, "wrapper": true, "documentation": "<p>Represents the output of one of the following operations:</p> <ul> <li> <i>AuthorizeCacheSecurityGroupIngress</i> </li> <li> <i>CreateCacheSecurityGroup</i> </li> <li> <i>RevokeCacheSecurityGroupIngress</i> </li> </ul>"}, "CacheSecurityGroupAlreadyExistsFault": {"type": "structure", "members": {}, "error": {"code": "CacheSecurityGroupAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>A cache security group with the specified name already exists.</p>"}, "CacheSecurityGroupMembership": {"type": "structure", "members": {"CacheSecurityGroupName": {"shape": "String", "documentation": "<p>The name of the cache security group.</p>"}, "Status": {"shape": "String", "documentation": "<p>The membership status in the cache security group. The status changes when a cache security group is modified, or when the cache security groups assigned to a cache cluster are modified.</p>"}}, "documentation": "<p>Represents a cache cluster's status within a particular cache security group.</p>"}, "CacheSecurityGroupMembershipList": {"type": "list", "member": {"shape": "CacheSecurityGroupMembership", "locationName": "CacheSecurityGroup"}}, "CacheSecurityGroupMessage": {"type": "structure", "members": {"Marker": {"shape": "String", "documentation": "<p>Provides an identifier to allow retrieval of paginated results.</p>"}, "CacheSecurityGroups": {"shape": "CacheSecurityGroups", "documentation": "<p>A list of cache security groups. Each element in the list contains detailed information about one group.</p>"}}, "documentation": "<p>Represents the output of a <i>DescribeCacheSecurityGroups</i> operation.</p>"}, "CacheSecurityGroupNameList": {"type": "list", "member": {"shape": "String", "locationName": "CacheSecurityGroupName"}}, "CacheSecurityGroupNotFoundFault": {"type": "structure", "members": {}, "error": {"code": "CacheSecurityGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The requested cache security group name does not refer to an existing cache security group.</p>"}, "CacheSecurityGroupQuotaExceededFault": {"type": "structure", "members": {}, "error": {"code": "QuotaExceeded.CacheSecurityGroup", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the allowed number of cache security groups.</p>"}, "CacheSecurityGroups": {"type": "list", "member": {"shape": "CacheSecurityGroup", "locationName": "CacheSecurityGroup"}}, "CacheSubnetGroup": {"type": "structure", "members": {"CacheSubnetGroupName": {"shape": "String", "documentation": "<p>The name of the cache subnet group.</p>"}, "CacheSubnetGroupDescription": {"shape": "String", "documentation": "<p>The description of the cache subnet group.</p>"}, "VpcId": {"shape": "String", "documentation": "<p>The Amazon Virtual Private Cloud identifier (VPC ID) of the cache subnet group.</p>"}, "Subnets": {"shape": "SubnetList", "documentation": "<p>A list of subnets associated with the cache subnet group.</p>"}}, "wrapper": true, "documentation": "<p>Represents the output of one of the following operations:</p> <ul> <li> <i>CreateCacheSubnetGroup</i> </li> <li> <i>ModifyCacheSubnetGroup</i> </li> </ul>"}, "CacheSubnetGroupAlreadyExistsFault": {"type": "structure", "members": {}, "error": {"code": "CacheSubnetGroupAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The requested cache subnet group name is already in use by an existing cache subnet group.</p>"}, "CacheSubnetGroupInUse": {"type": "structure", "members": {}, "error": {"code": "CacheSubnetGroupInUse", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache subnet group is currently in use.</p>"}, "CacheSubnetGroupMessage": {"type": "structure", "members": {"Marker": {"shape": "String", "documentation": "<p>Provides an identifier to allow retrieval of paginated results.</p>"}, "CacheSubnetGroups": {"shape": "CacheSubnetGroups", "documentation": "<p>A list of cache subnet groups. Each element in the list contains detailed information about one group.</p>"}}, "documentation": "<p>Represents the output of a <i>DescribeCacheSubnetGroups</i> operation.</p>"}, "CacheSubnetGroupNotFoundFault": {"type": "structure", "members": {}, "error": {"code": "CacheSubnetGroupNotFoundFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache subnet group name does not refer to an existing cache subnet group.</p>"}, "CacheSubnetGroupQuotaExceededFault": {"type": "structure", "members": {}, "error": {"code": "CacheSubnetGroupQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the allowed number of cache subnet groups.</p>"}, "CacheSubnetGroups": {"type": "list", "member": {"shape": "CacheSubnetGroup", "locationName": "CacheSubnetGroup"}}, "CacheSubnetQuotaExceededFault": {"type": "structure", "members": {}, "error": {"code": "CacheSubnetQuotaExceededFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the allowed number of subnets in a cache subnet group.</p>"}, "ClusterIdList": {"type": "list", "member": {"shape": "String", "locationName": "ClusterId"}}, "ClusterQuotaForCustomerExceededFault": {"type": "structure", "members": {}, "error": {"code": "ClusterQuotaForCustomerExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the allowed number of cache clusters per customer.</p>"}, "CopySnapshotMessage": {"type": "structure", "required": ["SourceSnapshotName", "TargetSnapshotName"], "members": {"SourceSnapshotName": {"shape": "String", "documentation": "<p>The name of an existing snapshot from which to copy.</p>"}, "TargetSnapshotName": {"shape": "String", "documentation": "<p>A name for the copied snapshot.</p>"}}, "documentation": "<p>Represents the input of a <i>CopySnapshotMessage</i> operation.</p>"}, "CreateCacheClusterMessage": {"type": "structure", "required": ["CacheClusterId"], "members": {"CacheClusterId": {"shape": "String", "documentation": "<p>The node group identifier. This parameter is stored as a lowercase string.</p> <p>Constraints:</p> <ul> <li>A name must contain from 1 to 20 alphanumeric characters or hyphens.</li> <li>The first character must be a letter.</li> <li>A name cannot end with a hyphen or contain two consecutive hyphens.</li> </ul>"}, "ReplicationGroupId": {"shape": "String", "documentation": "<p>The ID of the replication group to which this cache cluster should belong. If this parameter is specified, the cache cluster will be added to the specified replication group as a read replica; otherwise, the cache cluster will be a standalone primary that is not part of any replication group.</p> <p>If the specified replication group is Automatic Failover enabled and the availability zone is not specified, the cache cluster will be created in availability zones that provide the best spread of read replicas across availability zones.</p> <p><b>Note:</b> This parameter is only valid if the <code>Engine</code> parameter is <code>redis</code>.</p>"}, "AZMode": {"shape": "AZMode", "documentation": "<p>Specifies whether the nodes in this Memcached node group are created in a single Availability Zone or created across multiple Availability Zones in the cluster's region.</p> <p>This parameter is only supported for Memcached cache clusters.</p> <p>If the <code>AZMode</code> and <code>PreferredAvailabilityZones</code> are not specified, ElastiCache assumes <code>single-az</code> mode.</p>"}, "PreferredAvailabilityZone": {"shape": "String", "documentation": "<p>The EC2 Availability Zone in which the cache cluster will be created.</p> <p>All nodes belonging to this Memcached cache cluster are placed in the preferred Availability Zone. If you want to create your nodes across multiple Availability Zones, use <code>PreferredAvailabilityZones</code>.</p> <p>Default: System chosen Availability Zone.</p>"}, "PreferredAvailabilityZones": {"shape": "PreferredAvailabilityZoneList", "documentation": "<p>A list of the Availability Zones in which cache nodes will be created. The order of the zones in the list is not important.</p> <p>This option is only supported on Memcached.</p> <p>If you want all the nodes in the same Availability Zone, use <code>PreferredAvailabilityZone</code> instead, or repeat the Availability Zone multiple times in the list.</p> <p>Default: System chosen Availability Zones.</p> <p>Example: One Memcached node in each of three different Availability Zones: <code>PreferredAvailabilityZones.member.1=us-east-1a&amp;PreferredAvailabilityZones.member.2=us-east-1b&amp;PreferredAvailabilityZones.member.3=us-east-1d</code></p> <p>Example: All three Memcached nodes in one Availability Zone: <code>PreferredAvailabilityZones.member.1=us-east-1a&amp;PreferredAvailabilityZones.member.2=us-east-1a&amp;PreferredAvailabilityZones.member.3=us-east-1a</code></p>"}, "NumCacheNodes": {"shape": "IntegerOptional", "documentation": "<p>The initial number of cache nodes that the cache cluster will have.</p> <p>For Memcached, valid values are between 1 and 20. If you need to exceed this limit, please fill out the ElastiCache Limit Increase Request form at <a href=\"http://aws.amazon.com/contact-us/elasticache-node-limit-request/\">http://aws.amazon.com/contact-us/elasticache-node-limit-request/</a>.</p> <p>For Redis, only single-node cache cluster are supported at this time, so the value for this parameter must be 1.</p>"}, "CacheNodeType": {"shape": "String", "documentation": "<p>The compute and memory capacity of the nodes in the node group.</p> <p>Valid node types are as follows:</p> <ul> <li>General purpose: <ul> <li>Current generation: <code>cache.t2.micro</code>, <code>cache.t2.small</code>, <code>cache.t2.medium</code>, <code>cache.m3.medium</code>, <code>cache.m3.large</code>, <code>cache.m3.xlarge</code>, <code>cache.m3.2xlarge</code></li> <li>Previous generation: <code>cache.t1.micro</code>, <code>cache.m1.small</code>, <code>cache.m1.medium</code>, <code>cache.m1.large</code>, <code>cache.m1.xlarge</code></li> </ul></li> <li>Compute optimized: <code>cache.c1.xlarge</code></li> <li>Memory optimized <ul> <li>Current generation: <code>cache.r3.large</code>, <code>cache.r3.xlarge</code>, <code>cache.r3.2xlarge</code>, <code>cache.r3.4xlarge</code>, <code>cache.r3.8xlarge</code></li> <li>Previous generation: <code>cache.m2.xlarge</code>, <code>cache.m2.2xlarge</code>, <code>cache.m2.4xlarge</code></li> </ul></li> </ul> <p><b>Notes:</b></p> <ul> <li>All t2 instances are created in an Amazon Virtual Private Cloud (VPC).</li> <li>Redis backup/restore is not supported for t2 instances.</li> <li>Redis Append-only files (AOF) functionality is not supported for t1 or t2 instances.</li> </ul> <p>For a complete listing of cache node types and specifications, see <a href=\"http://aws.amazon.com/elasticache/details\">Amazon ElastiCache Product Features and Details</a> and <a href=\"http://docs.aws.amazon.com/AmazonElastiCache/latest/UserGuide/CacheParameterGroups.Memcached.html#CacheParameterGroups.Memcached.NodeSpecific\">Cache Node Type-Specific Parameters for Memcached</a> or <a href=\"http://docs.aws.amazon.com/AmazonElastiCache/latest/UserGuide/CacheParameterGroups.Redis.html#CacheParameterGroups.Redis.NodeSpecific\">Cache Node Type-Specific Parameters for Redis</a>. </p>"}, "Engine": {"shape": "String", "documentation": "<p>The name of the cache engine to be used for this cache cluster.</p> <p>Valid values for this parameter are:</p> <p><code>memcached</code> | <code>redis</code></p>"}, "EngineVersion": {"shape": "String", "documentation": "<p>The version number of the cache engine to be used for this cache cluster. To view the supported cache engine versions, use the <i>DescribeCacheEngineVersions</i> operation.</p>"}, "CacheParameterGroupName": {"shape": "String", "documentation": "<p>The name of the parameter group to associate with this cache cluster. If this argument is omitted, the default parameter group for the specified engine is used.</p>"}, "CacheSubnetGroupName": {"shape": "String", "documentation": "<p>The name of the subnet group to be used for the cache cluster.</p> <p>Use this parameter only when you are creating a cache cluster in an Amazon Virtual Private Cloud (VPC).</p>"}, "CacheSecurityGroupNames": {"shape": "CacheSecurityGroupNameList", "documentation": "<p>A list of security group names to associate with this cache cluster.</p> <p>Use this parameter only when you are creating a cache cluster outside of an Amazon Virtual Private Cloud (VPC).</p>"}, "SecurityGroupIds": {"shape": "SecurityGroupIdsList", "documentation": "<p>One or more VPC security groups associated with the cache cluster.</p> <p>Use this parameter only when you are creating a cache cluster in an Amazon Virtual Private Cloud (VPC).</p>"}, "SnapshotArns": {"shape": "SnapshotArnsList", "documentation": "<p>A single-element string list containing an Amazon Resource Name (ARN) that uniquely identifies a Redis RDB snapshot file stored in Amazon S3. The snapshot file will be used to populate the node group. The Amazon S3 object name in the ARN cannot contain any commas.</p> <p><b>Note:</b> This parameter is only valid if the <code>Engine</code> parameter is <code>redis</code>.</p> <p>Example of an Amazon S3 ARN: <code>arn:aws:s3:::my_bucket/snapshot1.rdb</code></p>"}, "SnapshotName": {"shape": "String", "documentation": "<p>The name of a snapshot from which to restore data into the new node group. The snapshot status changes to <code>restoring</code> while the new node group is being created.</p> <p><b>Note:</b> This parameter is only valid if the <code>Engine</code> parameter is <code>redis</code>.</p>"}, "PreferredMaintenanceWindow": {"shape": "String", "documentation": "<p>The weekly time range (in UTC) during which system maintenance can occur.</p> <p>Example: <code>sun:05:00-sun:09:00</code></p>"}, "Port": {"shape": "IntegerOptional", "documentation": "<p>The port number on which each of the cache nodes will accept connections.</p>"}, "NotificationTopicArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon Simple Notification Service (SNS) topic to which notifications will be sent.</p>"}, "AutoMinorVersionUpgrade": {"shape": "BooleanOptional", "documentation": "<p>Determines whether minor engine upgrades will be applied automatically to the node group during the maintenance window. A value of <code>true</code> allows these upgrades to occur; <code>false</code> disables automatic upgrades.</p> <p>Default: <code>true</code></p>"}, "SnapshotRetentionLimit": {"shape": "IntegerOptional", "documentation": "<p>The number of days for which ElastiCache will retain automatic snapshots before deleting them. For example, if you set <code>SnapshotRetentionLimit</code> to 5, then a snapshot that was taken today will be retained for 5 days before being deleted.</p> <p><b>Note:</b> This parameter is only valid if the <code>Engine</code> parameter is <code>redis</code>.</p> <p>Default: 0 (i.e., automatic backups are disabled for this cache cluster).</p>"}, "SnapshotWindow": {"shape": "String", "documentation": "<p>The daily time range (in UTC) during which ElastiCache will begin taking a daily snapshot of your node group.</p> <p>Example: <code>05:00-09:00</code></p> <p>If you do not specify this parameter, then ElastiCache will automatically choose an appropriate time range.</p> <p><b>Note:</b> This parameter is only valid if the <code>Engine</code> parameter is <code>redis</code>.</p>"}}, "documentation": "<p>Represents the input of a <i>CreateCacheCluster</i> operation.</p>"}, "CreateCacheParameterGroupMessage": {"type": "structure", "required": ["CacheParameterGroupName", "CacheParameterGroupFamily", "Description"], "members": {"CacheParameterGroupName": {"shape": "String", "documentation": "<p>A user-specified name for the cache parameter group.</p>"}, "CacheParameterGroupFamily": {"shape": "String", "documentation": "<p>The name of the cache parameter group family the cache parameter group can be used with.</p> <p>Valid values are: <code>memcached1.4</code> | <code>redis2.6</code> | <code>redis2.8</code></p>"}, "Description": {"shape": "String", "documentation": "<p>A user-specified description for the cache parameter group.</p>"}}, "documentation": "<p>Represents the input of a <i>CreateCacheParameterGroup</i> operation.</p>"}, "CreateCacheSecurityGroupMessage": {"type": "structure", "required": ["CacheSecurityGroupName", "Description"], "members": {"CacheSecurityGroupName": {"shape": "String", "documentation": "<p>A name for the cache security group. This value is stored as a lowercase string.</p> <p>Constraints: Must contain no more than 255 alphanumeric characters. Cannot be the word \"Default\".</p> <p>Example: <code>mysecuritygroup</code></p>"}, "Description": {"shape": "String", "documentation": "<p>A description for the cache security group.</p>"}}, "documentation": "<p>Represents the input of a <i>CreateCacheSecurityGroup</i> operation.</p>"}, "CreateCacheSubnetGroupMessage": {"type": "structure", "required": ["CacheSubnetGroupName", "CacheSubnetGroupDescription", "SubnetIds"], "members": {"CacheSubnetGroupName": {"shape": "String", "documentation": "<p>A name for the cache subnet group. This value is stored as a lowercase string.</p> <p>Constraints: Must contain no more than 255 alphanumeric characters or hyphens.</p> <p>Example: <code>mysubnetgroup</code></p>"}, "CacheSubnetGroupDescription": {"shape": "String", "documentation": "<p>A description for the cache subnet group.</p>"}, "SubnetIds": {"shape": "SubnetIdentifierList", "documentation": "<p>A list of VPC subnet IDs for the cache subnet group.</p>"}}, "documentation": "<p>Represents the input of a <i>CreateCacheSubnetGroup</i> operation.</p>"}, "CreateReplicationGroupMessage": {"type": "structure", "required": ["ReplicationGroupId", "ReplicationGroupDescription"], "members": {"ReplicationGroupId": {"shape": "String", "documentation": "<p>The replication group identifier. This parameter is stored as a lowercase string.</p> <p>Constraints:</p> <ul> <li>A name must contain from 1 to 20 alphanumeric characters or hyphens.</li> <li>The first character must be a letter.</li> <li>A name cannot end with a hyphen or contain two consecutive hyphens.</li> </ul>"}, "ReplicationGroupDescription": {"shape": "String", "documentation": "<p>A user-created description for the replication group.</p>"}, "PrimaryClusterId": {"shape": "String", "documentation": "<p>The identifier of the cache cluster that will serve as the primary for this replication group. This cache cluster must already exist and have a status of <i>available</i>.</p> <p>This parameter is not required if <i>NumCacheClusters</i> is specified.</p>"}, "AutomaticFailoverEnabled": {"shape": "BooleanOptional", "documentation": "<p>Specifies whether a read-only replica will be automatically promoted to read/write primary if the existing primary fails.</p> <p>If <code>true</code>, automatic failover is enabled for this replication group. If <code>false</code>, automatic failover is disabled for this replication group.</p> <p>Default: false</p>"}, "NumCacheClusters": {"shape": "IntegerOptional", "documentation": "<p>The number of cache clusters this replication group will initially have.</p> <p>If <i>AutomaticFailover</i> is <code>enabled</code>, the value of this parameter must be at least 2.</p> <p>The maximum permitted value for <i>NumCacheClusters</i> is 6 (primary plus 5 replicas). If you need to exceed this limit, please fill out the ElastiCache Limit Increase Request forrm at <a href=\"http://aws.amazon.com/contact-us/elasticache-node-limit-request\">http://aws.amazon.com/contact-us/elasticache-node-limit-request</a>.</p>"}, "PreferredCacheClusterAZs": {"shape": "AvailabilityZonesList", "documentation": "<p>A list of EC2 availability zones in which the replication group's cache clusters will be created. The order of the availability zones in the list is not important.</p> <p>Default: system chosen availability zones.</p> <p>Example: One Redis cache cluster in each of three availability zones. PreferredAvailabilityZones.member.1=us-east-1a PreferredAvailabilityZones.member.2=us-east-1c PreferredAvailabilityZones.member.3=us-east-1d</p>"}, "CacheNodeType": {"shape": "String", "documentation": "<p>The compute and memory capacity of the nodes in the node group.</p> <p>Valid node types are as follows:</p> <ul> <li>General purpose: <ul> <li>Current generation: <code>cache.t2.micro</code>, <code>cache.t2.small</code>, <code>cache.t2.medium</code>, <code>cache.m3.medium</code>, <code>cache.m3.large</code>, <code>cache.m3.xlarge</code>, <code>cache.m3.2xlarge</code></li> <li>Previous generation: <code>cache.t1.micro</code>, <code>cache.m1.small</code>, <code>cache.m1.medium</code>, <code>cache.m1.large</code>, <code>cache.m1.xlarge</code></li> </ul></li> <li>Compute optimized: <code>cache.c1.xlarge</code></li> <li>Memory optimized <ul> <li>Current generation: <code>cache.r3.large</code>, <code>cache.r3.xlarge</code>, <code>cache.r3.2xlarge</code>, <code>cache.r3.4xlarge</code>, <code>cache.r3.8xlarge</code></li> <li>Previous generation: <code>cache.m2.xlarge</code>, <code>cache.m2.2xlarge</code>, <code>cache.m2.4xlarge</code></li> </ul></li> </ul> <p><b>Notes:</b></p> <ul> <li>All t2 instances are created in an Amazon Virtual Private Cloud (VPC).</li> <li>Redis backup/restore is not supported for t2 instances.</li> <li>Redis Append-only files (AOF) functionality is not supported for t1 or t2 instances.</li> </ul> <p>For a complete listing of cache node types and specifications, see <a href=\"http://aws.amazon.com/elasticache/details\">Amazon ElastiCache Product Features and Details</a> and <a href=\"http://docs.aws.amazon.com/AmazonElastiCache/latest/UserGuide/CacheParameterGroups.Memcached.html#CacheParameterGroups.Memcached.NodeSpecific\">Cache Node Type-Specific Parameters for Memcached</a> or <a href=\"http://docs.aws.amazon.com/AmazonElastiCache/latest/UserGuide/CacheParameterGroups.Redis.html#CacheParameterGroups.Redis.NodeSpecific\">Cache Node Type-Specific Parameters for Redis</a>. </p>"}, "Engine": {"shape": "String", "documentation": "<p>The name of the cache engine to be used for the cache clusters in this replication group.</p> <p>Default: redis</p>"}, "EngineVersion": {"shape": "String", "documentation": "<p>The version number of the cach engine to be used for the cache clusters in this replication group. To view the supported cache engine versions, use the <i>DescribeCacheEngineVersions</i> operation.</p>"}, "CacheParameterGroupName": {"shape": "String", "documentation": "<p>The name of the parameter group to associate with this replication group. If this argument is omitted, the default cache parameter group for the specified engine is used.</p>"}, "CacheSubnetGroupName": {"shape": "String", "documentation": "<p>The name of the cache subnet group to be used for the replication group.</p>"}, "CacheSecurityGroupNames": {"shape": "CacheSecurityGroupNameList", "documentation": "<p>A list of cache security group names to associate with this replication group.</p>"}, "SecurityGroupIds": {"shape": "SecurityGroupIdsList", "documentation": "<p>One or more Amazon VPC security groups associated with this replication group.</p> <p>Use this parameter only when you are creating a replication group in an Amazon Virtual Private Cloud (VPC).</p>"}, "SnapshotArns": {"shape": "SnapshotArnsList", "documentation": "<p>A single-element string list containing an Amazon Resource Name (ARN) that uniquely identifies a Redis RDB snapshot file stored in Amazon S3. The snapshot file will be used to populate the node group. The Amazon S3 object name in the ARN cannot contain any commas.</p> <p><b>Note:</b> This parameter is only valid if the <code>Engine</code> parameter is <code>redis</code>.</p> <p>Example of an Amazon S3 ARN: <code>arn:aws:s3:::my_bucket/snapshot1.rdb</code></p>"}, "SnapshotName": {"shape": "String", "documentation": "<p>The name of a snapshot from which to restore data into the new node group. The snapshot status changes to <code>restoring</code> while the new node group is being created.</p> <p><b>Note:</b> This parameter is only valid if the <code>Engine</code> parameter is <code>redis</code>.</p>"}, "PreferredMaintenanceWindow": {"shape": "String", "documentation": "<p>The weekly time range (in UTC) during which system maintenance can occur.</p> <p>Example: <code>sun:05:00-sun:09:00</code></p>"}, "Port": {"shape": "IntegerOptional", "documentation": "<p>The port number on which each member of the replication group will accept connections.</p>"}, "NotificationTopicArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon Simple Notification Service (SNS) topic to which notifications will be sent.</p>"}, "AutoMinorVersionUpgrade": {"shape": "BooleanOptional", "documentation": "<p>Determines whether minor engine upgrades will be applied automatically to the node group during the maintenance window. A value of <code>true</code> allows these upgrades to occur; <code>false</code> disables automatic upgrades.</p> <p>Default: <code>true</code></p>"}, "SnapshotRetentionLimit": {"shape": "IntegerOptional", "documentation": "<p>The number of days for which ElastiCache will retain automatic snapshots before deleting them. For example, if you set <code>SnapshotRetentionLimit</code> to 5, then a snapshot that was taken today will be retained for 5 days before being deleted.</p> <p><b>Note:</b> This parameter is only valid if the <code>Engine</code> parameter is <code>redis</code>.</p> <p>Default: 0 (i.e., automatic backups are disabled for this cache cluster).</p>"}, "SnapshotWindow": {"shape": "String", "documentation": "<p>The daily time range (in UTC) during which ElastiCache will begin taking a daily snapshot of your node group.</p> <p>Example: <code>05:00-09:00</code></p> <p>If you do not specify this parameter, then ElastiCache will automatically choose an appropriate time range.</p> <p><b>Note:</b> This parameter is only valid if the <code>Engine</code> parameter is <code>redis</code>.</p>"}}, "documentation": "<p>Represents the input of a <i>CreateReplicationGroup</i> operation.</p>"}, "CreateSnapshotMessage": {"type": "structure", "required": ["CacheClusterId", "SnapshotName"], "members": {"CacheClusterId": {"shape": "String", "documentation": "<p>The identifier of an existing cache cluster. The snapshot will be created from this cache cluster.</p>"}, "SnapshotName": {"shape": "String", "documentation": "<p>A name for the snapshot being created.</p>"}}, "documentation": "<p>Represents the input of a <i>CreateSnapshot</i> operation.</p>"}, "DeleteCacheClusterMessage": {"type": "structure", "required": ["CacheClusterId"], "members": {"CacheClusterId": {"shape": "String", "documentation": "<p>The cache cluster identifier for the cluster to be deleted. This parameter is not case sensitive.</p>"}, "FinalSnapshotIdentifier": {"shape": "String", "documentation": "<p>The user-supplied name of a final cache cluster snapshot. This is the unique name that identifies the snapshot. ElastiCache creates the snapshot, and then deletes the cache cluster immediately afterward.</p>"}}, "documentation": "<p>Represents the input of a <i>DeleteCacheCluster</i> operation.</p>"}, "DeleteCacheParameterGroupMessage": {"type": "structure", "required": ["CacheParameterGroupName"], "members": {"CacheParameterGroupName": {"shape": "String", "documentation": "<p>The name of the cache parameter group to delete.</p>"}}, "documentation": "<p>Represents the input of a <i>DeleteCacheParameterGroup</i> operation.</p>"}, "DeleteCacheSecurityGroupMessage": {"type": "structure", "required": ["CacheSecurityGroupName"], "members": {"CacheSecurityGroupName": {"shape": "String", "documentation": "<p>The name of the cache security group to delete.</p>"}}, "documentation": "<p>Represents the input of a <i>DeleteCacheSecurityGroup</i> operation.</p>"}, "DeleteCacheSubnetGroupMessage": {"type": "structure", "required": ["CacheSubnetGroupName"], "members": {"CacheSubnetGroupName": {"shape": "String", "documentation": "<p>The name of the cache subnet group to delete.</p> <p>Constraints: Must contain no more than 255 alphanumeric characters or hyphens.</p>"}}, "documentation": "<p>Represents the input of a <i>DeleteCacheSubnetGroup</i> operation.</p>"}, "DeleteReplicationGroupMessage": {"type": "structure", "required": ["ReplicationGroupId"], "members": {"ReplicationGroupId": {"shape": "String", "documentation": "<p>The identifier for the cluster to be deleted. This parameter is not case sensitive.</p>"}, "RetainPrimaryCluster": {"shape": "BooleanOptional", "documentation": "<p>If set to <i>true</i>, all of the read replicas will be deleted, but the primary node will be retained.</p>"}, "FinalSnapshotIdentifier": {"shape": "String", "documentation": "<p>The name of a final node group snapshot. ElastiCache creates the snapshot from the primary node in the cluster, rather than one of the replicas; this is to ensure that it captures the freshest data. After the final snapshot is taken, the cluster is immediately deleted.</p>"}}, "documentation": "<p>Represents the input of a <i>DeleteReplicationGroup</i> operation.</p>"}, "DeleteSnapshotMessage": {"type": "structure", "required": ["SnapshotName"], "members": {"SnapshotName": {"shape": "String", "documentation": "<p>The name of the snapshot to be deleted.</p>"}}, "documentation": "<p>Represents the input of a <i>DeleteSnapshot</i> operation.</p>"}, "DescribeCacheClustersMessage": {"type": "structure", "members": {"CacheClusterId": {"shape": "String", "documentation": "<p>The user-supplied cluster identifier. If this parameter is specified, only information about that specific cache cluster is returned. This parameter isn't case sensitive.</p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p>The maximum number of records to include in the response. If more records exist than the specified <code>MaxRecords</code> value, a marker is included in the response so that the remaining results can be retrieved.</p> <p>Default: 100</p> <p>Constraints: minimum 20; maximum 100.</p>"}, "Marker": {"shape": "String", "documentation": "<p>An optional marker returned from a prior request. Use this marker for pagination of results from this operation. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <i>MaxRecords</i>.</p>"}, "ShowCacheNodeInfo": {"shape": "BooleanOptional", "documentation": "<p>An optional flag that can be included in the DescribeCacheCluster request to retrieve information about the individual cache nodes.</p>"}}, "documentation": "<p>Represents the input of a <i>DescribeCacheClusters</i> operation.</p>"}, "DescribeCacheEngineVersionsMessage": {"type": "structure", "members": {"Engine": {"shape": "String", "documentation": "<p>The cache engine to return. Valid values: <code>memcached</code> | <code>redis</code></p>"}, "EngineVersion": {"shape": "String", "documentation": "<p>The cache engine version to return.</p> <p>Example: <code>1.4.14</code></p>"}, "CacheParameterGroupFamily": {"shape": "String", "documentation": "<p>The name of a specific cache parameter group family to return details for.</p> <p>Constraints:</p> <ul> <li>Must be 1 to 255 alphanumeric characters</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p>The maximum number of records to include in the response. If more records exist than the specified <code>MaxRecords</code> value, a marker is included in the response so that the remaining results can be retrieved.</p> <p>Default: 100</p> <p>Constraints: minimum 20; maximum 100.</p>"}, "Marker": {"shape": "String", "documentation": "<p>An optional marker returned from a prior request. Use this marker for pagination of results from this operation. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <i>MaxRecords</i>.</p>"}, "DefaultOnly": {"shape": "Boolean", "documentation": "<p>If <i>true</i>, specifies that only the default version of the specified engine or engine and major version combination is to be returned.</p>"}}, "documentation": "<p>Represents the input of a <i>DescribeCacheEngineVersions</i> operation.</p>"}, "DescribeCacheParameterGroupsMessage": {"type": "structure", "members": {"CacheParameterGroupName": {"shape": "String", "documentation": "<p>The name of a specific cache parameter group to return details for.</p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p>The maximum number of records to include in the response. If more records exist than the specified <code>MaxRecords</code> value, a marker is included in the response so that the remaining results can be retrieved.</p> <p>Default: 100</p> <p>Constraints: minimum 20; maximum 100.</p>"}, "Marker": {"shape": "String", "documentation": "<p>An optional marker returned from a prior request. Use this marker for pagination of results from this operation. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <i>MaxRecords</i>.</p>"}}, "documentation": "<p>Represents the input of a <i>DescribeCacheParameterGroups</i> operation.</p>"}, "DescribeCacheParametersMessage": {"type": "structure", "required": ["CacheParameterGroupName"], "members": {"CacheParameterGroupName": {"shape": "String", "documentation": "<p>The name of a specific cache parameter group to return details for.</p>"}, "Source": {"shape": "String", "documentation": "<p>The parameter types to return.</p> <p>Valid values: <code>user</code> | <code>system</code> | <code>engine-default</code></p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p>The maximum number of records to include in the response. If more records exist than the specified <code>MaxRecords</code> value, a marker is included in the response so that the remaining results can be retrieved.</p> <p>Default: 100</p> <p>Constraints: minimum 20; maximum 100.</p>"}, "Marker": {"shape": "String", "documentation": "<p>An optional marker returned from a prior request. Use this marker for pagination of results from this operation. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <i>MaxRecords</i>.</p>"}}, "documentation": "<p>Represents the input of a <i>DescribeCacheParameters</i> operation.</p>"}, "DescribeCacheSecurityGroupsMessage": {"type": "structure", "members": {"CacheSecurityGroupName": {"shape": "String", "documentation": "<p>The name of the cache security group to return details for.</p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p>The maximum number of records to include in the response. If more records exist than the specified <code>MaxRecords</code> value, a marker is included in the response so that the remaining results can be retrieved.</p> <p>Default: 100</p> <p>Constraints: minimum 20; maximum 100.</p>"}, "Marker": {"shape": "String", "documentation": "<p>An optional marker returned from a prior request. Use this marker for pagination of results from this operation. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <i>MaxRecords</i>.</p>"}}, "documentation": "<p>Represents the input of a <i>DescribeCacheSecurityGroups</i> operation.</p>"}, "DescribeCacheSubnetGroupsMessage": {"type": "structure", "members": {"CacheSubnetGroupName": {"shape": "String", "documentation": "<p>The name of the cache subnet group to return details for.</p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p>The maximum number of records to include in the response. If more records exist than the specified <code>MaxRecords</code> value, a marker is included in the response so that the remaining results can be retrieved.</p> <p>Default: 100</p> <p>Constraints: minimum 20; maximum 100.</p>"}, "Marker": {"shape": "String", "documentation": "<p>An optional marker returned from a prior request. Use this marker for pagination of results from this operation. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <i>MaxRecords</i>.</p>"}}, "documentation": "<p>Represents the input of a <i>DescribeCacheSubnetGroups</i> operation.</p>"}, "DescribeEngineDefaultParametersMessage": {"type": "structure", "required": ["CacheParameterGroupFamily"], "members": {"CacheParameterGroupFamily": {"shape": "String", "documentation": "<p>The name of the cache parameter group family. Valid values are: <code>memcached1.4</code> | <code>redis2.6</code> | <code>redis2.8</code></p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p>The maximum number of records to include in the response. If more records exist than the specified <code>MaxRecords</code> value, a marker is included in the response so that the remaining results can be retrieved.</p> <p>Default: 100</p> <p>Constraints: minimum 20; maximum 100.</p>"}, "Marker": {"shape": "String", "documentation": "<p>An optional marker returned from a prior request. Use this marker for pagination of results from this operation. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <i>MaxRecords</i>.</p>"}}, "documentation": "<p>Represents the input of a <i>DescribeEngineDefaultParameters</i> operation.</p>"}, "DescribeEventsMessage": {"type": "structure", "members": {"SourceIdentifier": {"shape": "String", "documentation": "<p>The identifier of the event source for which events will be returned. If not specified, then all sources are included in the response.</p>"}, "SourceType": {"shape": "SourceType", "documentation": "<p>The event source to retrieve events for. If no value is specified, all events are returned.</p> <p>Valid values are: <code>cache-cluster</code> | <code>cache-parameter-group</code> | <code>cache-security-group</code> | <code>cache-subnet-group</code></p>"}, "StartTime": {"shape": "TStamp", "documentation": "<p>The beginning of the time interval to retrieve events for, specified in ISO 8601 format.</p>"}, "EndTime": {"shape": "TStamp", "documentation": "<p>The end of the time interval for which to retrieve events, specified in ISO 8601 format.</p>"}, "Duration": {"shape": "IntegerOptional", "documentation": "<p>The number of minutes' worth of events to retrieve.</p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p>The maximum number of records to include in the response. If more records exist than the specified <code>MaxRecords</code> value, a marker is included in the response so that the remaining results can be retrieved.</p> <p>Default: 100</p> <p>Constraints: minimum 20; maximum 100.</p>"}, "Marker": {"shape": "String", "documentation": "<p>An optional marker returned from a prior request. Use this marker for pagination of results from this operation. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <i>MaxRecords</i>.</p>"}}, "documentation": "<p>Represents the input of a <i>DescribeEvents</i> operation.</p>"}, "DescribeReplicationGroupsMessage": {"type": "structure", "members": {"ReplicationGroupId": {"shape": "String", "documentation": "<p>The identifier for the replication group to be described. This parameter is not case sensitive.</p> <p>If you do not specify this parameter, information about all replication groups is returned.</p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p>The maximum number of records to include in the response. If more records exist than the specified <code>MaxRecords</code> value, a marker is included in the response so that the remaining results can be retrieved.</p> <p>Default: 100</p> <p>Constraints: minimum 20; maximum 100.</p>"}, "Marker": {"shape": "String", "documentation": "<p>An optional marker returned from a prior request. Use this marker for pagination of results from this operation. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <i>MaxRecords</i>.</p>"}}, "documentation": "<p>Represents the input of a <i>DescribeReplicationGroups</i> operation.</p>"}, "DescribeReservedCacheNodesMessage": {"type": "structure", "members": {"ReservedCacheNodeId": {"shape": "String", "documentation": "<p>The reserved cache node identifier filter value. Use this parameter to show only the reservation that matches the specified reservation ID.</p>"}, "ReservedCacheNodesOfferingId": {"shape": "String", "documentation": "<p>The offering identifier filter value. Use this parameter to show only purchased reservations matching the specified offering identifier.</p>"}, "CacheNodeType": {"shape": "String", "documentation": "<p>The cache node type filter value. Use this parameter to show only those reservations matching the specified cache node type.</p>"}, "Duration": {"shape": "String", "documentation": "<p>The duration filter value, specified in years or seconds. Use this parameter to show only reservations for this duration.</p> <p>Valid Values: <code>1 | 3 | 31536000 | 94608000</code></p>"}, "ProductDescription": {"shape": "String", "documentation": "<p>The product description filter value. Use this parameter to show only those reservations matching the specified product description.</p>"}, "OfferingType": {"shape": "String", "documentation": "<p>The offering type filter value. Use this parameter to show only the available offerings matching the specified offering type.</p> <p>Valid values: <code>\"Light Utilization\" | \"Medium Utilization\" | \"Heavy Utilization\"</code></p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p>The maximum number of records to include in the response. If more records exist than the specified <code>MaxRecords</code> value, a marker is included in the response so that the remaining results can be retrieved.</p> <p>Default: 100</p> <p>Constraints: minimum 20; maximum 100.</p>"}, "Marker": {"shape": "String", "documentation": "<p>An optional marker returned from a prior request. Use this marker for pagination of results from this operation. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <i>MaxRecords</i>.</p>"}}, "documentation": "<p>Represents the input of a <i>DescribeReservedCacheNodes</i> operation.</p>"}, "DescribeReservedCacheNodesOfferingsMessage": {"type": "structure", "members": {"ReservedCacheNodesOfferingId": {"shape": "String", "documentation": "<p>The offering identifier filter value. Use this parameter to show only the available offering that matches the specified reservation identifier.</p> <p>Example: <code>438012d3-4052-4cc7-b2e3-8d3372e0e706</code></p>"}, "CacheNodeType": {"shape": "String", "documentation": "<p>The cache node type filter value. Use this parameter to show only the available offerings matching the specified cache node type.</p>"}, "Duration": {"shape": "String", "documentation": "<p>Duration filter value, specified in years or seconds. Use this parameter to show only reservations for a given duration.</p> <p>Valid Values: <code>1 | 3 | 31536000 | 94608000</code></p>"}, "ProductDescription": {"shape": "String", "documentation": "<p>The product description filter value. Use this parameter to show only the available offerings matching the specified product description.</p>"}, "OfferingType": {"shape": "String", "documentation": "<p>The offering type filter value. Use this parameter to show only the available offerings matching the specified offering type.</p> <p>Valid Values: <code>\"Light Utilization\" | \"Medium Utilization\" | \"Heavy Utilization\"</code></p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p>The maximum number of records to include in the response. If more records exist than the specified <code>MaxRecords</code> value, a marker is included in the response so that the remaining results can be retrieved.</p> <p>Default: 100</p> <p>Constraints: minimum 20; maximum 100.</p>"}, "Marker": {"shape": "String", "documentation": "<p>An optional marker returned from a prior request. Use this marker for pagination of results from this operation. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <i>MaxRecords</i>.</p>"}}, "documentation": "<p>Represents the input of a <i>DescribeReservedCacheNodesOfferings</i> operation.</p>"}, "DescribeSnapshotsListMessage": {"type": "structure", "members": {"Marker": {"shape": "String", "documentation": "<p>An optional marker returned from a prior request. Use this marker for pagination of results from this operation. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <i>MaxRecords</i>.</p>"}, "Snapshots": {"shape": "SnapshotList", "documentation": "<p>A list of snapshots. Each item in the list contains detailed information about one snapshot.</p>"}}, "documentation": "<p>Represents the output of a <i>DescribeSnapshots</i> operation.</p>"}, "DescribeSnapshotsMessage": {"type": "structure", "members": {"CacheClusterId": {"shape": "String", "documentation": "<p>A user-supplied cluster identifier. If this parameter is specified, only snapshots associated with that specific cache cluster will be described.</p>"}, "SnapshotName": {"shape": "String", "documentation": "<p>A user-supplied name of the snapshot. If this parameter is specified, only this snapshot will be described.</p>"}, "SnapshotSource": {"shape": "String", "documentation": "<p>If set to <code>system</code>, the output shows snapshots that were automatically created by ElastiCache. If set to <code>user</code> the output shows snapshots that were manually created. If omitted, the output shows both automatically and manually created snapshots.</p>"}, "Marker": {"shape": "String", "documentation": "<p>An optional marker returned from a prior request. Use this marker for pagination of results from this operation. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <i>MaxRecords</i>.</p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p>The maximum number of records to include in the response. If more records exist than the specified <code>MaxRecords</code> value, a marker is included in the response so that the remaining results can be retrieved.</p> <p>Default: 50</p> <p>Constraints: minimum 20; maximum 50.</p>"}}, "documentation": "<p>Represents the input of a <i>DescribeSnapshotsMessage</i> operation.</p>"}, "Double": {"type": "double"}, "EC2SecurityGroup": {"type": "structure", "members": {"Status": {"shape": "String", "documentation": "<p>The status of the Amazon EC2 security group.</p>"}, "EC2SecurityGroupName": {"shape": "String", "documentation": "<p>The name of the Amazon EC2 security group.</p>"}, "EC2SecurityGroupOwnerId": {"shape": "String", "documentation": "<p>The AWS account ID of the Amazon EC2 security group owner.</p>"}}, "documentation": "<p>Provides ownership and status information for an Amazon EC2 security group.</p>"}, "EC2SecurityGroupList": {"type": "list", "member": {"shape": "EC2SecurityGroup", "locationName": "EC2SecurityGroup"}}, "Endpoint": {"type": "structure", "members": {"Address": {"shape": "String", "documentation": "<p>The DNS hostname of the cache node.</p>"}, "Port": {"shape": "Integer", "documentation": "<p>The port number that the cache engine is listening on.</p>"}}, "documentation": "<p>Represents the information required for client programs to connect to a cache node.</p>"}, "EngineDefaults": {"type": "structure", "members": {"CacheParameterGroupFamily": {"shape": "String", "documentation": "<p>Specifies the name of the cache parameter group family to which the engine default parameters apply.</p>"}, "Marker": {"shape": "String", "documentation": "<p>Provides an identifier to allow retrieval of paginated results.</p>"}, "Parameters": {"shape": "ParametersList", "documentation": "<p>Contains a list of engine default parameters.</p>"}, "CacheNodeTypeSpecificParameters": {"shape": "CacheNodeTypeSpecificParametersList", "documentation": "<p>A list of parameters specific to a particular cache node type. Each element in the list contains detailed information about one parameter.</p>"}}, "wrapper": true, "documentation": "<p>Represents the output of a <i>DescribeEngineDefaultParameters</i> operation.</p>"}, "Event": {"type": "structure", "members": {"SourceIdentifier": {"shape": "String", "documentation": "<p>The identifier for the source of the event. For example, if the event occurred at the cache cluster level, the identifier would be the name of the cache cluster.</p>"}, "SourceType": {"shape": "SourceType", "documentation": "<p>Specifies the origin of this event - a cache cluster, a parameter group, a security group, etc.</p>"}, "Message": {"shape": "String", "documentation": "<p>The text of the event.</p>"}, "Date": {"shape": "TStamp", "documentation": "<p>The date and time when the event occurred.</p>"}}, "documentation": "<p>Represents a single occurrence of something interesting within the system. Some examples of events are creating a cache cluster, adding or removing a cache node, or rebooting a node.</p>"}, "EventList": {"type": "list", "member": {"shape": "Event", "locationName": "Event"}}, "EventsMessage": {"type": "structure", "members": {"Marker": {"shape": "String", "documentation": "<p>Provides an identifier to allow retrieval of paginated results.</p>"}, "Events": {"shape": "EventList", "documentation": "<p>A list of events. Each element in the list contains detailed information about one event.</p>"}}, "documentation": "<p>Represents the output of a <i>DescribeEvents</i> operation.</p>"}, "InsufficientCacheClusterCapacityFault": {"type": "structure", "members": {}, "error": {"code": "InsufficientCacheClusterCapacity", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache node type is not available in the specified Availability Zone.</p>"}, "Integer": {"type": "integer"}, "IntegerOptional": {"type": "integer"}, "InvalidCacheClusterStateFault": {"type": "structure", "members": {}, "error": {"code": "InvalidCacheClusterState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache cluster is not in the <i>available</i> state.</p>"}, "InvalidCacheParameterGroupStateFault": {"type": "structure", "members": {}, "error": {"code": "InvalidCacheParameterGroupState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The current state of the cache parameter group does not allow the requested action to occur. </p>"}, "InvalidCacheSecurityGroupStateFault": {"type": "structure", "members": {}, "error": {"code": "InvalidCacheSecurityGroupState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The current state of the cache security group does not allow deletion.</p>"}, "InvalidParameterCombinationException": {"type": "structure", "members": {"message": {"shape": "AwsQueryErrorMessage", "documentation": "<p>Two or more parameters that must not be used together were used together.</p>"}}, "error": {"code": "InvalidParameterCombination", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>Two or more incompatible parameters were specified.</p>"}, "InvalidParameterValueException": {"type": "structure", "members": {"message": {"shape": "AwsQueryErrorMessage", "documentation": "<p>A parameter value is invalid.</p>"}}, "error": {"code": "InvalidParameterValue", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The value for a parameter is invalid.</p>"}, "InvalidReplicationGroupStateFault": {"type": "structure", "members": {}, "error": {"code": "InvalidReplicationGroupState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested replication group is not in the <i>available</i> state.</p>"}, "InvalidSnapshotStateFault": {"type": "structure", "members": {}, "error": {"code": "InvalidSnapshotState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The current state of the snapshot does not allow the requested action to occur.</p>"}, "InvalidSubnet": {"type": "structure", "members": {}, "error": {"code": "InvalidSubnet", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>An invalid subnet identifier was specified.</p>"}, "InvalidVPCNetworkStateFault": {"type": "structure", "members": {}, "error": {"code": "InvalidVPCNetworkStateFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The VPC network is in an invalid state.</p>"}, "ModifyCacheClusterMessage": {"type": "structure", "required": ["CacheClusterId"], "members": {"CacheClusterId": {"shape": "String", "documentation": "<p>The cache cluster identifier. This value is stored as a lowercase string.</p>"}, "NumCacheNodes": {"shape": "IntegerOptional", "documentation": "<p>The number of cache nodes that the cache cluster should have. If the value for <code>NumCacheNodes</code> is greater than the sum of the number of current cache nodes and the number of cache nodes pending creation (which may be zero), then more nodes will be added. If the value is less than the number of existing cache nodes, then nodes will be removed. If the value is equal to the number of current cache nodes, then any pending add or remove requests are canceled.</p> <p>If you are removing cache nodes, you must use the <code>CacheNodeIdsToRemove</code> parameter to provide the IDs of the specific cache nodes to remove.</p> <p>For cache clusters running Redis, the value of <code>NumCacheNodes</code>must be 1.</p> <p><b>Note:</b><br>Adding or removing Memcached cache nodes can be applied immediately or as a pending action. See <code>ApplyImmediately</code>.<br> A pending action to modify the number of cache nodes in a cluster during its maintenance window, whether by adding or removing nodes in accordance with the scale out architecture, is not queued. The customer's latest request to add or remove nodes to the cluster overrides any previous pending actions to modify the number of cache nodes in the cluster. For example, a request to remove 2 nodes would override a previous pending action to remove 3 nodes. Similarly, a request to add 2 nodes would override a previous pending action to remove 3 nodes and vice versa. As Memcached cache nodes may now be provisioned in different Availability Zones with flexible cache node placement, a request to add nodes does not automatically override a previous pending action to add nodes. The customer can modify the previous pending action to add more nodes or explicitly cancel the pending request and retry the new request. To cancel pending actions to modify the number of cache nodes in a cluster, use the <code>ModifyCacheCluster</code> request and set <code>NumCacheNodes</code> equal to the number of cache nodes currently in the cache cluster.</p>"}, "CacheNodeIdsToRemove": {"shape": "CacheNodeIdsList", "documentation": "<p>A list of cache node IDs to be removed. A node ID is a numeric identifier (0001, 0002, etc.). This parameter is only valid when NumCacheNodes is less than the existing number of cache nodes. The number of cache node IDs supplied in this parameter must match the difference between the existing number of cache nodes in the cluster or pending cache nodes, whichever is greater, and the value of <i>NumCacheNodes</i> in the request.</p> <p>For example: If you have 3 active cache nodes, 7 pending cache nodes, and the number of cache nodes in this <code>ModifyCacheCluser</code> call is 5, you must list 2 (7 - 5) cache node IDs to remove.</p>"}, "AZMode": {"shape": "AZMode", "documentation": "<p>Specifies whether the new nodes in this Memcached cache cluster are all created in a single Availability Zone or created across multiple Availability Zones.</p> <p>Valid values: <code>single-az</code> | <code>cross-az</code>.</p> <p>This option is only supported for Memcached cache clusters.</p>"}, "NewAvailabilityZones": {"shape": "PreferredAvailabilityZoneList", "documentation": "<p>The list of Availability Zones where the new Memcached cache nodes will be created.</p> <p>This parameter is only valid when <code>NumCacheNodes</code> in the request is greater than the sum of the number of active cache nodes and the number of cache nodes pending creation (which may be zero). The number of Availability Zones supplied in this list must match the cache nodes being added in this request.</p> <p>This option is only supported on Memcached clusters.</p> <p>Scenarios: <ul> <li> <b>Scenario 1:</b> You have 3 active nodes and wish to add 2 nodes.<br> Specify <code>NumCacheNodes=5</code> (3 + 2) and optionally specify two Availability Zones for the two new nodes.</li> <li> <b>Scenario 2:</b> You have 3 active nodes and 2 nodes pending creation (from the scenario 1 call) and want to add 1 more node.<br> Specify <code>NumCacheNodes=6</code> ((3 + 2) + 1)</li> and optionally specify an Availability Zone for the new node. <li> <b>Scenario 3:</b> You want to cancel all pending actions.<br> Specify <code>NumCacheNodes=3</code> to cancel all pending actions.</li> </ul> </p> <p>The Availability Zone placement of nodes pending creation cannot be modified. If you wish to cancel any nodes pending creation, add 0 nodes by setting <code>NumCacheNodes</code> to the number of current nodes.</p> <p>If <code>cross-az</code> is specified, existing Memcached nodes remain in their current Availability Zone. Only newly created nodes can be located in different Availability Zones. For guidance on how to move existing Memcached nodes to different Availability Zones, see the <b>Availability Zone Considerations</b> section of <a href=\"http://docs.aws.amazon.com/AmazonElastiCache/latest/UserGuide/CacheNode.Memcached.html\">Cache Node Considerations for Memcached</a>.</p> <p><b>Impact of new add/remove requests upon pending requests</b></p> <table> <tr> <th>Scenarios</th> <th>Pending Operation</th> <th>New Request</th> <th>Results</th> </tr> <tr> <td>Scenario-1</td> <td>Delete</td> <td>Delete</td> <td>The new delete, pending or immediate, replaces the pending delete.</td> </tr> <tr> <td>Scenario-2</td> <td>Delete</td> <td>Create</td> <td>The new create, pending or immediate, replaces the pending delete.</td> </tr> <tr> <td>Scenario-3</td> <td>Create</td> <td>Delete</td> <td>The new delete, pending or immediate, replaces the pending create.</td> </tr> <tr> <td>Scenario-4</td> <td>Create</td> <td>Create</td> <td>The new create is added to the pending create.<br/> <b>Important:</b><br/>If the new create request is <b>Apply Immediately - Yes</b>, all creates are performed immediately. If the new create request is <b>Apply Immediately - No</b>, all creates are pending.</td> </tr> </table> <p>Example: <code>NewAvailabilityZones.member.1=us-east-1a&amp;NewAvailabilityZones.member.2=us-east-1b&amp;NewAvailabilityZones.member.3=us-east-1d</code></p>"}, "CacheSecurityGroupNames": {"shape": "CacheSecurityGroupNameList", "documentation": "<p>A list of cache security group names to authorize on this cache cluster. This change is asynchronously applied as soon as possible.</p> <p>This parameter can be used only with clusters that are created outside of an Amazon Virtual Private Cloud (VPC).</p> <p>Constraints: Must contain no more than 255 alphanumeric characters. Must not be \"Default\".</p>"}, "SecurityGroupIds": {"shape": "SecurityGroupIdsList", "documentation": "<p>Specifies the VPC Security Groups associated with the cache cluster.</p> <p>This parameter can be used only with clusters that are created in an Amazon Virtual Private Cloud (VPC).</p>"}, "PreferredMaintenanceWindow": {"shape": "String", "documentation": "<p>The weekly time range (in UTC) during which system maintenance can occur. Note that system maintenance may result in an outage. This change is made immediately. If you are moving this window to the current time, there must be at least 120 minutes between the current time and end of the window to ensure that pending changes are applied.</p>"}, "NotificationTopicArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon SNS topic to which notifications will be sent.</p>"}, "CacheParameterGroupName": {"shape": "String", "documentation": "<p>The name of the cache parameter group to apply to this cache cluster. This change is asynchronously applied as soon as possible for parameters when the <i>ApplyImmediately</i> parameter is specified as <i>true</i> for this request.</p>"}, "NotificationTopicStatus": {"shape": "String", "documentation": "<p>The status of the Amazon SNS notification topic. Notifications are sent only if the status is <i>active</i>.</p> <p>Valid values: <code>active</code> | <code>inactive</code></p>"}, "ApplyImmediately": {"shape": "Boolean", "documentation": "<p>If <code>true</code>, this parameter causes the modifications in this request and any pending modifications to be applied, asynchronously and as soon as possible, regardless of the <i>PreferredMaintenanceWindow</i> setting for the cache cluster.</p> <p>If <code>false</code>, then changes to the cache cluster are applied on the next maintenance reboot, or the next failure reboot, whichever occurs first.</p> <important>If you perform a <code>ModifyCacheCluster</code> before a pending modification is applied, the pending modification is replaced by the newer modification.</important> <p>Valid values: <code>true</code> | <code>false</code></p> <p>Default: <code>false</code></p>"}, "EngineVersion": {"shape": "String", "documentation": "<p>The upgraded version of the cache engine to be run on the cache nodes.</p>"}, "AutoMinorVersionUpgrade": {"shape": "BooleanOptional", "documentation": "<p>If <code>true</code>, then minor engine upgrades will be applied automatically to the cache cluster during the maintenance window.</p> <p>Valid values: <code>true</code> | <code>false</code></p> <p>Default: <code>true</code></p>"}, "SnapshotRetentionLimit": {"shape": "IntegerOptional", "documentation": "<p>The number of days for which ElastiCache will retain automatic cache cluster snapshots before deleting them. For example, if you set <i>SnapshotRetentionLimit</i> to 5, then a snapshot that was taken today will be retained for 5 days before being deleted.</p> <p><b>Important</b><br>If the value of SnapshotRetentionLimit is set to zero (0), backups are turned off.</p>"}, "SnapshotWindow": {"shape": "String", "documentation": "<p>The daily time range (in UTC) during which ElastiCache will begin taking a daily snapshot of your cache cluster. </p>"}}, "documentation": "<p>Represents the input of a <i>ModifyCacheCluster</i> operation.</p>"}, "ModifyCacheParameterGroupMessage": {"type": "structure", "required": ["CacheParameterGroupName", "ParameterNameValues"], "members": {"CacheParameterGroupName": {"shape": "String", "documentation": "<p>The name of the cache parameter group to modify.</p>"}, "ParameterNameValues": {"shape": "ParameterNameValueList", "documentation": "<p>An array of parameter names and values for the parameter update. You must supply at least one parameter name and value; subsequent arguments are optional. A maximum of 20 parameters may be modified per request.</p>"}}, "documentation": "<p>Represents the input of a <i>ModifyCacheParameterGroup</i> operation.</p>"}, "ModifyCacheSubnetGroupMessage": {"type": "structure", "required": ["CacheSubnetGroupName"], "members": {"CacheSubnetGroupName": {"shape": "String", "documentation": "<p>The name for the cache subnet group. This value is stored as a lowercase string.</p> <p>Constraints: Must contain no more than 255 alphanumeric characters or hyphens.</p> <p>Example: <code>mysubnetgroup</code></p>"}, "CacheSubnetGroupDescription": {"shape": "String", "documentation": "<p>A description for the cache subnet group.</p>"}, "SubnetIds": {"shape": "SubnetIdentifierList", "documentation": "<p>The EC2 subnet IDs for the cache subnet group.</p>"}}, "documentation": "<p>Represents the input of a <i>ModifyCacheSubnetGroup</i> operation.</p>"}, "ModifyReplicationGroupMessage": {"type": "structure", "required": ["ReplicationGroupId"], "members": {"ReplicationGroupId": {"shape": "String", "documentation": "<p>The identifier of the replication group to modify.</p>"}, "ReplicationGroupDescription": {"shape": "String", "documentation": "<p>A description for the replication group. Maximum length is 255 characters.</p>"}, "PrimaryClusterId": {"shape": "String", "documentation": "<p>If this parameter is specified, ElastiCache will promote each of the cache clusters in the specified replication group to the primary role. The nodes of all other cache clusters in the replication group will be read replicas.</p>"}, "SnapshottingClusterId": {"shape": "String", "documentation": "<p>The cache cluster ID that will be used as the daily snapshot source for the replication group.</p>"}, "AutomaticFailoverEnabled": {"shape": "BooleanOptional", "documentation": "<p>Whether a read replica will be automatically promoted to read/write primary if the existing primary encounters a failure.</p> <p>Valid values: <code>true</code> | <code>false</code></p>"}, "CacheSecurityGroupNames": {"shape": "CacheSecurityGroupNameList", "documentation": "<p>A list of cache security group names to authorize for the clusters in this replication group. This change is asynchronously applied as soon as possible.</p> <p>This parameter can be used only with replication group containing cache clusters running outside of an Amazon Virtual Private Cloud (VPC).</p> <p>Constraints: Must contain no more than 255 alphanumeric characters. Must not be \"Default\".</p>"}, "SecurityGroupIds": {"shape": "SecurityGroupIdsList", "documentation": "<p>Specifies the VPC Security Groups associated with the cache clusters in the replication group.</p> <p>This parameter can be used only with replication group containing cache clusters running in an Amazon Virtual Private Cloud (VPC).</p>"}, "PreferredMaintenanceWindow": {"shape": "String", "documentation": "<p>The weekly time range (in UTC) during which replication group system maintenance can occur. Note that system maintenance may result in an outage. This change is made immediately. If you are moving this window to the current time, there must be at least 120 minutes between the current time and end of the window to ensure that pending changes are applied.</p>"}, "NotificationTopicArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon SNS topic to which notifications will be sent.</p>"}, "CacheParameterGroupName": {"shape": "String", "documentation": "<p>The name of the cache parameter group to apply to all of the clusters in this replication group. This change is asynchronously applied as soon as possible for parameters when the <i>ApplyImmediately</i> parameter is specified as <i>true</i> for this request.</p>"}, "NotificationTopicStatus": {"shape": "String", "documentation": "<p>The status of the Amazon SNS notification topic for the replication group. Notifications are sent only if the status is <i>active</i>.</p> <p>Valid values: <code>active</code> | <code>inactive</code></p>"}, "ApplyImmediately": {"shape": "Boolean", "documentation": "<p>If <code>true</code>, this parameter causes the modifications in this request and any pending modifications to be applied, asynchronously and as soon as possible, regardless of the <i>PreferredMaintenanceWindow</i> setting for the replication group.</p> <p>If <code>false</code>, then changes to the nodes in the replication group are applied on the next maintenance reboot, or the next failure reboot, whichever occurs first.</p> <p>Valid values: <code>true</code> | <code>false</code></p> <p>Default: <code>false</code></p>"}, "EngineVersion": {"shape": "String", "documentation": "<p>The upgraded version of the cache engine to be run on the cache clusters in the replication group.</p>"}, "AutoMinorVersionUpgrade": {"shape": "BooleanOptional", "documentation": "<p>Determines whether minor engine upgrades will be applied automatically to all of the clusters in the replication group during the maintenance window. A value of <code>true</code> allows these upgrades to occur; <code>false</code> disables automatic upgrades.</p>"}, "SnapshotRetentionLimit": {"shape": "IntegerOptional", "documentation": "<p>The number of days for which ElastiCache will retain automatic node group snapshots before deleting them. For example, if you set <i>SnapshotRetentionLimit</i> to 5, then a snapshot that was taken today will be retained for 5 days before being deleted.</p> <p><b>Important</b><br>If the value of SnapshotRetentionLimit is set to zero (0), backups are turned off.</p>"}, "SnapshotWindow": {"shape": "String", "documentation": "<p>The daily time range (in UTC) during which ElastiCache will begin taking a daily snapshot of the node group specified by <i>SnapshottingClusterId</i>.</p> <p>Example: <code>05:00-09:00</code></p> <p>If you do not specify this parameter, then ElastiCache will automatically choose an appropriate time range.</p>"}}, "documentation": "<p>Represents the input of a <i>ModifyReplicationGroups</i> operation.</p>"}, "NodeGroup": {"type": "structure", "members": {"NodeGroupId": {"shape": "String", "documentation": "<p>The identifier for the node group. A replication group contains only one node group; therefore, the node group ID is 0001.</p>"}, "Status": {"shape": "String", "documentation": "<p>The current state of this replication group - <i>creating</i>, <i>available</i>, etc.</p>"}, "PrimaryEndpoint": {"shape": "Endpoint"}, "NodeGroupMembers": {"shape": "NodeGroupMemberList", "documentation": "<p>A list containing information about individual nodes within the node group.</p>"}}, "documentation": "<p>Represents a collection of cache nodes in a replication group.</p>"}, "NodeGroupList": {"type": "list", "member": {"shape": "NodeGroup", "locationName": "NodeGroup"}}, "NodeGroupMember": {"type": "structure", "members": {"CacheClusterId": {"shape": "String", "documentation": "<p>The ID of the cache cluster to which the node belongs.</p>"}, "CacheNodeId": {"shape": "String", "documentation": "<p>The ID of the node within its cache cluster. A node ID is a numeric identifier (0001, 0002, etc.).</p>"}, "ReadEndpoint": {"shape": "Endpoint"}, "PreferredAvailabilityZone": {"shape": "String", "documentation": "<p>The name of the Availability Zone in which the node is located.</p>"}, "CurrentRole": {"shape": "String", "documentation": "<p>The role that is currently assigned to the node - <i>primary</i> or <i>replica</i>.</p>"}}, "documentation": "<p>Represents a single node within a node group.</p>"}, "NodeGroupMemberList": {"type": "list", "member": {"shape": "NodeGroupMember", "locationName": "NodeGroupMember"}}, "NodeQuotaForClusterExceededFault": {"type": "structure", "members": {}, "error": {"code": "NodeQuotaForClusterExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the allowed number of cache nodes in a single cache cluster.</p>"}, "NodeQuotaForCustomerExceededFault": {"type": "structure", "members": {}, "error": {"code": "NodeQuotaForCustomerExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the allowed number of cache nodes per customer. </p>"}, "NodeSnapshot": {"type": "structure", "members": {"CacheNodeId": {"shape": "String", "documentation": "<p>The cache node identifier for the node in the source cache cluster.</p>"}, "CacheSize": {"shape": "String", "documentation": "<p>The size of the cache on the source cache node.</p>"}, "CacheNodeCreateTime": {"shape": "TStamp", "documentation": "<p>The date and time when the cache node was created in the source cache cluster.</p>"}, "SnapshotCreateTime": {"shape": "TStamp", "documentation": "<p>The date and time when the source node's metadata and cache data set was obtained for the snapshot.</p>"}}, "wrapper": true, "documentation": "<p>Represents an individual cache node in a snapshot of a cache cluster.</p>"}, "NodeSnapshotList": {"type": "list", "member": {"shape": "NodeSnapshot", "locationName": "NodeSnapshot"}}, "NotificationConfiguration": {"type": "structure", "members": {"TopicArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the topic.</p>"}, "TopicStatus": {"shape": "String", "documentation": "<p>The current state of the topic.</p>"}}, "documentation": "<p>Describes a notification topic and its status. Notification topics are used for publishing ElastiCache events to subscribers using Amazon Simple Notification Service (SNS).</p>"}, "Parameter": {"type": "structure", "members": {"ParameterName": {"shape": "String", "documentation": "<p>The name of the parameter.</p>"}, "ParameterValue": {"shape": "String", "documentation": "<p>The value of the parameter.</p>"}, "Description": {"shape": "String", "documentation": "<p>A description of the parameter.</p>"}, "Source": {"shape": "String", "documentation": "<p>The source of the parameter.</p>"}, "DataType": {"shape": "String", "documentation": "<p>The valid data type for the parameter.</p>"}, "AllowedValues": {"shape": "String", "documentation": "<p>The valid range of values for the parameter.</p>"}, "IsModifiable": {"shape": "Boolean", "documentation": "<p>Indicates whether (<code>true</code>) or not (<code>false</code>) the parameter can be modified. Some parameters have security or operational implications that prevent them from being changed.</p>"}, "MinimumEngineVersion": {"shape": "String", "documentation": "<p>The earliest cache engine version to which the parameter can apply.</p>"}}, "documentation": "<p>Describes an individual setting that controls some aspect of ElastiCache behavior.</p>"}, "ParameterNameValue": {"type": "structure", "members": {"ParameterName": {"shape": "String", "documentation": "<p>The name of the parameter.</p>"}, "ParameterValue": {"shape": "String", "documentation": "<p>The value of the parameter.</p>"}}, "documentation": "<p>Describes a name-value pair that is used to update the value of a parameter.</p>"}, "ParameterNameValueList": {"type": "list", "member": {"shape": "ParameterNameValue", "locationName": "ParameterNameValue"}}, "ParametersList": {"type": "list", "member": {"shape": "Parameter", "locationName": "Parameter"}}, "PendingAutomaticFailoverStatus": {"type": "string", "enum": ["enabled", "disabled"]}, "PendingModifiedValues": {"type": "structure", "members": {"NumCacheNodes": {"shape": "IntegerOptional", "documentation": "<p>The new number of cache nodes for the cache cluster.</p>"}, "CacheNodeIdsToRemove": {"shape": "CacheNodeIdsList", "documentation": "<p>A list of cache node IDs that are being removed (or will be removed) from the cache cluster. A node ID is a numeric identifier (0001, 0002, etc.).</p>"}, "EngineVersion": {"shape": "String", "documentation": "<p>The new cache engine version that the cache cluster will run.</p>"}}, "documentation": "<p>A group of settings that will be applied to the cache cluster in the future, or that are currently being applied.</p>"}, "PreferredAvailabilityZoneList": {"type": "list", "member": {"shape": "String", "locationName": "PreferredAvailabilityZone"}}, "PurchaseReservedCacheNodesOfferingMessage": {"type": "structure", "required": ["ReservedCacheNodesOfferingId"], "members": {"ReservedCacheNodesOfferingId": {"shape": "String", "documentation": "<p>The ID of the reserved cache node offering to purchase.</p> <p>Example: 438012d3-4052-4cc7-b2e3-8d3372e0e706</p>"}, "ReservedCacheNodeId": {"shape": "String", "documentation": "<p>A customer-specified identifier to track this reservation.</p> <p>Example: myreservationID</p>"}, "CacheNodeCount": {"shape": "IntegerOptional", "documentation": "<p>The number of cache node instances to reserve.</p> <p>Default: <code>1</code></p>"}}, "documentation": "<p>Represents the input of a <i>PurchaseReservedCacheNodesOffering</i> operation.</p>"}, "RebootCacheClusterMessage": {"type": "structure", "required": ["CacheClusterId", "CacheNodeIdsToReboot"], "members": {"CacheClusterId": {"shape": "String", "documentation": "<p>The cache cluster identifier. This parameter is stored as a lowercase string.</p>"}, "CacheNodeIdsToReboot": {"shape": "CacheNodeIdsList", "documentation": "<p>A list of cache node IDs to reboot. A node ID is a numeric identifier (0001, 0002, etc.). To reboot an entire cache cluster, specify all of the cache node IDs.</p>"}}, "documentation": "<p>Represents the input of a <i>RebootCacheCluster</i> operation.</p>"}, "RecurringCharge": {"type": "structure", "members": {"RecurringChargeAmount": {"shape": "Double", "documentation": "<p>The monetary amount of the recurring charge.</p>"}, "RecurringChargeFrequency": {"shape": "String", "documentation": "<p>The frequency of the recurring charge.</p>"}}, "wrapper": true, "documentation": "<p>Contains the specific price and frequency of a recurring charges for a reserved cache node, or for a reserved cache node offering.</p>"}, "RecurringChargeList": {"type": "list", "member": {"shape": "RecurringCharge", "locationName": "RecurringCharge"}}, "ReplicationGroup": {"type": "structure", "members": {"ReplicationGroupId": {"shape": "String", "documentation": "<p>The identifier for the replication group.</p>"}, "Description": {"shape": "String", "documentation": "<p>The description of the replication group.</p>"}, "Status": {"shape": "String", "documentation": "<p>The current state of this replication group - <i>creating</i>, <i>available</i>, etc.</p>"}, "PendingModifiedValues": {"shape": "ReplicationGroupPendingModifiedValues", "documentation": "<p>A group of settings to be applied to the replication group, either immediately or during the next maintenance window.</p>"}, "MemberClusters": {"shape": "ClusterIdList", "documentation": "<p>The names of all the cache clusters that are part of this replication group.</p>"}, "NodeGroups": {"shape": "NodeGroupList", "documentation": "<p>A single element list with information about the nodes in the replication group.</p>"}, "SnapshottingClusterId": {"shape": "String", "documentation": "<p>The cache cluster ID that is used as the daily snapshot source for the replication group.</p>"}, "AutomaticFailover": {"shape": "AutomaticFailoverStatus", "documentation": "<p>Indicates the status of automatic failover for this replication group.</p>"}}, "wrapper": true, "documentation": "<p>Contains all of the attributes of a specific replication group.</p>"}, "ReplicationGroupAlreadyExistsFault": {"type": "structure", "members": {}, "error": {"code": "ReplicationGroupAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The specified replication group already exists.</p>"}, "ReplicationGroupList": {"type": "list", "member": {"shape": "ReplicationGroup", "locationName": "ReplicationGroup"}}, "ReplicationGroupMessage": {"type": "structure", "members": {"Marker": {"shape": "String", "documentation": "<p>Provides an identifier to allow retrieval of paginated results.</p>"}, "ReplicationGroups": {"shape": "ReplicationGroupList", "documentation": "<p>A list of replication groups. Each item in the list contains detailed information about one replication group.</p>"}}, "documentation": "<p>Represents the output of a <i>DescribeReplicationGroups</i> operation.</p>"}, "ReplicationGroupNotFoundFault": {"type": "structure", "members": {}, "error": {"code": "ReplicationGroupNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The specified replication group does not exist.</p>"}, "ReplicationGroupPendingModifiedValues": {"type": "structure", "members": {"PrimaryClusterId": {"shape": "String", "documentation": "<p>The primary cluster ID which will be applied immediately (if <code>--apply-immediately</code> was specified), or during the next maintenance window.</p>"}, "AutomaticFailoverStatus": {"shape": "PendingAutomaticFailoverStatus", "documentation": "<p>Indicates the status of automatic failover for this replication group.</p>"}}, "documentation": "<p>The settings to be applied to the replication group, either immediately or during the next maintenance window.</p>"}, "ReservedCacheNode": {"type": "structure", "members": {"ReservedCacheNodeId": {"shape": "String", "documentation": "<p>The unique identifier for the reservation.</p>"}, "ReservedCacheNodesOfferingId": {"shape": "String", "documentation": "<p>The offering identifier.</p>"}, "CacheNodeType": {"shape": "String", "documentation": "<p>The cache node type for the reserved cache nodes.</p>"}, "StartTime": {"shape": "TStamp", "documentation": "<p>The time the reservation started.</p>"}, "Duration": {"shape": "Integer", "documentation": "<p>The duration of the reservation in seconds.</p>"}, "FixedPrice": {"shape": "Double", "documentation": "<p>The fixed price charged for this reserved cache node.</p>"}, "UsagePrice": {"shape": "Double", "documentation": "<p>The hourly price charged for this reserved cache node.</p>"}, "CacheNodeCount": {"shape": "Integer", "documentation": "<p>The number of cache nodes that have been reserved.</p>"}, "ProductDescription": {"shape": "String", "documentation": "<p>The description of the reserved cache node.</p>"}, "OfferingType": {"shape": "String", "documentation": "<p>The offering type of this reserved cache node.</p>"}, "State": {"shape": "String", "documentation": "<p>The state of the reserved cache node.</p>"}, "RecurringCharges": {"shape": "RecurringChargeList", "documentation": "<p>The recurring price charged to run this reserved cache node.</p>"}}, "wrapper": true, "documentation": "<p>Represents the output of a <i>PurchaseReservedCacheNodesOffering</i> operation.</p>"}, "ReservedCacheNodeAlreadyExistsFault": {"type": "structure", "members": {}, "error": {"code": "ReservedCacheNodeAlreadyExists", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>You already have a reservation with the given identifier.</p>"}, "ReservedCacheNodeList": {"type": "list", "member": {"shape": "ReservedCacheNode", "locationName": "ReservedCacheNode"}}, "ReservedCacheNodeMessage": {"type": "structure", "members": {"Marker": {"shape": "String", "documentation": "<p>Provides an identifier to allow retrieval of paginated results.</p>"}, "ReservedCacheNodes": {"shape": "ReservedCacheNodeList", "documentation": "<p>A list of reserved cache nodes. Each element in the list contains detailed information about one node.</p>"}}, "documentation": "<p>Represents the output of a <i>DescribeReservedCacheNodes</i> operation.</p>"}, "ReservedCacheNodeNotFoundFault": {"type": "structure", "members": {}, "error": {"code": "ReservedCacheNodeNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The requested reserved cache node was not found.</p>"}, "ReservedCacheNodeQuotaExceededFault": {"type": "structure", "members": {}, "error": {"code": "ReservedCacheNodeQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the user's cache node quota.</p>"}, "ReservedCacheNodesOffering": {"type": "structure", "members": {"ReservedCacheNodesOfferingId": {"shape": "String", "documentation": "<p>A unique identifier for the reserved cache node offering.</p>"}, "CacheNodeType": {"shape": "String", "documentation": "<p>The cache node type for the reserved cache node.</p>"}, "Duration": {"shape": "Integer", "documentation": "<p>The duration of the offering. in seconds.</p>"}, "FixedPrice": {"shape": "Double", "documentation": "<p>The fixed price charged for this offering.</p>"}, "UsagePrice": {"shape": "Double", "documentation": "<p>The hourly price charged for this offering.</p>"}, "ProductDescription": {"shape": "String", "documentation": "<p>The cache engine used by the offering.</p>"}, "OfferingType": {"shape": "String", "documentation": "<p>The offering type.</p>"}, "RecurringCharges": {"shape": "RecurringChargeList", "documentation": "<p>The recurring price charged to run this reserved cache node.</p>"}}, "wrapper": true, "documentation": "<p>Describes all of the attributes of a reserved cache node offering.</p>"}, "ReservedCacheNodesOfferingList": {"type": "list", "member": {"shape": "ReservedCacheNodesOffering", "locationName": "ReservedCacheNodesOffering"}}, "ReservedCacheNodesOfferingMessage": {"type": "structure", "members": {"Marker": {"shape": "String", "documentation": "<p>Provides an identifier to allow retrieval of paginated results.</p>"}, "ReservedCacheNodesOfferings": {"shape": "ReservedCacheNodesOfferingList", "documentation": "<p>A list of reserved cache node offerings. Each element in the list contains detailed information about one offering.</p>"}}, "documentation": "<p>Represents the output of a <i>DescribeReservedCacheNodesOfferings</i> operation.</p>"}, "ReservedCacheNodesOfferingNotFoundFault": {"type": "structure", "members": {}, "error": {"code": "ReservedCacheNodesOfferingNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The requested cache node offering does not exist.</p>"}, "ResetCacheParameterGroupMessage": {"type": "structure", "required": ["CacheParameterGroupName", "ParameterNameValues"], "members": {"CacheParameterGroupName": {"shape": "String", "documentation": "<p>The name of the cache parameter group to reset.</p>"}, "ResetAllParameters": {"shape": "Boolean", "documentation": "<p>If <i>true</i>, all parameters in the cache parameter group will be reset to default values. If <i>false</i>, no such action occurs.</p> <p>Valid values: <code>true</code> | <code>false</code></p>"}, "ParameterNameValues": {"shape": "ParameterNameValueList", "documentation": "<p>An array of parameter names to be reset. If you are not resetting the entire cache parameter group, you must specify at least one parameter name.</p>"}}, "documentation": "<p>Represents the input of a <i>ResetCacheParameterGroup</i> operation.</p>"}, "RevokeCacheSecurityGroupIngressMessage": {"type": "structure", "required": ["CacheSecurityGroupName", "EC2SecurityGroupName", "EC2SecurityGroupOwnerId"], "members": {"CacheSecurityGroupName": {"shape": "String", "documentation": "<p>The name of the cache security group to revoke ingress from.</p>"}, "EC2SecurityGroupName": {"shape": "String", "documentation": "<p>The name of the Amazon EC2 security group to revoke access from.</p>"}, "EC2SecurityGroupOwnerId": {"shape": "String", "documentation": "<p>The AWS account number of the Amazon EC2 security group owner. Note that this is not the same thing as an AWS access key ID - you must provide a valid AWS account number for this parameter.</p>"}}, "documentation": "<p>Represents the input of a <i>RevokeCacheSecurityGroupIngress</i> operation.</p>"}, "SecurityGroupIdsList": {"type": "list", "member": {"shape": "String", "locationName": "SecurityGroupId"}}, "SecurityGroupMembership": {"type": "structure", "members": {"SecurityGroupId": {"shape": "String", "documentation": "<p>The identifier of the cache security group.</p>"}, "Status": {"shape": "String", "documentation": "<p>The status of the cache security group membership. The status changes whenever a cache security group is modified, or when the cache security groups assigned to a cache cluster are modified.</p>"}}, "documentation": "<p>Represents a single cache security group and its status..</p>"}, "SecurityGroupMembershipList": {"type": "list", "member": {"shape": "SecurityGroupMembership"}}, "Snapshot": {"type": "structure", "members": {"SnapshotName": {"shape": "String", "documentation": "<p>The name of a snapshot. For an automatic snapshot, the name is system-generated; for a manual snapshot, this is the user-provided name.</p>"}, "CacheClusterId": {"shape": "String", "documentation": "<p>The user-supplied identifier of the source cache cluster.</p>"}, "SnapshotStatus": {"shape": "String", "documentation": "<p>The status of the snapshot. Valid values: <code>creating</code> | <code>available</code> | <code>restoring</code> | <code>copying</code> | <code>deleting</code>.</p>"}, "SnapshotSource": {"shape": "String", "documentation": "<p>Indicates whether the snapshot is from an automatic backup (<code>automated</code>) or was created manually (<code>manual</code>).</p>"}, "CacheNodeType": {"shape": "String", "documentation": "<p>The name of the compute and memory capacity node type for the source cache cluster.</p>"}, "Engine": {"shape": "String", "documentation": "<p>The name of the cache engine (<i>memcached</i> or <i>redis</i>) used by the source cache cluster.</p>"}, "EngineVersion": {"shape": "String", "documentation": "<p>The version of the cache engine version that is used by the source cache cluster.</p>"}, "NumCacheNodes": {"shape": "IntegerOptional", "documentation": "<p>The number of cache nodes in the source cache cluster.</p>"}, "PreferredAvailabilityZone": {"shape": "String", "documentation": "<p>The name of the Availability Zone in which the source cache cluster is located.</p>"}, "CacheClusterCreateTime": {"shape": "TStamp", "documentation": "<p>The date and time when the source cache cluster was created.</p>"}, "PreferredMaintenanceWindow": {"shape": "String", "documentation": "<p> The time range (in UTC) during which weekly system maintenance can occur on the source cache cluster.</p>"}, "TopicArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) for the topic used by the source cache cluster for publishing notifications.</p>"}, "Port": {"shape": "IntegerOptional", "documentation": "<p>The port number used by each cache nodes in the source cache cluster.</p>"}, "CacheParameterGroupName": {"shape": "String", "documentation": "<p>The cache parameter group that is associated with the source cache cluster.</p>"}, "CacheSubnetGroupName": {"shape": "String", "documentation": "<p>The name of the cache subnet group associated with the source cache cluster.</p>"}, "VpcId": {"shape": "String", "documentation": "<p>The Amazon Virtual Private Cloud identifier (VPC ID) of the cache subnet group for the source cache cluster.</p>"}, "AutoMinorVersionUpgrade": {"shape": "Boolean", "documentation": "<p>For the source cache cluster, indicates whether minor version patches are applied automatically (<code>true</code>) or not (<code>false</code>).</p>"}, "SnapshotRetentionLimit": {"shape": "IntegerOptional", "documentation": "<p>For an automatic snapshot, the number of days for which ElastiCache will retain the snapshot before deleting it.</p> <p>For manual snapshots, this field reflects the <i>SnapshotRetentionLimit</i> for the source cache cluster when the snapshot was created. This field is otherwise ignored: Manual snapshots do not expire, and can only be deleted using the <i>DeleteSnapshot</i> action. </p> <p><b>Important</b><br>If the value of SnapshotRetentionLimit is set to zero (0), backups are turned off.</p>"}, "SnapshotWindow": {"shape": "String", "documentation": "<p>The daily time range during which ElastiCache takes daily snapshots of the source cache cluster.</p>"}, "NodeSnapshots": {"shape": "NodeSnapshotList", "documentation": "<p>A list of the cache nodes in the source cache cluster.</p>"}}, "wrapper": true, "documentation": "<p>Represents a copy of an entire cache cluster as of the time when the snapshot was taken.</p>"}, "SnapshotAlreadyExistsFault": {"type": "structure", "members": {}, "error": {"code": "SnapshotAlreadyExistsFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>You already have a snapshot with the given name.</p>"}, "SnapshotArnsList": {"type": "list", "member": {"shape": "String", "locationName": "SnapshotArn"}}, "SnapshotFeatureNotSupportedFault": {"type": "structure", "members": {}, "error": {"code": "SnapshotFeatureNotSupportedFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>You attempted one of the following actions:</p> <ul> <li> <p>Creating a snapshot of a Redis cache cluster running on a <i>t1.micro</i> cache node.</p> </li> <li> <p>Creating a snapshot of a cache cluster that is running Memcached rather than Redis.</p> </li> </ul> <p>Neither of these are supported by ElastiCache.</p>"}, "SnapshotList": {"type": "list", "member": {"shape": "Snapshot", "locationName": "Snapshot"}}, "SnapshotNotFoundFault": {"type": "structure", "members": {}, "error": {"code": "SnapshotNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The requested snapshot name does not refer to an existing snapshot.</p>"}, "SnapshotQuotaExceededFault": {"type": "structure", "members": {}, "error": {"code": "SnapshotQuotaExceededFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The request cannot be processed because it would exceed the maximum number of snapshots.</p>"}, "SourceType": {"type": "string", "enum": ["cache-cluster", "cache-parameter-group", "cache-security-group", "cache-subnet-group"]}, "String": {"type": "string"}, "Subnet": {"type": "structure", "members": {"SubnetIdentifier": {"shape": "String", "documentation": "<p>The unique identifier for the subnet</p>"}, "SubnetAvailabilityZone": {"shape": "AvailabilityZone", "documentation": "<p>The Availability Zone associated with the subnet</p>"}}, "documentation": "<p>Represents the subnet associated with a cache cluster. This parameter refers to subnets defined in Amazon Virtual Private Cloud (Amazon VPC) and used with ElastiCache.</p>"}, "SubnetIdentifierList": {"type": "list", "member": {"shape": "String", "locationName": "SubnetIdentifier"}}, "SubnetInUse": {"type": "structure", "members": {}, "error": {"code": "SubnetInUse", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The requested subnet is being used by another cache subnet group.</p>"}, "SubnetList": {"type": "list", "member": {"shape": "Subnet", "locationName": "Subnet"}}, "TStamp": {"type": "timestamp"}, "AuthorizeCacheSecurityGroupIngressResult": {"type": "structure", "members": {"CacheSecurityGroup": {"shape": "CacheSecurityGroup"}}}, "CopySnapshotResult": {"type": "structure", "members": {"Snapshot": {"shape": "Snapshot"}}}, "CreateCacheClusterResult": {"type": "structure", "members": {"CacheCluster": {"shape": "CacheCluster"}}}, "CreateCacheParameterGroupResult": {"type": "structure", "members": {"CacheParameterGroup": {"shape": "CacheParameterGroup"}}}, "CreateCacheSecurityGroupResult": {"type": "structure", "members": {"CacheSecurityGroup": {"shape": "CacheSecurityGroup"}}}, "CreateCacheSubnetGroupResult": {"type": "structure", "members": {"CacheSubnetGroup": {"shape": "CacheSubnetGroup"}}}, "CreateReplicationGroupResult": {"type": "structure", "members": {"ReplicationGroup": {"shape": "ReplicationGroup"}}}, "CreateSnapshotResult": {"type": "structure", "members": {"Snapshot": {"shape": "Snapshot"}}}, "DeleteCacheClusterResult": {"type": "structure", "members": {"CacheCluster": {"shape": "CacheCluster"}}}, "DeleteReplicationGroupResult": {"type": "structure", "members": {"ReplicationGroup": {"shape": "ReplicationGroup"}}}, "DeleteSnapshotResult": {"type": "structure", "members": {"Snapshot": {"shape": "Snapshot"}}}, "DescribeEngineDefaultParametersResult": {"type": "structure", "members": {"EngineDefaults": {"shape": "EngineDefaults"}}}, "ModifyCacheClusterResult": {"type": "structure", "members": {"CacheCluster": {"shape": "CacheCluster"}}}, "ModifyCacheSubnetGroupResult": {"type": "structure", "members": {"CacheSubnetGroup": {"shape": "CacheSubnetGroup"}}}, "ModifyReplicationGroupResult": {"type": "structure", "members": {"ReplicationGroup": {"shape": "ReplicationGroup"}}}, "PurchaseReservedCacheNodesOfferingResult": {"type": "structure", "members": {"ReservedCacheNode": {"shape": "ReservedCacheNode"}}}, "RebootCacheClusterResult": {"type": "structure", "members": {"CacheCluster": {"shape": "CacheCluster"}}}, "RevokeCacheSecurityGroupIngressResult": {"type": "structure", "members": {"CacheSecurityGroup": {"shape": "CacheSecurityGroup"}}}}}