{"version": "2.0", "metadata": {"apiVersion": "2019-05-23", "endpointPrefix": "groundstation", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "AWS Ground Station", "serviceId": "GroundStation", "signatureVersion": "v4", "signingName": "groundstation", "uid": "groundstation-2019-05-23"}, "operations": {"CancelContact": {"name": "CancelContact", "http": {"method": "DELETE", "requestUri": "/contact/{contactId}", "responseCode": 200}, "input": {"shape": "CancelContactRequest"}, "output": {"shape": "ContactIdResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Cancels a contact with a specified contact ID.</p>", "idempotent": true}, "CreateConfig": {"name": "CreateConfig", "http": {"method": "POST", "requestUri": "/config", "responseCode": 200}, "input": {"shape": "CreateConfigRequest"}, "output": {"shape": "ConfigIdResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Creates a <code>Config</code> with the specified <code>configData</code> parameters.</p> <p>Only one type of <code>configData</code> can be specified.</p>"}, "CreateDataflowEndpointGroup": {"name": "CreateDataflowEndpointGroup", "http": {"method": "POST", "requestUri": "/dataflowEndpointGroup", "responseCode": 200}, "input": {"shape": "CreateDataflowEndpointGroupRequest"}, "output": {"shape": "DataflowEndpointGroupIdResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Creates a <code>DataflowEndpoint</code> group containing the specified list of <code>DataflowEndpoint</code> objects.</p> <p>The <code>name</code> field in each endpoint is used in your mission profile <code>DataflowEndpointConfig</code> to specify which endpoints to use during a contact.</p> <p>When a contact uses multiple <code>DataflowEndpointConfig</code> objects, each <code>Config</code> must match a <code>DataflowEndpoint</code> in the same group.</p>"}, "CreateEphemeris": {"name": "CreateEphemeris", "http": {"method": "POST", "requestUri": "/ephemeris", "responseCode": 200}, "input": {"shape": "CreateEphemerisRequest"}, "output": {"shape": "EphemerisIdResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Creates an Ephemeris with the specified <code>EphemerisData</code>.</p>"}, "CreateMissionProfile": {"name": "CreateMissionProfile", "http": {"method": "POST", "requestUri": "/missionprofile", "responseCode": 200}, "input": {"shape": "CreateMissionProfileRequest"}, "output": {"shape": "MissionProfileIdResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Creates a mission profile.</p> <p> <code>dataflowEdges</code> is a list of lists of strings. Each lower level list of strings has two elements: a <i>from</i> ARN and a <i>to</i> ARN.</p>"}, "DeleteConfig": {"name": "DeleteConfig", "http": {"method": "DELETE", "requestUri": "/config/{configType}/{configId}", "responseCode": 200}, "input": {"shape": "DeleteConfigRequest"}, "output": {"shape": "ConfigIdResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes a <code>Config</code>.</p>", "idempotent": true}, "DeleteDataflowEndpointGroup": {"name": "DeleteDataflowEndpointGroup", "http": {"method": "DELETE", "requestUri": "/dataflowEndpointGroup/{dataflowEndpointGroupId}", "responseCode": 200}, "input": {"shape": "DeleteDataflowEndpointGroupRequest"}, "output": {"shape": "DataflowEndpointGroupIdResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes a dataflow endpoint group.</p>", "idempotent": true}, "DeleteEphemeris": {"name": "DeleteEphemeris", "http": {"method": "DELETE", "requestUri": "/ephemeris/{ephemerisId}", "responseCode": 200}, "input": {"shape": "DeleteEphemerisRequest"}, "output": {"shape": "EphemerisIdResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes an ephemeris</p>", "idempotent": true}, "DeleteMissionProfile": {"name": "DeleteMissionProfile", "http": {"method": "DELETE", "requestUri": "/missionprofile/{missionProfileId}", "responseCode": 200}, "input": {"shape": "DeleteMissionProfileRequest"}, "output": {"shape": "MissionProfileIdResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes a mission profile.</p>", "idempotent": true}, "DescribeContact": {"name": "DescribeContact", "http": {"method": "GET", "requestUri": "/contact/{contactId}", "responseCode": 200}, "input": {"shape": "DescribeContactRequest"}, "output": {"shape": "DescribeContactResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes an existing contact.</p>"}, "DescribeEphemeris": {"name": "DescribeEphemeris", "http": {"method": "GET", "requestUri": "/ephemeris/{ephemerisId}", "responseCode": 200}, "input": {"shape": "DescribeEphemerisRequest"}, "output": {"shape": "DescribeEphemerisResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes an existing ephemeris.</p>"}, "GetAgentConfiguration": {"name": "GetAgentConfiguration", "http": {"method": "GET", "requestUri": "/agent/{agentId}/configuration", "responseCode": 200}, "input": {"shape": "GetAgentConfigurationRequest"}, "output": {"shape": "GetAgentConfigurationResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<note> <p> For use by AWS Ground Station Agent and shouldn't be called directly.</p> </note> <p>Gets the latest configuration information for a registered agent.</p>"}, "GetConfig": {"name": "GetConfig", "http": {"method": "GET", "requestUri": "/config/{configType}/{configId}", "responseCode": 200}, "input": {"shape": "GetConfigRequest"}, "output": {"shape": "GetConfigResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns <code>Config</code> information.</p> <p>Only one <code>Config</code> response can be returned.</p>"}, "GetDataflowEndpointGroup": {"name": "GetDataflowEndpointGroup", "http": {"method": "GET", "requestUri": "/dataflowEndpointGroup/{dataflowEndpointGroupId}", "responseCode": 200}, "input": {"shape": "GetDataflowEndpointGroupRequest"}, "output": {"shape": "GetDataflowEndpointGroupResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns the dataflow endpoint group.</p>"}, "GetMinuteUsage": {"name": "GetMinuteUsage", "http": {"method": "POST", "requestUri": "/minute-usage", "responseCode": 200}, "input": {"shape": "GetMinuteUsageRequest"}, "output": {"shape": "GetMinuteUsageResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns the number of reserved minutes used by account.</p>"}, "GetMissionProfile": {"name": "GetMissionProfile", "http": {"method": "GET", "requestUri": "/missionprofile/{missionProfileId}", "responseCode": 200}, "input": {"shape": "GetMissionProfileRequest"}, "output": {"shape": "GetMissionProfileResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns a mission profile.</p>"}, "GetSatellite": {"name": "GetSatellite", "http": {"method": "GET", "requestUri": "/satellite/{satelliteId}", "responseCode": 200}, "input": {"shape": "GetSatelliteRequest"}, "output": {"shape": "GetSatelliteResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns a satellite.</p>"}, "ListConfigs": {"name": "ListConfigs", "http": {"method": "GET", "requestUri": "/config", "responseCode": 200}, "input": {"shape": "ListConfigsRequest"}, "output": {"shape": "ListConfigsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns a list of <code>Config</code> objects.</p>"}, "ListContacts": {"name": "ListContacts", "http": {"method": "POST", "requestUri": "/contacts", "responseCode": 200}, "input": {"shape": "ListContactsRequest"}, "output": {"shape": "ListContactsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns a list of contacts.</p> <p>If <code>statusList</code> contains AVAILABLE, the request must include <code>groundStation</code>, <code>missionprofileArn</code>, and <code>satelliteArn</code>. </p>"}, "ListDataflowEndpointGroups": {"name": "ListDataflowEndpointGroups", "http": {"method": "GET", "requestUri": "/dataflowEndpointGroup", "responseCode": 200}, "input": {"shape": "ListDataflowEndpointGroupsRequest"}, "output": {"shape": "ListDataflowEndpointGroupsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns a list of <code>DataflowEndpoint</code> groups.</p>"}, "ListEphemerides": {"name": "ListEphemerides", "http": {"method": "POST", "requestUri": "/ephemerides", "responseCode": 200}, "input": {"shape": "ListEphemeridesRequest"}, "output": {"shape": "ListEphemeridesResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>List existing ephemerides.</p>"}, "ListGroundStations": {"name": "ListGroundStations", "http": {"method": "GET", "requestUri": "/groundstation", "responseCode": 200}, "input": {"shape": "ListGroundStationsRequest"}, "output": {"shape": "ListGroundStationsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns a list of ground stations. </p>"}, "ListMissionProfiles": {"name": "ListMissionProfiles", "http": {"method": "GET", "requestUri": "/missionprofile", "responseCode": 200}, "input": {"shape": "ListMissionProfilesRequest"}, "output": {"shape": "ListMissionProfilesResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns a list of mission profiles.</p>"}, "ListSatellites": {"name": "ListSatellites", "http": {"method": "GET", "requestUri": "/satellite", "responseCode": 200}, "input": {"shape": "ListSatellitesRequest"}, "output": {"shape": "ListSatellitesResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns a list of satellites.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns a list of tags for a specified resource.</p>"}, "RegisterAgent": {"name": "RegisterAgent", "http": {"method": "POST", "requestUri": "/agent", "responseCode": 200}, "input": {"shape": "RegisterAgentRequest"}, "output": {"shape": "RegisterAgentResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<note> <p> For use by AWS Ground Station Agent and shouldn't be called directly.</p> </note> <p> Registers a new agent with AWS Ground Station. </p>"}, "ReserveContact": {"name": "ReserveContact", "http": {"method": "POST", "requestUri": "/contact", "responseCode": 200}, "input": {"shape": "ReserveContactRequest"}, "output": {"shape": "ContactIdResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Reserves a contact using specified parameters.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Assigns a tag to a resource.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deassigns a resource tag.</p>", "idempotent": true}, "UpdateAgentStatus": {"name": "UpdateAgentStatus", "http": {"method": "PUT", "requestUri": "/agent/{agentId}", "responseCode": 200}, "input": {"shape": "UpdateAgentStatusRequest"}, "output": {"shape": "UpdateAgentStatusResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<note> <p> For use by AWS Ground Station Agent and shouldn't be called directly.</p> </note> <p>Update the status of the agent.</p>", "idempotent": true}, "UpdateConfig": {"name": "UpdateConfig", "http": {"method": "PUT", "requestUri": "/config/{configType}/{configId}", "responseCode": 200}, "input": {"shape": "UpdateConfigRequest"}, "output": {"shape": "ConfigIdResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Updates the <code>Config</code> used when scheduling contacts.</p> <p>Updating a <code>Config</code> will not update the execution parameters for existing future contacts scheduled with this <code>Config</code>.</p>", "idempotent": true}, "UpdateEphemeris": {"name": "UpdateEphemeris", "http": {"method": "PUT", "requestUri": "/ephemeris/{ephemerisId}", "responseCode": 200}, "input": {"shape": "UpdateEphemerisRequest"}, "output": {"shape": "EphemerisIdResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Updates an existing ephemeris</p>", "idempotent": true}, "UpdateMissionProfile": {"name": "UpdateMissionProfile", "http": {"method": "PUT", "requestUri": "/missionprofile/{missionProfileId}", "responseCode": 200}, "input": {"shape": "UpdateMissionProfileRequest"}, "output": {"shape": "MissionProfileIdResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DependencyException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Updates a mission profile.</p> <p>Updating a mission profile will not update the execution parameters for existing future contacts.</p>", "idempotent": true}}, "shapes": {"AWSRegion": {"type": "string", "max": 128, "min": 1, "pattern": "^[\\w-]+$"}, "AgentCpuCoresList": {"type": "list", "member": {"shape": "Integer"}, "max": 256, "min": 0}, "AgentDetails": {"type": "structure", "required": ["agentVersion", "componentVersions", "instanceId", "instanceType"], "members": {"agentCpuCores": {"shape": "AgentCpuCoresList", "documentation": "<p>List of CPU cores reserved for the agent.</p>"}, "agentVersion": {"shape": "VersionString", "documentation": "<p>Current agent version.</p>"}, "componentVersions": {"shape": "ComponentVersionList", "documentation": "<p>List of versions being used by agent components.</p>"}, "instanceId": {"shape": "InstanceId", "documentation": "<p>ID of EC2 instance agent is running on.</p>"}, "instanceType": {"shape": "InstanceType", "documentation": "<p>Type of EC2 instance agent is running on.</p>"}, "reservedCpuCores": {"shape": "AgentCpuCoresList", "documentation": "<note> <p>This field should not be used. Use agentCpuCores instead.</p> </note> <p>List of CPU cores reserved for processes other than the agent running on the EC2 instance.</p>"}}, "documentation": "<p>Detailed information about the agent.</p>"}, "AgentStatus": {"type": "string", "enum": ["SUCCESS", "FAILED", "ACTIVE", "INACTIVE"]}, "AggregateStatus": {"type": "structure", "required": ["status"], "members": {"signatureMap": {"shape": "SignatureMap", "documentation": "<p>Sparse map of failure signatures.</p>"}, "status": {"shape": "AgentStatus", "documentation": "<p>Aggregate status.</p>"}}, "documentation": "<p>Aggregate status of Agent components.</p>"}, "AngleUnits": {"type": "string", "enum": ["DEGREE_ANGLE", "RADIAN"]}, "AntennaDemodDecodeDetails": {"type": "structure", "members": {"outputNode": {"shape": "String", "documentation": "<p>Name of an antenna demod decode output node used in a contact.</p>"}}, "documentation": "<p>Details about an antenna demod decode <code>Config</code> used in a contact.</p>"}, "AntennaDownlinkConfig": {"type": "structure", "required": ["spectrumConfig"], "members": {"spectrumConfig": {"shape": "SpectrumConfig", "documentation": "<p>Object that describes a spectral <code>Config</code>.</p>"}}, "documentation": "<p>Information about how AWS Ground Station should configure an antenna for downlink during a contact.</p>"}, "AntennaDownlinkDemodDecodeConfig": {"type": "structure", "required": ["decodeConfig", "demodulationConfig", "spectrumConfig"], "members": {"decodeConfig": {"shape": "DecodeConfig", "documentation": "<p>Information about the decode <code>Config</code>.</p>"}, "demodulationConfig": {"shape": "DemodulationConfig", "documentation": "<p>Information about the demodulation <code>Config</code>.</p>"}, "spectrumConfig": {"shape": "SpectrumConfig", "documentation": "<p>Information about the spectral <code>Config</code>.</p>"}}, "documentation": "<p>Information about how AWS Ground Station should conﬁgure an antenna for downlink demod decode during a contact.</p>"}, "AntennaUplinkConfig": {"type": "structure", "required": ["spectrumConfig", "targetEirp"], "members": {"spectrumConfig": {"shape": "UplinkSpectrumConfig", "documentation": "<p>Information about the uplink spectral <code>Config</code>.</p>"}, "targetEirp": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>EIRP of the target.</p>"}, "transmitDisabled": {"shape": "Boolean", "documentation": "<p>Whether or not uplink transmit is disabled.</p>"}}, "documentation": "<p>Information about the uplink <code>Config</code> of an antenna.</p>"}, "AnyArn": {"type": "string", "max": 1024, "min": 5, "pattern": "^(arn:aws:)[\\s\\S]{0,1024}$"}, "AuditResults": {"type": "string", "enum": ["HEALTHY", "UNHEALTHY"]}, "AwsGroundStationAgentEndpoint": {"type": "structure", "required": ["egress<PERSON>ddress", "ingressAddress", "name"], "members": {"agentStatus": {"shape": "AgentStatus", "documentation": "<p>The status of AgentEndpoint.</p>"}, "auditResults": {"shape": "AuditResults", "documentation": "<p>The results of the audit.</p>"}, "egressAddress": {"shape": "ConnectionDetails", "documentation": "<p>The egress address of AgentEndpoint.</p>"}, "ingressAddress": {"shape": "RangedConnectionDetails", "documentation": "<p>The ingress address of AgentEndpoint.</p>"}, "name": {"shape": "SafeName", "documentation": "<p>Name string associated with AgentEndpoint. Used as a human-readable identifier for AgentEndpoint.</p>"}}, "documentation": "<p>Information about AwsGroundStationAgentEndpoint.</p>"}, "BandwidthUnits": {"type": "string", "enum": ["GHz", "MHz", "kHz"]}, "Boolean": {"type": "boolean", "box": true}, "BucketArn": {"type": "string"}, "CancelContactRequest": {"type": "structure", "required": ["contactId"], "members": {"contactId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of a contact.</p>", "location": "uri", "locationName": "contactId"}}, "documentation": "<p/>"}, "CapabilityArn": {"type": "string"}, "CapabilityArnList": {"type": "list", "member": {"shape": "CapabilityArn"}, "max": 20, "min": 1}, "CapabilityHealth": {"type": "string", "enum": ["UNHEALTHY", "HEALTHY"]}, "CapabilityHealthReason": {"type": "string", "enum": ["NO_REGISTERED_AGENT", "INVALID_IP_OWNERSHIP", "NOT_AUTHORIZED_TO_CREATE_SLR", "UNVERIFIED_IP_OWNERSHIP", "INITIALIZING_DATAPLANE", "DATAPLANE_FAILURE", "HEALTHY"]}, "CapabilityHealthReasonList": {"type": "list", "member": {"shape": "CapabilityHealthReason"}, "max": 500, "min": 0}, "ComponentStatusData": {"type": "structure", "required": ["capabilityArn", "componentType", "dataflowId", "status"], "members": {"bytesReceived": {"shape": "<PERSON>", "documentation": "<p>Bytes received by the component.</p>"}, "bytesSent": {"shape": "<PERSON>", "documentation": "<p>Bytes sent by the component.</p>"}, "capabilityArn": {"shape": "CapabilityArn", "documentation": "<p>Capability ARN of the component.</p>"}, "componentType": {"shape": "ComponentTypeString", "documentation": "<p>The Component type.</p>"}, "dataflowId": {"shape": "<PERSON><PERSON>", "documentation": "<p>Dataflow UUID associated with the component.</p>"}, "packetsDropped": {"shape": "<PERSON>", "documentation": "<p>Packets dropped by component.</p>"}, "status": {"shape": "AgentStatus", "documentation": "<p>Component status.</p>"}}, "documentation": "<p>Data on the status of agent components.</p>"}, "ComponentStatusList": {"type": "list", "member": {"shape": "ComponentStatusData"}, "max": 20, "min": 0}, "ComponentTypeString": {"type": "string", "pattern": "^[a-zA-Z0-9_]{1,64}$"}, "ComponentVersion": {"type": "structure", "required": ["componentType", "versions"], "members": {"componentType": {"shape": "ComponentTypeString", "documentation": "<p>Component type.</p>"}, "versions": {"shape": "VersionStringList", "documentation": "<p>List of versions.</p>"}}, "documentation": "<p>Version information for agent components.</p>"}, "ComponentVersionList": {"type": "list", "member": {"shape": "ComponentVersion"}, "max": 20, "min": 1}, "ConfigArn": {"type": "string"}, "ConfigCapabilityType": {"type": "string", "enum": ["antenna-downlink", "antenna-downlink-demod-decode", "antenna-uplink", "dataflow-endpoint", "tracking", "uplink-echo", "s3-recording"]}, "ConfigDetails": {"type": "structure", "members": {"antennaDemodDecodeDetails": {"shape": "AntennaDemodDecodeDetails", "documentation": "<p>Details for antenna demod decode <code>Config</code> in a contact.</p>"}, "endpointDetails": {"shape": "EndpointDetails"}, "s3RecordingDetails": {"shape": "S3RecordingDetails", "documentation": "<p>Details for an S3 recording <code>Config</code> in a contact.</p>"}}, "documentation": "<p>Details for certain <code>Config</code> object types in a contact.</p>", "union": true}, "ConfigIdResponse": {"type": "structure", "members": {"configArn": {"shape": "ConfigArn", "documentation": "<p>ARN of a <code>Config</code>.</p>"}, "configId": {"shape": "String", "documentation": "<p>UUID of a <code>Config</code>.</p>"}, "configType": {"shape": "ConfigCapabilityType", "documentation": "<p>Type of a <code>Config</code>.</p>"}}, "documentation": "<p/>"}, "ConfigList": {"type": "list", "member": {"shape": "ConfigListItem"}}, "ConfigListItem": {"type": "structure", "members": {"configArn": {"shape": "ConfigArn", "documentation": "<p>ARN of a <code>Config</code>.</p>"}, "configId": {"shape": "String", "documentation": "<p>UUID of a <code>Config</code>.</p>"}, "configType": {"shape": "ConfigCapabilityType", "documentation": "<p>Type of a <code>Config</code>.</p>"}, "name": {"shape": "String", "documentation": "<p>Name of a <code>Config</code>.</p>"}}, "documentation": "<p>An item in a list of <code>Config</code> objects.</p>"}, "ConfigTypeData": {"type": "structure", "members": {"antennaDownlinkConfig": {"shape": "AntennaDownlinkConfig", "documentation": "<p>Information about how AWS Ground Station should configure an antenna for downlink during a contact.</p>"}, "antennaDownlinkDemodDecodeConfig": {"shape": "AntennaDownlinkDemodDecodeConfig", "documentation": "<p>Information about how AWS Ground Station should conﬁgure an antenna for downlink demod decode during a contact.</p>"}, "antennaUplinkConfig": {"shape": "AntennaUplinkConfig", "documentation": "<p>Information about how AWS Ground Station should conﬁgure an antenna for uplink during a contact.</p>"}, "dataflowEndpointConfig": {"shape": "DataflowEndpointConfig", "documentation": "<p>Information about the dataflow endpoint <code>Config</code>.</p>"}, "s3RecordingConfig": {"shape": "S3RecordingConfig", "documentation": "<p>Information about an S3 recording <code>Config</code>.</p>"}, "trackingConfig": {"shape": "TrackingConfig", "documentation": "<p>Object that determines whether tracking should be used during a contact executed with this <code>Config</code> in the mission profile. </p>"}, "uplinkEchoConfig": {"shape": "UplinkEchoConfig", "documentation": "<p>Information about an uplink echo <code>Config</code>.</p> <p>Parameters from the <code>AntennaUplinkConfig</code>, corresponding to the specified <code>AntennaUplinkConfigArn</code>, are used when this <code>UplinkEchoConfig</code> is used in a contact.</p>"}}, "documentation": "<p>Object containing the parameters of a <code>Config</code>.</p> <p>See the subtype definitions for what each type of <code>Config</code> contains.</p>", "union": true}, "ConnectionDetails": {"type": "structure", "required": ["socketAddress"], "members": {"mtu": {"shape": "Integer", "documentation": "<p>Maximum transmission unit (MTU) size in bytes of a dataflow endpoint.</p>"}, "socketAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>A socket address.</p>"}}, "documentation": "<p>Egress address of AgentEndpoint with an optional mtu.</p>"}, "ContactData": {"type": "structure", "members": {"contactId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of a contact.</p>"}, "contactStatus": {"shape": "ContactStatus", "documentation": "<p>Status of a contact.</p>"}, "endTime": {"shape": "Timestamp", "documentation": "<p>End time of a contact in UTC.</p>"}, "errorMessage": {"shape": "String", "documentation": "<p>Error message of a contact.</p>"}, "groundStation": {"shape": "String", "documentation": "<p>Name of a ground station.</p>"}, "maximumElevation": {"shape": "Elevation", "documentation": "<p>Maximum elevation angle of a contact.</p>"}, "missionProfileArn": {"shape": "MissionProfileArn", "documentation": "<p>ARN of a mission profile.</p>"}, "postPassEndTime": {"shape": "Timestamp", "documentation": "<p>Amount of time after a contact ends that you’d like to receive a CloudWatch event indicating the pass has finished.</p>"}, "prePassStartTime": {"shape": "Timestamp", "documentation": "<p>Amount of time prior to contact start you’d like to receive a CloudWatch event indicating an upcoming pass.</p>"}, "region": {"shape": "String", "documentation": "<p>Region of a contact.</p>"}, "satelliteArn": {"shape": "satelliteArn", "documentation": "<p>ARN of a satellite.</p>"}, "startTime": {"shape": "Timestamp", "documentation": "<p>Start time of a contact in UTC.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>Tags assigned to a contact.</p>"}}, "documentation": "<p>Data describing a contact.</p>"}, "ContactIdResponse": {"type": "structure", "members": {"contactId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of a contact.</p>"}}, "documentation": "<p/>"}, "ContactList": {"type": "list", "member": {"shape": "ContactData"}}, "ContactStatus": {"type": "string", "enum": ["AVAILABLE", "AWS_CANCELLED", "AWS_FAILED", "CANCELLED", "CANCELLING", "COMPLETED", "FAILED", "FAILED_TO_SCHEDULE", "PASS", "POSTPASS", "PREPASS", "SCHEDULED", "SCHEDULING"]}, "CreateConfigRequest": {"type": "structure", "required": ["configData", "name"], "members": {"configData": {"shape": "ConfigTypeData", "documentation": "<p>Parameters of a <code>Config</code>.</p>"}, "name": {"shape": "SafeName", "documentation": "<p>Name of a <code>Config</code>.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>Tags assigned to a <code>Config</code>.</p>"}}, "documentation": "<p/>"}, "CreateDataflowEndpointGroupRequest": {"type": "structure", "required": ["endpointDetails"], "members": {"contactPostPassDurationSeconds": {"shape": "DataflowEndpointGroupDurationInSeconds", "documentation": "<p>Amount of time, in seconds, after a contact ends that the Ground Station Dataflow Endpoint Group will be in a <code>POSTPASS</code> state. A Ground Station Dataflow Endpoint Group State Change event will be emitted when the Dataflow Endpoint Group enters and exits the <code>POSTPASS</code> state.</p>"}, "contactPrePassDurationSeconds": {"shape": "DataflowEndpointGroupDurationInSeconds", "documentation": "<p>Amount of time, in seconds, before a contact starts that the Ground Station Dataflow Endpoint Group will be in a <code>PREPASS</code> state. A Ground Station Dataflow Endpoint Group State Change event will be emitted when the Dataflow Endpoint Group enters and exits the <code>PREPASS</code> state.</p>"}, "endpointDetails": {"shape": "EndpointDetailsList", "documentation": "<p>Endpoint details of each endpoint in the dataflow endpoint group.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>Tags of a dataflow endpoint group.</p>"}}, "documentation": "<p/>"}, "CreateEphemerisRequest": {"type": "structure", "required": ["name", "satelliteId"], "members": {"enabled": {"shape": "Boolean", "documentation": "<p>Whether to set the ephemeris status to <code>ENABLED</code> after validation.</p> <p>Setting this to false will set the ephemeris status to <code>DISABLED</code> after validation.</p>"}, "ephemeris": {"shape": "EphemerisData", "documentation": "<p>Ephemeris data.</p>"}, "expirationTime": {"shape": "Timestamp", "documentation": "<p>An overall expiration time for the ephemeris in UTC, after which it will become <code>EXPIRED</code>.</p>"}, "kmsKeyArn": {"shape": "KeyArn", "documentation": "<p>The ARN of a KMS key used to encrypt the ephemeris in Ground Station.</p>"}, "name": {"shape": "SafeName", "documentation": "<p>A name string associated with the ephemeris. Used as a human-readable identifier for the ephemeris.</p>"}, "priority": {"shape": "CustomerEphemerisPriority", "documentation": "<p>Customer-provided priority score to establish the order in which overlapping ephemerides should be used.</p> <p>The default for customer-provided ephemeris priority is 1, and higher numbers take precedence.</p> <p>Priority must be 1 or greater</p>"}, "satelliteId": {"shape": "<PERSON><PERSON>", "documentation": "<p>AWS Ground Station satellite ID for this ephemeris.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>Tags assigned to an ephemeris.</p>"}}}, "CreateMissionProfileRequest": {"type": "structure", "required": ["dataflowEdges", "minimumViableContactDurationSeconds", "name", "trackingConfigArn"], "members": {"contactPostPassDurationSeconds": {"shape": "DurationInSeconds", "documentation": "<p>Amount of time after a contact ends that you’d like to receive a CloudWatch event indicating the pass has finished.</p>"}, "contactPrePassDurationSeconds": {"shape": "DurationInSeconds", "documentation": "<p>Amount of time prior to contact start you’d like to receive a CloudWatch event indicating an upcoming pass.</p>"}, "dataflowEdges": {"shape": "DataflowEdgeList", "documentation": "<p>A list of lists of ARNs. Each list of ARNs is an edge, with a <i>from</i> <code>Config</code> and a <i>to</i> <code>Config</code>.</p>"}, "minimumViableContactDurationSeconds": {"shape": "PositiveDurationInSeconds", "documentation": "<p>Smallest amount of time in seconds that you’d like to see for an available contact. AWS Ground Station will not present you with contacts shorter than this duration.</p>"}, "name": {"shape": "SafeName", "documentation": "<p>Name of a mission profile.</p>"}, "streamsKmsKey": {"shape": "KmsKey", "documentation": "<p>KMS key to use for encrypting streams.</p>"}, "streamsKmsRole": {"shape": "RoleArn", "documentation": "<p>Role to use for encrypting streams with KMS key.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>Tags assigned to a mission profile.</p>"}, "trackingConfigArn": {"shape": "ConfigArn", "documentation": "<p>ARN of a tracking <code>Config</code>.</p>"}}, "documentation": "<p/>"}, "Criticality": {"type": "string", "enum": ["PREFERRED", "REMOVED", "REQUIRED"]}, "CustomerEphemerisPriority": {"type": "integer", "box": true, "max": 99999, "min": 1}, "DataflowDetail": {"type": "structure", "members": {"destination": {"shape": "Destination"}, "errorMessage": {"shape": "String", "documentation": "<p>Error message for a dataflow.</p>"}, "source": {"shape": "Source"}}, "documentation": "<p>Information about a dataflow edge used in a contact.</p>"}, "DataflowEdge": {"type": "list", "member": {"shape": "ConfigArn"}, "max": 2, "min": 2}, "DataflowEdgeList": {"type": "list", "member": {"shape": "DataflowEdge"}, "max": 500, "min": 0}, "DataflowEndpoint": {"type": "structure", "members": {"address": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Socket address of a dataflow endpoint.</p>"}, "mtu": {"shape": "DataflowEndpointMtuInteger", "documentation": "<p>Maximum transmission unit (MTU) size in bytes of a dataflow endpoint.</p>"}, "name": {"shape": "SafeName", "documentation": "<p>Name of a dataflow endpoint.</p>"}, "status": {"shape": "EndpointStatus", "documentation": "<p>Status of a dataflow endpoint.</p>"}}, "documentation": "<p>Information about a dataflow endpoint.</p>"}, "DataflowEndpointConfig": {"type": "structure", "required": ["dataflowEndpointName"], "members": {"dataflowEndpointName": {"shape": "String", "documentation": "<p>Name of a dataflow endpoint.</p>"}, "dataflowEndpointRegion": {"shape": "String", "documentation": "<p>Region of a dataflow endpoint.</p>"}}, "documentation": "<p>Information about the dataflow endpoint <code>Config</code>.</p>"}, "DataflowEndpointGroupArn": {"type": "string"}, "DataflowEndpointGroupDurationInSeconds": {"type": "integer", "box": true, "max": 480, "min": 120}, "DataflowEndpointGroupIdResponse": {"type": "structure", "members": {"dataflowEndpointGroupId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of a dataflow endpoint group.</p>"}}, "documentation": "<p/>"}, "DataflowEndpointGroupList": {"type": "list", "member": {"shape": "DataflowEndpointListItem"}}, "DataflowEndpointListItem": {"type": "structure", "members": {"dataflowEndpointGroupArn": {"shape": "DataflowEndpointGroupArn", "documentation": "<p>ARN of a dataflow endpoint group.</p>"}, "dataflowEndpointGroupId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of a dataflow endpoint group.</p>"}}, "documentation": "<p>Item in a list of <code>DataflowEndpoint</code> groups.</p>"}, "DataflowEndpointMtuInteger": {"type": "integer", "box": true, "max": 1500, "min": 1400}, "DataflowList": {"type": "list", "member": {"shape": "DataflowDetail"}}, "DecodeConfig": {"type": "structure", "required": ["unvalidatedJSON"], "members": {"unvalidatedJSON": {"shape": "JsonString", "documentation": "<p>Unvalidated JSON of a decode <code>Config</code>.</p>"}}, "documentation": "<p>Information about the decode <code>Config</code>.</p>"}, "DeleteConfigRequest": {"type": "structure", "required": ["configId", "configType"], "members": {"configId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of a <code>Config</code>.</p>", "location": "uri", "locationName": "configId"}, "configType": {"shape": "ConfigCapabilityType", "documentation": "<p>Type of a <code>Config</code>.</p>", "location": "uri", "locationName": "configType"}}, "documentation": "<p/>"}, "DeleteDataflowEndpointGroupRequest": {"type": "structure", "required": ["dataflowEndpointGroupId"], "members": {"dataflowEndpointGroupId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of a dataflow endpoint group.</p>", "location": "uri", "locationName": "dataflowEndpointGroupId"}}, "documentation": "<p/>"}, "DeleteEphemerisRequest": {"type": "structure", "required": ["ephemerisId"], "members": {"ephemerisId": {"shape": "<PERSON><PERSON>", "documentation": "<p>The AWS Ground Station ephemeris ID.</p>", "location": "uri", "locationName": "ephemerisId"}}}, "DeleteMissionProfileRequest": {"type": "structure", "required": ["missionProfileId"], "members": {"missionProfileId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of a mission profile.</p>", "location": "uri", "locationName": "missionProfileId"}}, "documentation": "<p/>"}, "DemodulationConfig": {"type": "structure", "required": ["unvalidatedJSON"], "members": {"unvalidatedJSON": {"shape": "JsonString", "documentation": "<p>Unvalidated JSON of a demodulation <code>Config</code>.</p>"}}, "documentation": "<p>Information about the demodulation <code>Config</code>.</p>"}, "DependencyException": {"type": "structure", "members": {"message": {"shape": "String"}, "parameterName": {"shape": "String", "documentation": "<p/>"}}, "documentation": "<p>Dependency encountered an error.</p>", "error": {"httpStatusCode": 531}, "exception": true, "fault": true}, "DescribeContactRequest": {"type": "structure", "required": ["contactId"], "members": {"contactId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of a contact.</p>", "location": "uri", "locationName": "contactId"}}, "documentation": "<p/>"}, "DescribeContactResponse": {"type": "structure", "members": {"contactId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of a contact.</p>"}, "contactStatus": {"shape": "ContactStatus", "documentation": "<p>Status of a contact.</p>"}, "dataflowList": {"shape": "DataflowList", "documentation": "<p>List describing source and destination details for each dataflow edge.</p>"}, "endTime": {"shape": "Timestamp", "documentation": "<p>End time of a contact in UTC.</p>"}, "errorMessage": {"shape": "String", "documentation": "<p>Error message for a contact.</p>"}, "groundStation": {"shape": "String", "documentation": "<p>Ground station for a contact.</p>"}, "maximumElevation": {"shape": "Elevation", "documentation": "<p>Maximum elevation angle of a contact.</p>"}, "missionProfileArn": {"shape": "MissionProfileArn", "documentation": "<p>ARN of a mission profile.</p>"}, "postPassEndTime": {"shape": "Timestamp", "documentation": "<p>Amount of time after a contact ends that you’d like to receive a CloudWatch event indicating the pass has finished.</p>"}, "prePassStartTime": {"shape": "Timestamp", "documentation": "<p>Amount of time prior to contact start you’d like to receive a CloudWatch event indicating an upcoming pass.</p>"}, "region": {"shape": "String", "documentation": "<p>Region of a contact.</p>"}, "satelliteArn": {"shape": "satelliteArn", "documentation": "<p>ARN of a satellite.</p>"}, "startTime": {"shape": "Timestamp", "documentation": "<p>Start time of a contact in UTC.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>Tags assigned to a contact.</p>"}}, "documentation": "<p/>"}, "DescribeEphemerisRequest": {"type": "structure", "required": ["ephemerisId"], "members": {"ephemerisId": {"shape": "<PERSON><PERSON>", "documentation": "<p>The AWS Ground Station ephemeris ID.</p>", "location": "uri", "locationName": "ephemerisId"}}}, "DescribeEphemerisResponse": {"type": "structure", "members": {"creationTime": {"shape": "Timestamp", "documentation": "<p>The time the ephemeris was uploaded in UTC.</p>"}, "enabled": {"shape": "Boolean", "documentation": "<p>Whether or not the ephemeris is enabled.</p>"}, "ephemerisId": {"shape": "<PERSON><PERSON>", "documentation": "<p>The AWS Ground Station ephemeris ID.</p>"}, "invalidReason": {"shape": "EphemerisInvalidReason", "documentation": "<p>Reason that an ephemeris failed validation. Only provided for ephemerides with <code>INVALID</code> status.</p>"}, "name": {"shape": "SafeName", "documentation": "<p>A name string associated with the ephemeris. Used as a human-readable identifier for the ephemeris.</p>"}, "priority": {"shape": "EphemerisPriority", "documentation": "<p>Customer-provided priority score to establish the order in which overlapping ephemerides should be used.</p> <p>The default for customer-provided ephemeris priority is 1, and higher numbers take precedence.</p> <p>Priority must be 1 or greater</p>"}, "satelliteId": {"shape": "<PERSON><PERSON>", "documentation": "<p>The AWS Ground Station satellite ID associated with ephemeris.</p>"}, "status": {"shape": "EphemerisStatus", "documentation": "<p>The status of the ephemeris.</p>"}, "suppliedData": {"shape": "EphemerisTypeDescription", "documentation": "<p>Supplied ephemeris data.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>Tags assigned to an ephemeris.</p>"}}}, "Destination": {"type": "structure", "members": {"configDetails": {"shape": "ConfigDetails", "documentation": "<p>Additional details for a <code>Config</code>, if type is dataflow endpoint or antenna demod decode.</p>"}, "configId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of a <code>Config</code>.</p>"}, "configType": {"shape": "ConfigCapabilityType", "documentation": "<p>Type of a <code>Config</code>.</p>"}, "dataflowDestinationRegion": {"shape": "String", "documentation": "<p>Region of a dataflow destination.</p>"}}, "documentation": "<p>Dataflow details for the destination side.</p>"}, "DiscoveryData": {"type": "structure", "required": ["capabilityArns", "privateIpAddresses", "publicIpAddresses"], "members": {"capabilityArns": {"shape": "CapabilityArnList", "documentation": "<p>List of capabilities to associate with agent.</p>"}, "privateIpAddresses": {"shape": "IpAddressList", "documentation": "<p>List of private IP addresses to associate with agent.</p>"}, "publicIpAddresses": {"shape": "IpAddressList", "documentation": "<p>List of public IP addresses to associate with agent.</p>"}}, "documentation": "<p>Data for agent discovery.</p>"}, "Double": {"type": "double", "box": true}, "DurationInSeconds": {"type": "integer", "box": true, "max": 21600, "min": 0}, "Eirp": {"type": "structure", "required": ["units", "value"], "members": {"units": {"shape": "EirpUnits", "documentation": "<p>Units of an EIRP.</p>"}, "value": {"shape": "Double", "documentation": "<p>Value of an EIRP. Valid values are between 20.0 to 50.0 dBW.</p>"}}, "documentation": "<p>Object that represents EIRP.</p>"}, "EirpUnits": {"type": "string", "enum": ["dBW"]}, "Elevation": {"type": "structure", "required": ["unit", "value"], "members": {"unit": {"shape": "AngleUnits", "documentation": "<p>Elevation angle units.</p>"}, "value": {"shape": "Double", "documentation": "<p>Elevation angle value.</p>"}}, "documentation": "<p>Elevation angle of the satellite in the sky during a contact.</p>"}, "EndpointDetails": {"type": "structure", "members": {"awsGroundStationAgentEndpoint": {"shape": "AwsGroundStationAgentEndpoint", "documentation": "<p>An agent endpoint.</p>"}, "endpoint": {"shape": "DataflowEndpoint", "documentation": "<p>A dataflow endpoint.</p>"}, "healthReasons": {"shape": "CapabilityHealthReasonList", "documentation": "<p>Health reasons for a dataflow endpoint. This field is ignored when calling <code>CreateDataflowEndpointGroup</code>.</p>"}, "healthStatus": {"shape": "CapabilityHealth", "documentation": "<p>A dataflow endpoint health status. This field is ignored when calling <code>CreateDataflowEndpointGroup</code>.</p>"}, "securityDetails": {"shape": "SecurityDetails", "documentation": "<p>Endpoint security details including a list of subnets, a list of security groups and a role to connect streams to instances.</p>"}}, "documentation": "<p>Information about the endpoint details.</p>"}, "EndpointDetailsList": {"type": "list", "member": {"shape": "EndpointDetails"}, "max": 500, "min": 0}, "EndpointStatus": {"type": "string", "enum": ["created", "creating", "deleted", "deleting", "failed"]}, "EphemeridesList": {"type": "list", "member": {"shape": "EphemerisItem"}, "max": 500, "min": 1}, "EphemerisData": {"type": "structure", "members": {"oem": {"shape": "OEMEphemeris"}, "tle": {"shape": "TLEEphemeris"}}, "documentation": "<p>Ephemeris data.</p>", "union": true}, "EphemerisDescription": {"type": "structure", "members": {"ephemerisData": {"shape": "UnboundedString", "documentation": "<p>Supplied ephemeris data.</p>"}, "sourceS3Object": {"shape": "S3Object", "documentation": "<p>Source S3 object used for the ephemeris.</p>"}}, "documentation": "<p>Description of ephemeris.</p>"}, "EphemerisIdResponse": {"type": "structure", "members": {"ephemerisId": {"shape": "<PERSON><PERSON>", "documentation": "<p>The AWS Ground Station ephemeris ID.</p>"}}}, "EphemerisInvalidReason": {"type": "string", "enum": ["METADATA_INVALID", "TIME_RANGE_INVALID", "TRAJECTORY_INVALID", "KMS_KEY_INVALID", "VALIDATION_ERROR"]}, "EphemerisItem": {"type": "structure", "members": {"creationTime": {"shape": "Timestamp", "documentation": "<p>The time the ephemeris was uploaded in UTC.</p>"}, "enabled": {"shape": "Boolean", "documentation": "<p>Whether or not the ephemeris is enabled.</p>"}, "ephemerisId": {"shape": "<PERSON><PERSON>", "documentation": "<p>The AWS Ground Station ephemeris ID.</p>"}, "name": {"shape": "SafeName", "documentation": "<p>A name string associated with the ephemeris. Used as a human-readable identifier for the ephemeris.</p>"}, "priority": {"shape": "EphemerisPriority", "documentation": "<p>Customer-provided priority score to establish the order in which overlapping ephemerides should be used.</p> <p>The default for customer-provided ephemeris priority is 1, and higher numbers take precedence.</p> <p>Priority must be 1 or greater</p>"}, "sourceS3Object": {"shape": "S3Object", "documentation": "<p>Source S3 object used for the ephemeris.</p>"}, "status": {"shape": "EphemerisStatus", "documentation": "<p>The status of the ephemeris.</p>"}}, "documentation": "<p>Ephemeris item.</p>"}, "EphemerisMetaData": {"type": "structure", "required": ["source"], "members": {"ephemerisId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of a customer-provided ephemeris.</p> <p>This field is not populated for default ephemerides from Space Track.</p>"}, "epoch": {"shape": "Timestamp", "documentation": "<p>The epoch of a default, ephemeris from Space Track in UTC.</p> <p>This field is not populated for customer-provided ephemerides.</p>"}, "name": {"shape": "SafeName", "documentation": "<p>A name string associated with the ephemeris. Used as a human-readable identifier for the ephemeris.</p> <p>A name is only returned for customer-provider ephemerides that have a name associated.</p>"}, "source": {"shape": "EphemerisSource", "documentation": "<p>The <code>EphemerisSource</code> that generated a given ephemeris.</p>"}}, "documentation": "<p><PERSON><PERSON><PERSON> describing a particular ephemeris.</p>"}, "EphemerisPriority": {"type": "integer", "box": true, "max": 99999, "min": 0}, "EphemerisSource": {"type": "string", "enum": ["CUSTOMER_PROVIDED", "SPACE_TRACK"]}, "EphemerisStatus": {"type": "string", "enum": ["VALIDATING", "INVALID", "ERROR", "ENABLED", "DISABLED", "EXPIRED"]}, "EphemerisStatusList": {"type": "list", "member": {"shape": "EphemerisStatus"}, "max": 500, "min": 0}, "EphemerisTypeDescription": {"type": "structure", "members": {"oem": {"shape": "EphemerisDescription"}, "tle": {"shape": "EphemerisDescription"}}, "documentation": "<p/>", "union": true}, "Frequency": {"type": "structure", "required": ["units", "value"], "members": {"units": {"shape": "FrequencyUnits", "documentation": "<p>Frequency units.</p>"}, "value": {"shape": "Double", "documentation": "<p>Frequency value. Valid values are between 2200 to 2300 MHz and 7750 to 8400 MHz for downlink and 2025 to 2120 MHz for uplink.</p>"}}, "documentation": "<p>Object that describes the frequency.</p>"}, "FrequencyBandwidth": {"type": "structure", "required": ["units", "value"], "members": {"units": {"shape": "BandwidthUnits", "documentation": "<p>Frequency bandwidth units.</p>"}, "value": {"shape": "Double", "documentation": "<p>Frequency bandwidth value. AWS Ground Station currently has the following bandwidth limitations:</p> <ul> <li> <p>For <code>AntennaDownlinkDemodDecodeconfig</code>, valid values are between 125 kHz to 650 MHz.</p> </li> <li> <p>For <code>AntennaDownlinkconfig</code>, valid values are between 10 kHz to 54 MHz.</p> </li> <li> <p>For <code>AntennaUplinkConfig</code>, valid values are between 10 kHz to 54 MHz.</p> </li> </ul>"}}, "documentation": "<p>Object that describes the frequency bandwidth. </p>"}, "FrequencyUnits": {"type": "string", "enum": ["GHz", "MHz", "kHz"]}, "GetAgentConfigurationRequest": {"type": "structure", "required": ["agentId"], "members": {"agentId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of agent to get configuration information for.</p>", "location": "uri", "locationName": "agentId"}}}, "GetAgentConfigurationResponse": {"type": "structure", "members": {"agentId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of agent.</p>"}, "taskingDocument": {"shape": "String", "documentation": "<p>Tasking document for agent.</p>"}}}, "GetConfigRequest": {"type": "structure", "required": ["configId", "configType"], "members": {"configId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of a <code>Config</code>.</p>", "location": "uri", "locationName": "configId"}, "configType": {"shape": "ConfigCapabilityType", "documentation": "<p>Type of a <code>Config</code>.</p>", "location": "uri", "locationName": "configType"}}, "documentation": "<p/>"}, "GetConfigResponse": {"type": "structure", "required": ["configArn", "configData", "configId", "name"], "members": {"configArn": {"shape": "ConfigArn", "documentation": "<p>ARN of a <code>Config</code> </p>"}, "configData": {"shape": "ConfigTypeData", "documentation": "<p>Data elements in a <code>Config</code>.</p>"}, "configId": {"shape": "String", "documentation": "<p>UUID of a <code>Config</code>.</p>"}, "configType": {"shape": "ConfigCapabilityType", "documentation": "<p>Type of a <code>Config</code>.</p>"}, "name": {"shape": "String", "documentation": "<p>Name of a <code>Config</code>.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>Tags assigned to a <code>Config</code>.</p>"}}, "documentation": "<p/>"}, "GetDataflowEndpointGroupRequest": {"type": "structure", "required": ["dataflowEndpointGroupId"], "members": {"dataflowEndpointGroupId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of a dataflow endpoint group.</p>", "location": "uri", "locationName": "dataflowEndpointGroupId"}}, "documentation": "<p/>"}, "GetDataflowEndpointGroupResponse": {"type": "structure", "members": {"contactPostPassDurationSeconds": {"shape": "DataflowEndpointGroupDurationInSeconds", "documentation": "<p>Amount of time, in seconds, after a contact ends that the Ground Station Dataflow Endpoint Group will be in a <code>POSTPASS</code> state. A Ground Station Dataflow Endpoint Group State Change event will be emitted when the Dataflow Endpoint Group enters and exits the <code>POSTPASS</code> state.</p>"}, "contactPrePassDurationSeconds": {"shape": "DataflowEndpointGroupDurationInSeconds", "documentation": "<p>Amount of time, in seconds, before a contact starts that the Ground Station Dataflow Endpoint Group will be in a <code>PREPASS</code> state. A Ground Station Dataflow Endpoint Group State Change event will be emitted when the Dataflow Endpoint Group enters and exits the <code>PREPASS</code> state.</p>"}, "dataflowEndpointGroupArn": {"shape": "DataflowEndpointGroupArn", "documentation": "<p>ARN of a dataflow endpoint group.</p>"}, "dataflowEndpointGroupId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of a dataflow endpoint group.</p>"}, "endpointsDetails": {"shape": "EndpointDetailsList", "documentation": "<p>Details of a dataflow endpoint.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>Tags assigned to a dataflow endpoint group.</p>"}}, "documentation": "<p/>"}, "GetMinuteUsageRequest": {"type": "structure", "required": ["month", "year"], "members": {"month": {"shape": "Month", "documentation": "<p>The month being requested, with a value of 1-12.</p>"}, "year": {"shape": "Year", "documentation": "<p>The year being requested, in the format of YYYY.</p>"}}, "documentation": "<p/>"}, "GetMinuteUsageResponse": {"type": "structure", "members": {"estimatedMinutesRemaining": {"shape": "Integer", "documentation": "<p>Estimated number of minutes remaining for an account, specific to the month being requested.</p>"}, "isReservedMinutesCustomer": {"shape": "Boolean", "documentation": "<p>Returns whether or not an account has signed up for the reserved minutes pricing plan, specific to the month being requested.</p>"}, "totalReservedMinuteAllocation": {"shape": "Integer", "documentation": "<p>Total number of reserved minutes allocated, specific to the month being requested.</p>"}, "totalScheduledMinutes": {"shape": "Integer", "documentation": "<p>Total scheduled minutes for an account, specific to the month being requested.</p>"}, "upcomingMinutesScheduled": {"shape": "Integer", "documentation": "<p>Upcoming minutes scheduled for an account, specific to the month being requested.</p>"}}, "documentation": "<p/>"}, "GetMissionProfileRequest": {"type": "structure", "required": ["missionProfileId"], "members": {"missionProfileId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of a mission profile.</p>", "location": "uri", "locationName": "missionProfileId"}}, "documentation": "<p/>"}, "GetMissionProfileResponse": {"type": "structure", "members": {"contactPostPassDurationSeconds": {"shape": "DurationInSeconds", "documentation": "<p>Amount of time after a contact ends that you’d like to receive a CloudWatch event indicating the pass has finished.</p>"}, "contactPrePassDurationSeconds": {"shape": "DurationInSeconds", "documentation": "<p>Amount of time prior to contact start you’d like to receive a CloudWatch event indicating an upcoming pass.</p>"}, "dataflowEdges": {"shape": "DataflowEdgeList", "documentation": "<p>A list of lists of ARNs. Each list of ARNs is an edge, with a <i>from</i> <code>Config</code> and a <i>to</i> <code>Config</code>.</p>"}, "minimumViableContactDurationSeconds": {"shape": "PositiveDurationInSeconds", "documentation": "<p>Smallest amount of time in seconds that you’d like to see for an available contact. AWS Ground Station will not present you with contacts shorter than this duration.</p>"}, "missionProfileArn": {"shape": "MissionProfileArn", "documentation": "<p>ARN of a mission profile.</p>"}, "missionProfileId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of a mission profile.</p>"}, "name": {"shape": "SafeName", "documentation": "<p>Name of a mission profile.</p>"}, "region": {"shape": "AWSRegion", "documentation": "<p>Region of a mission profile.</p>"}, "streamsKmsKey": {"shape": "KmsKey", "documentation": "<p>KMS key to use for encrypting streams.</p>"}, "streamsKmsRole": {"shape": "RoleArn", "documentation": "<p>Role to use for encrypting streams with KMS key.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>Tags assigned to a mission profile.</p>"}, "trackingConfigArn": {"shape": "ConfigArn", "documentation": "<p>ARN of a tracking <code>Config</code>.</p>"}}, "documentation": "<p/>"}, "GetSatelliteRequest": {"type": "structure", "required": ["satelliteId"], "members": {"satelliteId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of a satellite.</p>", "location": "uri", "locationName": "satelliteId"}}, "documentation": "<p/>"}, "GetSatelliteResponse": {"type": "structure", "members": {"currentEphemeris": {"shape": "EphemerisMetaData", "documentation": "<p>The current ephemeris being used to compute the trajectory of the satellite.</p>"}, "groundStations": {"shape": "GroundStationIdList", "documentation": "<p>A list of ground stations to which the satellite is on-boarded.</p>"}, "noradSatelliteID": {"shape": "noradSatelliteID", "documentation": "<p>NORAD satellite ID number.</p>"}, "satelliteArn": {"shape": "satelliteArn", "documentation": "<p>ARN of a satellite.</p>"}, "satelliteId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of a satellite.</p>"}}, "documentation": "<p/>"}, "GroundStationData": {"type": "structure", "members": {"groundStationId": {"shape": "GroundStationName", "documentation": "<p>UUID of a ground station.</p>"}, "groundStationName": {"shape": "GroundStationName", "documentation": "<p>Name of a ground station.</p>"}, "region": {"shape": "AWSRegion", "documentation": "<p>Ground station Region.</p>"}}, "documentation": "<p>Information about the ground station data.</p>"}, "GroundStationIdList": {"type": "list", "member": {"shape": "GroundStationName"}, "max": 500, "min": 0}, "GroundStationList": {"type": "list", "member": {"shape": "GroundStationData"}}, "GroundStationName": {"type": "string", "max": 500, "min": 4, "pattern": "^[ a-zA-Z0-9-._:=]{4,256}$"}, "InstanceId": {"type": "string", "max": 64, "min": 10, "pattern": "^[a-z0-9-]{10,64}$"}, "InstanceType": {"type": "string", "max": 64, "min": 1, "pattern": "^[a-z0-9.-]{1,64}$"}, "Integer": {"type": "integer", "box": true}, "IntegerRange": {"type": "structure", "required": ["maximum", "minimum"], "members": {"maximum": {"shape": "Integer", "documentation": "<p>A maximum value.</p>"}, "minimum": {"shape": "Integer", "documentation": "<p>A minimum value.</p>"}}, "documentation": "<p>An integer range that has a minimum and maximum value.</p>"}, "InvalidParameterException": {"type": "structure", "members": {"message": {"shape": "String"}, "parameterName": {"shape": "String", "documentation": "<p/>"}}, "documentation": "<p>One or more parameters are not valid.</p>", "error": {"httpStatusCode": 431, "senderFault": true}, "exception": true}, "IpAddressList": {"type": "list", "member": {"shape": "IpV4Address"}, "max": 20, "min": 1}, "IpV4Address": {"type": "string", "max": 16, "min": 7, "pattern": "^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}$"}, "JsonString": {"type": "string", "max": 8192, "min": 2, "pattern": "^[{}\\[\\]:.,\"0-9A-z\\-_\\s]{2,8192}$"}, "KeyAliasArn": {"type": "string", "max": 512, "min": 1, "pattern": "^arn:aws[a-zA-Z-]{0,16}:kms:[a-z]{2}(-[a-z]{1,16}){1,3}-\\d{1}:\\d{12}:((alias/[a-zA-Z0-9:/_-]{1,256}))$"}, "KeyAliasName": {"type": "string", "max": 256, "min": 1, "pattern": "^alias/[a-zA-Z0-9:/_-]+$"}, "KeyArn": {"type": "string"}, "KmsKey": {"type": "structure", "members": {"kmsAliasArn": {"shape": "KeyAliasArn", "documentation": "<p>KMS Alias Arn.</p>"}, "kmsAliasName": {"shape": "KeyAliasName", "documentation": "<p>KMS Alias Name.</p>"}, "kmsKeyArn": {"shape": "KeyArn", "documentation": "<p>KMS Key Arn.</p>"}}, "documentation": "<p>AWS Key Management Service (KMS) Key.</p>", "union": true}, "ListConfigsRequest": {"type": "structure", "members": {"maxResults": {"shape": "PaginationMaxResults", "documentation": "<p>Maximum number of <code>Configs</code> returned.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>Next token returned in the request of a previous <code>ListConfigs</code> call. Used to get the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}, "documentation": "<p/>"}, "ListConfigsResponse": {"type": "structure", "members": {"configList": {"shape": "ConfigList", "documentation": "<p>List of <code>Config</code> items.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>Next token returned in the response of a previous <code>ListConfigs</code> call. Used to get the next page of results.</p>"}}, "documentation": "<p/>"}, "ListContactsRequest": {"type": "structure", "required": ["endTime", "startTime", "statusList"], "members": {"endTime": {"shape": "Timestamp", "documentation": "<p>End time of a contact in UTC.</p>"}, "groundStation": {"shape": "GroundStationName", "documentation": "<p>Name of a ground station.</p>"}, "maxResults": {"shape": "PaginationMaxResults", "documentation": "<p>Maximum number of contacts returned.</p>"}, "missionProfileArn": {"shape": "MissionProfileArn", "documentation": "<p>ARN of a mission profile.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>Next token returned in the request of a previous <code>ListContacts</code> call. Used to get the next page of results.</p>"}, "satelliteArn": {"shape": "satelliteArn", "documentation": "<p>ARN of a satellite.</p>"}, "startTime": {"shape": "Timestamp", "documentation": "<p>Start time of a contact in UTC.</p>"}, "statusList": {"shape": "StatusList", "documentation": "<p>Status of a contact reservation.</p>"}}, "documentation": "<p/>"}, "ListContactsResponse": {"type": "structure", "members": {"contactList": {"shape": "ContactList", "documentation": "<p>List of contacts.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>Next token returned in the response of a previous <code>ListContacts</code> call. Used to get the next page of results.</p>"}}, "documentation": "<p/>"}, "ListDataflowEndpointGroupsRequest": {"type": "structure", "members": {"maxResults": {"shape": "PaginationMaxResults", "documentation": "<p>Maximum number of dataflow endpoint groups returned.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>Next token returned in the request of a previous <code>ListDataflowEndpointGroups</code> call. Used to get the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}, "documentation": "<p/>"}, "ListDataflowEndpointGroupsResponse": {"type": "structure", "members": {"dataflowEndpointGroupList": {"shape": "DataflowEndpointGroupList", "documentation": "<p>A list of dataflow endpoint groups.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>Next token returned in the response of a previous <code>ListDataflowEndpointGroups</code> call. Used to get the next page of results.</p>"}}, "documentation": "<p/>"}, "ListEphemeridesRequest": {"type": "structure", "required": ["endTime", "satelliteId", "startTime"], "members": {"endTime": {"shape": "Timestamp", "documentation": "<p>The end time to list in UTC. The operation will return an ephemeris if its expiration time is within the time range defined by the <code>startTime</code> and <code>endTime</code>.</p>"}, "maxResults": {"shape": "PaginationMaxResults", "documentation": "<p>Maximum number of ephemerides to return.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>Pagination token.</p>", "location": "querystring", "locationName": "nextToken"}, "satelliteId": {"shape": "<PERSON><PERSON>", "documentation": "<p>The AWS Ground Station satellite ID to list ephemeris for.</p>"}, "startTime": {"shape": "Timestamp", "documentation": "<p>The start time to list in UTC. The operation will return an ephemeris if its expiration time is within the time range defined by the <code>startTime</code> and <code>endTime</code>.</p>"}, "statusList": {"shape": "EphemerisStatusList", "documentation": "<p>The list of ephemeris status to return.</p>"}}}, "ListEphemeridesResponse": {"type": "structure", "members": {"ephemerides": {"shape": "EphemeridesList", "documentation": "<p>List of ephemerides.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>Pagination token.</p>"}}}, "ListGroundStationsRequest": {"type": "structure", "members": {"maxResults": {"shape": "PaginationMaxResults", "documentation": "<p>Maximum number of ground stations returned.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>Next token that can be supplied in the next call to get the next page of ground stations.</p>", "location": "querystring", "locationName": "nextToken"}, "satelliteId": {"shape": "<PERSON><PERSON>", "documentation": "<p>Satellite ID to retrieve on-boarded ground stations.</p>", "location": "querystring", "locationName": "satelliteId"}}, "documentation": "<p/>"}, "ListGroundStationsResponse": {"type": "structure", "members": {"groundStationList": {"shape": "GroundStationList", "documentation": "<p>List of ground stations.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>Next token that can be supplied in the next call to get the next page of ground stations.</p>"}}, "documentation": "<p/>"}, "ListMissionProfilesRequest": {"type": "structure", "members": {"maxResults": {"shape": "PaginationMaxResults", "documentation": "<p>Maximum number of mission profiles returned.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>Next token returned in the request of a previous <code>ListMissionProfiles</code> call. Used to get the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}, "documentation": "<p/>"}, "ListMissionProfilesResponse": {"type": "structure", "members": {"missionProfileList": {"shape": "MissionProfileList", "documentation": "<p>List of mission profiles.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>Next token returned in the response of a previous <code>ListMissionProfiles</code> call. Used to get the next page of results.</p>"}}, "documentation": "<p/>"}, "ListSatellitesRequest": {"type": "structure", "members": {"maxResults": {"shape": "PaginationMaxResults", "documentation": "<p>Maximum number of satellites returned.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>Next token that can be supplied in the next call to get the next page of satellites.</p>", "location": "querystring", "locationName": "nextToken"}}, "documentation": "<p/>"}, "ListSatellitesResponse": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>Next token that can be supplied in the next call to get the next page of satellites.</p>"}, "satellites": {"shape": "SatelliteList", "documentation": "<p>List of satellites.</p>"}}, "documentation": "<p/>"}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "AnyArn", "documentation": "<p>ARN of a resource.</p>", "location": "uri", "locationName": "resourceArn"}}, "documentation": "<p/>"}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagsMap", "documentation": "<p>Tags assigned to a resource.</p>"}}, "documentation": "<p/>"}, "Long": {"type": "long", "box": true}, "MissionProfileArn": {"type": "string"}, "MissionProfileIdResponse": {"type": "structure", "members": {"missionProfileId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of a mission profile.</p>"}}, "documentation": "<p/>"}, "MissionProfileList": {"type": "list", "member": {"shape": "MissionProfileListItem"}}, "MissionProfileListItem": {"type": "structure", "members": {"missionProfileArn": {"shape": "MissionProfileArn", "documentation": "<p>ARN of a mission profile.</p>"}, "missionProfileId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of a mission profile.</p>"}, "name": {"shape": "SafeName", "documentation": "<p>Name of a mission profile.</p>"}, "region": {"shape": "AWSRegion", "documentation": "<p>Region of a mission profile.</p>"}}, "documentation": "<p>Item in a list of mission profiles.</p>"}, "Month": {"type": "integer", "box": true, "max": 12, "min": 1}, "OEMEphemeris": {"type": "structure", "members": {"oemData": {"shape": "UnboundedString", "documentation": "<p>The data for an OEM ephemeris, supplied directly in the request rather than through an S3 object.</p>"}, "s3Object": {"shape": "S3Object", "documentation": "<p>Identifies the S3 object to be used as the ephemeris.</p>"}}, "documentation": "<p>Ephemeris data in Orbit Ephemeris Message (OEM) format.</p>"}, "PaginationMaxResults": {"type": "integer", "box": true, "max": 100, "min": 0}, "PaginationToken": {"type": "string", "max": 1000, "min": 3, "pattern": "^[A-Za-z0-9-/+_.=]+$"}, "Polarization": {"type": "string", "enum": ["LEFT_HAND", "NONE", "RIGHT_HAND"]}, "PositiveDurationInSeconds": {"type": "integer", "box": true, "max": 21600, "min": 1}, "RangedConnectionDetails": {"type": "structure", "required": ["socketAddress"], "members": {"mtu": {"shape": "RangedConnectionDetailsMtuInteger", "documentation": "<p>Maximum transmission unit (MTU) size in bytes of a dataflow endpoint.</p>"}, "socketAddress": {"shape": "RangedSocketAddress", "documentation": "<p>A ranged socket address.</p>"}}, "documentation": "<p>Ingress address of AgentEndpoint with a port range and an optional mtu.</p>"}, "RangedConnectionDetailsMtuInteger": {"type": "integer", "box": true, "max": 1500, "min": 1400}, "RangedSocketAddress": {"type": "structure", "required": ["name", "portRange"], "members": {"name": {"shape": "IpV4Address", "documentation": "<p>IPv4 socket address.</p>"}, "portRange": {"shape": "IntegerRange", "documentation": "<p>Port range of a socket address.</p>"}}, "documentation": "<p>A socket address with a port range.</p>"}, "RegisterAgentRequest": {"type": "structure", "required": ["agentDetails", "discoveryData"], "members": {"agentDetails": {"shape": "AgentDetails", "documentation": "<p>Detailed information about the agent being registered.</p>"}, "discoveryData": {"shape": "DiscoveryData", "documentation": "<p>Data for associating an agent with the capabilities it is managing.</p>"}}}, "RegisterAgentResponse": {"type": "structure", "members": {"agentId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of registered agent.</p>"}}}, "ReserveContactRequest": {"type": "structure", "required": ["endTime", "groundStation", "missionProfileArn", "satelliteArn", "startTime"], "members": {"endTime": {"shape": "Timestamp", "documentation": "<p>End time of a contact in UTC.</p>"}, "groundStation": {"shape": "GroundStationName", "documentation": "<p>Name of a ground station.</p>"}, "missionProfileArn": {"shape": "MissionProfileArn", "documentation": "<p>ARN of a mission profile.</p>"}, "satelliteArn": {"shape": "satelliteArn", "documentation": "<p>ARN of a satellite</p>"}, "startTime": {"shape": "Timestamp", "documentation": "<p>Start time of a contact in UTC.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>Tags assigned to a contact.</p>"}}, "documentation": "<p/>"}, "ResourceLimitExceededException": {"type": "structure", "members": {"message": {"shape": "String"}, "parameterName": {"shape": "String", "documentation": "<p/>"}}, "documentation": "<p>Account limits for this resource have been exceeded.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>Resource was not found.</p>", "error": {"httpStatusCode": 434, "senderFault": true}, "exception": true}, "RoleArn": {"type": "string"}, "S3BucketName": {"type": "string", "max": 63, "min": 3, "pattern": "^[a-z0-9.-]{3,63}$"}, "S3KeyPrefix": {"type": "string", "max": 900, "min": 1, "pattern": "^([a-zA-Z0-9_\\-=/]|\\{satellite_id\\}|\\{config\\-name}|\\{s3\\-config-id}|\\{year\\}|\\{month\\}|\\{day\\}){1,900}$"}, "S3Object": {"type": "structure", "members": {"bucket": {"shape": "S3BucketName", "documentation": "<p>An Amazon S3 Bucket name.</p>"}, "key": {"shape": "S3ObjectKey", "documentation": "<p>An Amazon S3 key for the ephemeris.</p>"}, "version": {"shape": "S3VersionId", "documentation": "<p>For versioned S3 objects, the version to use for the ephemeris.</p>"}}, "documentation": "<p>Object stored in S3 containing ephemeris data.</p>"}, "S3ObjectKey": {"type": "string", "max": 1024, "min": 1, "pattern": "^[a-zA-Z0-9!*'\\)\\(./_-]{1,1024}$"}, "S3RecordingConfig": {"type": "structure", "required": ["bucketArn", "roleArn"], "members": {"bucketArn": {"shape": "BucketArn", "documentation": "<p>ARN of the bucket to record to.</p>"}, "prefix": {"shape": "S3KeyPrefix", "documentation": "<p>S3 Key prefix to prefice data files.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>ARN of the role Ground Station assumes to write data to the bucket.</p>"}}, "documentation": "<p>Information about an S3 recording <code>Config</code>.</p>"}, "S3RecordingDetails": {"type": "structure", "members": {"bucketArn": {"shape": "BucketArn", "documentation": "<p>ARN of the bucket used.</p>"}, "keyTemplate": {"shape": "String", "documentation": "<p>Key template used for the S3 Recording Configuration</p>"}}, "documentation": "<p>Details about an S3 recording <code>Config</code> used in a contact.</p>"}, "S3VersionId": {"type": "string", "max": 1024, "min": 1, "pattern": "^[\\s\\S]{1,1024}$"}, "SafeName": {"type": "string", "max": 256, "min": 1, "pattern": "^[ a-zA-Z0-9_:-]{1,256}$"}, "SatelliteList": {"type": "list", "member": {"shape": "SatelliteListItem"}}, "SatelliteListItem": {"type": "structure", "members": {"currentEphemeris": {"shape": "EphemerisMetaData", "documentation": "<p>The current ephemeris being used to compute the trajectory of the satellite.</p>"}, "groundStations": {"shape": "GroundStationIdList", "documentation": "<p>A list of ground stations to which the satellite is on-boarded.</p>"}, "noradSatelliteID": {"shape": "noradSatelliteID", "documentation": "<p>NORAD satellite ID number.</p>"}, "satelliteArn": {"shape": "satelliteArn", "documentation": "<p>ARN of a satellite.</p>"}, "satelliteId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of a satellite.</p>"}}, "documentation": "<p>Item in a list of satellites.</p>"}, "SecurityDetails": {"type": "structure", "required": ["roleArn", "securityGroupIds", "subnetIds"], "members": {"roleArn": {"shape": "RoleArn", "documentation": "<p>ARN to a role needed for connecting streams to your instances. </p>"}, "securityGroupIds": {"shape": "SecurityGroupIdList", "documentation": "<p>The security groups to attach to the elastic network interfaces.</p>"}, "subnetIds": {"shape": "SubnetList", "documentation": "<p>A list of subnets where AWS Ground Station places elastic network interfaces to send streams to your instances.</p>"}}, "documentation": "<p>Information about endpoints.</p>"}, "SecurityGroupIdList": {"type": "list", "member": {"shape": "String"}}, "SignatureMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "Boolean"}}, "SocketAddress": {"type": "structure", "required": ["name", "port"], "members": {"name": {"shape": "String", "documentation": "<p>Name of a socket address.</p>"}, "port": {"shape": "Integer", "documentation": "<p>Port of a socket address.</p>"}}, "documentation": "<p>Information about the socket address.</p>"}, "Source": {"type": "structure", "members": {"configDetails": {"shape": "ConfigDetails", "documentation": "<p>Additional details for a <code>Config</code>, if type is <code>dataflow-endpoint</code> or <code>antenna-downlink-demod-decode</code> </p>"}, "configId": {"shape": "String", "documentation": "<p>UUID of a <code>Config</code>.</p>"}, "configType": {"shape": "ConfigCapabilityType", "documentation": "<p>Type of a <code>Config</code>.</p>"}, "dataflowSourceRegion": {"shape": "String", "documentation": "<p>Region of a dataflow source.</p>"}}, "documentation": "<p>Dataflow details for the source side.</p>"}, "SpectrumConfig": {"type": "structure", "required": ["bandwidth", "centerFrequency"], "members": {"bandwidth": {"shape": "FrequencyBandwidth", "documentation": "<p>Bandwidth of a spectral <code>Config</code>. AWS Ground Station currently has the following bandwidth limitations:</p> <ul> <li> <p>For <code>AntennaDownlinkDemodDecodeconfig</code>, valid values are between 125 kHz to 650 MHz.</p> </li> <li> <p>For <code>AntennaDownlinkconfig</code> valid values are between 10 kHz to 54 MHz.</p> </li> <li> <p>For <code>AntennaUplinkConfig</code>, valid values are between 10 kHz to 54 MHz.</p> </li> </ul>"}, "centerFrequency": {"shape": "Frequency", "documentation": "<p>Center frequency of a spectral <code>Config</code>. Valid values are between 2200 to 2300 MHz and 7750 to 8400 MHz for downlink and 2025 to 2120 MHz for uplink.</p>"}, "polarization": {"shape": "Polarization", "documentation": "<p>Polarization of a spectral <code>Config</code>. Capturing both <code>\"RIGHT_HAND\"</code> and <code>\"LEFT_HAND\"</code> polarization requires two separate configs.</p>"}}, "documentation": "<p>Object that describes a spectral <code>Config</code>.</p>"}, "StatusList": {"type": "list", "member": {"shape": "ContactStatus"}, "max": 500, "min": 0}, "String": {"type": "string"}, "SubnetList": {"type": "list", "member": {"shape": "String"}}, "TLEData": {"type": "structure", "required": ["tleLine1", "tleLine2", "validTimeRange"], "members": {"tleLine1": {"shape": "TleLineOne", "documentation": "<p>First line of two-line element set (TLE) data.</p>"}, "tleLine2": {"shape": "TleLineTwo", "documentation": "<p>Second line of two-line element set (TLE) data.</p>"}, "validTimeRange": {"shape": "TimeRange", "documentation": "<p>The valid time range for the TLE. Gaps or overlap are not permitted.</p>"}}, "documentation": "<p>Two-line element set (TLE) data.</p>"}, "TLEDataList": {"type": "list", "member": {"shape": "TLEData"}, "max": 500, "min": 1}, "TLEEphemeris": {"type": "structure", "members": {"s3Object": {"shape": "S3Object", "documentation": "<p>Identifies the S3 object to be used as the ephemeris.</p>"}, "tleData": {"shape": "TLEDataList", "documentation": "<p>The data for a TLE ephemeris, supplied directly in the request rather than through an S3 object.</p>"}}, "documentation": "<p>Two-line element set (TLE) ephemeris.</p>"}, "TagKeys": {"type": "list", "member": {"shape": "UnboundedString"}, "max": 500, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "AnyArn", "documentation": "<p>ARN of a resource tag.</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagsMap", "documentation": "<p>Tags assigned to a resource.</p>"}}, "documentation": "<p/>"}, "TagResourceResponse": {"type": "structure", "members": {}, "documentation": "<p/>"}, "TagsMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "TimeRange": {"type": "structure", "required": ["endTime", "startTime"], "members": {"endTime": {"shape": "Timestamp", "documentation": "<p>Time in UTC at which the time range ends.</p>"}, "startTime": {"shape": "Timestamp", "documentation": "<p>Time in UTC at which the time range starts.</p>"}}, "documentation": "<p>A time range with a start and end time.</p>"}, "Timestamp": {"type": "timestamp"}, "TleLineOne": {"type": "string", "max": 69, "min": 69, "pattern": "^1 [ 0-9]{5}[A-Z] [ 0-9]{5}[ A-Z]{3} [ 0-9]{5}[.][ 0-9]{8} (?:(?:[ 0+-][.][ 0-9]{8})|(?: [ +-][.][ 0-9]{7})) [ +-][ 0-9]{5}[+-][ 0-9] [ +-][ 0-9]{5}[+-][ 0-9] [ 0-9] [ 0-9]{4}[ 0-9]$"}, "TleLineTwo": {"type": "string", "max": 69, "min": 69, "pattern": "^2 [ 0-9]{5} [ 0-9]{3}[.][ 0-9]{4} [ 0-9]{3}[.][ 0-9]{4} [ 0-9]{7} [ 0-9]{3}[.][ 0-9]{4} [ 0-9]{3}[.][ 0-9]{4} [ 0-9]{2}[.][ 0-9]{13}[ 0-9]$"}, "TrackingConfig": {"type": "structure", "required": ["autotrack"], "members": {"autotrack": {"shape": "Criticality", "documentation": "<p>Current setting for autotrack.</p>"}}, "documentation": "<p>Object that determines whether tracking should be used during a contact executed with this <code>Config</code> in the mission profile.</p>"}, "UnboundedString": {"type": "string", "min": 1, "pattern": "^[\\s\\S]+$"}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "AnyArn", "documentation": "<p>ARN of a resource.</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeys", "documentation": "<p>Keys of a resource tag.</p>", "location": "querystring", "locationName": "tagKeys"}}, "documentation": "<p/>"}, "UntagResourceResponse": {"type": "structure", "members": {}, "documentation": "<p/>"}, "UpdateAgentStatusRequest": {"type": "structure", "required": ["agentId", "aggregateStatus", "componentStatuses", "taskId"], "members": {"agentId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of agent to update.</p>", "location": "uri", "locationName": "agentId"}, "aggregateStatus": {"shape": "AggregateStatus", "documentation": "<p>Aggregate status for agent.</p>"}, "componentStatuses": {"shape": "ComponentStatusList", "documentation": "<p>List of component statuses for agent.</p>"}, "taskId": {"shape": "<PERSON><PERSON>", "documentation": "<p>GUID of agent task.</p>"}}}, "UpdateAgentStatusResponse": {"type": "structure", "required": ["agentId"], "members": {"agentId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of updated agent.</p>"}}}, "UpdateConfigRequest": {"type": "structure", "required": ["configData", "configId", "configType", "name"], "members": {"configData": {"shape": "ConfigTypeData", "documentation": "<p>Parameters of a <code>Config</code>.</p>"}, "configId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of a <code>Config</code>.</p>", "location": "uri", "locationName": "configId"}, "configType": {"shape": "ConfigCapabilityType", "documentation": "<p>Type of a <code>Config</code>.</p>", "location": "uri", "locationName": "configType"}, "name": {"shape": "SafeName", "documentation": "<p>Name of a <code>Config</code>.</p>"}}, "documentation": "<p/>"}, "UpdateEphemerisRequest": {"type": "structure", "required": ["enabled", "ephemerisId"], "members": {"enabled": {"shape": "Boolean", "documentation": "<p>Whether the ephemeris is enabled or not. Changing this value will not require the ephemeris to be re-validated.</p>"}, "ephemerisId": {"shape": "<PERSON><PERSON>", "documentation": "<p>The AWS Ground Station ephemeris ID.</p>", "location": "uri", "locationName": "ephemerisId"}, "name": {"shape": "SafeName", "documentation": "<p>A name string associated with the ephemeris. Used as a human-readable identifier for the ephemeris.</p>"}, "priority": {"shape": "EphemerisPriority", "documentation": "<p>Customer-provided priority score to establish the order in which overlapping ephemerides should be used.</p> <p>The default for customer-provided ephemeris priority is 1, and higher numbers take precedence.</p> <p>Priority must be 1 or greater</p>"}}}, "UpdateMissionProfileRequest": {"type": "structure", "required": ["missionProfileId"], "members": {"contactPostPassDurationSeconds": {"shape": "DurationInSeconds", "documentation": "<p>Amount of time after a contact ends that you’d like to receive a CloudWatch event indicating the pass has finished.</p>"}, "contactPrePassDurationSeconds": {"shape": "DurationInSeconds", "documentation": "<p>Amount of time after a contact ends that you’d like to receive a CloudWatch event indicating the pass has finished.</p>"}, "dataflowEdges": {"shape": "DataflowEdgeList", "documentation": "<p>A list of lists of ARNs. Each list of ARNs is an edge, with a <i>from</i> <code>Config</code> and a <i>to</i> <code>Config</code>.</p>"}, "minimumViableContactDurationSeconds": {"shape": "PositiveDurationInSeconds", "documentation": "<p>Smallest amount of time in seconds that you’d like to see for an available contact. AWS Ground Station will not present you with contacts shorter than this duration.</p>"}, "missionProfileId": {"shape": "<PERSON><PERSON>", "documentation": "<p>UUID of a mission profile.</p>", "location": "uri", "locationName": "missionProfileId"}, "name": {"shape": "SafeName", "documentation": "<p>Name of a mission profile.</p>"}, "streamsKmsKey": {"shape": "KmsKey", "documentation": "<p>KMS key to use for encrypting streams.</p>"}, "streamsKmsRole": {"shape": "RoleArn", "documentation": "<p>Role to use for encrypting streams with KMS key.</p>"}, "trackingConfigArn": {"shape": "ConfigArn", "documentation": "<p>ARN of a tracking <code>Config</code>.</p>"}}, "documentation": "<p/>"}, "UplinkEchoConfig": {"type": "structure", "required": ["antennaUplinkConfigArn", "enabled"], "members": {"antennaUplinkConfigArn": {"shape": "ConfigArn", "documentation": "<p>ARN of an uplink <code>Config</code>.</p>"}, "enabled": {"shape": "Boolean", "documentation": "<p>Whether or not an uplink <code>Config</code> is enabled.</p>"}}, "documentation": "<p>Information about an uplink echo <code>Config</code>.</p> <p>Parameters from the <code>AntennaUplinkConfig</code>, corresponding to the specified <code>AntennaUplinkConfigArn</code>, are used when this <code>UplinkEchoConfig</code> is used in a contact.</p>"}, "UplinkSpectrumConfig": {"type": "structure", "required": ["centerFrequency"], "members": {"centerFrequency": {"shape": "Frequency", "documentation": "<p>Center frequency of an uplink spectral <code>Config</code>. Valid values are between 2025 to 2120 MHz.</p>"}, "polarization": {"shape": "Polarization", "documentation": "<p>Polarization of an uplink spectral <code>Config</code>. Capturing both <code>\"RIGHT_HAND\"</code> and <code>\"LEFT_HAND\"</code> polarization requires two separate configs.</p>"}}, "documentation": "<p>Information about the uplink spectral <code>Config</code>.</p>"}, "Uuid": {"type": "string", "max": 128, "min": 1, "pattern": "^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"}, "VersionString": {"type": "string", "max": 64, "min": 1, "pattern": "^(0|[1-9]\\d*)(\\.(0|[1-9]\\d*))*$"}, "VersionStringList": {"type": "list", "member": {"shape": "VersionString"}, "max": 20, "min": 1}, "Year": {"type": "integer", "box": true, "max": 3000, "min": 2018}, "noradSatelliteID": {"type": "integer", "max": 99999, "min": 0}, "satelliteArn": {"type": "string"}}, "documentation": "<p>Welcome to the AWS Ground Station API Reference. AWS Ground Station is a fully managed service that enables you to control satellite communications, downlink and process satellite data, and scale your satellite operations efficiently and cost-effectively without having to build or manage your own ground station infrastructure.</p>"}