{"version": "2.0", "metadata": {"apiVersion": "2021-04-21", "endpointPrefix": "redshift-serverless", "jsonVersion": "1.1", "protocol": "json", "serviceFullName": "Redshift Serverless", "serviceId": "Redshift Serverless", "signatureVersion": "v4", "signingName": "redshift-serverless", "targetPrefix": "RedshiftServerless", "uid": "redshift-serverless-2021-04-21"}, "operations": {"ConvertRecoveryPointToSnapshot": {"name": "ConvertRecoveryPointToSnapshot", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ConvertRecoveryPointToSnapshotRequest"}, "output": {"shape": "ConvertRecoveryPointToSnapshotResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "TooManyTagsException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Converts a recovery point to a snapshot. For more information about recovery points and snapshots, see <a href=\"https://docs.aws.amazon.com/redshift/latest/mgmt/serverless-snapshots-recovery.html\">Working with snapshots and recovery points</a>.</p>"}, "CreateCustomDomainAssociation": {"name": "CreateCustomDomainAssociation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateCustomDomainAssociationRequest"}, "output": {"shape": "CreateCustomDomainAssociationResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Creates a custom domain association for Amazon Redshift Serverless.</p>"}, "CreateEndpointAccess": {"name": "CreateEndpointAccess", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateEndpointAccessRequest"}, "output": {"shape": "CreateEndpointAccessResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates an Amazon Redshift Serverless managed VPC endpoint.</p>", "idempotent": true}, "CreateNamespace": {"name": "CreateNamespace", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateNamespaceRequest"}, "output": {"shape": "CreateNamespaceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "TooManyTagsException"}], "documentation": "<p>Creates a namespace in Amazon Redshift Serverless.</p>", "idempotent": true}, "CreateSnapshot": {"name": "CreateSnapshot", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateSnapshotRequest"}, "output": {"shape": "CreateSnapshotResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "TooManyTagsException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a snapshot of all databases in a namespace. For more information about snapshots, see <a href=\"https://docs.aws.amazon.com/redshift/latest/mgmt/serverless-snapshots-recovery.html\"> Working with snapshots and recovery points</a>.</p>", "idempotent": true}, "CreateUsageLimit": {"name": "CreateUsageLimit", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateUsageLimitRequest"}, "output": {"shape": "CreateUsageLimitResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a usage limit for a specified Amazon Redshift Serverless usage type. The usage limit is identified by the returned usage limit identifier. </p>", "idempotent": true}, "CreateWorkgroup": {"name": "CreateWorkgroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateWorkgroupRequest"}, "output": {"shape": "CreateWorkgroupResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InsufficientCapacityException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "TooManyTagsException"}], "documentation": "<p>Creates an workgroup in Amazon Redshift Serverless.</p>", "idempotent": true}, "DeleteCustomDomainAssociation": {"name": "DeleteCustomDomainAssociation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteCustomDomainAssociationRequest"}, "output": {"shape": "DeleteCustomDomainAssociationResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes a custom domain association for Amazon Redshift Serverless.</p>"}, "DeleteEndpointAccess": {"name": "DeleteEndpointAccess", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteEndpointAccessRequest"}, "output": {"shape": "DeleteEndpointAccessResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}], "documentation": "<p>Deletes an Amazon Redshift Serverless managed VPC endpoint.</p>", "idempotent": true}, "DeleteNamespace": {"name": "DeleteNamespace", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteNamespaceRequest"}, "output": {"shape": "DeleteNamespaceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}], "documentation": "<p>Deletes a namespace from Amazon Redshift Serverless. Before you delete the namespace, you can create a final snapshot that has all of the data within the namespace.</p>", "idempotent": true}, "DeleteResourcePolicy": {"name": "DeleteResourcePolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteResourcePolicyRequest"}, "output": {"shape": "DeleteResourcePolicyResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Deletes the specified resource policy.</p>"}, "DeleteSnapshot": {"name": "DeleteSnapshot", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteSnapshotRequest"}, "output": {"shape": "DeleteSnapshotResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}], "documentation": "<p>Deletes a snapshot from Amazon Redshift Serverless.</p>", "idempotent": true}, "DeleteUsageLimit": {"name": "DeleteUsageLimit", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteUsageLimitRequest"}, "output": {"shape": "DeleteUsageLimitResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}], "documentation": "<p>Deletes a usage limit from Amazon Redshift Serverless.</p>", "idempotent": true}, "DeleteWorkgroup": {"name": "DeleteWorkgroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteWorkgroupRequest"}, "output": {"shape": "DeleteWorkgroupResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}], "documentation": "<p>Deletes a workgroup.</p>", "idempotent": true}, "GetCredentials": {"name": "GetCredentials", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetCredentialsRequest"}, "output": {"shape": "GetCredentialsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns a database user name and temporary password with temporary authorization to log in to Amazon Redshift Serverless.</p> <p>By default, the temporary credentials expire in 900 seconds. You can optionally specify a duration between 900 seconds (15 minutes) and 3600 seconds (60 minutes).</p> <pre><code> &lt;p&gt;The Identity and Access Management (IAM) user or role that runs GetCredentials must have an IAM policy attached that allows access to all necessary actions and resources.&lt;/p&gt; &lt;p&gt;If the &lt;code&gt;DbName&lt;/code&gt; parameter is specified, the IAM policy must allow access to the resource dbname for the specified database name.&lt;/p&gt; </code></pre>"}, "GetCustomDomainAssociation": {"name": "GetCustomDomainAssociation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetCustomDomainAssociationRequest"}, "output": {"shape": "GetCustomDomainAssociationResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Gets information about a specific custom domain association.</p>"}, "GetEndpointAccess": {"name": "GetEndpointAccess", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetEndpointAccessRequest"}, "output": {"shape": "GetEndpointAccessResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns information, such as the name, about a VPC endpoint.</p>"}, "GetNamespace": {"name": "GetNamespace", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetNamespaceRequest"}, "output": {"shape": "GetNamespaceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns information about a namespace in Amazon Redshift Serverless.</p>"}, "GetRecoveryPoint": {"name": "GetRecoveryPoint", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetRecoveryPointRequest"}, "output": {"shape": "GetRecoveryPointResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns information about a recovery point.</p>"}, "GetResourcePolicy": {"name": "GetResourcePolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetResourcePolicyRequest"}, "output": {"shape": "GetResourcePolicyResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns a resource policy.</p>"}, "GetSnapshot": {"name": "GetSnapshot", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetSnapshotRequest"}, "output": {"shape": "GetSnapshotResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns information about a specific snapshot.</p>"}, "GetTableRestoreStatus": {"name": "GetTableRestoreStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetTableRestoreStatusRequest"}, "output": {"shape": "GetTableRestoreStatusResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns information about a <code>TableRestoreStatus</code> object.</p>"}, "GetUsageLimit": {"name": "GetUsageLimit", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetUsageLimitRequest"}, "output": {"shape": "GetUsageLimitResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns information about a usage limit.</p>"}, "GetWorkgroup": {"name": "GetWorkgroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetWorkgroupRequest"}, "output": {"shape": "GetWorkgroupResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns information about a specific workgroup.</p>"}, "ListCustomDomainAssociations": {"name": "ListCustomDomainAssociations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListCustomDomainAssociationsRequest"}, "output": {"shape": "ListCustomDomainAssociationsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidPaginationException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p> Lists custom domain associations for Amazon Redshift Serverless.</p>"}, "ListEndpointAccess": {"name": "ListEndpointAccess", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListEndpointAccessRequest"}, "output": {"shape": "ListEndpointAccessResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns an array of <code>EndpointAccess</code> objects and relevant information.</p>"}, "ListNamespaces": {"name": "ListNamespaces", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListNamespacesRequest"}, "output": {"shape": "ListNamespacesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns information about a list of specified namespaces.</p>"}, "ListRecoveryPoints": {"name": "ListRecoveryPoints", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListRecoveryPointsRequest"}, "output": {"shape": "ListRecoveryPointsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns an array of recovery points.</p>"}, "ListSnapshots": {"name": "ListSnapshots", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListSnapshotsRequest"}, "output": {"shape": "ListSnapshotsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns a list of snapshots.</p>"}, "ListTableRestoreStatus": {"name": "ListTableRestoreStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTableRestoreStatusRequest"}, "output": {"shape": "ListTableRestoreStatusResponse"}, "errors": [{"shape": "InvalidPaginationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns information about an array of <code>TableRestoreStatus</code> objects.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Lists the tags assigned to a resource.</p>"}, "ListUsageLimits": {"name": "ListUsageLimits", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListUsageLimitsRequest"}, "output": {"shape": "ListUsageLimitsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidPaginationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists all usage limits within Amazon Redshift Serverless.</p>"}, "ListWorkgroups": {"name": "ListWorkgroups", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListWorkgroupsRequest"}, "output": {"shape": "ListWorkgroupsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns information about a list of specified workgroups.</p>"}, "PutResourcePolicy": {"name": "PutResourcePolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutResourcePolicyRequest"}, "output": {"shape": "PutResourcePolicyResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates or updates a resource policy. Currently, you can use policies to share snapshots across Amazon Web Services accounts.</p>"}, "RestoreFromRecoveryPoint": {"name": "RestoreFromRecoveryPoint", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RestoreFromRecoveryPointRequest"}, "output": {"shape": "RestoreFromRecoveryPointResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}], "documentation": "<p>Restore the data from a recovery point.</p>"}, "RestoreFromSnapshot": {"name": "RestoreFromSnapshot", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RestoreFromSnapshotRequest"}, "output": {"shape": "RestoreFromSnapshotResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Restores a namespace from a snapshot.</p>", "idempotent": true}, "RestoreTableFromSnapshot": {"name": "RestoreTableFromSnapshot", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RestoreTableFromSnapshotRequest"}, "output": {"shape": "RestoreTableFromSnapshotResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}], "documentation": "<p>Restores a table from a snapshot to your Amazon Redshift Serverless instance. You can't use this operation to restore tables with <a href=\"https://docs.aws.amazon.com/redshift/latest/dg/t_Sorting_data.html#t_Sorting_data-interleaved\">interleaved sort keys</a>.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "TooManyTagsException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Assigns one or more tags to a resource.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Removes a tag or set of tags from a resource.</p>"}, "UpdateCustomDomainAssociation": {"name": "UpdateCustomDomainAssociation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateCustomDomainAssociationRequest"}, "output": {"shape": "UpdateCustomDomainAssociationResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Updates an Amazon Redshift Serverless certificate associated with a custom domain.</p>"}, "UpdateEndpointAccess": {"name": "UpdateEndpointAccess", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateEndpointAccessRequest"}, "output": {"shape": "UpdateEndpointAccessResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates an Amazon Redshift Serverless managed endpoint.</p>"}, "UpdateNamespace": {"name": "UpdateNamespace", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateNamespaceRequest"}, "output": {"shape": "UpdateNamespaceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}], "documentation": "<p>Updates a namespace with the specified settings. Unless required, you can't update multiple parameters in one request. For example, you must specify both <code>adminUsername</code> and <code>adminUserPassword</code> to update either field, but you can't update both <code>kmsKeyId</code> and <code>logExports</code> in a single request.</p>"}, "UpdateSnapshot": {"name": "UpdateSnapshot", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateSnapshotRequest"}, "output": {"shape": "UpdateSnapshotResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}], "documentation": "<p>Updates a snapshot.</p>"}, "UpdateUsageLimit": {"name": "UpdateUsageLimit", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateUsageLimitRequest"}, "output": {"shape": "UpdateUsageLimitResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}], "documentation": "<p>Update a usage limit in Amazon Redshift Serverless. You can't update the usage type or period of a usage limit.</p>"}, "UpdateWorkgroup": {"name": "UpdateWorkgroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateWorkgroupRequest"}, "output": {"shape": "UpdateWorkgroupResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InsufficientCapacityException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}], "documentation": "<p>Updates a workgroup with the specified configuration settings. You can't update multiple parameters in one request. For example, you can update <code>baseCapacity</code> or <code>port</code> in a single request, but you can't update both in the same request.</p>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"code": {"shape": "String"}, "message": {"shape": "String"}}, "documentation": "<p>You do not have sufficient access to perform this action.</p>", "exception": true}, "AccountIdList": {"type": "list", "member": {"shape": "String"}}, "AmazonResourceName": {"type": "string", "max": 1011, "min": 1}, "Association": {"type": "structure", "members": {"customDomainCertificateArn": {"shape": "CustomDomainCertificateArnString", "documentation": "<p>The custom domain name’s certificate Amazon resource name (ARN).</p>"}, "customDomainCertificateExpiryTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The expiration time for the certificate.</p>"}, "customDomainName": {"shape": "CustomDomainName", "documentation": "<p>The custom domain name associated with the workgroup.</p>"}, "workgroupName": {"shape": "WorkgroupName", "documentation": "<p>The name of the workgroup associated with the database.</p>"}}, "documentation": "<p>An object that represents the custom domain name association.</p>"}, "AssociationList": {"type": "list", "member": {"shape": "Association"}}, "Boolean": {"type": "boolean", "box": true}, "ConfigParameter": {"type": "structure", "members": {"parameterKey": {"shape": "Parameter<PERSON>ey", "documentation": "<p>The key of the parameter. The options are <code>auto_mv</code>, <code>datestyle</code>, <code>enable_case_sensitivity_identifier</code>, <code>enable_user_activity_logging</code>, <code>query_group</code>, <code>search_path</code>, and query monitoring metrics that let you define performance boundaries. For more information about query monitoring rules and available metrics, see <a href=\"https://docs.aws.amazon.com/redshift/latest/dg/cm-c-wlm-query-monitoring-rules.html#cm-c-wlm-query-monitoring-metrics-serverless\">Query monitoring metrics for Amazon Redshift Serverless</a>.</p>"}, "parameterValue": {"shape": "ParameterValue", "documentation": "<p>The value of the parameter to set.</p>"}}, "documentation": "<p>An array of key-value pairs to set for advanced control over Amazon Redshift Serverless.</p>"}, "ConfigParameterList": {"type": "list", "member": {"shape": "ConfigParameter"}}, "ConflictException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The submitted action has conflicts.</p>", "exception": true}, "ConvertRecoveryPointToSnapshotRequest": {"type": "structure", "required": ["recoveryPointId", "snapshotName"], "members": {"recoveryPointId": {"shape": "String", "documentation": "<p>The unique identifier of the recovery point.</p>"}, "retentionPeriod": {"shape": "Integer", "documentation": "<p>How long to retain the snapshot.</p>"}, "snapshotName": {"shape": "String", "documentation": "<p>The name of the snapshot.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>An array of <a href=\"https://docs.aws.amazon.com/redshift-serverless/latest/APIReference/API_Tag.html\">Tag objects</a> to associate with the created snapshot.</p>"}}}, "ConvertRecoveryPointToSnapshotResponse": {"type": "structure", "members": {"snapshot": {"shape": "Snapshot", "documentation": "<p>The snapshot converted from the recovery point.</p>"}}}, "CreateCustomDomainAssociationRequest": {"type": "structure", "required": ["customDomainCertificateArn", "customDomainName", "workgroupName"], "members": {"customDomainCertificateArn": {"shape": "CustomDomainCertificateArnString", "documentation": "<p>The custom domain name’s certificate Amazon resource name (ARN).</p>"}, "customDomainName": {"shape": "CustomDomainName", "documentation": "<p>The custom domain name to associate with the workgroup.</p>"}, "workgroupName": {"shape": "WorkgroupName", "documentation": "<p>The name of the workgroup associated with the database.</p>"}}}, "CreateCustomDomainAssociationResponse": {"type": "structure", "members": {"customDomainCertificateArn": {"shape": "CustomDomainCertificateArnString", "documentation": "<p>The custom domain name’s certificate Amazon resource name (ARN).</p>"}, "customDomainCertificateExpiryTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The expiration time for the certificate.</p>"}, "customDomainName": {"shape": "CustomDomainName", "documentation": "<p>The custom domain name to associate with the workgroup.</p>"}, "workgroupName": {"shape": "WorkgroupName", "documentation": "<p>The name of the workgroup associated with the database.</p>"}}}, "CreateEndpointAccessRequest": {"type": "structure", "required": ["endpointName", "subnetIds", "workgroupName"], "members": {"endpointName": {"shape": "String", "documentation": "<p>The name of the VPC endpoint. An endpoint name must contain 1-30 characters. Valid characters are A-Z, a-z, 0-9, and hyphen(-). The first character must be a letter. The name can't contain two consecutive hyphens or end with a hyphen.</p>"}, "subnetIds": {"shape": "SubnetIdList", "documentation": "<p>The unique identifers of subnets from which Amazon Redshift Serverless chooses one to deploy a VPC endpoint.</p>"}, "vpcSecurityGroupIds": {"shape": "VpcSecurityGroupIdList", "documentation": "<p>The unique identifiers of the security group that defines the ports, protocols, and sources for inbound traffic that you are authorizing into your endpoint.</p>"}, "workgroupName": {"shape": "String", "documentation": "<p>The name of the workgroup to associate with the VPC endpoint.</p>"}}}, "CreateEndpointAccessResponse": {"type": "structure", "members": {"endpoint": {"shape": "EndpointAccess", "documentation": "<p>The created VPC endpoint.</p>"}}}, "CreateNamespaceRequest": {"type": "structure", "required": ["namespaceName"], "members": {"adminPasswordSecretKmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>The ID of the Key Management Service (KMS) key used to encrypt and store the namespace's admin credentials secret. You can only use this parameter if <code>manageAdminPassword</code> is true.</p>"}, "adminUserPassword": {"shape": "DbPassword", "documentation": "<p>The password of the administrator for the first database created in the namespace.</p> <p>You can't use <code>adminUserPassword</code> if <code>manageAdminPassword</code> is true. </p>"}, "adminUsername": {"shape": "DbUser", "documentation": "<p>The username of the administrator for the first database created in the namespace.</p>"}, "dbName": {"shape": "String", "documentation": "<p>The name of the first database created in the namespace.</p>"}, "defaultIamRoleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role to set as a default in the namespace.</p>"}, "iamRoles": {"shape": "IamRoleArnList", "documentation": "<p>A list of IAM roles to associate with the namespace.</p>"}, "kmsKeyId": {"shape": "String", "documentation": "<p>The ID of the Amazon Web Services Key Management Service key used to encrypt your data.</p>"}, "logExports": {"shape": "LogExportList", "documentation": "<p>The types of logs the namespace can export. Available export types are <code>userlog</code>, <code>connectionlog</code>, and <code>useractivitylog</code>.</p>"}, "manageAdminPassword": {"shape": "Boolean", "documentation": "<p>If <code>true</code>, Amazon Redshift uses Secrets Manager to manage the namespace's admin credentials. You can't use <code>adminUserPassword</code> if <code>manageAdminPassword</code> is true. If <code>manageAdminPassword</code> is false or not set, Amazon Redshift uses <code>adminUserPassword</code> for the admin user account's password. </p>"}, "namespaceName": {"shape": "NamespaceName", "documentation": "<p>The name of the namespace.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>A list of tag instances.</p>"}}}, "CreateNamespaceResponse": {"type": "structure", "members": {"namespace": {"shape": "Namespace", "documentation": "<p>The created namespace object.</p>"}}}, "CreateSnapshotRequest": {"type": "structure", "required": ["namespaceName", "snapshotName"], "members": {"namespaceName": {"shape": "String", "documentation": "<p>The namespace to create a snapshot for.</p>"}, "retentionPeriod": {"shape": "Integer", "documentation": "<p>How long to retain the created snapshot.</p>"}, "snapshotName": {"shape": "String", "documentation": "<p>The name of the snapshot.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>An array of <a href=\"https://docs.aws.amazon.com/redshift-serverless/latest/APIReference/API_Tag.html\">Tag objects</a> to associate with the snapshot.</p>"}}}, "CreateSnapshotResponse": {"type": "structure", "members": {"snapshot": {"shape": "Snapshot", "documentation": "<p>The created snapshot object.</p>"}}}, "CreateUsageLimitRequest": {"type": "structure", "required": ["amount", "resourceArn", "usageType"], "members": {"amount": {"shape": "<PERSON>", "documentation": "<p>The limit amount. If time-based, this amount is in Redshift Processing Units (RPU) consumed per hour. If data-based, this amount is in terabytes (TB) of data transferred between Regions in cross-account sharing. The value must be a positive number.</p>"}, "breachAction": {"shape": "UsageLimitBreachAction", "documentation": "<p>The action that Amazon Redshift Serverless takes when the limit is reached. The default is log.</p>"}, "period": {"shape": "UsageLimitPeriod", "documentation": "<p>The time period that the amount applies to. A weekly period begins on Sunday. The default is monthly.</p>"}, "resourceArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon Redshift Serverless resource to create the usage limit for.</p>"}, "usageType": {"shape": "UsageLimitUsageType", "documentation": "<p>The type of Amazon Redshift Serverless usage to create a usage limit for.</p>"}}}, "CreateUsageLimitResponse": {"type": "structure", "members": {"usageLimit": {"shape": "UsageLimit", "documentation": "<p>The returned usage limit object.</p>"}}}, "CreateWorkgroupRequest": {"type": "structure", "required": ["namespaceName", "workgroupName"], "members": {"baseCapacity": {"shape": "Integer", "documentation": "<p>The base data warehouse capacity of the workgroup in Redshift Processing Units (RPUs).</p>"}, "configParameters": {"shape": "ConfigParameterList", "documentation": "<p>An array of parameters to set for advanced control over a database. The options are <code>auto_mv</code>, <code>datestyle</code>, <code>enable_case_sensitivity_identifier</code>, <code>enable_user_activity_logging</code>, <code>query_group</code>, <code>search_path</code>, and query monitoring metrics that let you define performance boundaries. For more information about query monitoring rules and available metrics, see <a href=\"https://docs.aws.amazon.com/redshift/latest/dg/cm-c-wlm-query-monitoring-rules.html#cm-c-wlm-query-monitoring-metrics-serverless\"> Query monitoring metrics for Amazon Redshift Serverless</a>.</p>"}, "enhancedVpcRouting": {"shape": "Boolean", "documentation": "<p>The value that specifies whether to turn on enhanced virtual private cloud (VPC) routing, which forces Amazon Redshift Serverless to route traffic through your VPC instead of over the internet.</p>"}, "maxCapacity": {"shape": "Integer", "documentation": "<p>The maximum data-warehouse capacity Amazon Redshift Serverless uses to serve queries. The max capacity is specified in RPUs.</p>"}, "namespaceName": {"shape": "NamespaceName", "documentation": "<p>The name of the namespace to associate with the workgroup.</p>"}, "port": {"shape": "Integer", "documentation": "<p>The custom port to use when connecting to a workgroup. Valid port ranges are 5431-5455 and 8191-8215. The default is 5439.</p>"}, "publiclyAccessible": {"shape": "Boolean", "documentation": "<p>A value that specifies whether the workgroup can be accessed from a public network.</p>"}, "securityGroupIds": {"shape": "SecurityGroupIdList", "documentation": "<p>An array of security group IDs to associate with the workgroup.</p>"}, "subnetIds": {"shape": "SubnetIdList", "documentation": "<p>An array of VPC subnet IDs to associate with the workgroup.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>A array of tag instances.</p>"}, "workgroupName": {"shape": "WorkgroupName", "documentation": "<p>The name of the created workgroup.</p>"}}}, "CreateWorkgroupResponse": {"type": "structure", "members": {"workgroup": {"shape": "Workgroup", "documentation": "<p>The created workgroup object.</p>"}}}, "CustomDomainCertificateArnString": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:[\\w+=/,.@-]+:acm:[\\w+=/,.@-]*:[0-9]+:[\\w+=,.@-]+(/[\\w+=,.@-]+)*"}, "CustomDomainName": {"type": "string", "max": 253, "min": 1, "pattern": "^(((?!-)[A-Za-z0-9-]{0,62}[A-Za-z0-9])\\.)+((?!-)[A-Za-z0-9-]{1,62}[A-Za-z0-9])$"}, "DbName": {"type": "string"}, "DbPassword": {"type": "string", "sensitive": true}, "DbUser": {"type": "string", "sensitive": true}, "DeleteCustomDomainAssociationRequest": {"type": "structure", "required": ["customDomainName", "workgroupName"], "members": {"customDomainName": {"shape": "CustomDomainName", "documentation": "<p>The custom domain name associated with the workgroup.</p>"}, "workgroupName": {"shape": "WorkgroupName", "documentation": "<p>The name of the workgroup associated with the database.</p>"}}}, "DeleteCustomDomainAssociationResponse": {"type": "structure", "members": {}}, "DeleteEndpointAccessRequest": {"type": "structure", "required": ["endpointName"], "members": {"endpointName": {"shape": "String", "documentation": "<p>The name of the VPC endpoint to delete.</p>"}}}, "DeleteEndpointAccessResponse": {"type": "structure", "members": {"endpoint": {"shape": "EndpointAccess", "documentation": "<p>The deleted VPC endpoint.</p>"}}}, "DeleteNamespaceRequest": {"type": "structure", "required": ["namespaceName"], "members": {"finalSnapshotName": {"shape": "String", "documentation": "<p>The name of the snapshot to be created before the namespace is deleted.</p>"}, "finalSnapshotRetentionPeriod": {"shape": "Integer", "documentation": "<p>How long to retain the final snapshot.</p>"}, "namespaceName": {"shape": "NamespaceName", "documentation": "<p>The name of the namespace to delete.</p>"}}}, "DeleteNamespaceResponse": {"type": "structure", "required": ["namespace"], "members": {"namespace": {"shape": "Namespace", "documentation": "<p>The deleted namespace object.</p>"}}}, "DeleteResourcePolicyRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the policy to delete.</p>"}}}, "DeleteResourcePolicyResponse": {"type": "structure", "members": {}}, "DeleteSnapshotRequest": {"type": "structure", "required": ["snapshotName"], "members": {"snapshotName": {"shape": "String", "documentation": "<p>The name of the snapshot to be deleted.</p>"}}}, "DeleteSnapshotResponse": {"type": "structure", "members": {"snapshot": {"shape": "Snapshot", "documentation": "<p>The deleted snapshot object.</p>"}}}, "DeleteUsageLimitRequest": {"type": "structure", "required": ["usageLimitId"], "members": {"usageLimitId": {"shape": "String", "documentation": "<p>The unique identifier of the usage limit to delete.</p>"}}}, "DeleteUsageLimitResponse": {"type": "structure", "members": {"usageLimit": {"shape": "UsageLimit", "documentation": "<p>The deleted usage limit object.</p>"}}}, "DeleteWorkgroupRequest": {"type": "structure", "required": ["workgroupName"], "members": {"workgroupName": {"shape": "WorkgroupName", "documentation": "<p>The name of the workgroup to be deleted.</p>"}}}, "DeleteWorkgroupResponse": {"type": "structure", "required": ["workgroup"], "members": {"workgroup": {"shape": "Workgroup", "documentation": "<p>The deleted workgroup object.</p>"}}}, "Double": {"type": "double", "box": true}, "Endpoint": {"type": "structure", "members": {"address": {"shape": "String", "documentation": "<p>The DNS address of the VPC endpoint.</p>"}, "port": {"shape": "Integer", "documentation": "<p>The port that Amazon Redshift Serverless listens on.</p>"}, "vpcEndpoints": {"shape": "VpcEndpointList", "documentation": "<p>An array of <code>VpcEndpoint</code> objects.</p>"}}, "documentation": "<p>The VPC endpoint object.</p>"}, "EndpointAccess": {"type": "structure", "members": {"address": {"shape": "String", "documentation": "<p>The DNS address of the endpoint.</p>"}, "endpointArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the VPC endpoint.</p>"}, "endpointCreateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time that the endpoint was created.</p>"}, "endpointName": {"shape": "String", "documentation": "<p>The name of the VPC endpoint.</p>"}, "endpointStatus": {"shape": "String", "documentation": "<p>The status of the VPC endpoint.</p>"}, "port": {"shape": "Integer", "documentation": "<p>The port number on which Amazon Redshift Serverless accepts incoming connections.</p>"}, "subnetIds": {"shape": "SubnetIdList", "documentation": "<p>The unique identifier of subnets where Amazon Redshift Serverless choose to deploy the VPC endpoint.</p>"}, "vpcEndpoint": {"shape": "VpcEndpoint", "documentation": "<p>The connection endpoint for connecting to Amazon Redshift Serverless.</p>"}, "vpcSecurityGroups": {"shape": "VpcSecurityGroupMembershipList", "documentation": "<p>The security groups associated with the endpoint.</p>"}, "workgroupName": {"shape": "String", "documentation": "<p>The name of the workgroup associated with the endpoint.</p>"}}, "documentation": "<p>Information about an Amazon Redshift Serverless VPC endpoint.</p>"}, "EndpointAccessList": {"type": "list", "member": {"shape": "EndpointAccess"}}, "GetCredentialsRequest": {"type": "structure", "members": {"customDomainName": {"shape": "CustomDomainName", "documentation": "<p>The custom domain name associated with the workgroup. The custom domain name or the workgroup name must be included in the request.</p>"}, "dbName": {"shape": "DbName", "documentation": "<p>The name of the database to get temporary authorization to log on to.</p> <p>Constraints:</p> <ul> <li> <p>Must be 1 to 64 alphanumeric characters or hyphens.</p> </li> <li> <p>Must contain only uppercase or lowercase letters, numbers, underscore, plus sign, period (dot), at symbol (@), or hyphen.</p> </li> <li> <p>The first character must be a letter.</p> </li> <li> <p>Must not contain a colon ( : ) or slash ( / ).</p> </li> <li> <p>Cannot be a reserved word. A list of reserved words can be found in <a href=\"https://docs.aws.amazon.com/redshift/latest/dg/r_pg_keywords.html\">Reserved Words </a> in the Amazon Redshift Database Developer Guide</p> </li> </ul>"}, "durationSeconds": {"shape": "Integer", "documentation": "<p>The number of seconds until the returned temporary password expires. The minimum is 900 seconds, and the maximum is 3600 seconds.</p>"}, "workgroupName": {"shape": "WorkgroupName", "documentation": "<p>The name of the workgroup associated with the database.</p>"}}}, "GetCredentialsResponse": {"type": "structure", "members": {"dbPassword": {"shape": "DbPassword", "documentation": "<p>A temporary password that authorizes the user name returned by <code>Db<PERSON>ser</code> to log on to the database <code>DbName</code>.</p>"}, "dbUser": {"shape": "DbUser", "documentation": "<p>A database user name that is authorized to log on to the database <code>DbName</code> using the password <code>DbPassword</code>. If the specified <code>DbUser</code> exists in the database, the new user name has the same database privileges as the the user named in <code>DbUser</code>. By default, the user is added to PUBLIC.</p>"}, "expiration": {"shape": "Timestamp", "documentation": "<p>The date and time the password in <code>DbPassword</code> expires.</p>"}, "nextRefreshTime": {"shape": "Timestamp", "documentation": "<p>The date and time of when the <code>DbUser</code> and <code>DbPassword</code> authorization refreshes.</p>"}}}, "GetCustomDomainAssociationRequest": {"type": "structure", "required": ["customDomainName", "workgroupName"], "members": {"customDomainName": {"shape": "CustomDomainName", "documentation": "<p>The custom domain name associated with the workgroup.</p>"}, "workgroupName": {"shape": "WorkgroupName", "documentation": "<p>The name of the workgroup associated with the database.</p>"}}}, "GetCustomDomainAssociationResponse": {"type": "structure", "members": {"customDomainCertificateArn": {"shape": "CustomDomainCertificateArnString", "documentation": "<p>The custom domain name’s certificate Amazon resource name (ARN).</p>"}, "customDomainCertificateExpiryTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The expiration time for the certificate.</p>"}, "customDomainName": {"shape": "CustomDomainName", "documentation": "<p>The custom domain name associated with the workgroup.</p>"}, "workgroupName": {"shape": "WorkgroupName", "documentation": "<p>The name of the workgroup associated with the database.</p>"}}}, "GetEndpointAccessRequest": {"type": "structure", "required": ["endpointName"], "members": {"endpointName": {"shape": "String", "documentation": "<p>The name of the VPC endpoint to return information for.</p>"}}}, "GetEndpointAccessResponse": {"type": "structure", "members": {"endpoint": {"shape": "EndpointAccess", "documentation": "<p>The returned VPC endpoint.</p>"}}}, "GetNamespaceRequest": {"type": "structure", "required": ["namespaceName"], "members": {"namespaceName": {"shape": "NamespaceName", "documentation": "<p>The name of the namespace to retrieve information for.</p>"}}}, "GetNamespaceResponse": {"type": "structure", "required": ["namespace"], "members": {"namespace": {"shape": "Namespace", "documentation": "<p>The returned namespace object.</p>"}}}, "GetRecoveryPointRequest": {"type": "structure", "required": ["recoveryPointId"], "members": {"recoveryPointId": {"shape": "String", "documentation": "<p>The unique identifier of the recovery point to return information for.</p>"}}}, "GetRecoveryPointResponse": {"type": "structure", "members": {"recoveryPoint": {"shape": "RecoveryPoint", "documentation": "<p>The returned recovery point object.</p>"}}}, "GetResourcePolicyRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the resource to return.</p>"}}}, "GetResourcePolicyResponse": {"type": "structure", "members": {"resourcePolicy": {"shape": "ResourcePolicy", "documentation": "<p>The returned resource policy.</p>"}}}, "GetSnapshotRequest": {"type": "structure", "members": {"ownerAccount": {"shape": "String", "documentation": "<p>The owner Amazon Web Services account of a snapshot shared with another user.</p>"}, "snapshotArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the snapshot to return.</p>"}, "snapshotName": {"shape": "String", "documentation": "<p>The name of the snapshot to return.</p>"}}}, "GetSnapshotResponse": {"type": "structure", "members": {"snapshot": {"shape": "Snapshot", "documentation": "<p>The returned snapshot object.</p>"}}}, "GetTableRestoreStatusRequest": {"type": "structure", "required": ["tableRestoreRequestId"], "members": {"tableRestoreRequestId": {"shape": "String", "documentation": "<p>The ID of the <code>RestoreTableFromSnapshot</code> request to return status for.</p>"}}}, "GetTableRestoreStatusResponse": {"type": "structure", "members": {"tableRestoreStatus": {"shape": "TableRestoreStatus", "documentation": "<p>The returned <code>TableRestoreStatus</code> object that contains information about the status of your <code>RestoreTableFromSnapshot</code> request.</p>"}}}, "GetUsageLimitRequest": {"type": "structure", "required": ["usageLimitId"], "members": {"usageLimitId": {"shape": "String", "documentation": "<p>The unique identifier of the usage limit to return information for.</p>"}}}, "GetUsageLimitResponse": {"type": "structure", "members": {"usageLimit": {"shape": "UsageLimit", "documentation": "<p>The returned usage limit object.</p>"}}}, "GetWorkgroupRequest": {"type": "structure", "required": ["workgroupName"], "members": {"workgroupName": {"shape": "WorkgroupName", "documentation": "<p>The name of the workgroup to return information for.</p>"}}}, "GetWorkgroupResponse": {"type": "structure", "required": ["workgroup"], "members": {"workgroup": {"shape": "Workgroup", "documentation": "<p>The returned workgroup object.</p>"}}}, "IamRoleArn": {"type": "string"}, "IamRoleArnList": {"type": "list", "member": {"shape": "IamRoleArn"}}, "InsufficientCapacityException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>There is an insufficient capacity to perform the action.</p>", "exception": true, "retryable": {"throttling": false}}, "Integer": {"type": "integer", "box": true}, "InternalServerException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The request processing has failed because of an unknown error, exception or failure.</p>", "exception": true, "fault": true, "retryable": {"throttling": false}}, "InvalidPaginationException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The provided pagination token is invalid.</p>", "exception": true}, "KmsKeyId": {"type": "string"}, "ListCustomDomainAssociationsRequest": {"type": "structure", "members": {"customDomainCertificateArn": {"shape": "CustomDomainCertificateArnString", "documentation": "<p>The custom domain name’s certificate Amazon resource name (ARN).</p>"}, "customDomainName": {"shape": "CustomDomainName", "documentation": "<p>The custom domain name associated with the workgroup.</p>"}, "maxResults": {"shape": "ListCustomDomainAssociationsRequestMaxResultsInteger", "documentation": "<p>An optional parameter that specifies the maximum number of results to return. You can use <code>nextToken</code> to display the next page of results.</p>", "box": true}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>When <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}}}, "ListCustomDomainAssociationsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListCustomDomainAssociationsResponse": {"type": "structure", "members": {"associations": {"shape": "AssociationList", "documentation": "<p>A list of Association objects.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>When <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}}}, "ListEndpointAccessRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListEndpointAccessRequestMaxResultsInteger", "documentation": "<p>An optional parameter that specifies the maximum number of results to return. You can use <code>nextToken</code> to display the next page of results.</p>", "box": true}, "nextToken": {"shape": "String", "documentation": "<p>If your initial <code>ListEndpointAccess</code> operation returns a <code>nextToken</code>, you can include the returned <code>nextToken</code> in following <code>ListEndpointAccess</code> operations, which returns results in the next page.</p>"}, "vpcId": {"shape": "String", "documentation": "<p>The unique identifier of the virtual private cloud with access to Amazon Redshift Serverless.</p>"}, "workgroupName": {"shape": "String", "documentation": "<p>The name of the workgroup associated with the VPC endpoint to return.</p>"}}}, "ListEndpointAccessRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListEndpointAccessResponse": {"type": "structure", "required": ["endpoints"], "members": {"endpoints": {"shape": "EndpointAccessList", "documentation": "<p>The returned VPC endpoints.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>When <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}}}, "ListNamespacesRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListNamespacesRequestMaxResultsInteger", "documentation": "<p>An optional parameter that specifies the maximum number of results to return. You can use <code>nextToken</code> to display the next page of results.</p>", "box": true}, "nextToken": {"shape": "String", "documentation": "<p>If your initial <code>ListNamespaces</code> operation returns a <code>nextToken</code>, you can include the returned <code>nextToken</code> in following <code>ListNamespaces</code> operations, which returns results in the next page.</p>"}}}, "ListNamespacesRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListNamespacesResponse": {"type": "structure", "required": ["namespaces"], "members": {"namespaces": {"shape": "NamespaceList", "documentation": "<p>The list of returned namespaces.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>When <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}}}, "ListRecoveryPointsRequest": {"type": "structure", "members": {"endTime": {"shape": "Timestamp", "documentation": "<p>The time when creation of the recovery point finished.</p>"}, "maxResults": {"shape": "ListRecoveryPointsRequestMaxResultsInteger", "documentation": "<p>An optional parameter that specifies the maximum number of results to return. You can use <code>nextToken</code> to display the next page of results.</p>", "box": true}, "namespaceArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the namespace from which to list recovery points.</p>"}, "namespaceName": {"shape": "NamespaceName", "documentation": "<p>The name of the namespace to list recovery points for.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>If your initial <code>ListRecoveryPoints</code> operation returns a <code>nextToken</code>, you can include the returned <code>nextToken</code> in following <code>ListRecoveryPoints</code> operations, which returns results in the next page.</p>"}, "startTime": {"shape": "Timestamp", "documentation": "<p>The time when the recovery point's creation was initiated.</p>"}}}, "ListRecoveryPointsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListRecoveryPointsResponse": {"type": "structure", "members": {"nextToken": {"shape": "String", "documentation": "<p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}, "recoveryPoints": {"shape": "RecoveryPointList", "documentation": "<p>The returned recovery point objects.</p>"}}}, "ListSnapshotsRequest": {"type": "structure", "members": {"endTime": {"shape": "Timestamp", "documentation": "<p>The timestamp showing when the snapshot creation finished.</p>"}, "maxResults": {"shape": "ListSnapshotsRequestMaxResultsInteger", "documentation": "<p>An optional parameter that specifies the maximum number of results to return. You can use <code>nextToken</code> to display the next page of results.</p>", "box": true}, "namespaceArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the namespace from which to list all snapshots.</p>"}, "namespaceName": {"shape": "String", "documentation": "<p>The namespace from which to list all snapshots.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}, "ownerAccount": {"shape": "String", "documentation": "<p>The owner Amazon Web Services account of the snapshot.</p>"}, "startTime": {"shape": "Timestamp", "documentation": "<p>The time when the creation of the snapshot was initiated.</p>"}}}, "ListSnapshotsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListSnapshotsResponse": {"type": "structure", "members": {"nextToken": {"shape": "String", "documentation": "<p>If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}, "snapshots": {"shape": "SnapshotList", "documentation": "<p>All of the returned snapshot objects.</p>"}}}, "ListTableRestoreStatusRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListTableRestoreStatusRequestMaxResultsInteger", "documentation": "<p>An optional parameter that specifies the maximum number of results to return. You can use nextToken to display the next page of results.</p>", "box": true}, "namespaceName": {"shape": "String", "documentation": "<p>The namespace from which to list all of the statuses of <code>RestoreTableFromSnapshot</code> operations .</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>If your initial <code>ListTableRestoreStatus</code> operation returns a nextToken, you can include the returned <code>nextToken</code> in following <code>ListTableRestoreStatus</code> operations. This will return results on the next page.</p>"}, "workgroupName": {"shape": "String", "documentation": "<p>The workgroup from which to list all of the statuses of <code>RestoreTableFromSnapshot</code> operations.</p>"}}}, "ListTableRestoreStatusRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListTableRestoreStatusResponse": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>If your initial <code>ListTableRestoreStatus</code> operation returns a <code>nextToken</code>, you can include the returned <code>nextToken</code> in following <code>ListTableRestoreStatus</code> operations. This will returns results on the next page.</p>"}, "tableRestoreStatuses": {"shape": "TableRestoreStatusList", "documentation": "<p>The array of returned <code>TableRestoreStatus</code> objects.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) of the resource to list tags for.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagList", "documentation": "<p>A map of the key-value pairs assigned to the resource.</p>"}}}, "ListUsageLimitsRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListUsageLimitsRequestMaxResultsInteger", "documentation": "<p>An optional parameter that specifies the maximum number of results to return. You can use <code>nextToken</code> to get the next page of results. The default is 100.</p>", "box": true}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>If your initial <code>ListUsageLimits</code> operation returns a <code>nextToken</code>, you can include the returned <code>nextToken</code> in following <code>ListUsageLimits</code> operations, which returns results in the next page. </p>"}, "resourceArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) associated with the resource whose usage limits you want to list.</p>"}, "usageType": {"shape": "UsageLimitUsageType", "documentation": "<p>The Amazon Redshift Serverless feature whose limits you want to see.</p>"}}}, "ListUsageLimitsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListUsageLimitsResponse": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>When <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page.</p>"}, "usageLimits": {"shape": "UsageLimits", "documentation": "<p>An array of returned usage limit objects.</p>"}}}, "ListWorkgroupsRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListWorkgroupsRequestMaxResultsInteger", "documentation": "<p>An optional parameter that specifies the maximum number of results to return. You can use <code>nextToken</code> to display the next page of results.</p>", "box": true}, "nextToken": {"shape": "String", "documentation": "<p>If your initial ListWorkgroups operation returns a <code>nextToken</code>, you can include the returned <code>nextToken</code> in following ListNamespaces operations, which returns results in the next page.</p>"}}}, "ListWorkgroupsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListWorkgroupsResponse": {"type": "structure", "required": ["workgroups"], "members": {"nextToken": {"shape": "String", "documentation": "<p> If <code>nextToken</code> is returned, there are more results available. The value of <code>nextToken</code> is a unique pagination token for each page. To retrieve the next page, make the call again using the returned token.</p>"}, "workgroups": {"shape": "WorkgroupList", "documentation": "<p>The returned array of workgroups.</p>"}}}, "LogExport": {"type": "string", "enum": ["useractivitylog", "userlog", "connectionlog"]}, "LogExportList": {"type": "list", "member": {"shape": "LogExport"}, "max": 16, "min": 0}, "Long": {"type": "long", "box": true}, "Namespace": {"type": "structure", "members": {"adminPasswordSecretArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) for the namespace's admin user credentials secret.</p>"}, "adminPasswordSecretKmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>The ID of the Key Management Service (KMS) key used to encrypt and store the namespace's admin credentials secret.</p>"}, "adminUsername": {"shape": "DbUser", "documentation": "<p>The username of the administrator for the first database created in the namespace.</p>"}, "creationDate": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The date of when the namespace was created.</p>"}, "dbName": {"shape": "String", "documentation": "<p>The name of the first database created in the namespace.</p>"}, "defaultIamRoleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role to set as a default in the namespace.</p>"}, "iamRoles": {"shape": "IamRoleArnList", "documentation": "<p>A list of IAM roles to associate with the namespace.</p>"}, "kmsKeyId": {"shape": "String", "documentation": "<p>The ID of the Amazon Web Services Key Management Service key used to encrypt your data.</p>"}, "logExports": {"shape": "LogExportList", "documentation": "<p>The types of logs the namespace can export. Available export types are User log, Connection log, and User activity log.</p>"}, "namespaceArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) associated with a namespace.</p>"}, "namespaceId": {"shape": "String", "documentation": "<p>The unique identifier of a namespace.</p>"}, "namespaceName": {"shape": "NamespaceName", "documentation": "<p>The name of the namespace. Must be between 3-64 alphanumeric characters in lowercase, and it cannot be a reserved word. A list of reserved words can be found in <a href=\"https://docs.aws.amazon.com/redshift/latest/dg/r_pg_keywords.html\">Reserved Words</a> in the Amazon Redshift Database Developer Guide.</p>"}, "status": {"shape": "NamespaceStatus", "documentation": "<p>The status of the namespace.</p>"}}, "documentation": "<p>A collection of database objects and users.</p>"}, "NamespaceList": {"type": "list", "member": {"shape": "Namespace"}}, "NamespaceName": {"type": "string", "max": 64, "min": 3, "pattern": "^[a-z0-9-]+$"}, "NamespaceStatus": {"type": "string", "enum": ["AVAILABLE", "MODIFYING", "DELETING"]}, "NetworkInterface": {"type": "structure", "members": {"availabilityZone": {"shape": "String", "documentation": "<p>The availability Zone.</p>"}, "networkInterfaceId": {"shape": "String", "documentation": "<p>The unique identifier of the network interface.</p>"}, "privateIpAddress": {"shape": "String", "documentation": "<p>The IPv4 address of the network interface within the subnet.</p>"}, "subnetId": {"shape": "String", "documentation": "<p>The unique identifier of the subnet.</p>"}}, "documentation": "<p>Contains information about a network interface in an Amazon Redshift Serverless managed VPC endpoint. </p>"}, "NetworkInterfaceList": {"type": "list", "member": {"shape": "NetworkInterface"}}, "PaginationToken": {"type": "string", "max": 1024, "min": 8}, "ParameterKey": {"type": "string"}, "ParameterValue": {"type": "string"}, "PutResourcePolicyRequest": {"type": "structure", "required": ["policy", "resourceArn"], "members": {"policy": {"shape": "String", "documentation": "<p>The policy to create or update. For example, the following policy grants a user authorization to restore a snapshot.</p> <p> <code>\"{\\\"Version\\\": \\\"2012-10-17\\\", \\\"Statement\\\" : [{ \\\"Sid\\\": \\\"AllowUserRestoreFromSnapshot\\\", \\\"Principal\\\":{\\\"AWS\\\": [\\\"************\\\"]}, \\\"Action\\\": [\\\"redshift-serverless:RestoreFromSnapshot\\\"] , \\\"Effect\\\": \\\"Allow\\\" }]}\"</code> </p>"}, "resourceArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the account to create or update a resource policy for.</p>"}}}, "PutResourcePolicyResponse": {"type": "structure", "members": {"resourcePolicy": {"shape": "ResourcePolicy", "documentation": "<p>The policy that was created or updated.</p>"}}}, "RecoveryPoint": {"type": "structure", "members": {"namespaceArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the namespace the recovery point is associated with.</p>"}, "namespaceName": {"shape": "NamespaceName", "documentation": "<p>The name of the namespace the recovery point is associated with.</p>"}, "recoveryPointCreateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time the recovery point is created.</p>"}, "recoveryPointId": {"shape": "String", "documentation": "<p>The unique identifier of the recovery point.</p>"}, "totalSizeInMegaBytes": {"shape": "Double", "documentation": "<p>The total size of the data in the recovery point in megabytes.</p>"}, "workgroupName": {"shape": "WorkgroupName", "documentation": "<p>The name of the workgroup the recovery point is associated with.</p>"}}, "documentation": "<p>The automatically created recovery point of a namespace. Recovery points are created every 30 minutes and kept for 24 hours.</p>"}, "RecoveryPointList": {"type": "list", "member": {"shape": "RecoveryPoint"}}, "ResourceNotFoundException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "resourceName": {"shape": "AmazonResourceName", "documentation": "<p>The name of the resource that could not be found.</p>"}}, "documentation": "<p>The resource could not be found.</p>", "exception": true}, "ResourcePolicy": {"type": "structure", "members": {"policy": {"shape": "String", "documentation": "<p>The resource policy.</p>"}, "resourceArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the policy.</p>"}}, "documentation": "<p>The resource policy object. Currently, you can use policies to share snapshots across Amazon Web Services accounts.</p>"}, "RestoreFromRecoveryPointRequest": {"type": "structure", "required": ["namespaceName", "recoveryPointId", "workgroupName"], "members": {"namespaceName": {"shape": "NamespaceName", "documentation": "<p>The name of the namespace to restore data into.</p>"}, "recoveryPointId": {"shape": "String", "documentation": "<p>The unique identifier of the recovery point to restore from.</p>"}, "workgroupName": {"shape": "WorkgroupName", "documentation": "<p>The name of the workgroup used to restore data.</p>"}}}, "RestoreFromRecoveryPointResponse": {"type": "structure", "members": {"namespace": {"shape": "Namespace", "documentation": "<p>The namespace that data was restored into.</p>"}, "recoveryPointId": {"shape": "String", "documentation": "<p>The unique identifier of the recovery point used for the restore.</p>"}}}, "RestoreFromSnapshotRequest": {"type": "structure", "required": ["namespaceName", "workgroupName"], "members": {"adminPasswordSecretKmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>The ID of the Key Management Service (KMS) key used to encrypt and store the namespace's admin credentials secret.</p>"}, "manageAdminPassword": {"shape": "Boolean", "documentation": "<p>If <code>true</code>, Amazon Redshift uses Secrets Manager to manage the restored snapshot's admin credentials. If <code>MmanageAdminPassword</code> is false or not set, Amazon Redshift uses the admin credentials that the namespace or cluster had at the time the snapshot was taken.</p>"}, "namespaceName": {"shape": "NamespaceName", "documentation": "<p>The name of the namespace to restore the snapshot to.</p>"}, "ownerAccount": {"shape": "String", "documentation": "<p>The Amazon Web Services account that owns the snapshot.</p>"}, "snapshotArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the snapshot to restore from. Required if restoring from Amazon Redshift Serverless to a provisioned cluster. Must not be specified at the same time as <code>snapshotName</code>.</p> <p>The format of the ARN is arn:aws:redshift:&lt;region&gt;:&lt;account_id&gt;:snapshot:&lt;cluster_identifier&gt;/&lt;snapshot_identifier&gt;.</p>"}, "snapshotName": {"shape": "String", "documentation": "<p>The name of the snapshot to restore from. Must not be specified at the same time as <code>snapshotArn</code>.</p>"}, "workgroupName": {"shape": "WorkgroupName", "documentation": "<p>The name of the workgroup used to restore the snapshot.</p>"}}}, "RestoreFromSnapshotResponse": {"type": "structure", "members": {"namespace": {"shape": "Namespace"}, "ownerAccount": {"shape": "String", "documentation": "<p>The owner Amazon Web Services; account of the snapshot that was restored.</p>"}, "snapshotName": {"shape": "String", "documentation": "<p>The name of the snapshot used to restore the namespace.</p>"}}}, "RestoreTableFromSnapshotRequest": {"type": "structure", "required": ["namespaceName", "newTableName", "snapshotName", "sourceDatabaseName", "sourceTableName", "workgroupName"], "members": {"activateCaseSensitiveIdentifier": {"shape": "Boolean", "documentation": "<p>Indicates whether name identifiers for database, schema, and table are case sensitive. If true, the names are case sensitive. If false, the names are not case sensitive. The default is false.</p>"}, "namespaceName": {"shape": "String", "documentation": "<p>The namespace of the snapshot to restore from.</p>"}, "newTableName": {"shape": "String", "documentation": "<p>The name of the table to create from the restore operation.</p>"}, "snapshotName": {"shape": "String", "documentation": "<p>The name of the snapshot to restore the table from.</p>"}, "sourceDatabaseName": {"shape": "String", "documentation": "<p>The name of the source database that contains the table being restored.</p>"}, "sourceSchemaName": {"shape": "String", "documentation": "<p>The name of the source schema that contains the table being restored.</p>"}, "sourceTableName": {"shape": "String", "documentation": "<p>The name of the source table being restored.</p>"}, "targetDatabaseName": {"shape": "String", "documentation": "<p>The name of the database to restore the table to.</p>"}, "targetSchemaName": {"shape": "String", "documentation": "<p>The name of the schema to restore the table to.</p>"}, "workgroupName": {"shape": "String", "documentation": "<p>The workgroup to restore the table to.</p>"}}}, "RestoreTableFromSnapshotResponse": {"type": "structure", "members": {"tableRestoreStatus": {"shape": "TableRestoreStatus", "documentation": "<p>The TableRestoreStatus object that contains the status of the restore operation.</p>"}}}, "SecurityGroupId": {"type": "string"}, "SecurityGroupIdList": {"type": "list", "member": {"shape": "SecurityGroupId"}}, "ServiceQuotaExceededException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The service limit was exceeded.</p>", "exception": true}, "Snapshot": {"type": "structure", "members": {"accountsWithProvisionedRestoreAccess": {"shape": "AccountIdList", "documentation": "<p>All of the Amazon Web Services accounts that have access to restore a snapshot to a provisioned cluster.</p>"}, "accountsWithRestoreAccess": {"shape": "AccountIdList", "documentation": "<p>All of the Amazon Web Services accounts that have access to restore a snapshot to a namespace.</p>"}, "actualIncrementalBackupSizeInMegaBytes": {"shape": "Double", "documentation": "<p>The size of the incremental backup in megabytes.</p>"}, "adminPasswordSecretArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) for the namespace's admin user credentials secret.</p>"}, "adminPasswordSecretKmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>The ID of the Key Management Service (KMS) key used to encrypt and store the namespace's admin credentials secret.</p>"}, "adminUsername": {"shape": "String", "documentation": "<p>The username of the database within a snapshot.</p>"}, "backupProgressInMegaBytes": {"shape": "Double", "documentation": "<p>The size in megabytes of the data that has been backed up to a snapshot.</p>"}, "currentBackupRateInMegaBytesPerSecond": {"shape": "Double", "documentation": "<p>The rate at which data is backed up into a snapshot in megabytes per second.</p>"}, "elapsedTimeInSeconds": {"shape": "<PERSON>", "documentation": "<p>The amount of time it took to back up data into a snapshot.</p>"}, "estimatedSecondsToCompletion": {"shape": "<PERSON>", "documentation": "<p>The estimated amount of seconds until the snapshot completes backup.</p>"}, "kmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>The unique identifier of the KMS key used to encrypt the snapshot.</p>"}, "namespaceArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the namespace the snapshot was created from.</p>"}, "namespaceName": {"shape": "String", "documentation": "<p>The name of the name<PERSON><PERSON>.</p>"}, "ownerAccount": {"shape": "String", "documentation": "<p>The owner Amazon Web Services; account of the snapshot.</p>"}, "snapshotArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the snapshot.</p>"}, "snapshotCreateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The timestamp of when the snapshot was created.</p>"}, "snapshotName": {"shape": "String", "documentation": "<p>The name of the snapshot.</p>"}, "snapshotRemainingDays": {"shape": "Integer", "documentation": "<p>The amount of days until the snapshot is deleted.</p>"}, "snapshotRetentionPeriod": {"shape": "Integer", "documentation": "<p>The period of time, in days, of how long the snapshot is retained.</p>"}, "snapshotRetentionStartTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The timestamp of when data within the snapshot started getting retained.</p>"}, "status": {"shape": "SnapshotStatus", "documentation": "<p>The status of the snapshot.</p>"}, "totalBackupSizeInMegaBytes": {"shape": "Double", "documentation": "<p>The total size, in megabytes, of how big the snapshot is.</p>"}}, "documentation": "<p>A snapshot object that contains databases.</p>"}, "SnapshotList": {"type": "list", "member": {"shape": "Snapshot"}}, "SnapshotStatus": {"type": "string", "enum": ["AVAILABLE", "CREATING", "DELETED", "CANCELLED", "FAILED", "COPYING"]}, "String": {"type": "string"}, "SubnetId": {"type": "string"}, "SubnetIdList": {"type": "list", "member": {"shape": "SubnetId"}}, "SyntheticTimestamp_date_time": {"type": "timestamp", "timestampFormat": "iso8601"}, "TableRestoreStatus": {"type": "structure", "members": {"message": {"shape": "String", "documentation": "<p>A description of the status of the table restore request. Status values include <code>SUCCEEDED</code>, <code>FAILED</code>, <code>CANCELED</code>, <code>PENDING</code>, <code>IN_PROGRESS</code>.</p>"}, "namespaceName": {"shape": "String", "documentation": "<p>The namespace of the table being restored from.</p>"}, "newTableName": {"shape": "String", "documentation": "<p>The name of the table to create from the restore operation.</p>"}, "progressInMegaBytes": {"shape": "<PERSON>", "documentation": "<p>The amount of data restored to the new table so far, in megabytes (MB).</p>"}, "requestTime": {"shape": "Timestamp", "documentation": "<p>The time that the table restore request was made, in Universal Coordinated Time (UTC).</p>"}, "snapshotName": {"shape": "String", "documentation": "<p>The name of the snapshot being restored from.</p>"}, "sourceDatabaseName": {"shape": "String", "documentation": "<p>The name of the source database being restored from.</p>"}, "sourceSchemaName": {"shape": "String", "documentation": "<p>The name of the source schema being restored from.</p>"}, "sourceTableName": {"shape": "String", "documentation": "<p>The name of the source table being restored from.</p>"}, "status": {"shape": "String", "documentation": "<p>A value that describes the current state of the table restore request. Possible values include <code>SUCCEEDED</code>, <code>FAILED</code>, <code>CANCELED</code>, <code>PENDING</code>, <code>IN_PROGRESS</code>.</p>"}, "tableRestoreRequestId": {"shape": "String", "documentation": "<p>The ID of the RestoreTableFromSnapshot request.</p>"}, "targetDatabaseName": {"shape": "String", "documentation": "<p>The name of the database to restore to.</p>"}, "targetSchemaName": {"shape": "String", "documentation": "<p>The name of the schema to restore to.</p>"}, "totalDataInMegaBytes": {"shape": "<PERSON>", "documentation": "<p>The total amount of data to restore to the new table, in megabytes (MB).</p>"}, "workgroupName": {"shape": "String", "documentation": "<p>The name of the workgroup being restored from.</p>"}}, "documentation": "<p>Contains information about a table restore request.</p>"}, "TableRestoreStatusList": {"type": "list", "member": {"shape": "TableRestoreStatus"}}, "Tag": {"type": "structure", "required": ["key", "value"], "members": {"key": {"shape": "TagKey", "documentation": "<p>The key to use in the tag.</p>"}, "value": {"shape": "TagValue", "documentation": "<p>The value of the tag.</p>"}}, "documentation": "<p>A map of key-value pairs.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 0}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "max": 200, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) of the resource to tag.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>The map of the key-value pairs used to tag the resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "ThrottlingException": {"type": "structure", "members": {"code": {"shape": "String"}, "message": {"shape": "String"}}, "documentation": "<p>The request was denied due to request throttling.</p>", "exception": true, "retryable": {"throttling": false}}, "Timestamp": {"type": "timestamp"}, "TooManyTagsException": {"type": "structure", "members": {"message": {"shape": "String"}, "resourceName": {"shape": "AmazonResourceName", "documentation": "<p>The name of the resource that exceeded the number of tags allowed for a resource.</p>"}}, "documentation": "<p>The request exceeded the number of tags allowed for a resource.</p>", "exception": true}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) of the resource to remove tags from.</p>"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>The tag or set of tags to remove from the resource.</p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateCustomDomainAssociationRequest": {"type": "structure", "required": ["customDomainCertificateArn", "customDomainName", "workgroupName"], "members": {"customDomainCertificateArn": {"shape": "CustomDomainCertificateArnString", "documentation": "<p>The custom domain name’s certificate Amazon resource name (ARN). This is optional.</p>"}, "customDomainName": {"shape": "CustomDomainName", "documentation": "<p>The custom domain name associated with the workgroup.</p>"}, "workgroupName": {"shape": "WorkgroupName", "documentation": "<p>The name of the workgroup associated with the database.</p>"}}}, "UpdateCustomDomainAssociationResponse": {"type": "structure", "members": {"customDomainCertificateArn": {"shape": "CustomDomainCertificateArnString", "documentation": "<p>The custom domain name’s certificate Amazon resource name (ARN).</p>"}, "customDomainCertificateExpiryTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The expiration time for the certificate.</p>"}, "customDomainName": {"shape": "CustomDomainName", "documentation": "<p>The custom domain name associated with the workgroup.</p>"}, "workgroupName": {"shape": "WorkgroupName", "documentation": "<p>The name of the workgroup associated with the database.</p>"}}}, "UpdateEndpointAccessRequest": {"type": "structure", "required": ["endpointName"], "members": {"endpointName": {"shape": "String", "documentation": "<p>The name of the VPC endpoint to update.</p>"}, "vpcSecurityGroupIds": {"shape": "VpcSecurityGroupIdList", "documentation": "<p>The list of VPC security groups associated with the endpoint after the endpoint is modified.</p>"}}}, "UpdateEndpointAccessResponse": {"type": "structure", "members": {"endpoint": {"shape": "EndpointAccess", "documentation": "<p>The updated VPC endpoint.</p>"}}}, "UpdateNamespaceRequest": {"type": "structure", "required": ["namespaceName"], "members": {"adminPasswordSecretKmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>The ID of the Key Management Service (KMS) key used to encrypt and store the namespace's admin credentials secret. You can only use this parameter if <code>manageAdminPassword</code> is true.</p>"}, "adminUserPassword": {"shape": "DbPassword", "documentation": "<p>The password of the administrator for the first database created in the namespace. This parameter must be updated together with <code>adminUsername</code>.</p> <p>You can't use <code>adminUserPassword</code> if <code>manageAdminPassword</code> is true. </p>"}, "adminUsername": {"shape": "DbUser", "documentation": "<p>The username of the administrator for the first database created in the namespace. This parameter must be updated together with <code>adminUserPassword</code>.</p>"}, "defaultIamRoleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role to set as a default in the namespace. This parameter must be updated together with <code>iamRoles</code>.</p>"}, "iamRoles": {"shape": "IamRoleArnList", "documentation": "<p>A list of IAM roles to associate with the namespace. This parameter must be updated together with <code>defaultIamRoleArn</code>.</p>"}, "kmsKeyId": {"shape": "String", "documentation": "<p>The ID of the Amazon Web Services Key Management Service key used to encrypt your data.</p>"}, "logExports": {"shape": "LogExportList", "documentation": "<p>The types of logs the namespace can export. The export types are <code>userlog</code>, <code>connectionlog</code>, and <code>useractivitylog</code>.</p>"}, "manageAdminPassword": {"shape": "Boolean", "documentation": "<p>If <code>true</code>, Amazon Redshift uses Secrets Manager to manage the namespace's admin credentials. You can't use <code>adminUserPassword</code> if <code>manageAdminPassword</code> is true. If <code>manageAdminPassword</code> is false or not set, Amazon Redshift uses <code>adminUserPassword</code> for the admin user account's password. </p>"}, "namespaceName": {"shape": "NamespaceName", "documentation": "<p>The name of the namespace to update. You can't update the name of a namespace once it is created.</p>"}}}, "UpdateNamespaceResponse": {"type": "structure", "required": ["namespace"], "members": {"namespace": {"shape": "Namespace", "documentation": "<p>A list of tag instances.</p>"}}}, "UpdateSnapshotRequest": {"type": "structure", "required": ["snapshotName"], "members": {"retentionPeriod": {"shape": "Integer", "documentation": "<p>The new retention period of the snapshot.</p>"}, "snapshotName": {"shape": "String", "documentation": "<p>The name of the snapshot.</p>"}}}, "UpdateSnapshotResponse": {"type": "structure", "members": {"snapshot": {"shape": "Snapshot", "documentation": "<p>The updated snapshot object.</p>"}}}, "UpdateUsageLimitRequest": {"type": "structure", "required": ["usageLimitId"], "members": {"amount": {"shape": "<PERSON>", "documentation": "<p>The new limit amount. If time-based, this amount is in Redshift Processing Units (RPU) consumed per hour. If data-based, this amount is in terabytes (TB) of data transferred between Regions in cross-account sharing. The value must be a positive number.</p>"}, "breachAction": {"shape": "UsageLimitBreachAction", "documentation": "<p>The new action that Amazon Redshift Serverless takes when the limit is reached.</p>"}, "usageLimitId": {"shape": "String", "documentation": "<p>The identifier of the usage limit to update.</p>"}}}, "UpdateUsageLimitResponse": {"type": "structure", "members": {"usageLimit": {"shape": "UsageLimit", "documentation": "<p>The updated usage limit object.</p>"}}}, "UpdateWorkgroupRequest": {"type": "structure", "required": ["workgroupName"], "members": {"baseCapacity": {"shape": "Integer", "documentation": "<p>The new base data warehouse capacity in Redshift Processing Units (RPUs).</p>"}, "configParameters": {"shape": "ConfigParameterList", "documentation": "<p>An array of parameters to set for advanced control over a database. The options are <code>auto_mv</code>, <code>datestyle</code>, <code>enable_case_sensitivity_identifier</code>, <code>enable_user_activity_logging</code>, <code>query_group</code>, <code>search_path</code>, and query monitoring metrics that let you define performance boundaries. For more information about query monitoring rules and available metrics, see <a href=\"https://docs.aws.amazon.com/redshift/latest/dg/cm-c-wlm-query-monitoring-rules.html#cm-c-wlm-query-monitoring-metrics-serverless\"> Query monitoring metrics for Amazon Redshift Serverless</a>.</p>"}, "enhancedVpcRouting": {"shape": "Boolean", "documentation": "<p>The value that specifies whether to turn on enhanced virtual private cloud (VPC) routing, which forces Amazon Redshift Serverless to route traffic through your VPC.</p>"}, "maxCapacity": {"shape": "Integer", "documentation": "<p>The maximum data-warehouse capacity Amazon Redshift Serverless uses to serve queries. The max capacity is specified in RPUs.</p>"}, "port": {"shape": "Integer", "documentation": "<p>The custom port to use when connecting to a workgroup. Valid port ranges are 5431-5455 and 8191-8215. The default is 5439.</p>"}, "publiclyAccessible": {"shape": "Boolean", "documentation": "<p>A value that specifies whether the workgroup can be accessible from a public network.</p>"}, "securityGroupIds": {"shape": "SecurityGroupIdList", "documentation": "<p>An array of security group IDs to associate with the workgroup.</p>"}, "subnetIds": {"shape": "SubnetIdList", "documentation": "<p>An array of VPC subnet IDs to associate with the workgroup.</p>"}, "workgroupName": {"shape": "WorkgroupName", "documentation": "<p>The name of the workgroup to update. You can't update the name of a workgroup once it is created.</p>"}}}, "UpdateWorkgroupResponse": {"type": "structure", "required": ["workgroup"], "members": {"workgroup": {"shape": "Workgroup", "documentation": "<p>The updated workgroup object.</p>"}}}, "UsageLimit": {"type": "structure", "members": {"amount": {"shape": "<PERSON>", "documentation": "<p>The limit amount. If time-based, this amount is in RPUs consumed per hour. If data-based, this amount is in terabytes (TB). The value must be a positive number.</p>"}, "breachAction": {"shape": "UsageLimitBreachAction", "documentation": "<p>The action that Amazon Redshift Serverless takes when the limit is reached.</p>"}, "period": {"shape": "UsageLimitPeriod", "documentation": "<p>The time period that the amount applies to. A weekly period begins on Sunday. The default is monthly.</p>"}, "resourceArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the Amazon Redshift Serverless resource.</p>"}, "usageLimitArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the resource associated with the usage limit.</p>"}, "usageLimitId": {"shape": "String", "documentation": "<p>The identifier of the usage limit.</p>"}, "usageType": {"shape": "UsageLimitUsageType", "documentation": "<p>The Amazon Redshift Serverless feature to limit.</p>"}}, "documentation": "<p>The usage limit object.</p>"}, "UsageLimitBreachAction": {"type": "string", "enum": ["log", "emit-metric", "deactivate"]}, "UsageLimitPeriod": {"type": "string", "enum": ["daily", "weekly", "monthly"]}, "UsageLimitUsageType": {"type": "string", "enum": ["serverless-compute", "cross-region-datasharing"]}, "UsageLimits": {"type": "list", "member": {"shape": "UsageLimit"}, "max": 100, "min": 1}, "ValidationException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The input failed to satisfy the constraints specified by an AWS service.</p>", "exception": true}, "VpcEndpoint": {"type": "structure", "members": {"networkInterfaces": {"shape": "NetworkInterfaceList", "documentation": "<p>One or more network interfaces of the endpoint. Also known as an interface endpoint.</p>"}, "vpcEndpointId": {"shape": "String", "documentation": "<p>The connection endpoint ID for connecting to Amazon Redshift Serverless.</p>"}, "vpcId": {"shape": "String", "documentation": "<p>The VPC identifier that the endpoint is associated with.</p>"}}, "documentation": "<p>The connection endpoint for connecting to Amazon Redshift Serverless through the proxy.</p>"}, "VpcEndpointList": {"type": "list", "member": {"shape": "VpcEndpoint"}}, "VpcSecurityGroupId": {"type": "string"}, "VpcSecurityGroupIdList": {"type": "list", "member": {"shape": "VpcSecurityGroupId"}}, "VpcSecurityGroupMembership": {"type": "structure", "members": {"status": {"shape": "String", "documentation": "<p>The status of the VPC security group.</p>"}, "vpcSecurityGroupId": {"shape": "VpcSecurityGroupId", "documentation": "<p>The unique identifier of the VPC security group.</p>"}}, "documentation": "<p>Describes the members of a VPC security group.</p>"}, "VpcSecurityGroupMembershipList": {"type": "list", "member": {"shape": "VpcSecurityGroupMembership"}}, "Workgroup": {"type": "structure", "members": {"baseCapacity": {"shape": "Integer", "documentation": "<p>The base data warehouse capacity of the workgroup in Redshift Processing Units (RPUs).</p>"}, "configParameters": {"shape": "ConfigParameterList", "documentation": "<p>An array of parameters to set for advanced control over a database. The options are <code>auto_mv</code>, <code>datestyle</code>, <code>enable_case_sensitivity_identifier</code>, <code>enable_user_activity_logging</code>, <code>query_group</code>, , <code>search_path</code>, and query monitoring metrics that let you define performance boundaries. For more information about query monitoring rules and available metrics, see <a href=\"https://docs.aws.amazon.com/redshift/latest/dg/cm-c-wlm-query-monitoring-rules.html#cm-c-wlm-query-monitoring-metrics-serverless\"> Query monitoring metrics for Amazon Redshift Serverless</a>.</p>"}, "creationDate": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The creation date of the workgroup.</p>"}, "customDomainCertificateArn": {"shape": "CustomDomainCertificateArnString", "documentation": "<p>The custom domain name’s certificate Amazon resource name (ARN).</p>"}, "customDomainCertificateExpiryTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The expiration time for the certificate.</p>"}, "customDomainName": {"shape": "CustomDomainName", "documentation": "<p>The custom domain name associated with the workgroup.</p>"}, "endpoint": {"shape": "Endpoint", "documentation": "<p>The endpoint that is created from the workgroup.</p>"}, "enhancedVpcRouting": {"shape": "Boolean", "documentation": "<p>The value that specifies whether to enable enhanced virtual private cloud (VPC) routing, which forces Amazon Redshift Serverless to route traffic through your VPC.</p>"}, "maxCapacity": {"shape": "Integer", "documentation": "<p>The maximum data-warehouse capacity Amazon Redshift Serverless uses to serve queries. The max capacity is specified in RPUs.</p>"}, "namespaceName": {"shape": "String", "documentation": "<p>The namespace the workgroup is associated with.</p>"}, "patchVersion": {"shape": "String", "documentation": "<p>The patch version of your Amazon Redshift Serverless workgroup. For more information about patch versions, see <a href=\"https://docs.aws.amazon.com/redshift/latest/mgmt/cluster-versions.html\">Cluster versions for Amazon Redshift</a>.</p>"}, "port": {"shape": "Integer", "documentation": "<p>The custom port to use when connecting to a workgroup. Valid port ranges are 5431-5455 and 8191-8215. The default is 5439.</p>"}, "publiclyAccessible": {"shape": "Boolean", "documentation": "<p>A value that specifies whether the workgroup can be accessible from a public network</p>"}, "securityGroupIds": {"shape": "SecurityGroupIdList", "documentation": "<p>An array of security group IDs to associate with the workgroup.</p>"}, "status": {"shape": "WorkgroupStatus", "documentation": "<p>The status of the workgroup.</p>"}, "subnetIds": {"shape": "SubnetIdList", "documentation": "<p>An array of subnet IDs the workgroup is associated with.</p>"}, "workgroupArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) that links to the workgroup.</p>"}, "workgroupId": {"shape": "String", "documentation": "<p>The unique identifier of the workgroup.</p>"}, "workgroupName": {"shape": "WorkgroupName", "documentation": "<p>The name of the workgroup.</p>"}, "workgroupVersion": {"shape": "String", "documentation": "<p>The Amazon Redshift Serverless version of your workgroup. For more information about Amazon Redshift Serverless versions, see<a href=\"https://docs.aws.amazon.com/redshift/latest/mgmt/cluster-versions.html\">Cluster versions for Amazon Redshift</a>.</p>"}}, "documentation": "<p>The collection of computing resources from which an endpoint is created.</p>"}, "WorkgroupList": {"type": "list", "member": {"shape": "Workgroup"}}, "WorkgroupName": {"type": "string", "max": 64, "min": 3, "pattern": "^[a-z0-9-]+$"}, "WorkgroupStatus": {"type": "string", "enum": ["CREATING", "AVAILABLE", "MODIFYING", "DELETING"]}}, "documentation": "<p>This is an interface reference for Amazon Redshift Serverless. It contains documentation for one of the programming or command line interfaces you can use to manage Amazon Redshift Serverless. </p> <p>Amazon Redshift Serverless automatically provisions data warehouse capacity and intelligently scales the underlying resources based on workload demands. Amazon Redshift Serverless adjusts capacity in seconds to deliver consistently high performance and simplified operations for even the most demanding and volatile workloads. Amazon Redshift Serverless lets you focus on using your data to acquire new insights for your business and customers. </p> <p> To learn more about Amazon Redshift Serverless, see <a href=\"https://docs.aws.amazon.com/redshift/latest/mgmt/serverless-whatis.html\">What is Amazon Redshift Serverless</a>. </p>"}