{"version": "2.0", "metadata": {"apiVersion": "2021-12-03", "endpointPrefix": "private-networks", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "AWS Private 5G", "serviceId": "PrivateNetworks", "signatureVersion": "v4", "signingName": "private-networks", "uid": "privatenetworks-2021-12-03"}, "operations": {"AcknowledgeOrderReceipt": {"name": "AcknowledgeOrderReceipt", "http": {"method": "POST", "requestUri": "/v1/orders/acknowledge", "responseCode": 200}, "input": {"shape": "AcknowledgeOrderReceiptRequest"}, "output": {"shape": "AcknowledgeOrderReceiptResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Acknowledges that the specified network order was received.</p>"}, "ActivateDeviceIdentifier": {"name": "ActivateDeviceIdentifier", "http": {"method": "POST", "requestUri": "/v1/device-identifiers/activate", "responseCode": 200}, "input": {"shape": "ActivateDeviceIdentifierRequest"}, "output": {"shape": "ActivateDeviceIdentifierResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Activates the specified device identifier.</p>", "idempotent": true}, "ActivateNetworkSite": {"name": "ActivateNetworkSite", "http": {"method": "POST", "requestUri": "/v1/network-sites/activate", "responseCode": 200}, "input": {"shape": "ActivateNetworkSiteRequest"}, "output": {"shape": "ActivateNetworkSiteResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Activates the specified network site.</p>", "idempotent": true}, "ConfigureAccessPoint": {"name": "ConfigureAccessPoint", "http": {"method": "POST", "requestUri": "/v1/network-resources/configure", "responseCode": 200}, "input": {"shape": "ConfigureAccessPointRequest"}, "output": {"shape": "ConfigureAccessPointResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Configures the specified network resource. </p> <p> Use this action to specify the geographic position of the hardware. You must provide Certified Professional Installer (CPI) credentials in the request so that we can obtain spectrum grants. For more information, see <a href=\"https://docs.aws.amazon.com/private-networks/latest/userguide/radio-units.html\">Radio units</a> in the <i>Amazon Web Services Private 5G User Guide</i>. </p>", "idempotent": true}, "CreateNetwork": {"name": "CreateNetwork", "http": {"method": "POST", "requestUri": "/v1/networks", "responseCode": 200}, "input": {"shape": "CreateNetworkRequest"}, "output": {"shape": "CreateNetworkResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a network.</p>", "idempotent": true}, "CreateNetworkSite": {"name": "CreateNetworkSite", "http": {"method": "POST", "requestUri": "/v1/network-sites", "responseCode": 200}, "input": {"shape": "CreateNetworkSiteRequest"}, "output": {"shape": "CreateNetworkSiteResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a network site.</p>", "idempotent": true}, "DeactivateDeviceIdentifier": {"name": "DeactivateDeviceIdentifier", "http": {"method": "POST", "requestUri": "/v1/device-identifiers/deactivate", "responseCode": 200}, "input": {"shape": "DeactivateDeviceIdentifierRequest"}, "output": {"shape": "DeactivateDeviceIdentifierResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deactivates the specified device identifier.</p>", "idempotent": true}, "DeleteNetwork": {"name": "DeleteNetwork", "http": {"method": "DELETE", "requestUri": "/v1/networks/{networkArn}", "responseCode": 200}, "input": {"shape": "DeleteNetworkRequest"}, "output": {"shape": "DeleteNetworkResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes the specified network. You must delete network sites before you delete the network. For more information, see <a href=\"https://docs.aws.amazon.com/private-networks/latest/APIReference/API_DeleteNetworkSite.html\">DeleteNetworkSite</a> in the <i>API Reference for Amazon Web Services Private 5G</i>.</p>", "idempotent": true}, "DeleteNetworkSite": {"name": "DeleteNetworkSite", "http": {"method": "DELETE", "requestUri": "/v1/network-sites/{networkSiteArn}", "responseCode": 200}, "input": {"shape": "DeleteNetworkSiteRequest"}, "output": {"shape": "DeleteNetworkSiteResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes the specified network site. Return the hardware after you delete the network site. You are responsible for minimum charges. For more information, see <a href=\"https://docs.aws.amazon.com/private-networks/latest/userguide/hardware-maintenance.html\">Hardware returns</a> in the <i>Amazon Web Services Private 5G User Guide</i>. </p>", "idempotent": true}, "GetDeviceIdentifier": {"name": "GetDeviceIdentifier", "http": {"method": "GET", "requestUri": "/v1/device-identifiers/{deviceIdentifierArn}", "responseCode": 200}, "input": {"shape": "GetDeviceIdentifierRequest"}, "output": {"shape": "GetDeviceIdentifierResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the specified device identifier.</p>"}, "GetNetwork": {"name": "GetNetwork", "http": {"method": "GET", "requestUri": "/v1/networks/{networkArn}", "responseCode": 200}, "input": {"shape": "GetNetworkRequest"}, "output": {"shape": "GetNetworkResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the specified network.</p>"}, "GetNetworkResource": {"name": "GetNetworkResource", "http": {"method": "GET", "requestUri": "/v1/network-resources/{networkResourceArn}", "responseCode": 200}, "input": {"shape": "GetNetworkResourceRequest"}, "output": {"shape": "GetNetworkResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the specified network resource.</p>"}, "GetNetworkSite": {"name": "GetNetworkSite", "http": {"method": "GET", "requestUri": "/v1/network-sites/{networkSiteArn}", "responseCode": 200}, "input": {"shape": "GetNetworkSiteRequest"}, "output": {"shape": "GetNetworkSiteResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the specified network site.</p>"}, "GetOrder": {"name": "GetOrder", "http": {"method": "GET", "requestUri": "/v1/orders/{orderArn}", "responseCode": 200}, "input": {"shape": "GetOrderRequest"}, "output": {"shape": "GetOrderResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the specified order.</p>"}, "ListDeviceIdentifiers": {"name": "ListDeviceIdentifiers", "http": {"method": "POST", "requestUri": "/v1/device-identifiers/list", "responseCode": 200}, "input": {"shape": "ListDeviceIdentifiersRequest"}, "output": {"shape": "ListDeviceIdentifiersResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists device identifiers. Add filters to your request to return a more specific list of results. Use filters to match the Amazon Resource Name (ARN) of an order, the status of device identifiers, or the ARN of the traffic group.</p> <p>If you specify multiple filters, filters are joined with an OR, and the request returns results that match all of the specified filters.</p>"}, "ListNetworkResources": {"name": "ListNetworkResources", "http": {"method": "POST", "requestUri": "/v1/network-resources", "responseCode": 200}, "input": {"shape": "ListNetworkResourcesRequest"}, "output": {"shape": "ListNetworkResourcesResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists network resources. Add filters to your request to return a more specific list of results. Use filters to match the Amazon Resource Name (ARN) of an order or the status of network resources.</p> <p>If you specify multiple filters, filters are joined with an OR, and the request returns results that match all of the specified filters.</p>"}, "ListNetworkSites": {"name": "ListNetworkSites", "http": {"method": "POST", "requestUri": "/v1/network-sites/list", "responseCode": 200}, "input": {"shape": "ListNetworkSitesRequest"}, "output": {"shape": "ListNetworkSitesResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists network sites. Add filters to your request to return a more specific list of results. Use filters to match the status of the network site.</p>"}, "ListNetworks": {"name": "ListNetworks", "http": {"method": "POST", "requestUri": "/v1/networks/list", "responseCode": 200}, "input": {"shape": "ListNetworksRequest"}, "output": {"shape": "ListNetworksResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists networks. Add filters to your request to return a more specific list of results. Use filters to match the status of the network.</p>"}, "ListOrders": {"name": "ListOrders", "http": {"method": "POST", "requestUri": "/v1/orders/list", "responseCode": 200}, "input": {"shape": "ListOrdersRequest"}, "output": {"shape": "ListOrdersResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists orders. Add filters to your request to return a more specific list of results. Use filters to match the Amazon Resource Name (ARN) of the network site or the status of the order.</p> <p>If you specify multiple filters, filters are joined with an OR, and the request returns results that match all of the specified filters.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Lists the tags for the specified resource.</p>"}, "Ping": {"name": "<PERSON>", "http": {"method": "GET", "requestUri": "/ping", "responseCode": 200}, "output": {"shape": "PingResponse"}, "errors": [{"shape": "InternalServerException"}], "documentation": "<p>Checks the health of the service.</p>"}, "StartNetworkResourceUpdate": {"name": "StartNetworkResourceUpdate", "http": {"method": "POST", "requestUri": "/v1/network-resources/update", "responseCode": 200}, "input": {"shape": "StartNetworkResourceUpdateRequest"}, "output": {"shape": "StartNetworkResourceUpdateResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Use this action to do the following tasks:</p> <ul> <li> <p>Update the duration and renewal status of the commitment period for a radio unit. The update goes into effect immediately.</p> </li> <li> <p>Request a replacement for a network resource.</p> </li> <li> <p>Request that you return a network resource.</p> </li> </ul> <p>After you submit a request to replace or return a network resource, the status of the network resource changes to <code>CREATING_SHIPPING_LABEL</code>. The shipping label is available when the status of the network resource is <code>PENDING_RETURN</code>. After the network resource is successfully returned, its status changes to <code>DELETED</code>. For more information, see <a href=\"https://docs.aws.amazon.com/private-networks/latest/userguide/radio-units.html#return-radio-unit\">Return a radio unit</a>.</p>", "idempotent": true}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p> Adds tags to the specified resource. </p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Removes tags from the specified resource.</p>"}, "UpdateNetworkSite": {"name": "UpdateNetworkSite", "http": {"method": "PUT", "requestUri": "/v1/network-sites/site", "responseCode": 200}, "input": {"shape": "UpdateNetworkSiteRequest"}, "output": {"shape": "UpdateNetworkSiteResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates the specified network site.</p>", "idempotent": true}, "UpdateNetworkSitePlan": {"name": "UpdateNetworkSitePlan", "http": {"method": "PUT", "requestUri": "/v1/network-sites/plan", "responseCode": 200}, "input": {"shape": "UpdateNetworkSitePlanRequest"}, "output": {"shape": "UpdateNetworkSiteResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates the specified network site plan.</p>", "idempotent": true}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>You do not have permission to perform this operation.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "AcknowledgeOrderReceiptRequest": {"type": "structure", "required": ["orderArn"], "members": {"orderArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the order.</p>"}}}, "AcknowledgeOrderReceiptResponse": {"type": "structure", "required": ["order"], "members": {"order": {"shape": "Order", "documentation": "<p>Information about the order.</p>"}}}, "AcknowledgmentStatus": {"type": "string", "enum": ["ACKNOWLEDGING", "ACKNOWLEDGED", "UNACKNOWLEDGED"]}, "ActivateDeviceIdentifierRequest": {"type": "structure", "required": ["deviceIdentifierArn"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/Run_Instance_Idempotency.html\">How to ensure idempotency</a>.</p>"}, "deviceIdentifierArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the device identifier.</p>"}}}, "ActivateDeviceIdentifierResponse": {"type": "structure", "required": ["deviceIdentifier"], "members": {"deviceIdentifier": {"shape": "DeviceIdentifier", "documentation": "<p>Information about the device identifier.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p> The tags on the device identifier. </p>"}}}, "ActivateNetworkSiteRequest": {"type": "structure", "required": ["networkSiteArn", "shippingAddress"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/Run_Instance_Idempotency.html\">How to ensure idempotency</a>.</p>"}, "commitmentConfiguration": {"shape": "CommitmentConfiguration", "documentation": "<p>Determines the duration and renewal status of the commitment period for all pending radio units.</p> <p>If you include <code>commitmentConfiguration</code> in the <code>ActivateNetworkSiteRequest</code> action, you must specify the following:</p> <ul> <li> <p>The commitment period for the radio unit. You can choose a 60-day, 1-year, or 3-year period.</p> </li> <li> <p>Whether you want your commitment period to automatically renew for one more year after your current commitment period expires.</p> </li> </ul> <p>For pricing, see <a href=\"http://aws.amazon.com/private5g/pricing\">Amazon Web Services Private 5G Pricing</a>.</p> <p>If you do not include <code>commitmentConfiguration</code> in the <code>ActivateNetworkSiteRequest</code> action, the commitment period is set to 60-days.</p>"}, "networkSiteArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the network site.</p>"}, "shippingAddress": {"shape": "Address", "documentation": "<p>The shipping address of the network site.</p>"}}}, "ActivateNetworkSiteResponse": {"type": "structure", "members": {"networkSite": {"shape": "NetworkSite", "documentation": "<p>Information about the network site.</p>"}}}, "Address": {"type": "structure", "required": ["city", "country", "name", "postalCode", "stateOrProvince", "street1"], "members": {"city": {"shape": "AddressContent", "documentation": "<p>The city for this address.</p>"}, "company": {"shape": "AddressContent", "documentation": "<p>The company name for this address.</p>"}, "country": {"shape": "AddressContent", "documentation": "<p>The country for this address.</p>"}, "emailAddress": {"shape": "AddressContent", "documentation": "<p>The recipient's email address.</p>"}, "name": {"shape": "AddressContent", "documentation": "<p>The recipient's name for this address.</p>"}, "phoneNumber": {"shape": "AddressContent", "documentation": "<p>The recipient's phone number.</p>"}, "postalCode": {"shape": "AddressContent", "documentation": "<p>The postal code for this address.</p>"}, "stateOrProvince": {"shape": "AddressContent", "documentation": "<p>The state or province for this address.</p>"}, "street1": {"shape": "AddressContent", "documentation": "<p>The first line of the street address.</p>"}, "street2": {"shape": "AddressContent", "documentation": "<p>The second line of the street address.</p>"}, "street3": {"shape": "AddressContent", "documentation": "<p>The third line of the street address.</p>"}}, "documentation": "<p>Information about an address.</p>"}, "AddressContent": {"type": "string", "max": 1024, "min": 1, "sensitive": true}, "Arn": {"type": "string", "pattern": "^arn:aws:private-networks:[a-z0-9-]+:[^:]*:.*$"}, "Boolean": {"type": "boolean", "box": true}, "ClientToken": {"type": "string", "max": 100, "min": 1}, "CommitmentConfiguration": {"type": "structure", "required": ["automaticRenewal", "<PERSON><PERSON><PERSON><PERSON>"], "members": {"automaticRenewal": {"shape": "Boolean", "documentation": "<p>Determines whether the commitment period for a radio unit is set to automatically renew for an additional 1 year after your current commitment period expires.</p> <p>Set to <code>True</code>, if you want your commitment period to automatically renew. Set to <code>False</code> if you do not want your commitment to automatically renew.</p> <p>You can do the following:</p> <ul> <li> <p>Set a 1-year commitment to automatically renew for an additional 1 year. The hourly rate for the additional year will continue to be the same as your existing 1-year rate.</p> </li> <li> <p>Set a 3-year commitment to automatically renew for an additional 1 year. The hourly rate for the additional year will continue to be the same as your existing 3-year rate.</p> </li> <li> <p>Turn off a previously-enabled automatic renewal on a 1-year or 3-year commitment.</p> </li> </ul> <p>You cannot use the automatic-renewal option for a 60-day commitment.</p>"}, "commitmentLength": {"shape": "CommitmentLength", "documentation": "<p>The duration of the commitment period for the radio unit. You can choose a 60-day, 1-year, or 3-year period.</p>"}}, "documentation": "<p>Determines the duration and renewal status of the commitment period for a radio unit.</p> <p>For pricing, see <a href=\"http://aws.amazon.com/private5g/pricing\">Amazon Web Services Private 5G Pricing</a>.</p>"}, "CommitmentInformation": {"type": "structure", "required": ["commitmentConfiguration"], "members": {"commitmentConfiguration": {"shape": "CommitmentConfiguration", "documentation": "<p>The duration and renewal status of the commitment period for the radio unit.</p>"}, "expiresOn": {"shape": "Timestamp", "documentation": "<p>The date and time that the commitment period ends. If you do not cancel or renew the commitment before the expiration date, you will be billed at the 60-day-commitment rate.</p>"}, "startAt": {"shape": "Timestamp", "documentation": "<p>The date and time that the commitment period started.</p>"}}, "documentation": "<p>Shows the duration, the date and time that the contract started and ends, and the renewal status of the commitment period for the radio unit.</p>"}, "CommitmentLength": {"type": "string", "enum": ["SIXTY_DAYS", "ONE_YEAR", "THREE_YEARS"]}, "ConfigureAccessPointRequest": {"type": "structure", "required": ["accessPointArn"], "members": {"accessPointArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the network resource.</p>"}, "cpiSecretKey": {"shape": "ConfigureAccessPointRequestCpiSecretKeyString", "documentation": "<p>A Base64 encoded string of the CPI certificate associated with the CPI user who is certifying the coordinates of the network resource. </p>"}, "cpiUserId": {"shape": "ConfigureAccessPointRequestCpiUserIdString", "documentation": "<p>The CPI user ID of the CPI user who is certifying the coordinates of the network resource. </p>"}, "cpiUserPassword": {"shape": "ConfigureAccessPointRequestCpiUserPasswordString", "documentation": "<p>The CPI password associated with the CPI certificate in <code>cpiSecret<PERSON>ey</code>.</p>"}, "cpiUsername": {"shape": "ConfigureAccessPointRequestCpiUsernameString", "documentation": "<p>The CPI user name of the CPI user who is certifying the coordinates of the radio unit.</p>"}, "position": {"shape": "Position", "documentation": "<p>The position of the network resource.</p>"}}}, "ConfigureAccessPointRequestCpiSecretKeyString": {"type": "string", "max": 100000, "min": 1, "sensitive": true}, "ConfigureAccessPointRequestCpiUserIdString": {"type": "string", "max": 4096, "min": 1, "sensitive": true}, "ConfigureAccessPointRequestCpiUserPasswordString": {"type": "string", "max": 4096, "min": 1, "sensitive": true}, "ConfigureAccessPointRequestCpiUsernameString": {"type": "string", "max": 4096, "min": 1, "sensitive": true}, "ConfigureAccessPointResponse": {"type": "structure", "required": ["accessPoint"], "members": {"accessPoint": {"shape": "NetworkResource", "documentation": "<p>Information about the network resource.</p>"}}}, "CreateNetworkRequest": {"type": "structure", "required": ["networkName"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/Run_Instance_Idempotency.html\">How to ensure idempotency</a>.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the network.</p>"}, "networkName": {"shape": "Name", "documentation": "<p>The name of the network. You can't change the name after you create the network.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p> The tags to apply to the network. </p>"}}}, "CreateNetworkResponse": {"type": "structure", "required": ["network"], "members": {"network": {"shape": "Network", "documentation": "<p>Information about the network.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p> The network tags. </p>"}}}, "CreateNetworkSiteRequest": {"type": "structure", "required": ["networkArn", "networkSiteName"], "members": {"availabilityZone": {"shape": "String", "documentation": "<p>The Availability Zone that is the parent of this site. You can't change the Availability Zone after you create the site.</p>"}, "availabilityZoneId": {"shape": "String", "documentation": "<p>The ID of the Availability Zone that is the parent of this site. You can't change the Availability Zone after you create the site.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/Run_Instance_Idempotency.html\">How to ensure idempotency</a>.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the site.</p>"}, "networkArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the network.</p>"}, "networkSiteName": {"shape": "Name", "documentation": "<p>The name of the site. You can't change the name after you create the site.</p>"}, "pendingPlan": {"shape": "SitePlan", "documentation": "<p>Information about the pending plan for this site.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p> The tags to apply to the network site. </p>"}}}, "CreateNetworkSiteResponse": {"type": "structure", "members": {"networkSite": {"shape": "NetworkSite", "documentation": "<p>Information about the network site.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p> The network site tags. </p>"}}}, "DeactivateDeviceIdentifierRequest": {"type": "structure", "required": ["deviceIdentifierArn"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/Run_Instance_Idempotency.html\">How to ensure idempotency</a>.</p>"}, "deviceIdentifierArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the device identifier.</p>"}}}, "DeactivateDeviceIdentifierResponse": {"type": "structure", "required": ["deviceIdentifier"], "members": {"deviceIdentifier": {"shape": "DeviceIdentifier", "documentation": "<p>Information about the device identifier.</p>"}}}, "DeleteNetworkRequest": {"type": "structure", "required": ["networkArn"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/Run_Instance_Idempotency.html\">How to ensure idempotency</a>.</p>", "location": "querystring", "locationName": "clientToken"}, "networkArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the network.</p>", "location": "uri", "locationName": "networkArn"}}}, "DeleteNetworkResponse": {"type": "structure", "required": ["network"], "members": {"network": {"shape": "Network", "documentation": "<p>Information about the network.</p>"}}}, "DeleteNetworkSiteRequest": {"type": "structure", "required": ["networkSiteArn"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/Run_Instance_Idempotency.html\">How to ensure idempotency</a>.</p>", "location": "querystring", "locationName": "clientToken"}, "networkSiteArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the network site.</p>", "location": "uri", "locationName": "networkSiteArn"}}}, "DeleteNetworkSiteResponse": {"type": "structure", "members": {"networkSite": {"shape": "NetworkSite", "documentation": "<p>Information about the network site.</p>"}}}, "Description": {"type": "string", "max": 100, "min": 0}, "DeviceIdentifier": {"type": "structure", "members": {"createdAt": {"shape": "Timestamp", "documentation": "<p>The creation time of this device identifier.</p>"}, "deviceIdentifierArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the device identifier.</p>"}, "iccid": {"shape": "String", "documentation": "<p>The Integrated Circuit Card Identifier of the device identifier.</p>"}, "imsi": {"shape": "DeviceIdentifierImsiString", "documentation": "<p>The International Mobile Subscriber Identity of the device identifier.</p>"}, "networkArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the network on which the device identifier appears.</p>"}, "orderArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the order used to purchase the device identifier.</p>"}, "status": {"shape": "DeviceIdentifierStatus", "documentation": "<p>The status of the device identifier.</p>"}, "trafficGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the traffic group to which the device identifier belongs.</p>"}, "vendor": {"shape": "String", "documentation": "<p>The vendor of the device identifier.</p>"}}, "documentation": "<p>Information about a subscriber of a device that can use a network.</p>"}, "DeviceIdentifierFilterKeys": {"type": "string", "enum": ["STATUS", "ORDER", "TRAFFIC_GROUP"]}, "DeviceIdentifierFilterValues": {"type": "list", "member": {"shape": "String"}}, "DeviceIdentifierFilters": {"type": "map", "key": {"shape": "DeviceIdentifierFilterKeys"}, "value": {"shape": "DeviceIdentifierFilterValues"}}, "DeviceIdentifierImsiString": {"type": "string", "pattern": "^[0-9]{15}$", "sensitive": true}, "DeviceIdentifierList": {"type": "list", "member": {"shape": "DeviceIdentifier"}}, "DeviceIdentifierStatus": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}, "Double": {"type": "double", "box": true}, "ElevationReference": {"type": "string", "enum": ["AGL", "AMSL"]}, "ElevationUnit": {"type": "string", "enum": ["FEET"]}, "GetDeviceIdentifierRequest": {"type": "structure", "required": ["deviceIdentifierArn"], "members": {"deviceIdentifierArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the device identifier.</p>", "location": "uri", "locationName": "deviceIdentifierArn"}}}, "GetDeviceIdentifierResponse": {"type": "structure", "members": {"deviceIdentifier": {"shape": "DeviceIdentifier", "documentation": "<p>Information about the device identifier.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p> The device identifier tags. </p>"}}}, "GetNetworkRequest": {"type": "structure", "required": ["networkArn"], "members": {"networkArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the network.</p>", "location": "uri", "locationName": "networkArn"}}}, "GetNetworkResourceRequest": {"type": "structure", "required": ["networkResourceArn"], "members": {"networkResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the network resource.</p>", "location": "uri", "locationName": "networkResourceArn"}}}, "GetNetworkResourceResponse": {"type": "structure", "required": ["networkResource"], "members": {"networkResource": {"shape": "NetworkResource", "documentation": "<p>Information about the network resource.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p> The network resource tags. </p>"}}}, "GetNetworkResponse": {"type": "structure", "required": ["network"], "members": {"network": {"shape": "Network", "documentation": "<p>Information about the network.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p> The network tags. </p>"}}}, "GetNetworkSiteRequest": {"type": "structure", "required": ["networkSiteArn"], "members": {"networkSiteArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the network site.</p>", "location": "uri", "locationName": "networkSiteArn"}}}, "GetNetworkSiteResponse": {"type": "structure", "members": {"networkSite": {"shape": "NetworkSite", "documentation": "<p>Information about the network site.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p> The network site tags. </p>"}}}, "GetOrderRequest": {"type": "structure", "required": ["orderArn"], "members": {"orderArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the order.</p>", "location": "uri", "locationName": "orderArn"}}}, "GetOrderResponse": {"type": "structure", "required": ["order"], "members": {"order": {"shape": "Order", "documentation": "<p>Information about the order.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p> The order tags. </p>"}}}, "HealthStatus": {"type": "string", "enum": ["INITIAL", "HEALTHY", "UNHEALTHY"]}, "Integer": {"type": "integer", "box": true}, "InternalServerException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String", "documentation": "<p>Description of the error.</p>"}, "retryAfterSeconds": {"shape": "Integer", "documentation": "<p>Advice to clients on when the call can be safely retried.</p>", "location": "header", "locationName": "Retry-After"}}, "documentation": "<p>Information about an internal error.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "LimitExceededException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The limit was exceeded.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "ListDeviceIdentifiersRequest": {"type": "structure", "required": ["networkArn"], "members": {"filters": {"shape": "DeviceIdentifierFilters", "documentation": "<p>The filters.</p> <ul> <li> <p> <code>ORDER</code> - The Amazon Resource Name (ARN) of the order.</p> </li> <li> <p> <code>STATUS</code> - The status (<code>ACTIVE</code> | <code>INACTIVE</code>).</p> </li> <li> <p> <code>TRAFFIC_GROUP</code> - The Amazon Resource Name (ARN) of the traffic group.</p> </li> </ul> <p>Filter values are case sensitive. If you specify multiple values for a filter, the values are joined with an <code>OR</code>, and the request returns all results that match any of the specified values.</p>"}, "maxResults": {"shape": "ListDeviceIdentifiersRequestMaxResultsInteger", "documentation": "<p>The maximum number of results to return.</p>"}, "networkArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the network.</p>"}, "startToken": {"shape": "PaginationToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "ListDeviceIdentifiersRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 20, "min": 1}, "ListDeviceIdentifiersResponse": {"type": "structure", "members": {"deviceIdentifiers": {"shape": "DeviceIdentifierList", "documentation": "<p>Information about the device identifiers.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "ListNetworkResourcesRequest": {"type": "structure", "required": ["networkArn"], "members": {"filters": {"shape": "NetworkResourceFilters", "documentation": "<p>The filters.</p> <ul> <li> <p> <code>ORDER</code> - The Amazon Resource Name (ARN) of the order.</p> </li> <li> <p> <code>STATUS</code> - The status (<code>AVAILABLE</code> | <code>DELETED</code> | <code>DELETING</code> | <code>PENDING</code> | <code>PENDING_RETURN</code> | <code>PROVISIONING</code> | <code>SHIPPED</code>).</p> </li> </ul> <p>Filter values are case sensitive. If you specify multiple values for a filter, the values are joined with an <code>OR</code>, and the request returns all results that match any of the specified values.</p>"}, "maxResults": {"shape": "ListNetworkResourcesRequestMaxResultsInteger", "documentation": "<p>The maximum number of results to return.</p>"}, "networkArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the network.</p>"}, "startToken": {"shape": "PaginationToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "ListNetworkResourcesRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 20, "min": 1}, "ListNetworkResourcesResponse": {"type": "structure", "members": {"networkResources": {"shape": "NetworkResourceList", "documentation": "<p>Information about network resources.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "ListNetworkSitesRequest": {"type": "structure", "required": ["networkArn"], "members": {"filters": {"shape": "NetworkSiteFilters", "documentation": "<p>The filters. Add filters to your request to return a more specific list of results. Use filters to match the status of the network sites.</p> <ul> <li> <p> <code>STATUS</code> - The status (<code>AVAILABLE</code> | <code>CREATED</code> | <code>DELETED</code> | <code>DEPROVISIONING</code> | <code>PROVISIONING</code>).</p> </li> </ul> <p>Filter values are case sensitive. If you specify multiple values for a filter, the values are joined with an <code>OR</code>, and the request returns all results that match any of the specified values.</p>"}, "maxResults": {"shape": "ListNetworkSitesRequestMaxResultsInteger", "documentation": "<p>The maximum number of results to return.</p>"}, "networkArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the network.</p>"}, "startToken": {"shape": "PaginationToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "ListNetworkSitesRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 20, "min": 1}, "ListNetworkSitesResponse": {"type": "structure", "members": {"networkSites": {"shape": "NetworkSiteList", "documentation": "<p>Information about the network sites.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "ListNetworksRequest": {"type": "structure", "members": {"filters": {"shape": "NetworkFilters", "documentation": "<p>The filters.</p> <ul> <li> <p> <code>STATUS</code> - The status (<code>AVAILABLE</code> | <code>CREATED</code> | <code>DELETED</code> | <code>DEPROVISIONING</code> | <code>PROVISIONING</code>).</p> </li> </ul> <p>Filter values are case sensitive. If you specify multiple values for a filter, the values are joined with an <code>OR</code>, and the request returns all results that match any of the specified values.</p>"}, "maxResults": {"shape": "ListNetworksRequestMaxResultsInteger", "documentation": "<p>The maximum number of results to return.</p>"}, "startToken": {"shape": "PaginationToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "ListNetworksRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 20, "min": 1}, "ListNetworksResponse": {"type": "structure", "members": {"networks": {"shape": "NetworkList", "documentation": "<p>The networks.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "ListOrdersRequest": {"type": "structure", "required": ["networkArn"], "members": {"filters": {"shape": "OrderFilters", "documentation": "<p>The filters.</p> <ul> <li> <p> <code>NETWORK_SITE</code> - The Amazon Resource Name (ARN) of the network site.</p> </li> <li> <p> <code>STATUS</code> - The status (<code>ACKNOWLEDGING</code> | <code>ACKNOWLEDGED</code> | <code>UNACKNOWLEDGED</code>).</p> </li> </ul> <p>Filter values are case sensitive. If you specify multiple values for a filter, the values are joined with an <code>OR</code>, and the request returns all results that match any of the specified values.</p>"}, "maxResults": {"shape": "ListOrdersRequestMaxResultsInteger", "documentation": "<p>The maximum number of results to return.</p>"}, "networkArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the network.</p>"}, "startToken": {"shape": "PaginationToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "ListOrdersRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 20, "min": 1}, "ListOrdersResponse": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>The token for the next page of results.</p>"}, "orders": {"shape": "OrderList", "documentation": "<p>Information about the orders.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagMap", "documentation": "<p>The resource tags.</p>"}}}, "Name": {"type": "string", "max": 64, "min": 1, "pattern": "^[0-9a-zA-Z-]*$"}, "NameValuePair": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "String", "documentation": "<p>The name of the pair.</p>"}, "value": {"shape": "String", "documentation": "<p>The value of the pair.</p>"}}, "documentation": "<p>Information about a name/value pair.</p>"}, "NameValuePairs": {"type": "list", "member": {"shape": "NameValuePair"}}, "Network": {"type": "structure", "required": ["networkArn", "networkName", "status"], "members": {"createdAt": {"shape": "Timestamp", "documentation": "<p>The creation time of the network.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the network.</p>"}, "networkArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the network.</p>"}, "networkName": {"shape": "Name", "documentation": "<p>The name of the network.</p>"}, "status": {"shape": "NetworkStatus", "documentation": "<p>The status of the network.</p>"}, "statusReason": {"shape": "String", "documentation": "<p>The status reason of the network.</p>"}}, "documentation": "<p>Information about a network.</p>"}, "NetworkFilterKeys": {"type": "string", "enum": ["STATUS"]}, "NetworkFilterValues": {"type": "list", "member": {"shape": "String"}}, "NetworkFilters": {"type": "map", "key": {"shape": "NetworkFilterKeys"}, "value": {"shape": "NetworkFilterValues"}}, "NetworkList": {"type": "list", "member": {"shape": "Network"}}, "NetworkResource": {"type": "structure", "members": {"attributes": {"shape": "NameValuePairs", "documentation": "<p>The attributes of the network resource.</p>"}, "commitmentInformation": {"shape": "CommitmentInformation", "documentation": "<p>Information about the commitment period for the radio unit. Shows the duration, the date and time that the contract started and ends, and the renewal status of the commitment period.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The creation time of the network resource.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the network resource.</p>"}, "health": {"shape": "HealthStatus", "documentation": "<p>The health of the network resource.</p>"}, "model": {"shape": "String", "documentation": "<p>The model of the network resource.</p>"}, "networkArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the network on which this network resource appears.</p>"}, "networkResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the network resource.</p>"}, "networkSiteArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the network site on which this network resource appears.</p>"}, "orderArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the order used to purchase this network resource.</p>"}, "position": {"shape": "Position", "documentation": "<p>The position of the network resource.</p>"}, "returnInformation": {"shape": "ReturnInformation", "documentation": "<p>Information about a request to return the network resource.</p>"}, "serialNumber": {"shape": "String", "documentation": "<p>The serial number of the network resource.</p>"}, "status": {"shape": "NetworkResourceStatus", "documentation": "<p>The status of the network resource.</p>"}, "statusReason": {"shape": "String", "documentation": "<p>The status reason of the network resource.</p>"}, "type": {"shape": "NetworkResourceType", "documentation": "<p>The type of the network resource.</p>"}, "vendor": {"shape": "String", "documentation": "<p>The vendor of the network resource.</p>"}}, "documentation": "<p>Information about a network resource.</p>"}, "NetworkResourceDefinition": {"type": "structure", "required": ["count", "type"], "members": {"count": {"shape": "NetworkResourceDefinitionCountInteger", "documentation": "<p>The count in the network resource definition.</p>"}, "options": {"shape": "Options", "documentation": "<p>The options in the network resource definition.</p>"}, "type": {"shape": "NetworkResourceDefinitionType", "documentation": "<p>The type in the network resource definition.</p>"}}, "documentation": "<p>Information about a network resource definition.</p>"}, "NetworkResourceDefinitionCountInteger": {"type": "integer", "box": true, "min": 0}, "NetworkResourceDefinitionType": {"type": "string", "enum": ["RADIO_UNIT", "DEVICE_IDENTIFIER"]}, "NetworkResourceDefinitions": {"type": "list", "member": {"shape": "NetworkResourceDefinition"}}, "NetworkResourceFilterKeys": {"type": "string", "enum": ["ORDER", "STATUS"]}, "NetworkResourceFilterValues": {"type": "list", "member": {"shape": "String"}}, "NetworkResourceFilters": {"type": "map", "key": {"shape": "NetworkResourceFilterKeys"}, "value": {"shape": "NetworkResourceFilterValues"}}, "NetworkResourceList": {"type": "list", "member": {"shape": "NetworkResource"}}, "NetworkResourceStatus": {"type": "string", "enum": ["PENDING", "SHIPPED", "PROVISIONING", "PROVISIONED", "AVAILABLE", "DELETING", "PENDING_RETURN", "DELETED", "CREATING_SHIPPING_LABEL"]}, "NetworkResourceType": {"type": "string", "enum": ["RADIO_UNIT"]}, "NetworkSite": {"type": "structure", "required": ["networkArn", "networkSiteArn", "networkSiteName", "status"], "members": {"availabilityZone": {"shape": "String", "documentation": "<p> The parent Availability Zone for the network site. </p>"}, "availabilityZoneId": {"shape": "String", "documentation": "<p> The parent Availability Zone ID for the network site. </p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The creation time of the network site.</p>"}, "currentPlan": {"shape": "SitePlan", "documentation": "<p>The current plan of the network site.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the network site.</p>"}, "networkArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the network to which the network site belongs.</p>"}, "networkSiteArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the network site.</p>"}, "networkSiteName": {"shape": "Name", "documentation": "<p>The name of the network site.</p>"}, "pendingPlan": {"shape": "SitePlan", "documentation": "<p>The pending plan of the network site.</p>"}, "status": {"shape": "NetworkSiteStatus", "documentation": "<p>The status of the network site.</p>"}, "statusReason": {"shape": "String", "documentation": "<p>The status reason of the network site.</p>"}}, "documentation": "<p>Information about a network site.</p>"}, "NetworkSiteFilterKeys": {"type": "string", "enum": ["STATUS"]}, "NetworkSiteFilterValues": {"type": "list", "member": {"shape": "String"}}, "NetworkSiteFilters": {"type": "map", "key": {"shape": "NetworkSiteFilterKeys"}, "value": {"shape": "NetworkSiteFilterValues"}}, "NetworkSiteList": {"type": "list", "member": {"shape": "NetworkSite"}}, "NetworkSiteStatus": {"type": "string", "enum": ["CREATED", "PROVISIONING", "AVAILABLE", "DEPROVISIONING", "DELETED"]}, "NetworkStatus": {"type": "string", "enum": ["CREATED", "PROVISIONING", "AVAILABLE", "DEPROVISIONING", "DELETED"]}, "Options": {"type": "list", "member": {"shape": "NameValuePair"}}, "Order": {"type": "structure", "members": {"acknowledgmentStatus": {"shape": "AcknowledgmentStatus", "documentation": "<p>The acknowledgement status of the order.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The creation time of the order.</p>"}, "networkArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the network associated with this order.</p>"}, "networkSiteArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the network site associated with this order.</p>"}, "orderArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the order.</p>"}, "orderedResources": {"shape": "OrderedResourceDefinitions", "documentation": "<p>A list of the network resources placed in the order.</p>"}, "shippingAddress": {"shape": "Address", "documentation": "<p>The shipping address of the order.</p>"}, "trackingInformation": {"shape": "TrackingInformationList", "documentation": "<p>The tracking information of the order.</p>"}}, "documentation": "<p>Information about an order.</p>"}, "OrderFilterKeys": {"type": "string", "enum": ["STATUS", "NETWORK_SITE"]}, "OrderFilterValues": {"type": "list", "member": {"shape": "String"}}, "OrderFilters": {"type": "map", "key": {"shape": "Order<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "value": {"shape": "OrderFilter<PERSON><PERSON>ues"}}, "OrderList": {"type": "list", "member": {"shape": "Order"}}, "OrderedResourceDefinition": {"type": "structure", "required": ["count", "type"], "members": {"commitmentConfiguration": {"shape": "CommitmentConfiguration", "documentation": "<p>The duration and renewal status of the commitment period for each radio unit in the order. Does not show details if the resource type is DEVICE_IDENTIFIER.</p>"}, "count": {"shape": "OrderedResourceDefinitionCountInteger", "documentation": "<p>The number of network resources in the order.</p>"}, "type": {"shape": "NetworkResourceDefinitionType", "documentation": "<p>The type of network resource in the order.</p>"}}, "documentation": "<p>Details of the network resources in the order.</p>"}, "OrderedResourceDefinitionCountInteger": {"type": "integer", "box": true, "min": 0}, "OrderedResourceDefinitions": {"type": "list", "member": {"shape": "OrderedResourceDefinition"}}, "PaginationToken": {"type": "string"}, "PingResponse": {"type": "structure", "members": {"status": {"shape": "String", "documentation": "<p>Information about the health of the service.</p>"}}}, "Position": {"type": "structure", "members": {"elevation": {"shape": "Double", "documentation": "<p>The elevation of the equipment at this position.</p>"}, "elevationReference": {"shape": "ElevationReference", "documentation": "<p>The reference point from which elevation is reported.</p>"}, "elevationUnit": {"shape": "ElevationUnit", "documentation": "<p>The units used to measure the elevation of the position.</p>"}, "latitude": {"shape": "Double", "documentation": "<p>The latitude of the position.</p>"}, "longitude": {"shape": "Double", "documentation": "<p>The longitude of the position.</p>"}}, "documentation": "<p>Information about a position.</p>"}, "ResourceNotFoundException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "String", "documentation": "<p>Description of the error.</p>"}, "resourceId": {"shape": "String", "documentation": "<p>Identifier of the affected resource.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>Type of the affected resource.</p>"}}, "documentation": "<p>The resource was not found.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "ReturnInformation": {"type": "structure", "members": {"replacementOrderArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the replacement order.</p>"}, "returnReason": {"shape": "String", "documentation": "<p>The reason for the return. If the return request did not include a reason for the return, this value is null.</p>"}, "shippingAddress": {"shape": "Address", "documentation": "<p>The shipping address.</p>"}, "shippingLabel": {"shape": "String", "documentation": "<p>The URL of the shipping label. The shipping label is available for download only if the status of the network resource is <code>PENDING_RETURN</code>. For more information, see <a href=\"https://docs.aws.amazon.com/private-networks/latest/userguide/radio-units.html#return-radio-unit\">Return a radio unit</a>.</p>"}}, "documentation": "<p>Information about a request to return a network resource.</p>"}, "SitePlan": {"type": "structure", "members": {"options": {"shape": "Options", "documentation": "<p>The options of the plan.</p>"}, "resourceDefinitions": {"shape": "NetworkResourceDefinitions", "documentation": "<p>The resource definitions of the plan.</p>"}}, "documentation": "<p>Information about a site plan.</p>"}, "StartNetworkResourceUpdateRequest": {"type": "structure", "required": ["networkResourceArn", "updateType"], "members": {"commitmentConfiguration": {"shape": "CommitmentConfiguration", "documentation": "<p>Use this action to extend and automatically renew the commitment period for the radio unit. You can do the following:</p> <ul> <li> <p>Change a 60-day commitment to a 1-year or 3-year commitment. The change is immediate and the hourly rate decreases to the rate for the new commitment period.</p> </li> <li> <p>Change a 1-year commitment to a 3-year commitment. The change is immediate and the hourly rate decreases to the rate for the 3-year commitment period.</p> </li> <li> <p>Set a 1-year commitment to automatically renew for an additional 1 year. The hourly rate for the additional year will continue to be the same as your existing 1-year rate.</p> </li> <li> <p>Set a 3-year commitment to automatically renew for an additional 1 year. The hourly rate for the additional year will continue to be the same as your existing 3-year rate.</p> </li> <li> <p>Turn off a previously-enabled automatic renewal on a 1-year or 3-year commitment. You cannot use the automatic-renewal option for a 60-day commitment.</p> </li> </ul> <p>For pricing, see <a href=\"http://aws.amazon.com/private5g/pricing\">Amazon Web Services Private 5G Pricing</a>.</p>"}, "networkResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the network resource.</p>"}, "returnReason": {"shape": "StartNetworkResourceUpdateRequestReturnReasonString", "documentation": "<p>The reason for the return. Providing a reason for a return is optional.</p>"}, "shippingAddress": {"shape": "Address", "documentation": "<p>The shipping address. If you don't provide a shipping address when replacing or returning a network resource, we use the address from the original order for the network resource.</p>"}, "updateType": {"shape": "UpdateType", "documentation": "<p>The update type.</p> <ul> <li> <p> <code>REPLACE</code> - Submits a request to replace a defective radio unit. We provide a shipping label that you can use for the return process and we ship a replacement radio unit to you.</p> </li> <li> <p> <code>RETURN</code> - Submits a request to return a radio unit that you no longer need. We provide a shipping label that you can use for the return process.</p> </li> <li> <p> <code>COMMITMENT</code> - Submits a request to change or renew the commitment period. If you choose this value, then you must set <a href=\"https://docs.aws.amazon.com/private-networks/latest/APIReference/API_StartNetworkResourceUpdate.html#privatenetworks-StartNetworkResourceUpdate-request-commitmentConfiguration\"> <code>commitmentConfiguration</code> </a>.</p> </li> </ul>"}}}, "StartNetworkResourceUpdateRequestReturnReasonString": {"type": "string", "max": 1000, "min": 0}, "StartNetworkResourceUpdateResponse": {"type": "structure", "members": {"networkResource": {"shape": "NetworkResource", "documentation": "<p>The network resource.</p>"}}}, "String": {"type": "string"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^(?!aws:)[^\\x00-\\x1f\\x22]+$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 1, "sensitive": true}, "TagMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "max": 50, "min": 1, "sensitive": true}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p> The Amazon Resource Name (ARN) of the resource. </p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags to add to the resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": "^[^\\x00-\\x1f\\x22]*$"}, "ThrottlingException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p> The request was denied due to request throttling. </p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true, "retryable": {"throttling": true}}, "Timestamp": {"type": "timestamp", "timestampFormat": "iso8601"}, "TrackingInformation": {"type": "structure", "members": {"trackingNumber": {"shape": "String", "documentation": "<p>The tracking number of the shipment.</p>"}}, "documentation": "<p>Information about tracking a shipment.</p>"}, "TrackingInformationList": {"type": "list", "member": {"shape": "TrackingInformation"}}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>The tag keys.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateNetworkSitePlanRequest": {"type": "structure", "required": ["networkSiteArn", "pendingPlan"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/Run_Instance_Idempotency.html\">How to ensure idempotency</a>.</p>"}, "networkSiteArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the network site.</p>"}, "pendingPlan": {"shape": "SitePlan", "documentation": "<p>The pending plan.</p>"}}}, "UpdateNetworkSiteRequest": {"type": "structure", "required": ["networkSiteArn"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier that you provide to ensure the idempotency of the request. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/Run_Instance_Idempotency.html\">How to ensure idempotency</a>.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description.</p>"}, "networkSiteArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the network site.</p>"}}}, "UpdateNetworkSiteResponse": {"type": "structure", "members": {"networkSite": {"shape": "NetworkSite", "documentation": "<p>Information about the network site.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p> The network site tags. </p>"}}}, "UpdateType": {"type": "string", "enum": ["REPLACE", "RETURN", "COMMITMENT"]}, "ValidationException": {"type": "structure", "required": ["message", "reason"], "members": {"fieldList": {"shape": "ValidationExceptionFieldList", "documentation": "<p>The list of fields that caused the error, if applicable.</p>"}, "message": {"shape": "String", "documentation": "<p>Description of the error.</p>"}, "reason": {"shape": "ValidationExceptionReason", "documentation": "<p>Reason the request failed validation.</p>"}}, "documentation": "<p>The request failed validation.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "ValidationExceptionField": {"type": "structure", "required": ["message", "name"], "members": {"message": {"shape": "String", "documentation": "<p>The message about the validation failure.</p>"}, "name": {"shape": "String", "documentation": "<p>The field name that failed validation.</p>"}}, "documentation": "<p>Information about a field that failed validation.</p>"}, "ValidationExceptionFieldList": {"type": "list", "member": {"shape": "ValidationExceptionField"}}, "ValidationExceptionReason": {"type": "string", "enum": ["UNKNOWN_OPERATION", "CANNOT_PARSE", "CANNOT_ASSUME_ROLE", "FIELD_VALIDATION_FAILED", "OTHER"]}}, "documentation": "<p>Amazon Web Services Private 5G is a managed service that makes it easy to deploy, operate, and scale your own private mobile network at your on-premises location. Private 5G provides the pre-configured hardware and software for mobile networks, helps automate setup, and scales capacity on demand to support additional devices as needed.</p>"}