{"version": "2.0", "metadata": {"apiVersion": "2018-05-22", "endpointPrefix": "personalize-runtime", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "Amazon Personalize Runtime", "serviceId": "Personalize Runtime", "signatureVersion": "v4", "signingName": "personalize", "uid": "personalize-runtime-2018-05-22"}, "operations": {"GetPersonalizedRanking": {"name": "GetPersonalizedRanking", "http": {"method": "POST", "requestUri": "/personalize-ranking"}, "input": {"shape": "GetPersonalizedRankingRequest"}, "output": {"shape": "GetPersonalizedRankingResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Re-ranks a list of recommended items for the given user. The first item in the list is deemed the most likely item to be of interest to the user.</p> <note> <p>The solution backing the campaign must have been created using a recipe of type PERSONALIZED_RANKING.</p> </note>", "idempotent": true}, "GetRecommendations": {"name": "GetRecommendations", "http": {"method": "POST", "requestUri": "/recommendations"}, "input": {"shape": "GetRecommendationsRequest"}, "output": {"shape": "GetRecommendationsResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns a list of recommended items. For campaigns, the campaign's Amazon Resource Name (ARN) is required and the required user and item input depends on the recipe type used to create the solution backing the campaign as follows:</p> <ul> <li> <p>USER_PERSONALIZATION - <code>userId</code> required, <code>itemId</code> not used</p> </li> <li> <p>RELATED_ITEMS - <code>itemId</code> required, <code>userId</code> not used</p> </li> </ul> <note> <p>Campaigns that are backed by a solution created using a recipe of type PERSONALIZED_RANKING use the API.</p> </note> <p> For recommenders, the recommender's ARN is required and the required item and user input depends on the use case (domain-based recipe) backing the recommender. For information on use case requirements see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/domain-use-cases.html\">Choosing recommender use cases</a>. </p>", "idempotent": true}}, "shapes": {"Arn": {"type": "string", "max": 256, "pattern": "arn:([a-z\\d-]+):personalize:.*:.*:.+"}, "AttributeName": {"type": "string", "max": 150, "pattern": "[A-Za-z\\d_]+"}, "AttributeValue": {"type": "string", "max": 1000, "sensitive": true}, "Context": {"type": "map", "key": {"shape": "AttributeName"}, "value": {"shape": "AttributeValue"}, "max": 150}, "ErrorMessage": {"type": "string"}, "FilterAttributeName": {"type": "string", "max": 50, "pattern": "[A-Za-z0-9_]+"}, "FilterAttributeValue": {"type": "string", "max": 1000, "sensitive": true}, "FilterValues": {"type": "map", "key": {"shape": "FilterAttributeName"}, "value": {"shape": "FilterAttributeValue"}, "max": 25}, "GetPersonalizedRankingRequest": {"type": "structure", "required": ["campaignArn", "inputList", "userId"], "members": {"campaignArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the campaign to use for generating the personalized ranking.</p>"}, "inputList": {"shape": "InputList", "documentation": "<p>A list of items (by <code>itemId</code>) to rank. If an item was not included in the training dataset, the item is appended to the end of the reranked list. The maximum is 500.</p>"}, "userId": {"shape": "UserID", "documentation": "<p>The user for which you want the campaign to provide a personalized ranking.</p>"}, "context": {"shape": "Context", "documentation": "<p>The contextual metadata to use when getting recommendations. Contextual metadata includes any interaction information that might be relevant when getting a user's recommendations, such as the user's current location or device type.</p>"}, "filterArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of a filter you created to include items or exclude items from recommendations for a given user. For more information, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/filter.html\">Filtering Recommendations</a>.</p>"}, "filterValues": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The values to use when filtering recommendations. For each placeholder parameter in your filter expression, provide the parameter name (in matching case) as a key and the filter value(s) as the corresponding value. Separate multiple values for one parameter with a comma. </p> <p>For filter expressions that use an <code>INCLUDE</code> element to include items, you must provide values for all parameters that are defined in the expression. For filters with expressions that use an <code>EXCLUDE</code> element to exclude items, you can omit the <code>filter-values</code>.In this case, Amazon Personalize doesn't use that portion of the expression to filter recommendations.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/filter.html\">Filtering Recommendations</a>.</p>"}}}, "GetPersonalizedRankingResponse": {"type": "structure", "members": {"personalizedRanking": {"shape": "ItemList", "documentation": "<p>A list of items in order of most likely interest to the user. The maximum is 500.</p>"}, "recommendationId": {"shape": "RecommendationID", "documentation": "<p>The ID of the recommendation.</p>"}}}, "GetRecommendationsRequest": {"type": "structure", "members": {"campaignArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the campaign to use for getting recommendations.</p>"}, "itemId": {"shape": "ItemID", "documentation": "<p>The item ID to provide recommendations for.</p> <p>Required for <code>RELATED_ITEMS</code> recipe type.</p>"}, "userId": {"shape": "UserID", "documentation": "<p>The user ID to provide recommendations for.</p> <p>Required for <code>USER_PERSONALIZATION</code> recipe type.</p>"}, "numResults": {"shape": "NumResults", "documentation": "<p>The number of results to return. The default is 25. The maximum is 500.</p>"}, "context": {"shape": "Context", "documentation": "<p>The contextual metadata to use when getting recommendations. Contextual metadata includes any interaction information that might be relevant when getting a user's recommendations, such as the user's current location or device type.</p>"}, "filterArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the filter to apply to the returned recommendations. For more information, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/filter.html\">Filtering Recommendations</a>.</p> <p>When using this parameter, be sure the filter resource is <code>ACTIVE</code>.</p>"}, "filterValues": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The values to use when filtering recommendations. For each placeholder parameter in your filter expression, provide the parameter name (in matching case) as a key and the filter value(s) as the corresponding value. Separate multiple values for one parameter with a comma. </p> <p>For filter expressions that use an <code>INCLUDE</code> element to include items, you must provide values for all parameters that are defined in the expression. For filters with expressions that use an <code>EXCLUDE</code> element to exclude items, you can omit the <code>filter-values</code>.In this case, Amazon Personalize doesn't use that portion of the expression to filter recommendations.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/filter.html\">Filtering recommendations and user segments</a>.</p>"}, "recommenderArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the recommender to use to get recommendations. Provide a recommender ARN if you created a Domain dataset group with a recommender for a domain use case.</p>"}, "promotions": {"shape": "PromotionList", "documentation": "<p>The promotions to apply to the recommendation request. A promotion defines additional business rules that apply to a configurable subset of recommended items.</p>"}}}, "GetRecommendationsResponse": {"type": "structure", "members": {"itemList": {"shape": "ItemList", "documentation": "<p>A list of recommendations sorted in descending order by prediction score. There can be a maximum of 500 items in the list.</p>"}, "recommendationId": {"shape": "RecommendationID", "documentation": "<p>The ID of the recommendation.</p>"}}}, "InputList": {"type": "list", "member": {"shape": "ItemID"}}, "InvalidInputException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>Provide a valid value for the field or parameter.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ItemID": {"type": "string", "max": 256}, "ItemList": {"type": "list", "member": {"shape": "PredictedItem"}}, "Name": {"type": "string", "max": 63, "min": 1, "pattern": "^[a-zA-Z0-9][a-zA-Z0-9\\-_]*"}, "NumResults": {"type": "integer", "min": 0}, "PercentPromotedItems": {"type": "integer", "max": 100, "min": 1}, "PredictedItem": {"type": "structure", "members": {"itemId": {"shape": "ItemID", "documentation": "<p>The recommended item ID.</p>"}, "score": {"shape": "Score", "documentation": "<p>A numeric representation of the model's certainty that the item will be the next user selection. For more information on scoring logic, see <a>how-scores-work</a>.</p>"}, "promotionName": {"shape": "Name", "documentation": "<p>The name of the promotion that included the predicted item.</p>"}}, "documentation": "<p>An object that identifies an item.</p> <p>The and APIs return a list of <code>PredictedItem</code>s.</p>"}, "Promotion": {"type": "structure", "members": {"name": {"shape": "Name", "documentation": "<p>The name of the promotion.</p>"}, "percentPromotedItems": {"shape": "PercentPromotedItems", "documentation": "<p>The percentage of recommended items to apply the promotion to.</p>"}, "filterArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the filter used by the promotion. This filter defines the criteria for promoted items. For more information, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/promoting-items.html#promotion-filters\">Promotion filters</a>.</p>"}, "filterValues": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The values to use when promoting items. For each placeholder parameter in your promotion's filter expression, provide the parameter name (in matching case) as a key and the filter value(s) as the corresponding value. Separate multiple values for one parameter with a comma. </p> <p>For filter expressions that use an <code>INCLUDE</code> element to include items, you must provide values for all parameters that are defined in the expression. For filters with expressions that use an <code>EXCLUDE</code> element to exclude items, you can omit the <code>filter-values</code>. In this case, Amazon Personalize doesn't use that portion of the expression to filter recommendations.</p> <p>For more information on creating filters, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/filter.html\">Filtering recommendations and user segments</a>.</p>"}}, "documentation": "<p>Contains information on a promotion. A promotion defines additional business rules that apply to a configurable subset of recommended items.</p>"}, "PromotionList": {"type": "list", "member": {"shape": "Promotion"}, "max": 1}, "RecommendationID": {"type": "string"}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The specified resource does not exist.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "Score": {"type": "double"}, "UserID": {"type": "string", "max": 256}}, "documentation": "<p/>"}