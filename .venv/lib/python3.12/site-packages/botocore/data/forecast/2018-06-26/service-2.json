{"version": "2.0", "metadata": {"apiVersion": "2018-06-26", "endpointPrefix": "forecast", "jsonVersion": "1.1", "protocol": "json", "serviceFullName": "Amazon Forecast Service", "serviceId": "forecast", "signatureVersion": "v4", "signingName": "forecast", "targetPrefix": "AmazonForecast", "uid": "forecast-2018-06-26"}, "operations": {"CreateAutoPredictor": {"name": "CreateAutoPredictor", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateAutoPredictorRequest"}, "output": {"shape": "CreateAutoPredictorResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates an Amazon Forecast predictor.</p> <p>Amazon Forecast creates predictors with AutoPredictor, which involves applying the optimal combination of algorithms to each time series in your datasets. You can use <a>CreateAutoPredictor</a> to create new predictors or upgrade/retrain existing predictors.</p> <p> <b>Creating new predictors</b> </p> <p>The following parameters are required when creating a new predictor:</p> <ul> <li> <p> <code>PredictorName</code> - A unique name for the predictor.</p> </li> <li> <p> <code>DatasetGroupArn</code> - The ARN of the dataset group used to train the predictor.</p> </li> <li> <p> <code>ForecastFrequency</code> - The granularity of your forecasts (hourly, daily, weekly, etc).</p> </li> <li> <p> <code>ForecastHorizon</code> - The number of time-steps that the model predicts. The forecast horizon is also called the prediction length.</p> </li> </ul> <p>When creating a new predictor, do not specify a value for <code>ReferencePredictorArn</code>.</p> <p> <b>Upgrading and retraining predictors</b> </p> <p>The following parameters are required when retraining or upgrading a predictor:</p> <ul> <li> <p> <code>PredictorName</code> - A unique name for the predictor.</p> </li> <li> <p> <code>ReferencePredictorArn</code> - The ARN of the predictor to retrain or upgrade.</p> </li> </ul> <p>When upgrading or retraining a predictor, only specify values for the <code>ReferencePredictorArn</code> and <code>PredictorName</code>. </p>"}, "CreateDataset": {"name": "CreateDataset", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateDatasetRequest"}, "output": {"shape": "CreateDatasetResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates an Amazon Forecast dataset. The information about the dataset that you provide helps <PERSON><PERSON><PERSON> understand how to consume the data for model training. This includes the following:</p> <ul> <li> <p> <i> <code>DataFrequency</code> </i> - How frequently your historical time-series data is collected.</p> </li> <li> <p> <i> <code>Domain</code> </i> and <i> <code>DatasetType</code> </i> - Each dataset has an associated dataset domain and a type within the domain. Amazon Forecast provides a list of predefined domains and types within each domain. For each unique dataset domain and type within the domain, Amazon Forecast requires your data to include a minimum set of predefined fields.</p> </li> <li> <p> <i> <code>Schema</code> </i> - A schema specifies the fields in the dataset, including the field name and data type.</p> </li> </ul> <p>After creating a dataset, you import your training data into it and add the dataset to a dataset group. You use the dataset group to create a predictor. For more information, see <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/howitworks-datasets-groups.html\">Importing datasets</a>.</p> <p>To get a list of all your datasets, use the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_ListDatasets.html\">ListDatasets</a> operation.</p> <p>For example Forecast datasets, see the <a href=\"https://github.com/aws-samples/amazon-forecast-samples\">Amazon Forecast Sample GitHub repository</a>.</p> <note> <p>The <code>Status</code> of a dataset must be <code>ACTIVE</code> before you can import training data. Use the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_DescribeDataset.html\">DescribeDataset</a> operation to get the status.</p> </note>"}, "CreateDatasetGroup": {"name": "CreateDatasetGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateDatasetGroupRequest"}, "output": {"shape": "CreateDatasetGroupResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates a dataset group, which holds a collection of related datasets. You can add datasets to the dataset group when you create the dataset group, or later by using the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_UpdateDatasetGroup.html\">UpdateDatasetGroup</a> operation.</p> <p>After creating a dataset group and adding datasets, you use the dataset group when you create a predictor. For more information, see <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/howitworks-datasets-groups.html\">Dataset groups</a>.</p> <p>To get a list of all your datasets groups, use the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_ListDatasetGroups.html\">ListDatasetGroups</a> operation.</p> <note> <p>The <code>Status</code> of a dataset group must be <code>ACTIVE</code> before you can use the dataset group to create a predictor. To get the status, use the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_DescribeDatasetGroup.html\">DescribeDatasetGroup</a> operation.</p> </note>"}, "CreateDatasetImportJob": {"name": "CreateDatasetImportJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateDatasetImportJobRequest"}, "output": {"shape": "CreateDatasetImportJobResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Imports your training data to an Amazon Forecast dataset. You provide the location of your training data in an Amazon Simple Storage Service (Amazon S3) bucket and the Amazon Resource Name (ARN) of the dataset that you want to import the data to.</p> <p>You must specify a <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_DataSource.html\">DataSource</a> object that includes an Identity and Access Management (IAM) role that Amazon Forecast can assume to access the data, as Amazon Forecast makes a copy of your data and processes it in an internal Amazon Web Services system. For more information, see <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/aws-forecast-iam-roles.html\">Set up permissions</a>.</p> <p>The training data must be in CSV or Parquet format. The delimiter must be a comma (,).</p> <p>You can specify the path to a specific file, the S3 bucket, or to a folder in the S3 bucket. For the latter two cases, Amazon Forecast imports all files up to the limit of 10,000 files.</p> <p>Because dataset imports are not aggregated, your most recent dataset import is the one that is used when training a predictor or generating a forecast. Make sure that your most recent dataset import contains all of the data you want to model off of, and not just the new data collected since the previous import.</p> <p>To get a list of all your dataset import jobs, filtered by specified criteria, use the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_ListDatasetImportJobs.html\">ListDatasetImportJobs</a> operation.</p>"}, "CreateExplainability": {"name": "CreateExplainability", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateExplainabilityRequest"}, "output": {"shape": "CreateExplainabilityResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "LimitExceededException"}], "documentation": "<note> <p>Explainability is only available for Forecasts and Predictors generated from an AutoPredictor (<a>CreateAutoPredictor</a>)</p> </note> <p>Creates an Amazon Forecast Explainability.</p> <p>Explainability helps you better understand how the attributes in your datasets impact forecast. Amazon Forecast uses a metric called Impact scores to quantify the relative impact of each attribute and determine whether they increase or decrease forecast values.</p> <p>To enable Forecast Explainability, your predictor must include at least one of the following: related time series, item metadata, or additional datasets like Holidays and the Weather Index.</p> <p>CreateExplainability accepts either a Predictor ARN or Forecast ARN. To receive aggregated Impact scores for all time series and time points in your datasets, provide a Predictor ARN. To receive Impact scores for specific time series and time points, provide a Forecast ARN.</p> <p> <b>CreateExplainability with a Predictor ARN</b> </p> <note> <p>You can only have one Explainability resource per predictor. If you already enabled <code>ExplainPredictor</code> in <a>CreateAutoPredictor</a>, that predictor already has an Explainability resource.</p> </note> <p>The following parameters are required when providing a Predictor ARN:</p> <ul> <li> <p> <code>ExplainabilityName</code> - A unique name for the Explainability.</p> </li> <li> <p> <code>ResourceArn</code> - The Arn of the predictor.</p> </li> <li> <p> <code>TimePointGranularity</code> - Must be set to “ALL”.</p> </li> <li> <p> <code>TimeSeriesGranularity</code> - Must be set to “ALL”.</p> </li> </ul> <p>Do not specify a value for the following parameters:</p> <ul> <li> <p> <code>DataSource</code> - Only valid when TimeSeriesGranularity is “SPECIFIC”.</p> </li> <li> <p> <code>Schema</code> - Only valid when TimeSeriesGranularity is “SPECIFIC”.</p> </li> <li> <p> <code>StartDateTime</code> - Only valid when TimePointGranularity is “SPECIFIC”.</p> </li> <li> <p> <code>EndDateTime</code> - Only valid when TimePointGranularity is “SPECIFIC”.</p> </li> </ul> <p> <b>CreateExplainability with a Forecast ARN</b> </p> <note> <p>You can specify a maximum of 50 time series and 500 time points.</p> </note> <p>The following parameters are required when providing a Predictor ARN:</p> <ul> <li> <p> <code>ExplainabilityName</code> - A unique name for the Explainability.</p> </li> <li> <p> <code>ResourceArn</code> - The Arn of the forecast.</p> </li> <li> <p> <code>TimePointGranularity</code> - Either “ALL” or “SPECIFIC”.</p> </li> <li> <p> <code>TimeSeriesGranularity</code> - Either “ALL” or “SPECIFIC”.</p> </li> </ul> <p>If you set TimeSeriesGranularity to “SPECIFIC”, you must also provide the following:</p> <ul> <li> <p> <code>DataSource</code> - The S3 location of the CSV file specifying your time series.</p> </li> <li> <p> <code>Schema</code> - The Schema defines the attributes and attribute types listed in the Data Source.</p> </li> </ul> <p>If you set TimePointGranularity to “SPECIFIC”, you must also provide the following:</p> <ul> <li> <p> <code>StartDateTime</code> - The first timestamp in the range of time points.</p> </li> <li> <p> <code>EndDateTime</code> - The last timestamp in the range of time points.</p> </li> </ul>"}, "CreateExplainabilityExport": {"name": "CreateExplainabilityExport", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateExplainabilityExportRequest"}, "output": {"shape": "CreateExplainabilityExportResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Exports an Explainability resource created by the <a>CreateExplainability</a> operation. Exported files are exported to an Amazon Simple Storage Service (Amazon S3) bucket.</p> <p>You must specify a <a>DataDestination</a> object that includes an Amazon S3 bucket and an Identity and Access Management (IAM) role that Amazon Forecast can assume to access the Amazon S3 bucket. For more information, see <a>aws-forecast-iam-roles</a>.</p> <note> <p>The <code>Status</code> of the export job must be <code>ACTIVE</code> before you can access the export in your Amazon S3 bucket. To get the status, use the <a>DescribeExplainabilityExport</a> operation.</p> </note>"}, "CreateForecast": {"name": "CreateForecast", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateForecastRequest"}, "output": {"shape": "CreateForecastResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates a forecast for each item in the <code>TARGET_TIME_SERIES</code> dataset that was used to train the predictor. This is known as inference. To retrieve the forecast for a single item at low latency, use the operation. To export the complete forecast into your Amazon Simple Storage Service (Amazon S3) bucket, use the <a>CreateForecastExportJob</a> operation.</p> <p>The range of the forecast is determined by the <code>ForecastHorizon</code> value, which you specify in the <a>CreatePredictor</a> request. When you query a forecast, you can request a specific date range within the forecast.</p> <p>To get a list of all your forecasts, use the <a>ListForecasts</a> operation.</p> <note> <p>The forecasts generated by Amazon Forecast are in the same time zone as the dataset that was used to create the predictor.</p> </note> <p>For more information, see <a>howitworks-forecast</a>.</p> <note> <p>The <code>Status</code> of the forecast must be <code>ACTIVE</code> before you can query or export the forecast. Use the <a>DescribeForecast</a> operation to get the status.</p> </note> <p>By default, a forecast includes predictions for every item (<code>item_id</code>) in the dataset group that was used to train the predictor. However, you can use the <code>TimeSeriesSelector</code> object to generate a forecast on a subset of time series. Forecast creation is skipped for any time series that you specify that are not in the input dataset. The forecast export file will not contain these time series or their forecasted values.</p>"}, "CreateForecastExportJob": {"name": "CreateForecastExportJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateForecastExportJobRequest"}, "output": {"shape": "CreateForecastExportJobResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Exports a forecast created by the <a>CreateForecast</a> operation to your Amazon Simple Storage Service (Amazon S3) bucket. The forecast file name will match the following conventions:</p> <p>&lt;ForecastExportJobName&gt;_&lt;ExportTimestamp&gt;_&lt;PartNumber&gt;</p> <p>where the &lt;ExportTimestamp&gt; component is in Java SimpleDateFormat (yyyy-MM-ddTHH-mm-ssZ).</p> <p>You must specify a <a>DataDestination</a> object that includes an Identity and Access Management (IAM) role that Amazon Forecast can assume to access the Amazon S3 bucket. For more information, see <a>aws-forecast-iam-roles</a>.</p> <p>For more information, see <a>howitworks-forecast</a>.</p> <p>To get a list of all your forecast export jobs, use the <a>ListForecastExportJobs</a> operation.</p> <note> <p>The <code>Status</code> of the forecast export job must be <code>ACTIVE</code> before you can access the forecast in your Amazon S3 bucket. To get the status, use the <a>DescribeForecastExportJob</a> operation.</p> </note>"}, "CreateMonitor": {"name": "CreateMonitor", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateMonitorRequest"}, "output": {"shape": "CreateMonitorResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates a predictor monitor resource for an existing auto predictor. Predictor monitoring allows you to see how your predictor's performance changes over time. For more information, see <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/predictor-monitoring.html\">Predictor Monitoring</a>. </p>"}, "CreatePredictor": {"name": "C<PERSON><PERSON><PERSON>ictor", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreatePredictorRequest"}, "output": {"shape": "CreatePredictorResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "LimitExceededException"}], "documentation": "<note> <p> This operation creates a legacy predictor that does not include all the predictor functionalities provided by Amazon Forecast. To create a predictor that is compatible with all aspects of Forecast, use <a>CreateAutoPredictor</a>.</p> </note> <p>Creates an Amazon Forecast predictor.</p> <p>In the request, provide a dataset group and either specify an algorithm or let Amazon Forecast choose an algorithm for you using AutoML. If you specify an algorithm, you also can override algorithm-specific hyperparameters.</p> <p>Amazon Forecast uses the algorithm to train a predictor using the latest version of the datasets in the specified dataset group. You can then generate a forecast using the <a>CreateForecast</a> operation.</p> <p> To see the evaluation metrics, use the <a>GetAccuracyMetrics</a> operation. </p> <p>You can specify a featurization configuration to fill and aggregate the data fields in the <code>TARGET_TIME_SERIES</code> dataset to improve model training. For more information, see <a>FeaturizationConfig</a>.</p> <p>For RELATED_TIME_SERIES datasets, <code>CreatePredictor</code> verifies that the <code>DataFrequency</code> specified when the dataset was created matches the <code>ForecastFrequency</code>. TARGET_TIME_SERIES datasets don't have this restriction. Amazon Forecast also verifies the delimiter and timestamp format. For more information, see <a>howitworks-datasets-groups</a>.</p> <p>By default, predictors are trained and evaluated at the 0.1 (P10), 0.5 (P50), and 0.9 (P90) quantiles. You can choose custom forecast types to train and evaluate your predictor by setting the <code>ForecastTypes</code>. </p> <p> <b>AutoML</b> </p> <p>If you want Amazon Forecast to evaluate each algorithm and choose the one that minimizes the <code>objective function</code>, set <code>PerformAutoML</code> to <code>true</code>. The <code>objective function</code> is defined as the mean of the weighted losses over the forecast types. By default, these are the p10, p50, and p90 quantile losses. For more information, see <a>EvaluationResult</a>.</p> <p>When AutoML is enabled, the following properties are disallowed:</p> <ul> <li> <p> <code>AlgorithmArn</code> </p> </li> <li> <p> <code>HPOConfig</code> </p> </li> <li> <p> <code>PerformHPO</code> </p> </li> <li> <p> <code>TrainingParameters</code> </p> </li> </ul> <p>To get a list of all of your predictors, use the <a>ListPredictors</a> operation.</p> <note> <p>Before you can use the predictor to create a forecast, the <code>Status</code> of the predictor must be <code>ACTIVE</code>, signifying that training has completed. To get the status, use the <a>DescribePredictor</a> operation.</p> </note>"}, "CreatePredictorBacktestExportJob": {"name": "CreatePredictorBacktestExportJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreatePredictorBacktestExportJobRequest"}, "output": {"shape": "CreatePredictorBacktestExportJobResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Exports backtest forecasts and accuracy metrics generated by the <a>CreateAutoPredictor</a> or <a>CreatePredictor</a> operations. Two folders containing CSV or Parquet files are exported to your specified S3 bucket.</p> <p> The export file names will match the following conventions:</p> <p> <code>&lt;ExportJobName&gt;_&lt;ExportTimestamp&gt;_&lt;PartNumber&gt;.csv</code> </p> <p>The &lt;ExportTimestamp&gt; component is in Java SimpleDate format (yyyy-MM-ddTHH-mm-ssZ).</p> <p>You must specify a <a>DataDestination</a> object that includes an Amazon S3 bucket and an Identity and Access Management (IAM) role that Amazon Forecast can assume to access the Amazon S3 bucket. For more information, see <a>aws-forecast-iam-roles</a>.</p> <note> <p>The <code>Status</code> of the export job must be <code>ACTIVE</code> before you can access the export in your Amazon S3 bucket. To get the status, use the <a>DescribePredictorBacktestExportJob</a> operation.</p> </note>"}, "CreateWhatIfAnalysis": {"name": "CreateWhatIfAnalysis", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateWhatIfAnalysisRequest"}, "output": {"shape": "CreateWhatIfAnalysisResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "LimitExceededException"}], "documentation": "<p>What-if analysis is a scenario modeling technique where you make a hypothetical change to a time series and compare the forecasts generated by these changes against the baseline, unchanged time series. It is important to remember that the purpose of a what-if analysis is to understand how a forecast can change given different modifications to the baseline time series.</p> <p>For example, imagine you are a clothing retailer who is considering an end of season sale to clear space for new styles. After creating a baseline forecast, you can use a what-if analysis to investigate how different sales tactics might affect your goals.</p> <p>You could create a scenario where everything is given a 25% markdown, and another where everything is given a fixed dollar markdown. You could create a scenario where the sale lasts for one week and another where the sale lasts for one month. With a what-if analysis, you can compare many different scenarios against each other.</p> <p>Note that a what-if analysis is meant to display what the forecasting model has learned and how it will behave in the scenarios that you are evaluating. Do not blindly use the results of the what-if analysis to make business decisions. For instance, forecasts might not be accurate for novel scenarios where there is no reference available to determine whether a forecast is good.</p> <p>The <a>TimeSeriesSelector</a> object defines the items that you want in the what-if analysis.</p>"}, "CreateWhatIfForecast": {"name": "CreateWhatIfForecast", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateWhatIfForecastRequest"}, "output": {"shape": "CreateWhatIfForecastResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "LimitExceededException"}], "documentation": "<p>A what-if forecast is a forecast that is created from a modified version of the baseline forecast. Each what-if forecast incorporates either a replacement dataset or a set of transformations to the original dataset. </p>"}, "CreateWhatIfForecastExport": {"name": "CreateWhatIfForecastExport", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateWhatIfForecastExportRequest"}, "output": {"shape": "CreateWhatIfForecastExportResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Exports a forecast created by the <a>CreateWhatIfForecast</a> operation to your Amazon Simple Storage Service (Amazon S3) bucket. The forecast file name will match the following conventions:</p> <p> <code>≈&lt;ForecastExportJobName&gt;_&lt;ExportTimestamp&gt;_&lt;PartNumber&gt;</code> </p> <p>The &lt;ExportTimestamp&gt; component is in Java SimpleDateFormat (yyyy-MM-ddTHH-mm-ssZ).</p> <p>You must specify a <a>DataDestination</a> object that includes an Identity and Access Management (IAM) role that Amazon Forecast can assume to access the Amazon S3 bucket. For more information, see <a>aws-forecast-iam-roles</a>.</p> <p>For more information, see <a>howitworks-forecast</a>.</p> <p>To get a list of all your what-if forecast export jobs, use the <a>ListWhatIfForecastExports</a> operation.</p> <note> <p>The <code>Status</code> of the forecast export job must be <code>ACTIVE</code> before you can access the forecast in your Amazon S3 bucket. To get the status, use the <a>DescribeWhatIfForecastExport</a> operation.</p> </note>"}, "DeleteDataset": {"name": "DeleteDataset", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDatasetRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Deletes an Amazon Forecast dataset that was created using the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_CreateDataset.html\">CreateDataset</a> operation. You can only delete datasets that have a status of <code>ACTIVE</code> or <code>CREATE_FAILED</code>. To get the status use the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_DescribeDataset.html\">DescribeDataset</a> operation.</p> <note> <p>Forecast does not automatically update any dataset groups that contain the deleted dataset. In order to update the dataset group, use the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_UpdateDatasetGroup.html\">UpdateDatasetGroup</a> operation, omitting the deleted dataset's ARN.</p> </note>", "idempotent": true}, "DeleteDatasetGroup": {"name": "DeleteDatasetGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDatasetGroupRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Deletes a dataset group created using the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_CreateDatasetGroup.html\">CreateDatasetGroup</a> operation. You can only delete dataset groups that have a status of <code>ACTIVE</code>, <code>CREATE_FAILED</code>, or <code>UPDATE_FAILED</code>. To get the status, use the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_DescribeDatasetGroup.html\">DescribeDatasetGroup</a> operation.</p> <p>This operation deletes only the dataset group, not the datasets in the group.</p>", "idempotent": true}, "DeleteDatasetImportJob": {"name": "DeleteDatasetImportJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDatasetImportJobRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Deletes a dataset import job created using the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_CreateDatasetImportJob.html\">CreateDatasetImportJob</a> operation. You can delete only dataset import jobs that have a status of <code>ACTIVE</code> or <code>CREATE_FAILED</code>. To get the status, use the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_DescribeDatasetImportJob.html\">DescribeDatasetImportJob</a> operation.</p>", "idempotent": true}, "DeleteExplainability": {"name": "DeleteExplainability", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteExplainabilityRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Deletes an Explainability resource.</p> <p>You can delete only predictor that have a status of <code>ACTIVE</code> or <code>CREATE_FAILED</code>. To get the status, use the <a>DescribeExplainability</a> operation.</p>", "idempotent": true}, "DeleteExplainabilityExport": {"name": "DeleteExplainabilityExport", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteExplainabilityExportRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Deletes an Explainability export.</p>", "idempotent": true}, "DeleteForecast": {"name": "DeleteForecast", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteForecastRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Deletes a forecast created using the <a>CreateForecast</a> operation. You can delete only forecasts that have a status of <code>ACTIVE</code> or <code>CREATE_FAILED</code>. To get the status, use the <a>DescribeForecast</a> operation.</p> <p>You can't delete a forecast while it is being exported. After a forecast is deleted, you can no longer query the forecast.</p>", "idempotent": true}, "DeleteForecastExportJob": {"name": "DeleteForecastExportJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteForecastExportJobRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Deletes a forecast export job created using the <a>CreateForecastExportJob</a> operation. You can delete only export jobs that have a status of <code>ACTIVE</code> or <code>CREATE_FAILED</code>. To get the status, use the <a>DescribeForecastExportJob</a> operation.</p>", "idempotent": true}, "DeleteMonitor": {"name": "DeleteMonitor", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteMonitorRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Deletes a monitor resource. You can only delete a monitor resource with a status of <code>ACTIVE</code>, <code>ACTIVE_STOPPED</code>, <code>CREATE_FAILED</code>, or <code>CREATE_STOPPED</code>.</p>", "idempotent": true}, "DeletePredictor": {"name": "DeletePredictor", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeletePredictorRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Deletes a predictor created using the <a>DescribePredictor</a> or <a>CreatePredictor</a> operations. You can delete only predictor that have a status of <code>ACTIVE</code> or <code>CREATE_FAILED</code>. To get the status, use the <a>DescribePredictor</a> operation.</p>", "idempotent": true}, "DeletePredictorBacktestExportJob": {"name": "DeletePredictorBacktestExportJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeletePredictorBacktestExportJobRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Deletes a predictor backtest export job.</p>", "idempotent": true}, "DeleteResourceTree": {"name": "DeleteResourceTree", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteResourceTreeRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Deletes an entire resource tree. This operation will delete the parent resource and its child resources.</p> <p>Child resources are resources that were created from another resource. For example, when a forecast is generated from a predictor, the forecast is the child resource and the predictor is the parent resource.</p> <p>Amazon Forecast resources possess the following parent-child resource hierarchies:</p> <ul> <li> <p> <b>Dataset</b>: dataset import jobs</p> </li> <li> <p> <b>Dataset Group</b>: predictors, predictor backtest export jobs, forecasts, forecast export jobs</p> </li> <li> <p> <b>Predictor</b>: predictor backtest export jobs, forecasts, forecast export jobs</p> </li> <li> <p> <b>Forecast</b>: forecast export jobs</p> </li> </ul> <note> <p> <code>DeleteResourceTree</code> will only delete Amazon Forecast resources, and will not delete datasets or exported files stored in Amazon S3. </p> </note>", "idempotent": true}, "DeleteWhatIfAnalysis": {"name": "DeleteWhatIfAnalysis", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteWhatIfAnalysisRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Deletes a what-if analysis created using the <a>CreateWhatIfAnalysis</a> operation. You can delete only what-if analyses that have a status of <code>ACTIVE</code> or <code>CREATE_FAILED</code>. To get the status, use the <a>DescribeWhatIfAnalysis</a> operation. </p> <p>You can't delete a what-if analysis while any of its forecasts are being exported.</p>", "idempotent": true}, "DeleteWhatIfForecast": {"name": "DeleteWhatIfForecast", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteWhatIfForecastRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Deletes a what-if forecast created using the <a>CreateWhatIfForecast</a> operation. You can delete only what-if forecasts that have a status of <code>ACTIVE</code> or <code>CREATE_FAILED</code>. To get the status, use the <a>DescribeWhatIfForecast</a> operation. </p> <p>You can't delete a what-if forecast while it is being exported. After a what-if forecast is deleted, you can no longer query the what-if analysis.</p>", "idempotent": true}, "DeleteWhatIfForecastExport": {"name": "DeleteWhatIfForecastExport", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteWhatIfForecastExportRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Deletes a what-if forecast export created using the <a>CreateWhatIfForecastExport</a> operation. You can delete only what-if forecast exports that have a status of <code>ACTIVE</code> or <code>CREATE_FAILED</code>. To get the status, use the <a>DescribeWhatIfForecastExport</a> operation. </p>", "idempotent": true}, "DescribeAutoPredictor": {"name": "DescribeAutoPredictor", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeAutoPredictorRequest"}, "output": {"shape": "DescribeAutoPredictorResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes a predictor created using the CreateAutoPredictor operation.</p>", "idempotent": true}, "DescribeDataset": {"name": "DescribeDataset", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeDatasetRequest"}, "output": {"shape": "DescribeDatasetResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes an Amazon Forecast dataset created using the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_CreateDataset.html\">CreateDataset</a> operation.</p> <p>In addition to listing the parameters specified in the <code>CreateDataset</code> request, this operation includes the following dataset properties:</p> <ul> <li> <p> <code>CreationTime</code> </p> </li> <li> <p> <code>LastModificationTime</code> </p> </li> <li> <p> <code>Status</code> </p> </li> </ul>", "idempotent": true}, "DescribeDatasetGroup": {"name": "DescribeDatasetGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeDatasetGroupRequest"}, "output": {"shape": "DescribeDatasetGroupResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes a dataset group created using the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_CreateDatasetGroup.html\">CreateDatasetGroup</a> operation.</p> <p>In addition to listing the parameters provided in the <code>CreateDatasetGroup</code> request, this operation includes the following properties:</p> <ul> <li> <p> <code>DatasetArns</code> - The datasets belonging to the group.</p> </li> <li> <p> <code>CreationTime</code> </p> </li> <li> <p> <code>LastModificationTime</code> </p> </li> <li> <p> <code>Status</code> </p> </li> </ul>", "idempotent": true}, "DescribeDatasetImportJob": {"name": "DescribeDatasetImportJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeDatasetImportJobRequest"}, "output": {"shape": "DescribeDatasetImportJobResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes a dataset import job created using the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_CreateDatasetImportJob.html\">CreateDatasetImportJob</a> operation.</p> <p>In addition to listing the parameters provided in the <code>CreateDatasetImportJob</code> request, this operation includes the following properties:</p> <ul> <li> <p> <code>CreationTime</code> </p> </li> <li> <p> <code>LastModificationTime</code> </p> </li> <li> <p> <code>DataSize</code> </p> </li> <li> <p> <code>FieldStatistics</code> </p> </li> <li> <p> <code>Status</code> </p> </li> <li> <p> <code>Message</code> - If an error occurred, information about the error.</p> </li> </ul>", "idempotent": true}, "DescribeExplainability": {"name": "DescribeExplainability", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeExplainabilityRequest"}, "output": {"shape": "DescribeExplainabilityResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes an Explainability resource created using the <a>CreateExplainability</a> operation.</p>", "idempotent": true}, "DescribeExplainabilityExport": {"name": "DescribeExplainabilityExport", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeExplainabilityExportRequest"}, "output": {"shape": "DescribeExplainabilityExportResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes an Explainability export created using the <a>CreateExplainabilityExport</a> operation.</p>", "idempotent": true}, "DescribeForecast": {"name": "DescribeForecast", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeForecastRequest"}, "output": {"shape": "DescribeForecastResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes a forecast created using the <a>CreateForecast</a> operation.</p> <p>In addition to listing the properties provided in the <code>CreateForecast</code> request, this operation lists the following properties:</p> <ul> <li> <p> <code>DatasetGroupArn</code> - The dataset group that provided the training data.</p> </li> <li> <p> <code>CreationTime</code> </p> </li> <li> <p> <code>LastModificationTime</code> </p> </li> <li> <p> <code>Status</code> </p> </li> <li> <p> <code>Message</code> - If an error occurred, information about the error.</p> </li> </ul>", "idempotent": true}, "DescribeForecastExportJob": {"name": "DescribeForecastExportJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeForecastExportJobRequest"}, "output": {"shape": "DescribeForecastExportJobResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes a forecast export job created using the <a>CreateForecastExportJob</a> operation.</p> <p>In addition to listing the properties provided by the user in the <code>CreateForecastExportJob</code> request, this operation lists the following properties:</p> <ul> <li> <p> <code>CreationTime</code> </p> </li> <li> <p> <code>LastModificationTime</code> </p> </li> <li> <p> <code>Status</code> </p> </li> <li> <p> <code>Message</code> - If an error occurred, information about the error.</p> </li> </ul>", "idempotent": true}, "DescribeMonitor": {"name": "DescribeMonitor", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeMonitorRequest"}, "output": {"shape": "DescribeMonitorResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes a monitor resource. In addition to listing the properties provided in the <a>CreateMonitor</a> request, this operation lists the following properties:</p> <ul> <li> <p> <code>Baseline</code> </p> </li> <li> <p> <code>CreationTime</code> </p> </li> <li> <p> <code>LastEvaluationTime</code> </p> </li> <li> <p> <code>LastEvaluationState</code> </p> </li> <li> <p> <code>LastModificationTime</code> </p> </li> <li> <p> <code>Message</code> </p> </li> <li> <p> <code>Status</code> </p> </li> </ul>", "idempotent": true}, "DescribePredictor": {"name": "DescribePredictor", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribePredictorRequest"}, "output": {"shape": "DescribePredictorResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<note> <p> This operation is only valid for legacy predictors created with CreatePredictor. If you are not using a legacy predictor, use <a>DescribeAutoPredictor</a>.</p> </note> <p>Describes a predictor created using the <a>CreatePredictor</a> operation.</p> <p>In addition to listing the properties provided in the <code>CreatePredictor</code> request, this operation lists the following properties:</p> <ul> <li> <p> <code>DatasetImportJobArns</code> - The dataset import jobs used to import training data.</p> </li> <li> <p> <code>AutoMLAlgorithmArns</code> - If AutoML is performed, the algorithms that were evaluated.</p> </li> <li> <p> <code>CreationTime</code> </p> </li> <li> <p> <code>LastModificationTime</code> </p> </li> <li> <p> <code>Status</code> </p> </li> <li> <p> <code>Message</code> - If an error occurred, information about the error.</p> </li> </ul>", "idempotent": true}, "DescribePredictorBacktestExportJob": {"name": "DescribePredictorBacktestExportJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribePredictorBacktestExportJobRequest"}, "output": {"shape": "DescribePredictorBacktestExportJobResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes a predictor backtest export job created using the <a>CreatePredictorBacktestExportJob</a> operation.</p> <p>In addition to listing the properties provided by the user in the <code>CreatePredictorBacktestExportJob</code> request, this operation lists the following properties:</p> <ul> <li> <p> <code>CreationTime</code> </p> </li> <li> <p> <code>LastModificationTime</code> </p> </li> <li> <p> <code>Status</code> </p> </li> <li> <p> <code>Message</code> (if an error occurred)</p> </li> </ul>", "idempotent": true}, "DescribeWhatIfAnalysis": {"name": "DescribeWhatIfAnalysis", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeWhatIfAnalysisRequest"}, "output": {"shape": "DescribeWhatIfAnalysisResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes the what-if analysis created using the <a>CreateWhatIfAnalysis</a> operation.</p> <p>In addition to listing the properties provided in the <code>CreateWhatIfAnalysis</code> request, this operation lists the following properties:</p> <ul> <li> <p> <code>CreationTime</code> </p> </li> <li> <p> <code>LastModificationTime</code> </p> </li> <li> <p> <code>Message</code> - If an error occurred, information about the error.</p> </li> <li> <p> <code>Status</code> </p> </li> </ul>", "idempotent": true}, "DescribeWhatIfForecast": {"name": "DescribeWhatIfForecast", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeWhatIfForecastRequest"}, "output": {"shape": "DescribeWhatIfForecastResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes the what-if forecast created using the <a>CreateWhatIfForecast</a> operation.</p> <p>In addition to listing the properties provided in the <code>CreateWhatIfForecast</code> request, this operation lists the following properties:</p> <ul> <li> <p> <code>CreationTime</code> </p> </li> <li> <p> <code>LastModificationTime</code> </p> </li> <li> <p> <code>Message</code> - If an error occurred, information about the error.</p> </li> <li> <p> <code>Status</code> </p> </li> </ul>", "idempotent": true}, "DescribeWhatIfForecastExport": {"name": "DescribeWhatIfForecastExport", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeWhatIfForecastExportRequest"}, "output": {"shape": "DescribeWhatIfForecastExportResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes the what-if forecast export created using the <a>CreateWhatIfForecastExport</a> operation.</p> <p>In addition to listing the properties provided in the <code>CreateWhatIfForecastExport</code> request, this operation lists the following properties:</p> <ul> <li> <p> <code>CreationTime</code> </p> </li> <li> <p> <code>LastModificationTime</code> </p> </li> <li> <p> <code>Message</code> - If an error occurred, information about the error.</p> </li> <li> <p> <code>Status</code> </p> </li> </ul>", "idempotent": true}, "GetAccuracyMetrics": {"name": "GetAccuracyMetrics", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetAccuracyMetricsRequest"}, "output": {"shape": "GetAccuracyMetricsResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Provides metrics on the accuracy of the models that were trained by the <a>CreatePredictor</a> operation. Use metrics to see how well the model performed and to decide whether to use the predictor to generate a forecast. For more information, see <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/metrics.html\">Predictor Metrics</a>.</p> <p>This operation generates metrics for each backtest window that was evaluated. The number of backtest windows (<code>NumberOfBacktestWindows</code>) is specified using the <a>EvaluationParameters</a> object, which is optionally included in the <code>CreatePredictor</code> request. If <code>NumberOfBacktestWindows</code> isn't specified, the number defaults to one.</p> <p>The parameters of the <code>filling</code> method determine which items contribute to the metrics. If you want all items to contribute, specify <code>zero</code>. If you want only those items that have complete data in the range being evaluated to contribute, specify <code>nan</code>. For more information, see <a>FeaturizationMethod</a>.</p> <note> <p>Before you can get accuracy metrics, the <code>Status</code> of the predictor must be <code>ACTIVE</code>, signifying that training has completed. To get the status, use the <a>DescribePredictor</a> operation.</p> </note>", "idempotent": true}, "ListDatasetGroups": {"name": "ListDatasetGroups", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListDatasetGroupsRequest"}, "output": {"shape": "ListDatasetGroupsResponse"}, "errors": [{"shape": "InvalidNextTokenException"}], "documentation": "<p>Returns a list of dataset groups created using the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_CreateDatasetGroup.html\">CreateDatasetGroup</a> operation. For each dataset group, this operation returns a summary of its properties, including its Amazon Resource Name (ARN). You can retrieve the complete set of properties by using the dataset group ARN with the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_DescribeDatasetGroup.html\">DescribeDatasetGroup</a> operation.</p>", "idempotent": true}, "ListDatasetImportJobs": {"name": "ListDatasetImportJobs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListDatasetImportJobsRequest"}, "output": {"shape": "ListDatasetImportJobsResponse"}, "errors": [{"shape": "InvalidNextTokenException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Returns a list of dataset import jobs created using the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_CreateDatasetImportJob.html\">CreateDatasetImportJob</a> operation. For each import job, this operation returns a summary of its properties, including its Amazon Resource Name (ARN). You can retrieve the complete set of properties by using the ARN with the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_DescribeDatasetImportJob.html\">DescribeDatasetImportJob</a> operation. You can filter the list by providing an array of <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_Filter.html\">Filter</a> objects.</p>", "idempotent": true}, "ListDatasets": {"name": "ListDatasets", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListDatasetsRequest"}, "output": {"shape": "ListDatasetsResponse"}, "errors": [{"shape": "InvalidNextTokenException"}], "documentation": "<p>Returns a list of datasets created using the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_CreateDataset.html\">CreateDataset</a> operation. For each dataset, a summary of its properties, including its Amazon Resource Name (ARN), is returned. To retrieve the complete set of properties, use the ARN with the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_DescribeDataset.html\">DescribeDataset</a> operation.</p>", "idempotent": true}, "ListExplainabilities": {"name": "ListExplainabilities", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListExplainabilitiesRequest"}, "output": {"shape": "ListExplainabilitiesResponse"}, "errors": [{"shape": "InvalidNextTokenException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Returns a list of Explainability resources created using the <a>CreateExplainability</a> operation. This operation returns a summary for each Explainability. You can filter the list using an array of <a>Filter</a> objects.</p> <p>To retrieve the complete set of properties for a particular Explainability resource, use the ARN with the <a>DescribeExplainability</a> operation.</p>", "idempotent": true}, "ListExplainabilityExports": {"name": "ListExplainabilityExports", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListExplainabilityExportsRequest"}, "output": {"shape": "ListExplainabilityExportsResponse"}, "errors": [{"shape": "InvalidNextTokenException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Returns a list of Explainability exports created using the <a>CreateExplainabilityExport</a> operation. This operation returns a summary for each Explainability export. You can filter the list using an array of <a>Filter</a> objects.</p> <p>To retrieve the complete set of properties for a particular Explainability export, use the ARN with the <a>DescribeExplainability</a> operation.</p>", "idempotent": true}, "ListForecastExportJobs": {"name": "ListForecastExportJobs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListForecastExportJobsRequest"}, "output": {"shape": "ListForecastExportJobsResponse"}, "errors": [{"shape": "InvalidNextTokenException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Returns a list of forecast export jobs created using the <a>CreateForecastExportJob</a> operation. For each forecast export job, this operation returns a summary of its properties, including its Amazon Resource Name (ARN). To retrieve the complete set of properties, use the ARN with the <a>DescribeForecastExportJob</a> operation. You can filter the list using an array of <a>Filter</a> objects.</p>", "idempotent": true}, "ListForecasts": {"name": "ListForecasts", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListForecastsRequest"}, "output": {"shape": "ListForecastsResponse"}, "errors": [{"shape": "InvalidNextTokenException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Returns a list of forecasts created using the <a>CreateForecast</a> operation. For each forecast, this operation returns a summary of its properties, including its Amazon Resource Name (ARN). To retrieve the complete set of properties, specify the ARN with the <a>DescribeForecast</a> operation. You can filter the list using an array of <a>Filter</a> objects.</p>", "idempotent": true}, "ListMonitorEvaluations": {"name": "ListMonitorEvaluations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListMonitorEvaluationsRequest"}, "output": {"shape": "ListMonitorEvaluationsResponse"}, "errors": [{"shape": "InvalidNextTokenException"}, {"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns a list of the monitoring evaluation results and predictor events collected by the monitor resource during different windows of time.</p> <p>For information about monitoring see <a>predictor-monitoring</a>. For more information about retrieving monitoring results see <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/predictor-monitoring-results.html\">Viewing Monitoring Results</a>.</p>", "idempotent": true}, "ListMonitors": {"name": "ListMonitors", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListMonitorsRequest"}, "output": {"shape": "ListMonitorsResponse"}, "errors": [{"shape": "InvalidNextTokenException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Returns a list of monitors created with the <a>CreateMonitor</a> operation and <a>CreateAutoPredictor</a> operation. For each monitor resource, this operation returns of a summary of its properties, including its Amazon Resource Name (ARN). You can retrieve a complete set of properties of a monitor resource by specify the monitor's ARN in the <a>DescribeMonitor</a> operation.</p>", "idempotent": true}, "ListPredictorBacktestExportJobs": {"name": "ListPredictorBacktestExportJobs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListPredictorBacktestExportJobsRequest"}, "output": {"shape": "ListPredictorBacktestExportJobsResponse"}, "errors": [{"shape": "InvalidNextTokenException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Returns a list of predictor backtest export jobs created using the <a>CreatePredictorBacktestExportJob</a> operation. This operation returns a summary for each backtest export job. You can filter the list using an array of <a>Filter</a> objects.</p> <p>To retrieve the complete set of properties for a particular backtest export job, use the ARN with the <a>DescribePredictorBacktestExportJob</a> operation.</p>", "idempotent": true}, "ListPredictors": {"name": "ListPredictors", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListPredictorsRequest"}, "output": {"shape": "ListPredictorsResponse"}, "errors": [{"shape": "InvalidNextTokenException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Returns a list of predictors created using the <a>CreateAutoPredictor</a> or <a>CreatePredictor</a> operations. For each predictor, this operation returns a summary of its properties, including its Amazon Resource Name (ARN). </p> <p>You can retrieve the complete set of properties by using the ARN with the <a>DescribeAutoPredictor</a> and <a>DescribePredictor</a> operations. You can filter the list using an array of <a>Filter</a> objects.</p>", "idempotent": true}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Lists the tags for an Amazon Forecast resource.</p>"}, "ListWhatIfAnalyses": {"name": "ListWhatIfAnalyses", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListWhatIfAnalysesRequest"}, "output": {"shape": "ListWhatIfAnalysesResponse"}, "errors": [{"shape": "InvalidNextTokenException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Returns a list of what-if analyses created using the <a>CreateWhatIfAnalysis</a> operation. For each what-if analysis, this operation returns a summary of its properties, including its Amazon Resource Name (ARN). You can retrieve the complete set of properties by using the what-if analysis ARN with the <a>DescribeWhatIfAnalysis</a> operation.</p>", "idempotent": true}, "ListWhatIfForecastExports": {"name": "ListWhatIfForecastExports", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListWhatIfForecastExportsRequest"}, "output": {"shape": "ListWhatIfForecastExportsResponse"}, "errors": [{"shape": "InvalidNextTokenException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Returns a list of what-if forecast exports created using the <a>CreateWhatIfForecastExport</a> operation. For each what-if forecast export, this operation returns a summary of its properties, including its Amazon Resource Name (ARN). You can retrieve the complete set of properties by using the what-if forecast export ARN with the <a>DescribeWhatIfForecastExport</a> operation.</p>", "idempotent": true}, "ListWhatIfForecasts": {"name": "ListWhatIfForecasts", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListWhatIfForecastsRequest"}, "output": {"shape": "ListWhatIfForecastsResponse"}, "errors": [{"shape": "InvalidNextTokenException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Returns a list of what-if forecasts created using the <a>CreateWhatIfForecast</a> operation. For each what-if forecast, this operation returns a summary of its properties, including its Amazon Resource Name (ARN). You can retrieve the complete set of properties by using the what-if forecast ARN with the <a>DescribeWhatIfForecast</a> operation.</p>", "idempotent": true}, "ResumeResource": {"name": "ResumeResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ResumeResourceRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Resumes a stopped monitor resource.</p>", "idempotent": true}, "StopResource": {"name": "StopResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StopResourceRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Stops a resource.</p> <p>The resource undergoes the following states: <code>CREATE_STOPPING</code> and <code>CREATE_STOPPED</code>. You cannot resume a resource once it has been stopped.</p> <p>This operation can be applied to the following resources (and their corresponding child resources):</p> <ul> <li> <p>Dataset Import Job</p> </li> <li> <p>Predictor Job</p> </li> <li> <p>Forecast Job</p> </li> <li> <p>Forecast Export Job</p> </li> <li> <p>Predictor Backtest Export Job</p> </li> <li> <p>Explainability Job</p> </li> <li> <p>Explainability Export Job</p> </li> </ul>", "idempotent": true}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Associates the specified tags to a resource with the specified <code>resourceArn</code>. If existing tags on a resource are not specified in the request parameters, they are not changed. When a resource is deleted, the tags associated with that resource are also deleted.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Deletes the specified tags from a resource.</p>"}, "UpdateDatasetGroup": {"name": "UpdateDatasetGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateDatasetGroupRequest"}, "output": {"shape": "UpdateDatasetGroupResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Replaces the datasets in a dataset group with the specified datasets.</p> <note> <p>The <code>Status</code> of the dataset group must be <code>ACTIVE</code> before you can use the dataset group to create a predictor. Use the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_DescribeDatasetGroup.html\">DescribeDatasetGroup</a> operation to get the status.</p> </note>", "idempotent": true}}, "shapes": {"Action": {"type": "structure", "required": ["AttributeName", "Operation", "Value"], "members": {"AttributeName": {"shape": "Name", "documentation": "<p>The related time series that you are modifying. This value is case insensitive.</p>"}, "Operation": {"shape": "Operation", "documentation": "<p>The operation that is applied to the provided attribute. Operations include:</p> <ul> <li> <p> <code>ADD</code> - adds <code>Value</code> to all rows of <code>AttributeName</code>.</p> </li> <li> <p> <code>SUBTRACT</code> - subtracts <code>Value</code> from all rows of <code>AttributeName</code>.</p> </li> <li> <p> <code>MULTIPLY</code> - multiplies all rows of <code>AttributeName</code> by <code>Value</code>.</p> </li> <li> <p> <code>DIVIDE</code> - divides all rows of <code>AttributeName</code> by <code>Value</code>.</p> </li> </ul>"}, "Value": {"shape": "Double", "documentation": "<p>The value that is applied for the chosen <code>Operation</code>.</p>"}}, "documentation": "<p>Defines the modifications that you are making to an attribute for a what-if forecast. For example, you can use this operation to create a what-if forecast that investigates a 10% off sale on all shoes. To do this, you specify <code>\"AttributeName\": \"shoes\"</code>, <code>\"Operation\": \"MULTIPLY\"</code>, and <code>\"Value\": \"0.90\"</code>. Pair this operation with the <a>TimeSeriesCondition</a> operation within the <a>CreateWhatIfForecastRequest$TimeSeriesTransformations</a> operation to define a subset of attribute items that are modified.</p>"}, "AdditionalDataset": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "Name", "documentation": "<p>The name of the additional dataset. Valid names: <code>\"holiday\"</code> and <code>\"weather\"</code>.</p>"}, "Configuration": {"shape": "Configuration", "documentation": "<p> <b>Weather Index</b> </p> <p>To enable the Weather Index, do not specify a value for <code>Configuration</code>.</p> <p> <b>Holidays</b> </p> <p> <b>Holidays</b> </p> <p>To enable Holidays, set <code>CountryCode</code> to one of the following two-letter country codes:</p> <ul> <li> <p>\"AL\" - ALBANIA</p> </li> <li> <p>\"AR\" - ARGENTINA</p> </li> <li> <p>\"AT\" - AUSTRIA</p> </li> <li> <p>\"AU\" - AUSTRALIA</p> </li> <li> <p>\"BA\" - BOSNIA HERZEGOVINA</p> </li> <li> <p>\"BE\" - BELGIUM</p> </li> <li> <p>\"BG\" - BULGARIA</p> </li> <li> <p>\"BO\" - BOLIVIA</p> </li> <li> <p>\"BR\" - BRAZIL</p> </li> <li> <p>\"BY\" - BELARUS</p> </li> <li> <p>\"CA\" - CANADA</p> </li> <li> <p>\"CL\" - CHILE</p> </li> <li> <p>\"CO\" - COLOMBIA</p> </li> <li> <p>\"CR\" - COSTA RICA</p> </li> <li> <p>\"HR\" - CROATIA</p> </li> <li> <p>\"CZ\" - CZECH REPUBLIC</p> </li> <li> <p>\"DK\" - DENMARK</p> </li> <li> <p>\"EC\" - ECUADOR</p> </li> <li> <p>\"EE\" - ESTONIA</p> </li> <li> <p>\"ET\" - ETHIOPIA</p> </li> <li> <p>\"FI\" - FINLAND</p> </li> <li> <p>\"FR\" - FRANCE</p> </li> <li> <p>\"DE\" - GERMANY</p> </li> <li> <p>\"GR\" - GREECE</p> </li> <li> <p>\"HU\" - HUNGARY</p> </li> <li> <p>\"IS\" - ICELAND</p> </li> <li> <p>\"IN\" - INDIA</p> </li> <li> <p>\"IE\" - IRELAND</p> </li> <li> <p>\"IT\" - ITALY</p> </li> <li> <p>\"JP\" - JAPAN</p> </li> <li> <p>\"KZ\" - KAZAKHSTAN</p> </li> <li> <p>\"KR\" - KOREA</p> </li> <li> <p>\"LV\" - LATVIA</p> </li> <li> <p>\"LI\" - LIECHTENSTEIN</p> </li> <li> <p>\"LT\" - LITHUANIA</p> </li> <li> <p>\"LU\" - LUXEMBOURG</p> </li> <li> <p>\"MK\" - MACEDONIA</p> </li> <li> <p>\"MT\" - MALTA</p> </li> <li> <p>\"MX\" - MEXICO</p> </li> <li> <p>\"MD\" - MOLDOVA</p> </li> <li> <p>\"ME\" - MONTENEGRO</p> </li> <li> <p>\"NL\" - NETHERLANDS</p> </li> <li> <p>\"NZ\" - NEW ZEALAND</p> </li> <li> <p>\"NI\" - NICARAGUA</p> </li> <li> <p>\"NG\" - NIGERIA</p> </li> <li> <p>\"NO\" - NORWAY</p> </li> <li> <p>\"PA\" - PANAMA</p> </li> <li> <p>\"PY\" - PARAGUAY</p> </li> <li> <p>\"PE\" - PERU</p> </li> <li> <p>\"PL\" - POLAND</p> </li> <li> <p>\"PT\" - PORTUGAL</p> </li> <li> <p>\"RO\" - ROMANIA</p> </li> <li> <p>\"RU\" - RUSSIA</p> </li> <li> <p>\"RS\" - SERBIA</p> </li> <li> <p>\"SK\" - SLOVAKIA</p> </li> <li> <p>\"SI\" - SLOVENIA</p> </li> <li> <p>\"ZA\" - SOUTH AFRICA</p> </li> <li> <p>\"ES\" - SPAIN</p> </li> <li> <p>\"SE\" - SWEDEN</p> </li> <li> <p>\"CH\" - SWITZERLAND</p> </li> <li> <p>\"UA\" - UKRAINE</p> </li> <li> <p>\"AE\" - UNITED ARAB EMIRATES</p> </li> <li> <p>\"US\" - UNITED STATES</p> </li> <li> <p>\"UK\" - UNITED KINGDOM</p> </li> <li> <p>\"UY\" - URUGUAY</p> </li> <li> <p>\"VE\" - VENEZUELA</p> </li> </ul>"}}, "documentation": "<p>Describes an additional dataset. This object is part of the <a>DataConfig</a> object. Forecast supports the Weather Index and Holidays additional datasets.</p> <p> <b>Weather Index</b> </p> <p>The Amazon Forecast Weather Index is a built-in dataset that incorporates historical and projected weather information into your model. The Weather Index supplements your datasets with over two years of historical weather data and up to 14 days of projected weather data. For more information, see <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/weather.html\">Amazon Forecast Weather Index</a>.</p> <p> <b>Holidays</b> </p> <p>Holidays is a built-in dataset that incorporates national holiday information into your model. It provides native support for the holiday calendars of 66 countries. To view the holiday calendars, refer to the <a href=\"http://jollyday.sourceforge.net/data.html\">Jollyday</a> library. For more information, see <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/holidays.html\">Holidays Featurization</a>.</p>"}, "AdditionalDatasets": {"type": "list", "member": {"shape": "AdditionalDataset"}, "max": 2, "min": 1}, "Arn": {"type": "string", "max": 256, "pattern": "arn:([a-z\\d-]+):forecast:.*:.*:.+"}, "ArnList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}}, "AttributeConfig": {"type": "structure", "required": ["AttributeName", "Transformations"], "members": {"AttributeName": {"shape": "Name", "documentation": "<p>The name of the attribute as specified in the schema. Amazon Forecast supports the target field of the target time series and the related time series datasets. For example, for the RETAIL domain, the target is <code>demand</code>.</p>"}, "Transformations": {"shape": "Transformations", "documentation": "<p>The method parameters (key-value pairs), which are a map of override parameters. Specify these parameters to override the default values. Related Time Series attributes do not accept aggregation parameters.</p> <p>The following list shows the parameters and their valid values for the \"filling\" featurization method for a <b>Target Time Series</b> dataset. Default values are bolded.</p> <ul> <li> <p> <code>aggregation</code>: <b>sum</b>, <code>avg</code>, <code>first</code>, <code>min</code>, <code>max</code> </p> </li> <li> <p> <code>frontfill</code>: <b>none</b> </p> </li> <li> <p> <code>middlefill</code>: <b>zero</b>, <code>nan</code> (not a number), <code>value</code>, <code>median</code>, <code>mean</code>, <code>min</code>, <code>max</code> </p> </li> <li> <p> <code>backfill</code>: <b>zero</b>, <code>nan</code>, <code>value</code>, <code>median</code>, <code>mean</code>, <code>min</code>, <code>max</code> </p> </li> </ul> <p>The following list shows the parameters and their valid values for a <b>Related Time Series</b> featurization method (there are no defaults):</p> <ul> <li> <p> <code>middlefill</code>: <code>zero</code>, <code>value</code>, <code>median</code>, <code>mean</code>, <code>min</code>, <code>max</code> </p> </li> <li> <p> <code>backfill</code>: <code>zero</code>, <code>value</code>, <code>median</code>, <code>mean</code>, <code>min</code>, <code>max</code> </p> </li> <li> <p> <code>futurefill</code>: <code>zero</code>, <code>value</code>, <code>median</code>, <code>mean</code>, <code>min</code>, <code>max</code> </p> </li> </ul> <p>To set a filling method to a specific value, set the fill parameter to <code>value</code> and define the value in a corresponding <code>_value</code> parameter. For example, to set backfilling to a value of 2, include the following: <code>\"backfill\": \"value\"</code> and <code>\"backfill_value\":\"2\"</code>. </p>"}}, "documentation": "<p>Provides information about the method used to transform attributes.</p> <p>The following is an example using the RETAIL domain:</p> <p> <code>{</code> </p> <p> <code>\"AttributeName\": \"demand\",</code> </p> <p> <code>\"Transformations\": {\"aggregation\": \"sum\", \"middlefill\": \"zero\", \"backfill\": \"zero\"}</code> </p> <p> <code>}</code> </p>"}, "AttributeConfigs": {"type": "list", "member": {"shape": "AttributeConfig"}, "max": 50, "min": 1}, "AttributeType": {"type": "string", "enum": ["string", "integer", "float", "timestamp", "geolocation"]}, "AttributeValue": {"type": "string", "max": 256, "pattern": ".+"}, "AutoMLOverrideStrategy": {"type": "string", "enum": ["LatencyOptimized", "AccuracyOptimized"]}, "Baseline": {"type": "structure", "members": {"PredictorBaseline": {"shape": "PredictorBaseline", "documentation": "<p>The initial <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/metrics.html\">accuracy metrics</a> for the predictor you are monitoring. Use these metrics as a baseline for comparison purposes as you use your predictor and the metrics change.</p>"}}, "documentation": "<p>Metrics you can use as a baseline for comparison purposes. Use these metrics when you interpret monitoring results for an auto predictor.</p>"}, "BaselineMetric": {"type": "structure", "members": {"Name": {"shape": "Name", "documentation": "<p>The name of the metric.</p>"}, "Value": {"shape": "Double", "documentation": "<p>The value for the metric.</p>"}}, "documentation": "<p>An individual metric that you can use for comparison as you evaluate your monitoring results.</p>"}, "BaselineMetrics": {"type": "list", "member": {"shape": "BaselineMetric"}}, "Boolean": {"type": "boolean"}, "CategoricalParameterRange": {"type": "structure", "required": ["Name", "Values"], "members": {"Name": {"shape": "Name", "documentation": "<p>The name of the categorical hyperparameter to tune.</p>"}, "Values": {"shape": "Values", "documentation": "<p>A list of the tunable categories for the hyperparameter.</p>"}}, "documentation": "<p>Specifies a categorical hyperparameter and it's range of tunable values. This object is part of the <a>ParameterRanges</a> object.</p>"}, "CategoricalParameterRanges": {"type": "list", "member": {"shape": "CategoricalParameterRange"}, "max": 20, "min": 1}, "Condition": {"type": "string", "enum": ["EQUALS", "NOT_EQUALS", "LESS_THAN", "GREATER_THAN"]}, "Configuration": {"type": "map", "key": {"shape": "Name"}, "value": {"shape": "Values"}}, "ContinuousParameterRange": {"type": "structure", "required": ["Name", "MaxValue", "MinValue"], "members": {"Name": {"shape": "Name", "documentation": "<p>The name of the hyperparameter to tune.</p>"}, "MaxValue": {"shape": "Double", "documentation": "<p>The maximum tunable value of the hyperparameter.</p>"}, "MinValue": {"shape": "Double", "documentation": "<p>The minimum tunable value of the hyperparameter.</p>"}, "ScalingType": {"shape": "ScalingType", "documentation": "<p>The scale that hyperparameter tuning uses to search the hyperparameter range. Valid values:</p> <dl> <dt>Auto</dt> <dd> <p>Amazon Forecast hyperparameter tuning chooses the best scale for the hyperparameter.</p> </dd> <dt>Linear</dt> <dd> <p>Hyperparameter tuning searches the values in the hyperparameter range by using a linear scale.</p> </dd> <dt>Logarithmic</dt> <dd> <p>Hyperparameter tuning searches the values in the hyperparameter range by using a logarithmic scale.</p> <p>Logarithmic scaling works only for ranges that have values greater than 0.</p> </dd> <dt>ReverseLogarithmic</dt> <dd> <p>hyperparameter tuning searches the values in the hyperparameter range by using a reverse logarithmic scale.</p> <p>Reverse logarithmic scaling works only for ranges that are entirely within the range 0 &lt;= x &lt; 1.0.</p> </dd> </dl> <p>For information about choosing a hyperparameter scale, see <a href=\"http://docs.aws.amazon.com/sagemaker/latest/dg/automatic-model-tuning-define-ranges.html#scaling-type\">Hyperparameter Scaling</a>. One of the following values:</p>"}}, "documentation": "<p>Specifies a continuous hyperparameter and it's range of tunable values. This object is part of the <a>ParameterRanges</a> object.</p>"}, "ContinuousParameterRanges": {"type": "list", "member": {"shape": "ContinuousParameterRange"}, "max": 20, "min": 1}, "CreateAutoPredictorRequest": {"type": "structure", "required": ["PredictorName"], "members": {"PredictorName": {"shape": "Name", "documentation": "<p>A unique name for the predictor</p>"}, "ForecastHorizon": {"shape": "Integer", "documentation": "<p>The number of time-steps that the model predicts. The forecast horizon is also called the prediction length.</p> <p>The maximum forecast horizon is the lesser of 500 time-steps or 1/4 of the TARGET_TIME_SERIES dataset length. If you are retraining an existing AutoPredictor, then the maximum forecast horizon is the lesser of 500 time-steps or 1/3 of the TARGET_TIME_SERIES dataset length.</p> <p>If you are upgrading to an AutoPredictor or retraining an existing AutoPredictor, you cannot update the forecast horizon parameter. You can meet this requirement by providing longer time-series in the dataset.</p>"}, "ForecastTypes": {"shape": "ForecastTypes", "documentation": "<p>The forecast types used to train a predictor. You can specify up to five forecast types. Forecast types can be quantiles from 0.01 to 0.99, by increments of 0.01 or higher. You can also specify the mean forecast with <code>mean</code>.</p>"}, "ForecastDimensions": {"shape": "ForecastDimensions", "documentation": "<p>An array of dimension (field) names that specify how to group the generated forecast.</p> <p>For example, if you are generating forecasts for item sales across all your stores, and your dataset contains a <code>store_id</code> field, you would specify <code>store_id</code> as a dimension to group sales forecasts for each store.</p>"}, "ForecastFrequency": {"shape": "Frequency", "documentation": "<p>The frequency of predictions in a forecast.</p> <p>Valid intervals are an integer followed by Y (Year), M (Month), W (Week), D (Day), H (Hour), and min (Minute). For example, \"1D\" indicates every day and \"15min\" indicates every 15 minutes. You cannot specify a value that would overlap with the next larger frequency. That means, for example, you cannot specify a frequency of 60 minutes, because that is equivalent to 1 hour. The valid values for each frequency are the following:</p> <ul> <li> <p>Minute - 1-59</p> </li> <li> <p>Hour - 1-23</p> </li> <li> <p>Day - 1-6</p> </li> <li> <p>Week - 1-4</p> </li> <li> <p>Month - 1-11</p> </li> <li> <p>Year - 1</p> </li> </ul> <p>Thus, if you want every other week forecasts, specify \"2W\". Or, if you want quarterly forecasts, you specify \"3M\".</p> <p>The frequency must be greater than or equal to the TARGET_TIME_SERIES dataset frequency.</p> <p>When a RELATED_TIME_SERIES dataset is provided, the frequency must be equal to the RELATED_TIME_SERIES dataset frequency.</p>"}, "DataConfig": {"shape": "DataConfig", "documentation": "<p>The data configuration for your dataset group and any additional datasets.</p>"}, "EncryptionConfig": {"shape": "EncryptionConfig"}, "ReferencePredictorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the predictor to retrain or upgrade. This parameter is only used when retraining or upgrading a predictor. When creating a new predictor, do not specify a value for this parameter.</p> <p>When upgrading or retraining a predictor, only specify values for the <code>ReferencePredictorArn</code> and <code>PredictorName</code>. The value for <code>PredictorName</code> must be a unique predictor name.</p>"}, "OptimizationMetric": {"shape": "OptimizationMetric", "documentation": "<p>The accuracy metric used to optimize the predictor.</p>"}, "ExplainPredictor": {"shape": "Boolean", "documentation": "<p>Create an Explainability resource for the predictor.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>Optional metadata to help you categorize and organize your predictors. Each tag consists of a key and an optional value, both of which you define. Tag keys and values are case sensitive.</p> <p>The following restrictions apply to tags:</p> <ul> <li> <p>For each resource, each tag key must be unique and each tag key must have one value.</p> </li> <li> <p>Maximum number of tags per resource: 50.</p> </li> <li> <p>Maximum key length: 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length: 256 Unicode characters in UTF-8.</p> </li> <li> <p>Accepted characters: all letters and numbers, spaces representable in UTF-8, and + - = . _ : / @. If your tagging schema is used across other services and resources, the character restrictions of those services also apply. </p> </li> <li> <p>Key prefixes cannot include any upper or lowercase combination of <code>aws:</code> or <code>AWS:</code>. Values can have this prefix. If a tag value has <code>aws</code> as its prefix but the key does not, For<PERSON><PERSON> considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of <code>aws</code> do not count against your tags per resource limit. You cannot edit or delete tag keys with this prefix.</p> </li> </ul>"}, "MonitorConfig": {"shape": "MonitorConfig", "documentation": "<p>The configuration details for predictor monitoring. Provide a name for the monitor resource to enable predictor monitoring.</p> <p>Predictor monitoring allows you to see how your predictor's performance changes over time. For more information, see <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/predictor-monitoring.html\">Predictor Monitoring</a>.</p>"}, "TimeAlignmentBoundary": {"shape": "TimeAlignmentBoundary", "documentation": "<p>The time boundary Forecast uses to align and aggregate any data that doesn't align with your forecast frequency. Provide the unit of time and the time boundary as a key value pair. For more information on specifying a time boundary, see <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/data-aggregation.html#specifying-time-boundary\">Specifying a Time Boundary</a>. If you don't provide a time boundary, Forecast uses a set of <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/data-aggregation.html#default-time-boundaries\">Default Time Boundaries</a>.</p>"}}}, "CreateAutoPredictorResponse": {"type": "structure", "members": {"PredictorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the predictor.</p>"}}}, "CreateDatasetGroupRequest": {"type": "structure", "required": ["DatasetGroupName", "Domain"], "members": {"DatasetGroupName": {"shape": "Name", "documentation": "<p>A name for the dataset group.</p>"}, "Domain": {"shape": "Domain", "documentation": "<p>The domain associated with the dataset group. When you add a dataset to a dataset group, this value and the value specified for the <code>Domain</code> parameter of the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_CreateDataset.html\">CreateDataset</a> operation must match.</p> <p>The <code>Domain</code> and <code>DatasetType</code> that you choose determine the fields that must be present in training data that you import to a dataset. For example, if you choose the <code>RETAIL</code> domain and <code>TARGET_TIME_SERIES</code> as the <code>DatasetType</code>, Amazon Forecast requires that <code>item_id</code>, <code>timestamp</code>, and <code>demand</code> fields are present in your data. For more information, see <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/howitworks-datasets-groups.html\">Dataset groups</a>.</p>"}, "DatasetArns": {"shape": "ArnList", "documentation": "<p>An array of Amazon Resource Names (ARNs) of the datasets that you want to include in the dataset group.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>The optional metadata that you apply to the dataset group to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource - 50.</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length - 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length - 256 Unicode characters in UTF-8.</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Do not use <code>aws:</code>, <code>AWS:</code>, or any upper or lowercase combination of such as a prefix for keys as it is reserved for Amazon Web Services use. You cannot edit or delete tag keys with this prefix. Values can have this prefix. If a tag value has <code>aws</code> as its prefix but the key does not, then Forecast considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of <code>aws</code> do not count against your tags per resource limit.</p> </li> </ul>"}}}, "CreateDatasetGroupResponse": {"type": "structure", "members": {"DatasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset group.</p>"}}}, "CreateDatasetImportJobRequest": {"type": "structure", "required": ["DatasetImportJobName", "DatasetArn", "DataSource"], "members": {"DatasetImportJobName": {"shape": "Name", "documentation": "<p>The name for the dataset import job. We recommend including the current timestamp in the name, for example, <code>20190721DatasetImport</code>. This can help you avoid getting a <code>ResourceAlreadyExistsException</code> exception.</p>"}, "DatasetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon Forecast dataset that you want to import data to.</p>"}, "DataSource": {"shape": "DataSource", "documentation": "<p>The location of the training data to import and an Identity and Access Management (IAM) role that Amazon Forecast can assume to access the data. The training data must be stored in an Amazon S3 bucket.</p> <p>If encryption is used, <code>DataSource</code> must include an Key Management Service (KMS) key and the IAM role must allow Amazon Forecast permission to access the key. The KMS key and IAM role must match those specified in the <code>EncryptionConfig</code> parameter of the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_CreateDataset.html\">CreateDataset</a> operation.</p>"}, "TimestampFormat": {"shape": "TimestampFormat", "documentation": "<p>The format of timestamps in the dataset. The format that you specify depends on the <code>DataFrequency</code> specified when the dataset was created. The following formats are supported</p> <ul> <li> <p>\"yyyy-MM-dd\"</p> <p>For the following data frequencies: Y, M, W, and D</p> </li> <li> <p>\"yyyy-MM-dd HH:mm:ss\"</p> <p>For the following data frequencies: H, 30min, 15min, and 1min; and optionally, for: Y, M, W, and D</p> </li> </ul> <p>If the format isn't specified, Amazon Forecast expects the format to be \"yyyy-MM-dd HH:mm:ss\".</p>"}, "TimeZone": {"shape": "TimeZone", "documentation": "<p>A single time zone for every item in your dataset. This option is ideal for datasets with all timestamps within a single time zone, or if all timestamps are normalized to a single time zone. </p> <p>Refer to the <a href=\"http://joda-time.sourceforge.net/timezones.html\">Joda-Time API</a> for a complete list of valid time zone names.</p>"}, "UseGeolocationForTimeZone": {"shape": "UseGeolocationForTimeZone", "documentation": "<p>Automatically derive time zone information from the geolocation attribute. This option is ideal for datasets that contain timestamps in multiple time zones and those timestamps are expressed in local time.</p>"}, "GeolocationFormat": {"shape": "GeolocationFormat", "documentation": "<p>The format of the geolocation attribute. The geolocation attribute can be formatted in one of two ways:</p> <ul> <li> <p> <code>LAT_LONG</code> - the latitude and longitude in decimal format (Example: 47.61_-122.33).</p> </li> <li> <p> <code>CC_POSTALCODE</code> (US Only) - the country code (US), followed by the 5-digit ZIP code (Example: US_98121).</p> </li> </ul>"}, "Tags": {"shape": "Tags", "documentation": "<p>The optional metadata that you apply to the dataset import job to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource - 50.</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length - 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length - 256 Unicode characters in UTF-8.</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Do not use <code>aws:</code>, <code>AWS:</code>, or any upper or lowercase combination of such as a prefix for keys as it is reserved for Amazon Web Services use. You cannot edit or delete tag keys with this prefix. Values can have this prefix. If a tag value has <code>aws</code> as its prefix but the key does not, then Forecast considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of <code>aws</code> do not count against your tags per resource limit.</p> </li> </ul>"}, "Format": {"shape": "Format", "documentation": "<p>The format of the imported data, CSV or PARQUET. The default value is CSV.</p>"}, "ImportMode": {"shape": "ImportMode", "documentation": "<p>Specifies whether the dataset import job is a <code>FULL</code> or <code>INCREMENTAL</code> import. A <code>FULL</code> dataset import replaces all of the existing data with the newly imported data. An <code>INCREMENTAL</code> import appends the imported data to the existing data.</p>"}}}, "CreateDatasetImportJobResponse": {"type": "structure", "members": {"DatasetImportJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset import job.</p>"}}}, "CreateDatasetRequest": {"type": "structure", "required": ["DatasetName", "Domain", "DatasetType", "<PERSON><PERSON><PERSON>"], "members": {"DatasetName": {"shape": "Name", "documentation": "<p>A name for the dataset.</p>"}, "Domain": {"shape": "Domain", "documentation": "<p>The domain associated with the dataset. When you add a dataset to a dataset group, this value and the value specified for the <code>Domain</code> parameter of the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_CreateDatasetGroup.html\">CreateDatasetGroup</a> operation must match.</p> <p>The <code>Domain</code> and <code>DatasetType</code> that you choose determine the fields that must be present in the training data that you import to the dataset. For example, if you choose the <code>RETAIL</code> domain and <code>TARGET_TIME_SERIES</code> as the <code>DatasetType</code>, Amazon Forecast requires <code>item_id</code>, <code>timestamp</code>, and <code>demand</code> fields to be present in your data. For more information, see <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/howitworks-datasets-groups.html\">Importing datasets</a>.</p>"}, "DatasetType": {"shape": "DatasetType", "documentation": "<p>The dataset type. Valid values depend on the chosen <code>Domain</code>.</p>"}, "DataFrequency": {"shape": "Frequency", "documentation": "<p>The frequency of data collection. This parameter is required for RELATED_TIME_SERIES datasets.</p> <p>Valid intervals are an integer followed by Y (Year), M (Month), W (Week), D (Day), H (Hour), and min (Minute). For example, \"1D\" indicates every day and \"15min\" indicates every 15 minutes. You cannot specify a value that would overlap with the next larger frequency. That means, for example, you cannot specify a frequency of 60 minutes, because that is equivalent to 1 hour. The valid values for each frequency are the following:</p> <ul> <li> <p>Minute - 1-59</p> </li> <li> <p>Hour - 1-23</p> </li> <li> <p>Day - 1-6</p> </li> <li> <p>Week - 1-4</p> </li> <li> <p>Month - 1-11</p> </li> <li> <p>Year - 1</p> </li> </ul> <p>Thus, if you want every other week forecasts, specify \"2W\". Or, if you want quarterly forecasts, you specify \"3M\".</p>"}, "Schema": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The schema for the dataset. The schema attributes and their order must match the fields in your data. The dataset <code>Domain</code> and <code>DatasetType</code> that you choose determine the minimum required fields in your training data. For information about the required fields for a specific dataset domain and type, see <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/howitworks-domains-ds-types.html\">Dataset Domains and Dataset Types</a>.</p>"}, "EncryptionConfig": {"shape": "EncryptionConfig", "documentation": "<p>An Key Management Service (KMS) key and the Identity and Access Management (IAM) role that Amazon Forecast can assume to access the key.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>The optional metadata that you apply to the dataset to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource - 50.</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length - 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length - 256 Unicode characters in UTF-8.</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Do not use <code>aws:</code>, <code>AWS:</code>, or any upper or lowercase combination of such as a prefix for keys as it is reserved for Amazon Web Services use. You cannot edit or delete tag keys with this prefix. Values can have this prefix. If a tag value has <code>aws</code> as its prefix but the key does not, then Forecast considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of <code>aws</code> do not count against your tags per resource limit.</p> </li> </ul>"}}}, "CreateDatasetResponse": {"type": "structure", "members": {"DatasetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset.</p>"}}}, "CreateExplainabilityExportRequest": {"type": "structure", "required": ["ExplainabilityExportName", "ExplainabilityArn", "Destination"], "members": {"ExplainabilityExportName": {"shape": "Name", "documentation": "<p>A unique name for the Explainability export.</p>"}, "ExplainabilityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Explainability to export.</p>"}, "Destination": {"shape": "DataDestination"}, "Tags": {"shape": "Tags", "documentation": "<p>Optional metadata to help you categorize and organize your resources. Each tag consists of a key and an optional value, both of which you define. Tag keys and values are case sensitive.</p> <p>The following restrictions apply to tags:</p> <ul> <li> <p>For each resource, each tag key must be unique and each tag key must have one value.</p> </li> <li> <p>Maximum number of tags per resource: 50.</p> </li> <li> <p>Maximum key length: 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length: 256 Unicode characters in UTF-8.</p> </li> <li> <p>Accepted characters: all letters and numbers, spaces representable in UTF-8, and + - = . _ : / @. If your tagging schema is used across other services and resources, the character restrictions of those services also apply. </p> </li> <li> <p>Key prefixes cannot include any upper or lowercase combination of <code>aws:</code> or <code>AWS:</code>. Values can have this prefix. If a tag value has <code>aws</code> as its prefix but the key does not, For<PERSON><PERSON> considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of <code>aws</code> do not count against your tags per resource limit. You cannot edit or delete tag keys with this prefix.</p> </li> </ul>"}, "Format": {"shape": "Format", "documentation": "<p>The format of the exported data, CSV or PARQUET.</p>"}}}, "CreateExplainabilityExportResponse": {"type": "structure", "members": {"ExplainabilityExportArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the export.</p>"}}}, "CreateExplainabilityRequest": {"type": "structure", "required": ["ExplainabilityName", "ResourceArn", "ExplainabilityConfig"], "members": {"ExplainabilityName": {"shape": "Name", "documentation": "<p>A unique name for the Explainability.</p>"}, "ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Predictor or Forecast used to create the Explainability.</p>"}, "ExplainabilityConfig": {"shape": "ExplainabilityConfig", "documentation": "<p>The configuration settings that define the granularity of time series and time points for the Explainability.</p>"}, "DataSource": {"shape": "DataSource"}, "Schema": {"shape": "<PERSON><PERSON><PERSON>"}, "EnableVisualization": {"shape": "Boolean", "documentation": "<p>Create an Explainability visualization that is viewable within the Amazon Web Services console.</p>"}, "StartDateTime": {"shape": "LocalDateTime", "documentation": "<p>If <code>TimePointGranularity</code> is set to <code>SPECIFIC</code>, define the first point for the Explainability.</p> <p>Use the following timestamp format: yyyy-MM-ddTHH:mm:ss (example: 2015-01-01T20:00:00)</p>"}, "EndDateTime": {"shape": "LocalDateTime", "documentation": "<p>If <code>TimePointGranularity</code> is set to <code>SPECIFIC</code>, define the last time point for the Explainability.</p> <p>Use the following timestamp format: yyyy-MM-ddTHH:mm:ss (example: 2015-01-01T20:00:00)</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>Optional metadata to help you categorize and organize your resources. Each tag consists of a key and an optional value, both of which you define. Tag keys and values are case sensitive.</p> <p>The following restrictions apply to tags:</p> <ul> <li> <p>For each resource, each tag key must be unique and each tag key must have one value.</p> </li> <li> <p>Maximum number of tags per resource: 50.</p> </li> <li> <p>Maximum key length: 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length: 256 Unicode characters in UTF-8.</p> </li> <li> <p>Accepted characters: all letters and numbers, spaces representable in UTF-8, and + - = . _ : / @. If your tagging schema is used across other services and resources, the character restrictions of those services also apply. </p> </li> <li> <p>Key prefixes cannot include any upper or lowercase combination of <code>aws:</code> or <code>AWS:</code>. Values can have this prefix. If a tag value has <code>aws</code> as its prefix but the key does not, For<PERSON><PERSON> considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of <code>aws</code> do not count against your tags per resource limit. You cannot edit or delete tag keys with this prefix.</p> </li> </ul>"}}}, "CreateExplainabilityResponse": {"type": "structure", "members": {"ExplainabilityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Explainability.</p>"}}}, "CreateForecastExportJobRequest": {"type": "structure", "required": ["ForecastExportJobName", "ForecastArn", "Destination"], "members": {"ForecastExportJobName": {"shape": "Name", "documentation": "<p>The name for the forecast export job.</p>"}, "ForecastArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the forecast that you want to export.</p>"}, "Destination": {"shape": "DataDestination", "documentation": "<p>The location where you want to save the forecast and an Identity and Access Management (IAM) role that Amazon Forecast can assume to access the location. The forecast must be exported to an Amazon S3 bucket.</p> <p>If encryption is used, <code>Destination</code> must include an Key Management Service (KMS) key. The IAM role must allow Amazon Forecast permission to access the key.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>The optional metadata that you apply to the forecast export job to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource - 50.</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length - 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length - 256 Unicode characters in UTF-8.</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Do not use <code>aws:</code>, <code>AWS:</code>, or any upper or lowercase combination of such as a prefix for keys as it is reserved for Amazon Web Services use. You cannot edit or delete tag keys with this prefix. Values can have this prefix. If a tag value has <code>aws</code> as its prefix but the key does not, then Forecast considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of <code>aws</code> do not count against your tags per resource limit.</p> </li> </ul>"}, "Format": {"shape": "Format", "documentation": "<p>The format of the exported data, CSV or PARQUET. The default value is CSV.</p>"}}}, "CreateForecastExportJobResponse": {"type": "structure", "members": {"ForecastExportJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the export job.</p>"}}}, "CreateForecastRequest": {"type": "structure", "required": ["ForecastName", "PredictorArn"], "members": {"ForecastName": {"shape": "Name", "documentation": "<p>A name for the forecast.</p>"}, "PredictorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the predictor to use to generate the forecast.</p>"}, "ForecastTypes": {"shape": "ForecastTypes", "documentation": "<p>The quantiles at which probabilistic forecasts are generated. <b>You can currently specify up to 5 quantiles per forecast</b>. Accepted values include <code>0.01 to 0.99</code> (increments of .01 only) and <code>mean</code>. The mean forecast is different from the median (0.50) when the distribution is not symmetric (for example, Beta and Negative Binomial). </p> <p>The default quantiles are the quantiles you specified during predictor creation. If you didn't specify quantiles, the default values are <code>[\"0.1\", \"0.5\", \"0.9\"]</code>. </p>"}, "Tags": {"shape": "Tags", "documentation": "<p>The optional metadata that you apply to the forecast to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource - 50.</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length - 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length - 256 Unicode characters in UTF-8.</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Do not use <code>aws:</code>, <code>AWS:</code>, or any upper or lowercase combination of such as a prefix for keys as it is reserved for Amazon Web Services use. You cannot edit or delete tag keys with this prefix. Values can have this prefix. If a tag value has <code>aws</code> as its prefix but the key does not, then Forecast considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of <code>aws</code> do not count against your tags per resource limit.</p> </li> </ul>"}, "TimeSeriesSelector": {"shape": "TimeSeriesSelector", "documentation": "<p>Defines the set of time series that are used to create the forecasts in a <code>TimeSeriesIdentifiers</code> object.</p> <p>The <code>TimeSeriesIdentifiers</code> object needs the following information:</p> <ul> <li> <p> <code>DataSource</code> </p> </li> <li> <p> <code>Format</code> </p> </li> <li> <p> <code>Schema</code> </p> </li> </ul>"}}}, "CreateForecastResponse": {"type": "structure", "members": {"ForecastArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the forecast.</p>"}}}, "CreateMonitorRequest": {"type": "structure", "required": ["MonitorName", "ResourceArn"], "members": {"MonitorName": {"shape": "Name", "documentation": "<p>The name of the monitor resource.</p>"}, "ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the predictor to monitor.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/tagging-forecast-resources.html\">tags</a> to apply to the monitor resource.</p>"}}}, "CreateMonitorResponse": {"type": "structure", "members": {"MonitorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the monitor resource.</p>"}}}, "CreatePredictorBacktestExportJobRequest": {"type": "structure", "required": ["PredictorBacktestExportJobName", "PredictorArn", "Destination"], "members": {"PredictorBacktestExportJobName": {"shape": "Name", "documentation": "<p>The name for the backtest export job.</p>"}, "PredictorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the predictor that you want to export.</p>"}, "Destination": {"shape": "DataDestination"}, "Tags": {"shape": "Tags", "documentation": "<p>Optional metadata to help you categorize and organize your backtests. Each tag consists of a key and an optional value, both of which you define. Tag keys and values are case sensitive.</p> <p>The following restrictions apply to tags:</p> <ul> <li> <p>For each resource, each tag key must be unique and each tag key must have one value.</p> </li> <li> <p>Maximum number of tags per resource: 50.</p> </li> <li> <p>Maximum key length: 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length: 256 Unicode characters in UTF-8.</p> </li> <li> <p>Accepted characters: all letters and numbers, spaces representable in UTF-8, and + - = . _ : / @. If your tagging schema is used across other services and resources, the character restrictions of those services also apply. </p> </li> <li> <p>Key prefixes cannot include any upper or lowercase combination of <code>aws:</code> or <code>AWS:</code>. Values can have this prefix. If a tag value has <code>aws</code> as its prefix but the key does not, Forecast considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of <code>aws</code> do not count against your tags per resource limit. You cannot edit or delete tag keys with this prefix.</p> </li> </ul>"}, "Format": {"shape": "Format", "documentation": "<p>The format of the exported data, CSV or PARQUET. The default value is CSV.</p>"}}}, "CreatePredictorBacktestExportJobResponse": {"type": "structure", "members": {"PredictorBacktestExportJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the predictor backtest export job that you want to export.</p>"}}}, "CreatePredictorRequest": {"type": "structure", "required": ["PredictorName", "ForecastHorizon", "InputDataConfig", "FeaturizationConfig"], "members": {"PredictorName": {"shape": "Name", "documentation": "<p>A name for the predictor.</p>"}, "AlgorithmArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the algorithm to use for model training. Required if <code>PerformAutoML</code> is not set to <code>true</code>.</p> <p class=\"title\"> <b>Supported algorithms:</b> </p> <ul> <li> <p> <code>arn:aws:forecast:::algorithm/ARIMA</code> </p> </li> <li> <p> <code>arn:aws:forecast:::algorithm/CNN-QR</code> </p> </li> <li> <p> <code>arn:aws:forecast:::algorithm/Deep_AR_Plus</code> </p> </li> <li> <p> <code>arn:aws:forecast:::algorithm/ETS</code> </p> </li> <li> <p> <code>arn:aws:forecast:::algorithm/NPTS</code> </p> </li> <li> <p> <code>arn:aws:forecast:::algorithm/Prophet</code> </p> </li> </ul>"}, "ForecastHorizon": {"shape": "Integer", "documentation": "<p>Specifies the number of time-steps that the model is trained to predict. The forecast horizon is also called the prediction length.</p> <p>For example, if you configure a dataset for daily data collection (using the <code>DataFrequency</code> parameter of the <a>CreateDataset</a> operation) and set the forecast horizon to 10, the model returns predictions for 10 days.</p> <p>The maximum forecast horizon is the lesser of 500 time-steps or 1/3 of the TARGET_TIME_SERIES dataset length.</p>"}, "ForecastTypes": {"shape": "ForecastTypes", "documentation": "<p>Specifies the forecast types used to train a predictor. You can specify up to five forecast types. Forecast types can be quantiles from 0.01 to 0.99, by increments of 0.01 or higher. You can also specify the mean forecast with <code>mean</code>. </p> <p>The default value is <code>[\"0.10\", \"0.50\", \"0.9\"]</code>.</p>"}, "PerformAutoML": {"shape": "Boolean", "documentation": "<p>Whether to perform AutoML. When Amazon Forecast performs AutoML, it evaluates the algorithms it provides and chooses the best algorithm and configuration for your training dataset.</p> <p>The default value is <code>false</code>. In this case, you are required to specify an algorithm.</p> <p>Set <code>PerformAutoML</code> to <code>true</code> to have Amazon Forecast perform AutoML. This is a good option if you aren't sure which algorithm is suitable for your training data. In this case, <code>PerformHPO</code> must be false.</p>"}, "AutoMLOverrideStrategy": {"shape": "AutoMLOverrideStrategy", "documentation": "<note> <p> The <code>LatencyOptimized</code> AutoML override strategy is only available in private beta. Contact Amazon Web Services Support or your account manager to learn more about access privileges. </p> </note> <p>Used to overide the default AutoML strategy, which is to optimize predictor accuracy. To apply an AutoML strategy that minimizes training time, use <code>LatencyOptimized</code>.</p> <p>This parameter is only valid for predictors trained using AutoML.</p>"}, "PerformHPO": {"shape": "Boolean", "documentation": "<p>Whether to perform hyperparameter optimization (HPO). HPO finds optimal hyperparameter values for your training data. The process of performing HPO is known as running a hyperparameter tuning job.</p> <p>The default value is <code>false</code>. In this case, Amazon Forecast uses default hyperparameter values from the chosen algorithm.</p> <p>To override the default values, set <code>PerformHPO</code> to <code>true</code> and, optionally, supply the <a>HyperParameterTuningJobConfig</a> object. The tuning job specifies a metric to optimize, which hyperparameters participate in tuning, and the valid range for each tunable hyperparameter. In this case, you are required to specify an algorithm and <code>PerformAutoML</code> must be false.</p> <p>The following algorithms support HPO:</p> <ul> <li> <p>DeepAR+</p> </li> <li> <p>CNN-QR</p> </li> </ul>"}, "TrainingParameters": {"shape": "TrainingParameters", "documentation": "<p>The hyperparameters to override for model training. The hyperparameters that you can override are listed in the individual algorithms. For the list of supported algorithms, see <a>aws-forecast-choosing-recipes</a>.</p>"}, "EvaluationParameters": {"shape": "EvaluationParameters", "documentation": "<p>Used to override the default evaluation parameters of the specified algorithm. Amazon Forecast evaluates a predictor by splitting a dataset into training data and testing data. The evaluation parameters define how to perform the split and the number of iterations.</p>"}, "HPOConfig": {"shape": "HyperParameterTuningJobConfig", "documentation": "<p>Provides hyperparameter override values for the algorithm. If you don't provide this parameter, Amazon Forecast uses default values. The individual algorithms specify which hyperparameters support hyperparameter optimization (HPO). For more information, see <a>aws-forecast-choosing-recipes</a>.</p> <p>If you included the <code>HPOConfig</code> object, you must set <code>PerformHPO</code> to true.</p>"}, "InputDataConfig": {"shape": "InputDataConfig", "documentation": "<p>Describes the dataset group that contains the data to use to train the predictor.</p>"}, "FeaturizationConfig": {"shape": "FeaturizationConfig", "documentation": "<p>The featurization configuration.</p>"}, "EncryptionConfig": {"shape": "EncryptionConfig", "documentation": "<p>An Key Management Service (KMS) key and the Identity and Access Management (IAM) role that Amazon Forecast can assume to access the key.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>The optional metadata that you apply to the predictor to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource - 50.</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length - 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length - 256 Unicode characters in UTF-8.</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Do not use <code>aws:</code>, <code>AWS:</code>, or any upper or lowercase combination of such as a prefix for keys as it is reserved for Amazon Web Services use. You cannot edit or delete tag keys with this prefix. Values can have this prefix. If a tag value has <code>aws</code> as its prefix but the key does not, then Forecast considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of <code>aws</code> do not count against your tags per resource limit.</p> </li> </ul>"}, "OptimizationMetric": {"shape": "OptimizationMetric", "documentation": "<p>The accuracy metric used to optimize the predictor.</p>"}}}, "CreatePredictorResponse": {"type": "structure", "members": {"PredictorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the predictor.</p>"}}}, "CreateWhatIfAnalysisRequest": {"type": "structure", "required": ["WhatIfAnalysisName", "ForecastArn"], "members": {"WhatIfAnalysisName": {"shape": "Name", "documentation": "<p>The name of the what-if analysis. Each name must be unique.</p>"}, "ForecastArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the baseline forecast.</p>"}, "TimeSeriesSelector": {"shape": "TimeSeriesSelector", "documentation": "<p>Defines the set of time series that are used in the what-if analysis with a <code>TimeSeriesIdentifiers</code> object. What-if analyses are performed only for the time series in this object.</p> <p>The <code>TimeSeriesIdentifiers</code> object needs the following information:</p> <ul> <li> <p> <code>DataSource</code> </p> </li> <li> <p> <code>Format</code> </p> </li> <li> <p> <code>Schema</code> </p> </li> </ul>"}, "Tags": {"shape": "Tags", "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/tagging-forecast-resources.html\">tags</a> to apply to the what if forecast.</p>"}}}, "CreateWhatIfAnalysisResponse": {"type": "structure", "members": {"WhatIfAnalysisArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the what-if analysis.</p>"}}}, "CreateWhatIfForecastExportRequest": {"type": "structure", "required": ["WhatIfForecastExportName", "WhatIfForecastArns", "Destination"], "members": {"WhatIfForecastExportName": {"shape": "Name", "documentation": "<p>The name of the what-if forecast to export.</p>"}, "WhatIfForecastArns": {"shape": "WhatIfForecastArnListForExport", "documentation": "<p>The list of what-if forecast Amazon Resource Names (ARNs) to export.</p>"}, "Destination": {"shape": "DataDestination", "documentation": "<p>The location where you want to save the forecast and an Identity and Access Management (IAM) role that Amazon Forecast can assume to access the location. The forecast must be exported to an Amazon S3 bucket.</p> <p>If encryption is used, <code>Destination</code> must include an Key Management Service (KMS) key. The IAM role must allow Amazon Forecast permission to access the key.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/tagging-forecast-resources.html\">tags</a> to apply to the what if forecast.</p>"}, "Format": {"shape": "Format", "documentation": "<p>The format of the exported data, CSV or PARQUET.</p>"}}}, "CreateWhatIfForecastExportResponse": {"type": "structure", "members": {"WhatIfForecastExportArn": {"shape": "LongArn", "documentation": "<p>The Amazon Resource Name (ARN) of the what-if forecast.</p>"}}}, "CreateWhatIfForecastRequest": {"type": "structure", "required": ["WhatIfForecastName", "WhatIfAnalysisArn"], "members": {"WhatIfForecastName": {"shape": "Name", "documentation": "<p>The name of the what-if forecast. Names must be unique within each what-if analysis.</p>"}, "WhatIfAnalysisArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the what-if analysis.</p>"}, "TimeSeriesTransformations": {"shape": "TimeSeriesTransformations", "documentation": "<p>The transformations that are applied to the baseline time series. Each transformation contains an action and a set of conditions. An action is applied only when all conditions are met. If no conditions are provided, the action is applied to all items.</p>"}, "TimeSeriesReplacementsDataSource": {"shape": "TimeSeriesReplacementsDataSource", "documentation": "<p>The replacement time series dataset, which contains the rows that you want to change in the related time series dataset. A replacement time series does not need to contain all rows that are in the baseline related time series. Include only the rows (measure-dimension combinations) that you want to include in the what-if forecast.</p> <p>This dataset is merged with the original time series to create a transformed dataset that is used for the what-if analysis.</p> <p>This dataset should contain the items to modify (such as item_id or workforce_type), any relevant dimensions, the timestamp column, and at least one of the related time series columns. This file should not contain duplicate timestamps for the same time series.</p> <p>Timestamps and item_ids not included in this dataset are not included in the what-if analysis. </p>"}, "Tags": {"shape": "Tags", "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/tagging-forecast-resources.html\">tags</a> to apply to the what if forecast.</p>"}}}, "CreateWhatIfForecastResponse": {"type": "structure", "members": {"WhatIfForecastArn": {"shape": "LongArn", "documentation": "<p>The Amazon Resource Name (ARN) of the what-if forecast.</p>"}}}, "DataConfig": {"type": "structure", "required": ["DatasetGroupArn"], "members": {"DatasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the dataset group used to train the predictor.</p>"}, "AttributeConfigs": {"shape": "AttributeConfigs", "documentation": "<p>Aggregation and filling options for attributes in your dataset group.</p>"}, "AdditionalDatasets": {"shape": "AdditionalDatasets", "documentation": "<p>Additional built-in datasets like Holidays and the Weather Index.</p>"}}, "documentation": "<p>The data configuration for your dataset group and any additional datasets.</p>"}, "DataDestination": {"type": "structure", "required": ["S3Config"], "members": {"S3Config": {"shape": "S3Config", "documentation": "<p>The path to an Amazon Simple Storage Service (Amazon S3) bucket along with the credentials to access the bucket.</p>"}}, "documentation": "<p>The destination for an export job. Provide an S3 path, an Identity and Access Management (IAM) role that allows Amazon Forecast to access the location, and an Key Management Service (KMS) key (optional). </p>"}, "DataSource": {"type": "structure", "required": ["S3Config"], "members": {"S3Config": {"shape": "S3Config", "documentation": "<p>The path to the data stored in an Amazon Simple Storage Service (Amazon S3) bucket along with the credentials to access the data.</p>"}}, "documentation": "<p>The source of your data, an Identity and Access Management (IAM) role that allows Amazon Forecast to access the data and, optionally, an Key Management Service (KMS) key.</p>"}, "DatasetGroupSummary": {"type": "structure", "members": {"DatasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset group.</p>"}, "DatasetGroupName": {"shape": "Name", "documentation": "<p>The name of the dataset group.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the dataset group was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>When the dataset group was created or last updated from a call to the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_UpdateDatasetGroup.html\">UpdateDatasetGroup</a> operation. While the dataset group is being updated, <code>LastModificationTime</code> is the current time of the <code>ListDatasetGroups</code> call.</p>"}}, "documentation": "<p>Provides a summary of the dataset group properties used in the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_ListDatasetGroups.html\">ListDatasetGroups</a> operation. To get the complete set of properties, call the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_DescribeDatasetGroup.html\">DescribeDatasetGroup</a> operation, and provide the <code>DatasetGroupArn</code>.</p>"}, "DatasetGroups": {"type": "list", "member": {"shape": "DatasetGroupSummary"}}, "DatasetImportJobSummary": {"type": "structure", "members": {"DatasetImportJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset import job.</p>"}, "DatasetImportJobName": {"shape": "Name", "documentation": "<p>The name of the dataset import job.</p>"}, "DataSource": {"shape": "DataSource", "documentation": "<p>The location of the training data to import and an Identity and Access Management (IAM) role that Amazon Forecast can assume to access the data. The training data must be stored in an Amazon S3 bucket.</p> <p>If encryption is used, <code>DataSource</code> includes an Key Management Service (KMS) key.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the dataset import job. States include:</p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> <li> <p> <code>CREATE_STOPPING</code>, <code>CREATE_STOPPED</code> </p> </li> </ul>"}, "Message": {"shape": "ErrorMessage", "documentation": "<p>If an error occurred, an informational message about the error.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the dataset import job was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The last time the resource was modified. The timestamp depends on the status of the job:</p> <ul> <li> <p> <code>CREATE_PENDING</code> - The <code>CreationTime</code>.</p> </li> <li> <p> <code>CREATE_IN_PROGRESS</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPING</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPED</code> - When the job stopped.</p> </li> <li> <p> <code>ACTIVE</code> or <code>CREATE_FAILED</code> - When the job finished or failed.</p> </li> </ul>"}, "ImportMode": {"shape": "ImportMode", "documentation": "<p>The import mode of the dataset import job, FULL or INCREMENTAL.</p>"}}, "documentation": "<p>Provides a summary of the dataset import job properties used in the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_ListDatasetImportJobs.html\">ListDatasetImportJobs</a> operation. To get the complete set of properties, call the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_DescribeDatasetImportJob.html\">DescribeDatasetImportJob</a> operation, and provide the <code>DatasetImportJobArn</code>.</p>"}, "DatasetImportJobs": {"type": "list", "member": {"shape": "DatasetImportJobSummary"}}, "DatasetSummary": {"type": "structure", "members": {"DatasetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset.</p>"}, "DatasetName": {"shape": "Name", "documentation": "<p>The name of the dataset.</p>"}, "DatasetType": {"shape": "DatasetType", "documentation": "<p>The dataset type.</p>"}, "Domain": {"shape": "Domain", "documentation": "<p>The domain associated with the dataset.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the dataset was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>When you create a dataset, <code>LastModificationTime</code> is the same as <code>CreationTime</code>. While data is being imported to the dataset, <code>LastModificationTime</code> is the current time of the <code>ListDatasets</code> call. After a <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_CreateDatasetImportJob.html\">CreateDatasetImportJob</a> operation has finished, <code>LastModificationTime</code> is when the import job completed or failed.</p>"}}, "documentation": "<p>Provides a summary of the dataset properties used in the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_ListDatasets.html\">ListDatasets</a> operation. To get the complete set of properties, call the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_DescribeDataset.html\">DescribeDataset</a> operation, and provide the <code>DatasetArn</code>.</p>"}, "DatasetType": {"type": "string", "enum": ["TARGET_TIME_SERIES", "RELATED_TIME_SERIES", "ITEM_METADATA"]}, "Datasets": {"type": "list", "member": {"shape": "DatasetSummary"}}, "DayOfMonth": {"type": "integer", "max": 28, "min": 1}, "DayOfWeek": {"type": "string", "enum": ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"]}, "DeleteDatasetGroupRequest": {"type": "structure", "required": ["DatasetGroupArn"], "members": {"DatasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset group to delete.</p>"}}}, "DeleteDatasetImportJobRequest": {"type": "structure", "required": ["DatasetImportJobArn"], "members": {"DatasetImportJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset import job to delete.</p>"}}}, "DeleteDatasetRequest": {"type": "structure", "required": ["DatasetArn"], "members": {"DatasetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset to delete.</p>"}}}, "DeleteExplainabilityExportRequest": {"type": "structure", "required": ["ExplainabilityExportArn"], "members": {"ExplainabilityExportArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Explainability export to delete. </p>"}}}, "DeleteExplainabilityRequest": {"type": "structure", "required": ["ExplainabilityArn"], "members": {"ExplainabilityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Explainability resource to delete.</p>"}}}, "DeleteForecastExportJobRequest": {"type": "structure", "required": ["ForecastExportJobArn"], "members": {"ForecastExportJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the forecast export job to delete.</p>"}}}, "DeleteForecastRequest": {"type": "structure", "required": ["ForecastArn"], "members": {"ForecastArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the forecast to delete.</p>"}}}, "DeleteMonitorRequest": {"type": "structure", "required": ["MonitorArn"], "members": {"MonitorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the monitor resource to delete.</p>"}}}, "DeletePredictorBacktestExportJobRequest": {"type": "structure", "required": ["PredictorBacktestExportJobArn"], "members": {"PredictorBacktestExportJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the predictor backtest export job to delete.</p>"}}}, "DeletePredictorRequest": {"type": "structure", "required": ["PredictorArn"], "members": {"PredictorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the predictor to delete.</p>"}}}, "DeleteResourceTreeRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the parent resource to delete. All child resources of the parent resource will also be deleted.</p>"}}}, "DeleteWhatIfAnalysisRequest": {"type": "structure", "required": ["WhatIfAnalysisArn"], "members": {"WhatIfAnalysisArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the what-if analysis that you want to delete.</p>"}}}, "DeleteWhatIfForecastExportRequest": {"type": "structure", "required": ["WhatIfForecastExportArn"], "members": {"WhatIfForecastExportArn": {"shape": "LongArn", "documentation": "<p>The Amazon Resource Name (ARN) of the what-if forecast export that you want to delete.</p>"}}}, "DeleteWhatIfForecastRequest": {"type": "structure", "required": ["WhatIfForecastArn"], "members": {"WhatIfForecastArn": {"shape": "LongArn", "documentation": "<p>The Amazon Resource Name (ARN) of the what-if forecast that you want to delete.</p>"}}}, "DescribeAutoPredictorRequest": {"type": "structure", "required": ["PredictorArn"], "members": {"PredictorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the predictor.</p>"}}}, "DescribeAutoPredictorResponse": {"type": "structure", "members": {"PredictorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the predictor</p>"}, "PredictorName": {"shape": "Name", "documentation": "<p>The name of the predictor.</p>"}, "ForecastHorizon": {"shape": "Integer", "documentation": "<p>The number of time-steps that the model predicts. The forecast horizon is also called the prediction length.</p>"}, "ForecastTypes": {"shape": "ForecastTypes", "documentation": "<p>The forecast types used during predictor training. Default value is [\"0.1\",\"0.5\",\"0.9\"].</p>"}, "ForecastFrequency": {"shape": "Frequency", "documentation": "<p>The frequency of predictions in a forecast.</p> <p>Valid intervals are Y (Year), M (Month), W (Week), D (Day), H (Hour), 30min (30 minutes), 15min (15 minutes), 10min (10 minutes), 5min (5 minutes), and 1min (1 minute). For example, \"Y\" indicates every year and \"5min\" indicates every five minutes.</p>"}, "ForecastDimensions": {"shape": "ForecastDimensions", "documentation": "<p>An array of dimension (field) names that specify the attributes used to group your time series.</p>"}, "DatasetImportJobArns": {"shape": "ArnList", "documentation": "<p>An array of the ARNs of the dataset import jobs used to import training data for the predictor.</p>"}, "DataConfig": {"shape": "DataConfig", "documentation": "<p>The data configuration for your dataset group and any additional datasets.</p>"}, "EncryptionConfig": {"shape": "EncryptionConfig"}, "ReferencePredictorSummary": {"shape": "ReferencePredictor<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The ARN and state of the reference predictor. This parameter is only valid for retrained or upgraded predictors.</p>"}, "EstimatedTimeRemainingInMinutes": {"shape": "<PERSON>", "documentation": "<p>The estimated time remaining in minutes for the predictor training job to complete.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the predictor. States include: </p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>CREATE_STOPPING</code>, <code>CREATE_STOPPED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> </ul>"}, "Message": {"shape": "Message", "documentation": "<p>In the event of an error, a message detailing the cause of the error.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>The timestamp of the CreateAutoPredictor request.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The last time the resource was modified. The timestamp depends on the status of the job:</p> <ul> <li> <p> <code>CREATE_PENDING</code> - The <code>CreationTime</code>.</p> </li> <li> <p> <code>CREATE_IN_PROGRESS</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPING</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPED</code> - When the job stopped.</p> </li> <li> <p> <code>ACTIVE</code> or <code>CREATE_FAILED</code> - When the job finished or failed.</p> </li> </ul>"}, "OptimizationMetric": {"shape": "OptimizationMetric", "documentation": "<p>The accuracy metric used to optimize the predictor.</p>"}, "ExplainabilityInfo": {"shape": "ExplainabilityInfo", "documentation": "<p>Provides the status and ARN of the Predictor Explainability.</p>"}, "MonitorInfo": {"shape": "MonitorInfo", "documentation": "<p>A object with the Amazon Resource Name (ARN) and status of the monitor resource.</p>"}, "TimeAlignmentBoundary": {"shape": "TimeAlignmentBoundary", "documentation": "<p>The time boundary Forecast uses when aggregating data.</p>"}}}, "DescribeDatasetGroupRequest": {"type": "structure", "required": ["DatasetGroupArn"], "members": {"DatasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset group.</p>"}}}, "DescribeDatasetGroupResponse": {"type": "structure", "members": {"DatasetGroupName": {"shape": "Name", "documentation": "<p>The name of the dataset group.</p>"}, "DatasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the dataset group.</p>"}, "DatasetArns": {"shape": "ArnList", "documentation": "<p>An array of Amazon Resource Names (ARNs) of the datasets contained in the dataset group.</p>"}, "Domain": {"shape": "Domain", "documentation": "<p>The domain associated with the dataset group.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the dataset group. States include:</p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> <li> <p> <code>UPDATE_PENDING</code>, <code>UPDATE_IN_PROGRESS</code>, <code>UPDATE_FAILED</code> </p> </li> </ul> <p>The <code>UPDATE</code> states apply when you call the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_UpdateDatasetGroup.html\">UpdateDatasetGroup</a> operation.</p> <note> <p>The <code>Status</code> of the dataset group must be <code>ACTIVE</code> before you can use the dataset group to create a predictor.</p> </note>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the dataset group was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>When the dataset group was created or last updated from a call to the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_UpdateDatasetGroup.html\">UpdateDatasetGroup</a> operation. While the dataset group is being updated, <code>LastModificationTime</code> is the current time of the <code>DescribeDatasetGroup</code> call.</p>"}}}, "DescribeDatasetImportJobRequest": {"type": "structure", "required": ["DatasetImportJobArn"], "members": {"DatasetImportJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset import job.</p>"}}}, "DescribeDatasetImportJobResponse": {"type": "structure", "members": {"DatasetImportJobName": {"shape": "Name", "documentation": "<p>The name of the dataset import job.</p>"}, "DatasetImportJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the dataset import job.</p>"}, "DatasetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset that the training data was imported to.</p>"}, "TimestampFormat": {"shape": "TimestampFormat", "documentation": "<p>The format of timestamps in the dataset. The format that you specify depends on the <code>DataFrequency</code> specified when the dataset was created. The following formats are supported</p> <ul> <li> <p>\"yyyy-MM-dd\"</p> <p>For the following data frequencies: Y, M, W, and D</p> </li> <li> <p>\"yyyy-MM-dd HH:mm:ss\"</p> <p>For the following data frequencies: H, 30min, 15min, and 1min; and optionally, for: Y, M, W, and D</p> </li> </ul>"}, "TimeZone": {"shape": "TimeZone", "documentation": "<p>The single time zone applied to every item in the dataset</p>"}, "UseGeolocationForTimeZone": {"shape": "UseGeolocationForTimeZone", "documentation": "<p>Whether <code>TimeZone</code> is automatically derived from the geolocation attribute.</p>"}, "GeolocationFormat": {"shape": "GeolocationFormat", "documentation": "<p>The format of the geolocation attribute. Valid Values:<code>\"LAT_LONG\"</code> and <code>\"CC_POSTALCODE\"</code>.</p>"}, "DataSource": {"shape": "DataSource", "documentation": "<p>The location of the training data to import and an Identity and Access Management (IAM) role that Amazon Forecast can assume to access the data.</p> <p>If encryption is used, <code>DataSource</code> includes an Key Management Service (KMS) key.</p>"}, "EstimatedTimeRemainingInMinutes": {"shape": "<PERSON>", "documentation": "<p>The estimated time remaining in minutes for the dataset import job to complete.</p>"}, "FieldStatistics": {"shape": "FieldStatistics", "documentation": "<p>Statistical information about each field in the input data.</p>"}, "DataSize": {"shape": "Double", "documentation": "<p>The size of the dataset in gigabytes (GB) after the import job has finished.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the dataset import job. States include:</p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> <li> <p> <code>CREATE_STOPPING</code>, <code>CREATE_STOPPED</code> </p> </li> </ul>"}, "Message": {"shape": "Message", "documentation": "<p>If an error occurred, an informational message about the error.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the dataset import job was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The last time the resource was modified. The timestamp depends on the status of the job:</p> <ul> <li> <p> <code>CREATE_PENDING</code> - The <code>CreationTime</code>.</p> </li> <li> <p> <code>CREATE_IN_PROGRESS</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPING</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPED</code> - When the job stopped.</p> </li> <li> <p> <code>ACTIVE</code> or <code>CREATE_FAILED</code> - When the job finished or failed.</p> </li> </ul>"}, "Format": {"shape": "Format", "documentation": "<p>The format of the imported data, CSV or PARQUET.</p>"}, "ImportMode": {"shape": "ImportMode", "documentation": "<p>The import mode of the dataset import job, FULL or INCREMENTAL.</p>"}}}, "DescribeDatasetRequest": {"type": "structure", "required": ["DatasetArn"], "members": {"DatasetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset.</p>"}}}, "DescribeDatasetResponse": {"type": "structure", "members": {"DatasetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset.</p>"}, "DatasetName": {"shape": "Name", "documentation": "<p>The name of the dataset.</p>"}, "Domain": {"shape": "Domain", "documentation": "<p>The domain associated with the dataset.</p>"}, "DatasetType": {"shape": "DatasetType", "documentation": "<p>The dataset type.</p>"}, "DataFrequency": {"shape": "Frequency", "documentation": "<p>The frequency of data collection.</p> <p>Valid intervals are Y (Year), M (Month), W (Week), D (Day), H (Hour), 30min (30 minutes), 15min (15 minutes), 10min (10 minutes), 5min (5 minutes), and 1min (1 minute). For example, \"M\" indicates every month and \"30min\" indicates every 30 minutes.</p>"}, "Schema": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>An array of <code>SchemaAttribute</code> objects that specify the dataset fields. Each <code>SchemaAttribute</code> specifies the name and data type of a field.</p>"}, "EncryptionConfig": {"shape": "EncryptionConfig", "documentation": "<p>The Key Management Service (KMS) key and the Identity and Access Management (IAM) role that Amazon Forecast can assume to access the key.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the dataset. States include:</p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> <li> <p> <code>UPDATE_PENDING</code>, <code>UPDATE_IN_PROGRESS</code>, <code>UPDATE_FAILED</code> </p> </li> </ul> <p>The <code>UPDATE</code> states apply while data is imported to the dataset from a call to the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_CreateDatasetImportJob.html\">CreateDatasetImportJob</a> operation and reflect the status of the dataset import job. For example, when the import job status is <code>CREATE_IN_PROGRESS</code>, the status of the dataset is <code>UPDATE_IN_PROGRESS</code>.</p> <note> <p>The <code>Status</code> of the dataset must be <code>ACTIVE</code> before you can import training data.</p> </note>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the dataset was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>When you create a dataset, <code>LastModificationTime</code> is the same as <code>CreationTime</code>. While data is being imported to the dataset, <code>LastModificationTime</code> is the current time of the <code>DescribeDataset</code> call. After a <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_CreateDatasetImportJob.html\">CreateDatasetImportJob</a> operation has finished, <code>LastModificationTime</code> is when the import job completed or failed.</p>"}}}, "DescribeExplainabilityExportRequest": {"type": "structure", "required": ["ExplainabilityExportArn"], "members": {"ExplainabilityExportArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Explainability export.</p>"}}}, "DescribeExplainabilityExportResponse": {"type": "structure", "members": {"ExplainabilityExportArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Explainability export.</p>"}, "ExplainabilityExportName": {"shape": "Name", "documentation": "<p>The name of the Explainability export.</p>"}, "ExplainabilityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Explainability export.</p>"}, "Destination": {"shape": "DataDestination"}, "Message": {"shape": "Message", "documentation": "<p>Information about any errors that occurred during the export.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the Explainability export. States include: </p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>CREATE_STOPPING</code>, <code>CREATE_STOPPED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> </ul>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the Explainability export was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The last time the resource was modified. The timestamp depends on the status of the job:</p> <ul> <li> <p> <code>CREATE_PENDING</code> - The <code>CreationTime</code>.</p> </li> <li> <p> <code>CREATE_IN_PROGRESS</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPING</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPED</code> - When the job stopped.</p> </li> <li> <p> <code>ACTIVE</code> or <code>CREATE_FAILED</code> - When the job finished or failed.</p> </li> </ul>"}, "Format": {"shape": "Format", "documentation": "<p>The format of the exported data, CSV or PARQUET.</p>"}}}, "DescribeExplainabilityRequest": {"type": "structure", "required": ["ExplainabilityArn"], "members": {"ExplainabilityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Explaianability to describe.</p>"}}}, "DescribeExplainabilityResponse": {"type": "structure", "members": {"ExplainabilityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Explainability.</p>"}, "ExplainabilityName": {"shape": "Name", "documentation": "<p>The name of the Explainability.</p>"}, "ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Predictor or Forecast used to create the Explainability resource.</p>"}, "ExplainabilityConfig": {"shape": "ExplainabilityConfig", "documentation": "<p>The configuration settings that define the granularity of time series and time points for the Explainability.</p>"}, "EnableVisualization": {"shape": "Boolean", "documentation": "<p>Whether the visualization was enabled for the Explainability resource.</p>"}, "DataSource": {"shape": "DataSource"}, "Schema": {"shape": "<PERSON><PERSON><PERSON>"}, "StartDateTime": {"shape": "LocalDateTime", "documentation": "<p>If <code>TimePointGranularity</code> is set to <code>SPECIFIC</code>, the first time point in the Explainability.</p>"}, "EndDateTime": {"shape": "LocalDateTime", "documentation": "<p>If <code>TimePointGranularity</code> is set to <code>SPECIFIC</code>, the last time point in the Explainability.</p>"}, "EstimatedTimeRemainingInMinutes": {"shape": "<PERSON>", "documentation": "<p>The estimated time remaining in minutes for the <a>CreateExplainability</a> job to complete.</p>"}, "Message": {"shape": "Message", "documentation": "<p>If an error occurred, a message about the error.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the Explainability resource. States include: </p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>CREATE_STOPPING</code>, <code>CREATE_STOPPED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> </ul>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the Explainability resource was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The last time the resource was modified. The timestamp depends on the status of the job:</p> <ul> <li> <p> <code>CREATE_PENDING</code> - The <code>CreationTime</code>.</p> </li> <li> <p> <code>CREATE_IN_PROGRESS</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPING</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPED</code> - When the job stopped.</p> </li> <li> <p> <code>ACTIVE</code> or <code>CREATE_FAILED</code> - When the job finished or failed.</p> </li> </ul>"}}}, "DescribeForecastExportJobRequest": {"type": "structure", "required": ["ForecastExportJobArn"], "members": {"ForecastExportJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the forecast export job.</p>"}}}, "DescribeForecastExportJobResponse": {"type": "structure", "members": {"ForecastExportJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the forecast export job.</p>"}, "ForecastExportJobName": {"shape": "Name", "documentation": "<p>The name of the forecast export job.</p>"}, "ForecastArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the exported forecast.</p>"}, "Destination": {"shape": "DataDestination", "documentation": "<p>The path to the Amazon Simple Storage Service (Amazon S3) bucket where the forecast is exported.</p>"}, "Message": {"shape": "Message", "documentation": "<p>If an error occurred, an informational message about the error.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the forecast export job. States include:</p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>CREATE_STOPPING</code>, <code>CREATE_STOPPED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> </ul> <note> <p>The <code>Status</code> of the forecast export job must be <code>ACTIVE</code> before you can access the forecast in your S3 bucket.</p> </note>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the forecast export job was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The last time the resource was modified. The timestamp depends on the status of the job:</p> <ul> <li> <p> <code>CREATE_PENDING</code> - The <code>CreationTime</code>.</p> </li> <li> <p> <code>CREATE_IN_PROGRESS</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPING</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPED</code> - When the job stopped.</p> </li> <li> <p> <code>ACTIVE</code> or <code>CREATE_FAILED</code> - When the job finished or failed.</p> </li> </ul>"}, "Format": {"shape": "Format", "documentation": "<p>The format of the exported data, CSV or PARQUET.</p>"}}}, "DescribeForecastRequest": {"type": "structure", "required": ["ForecastArn"], "members": {"ForecastArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the forecast.</p>"}}}, "DescribeForecastResponse": {"type": "structure", "members": {"ForecastArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The forecast ARN as specified in the request.</p>"}, "ForecastName": {"shape": "Name", "documentation": "<p>The name of the forecast.</p>"}, "ForecastTypes": {"shape": "ForecastTypes", "documentation": "<p>The quantiles at which probabilistic forecasts were generated.</p>"}, "PredictorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the predictor used to generate the forecast.</p>"}, "DatasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the dataset group that provided the data used to train the predictor.</p>"}, "EstimatedTimeRemainingInMinutes": {"shape": "<PERSON>", "documentation": "<p>The estimated time remaining in minutes for the forecast job to complete.</p>"}, "Status": {"shape": "String", "documentation": "<p>The status of the forecast. States include:</p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>CREATE_STOPPING</code>, <code>CREATE_STOPPED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> </ul> <note> <p>The <code>Status</code> of the forecast must be <code>ACTIVE</code> before you can query or export the forecast.</p> </note>"}, "Message": {"shape": "ErrorMessage", "documentation": "<p>If an error occurred, an informational message about the error.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the forecast creation task was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The last time the resource was modified. The timestamp depends on the status of the job:</p> <ul> <li> <p> <code>CREATE_PENDING</code> - The <code>CreationTime</code>.</p> </li> <li> <p> <code>CREATE_IN_PROGRESS</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPING</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPED</code> - When the job stopped.</p> </li> <li> <p> <code>ACTIVE</code> or <code>CREATE_FAILED</code> - When the job finished or failed.</p> </li> </ul>"}, "TimeSeriesSelector": {"shape": "TimeSeriesSelector", "documentation": "<p>The time series to include in the forecast.</p>"}}}, "DescribeMonitorRequest": {"type": "structure", "required": ["MonitorArn"], "members": {"MonitorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the monitor resource to describe.</p>"}}}, "DescribeMonitorResponse": {"type": "structure", "members": {"MonitorName": {"shape": "Name", "documentation": "<p>The name of the monitor.</p>"}, "MonitorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the monitor resource described.</p>"}, "ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the auto predictor being monitored.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the monitor resource.</p>"}, "LastEvaluationTime": {"shape": "Timestamp", "documentation": "<p>The timestamp of the latest evaluation completed by the monitor.</p>"}, "LastEvaluationState": {"shape": "EvaluationState", "documentation": "<p>The state of the monitor's latest evaluation.</p>"}, "Baseline": {"shape": "Baseline", "documentation": "<p>Metrics you can use as a baseline for comparison purposes. Use these values you interpret monitoring results for an auto predictor.</p>"}, "Message": {"shape": "Message", "documentation": "<p>An error message, if any, for the monitor.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>The timestamp for when the monitor resource was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The timestamp of the latest modification to the monitor.</p>"}, "EstimatedEvaluationTimeRemainingInMinutes": {"shape": "<PERSON>", "documentation": "<p>The estimated number of minutes remaining before the monitor resource finishes its current evaluation.</p>"}}}, "DescribePredictorBacktestExportJobRequest": {"type": "structure", "required": ["PredictorBacktestExportJobArn"], "members": {"PredictorBacktestExportJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the predictor backtest export job.</p>"}}}, "DescribePredictorBacktestExportJobResponse": {"type": "structure", "members": {"PredictorBacktestExportJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the predictor backtest export job.</p>"}, "PredictorBacktestExportJobName": {"shape": "Name", "documentation": "<p>The name of the predictor backtest export job.</p>"}, "PredictorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the predictor.</p>"}, "Destination": {"shape": "DataDestination"}, "Message": {"shape": "Message", "documentation": "<p>Information about any errors that may have occurred during the backtest export.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the predictor backtest export job. States include: </p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>CREATE_STOPPING</code>, <code>CREATE_STOPPED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> </ul>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the predictor backtest export job was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The last time the resource was modified. The timestamp depends on the status of the job:</p> <ul> <li> <p> <code>CREATE_PENDING</code> - The <code>CreationTime</code>.</p> </li> <li> <p> <code>CREATE_IN_PROGRESS</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPING</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPED</code> - When the job stopped.</p> </li> <li> <p> <code>ACTIVE</code> or <code>CREATE_FAILED</code> - When the job finished or failed.</p> </li> </ul>"}, "Format": {"shape": "Format", "documentation": "<p>The format of the exported data, CSV or PARQUET.</p>"}}}, "DescribePredictorRequest": {"type": "structure", "required": ["PredictorArn"], "members": {"PredictorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the predictor that you want information about.</p>"}}}, "DescribePredictorResponse": {"type": "structure", "members": {"PredictorArn": {"shape": "Name", "documentation": "<p>The ARN of the predictor.</p>"}, "PredictorName": {"shape": "Name", "documentation": "<p>The name of the predictor.</p>"}, "AlgorithmArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the algorithm used for model training.</p>"}, "AutoMLAlgorithmArns": {"shape": "ArnList", "documentation": "<p>When <code>PerformAutoML</code> is specified, the ARN of the chosen algorithm.</p>"}, "ForecastHorizon": {"shape": "Integer", "documentation": "<p>The number of time-steps of the forecast. The forecast horizon is also called the prediction length.</p>"}, "ForecastTypes": {"shape": "ForecastTypes", "documentation": "<p>The forecast types used during predictor training. Default value is <code>[\"0.1\",\"0.5\",\"0.9\"]</code> </p>"}, "PerformAutoML": {"shape": "Boolean", "documentation": "<p>Whether the predictor is set to perform AutoML.</p>"}, "AutoMLOverrideStrategy": {"shape": "AutoMLOverrideStrategy", "documentation": "<note> <p> The <code>LatencyOptimized</code> AutoML override strategy is only available in private beta. Contact Amazon Web Services Support or your account manager to learn more about access privileges. </p> </note> <p>The AutoML strategy used to train the predictor. Unless <code>LatencyOptimized</code> is specified, the AutoML strategy optimizes predictor accuracy.</p> <p>This parameter is only valid for predictors trained using AutoML.</p>"}, "PerformHPO": {"shape": "Boolean", "documentation": "<p>Whether the predictor is set to perform hyperparameter optimization (HPO).</p>"}, "TrainingParameters": {"shape": "TrainingParameters", "documentation": "<p>The default training parameters or overrides selected during model training. When running AutoML or choosing HPO with CNN-QR or DeepAR+, the optimized values for the chosen hyperparameters are returned. For more information, see <a>aws-forecast-choosing-recipes</a>.</p>"}, "EvaluationParameters": {"shape": "EvaluationParameters", "documentation": "<p>Used to override the default evaluation parameters of the specified algorithm. Amazon Forecast evaluates a predictor by splitting a dataset into training data and testing data. The evaluation parameters define how to perform the split and the number of iterations.</p>"}, "HPOConfig": {"shape": "HyperParameterTuningJobConfig", "documentation": "<p>The hyperparameter override values for the algorithm.</p>"}, "InputDataConfig": {"shape": "InputDataConfig", "documentation": "<p>Describes the dataset group that contains the data to use to train the predictor.</p>"}, "FeaturizationConfig": {"shape": "FeaturizationConfig", "documentation": "<p>The featurization configuration.</p>"}, "EncryptionConfig": {"shape": "EncryptionConfig", "documentation": "<p>An Key Management Service (KMS) key and the Identity and Access Management (IAM) role that Amazon Forecast can assume to access the key.</p>"}, "PredictorExecutionDetails": {"shape": "PredictorExecutionDetails", "documentation": "<p>Details on the the status and results of the backtests performed to evaluate the accuracy of the predictor. You specify the number of backtests to perform when you call the operation.</p>"}, "EstimatedTimeRemainingInMinutes": {"shape": "<PERSON>", "documentation": "<p>The estimated time remaining in minutes for the predictor training job to complete.</p>"}, "IsAutoPredictor": {"shape": "Boolean", "documentation": "<p>Whether the predictor was created with <a>CreateAutoPredictor</a>.</p>"}, "DatasetImportJobArns": {"shape": "ArnList", "documentation": "<p>An array of the ARNs of the dataset import jobs used to import training data for the predictor.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the predictor. States include:</p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> <li> <p> <code>CREATE_STOPPING</code>, <code>CREATE_STOPPED</code> </p> </li> </ul> <note> <p>The <code>Status</code> of the predictor must be <code>ACTIVE</code> before you can use the predictor to create a forecast.</p> </note>"}, "Message": {"shape": "Message", "documentation": "<p>If an error occurred, an informational message about the error.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the model training task was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The last time the resource was modified. The timestamp depends on the status of the job:</p> <ul> <li> <p> <code>CREATE_PENDING</code> - The <code>CreationTime</code>.</p> </li> <li> <p> <code>CREATE_IN_PROGRESS</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPING</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPED</code> - When the job stopped.</p> </li> <li> <p> <code>ACTIVE</code> or <code>CREATE_FAILED</code> - When the job finished or failed.</p> </li> </ul>"}, "OptimizationMetric": {"shape": "OptimizationMetric", "documentation": "<p>The accuracy metric used to optimize the predictor.</p>"}}}, "DescribeWhatIfAnalysisRequest": {"type": "structure", "required": ["WhatIfAnalysisArn"], "members": {"WhatIfAnalysisArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the what-if analysis that you are interested in.</p>"}}}, "DescribeWhatIfAnalysisResponse": {"type": "structure", "members": {"WhatIfAnalysisName": {"shape": "Name", "documentation": "<p>The name of the what-if analysis.</p>"}, "WhatIfAnalysisArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the what-if analysis.</p>"}, "ForecastArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the what-if forecast.</p>"}, "EstimatedTimeRemainingInMinutes": {"shape": "<PERSON>", "documentation": "<p>The approximate time remaining to complete the what-if analysis, in minutes.</p>"}, "Status": {"shape": "String", "documentation": "<p>The status of the what-if analysis. States include:</p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>CREATE_STOPPING</code>, <code>CREATE_STOPPED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> </ul> <note> <p>The <code>Status</code> of the what-if analysis must be <code>ACTIVE</code> before you can access the analysis.</p> </note>"}, "Message": {"shape": "ErrorMessage", "documentation": "<p>If an error occurred, an informational message about the error.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the what-if analysis was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The last time the resource was modified. The timestamp depends on the status of the job:</p> <ul> <li> <p> <code>CREATE_PENDING</code> - The <code>CreationTime</code>.</p> </li> <li> <p> <code>CREATE_IN_PROGRESS</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPING</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPED</code> - When the job stopped.</p> </li> <li> <p> <code>ACTIVE</code> or <code>CREATE_FAILED</code> - When the job finished or failed.</p> </li> </ul>"}, "TimeSeriesSelector": {"shape": "TimeSeriesSelector"}}}, "DescribeWhatIfForecastExportRequest": {"type": "structure", "required": ["WhatIfForecastExportArn"], "members": {"WhatIfForecastExportArn": {"shape": "LongArn", "documentation": "<p>The Amazon Resource Name (ARN) of the what-if forecast export that you are interested in.</p>"}}}, "DescribeWhatIfForecastExportResponse": {"type": "structure", "members": {"WhatIfForecastExportArn": {"shape": "LongArn", "documentation": "<p>The Amazon Resource Name (ARN) of the what-if forecast export.</p>"}, "WhatIfForecastExportName": {"shape": "Name", "documentation": "<p>The name of the what-if forecast export.</p>"}, "WhatIfForecastArns": {"shape": "LongArnList", "documentation": "<p>An array of Amazon Resource Names (ARNs) that represent all of the what-if forecasts exported in this resource.</p>"}, "Destination": {"shape": "DataDestination"}, "Message": {"shape": "Message", "documentation": "<p>If an error occurred, an informational message about the error.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the what-if forecast. States include:</p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>CREATE_STOPPING</code>, <code>CREATE_STOPPED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> </ul> <note> <p>The <code>Status</code> of the what-if forecast export must be <code>ACTIVE</code> before you can access the forecast export.</p> </note>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the what-if forecast export was created.</p>"}, "EstimatedTimeRemainingInMinutes": {"shape": "<PERSON>", "documentation": "<p>The approximate time remaining to complete the what-if forecast export, in minutes.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The last time the resource was modified. The timestamp depends on the status of the job:</p> <ul> <li> <p> <code>CREATE_PENDING</code> - The <code>CreationTime</code>.</p> </li> <li> <p> <code>CREATE_IN_PROGRESS</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPING</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPED</code> - When the job stopped.</p> </li> <li> <p> <code>ACTIVE</code> or <code>CREATE_FAILED</code> - When the job finished or failed.</p> </li> </ul>"}, "Format": {"shape": "Format", "documentation": "<p>The format of the exported data, CSV or PARQUET.</p>"}}}, "DescribeWhatIfForecastRequest": {"type": "structure", "required": ["WhatIfForecastArn"], "members": {"WhatIfForecastArn": {"shape": "LongArn", "documentation": "<p>The Amazon Resource Name (ARN) of the what-if forecast that you are interested in.</p>"}}}, "DescribeWhatIfForecastResponse": {"type": "structure", "members": {"WhatIfForecastName": {"shape": "Name", "documentation": "<p>The name of the what-if forecast.</p>"}, "WhatIfForecastArn": {"shape": "LongArn", "documentation": "<p>The Amazon Resource Name (ARN) of the what-if forecast.</p>"}, "WhatIfAnalysisArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the what-if analysis that contains this forecast.</p>"}, "EstimatedTimeRemainingInMinutes": {"shape": "<PERSON>", "documentation": "<p>The approximate time remaining to complete the what-if forecast, in minutes.</p>"}, "Status": {"shape": "String", "documentation": "<p>The status of the what-if forecast. States include:</p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>CREATE_STOPPING</code>, <code>CREATE_STOPPED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> </ul> <note> <p>The <code>Status</code> of the what-if forecast must be <code>ACTIVE</code> before you can access the forecast.</p> </note>"}, "Message": {"shape": "ErrorMessage", "documentation": "<p>If an error occurred, an informational message about the error.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the what-if forecast was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The last time the resource was modified. The timestamp depends on the status of the job:</p> <ul> <li> <p> <code>CREATE_PENDING</code> - The <code>CreationTime</code>.</p> </li> <li> <p> <code>CREATE_IN_PROGRESS</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPING</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPED</code> - When the job stopped.</p> </li> <li> <p> <code>ACTIVE</code> or <code>CREATE_FAILED</code> - When the job finished or failed.</p> </li> </ul>"}, "TimeSeriesTransformations": {"shape": "TimeSeriesTransformations", "documentation": "<p>An array of <code>Action</code> and <code>TimeSeriesConditions</code> elements that describe what transformations were applied to which time series.</p>"}, "TimeSeriesReplacementsDataSource": {"shape": "TimeSeriesReplacementsDataSource", "documentation": "<p>An array of <code>S3Config</code>, <code>Schema</code>, and <code>Format</code> elements that describe the replacement time series.</p>"}, "ForecastTypes": {"shape": "ForecastTypes", "documentation": "<p>The quantiles at which probabilistic forecasts are generated. You can specify up to five quantiles per what-if forecast in the <a>CreateWhatIfForecast</a> operation. If you didn't specify quantiles, the default values are <code>[\"0.1\", \"0.5\", \"0.9\"]</code>. </p>"}}}, "Detail": {"type": "string", "max": 256}, "Domain": {"type": "string", "enum": ["RETAIL", "CUSTOM", "INVENTORY_PLANNING", "EC2_CAPACITY", "WORK_FORCE", "WEB_TRAFFIC", "METRICS"]}, "Double": {"type": "double"}, "EncryptionConfig": {"type": "structure", "required": ["RoleArn", "KMSKeyArn"], "members": {"RoleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the IAM role that Amazon Forecast can assume to access the KMS key.</p> <p>Passing a role across Amazon Web Services accounts is not allowed. If you pass a role that isn't in your account, you get an <code>InvalidInputException</code> error.</p>"}, "KMSKeyArn": {"shape": "KMSKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key.</p>"}}, "documentation": "<p>An Key Management Service (KMS) key and an Identity and Access Management (IAM) role that Amazon Forecast can assume to access the key. You can specify this optional object in the <a>CreateDataset</a> and <a>CreatePredictor</a> requests.</p>"}, "ErrorMessage": {"type": "string"}, "ErrorMetric": {"type": "structure", "members": {"ForecastType": {"shape": "ForecastType", "documentation": "<p> The Forecast type used to compute WAPE, MAPE, MASE, and RMSE. </p>"}, "WAPE": {"shape": "Double", "documentation": "<p> The weighted absolute percentage error (WAPE). </p>"}, "RMSE": {"shape": "Double", "documentation": "<p> The root-mean-square error (RMSE). </p>"}, "MASE": {"shape": "Double", "documentation": "<p>The Mean Absolute Scaled Error (MASE)</p>"}, "MAPE": {"shape": "Double", "documentation": "<p>The Mean Absolute Percentage Error (MAPE)</p>"}}, "documentation": "<p> Provides detailed error metrics to evaluate the performance of a predictor. This object is part of the <a>Metrics</a> object. </p>"}, "ErrorMetrics": {"type": "list", "member": {"shape": "ErrorMetric"}}, "EvaluationParameters": {"type": "structure", "members": {"NumberOfBacktestWindows": {"shape": "Integer", "documentation": "<p>The number of times to split the input data. The default is 1. Valid values are 1 through 5.</p>"}, "BackTestWindowOffset": {"shape": "Integer", "documentation": "<p>The point from the end of the dataset where you want to split the data for model training and testing (evaluation). Specify the value as the number of data points. The default is the value of the forecast horizon. <code>BackTestWindowOffset</code> can be used to mimic a past virtual forecast start date. This value must be greater than or equal to the forecast horizon and less than half of the TARGET_TIME_SERIES dataset length.</p> <p> <code>ForecastHorizon</code> &lt;= <code>BackTestWindowOffset</code> &lt; 1/2 * TARGET_TIME_SERIES dataset length</p>"}}, "documentation": "<p>Parameters that define how to split a dataset into training data and testing data, and the number of iterations to perform. These parameters are specified in the predefined algorithms but you can override them in the <a>CreatePredictor</a> request.</p>"}, "EvaluationResult": {"type": "structure", "members": {"AlgorithmArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the algorithm that was evaluated.</p>"}, "TestWindows": {"shape": "TestWindows", "documentation": "<p>The array of test windows used for evaluating the algorithm. The <code>NumberOfBacktestWindows</code> from the <a>EvaluationParameters</a> object determines the number of windows in the array.</p>"}}, "documentation": "<p>The results of evaluating an algorithm. Returned as part of the <a>GetAccuracyMetrics</a> response.</p>"}, "EvaluationState": {"type": "string", "max": 256}, "EvaluationType": {"type": "string", "enum": ["SUMMARY", "COMPUTED"]}, "Explainabilities": {"type": "list", "member": {"shape": "ExplainabilitySummary"}}, "ExplainabilityConfig": {"type": "structure", "required": ["TimeSeriesGranularity", "TimePointGranularity"], "members": {"TimeSeriesGranularity": {"shape": "TimeSeriesGranularity", "documentation": "<p>To create an Explainability for all time series in your datasets, use <code>ALL</code>. To create an Explainability for specific time series in your datasets, use <code>SPECIFIC</code>.</p> <p>Specify time series by uploading a CSV or Parquet file to an Amazon S3 bucket and set the location within the <a>DataDestination</a> data type.</p>"}, "TimePointGranularity": {"shape": "TimePointGranularity", "documentation": "<p>To create an Explainability for all time points in your forecast horizon, use <code>ALL</code>. To create an Explainability for specific time points in your forecast horizon, use <code>SPECIFIC</code>.</p> <p>Specify time points with the <code>StartDateTime</code> and <code>EndDateTime</code> parameters within the <a>CreateExplainability</a> operation.</p>"}}, "documentation": "<p>The ExplainabilityConfig data type defines the number of time series and time points included in <a>CreateExplainability</a>.</p> <p>If you provide a predictor ARN for <code>ResourceArn</code>, you must set both <code>TimePointGranularity</code> and <code>TimeSeriesGranularity</code> to “ALL”. When creating Predictor Explainability, Amazon Forecast considers all time series and time points.</p> <p>If you provide a forecast ARN for <code>ResourceArn</code>, you can set <code>TimePointGranularity</code> and <code>TimeSeriesGranularity</code> to either “ALL” or “Specific”.</p>"}, "ExplainabilityExportSummary": {"type": "structure", "members": {"ExplainabilityExportArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Explainability export.</p>"}, "ExplainabilityExportName": {"shape": "Name", "documentation": "<p>The name of the Explainability export</p>"}, "Destination": {"shape": "DataDestination"}, "Status": {"shape": "Status", "documentation": "<p>The status of the Explainability export. States include: </p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>CREATE_STOPPING</code>, <code>CREATE_STOPPED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> </ul>"}, "Message": {"shape": "ErrorMessage", "documentation": "<p>Information about any errors that may have occurred during the Explainability export.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the Explainability was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The last time the resource was modified. The timestamp depends on the status of the job:</p> <ul> <li> <p> <code>CREATE_PENDING</code> - The <code>CreationTime</code>.</p> </li> <li> <p> <code>CREATE_IN_PROGRESS</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPING</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPED</code> - When the job stopped.</p> </li> <li> <p> <code>ACTIVE</code> or <code>CREATE_FAILED</code> - When the job finished or failed.</p> </li> </ul>"}}, "documentation": "<p>Provides a summary of the Explainability export properties used in the <a>ListExplainabilityExports</a> operation. To get a complete set of properties, call the <a>DescribeExplainabilityExport</a> operation, and provide the <code>ExplainabilityExportArn</code>.</p>"}, "ExplainabilityExports": {"type": "list", "member": {"shape": "ExplainabilityExportSummary"}}, "ExplainabilityInfo": {"type": "structure", "members": {"ExplainabilityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Explainability.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the Explainability. States include: </p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>CREATE_STOPPING</code>, <code>CREATE_STOPPED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> </ul>"}}, "documentation": "<p>Provides information about the Explainability resource.</p>"}, "ExplainabilitySummary": {"type": "structure", "members": {"ExplainabilityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Explainability.</p>"}, "ExplainabilityName": {"shape": "Name", "documentation": "<p>The name of the Explainability.</p>"}, "ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Predictor or Forecast used to create the Explainability.</p>"}, "ExplainabilityConfig": {"shape": "ExplainabilityConfig", "documentation": "<p>The configuration settings that define the granularity of time series and time points for the Explainability.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the Explainability. States include: </p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>CREATE_STOPPING</code>, <code>CREATE_STOPPED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> </ul>"}, "Message": {"shape": "Message", "documentation": "<p>Information about any errors that may have occurred during the Explainability creation process.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the Explainability was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The last time the resource was modified. The timestamp depends on the status of the job:</p> <ul> <li> <p> <code>CREATE_PENDING</code> - The <code>CreationTime</code>.</p> </li> <li> <p> <code>CREATE_IN_PROGRESS</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPING</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPED</code> - When the job stopped.</p> </li> <li> <p> <code>ACTIVE</code> or <code>CREATE_FAILED</code> - When the job finished or failed.</p> </li> </ul>"}}, "documentation": "<p>Provides a summary of the Explainability properties used in the <a>ListExplainabilities</a> operation. To get a complete set of properties, call the <a>DescribeExplainability</a> operation, and provide the listed <code>ExplainabilityArn</code>.</p>"}, "Featurization": {"type": "structure", "required": ["AttributeName"], "members": {"AttributeName": {"shape": "Name", "documentation": "<p>The name of the schema attribute that specifies the data field to be featurized. Amazon Forecast supports the target field of the <code>TARGET_TIME_SERIES</code> and the <code>RELATED_TIME_SERIES</code> datasets. For example, for the <code>RETAIL</code> domain, the target is <code>demand</code>, and for the <code>CUSTOM</code> domain, the target is <code>target_value</code>. For more information, see <a>howitworks-missing-values</a>.</p>"}, "FeaturizationPipeline": {"shape": "FeaturizationPipeline", "documentation": "<p>An array of one <code>FeaturizationMethod</code> object that specifies the feature transformation method.</p>"}}, "documentation": "<note> <p>This object belongs to the <a>CreatePredictor</a> operation. If you created your predictor with <a>CreateAutoPredictor</a>, see <a>AttributeConfig</a>.</p> </note> <p>Provides featurization (transformation) information for a dataset field. This object is part of the <a>FeaturizationConfig</a> object.</p> <p>For example:</p> <p> <code>{</code> </p> <p> <code>\"AttributeName\": \"demand\",</code> </p> <p> <code>FeaturizationPipeline [ {</code> </p> <p> <code>\"FeaturizationMethodName\": \"filling\",</code> </p> <p> <code>\"FeaturizationMethodParameters\": {\"aggregation\": \"avg\", \"backfill\": \"nan\"}</code> </p> <p> <code>} ]</code> </p> <p> <code>}</code> </p>"}, "FeaturizationConfig": {"type": "structure", "required": ["ForecastFrequency"], "members": {"ForecastFrequency": {"shape": "Frequency", "documentation": "<p>The frequency of predictions in a forecast.</p> <p>Valid intervals are an integer followed by Y (Year), M (Month), W (Week), D (Day), H (Hour), and min (Minute). For example, \"1D\" indicates every day and \"15min\" indicates every 15 minutes. You cannot specify a value that would overlap with the next larger frequency. That means, for example, you cannot specify a frequency of 60 minutes, because that is equivalent to 1 hour. The valid values for each frequency are the following:</p> <ul> <li> <p>Minute - 1-59</p> </li> <li> <p>Hour - 1-23</p> </li> <li> <p>Day - 1-6</p> </li> <li> <p>Week - 1-4</p> </li> <li> <p>Month - 1-11</p> </li> <li> <p>Year - 1</p> </li> </ul> <p>Thus, if you want every other week forecasts, specify \"2W\". Or, if you want quarterly forecasts, you specify \"3M\".</p> <p>The frequency must be greater than or equal to the TARGET_TIME_SERIES dataset frequency.</p> <p>When a RELATED_TIME_SERIES dataset is provided, the frequency must be equal to the TARGET_TIME_SERIES dataset frequency.</p>"}, "ForecastDimensions": {"shape": "ForecastDimensions", "documentation": "<p>An array of dimension (field) names that specify how to group the generated forecast.</p> <p>For example, suppose that you are generating a forecast for item sales across all of your stores, and your dataset contains a <code>store_id</code> field. If you want the sales forecast for each item by store, you would specify <code>store_id</code> as the dimension.</p> <p>All forecast dimensions specified in the <code>TARGET_TIME_SERIES</code> dataset don't need to be specified in the <code>CreatePredictor</code> request. All forecast dimensions specified in the <code>RELATED_TIME_SERIES</code> dataset must be specified in the <code>CreatePredictor</code> request.</p>"}, "Featurizations": {"shape": "Featurizations", "documentation": "<p>An array of featurization (transformation) information for the fields of a dataset.</p>"}}, "documentation": "<note> <p>This object belongs to the <a>CreatePredictor</a> operation. If you created your predictor with <a>CreateAutoPredictor</a>, see <a>AttributeConfig</a>.</p> </note> <p>In a <a>CreatePredictor</a> operation, the specified algorithm trains a model using the specified dataset group. You can optionally tell the operation to modify data fields prior to training a model. These modifications are referred to as <i>featurization</i>.</p> <p>You define featurization using the <code>FeaturizationConfig</code> object. You specify an array of transformations, one for each field that you want to featurize. You then include the <code>FeaturizationConfig</code> object in your <code>CreatePredictor</code> request. Amazon Forecast applies the featurization to the <code>TARGET_TIME_SERIES</code> and <code>RELATED_TIME_SERIES</code> datasets before model training.</p> <p>You can create multiple featurization configurations. For example, you might call the <code>CreatePredictor</code> operation twice by specifying different featurization configurations.</p>"}, "FeaturizationMethod": {"type": "structure", "required": ["FeaturizationMethodName"], "members": {"FeaturizationMethodName": {"shape": "FeaturizationMethodName", "documentation": "<p>The name of the method. The \"filling\" method is the only supported method.</p>"}, "FeaturizationMethodParameters": {"shape": "FeaturizationMethodParameters", "documentation": "<p>The method parameters (key-value pairs), which are a map of override parameters. Specify these parameters to override the default values. Related Time Series attributes do not accept aggregation parameters.</p> <p>The following list shows the parameters and their valid values for the \"filling\" featurization method for a <b>Target Time Series</b> dataset. Bold signifies the default value.</p> <ul> <li> <p> <code>aggregation</code>: <b>sum</b>, <code>avg</code>, <code>first</code>, <code>min</code>, <code>max</code> </p> </li> <li> <p> <code>frontfill</code>: <b>none</b> </p> </li> <li> <p> <code>middlefill</code>: <b>zero</b>, <code>nan</code> (not a number), <code>value</code>, <code>median</code>, <code>mean</code>, <code>min</code>, <code>max</code> </p> </li> <li> <p> <code>backfill</code>: <b>zero</b>, <code>nan</code>, <code>value</code>, <code>median</code>, <code>mean</code>, <code>min</code>, <code>max</code> </p> </li> </ul> <p>The following list shows the parameters and their valid values for a <b>Related Time Series</b> featurization method (there are no defaults):</p> <ul> <li> <p> <code>middlefill</code>: <code>zero</code>, <code>value</code>, <code>median</code>, <code>mean</code>, <code>min</code>, <code>max</code> </p> </li> <li> <p> <code>backfill</code>: <code>zero</code>, <code>value</code>, <code>median</code>, <code>mean</code>, <code>min</code>, <code>max</code> </p> </li> <li> <p> <code>futurefill</code>: <code>zero</code>, <code>value</code>, <code>median</code>, <code>mean</code>, <code>min</code>, <code>max</code> </p> </li> </ul> <p>To set a filling method to a specific value, set the fill parameter to <code>value</code> and define the value in a corresponding <code>_value</code> parameter. For example, to set backfilling to a value of 2, include the following: <code>\"backfill\": \"value\"</code> and <code>\"backfill_value\":\"2\"</code>. </p>"}}, "documentation": "<p>Provides information about the method that featurizes (transforms) a dataset field. The method is part of the <code>FeaturizationPipeline</code> of the <a>Featurization</a> object. </p> <p>The following is an example of how you specify a <code>FeaturizationMethod</code> object.</p> <p> <code>{</code> </p> <p> <code>\"FeaturizationMethodName\": \"filling\",</code> </p> <p> <code>\"FeaturizationMethodParameters\": {\"aggregation\": \"sum\", \"middlefill\": \"zero\", \"backfill\": \"zero\"}</code> </p> <p> <code>}</code> </p>"}, "FeaturizationMethodName": {"type": "string", "enum": ["filling"]}, "FeaturizationMethodParameters": {"type": "map", "key": {"shape": "Parameter<PERSON>ey"}, "value": {"shape": "ParameterValue"}, "max": 20, "min": 1}, "FeaturizationPipeline": {"type": "list", "member": {"shape": "FeaturizationMethod"}, "max": 1, "min": 1}, "Featurizations": {"type": "list", "member": {"shape": "Featurization"}, "max": 50, "min": 1}, "FieldStatistics": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "Statistics"}}, "Filter": {"type": "structure", "required": ["Key", "Value", "Condition"], "members": {"Key": {"shape": "String", "documentation": "<p>The name of the parameter to filter on.</p>"}, "Value": {"shape": "<PERSON><PERSON>", "documentation": "<p>The value to match.</p>"}, "Condition": {"shape": "FilterConditionString", "documentation": "<p>The condition to apply. To include the objects that match the statement, specify <code>IS</code>. To exclude matching objects, specify <code>IS_NOT</code>.</p>"}}, "documentation": "<p>Describes a filter for choosing a subset of objects. Each filter consists of a condition and a match statement. The condition is either <code>IS</code> or <code>IS_NOT</code>, which specifies whether to include or exclude the objects that match the statement, respectively. The match statement consists of a key and a value.</p>"}, "FilterConditionString": {"type": "string", "enum": ["IS", "IS_NOT"]}, "Filters": {"type": "list", "member": {"shape": "Filter"}}, "ForecastDimensions": {"type": "list", "member": {"shape": "Name"}, "max": 10, "min": 1}, "ForecastExportJobSummary": {"type": "structure", "members": {"ForecastExportJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the forecast export job.</p>"}, "ForecastExportJobName": {"shape": "Name", "documentation": "<p>The name of the forecast export job.</p>"}, "Destination": {"shape": "DataDestination", "documentation": "<p>The path to the Amazon Simple Storage Service (Amazon S3) bucket where the forecast is exported.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the forecast export job. States include:</p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>CREATE_STOPPING</code>, <code>CREATE_STOPPED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> </ul> <note> <p>The <code>Status</code> of the forecast export job must be <code>ACTIVE</code> before you can access the forecast in your S3 bucket.</p> </note>"}, "Message": {"shape": "ErrorMessage", "documentation": "<p>If an error occurred, an informational message about the error.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the forecast export job was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The last time the resource was modified. The timestamp depends on the status of the job:</p> <ul> <li> <p> <code>CREATE_PENDING</code> - The <code>CreationTime</code>.</p> </li> <li> <p> <code>CREATE_IN_PROGRESS</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPING</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPED</code> - When the job stopped.</p> </li> <li> <p> <code>ACTIVE</code> or <code>CREATE_FAILED</code> - When the job finished or failed.</p> </li> </ul>"}}, "documentation": "<p>Provides a summary of the forecast export job properties used in the <a>ListForecastExportJobs</a> operation. To get the complete set of properties, call the <a>DescribeForecastExportJob</a> operation, and provide the listed <code>ForecastExportJobArn</code>.</p>"}, "ForecastExportJobs": {"type": "list", "member": {"shape": "ForecastExportJobSummary"}}, "ForecastSummary": {"type": "structure", "members": {"ForecastArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the forecast.</p>"}, "ForecastName": {"shape": "Name", "documentation": "<p>The name of the forecast.</p>"}, "PredictorArn": {"shape": "String", "documentation": "<p>The ARN of the predictor used to generate the forecast.</p>"}, "CreatedUsingAutoPredictor": {"shape": "Boolean", "documentation": "<p>Whether the Forecast was created from an AutoPredictor.</p>"}, "DatasetGroupArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset group that provided the data used to train the predictor.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the forecast. States include:</p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>CREATE_STOPPING</code>, <code>CREATE_STOPPED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> </ul> <note> <p>The <code>Status</code> of the forecast must be <code>ACTIVE</code> before you can query or export the forecast.</p> </note>"}, "Message": {"shape": "ErrorMessage", "documentation": "<p>If an error occurred, an informational message about the error.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the forecast creation task was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The last time the resource was modified. The timestamp depends on the status of the job:</p> <ul> <li> <p> <code>CREATE_PENDING</code> - The <code>CreationTime</code>.</p> </li> <li> <p> <code>CREATE_IN_PROGRESS</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPING</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPED</code> - When the job stopped.</p> </li> <li> <p> <code>ACTIVE</code> or <code>CREATE_FAILED</code> - When the job finished or failed.</p> </li> </ul>"}}, "documentation": "<p>Provides a summary of the forecast properties used in the <a>ListForecasts</a> operation. To get the complete set of properties, call the <a>DescribeForecast</a> operation, and provide the <code>ForecastArn</code> that is listed in the summary.</p>"}, "ForecastType": {"type": "string", "max": 4, "min": 2, "pattern": "(^0?\\.\\d\\d?$|^mean$)"}, "ForecastTypes": {"type": "list", "member": {"shape": "ForecastType"}, "max": 20, "min": 1}, "Forecasts": {"type": "list", "member": {"shape": "ForecastSummary"}}, "Format": {"type": "string", "max": 7, "pattern": "^CSV|PARQUET$"}, "Frequency": {"type": "string", "max": 5, "min": 1, "pattern": "^Y|M|W|D|H|30min|15min|10min|5min|1min$"}, "GeolocationFormat": {"type": "string", "max": 256, "pattern": "^[a-zA-Z0-9_]+$"}, "GetAccuracyMetricsRequest": {"type": "structure", "required": ["PredictorArn"], "members": {"PredictorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the predictor to get metrics for.</p>"}}}, "GetAccuracyMetricsResponse": {"type": "structure", "members": {"PredictorEvaluationResults": {"shape": "PredictorEvaluationResults", "documentation": "<p>An array of results from evaluating the predictor.</p>"}, "IsAutoPredictor": {"shape": "Boolean", "documentation": "<p>Whether the predictor was created with <a>CreateAutoPredictor</a>.</p>"}, "AutoMLOverrideStrategy": {"shape": "AutoMLOverrideStrategy", "documentation": "<note> <p> The <code>LatencyOptimized</code> AutoML override strategy is only available in private beta. Contact Amazon Web Services Support or your account manager to learn more about access privileges. </p> </note> <p>The AutoML strategy used to train the predictor. Unless <code>LatencyOptimized</code> is specified, the AutoML strategy optimizes predictor accuracy.</p> <p>This parameter is only valid for predictors trained using AutoML.</p>"}, "OptimizationMetric": {"shape": "OptimizationMetric", "documentation": "<p>The accuracy metric used to optimize the predictor.</p>"}}}, "Hour": {"type": "integer", "max": 23, "min": 0}, "HyperParameterTuningJobConfig": {"type": "structure", "members": {"ParameterRanges": {"shape": "ParameterRanges", "documentation": "<p>Specifies the ranges of valid values for the hyperparameters.</p>"}}, "documentation": "<p>Configuration information for a hyperparameter tuning job. You specify this object in the <a>CreatePredictor</a> request.</p> <p>A <i>hyperparameter</i> is a parameter that governs the model training process. You set hyperparameters before training starts, unlike model parameters, which are determined during training. The values of the hyperparameters effect which values are chosen for the model parameters.</p> <p>In a <i>hyperparameter tuning job</i>, Amazon Forecast chooses the set of hyperparameter values that optimize a specified metric. Forecast accomplishes this by running many training jobs over a range of hyperparameter values. The optimum set of values depends on the algorithm, the training data, and the specified metric objective.</p>"}, "ImportMode": {"type": "string", "enum": ["FULL", "INCREMENTAL"]}, "InputDataConfig": {"type": "structure", "required": ["DatasetGroupArn"], "members": {"DatasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset group.</p>"}, "SupplementaryFeatures": {"shape": "SupplementaryFeatures", "documentation": "<p>An array of supplementary features. The only supported feature is a holiday calendar.</p>"}}, "documentation": "<note> <p>This object belongs to the <a>CreatePredictor</a> operation. If you created your predictor with <a>CreateAutoPredictor</a>, see <a>DataConfig</a>.</p> </note> <p>The data used to train a predictor. The data includes a dataset group and any supplementary features. You specify this object in the <a>CreatePredictor</a> request.</p>"}, "Integer": {"type": "integer"}, "IntegerParameterRange": {"type": "structure", "required": ["Name", "MaxValue", "MinValue"], "members": {"Name": {"shape": "Name", "documentation": "<p>The name of the hyperparameter to tune.</p>"}, "MaxValue": {"shape": "Integer", "documentation": "<p>The maximum tunable value of the hyperparameter.</p>"}, "MinValue": {"shape": "Integer", "documentation": "<p>The minimum tunable value of the hyperparameter.</p>"}, "ScalingType": {"shape": "ScalingType", "documentation": "<p>The scale that hyperparameter tuning uses to search the hyperparameter range. Valid values:</p> <dl> <dt>Auto</dt> <dd> <p>Amazon Forecast hyperparameter tuning chooses the best scale for the hyperparameter.</p> </dd> <dt>Linear</dt> <dd> <p>Hyperparameter tuning searches the values in the hyperparameter range by using a linear scale.</p> </dd> <dt>Logarithmic</dt> <dd> <p>Hyperparameter tuning searches the values in the hyperparameter range by using a logarithmic scale.</p> <p>Logarithmic scaling works only for ranges that have values greater than 0.</p> </dd> <dt>ReverseLogarithmic</dt> <dd> <p>Not supported for <code>IntegerParameterRange</code>.</p> <p>Reverse logarithmic scaling works only for ranges that are entirely within the range 0 &lt;= x &lt; 1.0.</p> </dd> </dl> <p>For information about choosing a hyperparameter scale, see <a href=\"http://docs.aws.amazon.com/sagemaker/latest/dg/automatic-model-tuning-define-ranges.html#scaling-type\">Hyperparameter Scaling</a>. One of the following values:</p>"}}, "documentation": "<p>Specifies an integer hyperparameter and it's range of tunable values. This object is part of the <a>ParameterRanges</a> object.</p>"}, "IntegerParameterRanges": {"type": "list", "member": {"shape": "IntegerParameterRange"}, "max": 20, "min": 1}, "InvalidInputException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>We can't process the request because it includes an invalid value or a value that exceeds the valid range.</p>", "exception": true}, "InvalidNextTokenException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The token is not valid. Tokens expire after 24 hours.</p>", "exception": true}, "KMSKeyArn": {"type": "string", "max": 256, "pattern": "arn:aws:kms:.*:key/.*"}, "LimitExceededException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The limit on the number of resources per account has been exceeded.</p>", "exception": true}, "ListDatasetGroupsRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>If the result of the previous request was truncated, the response includes a <code>NextToken</code>. To retrieve the next set of results, use the token in the next request. Tokens expire after 24 hours.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of items to return in the response.</p>"}}}, "ListDatasetGroupsResponse": {"type": "structure", "members": {"DatasetGroups": {"shape": "DatasetGroups", "documentation": "<p>An array of objects that summarize each dataset group's properties.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Forecast returns this token. To retrieve the next set of results, use the token in the next request.</p>"}}}, "ListDatasetImportJobsRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>If the result of the previous request was truncated, the response includes a <code>NextToken</code>. To retrieve the next set of results, use the token in the next request. Tokens expire after 24 hours.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of items to return in the response.</p>"}, "Filters": {"shape": "Filters", "documentation": "<p>An array of filters. For each filter, you provide a condition and a match statement. The condition is either <code>IS</code> or <code>IS_NOT</code>, which specifies whether to include or exclude the datasets that match the statement from the list, respectively. The match statement consists of a key and a value.</p> <p> <b>Filter properties</b> </p> <ul> <li> <p> <code>Condition</code> - The condition to apply. Valid values are <code>IS</code> and <code>IS_NOT</code>. To include the datasets that match the statement, specify <code>IS</code>. To exclude matching datasets, specify <code>IS_NOT</code>.</p> </li> <li> <p> <code>Key</code> - The name of the parameter to filter on. Valid values are <code>DatasetArn</code> and <code>Status</code>.</p> </li> <li> <p> <code>Value</code> - The value to match.</p> </li> </ul> <p>For example, to list all dataset import jobs whose status is ACTIVE, you specify the following filter:</p> <p> <code>\"Filters\": [ { \"Condition\": \"IS\", \"Key\": \"Status\", \"Value\": \"ACTIVE\" } ]</code> </p>"}}}, "ListDatasetImportJobsResponse": {"type": "structure", "members": {"DatasetImportJobs": {"shape": "DatasetImportJobs", "documentation": "<p>An array of objects that summarize each dataset import job's properties.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Forecast returns this token. To retrieve the next set of results, use the token in the next request.</p>"}}}, "ListDatasetsRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>If the result of the previous request was truncated, the response includes a <code>NextToken</code>. To retrieve the next set of results, use the token in the next request. Tokens expire after 24 hours.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of items to return in the response.</p>"}}}, "ListDatasetsResponse": {"type": "structure", "members": {"Datasets": {"shape": "Datasets", "documentation": "<p>An array of objects that summarize each dataset's properties.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Forecast returns this token. To retrieve the next set of results, use the token in the next request.</p>"}}}, "ListExplainabilitiesRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>If the result of the previous request was truncated, the response includes a NextToken. To retrieve the next set of results, use the token in the next request. Tokens expire after 24 hours.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of items returned in the response.</p>"}, "Filters": {"shape": "Filters", "documentation": "<p>An array of filters. For each filter, provide a condition and a match statement. The condition is either <code>IS</code> or <code>IS_NOT</code>, which specifies whether to include or exclude the resources that match the statement from the list. The match statement consists of a key and a value.</p> <p> <b>Filter properties</b> </p> <ul> <li> <p> <code>Condition</code> - The condition to apply. Valid values are <code>IS</code> and <code>IS_NOT</code>.</p> </li> <li> <p> <code>Key</code> - The name of the parameter to filter on. Valid values are <code>ResourceArn</code> and <code>Status</code>.</p> </li> <li> <p> <code>Value</code> - The value to match.</p> </li> </ul>"}}}, "ListExplainabilitiesResponse": {"type": "structure", "members": {"Explainabilities": {"shape": "Explainabilities", "documentation": "<p>An array of objects that summarize the properties of each Explainability resource.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Returns this token if the response is truncated. To retrieve the next set of results, use the token in the next request.</p>"}}}, "ListExplainabilityExportsRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>If the result of the previous request was truncated, the response includes a NextToken. To retrieve the next set of results, use the token in the next request. Tokens expire after 24 hours.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of items to return in the response.</p>"}, "Filters": {"shape": "Filters", "documentation": "<p>An array of filters. For each filter, provide a condition and a match statement. The condition is either <code>IS</code> or <code>IS_NOT</code>, which specifies whether to include or exclude resources that match the statement from the list. The match statement consists of a key and a value.</p> <p> <b>Filter properties</b> </p> <ul> <li> <p> <code>Condition</code> - The condition to apply. Valid values are <code>IS</code> and <code>IS_NOT</code>.</p> </li> <li> <p> <code>Key</code> - The name of the parameter to filter on. Valid values are <code>ResourceArn</code> and <code>Status</code>.</p> </li> <li> <p> <code>Value</code> - The value to match.</p> </li> </ul>"}}}, "ListExplainabilityExportsResponse": {"type": "structure", "members": {"ExplainabilityExports": {"shape": "ExplainabilityExports", "documentation": "<p>An array of objects that summarize the properties of each Explainability export.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Returns this token if the response is truncated. To retrieve the next set of results, use the token in the next request.</p>"}}}, "ListForecastExportJobsRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>If the result of the previous request was truncated, the response includes a <code>NextToken</code>. To retrieve the next set of results, use the token in the next request. Tokens expire after 24 hours.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of items to return in the response.</p>"}, "Filters": {"shape": "Filters", "documentation": "<p>An array of filters. For each filter, you provide a condition and a match statement. The condition is either <code>IS</code> or <code>IS_NOT</code>, which specifies whether to include or exclude the forecast export jobs that match the statement from the list, respectively. The match statement consists of a key and a value.</p> <p> <b>Filter properties</b> </p> <ul> <li> <p> <code>Condition</code> - The condition to apply. Valid values are <code>IS</code> and <code>IS_NOT</code>. To include the forecast export jobs that match the statement, specify <code>IS</code>. To exclude matching forecast export jobs, specify <code>IS_NOT</code>.</p> </li> <li> <p> <code>Key</code> - The name of the parameter to filter on. Valid values are <code>ForecastArn</code> and <code>Status</code>.</p> </li> <li> <p> <code>Value</code> - The value to match.</p> </li> </ul> <p>For example, to list all jobs that export a forecast named <i>electricityforecast</i>, specify the following filter:</p> <p> <code>\"Filters\": [ { \"Condition\": \"IS\", \"Key\": \"ForecastArn\", \"Value\": \"arn:aws:forecast:us-west-2:&lt;acct-id&gt;:forecast/electricityforecast\" } ]</code> </p>"}}}, "ListForecastExportJobsResponse": {"type": "structure", "members": {"ForecastExportJobs": {"shape": "ForecastExportJobs", "documentation": "<p>An array of objects that summarize each export job's properties.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Forecast returns this token. To retrieve the next set of results, use the token in the next request.</p>"}}}, "ListForecastsRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>If the result of the previous request was truncated, the response includes a <code>NextToken</code>. To retrieve the next set of results, use the token in the next request. Tokens expire after 24 hours.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of items to return in the response.</p>"}, "Filters": {"shape": "Filters", "documentation": "<p>An array of filters. For each filter, you provide a condition and a match statement. The condition is either <code>IS</code> or <code>IS_NOT</code>, which specifies whether to include or exclude the forecasts that match the statement from the list, respectively. The match statement consists of a key and a value.</p> <p> <b>Filter properties</b> </p> <ul> <li> <p> <code>Condition</code> - The condition to apply. Valid values are <code>IS</code> and <code>IS_NOT</code>. To include the forecasts that match the statement, specify <code>IS</code>. To exclude matching forecasts, specify <code>IS_NOT</code>.</p> </li> <li> <p> <code>Key</code> - The name of the parameter to filter on. Valid values are <code>DatasetGroupArn</code>, <code>PredictorArn</code>, and <code>Status</code>.</p> </li> <li> <p> <code>Value</code> - The value to match.</p> </li> </ul> <p>For example, to list all forecasts whose status is not ACTIVE, you would specify:</p> <p> <code>\"Filters\": [ { \"Condition\": \"IS_NOT\", \"Key\": \"Status\", \"Value\": \"ACTIVE\" } ]</code> </p>"}}}, "ListForecastsResponse": {"type": "structure", "members": {"Forecasts": {"shape": "Forecasts", "documentation": "<p>An array of objects that summarize each forecast's properties.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Forecast returns this token. To retrieve the next set of results, use the token in the next request.</p>"}}}, "ListMonitorEvaluationsRequest": {"type": "structure", "required": ["MonitorArn"], "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>If the result of the previous request was truncated, the response includes a <code>NextToken</code>. To retrieve the next set of results, use the token in the next request. Tokens expire after 24 hours.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of monitoring results to return.</p>"}, "MonitorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the monitor resource to get results from.</p>"}, "Filters": {"shape": "Filters", "documentation": "<p>An array of filters. For each filter, provide a condition and a match statement. The condition is either <code>IS</code> or <code>IS_NOT</code>, which specifies whether to include or exclude the resources that match the statement from the list. The match statement consists of a key and a value.</p> <p> <b>Filter properties</b> </p> <ul> <li> <p> <code>Condition</code> - The condition to apply. Valid values are <code>IS</code> and <code>IS_NOT</code>.</p> </li> <li> <p> <code>Key</code> - The name of the parameter to filter on. The only valid value is <code>EvaluationState</code>.</p> </li> <li> <p> <code>Value</code> - The value to match. Valid values are only <code>SUCCESS</code> or <code>FAILURE</code>.</p> </li> </ul> <p>For example, to list only successful monitor evaluations, you would specify:</p> <p> <code>\"Filters\": [ { \"Condition\": \"IS\", \"Key\": \"EvaluationState\", \"Value\": \"SUCCESS\" } ]</code> </p>"}}}, "ListMonitorEvaluationsResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Forecast returns this token. To retrieve the next set of results, use the token in the next request. Tokens expire after 24 hours.</p>"}, "PredictorMonitorEvaluations": {"shape": "PredictorMonitorEvaluations", "documentation": "<p>The monitoring results and predictor events collected by the monitor resource during different windows of time.</p> <p>For information about monitoring see <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/predictor-monitoring-results.html\">Viewing Monitoring Results</a>. For more information about retrieving monitoring results see <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/predictor-monitoring-results.html\">Viewing Monitoring Results</a>.</p>"}}}, "ListMonitorsRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>If the result of the previous request was truncated, the response includes a <code>NextToken</code>. To retrieve the next set of results, use the token in the next request. Tokens expire after 24 hours.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of monitors to include in the response.</p>"}, "Filters": {"shape": "Filters", "documentation": "<p>An array of filters. For each filter, provide a condition and a match statement. The condition is either <code>IS</code> or <code>IS_NOT</code>, which specifies whether to include or exclude the resources that match the statement from the list. The match statement consists of a key and a value.</p> <p> <b>Filter properties</b> </p> <ul> <li> <p> <code>Condition</code> - The condition to apply. Valid values are <code>IS</code> and <code>IS_NOT</code>.</p> </li> <li> <p> <code>Key</code> - The name of the parameter to filter on. The only valid value is <code>Status</code>.</p> </li> <li> <p> <code>Value</code> - The value to match.</p> </li> </ul> <p>For example, to list all monitors who's status is ACTIVE, you would specify:</p> <p> <code>\"Filters\": [ { \"Condition\": \"IS\", \"Key\": \"Status\", \"Value\": \"ACTIVE\" } ]</code> </p>"}}}, "ListMonitorsResponse": {"type": "structure", "members": {"Monitors": {"shape": "Monitors", "documentation": "<p>An array of objects that summarize each monitor's properties.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Forecast returns this token. To retrieve the next set of results, use the token in the next request.</p>"}}}, "ListPredictorBacktestExportJobsRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>If the result of the previous request was truncated, the response includes a NextToken. To retrieve the next set of results, use the token in the next request. Tokens expire after 24 hours.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of items to return in the response.</p>"}, "Filters": {"shape": "Filters", "documentation": "<p>An array of filters. For each filter, provide a condition and a match statement. The condition is either <code>IS</code> or <code>IS_NOT</code>, which specifies whether to include or exclude the predictor backtest export jobs that match the statement from the list. The match statement consists of a key and a value.</p> <p> <b>Filter properties</b> </p> <ul> <li> <p> <code>Condition</code> - The condition to apply. Valid values are <code>IS</code> and <code>IS_NOT</code>. To include the predictor backtest export jobs that match the statement, specify <code>IS</code>. To exclude matching predictor backtest export jobs, specify <code>IS_NOT</code>.</p> </li> <li> <p> <code>Key</code> - The name of the parameter to filter on. Valid values are <code>PredictorArn</code> and <code>Status</code>.</p> </li> <li> <p> <code>Value</code> - The value to match.</p> </li> </ul>"}}}, "ListPredictorBacktestExportJobsResponse": {"type": "structure", "members": {"PredictorBacktestExportJobs": {"shape": "PredictorBacktestExportJobs", "documentation": "<p>An array of objects that summarize the properties of each predictor backtest export job.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Returns this token if the response is truncated. To retrieve the next set of results, use the token in the next request.</p>"}}}, "ListPredictorsRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>If the result of the previous request was truncated, the response includes a <code>NextToken</code>. To retrieve the next set of results, use the token in the next request. Tokens expire after 24 hours.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of items to return in the response.</p>"}, "Filters": {"shape": "Filters", "documentation": "<p>An array of filters. For each filter, you provide a condition and a match statement. The condition is either <code>IS</code> or <code>IS_NOT</code>, which specifies whether to include or exclude the predictors that match the statement from the list, respectively. The match statement consists of a key and a value.</p> <p> <b>Filter properties</b> </p> <ul> <li> <p> <code>Condition</code> - The condition to apply. Valid values are <code>IS</code> and <code>IS_NOT</code>. To include the predictors that match the statement, specify <code>IS</code>. To exclude matching predictors, specify <code>IS_NOT</code>.</p> </li> <li> <p> <code>Key</code> - The name of the parameter to filter on. Valid values are <code>DatasetGroupArn</code> and <code>Status</code>.</p> </li> <li> <p> <code>Value</code> - The value to match.</p> </li> </ul> <p>For example, to list all predictors whose status is ACTIVE, you would specify:</p> <p> <code>\"Filters\": [ { \"Condition\": \"IS\", \"Key\": \"Status\", \"Value\": \"ACTIVE\" } ]</code> </p>"}}}, "ListPredictorsResponse": {"type": "structure", "members": {"Predictors": {"shape": "Predictors", "documentation": "<p>An array of objects that summarize each predictor's properties.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Forecast returns this token. To retrieve the next set of results, use the token in the next request.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the resource for which to list the tags. </p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "Tags", "documentation": "<p>The tags for the resource.</p>"}}}, "ListWhatIfAnalysesRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>If the result of the previous request was truncated, the response includes a <code>NextToken</code>. To retrieve the next set of results, use the token in the next request. Tokens expire after 24 hours.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of items to return in the response.</p>"}, "Filters": {"shape": "Filters", "documentation": "<p>An array of filters. For each filter, you provide a condition and a match statement. The condition is either <code>IS</code> or <code>IS_NOT</code>, which specifies whether to include or exclude the what-if analysis jobs that match the statement from the list, respectively. The match statement consists of a key and a value.</p> <p> <b>Filter properties</b> </p> <ul> <li> <p> <code>Condition</code> - The condition to apply. Valid values are <code>IS</code> and <code>IS_NOT</code>. To include the what-if analysis jobs that match the statement, specify <code>IS</code>. To exclude matching what-if analysis jobs, specify <code>IS_NOT</code>.</p> </li> <li> <p> <code>Key</code> - The name of the parameter to filter on. Valid values are <code>WhatIfAnalysisArn</code> and <code>Status</code>.</p> </li> <li> <p> <code>Value</code> - The value to match.</p> </li> </ul> <p>For example, to list all jobs that export a forecast named <i>electricityWhatIf</i>, specify the following filter:</p> <p> <code>\"Filters\": [ { \"Condition\": \"IS\", \"Key\": \"WhatIfAnalysisArn\", \"Value\": \"arn:aws:forecast:us-west-2:&lt;acct-id&gt;:forecast/electricityWhatIf\" } ]</code> </p>"}}}, "ListWhatIfAnalysesResponse": {"type": "structure", "members": {"WhatIfAnalyses": {"shape": "WhatIfAnalyses", "documentation": "<p>An array of <code>WhatIfAnalysisSummary</code> objects that describe the matched analyses.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Forecast returns this token. To retrieve the next set of results, use the token in the next request.</p>"}}}, "ListWhatIfForecastExportsRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>If the result of the previous request was truncated, the response includes a <code>NextToken</code>. To retrieve the next set of results, use the token in the next&#x2028; request. Tokens expire after 24 hours.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of items to return in the response.</p>"}, "Filters": {"shape": "Filters", "documentation": "<p>An array of filters. For each filter, you provide a condition and a match statement. The condition is either <code>IS</code> or <code>IS_NOT</code>, which specifies whether to include or exclude the what-if forecast export jobs that match the statement from the list, respectively. The match statement consists of a key and a value.</p> <p> <b>Filter properties</b> </p> <ul> <li> <p> <code>Condition</code> - The condition to apply. Valid values are <code>IS</code> and <code>IS_NOT</code>. To include the forecast export jobs that match the statement, specify <code>IS</code>. To exclude matching forecast export jobs, specify <code>IS_NOT</code>.</p> </li> <li> <p> <code>Key</code> - The name of the parameter to filter on. Valid values are <code>WhatIfForecastExportArn</code> and <code>Status</code>.</p> </li> <li> <p> <code>Value</code> - The value to match.</p> </li> </ul> <p>For example, to list all jobs that export a forecast named <i>electricityWIFExport</i>, specify the following filter:</p> <p> <code>\"Filters\": [ { \"Condition\": \"IS\", \"Key\": \"WhatIfForecastExportArn\", \"Value\": \"arn:aws:forecast:us-west-2:&lt;acct-id&gt;:forecast/electricityWIFExport\" } ]</code> </p>"}}}, "ListWhatIfForecastExportsResponse": {"type": "structure", "members": {"WhatIfForecastExports": {"shape": "WhatIfForecastExports", "documentation": "<p>An array of <code>WhatIfForecastExports</code> objects that describe the matched forecast exports.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Forecast returns this token. To retrieve the next set of results, use the token in the next request.</p>"}}}, "ListWhatIfForecastsRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>If the result of the previous request was truncated, the response includes a <code>NextToken</code>. To retrieve the next set of results, use the token in the next&#x2028; request. Tokens expire after 24 hours.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of items to return in the response.</p>"}, "Filters": {"shape": "Filters", "documentation": "<p>An array of filters. For each filter, you provide a condition and a match statement. The condition is either <code>IS</code> or <code>IS_NOT</code>, which specifies whether to include or exclude the what-if forecast export jobs that match the statement from the list, respectively. The match statement consists of a key and a value.</p> <p> <b>Filter properties</b> </p> <ul> <li> <p> <code>Condition</code> - The condition to apply. Valid values are <code>IS</code> and <code>IS_NOT</code>. To include the forecast export jobs that match the statement, specify <code>IS</code>. To exclude matching forecast export jobs, specify <code>IS_NOT</code>.</p> </li> <li> <p> <code>Key</code> - The name of the parameter to filter on. Valid values are <code>WhatIfForecastArn</code> and <code>Status</code>.</p> </li> <li> <p> <code>Value</code> - The value to match.</p> </li> </ul> <p>For example, to list all jobs that export a forecast named <i>electricityWhatIfForecast</i>, specify the following filter:</p> <p> <code>\"Filters\": [ { \"Condition\": \"IS\", \"Key\": \"WhatIfForecastArn\", \"Value\": \"arn:aws:forecast:us-west-2:&lt;acct-id&gt;:forecast/electricityWhatIfForecast\" } ]</code> </p>"}}}, "ListWhatIfForecastsResponse": {"type": "structure", "members": {"WhatIfForecasts": {"shape": "WhatIfForecasts", "documentation": "<p>An array of <code>WhatIfForecasts</code> objects that describe the matched forecasts.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the result of the previous request was truncated, the response includes a <code>NextToken</code>. To retrieve the next set of results, use the token in the next&#x2028; request. Tokens expire after 24 hours.</p>"}}}, "LocalDateTime": {"type": "string", "max": 19, "pattern": "^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}$"}, "Long": {"type": "long"}, "LongArn": {"type": "string", "max": 300, "pattern": "arn:([a-z\\d-]+):forecast:.*:.*:.+"}, "LongArnList": {"type": "list", "member": {"shape": "LongArn"}}, "MaxResults": {"type": "integer", "max": 100, "min": 1}, "Message": {"type": "string"}, "MetricName": {"type": "string", "max": 256}, "MetricResult": {"type": "structure", "members": {"MetricName": {"shape": "MetricName", "documentation": "<p>The name of the metric.</p>"}, "MetricValue": {"shape": "Double", "documentation": "<p>The value for the metric.</p>"}}, "documentation": "<p>An individual metric Forecast calculated when monitoring predictor usage. You can compare the value for this metric to the metric's value in the <a>Baseline</a> to see how your predictor's performance is changing.</p> <p>For more information about metrics generated by Forecast see <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/metrics.html\">Evaluating Predictor Accuracy</a> </p>"}, "MetricResults": {"type": "list", "member": {"shape": "Metric<PERSON><PERSON>ult"}}, "Metrics": {"type": "structure", "members": {"RMSE": {"shape": "Double", "documentation": "<p>The root-mean-square error (RMSE).</p>", "deprecated": true, "deprecatedMessage": "This property is deprecated, please refer to ErrorMetrics for both RMSE and WAPE"}, "WeightedQuantileLosses": {"shape": "WeightedQuantileLosses", "documentation": "<p>An array of weighted quantile losses. Quantiles divide a probability distribution into regions of equal probability. The distribution in this case is the loss function.</p>"}, "ErrorMetrics": {"shape": "ErrorMetrics", "documentation": "<p> Provides detailed error metrics for each forecast type. Metrics include root-mean square-error (RMSE), mean absolute percentage error (MAPE), mean absolute scaled error (MASE), and weighted average percentage error (WAPE). </p>"}, "AverageWeightedQuantileLoss": {"shape": "Double", "documentation": "<p>The average value of all weighted quantile losses.</p>"}}, "documentation": "<p>Provides metrics that are used to evaluate the performance of a predictor. This object is part of the <a>WindowSummary</a> object.</p>"}, "MonitorConfig": {"type": "structure", "required": ["MonitorName"], "members": {"MonitorName": {"shape": "Name", "documentation": "<p>The name of the monitor resource.</p>"}}, "documentation": "<p>The configuration details for the predictor monitor.</p>"}, "MonitorDataSource": {"type": "structure", "members": {"DatasetImportJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset import job used to import the data that initiated the monitor evaluation.</p>"}, "ForecastArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the forecast the monitor used during the evaluation.</p>"}, "PredictorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the predictor resource you are monitoring.</p>"}}, "documentation": "<p>The source of the data the monitor used during the evaluation.</p>"}, "MonitorInfo": {"type": "structure", "members": {"MonitorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the monitor resource.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the monitor. States include:</p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>ACTIVE_STOPPING</code>, <code>ACTIVE_STOPPED</code> </p> </li> <li> <p> <code>UPDATE_IN_PROGRESS</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> </ul>"}}, "documentation": "<p>Provides information about the monitor resource.</p>"}, "MonitorSummary": {"type": "structure", "members": {"MonitorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the monitor resource.</p>"}, "MonitorName": {"shape": "Name", "documentation": "<p>The name of the monitor resource.</p>"}, "ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the predictor being monitored.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the monitor. States include:</p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>ACTIVE_STOPPING</code>, <code>ACTIVE_STOPPED</code> </p> </li> <li> <p> <code>UPDATE_IN_PROGRESS</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> </ul>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the monitor resource was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The last time the monitor resource was modified. The timestamp depends on the status of the job:</p> <ul> <li> <p> <code>CREATE_PENDING</code> - The <code>CreationTime</code>.</p> </li> <li> <p> <code>CREATE_IN_PROGRESS</code> - The current timestamp.</p> </li> <li> <p> <code>STOPPED</code> - When the resource stopped.</p> </li> <li> <p> <code>ACTIVE</code> or <code>CREATE_FAILED</code> - When the monitor creation finished or failed.</p> </li> </ul>"}}, "documentation": "<p>Provides a summary of the monitor properties used in the <a>ListMonitors</a> operation. To get a complete set of properties, call the <a>DescribeMonitor</a> operation, and provide the listed <code>MonitorArn</code>.</p>"}, "Monitors": {"type": "list", "member": {"shape": "MonitorSummary"}}, "Month": {"type": "string", "enum": ["JANUARY", "FEBRUARY", "MARCH", "APRIL", "MAY", "JUNE", "JULY", "AUGUST", "SEPTEMBER", "OCTOBER", "NOVEMBER", "DECEMBER"]}, "Name": {"type": "string", "max": 63, "min": 1, "pattern": "^[a-zA-Z][a-zA-Z0-9_]*"}, "NextToken": {"type": "string", "max": 3000, "min": 1, "pattern": ".+"}, "Operation": {"type": "string", "enum": ["ADD", "SUBTRACT", "MULTIPLY", "DIVIDE"]}, "OptimizationMetric": {"type": "string", "enum": ["WAPE", "RMSE", "AverageWeightedQuantileLoss", "MASE", "MAPE"]}, "ParameterKey": {"type": "string", "max": 256, "pattern": "^[a-zA-Z0-9\\-\\_\\.\\/\\[\\]\\,\\\\]+$"}, "ParameterRanges": {"type": "structure", "members": {"CategoricalParameterRanges": {"shape": "CategoricalParameterRanges", "documentation": "<p>Specifies the tunable range for each categorical hyperparameter.</p>"}, "ContinuousParameterRanges": {"shape": "ContinuousParameterRanges", "documentation": "<p>Specifies the tunable range for each continuous hyperparameter.</p>"}, "IntegerParameterRanges": {"shape": "IntegerParameterRanges", "documentation": "<p>Specifies the tunable range for each integer hyperparameter.</p>"}}, "documentation": "<p>Specifies the categorical, continuous, and integer hyperparameters, and their ranges of tunable values. The range of tunable values determines which values that a hyperparameter tuning job can choose for the specified hyperparameter. This object is part of the <a>HyperParameterTuningJobConfig</a> object.</p>"}, "ParameterValue": {"type": "string", "max": 256, "pattern": "^[a-zA-Z0-9\\-\\_\\.\\/\\[\\]\\,\\\"\\\\\\s]+$"}, "PredictorBacktestExportJobSummary": {"type": "structure", "members": {"PredictorBacktestExportJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the predictor backtest export job.</p>"}, "PredictorBacktestExportJobName": {"shape": "Name", "documentation": "<p>The name of the predictor backtest export job.</p>"}, "Destination": {"shape": "DataDestination"}, "Status": {"shape": "Status", "documentation": "<p>The status of the predictor backtest export job. States include: </p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>CREATE_STOPPING</code>, <code>CREATE_STOPPED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> </ul>"}, "Message": {"shape": "ErrorMessage", "documentation": "<p>Information about any errors that may have occurred during the backtest export.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the predictor backtest export job was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The last time the resource was modified. The timestamp depends on the status of the job:</p> <ul> <li> <p> <code>CREATE_PENDING</code> - The <code>CreationTime</code>.</p> </li> <li> <p> <code>CREATE_IN_PROGRESS</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPING</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPED</code> - When the job stopped.</p> </li> <li> <p> <code>ACTIVE</code> or <code>CREATE_FAILED</code> - When the job finished or failed.</p> </li> </ul>"}}, "documentation": "<p>Provides a summary of the predictor backtest export job properties used in the <a>ListPredictorBacktestExportJobs</a> operation. To get a complete set of properties, call the <a>DescribePredictorBacktestExportJob</a> operation, and provide the listed <code>PredictorBacktestExportJobArn</code>.</p>"}, "PredictorBacktestExportJobs": {"type": "list", "member": {"shape": "PredictorBacktestExportJobSummary"}}, "PredictorBaseline": {"type": "structure", "members": {"BaselineMetrics": {"shape": "BaselineMetrics", "documentation": "<p>The initial <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/metrics.html\">accuracy metrics</a> for the predictor. Use these metrics as a baseline for comparison purposes as you use your predictor and the metrics change.</p>"}}, "documentation": "<p>Metrics you can use as a baseline for comparison purposes. Use these metrics when you interpret monitoring results for an auto predictor.</p>"}, "PredictorEvaluationResults": {"type": "list", "member": {"shape": "EvaluationResult"}}, "PredictorEvent": {"type": "structure", "members": {"Detail": {"shape": "Detail", "documentation": "<p>The type of event. For example, <code>Retrain</code>. A retraining event denotes the timepoint when a predictor was retrained. Any monitor results from before the <code>Datetime</code> are from the previous predictor. Any new metrics are for the newly retrained predictor.</p>"}, "Datetime": {"shape": "Timestamp", "documentation": "<p>The timestamp for when the event occurred.</p>"}}, "documentation": "<p>Provides details about a predictor event, such as a retraining.</p>"}, "PredictorExecution": {"type": "structure", "members": {"AlgorithmArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the algorithm used to test the predictor.</p>"}, "TestWindows": {"shape": "TestWindowDetails", "documentation": "<p>An array of test windows used to evaluate the algorithm. The <code>NumberOfBacktestWindows</code> from the object determines the number of windows in the array.</p>"}}, "documentation": "<p>The algorithm used to perform a backtest and the status of those tests.</p>"}, "PredictorExecutionDetails": {"type": "structure", "members": {"PredictorExecutions": {"shape": "PredictorExecutions", "documentation": "<p>An array of the backtests performed to evaluate the accuracy of the predictor against a particular algorithm. The <code>NumberOfBacktestWindows</code> from the object determines the number of windows in the array.</p>"}}, "documentation": "<p>Contains details on the backtests performed to evaluate the accuracy of the predictor. The tests are returned in descending order of accuracy, with the most accurate backtest appearing first. You specify the number of backtests to perform when you call the operation.</p>"}, "PredictorExecutions": {"type": "list", "member": {"shape": "PredictorExecution"}, "max": 5, "min": 1}, "PredictorMonitorEvaluation": {"type": "structure", "members": {"ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the resource to monitor.</p>"}, "MonitorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the monitor resource.</p>"}, "EvaluationTime": {"shape": "Timestamp", "documentation": "<p>The timestamp that indicates when the monitor evaluation was started. </p>"}, "EvaluationState": {"shape": "EvaluationState", "documentation": "<p>The status of the monitor evaluation. The state can be <code>SUCCESS</code> or <code>FAILURE</code>.</p>"}, "WindowStartDatetime": {"shape": "Timestamp", "documentation": "<p>The timestamp that indicates the start of the window that is used for monitor evaluation.</p>"}, "WindowEndDatetime": {"shape": "Timestamp", "documentation": "<p>The timestamp that indicates the end of the window that is used for monitor evaluation.</p>"}, "PredictorEvent": {"shape": "PredictorEvent", "documentation": "<p>Provides details about a predictor event, such as a retraining.</p>"}, "MonitorDataSource": {"shape": "MonitorDataSource", "documentation": "<p>The source of the data the monitor resource used during the evaluation.</p>"}, "MetricResults": {"shape": "MetricResults", "documentation": "<p>A list of metrics Forecast calculated when monitoring a predictor. You can compare the value for each metric in the list to the metric's value in the <a>Baseline</a> to see how your predictor's performance is changing.</p>"}, "NumItemsEvaluated": {"shape": "<PERSON>", "documentation": "<p>The number of items considered during the evaluation.</p>"}, "Message": {"shape": "Message", "documentation": "<p>Information about any errors that may have occurred during the monitor evaluation.</p>"}}, "documentation": "<p>Describes the results of a monitor evaluation.</p>"}, "PredictorMonitorEvaluations": {"type": "list", "member": {"shape": "PredictorMonitorEvaluation"}}, "PredictorSummary": {"type": "structure", "members": {"PredictorArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the predictor.</p>"}, "PredictorName": {"shape": "Name", "documentation": "<p>The name of the predictor.</p>"}, "DatasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset group that contains the data used to train the predictor.</p>"}, "IsAutoPredictor": {"shape": "Boolean", "documentation": "<p>Whether AutoPredictor was used to create the predictor.</p>"}, "ReferencePredictorSummary": {"shape": "ReferencePredictor<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>A summary of the reference predictor used if the predictor was retrained or upgraded.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the predictor. States include:</p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> <li> <p> <code>CREATE_STOPPING</code>, <code>CREATE_STOPPED</code> </p> </li> </ul> <note> <p>The <code>Status</code> of the predictor must be <code>ACTIVE</code> before you can use the predictor to create a forecast.</p> </note>"}, "Message": {"shape": "ErrorMessage", "documentation": "<p>If an error occurred, an informational message about the error.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the model training task was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The last time the resource was modified. The timestamp depends on the status of the job:</p> <ul> <li> <p> <code>CREATE_PENDING</code> - The <code>CreationTime</code>.</p> </li> <li> <p> <code>CREATE_IN_PROGRESS</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPING</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPED</code> - When the job stopped.</p> </li> <li> <p> <code>ACTIVE</code> or <code>CREATE_FAILED</code> - When the job finished or failed.</p> </li> </ul>"}}, "documentation": "<p>Provides a summary of the predictor properties that are used in the <a>ListPredictors</a> operation. To get the complete set of properties, call the <a>DescribePredictor</a> operation, and provide the listed <code>PredictorArn</code>.</p>"}, "Predictors": {"type": "list", "member": {"shape": "Predictor<PERSON><PERSON><PERSON><PERSON>"}}, "ReferencePredictorSummary": {"type": "structure", "members": {"Arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the reference predictor.</p>"}, "State": {"shape": "State", "documentation": "<p>Whether the reference predictor is <code>Active</code> or <code>Deleted</code>.</p>"}}, "documentation": "<p>Provides a summary of the reference predictor used when retraining or upgrading a predictor.</p>"}, "ResourceAlreadyExistsException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>There is already a resource with this name. Try again with a different name.</p>", "exception": true}, "ResourceInUseException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The specified resource is in use.</p>", "exception": true}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>We can't find a resource with that Amazon Resource Name (ARN). Check the ARN and try again.</p>", "exception": true}, "ResumeResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the monitor resource to resume.</p>"}}}, "S3Config": {"type": "structure", "required": ["Path", "RoleArn"], "members": {"Path": {"shape": "S3Path", "documentation": "<p>The path to an Amazon Simple Storage Service (Amazon S3) bucket or file(s) in an Amazon S3 bucket.</p>"}, "RoleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the Identity and Access Management (IAM) role that Amazon Forecast can assume to access the Amazon S3 bucket or files. If you provide a value for the <code>KMSKeyArn</code> key, the role must allow access to the key.</p> <p>Passing a role across Amazon Web Services accounts is not allowed. If you pass a role that isn't in your account, you get an <code>InvalidInputException</code> error.</p>"}, "KMSKeyArn": {"shape": "KMSKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of an Key Management Service (KMS) key.</p>"}}, "documentation": "<p>The path to the file(s) in an Amazon Simple Storage Service (Amazon S3) bucket, and an Identity and Access Management (IAM) role that Amazon Forecast can assume to access the file(s). Optionally, includes an Key Management Service (KMS) key. This object is part of the <a>DataSource</a> object that is submitted in the <a>CreateDatasetImportJob</a> request, and part of the <a>DataDestination</a> object.</p>"}, "S3Path": {"type": "string", "max": 4096, "min": 7, "pattern": "^s3://[a-z0-9].+$"}, "ScalingType": {"type": "string", "enum": ["Auto", "Linear", "Logarithmic", "ReverseLogarith<PERSON>"]}, "Schema": {"type": "structure", "members": {"Attributes": {"shape": "SchemaAttributes", "documentation": "<p>An array of attributes specifying the name and type of each field in a dataset.</p>"}}, "documentation": "<p>Defines the fields of a dataset.</p>"}, "SchemaAttribute": {"type": "structure", "members": {"AttributeName": {"shape": "Name", "documentation": "<p>The name of the dataset field.</p>"}, "AttributeType": {"shape": "AttributeType", "documentation": "<p>The data type of the field.</p> <p>For a related time series dataset, other than date, item_id, and forecast dimensions attributes, all attributes should be of numerical type (integer/float).</p>"}}, "documentation": "<p>An attribute of a schema, which defines a dataset field. A schema attribute is required for every field in a dataset. The <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_Schema.html\">Schema</a> object contains an array of <code>SchemaAttribute</code> objects.</p>"}, "SchemaAttributes": {"type": "list", "member": {"shape": "SchemaAttribute"}, "max": 100, "min": 1}, "State": {"type": "string", "enum": ["Active", "Deleted"]}, "Statistics": {"type": "structure", "members": {"Count": {"shape": "Integer", "documentation": "<p>The number of values in the field. If the response value is -1, refer to <code>CountLong</code>.</p>"}, "CountDistinct": {"shape": "Integer", "documentation": "<p>The number of distinct values in the field. If the response value is -1, refer to <code>CountDistinctLong</code>.</p>"}, "CountNull": {"shape": "Integer", "documentation": "<p>The number of null values in the field. If the response value is -1, refer to <code>CountNullLong</code>.</p>"}, "CountNan": {"shape": "Integer", "documentation": "<p>The number of NAN (not a number) values in the field. If the response value is -1, refer to <code>CountNanLong</code>.</p>"}, "Min": {"shape": "String", "documentation": "<p>For a numeric field, the minimum value in the field.</p>"}, "Max": {"shape": "String", "documentation": "<p>For a numeric field, the maximum value in the field.</p>"}, "Avg": {"shape": "Double", "documentation": "<p>For a numeric field, the average value in the field.</p>"}, "Stddev": {"shape": "Double", "documentation": "<p>For a numeric field, the standard deviation.</p>"}, "CountLong": {"shape": "<PERSON>", "documentation": "<p>The number of values in the field. <code>CountLong</code> is used instead of <code>Count</code> if the value is greater than 2,147,483,647.</p>"}, "CountDistinctLong": {"shape": "<PERSON>", "documentation": "<p>The number of distinct values in the field. <code>CountDistinctLong</code> is used instead of <code>CountDistinct</code> if the value is greater than 2,147,483,647.</p>"}, "CountNullLong": {"shape": "<PERSON>", "documentation": "<p>The number of null values in the field. <code>CountNullLong</code> is used instead of <code>CountNull</code> if the value is greater than 2,147,483,647.</p>"}, "CountNanLong": {"shape": "<PERSON>", "documentation": "<p>The number of NAN (not a number) values in the field. <code>Count<PERSON><PERSON><PERSON>ong</code> is used instead of <code>Count<PERSON>an</code> if the value is greater than 2,147,483,647.</p>"}}, "documentation": "<p>Provides statistics for each data field imported into to an Amazon Forecast dataset with the <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/API_CreateDatasetImportJob.html\">CreateDatasetImportJob</a> operation.</p>"}, "Status": {"type": "string", "max": 256}, "StopResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the resource to stop. The supported ARNs are <code>DatasetImportJobArn</code>, <code>PredictorArn</code>, <code>PredictorBacktestExportJobArn</code>, <code>ForecastArn</code>, <code>ForecastExportJobArn</code>, <code>ExplainabilityArn</code>, and <code>ExplainabilityExportArn</code>. </p>"}}}, "String": {"type": "string", "max": 256, "pattern": "^[a-zA-Z0-9\\_]+$"}, "SupplementaryFeature": {"type": "structure", "required": ["Name", "Value"], "members": {"Name": {"shape": "Name", "documentation": "<p>The name of the feature. Valid values: <code>\"holiday\"</code> and <code>\"weather\"</code>.</p>"}, "Value": {"shape": "Value", "documentation": "<p> <b>Weather Index</b> </p> <p>To enable the Weather Index, set the value to <code>\"true\"</code> </p> <p> <b>Holidays</b> </p> <p>To enable Holidays, specify a country with one of the following two-letter country codes:</p> <ul> <li> <p>\"AL\" - ALBANIA</p> </li> <li> <p>\"AR\" - ARGENTINA</p> </li> <li> <p>\"AT\" - AUSTRIA</p> </li> <li> <p>\"AU\" - AUSTRALIA</p> </li> <li> <p>\"BA\" - BOSNIA HERZEGOVINA</p> </li> <li> <p>\"BE\" - BELGIUM</p> </li> <li> <p>\"BG\" - BULGARIA</p> </li> <li> <p>\"BO\" - BOLIVIA</p> </li> <li> <p>\"BR\" - BRAZIL</p> </li> <li> <p>\"BY\" - BELARUS</p> </li> <li> <p>\"CA\" - CANADA</p> </li> <li> <p>\"CL\" - CHILE</p> </li> <li> <p>\"CO\" - COLOMBIA</p> </li> <li> <p>\"CR\" - COSTA RICA</p> </li> <li> <p>\"HR\" - CROATIA</p> </li> <li> <p>\"CZ\" - CZECH REPUBLIC</p> </li> <li> <p>\"DK\" - DENMARK</p> </li> <li> <p>\"EC\" - ECUADOR</p> </li> <li> <p>\"EE\" - ESTONIA</p> </li> <li> <p>\"ET\" - ETHIOPIA</p> </li> <li> <p>\"FI\" - FINLAND</p> </li> <li> <p>\"FR\" - FRANCE</p> </li> <li> <p>\"DE\" - GERMANY</p> </li> <li> <p>\"GR\" - GREECE</p> </li> <li> <p>\"HU\" - HUNGARY</p> </li> <li> <p>\"IS\" - ICELAND</p> </li> <li> <p>\"IN\" - INDIA</p> </li> <li> <p>\"IE\" - IRELAND</p> </li> <li> <p>\"IT\" - ITALY</p> </li> <li> <p>\"JP\" - JAPAN</p> </li> <li> <p>\"KZ\" - KAZAKHSTAN</p> </li> <li> <p>\"KR\" - KOREA</p> </li> <li> <p>\"LV\" - LATVIA</p> </li> <li> <p>\"LI\" - LIECHTENSTEIN</p> </li> <li> <p>\"LT\" - LITHUANIA</p> </li> <li> <p>\"LU\" - LUXEMBOURG</p> </li> <li> <p>\"MK\" - MACEDONIA</p> </li> <li> <p>\"MT\" - MALTA</p> </li> <li> <p>\"MX\" - MEXICO</p> </li> <li> <p>\"MD\" - MOLDOVA</p> </li> <li> <p>\"ME\" - MONTENEGRO</p> </li> <li> <p>\"NL\" - NETHERLANDS</p> </li> <li> <p>\"NZ\" - NEW ZEALAND</p> </li> <li> <p>\"NI\" - NICARAGUA</p> </li> <li> <p>\"NG\" - NIGERIA</p> </li> <li> <p>\"NO\" - NORWAY</p> </li> <li> <p>\"PA\" - PANAMA</p> </li> <li> <p>\"PY\" - PARAGUAY</p> </li> <li> <p>\"PE\" - PERU</p> </li> <li> <p>\"PL\" - POLAND</p> </li> <li> <p>\"PT\" - PORTUGAL</p> </li> <li> <p>\"RO\" - ROMANIA</p> </li> <li> <p>\"RU\" - RUSSIA</p> </li> <li> <p>\"RS\" - SERBIA</p> </li> <li> <p>\"SK\" - SLOVAKIA</p> </li> <li> <p>\"SI\" - SLOVENIA</p> </li> <li> <p>\"ZA\" - SOUTH AFRICA</p> </li> <li> <p>\"ES\" - SPAIN</p> </li> <li> <p>\"SE\" - SWEDEN</p> </li> <li> <p>\"CH\" - SWITZERLAND</p> </li> <li> <p>\"UA\" - UKRAINE</p> </li> <li> <p>\"AE\" - UNITED ARAB EMIRATES</p> </li> <li> <p>\"US\" - UNITED STATES</p> </li> <li> <p>\"UK\" - UNITED KINGDOM</p> </li> <li> <p>\"UY\" - URUGUAY</p> </li> <li> <p>\"VE\" - VENEZUELA</p> </li> </ul>"}}, "documentation": "<note> <p>This object belongs to the <a>CreatePredictor</a> operation. If you created your predictor with <a>CreateAutoPredictor</a>, see <a>AdditionalDataset</a>.</p> </note> <p>Describes a supplementary feature of a dataset group. This object is part of the <a>InputDataConfig</a> object. Forecast supports the Weather Index and Holidays built-in featurizations.</p> <p> <b>Weather Index</b> </p> <p>The Amazon Forecast Weather Index is a built-in featurization that incorporates historical and projected weather information into your model. The Weather Index supplements your datasets with over two years of historical weather data and up to 14 days of projected weather data. For more information, see <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/weather.html\">Amazon Forecast Weather Index</a>.</p> <p> <b>Holidays</b> </p> <p>Holidays is a built-in featurization that incorporates a feature-engineered dataset of national holiday information into your model. It provides native support for the holiday calendars of 66 countries. To view the holiday calendars, refer to the <a href=\"http://jollyday.sourceforge.net/data.html\">Jollyday</a> library. For more information, see <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/holidays.html\">Holidays Featurization</a>.</p>"}, "SupplementaryFeatures": {"type": "list", "member": {"shape": "SupplementaryFeature"}, "max": 2, "min": 1}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>One part of a key-value pair that makes up a tag. A <code>key</code> is a general label that acts like a category for more specific tag values.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The optional part of a key-value pair that makes up a tag. A <code>value</code> acts as a descriptor within a tag category (key).</p>"}}, "documentation": "<p>The optional metadata that you apply to a resource to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource - 50.</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length - 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length - 256 Unicode characters in UTF-8.</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Do not use <code>aws:</code>, <code>AWS:</code>, or any upper or lowercase combination of such as a prefix for keys as it is reserved for Amazon Web Services use. You cannot edit or delete tag keys with this prefix. Values can have this prefix. If a tag value has <code>aws</code> as its prefix but the key does not, then Forecast considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of <code>aws</code> do not count against your tags per resource limit.</p> </li> </ul>"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$", "sensitive": true}, "TagKeys": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the resource for which to list the tags. </p>"}, "Tags": {"shape": "Tags", "documentation": "<p>The tags to add to the resource. A tag is an array of key-value pairs.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource - 50.</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length - 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length - 256 Unicode characters in UTF-8.</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Do not use <code>aws:</code>, <code>AWS:</code>, or any upper or lowercase combination of such as a prefix for keys as it is reserved for Amazon Web Services use. You cannot edit or delete tag keys with this prefix. Values can have this prefix. If a tag value has <code>aws</code> as its prefix but the key does not, then Forecast considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of <code>aws</code> do not count against your tags per resource limit.</p> </li> </ul>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$", "sensitive": true}, "Tags": {"type": "list", "member": {"shape": "Tag"}, "max": 200, "min": 0}, "TestWindowDetails": {"type": "list", "member": {"shape": "TestWindowSummary"}}, "TestWindowSummary": {"type": "structure", "members": {"TestWindowStart": {"shape": "Timestamp", "documentation": "<p>The time at which the test began.</p>"}, "TestWindowEnd": {"shape": "Timestamp", "documentation": "<p>The time at which the test ended.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the test. Possible status values are:</p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_IN_PROGRESS</code> </p> </li> <li> <p> <code>CREATE_FAILED</code> </p> </li> </ul>"}, "Message": {"shape": "ErrorMessage", "documentation": "<p>If the test failed, the reason why it failed.</p>"}}, "documentation": "<p>The status, start time, and end time of a backtest, as well as a failure reason if applicable.</p>"}, "TestWindows": {"type": "list", "member": {"shape": "WindowSummary"}}, "TimeAlignmentBoundary": {"type": "structure", "members": {"Month": {"shape": "Month", "documentation": "<p>The month to use for time alignment during aggregation. The month must be in uppercase.</p>"}, "DayOfMonth": {"shape": "DayOfMonth", "documentation": "<p>The day of the month to use for time alignment during aggregation.</p>"}, "DayOfWeek": {"shape": "DayOfWeek", "documentation": "<p>The day of week to use for time alignment during aggregation. The day must be in uppercase.</p>"}, "Hour": {"shape": "Hour", "documentation": "<p>The hour of day to use for time alignment during aggregation.</p>"}}, "documentation": "<p>The time boundary Forecast uses to align and aggregate your data to match your forecast frequency. Provide the unit of time and the time boundary as a key value pair. If you don't provide a time boundary, Forecast uses a set of <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/data-aggregation.html#default-time-boundaries\">Default Time Boundaries</a>. </p> <p>For more information about aggregation, see <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/data-aggregation.html\">Data Aggregation for Different Forecast Frequencies</a>. For more information setting a custom time boundary, see <a href=\"https://docs.aws.amazon.com/forecast/latest/dg/data-aggregation.html#specifying-time-boundary\">Specifying a Time Boundary</a>. </p>"}, "TimePointGranularity": {"type": "string", "enum": ["ALL", "SPECIFIC"]}, "TimeSeriesCondition": {"type": "structure", "required": ["AttributeName", "AttributeValue", "Condition"], "members": {"AttributeName": {"shape": "Name", "documentation": "<p>The item_id, dimension name, IM name, or timestamp that you are modifying.</p>"}, "AttributeValue": {"shape": "AttributeValue", "documentation": "<p>The value that is applied for the chosen <code>Condition</code>.</p>"}, "Condition": {"shape": "Condition", "documentation": "<p>The condition to apply. Valid values are <code>EQUALS</code>, <code>NOT_EQUALS</code>, <code>LESS_THAN</code> and <code>GREATER_THAN</code>.</p>"}}, "documentation": "<p>Creates a subset of items within an attribute that are modified. For example, you can use this operation to create a subset of items that cost $5 or less. To do this, you specify <code>\"AttributeName\": \"price\"</code>, <code>\"AttributeValue\": \"5\"</code>, and <code>\"Condition\": \"LESS_THAN\"</code>. Pair this operation with the <a>Action</a> operation within the <a>CreateWhatIfForecastRequest$TimeSeriesTransformations</a> operation to define how the attribute is modified.</p>"}, "TimeSeriesConditions": {"type": "list", "member": {"shape": "TimeSeriesCondition"}, "max": 10, "min": 0}, "TimeSeriesGranularity": {"type": "string", "enum": ["ALL", "SPECIFIC"]}, "TimeSeriesIdentifiers": {"type": "structure", "members": {"DataSource": {"shape": "DataSource"}, "Schema": {"shape": "<PERSON><PERSON><PERSON>"}, "Format": {"shape": "Format", "documentation": "<p>The format of the data, either CSV or PARQUET.</p>"}}, "documentation": "<p>Details about the import file that contains the time series for which you want to create forecasts.</p>"}, "TimeSeriesReplacementsDataSource": {"type": "structure", "required": ["S3Config", "<PERSON><PERSON><PERSON>"], "members": {"S3Config": {"shape": "S3Config"}, "Schema": {"shape": "<PERSON><PERSON><PERSON>"}, "Format": {"shape": "Format", "documentation": "<p>The format of the replacement data, CSV or PARQUET.</p>"}, "TimestampFormat": {"shape": "TimestampFormat", "documentation": "<p>The timestamp format of the replacement data.</p>"}}, "documentation": "<p>A replacement dataset is a modified version of the baseline related time series that contains only the values that you want to include in a what-if forecast. The replacement dataset must contain the forecast dimensions and item identifiers in the baseline related time series as well as at least 1 changed time series. This dataset is merged with the baseline related time series to create a transformed dataset that is used for the what-if forecast.</p>"}, "TimeSeriesSelector": {"type": "structure", "members": {"TimeSeriesIdentifiers": {"shape": "TimeSeriesIdentifiers", "documentation": "<p>Details about the import file that contains the time series for which you want to create forecasts.</p>"}}, "documentation": "<p>Defines the set of time series that are used to create the forecasts in a <code>TimeSeriesIdentifiers</code> object.</p> <p>The <code>TimeSeriesIdentifiers</code> object needs the following information:</p> <ul> <li> <p> <code>DataSource</code> </p> </li> <li> <p> <code>Format</code> </p> </li> <li> <p> <code>Schema</code> </p> </li> </ul>"}, "TimeSeriesTransformation": {"type": "structure", "members": {"Action": {"shape": "Action", "documentation": "<p>An array of actions that define a time series and how it is transformed. These transformations create a new time series that is used for the what-if analysis.</p>"}, "TimeSeriesConditions": {"shape": "TimeSeriesConditions", "documentation": "<p>An array of conditions that define which members of the related time series are transformed.</p>"}}, "documentation": "<p>A transformation function is a pair of operations that select and modify the rows in a related time series. You select the rows that you want with a condition operation and you modify the rows with a transformation operation. All conditions are joined with an AND operation, meaning that all conditions must be true for the transformation to be applied. Transformations are applied in the order that they are listed.</p>"}, "TimeSeriesTransformations": {"type": "list", "member": {"shape": "TimeSeriesTransformation"}, "max": 30, "min": 0}, "TimeZone": {"type": "string", "max": 256, "pattern": "^[a-zA-Z0-9\\/\\+\\-\\_]+$"}, "Timestamp": {"type": "timestamp"}, "TimestampFormat": {"type": "string", "max": 256, "pattern": "^[a-zA-Z0-9\\-\\:\\.\\,\\'\\s]+$"}, "TrainingParameters": {"type": "map", "key": {"shape": "Parameter<PERSON>ey"}, "value": {"shape": "ParameterValue"}, "max": 100, "min": 0}, "Transformations": {"type": "map", "key": {"shape": "Name"}, "value": {"shape": "Value"}, "max": 20, "min": 1}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the resource for which to list the tags. </p>"}, "TagKeys": {"shape": "TagKeys", "documentation": "<p>The keys of the tags to be removed.</p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateDatasetGroupRequest": {"type": "structure", "required": ["DatasetGroupArn", "DatasetArns"], "members": {"DatasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the dataset group.</p>"}, "DatasetArns": {"shape": "ArnList", "documentation": "<p>An array of the Amazon Resource Names (ARNs) of the datasets to add to the dataset group.</p>"}}}, "UpdateDatasetGroupResponse": {"type": "structure", "members": {}}, "UseGeolocationForTimeZone": {"type": "boolean"}, "Value": {"type": "string", "max": 256, "pattern": "^[a-zA-Z0-9\\_\\-]+$"}, "Values": {"type": "list", "member": {"shape": "Value"}, "max": 20, "min": 1}, "WeightedQuantileLoss": {"type": "structure", "members": {"Quantile": {"shape": "Double", "documentation": "<p>The quantile. Quantiles divide a probability distribution into regions of equal probability. For example, if the distribution was divided into 5 regions of equal probability, the quantiles would be 0.2, 0.4, 0.6, and 0.8.</p>"}, "LossValue": {"shape": "Double", "documentation": "<p>The difference between the predicted value and the actual value over the quantile, weighted (normalized) by dividing by the sum over all quantiles.</p>"}}, "documentation": "<p>The weighted loss value for a quantile. This object is part of the <a>Metrics</a> object.</p>"}, "WeightedQuantileLosses": {"type": "list", "member": {"shape": "WeightedQuantileLoss"}}, "WhatIfAnalyses": {"type": "list", "member": {"shape": "WhatIfAnalysisSummary"}}, "WhatIfAnalysisSummary": {"type": "structure", "members": {"WhatIfAnalysisArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the what-if analysis.</p>"}, "WhatIfAnalysisName": {"shape": "Name", "documentation": "<p>The name of the what-if analysis.</p>"}, "ForecastArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the baseline forecast that is being used in this what-if analysis.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the what-if analysis. States include:</p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>CREATE_STOPPING</code>, <code>CREATE_STOPPED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> </ul> <note> <p>The <code>Status</code> of the what-if analysis must be <code>ACTIVE</code> before you can access the analysis.</p> </note>"}, "Message": {"shape": "ErrorMessage", "documentation": "<p>If an error occurred, an informational message about the error.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the what-if analysis was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The last time the resource was modified. The timestamp depends on the status of the job:</p> <ul> <li> <p> <code>CREATE_PENDING</code> - The <code>CreationTime</code>.</p> </li> <li> <p> <code>CREATE_IN_PROGRESS</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPING</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPED</code> - When the job stopped.</p> </li> <li> <p> <code>ACTIVE</code> or <code>CREATE_FAILED</code> - When the job finished or failed.</p> </li> </ul>"}}, "documentation": "<p>Provides a summary of the what-if analysis properties used in the <a>ListWhatIfAnalyses</a> operation. To get the complete set of properties, call the <a>DescribeWhatIfAnalysis</a> operation, and provide the <code>WhatIfAnalysisArn</code> that is listed in the summary.</p>"}, "WhatIfForecastArnListForExport": {"type": "list", "member": {"shape": "LongArn"}, "max": 50, "min": 1}, "WhatIfForecastExportSummary": {"type": "structure", "members": {"WhatIfForecastExportArn": {"shape": "LongArn", "documentation": "<p>The Amazon Resource Name (ARN) of the what-if forecast export.</p>"}, "WhatIfForecastArns": {"shape": "WhatIfForecastArnListForExport", "documentation": "<p>An array of Amazon Resource Names (ARNs) that define the what-if forecasts included in the export.</p>"}, "WhatIfForecastExportName": {"shape": "Name", "documentation": "<p>The what-if forecast export name.</p>"}, "Destination": {"shape": "DataDestination", "documentation": "<p>The path to the Amazon Simple Storage Service (Amazon S3) bucket where the forecast is exported.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the what-if forecast export. States include:</p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>CREATE_STOPPING</code>, <code>CREATE_STOPPED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> </ul> <note> <p>The <code>Status</code> of the what-if analysis must be <code>ACTIVE</code> before you can access the analysis.</p> </note>"}, "Message": {"shape": "ErrorMessage", "documentation": "<p>If an error occurred, an informational message about the error.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the what-if forecast export was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The last time the resource was modified. The timestamp depends on the status of the job:</p> <ul> <li> <p> <code>CREATE_PENDING</code> - The <code>CreationTime</code>.</p> </li> <li> <p> <code>CREATE_IN_PROGRESS</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPING</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPED</code> - When the job stopped.</p> </li> <li> <p> <code>ACTIVE</code> or <code>CREATE_FAILED</code> - When the job finished or failed.</p> </li> </ul>"}}, "documentation": "<p>Provides a summary of the what-if forecast export properties used in the <a>ListWhatIfForecastExports</a> operation. To get the complete set of properties, call the <a>DescribeWhatIfForecastExport</a> operation, and provide the <code>WhatIfForecastExportArn</code> that is listed in the summary.</p>"}, "WhatIfForecastExports": {"type": "list", "member": {"shape": "WhatIfForecastExportSummary"}}, "WhatIfForecastSummary": {"type": "structure", "members": {"WhatIfForecastArn": {"shape": "LongArn", "documentation": "<p>The Amazon Resource Name (ARN) of the what-if forecast.</p>"}, "WhatIfForecastName": {"shape": "Name", "documentation": "<p>The name of the what-if forecast.</p>"}, "WhatIfAnalysisArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the what-if analysis that contains this what-if forecast.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the what-if forecast. States include:</p> <ul> <li> <p> <code>ACTIVE</code> </p> </li> <li> <p> <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>CREATE_FAILED</code> </p> </li> <li> <p> <code>CREATE_STOPPING</code>, <code>CREATE_STOPPED</code> </p> </li> <li> <p> <code>DELETE_PENDING</code>, <code>DELETE_IN_PROGRESS</code>, <code>DELETE_FAILED</code> </p> </li> </ul> <note> <p>The <code>Status</code> of the what-if analysis must be <code>ACTIVE</code> before you can access the analysis.</p> </note>"}, "Message": {"shape": "ErrorMessage", "documentation": "<p>If an error occurred, an informational message about the error.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>When the what-if forecast was created.</p>"}, "LastModificationTime": {"shape": "Timestamp", "documentation": "<p>The last time the resource was modified. The timestamp depends on the status of the job:</p> <ul> <li> <p> <code>CREATE_PENDING</code> - The <code>CreationTime</code>.</p> </li> <li> <p> <code>CREATE_IN_PROGRESS</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPING</code> - The current timestamp.</p> </li> <li> <p> <code>CREATE_STOPPED</code> - When the job stopped.</p> </li> <li> <p> <code>ACTIVE</code> or <code>CREATE_FAILED</code> - When the job finished or failed.</p> </li> </ul>"}}, "documentation": "<p>Provides a summary of the what-if forecast properties used in the <a>ListWhatIfForecasts</a> operation. To get the complete set of properties, call the <a>DescribeWhatIfForecast</a> operation, and provide the <code>WhatIfForecastArn</code> that is listed in the summary.</p>"}, "WhatIfForecasts": {"type": "list", "member": {"shape": "WhatIfForecastSummary"}}, "WindowSummary": {"type": "structure", "members": {"TestWindowStart": {"shape": "Timestamp", "documentation": "<p>The timestamp that defines the start of the window.</p>"}, "TestWindowEnd": {"shape": "Timestamp", "documentation": "<p>The timestamp that defines the end of the window.</p>"}, "ItemCount": {"shape": "Integer", "documentation": "<p>The number of data points within the window.</p>"}, "EvaluationType": {"shape": "EvaluationType", "documentation": "<p>The type of evaluation.</p> <ul> <li> <p> <code>SUMMARY</code> - The average metrics across all windows.</p> </li> <li> <p> <code>COMPUTED</code> - The metrics for the specified window.</p> </li> </ul>"}, "Metrics": {"shape": "Metrics", "documentation": "<p>Provides metrics used to evaluate the performance of a predictor.</p>"}}, "documentation": "<p>The metrics for a time range within the evaluation portion of a dataset. This object is part of the <a>EvaluationResult</a> object.</p> <p>The <code>TestWindowStart</code> and <code>TestWindowEnd</code> parameters are determined by the <code>BackTestWindowOffset</code> parameter of the <a>EvaluationParameters</a> object.</p>"}}, "documentation": "<p>Provides APIs for creating and managing Amazon Forecast resources.</p>"}