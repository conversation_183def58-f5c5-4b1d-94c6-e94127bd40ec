{"version": "2.0", "metadata": {"apiVersion": "2020-07-20", "endpointPrefix": "proton", "jsonVersion": "1.0", "protocol": "json", "serviceFullName": "AWS Proton", "serviceId": "Proton", "signatureVersion": "v4", "signingName": "proton", "targetPrefix": "AwsProton20200720", "uid": "proton-2020-07-20"}, "operations": {"AcceptEnvironmentAccountConnection": {"name": "AcceptEnvironmentAccountConnection", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AcceptEnvironmentAccountConnectionInput"}, "output": {"shape": "AcceptEnvironmentAccountConnectionOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>In a management account, an environment account connection request is accepted. When the environment account connection request is accepted, Proton can use the associated IAM role to provision environment infrastructure resources in the associated environment account.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-env-account-connections.html\">Environment account connections</a> in the <i>Proton User guide</i>.</p>", "idempotent": true}, "CancelComponentDeployment": {"name": "CancelComponentDeployment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CancelComponentDeploymentInput"}, "output": {"shape": "CancelComponentDeploymentOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Attempts to cancel a component deployment (for a component that is in the <code>IN_PROGRESS</code> deployment status).</p> <p>For more information about components, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-components.html\">Proton components</a> in the <i>Proton User Guide</i>.</p>"}, "CancelEnvironmentDeployment": {"name": "CancelEnvironmentDeployment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CancelEnvironmentDeploymentInput"}, "output": {"shape": "CancelEnvironmentDeploymentOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Attempts to cancel an environment deployment on an <a>UpdateEnvironment</a> action, if the deployment is <code>IN_PROGRESS</code>. For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-env-update.html\">Update an environment</a> in the <i>Proton User guide</i>.</p> <p>The following list includes potential cancellation scenarios.</p> <ul> <li> <p>If the cancellation attempt succeeds, the resulting deployment state is <code>CANCELLED</code>.</p> </li> <li> <p>If the cancellation attempt fails, the resulting deployment state is <code>FAILED</code>.</p> </li> <li> <p>If the current <a>UpdateEnvironment</a> action succeeds before the cancellation attempt starts, the resulting deployment state is <code>SUCCEEDED</code> and the cancellation attempt has no effect.</p> </li> </ul>"}, "CancelServiceInstanceDeployment": {"name": "CancelServiceInstanceDeployment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CancelServiceInstanceDeploymentInput"}, "output": {"shape": "CancelServiceInstanceDeploymentOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Attempts to cancel a service instance deployment on an <a>UpdateServiceInstance</a> action, if the deployment is <code>IN_PROGRESS</code>. For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-svc-instance-update.html\">Update a service instance</a> in the <i>Proton User guide</i>.</p> <p>The following list includes potential cancellation scenarios.</p> <ul> <li> <p>If the cancellation attempt succeeds, the resulting deployment state is <code>CANCELLED</code>.</p> </li> <li> <p>If the cancellation attempt fails, the resulting deployment state is <code>FAILED</code>.</p> </li> <li> <p>If the current <a>UpdateServiceInstance</a> action succeeds before the cancellation attempt starts, the resulting deployment state is <code>SUCCEEDED</code> and the cancellation attempt has no effect.</p> </li> </ul>"}, "CancelServicePipelineDeployment": {"name": "CancelServicePipelineDeployment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CancelServicePipelineDeploymentInput"}, "output": {"shape": "CancelServicePipelineDeploymentOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Attempts to cancel a service pipeline deployment on an <a>UpdateServicePipeline</a> action, if the deployment is <code>IN_PROGRESS</code>. For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-svc-pipeline-update.html\">Update a service pipeline</a> in the <i>Proton User guide</i>.</p> <p>The following list includes potential cancellation scenarios.</p> <ul> <li> <p>If the cancellation attempt succeeds, the resulting deployment state is <code>CANCELLED</code>.</p> </li> <li> <p>If the cancellation attempt fails, the resulting deployment state is <code>FAILED</code>.</p> </li> <li> <p>If the current <a>UpdateServicePipeline</a> action succeeds before the cancellation attempt starts, the resulting deployment state is <code>SUCCEEDED</code> and the cancellation attempt has no effect.</p> </li> </ul>"}, "CreateComponent": {"name": "CreateComponent", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateComponentInput"}, "output": {"shape": "CreateComponentOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Create an Proton component. A component is an infrastructure extension for a service instance.</p> <p>For more information about components, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-components.html\">Proton components</a> in the <i>Proton User Guide</i>.</p>", "idempotent": true}, "CreateEnvironment": {"name": "CreateEnvironment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateEnvironmentInput"}, "output": {"shape": "CreateEnvironmentOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deploy a new environment. An Proton environment is created from an environment template that defines infrastructure and resources that can be shared across services.</p> <p class=\"title\"> <b>You can provision environments using the following methods:</b> </p> <ul> <li> <p>Amazon Web Services-managed provisioning: Proton makes direct calls to provision your resources.</p> </li> <li> <p>Self-managed provisioning: Proton makes pull requests on your repository to provide compiled infrastructure as code (IaC) files that your IaC engine uses to provision resources.</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-environments.html\">Environments</a> and <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-works-prov-methods.html\">Provisioning methods</a> in the <i>Proton User Guide</i>.</p>", "idempotent": true}, "CreateEnvironmentAccountConnection": {"name": "CreateEnvironmentAccountConnection", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateEnvironmentAccountConnectionInput"}, "output": {"shape": "CreateEnvironmentAccountConnectionOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Create an environment account connection in an environment account so that environment infrastructure resources can be provisioned in the environment account from a management account.</p> <p>An environment account connection is a secure bi-directional connection between a <i>management account</i> and an <i>environment account</i> that maintains authorization and permissions. For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-env-account-connections.html\">Environment account connections</a> in the <i>Proton User guide</i>.</p>", "idempotent": true}, "CreateEnvironmentTemplate": {"name": "CreateEnvironmentTemplate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateEnvironmentTemplateInput"}, "output": {"shape": "CreateEnvironmentTemplateOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Create an environment template for Proton. For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-templates.html\">Environment Templates</a> in the <i>Proton User Guide</i>.</p> <p>You can create an environment template in one of the two following ways:</p> <ul> <li> <p>Register and publish a <i>standard</i> environment template that instructs Proton to deploy and manage environment infrastructure.</p> </li> <li> <p>Register and publish a <i>customer managed</i> environment template that connects Proton to your existing provisioned infrastructure that you manage. Proton <i>doesn't</i> manage your existing provisioned infrastructure. To create an environment template for customer provisioned and managed infrastructure, include the <code>provisioning</code> parameter and set the value to <code>CUSTOMER_MANAGED</code>. For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/template-create.html\">Register and publish an environment template</a> in the <i>Proton User Guide</i>.</p> </li> </ul>", "idempotent": true}, "CreateEnvironmentTemplateVersion": {"name": "CreateEnvironmentTemplateVersion", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateEnvironmentTemplateVersionInput"}, "output": {"shape": "CreateEnvironmentTemplateVersionOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Create a new major or minor version of an environment template. A major version of an environment template is a version that <i>isn't</i> backwards compatible. A minor version of an environment template is a version that's backwards compatible within its major version.</p>", "idempotent": true}, "CreateRepository": {"name": "CreateRepository", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateRepositoryInput"}, "output": {"shape": "CreateRepositoryOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Create and register a link to a repository. <PERSON>n uses the link to repeatedly access the repository, to either push to it (self-managed provisioning) or pull from it (template sync). You can share a linked repository across multiple resources (like environments using self-managed provisioning, or synced templates). When you create a repository link, <PERSON><PERSON> creates a <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/using-service-linked-roles.html\">service-linked role</a> for you.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-works-prov-methods.html#ag-works-prov-methods-self\">Self-managed provisioning</a>, <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-template-authoring.html#ag-template-bundles\">Template bundles</a>, and <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-template-sync-configs.html\">Template sync configurations</a> in the <i>Proton User Guide</i>.</p>", "idempotent": true}, "CreateService": {"name": "CreateService", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateServiceInput"}, "output": {"shape": "CreateServiceOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Create an Proton service. An Proton service is an instantiation of a service template and often includes several service instances and pipeline. For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-services.html\">Services</a> in the <i>Proton User Guide</i>.</p>", "idempotent": true}, "CreateServiceInstance": {"name": "CreateServiceInstance", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateServiceInstanceInput"}, "output": {"shape": "CreateServiceInstanceOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Create a service instance.</p>", "idempotent": true}, "CreateServiceSyncConfig": {"name": "CreateServiceSyncConfig", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateServiceSyncConfigInput"}, "output": {"shape": "CreateServiceSyncConfigOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Create the Proton Ops configuration file.</p>", "idempotent": true}, "CreateServiceTemplate": {"name": "CreateServiceTemplate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateServiceTemplateInput"}, "output": {"shape": "CreateServiceTemplateOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Create a service template. The administrator creates a service template to define standardized infrastructure and an optional CI/CD service pipeline. Developers, in turn, select the service template from Proton. If the selected service template includes a service pipeline definition, they provide a link to their source code repository. Proton then deploys and manages the infrastructure defined by the selected service template. For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-templates.html\">Proton templates</a> in the <i>Proton User Guide</i>.</p>", "idempotent": true}, "CreateServiceTemplateVersion": {"name": "CreateServiceTemplateVersion", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateServiceTemplateVersionInput"}, "output": {"shape": "CreateServiceTemplateVersionOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Create a new major or minor version of a service template. A major version of a service template is a version that <i>isn't</i> backward compatible. A minor version of a service template is a version that's backward compatible within its major version.</p>", "idempotent": true}, "CreateTemplateSyncConfig": {"name": "CreateTemplateSyncConfig", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateTemplateSyncConfigInput"}, "output": {"shape": "CreateTemplateSyncConfigOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Set up a template to create new template versions automatically by tracking a linked repository. A linked repository is a repository that has been registered with Proton. For more information, see <a>CreateRepository</a>.</p> <p>When a commit is pushed to your linked repository, Proton checks for changes to your repository template bundles. If it detects a template bundle change, a new major or minor version of its template is created, if the version doesn’t already exist. For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-template-sync-configs.html\">Template sync configurations</a> in the <i>Proton User Guide</i>.</p>", "idempotent": true}, "DeleteComponent": {"name": "DeleteComponent", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteComponentInput"}, "output": {"shape": "DeleteComponentOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Delete an Proton component resource.</p> <p>For more information about components, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-components.html\">Proton components</a> in the <i>Proton User Guide</i>.</p>", "idempotent": true}, "DeleteDeployment": {"name": "DeleteDeployment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDeploymentInput"}, "output": {"shape": "DeleteDeploymentOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Delete the deployment.</p>", "idempotent": true}, "DeleteEnvironment": {"name": "DeleteEnvironment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteEnvironmentInput"}, "output": {"shape": "DeleteEnvironmentOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Delete an environment.</p>", "idempotent": true}, "DeleteEnvironmentAccountConnection": {"name": "DeleteEnvironmentAccountConnection", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteEnvironmentAccountConnectionInput"}, "output": {"shape": "DeleteEnvironmentAccountConnectionOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>In an environment account, delete an environment account connection.</p> <p>After you delete an environment account connection that’s in use by an Proton environment, Proton <i>can’t</i> manage the environment infrastructure resources until a new environment account connection is accepted for the environment account and associated environment. You're responsible for cleaning up provisioned resources that remain without an environment connection.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-env-account-connections.html\">Environment account connections</a> in the <i>Proton User guide</i>.</p>", "idempotent": true}, "DeleteEnvironmentTemplate": {"name": "DeleteEnvironmentTemplate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteEnvironmentTemplateInput"}, "output": {"shape": "DeleteEnvironmentTemplateOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>If no other major or minor versions of an environment template exist, delete the environment template.</p>", "idempotent": true}, "DeleteEnvironmentTemplateVersion": {"name": "DeleteEnvironmentTemplateVersion", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteEnvironmentTemplateVersionInput"}, "output": {"shape": "DeleteEnvironmentTemplateVersionOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>If no other minor versions of an environment template exist, delete a major version of the environment template if it's not the <code>Recommended</code> version. Delete the <code>Recommended</code> version of the environment template if no other major versions or minor versions of the environment template exist. A major version of an environment template is a version that's not backward compatible.</p> <p>Delete a minor version of an environment template if it <i>isn't</i> the <code>Recommended</code> version. Delete a <code>Recommended</code> minor version of the environment template if no other minor versions of the environment template exist. A minor version of an environment template is a version that's backward compatible.</p>", "idempotent": true}, "DeleteRepository": {"name": "DeleteRepository", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteRepositoryInput"}, "output": {"shape": "DeleteRepositoryOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>De-register and unlink your repository.</p>", "idempotent": true}, "DeleteService": {"name": "DeleteService", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteServiceInput"}, "output": {"shape": "DeleteServiceOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Delete a service, with its instances and pipeline.</p> <note> <p>You can't delete a service if it has any service instances that have components attached to them.</p> <p>For more information about components, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-components.html\">Proton components</a> in the <i>Proton User Guide</i>.</p> </note>", "idempotent": true}, "DeleteServiceSyncConfig": {"name": "DeleteServiceSyncConfig", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteServiceSyncConfigInput"}, "output": {"shape": "DeleteServiceSyncConfigOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Delete the Proton Ops file.</p>", "idempotent": true}, "DeleteServiceTemplate": {"name": "DeleteServiceTemplate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteServiceTemplateInput"}, "output": {"shape": "DeleteServiceTemplateOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>If no other major or minor versions of the service template exist, delete the service template.</p>", "idempotent": true}, "DeleteServiceTemplateVersion": {"name": "DeleteServiceTemplateVersion", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteServiceTemplateVersionInput"}, "output": {"shape": "DeleteServiceTemplateVersionOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>If no other minor versions of a service template exist, delete a major version of the service template if it's not the <code>Recommended</code> version. Delete the <code>Recommended</code> version of the service template if no other major versions or minor versions of the service template exist. A major version of a service template is a version that <i>isn't</i> backwards compatible.</p> <p>Delete a minor version of a service template if it's not the <code>Recommended</code> version. Delete a <code>Recommended</code> minor version of the service template if no other minor versions of the service template exist. A minor version of a service template is a version that's backwards compatible.</p>", "idempotent": true}, "DeleteTemplateSyncConfig": {"name": "DeleteTemplateSyncConfig", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteTemplateSyncConfigInput"}, "output": {"shape": "DeleteTemplateSyncConfigOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Delete a template sync configuration.</p>", "idempotent": true}, "GetAccountSettings": {"name": "GetAccountSettings", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetAccountSettingsInput"}, "output": {"shape": "GetAccountSettingsOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get detail data for Proton account-wide settings.</p>"}, "GetComponent": {"name": "GetComponent", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetComponentInput"}, "output": {"shape": "GetComponentOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get detailed data for a component.</p> <p>For more information about components, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-components.html\">Proton components</a> in the <i>Proton User Guide</i>.</p>"}, "GetDeployment": {"name": "GetDeployment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetDeploymentInput"}, "output": {"shape": "GetDeploymentOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get detailed data for a deployment.</p>"}, "GetEnvironment": {"name": "GetEnvironment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetEnvironmentInput"}, "output": {"shape": "GetEnvironmentOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get detailed data for an environment.</p>"}, "GetEnvironmentAccountConnection": {"name": "GetEnvironmentAccountConnection", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetEnvironmentAccountConnectionInput"}, "output": {"shape": "GetEnvironmentAccountConnectionOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>In an environment account, get the detailed data for an environment account connection.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-env-account-connections.html\">Environment account connections</a> in the <i>Proton User guide</i>.</p>"}, "GetEnvironmentTemplate": {"name": "GetEnvironmentTemplate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetEnvironmentTemplateInput"}, "output": {"shape": "GetEnvironmentTemplateOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get detailed data for an environment template.</p>"}, "GetEnvironmentTemplateVersion": {"name": "GetEnvironmentTemplateVersion", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetEnvironmentTemplateVersionInput"}, "output": {"shape": "GetEnvironmentTemplateVersionOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get detailed data for a major or minor version of an environment template.</p>"}, "GetRepository": {"name": "GetRepository", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetRepositoryInput"}, "output": {"shape": "GetRepositoryOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get detail data for a linked repository.</p>"}, "GetRepositorySyncStatus": {"name": "GetRepositorySyncStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetRepositorySyncStatusInput"}, "output": {"shape": "GetRepositorySyncStatusOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get the sync status of a repository used for Proton template sync. For more information about template sync, see .</p> <note> <p>A repository sync status isn't tied to the Proton Repository resource (or any other Proton resource). Therefore, tags on an Proton Repository resource have no effect on this action. Specifically, you can't use these tags to control access to this action using Attribute-based access control (ABAC).</p> <p>For more information about ABAC, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/security_iam_service-with-iam.html#security_iam_service-with-iam-tags\">ABAC</a> in the <i>Proton User Guide</i>.</p> </note>"}, "GetResourcesSummary": {"name": "GetResourcesSummary", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetResourcesSummaryInput"}, "output": {"shape": "GetResourcesSummaryOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get counts of Proton resources.</p> <p>For infrastructure-provisioning resources (environments, services, service instances, pipelines), the action returns staleness counts. A resource is stale when it's behind the recommended version of the Proton template that it uses and it needs an update to become current.</p> <p>The action returns staleness counts (counts of resources that are up-to-date, behind a template major version, or behind a template minor version), the total number of resources, and the number of resources that are in a failed state, grouped by resource type. Components, environments, and service templates return less information - see the <code>components</code>, <code>environments</code>, and <code>serviceTemplates</code> field descriptions.</p> <p>For context, the action also returns the total number of each type of Proton template in the Amazon Web Services account.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/monitoring-dashboard.html\">Proton dashboard</a> in the <i>Proton User Guide</i>.</p>"}, "GetService": {"name": "GetService", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetServiceInput"}, "output": {"shape": "GetServiceOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get detailed data for a service.</p>"}, "GetServiceInstance": {"name": "GetServiceInstance", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetServiceInstanceInput"}, "output": {"shape": "GetServiceInstanceOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get detailed data for a service instance. A service instance is an instantiation of service template and it runs in a specific environment.</p>"}, "GetServiceInstanceSyncStatus": {"name": "GetServiceInstanceSyncStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetServiceInstanceSyncStatusInput"}, "output": {"shape": "GetServiceInstanceSyncStatusOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get the status of the synced service instance.</p>"}, "GetServiceSyncBlockerSummary": {"name": "GetServiceSyncBlockerSummary", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetServiceSyncBlockerSummaryInput"}, "output": {"shape": "GetServiceSyncBlockerSummaryOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get detailed data for the service sync blocker summary.</p>"}, "GetServiceSyncConfig": {"name": "GetServiceSyncConfig", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetServiceSyncConfigInput"}, "output": {"shape": "GetServiceSyncConfigOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get detailed information for the service sync configuration.</p>"}, "GetServiceTemplate": {"name": "GetServiceTemplate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetServiceTemplateInput"}, "output": {"shape": "GetServiceTemplateOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get detailed data for a service template.</p>"}, "GetServiceTemplateVersion": {"name": "GetServiceTemplateVersion", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetServiceTemplateVersionInput"}, "output": {"shape": "GetServiceTemplateVersionOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get detailed data for a major or minor version of a service template.</p>"}, "GetTemplateSyncConfig": {"name": "GetTemplateSyncConfig", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetTemplateSyncConfigInput"}, "output": {"shape": "GetTemplateSyncConfigOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get detail data for a template sync configuration.</p>"}, "GetTemplateSyncStatus": {"name": "GetTemplateSyncStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetTemplateSyncStatusInput"}, "output": {"shape": "GetTemplateSyncStatusOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get the status of a template sync.</p>"}, "ListComponentOutputs": {"name": "ListComponentOutputs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListComponentOutputsInput"}, "output": {"shape": "ListComponentOutputsOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get a list of component Infrastructure as Code (IaC) outputs.</p> <p>For more information about components, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-components.html\">Proton components</a> in the <i>Proton User Guide</i>.</p>"}, "ListComponentProvisionedResources": {"name": "ListComponentProvisionedResources", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListComponentProvisionedResourcesInput"}, "output": {"shape": "ListComponentProvisionedResourcesOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>List provisioned resources for a component with details.</p> <p>For more information about components, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-components.html\">Proton components</a> in the <i>Proton User Guide</i>.</p>"}, "ListComponents": {"name": "ListComponents", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListComponentsInput"}, "output": {"shape": "ListComponentsOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>List components with summary data. You can filter the result list by environment, service, or a single service instance.</p> <p>For more information about components, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-components.html\">Proton components</a> in the <i>Proton User Guide</i>.</p>"}, "ListDeployments": {"name": "ListDeployments", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListDeploymentsInput"}, "output": {"shape": "ListDeploymentsOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>List deployments. You can filter the result list by environment, service, or a single service instance.</p>"}, "ListEnvironmentAccountConnections": {"name": "ListEnvironmentAccountConnections", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListEnvironmentAccountConnectionsInput"}, "output": {"shape": "ListEnvironmentAccountConnectionsOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>View a list of environment account connections.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-env-account-connections.html\">Environment account connections</a> in the <i>Proton User guide</i>.</p>"}, "ListEnvironmentOutputs": {"name": "ListEnvironmentOutputs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListEnvironmentOutputsInput"}, "output": {"shape": "ListEnvironmentOutputsOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>List the infrastructure as code outputs for your environment.</p>"}, "ListEnvironmentProvisionedResources": {"name": "ListEnvironmentProvisionedResources", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListEnvironmentProvisionedResourcesInput"}, "output": {"shape": "ListEnvironmentProvisionedResourcesOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>List the provisioned resources for your environment.</p>"}, "ListEnvironmentTemplateVersions": {"name": "ListEnvironmentTemplateVersions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListEnvironmentTemplateVersionsInput"}, "output": {"shape": "ListEnvironmentTemplateVersionsOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>List major or minor versions of an environment template with detail data.</p>"}, "ListEnvironmentTemplates": {"name": "ListEnvironmentTemplates", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListEnvironmentTemplatesInput"}, "output": {"shape": "ListEnvironmentTemplatesOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>List environment templates.</p>"}, "ListEnvironments": {"name": "ListEnvironments", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListEnvironmentsInput"}, "output": {"shape": "ListEnvironmentsOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>List environments with detail data summaries.</p>"}, "ListRepositories": {"name": "ListRepositories", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListRepositoriesInput"}, "output": {"shape": "ListRepositoriesOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>List linked repositories with detail data.</p>"}, "ListRepositorySyncDefinitions": {"name": "ListRepositorySyncDefinitions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListRepositorySyncDefinitionsInput"}, "output": {"shape": "ListRepositorySyncDefinitionsOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>List repository sync definitions with detail data.</p>"}, "ListServiceInstanceOutputs": {"name": "ListServiceInstanceOutputs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListServiceInstanceOutputsInput"}, "output": {"shape": "ListServiceInstanceOutputsOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get a list service of instance Infrastructure as Code (IaC) outputs.</p>"}, "ListServiceInstanceProvisionedResources": {"name": "ListServiceInstanceProvisionedResources", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListServiceInstanceProvisionedResourcesInput"}, "output": {"shape": "ListServiceInstanceProvisionedResourcesOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>List provisioned resources for a service instance with details.</p>"}, "ListServiceInstances": {"name": "ListServiceInstances", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListServiceInstancesInput"}, "output": {"shape": "ListServiceInstancesOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>List service instances with summary data. This action lists service instances of all services in the Amazon Web Services account.</p>"}, "ListServicePipelineOutputs": {"name": "ListServicePipelineOutputs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListServicePipelineOutputsInput"}, "output": {"shape": "ListServicePipelineOutputsOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get a list of service pipeline Infrastructure as Code (IaC) outputs.</p>"}, "ListServicePipelineProvisionedResources": {"name": "ListServicePipelineProvisionedResources", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListServicePipelineProvisionedResourcesInput"}, "output": {"shape": "ListServicePipelineProvisionedResourcesOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>List provisioned resources for a service and pipeline with details.</p>"}, "ListServiceTemplateVersions": {"name": "ListServiceTemplateVersions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListServiceTemplateVersionsInput"}, "output": {"shape": "ListServiceTemplateVersionsOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>List major or minor versions of a service template with detail data.</p>"}, "ListServiceTemplates": {"name": "ListServiceTemplates", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListServiceTemplatesInput"}, "output": {"shape": "ListServiceTemplatesOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>List service templates with detail data.</p>"}, "ListServices": {"name": "ListServices", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListServicesInput"}, "output": {"shape": "ListServicesOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>List services with summaries of detail data.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceInput"}, "output": {"shape": "ListTagsForResourceOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>List tags for a resource. For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/resources.html\">Proton resources and tagging</a> in the <i>Proton User Guide</i>.</p>"}, "NotifyResourceDeploymentStatusChange": {"name": "NotifyResourceDeploymentStatusChange", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "NotifyResourceDeploymentStatusChangeInput"}, "output": {"shape": "NotifyResourceDeploymentStatusChangeOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Notify Proton of status changes to a provisioned resource when you use self-managed provisioning.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-works-prov-methods.html#ag-works-prov-methods-self\">Self-managed provisioning</a> in the <i>Proton User Guide</i>.</p>"}, "RejectEnvironmentAccountConnection": {"name": "RejectEnvironmentAccountConnection", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RejectEnvironmentAccountConnectionInput"}, "output": {"shape": "RejectEnvironmentAccountConnectionOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>In a management account, reject an environment account connection from another environment account.</p> <p>After you reject an environment account connection request, you <i>can't</i> accept or use the rejected environment account connection.</p> <p>You <i>can’t</i> reject an environment account connection that's connected to an environment.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-env-account-connections.html\">Environment account connections</a> in the <i>Proton User guide</i>.</p>", "idempotent": true}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceInput"}, "output": {"shape": "TagResourceOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Tag a resource. A tag is a key-value pair of metadata that you associate with an Proton resource.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/resources.html\">Proton resources and tagging</a> in the <i>Proton User Guide</i>.</p>", "idempotent": true}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceInput"}, "output": {"shape": "UntagResourceOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Remove a customer tag from a resource. A tag is a key-value pair of metadata associated with an Proton resource.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/resources.html\">Proton resources and tagging</a> in the <i>Proton User Guide</i>.</p>", "idempotent": true}, "UpdateAccountSettings": {"name": "UpdateAccountSettings", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateAccountSettingsInput"}, "output": {"shape": "UpdateAccountSettingsOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Update Proton settings that are used for multiple services in the Amazon Web Services account.</p>"}, "UpdateComponent": {"name": "UpdateComponent", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateComponentInput"}, "output": {"shape": "UpdateComponentOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Update a component.</p> <p>There are a few modes for updating a component. The <code>deploymentType</code> field defines the mode.</p> <note> <p>You can't update a component while its deployment status, or the deployment status of a service instance attached to it, is <code>IN_PROGRESS</code>.</p> </note> <p>For more information about components, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-components.html\">Proton components</a> in the <i>Proton User Guide</i>.</p>"}, "UpdateEnvironment": {"name": "UpdateEnvironment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateEnvironmentInput"}, "output": {"shape": "UpdateEnvironmentOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Update an environment.</p> <p>If the environment is associated with an environment account connection, <i>don't</i> update or include the <code>protonServiceRoleArn</code> and <code>provisioningRepository</code> parameter to update or connect to an environment account connection.</p> <p>You can only update to a new environment account connection if that connection was created in the same environment account that the current environment account connection was created in. The account connection must also be associated with the current environment.</p> <p>If the environment <i>isn't</i> associated with an environment account connection, <i>don't</i> update or include the <code>environmentAccountConnectionId</code> parameter. You <i>can't</i> update or connect the environment to an environment account connection if it <i>isn't</i> already associated with an environment connection.</p> <p>You can update either the <code>environmentAccountConnectionId</code> or <code>protonServiceRoleArn</code> parameter and value. You can’t update both.</p> <p>If the environment was configured for Amazon Web Services-managed provisioning, omit the <code>provisioningRepository</code> parameter.</p> <p>If the environment was configured for self-managed provisioning, specify the <code>provisioningRepository</code> parameter and omit the <code>protonServiceRoleArn</code> and <code>environmentAccountConnectionId</code> parameters.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-environments.html\">Environments</a> and <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-works-prov-methods.html\">Provisioning methods</a> in the <i>Proton User Guide</i>.</p> <p>There are four modes for updating an environment. The <code>deploymentType</code> field defines the mode.</p> <dl> <dt/> <dd> <p> <code>NONE</code> </p> <p>In this mode, a deployment <i>doesn't</i> occur. Only the requested metadata parameters are updated.</p> </dd> <dt/> <dd> <p> <code>CURRENT_VERSION</code> </p> <p>In this mode, the environment is deployed and updated with the new spec that you provide. Only requested parameters are updated. <i>Don’t</i> include minor or major version parameters when you use this <code>deployment-type</code>.</p> </dd> <dt/> <dd> <p> <code>MINOR_VERSION</code> </p> <p>In this mode, the environment is deployed and updated with the published, recommended (latest) minor version of the current major version in use, by default. You can also specify a different minor version of the current major version in use.</p> </dd> <dt/> <dd> <p> <code>MAJOR_VERSION</code> </p> <p>In this mode, the environment is deployed and updated with the published, recommended (latest) major and minor version of the current template, by default. You can also specify a different major version that's higher than the major version in use and a minor version.</p> </dd> </dl>"}, "UpdateEnvironmentAccountConnection": {"name": "UpdateEnvironmentAccountConnection", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateEnvironmentAccountConnectionInput"}, "output": {"shape": "UpdateEnvironmentAccountConnectionOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>In an environment account, update an environment account connection to use a new IAM role.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-env-account-connections.html\">Environment account connections</a> in the <i>Proton User guide</i>.</p>", "idempotent": true}, "UpdateEnvironmentTemplate": {"name": "UpdateEnvironmentTemplate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateEnvironmentTemplateInput"}, "output": {"shape": "UpdateEnvironmentTemplateOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Update an environment template.</p>"}, "UpdateEnvironmentTemplateVersion": {"name": "UpdateEnvironmentTemplateVersion", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateEnvironmentTemplateVersionInput"}, "output": {"shape": "UpdateEnvironmentTemplateVersionOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Update a major or minor version of an environment template.</p>"}, "UpdateService": {"name": "UpdateService", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateServiceInput"}, "output": {"shape": "UpdateServiceOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Edit a service description or use a spec to add and delete service instances.</p> <note> <p>Existing service instances and the service pipeline <i>can't</i> be edited using this API. They can only be deleted.</p> </note> <p>Use the <code>description</code> parameter to modify the description.</p> <p>Edit the <code>spec</code> parameter to add or delete instances.</p> <note> <p>You can't delete a service instance (remove it from the spec) if it has an attached component.</p> <p>For more information about components, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-components.html\">Proton components</a> in the <i>Proton User Guide</i>.</p> </note>"}, "UpdateServiceInstance": {"name": "UpdateServiceInstance", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateServiceInstanceInput"}, "output": {"shape": "UpdateServiceInstanceOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Update a service instance.</p> <p>There are a few modes for updating a service instance. The <code>deploymentType</code> field defines the mode.</p> <note> <p>You can't update a service instance while its deployment status, or the deployment status of a component attached to it, is <code>IN_PROGRESS</code>.</p> <p>For more information about components, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-components.html\">Proton components</a> in the <i>Proton User Guide</i>.</p> </note>"}, "UpdateServicePipeline": {"name": "UpdateServicePipeline", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateServicePipelineInput"}, "output": {"shape": "UpdateServicePipelineOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Update the service pipeline.</p> <p>There are four modes for updating a service pipeline. The <code>deploymentType</code> field defines the mode.</p> <dl> <dt/> <dd> <p> <code>NONE</code> </p> <p>In this mode, a deployment <i>doesn't</i> occur. Only the requested metadata parameters are updated.</p> </dd> <dt/> <dd> <p> <code>CURRENT_VERSION</code> </p> <p>In this mode, the service pipeline is deployed and updated with the new spec that you provide. Only requested parameters are updated. <i>Don’t</i> include major or minor version parameters when you use this <code>deployment-type</code>.</p> </dd> <dt/> <dd> <p> <code>MINOR_VERSION</code> </p> <p>In this mode, the service pipeline is deployed and updated with the published, recommended (latest) minor version of the current major version in use, by default. You can specify a different minor version of the current major version in use.</p> </dd> <dt/> <dd> <p> <code>MAJOR_VERSION</code> </p> <p>In this mode, the service pipeline is deployed and updated with the published, recommended (latest) major and minor version of the current template by default. You can specify a different major version that's higher than the major version in use and a minor version.</p> </dd> </dl>"}, "UpdateServiceSyncBlocker": {"name": "UpdateServiceSyncBlocker", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateServiceSyncBlockerInput"}, "output": {"shape": "UpdateServiceSyncBlockerOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Update the service sync blocker by resolving it.</p>"}, "UpdateServiceSyncConfig": {"name": "UpdateServiceSyncConfig", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateServiceSyncConfigInput"}, "output": {"shape": "UpdateServiceSyncConfigOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Update the Proton Ops config file.</p>"}, "UpdateServiceTemplate": {"name": "UpdateServiceTemplate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateServiceTemplateInput"}, "output": {"shape": "UpdateServiceTemplateOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Update a service template.</p>"}, "UpdateServiceTemplateVersion": {"name": "UpdateServiceTemplateVersion", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateServiceTemplateVersionInput"}, "output": {"shape": "UpdateServiceTemplateVersionOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Update a major or minor version of a service template.</p>"}, "UpdateTemplateSyncConfig": {"name": "UpdateTemplateSyncConfig", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateTemplateSyncConfigInput"}, "output": {"shape": "UpdateTemplateSyncConfigOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Update template sync configuration parameters, except for the <code>templateName</code> and <code>templateType</code>. Repository details (branch, name, and provider) should be of a linked repository. A linked repository is a repository that has been registered with Proton. For more information, see <a>CreateRepository</a>.</p>"}}, "shapes": {"AcceptEnvironmentAccountConnectionInput": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "EnvironmentAccountConnectionId", "documentation": "<p>The ID of the environment account connection.</p>"}}}, "AcceptEnvironmentAccountConnectionOutput": {"type": "structure", "required": ["environmentAccountConnection"], "members": {"environmentAccountConnection": {"shape": "EnvironmentAccountConnection", "documentation": "<p>The environment account connection data that's returned by Proton.</p>"}}}, "AccessDeniedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>There <i>isn't</i> sufficient access for performing this action.</p>", "exception": true}, "AccountSettings": {"type": "structure", "members": {"pipelineCodebuildRoleArn": {"shape": "RoleArnOrEmptyString", "documentation": "<p>The Amazon Resource Name (ARN) of the service role that Proton uses for provisioning pipelines. Proton assumes this role for CodeBuild-based provisioning.</p>"}, "pipelineProvisioningRepository": {"shape": "RepositoryBranch", "documentation": "<p>The linked repository for pipeline provisioning. Required if you have environments configured for self-managed provisioning with services that include pipelines. A linked repository is a repository that has been registered with Proton. For more information, see <a>CreateRepository</a>.</p>"}, "pipelineServiceRoleArn": {"shape": "RoleArnOrEmptyString", "documentation": "<p>The Amazon Resource Name (ARN) of the service role you want to use for provisioning pipelines. Assumed by Proton for Amazon Web Services-managed provisioning, and by customer-owned automation for self-managed provisioning.</p>"}}, "documentation": "<p>Proton settings that are used for multiple services in the Amazon Web Services account.</p>"}, "Arn": {"type": "string", "max": 200, "min": 1, "pattern": "^arn:(aws|aws-cn|aws-us-gov):[a-zA-Z0-9-]+:[a-zA-Z0-9-]*:\\d{12}:([\\w+=,.@-]+[/:])*[\\w+=,.@-]+$"}, "AwsAccountId": {"type": "string", "pattern": "^\\d{12}$"}, "BlockerStatus": {"type": "string", "enum": ["ACTIVE", "RESOLVED"]}, "BlockerType": {"type": "string", "enum": ["AUTOMATED"]}, "Boolean": {"type": "boolean", "box": true}, "CancelComponentDeploymentInput": {"type": "structure", "required": ["componentName"], "members": {"componentName": {"shape": "ResourceName", "documentation": "<p>The name of the component with the deployment to cancel.</p>"}}}, "CancelComponentDeploymentOutput": {"type": "structure", "required": ["component"], "members": {"component": {"shape": "Component", "documentation": "<p>The detailed data of the component with the deployment that is being canceled.</p>"}}}, "CancelEnvironmentDeploymentInput": {"type": "structure", "required": ["environmentName"], "members": {"environmentName": {"shape": "ResourceName", "documentation": "<p>The name of the environment with the deployment to cancel.</p>"}}}, "CancelEnvironmentDeploymentOutput": {"type": "structure", "required": ["environment"], "members": {"environment": {"shape": "Environment", "documentation": "<p>The environment summary data that's returned by Proton.</p>"}}}, "CancelServiceInstanceDeploymentInput": {"type": "structure", "required": ["serviceInstanceName", "serviceName"], "members": {"serviceInstanceName": {"shape": "ResourceName", "documentation": "<p>The name of the service instance with the deployment to cancel.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service with the service instance deployment to cancel.</p>"}}}, "CancelServiceInstanceDeploymentOutput": {"type": "structure", "required": ["serviceInstance"], "members": {"serviceInstance": {"shape": "ServiceInstance", "documentation": "<p>The service instance summary data that's returned by Proton.</p>"}}}, "CancelServicePipelineDeploymentInput": {"type": "structure", "required": ["serviceName"], "members": {"serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service with the service pipeline deployment to cancel.</p>"}}}, "CancelServicePipelineDeploymentOutput": {"type": "structure", "required": ["pipeline"], "members": {"pipeline": {"shape": "ServicePipeline", "documentation": "<p>The service pipeline detail data that's returned by Proton.</p>"}}}, "ClientToken": {"type": "string", "max": 64, "min": 0, "pattern": "^[!-~]*$"}, "CompatibleEnvironmentTemplate": {"type": "structure", "required": ["majorVersion", "templateName"], "members": {"majorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The major version of the compatible environment template.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The compatible environment template name.</p>"}}, "documentation": "<p>Compatible environment template data.</p>"}, "CompatibleEnvironmentTemplateInput": {"type": "structure", "required": ["majorVersion", "templateName"], "members": {"majorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The major version of the compatible environment template.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The compatible environment template name.</p>"}}, "documentation": "<p>Compatible environment template data.</p>"}, "CompatibleEnvironmentTemplateInputList": {"type": "list", "member": {"shape": "CompatibleEnvironmentTemplateInput"}, "max": 10, "min": 1}, "CompatibleEnvironmentTemplateList": {"type": "list", "member": {"shape": "CompatibleEnvironmentTemplate"}}, "Component": {"type": "structure", "required": ["arn", "createdAt", "deploymentStatus", "environmentName", "lastModifiedAt", "name"], "members": {"arn": {"shape": "ComponentArn", "documentation": "<p>The Amazon Resource Name (ARN) of the component.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the component was created.</p>"}, "deploymentStatus": {"shape": "DeploymentStatus", "documentation": "<p>The component deployment status.</p>"}, "deploymentStatusMessage": {"shape": "StatusMessage", "documentation": "<p>The message associated with the component deployment status.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the component.</p>"}, "environmentName": {"shape": "ResourceName", "documentation": "<p>The name of the Proton environment that this component is associated with.</p>"}, "lastAttemptedDeploymentId": {"shape": "DeploymentId", "documentation": "<p>The ID of the last attempted deployment of this component.</p>"}, "lastClientRequestToken": {"shape": "String", "documentation": "<p>The last token the client requested.</p>"}, "lastDeploymentAttemptedAt": {"shape": "Timestamp", "documentation": "<p>The time when a deployment of the component was last attempted.</p>"}, "lastDeploymentSucceededAt": {"shape": "Timestamp", "documentation": "<p>The time when the component was last deployed successfully.</p>"}, "lastModifiedAt": {"shape": "Timestamp", "documentation": "<p>The time when the component was last modified.</p>"}, "lastSucceededDeploymentId": {"shape": "DeploymentId", "documentation": "<p>The ID of the last successful deployment of this component.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the component.</p>"}, "serviceInstanceName": {"shape": "ResourceName", "documentation": "<p>The name of the service instance that this component is attached to. Provided when a component is attached to a service instance.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service that <code>serviceInstanceName</code> is associated with. Provided when a component is attached to a service instance.</p>"}, "serviceSpec": {"shape": "SpecContents", "documentation": "<p>The service spec that the component uses to access service inputs. Provided when a component is attached to a service instance.</p>"}}, "documentation": "<p>Detailed data of an Proton component resource.</p> <p>For more information about components, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-components.html\">Proton components</a> in the <i>Proton User Guide</i>.</p>"}, "ComponentArn": {"type": "string"}, "ComponentDeploymentIdList": {"type": "list", "member": {"shape": "DeploymentId"}, "max": 1, "min": 0}, "ComponentDeploymentUpdateType": {"type": "string", "enum": ["NONE", "CURRENT_VERSION"]}, "ComponentState": {"type": "structure", "members": {"serviceInstanceName": {"shape": "ResourceNameOrEmpty", "documentation": "<p>The name of the service instance that this component is attached to. Provided when a component is attached to a service instance.</p>"}, "serviceName": {"shape": "ResourceNameOrEmpty", "documentation": "<p>The name of the service that <code>serviceInstanceName</code> is associated with. Provided when a component is attached to a service instance.</p>"}, "serviceSpec": {"shape": "SpecContents", "documentation": "<p>The service spec that the component uses to access service inputs. Provided when a component is attached to a service instance.</p>"}, "templateFile": {"shape": "TemplateFileContents", "documentation": "<p>The template file used.</p>"}}, "documentation": "<p>The detailed data about the current state of the component.</p>"}, "ComponentSummary": {"type": "structure", "required": ["arn", "createdAt", "deploymentStatus", "environmentName", "lastModifiedAt", "name"], "members": {"arn": {"shape": "ComponentArn", "documentation": "<p>The Amazon Resource Name (ARN) of the component.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the component was created.</p>"}, "deploymentStatus": {"shape": "DeploymentStatus", "documentation": "<p>The component deployment status.</p>"}, "deploymentStatusMessage": {"shape": "StatusMessage", "documentation": "<p>The message associated with the component deployment status.</p>"}, "environmentName": {"shape": "ResourceName", "documentation": "<p>The name of the Proton environment that this component is associated with.</p>"}, "lastAttemptedDeploymentId": {"shape": "DeploymentId", "documentation": "<p>The ID of the last attempted deployment of this component.</p>"}, "lastDeploymentAttemptedAt": {"shape": "Timestamp", "documentation": "<p>The time when a deployment of the component was last attempted.</p>"}, "lastDeploymentSucceededAt": {"shape": "Timestamp", "documentation": "<p>The time when the component was last deployed successfully.</p>"}, "lastModifiedAt": {"shape": "Timestamp", "documentation": "<p>The time when the component was last modified.</p>"}, "lastSucceededDeploymentId": {"shape": "DeploymentId", "documentation": "<p>The ID of the last successful deployment of this component.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the component.</p>"}, "serviceInstanceName": {"shape": "ResourceName", "documentation": "<p>The name of the service instance that this component is attached to. Provided when a component is attached to a service instance.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service that <code>serviceInstanceName</code> is associated with. Provided when a component is attached to a service instance.</p>"}}, "documentation": "<p>Summary data of an Proton component resource.</p> <p>For more information about components, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-components.html\">Proton components</a> in the <i>Proton User Guide</i>.</p>"}, "ComponentSummaryList": {"type": "list", "member": {"shape": "ComponentSummary"}}, "ConflictException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The request <i>couldn't</i> be made due to a conflicting operation or resource.</p>", "exception": true}, "CountsSummary": {"type": "structure", "members": {"components": {"shape": "ResourceCountsSummary", "documentation": "<p>The total number of components in the Amazon Web Services account.</p> <p>The semantics of the <code>components</code> field are different from the semantics of results for other infrastructure-provisioning resources. That's because at this time components don't have associated templates, therefore they don't have the concept of staleness. The <code>components</code> object will only contain <code>total</code> and <code>failed</code> members.</p>"}, "environmentTemplates": {"shape": "ResourceCountsSummary", "documentation": "<p>The total number of environment templates in the Amazon Web Services account. The <code>environmentTemplates</code> object will only contain <code>total</code> members.</p>"}, "environments": {"shape": "ResourceCountsSummary", "documentation": "<p>The staleness counts for Proton environments in the Amazon Web Services account. The <code>environments</code> object will only contain <code>total</code> members.</p>"}, "pipelines": {"shape": "ResourceCountsSummary", "documentation": "<p>The staleness counts for Proton pipelines in the Amazon Web Services account.</p>"}, "serviceInstances": {"shape": "ResourceCountsSummary", "documentation": "<p>The staleness counts for Proton service instances in the Amazon Web Services account.</p>"}, "serviceTemplates": {"shape": "ResourceCountsSummary", "documentation": "<p>The total number of service templates in the Amazon Web Services account. The <code>serviceTemplates</code> object will only contain <code>total</code> members.</p>"}, "services": {"shape": "ResourceCountsSummary", "documentation": "<p>The staleness counts for Proton services in the Amazon Web Services account.</p>"}}, "documentation": "<p>Summary counts of each Proton resource type.</p>"}, "CreateComponentInput": {"type": "structure", "required": ["manifest", "name", "templateFile"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>The client token for the created component.</p>", "idempotencyToken": true}, "description": {"shape": "Description", "documentation": "<p>An optional customer-provided description of the component.</p>"}, "environmentName": {"shape": "ResourceName", "documentation": "<p>The name of the Proton environment that you want to associate this component with. You must specify this when you don't specify <code>serviceInstanceName</code> and <code>serviceName</code>.</p>"}, "manifest": {"shape": "TemplateManifestContents", "documentation": "<p>A path to a manifest file that lists the Infrastructure as Code (IaC) file, template language, and rendering engine for infrastructure that a custom component provisions.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The customer-provided name of the component.</p>"}, "serviceInstanceName": {"shape": "ResourceName", "documentation": "<p>The name of the service instance that you want to attach this component to. If you don't specify this, the component isn't attached to any service instance. Specify both <code>serviceInstanceName</code> and <code>serviceName</code> or neither of them.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service that <code>serviceInstanceName</code> is associated with. If you don't specify this, the component isn't attached to any service instance. Specify both <code>serviceInstanceName</code> and <code>serviceName</code> or neither of them.</p>"}, "serviceSpec": {"shape": "SpecContents", "documentation": "<p>The service spec that you want the component to use to access service inputs. Set this only when you attach the component to a service instance.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>An optional list of metadata items that you can associate with the Proton component. A tag is a key-value pair.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/resources.html\">Proton resources and tagging</a> in the <i>Proton User Guide</i>.</p>"}, "templateFile": {"shape": "TemplateFileContents", "documentation": "<p>A path to the Infrastructure as Code (IaC) file describing infrastructure that a custom component provisions.</p> <note> <p>Components support a single IaC file, even if you use Terraform as your template language.</p> </note>"}}}, "CreateComponentOutput": {"type": "structure", "required": ["component"], "members": {"component": {"shape": "Component", "documentation": "<p>The detailed data of the created component.</p>"}}}, "CreateEnvironmentAccountConnectionInput": {"type": "structure", "required": ["environmentName", "managementAccountId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>When included, if two identical requests are made with the same client token, <PERSON>n returns the environment account connection that the first request created.</p>", "idempotencyToken": true}, "codebuildRoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of an IAM service role in the environment account. Proton uses this role to provision infrastructure resources using CodeBuild-based provisioning in the associated environment account.</p>"}, "componentRoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM service role that Proton uses when provisioning directly defined components in the associated environment account. It determines the scope of infrastructure that a component can provision in the account.</p> <p>You must specify <code>componentRoleArn</code> to allow directly defined components to be associated with any environments running in this account.</p> <p>For more information about components, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-components.html\">Proton components</a> in the <i>Proton User Guide</i>.</p>"}, "environmentName": {"shape": "ResourceName", "documentation": "<p>The name of the Proton environment that's created in the associated management account.</p>"}, "managementAccountId": {"shape": "AwsAccountId", "documentation": "<p>The ID of the management account that accepts or rejects the environment account connection. You create and manage the Proton environment in this account. If the management account accepts the environment account connection, Proton can use the associated IAM role to provision environment infrastructure resources in the associated environment account.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM service role that's created in the environment account. Proton uses this role to provision infrastructure resources in the associated environment account.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>An optional list of metadata items that you can associate with the Proton environment account connection. A tag is a key-value pair.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/resources.html\">Proton resources and tagging</a> in the <i>Proton User Guide</i>.</p>"}}}, "CreateEnvironmentAccountConnectionOutput": {"type": "structure", "required": ["environmentAccountConnection"], "members": {"environmentAccountConnection": {"shape": "EnvironmentAccountConnection", "documentation": "<p>The environment account connection detail data that's returned by Proton.</p>"}}}, "CreateEnvironmentInput": {"type": "structure", "required": ["name", "spec", "templateMajorVersion", "templateName"], "members": {"codebuildRoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM service role that allows Proton to provision infrastructure using CodeBuild-based provisioning on your behalf.</p> <p>To use CodeBuild-based provisioning for the environment or for any service instance running in the environment, specify either the <code>environmentAccountConnectionId</code> or <code>codebuildRoleArn</code> parameter.</p>"}, "componentRoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM service role that Proton uses when provisioning directly defined components in this environment. It determines the scope of infrastructure that a component can provision.</p> <p>You must specify <code>componentRoleArn</code> to allow directly defined components to be associated with this environment.</p> <p>For more information about components, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-components.html\">Proton components</a> in the <i>Proton User Guide</i>.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the environment that's being created and deployed.</p>"}, "environmentAccountConnectionId": {"shape": "EnvironmentAccountConnectionId", "documentation": "<p>The ID of the environment account connection that you provide if you're provisioning your environment infrastructure resources to an environment account. For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-env-account-connections.html\">Environment account connections</a> in the <i>Proton User guide</i>.</p> <p>To use Amazon Web Services-managed provisioning for the environment, specify either the <code>environmentAccountConnectionId</code> or <code>protonServiceRoleArn</code> parameter and omit the <code>provisioningRepository</code> parameter.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the environment.</p>"}, "protonServiceRoleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Proton service role that allows Proton to make calls to other services on your behalf.</p> <p>To use Amazon Web Services-managed provisioning for the environment, specify either the <code>environmentAccountConnectionId</code> or <code>protonServiceRoleArn</code> parameter and omit the <code>provisioningRepository</code> parameter.</p>"}, "provisioningRepository": {"shape": "RepositoryBranchInput", "documentation": "<p>The linked repository that you use to host your rendered infrastructure templates for self-managed provisioning. A linked repository is a repository that has been registered with Proton. For more information, see <a>CreateRepository</a>.</p> <p>To use self-managed provisioning for the environment, specify this parameter and omit the <code>environmentAccountConnectionId</code> and <code>protonServiceRoleArn</code> parameters.</p>"}, "spec": {"shape": "SpecContents", "documentation": "<p>A YAML formatted string that provides inputs as defined in the environment template bundle schema file. For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-environments.html\">Environments</a> in the <i>Proton User Guide</i>.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>An optional list of metadata items that you can associate with the Proton environment. A tag is a key-value pair.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/resources.html\">Proton resources and tagging</a> in the <i>Proton User Guide</i>.</p>"}, "templateMajorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The major version of the environment template.</p>"}, "templateMinorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The minor version of the environment template.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the environment template. For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-templates.html\">Environment Templates</a> in the <i>Proton User Guide</i>.</p>"}}}, "CreateEnvironmentOutput": {"type": "structure", "required": ["environment"], "members": {"environment": {"shape": "Environment", "documentation": "<p>The environment detail data that's returned by Proton.</p>"}}}, "CreateEnvironmentTemplateInput": {"type": "structure", "required": ["name"], "members": {"description": {"shape": "Description", "documentation": "<p>A description of the environment template.</p>"}, "displayName": {"shape": "DisplayName", "documentation": "<p>The environment template name as displayed in the developer interface.</p>"}, "encryptionKey": {"shape": "<PERSON><PERSON>", "documentation": "<p>A customer provided encryption key that Proton uses to encrypt data.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the environment template.</p>"}, "provisioning": {"shape": "Provisioning", "documentation": "<p>When included, indicates that the environment template is for customer provisioned and managed infrastructure.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>An optional list of metadata items that you can associate with the Proton environment template. A tag is a key-value pair.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/resources.html\">Proton resources and tagging</a> in the <i>Proton User Guide</i>.</p>"}}}, "CreateEnvironmentTemplateOutput": {"type": "structure", "required": ["environmentTemplate"], "members": {"environmentTemplate": {"shape": "EnvironmentTemplate", "documentation": "<p>The environment template detail data that's returned by Proton.</p>"}}}, "CreateEnvironmentTemplateVersionInput": {"type": "structure", "required": ["source", "templateName"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>When included, if two identical requests are made with the same client token, <PERSON><PERSON> returns the environment template version that the first request created.</p>", "idempotencyToken": true}, "description": {"shape": "Description", "documentation": "<p>A description of the new version of an environment template.</p>"}, "majorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>To create a new minor version of the environment template, include <code>major Version</code>.</p> <p>To create a new major and minor version of the environment template, exclude <code>major Version</code>.</p>"}, "source": {"shape": "TemplateVersionSourceInput", "documentation": "<p>An object that includes the template bundle S3 bucket path and name for the new version of an template.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>An optional list of metadata items that you can associate with the Proton environment template version. A tag is a key-value pair.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/resources.html\">Proton resources and tagging</a> in the <i>Proton User Guide</i>.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the environment template.</p>"}}}, "CreateEnvironmentTemplateVersionOutput": {"type": "structure", "required": ["environmentTemplateVersion"], "members": {"environmentTemplateVersion": {"shape": "EnvironmentTemplateVersion", "documentation": "<p>The environment template detail data that's returned by Proton.</p>"}}}, "CreateRepositoryInput": {"type": "structure", "required": ["connectionArn", "name", "provider"], "members": {"connectionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of your AWS CodeStar connection that connects Proton to your repository provider account. For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/setting-up-for-service.html\">Setting up for Proton</a> in the <i>Proton User Guide</i>.</p>"}, "encryptionKey": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of your customer Amazon Web Services Key Management Service (Amazon Web Services KMS) key.</p>"}, "name": {"shape": "RepositoryName", "documentation": "<p>The repository name (for example, <code>myrepos/myrepo</code>).</p>"}, "provider": {"shape": "RepositoryProvider", "documentation": "<p>The repository provider.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>An optional list of metadata items that you can associate with the Proton repository. A tag is a key-value pair.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/resources.html\">Proton resources and tagging</a> in the <i>Proton User Guide</i>.</p>"}}}, "CreateRepositoryOutput": {"type": "structure", "required": ["repository"], "members": {"repository": {"shape": "Repository", "documentation": "<p>The repository link's detail data that's returned by Proton.</p>"}}}, "CreateServiceInput": {"type": "structure", "required": ["name", "spec", "templateMajorVersion", "templateName"], "members": {"branchName": {"shape": "GitBranchName", "documentation": "<p>The name of the code repository branch that holds the code that's deployed in Proton. <i>Don't</i> include this parameter if your service template <i>doesn't</i> include a service pipeline.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the Proton service.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The service name.</p>"}, "repositoryConnectionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the repository connection. For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/setting-up-for-service.html#setting-up-vcontrol\">Setting up an AWS CodeStar connection</a> in the <i>Proton User Guide</i>. <i>Don't</i> include this parameter if your service template <i>doesn't</i> include a service pipeline.</p>"}, "repositoryId": {"shape": "RepositoryId", "documentation": "<p>The ID of the code repository. <i>Don't</i> include this parameter if your service template <i>doesn't</i> include a service pipeline.</p>"}, "spec": {"shape": "SpecContents", "documentation": "<p>A link to a spec file that provides inputs as defined in the service template bundle schema file. The spec file is in YAML format. <i>Don’t</i> include pipeline inputs in the spec if your service template <i>doesn’t</i> include a service pipeline. For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-create-svc.html\">Create a service</a> in the <i>Proton User Guide</i>.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>An optional list of metadata items that you can associate with the Proton service. A tag is a key-value pair.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/resources.html\">Proton resources and tagging</a> in the <i>Proton User Guide</i>.</p>"}, "templateMajorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The major version of the service template that was used to create the service.</p>"}, "templateMinorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The minor version of the service template that was used to create the service.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the service template that's used to create the service.</p>"}}}, "CreateServiceInstanceInput": {"type": "structure", "required": ["name", "serviceName", "spec"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>The client token of the service instance to create.</p>", "idempotencyToken": true}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the service instance to create.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service the service instance is added to.</p>"}, "spec": {"shape": "SpecContents", "documentation": "<p>The spec for the service instance you want to create.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>An optional list of metadata items that you can associate with the Proton service instance. A tag is a key-value pair.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/resources.html\">Proton resources and tagging</a> in the <i>Proton User Guide</i>.</p>"}, "templateMajorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>To create a new major and minor version of the service template, <i>exclude</i> <code>major Version</code>.</p>"}, "templateMinorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>To create a new minor version of the service template, include a <code>major Version</code>.</p>"}}}, "CreateServiceInstanceOutput": {"type": "structure", "required": ["serviceInstance"], "members": {"serviceInstance": {"shape": "ServiceInstance", "documentation": "<p>The detailed data of the service instance being created.</p>"}}}, "CreateServiceOutput": {"type": "structure", "required": ["service"], "members": {"service": {"shape": "Service", "documentation": "<p>The service detail data that's returned by Proton.</p>"}}}, "CreateServiceSyncConfigInput": {"type": "structure", "required": ["branch", "filePath", "repositoryName", "repositoryProvider", "serviceName"], "members": {"branch": {"shape": "GitBranchName", "documentation": "<p>The repository branch for your Proton Ops file.</p>"}, "filePath": {"shape": "OpsFilePath", "documentation": "<p>The path to the Proton Ops file.</p>"}, "repositoryName": {"shape": "RepositoryName", "documentation": "<p>The repository name.</p>"}, "repositoryProvider": {"shape": "RepositoryProvider", "documentation": "<p>The provider type for your repository.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service the Proton Ops file is for.</p>"}}}, "CreateServiceSyncConfigOutput": {"type": "structure", "members": {"serviceSyncConfig": {"shape": "ServiceSyncConfig", "documentation": "<p>The detailed data of the Proton Ops file.</p>"}}}, "CreateServiceTemplateInput": {"type": "structure", "required": ["name"], "members": {"description": {"shape": "Description", "documentation": "<p>A description of the service template.</p>"}, "displayName": {"shape": "DisplayName", "documentation": "<p>The name of the service template as displayed in the developer interface.</p>"}, "encryptionKey": {"shape": "<PERSON><PERSON>", "documentation": "<p>A customer provided encryption key that's used to encrypt data.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the service template.</p>"}, "pipelineProvisioning": {"shape": "Provisioning", "documentation": "<p>By default, Proton provides a service pipeline for your service. When this parameter is included, it indicates that an Proton service pipeline <i>isn't</i> provided for your service. After it's included, it <i>can't</i> be changed. For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-template-authoring.html#ag-template-bundles\">Template bundles</a> in the <i>Proton User Guide</i>.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>An optional list of metadata items that you can associate with the Proton service template. A tag is a key-value pair.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/resources.html\">Proton resources and tagging</a> in the <i>Proton User Guide</i>.</p>"}}}, "CreateServiceTemplateOutput": {"type": "structure", "required": ["serviceTemplate"], "members": {"serviceTemplate": {"shape": "ServiceTemplate", "documentation": "<p>The service template detail data that's returned by Proton.</p>"}}}, "CreateServiceTemplateVersionInput": {"type": "structure", "required": ["compatibleEnvironmentTemplates", "source", "templateName"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>When included, if two identical requests are made with the same client token, <PERSON>n returns the service template version that the first request created.</p>", "idempotencyToken": true}, "compatibleEnvironmentTemplates": {"shape": "CompatibleEnvironmentTemplateInputList", "documentation": "<p>An array of environment template objects that are compatible with the new service template version. A service instance based on this service template version can run in environments based on compatible templates.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the new version of a service template.</p>"}, "majorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>To create a new minor version of the service template, include a <code>major Version</code>.</p> <p>To create a new major and minor version of the service template, <i>exclude</i> <code>major Version</code>.</p>"}, "source": {"shape": "TemplateVersionSourceInput", "documentation": "<p>An object that includes the template bundle S3 bucket path and name for the new version of a service template.</p>"}, "supportedComponentSources": {"shape": "ServiceTemplateSupportedComponentSourceInputList", "documentation": "<p>An array of supported component sources. Components with supported sources can be attached to service instances based on this service template version.</p> <p>For more information about components, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-components.html\">Proton components</a> in the <i>Proton User Guide</i>.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>An optional list of metadata items that you can associate with the Proton service template version. A tag is a key-value pair.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/resources.html\">Proton resources and tagging</a> in the <i>Proton User Guide</i>.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the service template.</p>"}}}, "CreateServiceTemplateVersionOutput": {"type": "structure", "required": ["serviceTemplateVersion"], "members": {"serviceTemplateVersion": {"shape": "ServiceTemplateVersion", "documentation": "<p>The service template version summary of detail data that's returned by Proton.</p>"}}}, "CreateTemplateSyncConfigInput": {"type": "structure", "required": ["branch", "repositoryName", "repositoryProvider", "templateName", "templateType"], "members": {"branch": {"shape": "GitBranchName", "documentation": "<p>The repository branch for your template.</p>"}, "repositoryName": {"shape": "RepositoryName", "documentation": "<p>The repository name (for example, <code>myrepos/myrepo</code>).</p>"}, "repositoryProvider": {"shape": "RepositoryProvider", "documentation": "<p>The provider type for your repository.</p>"}, "subdirectory": {"shape": "Subdirectory", "documentation": "<p>A repository subdirectory path to your template bundle directory. When included, <PERSON><PERSON> limits the template bundle search to this repository directory.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of your registered template.</p>"}, "templateType": {"shape": "TemplateType", "documentation": "<p>The type of the registered template.</p>"}}}, "CreateTemplateSyncConfigOutput": {"type": "structure", "members": {"templateSyncConfig": {"shape": "TemplateSyncConfig", "documentation": "<p>The template sync configuration detail data that's returned by Proton.</p>"}}}, "DeleteComponentInput": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "ResourceName", "documentation": "<p>The name of the component to delete.</p>"}}}, "DeleteComponentOutput": {"type": "structure", "members": {"component": {"shape": "Component", "documentation": "<p>The detailed data of the component being deleted.</p>"}}}, "DeleteDeploymentInput": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "DeploymentId", "documentation": "<p>The ID of the deployment to delete.</p>"}}}, "DeleteDeploymentOutput": {"type": "structure", "members": {"deployment": {"shape": "Deployment", "documentation": "<p>The detailed data of the deployment being deleted.</p>"}}}, "DeleteEnvironmentAccountConnectionInput": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "EnvironmentAccountConnectionId", "documentation": "<p>The ID of the environment account connection to delete.</p>"}}}, "DeleteEnvironmentAccountConnectionOutput": {"type": "structure", "members": {"environmentAccountConnection": {"shape": "EnvironmentAccountConnection", "documentation": "<p>The detailed data of the environment account connection being deleted.</p>"}}}, "DeleteEnvironmentInput": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "ResourceName", "documentation": "<p>The name of the environment to delete.</p>"}}}, "DeleteEnvironmentOutput": {"type": "structure", "members": {"environment": {"shape": "Environment", "documentation": "<p>The detailed data of the environment being deleted.</p>"}}}, "DeleteEnvironmentTemplateInput": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "ResourceName", "documentation": "<p>The name of the environment template to delete.</p>"}}}, "DeleteEnvironmentTemplateOutput": {"type": "structure", "members": {"environmentTemplate": {"shape": "EnvironmentTemplate", "documentation": "<p>The detailed data of the environment template being deleted.</p>"}}}, "DeleteEnvironmentTemplateVersionInput": {"type": "structure", "required": ["majorVersion", "minorVersion", "templateName"], "members": {"majorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The environment template major version to delete.</p>"}, "minorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The environment template minor version to delete.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the environment template.</p>"}}}, "DeleteEnvironmentTemplateVersionOutput": {"type": "structure", "members": {"environmentTemplateVersion": {"shape": "EnvironmentTemplateVersion", "documentation": "<p>The detailed data of the environment template version being deleted.</p>"}}}, "DeleteRepositoryInput": {"type": "structure", "required": ["name", "provider"], "members": {"name": {"shape": "RepositoryName", "documentation": "<p>The repository name.</p>"}, "provider": {"shape": "RepositoryProvider", "documentation": "<p>The repository provider.</p>"}}}, "DeleteRepositoryOutput": {"type": "structure", "members": {"repository": {"shape": "Repository", "documentation": "<p>The deleted repository link's detail data that's returned by Proton.</p>"}}}, "DeleteServiceInput": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "ResourceName", "documentation": "<p>The name of the service to delete.</p>"}}}, "DeleteServiceOutput": {"type": "structure", "members": {"service": {"shape": "Service", "documentation": "<p>The detailed data of the service being deleted.</p>"}}}, "DeleteServiceSyncConfigInput": {"type": "structure", "required": ["serviceName"], "members": {"serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service that you want to delete the service sync configuration for.</p>"}}}, "DeleteServiceSyncConfigOutput": {"type": "structure", "members": {"serviceSyncConfig": {"shape": "ServiceSyncConfig", "documentation": "<p>The detailed data for the service sync config.</p>"}}}, "DeleteServiceTemplateInput": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "ResourceName", "documentation": "<p>The name of the service template to delete.</p>"}}}, "DeleteServiceTemplateOutput": {"type": "structure", "members": {"serviceTemplate": {"shape": "ServiceTemplate", "documentation": "<p>The detailed data of the service template being deleted.</p>"}}}, "DeleteServiceTemplateVersionInput": {"type": "structure", "required": ["majorVersion", "minorVersion", "templateName"], "members": {"majorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The service template major version to delete.</p>"}, "minorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The service template minor version to delete.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the service template.</p>"}}}, "DeleteServiceTemplateVersionOutput": {"type": "structure", "members": {"serviceTemplateVersion": {"shape": "ServiceTemplateVersion", "documentation": "<p>The detailed data of the service template version being deleted.</p>"}}}, "DeleteTemplateSyncConfigInput": {"type": "structure", "required": ["templateName", "templateType"], "members": {"templateName": {"shape": "ResourceName", "documentation": "<p>The template name.</p>"}, "templateType": {"shape": "TemplateType", "documentation": "<p>The template type.</p>"}}}, "DeleteTemplateSyncConfigOutput": {"type": "structure", "members": {"templateSyncConfig": {"shape": "TemplateSyncConfig", "documentation": "<p>The template sync configuration detail data that's returned by Proton.</p>"}}}, "Deployment": {"type": "structure", "required": ["arn", "createdAt", "deploymentStatus", "environmentName", "id", "lastModifiedAt", "targetArn", "targetResourceCreatedAt", "targetResourceType"], "members": {"arn": {"shape": "DeploymentArn", "documentation": "<p>The Amazon Resource Name (ARN) of the deployment.</p>"}, "completedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the deployment was completed.</p>"}, "componentName": {"shape": "ResourceName", "documentation": "<p>The name of the component associated with this deployment.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The date and time the deployment was created.</p>"}, "deploymentStatus": {"shape": "DeploymentStatus", "documentation": "<p>The status of the deployment.</p>"}, "deploymentStatusMessage": {"shape": "StatusMessage", "documentation": "<p>The deployment status message.</p>"}, "environmentName": {"shape": "ResourceName", "documentation": "<p>The name of the environment associated with this deployment.</p>"}, "id": {"shape": "DeploymentId", "documentation": "<p>The ID of the deployment.</p>"}, "initialState": {"shape": "DeploymentState", "documentation": "<p>The initial state of the target resource at the time of the deployment.</p>"}, "lastAttemptedDeploymentId": {"shape": "DeploymentId", "documentation": "<p>The ID of the last attempted deployment.</p>"}, "lastModifiedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the deployment was last modified.</p>"}, "lastSucceededDeploymentId": {"shape": "DeploymentId", "documentation": "<p>The ID of the last successful deployment.</p>"}, "serviceInstanceName": {"shape": "ResourceName", "documentation": "<p>The name of the deployment's service instance.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service in this deployment.</p>"}, "targetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the target of the deployment.</p>"}, "targetResourceCreatedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the depoyment target was created.</p>"}, "targetResourceType": {"shape": "DeploymentTargetResourceType", "documentation": "<p>The resource type of the deployment target. It can be an environment, service, service instance, or component.</p>"}, "targetState": {"shape": "DeploymentState", "documentation": "<p>The target state of the target resource at the time of the deployment.</p>"}}, "documentation": "<p>The detailed information about a deployment.</p>"}, "DeploymentArn": {"type": "string"}, "DeploymentId": {"type": "string", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"}, "DeploymentState": {"type": "structure", "members": {"component": {"shape": "ComponentState", "documentation": "<p>The state of the component associated with the deployment.</p>"}, "environment": {"shape": "EnvironmentState", "documentation": "<p>The state of the environment associated with the deployment.</p>"}, "serviceInstance": {"shape": "ServiceInstanceState", "documentation": "<p>The state of the service instance associated with the deployment.</p>"}, "servicePipeline": {"shape": "ServicePipelineState", "documentation": "<p>The state of the service pipeline associated with the deployment.</p>"}}, "documentation": "<p>The detailed data about the current state of the deployment.</p>", "union": true}, "DeploymentStatus": {"type": "string", "enum": ["IN_PROGRESS", "FAILED", "SUCCEEDED", "DELETE_IN_PROGRESS", "DELETE_FAILED", "DELETE_COMPLETE", "CANCELLING", "CANCELLED"]}, "DeploymentSummary": {"type": "structure", "required": ["arn", "createdAt", "deploymentStatus", "environmentName", "id", "lastModifiedAt", "targetArn", "targetResourceCreatedAt", "targetResourceType"], "members": {"arn": {"shape": "DeploymentArn", "documentation": "<p>The Amazon Resource Name (ARN) of the deployment.</p>"}, "completedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the deployment was completed.</p>"}, "componentName": {"shape": "ResourceName", "documentation": "<p>The name of the component associated with the deployment.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The date and time the deployment was created.</p>"}, "deploymentStatus": {"shape": "DeploymentStatus", "documentation": "<p>The current status of the deployment.</p>"}, "environmentName": {"shape": "ResourceName", "documentation": "<p>The name of the environment associated with the deployment.</p>"}, "id": {"shape": "DeploymentId", "documentation": "<p>The ID of the deployment.</p>"}, "lastAttemptedDeploymentId": {"shape": "DeploymentId", "documentation": "<p>The ID of the last attempted deployment.</p>"}, "lastModifiedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the deployment was last modified.</p>"}, "lastSucceededDeploymentId": {"shape": "DeploymentId", "documentation": "<p>The ID of the last successful deployment.</p>"}, "serviceInstanceName": {"shape": "ResourceName", "documentation": "<p>The name of the service instance associated with the deployment.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service associated with the deployment.</p>"}, "targetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the target of the deployment.</p>"}, "targetResourceCreatedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the target resource was created.</p>"}, "targetResourceType": {"shape": "DeploymentTargetResourceType", "documentation": "<p>The resource type of the deployment target. It can be an environment, service, service instance, or component.</p>"}}, "documentation": "<p>Summary data of the deployment.</p>"}, "DeploymentSummaryList": {"type": "list", "member": {"shape": "DeploymentSummary"}}, "DeploymentTargetResourceType": {"type": "string", "enum": ["ENVIRONMENT", "SERVICE_PIPELINE", "SERVICE_INSTANCE", "COMPONENT"]}, "DeploymentUpdateType": {"type": "string", "enum": ["NONE", "CURRENT_VERSION", "MINOR_VERSION", "MAJOR_VERSION"]}, "Description": {"type": "string", "max": 500, "min": 0, "sensitive": true}, "DisplayName": {"type": "string", "max": 100, "min": 1, "sensitive": true}, "EmptyNextToken": {"type": "string", "max": 0, "min": 0}, "Environment": {"type": "structure", "required": ["arn", "createdAt", "deploymentStatus", "lastDeploymentAttemptedAt", "lastDeploymentSucceededAt", "name", "templateMajorVersion", "templateMinorVersion", "templateName"], "members": {"arn": {"shape": "EnvironmentArn", "documentation": "<p>The Amazon Resource Name (ARN) of the environment.</p>"}, "codebuildRoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM service role that allows Proton to provision infrastructure using CodeBuild-based provisioning on your behalf.</p>"}, "componentRoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM service role that Proton uses when provisioning directly defined components in this environment. It determines the scope of infrastructure that a component can provision.</p> <p>The environment must have a <code>componentRoleArn</code> to allow directly defined components to be associated with the environment.</p> <p>For more information about components, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-components.html\">Proton components</a> in the <i>Proton User Guide</i>.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the environment was created.</p>"}, "deploymentStatus": {"shape": "DeploymentStatus", "documentation": "<p>The environment deployment status.</p>"}, "deploymentStatusMessage": {"shape": "StatusMessage", "documentation": "<p>An environment deployment status message.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the environment.</p>"}, "environmentAccountConnectionId": {"shape": "EnvironmentAccountConnectionId", "documentation": "<p>The ID of the environment account connection that's used to provision infrastructure resources in an environment account.</p>"}, "environmentAccountId": {"shape": "AwsAccountId", "documentation": "<p>The ID of the environment account that the environment infrastructure resources are provisioned in.</p>"}, "lastAttemptedDeploymentId": {"shape": "DeploymentId", "documentation": "<p>The ID of the last attempted deployment of this environment.</p>"}, "lastDeploymentAttemptedAt": {"shape": "Timestamp", "documentation": "<p>The time when a deployment of the environment was last attempted.</p>"}, "lastDeploymentSucceededAt": {"shape": "Timestamp", "documentation": "<p>The time when the environment was last deployed successfully.</p>"}, "lastSucceededDeploymentId": {"shape": "DeploymentId", "documentation": "<p>The ID of the last successful deployment of this environment.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the environment.</p>"}, "protonServiceRoleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Proton service role that allows Proton to make calls to other services on your behalf.</p>"}, "provisioning": {"shape": "Provisioning", "documentation": "<p>When included, indicates that the environment template is for customer provisioned and managed infrastructure.</p>"}, "provisioningRepository": {"shape": "RepositoryBranch", "documentation": "<p>The linked repository that you use to host your rendered infrastructure templates for self-managed provisioning. A linked repository is a repository that has been registered with Proton. For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/APIReference/API_CreateRepository.html\">CreateRepository</a>.</p>"}, "spec": {"shape": "SpecContents", "documentation": "<p>The environment spec.</p>"}, "templateMajorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The major version of the environment template.</p>"}, "templateMinorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The minor version of the environment template.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The Amazon Resource Name (ARN) of the environment template.</p>"}}, "documentation": "<p>Detailed data of an Proton environment resource. An Proton environment is a set of resources shared across Proton services.</p>"}, "EnvironmentAccountConnection": {"type": "structure", "required": ["arn", "environmentAccountId", "environmentName", "id", "lastModifiedAt", "managementAccountId", "requestedAt", "roleArn", "status"], "members": {"arn": {"shape": "EnvironmentAccountConnectionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the environment account connection.</p>"}, "codebuildRoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of an IAM service role in the environment account. Proton uses this role to provision infrastructure resources using CodeBuild-based provisioning in the associated environment account.</p>"}, "componentRoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM service role that Proton uses when provisioning directly defined components in the associated environment account. It determines the scope of infrastructure that a component can provision in the account.</p> <p>The environment account connection must have a <code>componentRoleArn</code> to allow directly defined components to be associated with any environments running in the account.</p> <p>For more information about components, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-components.html\">Proton components</a> in the <i>Proton User Guide</i>.</p>"}, "environmentAccountId": {"shape": "AwsAccountId", "documentation": "<p>The environment account that's connected to the environment account connection.</p>"}, "environmentName": {"shape": "ResourceName", "documentation": "<p>The name of the environment that's associated with the environment account connection.</p>"}, "id": {"shape": "EnvironmentAccountConnectionId", "documentation": "<p>The ID of the environment account connection.</p>"}, "lastModifiedAt": {"shape": "Timestamp", "documentation": "<p>The time when the environment account connection was last modified.</p>"}, "managementAccountId": {"shape": "AwsAccountId", "documentation": "<p>The ID of the management account that's connected to the environment account connection.</p>"}, "requestedAt": {"shape": "Timestamp", "documentation": "<p>The time when the environment account connection request was made.</p>"}, "roleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The IAM service role that's associated with the environment account connection.</p>"}, "status": {"shape": "EnvironmentAccountConnectionStatus", "documentation": "<p>The status of the environment account connection.</p>"}}, "documentation": "<p>Detailed data of an Proton environment account connection resource.</p>"}, "EnvironmentAccountConnectionArn": {"type": "string"}, "EnvironmentAccountConnectionId": {"type": "string", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"}, "EnvironmentAccountConnectionRequesterAccountType": {"type": "string", "enum": ["MANAGEMENT_ACCOUNT", "ENVIRONMENT_ACCOUNT"]}, "EnvironmentAccountConnectionStatus": {"type": "string", "enum": ["PENDING", "CONNECTED", "REJECTED"]}, "EnvironmentAccountConnectionStatusList": {"type": "list", "member": {"shape": "EnvironmentAccountConnectionStatus"}}, "EnvironmentAccountConnectionSummary": {"type": "structure", "required": ["arn", "environmentAccountId", "environmentName", "id", "lastModifiedAt", "managementAccountId", "requestedAt", "roleArn", "status"], "members": {"arn": {"shape": "EnvironmentAccountConnectionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the environment account connection.</p>"}, "componentRoleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM service role that Proton uses when provisioning directly defined components in the associated environment account. It determines the scope of infrastructure that a component can provision in the account.</p> <p>The environment account connection must have a <code>componentRoleArn</code> to allow directly defined components to be associated with any environments running in the account.</p> <p>For more information about components, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-components.html\">Proton components</a> in the <i>Proton User Guide</i>.</p>"}, "environmentAccountId": {"shape": "AwsAccountId", "documentation": "<p>The ID of the environment account that's connected to the environment account connection.</p>"}, "environmentName": {"shape": "ResourceName", "documentation": "<p>The name of the environment that's associated with the environment account connection.</p>"}, "id": {"shape": "EnvironmentAccountConnectionId", "documentation": "<p>The ID of the environment account connection.</p>"}, "lastModifiedAt": {"shape": "Timestamp", "documentation": "<p>The time when the environment account connection was last modified.</p>"}, "managementAccountId": {"shape": "AwsAccountId", "documentation": "<p>The ID of the management account that's connected to the environment account connection.</p>"}, "requestedAt": {"shape": "Timestamp", "documentation": "<p>The time when the environment account connection request was made.</p>"}, "roleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The IAM service role that's associated with the environment account connection.</p>"}, "status": {"shape": "EnvironmentAccountConnectionStatus", "documentation": "<p>The status of the environment account connection.</p>"}}, "documentation": "<p>Summary data of an Proton environment account connection resource.</p>"}, "EnvironmentAccountConnectionSummaryList": {"type": "list", "member": {"shape": "EnvironmentAccountConnectionSummary"}}, "EnvironmentArn": {"type": "string"}, "EnvironmentState": {"type": "structure", "required": ["templateMajorVersion", "templateMinorVersion", "templateName"], "members": {"spec": {"shape": "SpecContents", "documentation": "<p>The environment spec that was used to create the environment.</p>"}, "templateMajorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The major version of the environment template that was used to create the environment.</p>"}, "templateMinorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The minor version of the environment template that was used to create the environment.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the environment template that was used to create the environment.</p>"}}, "documentation": "<p>The detailed data about the current state of the environment.</p>"}, "EnvironmentSummary": {"type": "structure", "required": ["arn", "createdAt", "deploymentStatus", "lastDeploymentAttemptedAt", "lastDeploymentSucceededAt", "name", "templateMajorVersion", "templateMinorVersion", "templateName"], "members": {"arn": {"shape": "EnvironmentArn", "documentation": "<p>The Amazon Resource Name (ARN) of the environment.</p>"}, "componentRoleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM service role that Proton uses when provisioning directly defined components in this environment. It determines the scope of infrastructure that a component can provision.</p> <p>The environment must have a <code>componentRoleArn</code> to allow directly defined components to be associated with the environment.</p> <p>For more information about components, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-components.html\">Proton components</a> in the <i>Proton User Guide</i>.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the environment was created.</p>"}, "deploymentStatus": {"shape": "DeploymentStatus", "documentation": "<p>The environment deployment status.</p>"}, "deploymentStatusMessage": {"shape": "StatusMessage", "documentation": "<p>An environment deployment status message.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the environment.</p>"}, "environmentAccountConnectionId": {"shape": "EnvironmentAccountConnectionId", "documentation": "<p>The ID of the environment account connection that the environment is associated with.</p>"}, "environmentAccountId": {"shape": "AwsAccountId", "documentation": "<p>The ID of the environment account that the environment infrastructure resources are provisioned in.</p>"}, "lastAttemptedDeploymentId": {"shape": "DeploymentId", "documentation": "<p>The ID of the last attempted deployment of this environment.</p>"}, "lastDeploymentAttemptedAt": {"shape": "Timestamp", "documentation": "<p>The time when a deployment of the environment was last attempted.</p>"}, "lastDeploymentSucceededAt": {"shape": "Timestamp", "documentation": "<p>The time when the environment was last deployed successfully.</p>"}, "lastSucceededDeploymentId": {"shape": "DeploymentId", "documentation": "<p>The ID of the last successful deployment of this environment.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the environment.</p>"}, "protonServiceRoleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Proton service role that allows Proton to make calls to other services on your behalf.</p>"}, "provisioning": {"shape": "Provisioning", "documentation": "<p>When included, indicates that the environment template is for customer provisioned and managed infrastructure.</p>"}, "templateMajorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The major version of the environment template.</p>"}, "templateMinorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The minor version of the environment template.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the environment template.</p>"}}, "documentation": "<p>Summary data of an Proton environment resource. An Proton environment is a set of resources shared across Proton services.</p>"}, "EnvironmentSummaryList": {"type": "list", "member": {"shape": "EnvironmentSummary"}}, "EnvironmentTemplate": {"type": "structure", "required": ["arn", "createdAt", "lastModifiedAt", "name"], "members": {"arn": {"shape": "EnvironmentTemplateArn", "documentation": "<p>The Amazon Resource Name (ARN) of the environment template.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the environment template was created.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the environment template.</p>"}, "displayName": {"shape": "DisplayName", "documentation": "<p>The name of the environment template as displayed in the developer interface.</p>"}, "encryptionKey": {"shape": "<PERSON><PERSON>", "documentation": "<p>The customer provided encryption key for the environment template.</p>"}, "lastModifiedAt": {"shape": "Timestamp", "documentation": "<p>The time when the environment template was last modified.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the environment template.</p>"}, "provisioning": {"shape": "Provisioning", "documentation": "<p>When included, indicates that the environment template is for customer provisioned and managed infrastructure.</p>"}, "recommendedVersion": {"shape": "FullTemplateVersionNumber", "documentation": "<p>The ID of the recommended version of the environment template.</p>"}}, "documentation": "<p>The environment template data.</p>"}, "EnvironmentTemplateArn": {"type": "string"}, "EnvironmentTemplateFilter": {"type": "structure", "required": ["majorVersion", "templateName"], "members": {"majorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>Include <code>majorVersion</code> to filter search for a major version.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>Include <code>templateName</code> to filter search for a template name.</p>"}}, "documentation": "<p>A search filter for environment templates.</p>"}, "EnvironmentTemplateFilterList": {"type": "list", "member": {"shape": "EnvironmentTemplateFilter"}}, "EnvironmentTemplateSummary": {"type": "structure", "required": ["arn", "createdAt", "lastModifiedAt", "name"], "members": {"arn": {"shape": "EnvironmentTemplateArn", "documentation": "<p>The Amazon Resource Name (ARN) of the environment template.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the environment template was created.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the environment template.</p>"}, "displayName": {"shape": "DisplayName", "documentation": "<p>The name of the environment template as displayed in the developer interface.</p>"}, "lastModifiedAt": {"shape": "Timestamp", "documentation": "<p>The time when the environment template was last modified.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the environment template.</p>"}, "provisioning": {"shape": "Provisioning", "documentation": "<p>When included, indicates that the environment template is for customer provisioned and managed infrastructure.</p>"}, "recommendedVersion": {"shape": "FullTemplateVersionNumber", "documentation": "<p>The recommended version of the environment template.</p>"}}, "documentation": "<p>The environment template data.</p>"}, "EnvironmentTemplateSummaryList": {"type": "list", "member": {"shape": "EnvironmentTemplateSummary"}}, "EnvironmentTemplateVersion": {"type": "structure", "required": ["arn", "createdAt", "lastModifiedAt", "majorVersion", "minorVersion", "status", "templateName"], "members": {"arn": {"shape": "EnvironmentTemplateVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the version of an environment template.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the version of an environment template was created.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the minor version of an environment template.</p>"}, "lastModifiedAt": {"shape": "Timestamp", "documentation": "<p>The time when the version of an environment template was last modified.</p>"}, "majorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The latest major version that's associated with the version of an environment template.</p>"}, "minorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The minor version of an environment template.</p>"}, "recommendedMinorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The recommended minor version of the environment template.</p>"}, "schema": {"shape": "TemplateSchema", "documentation": "<p>The schema of the version of an environment template.</p>"}, "status": {"shape": "TemplateVersionStatus", "documentation": "<p>The status of the version of an environment template.</p>"}, "statusMessage": {"shape": "StatusMessage", "documentation": "<p>The status message of the version of an environment template.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the version of an environment template.</p>"}}, "documentation": "<p>The environment template version data.</p>"}, "EnvironmentTemplateVersionArn": {"type": "string"}, "EnvironmentTemplateVersionSummary": {"type": "structure", "required": ["arn", "createdAt", "lastModifiedAt", "majorVersion", "minorVersion", "status", "templateName"], "members": {"arn": {"shape": "EnvironmentTemplateVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the version of an environment template.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the version of an environment template was created.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the version of an environment template.</p>"}, "lastModifiedAt": {"shape": "Timestamp", "documentation": "<p>The time when the version of an environment template was last modified.</p>"}, "majorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The latest major version that's associated with the version of an environment template.</p>"}, "minorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The version of an environment template.</p>"}, "recommendedMinorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The recommended minor version of the environment template.</p>"}, "status": {"shape": "TemplateVersionStatus", "documentation": "<p>The status of the version of an environment template.</p>"}, "statusMessage": {"shape": "StatusMessage", "documentation": "<p>The status message of the version of an environment template.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the environment template.</p>"}}, "documentation": "<p>A summary of the version of an environment template detail data.</p>"}, "EnvironmentTemplateVersionSummaryList": {"type": "list", "member": {"shape": "EnvironmentTemplateVersionSummary"}}, "ErrorMessage": {"type": "string", "sensitive": true}, "FullTemplateVersionNumber": {"type": "string", "max": 10, "min": 1, "pattern": "^(0|([1-9]{1}\\d*)).(0|([1-9]{1}\\d*))$"}, "GetAccountSettingsInput": {"type": "structure", "members": {}}, "GetAccountSettingsOutput": {"type": "structure", "members": {"accountSettings": {"shape": "AccountSettings", "documentation": "<p>The Proton pipeline service role detail data that's returned by Proton.</p>"}}}, "GetComponentInput": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "ResourceName", "documentation": "<p>The name of the component that you want to get the detailed data for.</p>"}}}, "GetComponentOutput": {"type": "structure", "members": {"component": {"shape": "Component", "documentation": "<p>The detailed data of the requested component.</p>"}}}, "GetDeploymentInput": {"type": "structure", "required": ["id"], "members": {"componentName": {"shape": "ResourceName", "documentation": "<p>The name of a component that you want to get the detailed data for.</p>"}, "environmentName": {"shape": "ResourceName", "documentation": "<p>The name of a environment that you want to get the detailed data for.</p>"}, "id": {"shape": "DeploymentId", "documentation": "<p>The ID of the deployment that you want to get the detailed data for.</p>"}, "serviceInstanceName": {"shape": "ResourceName", "documentation": "<p>The name of the service instance associated with the given deployment ID. <code>serviceName</code> must be specified to identify the service instance.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service associated with the given deployment ID.</p>"}}}, "GetDeploymentOutput": {"type": "structure", "members": {"deployment": {"shape": "Deployment", "documentation": "<p>The detailed data of the requested deployment.</p>"}}}, "GetEnvironmentAccountConnectionInput": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "EnvironmentAccountConnectionId", "documentation": "<p>The ID of the environment account connection that you want to get the detailed data for.</p>"}}}, "GetEnvironmentAccountConnectionOutput": {"type": "structure", "required": ["environmentAccountConnection"], "members": {"environmentAccountConnection": {"shape": "EnvironmentAccountConnection", "documentation": "<p>The detailed data of the requested environment account connection.</p>"}}}, "GetEnvironmentInput": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "ResourceName", "documentation": "<p>The name of the environment that you want to get the detailed data for.</p>"}}}, "GetEnvironmentOutput": {"type": "structure", "required": ["environment"], "members": {"environment": {"shape": "Environment", "documentation": "<p>The detailed data of the requested environment.</p>"}}}, "GetEnvironmentTemplateInput": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "ResourceName", "documentation": "<p>The name of the environment template that you want to get the detailed data for.</p>"}}}, "GetEnvironmentTemplateOutput": {"type": "structure", "required": ["environmentTemplate"], "members": {"environmentTemplate": {"shape": "EnvironmentTemplate", "documentation": "<p>The detailed data of the requested environment template.</p>"}}}, "GetEnvironmentTemplateVersionInput": {"type": "structure", "required": ["majorVersion", "minorVersion", "templateName"], "members": {"majorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>To get environment template major version detail data, include <code>major Version</code>.</p>"}, "minorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>To get environment template minor version detail data, include <code>minorVersion</code>.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the environment template a version of which you want to get detailed data for.</p>"}}}, "GetEnvironmentTemplateVersionOutput": {"type": "structure", "required": ["environmentTemplateVersion"], "members": {"environmentTemplateVersion": {"shape": "EnvironmentTemplateVersion", "documentation": "<p>The detailed data of the requested environment template version.</p>"}}}, "GetRepositoryInput": {"type": "structure", "required": ["name", "provider"], "members": {"name": {"shape": "RepositoryName", "documentation": "<p>The repository name, for example <code>myrepos/myrepo</code>.</p>"}, "provider": {"shape": "RepositoryProvider", "documentation": "<p>The repository provider.</p>"}}}, "GetRepositoryOutput": {"type": "structure", "required": ["repository"], "members": {"repository": {"shape": "Repository", "documentation": "<p>The repository link's detail data that's returned by Proton.</p>"}}}, "GetRepositorySyncStatusInput": {"type": "structure", "required": ["branch", "repositoryName", "repositoryProvider", "syncType"], "members": {"branch": {"shape": "GitBranchName", "documentation": "<p>The repository branch.</p>"}, "repositoryName": {"shape": "RepositoryName", "documentation": "<p>The repository name.</p>"}, "repositoryProvider": {"shape": "RepositoryProvider", "documentation": "<p>The repository provider.</p>"}, "syncType": {"shape": "SyncType", "documentation": "<p>The repository sync type.</p>"}}}, "GetRepositorySyncStatusOutput": {"type": "structure", "members": {"latestSync": {"shape": "RepositorySyncAttempt", "documentation": "<p>The repository sync status detail data that's returned by Proton.</p>"}}}, "GetResourcesSummaryInput": {"type": "structure", "members": {}}, "GetResourcesSummaryOutput": {"type": "structure", "required": ["counts"], "members": {"counts": {"shape": "CountsSummary", "documentation": "<p>Summary counts of each Proton resource type.</p>"}}}, "GetServiceInput": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "ResourceName", "documentation": "<p>The name of the service that you want to get the detailed data for.</p>"}}}, "GetServiceInstanceInput": {"type": "structure", "required": ["name", "serviceName"], "members": {"name": {"shape": "ResourceName", "documentation": "<p>The name of a service instance that you want to get the detailed data for.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service that you want the service instance input for.</p>"}}}, "GetServiceInstanceOutput": {"type": "structure", "required": ["serviceInstance"], "members": {"serviceInstance": {"shape": "ServiceInstance", "documentation": "<p>The detailed data of the requested service instance.</p>"}}}, "GetServiceInstanceSyncStatusInput": {"type": "structure", "required": ["serviceInstanceName", "serviceName"], "members": {"serviceInstanceName": {"shape": "ResourceName", "documentation": "<p>The name of the service instance that you want the sync status input for.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service that the service instance belongs to.</p>"}}}, "GetServiceInstanceSyncStatusOutput": {"type": "structure", "members": {"desiredState": {"shape": "Revision", "documentation": "<p>The service instance sync desired state that's returned by Proton</p>"}, "latestSuccessfulSync": {"shape": "ResourceSyncAttempt", "documentation": "<p>The detailed data of the latest successful sync with the service instance.</p>"}, "latestSync": {"shape": "ResourceSyncAttempt", "documentation": "<p>The detailed data of the latest sync with the service instance.</p>"}}}, "GetServiceOutput": {"type": "structure", "members": {"service": {"shape": "Service", "documentation": "<p>The detailed data of the requested service.</p>"}}}, "GetServiceSyncBlockerSummaryInput": {"type": "structure", "required": ["serviceName"], "members": {"serviceInstanceName": {"shape": "ResourceName", "documentation": "<p>The name of the service instance that you want to get the service sync blocker summary for. If given bothe the instance name and the service name, only the instance is blocked.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service that you want to get the service sync blocker summary for. If given only the service name, all instances are blocked.</p>"}}}, "GetServiceSyncBlockerSummaryOutput": {"type": "structure", "members": {"serviceSyncBlockerSummary": {"shape": "ServiceSyncBlockerSummary", "documentation": "<p>The detailed data of the requested service sync blocker summary.</p>"}}}, "GetServiceSyncConfigInput": {"type": "structure", "required": ["serviceName"], "members": {"serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service that you want to get the service sync configuration for.</p>"}}}, "GetServiceSyncConfigOutput": {"type": "structure", "members": {"serviceSyncConfig": {"shape": "ServiceSyncConfig", "documentation": "<p>The detailed data of the requested service sync configuration.</p>"}}}, "GetServiceTemplateInput": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "ResourceName", "documentation": "<p>The name of the service template that you want to get detailed data for.</p>"}}}, "GetServiceTemplateOutput": {"type": "structure", "required": ["serviceTemplate"], "members": {"serviceTemplate": {"shape": "ServiceTemplate", "documentation": "<p>The detailed data of the requested service template.</p>"}}}, "GetServiceTemplateVersionInput": {"type": "structure", "required": ["majorVersion", "minorVersion", "templateName"], "members": {"majorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>To get service template major version detail data, include <code>major Version</code>.</p>"}, "minorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>To get service template minor version detail data, include <code>minorVersion</code>.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the service template a version of which you want to get detailed data for.</p>"}}}, "GetServiceTemplateVersionOutput": {"type": "structure", "required": ["serviceTemplateVersion"], "members": {"serviceTemplateVersion": {"shape": "ServiceTemplateVersion", "documentation": "<p>The detailed data of the requested service template version.</p>"}}}, "GetTemplateSyncConfigInput": {"type": "structure", "required": ["templateName", "templateType"], "members": {"templateName": {"shape": "ResourceName", "documentation": "<p>The template name.</p>"}, "templateType": {"shape": "TemplateType", "documentation": "<p>The template type.</p>"}}}, "GetTemplateSyncConfigOutput": {"type": "structure", "members": {"templateSyncConfig": {"shape": "TemplateSyncConfig", "documentation": "<p>The template sync configuration detail data that's returned by Proton.</p>"}}}, "GetTemplateSyncStatusInput": {"type": "structure", "required": ["templateName", "templateType", "templateVersion"], "members": {"templateName": {"shape": "ResourceName", "documentation": "<p>The template name.</p>"}, "templateType": {"shape": "TemplateType", "documentation": "<p>The template type.</p>"}, "templateVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The template major version.</p>"}}}, "GetTemplateSyncStatusOutput": {"type": "structure", "members": {"desiredState": {"shape": "Revision", "documentation": "<p>The template sync desired state that's returned by Proton.</p>"}, "latestSuccessfulSync": {"shape": "ResourceSyncAttempt", "documentation": "<p>The details of the last successful sync that's returned by Proton.</p>"}, "latestSync": {"shape": "ResourceSyncAttempt", "documentation": "<p>The details of the last sync that's returned by Proton.</p>"}}}, "GitBranchName": {"type": "string", "max": 200, "min": 1}, "Integer": {"type": "integer", "box": true}, "InternalServerException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The request failed to register with the service.</p>", "exception": true, "fault": true, "retryable": {"throttling": false}}, "LatestSyncBlockers": {"type": "list", "member": {"shape": "SyncBlocker"}}, "ListComponentOutputsInput": {"type": "structure", "required": ["componentName"], "members": {"componentName": {"shape": "ResourceName", "documentation": "<p>The name of the component whose outputs you want.</p>"}, "deploymentId": {"shape": "DeploymentId", "documentation": "<p>The ID of the deployment whose outputs you want.</p>"}, "nextToken": {"shape": "EmptyNextToken", "documentation": "<p>A token that indicates the location of the next output in the array of outputs, after the list of outputs that was previously requested.</p>"}}}, "ListComponentOutputsOutput": {"type": "structure", "required": ["outputs"], "members": {"nextToken": {"shape": "EmptyNextToken", "documentation": "<p>A token that indicates the location of the next output in the array of outputs, after the list of outputs that was previously requested.</p>"}, "outputs": {"shape": "OutputsList", "documentation": "<p>An array of component Infrastructure as Code (IaC) outputs.</p>"}}}, "ListComponentProvisionedResourcesInput": {"type": "structure", "required": ["componentName"], "members": {"componentName": {"shape": "ResourceName", "documentation": "<p>The name of the component whose provisioned resources you want.</p>"}, "nextToken": {"shape": "EmptyNextToken", "documentation": "<p>A token that indicates the location of the next provisioned resource in the array of provisioned resources, after the list of provisioned resources that was previously requested.</p>"}}}, "ListComponentProvisionedResourcesOutput": {"type": "structure", "required": ["provisionedResources"], "members": {"nextToken": {"shape": "EmptyNextToken", "documentation": "<p>A token that indicates the location of the next provisioned resource in the array of provisioned resources, after the current requested list of provisioned resources.</p>"}, "provisionedResources": {"shape": "ProvisionedResourceList", "documentation": "<p>An array of provisioned resources for a component.</p>"}}}, "ListComponentsInput": {"type": "structure", "members": {"environmentName": {"shape": "ResourceName", "documentation": "<p>The name of an environment for result list filtering. Proton returns components associated with the environment or attached to service instances running in it.</p>"}, "maxResults": {"shape": "MaxPageResults", "documentation": "<p>The maximum number of components to list.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates the location of the next component in the array of components, after the list of components that was previously requested.</p>"}, "serviceInstanceName": {"shape": "ResourceName", "documentation": "<p>The name of a service instance for result list filtering. Proton returns the component attached to the service instance, if any.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of a service for result list filtering. Proton returns components attached to service instances of the service.</p>"}}}, "ListComponentsOutput": {"type": "structure", "required": ["components"], "members": {"components": {"shape": "ComponentSummaryList", "documentation": "<p>An array of components with summary data.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates the location of the next component in the array of components, after the current requested list of components.</p>"}}}, "ListDeploymentsInput": {"type": "structure", "members": {"componentName": {"shape": "ResourceName", "documentation": "<p>The name of a component for result list filtering. Proton returns deployments associated with that component.</p>"}, "environmentName": {"shape": "ResourceName", "documentation": "<p>The name of an environment for result list filtering. Proton returns deployments associated with the environment.</p>"}, "maxResults": {"shape": "MaxPageResults", "documentation": "<p>The maximum number of deployments to list.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates the location of the next deployment in the array of deployment, after the list of deployment that was previously requested.</p>"}, "serviceInstanceName": {"shape": "ResourceName", "documentation": "<p>The name of a service instance for result list filtering. Proton returns the deployments associated with the service instance.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of a service for result list filtering. Proton returns deployments associated with service instances of the service.</p>"}}}, "ListDeploymentsOutput": {"type": "structure", "required": ["deployments"], "members": {"deployments": {"shape": "DeploymentSummaryList", "documentation": "<p>An array of deployment with summary data.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates the location of the next deployment in the array of deployment, after the current requested list of deployment.</p>"}}}, "ListEnvironmentAccountConnectionsInput": {"type": "structure", "required": ["requestedBy"], "members": {"environmentName": {"shape": "ResourceName", "documentation": "<p>The environment name that's associated with each listed environment account connection.</p>"}, "maxResults": {"shape": "MaxPageResults", "documentation": "<p>The maximum number of environment account connections to list.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates the location of the next environment account connection in the array of environment account connections, after the list of environment account connections that was previously requested.</p>"}, "requestedBy": {"shape": "EnvironmentAccountConnectionRequesterAccountType", "documentation": "<p>The type of account making the <code>ListEnvironmentAccountConnections</code> request.</p>"}, "statuses": {"shape": "EnvironmentAccountConnectionStatusList", "documentation": "<p>The status details for each listed environment account connection.</p>"}}}, "ListEnvironmentAccountConnectionsOutput": {"type": "structure", "required": ["environmentAccountConnections"], "members": {"environmentAccountConnections": {"shape": "EnvironmentAccountConnectionSummaryList", "documentation": "<p>An array of environment account connections with details that's returned by Proton. </p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates the location of the next environment account connection in the array of environment account connections, after the current requested list of environment account connections.</p>"}}}, "ListEnvironmentOutputsInput": {"type": "structure", "required": ["environmentName"], "members": {"deploymentId": {"shape": "DeploymentId", "documentation": "<p>The ID of the deployment whose outputs you want.</p>"}, "environmentName": {"shape": "ResourceName", "documentation": "<p>The environment name.</p>"}, "nextToken": {"shape": "EmptyNextToken", "documentation": "<p>A token that indicates the location of the next environment output in the array of environment outputs, after the list of environment outputs that was previously requested.</p>"}}}, "ListEnvironmentOutputsOutput": {"type": "structure", "required": ["outputs"], "members": {"nextToken": {"shape": "EmptyNextToken", "documentation": "<p>A token that indicates the location of the next environment output in the array of environment outputs, after the current requested list of environment outputs.</p>"}, "outputs": {"shape": "OutputsList", "documentation": "<p>An array of environment outputs with detail data.</p>"}}}, "ListEnvironmentProvisionedResourcesInput": {"type": "structure", "required": ["environmentName"], "members": {"environmentName": {"shape": "ResourceName", "documentation": "<p>The environment name.</p>"}, "nextToken": {"shape": "EmptyNextToken", "documentation": "<p>A token that indicates the location of the next environment provisioned resource in the array of environment provisioned resources, after the list of environment provisioned resources that was previously requested.</p>"}}}, "ListEnvironmentProvisionedResourcesOutput": {"type": "structure", "required": ["provisionedResources"], "members": {"nextToken": {"shape": "EmptyNextToken", "documentation": "<p>A token that indicates the location of the next environment provisioned resource in the array of provisioned resources, after the current requested list of environment provisioned resources.</p>"}, "provisionedResources": {"shape": "ProvisionedResourceList", "documentation": "<p>An array of environment provisioned resources.</p>"}}}, "ListEnvironmentTemplateVersionsInput": {"type": "structure", "required": ["templateName"], "members": {"majorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>To view a list of minor of versions under a major version of an environment template, include <code>major Version</code>.</p> <p>To view a list of major versions of an environment template, <i>exclude</i> <code>major Version</code>.</p>"}, "maxResults": {"shape": "MaxPageResults", "documentation": "<p>The maximum number of major or minor versions of an environment template to list.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates the location of the next major or minor version in the array of major or minor versions of an environment template, after the list of major or minor versions that was previously requested.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the environment template.</p>"}}}, "ListEnvironmentTemplateVersionsOutput": {"type": "structure", "required": ["templateVersions"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates the location of the next major or minor version in the array of major or minor versions of an environment template, after the list of major or minor versions that was previously requested.</p>"}, "templateVersions": {"shape": "EnvironmentTemplateVersionSummaryList", "documentation": "<p>An array of major or minor versions of an environment template detail data.</p>"}}}, "ListEnvironmentTemplatesInput": {"type": "structure", "members": {"maxResults": {"shape": "MaxPageResults", "documentation": "<p>The maximum number of environment templates to list.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates the location of the next environment template in the array of environment templates, after the list of environment templates that was previously requested.</p>"}}}, "ListEnvironmentTemplatesOutput": {"type": "structure", "required": ["templates"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates the location of the next environment template in the array of environment templates, after the current requested list of environment templates.</p>"}, "templates": {"shape": "EnvironmentTemplateSummaryList", "documentation": "<p>An array of environment templates with detail data.</p>"}}}, "ListEnvironmentsInput": {"type": "structure", "members": {"environmentTemplates": {"shape": "EnvironmentTemplateFilterList", "documentation": "<p>An array of the versions of the environment template.</p>"}, "maxResults": {"shape": "MaxPageResults", "documentation": "<p>The maximum number of environments to list.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates the location of the next environment in the array of environments, after the list of environments that was previously requested.</p>"}}}, "ListEnvironmentsOutput": {"type": "structure", "required": ["environments"], "members": {"environments": {"shape": "EnvironmentSummaryList", "documentation": "<p>An array of environment detail data summaries.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates the location of the next environment in the array of environments, after the current requested list of environments.</p>"}}}, "ListRepositoriesInput": {"type": "structure", "members": {"maxResults": {"shape": "MaxPageResults", "documentation": "<p>The maximum number of repositories to list.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates the location of the next repository in the array of repositories, after the list of repositories previously requested.</p>"}}}, "ListRepositoriesOutput": {"type": "structure", "required": ["repositories"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates the location of the next repository in the array of repositories, after the current requested list of repositories. </p>"}, "repositories": {"shape": "RepositorySummaryList", "documentation": "<p>An array of repository links.</p>"}}}, "ListRepositorySyncDefinitionsInput": {"type": "structure", "required": ["repositoryName", "repositoryProvider", "syncType"], "members": {"nextToken": {"shape": "EmptyNextToken", "documentation": "<p>A token that indicates the location of the next repository sync definition in the array of repository sync definitions, after the list of repository sync definitions previously requested.</p>"}, "repositoryName": {"shape": "RepositoryName", "documentation": "<p>The repository name.</p>"}, "repositoryProvider": {"shape": "RepositoryProvider", "documentation": "<p>The repository provider.</p>"}, "syncType": {"shape": "SyncType", "documentation": "<p>The sync type. The only supported value is <code>TEMPLATE_SYNC</code>.</p>"}}}, "ListRepositorySyncDefinitionsOutput": {"type": "structure", "required": ["syncDefinitions"], "members": {"nextToken": {"shape": "EmptyNextToken", "documentation": "<p>A token that indicates the location of the next repository sync definition in the array of repository sync definitions, after the current requested list of repository sync definitions.</p>"}, "syncDefinitions": {"shape": "RepositorySyncDefinitionList", "documentation": "<p>An array of repository sync definitions.</p>"}}}, "ListServiceInstanceOutputsInput": {"type": "structure", "required": ["serviceInstanceName", "serviceName"], "members": {"deploymentId": {"shape": "DeploymentId", "documentation": "<p>The ID of the deployment whose outputs you want.</p>"}, "nextToken": {"shape": "EmptyNextToken", "documentation": "<p>A token that indicates the location of the next output in the array of outputs, after the list of outputs that was previously requested.</p>"}, "serviceInstanceName": {"shape": "ResourceName", "documentation": "<p>The name of the service instance whose outputs you want.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service that <code>serviceInstanceName</code> is associated to.</p>"}}}, "ListServiceInstanceOutputsOutput": {"type": "structure", "required": ["outputs"], "members": {"nextToken": {"shape": "EmptyNextToken", "documentation": "<p>A token that indicates the location of the next output in the array of outputs, after the current requested list of outputs.</p>"}, "outputs": {"shape": "OutputsList", "documentation": "<p>An array of service instance Infrastructure as Code (IaC) outputs.</p>"}}}, "ListServiceInstanceProvisionedResourcesInput": {"type": "structure", "required": ["serviceInstanceName", "serviceName"], "members": {"nextToken": {"shape": "EmptyNextToken", "documentation": "<p>A token that indicates the location of the next provisioned resource in the array of provisioned resources, after the list of provisioned resources that was previously requested.</p>"}, "serviceInstanceName": {"shape": "ResourceName", "documentation": "<p>The name of the service instance whose provisioned resources you want.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service that <code>serviceInstanceName</code> is associated to.</p>"}}}, "ListServiceInstanceProvisionedResourcesOutput": {"type": "structure", "required": ["provisionedResources"], "members": {"nextToken": {"shape": "EmptyNextToken", "documentation": "<p>A token that indicates the location of the next provisioned resource in the array of provisioned resources, after the current requested list of provisioned resources.</p>"}, "provisionedResources": {"shape": "ProvisionedResourceList", "documentation": "<p>An array of provisioned resources for a service instance.</p>"}}}, "ListServiceInstancesFilter": {"type": "structure", "members": {"key": {"shape": "ListServiceInstancesFilterBy", "documentation": "<p>The name of a filtering criterion.</p>"}, "value": {"shape": "ListServiceInstancesFilterValue", "documentation": "<p>A value to filter by.</p> <p>With the date/time keys (<code>*At{Before,After}</code>), the value is a valid <a href=\"https://datatracker.ietf.org/doc/html/rfc3339.html\">RFC 3339</a> string with no UTC offset and with an optional fractional precision (for example, <code>1985-04-12T23:20:50.52Z</code>).</p>"}}, "documentation": "<p>A filtering criterion to scope down the result list of the <a>ListServiceInstances</a> action.</p>"}, "ListServiceInstancesFilterBy": {"type": "string", "enum": ["name", "deploymentStatus", "templateName", "serviceName", "deployedTemplateVersionStatus", "environmentName", "lastDeploymentAttemptedAtBefore", "lastDeploymentAttemptedAtAfter", "createdAtBefore", "createdAtAfter"]}, "ListServiceInstancesFilterList": {"type": "list", "member": {"shape": "ListServiceInstancesFilter"}}, "ListServiceInstancesFilterValue": {"type": "string"}, "ListServiceInstancesInput": {"type": "structure", "members": {"filters": {"shape": "ListServiceInstancesFilterList", "documentation": "<p>An array of filtering criteria that scope down the result list. By default, all service instances in the Amazon Web Services account are returned.</p>"}, "maxResults": {"shape": "MaxPageResults", "documentation": "<p>The maximum number of service instances to list.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates the location of the next service in the array of service instances, after the list of service instances that was previously requested.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service that the service instance belongs to.</p>"}, "sortBy": {"shape": "ListServiceInstancesSortBy", "documentation": "<p>The field that the result list is sorted by.</p> <p>When you choose to sort by <code>serviceName</code>, service instances within each service are sorted by service instance name.</p> <p>Default: <code>serviceName</code> </p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>Result list sort order.</p> <p>Default: <code>ASCENDING</code> </p>"}}}, "ListServiceInstancesOutput": {"type": "structure", "required": ["serviceInstances"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates the location of the next service instance in the array of service instances, after the current requested list of service instances.</p>"}, "serviceInstances": {"shape": "ServiceInstanceSummaryList", "documentation": "<p>An array of service instances with summary data.</p>"}}}, "ListServiceInstancesSortBy": {"type": "string", "enum": ["name", "deploymentStatus", "templateName", "serviceName", "environmentName", "lastDeploymentAttemptedAt", "createdAt"]}, "ListServicePipelineOutputsInput": {"type": "structure", "required": ["serviceName"], "members": {"deploymentId": {"shape": "DeploymentId", "documentation": "<p>The ID of the deployment you want the outputs for.</p>"}, "nextToken": {"shape": "EmptyNextToken", "documentation": "<p>A token that indicates the location of the next output in the array of outputs, after the list of outputs that was previously requested.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service whose pipeline's outputs you want.</p>"}}}, "ListServicePipelineOutputsOutput": {"type": "structure", "required": ["outputs"], "members": {"nextToken": {"shape": "EmptyNextToken", "documentation": "<p>A token that indicates the location of the next output in the array of outputs, after the current requested list of outputs.</p>"}, "outputs": {"shape": "OutputsList", "documentation": "<p>An array of service pipeline Infrastructure as Code (IaC) outputs.</p>"}}}, "ListServicePipelineProvisionedResourcesInput": {"type": "structure", "required": ["serviceName"], "members": {"nextToken": {"shape": "EmptyNextToken", "documentation": "<p>A token that indicates the location of the next provisioned resource in the array of provisioned resources, after the list of provisioned resources that was previously requested.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service whose pipeline's provisioned resources you want.</p>"}}}, "ListServicePipelineProvisionedResourcesOutput": {"type": "structure", "required": ["provisionedResources"], "members": {"nextToken": {"shape": "EmptyNextToken", "documentation": "<p>A token that indicates the location of the next provisioned resource in the array of provisioned resources, after the current requested list of provisioned resources.</p>"}, "provisionedResources": {"shape": "ProvisionedResourceList", "documentation": "<p>An array of provisioned resources for a service and pipeline.</p>"}}}, "ListServiceTemplateVersionsInput": {"type": "structure", "required": ["templateName"], "members": {"majorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>To view a list of minor of versions under a major version of a service template, include <code>major Version</code>.</p> <p>To view a list of major versions of a service template, <i>exclude</i> <code>major Version</code>.</p>"}, "maxResults": {"shape": "MaxPageResults", "documentation": "<p>The maximum number of major or minor versions of a service template to list.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates the location of the next major or minor version in the array of major or minor versions of a service template, after the list of major or minor versions that was previously requested.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the service template.</p>"}}}, "ListServiceTemplateVersionsOutput": {"type": "structure", "required": ["templateVersions"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates the location of the next major or minor version in the array of major or minor versions of a service template, after the current requested list of service major or minor versions.</p>"}, "templateVersions": {"shape": "ServiceTemplateVersionSummaryList", "documentation": "<p>An array of major or minor versions of a service template with detail data.</p>"}}}, "ListServiceTemplatesInput": {"type": "structure", "members": {"maxResults": {"shape": "MaxPageResults", "documentation": "<p>The maximum number of service templates to list.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates the location of the next service template in the array of service templates, after the list of service templates previously requested.</p>"}}}, "ListServiceTemplatesOutput": {"type": "structure", "required": ["templates"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates the location of the next service template in the array of service templates, after the current requested list of service templates.</p>"}, "templates": {"shape": "ServiceTemplateSummaryList", "documentation": "<p>An array of service templates with detail data.</p>"}}}, "ListServicesInput": {"type": "structure", "members": {"maxResults": {"shape": "MaxPageResults", "documentation": "<p>The maximum number of services to list.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates the location of the next service in the array of services, after the list of services that was previously requested.</p>"}}}, "ListServicesOutput": {"type": "structure", "required": ["services"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates the location of the next service in the array of services, after the current requested list of services.</p>"}, "services": {"shape": "ServiceSummaryList", "documentation": "<p>An array of services with summaries of detail data.</p>"}}}, "ListTagsForResourceInput": {"type": "structure", "required": ["resourceArn"], "members": {"maxResults": {"shape": "MaxPageResults", "documentation": "<p>The maximum number of tags to list.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>A token that indicates the location of the next resource tag in the array of resource tags, after the list of resource tags that was previously requested.</p>"}, "resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the resource for the listed tags.</p>"}}}, "ListTagsForResourceOutput": {"type": "structure", "required": ["tags"], "members": {"nextToken": {"shape": "String", "documentation": "<p>A token that indicates the location of the next resource tag in the array of resource tags, after the current requested list of resource tags.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>A list of resource tags with detail data.</p>"}}}, "MaxPageResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "NextToken": {"type": "string", "pattern": "^[A-Za-z0-9+=/]+$"}, "NotifyResourceDeploymentStatusChangeInput": {"type": "structure", "required": ["resourceArn"], "members": {"deploymentId": {"shape": "DeploymentId", "documentation": "<p>The deployment ID for your provisioned resource.</p>"}, "outputs": {"shape": "NotifyResourceDeploymentStatusChangeInputOutputsList", "documentation": "<p>The provisioned resource state change detail data that's returned by Proton.</p>"}, "resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The provisioned resource Amazon Resource Name (ARN).</p>"}, "status": {"shape": "ResourceDeploymentStatus", "documentation": "<p>The status of your provisioned resource.</p>"}, "statusMessage": {"shape": "NotifyResourceDeploymentStatusChangeInputStatusMessageString", "documentation": "<p>The deployment status message for your provisioned resource.</p>"}}}, "NotifyResourceDeploymentStatusChangeInputOutputsList": {"type": "list", "member": {"shape": "Output"}, "max": 50, "min": 0}, "NotifyResourceDeploymentStatusChangeInputStatusMessageString": {"type": "string", "max": 5000, "min": 0, "sensitive": true}, "NotifyResourceDeploymentStatusChangeOutput": {"type": "structure", "members": {}}, "OpsFilePath": {"type": "string", "max": 4096, "min": 1}, "Output": {"type": "structure", "members": {"key": {"shape": "OutputKey", "documentation": "<p>The output key.</p>"}, "valueString": {"shape": "OutputValueString", "documentation": "<p>The output value.</p>"}}, "documentation": "<p>An infrastructure as code defined resource output.</p>", "sensitive": true}, "OutputKey": {"type": "string", "max": 1024, "min": 1}, "OutputValueString": {"type": "string", "max": 1024, "min": 1}, "OutputsList": {"type": "list", "member": {"shape": "Output"}}, "ProvisionedResource": {"type": "structure", "members": {"identifier": {"shape": "ProvisionedResourceIdentifier", "documentation": "<p>The provisioned resource identifier.</p>"}, "name": {"shape": "ProvisionedResourceName", "documentation": "<p>The provisioned resource name.</p>"}, "provisioningEngine": {"shape": "ProvisionedResourceEngine", "documentation": "<p>The resource provisioning engine. At this time, <code>CLOUDFORMATION</code> can be used for Amazon Web Services-managed provisioning, and <code>TERRAFORM</code> can be used for self-managed provisioning.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-works-prov-methods.html#ag-works-prov-methods-self\">Self-managed provisioning</a> in the <i>Proton User Guide</i>.</p>"}}, "documentation": "<p>Detail data for a provisioned resource.</p>"}, "ProvisionedResourceEngine": {"type": "string", "documentation": "<p>List of provisioning engines</p>", "enum": ["CLOUDFORMATION", "TERRAFORM"]}, "ProvisionedResourceIdentifier": {"type": "string", "max": 200, "min": 1}, "ProvisionedResourceList": {"type": "list", "member": {"shape": "ProvisionedResource"}}, "ProvisionedResourceName": {"type": "string", "max": 200, "min": 1}, "Provisioning": {"type": "string", "enum": ["CUSTOMER_MANAGED"]}, "RejectEnvironmentAccountConnectionInput": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "EnvironmentAccountConnectionId", "documentation": "<p>The ID of the environment account connection to reject.</p>"}}}, "RejectEnvironmentAccountConnectionOutput": {"type": "structure", "required": ["environmentAccountConnection"], "members": {"environmentAccountConnection": {"shape": "EnvironmentAccountConnection", "documentation": "<p>The environment connection account detail data that's returned by Proton.</p>"}}}, "Repository": {"type": "structure", "required": ["arn", "connectionArn", "name", "provider"], "members": {"arn": {"shape": "RepositoryArn", "documentation": "<p>The Amazon Resource Name (ARN) of the linked repository.</p>"}, "connectionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of your AWS CodeStar connection that connects Proton to your repository provider account.</p>"}, "encryptionKey": {"shape": "<PERSON><PERSON>", "documentation": "<p>Your customer Amazon Web Services KMS encryption key.</p>"}, "name": {"shape": "RepositoryName", "documentation": "<p>The repository name.</p>"}, "provider": {"shape": "RepositoryProvider", "documentation": "<p>The repository provider.</p>"}}, "documentation": "<p>Detailed data of a linked repository—a repository that has been registered with Proton.</p>"}, "RepositoryArn": {"type": "string"}, "RepositoryBranch": {"type": "structure", "required": ["arn", "branch", "name", "provider"], "members": {"arn": {"shape": "RepositoryArn", "documentation": "<p>The Amazon Resource Name (ARN) of the linked repository.</p>"}, "branch": {"shape": "GitBranchName", "documentation": "<p>The repository branch.</p>"}, "name": {"shape": "RepositoryName", "documentation": "<p>The repository name.</p>"}, "provider": {"shape": "RepositoryProvider", "documentation": "<p>The repository provider.</p>"}}, "documentation": "<p>Detail data for a linked repository branch.</p>"}, "RepositoryBranchInput": {"type": "structure", "required": ["branch", "name", "provider"], "members": {"branch": {"shape": "GitBranchName", "documentation": "<p>The repository branch.</p>"}, "name": {"shape": "RepositoryName", "documentation": "<p>The repository name.</p>"}, "provider": {"shape": "RepositoryProvider", "documentation": "<p>The repository provider.</p>"}}, "documentation": "<p>Detail input data for a linked repository branch.</p>"}, "RepositoryId": {"type": "string", "max": 200, "min": 1}, "RepositoryName": {"type": "string", "max": 100, "min": 1, "pattern": "[A-Za-z0-9_.-].*/[A-Za-z0-9_.-].*"}, "RepositoryProvider": {"type": "string", "enum": ["GITHUB", "GITHUB_ENTERPRISE", "BITBUCKET"]}, "RepositorySummary": {"type": "structure", "required": ["arn", "connectionArn", "name", "provider"], "members": {"arn": {"shape": "RepositoryArn", "documentation": "<p>The Amazon Resource Name (ARN) of the linked repository.</p>"}, "connectionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the of your connection that connects Proton to your repository.</p>"}, "name": {"shape": "RepositoryName", "documentation": "<p>The repository name.</p>"}, "provider": {"shape": "RepositoryProvider", "documentation": "<p>The repository provider.</p>"}}, "documentation": "<p>Summary data of a linked repository—a repository that has been registered with Proton.</p>"}, "RepositorySummaryList": {"type": "list", "member": {"shape": "RepositorySummary"}}, "RepositorySyncAttempt": {"type": "structure", "required": ["events", "startedAt", "status"], "members": {"events": {"shape": "RepositorySyncEvents", "documentation": "<p>Detail data for sync attempt events.</p>"}, "startedAt": {"shape": "Timestamp", "documentation": "<p>The time when the sync attempt started.</p>"}, "status": {"shape": "RepositorySyncStatus", "documentation": "<p>The sync attempt status.</p>"}}, "documentation": "<p>Detail data for a repository sync attempt activated by a push to a repository.</p>"}, "RepositorySyncDefinition": {"type": "structure", "required": ["branch", "directory", "parent", "target"], "members": {"branch": {"shape": "GitBranchName", "documentation": "<p>The repository branch.</p>"}, "directory": {"shape": "String", "documentation": "<p>The directory in the repository.</p>"}, "parent": {"shape": "String", "documentation": "<p>The resource that is synced from.</p>"}, "target": {"shape": "String", "documentation": "<p>The resource that is synced to.</p>"}}, "documentation": "<p>A repository sync definition.</p>"}, "RepositorySyncDefinitionList": {"type": "list", "member": {"shape": "RepositorySyncDefinition"}}, "RepositorySyncEvent": {"type": "structure", "required": ["event", "time", "type"], "members": {"event": {"shape": "String", "documentation": "<p>Event detail for a repository sync attempt.</p>"}, "externalId": {"shape": "String", "documentation": "<p>The external ID of the sync event.</p>"}, "time": {"shape": "Timestamp", "documentation": "<p>The time that the sync event occurred.</p>"}, "type": {"shape": "String", "documentation": "<p>The type of event.</p>"}}, "documentation": "<p>Repository sync event detail data for a sync attempt.</p>"}, "RepositorySyncEvents": {"type": "list", "member": {"shape": "RepositorySyncEvent"}}, "RepositorySyncStatus": {"type": "string", "enum": ["INITIATED", "IN_PROGRESS", "SUCCEEDED", "FAILED", "QUEUED"]}, "ResourceCountsSummary": {"type": "structure", "required": ["total"], "members": {"behindMajor": {"shape": "Integer", "documentation": "<p>The number of resources of this type in the Amazon Web Services account that need a major template version update.</p>"}, "behindMinor": {"shape": "Integer", "documentation": "<p>The number of resources of this type in the Amazon Web Services account that need a minor template version update.</p>"}, "failed": {"shape": "Integer", "documentation": "<p>The number of resources of this type in the Amazon Web Services account that failed to deploy.</p>"}, "total": {"shape": "Integer", "documentation": "<p>The total number of resources of this type in the Amazon Web Services account.</p>"}, "upToDate": {"shape": "Integer", "documentation": "<p>The number of resources of this type in the Amazon Web Services account that are up-to-date with their template.</p>"}}, "documentation": "<p>Summary counts of each Proton resource types.</p>"}, "ResourceDeploymentStatus": {"type": "string", "documentation": "<p>The state that a PR-based deployment can be updated to.</p>", "enum": ["IN_PROGRESS", "FAILED", "SUCCEEDED"]}, "ResourceName": {"type": "string", "max": 100, "min": 1, "pattern": "^[0-9A-Za-z]+[0-9A-Za-z_\\-]*$"}, "ResourceNameOrEmpty": {"type": "string", "max": 100, "min": 0, "pattern": "(^$)|^[0-9A-Za-z]+[0-9A-Za-z_\\-]*$"}, "ResourceNotFoundException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The requested resource <i>wasn't</i> found.</p>", "exception": true}, "ResourceSyncAttempt": {"type": "structure", "required": ["events", "initialRevision", "startedAt", "status", "target", "targetRevision"], "members": {"events": {"shape": "ResourceSyncEvents", "documentation": "<p>An array of events with detail data.</p>"}, "initialRevision": {"shape": "Revision", "documentation": "<p>Detail data for the initial repository commit, path and push.</p>"}, "startedAt": {"shape": "Timestamp", "documentation": "<p>The time when the sync attempt started.</p>"}, "status": {"shape": "ResourceSyncStatus", "documentation": "<p>The status of the sync attempt.</p>"}, "target": {"shape": "String", "documentation": "<p>The resource that is synced to.</p>"}, "targetRevision": {"shape": "Revision", "documentation": "<p>Detail data for the target revision.</p>"}}, "documentation": "<p>Detail data for a resource sync attempt activated by a push to a repository.</p>"}, "ResourceSyncEvent": {"type": "structure", "required": ["event", "time", "type"], "members": {"event": {"shape": "String", "documentation": "<p>A resource sync event.</p>"}, "externalId": {"shape": "String", "documentation": "<p>The external ID for the event.</p>"}, "time": {"shape": "Timestamp", "documentation": "<p>The time when the event occurred.</p>"}, "type": {"shape": "String", "documentation": "<p>The type of event.</p>"}}, "documentation": "<p>Detail data for a resource sync event.</p>"}, "ResourceSyncEvents": {"type": "list", "member": {"shape": "ResourceSyncEvent"}}, "ResourceSyncStatus": {"type": "string", "enum": ["INITIATED", "IN_PROGRESS", "SUCCEEDED", "FAILED"]}, "Revision": {"type": "structure", "required": ["branch", "directory", "repositoryName", "repositoryProvider", "sha"], "members": {"branch": {"shape": "GitBranchName", "documentation": "<p>The repository branch.</p>"}, "directory": {"shape": "String", "documentation": "<p>The repository directory changed by a commit and push that activated the sync attempt.</p>"}, "repositoryName": {"shape": "RepositoryName", "documentation": "<p>The repository name.</p>"}, "repositoryProvider": {"shape": "RepositoryProvider", "documentation": "<p>The repository provider.</p>"}, "sha": {"shape": "SHA", "documentation": "<p>The secure hash algorithm (SHA) hash for the revision.</p>"}}, "documentation": "<p>Revision detail data for a commit and push that activates a sync attempt</p>"}, "RoleArn": {"type": "string", "max": 2048, "min": 1, "pattern": "^arn:(aws|aws-cn|aws-us-gov):iam::\\d{12}:role/([\\w+=,.@-]{1,512}[/:])*([\\w+=,.@-]{1,64})$"}, "RoleArnOrEmptyString": {"type": "string", "max": 2048, "min": 0, "pattern": "(^$)|(^arn:(aws|aws-cn|aws-us-gov):iam::\\d{12}:role/([\\w+=,.@-]{1,512}[/:])*([\\w+=,.@-]{1,64})$)"}, "S3Bucket": {"type": "string", "max": 63, "min": 3, "pattern": "^[a-z0-9]+[a-z0-9-\\.]+[a-z0-9]+$"}, "S3Key": {"type": "string", "max": 1024, "min": 1}, "S3ObjectSource": {"type": "structure", "required": ["bucket", "key"], "members": {"bucket": {"shape": "S3Bucket", "documentation": "<p>The name of the S3 bucket that contains a template bundle.</p>"}, "key": {"shape": "S3Key", "documentation": "<p>The path to the S3 bucket that contains a template bundle.</p>"}}, "documentation": "<p>Template bundle S3 bucket data.</p>"}, "SHA": {"type": "string", "max": 255, "min": 1}, "Service": {"type": "structure", "required": ["arn", "createdAt", "lastModifiedAt", "name", "spec", "status", "templateName"], "members": {"arn": {"shape": "ServiceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the service.</p>"}, "branchName": {"shape": "GitBranchName", "documentation": "<p>The name of the code repository branch that holds the code that's deployed in Proton.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the service was created.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the service.</p>"}, "lastModifiedAt": {"shape": "Timestamp", "documentation": "<p>The time when the service was last modified.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the service.</p>"}, "pipeline": {"shape": "ServicePipeline", "documentation": "<p>The service pipeline detail data.</p>"}, "repositoryConnectionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the repository connection. For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/setting-up-for-service.html#setting-up-vcontrol\">Setting up an AWS CodeStar connection</a> in the <i>Proton User Guide</i>.</p>"}, "repositoryId": {"shape": "RepositoryId", "documentation": "<p>The ID of the source code repository.</p>"}, "spec": {"shape": "SpecContents", "documentation": "<p>The formatted specification that defines the service.</p>"}, "status": {"shape": "ServiceStatus", "documentation": "<p>The status of the service.</p>"}, "statusMessage": {"shape": "StatusMessage", "documentation": "<p>A service status message.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the service template.</p>"}}, "documentation": "<p>Detailed data of an Proton service resource.</p>"}, "ServiceArn": {"type": "string"}, "ServiceInstance": {"type": "structure", "required": ["arn", "createdAt", "deploymentStatus", "environmentName", "lastDeploymentAttemptedAt", "lastDeploymentSucceededAt", "name", "serviceName", "templateMajorVersion", "templateMinorVersion", "templateName"], "members": {"arn": {"shape": "ServiceInstanceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the service instance.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the service instance was created.</p>"}, "deploymentStatus": {"shape": "DeploymentStatus", "documentation": "<p>The service instance deployment status.</p>"}, "deploymentStatusMessage": {"shape": "StatusMessage", "documentation": "<p>The message associated with the service instance deployment status.</p>"}, "environmentName": {"shape": "ResourceName", "documentation": "<p>The name of the environment that the service instance was deployed into.</p>"}, "lastAttemptedDeploymentId": {"shape": "DeploymentId", "documentation": "<p>The ID of the last attempted deployment of this service instance.</p>"}, "lastClientRequestToken": {"shape": "String", "documentation": "<p>The last client request token received.</p>"}, "lastDeploymentAttemptedAt": {"shape": "Timestamp", "documentation": "<p>The time when a deployment of the service instance was last attempted.</p>"}, "lastDeploymentSucceededAt": {"shape": "Timestamp", "documentation": "<p>The time when the service instance was last deployed successfully.</p>"}, "lastSucceededDeploymentId": {"shape": "DeploymentId", "documentation": "<p>The ID of the last successful deployment of this service instance.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the service instance.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service that the service instance belongs to.</p>"}, "spec": {"shape": "SpecContents", "documentation": "<p>The service spec that was used to create the service instance.</p>"}, "templateMajorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The major version of the service template that was used to create the service instance.</p>"}, "templateMinorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The minor version of the service template that was used to create the service instance.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the service template that was used to create the service instance.</p>"}}, "documentation": "<p>Detailed data of an Proton service instance resource.</p>"}, "ServiceInstanceArn": {"type": "string"}, "ServiceInstanceState": {"type": "structure", "required": ["spec", "templateMajorVersion", "templateMinorVersion", "templateName"], "members": {"lastSuccessfulComponentDeploymentIds": {"shape": "ComponentDeploymentIdList", "documentation": "<p>The IDs for the last successful components deployed for this service instance.</p>"}, "lastSuccessfulEnvironmentDeploymentId": {"shape": "DeploymentId", "documentation": "<p>The ID for the last successful environment deployed for this service instance.</p>"}, "lastSuccessfulServicePipelineDeploymentId": {"shape": "DeploymentId", "documentation": "<p>The ID for the last successful service pipeline deployed for this service instance.</p>"}, "spec": {"shape": "SpecContents", "documentation": "<p>The service spec that was used to create the service instance.</p>"}, "templateMajorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The major version of the service template that was used to create the service pipeline.</p>"}, "templateMinorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The minor version of the service template that was used to create the service pipeline.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the service template that was used to create the service instance.</p>"}}, "documentation": "<p>The detailed data about the current state of this service instance.</p>"}, "ServiceInstanceSummary": {"type": "structure", "required": ["arn", "createdAt", "deploymentStatus", "environmentName", "lastDeploymentAttemptedAt", "lastDeploymentSucceededAt", "name", "serviceName", "templateMajorVersion", "templateMinorVersion", "templateName"], "members": {"arn": {"shape": "ServiceInstanceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the service instance.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the service instance was created.</p>"}, "deploymentStatus": {"shape": "DeploymentStatus", "documentation": "<p>The service instance deployment status.</p>"}, "deploymentStatusMessage": {"shape": "StatusMessage", "documentation": "<p>A service instance deployment status message.</p>"}, "environmentName": {"shape": "ResourceName", "documentation": "<p>The name of the environment that the service instance was deployed into.</p>"}, "lastAttemptedDeploymentId": {"shape": "DeploymentId", "documentation": "<p>The ID of the last attempted deployment of this service instance.</p>"}, "lastDeploymentAttemptedAt": {"shape": "Timestamp", "documentation": "<p>The time when a deployment of the service was last attempted.</p>"}, "lastDeploymentSucceededAt": {"shape": "Timestamp", "documentation": "<p>The time when the service was last deployed successfully.</p>"}, "lastSucceededDeploymentId": {"shape": "DeploymentId", "documentation": "<p>The ID of the last successful deployment of this service instance.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the service instance.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service that the service instance belongs to.</p>"}, "templateMajorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The service instance template major version.</p>"}, "templateMinorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The service instance template minor version.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the service template.</p>"}}, "documentation": "<p>Summary data of an Proton service instance resource.</p>"}, "ServiceInstanceSummaryList": {"type": "list", "member": {"shape": "ServiceInstanceSummary"}}, "ServicePipeline": {"type": "structure", "required": ["arn", "createdAt", "deploymentStatus", "lastDeploymentAttemptedAt", "lastDeploymentSucceededAt", "templateMajorVersion", "templateMinorVersion", "templateName"], "members": {"arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the service pipeline.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the service pipeline was created.</p>"}, "deploymentStatus": {"shape": "DeploymentStatus", "documentation": "<p>The deployment status of the service pipeline.</p>"}, "deploymentStatusMessage": {"shape": "StatusMessage", "documentation": "<p>A service pipeline deployment status message.</p>"}, "lastAttemptedDeploymentId": {"shape": "DeploymentId", "documentation": "<p>The ID of the last attempted deployment of this service pipeline.</p>"}, "lastDeploymentAttemptedAt": {"shape": "Timestamp", "documentation": "<p>The time when a deployment of the service pipeline was last attempted.</p>"}, "lastDeploymentSucceededAt": {"shape": "Timestamp", "documentation": "<p>The time when the service pipeline was last deployed successfully.</p>"}, "lastSucceededDeploymentId": {"shape": "DeploymentId", "documentation": "<p>The ID of the last successful deployment of this service pipeline.</p>"}, "spec": {"shape": "SpecContents", "documentation": "<p>The service spec that was used to create the service pipeline.</p>"}, "templateMajorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The major version of the service template that was used to create the service pipeline.</p>"}, "templateMinorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The minor version of the service template that was used to create the service pipeline.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the service template that was used to create the service pipeline.</p>"}}, "documentation": "<p>Detailed data of an Proton service instance pipeline resource.</p>"}, "ServicePipelineState": {"type": "structure", "required": ["templateMajorVersion", "templateMinorVersion", "templateName"], "members": {"spec": {"shape": "SpecContents", "documentation": "<p>The service spec that was used to create the service pipeline.</p>"}, "templateMajorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The major version of the service template that was used to create the service pipeline.</p>"}, "templateMinorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The minor version of the service template that was used to create the service pipeline.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the service template that was used to create the service pipeline.</p>"}}, "documentation": "<p>The detailed data about the current state of the service pipeline.</p>"}, "ServiceQuotaExceededException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>A quota was exceeded. For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-limits.html\">Proton Quotas</a> in the <i>Proton User Guide</i>.</p>", "exception": true}, "ServiceStatus": {"type": "string", "enum": ["CREATE_IN_PROGRESS", "CREATE_FAILED_CLEANUP_IN_PROGRESS", "CREATE_FAILED_CLEANUP_COMPLETE", "CREATE_FAILED_CLEANUP_FAILED", "CREATE_FAILED", "ACTIVE", "DELETE_IN_PROGRESS", "DELETE_FAILED", "UPDATE_IN_PROGRESS", "UPDATE_FAILED_CLEANUP_IN_PROGRESS", "UPDATE_FAILED_CLEANUP_COMPLETE", "UPDATE_FAILED_CLEANUP_FAILED", "UPDATE_FAILED", "UPDATE_COMPLETE_CLEANUP_FAILED"]}, "ServiceSummary": {"type": "structure", "required": ["arn", "createdAt", "lastModifiedAt", "name", "status", "templateName"], "members": {"arn": {"shape": "ServiceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the service.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the service was created.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the service.</p>"}, "lastModifiedAt": {"shape": "Timestamp", "documentation": "<p>The time when the service was last modified.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the service.</p>"}, "status": {"shape": "ServiceStatus", "documentation": "<p>The status of the service.</p>"}, "statusMessage": {"shape": "StatusMessage", "documentation": "<p>A service status message.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the service template.</p>"}}, "documentation": "<p>Summary data of an Proton service resource.</p>"}, "ServiceSummaryList": {"type": "list", "member": {"shape": "ServiceSummary"}}, "ServiceSyncBlockerSummary": {"type": "structure", "required": ["serviceName"], "members": {"latestBlockers": {"shape": "LatestSyncBlockers", "documentation": "<p>The latest active blockers for the synced service.</p>"}, "serviceInstanceName": {"shape": "String", "documentation": "<p>The name of the service instance that you want sync your service configuration with.</p>"}, "serviceName": {"shape": "String", "documentation": "<p>The name of the service that you want to get the sync blocker summary for. If given a service instance name and a service name, it will return the blockers only applying to the instance that is blocked.</p> <p>If given only a service name, it will return the blockers that apply to all of the instances. In order to get the blockers for a single instance, you will need to make two distinct calls, one to get the sync blocker summary for the service and the other to get the sync blocker for the service instance.</p>"}}, "documentation": "<p>If a service instance is manually updated, <PERSON><PERSON> wants to prevent accidentally overriding a manual change.</p> <p>A blocker is created because of the manual update or deletion of a service instance. The summary describes the blocker as being active or resolved.</p>"}, "ServiceSyncConfig": {"type": "structure", "required": ["branch", "filePath", "repositoryName", "repositoryProvider", "serviceName"], "members": {"branch": {"shape": "GitBranchName", "documentation": "<p>The name of the code repository branch that holds the service code Proton will sync with.</p>"}, "filePath": {"shape": "OpsFilePath", "documentation": "<p>The file path to the service sync configuration file.</p>"}, "repositoryName": {"shape": "RepositoryName", "documentation": "<p>The name of the code repository that holds the service code Proton will sync with.</p>"}, "repositoryProvider": {"shape": "RepositoryProvider", "documentation": "<p>The name of the repository provider that holds the repository Proton will sync with.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service that the service instance is added to.</p>"}}, "documentation": "<p>Detailed data of the service sync configuration.</p>"}, "ServiceTemplate": {"type": "structure", "required": ["arn", "createdAt", "lastModifiedAt", "name"], "members": {"arn": {"shape": "ServiceTemplateArn", "documentation": "<p>The Amazon Resource Name (ARN) of the service template.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the service template was created.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the service template.</p>"}, "displayName": {"shape": "DisplayName", "documentation": "<p>The service template name as displayed in the developer interface.</p>"}, "encryptionKey": {"shape": "<PERSON><PERSON>", "documentation": "<p>The customer provided service template encryption key that's used to encrypt data.</p>"}, "lastModifiedAt": {"shape": "Timestamp", "documentation": "<p>The time when the service template was last modified.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the service template.</p>"}, "pipelineProvisioning": {"shape": "Provisioning", "documentation": "<p>If <code>pipelineProvisioning</code> is <code>true</code>, a service pipeline is included in the service template. Otherwise, a service pipeline <i>isn't</i> included in the service template.</p>"}, "recommendedVersion": {"shape": "FullTemplateVersionNumber", "documentation": "<p>The recommended version of the service template.</p>"}}, "documentation": "<p>Detailed data of an Proton service template resource.</p>"}, "ServiceTemplateArn": {"type": "string"}, "ServiceTemplateSummary": {"type": "structure", "required": ["arn", "createdAt", "lastModifiedAt", "name"], "members": {"arn": {"shape": "ServiceTemplateArn", "documentation": "<p>The Amazon Resource Name (ARN) of the service template.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the service template was created.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the service template.</p>"}, "displayName": {"shape": "DisplayName", "documentation": "<p>The service template name as displayed in the developer interface.</p>"}, "lastModifiedAt": {"shape": "Timestamp", "documentation": "<p>The time when the service template was last modified.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the service template.</p>"}, "pipelineProvisioning": {"shape": "Provisioning", "documentation": "<p>If <code>pipelineProvisioning</code> is <code>true</code>, a service pipeline is included in the service template, otherwise a service pipeline <i>isn't</i> included in the service template.</p>"}, "recommendedVersion": {"shape": "FullTemplateVersionNumber", "documentation": "<p>The recommended version of the service template.</p>"}}, "documentation": "<p>Summary data of an Proton service template resource.</p>"}, "ServiceTemplateSummaryList": {"type": "list", "member": {"shape": "ServiceTemplateSummary"}}, "ServiceTemplateSupportedComponentSourceInputList": {"type": "list", "member": {"shape": "ServiceTemplateSupportedComponentSourceType"}}, "ServiceTemplateSupportedComponentSourceType": {"type": "string", "enum": ["DIRECTLY_DEFINED"]}, "ServiceTemplateVersion": {"type": "structure", "required": ["arn", "compatibleEnvironmentTemplates", "createdAt", "lastModifiedAt", "majorVersion", "minorVersion", "status", "templateName"], "members": {"arn": {"shape": "ServiceTemplateVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the version of a service template.</p>"}, "compatibleEnvironmentTemplates": {"shape": "CompatibleEnvironmentTemplateList", "documentation": "<p>An array of compatible environment template names for the major version of a service template.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the version of a service template was created.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the version of a service template.</p>"}, "lastModifiedAt": {"shape": "Timestamp", "documentation": "<p>The time when the version of a service template was last modified.</p>"}, "majorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The latest major version that's associated with the version of a service template.</p>"}, "minorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The minor version of a service template.</p>"}, "recommendedMinorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The recommended minor version of the service template.</p>"}, "schema": {"shape": "TemplateSchema", "documentation": "<p>The schema of the version of a service template.</p>"}, "status": {"shape": "TemplateVersionStatus", "documentation": "<p>The service template version status.</p>"}, "statusMessage": {"shape": "StatusMessage", "documentation": "<p>A service template version status message.</p>"}, "supportedComponentSources": {"shape": "ServiceTemplateSupportedComponentSourceInputList", "documentation": "<p>An array of supported component sources. Components with supported sources can be attached to service instances based on this service template version.</p> <p>For more information about components, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-components.html\">Proton components</a> in the <i>Proton User Guide</i>.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the version of a service template.</p>"}}, "documentation": "<p>Detailed data of an Proton service template version resource.</p>"}, "ServiceTemplateVersionArn": {"type": "string"}, "ServiceTemplateVersionSummary": {"type": "structure", "required": ["arn", "createdAt", "lastModifiedAt", "majorVersion", "minorVersion", "status", "templateName"], "members": {"arn": {"shape": "ServiceTemplateVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the version of a service template.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the version of a service template was created.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the version of a service template.</p>"}, "lastModifiedAt": {"shape": "Timestamp", "documentation": "<p>The time when the version of a service template was last modified.</p>"}, "majorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The latest major version that's associated with the version of a service template.</p>"}, "minorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The minor version of a service template.</p>"}, "recommendedMinorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The recommended minor version of the service template.</p>"}, "status": {"shape": "TemplateVersionStatus", "documentation": "<p>The service template minor version status.</p>"}, "statusMessage": {"shape": "StatusMessage", "documentation": "<p>A service template minor version status message.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the service template.</p>"}}, "documentation": "<p>Summary data of an Proton service template version resource.</p>"}, "ServiceTemplateVersionSummaryList": {"type": "list", "member": {"shape": "ServiceTemplateVersionSummary"}}, "SortOrder": {"type": "string", "enum": ["ASCENDING", "DESCENDING"]}, "SpecContents": {"type": "string", "max": 51200, "min": 1, "sensitive": true}, "StatusMessage": {"type": "string", "sensitive": true}, "String": {"type": "string"}, "Subdirectory": {"type": "string", "max": 4096, "min": 1}, "SyncBlocker": {"type": "structure", "required": ["createdAt", "createdReason", "id", "status", "type"], "members": {"contexts": {"shape": "SyncBlockerContexts", "documentation": "<p>The contexts for the sync blocker.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the sync blocker was created.</p>"}, "createdReason": {"shape": "String", "documentation": "<p>The reason why the sync blocker was created.</p>"}, "id": {"shape": "String", "documentation": "<p>The ID of the sync blocker.</p>"}, "resolvedAt": {"shape": "Timestamp", "documentation": "<p>The time the sync blocker was resolved.</p>"}, "resolvedReason": {"shape": "String", "documentation": "<p>The reason the sync blocker was resolved.</p>"}, "status": {"shape": "BlockerStatus", "documentation": "<p>The status of the sync blocker.</p>"}, "type": {"shape": "BlockerType", "documentation": "<p>The type of the sync blocker.</p>"}}, "documentation": "<p>Detailed data of the sync blocker.</p>"}, "SyncBlockerContext": {"type": "structure", "required": ["key", "value"], "members": {"key": {"shape": "String", "documentation": "<p>The key for the sync blocker context.</p>"}, "value": {"shape": "String", "documentation": "<p>The value of the sync blocker context.</p>"}}, "documentation": "<p>Detailed data of the context of the sync blocker.</p>"}, "SyncBlockerContexts": {"type": "list", "member": {"shape": "SyncBlockerContext"}}, "SyncType": {"type": "string", "enum": ["TEMPLATE_SYNC", "SERVICE_SYNC"]}, "Tag": {"type": "structure", "required": ["key", "value"], "members": {"key": {"shape": "TagKey", "documentation": "<p>The key of the resource tag.</p>"}, "value": {"shape": "TagValue", "documentation": "<p>The value of the resource tag.</p>"}}, "documentation": "<p>A description of a resource tag.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 0}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "max": 50, "min": 0}, "TagResourceInput": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Proton resource to apply customer tags to.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>A list of customer tags to apply to the Proton resource.</p>"}}}, "TagResourceOutput": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "TemplateFileContents": {"type": "string", "max": 51200, "min": 1, "sensitive": true}, "TemplateManifestContents": {"type": "string", "max": 1024, "min": 1, "sensitive": true}, "TemplateSchema": {"type": "string", "max": 51200, "min": 1, "sensitive": true}, "TemplateSyncConfig": {"type": "structure", "required": ["branch", "repositoryName", "repositoryProvider", "templateName", "templateType"], "members": {"branch": {"shape": "GitBranchName", "documentation": "<p>The repository branch.</p>"}, "repositoryName": {"shape": "RepositoryName", "documentation": "<p>The repository name (for example, <code>myrepos/myrepo</code>).</p>"}, "repositoryProvider": {"shape": "RepositoryProvider", "documentation": "<p>The repository provider.</p>"}, "subdirectory": {"shape": "Subdirectory", "documentation": "<p>A subdirectory path to your template bundle version.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The template name.</p>"}, "templateType": {"shape": "TemplateType", "documentation": "<p>The template type.</p>"}}, "documentation": "<p>The detail data for a template sync configuration.</p>"}, "TemplateType": {"type": "string", "enum": ["ENVIRONMENT", "SERVICE"]}, "TemplateVersionPart": {"type": "string", "max": 20, "min": 1, "pattern": "^(0|([1-9]{1}\\d*))$"}, "TemplateVersionSourceInput": {"type": "structure", "members": {"s3": {"shape": "S3ObjectSource", "documentation": "<p>An S3 source object that includes the template bundle S3 path and name for a template minor version.</p>"}}, "documentation": "<p>Template version source data.</p>", "union": true}, "TemplateVersionStatus": {"type": "string", "enum": ["REGISTRATION_IN_PROGRESS", "REGISTRATION_FAILED", "DRAFT", "PUBLISHED"]}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The request was denied due to request throttling.</p>", "exception": true, "retryable": {"throttling": true}}, "Timestamp": {"type": "timestamp"}, "UntagResourceInput": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the resource to remove customer tags from.</p>"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>A list of customer tag keys that indicate the customer tags to be removed from the resource.</p>"}}}, "UntagResourceOutput": {"type": "structure", "members": {}}, "UpdateAccountSettingsInput": {"type": "structure", "members": {"deletePipelineProvisioningRepository": {"shape": "Boolean", "documentation": "<p>Set to <code>true</code> to remove a configured pipeline repository from the account settings. Don't set this field if you are updating the configured pipeline repository.</p>"}, "pipelineCodebuildRoleArn": {"shape": "RoleArnOrEmptyString", "documentation": "<p>The Amazon Resource Name (ARN) of the service role you want to use for provisioning pipelines. Proton assumes this role for CodeBuild-based provisioning.</p>"}, "pipelineProvisioningRepository": {"shape": "RepositoryBranchInput", "documentation": "<p>A linked repository for pipeline provisioning. Specify it if you have environments configured for self-managed provisioning with services that include pipelines. A linked repository is a repository that has been registered with Proton. For more information, see <a>CreateRepository</a>.</p> <p>To remove a previously configured repository, set <code>deletePipelineProvisioningRepository</code> to <code>true</code>, and don't set <code>pipelineProvisioningRepository</code>.</p>"}, "pipelineServiceRoleArn": {"shape": "RoleArnOrEmptyString", "documentation": "<p>The Amazon Resource Name (ARN) of the service role you want to use for provisioning pipelines. Assumed by Proton for Amazon Web Services-managed provisioning, and by customer-owned automation for self-managed provisioning.</p> <p>To remove a previously configured ARN, specify an empty string.</p>"}}}, "UpdateAccountSettingsOutput": {"type": "structure", "required": ["accountSettings"], "members": {"accountSettings": {"shape": "AccountSettings", "documentation": "<p>The Proton pipeline service role and repository data shared across the Amazon Web Services account.</p>"}}}, "UpdateComponentInput": {"type": "structure", "required": ["deploymentType", "name"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>The client token for the updated component.</p>", "idempotencyToken": true}, "deploymentType": {"shape": "ComponentDeploymentUpdateType", "documentation": "<p>The deployment type. It defines the mode for updating a component, as follows:</p> <dl> <dt/> <dd> <p> <code>NONE</code> </p> <p>In this mode, a deployment <i>doesn't</i> occur. Only the requested metadata parameters are updated. You can only specify <code>description</code> in this mode.</p> </dd> <dt/> <dd> <p> <code>CURRENT_VERSION</code> </p> <p>In this mode, the component is deployed and updated with the new <code>serviceSpec</code>, <code>templateSource</code>, and/or <code>type</code> that you provide. Only requested parameters are updated.</p> </dd> </dl>"}, "description": {"shape": "Description", "documentation": "<p>An optional customer-provided description of the component.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the component to update.</p>"}, "serviceInstanceName": {"shape": "ResourceNameOrEmpty", "documentation": "<p>The name of the service instance that you want to attach this component to. Don't specify to keep the component's current service instance attachment. Specify an empty string to detach the component from the service instance it's attached to. Specify non-empty values for both <code>serviceInstanceName</code> and <code>serviceName</code> or for neither of them.</p>"}, "serviceName": {"shape": "ResourceNameOrEmpty", "documentation": "<p>The name of the service that <code>serviceInstanceName</code> is associated with. Don't specify to keep the component's current service instance attachment. Specify an empty string to detach the component from the service instance it's attached to. Specify non-empty values for both <code>serviceInstanceName</code> and <code>serviceName</code> or for neither of them.</p>"}, "serviceSpec": {"shape": "SpecContents", "documentation": "<p>The service spec that you want the component to use to access service inputs. Set this only when the component is attached to a service instance.</p>"}, "templateFile": {"shape": "TemplateFileContents", "documentation": "<p>A path to the Infrastructure as Code (IaC) file describing infrastructure that a custom component provisions.</p> <note> <p>Components support a single IaC file, even if you use Terraform as your template language.</p> </note>"}}}, "UpdateComponentOutput": {"type": "structure", "required": ["component"], "members": {"component": {"shape": "Component", "documentation": "<p>The detailed data of the updated component.</p>"}}}, "UpdateEnvironmentAccountConnectionInput": {"type": "structure", "required": ["id"], "members": {"codebuildRoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of an IAM service role in the environment account. Proton uses this role to provision infrastructure resources using CodeBuild-based provisioning in the associated environment account.</p>"}, "componentRoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM service role that Proton uses when provisioning directly defined components in the associated environment account. It determines the scope of infrastructure that a component can provision in the account.</p> <p>The environment account connection must have a <code>componentRoleArn</code> to allow directly defined components to be associated with any environments running in the account.</p> <p>For more information about components, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-components.html\">Proton components</a> in the <i>Proton User Guide</i>.</p>"}, "id": {"shape": "EnvironmentAccountConnectionId", "documentation": "<p>The ID of the environment account connection to update.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM service role that's associated with the environment account connection to update.</p>"}}}, "UpdateEnvironmentAccountConnectionOutput": {"type": "structure", "required": ["environmentAccountConnection"], "members": {"environmentAccountConnection": {"shape": "EnvironmentAccountConnection", "documentation": "<p>The environment account connection detail data that's returned by Proton.</p>"}}}, "UpdateEnvironmentInput": {"type": "structure", "required": ["deploymentType", "name"], "members": {"codebuildRoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM service role that allows Proton to provision infrastructure using CodeBuild-based provisioning on your behalf.</p>"}, "componentRoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM service role that Proton uses when provisioning directly defined components in this environment. It determines the scope of infrastructure that a component can provision.</p> <p>The environment must have a <code>componentRoleArn</code> to allow directly defined components to be associated with the environment.</p> <p>For more information about components, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-components.html\">Proton components</a> in the <i>Proton User Guide</i>.</p>"}, "deploymentType": {"shape": "DeploymentUpdateType", "documentation": "<p>There are four modes for updating an environment. The <code>deploymentType</code> field defines the mode.</p> <dl> <dt/> <dd> <p> <code>NONE</code> </p> <p>In this mode, a deployment <i>doesn't</i> occur. Only the requested metadata parameters are updated.</p> </dd> <dt/> <dd> <p> <code>CURRENT_VERSION</code> </p> <p>In this mode, the environment is deployed and updated with the new spec that you provide. Only requested parameters are updated. <i>Don’t</i> include major or minor version parameters when you use this <code>deployment-type</code>.</p> </dd> <dt/> <dd> <p> <code>MINOR_VERSION</code> </p> <p>In this mode, the environment is deployed and updated with the published, recommended (latest) minor version of the current major version in use, by default. You can also specify a different minor version of the current major version in use.</p> </dd> <dt/> <dd> <p> <code>MAJOR_VERSION</code> </p> <p>In this mode, the environment is deployed and updated with the published, recommended (latest) major and minor version of the current template, by default. You can also specify a different major version that is higher than the major version in use and a minor version (optional).</p> </dd> </dl>"}, "description": {"shape": "Description", "documentation": "<p>A description of the environment update.</p>"}, "environmentAccountConnectionId": {"shape": "EnvironmentAccountConnectionId", "documentation": "<p>The ID of the environment account connection.</p> <p>You can only update to a new environment account connection if it was created in the same environment account that the current environment account connection was created in and is associated with the current environment.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the environment to update.</p>"}, "protonServiceRoleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Proton service role that allows Proton to make API calls to other services your behalf.</p>"}, "provisioningRepository": {"shape": "RepositoryBranchInput", "documentation": "<p>The linked repository that you use to host your rendered infrastructure templates for self-managed provisioning. A linked repository is a repository that has been registered with Proton. For more information, see <a>CreateRepository</a>.</p>"}, "spec": {"shape": "SpecContents", "documentation": "<p>The formatted specification that defines the update.</p>"}, "templateMajorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The major version of the environment to update.</p>"}, "templateMinorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The minor version of the environment to update.</p>"}}}, "UpdateEnvironmentOutput": {"type": "structure", "required": ["environment"], "members": {"environment": {"shape": "Environment", "documentation": "<p>The environment detail data that's returned by Proton.</p>"}}}, "UpdateEnvironmentTemplateInput": {"type": "structure", "required": ["name"], "members": {"description": {"shape": "Description", "documentation": "<p>A description of the environment template update.</p>"}, "displayName": {"shape": "DisplayName", "documentation": "<p>The name of the environment template to update as displayed in the developer interface.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the environment template to update.</p>"}}}, "UpdateEnvironmentTemplateOutput": {"type": "structure", "required": ["environmentTemplate"], "members": {"environmentTemplate": {"shape": "EnvironmentTemplate", "documentation": "<p>The environment template detail data that's returned by Proton.</p>"}}}, "UpdateEnvironmentTemplateVersionInput": {"type": "structure", "required": ["majorVersion", "minorVersion", "templateName"], "members": {"description": {"shape": "Description", "documentation": "<p>A description of environment template version to update.</p>"}, "majorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>To update a major version of an environment template, include <code>major Version</code>.</p>"}, "minorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>To update a minor version of an environment template, include <code>minorVersion</code>.</p>"}, "status": {"shape": "TemplateVersionStatus", "documentation": "<p>The status of the environment template minor version to update.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the environment template.</p>"}}}, "UpdateEnvironmentTemplateVersionOutput": {"type": "structure", "required": ["environmentTemplateVersion"], "members": {"environmentTemplateVersion": {"shape": "EnvironmentTemplateVersion", "documentation": "<p>The environment template version detail data that's returned by Proton.</p>"}}}, "UpdateServiceInput": {"type": "structure", "required": ["name"], "members": {"description": {"shape": "Description", "documentation": "<p>The edited service description.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the service to edit.</p>"}, "spec": {"shape": "SpecContents", "documentation": "<p>Lists the service instances to add and the existing service instances to remain. Omit the existing service instances to delete from the list. <i>Don't</i> include edits to the existing service instances or pipeline. For more information, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-svc-update.html\">Edit a service</a> in the <i>Proton User Guide</i>.</p>"}}}, "UpdateServiceInstanceInput": {"type": "structure", "required": ["deploymentType", "name", "serviceName"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>The client token of the service instance to update.</p>", "idempotencyToken": true}, "deploymentType": {"shape": "DeploymentUpdateType", "documentation": "<p>The deployment type. It defines the mode for updating a service instance, as follows:</p> <dl> <dt/> <dd> <p> <code>NONE</code> </p> <p>In this mode, a deployment <i>doesn't</i> occur. Only the requested metadata parameters are updated.</p> </dd> <dt/> <dd> <p> <code>CURRENT_VERSION</code> </p> <p>In this mode, the service instance is deployed and updated with the new spec that you provide. Only requested parameters are updated. <i>Don’t</i> include major or minor version parameters when you use this deployment type.</p> </dd> <dt/> <dd> <p> <code>MINOR_VERSION</code> </p> <p>In this mode, the service instance is deployed and updated with the published, recommended (latest) minor version of the current major version in use, by default. You can also specify a different minor version of the current major version in use.</p> </dd> <dt/> <dd> <p> <code>MAJOR_VERSION</code> </p> <p>In this mode, the service instance is deployed and updated with the published, recommended (latest) major and minor version of the current template, by default. You can specify a different major version that's higher than the major version in use and a minor version.</p> </dd> </dl>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the service instance to update.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service that the service instance belongs to.</p>"}, "spec": {"shape": "SpecContents", "documentation": "<p>The formatted specification that defines the service instance update.</p>"}, "templateMajorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The major version of the service template to update.</p>"}, "templateMinorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The minor version of the service template to update.</p>"}}}, "UpdateServiceInstanceOutput": {"type": "structure", "required": ["serviceInstance"], "members": {"serviceInstance": {"shape": "ServiceInstance", "documentation": "<p>The service instance summary data that's returned by Proton.</p>"}}}, "UpdateServiceOutput": {"type": "structure", "required": ["service"], "members": {"service": {"shape": "Service", "documentation": "<p>The service detail data that's returned by Proton.</p>"}}}, "UpdateServicePipelineInput": {"type": "structure", "required": ["deploymentType", "serviceName", "spec"], "members": {"deploymentType": {"shape": "DeploymentUpdateType", "documentation": "<p>The deployment type.</p> <p>There are four modes for updating a service pipeline. The <code>deploymentType</code> field defines the mode.</p> <dl> <dt/> <dd> <p> <code>NONE</code> </p> <p>In this mode, a deployment <i>doesn't</i> occur. Only the requested metadata parameters are updated.</p> </dd> <dt/> <dd> <p> <code>CURRENT_VERSION</code> </p> <p>In this mode, the service pipeline is deployed and updated with the new spec that you provide. Only requested parameters are updated. <i>Don’t</i> include major or minor version parameters when you use this <code>deployment-type</code>.</p> </dd> <dt/> <dd> <p> <code>MINOR_VERSION</code> </p> <p>In this mode, the service pipeline is deployed and updated with the published, recommended (latest) minor version of the current major version in use, by default. You can specify a different minor version of the current major version in use.</p> </dd> <dt/> <dd> <p> <code>MAJOR_VERSION</code> </p> <p>In this mode, the service pipeline is deployed and updated with the published, recommended (latest) major and minor version of the current template, by default. You can specify a different major version that's higher than the major version in use and a minor version.</p> </dd> </dl>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service to that the pipeline is associated with.</p>"}, "spec": {"shape": "SpecContents", "documentation": "<p>The spec for the service pipeline to update.</p>"}, "templateMajorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The major version of the service template that was used to create the service that the pipeline is associated with.</p>"}, "templateMinorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>The minor version of the service template that was used to create the service that the pipeline is associated with.</p>"}}}, "UpdateServicePipelineOutput": {"type": "structure", "required": ["pipeline"], "members": {"pipeline": {"shape": "ServicePipeline", "documentation": "<p>The pipeline details that are returned by Proton.</p>"}}}, "UpdateServiceSyncBlockerInput": {"type": "structure", "required": ["id", "resolvedReason"], "members": {"id": {"shape": "String", "documentation": "<p>The ID of the service sync blocker.</p>"}, "resolvedReason": {"shape": "String", "documentation": "<p>The reason the service sync blocker was resolved.</p>"}}}, "UpdateServiceSyncBlockerOutput": {"type": "structure", "required": ["serviceName", "serviceSyncBlocker"], "members": {"serviceInstanceName": {"shape": "ResourceName", "documentation": "<p>The name of the service instance that you want to update the service sync blocker for.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service that you want to update the service sync blocker for.</p>"}, "serviceSyncBlocker": {"shape": "SyncBlocker", "documentation": "<p>The detailed data on the service sync blocker that was updated.</p>"}}}, "UpdateServiceSyncConfigInput": {"type": "structure", "required": ["branch", "filePath", "repositoryName", "repositoryProvider", "serviceName"], "members": {"branch": {"shape": "GitBranchName", "documentation": "<p>The name of the code repository branch where the Proton Ops file is found.</p>"}, "filePath": {"shape": "OpsFilePath", "documentation": "<p>The path to the Proton Ops file.</p>"}, "repositoryName": {"shape": "RepositoryName", "documentation": "<p>The name of the repository where the Proton Ops file is found.</p>"}, "repositoryProvider": {"shape": "RepositoryProvider", "documentation": "<p>The name of the repository provider where the Proton Ops file is found.</p>"}, "serviceName": {"shape": "ResourceName", "documentation": "<p>The name of the service the Proton Ops file is for.</p>"}}}, "UpdateServiceSyncConfigOutput": {"type": "structure", "members": {"serviceSyncConfig": {"shape": "ServiceSyncConfig", "documentation": "<p>The detailed data of the Proton Ops file.</p>"}}}, "UpdateServiceTemplateInput": {"type": "structure", "required": ["name"], "members": {"description": {"shape": "Description", "documentation": "<p>A description of the service template update.</p>"}, "displayName": {"shape": "DisplayName", "documentation": "<p>The name of the service template to update that's displayed in the developer interface.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the service template to update.</p>"}}}, "UpdateServiceTemplateOutput": {"type": "structure", "required": ["serviceTemplate"], "members": {"serviceTemplate": {"shape": "ServiceTemplate", "documentation": "<p>The service template detail data that's returned by Proton.</p>"}}}, "UpdateServiceTemplateVersionInput": {"type": "structure", "required": ["majorVersion", "minorVersion", "templateName"], "members": {"compatibleEnvironmentTemplates": {"shape": "CompatibleEnvironmentTemplateInputList", "documentation": "<p>An array of environment template objects that are compatible with this service template version. A service instance based on this service template version can run in environments based on compatible templates.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of a service template version to update.</p>"}, "majorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>To update a major version of a service template, include <code>major Version</code>.</p>"}, "minorVersion": {"shape": "TemplateVersionPart", "documentation": "<p>To update a minor version of a service template, include <code>minorVersion</code>.</p>"}, "status": {"shape": "TemplateVersionStatus", "documentation": "<p>The status of the service template minor version to update.</p>"}, "supportedComponentSources": {"shape": "ServiceTemplateSupportedComponentSourceInputList", "documentation": "<p>An array of supported component sources. Components with supported sources can be attached to service instances based on this service template version.</p> <note> <p>A change to <code>supportedComponentSources</code> doesn't impact existing component attachments to instances based on this template version. A change only affects later associations.</p> </note> <p>For more information about components, see <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/ag-components.html\">Proton components</a> in the <i>Proton User Guide</i>.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The name of the service template.</p>"}}}, "UpdateServiceTemplateVersionOutput": {"type": "structure", "required": ["serviceTemplateVersion"], "members": {"serviceTemplateVersion": {"shape": "ServiceTemplateVersion", "documentation": "<p>The service template version detail data that's returned by Proton.</p>"}}}, "UpdateTemplateSyncConfigInput": {"type": "structure", "required": ["branch", "repositoryName", "repositoryProvider", "templateName", "templateType"], "members": {"branch": {"shape": "GitBranchName", "documentation": "<p>The repository branch for your template.</p>"}, "repositoryName": {"shape": "RepositoryName", "documentation": "<p>The repository name (for example, <code>myrepos/myrepo</code>).</p>"}, "repositoryProvider": {"shape": "RepositoryProvider", "documentation": "<p>The repository provider.</p>"}, "subdirectory": {"shape": "Subdirectory", "documentation": "<p>A subdirectory path to your template bundle version. When included, limits the template bundle search to this repository directory.</p>"}, "templateName": {"shape": "ResourceName", "documentation": "<p>The synced template name.</p>"}, "templateType": {"shape": "TemplateType", "documentation": "<p>The synced template type.</p>"}}}, "UpdateTemplateSyncConfigOutput": {"type": "structure", "members": {"templateSyncConfig": {"shape": "TemplateSyncConfig", "documentation": "<p>The template sync configuration detail data that's returned by Proton.</p>"}}}, "ValidationException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The input is invalid or an out-of-range value was supplied for the input parameter.</p>", "exception": true}}, "documentation": "<p>This is the Proton Service API Reference. It provides descriptions, syntax and usage examples for each of the <a href=\"https://docs.aws.amazon.com/proton/latest/APIReference/API_Operations.html\">actions</a> and <a href=\"https://docs.aws.amazon.com/proton/latest/APIReference/API_Types.html\">data types</a> for the Proton service.</p> <p>The documentation for each action shows the Query API request parameters and the XML response.</p> <p>Alternatively, you can use the Amazon Web Services CLI to access an API. For more information, see the <a href=\"https://docs.aws.amazon.com/cli/latest/userguide/cli-chap-welcome.html\">Amazon Web Services Command Line Interface User Guide</a>.</p> <p>The Proton service is a two-pronged automation framework. Administrators create service templates to provide standardized infrastructure and deployment tooling for serverless and container based applications. Developers, in turn, select from the available service templates to automate their application or service deployments.</p> <p>Because administrators define the infrastructure and tooling that Proton deploys and manages, they need permissions to use all of the listed API operations.</p> <p>When developers select a specific infrastructure and tooling set, Proton deploys their applications. To monitor their applications that are running on Proton, developers need permissions to the service <i>create</i>, <i>list</i>, <i>update</i> and <i>delete</i> API operations and the service instance <i>list</i> and <i>update</i> API operations.</p> <p>To learn more about Proton, see the <a href=\"https://docs.aws.amazon.com/proton/latest/userguide/Welcome.html\">Proton User Guide</a>.</p> <p> <b>Ensuring Idempotency</b> </p> <p>When you make a mutating API request, the request typically returns a result before the asynchronous workflows of the operation are complete. Operations might also time out or encounter other server issues before they're complete, even if the request already returned a result. This might make it difficult to determine whether the request succeeded. Moreover, you might need to retry the request multiple times to ensure that the operation completes successfully. However, if the original request and the subsequent retries are successful, the operation occurs multiple times. This means that you might create more resources than you intended.</p> <p> <i>Idempotency</i> ensures that an API request action completes no more than one time. With an idempotent request, if the original request action completes successfully, any subsequent retries complete successfully without performing any further actions. However, the result might contain updated information, such as the current creation status.</p> <p>The following lists of APIs are grouped according to methods that ensure idempotency.</p> <p> <b>Idempotent create APIs with a client token</b> </p> <p>The API actions in this list support idempotency with the use of a <i>client token</i>. The corresponding Amazon Web Services CLI commands also support idempotency using a client token. A client token is a unique, case-sensitive string of up to 64 ASCII characters. To make an idempotent API request using one of these actions, specify a client token in the request. We recommend that you <i>don't</i> reuse the same client token for other API requests. If you don’t provide a client token for these APIs, a default client token is automatically provided by SDKs.</p> <p>Given a request action that has succeeded:</p> <p>If you retry the request using the same client token and the same parameters, the retry succeeds without performing any further actions other than returning the original resource detail data in the response.</p> <p>If you retry the request using the same client token, but one or more of the parameters are different, the retry throws a <code>ValidationException</code> with an <code>IdempotentParameterMismatch</code> error.</p> <p>Client tokens expire eight hours after a request is made. If you retry the request with the expired token, a new resource is created.</p> <p>If the original resource is deleted and you retry the request, a new resource is created.</p> <p>Idempotent create APIs with a client token:</p> <ul> <li> <p>CreateEnvironmentTemplateVersion</p> </li> <li> <p>CreateServiceTemplateVersion</p> </li> <li> <p>CreateEnvironmentAccountConnection</p> </li> </ul> <p> <b>Idempotent create APIs</b> </p> <p>Given a request action that has succeeded:</p> <p>If you retry the request with an API from this group, and the original resource <i>hasn't</i> been modified, the retry succeeds without performing any further actions other than returning the original resource detail data in the response.</p> <p>If the original resource has been modified, the retry throws a <code>ConflictException</code>.</p> <p>If you retry with different input parameters, the retry throws a <code>ValidationException</code> with an <code>IdempotentParameterMismatch</code> error.</p> <p>Idempotent create APIs:</p> <ul> <li> <p>CreateEnvironmentTemplate</p> </li> <li> <p>CreateServiceTemplate</p> </li> <li> <p>CreateEnvironment</p> </li> <li> <p>CreateService</p> </li> </ul> <p> <b>Idempotent delete APIs</b> </p> <p>Given a request action that has succeeded:</p> <p>When you retry the request with an API from this group and the resource was deleted, its metadata is returned in the response.</p> <p>If you retry and the resource doesn't exist, the response is empty.</p> <p>In both cases, the retry succeeds.</p> <p>Idempotent delete APIs:</p> <ul> <li> <p>DeleteEnvironmentTemplate</p> </li> <li> <p>DeleteEnvironmentTemplateVersion</p> </li> <li> <p>DeleteServiceTemplate</p> </li> <li> <p>DeleteServiceTemplateVersion</p> </li> <li> <p>DeleteEnvironmentAccountConnection</p> </li> </ul> <p> <b>Asynchronous idempotent delete APIs</b> </p> <p>Given a request action that has succeeded:</p> <p>If you retry the request with an API from this group, if the original request delete operation status is <code>DELETE_IN_PROGRESS</code>, the retry returns the resource detail data in the response without performing any further actions.</p> <p>If the original request delete operation is complete, a retry returns an empty response.</p> <p>Asynchronous idempotent delete APIs:</p> <ul> <li> <p>DeleteEnvironment</p> </li> <li> <p>DeleteService</p> </li> </ul>"}