{"version": "2.0", "metadata": {"apiVersion": "2018-05-10", "endpointPrefix": "rum", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "CloudWatch RUM", "serviceId": "RUM", "signatureVersion": "v4", "signingName": "rum", "uid": "rum-2018-05-10"}, "operations": {"BatchCreateRumMetricDefinitions": {"name": "BatchCreateRumMetricDefinitions", "http": {"method": "POST", "requestUri": "/rummetrics/{AppMonitorName}/metrics", "responseCode": 200}, "input": {"shape": "BatchCreateRumMetricDefinitionsRequest"}, "output": {"shape": "BatchCreateRumMetricDefinitionsResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Specifies the extended metrics and custom metrics that you want a CloudWatch RUM app monitor to send to a destination. Valid destinations include CloudWatch and Evidently.</p> <p>By default, RUM app monitors send some metrics to CloudWatch. These default metrics are listed in <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-RUM-metrics.html\">CloudWatch metrics that you can collect with CloudWatch RUM</a>.</p> <p>In addition to these default metrics, you can choose to send extended metrics or custom metrics or both.</p> <ul> <li> <p>Extended metrics enable you to send metrics with additional dimensions not included in the default metrics. You can also send extended metrics to Evidently as well as CloudWatch. The valid dimension names for the additional dimensions for extended metrics are <code>BrowserName</code>, <code>CountryCode</code>, <code>DeviceType</code>, <code>FileType</code>, <code>OSName</code>, and <code>PageId</code>. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-RUM-vended-metrics.html\"> Extended metrics that you can send to CloudWatch and CloudWatch Evidently</a>.</p> </li> <li> <p>Custom metrics are metrics that you define. You can send custom metrics to CloudWatch or to CloudWatch Evidently or to both. With custom metrics, you can use any metric name and namespace, and to derive the metrics you can use any custom events, built-in events, custom attributes, or default attributes. </p> <p>You can't send custom metrics to the <code>AWS/RUM</code> namespace. You must send custom metrics to a custom namespace that you define. The namespace that you use can't start with <code>AWS/</code>. CloudWatch RUM prepends <code>RUM/CustomMetrics/</code> to the custom namespace that you define, so the final namespace for your metrics in CloudWatch is <code>RUM/CustomMetrics/<i>your-custom-namespace</i> </code>.</p> </li> </ul> <p>The maximum number of metric definitions that you can specify in one <code>BatchCreateRumMetricDefinitions</code> operation is 200.</p> <p>The maximum number of metric definitions that one destination can contain is 2000.</p> <p>Extended metrics sent to CloudWatch and RUM custom metrics are charged as CloudWatch custom metrics. Each combination of additional dimension name and dimension value counts as a custom metric. For more information, see <a href=\"https://aws.amazon.com/cloudwatch/pricing/\">Amazon CloudWatch Pricing</a>.</p> <p>You must have already created a destination for the metrics before you send them. For more information, see <a href=\"https://docs.aws.amazon.com/cloudwatchrum/latest/APIReference/API_PutRumMetricsDestination.html\">PutRumMetricsDestination</a>.</p> <p>If some metric definitions specified in a <code>BatchCreateRumMetricDefinitions</code> operations are not valid, those metric definitions fail and return errors, but all valid metric definitions in the same operation still succeed.</p>", "idempotent": true}, "BatchDeleteRumMetricDefinitions": {"name": "BatchDeleteRumMetricDefinitions", "http": {"method": "DELETE", "requestUri": "/rummetrics/{AppMonitorName}/metrics", "responseCode": 200}, "input": {"shape": "BatchDeleteRumMetricDefinitionsRequest"}, "output": {"shape": "BatchDeleteRumMetricDefinitionsResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Removes the specified metrics from being sent to an extended metrics destination.</p> <p>If some metric definition IDs specified in a <code>BatchDeleteRumMetricDefinitions</code> operations are not valid, those metric definitions fail and return errors, but all valid metric definition IDs in the same operation are still deleted.</p> <p>The maximum number of metric definitions that you can specify in one <code>BatchDeleteRumMetricDefinitions</code> operation is 200.</p>", "idempotent": true}, "BatchGetRumMetricDefinitions": {"name": "BatchGetRumMetricDefinitions", "http": {"method": "GET", "requestUri": "/rummetrics/{AppMonitorName}/metrics", "responseCode": 200}, "input": {"shape": "BatchGetRumMetricDefinitionsRequest"}, "output": {"shape": "BatchGetRumMetricDefinitionsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves the list of metrics and dimensions that a RUM app monitor is sending to a single destination.</p>"}, "CreateAppMonitor": {"name": "CreateAppMonitor", "http": {"method": "POST", "requestUri": "/appmonitor", "responseCode": 200}, "input": {"shape": "CreateAppMonitorRequest"}, "output": {"shape": "CreateAppMonitorResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates a Amazon CloudWatch RUM app monitor, which collects telemetry data from your application and sends that data to RUM. The data includes performance and reliability information such as page load time, client-side errors, and user behavior.</p> <p>You use this operation only to create a new app monitor. To update an existing app monitor, use <a href=\"https://docs.aws.amazon.com/cloudwatchrum/latest/APIReference/API_UpdateAppMonitor.html\">UpdateAppMonitor</a> instead.</p> <p>After you create an app monitor, sign in to the CloudWatch RUM console to get the JavaScript code snippet to add to your web application. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-RUM-find-code-snippet.html\">How do I find a code snippet that I've already generated?</a> </p>", "idempotent": true}, "DeleteAppMonitor": {"name": "DeleteAppMonitor", "http": {"method": "DELETE", "requestUri": "/appmonitor/{Name}", "responseCode": 200}, "input": {"shape": "DeleteAppMonitorRequest"}, "output": {"shape": "DeleteAppMonitorResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes an existing app monitor. This immediately stops the collection of data.</p>", "idempotent": true}, "DeleteRumMetricsDestination": {"name": "DeleteRumMetricsDestination", "http": {"method": "DELETE", "requestUri": "/rummetrics/{AppMonitorName}/metricsdestination", "responseCode": 200}, "input": {"shape": "DeleteRumMetricsDestinationRequest"}, "output": {"shape": "DeleteRumMetricsDestinationResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes a destination for CloudWatch RUM extended metrics, so that the specified app monitor stops sending extended metrics to that destination.</p>", "idempotent": true}, "GetAppMonitor": {"name": "GetAppMonitor", "http": {"method": "GET", "requestUri": "/appmonitor/{Name}", "responseCode": 200}, "input": {"shape": "GetAppMonitorRequest"}, "output": {"shape": "GetAppMonitorResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves the complete configuration information for one app monitor.</p>"}, "GetAppMonitorData": {"name": "GetAppMonitorData", "http": {"method": "POST", "requestUri": "/appmonitor/{Name}/data", "responseCode": 200}, "input": {"shape": "GetAppMonitorDataRequest"}, "output": {"shape": "GetAppMonitorDataResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves the raw performance events that RUM has collected from your web application, so that you can do your own processing or analysis of this data.</p>"}, "ListAppMonitors": {"name": "ListAppMonitors", "http": {"method": "POST", "requestUri": "/appmonitors", "responseCode": 200}, "input": {"shape": "ListAppMonitorsRequest"}, "output": {"shape": "ListAppMonitorsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Returns a list of the Amazon CloudWatch RUM app monitors in the account.</p>"}, "ListRumMetricsDestinations": {"name": "ListRumMetricsDestinations", "http": {"method": "GET", "requestUri": "/rummetrics/{AppMonitorName}/metricsdestination", "responseCode": 200}, "input": {"shape": "ListRumMetricsDestinationsRequest"}, "output": {"shape": "ListRumMetricsDestinationsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Returns a list of destinations that you have created to receive RUM extended metrics, for the specified app monitor.</p> <p>For more information about extended metrics, see <a href=\"https://docs.aws.amazon.com/cloudwatchrum/latest/APIReference/API_AddRumMetrcs.html\">AddRumMetrics</a>.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{ResourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}], "documentation": "<p>Displays the tags associated with a CloudWatch RUM resource.</p>"}, "PutRumEvents": {"name": "PutRumEvents", "http": {"method": "POST", "requestUri": "/appmonitors/{Id}/", "responseCode": 200}, "input": {"shape": "PutRumEventsRequest"}, "output": {"shape": "PutRumEventsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Sends telemetry events about your application performance and user behavior to CloudWatch RUM. The code snippet that RUM generates for you to add to your application includes <code>PutRumEvents</code> operations to send this data to RUM.</p> <p>Each <code>PutRumEvents</code> operation can send a batch of events from one user session.</p>", "endpoint": {"hostPrefix": "dataplane."}}, "PutRumMetricsDestination": {"name": "PutRumMetricsDestination", "http": {"method": "POST", "requestUri": "/rummetrics/{AppMonitorName}/metricsdestination", "responseCode": 200}, "input": {"shape": "PutRumMetricsDestinationRequest"}, "output": {"shape": "PutRumMetricsDestinationResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates or updates a destination to receive extended metrics from CloudWatch RUM. You can send extended metrics to CloudWatch or to a CloudWatch Evidently experiment.</p> <p>For more information about extended metrics, see <a href=\"https://docs.aws.amazon.com/cloudwatchrum/latest/APIReference/API_BatchCreateRumMetricDefinitions.html\">BatchCreateRumMetricDefinitions</a>.</p>", "idempotent": true}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{ResourceArn}", "responseCode": 200}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}], "documentation": "<p>Assigns one or more tags (key-value pairs) to the specified CloudWatch RUM resource. Currently, the only resources that can be tagged app monitors.</p> <p>Tags can help you organize and categorize your resources. You can also use them to scope user permissions by granting a user permission to access or change only resources with certain tag values.</p> <p>Tags don't have any semantic meaning to Amazon Web Services and are interpreted strictly as strings of characters.</p> <p>You can use the <code>TagResource</code> action with a resource that already has tags. If you specify a new tag key for the resource, this tag is appended to the list of tags associated with the alarm. If you specify a tag key that is already associated with the resource, the new tag value that you specify replaces the previous value for that tag.</p> <p>You can associate as many as 50 tags with a resource.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html\">Tagging Amazon Web Services resources</a>.</p>", "idempotent": true}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{ResourceArn}", "responseCode": 200}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}], "documentation": "<p>Removes one or more tags from the specified resource.</p>", "idempotent": true}, "UpdateAppMonitor": {"name": "UpdateAppMonitor", "http": {"method": "PATCH", "requestUri": "/appmonitor/{Name}", "responseCode": 200}, "input": {"shape": "UpdateAppMonitorRequest"}, "output": {"shape": "UpdateAppMonitorResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates the configuration of an existing app monitor. When you use this operation, only the parts of the app monitor configuration that you specify in this operation are changed. For any parameters that you omit, the existing values are kept.</p> <p>You can't use this operation to change the tags of an existing app monitor. To change the tags of an existing app monitor, use <a href=\"https://docs.aws.amazon.com/cloudwatchrum/latest/APIReference/API_TagResource.html\">TagResource</a>.</p> <p>To create a new app monitor, use <a href=\"https://docs.aws.amazon.com/cloudwatchrum/latest/APIReference/API_CreateAppMonitor.html\">CreateAppMonitor</a>.</p> <p>After you update an app monitor, sign in to the CloudWatch RUM console to get the updated JavaScript code snippet to add to your web application. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-RUM-find-code-snippet.html\">How do I find a code snippet that I've already generated?</a> </p>"}, "UpdateRumMetricDefinition": {"name": "UpdateRumMetricDefinition", "http": {"method": "PATCH", "requestUri": "/rummetrics/{AppMonitorName}/metrics", "responseCode": 200}, "input": {"shape": "UpdateRumMetricDefinitionRequest"}, "output": {"shape": "UpdateRumMetricDefinitionResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Modifies one existing metric definition for CloudWatch RUM extended metrics. For more information about extended metrics, see <a href=\"https://docs.aws.amazon.com/cloudwatchrum/latest/APIReference/API_BatchCreateRumMetricsDefinitions.html\">BatchCreateRumMetricsDefinitions</a>.</p>", "idempotent": true}}, "shapes": {"AccessDeniedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>You don't have sufficient permissions to perform this action.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "AppMonitor": {"type": "structure", "members": {"AppMonitorConfiguration": {"shape": "AppMonitorConfiguration", "documentation": "<p>A structure that contains much of the configuration data for the app monitor.</p>"}, "Created": {"shape": "ISOTimestampString", "documentation": "<p>The date and time that this app monitor was created.</p>"}, "CustomEvents": {"shape": "CustomEvents", "documentation": "<p>Specifies whether this app monitor allows the web client to define and send custom events.</p> <p>For more information about custom events, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-RUM-custom-events.html\">Send custom events</a>.</p>"}, "DataStorage": {"shape": "DataStorage", "documentation": "<p>A structure that contains information about whether this app monitor stores a copy of the telemetry data that RUM collects using CloudWatch Logs.</p>"}, "Domain": {"shape": "AppMonitorDomain", "documentation": "<p>The top-level internet domain name for which your application has administrative authority.</p>"}, "Id": {"shape": "AppMonitorId", "documentation": "<p>The unique ID of this app monitor.</p>"}, "LastModified": {"shape": "ISOTimestampString", "documentation": "<p>The date and time of the most recent changes to this app monitor's configuration.</p>"}, "Name": {"shape": "AppMonitorName", "documentation": "<p>The name of the app monitor.</p>"}, "State": {"shape": "StateEnum", "documentation": "<p>The current state of the app monitor.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The list of tag keys and values associated with this app monitor.</p>"}}, "documentation": "<p>A RUM app monitor collects telemetry data from your application and sends that data to RUM. The data includes performance and reliability information such as page load time, client-side errors, and user behavior.</p>"}, "AppMonitorConfiguration": {"type": "structure", "members": {"AllowCookies": {"shape": "Boolean", "documentation": "<p>If you set this to <code>true</code>, the RUM web client sets two cookies, a session cookie and a user cookie. The cookies allow the RUM web client to collect data relating to the number of users an application has and the behavior of the application across a sequence of events. Cookies are stored in the top-level domain of the current page.</p>"}, "EnableXRay": {"shape": "Boolean", "documentation": "<p>If you set this to <code>true</code>, RUM enables X-Ray tracing for the user sessions that RUM samples. RUM adds an X-Ray trace header to allowed HTTP requests. It also records an X-Ray segment for allowed HTTP requests. You can see traces and segments from these user sessions in the X-Ray console and the CloudWatch ServiceLens console. For more information, see <a href=\"https://docs.aws.amazon.com/xray/latest/devguide/aws-xray.html\">What is X-Ray?</a> </p>"}, "ExcludedPages": {"shape": "Pages", "documentation": "<p>A list of URLs in your website or application to exclude from RUM data collection.</p> <p>You can't include both <code>ExcludedPages</code> and <code>IncludedPages</code> in the same operation.</p>"}, "FavoritePages": {"shape": "FavoritePages", "documentation": "<p>A list of pages in your application that are to be displayed with a \"favorite\" icon in the CloudWatch RUM console.</p>"}, "GuestRoleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the guest IAM role that is attached to the Amazon Cognito identity pool that is used to authorize the sending of data to RUM.</p>"}, "IdentityPoolId": {"shape": "IdentityPoolId", "documentation": "<p>The ID of the Amazon Cognito identity pool that is used to authorize the sending of data to RUM.</p>"}, "IncludedPages": {"shape": "Pages", "documentation": "<p>If this app monitor is to collect data from only certain pages in your application, this structure lists those pages. </p> <p>You can't include both <code>ExcludedPages</code> and <code>IncludedPages</code> in the same operation.</p>"}, "SessionSampleRate": {"shape": "SessionSampleRate", "documentation": "<p>Specifies the portion of user sessions to use for RUM data collection. Choosing a higher portion gives you more data but also incurs more costs.</p> <p>The range for this value is 0 to 1 inclusive. Setting this to 1 means that 100% of user sessions are sampled, and setting it to 0.1 means that 10% of user sessions are sampled.</p> <p>If you omit this parameter, the default of 0.1 is used, and 10% of sessions will be sampled.</p>"}, "Telemetries": {"shape": "Telemetries", "documentation": "<p>An array that lists the types of telemetry data that this app monitor is to collect.</p> <ul> <li> <p> <code>errors</code> indicates that RUM collects data about unhandled JavaScript errors raised by your application.</p> </li> <li> <p> <code>performance</code> indicates that RUM collects performance data about how your application and its resources are loaded and rendered. This includes Core Web Vitals.</p> </li> <li> <p> <code>http</code> indicates that RUM collects data about HTTP errors thrown by your application.</p> </li> </ul>"}}, "documentation": "<p>This structure contains much of the configuration data for the app monitor.</p>"}, "AppMonitorDetails": {"type": "structure", "members": {"id": {"shape": "String", "documentation": "<p>The unique ID of the app monitor.</p>"}, "name": {"shape": "String", "documentation": "<p>The name of the app monitor.</p>"}, "version": {"shape": "String", "documentation": "<p>The version of the app monitor.</p>"}}, "documentation": "<p>A structure that contains information about the RUM app monitor.</p>"}, "AppMonitorDomain": {"type": "string", "max": 253, "min": 1, "pattern": "^(localhost)|^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^(?![-.])([A-Za-z0-9-\\.\\-]{0,63})((?![-])([a-zA-Z0-9]{1}|^[a-zA-Z0-9]{0,1}))\\.(?![-])[A-Za-z-0-9]{1,63}((?![-])([a-zA-Z0-9]{1}|^[a-zA-Z0-9]{0,1}))|^(\\*\\.)(?![-.])([A-Za-z0-9-\\.\\-]{0,63})((?![-])([a-zA-Z0-9]{1}|^[a-zA-Z0-9]{0,1}))\\.(?![-])[A-Za-z-0-9]{1,63}((?![-])([a-zA-Z0-9]{1}|^[a-zA-Z0-9]{0,1}))"}, "AppMonitorId": {"type": "string", "max": 36, "min": 36, "pattern": "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"}, "AppMonitorName": {"type": "string", "max": 255, "min": 1, "pattern": "^(?!\\.)[\\.\\-_#A-Za-z0-9]+$"}, "AppMonitorSummary": {"type": "structure", "members": {"Created": {"shape": "ISOTimestampString", "documentation": "<p>The date and time that the app monitor was created.</p>"}, "Id": {"shape": "AppMonitorId", "documentation": "<p>The unique ID of this app monitor.</p>"}, "LastModified": {"shape": "ISOTimestampString", "documentation": "<p>The date and time of the most recent changes to this app monitor's configuration.</p>"}, "Name": {"shape": "AppMonitorName", "documentation": "<p>The name of this app monitor.</p>"}, "State": {"shape": "StateEnum", "documentation": "<p>The current state of this app monitor.</p>"}}, "documentation": "<p>A structure that includes some data about app monitors and their settings.</p>"}, "AppMonitorSummaryList": {"type": "list", "member": {"shape": "AppMonitorSummary"}}, "Arn": {"type": "string", "pattern": "arn:[^:]*:[^:]*:[^:]*:[^:]*:.*"}, "BatchCreateRumMetricDefinitionsError": {"type": "structure", "required": ["ErrorCode", "ErrorMessage", "MetricDefinition"], "members": {"ErrorCode": {"shape": "String", "documentation": "<p>The error code.</p>"}, "ErrorMessage": {"shape": "String", "documentation": "<p>The error message for this metric definition.</p>"}, "MetricDefinition": {"shape": "MetricDefinitionRequest", "documentation": "<p>The metric definition that caused this error.</p>"}}, "documentation": "<p>A structure that defines one error caused by a <a href=\"https://docs.aws.amazon.com/cloudwatchrum/latest/APIReference/API_BatchCreateRumMetricsDefinitions.html\">BatchCreateRumMetricsDefinitions</a> operation.</p>"}, "BatchCreateRumMetricDefinitionsErrors": {"type": "list", "member": {"shape": "BatchCreateRumMetricDefinitionsError"}}, "BatchCreateRumMetricDefinitionsRequest": {"type": "structure", "required": ["AppMonitorName", "Destination", "MetricDefinitions"], "members": {"AppMonitorName": {"shape": "AppMonitorName", "documentation": "<p>The name of the CloudWatch RUM app monitor that is to send the metrics.</p>", "location": "uri", "locationName": "AppMonitorName"}, "Destination": {"shape": "MetricDestination", "documentation": "<p>The destination to send the metrics to. Valid values are <code>CloudWatch</code> and <code>Evidently</code>. If you specify <code>Evidently</code>, you must also specify the ARN of the CloudWatchEvidently experiment that will receive the metrics and an IAM role that has permission to write to the experiment.</p>"}, "DestinationArn": {"shape": "DestinationArn", "documentation": "<p>This parameter is required if <code>Destination</code> is <code>Evidently</code>. If <code>Destination</code> is <code>CloudWatch</code>, do not use this parameter.</p> <p>This parameter specifies the ARN of the Evidently experiment that is to receive the metrics. You must have already defined this experiment as a valid destination. For more information, see <a href=\"https://docs.aws.amazon.com/cloudwatchrum/latest/APIReference/API_PutRumMetricsDestination.html\">PutRumMetricsDestination</a>.</p>"}, "MetricDefinitions": {"shape": "MetricDefinitionsRequest", "documentation": "<p>An array of structures which define the metrics that you want to send.</p>"}}}, "BatchCreateRumMetricDefinitionsResponse": {"type": "structure", "required": ["Errors"], "members": {"Errors": {"shape": "BatchCreateRumMetricDefinitionsErrors", "documentation": "<p>An array of error objects, if the operation caused any errors.</p>"}, "MetricDefinitions": {"shape": "MetricDefinitions", "documentation": "<p>An array of structures that define the extended metrics.</p>"}}}, "BatchDeleteRumMetricDefinitionsError": {"type": "structure", "required": ["ErrorCode", "ErrorMessage", "MetricDefinitionId"], "members": {"ErrorCode": {"shape": "String", "documentation": "<p>The error code.</p>"}, "ErrorMessage": {"shape": "String", "documentation": "<p>The error message for this metric definition.</p>"}, "MetricDefinitionId": {"shape": "MetricDefinitionId", "documentation": "<p>The ID of the metric definition that caused this error.</p>"}}, "documentation": "<p>A structure that defines one error caused by a <a href=\"https://docs.aws.amazon.com/cloudwatchrum/latest/APIReference/API_BatchDeleteRumMetricsDefinitions.html\">BatchCreateRumMetricsDefinitions</a> operation.</p>"}, "BatchDeleteRumMetricDefinitionsErrors": {"type": "list", "member": {"shape": "BatchDeleteRumMetricDefinitionsError"}}, "BatchDeleteRumMetricDefinitionsRequest": {"type": "structure", "required": ["AppMonitorName", "Destination", "MetricDefinitionIds"], "members": {"AppMonitorName": {"shape": "AppMonitorName", "documentation": "<p>The name of the CloudWatch RUM app monitor that is sending these metrics.</p>", "location": "uri", "locationName": "AppMonitorName"}, "Destination": {"shape": "MetricDestination", "documentation": "<p>Defines the destination where you want to stop sending the specified metrics. Valid values are <code>CloudWatch</code> and <code>Evidently</code>. If you specify <code>Evidently</code>, you must also specify the ARN of the CloudWatchEvidently experiment that is to be the destination and an IAM role that has permission to write to the experiment.</p>", "location": "querystring", "locationName": "destination"}, "DestinationArn": {"shape": "DestinationArn", "documentation": "<p>This parameter is required if <code>Destination</code> is <code>Evidently</code>. If <code>Destination</code> is <code>CloudWatch</code>, do not use this parameter. </p> <p>This parameter specifies the ARN of the Evidently experiment that was receiving the metrics that are being deleted.</p>", "location": "querystring", "locationName": "destinationArn"}, "MetricDefinitionIds": {"shape": "MetricDefinitionIds", "documentation": "<p>An array of structures which define the metrics that you want to stop sending.</p>", "location": "querystring", "locationName": "metricDefinitionIds"}}}, "BatchDeleteRumMetricDefinitionsResponse": {"type": "structure", "required": ["Errors"], "members": {"Errors": {"shape": "BatchDeleteRumMetricDefinitionsErrors", "documentation": "<p>An array of error objects, if the operation caused any errors.</p>"}, "MetricDefinitionIds": {"shape": "MetricDefinitionIds", "documentation": "<p>The IDs of the metric definitions that were deleted.</p>"}}}, "BatchGetRumMetricDefinitionsRequest": {"type": "structure", "required": ["AppMonitorName", "Destination"], "members": {"AppMonitorName": {"shape": "AppMonitorName", "documentation": "<p>The name of the CloudWatch RUM app monitor that is sending the metrics.</p>", "location": "uri", "locationName": "AppMonitorName"}, "Destination": {"shape": "MetricDestination", "documentation": "<p>The type of destination that you want to view metrics for. Valid values are <code>CloudWatch</code> and <code>Evidently</code>.</p>", "location": "querystring", "locationName": "destination"}, "DestinationArn": {"shape": "DestinationArn", "documentation": "<p>This parameter is required if <code>Destination</code> is <code>Evidently</code>. If <code>Destination</code> is <code>CloudWatch</code>, do not use this parameter.</p> <p>This parameter specifies the ARN of the Evidently experiment that corresponds to the destination.</p>", "location": "querystring", "locationName": "destinationArn"}, "MaxResults": {"shape": "MaxResultsInteger", "documentation": "<p>The maximum number of results to return in one operation. The default is 50. The maximum that you can specify is 100.</p> <p>To retrieve the remaining results, make another call with the returned <code>NextToken</code> value. </p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "String", "documentation": "<p>Use the token returned by the previous operation to request the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "BatchGetRumMetricDefinitionsResponse": {"type": "structure", "members": {"MetricDefinitions": {"shape": "MetricDefinitions", "documentation": "<p>An array of structures that display information about the metrics that are sent by the specified app monitor to the specified destination.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>A token that you can use in a subsequent operation to retrieve the next set of results.</p>"}}}, "Boolean": {"type": "boolean", "box": true}, "ConflictException": {"type": "structure", "required": ["message", "resourceName"], "members": {"message": {"shape": "String"}, "resourceName": {"shape": "String", "documentation": "<p>The name of the resource that is associated with the error.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>The type of the resource that is associated with the error.</p>"}}, "documentation": "<p>This operation attempted to create a resource that already exists.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "CreateAppMonitorRequest": {"type": "structure", "required": ["Domain", "Name"], "members": {"AppMonitorConfiguration": {"shape": "AppMonitorConfiguration", "documentation": "<p>A structure that contains much of the configuration data for the app monitor. If you are using Amazon Cognito for authorization, you must include this structure in your request, and it must include the ID of the Amazon Cognito identity pool to use for authorization. If you don't include <code>AppMonitorConfiguration</code>, you must set up your own authorization method. For more information, see <a href=\"https://docs.aws.amazon.com/monitoring/CloudWatch-RUM-get-started-authorization.html\">Authorize your application to send data to Amazon Web Services</a>.</p> <p>If you omit this argument, the sample rate used for RUM is set to 10% of the user sessions.</p>"}, "CustomEvents": {"shape": "CustomEvents", "documentation": "<p>Specifies whether this app monitor allows the web client to define and send custom events. If you omit this parameter, custom events are <code>DISABLED</code>.</p> <p>For more information about custom events, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-RUM-custom-events.html\">Send custom events</a>.</p>"}, "CwLogEnabled": {"shape": "Boolean", "documentation": "<p>Data collected by RUM is kept by RUM for 30 days and then deleted. This parameter specifies whether RUM sends a copy of this telemetry data to Amazon CloudWatch Logs in your account. This enables you to keep the telemetry data for more than 30 days, but it does incur Amazon CloudWatch Logs charges.</p> <p>If you omit this parameter, the default is <code>false</code>.</p>"}, "Domain": {"shape": "AppMonitorDomain", "documentation": "<p>The top-level internet domain name for which your application has administrative authority.</p>"}, "Name": {"shape": "AppMonitorName", "documentation": "<p>A name for the app monitor.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>Assigns one or more tags (key-value pairs) to the app monitor.</p> <p>Tags can help you organize and categorize your resources. You can also use them to scope user permissions by granting a user permission to access or change only resources with certain tag values.</p> <p>Tags don't have any semantic meaning to Amazon Web Services and are interpreted strictly as strings of characters.</p> <p>You can associate as many as 50 tags with an app monitor.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html\">Tagging Amazon Web Services resources</a>.</p>"}}}, "CreateAppMonitorResponse": {"type": "structure", "members": {"Id": {"shape": "AppMonitorId", "documentation": "<p>The unique ID of the new app monitor.</p>"}}}, "CustomEvents": {"type": "structure", "members": {"Status": {"shape": "CustomEventsStatus", "documentation": "<p>Specifies whether this app monitor allows the web client to define and send custom events. The default is for custom events to be <code>DISABLED</code>.</p>"}}, "documentation": "<p>A structure that contains information about custom events for this app monitor.</p>"}, "CustomEventsStatus": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "CwLog": {"type": "structure", "members": {"CwLogEnabled": {"shape": "Boolean", "documentation": "<p>Indicated whether the app monitor stores copies of the data that RUM collects in CloudWatch Logs.</p>"}, "CwLogGroup": {"shape": "String", "documentation": "<p>The name of the log group where the copies are stored.</p>"}}, "documentation": "<p>A structure that contains the information about whether the app monitor stores copies of the data that RUM collects in CloudWatch Logs. If it does, this structure also contains the name of the log group.</p>"}, "DataStorage": {"type": "structure", "members": {"CwLog": {"shape": "CwLog", "documentation": "<p>A structure that contains the information about whether the app monitor stores copies of the data that RUM collects in CloudWatch Logs. If it does, this structure also contains the name of the log group.</p>"}}, "documentation": "<p>A structure that contains information about whether this app monitor stores a copy of the telemetry data that RUM collects using CloudWatch Logs.</p>"}, "DeleteAppMonitorRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "AppMonitorName", "documentation": "<p>The name of the app monitor to delete.</p>", "location": "uri", "locationName": "Name"}}}, "DeleteAppMonitorResponse": {"type": "structure", "members": {}}, "DeleteRumMetricsDestinationRequest": {"type": "structure", "required": ["AppMonitorName", "Destination"], "members": {"AppMonitorName": {"shape": "AppMonitorName", "documentation": "<p>The name of the app monitor that is sending metrics to the destination that you want to delete.</p>", "location": "uri", "locationName": "AppMonitorName"}, "Destination": {"shape": "MetricDestination", "documentation": "<p>The type of destination to delete. Valid values are <code>CloudWatch</code> and <code>Evidently</code>.</p>", "location": "querystring", "locationName": "destination"}, "DestinationArn": {"shape": "DestinationArn", "documentation": "<p>This parameter is required if <code>Destination</code> is <code>Evidently</code>. If <code>Destination</code> is <code>CloudWatch</code>, do not use this parameter. This parameter specifies the ARN of the Evidently experiment that corresponds to the destination to delete.</p>", "location": "querystring", "locationName": "destinationArn"}}}, "DeleteRumMetricsDestinationResponse": {"type": "structure", "members": {}}, "DestinationArn": {"type": "string", "max": 2048, "min": 0, "pattern": "arn:[^:]*:[^:]*:[^:]*:[^:]*:.*"}, "DimensionKey": {"type": "string", "max": 280, "min": 1}, "DimensionKeysMap": {"type": "map", "key": {"shape": "DimensionKey"}, "value": {"shape": "DimensionName"}, "max": 29, "min": 0}, "DimensionName": {"type": "string", "max": 255, "min": 1, "pattern": "^(?!:).*[^\\s].*"}, "EventData": {"type": "string"}, "EventDataList": {"type": "list", "member": {"shape": "EventData"}}, "EventPattern": {"type": "string", "max": 4000, "min": 0}, "FavoritePages": {"type": "list", "member": {"shape": "String"}, "max": 50, "min": 0}, "GetAppMonitorDataRequest": {"type": "structure", "required": ["Name", "TimeRange"], "members": {"Filters": {"shape": "QueryFilters", "documentation": "<p>An array of structures that you can use to filter the results to those that match one or more sets of key-value pairs that you specify.</p>"}, "MaxResults": {"shape": "MaxQueryResults", "documentation": "<p>The maximum number of results to return in one operation. </p>"}, "Name": {"shape": "AppMonitorName", "documentation": "<p>The name of the app monitor that collected the data that you want to retrieve.</p>", "location": "uri", "locationName": "Name"}, "NextToken": {"shape": "Token", "documentation": "<p>Use the token returned by the previous operation to request the next page of results.</p>"}, "TimeRange": {"shape": "TimeRange", "documentation": "<p>A structure that defines the time range that you want to retrieve results from.</p>"}}}, "GetAppMonitorDataResponse": {"type": "structure", "members": {"Events": {"shape": "EventDataList", "documentation": "<p>The events that RUM collected that match your request.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>A token that you can use in a subsequent operation to retrieve the next set of results.</p>"}}}, "GetAppMonitorRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "AppMonitorName", "documentation": "<p>The app monitor to retrieve information for.</p>", "location": "uri", "locationName": "Name"}}}, "GetAppMonitorResponse": {"type": "structure", "members": {"AppMonitor": {"shape": "AppMonitor", "documentation": "<p>A structure containing all the configuration information for the app monitor.</p>"}}}, "ISOTimestampString": {"type": "string", "max": 19, "min": 19}, "IamRoleArn": {"type": "string", "pattern": "arn:[^:]*:[^:]*:[^:]*:[^:]*:.*"}, "IdentityPoolId": {"type": "string", "max": 55, "min": 1, "pattern": "[\\w-]+:[0-9a-f-]+"}, "Integer": {"type": "integer", "box": true}, "InternalServerException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "retryAfterSeconds": {"shape": "Integer", "documentation": "<p>The value of a parameter in the request caused an error.</p>", "location": "header", "locationName": "Retry-After"}}, "documentation": "<p>Internal service exception.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "JsonValue": {"type": "string"}, "ListAppMonitorsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResultsInteger", "documentation": "<p>The maximum number of results to return in one operation. The default is 50. The maximum that you can specify is 100.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "String", "documentation": "<p>Use the token returned by the previous operation to request the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListAppMonitorsResponse": {"type": "structure", "members": {"AppMonitorSummaries": {"shape": "AppMonitorSummaryList", "documentation": "<p>An array of structures that contain information about the returned app monitors.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>A token that you can use in a subsequent operation to retrieve the next set of results.</p>"}}}, "ListRumMetricsDestinationsRequest": {"type": "structure", "required": ["AppMonitorName"], "members": {"AppMonitorName": {"shape": "AppMonitorName", "documentation": "<p>The name of the app monitor associated with the destinations that you want to retrieve.</p>", "location": "uri", "locationName": "AppMonitorName"}, "MaxResults": {"shape": "MaxResultsInteger", "documentation": "<p>The maximum number of results to return in one operation. The default is 50. The maximum that you can specify is 100.</p> <p>To retrieve the remaining results, make another call with the returned <code>NextToken</code> value. </p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "String", "documentation": "<p>Use the token returned by the previous operation to request the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListRumMetricsDestinationsResponse": {"type": "structure", "members": {"Destinations": {"shape": "MetricDestinationSummaryList", "documentation": "<p>The list of CloudWatch RUM extended metrics destinations associated with the app monitor that you specified.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>A token that you can use in a subsequent operation to retrieve the next set of results.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the resource that you want to see the tags of.</p>", "location": "uri", "locationName": "ResourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the resource that you are viewing.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The list of tag keys and values associated with the resource you specified.</p>"}}}, "MaxQueryResults": {"type": "integer", "max": 100, "min": 0}, "MaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "MetricDefinition": {"type": "structure", "required": ["MetricDefinitionId", "Name"], "members": {"DimensionKeys": {"shape": "DimensionKeysMap", "documentation": "<p>This field is a map of field paths to dimension names. It defines the dimensions to associate with this metric in CloudWatch The value of this field is used only if the metric destination is <code>CloudWatch</code>. If the metric destination is <code>Evidently</code>, the value of <code>DimensionKeys</code> is ignored.</p>"}, "EventPattern": {"shape": "EventPattern", "documentation": "<p>The pattern that defines the metric. RUM checks events that happen in a user's session against the pattern, and events that match the pattern are sent to the metric destination.</p> <p>If the metrics destination is <code>CloudWatch</code> and the event also matches a value in <code>DimensionKeys</code>, then the metric is published with the specified dimensions. </p>"}, "MetricDefinitionId": {"shape": "MetricDefinitionId", "documentation": "<p>The ID of this metric definition.</p>"}, "Name": {"shape": "MetricName", "documentation": "<p>The name of the metric that is defined in this structure.</p>"}, "Namespace": {"shape": "Namespace", "documentation": "<p>If this metric definition is for a custom metric instead of an extended metric, this field displays the metric namespace that the custom metric is published to.</p>"}, "UnitLabel": {"shape": "UnitLabel", "documentation": "<p>Use this field only if you are sending this metric to CloudWatch. It defines the CloudWatch metric unit that this metric is measured in. </p>"}, "ValueKey": {"shape": "ValueKey", "documentation": "<p>The field within the event object that the metric value is sourced from.</p>"}}, "documentation": "<p>A structure that displays the definition of one extended metric that RUM sends to CloudWatch or CloudWatch Evidently. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-RUM-vended-metrics.html\"> Additional metrics that you can send to CloudWatch and CloudWatch Evidently</a>.</p>"}, "MetricDefinitionId": {"type": "string", "max": 255, "min": 1}, "MetricDefinitionIds": {"type": "list", "member": {"shape": "MetricDefinitionId"}}, "MetricDefinitionRequest": {"type": "structure", "required": ["Name"], "members": {"DimensionKeys": {"shape": "DimensionKeysMap", "documentation": "<p>Use this field only if you are sending the metric to CloudWatch.</p> <p>This field is a map of field paths to dimension names. It defines the dimensions to associate with this metric in CloudWatch. For extended metrics, valid values for the entries in this field are the following:</p> <ul> <li> <p> <code>\"metadata.pageId\": \"PageId\"</code> </p> </li> <li> <p> <code>\"metadata.browserName\": \"BrowserName\"</code> </p> </li> <li> <p> <code>\"metadata.deviceType\": \"DeviceType\"</code> </p> </li> <li> <p> <code>\"metadata.osName\": \"OSName\"</code> </p> </li> <li> <p> <code>\"metadata.countryCode\": \"CountryCode\"</code> </p> </li> <li> <p> <code>\"event_details.fileType\": \"FileType\"</code> </p> </li> </ul> <p> For both extended metrics and custom metrics, all dimensions listed in this field must also be included in <code>EventPattern</code>.</p>"}, "EventPattern": {"shape": "EventPattern", "documentation": "<p>The pattern that defines the metric, specified as a JSON object. RUM checks events that happen in a user's session against the pattern, and events that match the pattern are sent to the metric destination.</p> <p>When you define extended metrics, the metric definition is not valid if <code>EventPattern</code> is omitted.</p> <p>Example event patterns:</p> <ul> <li> <p> <code>'{ \"event_type\": [\"com.amazon.rum.js_error_event\"], \"metadata\": { \"browserName\": [ \"Chrome\", \"Safari\" ], } }'</code> </p> </li> <li> <p> <code>'{ \"event_type\": [\"com.amazon.rum.performance_navigation_event\"], \"metadata\": { \"browserName\": [ \"Chrome\", \"Firefox\" ] }, \"event_details\": { \"duration\": [{ \"numeric\": [ \"&lt;\", 2000 ] }] } }'</code> </p> </li> <li> <p> <code>'{ \"event_type\": [\"com.amazon.rum.performance_navigation_event\"], \"metadata\": { \"browserName\": [ \"Chrome\", \"Safari\" ], \"countryCode\": [ \"US\" ] }, \"event_details\": { \"duration\": [{ \"numeric\": [ \"&gt;=\", 2000, \"&lt;\", 8000 ] }] } }'</code> </p> </li> </ul> <p>If the metrics destination' is <code>CloudWatch</code> and the event also matches a value in <code>DimensionKeys</code>, then the metric is published with the specified dimensions. </p>"}, "Name": {"shape": "MetricName", "documentation": "<p>The name for the metric that is defined in this structure. For custom metrics, you can specify any name that you like. For extended metrics, valid values are the following:</p> <ul> <li> <p> <code>PerformanceNavigationDuration</code> </p> </li> <li> <p> <code>PerformanceResourceDuration </code> </p> </li> <li> <p> <code>NavigationSatisfiedTransaction</code> </p> </li> <li> <p> <code>NavigationToleratedTransaction</code> </p> </li> <li> <p> <code>NavigationFrustratedTransaction</code> </p> </li> <li> <p> <code>WebVitalsCumulativeLayoutShift</code> </p> </li> <li> <p> <code>WebVitalsFirstInputDelay</code> </p> </li> <li> <p> <code>WebVitalsLargestContentfulPaint</code> </p> </li> <li> <p> <code>JsErrorCount</code> </p> </li> <li> <p> <code>HttpErrorCount</code> </p> </li> <li> <p> <code>SessionCount</code> </p> </li> </ul>"}, "Namespace": {"shape": "Namespace", "documentation": "<p>If this structure is for a custom metric instead of an extended metrics, use this parameter to define the metric namespace for that custom metric. Do not specify this parameter if this structure is for an extended metric.</p> <p>You cannot use any string that starts with <code>AWS/</code> for your namespace.</p>"}, "UnitLabel": {"shape": "UnitLabel", "documentation": "<p>The CloudWatch metric unit to use for this metric. If you omit this field, the metric is recorded with no unit.</p>"}, "ValueKey": {"shape": "ValueKey", "documentation": "<p>The field within the event object that the metric value is sourced from.</p> <p>If you omit this field, a hardcoded value of 1 is pushed as the metric value. This is useful if you just want to count the number of events that the filter catches. </p> <p>If this metric is sent to CloudWatch Evidently, this field will be passed to Evidently raw and Evidently will handle data extraction from the event.</p>"}}, "documentation": "<p>Use this structure to define one extended metric or custom metric that RUM will send to CloudWatch or CloudWatch Evidently. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-RUM-vended-metrics.html\"> Additional metrics that you can send to CloudWatch and CloudWatch Evidently</a>.</p> <p>This structure is validated differently for extended metrics and custom metrics. For extended metrics that are sent to the <code>AWS/RUM</code> namespace, the following validations apply:</p> <ul> <li> <p>The <code>Namespace</code> parameter must be omitted or set to <code>AWS/RUM</code>.</p> </li> <li> <p>Only certain combinations of values for <code>Name</code>, <code>ValueKey</code>, and <code>EventPattern</code> are valid. In addition to what is displayed in the list below, the <code>EventPattern</code> can also include information used by the <code>DimensionKeys</code> field.</p> <ul> <li> <p>If <code>Name</code> is <code>PerformanceNavigationDuration</code>, then <code>ValueKey</code>must be <code>event_details.duration</code> and the <code>EventPattern</code> must include <code>{\"event_type\":[\"com.amazon.rum.performance_navigation_event\"]}</code> </p> </li> <li> <p>If <code>Name</code> is <code>PerformanceResourceDuration</code>, then <code>ValueKey</code>must be <code>event_details.duration</code> and the <code>EventPattern</code> must include <code>{\"event_type\":[\"com.amazon.rum.performance_resource_event\"]}</code> </p> </li> <li> <p>If <code>Name</code> is <code>NavigationSatisfiedTransaction</code>, then <code>ValueKey</code>must be null and the <code>EventPattern</code> must include <code>{ \"event_type\": [\"com.amazon.rum.performance_navigation_event\"], \"event_details\": { \"duration\": [{ \"numeric\": [\"&gt;\",2000] }] } }</code> </p> </li> <li> <p>If <code>Name</code> is <code>NavigationToleratedTransaction</code>, then <code>ValueKey</code>must be null and the <code>EventPattern</code> must include <code>{ \"event_type\": [\"com.amazon.rum.performance_navigation_event\"], \"event_details\": { \"duration\": [{ \"numeric\": [\"&gt;=\",2000,\"&lt;\"8000] }] } }</code> </p> </li> <li> <p>If <code>Name</code> is <code>NavigationFrustratedTransaction</code>, then <code>ValueKey</code>must be null and the <code>EventPattern</code> must include <code>{ \"event_type\": [\"com.amazon.rum.performance_navigation_event\"], \"event_details\": { \"duration\": [{ \"numeric\": [\"&gt;=\",8000] }] } }</code> </p> </li> <li> <p>If <code>Name</code> is <code>WebVitalsCumulativeLayoutShift</code>, then <code>ValueKey</code>must be <code>event_details.value</code> and the <code>EventPattern</code> must include <code>{\"event_type\":[\"com.amazon.rum.cumulative_layout_shift_event\"]}</code> </p> </li> <li> <p>If <code>Name</code> is <code>WebVitalsFirstInputDelay</code>, then <code>ValueKey</code>must be <code>event_details.value</code> and the <code>EventPattern</code> must include <code>{\"event_type\":[\"com.amazon.rum.first_input_delay_event\"]}</code> </p> </li> <li> <p>If <code>Name</code> is <code>WebVitalsLargestContentfulPaint</code>, then <code>ValueKey</code>must be <code>event_details.value</code> and the <code>EventPattern</code> must include <code>{\"event_type\":[\"com.amazon.rum.largest_contentful_paint_event\"]}</code> </p> </li> <li> <p>If <code>Name</code> is <code>JsErrorCount</code>, then <code>ValueKey</code>must be null and the <code>EventPattern</code> must include <code>{\"event_type\":[\"com.amazon.rum.js_error_event\"]}</code> </p> </li> <li> <p>If <code>Name</code> is <code>HttpErrorCount</code>, then <code>ValueKey</code>must be null and the <code>EventPattern</code> must include <code>{\"event_type\":[\"com.amazon.rum.http_event\"]}</code> </p> </li> <li> <p>If <code>Name</code> is <code>SessionCount</code>, then <code>ValueKey</code>must be null and the <code>EventPattern</code> must include <code>{\"event_type\":[\"com.amazon.rum.session_start_event\"]}</code> </p> </li> </ul> </li> </ul> <p>For custom metrics, the following validation rules apply:</p> <ul> <li> <p>The namespace can't be omitted and can't be <code>AWS/RUM</code>. You can use the <code>AWS/RUM</code> namespace only for extended metrics.</p> </li> <li> <p>All dimensions listed in the <code>DimensionKeys</code> field must be present in the value of <code>EventPattern</code>.</p> </li> <li> <p>The values that you specify for <code>ValueKey</code>, <code>EventPattern</code>, and <code>DimensionKeys</code> must be fields in RUM events, so all first-level keys in these fields must be one of the keys in the list later in this section.</p> </li> <li> <p>If you set a value for <code>EventPattern</code>, it must be a JSON object.</p> </li> <li> <p>For every non-empty <code>event_details</code>, there must be a non-empty <code>event_type</code>.</p> </li> <li> <p>If <code>EventPattern</code> contains an <code>event_details</code> field, it must also contain an <code>event_type</code>. For every built-in <code>event_type</code> that you use, you must use a value for <code>event_details</code> that corresponds to that <code>event_type</code>. For information about event details that correspond to event types, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-RUM-datacollected.html#CloudWatch-RUM-datacollected-eventDetails\"> RUM event details</a>.</p> </li> <li> <p>In <code>EventPattern</code>, any JSON array must contain only one value.</p> </li> </ul> <p>Valid key values for first-level keys in the <code>ValueKey</code>, <code>EventPattern</code>, and <code>DimensionKeys</code> fields:</p> <ul> <li> <p> <code>account_id</code> </p> </li> <li> <p> <code>application_Id</code> </p> </li> <li> <p> <code>application_version</code> </p> </li> <li> <p> <code>application_name</code> </p> </li> <li> <p> <code>batch_id</code> </p> </li> <li> <p> <code>event_details</code> </p> </li> <li> <p> <code>event_id</code> </p> </li> <li> <p> <code>event_interaction</code> </p> </li> <li> <p> <code>event_timestamp</code> </p> </li> <li> <p> <code>event_type</code> </p> </li> <li> <p> <code>event_version</code> </p> </li> <li> <p> <code>log_stream</code> </p> </li> <li> <p> <code>metadata</code> </p> </li> <li> <p> <code>sessionId</code> </p> </li> <li> <p> <code>user_details</code> </p> </li> <li> <p> <code>userId</code> </p> </li> </ul>"}, "MetricDefinitions": {"type": "list", "member": {"shape": "MetricDefinition"}}, "MetricDefinitionsRequest": {"type": "list", "member": {"shape": "MetricDefinitionRequest"}}, "MetricDestination": {"type": "string", "enum": ["CloudWatch", "Evidently"]}, "MetricDestinationSummary": {"type": "structure", "members": {"Destination": {"shape": "MetricDestination", "documentation": "<p>Specifies whether the destination is <code>CloudWatch</code> or <code>Evidently</code>.</p>"}, "DestinationArn": {"shape": "DestinationArn", "documentation": "<p>If the destination is <code>Evidently</code>, this specifies the ARN of the Evidently experiment that receives the metrics.</p>"}, "IamRoleArn": {"shape": "IamRoleArn", "documentation": "<p>This field appears only when the destination is <code>Evidently</code>. It specifies the ARN of the IAM role that is used to write to the Evidently experiment that receives the metrics.</p>"}}, "documentation": "<p>A structure that displays information about one destination that CloudWatch RUM sends extended metrics to.</p>"}, "MetricDestinationSummaryList": {"type": "list", "member": {"shape": "MetricDestinationSummary"}}, "MetricName": {"type": "string", "max": 255, "min": 1}, "Namespace": {"type": "string", "max": 237, "min": 1, "pattern": "[a-zA-Z0-9-._/#:]+$"}, "Pages": {"type": "list", "member": {"shape": "Url"}, "max": 50, "min": 0}, "PutRumEventsRequest": {"type": "structure", "required": ["AppMonitorDetails", "BatchId", "Id", "RumEvents", "UserDetails"], "members": {"AppMonitorDetails": {"shape": "AppMonitorDetails", "documentation": "<p>A structure that contains information about the app monitor that collected this telemetry information.</p>"}, "BatchId": {"shape": "PutRumEventsRequestBatchIdString", "documentation": "<p>A unique identifier for this batch of RUM event data.</p>"}, "Id": {"shape": "PutRumEventsRequestIdString", "documentation": "<p>The ID of the app monitor that is sending this data.</p>", "location": "uri", "locationName": "Id"}, "RumEvents": {"shape": "RumEventList", "documentation": "<p>An array of structures that contain the telemetry event data.</p>"}, "UserDetails": {"shape": "UserDetails", "documentation": "<p>A structure that contains information about the user session that this batch of events was collected from.</p>"}}}, "PutRumEventsRequestBatchIdString": {"type": "string", "max": 36, "min": 36, "pattern": "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"}, "PutRumEventsRequestIdString": {"type": "string", "max": 36, "min": 36, "pattern": "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"}, "PutRumEventsResponse": {"type": "structure", "members": {}}, "PutRumMetricsDestinationRequest": {"type": "structure", "required": ["AppMonitorName", "Destination"], "members": {"AppMonitorName": {"shape": "AppMonitorName", "documentation": "<p>The name of the CloudWatch RUM app monitor that will send the metrics.</p>", "location": "uri", "locationName": "AppMonitorName"}, "Destination": {"shape": "MetricDestination", "documentation": "<p>Defines the destination to send the metrics to. Valid values are <code>CloudWatch</code> and <code>Evidently</code>. If you specify <code>Evidently</code>, you must also specify the ARN of the CloudWatchEvidently experiment that is to be the destination and an IAM role that has permission to write to the experiment.</p>"}, "DestinationArn": {"shape": "DestinationArn", "documentation": "<p>Use this parameter only if <code>Destination</code> is <code>Evidently</code>. This parameter specifies the ARN of the Evidently experiment that will receive the extended metrics.</p>"}, "IamRoleArn": {"shape": "IamRoleArn", "documentation": "<p>This parameter is required if <code>Destination</code> is <code>Evidently</code>. If <code>Destination</code> is <code>CloudWatch</code>, do not use this parameter.</p> <p>This parameter specifies the ARN of an IAM role that RUM will assume to write to the Evidently experiment that you are sending metrics to. This role must have permission to write to that experiment.</p>"}}}, "PutRumMetricsDestinationResponse": {"type": "structure", "members": {}}, "QueryFilter": {"type": "structure", "members": {"Name": {"shape": "QueryFilter<PERSON>ey", "documentation": "<p>The name of a key to search for. The filter returns only the events that match the <code>Name</code> and <code>Values</code> that you specify. </p> <p>Valid values for <code>Name</code> are <code>Browser</code> | <code>Device</code> | <code>Country</code> | <code>Page</code> | <code>OS</code> | <code>EventType</code> | <code>Invert</code> </p>"}, "Values": {"shape": "QueryFilterValueList", "documentation": "<p>The values of the <code>Name</code> that are to be be included in the returned results.</p>"}}, "documentation": "<p>A structure that defines a key and values that you can use to filter the results. The only performance events that are returned are those that have values matching the ones that you specify in one of your <code>QueryFilter</code> structures.</p> <p>For example, you could specify <code>Browser</code> as the <code>Name</code> and specify <code>Chrome,Firefox</code> as the <code>Values</code> to return events generated only from those browsers.</p> <p>Specifying <code>Invert</code> as the <code>Name</code> works as a \"not equal to\" filter. For example, specify <code>Invert</code> as the <code>Name</code> and specify <code>Chrome</code> as the value to return all events except events from user sessions with the Chrome browser.</p>"}, "QueryFilterKey": {"type": "string"}, "QueryFilterValue": {"type": "string"}, "QueryFilterValueList": {"type": "list", "member": {"shape": "QueryFilterValue"}}, "QueryFilters": {"type": "list", "member": {"shape": "Query<PERSON><PERSON>er"}}, "QueryTimestamp": {"type": "long"}, "ResourceNotFoundException": {"type": "structure", "required": ["message", "resourceName"], "members": {"message": {"shape": "String"}, "resourceName": {"shape": "String", "documentation": "<p>The name of the resource that is associated with the error.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>The type of the resource that is associated with the error.</p>"}}, "documentation": "<p>Resource not found.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "RumEvent": {"type": "structure", "required": ["details", "id", "timestamp", "type"], "members": {"details": {"shape": "JsonValue", "documentation": "<p>A string containing details about the event.</p>", "jsonvalue": true}, "id": {"shape": "RumEventIdString", "documentation": "<p>A unique ID for this event.</p>"}, "metadata": {"shape": "JsonValue", "documentation": "<p><PERSON><PERSON><PERSON> about this event, which contains a JSON serialization of the identity of the user for this session. The user information comes from information such as the HTTP user-agent request header and document interface.</p>", "jsonvalue": true}, "timestamp": {"shape": "Timestamp", "documentation": "<p>The exact time that this event occurred.</p>"}, "type": {"shape": "String", "documentation": "<p>The JSON schema that denotes the type of event this is, such as a page load or a new session.</p>"}}, "documentation": "<p>A structure that contains the information for one performance event that RUM collects from a user session with your application.</p>"}, "RumEventIdString": {"type": "string", "max": 36, "min": 36, "pattern": "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"}, "RumEventList": {"type": "list", "member": {"shape": "RumEvent"}}, "ServiceQuotaExceededException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>This request exceeds a service quota.</p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "SessionSampleRate": {"type": "double", "max": 1, "min": 0}, "StateEnum": {"type": "string", "enum": ["CREATED", "DELETING", "ACTIVE"]}, "String": {"type": "string"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^(?!aws:)[a-zA-Z+-=._:/]+$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 0}, "TagMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}}, "TagResourceRequest": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the CloudWatch RUM resource that you're adding tags to.</p>", "location": "uri", "locationName": "ResourceArn"}, "Tags": {"shape": "TagMap", "documentation": "<p>The list of key-value pairs to associate with the resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "Telemetries": {"type": "list", "member": {"shape": "Telemetry"}}, "Telemetry": {"type": "string", "enum": ["errors", "performance", "http"]}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "quotaCode": {"shape": "String", "documentation": "<p>The ID of the service quota that was exceeded.</p>"}, "retryAfterSeconds": {"shape": "Integer", "documentation": "<p>The value of a parameter in the request caused an error.</p>", "location": "header", "locationName": "Retry-After"}, "serviceCode": {"shape": "String", "documentation": "<p>The ID of the service that is associated with the error.</p>"}}, "documentation": "<p>The request was throttled because of quota limits.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true, "retryable": {"throttling": true}}, "TimeRange": {"type": "structure", "required": ["After"], "members": {"After": {"shape": "QueryTimestamp", "documentation": "<p>The beginning of the time range to retrieve performance events from.</p>"}, "Before": {"shape": "QueryTimestamp", "documentation": "<p>The end of the time range to retrieve performance events from. If you omit this, the time range extends to the time that this operation is performed.</p>"}}, "documentation": "<p>A structure that defines the time range that you want to retrieve results from.</p>"}, "Timestamp": {"type": "timestamp"}, "Token": {"type": "string"}, "UnitLabel": {"type": "string", "max": 256, "min": 1}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the CloudWatch RUM resource that you're removing tags from.</p>", "location": "uri", "locationName": "ResourceArn"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>The list of tag keys to remove from the resource.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateAppMonitorRequest": {"type": "structure", "required": ["Name"], "members": {"AppMonitorConfiguration": {"shape": "AppMonitorConfiguration", "documentation": "<p>A structure that contains much of the configuration data for the app monitor. If you are using Amazon Cognito for authorization, you must include this structure in your request, and it must include the ID of the Amazon Cognito identity pool to use for authorization. If you don't include <code>AppMonitorConfiguration</code>, you must set up your own authorization method. For more information, see <a href=\"https://docs.aws.amazon.com/monitoring/CloudWatch-RUM-get-started-authorization.html\">Authorize your application to send data to Amazon Web Services</a>.</p>"}, "CustomEvents": {"shape": "CustomEvents", "documentation": "<p>Specifies whether this app monitor allows the web client to define and send custom events. The default is for custom events to be <code>DISABLED</code>.</p> <p>For more information about custom events, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-RUM-custom-events.html\">Send custom events</a>.</p>"}, "CwLogEnabled": {"shape": "Boolean", "documentation": "<p>Data collected by RUM is kept by RUM for 30 days and then deleted. This parameter specifies whether RUM sends a copy of this telemetry data to Amazon CloudWatch Logs in your account. This enables you to keep the telemetry data for more than 30 days, but it does incur Amazon CloudWatch Logs charges.</p>"}, "Domain": {"shape": "AppMonitorDomain", "documentation": "<p>The top-level internet domain name for which your application has administrative authority.</p>"}, "Name": {"shape": "AppMonitorName", "documentation": "<p>The name of the app monitor to update.</p>", "location": "uri", "locationName": "Name"}}}, "UpdateAppMonitorResponse": {"type": "structure", "members": {}}, "UpdateRumMetricDefinitionRequest": {"type": "structure", "required": ["AppMonitorName", "Destination", "MetricDefinition", "MetricDefinitionId"], "members": {"AppMonitorName": {"shape": "AppMonitorName", "documentation": "<p>The name of the CloudWatch RUM app monitor that sends these metrics.</p>", "location": "uri", "locationName": "AppMonitorName"}, "Destination": {"shape": "MetricDestination", "documentation": "<p>The destination to send the metrics to. Valid values are <code>CloudWatch</code> and <code>Evidently</code>. If you specify <code>Evidently</code>, you must also specify the ARN of the CloudWatchEvidently experiment that will receive the metrics and an IAM role that has permission to write to the experiment.</p>"}, "DestinationArn": {"shape": "DestinationArn", "documentation": "<p>This parameter is required if <code>Destination</code> is <code>Evidently</code>. If <code>Destination</code> is <code>CloudWatch</code>, do not use this parameter.</p> <p>This parameter specifies the ARN of the Evidently experiment that is to receive the metrics. You must have already defined this experiment as a valid destination. For more information, see <a href=\"https://docs.aws.amazon.com/cloudwatchrum/latest/APIReference/API_PutRumMetricsDestination.html\">PutRumMetricsDestination</a>.</p>"}, "MetricDefinition": {"shape": "MetricDefinitionRequest", "documentation": "<p>A structure that contains the new definition that you want to use for this metric.</p>"}, "MetricDefinitionId": {"shape": "MetricDefinitionId", "documentation": "<p>The ID of the metric definition to update.</p>"}}}, "UpdateRumMetricDefinitionResponse": {"type": "structure", "members": {}}, "Url": {"type": "string", "max": 1260, "min": 1, "pattern": "https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&*//=]*)"}, "UserDetails": {"type": "structure", "members": {"sessionId": {"shape": "UserDetailsSessionIdString", "documentation": "<p>The session ID that the performance events are from.</p>"}, "userId": {"shape": "UserDetailsUserIdString", "documentation": "<p>The ID of the user for this user session. This ID is generated by RUM and does not include any personally identifiable information about the user.</p>"}}, "documentation": "<p>A structure that contains information about the user session that this batch of events was collected from.</p>"}, "UserDetailsSessionIdString": {"type": "string", "max": 36, "min": 36, "pattern": "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"}, "UserDetailsUserIdString": {"type": "string", "max": 36, "min": 36, "pattern": "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"}, "ValidationException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>One of the arguments for the request is not valid.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "ValueKey": {"type": "string", "max": 280, "min": 1}}, "documentation": "<p>With Amazon CloudWatch RUM, you can perform real-user monitoring to collect client-side data about your web application performance from actual user sessions in real time. The data collected includes page load times, client-side errors, and user behavior. When you view this data, you can see it all aggregated together and also see breakdowns by the browsers and devices that your customers use.</p> <p>You can use the collected data to quickly identify and debug client-side performance issues. CloudWatch RUM helps you visualize anomalies in your application performance and find relevant debugging data such as error messages, stack traces, and user sessions. You can also use RUM to understand the range of end-user impact including the number of users, geolocations, and browsers used.</p>"}