# Flask 配置
SECRET_KEY=your-secret-key-here
FLASK_ENV=development
FLASK_APP=app.py

# 数据库配置
DATABASE_URL=mysql+pymysql://username:password@localhost/cmdb

# Redis 配置
REDIS_URL=redis://localhost:6379/0

# JWT 配置
JWT_SECRET_KEY=your-jwt-secret-key

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/cmdb.log

# 云同步配置
CLOUD_SYNC_INTERVAL=300

# 阿里云配置
ALIYUN_ACCESS_KEY_ID=your-aliyun-access-key-id
ALIYUN_ACCESS_KEY_SECRET=your-aliyun-access-key-secret
ALIYUN_REGION_ID=cn-hangzhou

# 腾讯云配置
TENCENT_SECRET_ID=your-tencent-secret-id
TENCENT_SECRET_KEY=your-tencent-secret-key
TENCENT_REGION=ap-beijing

# AWS 配置
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_DEFAULT_REGION=us-east-1
